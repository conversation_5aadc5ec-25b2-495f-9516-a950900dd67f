<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_careLabelInfo" id="sgs_smart_customer_careLabelInfo">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-table
                        :data="dataList.careList"
                        border
                        fit
                        size="mini"
                    >
                        <el-table-column prop="imgPathYun" label="Care Image" style="padding: 20px 10px">
                            <template slot="header">
                                <div class="custom-table-header">
                                    <el-col>
                                        <span>Care Image</span>
                                        <el-button  v-if="!createNew && careLabelResult.id"
                                            :disabled="false"
                                            type="text"
                                            style="padding:2px 5px"
                                            @click="openComment('label')">
                                            <i :class="getCommentIcon('careImage')"></i>
                                        </el-button>
                                        <i v-if="showApproved && [0,1].includes(labelApprovedStatus)"
                                        :disabled="false"
                                        class="el-icon-success approved_icon"
                                        :class="getApprovedClass('label','Approved')"
                                        style="color: #00d26a;"
                                        @click="approvedObj('label',1)"
                                        ></i>
                                        <i v-if="showApproved && [0,2].includes(labelApprovedStatus)"
                                        :disabled="false"
                                        class="el-icon-error approved_icon"
                                        :class="getApprovedClass('label','Reject')"
                                        style="color:#ff6600;"
                                        @click="approvedObj('label',2)"
                                        ></i>
                                        <i v-if="showApproved && [1,2].includes(labelApprovedStatus)"
                                        :disabled="false"
                                        class="el-icon-more approved_icon more_icon"
                                        @click="approvedObj('label',0)"
                                        ></i>
                                    </el-col>
                                    <el-col :span="6" style="text-align: right;">
                                        <el-button type="text" @click="addCares" v-if="editRule"><i class="el-icon-plus"></i> Add</el-button>
                                    </el-col>
                                </div>
                            </template>
                            <template slot-scope="{row}">
                                <img :src="row.path"/>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="12">
                    <el-table
                            :data="dataList.labelList"
                            border
                            fit
                            size="mini"
                    >
                        <el-table-column prop="careInstruction" label="Care Wording">
                            <template slot="header">
                                <div class="custom-table-header">
                                    <el-col>
                                        <span>Care Wording</span>
                                        <el-button  v-if="!createNew && careLabelResult.id"
                                        :disabled="false"
                                        type="text"
                                        style="padding:2px 5px"
                                        @click="openComment('wording')">
                                            <i :class="getCommentIcon('careWording')"></i>
                                        </el-button>
                                        <i v-if="showApproved && [0,1].includes(wordingApprovedStatus)"
                                        :disabled="false"
                                        class="el-icon-success approved_icon"
                                        :class="getApprovedClass('wording','Approved')"
                                        style="color: #00d26a;"
                                        @click="approvedObj('wording',1)"
                                        ></i>
                                        <i v-if="showApproved && [0,2].includes(wordingApprovedStatus)"
                                        :disabled="false"
                                        class="el-icon-error approved_icon"
                                        :class="getApprovedClass('wording','Reject')"
                                        style="color:#ff6600;"
                                        @click="approvedObj('wording',2)"
                                        ></i>
                                        <i  v-if="showApproved && [1,2].includes(wordingApprovedStatus)"
                                            :disabled="false"
                                            class="el-icon-more approved_icon more_icon"
                                            @click="approvedObj('wording',0)"
                                        ></i>
                                    </el-col>
                                    <el-col :span="6" style="text-align: right">
                                        <el-button type="text" @click="addWording" v-if="editRule"><i class="el-icon-plus"></i> Add</el-button>
                                    </el-col>
                                </div>
                            </template>
                            <template slot-scope="{row}">
                                <el-input type="textarea"
                                          show-word-limit
                                          maxlength="500"
                                          clearable
                                          :rows="3"
                                          v-model="row.careInstruction"></el-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
            </el-row>
        </div>

        <reject-provided v-if="rejectShow"
                         :object-id="careLabelResult.id"
                         :object-type="objectType"
                         @cancelDia="rejectShow=false"
                         @rejectSuccess="rejectSuccess"
        ></reject-provided>
        <chat-view
                v-if="showChat"
                :object-id="careLabelResult.id"
                :object-type="objectType"
                @cancelDia="showChat=false"
        ></chat-view>

        <el-dialog
                title="Select Care Instruction"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                v-dialog-drag
                :lock-scroll="false"
                show-close
                :visible.sync="showAddCareDia"
                width="70%">
            <care-label-info-dia
                v-if="showAddCareDia"
                ref="careLabelInfoDia"
                :customer-code="customerGroupCode"
                :care-label-object="careLabelResult"
                :show-img="showImg"
                :show-desc="showDesc"
            ></care-label-info-dia>
            <div slot="footer" class="dialog-footer">
                <el-button type="info" @click="showAddCareDia = false">Cancel</el-button>
                <el-button type="primary" v-loading="saveLoading" @click="saveCareInstruction">Save</el-button>
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
    import CareLabelInfoDia from "./careLabelInfoDia";
    import productApi from '@/api/newSamples'
    import api from "../../../api/newSamples";
    import RejectProvided from "../materialAndProductCommon/rejectProvided";
    import ChatView from "../materialAndProductCommon/chatView";
    export default {
        name: "careLabelInfo",
        data() {
            return {
                pageLoading: false,
                showAddCareDia:false,
                saveLoading:false,
                showImg:false,
                showDesc:false,
                rejectShow:false,
                showChat:false,
                careLabelResult:{},
                objectType:'',
                labelApprovedStatus:0,
                wordingApprovedStatus:0,
                dataList:{
                    careList:[],
                    labelList:[{careInstruction:""}]
                }
            }
        },
        methods: {
            openComment(objectType){
                this.objectType = objectType;
                this.showChat = true;
            },
            getApprovedClass(careType,btnType){
                let approvedStatus= this[careType+'ApprovedStatus'];
                if(!approvedStatus || approvedStatus==0){
                    return 'approve_gary';
                }
                if(approvedStatus==1){
                    if(btnType=='Approved'){
                        return 'approve_green';
                    }
                    if(btnType=='Reject'){
                        return 'approve_gary';
                    }
                }
                if(approvedStatus==2){
                    if(btnType=='Approved'){
                        return 'approve_gary';
                    }
                    if(btnType=='Reject'){
                        return 'approve_red';
                    }
                }

            },
            handlerApproved(param,refreshPage=false){
                api.approved(param).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        let {data} = res.data;
                        if(data=='confirm'){
                            //需要弹窗确认，再次请求，然后刷新整个页面
                            this.$confirm('All info has been approved. Please confirm you would like to set the product status to "Approved"',
                                'Tips', {
                                    confirmButtonText: 'Confirm',
                                    cancelButtonText: 'Cancel',
                                    type: 'warning'
                                }).then(() => {
                                param['checkApproved'] = false;
                                this.handlerApproved(param,true);
                            }).catch(err=>{})
                            return;
                        }
                        this.$notify.success("Success")
                        if(refreshPage){
                            window.location.reload();
                            return;
                        }
                        if(this.reloadPage){
                            this.reloadPage()
                        }else{
                            window.location.reload();
                        }
                    }else{
                        this.$notify.error("Fail")
                    }
                }).catch(err=>{
                    this.$notify.error("Fail")
                })
            },
            rejectSuccess(approvedType){
                let param ={
                    sampleId: this.sampleId,
                    objectId : this.careLabelData.id,
                    approvedType,
                    approvedStatus:2
                }
                this.rejectShow = false;
                this.handlerApproved(param);
            },
            approvedObj(approvedType,approvedStatus){
                if(approvedType=='label' && this.labelApprovedStatus==approvedStatus){
                    return
                }
                if(approvedType=='wording' && this.wordingApprovedStatus==approvedStatus){
                    return
                }
                let tips = "";
                if(approvedStatus==0){
                    tips = "Return"
                }
                if(approvedStatus==1){
                    tips = "Approve"
                }
                if(approvedStatus==2){
                    tips = "Reject"
                }
                this.$confirm(tips+' the care '+approvedType+'?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    if(approvedStatus==2){
                        this.objectType = approvedType;
                        this.rejectShow = true;
                        return;
                    }
                    let param ={
                        sampleId: this.sampleId,
                        objectId : this.careLabelData.id,
                        approvedType,
                        approvedStatus
                    }
                    this.handlerApproved(param);
                }).catch(err=>{})
            },
            initPage() {
                //console.log("接收到了careLabelData",this.careLabelData);
                this.careLabelResult = this.careLabelData;
                let {careLabelFileId,careInstruction,labelApprovedStatus,wordingApprovedStatus} = this.careLabelResult;
                this.labelApprovedStatus = labelApprovedStatus?labelApprovedStatus:0;
                this.wordingApprovedStatus = wordingApprovedStatus?wordingApprovedStatus:0;
                if(careLabelFileId){
                    productApi.careLabelApi.downloadPathById({key:careLabelFileId}).then(imgPathResp=>{
                        let imgPathYun = [];
                        if(imgPathResp.status==200 && imgPathResp.data){
                            let path = imgPathResp.data;
                            imgPathYun.push({
                                path,
                                key:careLabelFileId
                            })
                        }
                        this.dataList.careList = imgPathYun;
                    });
                }
                if(careInstruction){
                    this.dataList.labelList = [{careInstruction}];
                }
            },
            rightaaa(){
                //this.$notify.success("Please wait...");
            },
            erroraa(){
                //this.$notify.success("Please wait...");
            },
            async saveCareInstruction(){
                this.saveLoading = true;
                let saveResult = await this.$refs.careLabelInfoDia.getSaveData();
                this.saveLoading = false;
                if(saveResult=='error'){
                    return;
                }
                //根据fileId 获取图片base64
                let {careLabelFileId,imgArray,careInstruction,imgPathYun} = saveResult;
                if(this.showImg){
                    this.dataList.careList = imgPathYun;
                }
                if(this.showDesc){
                    let preInstruction = this.dataList.labelList[0].careInstruction?(this.dataList.labelList[0].careInstruction+'/'):'';
                    careInstruction = preInstruction + careInstruction;
                    if(careInstruction.length>500){
                        careInstruction = [...careInstruction].slice(0,500).join("");
                    }
                    this.dataList.labelList = [{careInstruction}];

                }
                this.careLabelResult = Object.assign({},this.careLabelResult,saveResult,{careInstruction});
                this.showAddCareDia = false;
            },
            addCares(){
                this.showImg = true;
                this.showDesc = false;
                this.showAddCareDia = true;
            },
            addWording(){
                this.showDesc = true;
                this.showImg = false;
                this.showAddCareDia = true;
            },
            getSaveData(){
                let {careInstruction} = this.dataList.labelList[0];
                return Object.assign({}, this.careLabelResult,{careInstruction});
            },
            getCommentIcon(type){
                const commentsNum = type === 'careImage' ? this.careLabelData.commentsNum : this.careLabelData.wordingCommentsNum;
                //没有对话信息
                if(!commentsNum || commentsNum.length==0){
                    return 'el-icon-chat-square';
                }else {
                    return 'el-icon-s-comment';
                }
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            customerGroupCode:{
              type:String,
              default(){
                  return ''
              }
            },
            careLabelData:{
                type:Object,
                default(){
                    return {}
                }
            },
            sampleId:'',
            showApproved:{
                type:Boolean,
                default(){
                    return false
                }
            },
            createNew:{
                type:Boolean,
                default(){
                    return false
                }
            },
            editRule:{
                type:Boolean,
                default(){
                    return true
                }
            },
            reloadPage:Function
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {ChatView, RejectProvided, CareLabelInfoDia}
    }
</script>

<style lang="scss">
    .sgs_smart_customer_careLabelInfo {
        .el-table__empty-block{
            padding: 15px 10px !important;
        }
        .custom-table-header{
           display: flex;
           justify-content: space-between;
           align-items: center;
        }
    }
</style>