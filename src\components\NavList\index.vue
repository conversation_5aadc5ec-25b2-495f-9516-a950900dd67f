<template>
  <ul class="nav-list" ref="nav" id="trf-left-nav">
    <template
      v-for="(item, index) in navList.filter((n) => n.show)"
      :key="item.name"
    >
      <li
        :class="{ 'is-sub': item.isSub }"
        :style="{
          color: item.active ? '#f60' : '#000',
          borderColor: item.active ? '#f60' : '#d9d9d9',
        }"
        class="nav-item"
        @click="toTarget(index)"
      >
        <h5>{{ item.name }}</h5>
      </li>
    </template>
  </ul>
</template>

<script setup>
import { watch, nextTick } from 'vue'

const props = defineProps({
  navList: {
    type: Array,
    required: true,
  },
  toTarget: {
    type: Function,
    required: true,
  },
})
</script>

<style scoped lang="scss">
.nav-list {
  list-style: none;
  margin-top: 24px;
  padding-left: 0;
  li {
    cursor: pointer;
    border-left: 3px solid #d9d9d9;
    padding-left: 24px;
    font-size: 16px;
    font-weight: 400;
    color: #000000;
    &.is-sub {
      padding-left: 40px;
      font-size: 16px;
      font-weight: 400;
      color: #656565;
      h5 {
      }
    }
    &.active {
      color: #f60;
      border-color: #f60;
    }
    h5 {
      padding: 10px 0;
      margin: 0;
      font-weight: normal;
    }
  }
}
</style>
