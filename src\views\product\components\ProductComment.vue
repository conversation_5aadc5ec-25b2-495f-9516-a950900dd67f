<template>
  <div class="product-comment">
    <div class="module-title-container">
      <span class="module-title-text">Product Comment</span>
    </div>
    <div class="product-comment-insert">
      <div class="module-sub-title-container">
        <span class="module-sub-title-text">Your Comment</span>
      </div>
      <el-input placeholder="Write a Comment"
                v-model="data.newComment"
                type="textarea" />
      <el-button @click="addComment">Post</el-button>
    </div>
    <div class="product-comment-list">
      <div class="module-sub-title-container">
        <span class="module-sub-title-text">View Activities</span>
      </div>
      <div class="product-comment-list-select">
        <el-select v-model="data.selectedFilter"
                   placeholder="Filter by"
                   @change="filterComments">
          <el-option label="All"
                     value="all"></el-option>
          <el-option label="Positive"
                     value="positive"></el-option>
          <el-option label="Negative"
                     value="negative"></el-option>
        </el-select>
      </div>
      <div class="product-comment-item">
        <el-timeline style="max-width: 600px">
          <el-timeline-item v-for="(activity, index) in data.activities"
                            :key="index"
                            :color="activity.color"
                            :timestamp="activity.timestamp">
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <el-dialog v-model="data.imgDialogVisible">
      <div class="img-zoom">
        <img :src="data.imgDialogUrl" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElNotification, ElMessageBox, ElMessage } from 'element-plus'

const props = defineProps({
  //   createNew: Boolean,
  //   productForm: {
  //     type: Object,
  //     required: true,
  //   },
  //   btnRole: {
  //     type: Function,
  //     required: true,
  //   },
})
console.log(props)
const data = reactive({
  commentList: [{}, {}],
  activities: [
    {
      content: 'Custom icon',
      timestamp: '2018-04-12 20:46 ',
      color: '#ff6600',
    },
    {
      content: 'Custom color',
      timestamp: '2018-04-03 20:46',
    },
    {
      content: 'Custom size',
      timestamp: '2018-04-03 20:46',
    },
    {
      content: 'Custom hollow',
      timestamp: '2018-04-03 20:46',
    },
    {
      content: 'Default node',
      timestamp: '2018-04-03 20:46',
    },
  ],
})
</script>

<style scoped lang="scss">
@use '@/assets/style/unit.module.scss' as *;

.product-comment {
  .product-comment-add {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 1px dashed $border-color;
    padding: $module-padding-horizontal 0;
    cursor: pointer;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
}
</style>