<template>
    <div>
        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
            <el-form-item>
                <el-input
                    @clear="onSearch"
                    @input="onSearch"
                    @keyup.enter.native="onSearch"
                    v-model="query.addressDetail"
                    :placeholder="$t('address.detail')"
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <!-- <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button> -->
                <!-- <el-button type="primary" v-if="permissionList.addBtn" @click="addRow">{{$t('operation.add')}}</el-button> -->

                <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn" id="add-address">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
                :data="tableData"
                style="width: 100%"
                size="medium">
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="addressDetail"
                    show-overflow-tooltip
                    :label="$t('address.detail')">
            </el-table-column>
            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')"
                    width="150"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="updateUser"
                    :label="$t('common.operator')"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="isDefault"
                    :label="$t('common.isDefault')"
                    width="80">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')" placement="top">
                        <el-switch
                                v-model="scope.row.isDefault"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9"
                                :active-value = "1"
                                :inactive-value = "0"
                                @change="changeDefault(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    prop="status"
                    :label="$t('common.status.title')"
                    width="80"
                    align="center">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                        <el-switch
                                v-model="scope.row.status"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9"
                                :active-value = "1"
                                :inactive-value = "0"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="150"
                    align="center">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)">{{$t('operation.edit')}}</el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog
            width="660px"
            append-to-body="true" 
            @close="handleClose"
            :modal="$store.state.user.setAddress ? false : true" 
            :title="title" 
            :visible.sync="dialogFormVisible" 
            :close-on-click-modal="false" 
            id="add-address-dialog"
            class="z-index-cover">
            <el-form id="add-address-form" ref="form" :model="form"  label-width="100px" label-position="left"  size="medium" class="sgs-form">
                <el-form-item :label="$t('address.detail')" :rules="{ required: true, message: $t('address.validate.addressBlur'), trigger: 'blur' }" prop="addressDetail">
                    <el-input
                            type="textarea"
                            maxlength="200"
                            show-word-limit
                            v-model="form.addressDetail">
                    </el-input>
                </el-form-item>
            </el-form>
            <div id="stepContent" :class="{'submit-tip-step': submitTips}"></div>
            <br>
            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button id="add-address-confirm" size="small" type="primary" @click="submitForm('form')" :loading="submitLoading">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {getPageByUser,add,detail,remove,setDefault,updateAddrStatus} from "@/api/customer/customerAddress";
    import {mapGetters} from "vuex";
    export default {
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        data(){
            return{
                name: "customerAddress",
                dialogFormVisible: false,
                submitLoading: false,
                title: this.$t('address.title.add'),
                tableData: [],
                form: {},
                query: {},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                stepContent: '',
                submitTips: false,
            }
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:address:add'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:address:edit'],false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:address:delete'],false),
                };
            }
        },
        watch: {
            '$store.state.user.setAddress' (newVal, oldVal) {
                console.log('设置地址引导 ', newVal)
                this.dialogFormVisible = newVal
                if(newVal) { // DOM操作
                    setTimeout(() => {
                        document.querySelector("#add-address-dialog #stepContent").appendChild(document.querySelector('.step-content'))
                    }, 200)
                }
            },
            '$store.state.user.taskType'(newVal, oldVal) {
                if(newVal == '') {
                    this.dialogFormVisible = false
                }
            },
            'form.addressDetail': {
                deep: true,
                handler: function(newVal) {
                    if(newVal && this.$store.state.user.taskType != '') {
                        this.submitTips = true
                        document.querySelector("#stepContent .tit").innerText = this.$t('guide.clickSubmit')
                        document.querySelector("#stepContent .step-num span").innerText = this.$t('guide.step4') + this.$t('guide.total4')
                    }
                }
            }
        },
        methods:{
            handleClose() {
                this.$store.commit('SET_TASK_TYPE', '')
                this.$store.commit('SET_GUIDE', { name: 'setAddress', val: false }) // 关闭step中的添加弹窗
                document.querySelectorAll("#stepContent").forEach(item => item.innerHTML = '')
                this.form.addressDetail = ''
                this.submitTips = false
            },
            onSearch() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query,this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        if(this.$store.state.user.taskType != '') { // 完成添加地址
                            let task = JSON.parse(localStorage.getItem('guideTask'))
                            let index = task.findIndex(item => item.type == 'setAddress')
                            if(!task[index].val) {
                                task[index].val = true  // 设置当前任务为完成状态
                                this.$notify({
                                    title: this.$t('success'),
                                    message: this.$t('guide.addAddressSuccess'),
                                    type: 'success',
                                    duration: 1500
                                });
                            }
                            localStorage.setItem('guideTask', JSON.stringify(task))
                            let complete = task.every(item => item.val == true);
                            if(!complete) {
                                setTimeout(()=>{
                                    this.$store.commit('SET_TASK_DIALOG', true) // 2s后打开任务列表
                                }, 2000)
                            }
                        }
                        this.submitLoading=true;
                        add(this.form).then(res =>{
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.submitLoading = false;
                            this.dialogFormVisible=false;
                            this.onLoad(this.page);
                        }, error => {
                            this.submitLoading = false;
                        });
                    } else {
                        return false;
                    }
                });

            },
            removeRow(row){
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                .then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            detailRow(row){
                this.title = this.$t('address.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form=res.data.data;
                });

            },
            addRow(){
                this.form = {};
                this.title = this.$t('address.title.add');
                this.dialogFormVisible =true;
            },
            changeStatus(row){
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                updateAddrStatus(modifiedForm).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            changeDefault(row){
                console.log(row.isDefault);
                setDefault(row.id,row.isDefault).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            }

        },
        created() {
            this.onLoad(this.page);
        }

    }
</script>

<style lang="scss" scoped>
#add-address-dialog {
    .el-dialog {
        .el-form-item__label {
            padding-top: 6px;
        }
        #stepContent {
            position: absolute;
            left: 90px;
            bottom: 90px;
            transition: all .2s;
        }
        .submit-tip-step {
            transform: translate(244px, 58px);
        }
    }
}
</style>
