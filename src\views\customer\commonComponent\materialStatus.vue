<template>
    <div class="sgs_smart_customer_materialStatus" id="sgs_smart_customer_materialStatus" v-if="status">
        <el-tooltip class="tooltip-item" effect="dark" :content="materialStatus[status+'']" placement="top">
            <img :src="'/img/material/icon_point_'+status+'.png'" style="transform: translateY(2px); margin:0 20px;height: 20px;">
        </el-tooltip>
    </div>
</template>

<script>
    export default {
        name: "materialStatus",
        data() {
            return {
                pageLoading: false,
                materialStatus:{
                  "1":  'In-Progress',
                  "2":  'Submitted',
                  "3":  'Approved',
                  "4":  'Cancelled',
                  "5":  'Not In Use',
                },
            }
        },
        methods: {
            initPage() {

            }
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            status:null
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
.sgs_smart_customer_materialStatus{
    display: inline;
}
</style>