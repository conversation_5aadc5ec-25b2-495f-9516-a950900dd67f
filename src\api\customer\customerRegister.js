import request from '@/router/axios';
import { encryptor } from '@/util/auth';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/register',
        method: 'post',
        data: form
    })
}
export const addRegister = (form) => {
    let pass = encryptor(form.customer.account.password);
    form.customer.account.password = pass;
    if(form.customer.password){
        form.customer.password = pass
    }
    if(form.customer.doublePassword){
        form.customer.doublePassword = pass
    }
    return request({
        url: '/api/sgs-mart/customer/simpleRegister',
        method: 'post',
        data: form
    })
}
export const authenticationCustomer = (form) => {
    return request({
        url: '/api/sgs-mart/customer/authenticationCustomer',
        method: 'post',
        data: form
    })
}

export const addOfflineRegister = (form) => {
    return request({
        url: '/api/sgs-mart/customer/addOfflineRegister',
        method: 'post',
        data: form
    })
}

export const addAccount = (form) => {
    return request({
        url: '/api/sgs-mart/customer/applyAccount/add',
        method: 'post',
        data: form
    })
}

/**
   * 获取图片验证码
   * @param data
   */
export const getImageCode=(data) => {
  return request({
    url: '/api/sgs-mart/captcha/image',
    method: 'post',
    data
  })
}

/**
 * 检查图片验证码
 * @param data
 */
export const  checkImageCode= (code) => {
  return request({
    url: '/api/sgs-mart/captcha/image/check',
    method: 'get',
        params: {
            code,
        }
  })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getCustomerNotPageList = (params) => {
    return request({
        url: '/api/sgs-mart/customer/list-not-page',
        method: 'get',
        params: {
            ...params,
        }
    })
}


export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/detail',
        method: 'get',
        params: {
            id,
        }
    })
}


export const registerDetail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/registerDetail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const detailForCurrentUser = () => {
    return request({
        url: '/api/sgs-mart/customer/detail-for-user',
        method: 'get'
    })
}

export const approve = (form) => {
    return request({
        url: '/api/sgs-mart/customer/approve',
        method: 'post',
        data: form
    })
}

export const checkCustomer = (customerName) => {
    return request({
        url: '/api/sgs-mart/customer/check-customer',
        method: 'get',
        params: {
            customerName,
        }
    })
}

export const checkAccount = (form) => {
    return request({
        url: '/api/sgsapi/UserManagementApi/conpanyEmployee/checkCompanyEmployeeExist',
        method: 'post',
        data: form
    })
}
export const checkSmsCode = (phone,checkCode) => {
    return request({
        url: '/api/sgs-mart/customer/check-smsCode',
        method: 'get',
        params: {
            phone,
            checkCode
        }
    })
}

export const sendVerificationCode = (mobile,type) => {
    return request({
        url: '/api/sgs-mart/sms/send/verification-code',
        method: 'get',
        params: {
            mobile,
            type
        }
    })
}
export const validateAccount = (account) => {
    return request({
        url: '/api/sgs-mart/customer/check-account',
        method: 'get',
        params: {
            account
        }
    })
}
export const updateCustomerProductCategory = (form) => {
    return request({
        url: '/api/sgs-mart/customer/updateCustomerProductCategory',
        method: 'post',
        data: form
    })
}

export const updateCompanyNameAndAddress = (params)=>{
    return request({
        url:'/api/sgs-mart/customer/updateCustomerCompanyInfo',
        method:'post',
        data:params
    })
}

export const getCustomerByBossNos = (params) => {
    return request({
        url: '/api/sgs-mart/customer/listByBossNos',
        method: 'post',
        data:params
    })
}