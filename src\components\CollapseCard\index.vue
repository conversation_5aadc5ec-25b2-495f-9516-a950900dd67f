<template>
    <div class="collapse-card">
      <div class="header" >
        <div class="title-wrapper">
            <span class="title" v-if="title">{{ title }}</span>
            <slot name="title" v-else></slot>
        </div>
        <el-icon :class="['arrow', { 'arrow-down': isActive }]" @click="toggle">
            <ArrowDown />
        </el-icon>
      </div>
      <transition name="slide">
        <div v-show="isActive" class="content">
          <slot></slot>
        </div>
      </transition>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import { ArrowDown } from '@element-plus/icons-vue'

  const props = defineProps({
    title: {
      type: String,
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  })
  
  const isActive = ref(true)
  
  const toggle = () => {
    if (!props.disabled) {
      isActive.value = !isActive.value
    }
  }
  </script>
  
  <style scoped>
  .collapse-card {
    margin-bottom: 10px;
  }
  
  .header {
    padding: 12px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;

  }
  
  .title {
    font-weight: bold;
  }

  .title-wrapper{
    display: flex;
    align-items: center;
  }
  
  .arrow {
    cursor: pointer;
    transition: transform 0.3s;
  }
  
  .arrow-down {
    transform: rotate(180deg);
  }
  
  .content {
    padding: 12px;
  }
  
  .slide-enter-active,
  .slide-leave-active {
    transition: all 0.3s ease;
  }
  
  .slide-enter-from,
  .slide-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }
  </style>