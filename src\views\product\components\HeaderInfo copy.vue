<template>
  <div class="smart_views_HeaderInfo"
       id="smart_views_HeaderInfo"
       v-if="loadingFinish">
    <el-row :gutter="20">
      <el-col v-for="(f,index) in data.currentFields"
              :key="'field_'+index"
              :span="['percentageSelect'].includes(f.fieldType)?24:12">
        <el-form ref="headerForm"
                 :model="formValues"
                 label-position="left"
                 label-width="150px">
                <!-- <el-input v-model="formValues[f.fieldCode]" /> -->
          <FormField :formData="formValues"
                     :fieldCode="f.fieldCode"
                     :fieldType="f.fieldType"
                     :isRequired="f.isRequired"
                     :isReadOnly="f.isReadOnly"
                     :fieldLabel="f.fieldLabel"
                     :length="f.length"
                     :tips="f.tips"
                     :sourceValue="f.sourceValue">
            <template #extendIcon
                      v-if="f.extendIcon">
              <el-button type="primary"
                         text
                         :icon="Plus"
                         @click="clickTip"></el-button>
            </template>
          </FormField>
        </el-form>
      </el-col>
      <el-button @click="handleSave">save</el-button>
    </el-row>
    <!-- <el-row>
            <CheckboxTable
                    :tableHeader="formData.checkboxTableData"
                    title="checkbox 多维度表格"
            ></CheckboxTable>
        </el-row> -->
  </div>
</template>


<script setup>
import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  computed,
  watch,
  provide,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElNotification } from 'element-plus'
import { useI18n } from 'vue-i18n'
import CollapseCard from '@/components/CollapseCard/index.vue'
import FormField from '@/components/FormField/index.vue'
import { Plus } from '@element-plus/icons-vue'
import CheckboxTable from './CheckboxTable.vue'

const { t } = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)
defineOptions({
  name: 'HeaderInfo',
})
const props = defineProps({
  templateForHeader: {
    type: Object,
    default: () => {},
  },
  formData: {
    type: Object,
    default: () => {},
  },
})

const data = reactive({
  currentFields: [],
})

// 创建表单数据对象来存储用户输入的值
const formValues = reactive({})

watch(language, (newVal) => {})

const loadingFinish = ref(false)
const percentageData = ref({})
onMounted(() => {
  //   initDemoData()
  loadingFinish.value = true
  data.currentFields = props.formData

  // 初始化表单数据对象
  if (Array.isArray(props.formData)) {
    props.formData.forEach(field => {
      if (field.fieldCode) {
        // 设置初始值，如果字段有默认值则使用，否则根据类型设置
        if (field.defaultValue !== undefined) {
          formValues[field.fieldCode] = field.defaultValue
        } else {
          // 根据字段类型设置默认值
          switch (field.fieldType) {
            case 'number':
              formValues[field.fieldCode] = 0
              break
            case 'checkboxGroup':
            case 'select2':
              formValues[field.fieldCode] = []
              break
            case 'checkbox':
              formValues[field.fieldCode] = false
              break
            default:
              formValues[field.fieldCode] = ''
          }
        }
      }
    })
  }
})

/**
 * 业务代码区域
 * 合并单元格
 * */

const initDemoData = () => {
  /* 多维checkbox表格数据 和结构生成
    结构为
    [[[]]] = 最外层理解为row , 第二层理解为col ，第三层理解为单元格，因为单元格要渲染N个checkbox
    */
  props.formData.checkboxTableData = []
  let rowLength = Math.random() * 10
  rowLength = rowLength < 2 ? 2 : rowLength
  let colLength = Math.random() * 10
  colLength = colLength < 2 ? 2 : colLength
  let itemLength = Math.random() * 20
  itemLength = itemLength < 5 ? 5 : itemLength
  for (let row = 0; row < rowLength; row++) {
    let rowData = []
    for (let col = 0; col < colLength; col++) {
      let colData = []
      for (let item = 0; item < itemLength; item++) {
        let obj = {
          name: Math.random().toString(36).substr(2),
          code: Math.random().toString(36).substr(2),
          value: 1,
          type: item % 5 == 0 ? 'text' : '',
        }
        colData.push(obj)
      }
      rowData.push(colData)
    }
    props.formData.checkboxTableData.push(rowData)
  }

  //表头加载，现在枚举所有支持的类型，重点是fieldType
}
const clickTip = () => {
  ElNotification({
    title: 'Tip',
    message: 'This is a tip',
    type: 'success',
  })
}

const emit = defineEmits(['testSave'])
/* 测试保存 */
const handleSave = () => {
  debugger
  // 调用父组件方法 createSample，传递用户输入的表单数据
  emit('testSave', formValues)
}
</script>

<style lang="scss">
.smart_views_HeaderInfo {
}
</style>