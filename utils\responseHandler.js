// 处理文件类型响应
function handleFileResponse(ctx, response) {
    const contentType = response.headers.get('content-type');
    ctx.set('Content-Type', contentType);
  
    const contentDisposition = response.headers.get('content-disposition');
    if (contentDisposition) {
      ctx.set('Content-Disposition', contentDisposition);
    }
  
    return response.body;
  }
  
  // 处理非文件类型响应
  async function handleJsonResponse(response) {
    return response.json();
  }
  
  // 处理响应状态错误
  function handleResponseError(response) {
    const errorMessages = {
        400: 'Bad Request',
        401: 'Unauthorized',
        403: 'Forbidden',
        404: 'Not Found',
        500: 'Internal Server Error'
    };
    const defaultMessage = `${response.status}`;
    throw new Error(errorMessages[response.status] || defaultMessage);
  }
  
  module.exports = {
    handleFileResponse,
    handleJsonResponse,
    handleResponseError
  };