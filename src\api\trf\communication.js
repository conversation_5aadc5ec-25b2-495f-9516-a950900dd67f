import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/communicationLog/submit',
        method: 'post',
        data: form
    })
}
export const getCommunicationLogList = (params) => {
    return request({
        url: '/api/sgs-mart/communicationLog/listByParamNotPage',
        method: 'get',
        params: {
            ...params,
        }
    })
}

/**
 * 查询reports
 * @param data
 */
export function getReports(trfId) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/trf/report/reports',
        params: {
            trfId
        }
    })
}

/**
 * 查询afl reports
 * @param data
 */
export function getAflReports(trfId) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/afl/report/reports',
        params: {
            trfId
        }
    })
}

//TRF 添加评论 替换 add 方法
export const submitTrfComment = (form) => {
    return request({
        url: '/api/sgs-mart/communicationLog/trfCommentSubmit',
        method: 'post',
        data: form
    })
}

//TRF 评论获取 替换 getCommunicationLogList 方法
export function queryTrfComments(params) {
    return request({
        url: '/api/sgs-mart/communicationLog/trfComments',
        method: 'post',
        data: params
    })
}
