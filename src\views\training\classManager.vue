<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.classManager')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.classManager')}}</h1>
        <el-card shadow="never" class="sgs-box">
            <el-row>
                <el-form :inline="true" :model="form" size="medium">
                    <el-form-item>
                        <el-input clearable v-model="query.courseTitle"
                                :placeholder="$t('training.CourseTitle')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input clearable v-model="query.trainingLocation"
                                :placeholder="$t('training.TrainingLocation')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-if="permissionList.searchBtn" type="primary" @click="searchLabList"
                                v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.search')}}
                        </el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-if="permissionList.addBtn" type="primary" @click="toNewClass('0')"
                                v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.add')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <el-table :data="tableData" stripe style="width:100%" v-loading="listLoading">
                    <el-table-column type="index" label="#" width="50"></el-table-column>
                    <el-table-column width="200" prop="courseTitle" :label="$t('training.CourseTitle')"></el-table-column>
                    <el-table-column width="200" prop="courseIntroduction" :label="$t('training.CourseIntroduction')"
                                    :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column width="200" prop="moduleName" :label="$t('training.Module')"
                                    :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column width="200" prop="courseLevel" :label="$t('training.TrainingCourseLevel')"
                                    :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column width="200" prop="classIntroduction" :label="$t('training.ClassIntroduction')"
                                    :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column width="120" prop="trainingDate" :label="$t('training.TrainingDate')"></el-table-column>
                    <el-table-column width="130" prop="trainingLocation"
                                    :label="$t('training.TrainingLocation')"></el-table-column>
                    <el-table-column width="200" prop="trainingAddress"
                                    :label="$t('training.TrainingAddress')"></el-table-column>
                    <el-table-column width="100" prop="isPublish" :label="$t('training.isPublish')"
                                    :formatter="totextOfPublish"></el-table-column>
                    <!--<el-table-column prop="isPublish" :label="$t('training.isPublish')"  align="center" width="120px"  >
                    <template slot-scope="scope">
                        <el-switch
                                v-model="scope.row.isPublish"
                                active-color="#ff6600"
                                inactive-color="#ff4949"
                                active-text="" active-value=1  inactive-text=""  inactive-value=0  >
                        </el-switch>
                    </template>
                    </el-table-column>-->
                    <el-table-column fixed="right" :label="$t('training.Action')" align="center" width="260px;">
                        <template scope="scope">
                            <el-button v-if="permissionList.editBtn"  type="text" size="small"
                                    @click="editClass(scope.row.id)" icon="el-icon-tickets">{{ $t('training.detail') }}
                            </el-button>
                            <el-button v-if="permissionList.deleteBtn"  :disabled="scope.row.isComplete==1" type="text"
                                    @click="handleDel(scope.row.id)" icon="el-icon-delete">{{ $t('training.Delete') }}
                            </el-button>
                            <el-button v-if="permissionList.publishBtn" :disabled="scope.row.isComplete==1" type="text"
                                    @click="updPublish(scope.row,1)">{{ $t('training.Publish') }}
                            </el-button>
                            <el-button v-if="permissionList.withdrawBtn" :disabled="scope.row.isComplete==1" type="text" 
                                    @click="updPublish(scope.row,0)">{{ $t('training.NotPublish') }}
                            </el-button>
                            <!--spint 8 07-13逻辑修改 去除attendance Button时间控制 new Date(scope.row.trainingDate).getTime() < Date.now()-->
                            <el-button v-if="permissionList.attendanceBtn" :disabled="scope.row.isComplete==1" type="text"
                                    @click="attendance(scope.row)">{{ $t('training.Attendance') }}
                            </el-button>
                            <!--2021-02-18新增完成逻辑-->
                            <el-button v-if="permissionList.completeBtn" :disabled="scope.row.isComplete==1 || scope.row.isPublish==0" type="text"
                                    @click="complete(scope.row)">{{ $t('training.CompleteClass') }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
    </basic-container>
</template>


<script>
    import {getList, updPublish,completeClass} from "@/api/training/classManager";
    import {handleDel2} from "@/api/training/classList";
    import {mapGetters} from "vuex";

    export default {
        data() {
            return {
                name: "list",
                query: {},
                sort: {descs: 'update_time'},
                tableData: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                notPublishBtn: false,
                publishBtn: false,
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    searchBtn: this.vaildData(this.permission['sgs:training:class:search'], false),
                    addBtn: this.vaildData(this.permission['sgs:training:class:add'], false),
                    editBtn: this.vaildData(this.permission['sgs:training:class:edit'], false),
                    deleteBtn: this.vaildData(this.permission['sgs:training:class:delete'], false),
                    publishBtn: this.vaildData(this.permission['sgs:training:class:publish'], false),
                    withdrawBtn: this.vaildData(this.permission['sgs:training:class:withdraw'], false),
                    saveBtn: this.vaildData(this.permission['sgs:training:class:save'], false),
                    attendanceBtn: this.vaildData(this.permission['sgs:training:training:attendance'], false),
                    completeBtn: this.vaildData(this.permission['sgs:training:training:complete'], false),
                };
            }
        },
        created: function () {
            this.onLoad(this.page);
        },
        methods: {
            totextOfPublish(val) {
                if (val.isPublish === "1") {
                    return this.$t('training.Published')
                } else {
                    return this.$t('training.NoPublish')
                }
            },
            updPublish(row, isPublish) {
                this.$confirm(this.$t('operation.confirmStatus'), '提示', {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: 'warning'
                }).then(() => {
                    updPublish(Object.assign({
                        id: row.id,
                        isPublish: isPublish,
                        courseTitle: row.courseTitle
                    })).then(res => {
                        this.onLoad(this.page);
                        this.isDisabled = false;
                        this.$message({
                            type: "success",
                            message: this.$t('training.success')
                        });
                    }, error => {
                        console.log(error);

                    });

                }).catch(() => {
                });
            },
            onLoad(page) {
                var params = {};
                getList(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
                    console.log(res);
                    this.loading = false;
                    this.fullscreenLoading = false;
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.tableData = data.records;
                });
            },
            searchLabList() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            toNewClass() {
                this.$router.push({path: '/training/class'})
            },
            complete(row) {
                this.$confirm(this.$t('training.confirmComplete'), this.$t('tip'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: 'warning'
                }).then(() => {
                    completeClass(row.id).then(() => {
                        this.isDisabled = false;
                        this.$message({
                            type: "success",
                            message: this.$t('training.success')
                        });
                        this.onLoad(this.page);
                    }, error => {
                        console.log(error);
                    });
                }).catch(() => {

                });
            },
            attendance(row) {
                /*if (new Date(row.trainingDate).getTime()  > Date.now()){
                    this.$message({
                        type: "info",
                        message: this.$t('training.attendanceStatus')
                    });
                } else {

                }*/
                this.$router.push({path: '/training/attendance?classId=' + row.id})
            },
            handleDel(id) {
                this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: 'warning'
                }).then(() => {
                    handleDel2(id).then(() => {
                        this.isDisabled = false;
                        this.$message({
                            type: "success",
                            message: this.$t('training.success')
                        });
                        this.onLoad(this.page);
                    }, error => {
                        console.log(error);
                    });
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });*/
                });
            },
            editClass(classId) {
                this.$router.push({path: '/training/class?classId=' + classId})
            },
        }
    }


</script>

<style lang="scss">
    .el-tooltip__popper {
        max-width: 400px;
        line-height: 180%;
    }
</style>
