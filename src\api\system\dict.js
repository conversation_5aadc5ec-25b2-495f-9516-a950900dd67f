import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/nkop-system/dict/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const remove = (ids) => {
  return request({
    url: '/api/nkop-system/dict/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/nkop-system/dict/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/nkop-system/dict/submit',
    method: 'post',
    data: row
  })
}


export const getDict = (id) => {
  return request({
    url: '/api/nkop-system/dict/detail',
    method: 'get',
    params: {
      id,
    }
  })
}

export const getDictTree = () => {
  return request({
    url: '/api/nkop-system/dict/tree?code=DICT',
    method: 'get'
  })
}

export const getDictByCode = (code) => {
    return request({
        url: '/api/nkop-system/dict/dictionary',
        method: 'get',
        params: {
            code,
        }
    })
}
