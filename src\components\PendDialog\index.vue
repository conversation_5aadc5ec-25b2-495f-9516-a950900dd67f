<template>
  <el-dialog
    :title="t('pend.name')"
    :model-value="dialogVisible"
    @update:model-value="handleDialogVisibleChange"
    :modal-append-to-body="false"
    width="70%"
  >
    <el-table :data="pendList" style="width: 100%">
      <el-table-column
        prop="type"
        :label="t('pend.pendType')"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="pendDescribe"
        :label="t('pend.pendDescribe')"
      ></el-table-column>
      <el-table-column
        prop="isRead"
        :label="t('pend.isRead')"
        width="140"
        :formatter="readFormtter"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        :label="t('pend.createTime')"
        width="160"
      ></el-table-column>
      <el-table-column :label="t('operation.title')" width="110" align="center">
        <template #default="scope">
          <el-button
            @click="handle(scope.row)"
            link
            type="primary"
            size="small"
            v-if="scope.row.isHandle == 0"
          >
            {{ t('pend.goHandle') }}
          </el-button>
          <el-button
            :disabled="true"
            link
            type="primary"
            size="small"
            v-if="scope.row.isHandle == 1"
          >
            {{ t('pend.processed') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pt-10"
      @size-change="sizeChange"
      @current-change="currentChange"
      :current-page="page.currentPage"
      :page-sizes="[5, 10, 20, 50, 100]"
      :page-size="page.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="page.total"
    ></el-pagination>
  </el-dialog>
</template>

<script lang="ts">
import { ref, watch, defineComponent } from 'vue'
import { useI18n, type ComposerTranslation } from 'vue-i18n'
import { queryPendList, updatePendReadStatus } from '@/api/pend'
import { scmBaseUrl } from '@/config/env'

// Define props type
interface PendDialogProps {
  dialogVisible: boolean
}

export default defineComponent({
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:dialogVisible'],
  setup(props: PendDialogProps, { emit }) {
    const { t } = useI18n()
    const pendList = ref<any[]>([])
    const page = ref({
      pageSize: 5,
      currentPage: 1,
      total: 0,
    })

    const readFormtter = (row: any, column: any) => {
      const type = row[column.property]
      return type === 0 ? t('common.no') : t('common.yes')
    }

    const handle = (row: any) => {
      window.open(scmBaseUrl + row.url, '_blank')
      if (row.isRead === 0) {
        updatePendReadStatus({ id: row.id, isRead: 1 }).then(() => {
          page.value.currentPage = 1
          initLoadPend()
        })
      }
      emit('update:dialogVisible', false)
    }

    const sizeChange = (pageSize: number) => {
      page.value.pageSize = pageSize
      initLoadPend()
    }

    const currentChange = (currentPage: number) => {
      page.value.currentPage = currentPage
      initLoadPend()
    }

    const initLoadPend = () => {
      queryPendList(page.value.currentPage, page.value.pageSize, {
        isHandle_equal: 0,
        descs: 'update_time',
      }).then((res) => {
        pendList.value = res.data.records || []
        page.value.total = res.data.total || 0
      })
    }

    const handleDialogVisibleChange = (newValue: boolean) => {
      emit('update:dialogVisible', newValue)
    }

    watch(
      () => props.dialogVisible,
      (newVal) => {
        if (newVal) {
          initLoadPend()
        }
      },
    )

    return {
      t: t as ComposerTranslation,
      pendList,
      page,
      readFormtter,
      handle,
      sizeChange,
      currentChange,
      handleDialogVisibleChange,
    }
  },
})
</script>

<style scoped lang="scss">
.pt-10 {
  padding-top: 10px;
}
</style>
