export default {
  /*菜单路由*/
  route: {},
  footerBar: {
    termsOfUse: 'SGS SMART 使用条款',
    DPP: '数据隐私声明',
    termsOfUsePdfPath: '/static/pdf/SGSSMARTTermsofUseCH.pdf',
    DPPPdfPath: '/static/pdf/SGSSMARTDataPrivacyNoticeCH.pdf',
  },
  action:'操作',
  tip: '提示',
  reminder: '提醒',
  title: '通标标准技术服务有限公司门户网站',
  logoutTip: '退出系统, 是否继续?',
  submitText: '确定',
  cancelText: '取消',
  select: '请选择',
  search: '请输入搜索内容',
  searchTxt: '请输入',
  menuTip: '没有发现菜单',
  loading: '拼命加载中',
  uploadType: '上传图片只能是 JPG 格式!',
  uploadType_PDF: '上传附件只能是 PDF 格式!',
  uploadSize: '上传图片大小不能超过 2MB!',
  uploadFileSize: '上传文件大小不能超过10MB!',
  uploadFileSizeError: '上传文件大小不能超过',
  uploadSuccess: '上传成功',
  uploadLoadingText: '文件上传中…',
  uploadExceed_1: '最多支持上传一份附件，请删除后重新上传',
  systemLanguage: '系统语言',
  number: '编号',
  piece: '件',
  success: '成功',
  NoData: '暂无数据',
  ascendingOrder: '升序',
  descendingOrder: '降序',
  language: {
    add: '添加语言',
    name: '语言',
    curLanguage: '当前语言',
    default: '默认语言',
    zh: '中文',
    en: '英文',
    repeatLanguageMsg: '已存在相同语言数据，请勿重复添加',
    manage: '语言管理',
    validate: {
      selLanguageBlur: '请选择对应语言',
      setDefaultLanguage: '请设置默认语言',
    },
  },
  form: {
    printBtn: '打 印',
    mockBtn: '模 拟',
    submitBtn: '提 交',
    emptyBtn: '清 空',
    exportTemplate: '下载模板',
    importTip: '请上传 .xls .xlsx 标准格式文件',
  },
  operation: {
    title: '操 作',
    add: '添 加',
    edit: '编 辑',
    modify: '修 改',
    remove: '删 除',
    chat: '沟通',
    view: '查 看',
    search: '查 询',
    submit: '提 交',
    confirm: '确 认',
    cancel: '取 消',
    reset: '清 空',
    resetTip: '清空过滤',
    disable: '禁 用',
    approve: '审 核',
    pass: '通 过',
    reject: '拒 绝',
    auth: '授 权',
    select: '选 择',
    upload: '点击上传',
    download: '下载',
    detail: '详情',
    confirmDelete: '确定将选择数据删除！',
    confirmStatus: '确定更新选择数据！',
    confirmEnable: '确定将启用选择数据！',
    confirmDisable: '确定将禁用选择数据！',
    confirmDeleteRoot: '存在子节点，确定删除吗',
    save: '保 存',
    finalizeReview: '完成评定',
    complete: '完 成',
    isEnable: '是否启用',
    saveAsTemplate: '另存为模板',
    back: '返回',
    goBack: '返 回',
    backToList: '返回列表',
    print: '预览/打印',
    preview: '预览',
    copy: '复制',
    downLoad: '导出',
    export: '导出',
    exportTRF: '导出申请表记录',
    exportReportFiles: '导出报告文件',
    exportReportTips: '请先筛选TRF。',
    exportReportMaxTips: '最多可以导出20个报告文件，请调整过滤条件。',
    selected: '选择',
    remark: '备注',
    more: '更多',
    pleaseSelect: '请选择',
    and: '和',
    pleaseEnterContent: '请输入内容...',
  },
  notice: {
    maintenanceNotice: 'SGS SMART 系统维护通知',
    maintenanceMessage:
      'SGS SMART将在北京时间2021-03-26日19点到2021-03-28日0点升级，升级期间系统将不能使用。多有不便，敬请谅解！',
  },

  page: {
    prePage: '上一页',
    nextPage: '下一页',
    clock: '顺时针',
    counterClock: '逆时针',
  },
  datePicker: {
    startTime: '开始时间',
    endTime: '结束时间',
    lastWeek: '最近一周',
    lastMonth: '最近一个月',
    lastThreeMonths: '最近三个月',
    lastHalfYear: '最近半年',
    lastYear: '近一年',
    error: {
      timeSpanMsg: '只能查看一年内的数据.',
    },
  },
  navbar: {
    home: '首页',
    logOut: '退出登录',
    changePassword: '修改密码',
    dashboard: '仪表盘',
    userinfo: '个人信息',
    companyInfo: '公司信息',
    personalitySetting: '个性化设置',
  },
  pend: {
    name: '待办',
    pendType: '类型',
    pendDescribe: '待办事项',
    isRead: '是否已读',
    goHandle: '前往处理',
    processed: '已处理',
    createTime: '创建时间',
  },
  common: {
    title: 'SGS SMART 中文',
    status: {
      title: '状态',
      enable: '启用',
      disable: '禁用',
      push: '已推送',
      notPush: '未推送',
      reject: '已拒绝',
    },
    all: '全部',
    register: '注册',
    effectiveDate: '生效日期',
    invalidDate: '失效日期',
    operator: '操作人',
    time: '时间',
    operationTime: '操作时间',
    other: '其它',
    default: '设为默认',
    isDefault: '默认',
    yes: '是',
    no: '否',
    enable: '启用',
    disable: '禁用',
    approveStatus: '审核状态',
    pass: '通过',
    reject: '拒绝',
    isAdmin: '是否管理员',
    isChangeStatus: '是否修改状态',
    inputFilter: '输入关键字进行过滤',
    opinion: '审核意见',
    closeTab: '请在关闭窗口前确保已保存该信息',
  },
  formValidate: {
    validateError: '必填项不完整',
  },
  filter: {
    set: '设为',
    default: '默认',
    delete: '删除',
    saveFilter: '保存视图',
    replaceAnExisting: '替换当前过滤条件',
    saveAsNew: '保存为新的过滤条件',
    save: '保存',
    saveAsLocal: '存为私有条件',
    saveAsPublic: '存为公共条件',
    filterPlaceHolder: '我的视图列表',
  },
  api: {
    success: '操作成功!',
    error: '操作失败！',
  },
  register: {
    error: '错误',
    userName: '用户名',
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    mobilePhone: '手机号',
    mobilePhoneTip: '您可以使用手机号登录',
    getCode: '获取验证码',
    verificationCode: '验证码',
    next: '下一步',
    companyName: '公司名称',
    companyNameCN: '公司名称(中文)',
    taxNo: '税号',
    joinCompany: '加入公司',
    fillCompanyInformation: '填写公司信息',
    companyAddress: '公司地址',
    companyAddressCN: '公司地址(中文)',
    productCategory: '产品类别',
    sgsContacts: 'SGS联系人邮箱',
    sgsReportNo: 'SGS报告编号',
    approveTip: '提供更多信息以加快审批',
    success: '成功',
    registrationSubmitedTo:'您的注册已提交至',
    adminContact:'管理员',
    registrationSuccess:'您将在注册被批准时收到一封电子邮件。',
    thankYou:'感谢您选择SGS。',
    welcomeText:'您好',
    welcomeToSGS:'欢迎来到SGS SMART。', 
    pleaseUserInfo:'请填写信息。',
    pleaseChooseCompany:'请选择公司。',
    pleaseFillCompanyInfo:'请填写公司信息。',
    submit:'提交',
    returnLoginPage:'返回登录页面',
    companyExist:'公司信息已存在，请加入公司',
    validate:{
      emailExist:'邮箱地址已存在，请修改或者直接登录',
      passwordFormat:'密码必须包含大写、小写、数字和特殊符号',
      errorRegister:'非法参数，请重新注册',
      companyNameEN:'请输入公司名称',
      taxNo:'请输入税号',
      companyAddress:'请输入公司地址',
      serviceUnits:'请选择产品类别',
      passwordRequired:'请输入密码',
      passwordNotMatch:'两次密码不匹配',
      confirmPasswordRequired:'请输入确认密码',
      verificationCodeRequired:'请输入验证码',
      passwordLength:'密码长度为8到20个字符',
      mobileRequired:'请输入手机号',
      mobileFormat:'目前，仅支持中国大陆的手机号码',
      verificationCodeLength:'验证码应为6位数字',
      userNameRequired:'请输入用户名',
      userNameLength:'用户名长度应为3到50个字符',
      emailRequired:'请输入邮箱',
      emailFormat:'请输入正确的邮箱格式',
      sgsReportNo:'请输入SGS报告编号',
      sgsContacts:'请输入SGS联系人',
      companyNameCN:'请输入公司名称(本地语言)',
    }
  },
  companyInfo:{
    title:'公司信息',
    customerNo:'SGS 客户编码',
    reportNo:'SGS 报告号',
    email:'SGS 联系人',
    businessLicense:'营业执照'
  },
  scm:{
    iamSupplier:'我是供应商',
    iamBuyer:'我是买家',
    MyBuyer:'我的买家',
    MySupplier:'我的供应商',
    MyManufacture:'我的生产厂商',
    address:'地址',
    assignedCode:'已分配编码',
    sgsCode:'SGS客户编码',
    approveStatus: '审批状态',
    supplierName: '供应商名称',
    buyerName: '买家名称',
    manufacturerName: '生产厂商名称',
    manufacturer:'生产厂商',
    companyName: '公司名称',
    searchSupplier:'搜索供应商',
    searchBuyer:'搜索买家',
    searchManufacture:'搜索生产厂商',
    createSupplier:'创建供应商',
    createBuyer:'创建买家',
    createManufacture:'创建生产厂商',
    updateUser:'修改人',
    updateTime:'修改时间',
    sgsBoard:'是否注册SGS SMART',
    company:{
      en:'公司名称(英文)',
      cn:'公司名称(中文)',
      addressEn:'公司地址(英文)',
      addressCn:'公司地址(中文)',
      businessContacts:'公司联系人',
    },
    approveStatusEnumName:{
      approve:'已审批',
      reject:'已拒绝',
      inProgress:'审批中',
      noRequired:'无需审批',
    },
    action:'操作',
    linkName:{
      linkBuyer:'关联买家',
      linkSupplier:'关联供应商',
      linkManufacture:'关联生产厂商'
    },
    contact:{
      title:'联系人',
      name:'姓名',
      email:'邮箱',
      mobile:'手机号',
      addContact:'添加联系人',
      editContact:'编辑联系人',
    },
    btnName:{
      add:'添加',
      cancel:'取消',
      save:'保存',
      edit:'编辑',
      delete:'删除',
      disable:'禁用',
      enable:'启用',
      approve:'审批',
      reject:'拒绝'
    },
    validate:{
      contactName:'请输入姓名',
      companyName:'请输入公司名称'
    }
  }
}
