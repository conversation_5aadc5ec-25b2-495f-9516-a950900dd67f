<template>
    <basic-container>
        <div class="sgs_smart_new_material_audit_trail" id="sgs_smart_new_material_audit_trail">
            <audit-trail-table
                v-for="(obj,ind) in objects"
                :key="'audit_'+ind"
                :object-type="obj.objectType"
                :section-name="obj.name"
                :root-object-id="obj.rootObjectId"
                :object-date="obj.objectDate"
                :object-no="objectNo"
            ></audit-trail-table>
        </div>
    </basic-container>
</template>

<script>

    import AuditTrailTable from "./auditTrailTable";
    export default {
        name: "",
        data() {
            return {}
        },
        methods: {},
        mounted() {
        },
        created() {
        },
        watch: {},
        computed: {},
        props: {
            objects:[],
            objectNo:''
        },
        updated() {
        },
        components: {AuditTrailTable}
    }
</script>

<style scoped>
    .auditTrail {
    }
</style>