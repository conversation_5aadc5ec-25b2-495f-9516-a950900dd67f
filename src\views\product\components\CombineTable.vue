<template>
    <div class="smart_views_CombineTable" id="smart_views_CombineTable">
        <CommonTable
                border
                v-loading="tableOption.loading"
                ref="manufactureInfoTable"
                style="width:100%"
                row-key="id"
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
                @sortBy="changeSort"
                @selection-change="selectionChange"
                @changePage="changePage"
        >
            <template #actionColumn="{row,$index}">
                <el-button text type="primary" size="mini">
                    Edit
                </el-button>
            </template>
        </CommonTable>
    </div>
</template>


<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'
import CollapseCard from '@/components/CollapseCard/index.vue'
import CommonTable from "@/components/TableList/CommonTable.vue";

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)
defineOptions({
    name: 'CombineTable',
    desc:'支持合并行单元格'
})

watch(language, (newVal) => {

})
onMounted(() => {
})

const tableOption = reactive({
    dataList: [
        {id:1,companyName:'SGS',companyAddress:'宜山路',childAssignCode:'555',sgsCode:'Code1'},
        {id:2,companyName:'SGS 南京',companyAddress:'漕河泾',childAssignCode:'555',sgsCode:'Code2'},
        {id:3,companyName:'字节跳动1',companyAddress:'徐家汇',childAssignCode:'555',sgsCode:'55'},
        {id:4,companyName:'字节跳动1',companyAddress:'漕河泾',childAssignCode:'222',sgsCode:'123'},
        {id:5,companyName:'商汤',companyAddress:'漕河泾',childAssignCode:'222',sgsCode:'123'},
    ],
    originalDataList: [],
    loading: false,
    menuShow: true,
    size: 'small',
    option: {
        hideRowColor: true,
        selection: true,
        selectionDis: ()=>{return true},
        sortSelectionFiledName: '',
        showSortIcon: true,
        index: true,
        action: true,
        actionWidth: 150,
        disableOption:{
            disableNeedStrikethrough:true,
            disableValue:'0',
            disableProp:'status'
        },
        combineProp:['companyName','childAssignCode','companyAddress'],
        combineColor:'#c8c8c033',
        column: [
            {prop: 'companyName', label: 'scm.manufacturerName', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'companyAddress', label: 'scm.address', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'childAssignCode', label:  'scm.assignedCode', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'sgsCode', label: 'scm.sgsCode', hide: false, filter: true, slot: false, type: 'Input'},
        ]
    },
    sort:{
        sortBy:"",
        sortOrder:""
    },
    filters: {},
    page: {
        show: true,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})







</script>

<style lang="scss">
.smart_views_CombineTable {

}
</style>