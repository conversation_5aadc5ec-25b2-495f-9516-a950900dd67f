<template>
  <basic-container>
    <avue-crud v-if="crudOptions.column"
               :option="crudOptions"
               :data="data"
               ref="crud"
               v-model="form"
               :page="page"
               :permission="permissionList"
               :key = "form.formId"
               @row-del="rowDel"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="scope" slot="menuLeft">
        <el-button type="danger"
                   icon="el-icon-plus"
                   size="small"
                   plain
                   @click.stop="rowInput()">新 增</el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button type="text"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   @click="rowInput(scope.row)">编 辑
        </el-button>
      </template>
    </avue-crud>
    <el-drawer
            title="表单数据录入"
            :visible.sync="dialogVisible"
            size="100%"
            :before-close="handleClose">
      <avue-form  ref="form"
                  class="preview-form"
                  :option="widgetForm"
                  v-model="widgetModels"
                  @submit="handleSaveFormData">
      </avue-form>
    </el-drawer>
  </basic-container>
</template>

<script>
  import {getList, formDataAdd,getFormDataList,getFormOption,getFormDetail} from "@/api/form/form";
  import {remove, update, removeByDocId} from "@/api/form/data";
  import {mapGetters} from "vuex";


  export default {
    data() {
      return {
        form: {formId: '',formCode: this.$route.params.formCode || (this.$route.query.formCode||'')},
        dialogVisible: false,
        jsonData: {},
        formData: {},
        dictionary:[],// 本页面用到的数据字典项
        selectionList: [],
        query: {},
          widgetForm:{},
          widgetModels:{},
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        crudOptions: {},
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          viewBtn:false,
          addBtn: false,
          delBtn: this.vaildData(this.permission.form_delete, true),
          editBtn: false,
          inputBtn: this.vaildData(this.permission.form_input, true),
          designBtn: this.vaildData(this.permission.form_design, true),
          dataBtn: this.vaildData(this.permission.form_data, true),
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created: function(){
        // 加载表格配置
        this.loadCrudOption();
        // 加载表单配置
        this.loadFormOption();
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeByDocId(this.form.formCode, row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params,done) {
          this.query = params;
          this.onLoad(this.page, params);
          done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      handleDelete() {
          if (this.selectionList.length === 0) {
              this.$message.warning("请选择至少一条数据");
              return;
          }
          this.$confirm("确定将选择数据删除?", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
          })
          .then(() => {
              return remove(this.form.formCode, this.ids);
          })
          .then(() => {
              this.onLoad(this.page);
              this.$message({
                  type: "success",
                  message: "操作成功!"
              });
              this.$refs.crud.toggleSelection();
          });
      },
      loadCrudOption(){
          // 1、获取表单定义
          getFormOption(this.form.formCode).then(res => {
              const data = res.data.data;
              debugger
              this.form.formId = data.id;
              delete data.id;
              const columns = [];
              data.column.forEach(function(column, index){
                  column = JSON.parse(column.content);
                  if(column.dicData&&column.dicData!=[]){
                      column.props.label = 'label';
                      column.props.value = 'value';
                  }
                  columns.push(column);
                  // if(column.dicUrl&&column.dicData!=[]){
                  //     delete column.dicData;
                  // }
              })
              data.column = columns;
              this.crudOptions = data;
          });
      },
      loadFormOption(){
          // 获取表单定义
          getFormDetail(this.form.formCode).then(res => {
              const data = res.data.data;
              this.widgetForm = JSON.parse(data.content);

              // 重新解析字段配置，替换正则表达式及方法
              this.widgetForm.column.forEach(function(column, index){
                  // 添加正则表达式解析逻辑
                  if(column.rules&&column.pattern){
                      column.rules.forEach(function (rule) {
                          if(rule.pattern){
                              rule.pattern = new RegExp(column.pattern)
                          }
                      })
                  }
                  if(column.method){
                      Object.keys(column.method).forEach(function (key) {
                          const funcStr = column.method[key];
                          column[key] = new Function(funcStr);
                      })
                  }
              })
              // 重新解析字段配置，替换正则表达式及方法
              if(this.widgetForm.group){
                this.widgetForm.group.forEach(function(column, index){
                    // 添加正则表达式解析逻辑
                    if(column.rules&&column.pattern){
                        column.rules.forEach(function (rule) {
                            if(rule.pattern){
                                rule.pattern = new RegExp(column.pattern)
                            }
                        })
                    }
                    if(column.method){
                        Object.keys(column.method).forEach(function (key) {
                            const funcStr = column.method[key];
                            column[key] = new Function(funcStr);
                        })
                    }
                })
              }
          });
      },
      rowInput(row) {
        // 如果存在表单，且为新增重置表单内容
        if(this.$refs.form&&!row) this.$refs.form.resetForm();
        this.widgetModels = row || {};
        this.dialogVisible = true;
      },
      handleSaveFormData(form,done){
        let formDataParams = {};
        // 基于Form组件，生成JSON数据对象
        formDataParams.data = JSON.stringify(form);
        formDataParams.id = this.widgetModels.id;
        formDataAdd(Object.assign(formDataParams,this.form)).then(() => {
            this.onLoad(this.page);
            this.$message({
                type: "success",
                message: "操作成功!"
            });
            done();
        }, error => {
            done();
            console.log(error);
        });
        this.dialogVisible = false;
      },
      handleClose(done) {
          this.$confirm('确认关闭？')
              .then(_ => {
                  done();
              })
              .catch(_ => {});
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
          // 获取表单数据
          getFormDataList(this.page.currentPage, this.page.pageSize, this.form.formCode).then(res => {
              const data = res.data.data;
              this.page.total = data.total;
              this.data = data.records;
              // this.widgetModels = data.records;
              // 强制更新刷新配置
              // this.$refs.crud.$forceUpdate();
          }, error => {
              console.log(error);
          });
      }
    }
  };
</script>

<style>
</style>
