<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_index_HomePageTop5" id="sgs_smart_index_HomePageTop5">
            <el-table
                fit
                border
                :data="tableData"
                v-loading="tableDataLoading"
            >
                <el-table-column show-overflow-tooltip
                                 width="200"
                                 prop="trf_header_trfStatusName"
                                 :label="$t('trfList.jobStatus')">
                    <template slot-scope="scope">
                       <slot name="trfStatus_slot" v-bind="scope"></slot>
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip width="200" prop="trf_trfNo" :label="$t('trfList.trfNo')">
                    <template slot-scope="scope">
                        <slot name="trfNo_slot" v-bind="scope"></slot>
                    </template>
                </el-table-column>
                <el-table-column
                        show-overflow-tooltip
                        width="200"
                        v-for="(dffCol,index) in dffColumnList"
                        :key="'dff_col_'+index"
                        :prop="dffCol.fieldCode"
                        :label="dffCol.displayName"
                ></el-table-column>
                <el-table-column show-overflow-tooltip width="200" prop="report_reportNo" :label="$t('trfList.reportNo')">
                    <template slot-scope="scope">
                        <slot name="reportNo_slot" v-bind="scope"></slot>
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip width="200" prop="report_conclusion_customerConclusion" :label="$t('trfList.conclusion')"></el-table-column>
                <el-table-column show-overflow-tooltip width="200" prop="trf_header_trfSubmissionDate" :label="$t('trfList.submitDate')"></el-table-column>
                <el-table-column show-overflow-tooltip width="200" prop="" :label="$t('trfList.dynamicMsg')">
                    <template slot-scope="scope">
                        <slot name="latest_slot" v-bind="scope"></slot>
                    </template>
                </el-table-column>
                <el-table-column prop="" :label="$t('operation.title')" width="200" fixed="right">
                    <template slot-scope="scope">
                        <slot name="action_slot" v-bind="scope"></slot>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </basic-container>
</template>

<script>
    import {trfListDimensionQuery,trfListTop5Query}  from "@/api/trf/trf";
    import {mapGetters} from "vuex";

    export default {
        name: "HomePageTop5",
        data() {
            return {
                pageLoading: false,
                tableDataLoading:false,
                dffColumnList:[],
                tableData:[]
            }
        },
        methods: {
            initPage() {
                //加载dff配置
                this.initDffConfig();
                this.initTable();
            },
            initTable() {
                //这里是查询数据了，
                let pageNo = 1;
                let pageSize = 5;
                //为了方便后端时区处理，所有的date类型的查询条件都加上时分秒
                let param = {dynamicForm:{},general:{},sortBy:'',sortOrder:'',pageNo,pageSize}
                this.tableData = [];
                this.tableDataLoading = true;
                trfListTop5Query(param).then(res=>{
                    this.tableDataLoading = false;
                    if(res.status==200){
                        let {data} = res.data;
                        let {content} = data;
                        this.tableData = content;
                    }
                },error=>{
                    this.tableDataLoading = false;
                    console.log("查询返回结果 error：",error);
                    //this.$notify.error(error)
                }).catch(err=>{
                    this.tableDataLoading = false;
                    console.log("查询返回结果 err：",err);
                })
            },
            initDffConfig(){
                let param = {
                    productLineCode : this.userInfo.productLineCode || 'all'
                }
                trfListDimensionQuery(param).then(async (res)=>{
                    let {data} = res.data;
                    if(!data){
                        return;
                    }
                    let {dffList} = data;
                    dffList = dffList || [];
                    dffList.forEach((dff,dffIndex)=>{
                        let {fieldSeq} = dff;
                        if(fieldSeq=='null' || !fieldSeq || fieldSeq=='undefined'){
                            fieldSeq = 999;
                        }
                        dff.fieldSeq = fieldSeq;
                    })
                    dffList.sort((a,b)=>a.fieldSeq-b.fieldSeq);
                    this.dffColumnList = dffList.slice(0,3);
                }).catch(err=>{
                    console.log("dimensionQuery err",err)
                })
            }
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {
            ...mapGetters(["userInfo", "permission", "language", "menu", "dimensions"]),
        },
        props: {},
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_index_HomePageTop5 {
        font-family: 'Arial' !important;
        background: #fff;
    }
</style>