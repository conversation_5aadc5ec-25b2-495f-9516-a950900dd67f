<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_template_templateField" id="sgs_smart_customer_template_templateField">
            <el-form :model="fieldForm" size="mini" ref="addTemplateForm" label-position="left" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="Sequence" prop="sequence" :rules="{ required: true, message: 'Sequence is required', trigger: ['blur','change']}">
                            <el-input-number v-model="fieldForm.sequence" controls-position="right":min="1"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="Field Code" prop="fieldCode" :rules="{ required: true, message: 'Field Code is required', trigger: ['blur','change']}">
                            <el-select filterable
                                       style="width: 100%"
                                       v-model="fieldForm.fieldCode"
                                       @change="changeDff"
                            >
                                <el-option
                                    v-for="(dff,dffInd) in dffList"
                                    :key="'dff_'+dffInd"
                                    :label="dff.code"
                                    :value="dff.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="18">
                        <el-form-item label="Field Name" prop="dispalyName" :rules="{ required: true, message: 'Field Name is required', trigger: ['blur','change']}">
                            <el-input clearable v-model="fieldForm.dispalyName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-select size="mini" v-model="lanSelect" disabled style="width: 100%" >
                            <el-option label="English" value="english"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="2">
                        <i class="el-icon-plus menu-icon" @click="addLanguage"></i>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-for="(lan,index) in fieldForm.displayNameMultLanguage">
                    <el-col :span="18">
                        <el-form-item label="Field Name">
                            <el-input clearable v-model="lan.displayName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-select size="mini" v-model="lan.languageCode" style="width: 100%">
                            <el-option label="Chinese" value="CHI"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="2">
                        <i class="el-icon-delete menu-icon" @click="fieldForm.displayNameMultLanguage.splice(index,1)"></i>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="Mandatory" prop="mandatoryFlag">
                            <el-switch
                                    v-model="fieldForm.mandatoryFlag"
                                    :disabled="fieldForm.fieldType=='Percentage'"
                                    :active-value="1"
                                    :inactive-value="0"
                                    active-color="#FF6600"
                                    inactive-color="#cccccc">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="Check Unique" prop="formUniqueFlag">
                            <el-switch
                                    v-model="fieldForm.formUniqueFlag"
                                    :active-value="1"
                                    :inactive-value="0"
                                    active-color="#FF6600"
                                    inactive-color="#cccccc">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="Display in list" prop="displayedInFrontEndFlag">
                            <el-switch
                                    v-model="fieldForm.displayedInFrontEndFlag"
                                    :active-value="1"
                                    :inactive-value="0"
                                    active-color="#FF6600"
                                    inactive-color="#cccccc">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="16">
                        <el-form-item label="Input Type" prop="fieldType" :rules="{ required: true, message: 'Input Type is required', trigger: ['blur','change']}">
                            <el-select filterable
                                       style="width: 100%"
                                       v-model="fieldForm.fieldType"
                                       @change="changeInputType"
                            >
                                <template v-if="!['countryOfOrigin','countryOfDestination'].includes(fieldForm.fieldCode)">
                                    <el-option
                                            v-for="(op,ind) in filedTypeList"
                                            :key="'ft_'+ind"
                                            :label="op.label"
                                            :value="op.value"
                                    ></el-option>
                                </template>
                                <template v-if="['countryOfOrigin','countryOfDestination'].includes(fieldForm.fieldCode)">
                                    <el-option label="Single Select" value="Select"></el-option>
                                    <el-option label="Multi Select" value="Select2"></el-option>
                                </template>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="['Select','Select2'].includes(fieldForm.fieldType) && !['countryOfOrigin','countryOfDestination'].includes(fieldForm.fieldCode)">
                        <el-form-item label="Inputable" prop="inputable" >
                            <el-switch
                                    v-model="fieldForm.inputable"
                                    :active-value="1"
                                    :inactive-value="0"
                                    active-color="#FF6600"
                                    inactive-color="#cccccc">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="!['Input','TextArea','Date','DateRange','Percentage'].includes(fieldForm.fieldType) && !['countryOfOrigin','countryOfDestination'].includes(fieldForm.fieldCode)">
                    <el-col>
                        <el-form-item label="List of Values"
                                      prop="listOfValuesDataList"
                                      :rules="{ required: true, message: 'List of Values is required'}">
                            <el-table
                                size="mini"
                                :data="fieldForm.listOfValuesDataList"
                                border
                                max-height="400"
                                fit
                            >
                                <el-table-column type="index" width="40" header-align="center">
                                    <template slot="header">
                                        <i class="el-icon-circle-plus-outline" style="cursor: pointer;font-size: 16px;color:#FF6600" @click="addListOfValues"></i>
                                    </template>
                                </el-table-column>
                                <el-table-column label="Name" prop="name">
                                    <template slot-scope="{row,$index}">
                                        <el-form-item :rules="{ required: true, message: 'Name is required', trigger: ['blur','change']}"
                                                      :prop="'listOfValuesDataList.'+$index+'.name'">
                                            <el-input v-model="row.name" size="mini" clearable></el-input>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column label="Code" prop="code">
                                    <template slot-scope="{row,$index}">
                                        <el-form-item :rules="{ required: true, message: 'Code is required', trigger: ['blur','change']}"
                                                      :prop="'listOfValuesDataList.'+$index+'.code'">
                                            <el-input v-model="row.code" size="mini" clearable></el-input>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column label="" width="50" align="center">
                                    <template slot-scope="{row,$index}">
                                        <el-popconfirm
                                                v-if="fieldForm.listOfValuesDataList.length>1"
                                                confirm-button-text='Delete'
                                                cancel-button-text='Cancel'
                                                icon="el-icon-info"
                                                icon-color="red"
                                                title="Delete the data?"
                                                @confirm="delListOfValues(row)"
                                        >
                                            <i slot="reference"  style="cursor: pointer;color:#ff6600;padding:2px 5px" class="el-icon-delete"></i>
                                        </el-popconfirm>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col>
                        <el-form-item label="Input tips" prop="tips">
                            <el-input clearable placeholder='Input tips' v-model="fieldForm.tips"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-form :model="fieldForm" v-if="false" size="mini" label-position="left" label-width="160px">
                <el-row :gutter="20">
                    <el-col>
                        <el-collapse v-model="openCollapse">
                            <el-collapse-item name="1">
                                <template slot="title">
                                    <i class="header-icon el-icon-circle-plus-outline"></i>Advanced Settings
                                </template>
                                <el-row>
                                    <el-col>Input Rules:</el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="Data Type">
                                            <el-select
                                                    clearable
                                                    filterable
                                                    v-model="DataType.ruleValue"
                                            >
                                                <el-option label="0-9" value="^[0-9]"></el-option>
                                                <el-option label="A-Z" value="^[a-z]"></el-option>
                                                <el-option label="0-9/A-Z" value="^[0-9A-Za-z]"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="Digits Limits">
                                            <el-input clearable v-model="DigitsLimit.ruleValue"  oninput="this.value=this.value.replace(/\D/g,'')"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="Mandatory When">
                                            <el-select
                                                    filterable
                                                    clearable
                                                    v-model="MandatoryWhen.refFieldCode">
                                                <el-option
                                                        v-for="(f,indx) in otherFieldList"
                                                        :key="'fie_'+indx"
                                                        :value="f.fieldCode"
                                                        :label="f.dispalyName"
                                                ></el-option>
                                            </el-select>
                                            <el-select
                                                    filterable
                                                    clearable
                                                    v-model="MandatoryWhen.condition">
                                                <el-option label="equals to" value="eq"></el-option>
                                                <el-option label="contains" value="contains"></el-option>
                                            </el-select>
                                            <el-input clearable v-model="MandatoryWhen.ruleValue"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-collapse-item>
                        </el-collapse>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </basic-container>
</template>

<script>
    import _ from 'lodash'
    export default {
        name: "templateField",
        data() {
            return {
                dffList:[],
                pageLoading: false,
                openCollapse:"",
                lanSelect:"english",
                otherFieldList:[],
                DataType: {label:'Data Type', ruleCode: "DataType", refFieldCode: "", ruleValue: ""},
                DigitsLimit:{label: "Digits Limit",ruleCode: "DigitsLimit",refFieldCode: "",ruleValue: ""},
                MandatoryWhen:{label: "Mandatory When",ruleCode: "MandatoryWhen",refFieldCode: "",ruleValue: "",condition:''},
                filedTypeList:[
                    {label:"Text" ,value:"Input"},
                    {label:"Text Area", value:"TextArea"},
                    {label:"Single Select" ,value:"Select"},
                    {label:"Multi Select", value:"Select2"},
                    {label:"Radio button", value:"Radio"},
                    {label:"Checkbox" ,value:"Checkbox"},
                    {label:"Date" ,value:"Date"},
                    {label:"Date Range", value:"DateRange"},
                    {label:"Percentage", value:"Percentage"},
                ],
                fieldForm:{
                    id:'',
                    sequence:1,
                    fieldCode:'',
                    dispalyName:'',
                    mandatoryFlag:0,
                    formUniqueFlag:0,
                    displayedInFrontEndFlag:0,
                    searchable:1,
                    fieldType:'Input',
                    inputable:0,
                    listOfValuesDataList:[],
                    sourceValues:[],
                    tips:'',
                    rulesConfig:[],
                    displayNameMultLanguage:[],
                    languageList:[],
                    relId:'',//field 配置项的id,用于新增
                    sourceValueLanguage:[
                        {
                            languageCode:'EN',
                            sourceValue:[]
                        }
                    ]
                },
            }
        },
        methods: {
            changeListValues(){
                this.fieldForm.listOfValuesDataList = [];
                if(['countryOfOrigin','countryOfDestination'].includes(this.fieldForm.fieldCode)){
                    //远程加载list
                    this.fieldForm.fieldType = 'Select';
                }
            },
            changeDff(){
                let {labelName,labelNameCN,fieldType,isMandatory,tips,dataSource,dataSourceCN,sequenceNo,id} = this.dffList.find(dff=>dff.code==this.fieldForm.fieldCode) || {};
                this.fieldForm.dispalyName = labelName;
                this.fieldForm.languageList = [];
                this.fieldForm.displayNameMultLanguage = [];
                this.fieldForm.mandatoryFlag = isMandatory;
                this.fieldForm.fieldType = fieldType;
                this.fieldForm.inputable = 0;
                this.fieldForm.tips = '';
                //this.fieldForm.sequence = !!sequenceNo?sequenceNo-0:1;
                this.changeListValues();
                this.fieldForm.listOfValuesDataList = dataSource;
            },
            initOtherFiled(){
                let {fieldCode} = this.fieldObj || {};
                let otherFieldList = this.allFieldConfig.filter(c=>c.fieldCode!=fieldCode);
                this.otherFieldList = otherFieldList;
            },
            initStandardDff(){
                let dffList = [];
                this.standardDffList.forEach(dff=>{
                    let {customerFieldCode,customerLabelNameCN,customerLabelNameEN,
                        fieldType,formType,sequenceNo ,dataSource} = dff;
                    let code = _.lowerFirst(customerFieldCode);
                    let tips = '';
                    let dataSourceEN =(dataSource || '').split("|").map(da=>{
                        let obj = {
                            id:Math.random(),
                            name:da,
                            code:da
                        }
                        return obj;
                    });
                    let dataSourceCN = dataSourceEN;
                    let isMandatory = 0;
                    let labelName = customerLabelNameEN;
                    let labelNameCN = customerLabelNameCN;
                    fieldType = fieldType || 'Input';
                    dffList.push({
                        code,
                        labelName,
                        labelNameCN,
                        fieldType,
                        sequenceNo,
                        isMandatory,
                        tips,
                        dataSource:dataSourceEN,
                        dataSourceCN
                    })
                })
                //当前已经选中的code
                let selectedFieldCode = this.allFieldConfig.map(c=>c.fieldCode);
                //如果是编辑状态，则排除当前code
                let {fieldCode} = this.fieldObj || {};
                let selectedOtherCode = selectedFieldCode.filter(a=>a!=fieldCode);
                //标准字段排除已经选中的code
                dffList = dffList.filter(d=>!selectedOtherCode.includes(d.code));
                this.dffList = dffList;
            },
            handlerSourceValue(sourceValue){
                let listOfValuesDataList = [];
                if(!sourceValue || sourceValue.length==0){
                    return listOfValuesDataList;
                }
                try{
                    listOfValuesDataList = sourceValue.map(v=>JSON.parse(v));
                }catch (e) {}
                return listOfValuesDataList;
            },
            initPage() {
                if(!this.fieldObj || !this.fieldObj.fieldCode){
                    this.fieldForm.sequence = this.allFieldConfig.length+1;
                    this.fieldForm.relId = this.fieldObj.relId;
                    return;
                }
                let copyFieldObj = JSON.parse(JSON.stringify(this.fieldObj))
                let {sourceValueLanguage,languageList,displayNameMultLanguage} = copyFieldObj;
                if(!languageList){
                    languageList = [];
                }
                if(!displayNameMultLanguage){
                    displayNameMultLanguage = [];
                }
                console.log("sourceValueLanguage",sourceValueLanguage);
                if(!sourceValueLanguage || sourceValueLanguage.length==0){
                    sourceValueLanguage = [
                        {
                            languageCode:'EN',
                            sourceValue:[]
                        }
                    ]
                }
                let {sourceValue} = (sourceValueLanguage || []).find(s=>s.languageCode=='EN') || {};
                let listOfValuesDataList = this.handlerSourceValue(sourceValue);
                (copyFieldObj.rulesConfig || []).forEach(r=>{
                    let {ruleCode,ruleValue} = r;
                    if(!!ruleValue){
                        this.openCollapse = "1";
                    }
                    this.$set(this,ruleCode,r);
                })
                this.fieldForm = Object.assign({},copyFieldObj,{listOfValuesDataList,sourceValueLanguage,languageList,displayNameMultLanguage});
                if(listOfValuesDataList.length==0){
                    this.addListOfValues();
                }
            },
            changeInputType(){
                this.fieldForm.sourceValueLanguage = this.fieldForm.fieldType=='text'?[]:[ {
                    languageCode:'EN',
                    sourceValue:[]
                }];
                if(this.fieldForm.fieldType=='Percentage'){
                    this.$set(this.fieldForm,'mandatoryFlag',1);
                }
            },
            addLanguage(){
                if(this.fieldForm.displayNameMultLanguage &&  this.fieldForm.displayNameMultLanguage.length>0){
                    return
                }
                if(!this.fieldForm.displayNameMultLanguage){
                    this.fieldForm['displayNameMultLanguage'] = [];
                }
                this.fieldForm.displayNameMultLanguage.push({
                    "languageCode": "CHI",
                    "displayName": ""
                })

            },
            getSaveData(){
                return new Promise(resolve => {
                    this.$refs['addTemplateForm'].validate(valid=>{
                        if(!valid){
                            //this.$notify.warning('Necessary info is not completed')
                            resolve(false);
                            return
                        }
                        let length = (this.fieldForm.listOfValuesDataList || []).length;
                        let codes = new Set((this.fieldForm.listOfValuesDataList || []).map(da=>da.code));
                        if(codes.size!=length){
                            this.$notify.error('List of values Code cannot be duplicated');
                            resolve(false);
                            return;
                        }

                        this.fieldForm.rulesConfig = [];
                        let sourceValueArr = this.fieldForm.listOfValuesDataList.map(v=>JSON.stringify(v));
                        this.fieldForm.sourceValueLanguage[0].sourceValue = sourceValueArr;
                        delete this.fieldForm.listOfValuesDataList;
                        this.fieldForm.rulesConfig.push(this.DataType);
                        this.fieldForm.rulesConfig.push(this.DigitsLimit);
                        this.fieldForm.rulesConfig.push(this.MandatoryWhen);

                        resolve(this.fieldForm);
                    })
                })
            },
            addListOfValues(){
                let obj = {
                    id:Math.random(),
                    name:'',
                    code:''
                }
                this.fieldForm.listOfValuesDataList.push(obj);
            },
            delListOfValues(row){
                let {id} = row;
                let index = this.fieldForm.listOfValuesDataList.findIndex(l=>l.id==id);
                if(index>-1){
                    this.fieldForm.listOfValuesDataList.splice(index,1);
                }
            }
        },
        mounted() {
        },
        created() {
            this.initOtherFiled();
            this.initStandardDff();
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            fieldObj:{},
            standardDffList:[],
            allFieldConfig:[]
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_customer_template_templateField {
        font-family: 'Arial' !important;
        background: #fff;
        padding: 24px 32px;
        max-height: 600px;
        overflow-y: auto;

        .menu-icon{
            font-size: 20px;
            cursor: pointer;
            margin: 0 10px;
            color:#ff6600
        }
    }
</style>