import request from '@/router/axios';




export const add = (form) => {
    return request({
        url: '/api/sgs-mart/vipProgram/submit',
        method: 'post',
        data: form
    })
}

export const getList = (data) => {
    debugger;
    return request({
        url: '/api/sgs-mart/vipProgram/page',
        method: 'post',
        data: data
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/vipProgram/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/vipProgram/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}


export const getPrograms = (data) => {
    return request({
        url: '/api/sgs-mart/vipProgram/list',
        method: 'post',
        data: data
    })
}