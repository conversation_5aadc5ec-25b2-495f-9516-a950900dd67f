<template>
  <div v-if="listVisible" v-loading="trfLoading">
    <el-row>
      <el-col :span="4">
        <el-autocomplete v-model="form.properties.queryValue" style="{}"
                         ref="autocomplete"
                         placeholder="TRF Number"
                         :fetch-suggestions="selectTRFList"
                         :trigger-on-focus="false"
                         filterable="false"
                         @focus="searchFocus"
                         clearable>
          <i slot="prefix" class="el-input__icon el-icon-search"
             style="left: -10px;position: relative; color: #000; font-weight: bold;"></i>
        </el-autocomplete>
        <el-input v-if="false" placeholder="TRF Number"
                  v-model.trim="form.properties.queryValue"
                  @keyup.enter.native="selectTRFList"
                  clearable>
          <i slot="prefix" class="el-input__icon el-icon-search"
             style="left: -10px;position: relative;"></i>
        </el-input>
      </el-col>
      <el-col :span="2" v-if="false">
        <el-button @click="selectTRFList" style="margin-left: 15px" type="primary">Search</el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          highlight-current-row
          @row-click="searchPP"
          :data="form.list" style="width: 100%" >
          <el-table-column :label="$t('trfList.trfNo')" :show-overflow-tooltip="true" header-align="center" width="120"
                           align="center">
            <template slot-scope="scope">
              <a v-text="scope.row.trf_trfNo" style="cursor: pointer;color: #f60"></a>
            </template>
          </el-table-column>

          <el-table-column :label="$t('trfList.jobStatus')" :show-overflow-tooltip="true" header-align="center"
                           align="center" width="200">
            <template slot-scope="scope">
              <div :data-row-key="scope.row.id"
                   :key="scope.row.id"
                   >
                <TRFStatus :status="(scope.row.trf_header_trfStatus-0)" />
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('trfList.applyCustomer')" prop="trf_customer_applicantNameEn"
                           header-align="center" align="center" width="150"
                           :show-overflow-tooltip="true"></el-table-column>
          <el-table-column :label="$t('trfList.submitDate')" prop="trf_header_trfSubmissionDate" header-align="center"
                           :render-header="renderheader"
                           align="center" width="180"
                           :show-overflow-tooltip="true">
            <template slot-scope="{row}">
              {{getDateText(row.trf_header_trfSubmissionDate)}}
            </template>
          </el-table-column>
          <el-table-column :label="$t('trfList.buyer')" prop="trf_customer_buyerCustomerNameEn"
                           :show-overflow-tooltip="true" header-align="center" align="center"></el-table-column>
          <el-table-column :label="$t('trfList.labName')" prop="trf_lab_labName" :show-overflow-tooltip="true"
                           header-align="center" align="center"></el-table-column>
          <el-table-column :label="$t('trfList.sampleDate')"
                           prop="trf_header_sampleReceiveDate"
                           width="95"
                           header-align="center" align="center" :render-header="renderheader"
                           :show-overflow-tooltip="true">
              <template slot-scope="{row}">
                {{getDateText(row.trf_header_sampleReceiveDate)}}
              </template>
          </el-table-column>
          <el-table-column :label="$t('trfList.agent')"  prop="trf_customer_agentCustomerNameEn" :show-overflow-tooltip="true"
                           header-align="center" align="center"></el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <div style="text-align: center">
      <el-pagination
        layout="prev, pager, next"
        :page-size="form.pageSize"
        @current-change="handleCurrentChange"
        :total="form.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  import { getDataEntryTrfList } from '../../../api/trf/dataEntryTrf'
  import {mapGetters} from "vuex";
  import moment from 'moment'

  export default {
    name: 'TrfList',
    components: {
      TRFStatus: resolve => require(['../../../components/trf/TRFStatus'], resolve),
    },
    computed: {
      ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
    },
    data() {
      return {
        publicPath: 'static/',
        timeout: null,
        trfLoading: false,
        queryModel:{
          "dynamicForm": {
            "trf_header_trfStatusName": {
              "condition": "in",
              "conditionValue": [
                "Application Accepted",
                "Testing",
                "Report Issued"
              ]
            }
          },
          "general": {
            "queryValue": "",
            "isSubmissionTimeout": 0,
            "currentDate": -1,
            "productLineCode": "all",
            "showCollapseFilter": false
          },
          "pageSize": 20,
          "pageNo": 1
        },
        form: {
          pageNo: 1,
          pageSize: 10,
          list: [],
          total: 0,
          properties: {
              queryValue: ''
          }

        }
      }
    },
    props: {
      listVisible: {
        type: Boolean,
        default: () => {
        }
      }
    },
    methods: {
      getDateText(val){
        let text = val;
        try{
          text = moment(val).format("YYYY-MM-DD")
        }catch (e) {}
        return text;
      },
      renderheader(h, { column, $index }) {
        return h('span', {}, [
          h('span', {}, column.label.split('/')[0]),
          h('br'),
          h('span', {}, column.label.split('/')[1])
        ])
      },
      searchFocus() {
        this.$refs.autocomplete.activated = false;
      },
      async init() {
        //07-14首次加載時不加载TRF List
        if(this.form.properties.queryValue && this.form.properties.queryValue.toLowerCase() !== 'trf'
            && this.form.properties.queryValue.toLowerCase().replace('trf', '').length >= 3
         ) {
          this.trfLoading = true;
          let param = Object.assign({},this.queryModel,{
            "pageSize":this.form.pageSize,
            "pageNo": this.form.pageNo
          })
          getDataEntryTrfList(param).then(res => {
            console.log("res,",res);
            if(res.data.code === 200) {
              let result = res.data.data;
              this.form.list = result.content;
              this.form.total = result.totalElements;
            }
          }).finally(() => {
            this.trfLoading = false;
          });

        }
      },
      toSgsMart(row) {
        //window.open(process.env.SMART_API + '/#/trf/edit/' + row.id + '?from=1&isReportFlag=true', '_blank');
        window.open(process.env.SMART_API + '/#/trf/edit/' + row.id + '?from=1&isReportFlag=true', '_blank');

      },
      handleCurrentChange(val) {
        this.form.pageNo = val
        this.init()
      },
      async selectTRFList() {
        if(!this.form.properties.queryValue) {
          return;
        }
        let val = this.form.properties.queryValue;
        this.queryModel.general.queryValue = val;
        this.form.pageNo = 1;
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
          this.init();
        }, 2000 * Math.random());
        return [];
      },
      searchPP(val) {
        this.$emit('trfNoClick', val)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
</style>
