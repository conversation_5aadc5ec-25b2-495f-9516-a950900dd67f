const getters = {
    tag: state => state.tags.tag,
    language: state => state.common.language,
    website: state => state.common.website,
    userInfo: state => state.user.userInfo,
    companyId: state => state.user.userInfo.companyId,
    colorName: state => state.common.colorName,
    themeName: state => state.common.themeName,
    isShade: state => state.common.isShade,
    isCollapse: state => state.common.isCollapse,
    keyCollapse: (state, getters) => getters.screen > 1 ? getters.isCollapse : false,
    screen: state => state.common.screen,
    isLock: state => state.common.isLock,
    isFullScren: state => state.common.isFullScren,
    lockPasswd: state => state.common.lockPasswd,
    tagList: state => state.tags.tagList,
    tagWel: state => state.tags.tagWel,
    token: state => state.user.token,
    roles: state => state.user.roles,
    permission: state => state.user.permission,
    dimensions: state => state.user.dimensions,
    posts: state => state.user.posts,
    menu: state => state.user.menu,
    menuAll: state => state.user.menuAll,
    menuMap: state => state.user.menuMap,
    logsList: state => state.logs.logsList,
    logsLen: state => state.logs.logsList.length || 0,
    logsFlag: (state, getters) => getters.logsLen === 0,

    //dff
    trfTemplate: (state, getters) => {
        let dffTemplate = _.filter(_.get(state, 'dff.trfTemplate', []), item => {
            return !item.templateName.includes('自测') && !item.templateName.toLowerCase().includes('selftest')
        })
        return dffTemplate
    },
    // 获取接收的信息
    socketMsgs: state => state.chat.msg,
    webSocketPingTimer:state =>state.chat.webSocketPingTimer,
    userId:state=>state.chat.userId,
    trfNo:state=>state.chat.trfNo,
    firstOpenTime:state=>state.chat.firstOpenTime,
    // imGroupUser:state=>state.chat.imGroupUser,
    // imGroupUsers:state=>state.chat.imGroupUsers,
    // hideBtn:state=>state.chat.hideBtn,
    socketTask:state=>state.chat.socketTask,
    webSocketReconnectCount:state=>state.chat.webSocketReconnectCount,
}
export default getters
