<template>
    <div class="container" v-if="specificConfig && specificConfig.length > 0">
        <el-card class="card" v-for="(item, index) in specificConfig" :key="index">
            <div class="sgs-group">
                <h3>{{ language == 'zh-CN' ? item.specificNameCn : item.specificNameEn}}</h3>
            </div>
            <div v-html="language == 'zh-CN' ? item.contentCn[item.extendSpecificCode] : item.contentEn[item.extendSpecificCode]"></div>
        </el-card>
    </div>
</template>

<script>
    import { getSpecificConfig } from '@/api/trf/specific';
    import {mapGetters} from "vuex";
    export default {
        name: "TRFSpecificExtend",
        props: {
            trfId: {
                type: String,
                default: ""
            },
            templateId: {
                required: true,
                type: Number
            }
        },
        computed: {
            ...mapGetters(["language"]),
        },
        watch: {
            templateId: function (val) {
                this.loadSpecificExtendConfig();
            }
        },

        data() {
            return {
                specificConfig: [],
                extendMenus: [],
                previousTemplateId: ''
            }
        },

        mounted() {
            this.loadSpecificExtendConfig();
        },
        methods: {
            loadSpecificExtendConfig() {
                if(this.templateId == this.previousTemplateId) return;
                getSpecificConfig(Object.assign({'trfId': this.trfId, 'templateId': this.templateId})).then(res => {
                    this.previousTemplateId = this.templateId;
                    this.specificConfig = res.data.data;
                    if(!this.specificConfig) return;
                    this.extendMenus = [];
                    this.specificConfig.forEach(item => {
                        item.contentEn = item.contentEn ? JSON.parse(item.contentEn) : item.contentEn;
                        item.contentCn = item.contentCn ? JSON.parse(item.contentCn) : item.contentCn;
                        if(item.isMenu != 0) {
                            this.extendMenus.push({
                                alias: null,
                                specificNameCn: item.specificNameCn,
                                specificNameEn: item.specificNameEn,
                                isExtend: true,
                                isSub: false
                            })
                        };
                    });
                    this.$emit("showMenus", this.extendMenus);
                });
            }
        }
    }
</script>

<style lang="scss" scoped>
    .container {
      width: 100%;
      background-color: white;
    }
    .card {
      width: 100%;
      border-style: none;
      height: auto;
    }
</style>
