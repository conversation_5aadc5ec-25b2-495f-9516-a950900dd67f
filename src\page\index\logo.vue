<template>
  <div class="sgs-logo">
      <router-link :to="{path:'/'}">
        <span class="sgs-logo_subtitle" key="0">
            <img src="/img/SGS_SMART_logo.png"  width="80" />
          </span>
      </router-link>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "logo",
  data() {
    return {};
  },
  created() {},
  computed: {
    ...mapGetters(["website", "keyCollapse"])
  },
  methods: {}
};
</script>

<style lang="scss">
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.sgs-logo {
  top: 0;
  left: 0;
  width: 100px;
  height: 80px;
  line-height: 80px;
  font-size: 20px;
  overflow: hidden;
  z-index: 1024;
  margin-top: 12px;
}
</style>
