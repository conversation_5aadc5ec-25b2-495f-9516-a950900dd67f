<template>
  <div>
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
    />
    <Editor
      style="min-height: 400px; overflow-y: hidden"
      :value="valueHtml"
      @onChange="handleEditorChange"
      :defaultConfig="editorConfig"
      @onCreated="handleEditorCreated"
    />
  </div>
</template>

<script setup>
import { ref, shallowRef, onBeforeUnmount, watch } from 'vue'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { i18nChangeLanguage } from '@wangeditor/editor'

const props = defineProps({
  editorContent: {
    type: String,
    default: '',
  },
})
const valueHtml = ref(props.editorContent)

const emits = defineEmits(['update:editorContent'])

// 定义 editor 变量
const editorRef = shallowRef()

const toolbarConfig = ref({
  toolbarKeys: [
    'bold',
    'underline',
    'italic',
    'through',
    'sub',
    'sup',
    'clearStyle',
    'color',
    'bgColor',
    'fontSize',
    'fontFamily',
    'indent',
    'delIndent',
    'justifyLeft',
    'justifyRight',
    'justifyCenter',
    'justifyJustify',
    'divider',
    'bulletedList',
    'numberedList',
    'headerSelect',
    'redo',
    'undo',
  ],
})

// 配置编辑器
const editorConfig = ref({
  placeholder: 'please input content...',
  // 其他配置项
})

// 编辑器创建完成后的回调函数
const handleEditorCreated = (editorInstance) => {
  i18nChangeLanguage('en')
  editorRef.value = editorInstance
}

watch(
  () => props.editorContent,
  (newValue) => {
    editorRef.value.setHtml(newValue)
    valueHtml.value = newValue
  },
)

// 编辑器内容改变时触发 update:editorContent 事件
const handleEditorChange = (newValue) => {
  emits('update:editorContent', newValue.getHtml())
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
</script>

<style scoped>
/* 可以添加编辑器的样式 */
</style>
