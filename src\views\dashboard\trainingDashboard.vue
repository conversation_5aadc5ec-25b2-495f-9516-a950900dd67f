<template>
    <basic-container>
        <el-card class="sgs-box">
            <el-form ref="form" :model="form" label-width="180px" class="demo-ruleForm" size="small">
                <el-row>
                    <!--<el-col :span="3">
                      <el-button size="small" type="primary">{{ $t('training.Supplier') }}</el-button>
                    </el-col>
                    <el-col :span="3">
                      <el-button size="small" type="primary">{{ $t('training.InternalStaff') }}</el-button>
                    </el-col>-->
                    <el-col :span="21">
                    </el-col>
                    <el-col :span="3">
                        <el-button size="small" type="primary" @click="toClassList">{{ $t('training.ClassList') }}
                        </el-button>
                    </el-col>
                </el-row>
                <el-row>
                    <el-collapse v-model="activeNames" @change="handleChange">
                        <el-collapse-item :title="$t('training.filter')" name="1">
                            <el-form ref="formSelect" :model="formSelect" label-width="180px" class="demo-ruleForm"
                                     size="small">
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.Supplier')" prop="supplierName">
                                            <el-input v-model="formSelect.traineeCustomerName"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.fsCode')" prop="supplierCode">
                                            <el-input v-model="formSelect.supplierCode"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.TrainingLocation')" prop="trainingLocation">
                                            <el-select
                                                    v-model="formSelect.trainingLocation"
                                                    multiple
                                                    style="width: 100%"
                                                    :placeholder="$t('training.TrainingLocation')"
                                                    :remote-method="remoteMethod"
                                                    :loading="loading">
                                                <el-option
                                                        v-for="item in list"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.TrainingLevel')" prop="courseLevel">
                                            <el-select v-model="formSelect.courseLevel" @change="selectLevelChange"
                                                       :placeholder="$t('training.TrainingCourseLevel')"
                                                       style="width: 100%">
                                                <el-option v-for="item in courseLevelList" :key="item.id"
                                                           :lable="item.id" :value="item.name"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.year')" prop="year">
                                            <!-- <el-date-picker
                                                    v-model="formSelect.year"
                                                    type="year"
                                                    style="width: 100%"
                                                    value-format="yyyy"
                                                    >
                                            </el-date-picker>-->
                                            <el-select v-model="formSelect.year" multiple @change="selectLevelChange"
                                                       :placeholder="$t('training.year')" style="width: 100%">
                                                <el-option v-for="item in yearList" :key="item.id" :lable="item.id"
                                                           :value="item.id"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.className')" prop="moduleName">
                                            <el-select
                                                    v-model="formSelect.moduleName"
                                                    style="width: 100%"
                                                    multiple
                                                    collapse-tags
                                                    :placeholder="$t('select')"
                                                    @change="trainingClassListChange"
                                            >
                                                <el-option v-for="item in trainingClassList" :key="item.id"
                                                           :lable="item.id" :value="item.name">
                                                    <span>{{ item.name }}</span>
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12">
                                        <el-form-item :label="$t('training.tier')" prop="tier">
                                            <el-input v-model="formSelect.tier"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item :label="$t('department.title.default')">
                                            <el-select v-model="formSelect.departmentIds" multiple
                                                       style="width: 100%">
                                                <el-option
                                                        v-for="dept in departmentList"
                                                        :key="dept.id"
                                                        :label="dept.departmentName"
                                                        :value="dept.id"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="16"></el-col>
                                    <el-col :span="4">
                                        <el-button size="small" type="primary" @click="onLoad">{{ $t('operation.search')
                                            }}
                                        </el-button>
                                        <el-button size="small" type="primary" @click="exportExcel">
                                            {{$t('customerMaterialConfig.exportExcel')}}
                                        </el-button>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-collapse-item>
                    </el-collapse>
                </el-row>
            </el-form>
        </el-card>
        <el-card class="sgs-box">
            <el-row>
                <el-col :span="24">
                    <div id="chart1" style="width: 100%"></div>
                </el-col>
            </el-row>
            <el-row v-for="item in echartsDataLoaction">
                <el-col :span="24">
                    <div :id="item.locationForId" class="echart" style="width: 100%"></div>
                </el-col>
            </el-row>
        </el-card>

        <el-table :data="excelData" stripe style="width: 0px;height:0px" type="hidden" id="exportTable">
            <el-table-column label="Company" prop="traineeCustomerName" width="120"></el-table-column>
            <el-table-column label="Name" prop="traineeUserName" width="120"></el-table-column>
            <el-table-column label="Register" prop="isRegistered" :formatter="registerFormtter"></el-table-column>
            <el-table-column label="Enrolled" prop="isEnrolled" :formatter="enrolledFormatter"></el-table-column>
            <el-table-column label="Result" prop="isPass" :formatter="passFormatter"></el-table-column>
            <el-table-column label="Score" prop="score"></el-table-column>
            <el-table-column label="Remark" prop="remark"></el-table-column>
            <el-table-column label="Program" prop="courseTitle"></el-table-column>
            <el-table-column label="Level" prop="courseLevel"></el-table-column>
            <el-table-column label="Module" prop="moduleName"></el-table-column>
            <el-table-column label="Training Date" prop="trainingDate" :formatter="formtterDate"></el-table-column>
            <el-table-column label="Year" prop="year"></el-table-column>
            <el-table-column label="Location" prop="trainingLocation"></el-table-column>
            <el-table-column label="Supplier Code" prop="supplierCode"></el-table-column>
            <el-table-column label="Tier" prop="tier"></el-table-column>
            <el-table-column label="Department Name" prop="departmentName"></el-table-column>
        </el-table>


    </basic-container>
</template>


<script>

    import {getPage, selectTrainingClassList} from "@/api/training/performance";
    import {selLocationList} from "@/api/training/class";
    import {Get_CourseLevel} from "@/api/training/course";

    import {getDepts} from "@/api/customer/customerDept";
    import moment from 'moment'

    //import myCharts from "@/api/training/performanceEcharts";
    //import {install} from "@/api/training/performanceEcharts";
    //import echarts from 'echarts'
    import XLSX from "xlsx";
    import FileSaver from "file-saver";
    import {mapGetters} from "vuex";
    //Vue.use(myCharts);
    export default {
        name: 'trainingDashboard',
        data() {
            return {
                yearList: [],
                departmentList: [],
                formSelect: {
                    /* year:new Date(),*/
                    moduleId: [],
                    courseLevel: 'Foundational',

                    /*year:2020*/
                },
                value: [],//location 使用
                list: [],//location 使用
                loading: false,//location 使用
                states: [],//location 使用
                courseLevelList: "",
                trainingClassList: "",
                echartsData: {},
                echartsDataLoaction: [],
                excelData: [],
            }
        },
        computed: {
            ...mapGetters(["permission", "userInfo"]),
        },
        methods: {

            initDepartment() {
                //获取当前登录用户的department
                const customerId = this.userInfo.companyId;
                getDepts(customerId).then(res => {
                    const data = res.data.data;
                    this.departmentList = data;
                });

            },
            passFormatter(row, column) {
                var passVal = row[column.property];
                var passValStr = "Pass"
                if(passVal==''){
                    passValStr='';
                }
                if (passVal == '0' || passVal === 0) {
                    passValStr = 'Fail';
                } else if (passVal == 2 || passVal == '2') {
                    passValStr = 'Not Attended';
                }
                return passValStr;
            },
            enrolledFormatter(row, column) {
                var enrolled = row[column.property];
                var enrolledStr = "Enrolled"
                if (enrolled == '0' || enrolled == 0) {
                    enrolledStr = 'NotEnrolled';
                }
                return enrolledStr;
            },

            registerFormtter(row, column) {
                var register = row[column.property];
                var registerStr = "Register"
                if (register == '0' || register == 0) {
                    registerStr = 'NotRegister';
                }
                return registerStr;
            },
            formtterDate(row, column) {
                var date = row[column.property];

                if (date == undefined || date == '') {
                    return ''
                }
                ;

                return moment(date).format("YYYY-MM-DD")
            },
            exportExcel() {
                debugger;
                this.$nextTick(() => {
                    var xlsxParam = {raw: true} // 导出的内容只做解析，不进行格式转换
                    var wb = XLSX.utils.table_to_book(document.querySelector('#exportTable'), xlsxParam)
                    console.log(wb);
                    /* get binary string as output */
                    var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'array'})
                    try {
                        FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), 'Training Results.xlsx')
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log(e, wbout)
                        }
                    }
                    return wbout
                })
            },

            onLoad(params = {}) {
                getPage(this.formSelect).then(res => {
                    debugger;
                    this.excelData = res.data.data.performanceExcelData;
                    this.echartsData = res.data.data.performance;
                    this.echartsDataLoaction = res.data.data.performanceLocation;
                    this.$set(this.echartsData, 'location', 'Overall  ' + this.formSelect.courseLevel + ' performance');
                    this.install('chart1', this.echartsData);
                    this.echartsDataLoaction.forEach(item => {
                        const vue = this;
                        var t = setTimeout(function () {
                            //console.log(item);
                            vue.install(item.locationForId, item);
                        }, "1000");
                    })
                });
            },
            install(id, globalEchartsData) {
                this.chart = echarts.init(document.getElementById(id));
                this.chart.clear();

                const optionData = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                        }
                    },
                    title: {
                        text: globalEchartsData.location
                    },
                    legend: {
                        //data: ['PASS', 'FAIL', 'NOT ATTENDED']
                        //data: this.echartsData.moduleName
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: globalEchartsData.moduleName,
                            axisLabel: {
                                interval: 0,
                                formatter: function (value) {
                                    var ret = "";//拼接加\n返回的类目项
                                    var maxLength = 14;//每项显示文字个数
                                    var valLength = value.length;//X轴类目项的文字个数
                                    var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                                    if (rowN > 1)//如果类目项的文字大于3,
                                    {
                                        for (var i = 0; i < rowN; i++) {
                                            var temp = "";//每次截取的字符串
                                            var start = i * maxLength;//开始截取的位置
                                            var end = start + maxLength;//结束截取的位置
                                            //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                                            temp = value.substring(start, end) + "\n";
                                            ret += temp; //凭借最终的字符串
                                        }
                                        return ret;
                                    } else {
                                        return value;
                                    }
                                },
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value'
                        }
                    ],
                    series: [
                        {
                            name: 'PASS',
                            type: 'bar',
                            stack: '广告',
                            data: globalEchartsData.totalOfPass,
                            itemStyle: {
                                normal: {color: "#62ff1d"},
                            },
                            barWidth: 30
                        },
                        {
                            name: 'FAIL',
                            type: 'bar',
                            stack: '广告',
                            data: globalEchartsData.totalOfFail,
                            itemStyle: {
                                normal: {color: "#ff2e34"},
                            }
                        },
                        {
                            name: 'NOT ATTENDED',
                            type: 'bar',
                            stack: '广告',
                            data: globalEchartsData.totalOfAttended,
                            itemStyle: {
                                normal: {color: "#c2bac4"},
                            }
                        }
                    ]
                };

                this.chart.setOption(optionData);
            },

            remoteMethod(locationName) {
                this.list = [];
                selLocationList({
                    locationName,
                }).then(res => {
                    debugger;
                    let aa = res.data.data;
                    if (aa) {
                        aa.map(item => {
                            this.list.push({
                                label: item.locationName,
                                //value:item.locationCode
                                value: item.locationName
                            })
                        })
                    }

                });
            },
            trainingClassListChange(val) {
                var obj = [];
                this.trainingClassList.find(function (item) {//obj是选中的对象
                    //console.log(item.name);
                    val.find(function (item2) {
                        //console.log(item2);
                        if (item.name === item2) {
                            obj.push(item.id);
                        }
                    })
                });
                this.formSelect.moduleId = obj;
            },
            trainingClassListChangeAdd(val) {
                console.log(val);
            },
            toClassList() {
                this.$router.push({path: '/training/classManager'})
            },
            selectLevelList() {//初始化下拉框动态数据
                let obj = [];
                Get_CourseLevel({}).then(res => {
                    res.data.forEach((item, index) => {//关键的是将后台返回的数据进行遍历，并封装好
                        obj.push({
                            id: item.id,//id必须品啊
                            name: item.levelName//name必须品啊
                        });
                    });
                });
                this.courseLevelList = obj;
                return obj;
            },
            initYear() {
                let mydate = new Date();
                let startyear = mydate.getFullYear() - 5;
                let endyear = mydate.getFullYear() + 5;
                for (var i = startyear; i <= endyear; i++) {
                    let year = {id: i, name: i};
                    this.yearList.push(year);
                }
            },
            selectLevelChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.courseLevelList.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.selectTrainingClassList();
            },
            selectTrainingClassList() {
                let obj = [];
                let level = this.formSelect.courseLevel
                selectTrainingClassList({level}).then(res => {
                    res.data.data.forEach((item, index) => {//关键的是将后台返回的数据进行遍历，并封装好
                        obj.push({
                            id: item.id.toString(),
                            name: item.courseTitle + "(" + item.moduleName + ")",
                            moduleName: item.moduleName,
                            courseName: item.courseTitle
                        });
                    });
                });
                this.trainingClassList = obj;
                return obj;
            },
        },
        mounted() {
            /*this.$chart.line1('chart1');
            this.$chart.line1('chart2');*/
            //this.install()
            this.$nextTick(() => {
                if (this.$chart) {
                    this.$chart.install('chart1', null);
                }
                console.log(this.echartsDataLoaction);
            });

        },
        created() {
            this.initDepartment();
            this.initYear();
            this.selectLevelList();
            this.remoteMethod();
            this.selectLevelChange('Foundational');
            this.onLoad();
        },
    }
</script>
<style scoped>
    #chart1 {
        width: 300px;
        height: 300px;
    }

    .echart {
        width: 150px;
        height: 300px;
    }
</style>
