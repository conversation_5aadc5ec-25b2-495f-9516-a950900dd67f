import request from './request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const updateUserLanguage = (language: string) => {
  return request({
    url: `/sgs-mart/userSetting/updateUserLanguage?hash=${new Date().getTime()}`,
    method: 'get',
    params: {
      language,
    },
  })
}

/**
 * 查询用户语言设置0
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryUserLanguage = () => {
  return request({
    url: `/sgs-mart/userSetting/queryUserLanguage?hash=${new Date().getTime()}`,
    method: 'get',
  })
}
