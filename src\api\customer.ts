import request from './request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const updateUserLanguage = (language: string) => {
  return request({
    url: '/api/sgs-mart/userSetting/updateUserLanguage',
    method: 'get',
    params: {
      language,
    },
  })
}

/**
 * 查询用户语言设置
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryUserLanguage = () => {
  return request({
    url: '/api/sgs-mart/userSetting/queryUserLanguage',
    method: 'post',
  })
}

/**
 * 查询客户信息
 * @param data - 查询参数，类型为包含任意键值对的对象
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryScmCustomer = (data: any) => {
  return request({
    url: '/api/sgs-mart/customer/scm/query',
    method: 'post',
    data,
  })
}
