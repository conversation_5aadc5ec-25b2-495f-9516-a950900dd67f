<template>
  <el-card class="nav-con">
    <el-menu :active-text-color="'#FF6600'" :default-active="computedActiveIndex" class="el-menu-vertical-demo"  background-color="#FFFFFF" text-color="#000000"
    @select="handleMenuSelect">
    
     <div class="search-container">
      <el-input :placeholder="$t('work.navigationBar.searchPlaceholder')" prefix-icon="el-icon-search" clearable size="small" v-model="searchQuery" style=" width: 85%;" @input="handleSearch" >
        <!-- <template #append>
          <el-button class="search-button" type="primary" icon="Search" size="small" @click="handleSearch" >
      搜索
    </el-button> 
      </template> -->
      </el-input>
     </div>
   
    <!-- 搜索无结果提示 -->
    <div v-if="searchQuery && menuList.length === 0" class="no-result">
      <el-empty :description="$t('work.navigationBar.noSearchResult')" />
    </div>
    <!--item.paymentFlag==0 1 2 显示支付按钮-->
    <!-- 渲染一级菜单项 -->
    <template v-for="(item) in menuList" :key="item.id">
      <!-- 如果有子菜单，使用 el-sub-menu -->
      <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id">
        <template #title>
          <div class="menu-item-content">
            <img v-if="getIconPath(item.categoryNameEn || item.categoryDescCn || item.categoryDescEn)" 
                 :src="getIconPath(item.categoryNameEn || item.categoryDescCn || item.categoryDescEn)" 
                 class="menu-icon" />
            <span>{{ item.categoryDescCn || item.categoryDescEn }}</span>
          </div>
        </template>
        
        <!-- 渲染子菜单项 -->
        <el-menu-item v-for="child in item.children" :key="child.id" :index="child.id" 
                     :class="{ 'external-link': child.isOpen == 1 }">
          <div class="menu-item-content">
            <img v-if="getIconPath(child.categoryNameEn || child.categoryDescCn || child.categoryDescEn)" 
                 :src="getIconPath(child.categoryNameEn || child.categoryDescCn || child.categoryDescEn)" 
                 class="menu-icon" />
            <span>{{ child.categoryDescCn || child.categoryDescEn }}</span>
            <!-- Show external link indicator for items with isOpen=1 -->
            <!-- <el-icon v-if="child.isOpen === 1" class="external-icon"><position /></el-icon> -->
          </div>
        </el-menu-item>
      </el-sub-menu>
      
      <!-- 如果没有子菜单，使用 el-menu-item -->
      <el-menu-item v-else :index="item.id" :class="{ 'external-link': item.isOpen == 1 }">
        <div class="menu-item-content">
          <img v-if="getIconPath(item.categoryNameEn || item.categoryDescCn || item.categoryDescEn)" 
               :src="getIconPath(item.categoryNameEn || item.categoryDescCn || item.categoryDescEn)" 
               class="menu-icon" />
          <span>{{ item.categoryDescCn || item.categoryDescEn }}</span>
          <!-- Show external link indicator for items with isOpen=1 -->
         
        </div>
      </el-menu-item>
    </template>
  </el-menu>
  </el-card>
 

</template>

<script lang="ts" setup>
import { ref, onMounted,computed } from 'vue'
import { navigationBarTree } from '../../api/navigationBar/navigationBar'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
//import { Position } from '@element-plus/icons-vue'
const store = useStore()
const userInfo =computed(()=>store.state.user.userInfo)
const { t } = useI18n()

//点击菜单时 判断 userInfo.value.isVip ='' 0 1 和 userInfo.value.isInternalUser ='' 0 1
//paymentFlag => 0 都能看 1 isVip和isInternalUser 满足其中一个为1即可 2 isInternalUser时1才能看
// import {
//   List,
//   Menu,
//   Coin,
//   Phone,
//   Box,
//   User,
//   HelpFilled,
//   CircleCheck,
//   Collection,
//   Calendar,
// } from '@element-plus/icons-vue'

// Define interfaces for menu items
interface MenuItem {
  id: string;
  categoryNameEn?: string | null;
  categoryDescEn?: string | null;
  categoryNameCn?: string | null;
  categoryDescCn?: string | null;
  icon?: string | null;
  children?: any[];
  isOpen?: number;    // Flag indicating if this menu should open an external URL
  openUrl?: string;   // URL to open when isOpen is 1
  paymentFlag?: number; // Permission flag for access control
  [key: string]: any; // For other properties
}

const activeIndex = ref('0')  // 当前活动的菜单项
const storedActiveIndex = ref('0') // Store the last non-external active index
const searchQuery = ref('')   // 搜索框的内容
const loading = ref(false)
const menuList = ref<any[]>([])

// Create a computed property for the active index that won't activate external links
const computedActiveIndex = computed(() => {
  // Check if current activeIndex corresponds to an external link
  const isExternalLink = isMenuItemExternal(activeIndex.value)
  
  // If it's an external link, return the stored non-external active index
  return isExternalLink ? storedActiveIndex.value : activeIndex.value
})

// Function to check if a menu item with the given index is an external link
const isMenuItemExternal = (index: string): boolean => {
  // Find the menu item with the given index
  let menuItem: MenuItem | null = null
  
  for (const item of menuList.value) {
    if (item.id === index) {
      menuItem = item
      break
    }
    
    // Check children
    if (item.children && item.children.length > 0) {
      const childItem = item.children.find((child: any) => child.id === index)
      if (childItem) {
        menuItem = childItem
        break
      }
    }
  }
  
  // Return true if it's an external link (isOpen=1), false otherwise
  return Boolean(menuItem && menuItem.isOpen === 1)
}

// 定义事件
const emit = defineEmits(['update:regulations', 'update:totalItems'])


// 根据菜单项获取对应的图标名称
/* const getIconName = (item: MenuItem): string => {
  // 默认图标映射
  const iconMap: Record<string, string> = {
    '法律法规': 'Menu',
    'Safeguard': 'Coin',
    'Recall': 'Phone',
    'GB产标选择器': 'Box',
    'GB标准选择器': 'Box',
    'Restricted Substances': 'User',
    '质通全球': 'HelpFilled',
    '质监在中国': 'CircleCheck',
    'CS Knowledge': 'Collection',
    '培训课程': 'Calendar'
  }
  
  // 如果有自定义图标，优先使用自定义图标
  if (item.icon) {
    return item.icon
  }
  
  // 否则根据菜单名称获取默认图标
  const menuName = item.categoryDescCn || item.categoryDescEn || item.categoryName
  return menuName && iconMap[menuName] ? iconMap[menuName] : 'List' // 默认使用List图标
} */

// 添加获取图标路径的方法
const getIconPath = (categoryNameEn: string) => {
  const iconMap: { [key: string]: string } = {
    'All': '/knowledge/navigationBar/whole.svg',
    '全部': '/knowledge/navigationBar/whole.svg',
    'Laws and Regulations': '/knowledge/navigationBar/falv.svg',
    '法律法规': '/knowledge/navigationBar/falv.svg',
    'SafeGuardS': '/knowledge/navigationBar/safe.svg',
    'Recall': '/knowledge/navigationBar/recall.svg',
    'GB Selector': '/knowledge/navigationBar/GBb.svg',
    'GB产标选择器': '/knowledge/navigationBar/GBb.svg',
    'GB标准选择器': '/knowledge/navigationBar/GBb.svg',
    'Restricted Substances': '/knowledge/navigationBar/res.svg',
    'Quality Global': '/knowledge/navigationBar/zhit.svg',
    '质通全球': '/knowledge/navigationBar/zhit.svg',
    'Quality in China': '/knowledge/navigationBar/zhij.svg',
    '质监在中国': '/knowledge/navigationBar/zhij.svg',
    'SL CS Inquiry Assistant': '/knowledge/navigationBar/csk.svg',
    'Training Course': '/knowledge/navigationBar/pei.svg',
    '培训课程': '/knowledge/navigationBar/pei.svg'
  }
  
  return iconMap[categoryNameEn] || ''
}

// 搜索处理函数
const handleSearch = async () => {
  console.log('搜索内容:', searchQuery.value);
  
  // 调用接口获取搜索结果
  const res: any = await navigationBarTree();
  
  if (res && res.data) {
    let filteredMenu;
    if (searchQuery.value.trim() === '') {
      // 如果搜索框为空，显示全部菜单
      const allOption: MenuItem = {
        id: '0',
        categoryDescCn: '全部',
        categoryDescEn: 'All',
        children: []
      };
      filteredMenu = [allOption, ...res.data];
    } else {
      // 过滤菜单项，显示与搜索关键字匹配的项
      filteredMenu = res.data.filter((item: MenuItem) => {
        const descCn = item.categoryDescCn || '';
        const descEn = item.categoryDescEn || '';
        return descCn.includes(searchQuery.value) || descEn.includes(searchQuery.value);
      });

      // 只有当搜索关键字为"全部"时才添加"全部"选项
      if (searchQuery.value.trim() === '全部') {
        const allOption: MenuItem = {
          id: '0',
          categoryDescCn: '全部',
          categoryDescEn: 'All',
          children: []
          
        };
        filteredMenu = [allOption, ...filteredMenu];
      }
    }
    
    menuList.value = filteredMenu;
  } else {
    console.error('搜索失败或无匹配结果');
  }
};

// 菜单选择处理函数
const handleMenuSelect = async (index: string) => {
  
  console.log('选择菜单:', index)
  
  // 查找选中的菜单项（可能是子菜单）
  let selectedMenu: MenuItem | null = null
  
  // 遍历查找菜单项
  for (const menu of menuList.value) {
    if (menu.id === index) {
      selectedMenu = menu
      break
    }
    
    // 检查子菜单
    if (menu.children && menu.children.length > 0) {
      const childMenu = menu.children.find((child: any) => child.id === index)
      if (childMenu) {
        selectedMenu = childMenu
        break
      }
    }
  }
  
  // Check if the menu is configured to open external URL
  if (selectedMenu && selectedMenu.isOpen == 1 && selectedMenu.openUrl) {
    // Open the URL in a new browser tab
    window.open(selectedMenu.openUrl, '_blank')
    // Don't update activeIndex for external links
    return
  }
  
  // 权限判断
  if (selectedMenu && selectedMenu.paymentFlag !== undefined) {
    debugger
    const isVip = userInfo.value.isVip === '1' || userInfo.value.isVip === 1
    const isInternalUser = userInfo.value.isInternalUser === '1' || userInfo.value.isInternalUser === 1
 
    // paymentFlag => 0 都能看 1 isVip和isInternalUser 满足其中一个为1即可 2 isInternalUser为1才能看
    if (selectedMenu.paymentFlag == 1 && !isVip && !isInternalUser) {
      // 显示付费提示
      ElMessage({
        message: t('work.navigationBar.paymentRequired'),
        type: 'warning'
      })
      return
    } else if (selectedMenu.paymentFlag == 2 && !isInternalUser) {
      // 显示管理员权限提示
      ElMessage({
        message: t('work.navigationBar.adminRequired'),
        type: 'warning'
      })
      return
    }
  }
  
  // Store the current active index as a non-external index
  storedActiveIndex.value = index
  activeIndex.value = index // 更新活动菜单项
  
  // 构建基础参数
  const params = {
    current: 1,
    size: 10,
    categoryId: index !== '0' ? index : null, // 如果点击"全部"，categoryId 为 null
  }

  emit('update:regulations', params);
}

// 获取菜单数据
const fetchRegulationList = async () => {
   // 确保 id 被正确传递
  try {
    loading.value = true
    
    
    const res: any = await navigationBarTree()
    
    
    if (res && res.data) {
      // 添加"全部"选项到菜单列表的开头
      const allOption: MenuItem = {
        id: '0',
        categoryDescCn: '全部',
        categoryDescEn: 'All',
        children: []
      }
      
      menuList.value = [allOption, ...res.data]
    }
  } catch (error) {
    console.error('获取菜单数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取菜单数据
onMounted(() => {
  fetchRegulationList()
})

defineExpose({
  activeIndex,
  menuList
});
</script>

<style lang="scss">
/* 添加全局样式 */
.nav-con{
  .el-input{
    .el-input__inner{
      margin-left: -15px;
    }
  }
  margin-top: 20px;
  border: rgba(0, 0, 0, 0.12) 1px solid;
  padding: 0;
  padding-bottom: 100px;
  width: 350px;
  
  .el-card__body{
    padding: 0;
    font-weight: bold;
  }
  
  .el-menu {
    width: 350px;
    border-right: none !important;
    
    .el-menu-item {
      height: 45px;
      line-height: 45px;
    }

    .el-sub-menu__title {
      height: 45px;
      line-height: 45px;
    }

    .el-menu-item.is-active {
      background-color: #f5f7fa !important;
      .menu-icon {
        filter: invert(48%) sepia(90%) saturate(2299%) hue-rotate(346deg) brightness(101%) contrast(101%);
      }
    }

  

    .el-sub-menu.is-active {
      .el-sub-menu__title {
        background-color: #f5f7fa !important;
        .menu-icon {
          filter: invert(48%) sepia(90%) saturate(2299%) hue-rotate(346deg) brightness(101%) contrast(101%);
        }
      }
    }

    .el-menu-item:hover {
      background-color: #f5f7fa !important;
    }

    .el-sub-menu__title:hover {
      background-color: #f5f7fa !important;
    }
      /* External link styling */
    //   .el-menu-item.external-link {
    //   color: rgb(64,7,162) !important;
    //   font-style: italic;
    //   position: relative;
      
    //   &:hover {
    //     color: #0000FF55 !important;
    //   }
      
    //   background-color: #fff !important;
    
    //   .menu-icon {
    //       filter: initial !important;
    //     }
     
    
    //   .external-icon {
    //     margin-left: 5px;
    //     font-size: 12px;
    //   }
    // }
    .el-input__wrapper{
      height: 36px;
    }
  
    .search-container{
      margin-top: 20px;
      width: 100%;
      display: flex;
      justify-content: center;
      padding-top: 0px;
      .search-button{
        padding: 0px 10px;
        background-color: #FF6600;
        color: #fff;
      }
    }
  }
  .el-button {
    padding: 10px;
  }
  /* 添加子菜单样式 */
  .el-sub-menu {
    .el-menu-item {
      padding-left: 40px !important;
      
      &.is-active {
        .menu-icon {
          filter: invert(48%) sepia(90%) saturate(2299%) hue-rotate(346deg) brightness(101%) contrast(101%);
        }
      }
    }
  }
}

/* 添加无结果提示样式 */
.no-result {
  padding: 20px;
  text-align: center;
  color: #909399;
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-icon {
  width: 16px;
  height: 16px;
  transition: filter 0.3s;  // 添加过渡效果
}

/* External link icon styling */
.external-icon {
  color: #409EFF;
}
</style>