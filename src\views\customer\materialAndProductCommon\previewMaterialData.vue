<template>
    <basic-container>
        <div class="smart_views_previewMaterialData" id="smart_views_previewMaterialData">
            <common-table
                    v-if="showTable"
                    :element-loading-text="$t('loading')"
                    border
                    fit
                    style="width: 100%"
                    row-key="sampleCode"
                    ref="preview_material_data_table"
                    stripe
                    height="400"
                    :size="tableOption.size"
                    :data="dataList"
                    :option="tableOption"
                    :filters="tableOption.filters"
                    filter-by-local
            >
                <template slot="recordStatus" slot-scope="{row}">
                    <el-tag :type="row.recordStatus=='Exist'? 'info' : 'success'">{{row.recordStatus}}</el-tag>
                </template>
                <template slot="actionColumn" slot-scope="{$index,row}">
                    <el-popconfirm
                            confirm-button-text="Delete"
                            cancel-button-text="Cancel"
                            icon="el-icon-info"
                            icon-color="red"
                            title="Delete the data?"
                            @confirm="deletePreviewData($index)"
                    >
                        <i slot="reference"
                           style="color: #ff6600; padding: 2px 5px; cursor: pointer;"
                           class="el-icon-close"
                        ></i>
                    </el-popconfirm>
                </template>
            </common-table>
        </div>
    </basic-container>
</template>

<script>
import {mapGetters} from 'vuex';
import commonTable from "@/components/tableList/commonTable";

export default {
    name: "previewMaterialData",
    data() {
        return {
            pageLoading: false,
            dataList: [],
            showTable:false,
            tableOption: {
                size: "small",
                border: true,
                menuShow: false,
                filters: {},
                showSortIcon: false,
                index: true,
                selection: false,
                action: true,
                actionWidth: 80,
                actionName:'Action',
                column: [
                    {label: 'Material ID', prop: 'sampleNo', filter: true, slot: false, minWidth: 220, type: "Input"},
                    {
                        label: 'Material Code',
                        prop: 'sampleCode',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'Material Name',
                        prop: 'sampleName',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'Material Type',
                        prop: 'sampleType',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'Material Description',
                        prop: 'sampleDescription',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {label: 'Record Status',
                        prop: 'recordStatus',
                        filter: true,
                        slot: true, minWidth: 220,
                        type: "Select",
                        dicData:[
                            {label:'Exist',value:'Exist'},
                            {label:'New',value:'New'},
                        ]
                    }
                ]
            },
        }
    },
    methods: {
        getImportData() {
            return this.dataList;
        },
        deletePreviewData(index) {
            this.dataList.splice(index, 1);
        },
        parseTemplateData(){
            let template = this.previewDataTemplate;
            let {fieldList} = template || {};
            if(!fieldList || fieldList.length==0){
                fieldList = [];
            }
            //处理template
            fieldList.forEach(field=>{
                let {fieldCode,fieldType,dispalyName,sourceValueLanguage} = field;
                let dffColumn =  {label: dispalyName, prop: fieldCode, filter: true, slot: false, minWidth: 220, type: "Input"};
                if( ['select','select2'].includes(fieldType.toLowerCase())){
                    dffColumn.type = 'Select';
                    let dicData = [];
                    let {coverSourceValue} = sourceValueLanguage[0] || [];
                    coverSourceValue.forEach(sv=>{
                        let {code,name} = sv;
                        dicData.push({label:name,value:code});
                    })
                    dffColumn['dicData'] = dicData;
                }
                this.tableOption.column.push(dffColumn);
            })
            //设置数据集
            let list = this.previewDataList;
            list.forEach(da=>{
                let {fieldList} = da;
                fieldList.forEach(field=>{
                    let {fieldCode,materialExtendFieldValue} = field;
                    this.$set(da,fieldCode,materialExtendFieldValue);
                })
            });
            this.dataList = list || [];
            this.showTable = true;
        }
    },
    created() {
        this.parseTemplateData();
    },
    mounted() {

    },
    watch: {},
    computed: {
        ...mapGetters(['permission', 'userInfo', 'language'])
    },
    props: {
        previewDataTemplate:{
            type:Object,
            default(){
                return {}
            }
        },
        previewDataList: {
            type: Array,
            default() {
                return []
            }
        }
    },
    updated() {
    },
    beforeDestroy() {
    },
    destroy() {
    },
    components: {commonTable}
}
</script>

<style lang="scss">
.smart_views_previewMaterialData {

}
</style>