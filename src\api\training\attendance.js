import request from '@/router/axios';






export const add = (form) => {
    return request({
        url: '/api/sgs-training/trainee/update',
        method: 'post',
        data:
            form,

    })
}
export const selClassTraineeList = (current, size,params) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/trainee/list",
        changeOrigin: true,
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const getClassResult = (params) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/trainee/class-result",
        changeOrigin: true,
        params: {
            ...params
        }
    })
}
export const handleDel2 = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/delTrainingModuleById",
        changeOrigin: true,
        data: id
    })
}
export const sel_group = () => {
    return request({
        url: '/api/sgs-training/course/selGroup',
        method: 'post',
    })
}