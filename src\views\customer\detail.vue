<template>
    <el-form :model="form" label-position="left" label-width="200px" class="sgs-form" ref="form" size="medium">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item :label="$t('customer.name')">
                    <el-input maxlength="100" v-model="form.customer.customerNameEn==''||form.customer.customerNameEn==null?form.customer.customerNameZh :form.customer.customerNameEn "></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('customer.taxNo')">
                    <el-input maxlength="50" v-model="form.customer.taxNo"></el-input>
                </el-form-item>
            </el-col>
            <!--<el-col span="12">-->
            <!--<el-form-item :label="$t('customer.nameEn')">-->
            <!--<el-input v-model="form.customer.customerNameEn"></el-input>-->
            <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col :span="12">
                <el-form-item :label="$t('customer.contactMobile')">
                    <el-input maxlength="20" v-model="form.customer.contactMobile"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('customer.address')">
                    <el-input maxlength="100" v-model="form.customer.customerAddressZh"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('customer.sgs.customerNo')">
                    <el-input maxlength="30" v-model="form.customer.bossNo"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('customer.sgs.reportNo')">
                    <el-input maxlength="100" v-model="form.customer.reportNo"></el-input>
                </el-form-item>
            </el-col>
            <!--<el-col span="24">-->
            <!--<el-form-item :label="$t('address.en')">-->
            <!--<el-input v-model="form.customer.customerAddressEn"></el-input>-->
            <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col span="24">
                <el-form-item :label="$t('customer.certificateNew')">
                    <el-image
                            style="width: 100px;"
                            :src="qualificationUrl"
                            :preview-src-list="[qualificationUrl]">
                    </el-image>
                </el-form-item>
            </el-col>
        </el-row>
        <!--<div>-->
        <!--<div class="sgs-footer">-->
        <!--<el-button size="small" type="primary" @click="rowSave()">{{$t('operation.save')}}</el-button>-->
        <!--</div>-->
        <!--</div>-->
    </el-form>
</template>

<script>
    import {detailForCurrentUser} from "@/api/customer/customerRegister";
    import {getCloudFileURL} from "@/api/common/index";
    import {add} from "@/api/customer/customer";
    import {validatenull} from "../../util/validate";

    export default {
        name: "detail",
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        data() {
            return {
                form: {
                    customer: {},
                },
                qualificationUrl: '',
            };
        },
        methods: {
            rowSave() {
                add(this.form.customer).then(() => {
                    this.onLoad();
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                }, error => {
                    console.log(error);
                });
            },
            onLoad() {
                this.customerId = this.customerId || 1;
                detailForCurrentUser(this.customerId).then(res => {
                    this.form = res.data.data;
                    if (!validatenull(this.form.customer.qualification.attachmentId)) {
                        getCloudFileURL(this.form.customer.qualification.attachmentId).then(res => {
                            this.qualificationUrl = res.data;
                        });
                    }

                });
            },
            uploadSuccess(res, file) {
                this.form.qualification = res.data[0].id;
            }
        },
        created() {
            this.onLoad();
        },
        watch: {
            customerId: function (data) {
                this.onLoad(data);
            },
        }
    }
</script>

<style scoped>

</style>
