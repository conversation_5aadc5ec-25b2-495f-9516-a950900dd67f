<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_new_material_info" id="sgs_smart_new_material_info">
            <div>
                <el-row >
                    <el-col :span="4" v-if="!viewPage">
                        <ul class="nav-list" ref="nav" id="trf-left-nav">
                            <template v-for="(item, index) in navList">
                                <li :class="{'is-sub': item.isSub,'active': item.active}" :key="item.name" class="nav-item" @click="toTarget(index)">
                                    <h5>
                                        {{ $t(item.name) }}
                                        <i 
                                            :class="['el-icon-success', item.pass ? 'pass': 'nopass']"
                                            v-if="item.isRequired"
                                        ></i>
                                    </h5>
                                </li>
                            </template>
                        </ul>
                    </el-col>
                    <el-col :offset="viewPage?1:0" :span="viewPage?22:20" v-if="showCompoment">
                        <el-form ref="materialDetailForm" :model="materialObj"
                                 label-position="top" label-width="300px"
                                 :disabled="!viewPage && !(createNew || btnRole(materialObj,'Edit'))">
                            <el-collapse v-model="activeNames">
                                <el-card class="sgs-box content-item" v-for="(config,index) in generalFieldConfig" :key="'gen_'+index">
                                    <el-collapse-item :title="config.sectionName" :name="config.sectionCode">
                                        <template slot="title">
                                            <h4 class="sgs-title">
                                               {{config.sectionLabel || config.sectionName}}
                                            </h4>
                                        </template>
                                        <el-row :gutter="20">
                                            <el-col :span="8" v-for="(field,ind) in config.fieldList" :key="'cfl_'+ind">
                                                <!--<div class="section_label">{{field.fieldLabel || 'No label name'}}</div>-->
                                                <el-form-item
                                                        :rules="{ required: field.isRequired==1 , message: 'please input', trigger: ['blur', 'change'] }"
                                                        :label="field.fieldLabel || 'No label name'"
                                                        :prop="lowerField(field.fieldCode)"
                                                        >
                                                    <el-input v-if="!field.fieldType || field.fieldType!='select'"
                                                              placeholder="Please input"
                                                              :type="field.fieldType=='textArea'?'textarea':''"
                                                              show-word-limit
                                                              :rows="3"
                                                              clearable
                                                              v-model="materialObj[lowerField(field.fieldCode)]"></el-input>
                                                    <el-select
                                                        v-if="field.fieldType=='select'"
                                                        style="width: 100%"
                                                        :disabled="field.fieldCode=='sampleType'"
                                                        clearable
                                                        filterable
                                                        placeholder="Please select"
                                                        v-model="materialObj[lowerField(field.fieldCode)]"
                                                    >
                                                        <el-option
                                                            v-for="(op,opIndex) in field.sourceValue"
                                                            :key="field.fieldCode+'_'+opIndex"
                                                            :label="op.name"
                                                            :value="op.code"
                                                        ></el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-collapse-item>
                                </el-card>
                                <el-card class="sgs-box content-item" v-if="materialObj.fieldList && materialObj.fieldList.length>0">
                                    <el-collapse-item name="materialDetail">
                                        <template slot="title">
                                            <h4 class="sgs-title">
                                                Material Detail
                                            </h4>
                                        </template>
                                        <!-- 下面开始是动态的了-->
                                        <el-row :gutter="20">
                                            <el-col :span="12" v-for="(f,index) in materialObj.fieldList" :key="'dff_'+index">
                                                <!--<div class="section_label">{{f.dffFormFieldNameEn || 'No Name'+index}}</div>-->
                                                <el-form-item
                                                        :rules="{ required: f.mandatoryFlag==1 , message: ((!f.fieldType || ['TextArea','Input'].includes(f.fieldType))? 'please input' : 'please select'), trigger: ['blur', 'change']}"
                                                        :label="f.dffFormFieldNameEn || ('No Name'+index)"
                                                        :prop="'fieldList.'+index+'.materialExtendFieldValue'">
                                                    <template slot="label">
                                                        {{f.dffFormFieldNameEn || ''}}
                                                        <el-tooltip effect="dark"
                                                                    :content="f.tips"
                                                                    placement="right"
                                                                    v-if="f.tips">
                                                            <i class="el-icon-info" style="color: #c8c8c8"></i>
                                                        </el-tooltip>
                                                    </template>
                                                    <div style="min-height: 53px"  v-if="!['Percentage'].includes(f.fieldType)">
                                                        <el-input v-if="!f.fieldType || ['TextArea','Input'].includes(f.fieldType)"
                                                                  :type="f.fieldType=='TextArea'?'textarea':'text'"
                                                                  placeholder="Please input"
                                                                  :rows="3"
                                                                  clearable
                                                                  v-model="f.materialExtendFieldValue"></el-input>
                                                        <el-select
                                                                v-if="['Select','Select2'].includes(f.fieldType)"
                                                                style="width: 100%;"
                                                                :multiple="['Select2'].includes(f.fieldType)"
                                                                placeholder="Please select"
                                                                v-model="f.materialExtendFieldValue" clearable filterable>
                                                            <el-option
                                                                v-for="(o,ind) in getFieldSourceValues(index)"
                                                                :key="'o_se'+o.code+ind"
                                                                :label="o.name"
                                                                :value="o.code"
                                                            ></el-option>
                                                        </el-select>
                                                        <el-checkbox-group
                                                            v-if="['Checkbox'].includes(f.fieldType)"
                                                            v-model="f.materialExtendFieldValue"
                                                        >
                                                            <el-checkbox
                                                                    v-for="(o,ind) in getFieldSourceValues(index)"
                                                                    :key="'o_check'+ind"
                                                                    :label="o.code"
                                                                    :value="o.code"
                                                            >{{o.name}}</el-checkbox>
                                                        </el-checkbox-group>
                                                        <el-radio-group
                                                                v-if="['Radio'].includes(f.fieldType)"
                                                                v-model="f.materialExtendFieldValue"
                                                        >
                                                            <el-radio
                                                                    v-for="(o,ind) in getFieldSourceValues(index)"
                                                                    :key="'o_rad'+ind"
                                                                    :label="o.code"
                                                                    :value="o.code"
                                                            >{{o.name}}</el-radio>
                                                        </el-radio-group>
                                                        <el-date-picker
                                                                v-if="['Date','DateRange'].includes(f.fieldType)"
                                                                style="width: 100%;"
                                                                :type="['Date'].includes(f.fieldType)?'date':'daterange'"
                                                                v-model="f.materialExtendFieldValue"
                                                                range-separator="-"
                                                                format="yyyy-MM-dd"
                                                                value-format="yyyy-MM-dd"
                                                                placeholder="Please select">
                                                        </el-date-picker>
                                                    </div>
                                                    <percentage
                                                            v-if="['Percentage'].includes(f.fieldType)"
                                                            :field-obj="f"
                                                            :ref="'percentage_'+f.fieldCode"
                                                    ></percentage>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-collapse-item>
                                </el-card>
                            </el-collapse>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
            <el-row class="sgs-footer page-no-print" style="position: fixed" v-if="!viewPage && !pageLoading">
                <el-col style="text-align: center">
                    <el-button type="primary" @click="saveMaterial()" :disabeld="disabledSaveBtn" v-if="createNew || btnRole(materialObj,'Edit')">Save</el-button>
                    <el-button type="primary" @click="saveMaterial('Submit')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'Submit')">Submit</el-button>
                    <el-button type="primary" @click="createTRF" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'ToTrf')">Create TRF</el-button>
                    <el-button type="primary" @click="actionMaterial('Return')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'Return')">Return</el-button>
                    <el-button type="primary" @click="actionMaterial('NotInUse')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'NotInUse')">Not In Use</el-button>
                    <el-button type="primary" @click="actionMaterial('InUse')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'InUse')">In Use</el-button>
                    <el-button type="info" @click="handlerCancelMaterial" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'Cancel')">Cancel</el-button>
                </el-col>
            </el-row>
        </div>
    </basic-container>

</template>

<script>
    import {objectIsNull} from "../../../util/validate";
    import api from '../../../api/newSamples'
    import {getServiceType, queryBuSetting} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import ReportCertificate from "../commonComponent/reportCertificate";
    import _ from 'lodash'
    import Percentage from "../materialAndProductCommon/percentage";
    export default {
        name: "materialInfo",
        provide(){
            return {
                getMainObjectId:()=>{return this.materialObj.id || this.pageId}
            }
        },
        data() {
            return {
                pageLoading:false,
                showCompoment:false,
                action:'',
                sourceType:'',
                sampleId:'',//通常为productId
                disabledSaveBtn:false,
                serviceTypeList:[
                    {
                        "serviceTypeCode": "1",
                        "serviceTypeName": "Regular"
                    },
                    {
                        "serviceTypeCode": "2",
                        "serviceTypeName": "Express"
                    },
                    {
                        "serviceTypeCode": "3",
                        "serviceTypeName": "Double express"
                    },
                    {
                        "serviceTypeCode": "4",
                        "serviceTypeName": "Emergency"
                    }
                ],
                pageId:'',
                fieldList:[],
                generalFieldConfig:[],
                templateParam:{
                    templateId:''
                },
                createNew:false,
                materialObj:{
                    id:null,
                    productLineCode:'',
                    customerGroupCode:'',
                    "templateId": "",
                    "materialName": "",
                    "materialDescription": "",
                    "materialCategory": "",
                    "materialCode": "",
                    "supplierName": "",
                    "supplierCode": "",
                    "supplierAddress": "",
                    "supplierContact": "",
                    "supplierTel": "",
                    "supplierEmail": "",
                    "manufacturerName": "",
                    "manufacturerCode": "",
                    "manufacturerAddress": "",
                    "manufacturerContact": "",
                    "manufacturerTel": "",
                    "manufacturerEmail": "",
                    "serviceType": "",
                    "labName": "",
                    "labContact": "",
                    "fieldList": []
                },
                navList:[],
                activeNames:[],
                element:{
                    nav:[],
                    content:[]
                },
                pageSelectList:{
                    categoryList:[]
                },
            }
        },
        methods: {
            getFieldSourceValues(index){
              let {sourceValueLanguage} = this.materialObj.fieldList[index];
              if(!sourceValueLanguage || sourceValueLanguage.length==0){
                  return []
              }
              if(this.viewPage == 'preview'){
                  return this.handlerSourceValue(sourceValueLanguage[0].sourceValue);
                }else{
                  return sourceValueLanguage[0].coverSourceValue || [];
              }
            },
            handlerSourceValue(sourceValue){
                let listOfValuesDataList = [];
                if(!sourceValue || sourceValue.length==0){
                    return listOfValuesDataList;
                }
                try{
                    listOfValuesDataList = sourceValue.map(v=>JSON.parse(v));
                }catch(e){}
                return listOfValuesDataList;
            },
            lowerField(fieldCode){
              return _.lowerFirst(fieldCode)
            },
            loadPage(){
                let query = this.$route.query;
                let {action,templateId,id,sourceType,sampleId} = query;
                this.action = action;
                this.sourceType = sourceType;
                this.sampleId = sampleId;
                //处理页面select
                //this.initSelectList();

                if(this.viewPage && this.viewPage == 'preview'){
                    this.initPreview();
                }else if(action=='copy'){
                    this.handlerCopy(id);
                }else{
                    this.materialObj.id = id;
                    this.pageId = id;
                    this.templateParam.templateId = templateId;
                    this.queryMaterialDetail(!id);
                }

            },
            initSelectList(){
                this.initCategoryType();
            },
            initCategoryType(){
                let paramCode = 'MaterialType';
                let params ={
                    systemId:1,
                    groupCode:'Template_Product_Material_Type',
                    productLineCode:this.materialObj.productLineCode,
                    paramCode
                }
                this.pageSelectList.categoryList = [];
                queryBuSetting(params).then(res=>{
                    console.log("query type list",res);
                    if(res.status==200 && res.data && res.data.data){
                        let {data} = res.data;
                        if(!data || data.length==0){
                            return;
                        }

                        data = data[0];
                        let {paramValue} = data;
                        try{
                            let values = JSON.parse(paramValue);
                            this.pageSelectList.categoryList =values;
                            this.changeCategorySelect();
                        }catch (e) {}
                    }
                }).catch(err=>{
                })
            },
            changeCategorySelect(){
                if(!this.pageSelectList.categoryList || Object.keys(this.pageSelectList.categoryList)==0){
                    return
                }
                let categoryList = this.pageSelectList.categoryList[this.language];
                let sourceValueList = (categoryList || []).map(f=>{
                    return {name:f.label,code:f.code};
                })
                //特殊处理仅显示
                this.generalFieldConfig.forEach(c=>{
                    let {sectionCode} = c;
                    if(sectionCode != 'Material Info'){
                        return
                    }
                    c.fieldList.forEach(field=>{
                        let {fieldCode} = field;
                        if(fieldCode!='sampleType'){
                            return
                        }
                        field.sourceValue = sourceValueList;
                    })
                })

            },
            handlerCancelMaterial(){
                this.actionMaterial('Cancel',()=>{
                    this.backToList(false);
                });
            },
            actionMaterial(action,callback){
                this.$confirm(action+' the material?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    let {id} = this.materialObj;
                    api.actionSamples({ids:[id],action}).then(res=>{
                        if(res.status==200 && res.data){
                            this.$notify.success("Success");
                            if(callback){
                                callback();
                            }else{
                                window.location.reload();
                            }
                        }else{
                            this.$notify.error(res.message || "Operation Fail");
                        }
                    }).catch(err=>{
                    })
                });
            },
            createTRF(){
                let customerGroupCode = this.materialObj.buyerCustomerGroupCode;
                let bossNo = this.materialObj.buyerCustomerBossNo;
                let customer = {
                    bossNo,
                    customerGroupCode
                }
                customer = encodeURIComponent(JSON.stringify(customer));
                let ids = [this.materialObj.id];
                let bu = this.materialObj.productLineCode;
                let actionType="individualSample";
                let flag =1;
                this.$router.push({
                    path:'/trf/trfForm',
                    query:{flag,actionType,ids,customer,bu}
                })
            },
            checkDffPercentage(){
                let validPercentage = true;
                let scRef = '';
                this.materialObj.fieldList.forEach(field=>{
                    let {fieldCode,fieldType} = field;
                    if((fieldType||'').toUpperCase() =='PERCENTAGE'){
                        let refCode = 'percentage_'+fieldCode;
                        let checked = this.$refs[refCode][0].validatePercentage();
                        if(!checked){
                            scRef = refCode;
                            validPercentage = false;
                        }
                    }
                });
                if(!validPercentage){
                    this.$refs[scRef][0].$el.scrollIntoView({
                        block: 'center',
                        behavior: 'smooth'
                    });
                }
                return validPercentage;
            },
            saveMaterial(submitAction){
                this.$refs['materialDetailForm'].validate((valid) => {
                    if(!valid){
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.trfValidateError'),
                            type: 'warning'
                        });
                        return
                    }
                    //单独校验所有percentage
                    let validPercentage = this.checkDffPercentage();
                    if(!validPercentage){
                        return;
                    }
                    let param = Object.assign({},this.materialObj);
                    param['dataType'] = 'material';
                    param['sourceType']=this.sourceType;
                    param['sampleId']=this.sampleId;
                    this.pageLoading = true;
                    api.createMaterial(param).then(res=>{
                        this.pageLoading = false;
                        if(res.status==200 && res.data && res.data.data){
                            if(submitAction){
                                this.actionMaterial('Submit');
                                return;
                            }
                            let {id} = res.data.data;
                             window.location.href = '/#/customer/newMaterial/detail?id='+id;
                             window.location.reload()
                        }else{
                            this.$notify.error(res.message || 'Save Fail');
                        }
                    }).catch(err=>{
                        this.pageLoading = false;
                        console.log("save material err",err)
                    })
                })
            },
            renderNavList(){
                let navList = [];
                let activeNames = [];
                (this.generalFieldConfig || []).forEach((gen,index)=>{
                    let {sectionCode, sectionLabel, fieldList} = gen;
                    let isRequired = (fieldList || []).some(field => field.isRequired == 1);
                    let nav = {name:sectionLabel,id:"#"+sectionCode,seq:index+1,active:false,isSub:false,show:true,isRequired:isRequired, pass: false}
                    navList.push(nav);
                    activeNames.push(sectionCode);
                })
                if(this.fieldList && this.fieldList.length>0){
                    let isRequired = (this.fieldList || []).some(field => field.isRequired == 1);
                    navList.push({name:"Material Detail",id:"#dff",seq:navList.length+1,active:false,isSub:false,isRequired:isRequired, pass: false});
                    activeNames.push('materialDetail')
                }
                this.navList = navList;
                this.activeNames = activeNames;
                if(this.navList.length>0){
                    this.$nextTick(()=>{
                        const nav = document.getElementsByClassName('nav-item')
                        const cont = document.getElementsByClassName('content-item')
                        this.element.nav = nav
                        this.element.content = cont
                        //导航初始化样式（默认第一个）
                        //nav[0].classList.add('active')
                        this.navList[0].active = true;
                        window.addEventListener("scroll", this.toScroll)
                    })
                }
            },
            renderPageComponent(){
                let list = [];
                this.fieldList.forEach(fl=>{
                    let {tips,fieldCode,id,dispalyName,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = fl;
                    let materialExtendFieldValue = ['Checkbox','Select2','DateRange'].includes(fieldType)?[]: '';
                    list.push({
                        fieldCode,
                        dffFormFieldId: id,
                        dffFormFieldNameEn:dispalyName,
                        materialExtendFieldValue,
                        mandatoryFlag,
                        fieldType,
                        sequence,
                        sourceValueLanguage,
                        tips
                    })
                    if(this.pageId){
                        let dbObj = this.materialObj.fieldList.find(l=>l.dffFormFieldId==id);
                        if(!dbObj){
                            this.materialObj.fieldList.push({
                                fieldCode,
                                dffFormFieldId: id,
                                dffFormFieldNameEn:dispalyName,
                                materialExtendFieldValue,
                                fieldType,
                                sequence,
                                sourceValueLanguage,
                                tips
                            });
                        }
                    }
                });
                list.sort((a,b)=> {
                    return a.sequence - b.sequence
                })
                if(this.pageId){
                    this.materialObj.fieldList.forEach(f=>{
                        let {dffFormFieldId,materialExtendFieldValue} = f;
                        let templateObj = list.find(l=>l.dffFormFieldId==dffFormFieldId);
                        if(templateObj){
                            let {tips,dffFormFieldNameEn,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = templateObj;
                            this.$set(f,'dffFormFieldNameEn',dffFormFieldNameEn);
                            this.$set(f,'sourceValueLanguage',sourceValueLanguage);
                            this.$set(f,'mandatoryFlag',mandatoryFlag);
                            this.$set(f,'fieldType',fieldType);
                            this.$set(f,'sequence',sequence);
                            this.$set(f,'tips',tips);
                            let defaultMaterialExtendFieldValue = ['Checkbox','Select2','DateRange'].includes(fieldType)?[]: '';
                            if(!materialExtendFieldValue){
                                this.$set(f,'materialExtendFieldValue',defaultMaterialExtendFieldValue);
                            }
                        }
                    })
                    this.materialObj.fieldList.sort((a,b)=> {
                        return a.sequence - b.sequence;
                    })
                }else{
                    //设置默认值
                    this.generalFieldConfig.forEach(gf=>{
                        let {fieldList} = gf;
                        (fieldList || []).forEach(f=>{
                            let {fieldCode,defaultValue} = f;
                            this.$set( this.materialObj,this.lowerField(fieldCode),defaultValue);
                        })
                    })

                    this.materialObj.fieldList = list;
                }
                this.showCompoment = true;
            },
            initPreview(){
                let {fieldList,generalFieldConfig} = this.templateData || {};
                if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                    this.$notify.warning("No template data")
                    return;
                }
                this.fieldList = fieldList;
                this.generalFieldConfig = generalFieldConfig;
                this.renderPageComponent();
                this.renderNavList();
            },
            // 公共查询模板 useNew 目前针对copy场景
            queryTemplate(templateId,useNew=false){
                this.pageLoading = true;
                let formPurpose = 'Material';
                let apiReq = useNew?api.getSampleNewTemplateList({templateId,formPurpose}) : api.geSampleTemplateList({templateId,formPurpose});
                apiReq.then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        if(res.data.data.length==0){
                            this.pageLoading = false;
                            this.disabledSaveBtn = true;
                            this.$notify.error("Load Template Fail!");
                            return;
                        }
                        let {fieldList,generalFieldConfig,customerGroupCode,buCode,customerCategory} = res.data.data[0];
                        if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                            this.$notify.warning("No template data")
                            return;
                        }
                        this.$emit("getMaterialTemplateInfo",res.data.data[0]);
                        this.fieldList = fieldList;
                        this.generalFieldConfig = this.getSpecificSections(generalFieldConfig);

                        this.materialObj.templateId= useNew ? res.data.data[0]['templateId'] : templateId;
                        this.materialObj.productLineCode = buCode;
                        this.materialObj.customerGroupCode = customerGroupCode;
                        this.renderPageComponent();
                        this.renderNavList();
                        this.initSelectList();
                        this.materialObj.sampleType = customerCategory;
                    }else{
                        this.disabledSaveBtn = true;
                        this.$notify.error("Load Template Fail!");
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    this.pageLoading = false;
                })

            },
            handlerCopyData(oldData,newData){
                let {id} = newData;
                oldData['id'] = id;
                oldData.permissions = [{action:'Edit'}];
                return oldData;
            },
            handlerCopy(id){
                this.pageLoading = true;
                api.copySample({id}).then(async res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        let dbData = res.data.data;
                        this.pageId = dbData.id;
                        this.createNew = true;
                        this.materialObj = dbData;
                        this.$emit("getMaterialInfo",this.materialObj);
                        this.queryTemplate(this.materialObj.templateId,true)
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no material detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    this.$notify.warning("Query no material detail data");
                })
            },
            queryMaterialDetail(createNew=false){
                this.pageLoading = true;
                let {id} = this.materialObj;
                api.querySampleDetail({id,dataType:'material'}).then(async res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        if(createNew){
                            this.createNew = true;
                            this.materialObj = res.data.data;
                            this.queryTemplate(this.templateParam.templateId)
                            return;
                        }
                        let dbData = res.data.data;
                        if(this.action == 'copy'){
                            delete res.data.data.id;
                            let copyObj = await api.querySampleDetail({id:'',dataType:'material'});
                            if(copyObj.status==200 && copyObj.data && copyObj.data.data){
                                dbData = this.handlerCopyData(res.data.data,copyObj.data.data)
                            }
                        }
                        this.materialObj = dbData;
                        this.$emit("getMaterialInfo",this.materialObj);
                        this.queryTemplate(this.materialObj.templateId,this.action == 'copy')
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no material detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    console.log("queryMaterialDetail err",err)
                    this.$notify.warning("Query no material detail data");
                })
            },
            backToList(showTips = true){
                if(!showTips  || (!this.createNew && !this.btnRole(this.materialObj,'Edit'))){
                    this.$router.push({
                        path:'/customer/newMaterial/list',
                        query:{}
                    })
                }else{
                    this.$confirm("Please make sure you have saved the information before you close the window.",'Tips',{
                        confirmButtonText: 'Confirm',
                        cancelButtonText: 'Cancel',
                        type: 'warning'
                    }).then(res=>{
                        this.$router.push({
                            path:'/customer/newMaterial/list',
                            query:{}
                        })
                    });
                }
            },
            btnRole({permissions},code){
                if(!this.materialObj.id && this.viewPage!='preview' && code=='Edit'){
                    return true;
                }
                return (permissions || []).map(p=>p.action).includes(code);
            },
            toTarget(index){
                const { content, nav } = this.element;
                this.navList.forEach((na,ind)=>{
                    na.active = index==ind;
                })
                const scrollTop = document.documentElement.scrollTop|| document.body.scrollTop;
                switch (index) {
                    case 0:
                        document.documentElement.scrollTop = 0;
                        return;
                }
                document.documentElement.scrollTop =  ((document.documentElement.scrollTop===0?-100:document.documentElement.scrollTop)+ (content[index].getBoundingClientRect().top))-100;
            },
            toScroll() {
                let navEle = document.querySelector('.nav-list')
                if(objectIsNull(navEle)){
                    return;
                }
                navEle.style.width = navEle.clientWidth + 'px'
                var w;
                //获取滚动距离
                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                const { content, nav } = this.element
                this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                window.addEventListener("resize", () => {
                    this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                });
                // 侧边栏和评论栏固定
                if(scrollTop != undefined && scrollTop > 170) {
                    navEle.style.position = 'fixed'
                    navEle.style.top = '80px'
                } else {
                    navEle.style.position = 'initial'
                }
                // 侧边栏菜单添加当前高亮
                let len = content.length;
                let scrollIndex = 0;
                for (let i = 0;i < len; i++) {
                    //获取每块内容距离顶部距离
                    const offset = content[i].offsetTop;
                    //当划过第一块内容 改变左侧导航样式
                    if (scrollTop >= offset) {
                        scrollIndex = i;
                    }
                }
                for (let n = 0; n < len; n++) {
                    if(!nav[n]) {
                        return;
                    }
                    n == scrollIndex ? nav[n].classList.add('active') : nav[n].classList.remove('active')
                }

                if(scrollTop == 0) {
                    nav[0].classList.add('active')
                    nav[1].classList.remove('active')
                }
            },
             // 获取Document tests 中特定的section
             getSpecificSections(generalFieldConfig) {
                let filtered = generalFieldConfig.filter(item =>
                    ['Supplier', 'Manufacturer', 'Service', 'Material Info'].includes(item.sectionCode)
                );
                //console.log("filtered",filtered,"generalFieldConfig",generalFieldConfig)
                return filtered;
            },
             // 检查必填项是否已填写
             checkRequiredFields(sectionCode) {
                let config = this.generalFieldConfig.find(g => g.sectionCode === sectionCode);
                if (!config || !config.fieldList) return true;

                return config.fieldList.every(field => {
                    if (field.isRequired == 1) {
                        let fieldValue = this.materialObj[this.lowerField(field.fieldCode)];
                        return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
                    }
                    return true;
                });
            },

            // 更新 navList 的 pass 字段
            updateNavListPass() {
                this.navList.forEach(nav => {
                    nav.pass = this.checkRequiredFields(nav.id.replace('#', ''));
                });
            },

            // 在表单值变化时调用
            handleFormChange() {
                this.updateNavListPass();
            }
        },
        mounted() {},
        created() {
            this.loadPage();
        },
        watch: {
            // 监听表单变化
            materialObj: {
                handler: 'handleFormChange',
                deep: true
            },
            language: {
                handler: function(newVal, oldVal) {
                    this.changeCategorySelect();
                },
                immediate: true
            },
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
        },
        props: {
            viewPage:'',
            templateData:{}
        },
        updated() {
        },
        components: {Percentage, ReportCertificate}
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";
    .sgs_smart_new_material_info {
        .el-collapse{
            border-bottom: none !important;
            border-top: none !important;
        }
        .el-collapse-item__wrap,.el-collapse-item__header{
            border-bottom: none !important;
        }
        p.sample_tips{
            color:#c8c8c8;
        }
        .nav-list {
            list-style: none;
            margin-top: 24px;
            padding-left: 0;
            li {
                cursor: pointer;
                border-left: 3px solid #D9D9D9;
                padding-left: 24px;
                font-size: 16px;
                font-weight: 400;
                color: #000000;
                &.is-sub {
                    padding-left: 40px;
                    font-size: 16px;
                    font-weight: 400;
                    color: #656565;
                    h5 {
                        font-weight: normal;
                    }
                }
                &.active {
                    color: #f60;
                    border-color: #f60;
                }
                h5 {
                    padding: 10px 0;
                    margin: 0;
                    .pass{
                        color: #f60;
                        margin-right: 10px;
                    }
                    .nopass{
                        color: #D9D9D9;
                        margin-right: 10px;
                    }
                }
            }
        }
        /* 重置input边距 */
        .el-input--suffix .el-input__inner {
            padding: 0;
        }
        .el-form-item__label {
            color: $text-color-value;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .el-collapse-item__content {
            padding-bottom: 0;
        }
        .el-row {
            display: flex;
            flex-wrap: wrap;
        }
    }
</style>