import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/sgs-mart/customer/materials/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const exportMaterials = (current, size, params) => {
  return request({
    url: '/api/sgs-mart/customer/materials/exportMaterials',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


//查询带有报告的materialList
export const reportMaterialPage = (current, size, params) => {
  return request({
    url: '/api/sgs-mart/customer/materials/reportMaterialPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const exportReportMaterials = (current, size, params) => {
  return request({
    url: '/api/sgs-mart/customer/materials/exportReportMaterials',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const remove = (classParam) => {
  return request({
    url: '/api/sgs-mart/customer/materials/remove',
    method: 'post',
    data: classParam
  })
}
export const downloadExcel1 = (classParam) => {
  return request({
    url: '/api/sgs-mart/customer/materials/download-excel',
    method: 'post',
    data: classParam
  })
}
export const uploadExcelData = (classParam) => {
  return request({
    headers:{'content-type': 'application/x-www-form-urlencoded'},
    url: '/api/sgs-mart/customer/materials/upload-excel',
    method: 'post',
    data: classParam
  })
}
export const uploadOrAddMaterialTrfTemplate = (materialParams) => {
  return request({
    url: '/api/sgs-mart/customer/materials/uploadOrAddMaterialTrfTemplate',
    method: 'post',
    data: materialParams
  })
}
export const newTrfOrAddTrmTemplate = (materialParams) => {
  return request({
    url: '/api/sgs-mart/customer/materials/udpateMaterialTemplate',
    method: 'post',
    data: materialParams
  })
}


export const downloadExcel = (materialConfigId) => {
  return request({
    url: '/api/sgs-mart/customer/materials/download-excel',
    method: 'post',
    responseType: 'blob',
    params: {
      materialConfigId,
    }
  })
}
