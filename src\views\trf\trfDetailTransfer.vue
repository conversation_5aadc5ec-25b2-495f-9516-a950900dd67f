<template>

</template>

<script>
    import { createSignature } from '@/api/trf/trf';

    export default {
        mounted() {
            this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
           let params = this.$route.query;
            params.trfId = params.id;
            createSignature(params).then(res => {
                if(res.status === 200 && res.data && res.data.code === 200) {
                    params.signature = res.data.data;
                    delete params.trfId;
                    this.$router.push({ path: '/trf/trfDetail', query: params });
                }
            });
        }
    }

</script>

<style scoped>

</style>
