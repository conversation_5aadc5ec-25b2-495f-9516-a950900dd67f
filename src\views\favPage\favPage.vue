<template>
    <el-row :gutter="24" style="width: 100%;">
        <el-col :span="1"></el-col>
        <el-col :span="5">
            <NavigationBar ref="navigationBar" @update:regulations="handleUpdateRegulations" />
        </el-col>
        <el-col :span="17">
            <div class="container">
                <!-- 搜索条件区域 -->
                <el-card class="search-box" style="padding-bottom: 0px; position: relative;">
                    <!-- 添加订阅按钮 -->
                    <!-- <div class="subscribe-button-container">
                        <el-button 
                            :class="['subscribe-button', isSubscribed ? 'subscribed' : 'unsubscribed']"
                            @click="toggleSubscribe">
                            <span v-if="isSubscribed">{{ $t('work.favPage.subscribed') }}</span>
                            <span v-else>{{ $t('work.favPage.subscribe') }}</span>
                        </el-button>
                    </div> -->
                    <div class="el-card1" style="margin-top: -10px;">
                    <el-form :model="searchObj" size="small">
                        <el-row :gutter="24">
                            <!-- 搜索框和按钮 -->
                            <el-col :span="7">
                                <el-form-item :label="$t('work.favPage.searchKeyword')+':'" label-width="133px" class="form-label-right" style="align-items: center;">
                                    <el-input v-model="searchObj.searchQuery" :placeholder="$t('work.favPage.searchPlaceholder')"
                                        prefix-icon="el-icon-search" clearable 
                                        class="search-input" style="height: 36px;"
                                        @keyup.enter="handleSearchClick" @focus="handleInputFocus" @blur="handleInputBlur"
                                    @input="handleInput">
                                </el-input>
                            <!-- 搜索历史下拉列表 -->
                            <div v-if="suggestions.length > 0" class="suggestions-list">
                                <ul>
                                            <li v-for="(item, index) in suggestions" :key="index" class="suggestion-item">
                                        <span @click="pushToContent(item)">{{ item }}</span>
                                                <el-icon class="delete-icon" @click.stop="deleteSearchHistory(index)">
                                            <Close />
                                        </el-icon>
                                    </li>
                                </ul>
                            </div>
                            </el-form-item>
                            </el-col>
                            
                            <el-col :span="7">
                                <el-form-item :label="$t('work.favPage.searchType')+':'" label-width="133px" class="form-label-right" style="align-items: center;">
                                    <el-select v-model="searchObj.searchType" :placeholder="$t('common.all')" style="width: 100%">
                                        <el-option :label="$t('common.all')" value="全部" />
                                        <el-option :label="$t('work.contentPage.title')" value="标题" />
                                        <el-option :label="$t('work.contentPage.summary')" value="摘要" />
                                        <el-option :label="$t('work.contentPage.attachment')" value="附件" />
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                            
                            <el-col :span="7">
                                <el-form-item :label="$t('work.favPage.publishTime')+':'" label-width="133px" class="form-label-right" style="align-items: center;">
                                    <el-select v-model="searchObj.publishDateRange" :placeholder="$t('common.all')" style="width: 100%">
                                        <el-option :label="$t('common.all')" value="全部" />
                                        <el-option :label="$t('work.contentPage.lastWeek')" value="最近七天" />
                                        <el-option :label="$t('work.contentPage.lastMonth')" value="近一个月" />
                                        <el-option :label="$t('work.contentPage.lastHalfYear')" value="最近半年" />
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                            <el-col :span="3">
                                <div class="subscribe-button-container" style="display: flex; justify-content: flex-end; margin-right: -5px;">
                        <el-button 
                            :class="['subscribe-button', isSubscribed ? 'subscribed' : 'unsubscribed']"
                            @click="toggleSubscribe">
                            <span v-if="isSubscribed">{{ $t('work.favPage.subscribed') }}</span>
                            <span v-else>{{ $t('work.favPage.subscribe') }}</span>
                        </el-button>
                    </div>
                                        </el-col>
                        </el-row>
                        
                        <el-row :gutter="24">
                            <el-col :span="7">
                                <el-form-item label-width="133px" class="form-label-right" style="align-items: center;">
                                    <template #label>
                                        <div style="display: flex; align-items: center; justify-content: flex-end; width: 100%; white-space: nowrap; margin-right: -8px;">
                                            <img src="/contentPage/label.svg"
                                                style="width: 16px; height: 12px; filter: grayscale(100%); margin-right: 4px;" alt="">
                                            {{ $t('work.favPage.tagSelection') }}：
                                        </div>
                                    </template>
                                    <el-select
                                        v-model="searchObj.selectedTags"
                                        multiple
                                        filterable
                                        :collapse-tags="true"
                                        :collapse-tags-tooltip="true"
                                        :max-collapse-tags="1"
                                        :placeholder="$t('work.favPage.pleaseSelectTags')"
                                        style="width: 100%;"
                                        @change="toggleTag"
                                        class="tag-select"
                                        clearable
                                        :loading="false"
                                        :no-data-text="$t('common.NoData')"
                                    >
                                        <el-option
                                            v-for="tag in tagList"
                                            :key="tag.id"
                                            :label="tag.tagNameCn"
                                            :value="tag.id"
                                        />
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                            <el-col :span="16"></el-col>
                                    </el-row>

                        <!-- 搜索和重置按钮，折叠面板打开时隐藏 -->
                        <el-row v-if="!collapseActive.includes('1')" style="margin-top: 10px; position: relative; margin-bottom: 15px;">
                            <el-col :span="24" style="display: flex; justify-content: flex-start; padding-bottom: 12px; padding-left: 133px;">
                                <div class="button-group">
                                    <el-button type="primary" @click="handleSearchClick" 
                                        style="width: 80px; margin-right: 10px; height: 30px;">
                                        <el-icon><Search /></el-icon>
                                        {{ $t('work.favPage.search') }}
                                    </el-button>
                                    <el-button @click="resetSearch" style="width: 80px; height: 30px;">
                                        <el-icon><Refresh /></el-icon>
                                        {{ $t('work.favPage.reset') }}
                                    </el-button>
                                    </div>
                            </el-col>
                            
                            <!-- 展开条件按钮放在固定位置最右侧 -->
                            <div v-if="showCollapsePanel" class="expand-conditions-btn" style="margin-right: -41px;">
                                <el-button 
                                    type="text" 
                                    @click="collapseActive = ['1']">
                                    <span>{{ $t('work.favPage.expandCondition') }}</span>
                                    <el-icon><ArrowDown /></el-icon>
                                </el-button>
                            </div>
                        </el-row>

                        <!-- 折叠面板 - 更多条件 -->
                        <el-row>
                            <el-col :span="24">
                                <el-collapse style="width: 100%;" v-model="collapseActive" v-if="showCollapsePanel">
                                <el-collapse-item name="1">
                                    <template #title>
                                        <!-- 移除这里的标题，因为我们已经创建了单独的按钮 -->
                                    </template>

                                    <el-row :gutter="24">
                                            <!-- 动态表单项 -->
                                            <el-col :span="7" v-for="(formItem, index) in dynamicFormItems" :key="index">
                                                <el-form-item :label="`${formItem.modelNameCn}：`" label-position="left" label-width="133px" class="form-label-right" style="align-items:center ;">
                                                    <!-- 文本输入框 -->
                                                    <el-input v-if="formItem.modelType === 'txt'" 
                                                        v-model="formItem.modelValue" 
                                                        :placeholder="`${formItem.modelNameCn}`" />
                                                    
                                                    <!-- 日期选择器 -->
                                                    <el-date-picker v-else-if="formItem.modelType === 'date'" 
                                                        v-model="formItem.modelValue" 
                                                        type="date" 
                                                        style="width: 100%;"
                                                        :placeholder="`${formItem.modelNameCn}`" />
                                                    
                                                    <!-- 下拉选择框 -->
                                                    <el-select v-else-if="formItem.modelType === 'pullTochose'" 
                                                        v-model="formItem.modelValue" 
                                                        style="width: 100%;"
                                                        :placeholder="`${formItem.modelNameCn}`"
                                                        filterable
                                                        clearable
                                                        :data-model-code="formItem.modelCode"
                                                        :popper-class="'select-dropdown-' + formItem.modelCode"
                                                        @visible-change="handleDropdownVisible($event, formItem.modelCode)"
                                                        :filter-method="(query: string) => handleFilter(query, formItem)"
                                                        :loading="formItem.loading">
                                                        <el-option 
                                                            v-for="(option, optIndex) in formItem.filteredOptions || getOptionsList(formItem)" 
                                                            :key="optIndex" 
                                                            :label="option.label || option.value" 
                                                            :value="option.value" />
                                                    </el-select>
                                                    
                                                    <!-- 下拉多选框 -->
                                                    <el-select v-else-if="formItem.modelType === 'pullTochoses'" 
                                                        v-model="formItem.selectValues" 
                                                        multiple
                                                        collapse-tags
                                                        style="width: 100%;"
                                                        @change="handleMultiSelectChange(formItem)"
                                                        :placeholder="`${formItem.modelNameCn}`"
                                                        filterable
                                                        clearable
                                                        :data-model-code="formItem.modelCode"
                                                        :popper-class="'select-dropdown-' + formItem.modelCode"
                                                        @visible-change="handleDropdownVisible($event, formItem.modelCode)"
                                                        :filter-method="(query: string) => handleFilter(query, formItem)"
                                                        :loading="formItem.loading">
                                                        <el-option 
                                                            v-for="(option, optIndex) in formItem.filteredOptions || getOptionsList(formItem)" 
                                                            :key="optIndex" 
                                                            :label="option.label || option.value" 
                                                            :value="option.value" />
                                                    </el-select>
                                                    
                                                    <!-- 日期范围选择器 -->
                                                    <el-date-picker v-else-if="formItem.modelType === 'dateRange'" 
                                                        v-model="formItem.dateRangeValue" 
                                                        type="daterange" 
                                                        range-separator="-"
                                                        :start-placeholder="$t('work.favPage.startDate')"
                                                        :end-placeholder="$t('work.favPage.endDate')"
                                                        @change="handleDateRangeChange(formItem)"
                                                        style="width: 100%; height: 36px;"
                                                        value-format="YYYY-MM-DD" class="date-picker" />
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        
                                        <!-- 搜索和重置按钮 -->
                                        <el-row style="margin-top: 15px;">
                                            <el-col :span="24" style="display: flex; justify-content: flex-start; padding-left: 133px;">
                                                <el-button type="primary" @click="handleSearchClick" 
                                                    style="width: 80px; margin-right: 15px;">
                                                    <el-icon><Search /></el-icon>
                                                    {{ $t('work.favPage.search') }}
                                                </el-button>
                                                <el-button @click="resetSearch" style="width: 80px;">
                                                    <el-icon><Refresh /></el-icon>
                                                    {{ $t('work.favPage.reset') }}
                                                </el-button>
                                            </el-col>
                                        </el-row>

                                        <!-- 收起条件按钮 -->
                                        <el-row style="margin-top: 10px; margin-right: -33px;">
                                            <el-col :span="24" style="display: flex; justify-content: flex-end; padding-right: 15px;">
                                                <el-button type="text" @click="collapseActive = []" 
                                                          class="collapse-button">
                                                    <span>{{ $t('work.favPage.collapseCondition') }}</span>
                                                    <el-icon><ArrowUp /></el-icon>
                                                </el-button>
                                            </el-col>
                                        </el-row>
                                </el-collapse-item>
                            </el-collapse>
                            </el-col>
                        </el-row>
                    </el-form>
                    </div>
                </el-card>

                <!-- 法规列表 -->
                <el-card class="regulation-list">
                    <el-row :gutter="24" type="flex" justify="end">
                        <el-dropdown @command="handleCommand" size="small">
                            <span class="el-dropdown-link">
                                <el-icon>
                                    <Expand />
                                </el-icon>
                                {{ getSortText(searchObj.sortType) }}
                                <el-icon>
                                    <CaretBottom />
                                </el-icon>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="desc" style="font-size:small;"><el-icon>
                                            <SortDown />
                                        </el-icon>{{ t('work.favPage.publishTimeDesc') }}</el-dropdown-item>
                                    <el-dropdown-item command="asc" style="font-size:small;"><el-icon>
                                            <SortUp />
                                        </el-icon>{{ t('work.favPage.publishTimeAsc') }}</el-dropdown-item>
                                    <el-dropdown-item command="relation" style="font-size:small;"><el-icon>
                                            <Connection />
                                        </el-icon>{{ t('work.favPage.relevance') }}</el-dropdown-item>
                                    <el-dropdown-item command="viewcount" style="font-size:small;"><el-icon>
                                            <View />
                                        </el-icon>{{ t('work.favPage.viewCount') }}</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </el-row>
                    <div class="regulation-list-main">
                        <!-- 添加无数据提示 -->
                        <div v-if="regulations.length === 0 && !loading" class="no-data">
                            <el-empty :description="getEmptyDescription">
                                <template #image>
                                    <el-icon style="font-size: 60px; color: #909399;"><Search /></el-icon>
                                </template>
                            </el-empty>
                        </div>

                        <div v-else v-for="(item, index) in regulations" :key="index" class="regulation-item">
                            <el-row :gutter="24">
                                <!-- 左侧图片区域 -->
                                <el-col :span="5" style="height: 130px;">
                                    <div class="image-container">
                                        <template v-if="item.coverPlan">
                                            <img :src="item.coverPlan"
                                                style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;"
                                                @error="() => item.coverPlan = item.categoryId == '1' ? '/img/law.jpg' : '/contentPage/no-data.svg'" />
                                        </template>

                                        <template v-else>
                                            <el-image
                                                :src="item.categoryId == '1' ? '/knowledge/img/law.jpg' : '/knowledge/contentPage/no-data.svg'"
                                                fit="contain" style="width: 100%; height: 100%;" />
                                        </template>
                                    </div>
                                </el-col>

                                <!-- 右侧内容区域 -->
                                <el-col :span="19">
                                    <div class="content-wrapper" style="position: relative; min-height: 130px; display: flex; flex-direction: column; padding-bottom: 0;">
                                        <!-- 标题和标签区域 -->
                                    <div class="regulation-header">
                                            <div class="title-container">
                                                <div class="title" @click="pushToDetail(item)">
                                                    {{ truncateText(item.title, 40) }}
                                                </div>

                                                <!-- 标签区域 -->
                                                <div v-if="item.knowledgeTagEntityList && item.knowledgeTagEntityList.length != 0"
                                                    class="tag-container">
                                                    <div v-for="(tags, index2) in item.knowledgeTagEntityList"
                                                        :key="index2" class="tag-item">
                                                        <img src="/contentPage/label.svg" style="width: 16px; height: 12px;" alt="">
                                                        <span>{{ tags?.tagNameCn }}</span>
                                                </div>
                                                </div>

                                                <!-- 移除收藏按钮 -->
                                                <span class="favorite-action" @click="cancelFavorite(item.id)">
                                                    <img src="/detailedArticle/star-selected.svg"
                                                        style="width: 16px; height: 16px; margin-right: 4px; vertical-align: middle;" />
                                                    {{ $t('work.detailedArticle.alreadyFavorite') }}
                                                </span>
                                            </div>
                                    </div>
                                    <div v-if="item.categoryId == '1'" class="standard-name">
                                            {{ item.ltsTitle || '' }}
                                    </div>

                                        <!-- 栏目 -->
                                        <div class="regulation-category">
                                            {{ item.categoryName }}
                                        </div>

                                        <!-- 添加标题简写，仅当栏目为法律法规时显示 -->
                                     <!--    <div v-if="item.categoryId === '1'" class="standard-name">
                                            {{ item.standardName || '-' }}
                                        </div> -->

                                        <!-- 内容区 -->
                                        <div class="middle-content" style="flex: 1; margin-bottom: 20px;">
                                            <!-- 描述内容 -->
                                    <div class="regulation-content">
                                                {{ item.description || $t('work.favPage.noDescription') }}
                                    </div>
                                            
                                            <!-- 附件区域 -->
                                            <div v-if="item.knowledgeAttachmentList && item.knowledgeAttachmentList.length > 0" 
                                                class="regulation-attachments">
                                                <div class="attachment-container">
                                                    <span v-for="(attachment, index) in item.knowledgeAttachmentList"
                                                        :key="index" class="attachment-item"
                                                        @click="downloadFile(attachment)" :title="attachment.fileName">
                                                        <img :src="getFileIcon(attachment.fileName)" 
                                                            style="width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;" />
                                                        <span class="attachment-name">{{ attachment.fileName }}</span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 元信息行，固定在底部 -->
                                        <div class="regulation-meta" style="position: absolute; bottom: 0; width: 100%; margin-bottom: 0;">
                                            <span class="meta-item status">{{ $t('work.detailedArticle.status') }}：
                                                <template v-if="item.categoryId == '1'">{{ item.ltsStatus || '-' }}</template>
                                                <template v-else>{{ getStatusText(Number(item.status)) }}</template>
                                            </span>
                                            <span class="meta-divider">|</span>
                                            <span class="meta-item publish-time">{{ $t('work.contentPage.publishTimee') }}：{{ formatDate(item.createTime) }}</span>
                                            <span class="meta-divider" v-if="item.categoryId == '1'">|</span>
                                            <span class="meta-item valid-time" v-if="item.categoryId == '1'">{{ $t('work.contentPage.validTime') }}：{{ formatDate(item.approveTime) }}</span>
                                            <span class="meta-divider">|</span>
                                            <span class="meta-item author">{{ $t('work.detailedArticle.author') }}：{{ item.author }}</span>
                                            <span class="meta-divider">|</span>
                                            <span class="meta-item view-count">{{ $t('work.detailedArticle.viewCount') }}：{{ item.viewCount }}</span>
                                        </div>
                                                </div>
                                            </el-col>
                                        </el-row>
                        </div>
                    </div>

                    <!-- 添加分页组件 -->
                    <div class="pagination-container">
                        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                            :page-sizes="[5, 10, 20, 30]" :total="total" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" 
                            :total-text="t('work.pagination.total')"
                            :page-size-text="t('work.pagination.itemsPerPage')"
                            :prev-text="t('work.pagination.prev')"
                            :next-text="t('work.pagination.next')"
                            :jumper-text="t('work.pagination.jumper')"
                            layout="total, sizes, prev, pager, next, jumper"
                            background size="small" />
                    </div>
                </el-card>
            </div>
        </el-col>
        <el-col :span="1"></el-col>
    </el-row>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
const route = useRoute();

import { ref, onMounted, computed } from 'vue'
import { Expand, SortDown, SortUp, Connection, View, CaretBottom, Search, Close, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import NavigationBar from '../../components/navigationBar/navigationBar.vue'
import { contentPageTagPage, contentPageGetlist } from '../../api/contentPage/contentPage.ts'
import { contentPagePage, contentPageRemove,contentPagesubscription,contentPagegetsubscription } from '../../api/favPage/favPage.ts'
import { getCloudFileURL, attachmentviewcount } from '@/api/getCloudFileUrl.ts'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const navigationBar = ref()

// 定义数据类型
interface Tag {
    id: string;
    tagNameCn: string;
    tagNameEn?: string;
    createTime?: string;
    updateTime?: string;
    status?: number;
}

// 定义动态表单项接口
interface DynamicFormItem {
    id: string;
    createUser?: string;
    createDept?: string | null;
    createTime?: string;
    updateUser?: string;
    updateTime?: string;
    status?: number;
    isDeleted?: number;
    tenantId?: string;
    categoryId?: number;
    modelCode: string;
    modelType: 'txt' | 'date' | 'pullTochose' | 'pullTochoses' | 'dateRange';
    modelNameEn: string;
    modelDescEn: string;
    modelNameCn: string;
    modelDescCn: string;
    assType?: string;
    assValue?: string;
    modelLength?: number;
    queriesFlag?: number;
    requiredFlag?: number;
    viewFlag?: number;
    sortKey?: number;
    paymentFlag?: string;
    dictsList?: any | null;
    predefinedValList?: Array<{label?: string, value: string}> | string[] | null;
    modelValue?: any; // 用户输入的值
    selectValues?: string[]; // Added for 'pullTochoses'
    dateRangeValue?: [string, string]; // Added for 'dateRange'
    modelValueStart?: string; // dateRange 开始日期
    modelValueEnd?: string; // dateRange 结束日期
    loading?: boolean;
    filteredOptions?: SelectOption[];
}

// 添加选项接口
interface SelectOption {
    label: string;
    value: string;
    dictKey?: string;
    dictValue?: string;
}

interface Regulation {
    id?: string;
    title: string;
    tag?: string[];
    url?: string;
    coverPlan?: string;
    status?: string;
    createTime?: string;
    updateTime?: string;
    viewCount?: number;
    labels?: string;
    createUser?: string;
    content?: string;
    contentType?: string;
    description?: string;
    knowledgeAttachmentList?: any[];
    knowledgeTagEntityList?: { name: string; value: string; tagNameCn: string; }[];
    categoryId?: string;
    categoryNameCn?: string;
    categoryName?: string;
    author?: string;
    approveTime?: string;
    publishTime?: string;
    knowledgeVO?: {
        categoryName: string;
    };
    standardName?: string;
    ltsTitle?: string;
    ltsStatus?: string;
}

const collapseActive = ref<string[]>([])
const loading = ref(false)
const tagList = ref<Tag[]>([])
const regulations = ref<Regulation[]>([])
// 定义动态表单项列表
const dynamicFormItems = ref<DynamicFormItem[]>([]);

// 添加分页相关的响应式变量
const currentPage = ref(1)
const pageSize = ref(5)
const total = ref(0)

//跳转详情
const pushToDetail = (item: Regulation) => {
    // If the item is a URL type article, open the URL directly
    if (item.contentType === 'url' && item.content) {
        window.open(item.content, '_blank')
        return
    }
    
    // Otherwise, navigate to the detail page as usual
    window.open('/knowledge/detailedArticle/detailedArticle?id=' + item.id + '&categoryId=' + item.categoryId)
}

//附件下载
const downloadFile = (item: any) => {
    getCloudFileURL({ cloudID: item.cloudId, systemID: 1, networkType: 2 }).then((res: any) => {
        console.log("item=>", item);
        const ids = [{ id: item.id.toString() }]
        attachmentviewcount(ids).then((res: any) => {
            if (res?.code === 200) {
                console.log("file download count=>", res);
            }
        })
        window.open(res)
    })
}

// 添加文本截断方法
const truncateText = (text: string | undefined, length: number) => {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
};

const searchObj = ref({
    searchQuery: '',
    searchType: '全部',
    publishDateRange: '全部',
    selectedTags: [] as string[],
    enactmentDate: '',
    sortType: '',
    ageGroup: '',
    range: '',
    region: '',
    area: '',
    province: '',
    docType: '',
    keywords: '',
    categoryId: ''
})

// 获取标签列表
const getTagsList = async () => {
    try {
        loading.value = true;
        const params = {
            current: 1,
            size: 100  // 获取足够多的标签
        };
        const response = await contentPageTagPage(params);
        console.log('标签列表响应:', response);

        if (response?.data?.records && Array.isArray(response.data.records)) {
            // 过滤掉禁用状态(status=0)的标签，只保留启用状态(status=1)的标签
            tagList.value = response.data.records.filter((tag: Tag) => tag.status === 1);
            console.log('标签列表:', tagList.value);
        } else {
            console.warn('未获取到标签数据');
            tagList.value = [];
        }
    } catch (error) {
        console.error('获取标签列表失败:', error);
        tagList.value = [];
    } finally {
        loading.value = false;
    }
};

// 获取下拉选项列表
const getOptionsList = (formItem: DynamicFormItem): SelectOption[] => {
    // 判断 assType 类型
    if (formItem.assType === 'predefined' && formItem.predefinedValList) {
        // 处理 predefinedValList 可能是字符串数组的情况
        if (Array.isArray(formItem.predefinedValList) && formItem.predefinedValList.length > 0) {
            // 判断是字符串数组还是对象数组
            if (typeof formItem.predefinedValList[0] === 'string') {
                // 字符串数组转换为对象数组
                return (formItem.predefinedValList as string[]).map(item => ({ 
                    label: item, 
                    value: item 
                }));
            } else {
                // 已经是对象数组类型，确保每个对象都有 label
                return (formItem.predefinedValList as Array<{label?: string, value: string}>).map(item => ({
                    label: item.label || item.value,
                    value: item.value
                }));
            }
        }
    } else if (formItem.assType === 'dictionary') {
        // 如果是字典类型，优先使用 dictsList
        if (formItem.dictsList && Array.isArray(formItem.dictsList) && formItem.dictsList.length > 0) {
            // 将 dictsList 转换为选项数组，使用 dictValue 作为 label，dictKey 作为 value
            return formItem.dictsList.map((item: { dictValue: string; dictKey: string }) => ({
                label: item.dictValue,
                value: item.dictKey
            }));
        }
        
        // 如果 dictsList 不存在或为空，尝试从缓存中获取
        const key = `dictionary_${formItem.assValue}`;
        const cachedDict = localStorage.getItem(key);
        
        if (cachedDict) {
            try {
                const parsed = JSON.parse(cachedDict);
                // 确保解析的数据符合 SelectOption 接口
                return parsed.map((item: any) => ({
                    label: item.label || item.value,
                    value: item.value
                }));
            } catch (e) {
                console.error('解析字典缓存数据失败:', e);
                return getMockDictionary(formItem.assValue);
            }
        } else {
            return getMockDictionary(formItem.assValue);
        }
    }
    
    return [];
};

// 字典数据缓存
const dictionaryCache = ref<{[key: string]: Array<{label: string, value: string}>}>({});

// 获取模拟字典数据
const getMockDictionary = (dictCode?: string): SelectOption[] => {
    if (!dictCode) return [];
    
    switch (dictCode) {
        case 'region':
            return [
                { label: '华东', value: 'east' },
                { label: '华北', value: 'north' },
                { label: '华南', value: 'south' },
                { label: '西北', value: 'northwest' },
                { label: '西南', value: 'southwest' },
                { label: '东北', value: 'northeast' }
            ];
        case 'docType':
            return [
                { label: '标准', value: 'standard' },
                { label: '法规', value: 'regulation' },
                { label: '指南', value: 'guide' },
                { label: '政策', value: 'policy' },
                { label: '通知', value: 'notice' }
            ];
        default:
            return [
                { label: '选项1', value: 'option1' },
                { label: '选项2', value: 'option2' },
                { label: '选项3', value: 'option3' }
            ];
    }
};

// 加载字典数据
const loadDictionary = async (dictCode?: string) => {
    if (!dictCode) return;
    
    // 缓存键名
    const key = `dictionary_${dictCode}`;
    
    // 如果已经在加载中，不重复加载
    if (dictionaryCache.value[key]) {
        return;
    }
    
    try {
        // 这里应该调用字典接口获取数据
        // 由于接口不存在，我们使用模拟数据并存入缓存
        const mockData = getMockDictionary(dictCode);
        
        // 更新缓存
        dictionaryCache.value[key] = mockData;
        
        // 同时存入localStorage作为持久化缓存
        localStorage.setItem(key, JSON.stringify(mockData));
        
        // 强制更新视图
        dynamicFormItems.value = [...dynamicFormItems.value];
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};

// 获取模型列表和动态表单项
const getModelList = async (categoryId?: string) => {
    try {
        loading.value = true;
        const response = await contentPageGetlist({ categoryId: categoryId || searchObj.value.categoryId });
        
        if (response && response.data && Array.isArray(response.data)) {
            dynamicFormItems.value = response.data.map((item: DynamicFormItem) => {
                // 添加新的属性
                const formItem = { 
                    ...item, 
                    modelValue: undefined,
                    loading: false,
                    filteredOptions: undefined
                };
                
                if (item.assType === 'dictionary' && item.assValue) {
                    loadDictionary(item.assValue);
                }
                
                return formItem;
            });
        } else {
            dynamicFormItems.value = [];
        }
        
        console.log('获取到的动态表单项:', dynamicFormItems.value);
        
        if (dynamicFormItems.value.length === 0 && collapseActive.value.includes('1')) {
            collapseActive.value = [];
        }
    } catch (error) {
        console.error('获取模型列表失败:', error);
        dynamicFormItems.value = [];
        if (collapseActive.value.includes('1')) {
            collapseActive.value = [];
        }
    } finally {
        loading.value = false;
    }
};

// 添加categoryId的响应式变量
const categoryId = ref<string | null>(null);

// 页面加载时获取数据
onMounted(() => {
    getTagsList();
    
    const queryTitle = route.query.query as string;
    if (queryTitle) {
        searchObj.value.searchQuery = queryTitle;
        saveSearchHistory(queryTitle);
    }
    
    // 获取订阅状态
    getSubscriptionStatus();
    
    handleSearch();
});

// 接收来自NavigationBar的数据更新
const handleUpdateRegulations = (params: any) => {
    console.log('NavigationBar params:', params);
    categoryId.value = params.categoryId; // 更新分类 ID
    currentPage.value = 1; // 重置分页页码
    
    // 调用getModelList获取动态表单项
    getModelList(params.categoryId);
    
    // 调用搜索方法加载数据
    handleSearch();
};

// 搜索函数
const handleSearch = async () => {
    try {
        loading.value = true;
        
        // 构建搜索参数
        const params: any = {
            keyword: searchObj.value.searchQuery || '',
            searchTypes: searchObj.value.searchType === '全部' ? null : [searchObj.value.searchType],
            publishTimeRange: searchObj.value.publishDateRange === '全部' ? 'all' : searchObj.value.publishDateRange,
            tagIds: searchObj.value.selectedTags.length > 0 ? searchObj.value.selectedTags : null,
            orderField: searchObj.value.sortType,
            categoryId: categoryId.value || navigationBar.value?.activeIndex,
        };
        
        // 添加动态表单项的值
        const modelList = dynamicFormItems.value
            .filter(item => {
                // 根据不同类型判断是否有值
                if (item.modelType === 'dateRange') {
                    return (item.modelValueStart && item.modelValueEnd);
                } else {
                    return item.modelValue !== undefined && item.modelValue !== '';
                }
            })
            .map(item => {
                if (item.modelType === 'dateRange') {
                    return {
                        modelCode: item.modelCode,
                        modelValueStart: item.modelValueStart,
                        modelValueEnd: item.modelValueEnd,
                        modelType: item.modelType
                    };
                } else {
                    return {
                        modelCode: item.modelCode,
                        modelValue: item.modelValue,
                        modelType: item.modelType
                    };
                }
            });
            
        // 如果有动态表单项值，添加到参数中，否则不传该字段
        if (modelList.length > 0) {
            params.knowledgeModelRelationDTOList = modelList;
        }

        console.log('搜索参数:', params);
        const response = await contentPagePage(params, {current: currentPage.value, size: pageSize.value});
        console.log('搜索响应:', response);

        if (response?.data?.records) {
            regulations.value = response.data.records;
            total.value = response.data.total || 0;
        }
    } catch (error) {
        console.error('搜索失败:', error);
        ElMessage.error(t('work.favPage.searchFailed'));
    } finally {
        loading.value = false;
    }
};

// 切换标签选择
const toggleTag = () => {
    console.log('选中的标签:', searchObj.value.selectedTags);
    // 移除自动搜索,只在用户点击搜索按钮时才触发搜索
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
    pageSize.value = val
    currentPage.value = 1 // 重置到第一页
    handleSearch()
    // 滚动到列表顶部
    const listElement = document.querySelector('.regulation-list-main')
    if (listElement) {
        listElement.scrollTop = 0
    }
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
    currentPage.value = val
    handleSearch()
    // 滚动到列表顶部
    const listElement = document.querySelector('.regulation-list-main')
    if (listElement) {
        listElement.scrollTop = 0
    }
}

// 导出需要的属性和方法
defineExpose({
    handleUpdateRegulations
})

// 获取排序文本
const getSortText = (type: string) => {
    switch (type) {
        case 'desc': return t('work.favPage.publishTimeDesc');
        case 'asc': return t('work.favPage.publishTimeAsc');
        case 'relation': return t('work.favPage.relevance');
        case 'viewcount': return t('work.favPage.viewCount');
        default: return t('work.favPage.defaultSort');
    }
}

// 添加获取空状态描述的计算属性
const getEmptyDescription = computed(() => {
    return searchObj.value.searchQuery 
        ? t('work.favPage.noResultsFound', { query: searchObj.value.searchQuery })
        : t('work.favPage.noData');
});

const handleCommand = (e: any) => {
    searchObj.value.sortType = e
    handleSearch() // 当排序改变时，触发搜索
}

// 添加搜索历史相关的响应式变量
const suggestions = ref<string[]>([]);

// 删除搜索历史记录
const deleteSearchHistory = (index: number) => {
    let searchHistory = JSON.parse(localStorage.getItem('favPageSearchHistory') || '[]');
    searchHistory.splice(index, 1);
    localStorage.setItem('favPageSearchHistory', JSON.stringify(searchHistory));
    suggestions.value = searchHistory;
    if (searchHistory.length === 0) {
        suggestions.value = [];
    }
};

// 保存搜索历史的方法
const saveSearchHistory = (searchText: string) => {
    if (!searchText.trim()) return;
    
    // 获取搜索历史并排除空字符串
    let searchHistory = JSON.parse(localStorage.getItem('favPageSearchHistory') || '[]');
    searchHistory = searchHistory.filter((item: string) => item && item.trim() !== '');
    if (!Array.isArray(searchHistory)) {
        searchHistory = [];
    }

    // 如果已存在相同的搜索记录，先删除旧的
    const index = searchHistory.indexOf(searchText);
    if (index > -1) {
        searchHistory.splice(index, 1);
    }

    // 添加新的搜索记录到开头
    searchHistory.unshift(searchText);

    // 只保留最近10条记录
    if (searchHistory.length > 10) {
        searchHistory = searchHistory.slice(0, 10);
    }

    // 保存更新后的历史记录
    localStorage.setItem('favPageSearchHistory', JSON.stringify(searchHistory));
    console.log('保存搜索历史:', searchHistory);
};

// 处理搜索框获得焦点
const handleInputFocus = () => {
    // 获取搜索历史并过滤空字符串
    let searchHistory = JSON.parse(localStorage.getItem('favPageSearchHistory') || '[]');
    searchHistory = searchHistory.filter((item: string) => item && item.trim() !== '');
    
    // 只在没有当前搜索内容时显示建议
    if (searchObj.value.searchQuery === '' && searchHistory.length > 0) {
        // 限制只显示最近5条历史记录
        suggestions.value = searchHistory.slice(0, 5);
        console.log('显示搜索历史:', suggestions.value);
    }
};

// 监听搜索框输入
const handleInput = () => {
    if (searchObj.value.searchQuery === '') {
        handleInputFocus(); // 如果输入框为空，显示历史记录
    } else {
        suggestions.value = []; // 如果有输入内容，清空建议列表
    }
};

// 处理点击搜索历史项
const pushToContent = (title: string) => {
    searchObj.value.searchQuery = title;
    suggestions.value = []; // 立即清空建议列表
    saveSearchHistory(title); // 保存到搜索历史
    handleSearch();
};

// 处理搜索框失去焦点
const handleInputBlur = () => {
    // 延迟隐藏建议列表，以允许点击建议项
    setTimeout(() => {
        suggestions.value = [];
    }, 300);
};

// 处理搜索按钮点击
const handleSearchClick = () => {
    const searchText = searchObj.value.searchQuery.trim();
    if (searchText) {
        saveSearchHistory(searchText);
    }
    suggestions.value = []; // 清空建议列表
    handleSearch();
};

/* const getStatusText = (status: number | string | undefined) => {
    if (status === 1 || status === '1') return t('work.contentPage.active');
    if (status === 0 || status === '0') return t('work.contentPage.inactive');
    return t('work.contentPage.unknownStatus');
}; */

// 取消收藏的方法
const cancelFavorite = (id: string | undefined) => {
    if (!id) {
        ElMessage.warning(t('work.favPage.articleIdCannotBeEmpty'));
        return;
    }
    
    console.log('取消收藏参数:', { id: id });
    
    // 调用取消收藏接口，传递id参数
    contentPageRemove({
        id: id
    }).then((res: any) => {
        if (res.code === 200 || res.success) {
            ElMessage.success(t('work.favPage.cancelFavoriteSuccess'));
            // 从列表中移除该项
            regulations.value = regulations.value.filter(item => item.id !== id);
            total.value -= 1;
        } else {
            ElMessage.error(res.message || res.msg || t('work.favPage.cancelFavoriteFailed'));
        }
    }).catch((error: any) => {
        console.error('取消收藏失败:', error);
        ElMessage.error(t('work.favPage.cancelFavoriteFailed'));
    });
};

// Added for 'pullTochoses'
const handleMultiSelectChange = (formItem: DynamicFormItem) => {
    // 将多选值用逗号拼接为字符串保存到modelValue中
    if (formItem.selectValues && formItem.selectValues.length > 0) {
        formItem.modelValue = formItem.selectValues.join(',');
    } else {
        formItem.modelValue = '';
    }
    console.log('Multi-select change:', formItem.selectValues, 'joined value:', formItem.modelValue);
};

// 处理日期范围变化
const handleDateRangeChange = (formItem: DynamicFormItem) => {
    if (formItem.dateRangeValue && formItem.dateRangeValue.length === 2) {
        formItem.modelValueStart = formItem.dateRangeValue[0];
        formItem.modelValueEnd = formItem.dateRangeValue[1];
    } else {
        formItem.modelValueStart = '';
        formItem.modelValueEnd = '';
    }
    console.log('Date range change:', formItem.dateRangeValue, 'start:', formItem.modelValueStart, 'end:', formItem.modelValueEnd);
};

// 添加计算属性来判断是否显示折叠面板
const showCollapsePanel = computed(() => {
    // 如果选择的是"全部"分类（categoryId为0或者未定义），则不显示
    const isAllCategory = !categoryId.value || categoryId.value === '0' || navigationBar.value?.activeIndex === '0';
    
    // 如果动态表单项为空，也不显示
    const hasDynamicFormItems = dynamicFormItems.value && dynamicFormItems.value.length > 0;
    
    return !isAllCategory && hasDynamicFormItems;
});

// 重置搜索条件
const resetSearch = () => {
    searchObj.value.searchQuery = '';
    searchObj.value.searchType = '全部';
    searchObj.value.publishDateRange = '全部';
    searchObj.value.selectedTags = [];
    searchObj.value.enactmentDate = '';
    searchObj.value.sortType = '';
    searchObj.value.ageGroup = '';
    searchObj.value.range = '';
    searchObj.value.region = '';
    searchObj.value.area = '';
    searchObj.value.province = '';
    searchObj.value.docType = '';
    searchObj.value.keywords = '';
    searchObj.value.categoryId = '';
    
    // 重置动态表单项的值
    dynamicFormItems.value.forEach(item => {
        item.modelValue = undefined;
        if (item.selectValues) {
            item.selectValues = [];
        }
        if (item.modelType === 'dateRange') {
            item.dateRangeValue = undefined;
            item.modelValueStart = undefined;
            item.modelValueEnd = undefined;
        }
    });
    
    currentPage.value = 1;
    handleSearch();
};

// 获取图标类型函数
const getFileIcon = (filename: string) => {
    const fileType = filename.split(".").pop()?.toLowerCase();
    switch (fileType) {
        case "docx": return "/knowledge/knowledgeBaseHome/worddocicon.svg";
        case "doc": return "/knowledge/knowledgeBaseHome/worddocicon.svg";
        case "pdf": return "/knowledge/knowledgeBaseHome/pdfdocicon.svg";
        case "pptx": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg";
        case "ppt": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg";
        case "xls": return "/knowledge/knowledgeBaseHome/exceldocicon.svg";
        case "xlsx": return "/knowledge/knowledgeBaseHome/exceldocicon.svg";
        case "txt": return "/knowledge/knowledgeBaseHome/txtdocicon.svg";
        case "md": return "/knowledge/knowledgeBaseHome/xmldocicon.svg";
        case "png": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "jpg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "jpeg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "gif": return "/knowledge/knowledgeBaseHome/videodocicon.svg";
        case "bmp": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "ico": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "zip": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "rar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "7z": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "tar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "gz": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "bz2": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "iso": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "dmg": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        default: return "/knowledge/knowledgeBaseHome/generaldocicon.svg";
    }
};

// 添加订阅相关的状态
const isSubscribed = ref(false); // 默认未订阅状态

// 切换订阅状态的方法
const toggleSubscribe = async () => {
    try {
        // 根据当前状态确定要发送的订阅状态
        const subStatus = isSubscribed.value ? 0 : 1;
        // 调用订阅接口
        const response: any = await contentPagesubscription({ subStatus });
        if (response && (response.code === 200 || response.success)) {
            // 更新订阅状态
            isSubscribed.value = !isSubscribed.value;
            ElMessage.success(isSubscribed.value ? t('work.favPage.subscribeSuccess') : t('work.favPage.cancelSubscribeSuccess'));
        } else {
            ElMessage.error(response?.msg || t('work.favPage.operationFailed'));
        }
    } catch (error) {
        console.error('订阅操作失败:', error);
        ElMessage.error(t('work.favPage.operationFailed'));
    }
};

// 获取订阅状态
const getSubscriptionStatus = async () => {
    try {
        const response: any = await contentPagegetsubscription({});
        console.log('获取订阅状态响应:', response);
        
        if (response && (response.code === 200 || response.success)) {
            // 后端返回的data是字符串格式的"1"，需要进行字符串比较
            // data为"1"时，设置为已订阅状态
            // data为"0"时，设置为未订阅状态
            isSubscribed.value = response.data === "1" || response.data === 1;
            console.log('解析后的订阅状态:', response.data, isSubscribed.value);
        }
    } catch (error) {
        console.error('获取订阅状态失败:', error);
    }
};

// 处理搜索过滤
const handleFilter = (searchValue: string, formItem: DynamicFormItem) => {
    if (!searchValue) {
        formItem.filteredOptions = getOptionsList(formItem);
        return;
    }

    const options = getOptionsList(formItem);
    const lowerSearchValue = searchValue.toLowerCase();
    formItem.filteredOptions = options.filter((item: SelectOption) => {
        return item.label.toLowerCase().includes(lowerSearchValue);
    });
};

// 添加handleDropdownVisible方法
const handleDropdownVisible = (visible: boolean, modelCode: string) => {
    if (visible) {
        // 获取select元素
        const selectEl = document.querySelector(`[data-model-code="${modelCode}"]`);
        if (selectEl) {
            // 获取输入框的宽度
            const inputWidth = selectEl.getBoundingClientRect().width;
            // 设置下拉框的宽度
            const dropdownEl = document.querySelector(`.select-dropdown-${modelCode}`);
            if (dropdownEl) {
                (dropdownEl as HTMLElement).style.width = `${inputWidth}px`;
                (dropdownEl as HTMLElement).style.minWidth = `${inputWidth}px`;
                (dropdownEl as HTMLElement).style.maxWidth = `${inputWidth}px`;
            }
        }
    }
};

// 添加日期格式化方法
const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return '-';
    // 如果日期格式为 yyyy-MM-dd HH:mm:ss，截取前10位即可得到 yyyy-MM-dd
    if (dateString.length >= 10) {
        return dateString.substring(0, 10);
    }
    return dateString;
};

const getStatusText = (status: number | undefined): string => {
    switch (status) {
        case 1: return t('work.contentPage.active');
        case 0: return t('work.contentPage.inactive');
        default: return t('work.contentPage.unknownStatus');
    }
};
</script>

<style scoped lang="scss">
:deep(.el-select__tags-text){
  max-width: 60px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep(.el-dropdown .el-dropdown-link) {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

:deep(.pagination-container .el-select__wrapper.el-tooltip__trigger.el-tooltip__trigger) {
  height: 20px;
  width: 80px;
  
}
/* .el-card__body{
    margin-top: -10px;
} */
:deep(.el-select__wrapper.el-tooltip__trigger.el-tooltip__trigger){
    height: 36px;

}
.el-form.el-form--small.el-form--label-right{
margin-top: 10px !important;
margin-bottom: -28px !important;
}
.container::v-deep {
    .el-form-item__label {
        text-align: right !important;
        justify-content: flex-end !important;
        white-space: nowrap !important;
        min-width: 120px !important; /* 减小标签宽度 */
        padding-right: 12px !important;
    }
    
    .el-form-item .el-form-item__label-wrap, 
    .el-form-item .el-form-item__label {
        display: flex !important;
        align-items: center !important;
        white-space: nowrap !important;
        justify-content: flex-end !important;
    }
    
    .form-label-right .el-form-item__label {
        display: flex !important;
        justify-content: flex-end !important;
        align-items: center !important;
    }

    // 添加下拉框样式
    .el-select {
        width: 100% !important;

        .el-input {
            width: 100% !important;
        }

        .el-input__wrapper {
            width: 100% !important;
        }
    }

    // 确保下拉菜单宽度与输入框一致
    .el-select__popper {
        min-width: unset !important;
        width: var(--el-select-width, 100%) !important;
    }

    // 调整下拉菜单位置
    .el-popper {
        margin-top: 0 !important;
    }

    .el-select .el-select__tags {
        width: 100% !important;
    }

    // 输入框样式统一
    .el-input__wrapper {
        border-radius: 0;
        width: 100% !important;
    }

    .el-input {
        width: 100% !important;
    }

    // 日期选择器样式统一
    .el-date-editor {
        width: 100% !important;
    }

    .tag-select {
        width: 100% !important;
        .el-select__tags {
            overflow: hidden;
        }
    }

    .el-radio__input {
        border-radius: 0 !important;
    }

    .el-radio__inner {
        border-radius: 0 !important;
    }

    .el-radio__input.is-checked .el-radio__inner {
        border-radius: 0 !important;
    }

    .el-collapse-item__wrap {
        padding: 20px 0px;

        .el-collapse-item__content {
            padding-bottom: 0;
        }
    }

    .el-card {
        padding: 0;
    }

    .el-collapse-item__header {
        border-bottom: none;
    }
    .el-input__inner{
        margin-left:-1px ;
    }

    .el-input__wrapper {
        border-radius: 0;
        //margin-left: -11px;
    }
    .el-input__prefix-inner{
        margin-left: -20px;
    }

    .el-checkbox-button {
        &.is-checked {
            .el-checkbox-button__inner {
                border-radius: 0;
                border: 1px solid rgb(255, 102, 0);
            }

        }

        .el-checkbox-button__inner {
            border-radius: 0;
            border-color: rgb(220, 223, 230);
        }


    }

    //padding: 20px;
    background: #f5f7fa;

    .search-box {
        margin-bottom: 20px;
        margin-top: 20px;
        //padding-top: 0px;

        .search-header {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .filter-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            min-height: 40px;
        }

        .filter-label {
            width: 100px;
            color: #606266;
            margin-right: 10px;
            line-height: 1.5;
            display: flex;
            align-items: center;
        }

        .checkbox-group {
            display: flex;

            .el-checkbox {
                width: initial;
            }

            // align-items: center;
            // grid-template-columns: repeat(4, auto);
            // /* 每行 4 个 */
            // gap: 10px 20px;
            /* 适当增加间距 */
        }

        .el-checkbox {
            margin-right: 25px;
            white-space: nowrap;
            display: flex;
            align-items: center;
            width: 70px;
        }

        .el-collapse {
            .el-collapse-item {
                position: relative;

                .el-collapse-item__header {
                    width: 100px;
                    position: absolute;
                    right: 0;
                    top: -52px;
                    color: rgb(234, 101, 13);
                    margin-right: -38px;
                }

                .el-collapse-item__arrow {
                    display: none;
                }
            }
        }

        .expanded-conditions {
            margin-top: 15px;
        }

        .collapse-conditions {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }
    }




    .filter-label {
        width: 100px;
        color: #606266;
        margin-right: 10px;
        margin-top: 3px;
        line-height: 1.5;
        display: flex;
        align-items: center;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        /* 为了给复选框之间提供间距 */
    }

    .regulation-list {

        .regulation-list-main {
            max-height: calc(100vh - 357px);
            overflow-x: hidden;
            overflow-y: auto;
            padding-right: 12px; /* 添加右侧内边距，使滚动条向右移动 */
            margin-right: -12px; /* 添加负边距，确保内容区域宽度不变 */

            .regulation-item {
                padding: 15px 0;
                border-bottom: 1px solid #ebeef5;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .block {
                    height: 100px;
                    width: 100%;
                    overflow: hidden;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                }

                .block img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .regulation-header {
                    margin-bottom: 8px;
                }

                .title-container {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                    .title {
                        font-weight: bold;
                        cursor: pointer;
                        color: rgb(0, 0, 0);
                        transition: color 0.3s ease-in-out;
                        display: flex;
                        align-items: center;

                        &:hover {
                            color: rgb(189, 49, 36);
                        }
                    }

                .tag-container {
                        display: flex;
                        align-items: center;
                    gap: 8px;
                    margin-left: 10px;
                }

                .tag-item {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 12px;
                    color: rgb(255, 102, 0);
                }

                .regulation-category {
                    display: inline-block;
                    font-size: 12px;
                    color: #ffffff;
                    margin-bottom: 8px;
                    background-color: rgb(255, 181, 131);
                    display: inline-block;
                    padding: 1px 8px;
                    border-radius: 10px;
                    white-space: nowrap;
                    width: auto;
                    max-width: fit-content;
                }

                .standard-name {
                    color: #909399;
                    font-size: 12px;
                    margin-bottom: 0px;
                    line-height: 1.4;
                    margin-top: 0px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .regulation-content {
                    color: #606266;
                    font-size: 13px;
                    margin-bottom:18px;
                    line-height: 1.4;
                    max-height: 2.8em; /* 限制高度为两行 */
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .regulation-attachments {
                    margin-bottom: 1px;
                    color: rgb(57, 111, 198);
                    position: absolute; /* 固定位置 */
                    bottom: 15px; /* 调整位置，更靠近元信息行 */
                    width: 100%;
                }

                .attachment-container {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 6px;
                    margin-bottom: 5px;
                }

                .attachment-item {
                    max-width: 120px;
                    display: inline-flex;
                    align-items: center;
                    cursor: pointer;
                    font-size: 13px;
                    overflow: hidden;
                    
                    &:hover {
                        color: #ff6600;
                    }
                }

                .attachment-name {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 60px; /* 考虑图标宽度 */
                }

                /* 修改元信息行的样式 */
                .regulation-meta {
                    color: #909399;
                    font-size: 12px; /* 调整回正常大小 */
                    margin: 8px 0;
                    padding: 3px 0;
                    display: flex;
                    flex-wrap: nowrap; /* 不换行 */
                    align-items: center;
                    width: 100%; /* 确保占满容器宽度 */
                    line-height: 1.3; /* 略微增加行高 */
                }

                .regulation-meta .meta-item {
                    display: inline-flex;
                    align-items: center;
                    white-space: nowrap; /* 确保每项内容不会被拆分 */
                    flex-shrink: 0; /* 防止被压缩 */
                    letter-spacing: -0.2px; /* 稍微放宽字母间距 */
                    padding: 0;
                    margin: 0;
                }

                .regulation-meta .meta-divider {
                    margin: 0 19px;
                    color: #dcdfe6;
                    display: inline-flex;
                    align-items: center;
                    flex-shrink: 0;
                }

                .favorite-action {
                    margin-left: auto;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    color: #ff6600;

                    &:hover {
                        color: #ff6600;
                    }
                }
            }
        }


    }

    .title-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .tag-container {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 10px;
    }

    .tag-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: rgb(255, 102, 0);
    }

    .active-tag {
        color: white !important;
        background-color: rgb(234, 101, 13) !important;
        font-weight: bold;
    }

    .pagination-container {
        display: flex;
        position: relative;
        justify-content: flex-end;
        align-items: center;

        :deep(.el-pagination) {
            //padding: 30px 0;
            justify-content: center;

            .el-pagination__total {
                font-size: 13px;
            }

            .el-pagination__sizes {
                margin-left: 15px;
            }

            .btn-prev,
            .btn-next {
                padding: 0 10px;
                min-width: 35px;
            }

            .el-pager li {
                margin: 0 3px;
                min-width: 30px;
                border-radius: 2px;
            }
        }
    }

    .mx-1 {
        cursor: pointer;
        transition: all 0.3s;
        padding: 4px 8px;
        border-radius: 4px;

        &:hover {
            color: #e86b1f;
            background-color: rgba(232, 107, 31, 0.1);
        }
    }

    .el-select {
        .el-input__inner {
            text-align: left;
        }
    }
    
    // 确保下拉菜单中的选项文本左对齐
    .el-select-dropdown__item {
        text-align: left;
    }

    }

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        color: #909399;
        font-size: 14px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.suggestions-list {
    position: absolute;
    z-index: 1000;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    max-height: 130px;
    //overflow-y: auto;
    width: 100%;
    top: 100%;
    left: 0;
    margin-top: 2px;

    ul {
        padding: 0;
        margin: 0;
        list-style: none;
    }

        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        height: 20px;

            &:hover {
                background-color: #f5f7fa;
            }

        span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            }

            .delete-icon {
                margin-left: 8px;
            color: #909399;
            cursor: pointer;

                &:hover {
                color: #ff6600;
            }
        }
    }
}

.image-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 4px;
}

.attachment-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 5px;
}

.attachment-item {
    max-width: 120px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-size: 13px;
    overflow: hidden;
    
    &:hover {
        color: #ff6600;
    }
}

.attachment-name {
    white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
    max-width: 60px; /* 考虑图标宽度 */
}

/* 添加一个新样式用于展开条件按钮 */
.expand-button {
    cursor: pointer;
                white-space: nowrap;
    display: flex;
    align-items: center;
    margin-right: 20px;
}

/* 调整按钮组样式 */
.button-group {
    display: flex;
    align-items: center;
}

/* 展开条件按钮样式 */
.expand-conditions-btn {
    position: absolute; 
    right: 15px; 
    top: 12px;
    z-index: 10;
}

.expand-conditions-btn .el-button,
.collapse-button {
    color: rgb(234, 101, 13); 
    font-size: 14px; 
    white-space: nowrap;
    padding: 4px 16px;
    display: flex;
    align-items: center;
}

.expand-conditions-btn .el-button span,
.collapse-button span {
    margin-right: 5px;
}

.container::v-deep .expand-conditions-btn .el-button:hover,
.container::v-deep .expand-conditions-btn .el-button:focus,
.container::v-deep .collapse-button:hover,
.container::v-deep .collapse-button:focus {
    color: rgb(234, 101, 13);
    background-color: transparent;
}

/* 订阅按钮容器样式 */
.subscribe-button-container {
  //position: absolute;
  margin-top: -1px;
  right: 20px;
  z-index: 10;
  justify-content: flex-end;
}

/* 订阅按钮基础样式 */
.subscribe-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 已订阅状态 - 灰色 */
.subscribed {
  background-color: #909399;
  color: white;

  &:hover {
    background-color: #7c7f86;
  }

  &:active {
    background-color: #6c6e72;
  }
}

/* 未订阅状态 - 橙色 */
.unsubscribed {
  background-color: #ff6600;
  color: white;

  &:hover {
    background-color: #e55c00;
  }

  &:active {
    background-color: #d45500;
  }
}

// 添加全局样式以确保下拉菜单宽度正确
:deep(.el-select-dropdown.el-popper) {
    min-width: unset !important;
    width: var(--el-select-width) !important;
}

:deep(.el-select-dropdown__wrap) {
    max-height: 274px !important;
    overflow-x: hidden !important;
}

/* 添加自定义下拉框样式 */
:deep(.custom-select) {
    width: 100% !important;

    .el-input {
        width: 100% !important;
    }

    .el-input__wrapper {
        width: 100% !important;
    }

    /* 下拉菜单样式 */
    &.el-select {
        --el-select-width: 100%;
    }
}

/* 确保下拉菜单宽度与输入框一致 */
:deep(.el-select__popper[x-placement^="bottom"]) {
    width: var(--el-select-width, 100%) !important;
    min-width: 0 !important;
    max-width: 100% !important;
}

/* 调整下拉菜单选项的样式 */
:deep(.el-select-dropdown__item) {
    padding: 0 12px !important;
    height: 34px !important;
    line-height: 34px !important;
    text-align: left !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 多选标签的样式 */
:deep(.custom-select .el-select__tags) {
    width: 100% !important;
    flex-wrap: wrap !important;
}

/* 强制下拉框宽度 */
:deep(.el-select__popper.el-popper[role="tooltip"]) {
    width: var(--el-select-width, 100%) !important;
    min-width: 0 !important;
    max-width: 100% !important;
}

:deep(.el-select-dropdown.el-popper) {
    width: var(--el-select-width, 100%) !important;
    min-width: 0 !important;
    max-width: 100% !important;
}

:deep(.el-select-dropdown__wrap) {
    max-height: 274px !important;
    overflow-x: hidden !important;
}

/* 确保选项内容超出时显示省略号 */
:deep(.el-select-dropdown__item span) {
    display: block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 展开条件区域内的下拉框样式 */
:deep(.el-collapse-item__content) {
    .el-select {
        width: 100% !important;
    }
    
    /* 固定下拉菜单宽度 */
    .el-popper.el-select__popper {
        position: absolute;
        width: 270px !important;
        min-width: 270px !important;
        max-width: 270px !important;
        transform-origin: center top;
        
        .el-select-dropdown {
            width: 270px !important;
            min-width: 270px !important;
            max-width: 270px !important;
        }
        
        .el-select-dropdown__wrap {
            width: 270px !important;
            max-width: 270px !important;
        }
        
        .el-scrollbar__view {
            width: 270px !important;
        }
        
        .el-select-dropdown__list {
            width: 270px !important;
        }
        
        .el-select-dropdown__item {
            width: 270px !important;
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            box-sizing: border-box !important;
        }
    }
}

/* 处理多选标签的情况 */
:deep(.el-select .el-select__tags) {
    width: 100% !important;
    flex-wrap: wrap !important;
}

/* 确保滚动条不影响宽度 */
:deep(.el-scrollbar) {
    overflow-x: hidden !important;
    
    .el-scrollbar__bar.is-horizontal {
        display: none !important;
    }
}


.search-input {
    width: 100%;
    :deep(.el-input__wrapper) {
        border-radius: 4px;
    }
}

.date-picker {
    width: 100%;
    :deep(.el-input__wrapper) {
        border-radius: 4px;
    }
}

:deep(.el-date-editor.el-input) {
    width: 100%;
    .el-input__wrapper {
        border-radius: 4px !important;
    }
}

:deep(.el-date-editor) {
    .el-input__wrapper {
        border-radius: 4px !important;
    }
    .el-range-input {
        border-radius: 4px !important;
    }
    .el-range-separator {
        padding: 0 5px;
    }
}

:deep(.el-range-editor.el-input__wrapper) {
    border-radius: 4px !important;
    padding: 0 10px;
}

:deep(.el-date-editor--daterange) {
    --el-date-editor-width: 100%;
    .el-range-separator {
        padding: 0 5px;
    }
}
</style>