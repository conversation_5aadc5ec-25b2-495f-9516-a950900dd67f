<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_template_newTemplate" id="sgs_smart_customer_template_newTemplate">
            <div class="title_justify">
                <div style="display: inline">
                    <h3>New {{formPurpose}} Template</h3>
                </div>
            </div>
            <el-card :body-style="{padding:'10px'}" v-if="loadingForm">
                <el-form size="mini" ref="template_form" :model="templateForm" label-position="top" label-width="300px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="Template Name" prop="formName" :rules="{ required: true, message: 'Template Name is required', trigger: ['blur','change']}">
                                <el-input placeholder="Template Name" type="text" @input="templateForm.formAliasName = templateForm.formName" clearable v-model="templateForm.formName"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="Customer Name" prop="customerGroupCode" :rules="{ required: true, message: 'Customer Name is required', trigger: ['blur','change']}">
                                <el-select clearable filterable
                                           placeholder="Customer Name"
                                           :disabled="!!pageTemplateId"
                                           style="width: 100%"
                                           @change="changeCustomer"
                                           v-model="templateForm.customerGroupCode">
                                    <el-option
                                            v-for="(ms,index) of filterList.customerList"
                                            :key="'ms_'+index"
                                            :label="ms.customerGroupName"
                                            :value="ms.customerGroupCode">
                                        {{ms.customerGroupName }} ({{ms.customerGroupCode}})
                                    </el-option>
                                    <el-option v-if="role.isSGS" label="General" value="general"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="Product Line" prop="buCode" :rules="{ required: true, message: 'Product Line is required', trigger: ['blur','change']}">
                                <el-select
                                        placeholder="Product Line"
                                        :disabled="!!pageTemplateId"
                                        style="width: 100%"
                                        clearable
                                        filterable
                                        v-model="templateForm.buCode"
                                        @change="changeBu()"
                                >
                                    <el-option
                                            v-for="(pl,index) of filterList.productLineList"
                                            :key="'pl_'+index"
                                            :label="pl.productLineName"
                                            :value="pl.productLineCode"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item :label="formPurpose+' Type'" prop="customerCategory" :rules="{ required: true, message: 'Material Type is required', trigger: ['blur','change']}">
                                <el-select clearable filterable
                                           :placeholder="formPurpose+' Type'"
                                           style="width: 100%"
                                           v-model="templateForm.customerCategory">
                                    <el-option
                                            v-for="(mt,index) of (filterList.materialTypeList[language] || [])"
                                            :key="'mt_'+index"
                                            :label="mt.label"
                                            :value="mt.code"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col>
                            <el-form-item
                                    label="Required Sessions"
                                    prop="generalFieldConfig"
                                    :rules="generalFieldConfigRules['configRule']"
                            >
                                <el-button v-if="!templateForm.generalFieldConfig || templateForm.generalFieldConfig.filter(g=>g.display-1==0).length==0" type="primary" @click="addRequiredSession()">Add Required Session</el-button>
                                <el-button-group v-if="templateForm.generalFieldConfig && templateForm. generalFieldConfig.filter(g=>g.display-1==0).length>0">
                                    <el-button
                                            v-for="(btn,btnInd) in templateForm.generalFieldConfig.filter(g=>g.display-1==0)"
                                            :key="'temp_btn_'+btnInd"
                                            @click="addRequiredSession(1)">
                                        {{btn.sectionName || btn.section}}
                                    </el-button>
                                </el-button-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row :gutter="20">
                    <el-col :span="12">
                        Template Fields
                    </el-col>
                    <el-col :span="12" style="text-align: right">
                        <el-button type="primary" @click="addField" size="mini">Add Field</el-button>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col>
                        <el-table
                                ref="newMaterialTemplateTable"
                                :data="fieldList"
                                :max-height="500"
                                size="mini"
                                class="sgs_table_new_material_template_table"
                                fit
                                border
                                resizable
                        >
                            <el-table-column width="50" type="index">
                                <template slot="header">#</template>
                            </el-table-column>
                            <el-table-column prop="dispalyName" label="Field Label EN" :min-width="220"></el-table-column>
                            <el-table-column prop="fieldLabelName" label="Field Label CN" :min-width="220">
                                <template slot-scope="{row}">
                                    {{((row.displayNameMultLanguage || []).find(d=>d.languageCode=='CHI') || {displayName:''}).displayName }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="fieldCode" label="Field Code" :min-width="220"></el-table-column>
                            <el-table-column prop="fieldType" label="Input Type" :min-width="220"></el-table-column>
                            <el-table-column prop="mandatoryFlag" label="Mandatory" :min-width="220">
                                <template slot-scope="{row}">
                                    {{row.mandatoryFlag-1==0?'Yes':'No'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="formUniqueFlag" label="Check Unique" :min-width="220">
                                <template slot-scope="{row}">
                                    {{row.formUniqueFlag-1==0?'Yes':'No'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="modifiedBy" label="Updated By" :min-width="220"></el-table-column>
                            <el-table-column prop="modifiedDate" label="Update Time" :min-width="220"></el-table-column>
                            <el-table-column prop="id" fixed="right" width="150px">
                                <template slot="header">
                                    Action
                                </template>
                                <template slot-scope="{row,$index}">
                                    <el-tooltip content="Edit"  placement="top">
                                        <i class="el-icon-edit-outline menu-icon" @click="editField(row)"></i>
                                    </el-tooltip>
                                    <el-tooltip content="Remove"  placement="top">
                                        <el-popconfirm
                                                confirm-button-text='Confirm'
                                                cancel-button-text='Cancel'
                                                icon="el-icon-info"
                                                icon-color="red"
                                                title="Remove the data?"
                                                @confirm="cancelField(row,$index)"
                                        >
                                            <i slot="reference" class="el-icon-delete menu-icon"></i>
                                        </el-popconfirm>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                </el-row>
                <el-row style="padding-top: 20px">
                    <el-col style="text-align: center">
                        <el-button size="medium" type="primary" @click="previewTemplate">Preview</el-button>
                        <el-button size="medium" type="primary" @click="saveTemplate">Save</el-button>
                        <el-button size="medium" type="info" @click="cancelCreate">Close</el-button>
                    </el-col>
                </el-row>
            </el-card>
            <!-- template fields-->
            <el-dialog
                    title="Add Template Field"
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    v-dialog-drag
                    :lock-scroll="false"
                    custom-class="add_template_dia"
                    :visible.sync="showAddTemplateDia"
                    width="60%">
                <template-field
                    v-if="showAddTemplateDia"
                    :field-obj="fieldObj"
                    :standard-dff-list="standDffList.filter(s=>!generalMappingCustomerFieldCodes.includes(s.customerFieldCode.toLowerCase()))"
                    :all-field-config="fieldList"
                    ref="templateField"
                ></template-field>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="saveTemplateField" v-loading="saveFieldLoading">Save</el-button>
                    <el-button type="info" @click="showAddTemplateDia=false">Cancel</el-button>
                </span>
            </el-dialog>
            <!-- required session-->
            <el-dialog
                    title="Add Required Sessions"
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    v-dialog-drag
                    :lock-scroll="false"
                    custom-class="add_required_sessions_dia"
                    :visible.sync="showAddRequiredDia"
                    top="8vh"
                    width="70%">
                    <required-session
                            v-if="showAddRequiredDia && formPurpose !='Product'"
                            ref="requiredSession"
                            :form-purpose="formPurpose"
                            :standard-dff-list="standDffList.filter(sf=>!(fieldList || []).map(f=>f.fieldCode.toLowerCase()).includes(sf.customerFieldCode.toLowerCase()))"
                            :field-config="templateForm.generalFieldConfig"
                    >
                    </required-session>
                    <product-required-session
                            v-if="showAddRequiredDia && formPurpose =='Product'"
                            ref="requiredSession"
                            :form-purpose="formPurpose"
                            :standard-dff-list="standDffList.filter(sf=>!(fieldList || []).map(f=>f.fieldCode.toLowerCase()).includes(sf.customerFieldCode.toLowerCase()))"
                            :field-config="templateForm.generalFieldConfig"
                    ></product-required-session>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="saveRequiredSession" v-loading="saveRequiredSessionLoading">Save</el-button>
                    <el-button type="info" @click="showAddRequiredDia=false">Cancel</el-button>
                </span>
            </el-dialog>
            <el-dialog
                    title="Preview"
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    v-dialog-drag
                    :lock-scroll="false"
                    custom-class="preview_template_dia"
                    :visible.sync="showPreviewTemplateDia"
                    top="8vh"
                    width="90%">
                    <div v-if="showPreviewTemplateDia" style="height: 600px;overflow-y: auto">
                        <material-info
                                v-if="formPurpose=='Material'"
                                view-page="preview"
                                :template-data="templateData">
                        </material-info>
                        <product-info
                                v-if="formPurpose=='Product'"
                                view-page="preview"
                                :template-data="templateData"
                        ></product-info>
                    </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="info" @click="showPreviewTemplateDia=false">Cancel</el-button>
                </span>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
    import api from "@/api/newSamples";
    import {mapGetters} from "vuex";
    import { getProductLine,queryBuSetting,getCustomerGroup} from "@/api/common/index";
    import {validatenull,objectIsNull} from "@/util/validate";
    import TemplateField from "./templateField";
    import RequiredSession from "./requiredSession";
    import MaterialInfo from "../newMaterial/materialInfo";
    import _ from 'lodash'
    import ProductRequiredSession from "./productRequiredSession";
    import ProductInfo from "../newProduct/productInfo";
    import {LanguageEnums} from "@/commons/enums/LanguageEnums";
    export default {
        name: "newTemplate",
        data() {
            return {
                LanguageEnums:LanguageEnums,
                pageTemplateId:"",
                formPurpose:'Material',
                showAddTemplateDia:false,
                showAddRequiredDia:false,
                saveRequiredSessionLoading:false,
                showPreviewTemplateDia:false,
                fieldObj:{},
                standDffList:[],
                generalMappingCustomerFieldCodes:[],//general 部分已经选择的mapping字段
                pageLoading: false,
                loadingForm: true,
                saveFieldLoading:false,
                templateForm2:{},
                templateForm:{
                    systemCode: "SGSMart",
                    moduleCode: "TRF-Product-Header",
                    formType: "FORM",
                    formPurpose: "Material",
                    formSequence: "1",
                    formName:'',
                    formAliasName:'',
                    countryCode: "CN",
                    currentUser: "",
                    customerGroupCode:'',
                    customerNo:'',
                    buCode:'',
                    buName:'',
                    customerCategory:'',
                    generalFieldConfig:[]
                },
                generalFieldConfigRules:{
                   configRule:[
                       {required:true,message:'Please add required session',trigger:'change'},
                       {
                           required: true,
                           validator:(rule,value,callback)=>{
                                if(!value || !this.templateForm.generalFieldConfig || this.templateForm.generalFieldConfig.length==0){
                                    callback(new Error('Please add required session'))
                                }else{
                                    let displayConfigs = this.templateForm.generalFieldConfig.filter(c=>c.display-1==0);
                                    if(!displayConfigs || displayConfigs.length==0){
                                        callback(new Error('Please add required session which at least one section'))
                                    }else{
                                        callback();
                                    }
                                }
                           },
                           trigger:'change'
                       }
                   ]
                },
                filterList:{
                    productLineList:[],
                    customerList:[],
                    materialTypeList:{}
                },
                fieldList:[],
                templateData:{
                    generalFieldConfig:[],
                    fieldList:[]
                },
            }
        },
        methods: {
            changeCustomer(){
                if(!this.templateForm.customerGroupCode){
                    this.$set(this.templateForm,'customerNo','');
                    //清空field配置
                    this.fieldList = [];
                    return;
                }
                let customer = this.filterList.customerList.find(c=>c.customerGroupCode == this.templateForm.customerGroupCode);
                let {customerNo} = customer || {};
                this.$set(this.templateForm,'customerNo',customerNo);
                this.initStandardDffList();
            },
            changeBu(init=false){
                let bu =  this.filterList.productLineList.find(l=>l.productLineCode ==this.templateForm.buCode);
                this.templateForm.buName = (bu || {}).productLineName;
                if(!init){
                    this.filterList.materialTypeList = {};
                    this.templateForm.customerCategory = '';
                    //清空field配置
                    this.fieldList = [];
                }
                if(!this.templateForm.buCode){
                    return;
                }
                this.initTemplateType();
                this.initStandardDffList();
            },
            addField(){
                let {buCode,customerGroupCode} = this.templateForm;
                if(!buCode || !customerGroupCode){
                    this.$notify.warning("Please select Customer Name and Product Line first");
                    return;
                }
                this.fieldObj = {
                    relId:Math.random()
                };
                let mappingSection = this.templateForm.generalFieldConfig.find(c=>c.display-1==0 && c.useMapping==1);
                if(mappingSection && mappingSection.fieldList){
                    let generalMappingCustomerFieldCodes = mappingSection.fieldList.map(f=>f.customerFieldCode.toLowerCase());
                    this.generalMappingCustomerFieldCodes = generalMappingCustomerFieldCodes;
                }
                this.showAddTemplateDia = true;
            },
            addRequiredSession(){
                let {buCode,customerGroupCode} = this.templateForm;
                if(!buCode || !customerGroupCode){
                    this.$notify.warning("Please select Customer Name and Product Line first");
                    return;
                }
                this.showAddRequiredDia = true;
            },
            async saveRequiredSession(){
                let saveData =  this.$refs.requiredSession.getSaveData();
                if(!saveData){
                    return;
                }
                this.saveRequiredSessionLoading = true;
                //console.log("saveData",saveData);
                this.$set(this.templateForm,'generalFieldConfig',saveData);
                this.saveRequiredSessionLoading = false;
                this.showAddRequiredDia = false;
                this.$refs['template_form'].validate();
            },
            async saveTemplateField() {
                this.saveFieldLoading = true;
                let data = await this.$refs.templateField.getSaveData();
                console.log("save field data",data);
                if (!data) {
                    this.saveFieldLoading = false;
                    return
                }
                let existIndex = this.fieldList.findIndex(da=>da.relId==data.relId);
                if(existIndex>-1){
                    this.fieldList.splice(existIndex,1,data);
                }else{
                    this.fieldList.push(data);
                }
                this.saveFieldLoading = false;
                this.showAddTemplateDia = false;
            },
            previewTemplate(){
                let templateData = {
                    generalFieldConfig:this.templateForm.generalFieldConfig.filter(g=>g.display==1),
                    fieldList:this.fieldList
                }
                this.templateData = templateData;
                this.showPreviewTemplateDia = true;
            },
            saveTemplate(){
                this.$refs['template_form'].validate(valid=>{
                    if(!valid){
                        return;
                    }
                    let fieldList = JSON.parse(JSON.stringify(this.fieldList));
                    fieldList.forEach(f=>{
                        delete f.relId;
                    })
                    let saveData = Object.assign({},this.templateForm,{fieldList})
                    saveData.generalFieldConfig = saveData.generalFieldConfig.filter(g=>g.display==1);
                    let {customerCategory} = saveData;
                    let customerCategoryName = '';
                    (this.filterList.materialTypeList[this.language] || []).forEach(mt=>{
                        let {code,label} = mt;
                        if(code == customerCategory){
                            customerCategoryName = label;
                        }
                    })
                    saveData['customerCategoryName'] = customerCategoryName;
                    //console.log("需要保存的数据",saveData);
                    this.pageLoading = true;
                    api.saveDFFFormData(saveData).then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            let id = res.data.data;
                            this.$notify.success("Success");
                            let formPurpose = this.formPurpose
                            window.location.href=`/#/customer/template/templateDetail?id=${id}&formPurpose=${formPurpose}`;
                            window.location.reload();
                        }else{
                            this.$notify.error((res.data && res.data.message) || "Save Fail");
                        }
                        this.pageLoading = false;
                    }).catch(err=>{
                        console.log("err",err);
                    })
                })
            },
            cancelCreate(){
                let pathSuffix = _.lowerFirst(this.formPurpose);
                this.$router.push({
                    path:'/customer/template/'+pathSuffix,
                    query:{}
                })
            },
            editField(row){
                 this.fieldObj = row;
                 this.showAddTemplateDia = true;
            },
            cancelField(row,rowIndex){
                this.fieldList.splice(rowIndex,1);
            },
            initPage() {
                this.templateForm.currentUser = this.userInfo.userName;
                this.initProductLine();
                this.initCustomer();
                this.initRouteQuery();
            },
            initStandardDffList(){
                let {buCode,customerGroupCode} = this.templateForm;
                this.standDffList = [];
                if(!buCode || !customerGroupCode){
                    return;
                }
                this.pageLoading = true;
                api.queryCustomerField({buCode,customerGroupCode}).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        this.standDffList = res.data.data || []
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    console.log("err",err)
                })
            },
            initTemplateType(){
                let paramCode = this.formPurpose=='Material'?'MaterialType':'ProductCategory';
                let params ={
                    systemId:1,
                    groupCode:'Template_Product_Material_Type',
                    productLineCode:this.templateForm.buCode,
                    paramCode
                }
                this.filterList.materialTypeList = {};
                queryBuSetting(params).then(res=>{
                    console.log("query type list",res);
                    if(res.status==200 && res.data && res.data.data){
                        let {data} = res.data;
                        if(!data || data.length==0){
                            return;
                        }

                        data = data[0];
                        let {paramValue} = data;
                        try{
                            let values = JSON.parse(paramValue);
                            this.filterList.materialTypeList =values;
                        }catch (e) {}
                    }

                }).catch(err=>{

                })
            },
            initRouteQuery(){
                let {id,formPurpose} = this.$route.query;
                this.formPurpose = formPurpose || 'Material';
                this.templateForm.formPurpose = formPurpose || 'Material';
                if(!id){
                    return
                }
                this.pageTemplateId = id;
                //查询当前模板的配置数据
                this.pageLoading = true;
                let param = {
                    formId:id,
                    formPurpose:this.formPurpose,
                    queryAllVersion: 1,
                    rows: 20,
                    page:1
                }
                api.queryProductViewForm(param).then(async res=>{
                    //console.log("queryProductViewCustomerList",res)
                    if(res.status==200 && res.data && res.data.data && res.data.data.rows && res.data.data.rows.length>0){
                        let formObj = res.data.data.rows[0];
                        let {generalFieldConfig} = formObj;
                        let dffFieldResp = await api.queryDffFormAttrByDffFormIDList({dffIDList:[id]});
                        //console.log("queryDffFormAttrByDffFormIDList",dffFieldResp);
                        if(dffFieldResp.status!=200 ){
                            this.pageLoading = false;
                            this.$notify.error("Load Template Fail!");
                            return;
                        }
                        let fieldList = [];
                        if(dffFieldResp.data && dffFieldResp.data.length>0){
                            fieldList = dffFieldResp.data || [];
                            fieldList.sort((f1,f2)=>(f1.sequence-0)-(f2.sequence-0));
                            fieldList.forEach(f=>{
                                this.$set(f,'relId',f.id);
                            })
                        }
                        if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                            this.$notify.warning("No template data");
                            return;
                        }
                        this.fieldList = fieldList;
                        (generalFieldConfig || []).forEach(g=>{
                            g['display'] = 1;
                            (g.fieldList || []).forEach(f=>{
                                f['display'] = 1;
                            });
                        });
                        this.templateForm = Object.assign({},this.templateForm, formObj,{fieldList});
                        this.changeBu(true);
                    }else{
                        this.$notify.error("Load Template Fail!");
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    console.log("load template error",err);
                    this.$notify.error("Load Template Fail!");
                    this.pageLoading = false;
                })
            },
            initProductLine(){
                getProductLine().then(res => {
                    if(res.status==200 && res.data){
                        const data = res.data.data || [];
                        let currentUserPL = (this.userInfo.productLineCodes || '').split(",");
                        if(!this.userInfo.productLineCodes && this.userInfo.productLineCode=='all'){
                            this.filterList.productLineList = data;
                            return;
                        }
                        this.filterList.productLineList = data.filter(da=>currentUserPL.includes(da.productLineCode));
                    }
                });
            },
            initCustomer(){
                if(this.role.isSGS){
                    getCustomerGroup().then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            this.filterList.customerList = res.data.data || [];
                        }
                    }).catch(err=>{
                        console.log("查询customer err",err);
                    })
                }else{
                    let scmCustomerReq = {
                        page:1,
                        rows:9999,
                        list:[{
                            buCode : this.userInfo.productLineCode,
                            relationshipType : 'buyer',
                            customerNo : this.userInfo.bossNo
                        }]
                    }
                    let customerList = [];
                    api.queryScmCustomerList(scmCustomerReq).then(res=>{
                        if(res.status==200 && res.data && res.data.rows){
                            let customerData = res.data.rows;
                            customerData.forEach(c=>{
                                let {scmCustomerGroupCode,scmCustomerGroupName,scmCustomerNo,scmCustomerNameCN,scmCustomerNameEN} = c;
                                let customerName = '';
                                if(validatenull(scmCustomerGroupCode)){//客户
                                    if(this.language==this.LanguageEnums.CN.name){
                                        customerName = scmCustomerNameCN;
                                    }
                                    if(validatenull(customerName)){
                                        customerName=scmCustomerNameEN;
                                    }
                                }

                                customerList.push({
                                    customerGroupCode:scmCustomerGroupCode,
                                    customerGroupName:scmCustomerGroupName || customerName,
                                    bossNo:scmCustomerNo
                                })
                            })
                        }
                        let currentUserCusIndex = customerList.findIndex(c=>c.customerGroupCode==this.userInfo.customerGroupCode);
                        if(!objectIsNull(this.userInfo.customerGroupCode) && currentUserCusIndex==-1){
                            let groupObj = {
                                customerGroupCode : this.userInfo.customerGroupCode,
                                customerGroupName : this.userInfo.customerGroupName  || this.userInfo.customerName,
                                bossNo : this.userInfo.customerGroupCode?"":this.userInfo.bossNo
                            }
                            customerList.push(groupObj);
                        }
                        this.filterList.customerList = customerList.filter(c=>c.customerGroupCode);
                    }).catch(err=>{
                        if(!objectIsNull(this.userInfo.customerGroupCode)){
                            let groupObj = {
                                customerGroupCode : this.userInfo.customerGroupCode,
                                customerGroupName : this.userInfo.customerGroupName  || this.userInfo.customerName,
                                bossNo : this.userInfo.customerGroupCode?"":this.userInfo.bossNo
                            }
                            customerList.push(groupObj);
                        }
                        this.filterList.customerList = customerList;
                        console.log("query scm err",err)
                    })
                }
            },
            initTemplateCustomerList(){
                let currentUser = this.role.isSGS ? "" : this.userInfo.userName;
                let param = {
                    currentUser, //必传
                    formPurpose:this.formPurpose, //必传
                }
                api.queryProductViewCustomerList(param).then(res=>{
                    let customerList = [];
                    if(res.status==200 && res.data && res.data.status==200){
                        customerList = res.data.data;
                    }
                    let currentUserInConfig = customerList.find(c=>c.customerGroupCode==this.userInfo.customerGroupCode);
                    if(!currentUserInConfig){
                        let {customerGroupCode,customerGroupId,customerGroupName,bossNo} = this.userInfo;
                        if(customerGroupCode){
                            customerList.push({customerGroupCode,customerGroupId,customerGroupName,customerNo:bossNo})
                        }
                    }
                    this.filterList.customerList = customerList;
                })
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.userInfo.dimensions)) {
                    return false;
                } else {
                    if (this.userInfo.dimensions.hasOwnProperty(type)) {
                        if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {
            ...mapGetters([
                "permission",
                "userInfo",
                "language"
            ]),
            role() {
                return {
                    isSGS: this.haseRole("SGSUserRole", "SgsAdmin")|| this.haseRole("SGSUserRole", "SgsLabUser"),
                    isThirdPartyLab: this.haseRole('UserRole', 'ThirdPartyLab')
                }
            },
        },
        props: {},
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {ProductInfo, ProductRequiredSession, MaterialInfo, RequiredSession, TemplateField}
    }
</script>

<style lang="scss">
    .sgs_smart_customer_template_newTemplate {
        font-family: 'Arial' !important;
        background: #fff;
        padding: 24px 32px;

        .menu-icon{
            font-size: 20px;
            cursor: pointer;
            margin: 0 10px;
            color:#ff6600
        }

        table.requiredTable{
            .title_justify{
                text-align: justify;
                display: flex;
                justify-content: space-around;
                padding: 0;
                align-items: center;
            }
            td.section{
                vertical-align: baseline;
            }
           /* td{
                border:solid 1px #c8c8c8;
            }*/
            td.requiredTable_title{
                text-align: center;
            }
        }

        .sgs_table_new_material_template_table{
            .el-table--mini .el-table__cell{
                padding: 0 !important;
            }
        }
    }
</style>