<template>
    <div class="sgs_smart_common_careLabelInfoDia" id="sgs_smart_common_careLabelInfoDia">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane :label="'Buyer' + customerCode" name="buyerList"></el-tab-pane>
            <el-tab-pane label="General" name="generalList"></el-tab-pane>
        </el-tabs>

        <div v-if="activeName === 'buyerList' && showImg">
            <el-row class="module-margin-vertical">
                <el-col :span="4"> Reference Care Code:</el-col>
                <el-col :span="20">
                    <div class="search-btn">
                        <el-input v-model="rccCode"/>
                        <el-button type="primary" @click="queryCareLabelCode">Search</el-button>
                    </div>
                </el-col>
            </el-row>

            <el-table :data="rccDataList" class="module-margin-vertical">
                <el-table-column prop="" label="Action" width="80px">
                    <template #default="{ row }">
                        <input type="checkbox" checked/>
                    </template>
                </el-table-column>
                <el-table-column prop="rccCode" label="Reference Care Code(RCC)" width="280px"/>
                <el-table-column label="Image">
                    <template #default="{ row }">
                        <img
                                v-for="(item, index) in row.selectCareLabel"
                                :key="'img_' + index"
                                v-show="item.picture"
                                :src="'data:image/png;base64,' + item.picture"
                        />
                    </template>
                </el-table-column>
            </el-table>

            <el-table :data="rccDescDataList" class="module-margin-vertical">
                <el-table-column prop="" label="Action" width="80px">
                    <template #default="{ row }">
                        <input type="checkbox" v-model="row.selectDetailsDescription"/>
                    </template>
                </el-table-column>
                <el-table-column prop="careLableDetailsDescription" label="CareLabel Details Description"/>
            </el-table>
        </div>

        <div v-if="activeName === 'generalList' && showImg">
            <el-row>
                <el-col :span="4"> Country:</el-col>
                <el-col :span="8">
                    <el-select style="width: 100%" v-model="selectCountry" @change="changeCountry" filterable>
                        <el-option
                                v-for="(item, index) in countryArray"
                                :key="'count_' + index"
                                :value="item.countryRegionName"
                                :label="item.countryRegionName"
                        />
                    </el-select>
                </el-col>
                <el-col :span="12">
                    <transition-group style="display: inline">
                        <draggable
                                v-model="generalSelectCareLabelArray"
                                group="careLabel"
                                animation="300">
                            <template #item="{element}">
                                <img :src="'data:image/png;base64,' + element.picture"/>
                            </template>
                        </draggable>
                    </transition-group>
                    <a @click="clearCareLabel" v-show="generalSelectCareLabelArray.length > 0">
                        <el-icon>
                            <Remove/>
                        </el-icon>
                    </a>
                </el-col>
            </el-row>

            <el-table
                    class="module-margin-vertical"
                    :data="careLabelArray"
                    v-loading="imgTableLoading">
                <el-table-column prop="conditionType" label="Condition Type" width="120px"/>
                <el-table-column label="Care Image">
                    <template #default="{ row }">
                        <el-row :gutter="20">
                            <el-col
                                    v-for="(itemImg, indexImg) in (row.imgArray || []).slice(0, 12)"
                                    :key="'img_' + indexImg"
                                    :span="2"
                                    style="text-align: center"
                            >
                                <img
                                        @click="itemImg.checked = !itemImg.checked; checkboxChange()"
                                        style="cursor: pointer"
                                        :src="'data:image/png;base64,' + itemImg.picture"
                                /><br/>
                                <input
                                        type="checkbox"
                                        @change="checkboxChange"
                                        v-model="itemImg.checked"
                                        :checked="itemImg.checked"
                                        :true-value="true"
                                        :false-value="false"
                                        :key="indexImg"
                                />
                            </el-col>
                        </el-row>
                        <el-row :gutter="20" v-show="row.showMore">
                            <el-col
                                    v-for="(itemImg, indexImg) in (row.imgArray || []).slice(12)"
                                    :key="'img_' + indexImg"
                                    :span="2"
                                    style="text-align: center"
                            >
                                <img :src="'data:image/png;base64,' + itemImg.picture"/><br/>
                                <input
                                        type="checkbox"
                                        @change="checkboxChange(itemImg)"
                                        v-model="itemImg.checked"
                                        :checked="itemImg.checked"
                                        :true-value="true"
                                        :false-value="false"
                                        :key="indexImg"
                                />
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>
                <el-table-column width="80">
                    <template #default="{ row }">
                        <a v-if="row.imgArray.length > 12" @click="row.showMore = !row.showMore"
                           style="cursor: pointer">
                            MORE
                        </a>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div v-if="showDesc">
            <el-table
                    ref="descTable"
                    v-if="showDescTable"
                    :data="descMap[activeName]"
                    border
                    fit
                    size="mini"
                    @selection-change="handleSelectionChange"
            >
                <el-table-column  type="selection"/>
                <el-table-column prop="warningId" label="Text Code" width="120px"/>
                <el-table-column prop="warningDescription" label="Additional Care Text Information(ACI)Description"/>
            </el-table>
        </div>
    </div>
</template>

<script setup>
import {ref, reactive, onMounted, computed, defineProps, defineEmits, nextTick, defineExpose} from 'vue'
import productApi from '@/api/product.ts'
import {Remove, Delete, Plus} from '@element-plus/icons-vue'

import draggable from 'vuedraggable'

defineOptions({
    name: 'CareLabelInfoDia',
})
const props = defineProps({
    customerCode: {
        type: String,
        default: () => '',
    },
    careLabelObject: {
        type: Object,
        default: () => ({}),
    },
    selectImgIds: {
        type: String,
        default: () => '',
    },
    showImg: {
        type: Boolean,
        default: () => false,
    },
    showDesc: {
        type: Boolean,
        default: () => false,
    },
})

const emit = defineEmits(['update'])


// Data
const pageLoading = ref(false)
const showDescTable = ref(true)
const imgTableLoading = ref(false)
const activeName = ref('buyerList')
const rccDataList = ref([])
const rccDescDataList = ref([])
const careLabelArray = ref([])
const selectCountry = ref('')
const countryArray = ref([])
const descMap = ref({
    buyerList: [],
    generalList: [],
})
const rccCode = ref('')
const generalSelectCareLabelArray = ref([])

// Methods
const initPage = async () => {
    const paramGeneral = {
        warningType: 'General',
    }
    //查询洗语文本信息
    const resGeneral = await productApi.careLabelApi.queryWarning(paramGeneral)
    descMap.value.generalList = resGeneral || []

    if (props.customerCode) {
        const paramClient = {
            customerCode: props.customerCode,
            warningType: 'Client',
            customerType: 'Group',
        }
        //查询对应客户的洗语文本信息
        const resClient = await productApi.careLabelApi.queryWarning(paramClient)
        descMap.value.buyerList = resClient || []
    }

    //查询所有国家列表
    const resCountry = await productApi.careLabelApi.queryCareLabelCountry();
    console.log("resCountry", resCountry)
    countryArray.value = resCountry || []

    initObjData()
}

const initObjData = () => {
    if (!props.showImg || !props.careLabelObject || !props.careLabelObject.selectImgIds) {
        return
    }
    //初始化 或者回显
    const {radioType, selectCountry: selectedCountry} = props.careLabelObject
    activeName.value = radioType === 2 ? 'generalList' : 'buyerList'
    selectCountry.value = selectedCountry
    clearCareLabel()
    //回显图片
    queryCareImg(props.selectImgIds, () => {
        //触发勾选图片
        checkboxChange()
    })
}

const queryCareLabelCode = async () => {
    if (!props.customerCode || !rccCode.value) return
    rccDataList.value = []
    rccDescDataList.value = []

    const params = {
        countryOrRegion: '',
        usageType: '',
        requestType: 'Client',
        customerCode: props.customerCode,
        careLabelCode: rccCode.value,
        customerType: 'Group',
        careLabelDetailDescFlag: 'true',
    }

    const res = await productApi.careLabelApi.queryBuyerCareLabel(params)
    const {data} = res
    if (!data || data.length === 0) return

    let careLabelArr = data.slice(0, -2)
    rccDataList.value.push({selectCareLabel: careLabelArr, rccCode: rccCode.value})

    const detailDescObj = data[data.length - 2]
    const {careLabelDetailsDescription} = detailDescObj || {}
    rccDescDataList.value.push({
        selectDetailsDescription: !!careLabelDetailsDescription,
        careLableDetailsDescription,
    })
}

const changeCountry = () => {
    clearCareLabel()
    queryCareImg()
}

const clearCareLabel = () => {
    careLabelArray.value.forEach((item) => {
        if (item.imgArray) {
            item.imgArray.forEach((img) => {
                img.checked = false
            })
        }
    })
    generalSelectCareLabelArray.value = []
}

const queryCareImg = (selectImgIds, callback) => {
    const params = {
        countryOrRegion: selectCountry.value,
        usageType: 'Washing',
        requestType: 'General',
    }
    const selectIds = (selectImgIds || '').split(',')
    imgTableLoading.value = true
    try {
        //TODO Ken 这个接口内部有处理Others ，存在问题
        // 由于接口数据结构没有封装成对象结构，查询不到数据直接返回了NULL，
        // node中转无法处理非结构的null，会对请求类型为Others没有数据的接口报错
        // 处理方式是后端接口不要返回null
        productApi.careLabelApi.queryCareLabel(params, data => {
            data.forEach((da) => {
                da.showMore = false
                if (da.imgArray && da.imgArray.length > 0) {
                    da.imgArray.forEach((im) => {
                        im.checked = selectIds.includes(im.imageDetailId)
                    })
                }
            })
            careLabelArray.value = data
            if (callback) {
                callback()
            }
        })
    } finally {
        imgTableLoading.value = false
    }
}

const getSaveData = async () => {
    let result
    if (props.showImg) {
        result = await getImgData()
    }
    if (props.showDesc) {
        result = getDescData()
    }
    return result || Promise.reject('error')
}

const getImgData = async () => {
    let selectImgIds = ''
    let imgArray = []

    if (activeName.value === 'buyerList') {
        if (rccDataList.value.length > 0) {
            const imgIds = []
            rccDataList.value[0].selectCareLabel.forEach((c) => {
                imgIds.push(c.imageDetailId)
                imgArray.push(c)
            })
            selectImgIds = imgIds.join(',')
        }
    } else {
        selectImgIds = generalSelectCareLabelArray.value.map((c) => c.imageDetailId).join(',')
        imgArray = generalSelectCareLabelArray.value
    }

    if (!imgArray || imgArray.length === 0) {
        return {
            careLabelSeq: 1,
            productItemNo: ['SupplierCode_1'],
            imgArray: [],
            radioType: activeName.value === 'buyerList' ? 1 : 2,
            selectCountry: '',
            selectImgIds,
            imgPathYun: [],
            careLabelFileId: '',
            cloudId: '',
        }
    }

    const updateParam = {imgArray, orderId: 1}
    const uploadResult = await productApi.careLabelApi.uploadCareLabel(updateParam)
    if (uploadResult.status !== 200 || !uploadResult.data) {
        // 上传接口时不时会错误，返回空，这里处理一下 ，可以重复调用
        alert('Merge care image failed, please save again')
        return false
    }

    const uploadResp = uploadResult.data
    const {careLabelFileId} = uploadResp

    const imgPathResp = await productApi.careLabelApi.downloadPathById({key: careLabelFileId})
    const imgPathYun = imgPathResp ? [{
        path: imgPathResp,
        key: careLabelFileId
    }] : []

    return {
        careLabelSeq: 1,
        productItemNo: ['SupplierCode_1'],
        imgArray,
        radioType: activeName.value === 'buyerList' ? 1 : 2,
        selectCountry: selectCountry.value,
        selectImgIds,
        imgPathYun,
        careLabelFileId,
        cloudId: uploadResp.cloudId,
    }
}
const selectedRows = ref([]);
const handleSelectionChange = (rows) => {
    selectedRows.value = rows;
};
const getDescData = () => {
    const descSelection = selectedRows.value;
    const careInstruction = descSelection.map((d) => d.warningDescription).join('/')
    return {careInstruction}
}

const checkboxChange = () => {
    const selected = []
    careLabelArray.value.forEach((item) => {
        if (item.imgArray && item.imgArray.length > 0) {
            selected.push(...item.imgArray.filter((img) => img.checked))
        }
    })
    generalSelectCareLabelArray.value = selected
}

const handleClick = () => {
    showDescTable.value = false
    nextTick(() => {
        showDescTable.value = true
    })
}

onMounted(() => {
    initPage()
})

//方法暴露出去
defineExpose({getSaveData})

</script>

<style lang="scss">
@use '@/assets/style/unit.module.scss' as *;

.sgs_smart_common_careLabelInfoDia {
  font-family: 'Arial' !important;
  background: #fff;

  .search-btn {
    position: relative;

    .el-button {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .module-margin-vertical {
    margin-top: 20px;
  }
}
</style>
