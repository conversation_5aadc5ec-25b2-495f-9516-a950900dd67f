<template>
  <basic-container class="sgs_smart_newTestRequestForm">
    <el-row>
      <!-- 新建申请单 -->
      <!-- <el-button type="primary" v-if="permissionList.trfAddBtn" icon="el-icon-edit" size="medium" @click="addTrf()">
          {{$t('trf.createtrf')}}
        </el-button> -->
        <HomePageTop5>
          <template slot="trfStatus_slot" slot-scope="{row}">
            <AFLTRFStatus :key="row.trf_trfId" v-if="row.trf_header_productLineCode=='AFL' && row.trf_header_trfType=='30'" :status="(row.trf_header_trfStatus-0)"/>
            <TRFStatus v-else :key="row.trf_trfId" :status="(row.trf_header_trfStatus-0)" :reason="row.trf_header_pendingReason"/>
          </template>
          <template slot="trfNo_slot" slot-scope="{row}">
            <i v-show="row.trf_header_trfStatus == 2 && row.trf_header_isSubmissionTimeout === 1" class="el-icon-alarm-clock"
               title="Submission Timeout" style="cursor:pointer;color: red"></i>
            <a @click="trfDetailClick(row)" style="color: #FF6600;font-size: 14px;cursor:pointer">{{row.trf_header_customerReferenceNo || row.trf_trfNo}}</a>
          </template>
          <!--report no-->
          <template slot="reportNo_slot" slot-scope="{row}">
            <el-tooltip placement="top" :content="row.report_reportNo">
              <div class="sgs_smart_table_column_data">
                <i class="el-icon-document"
                   v-if="row.report_id_reportId"
                   @click="downloadReportFile(row.report_id_reportId)"
                   style="color: #FF6600;font-size: 14px;cursor:pointer">
                </i>
                {{ row.report_reportNo }}
              </div>
            </el-tooltip>
          </template>

          <!-- latest date-->
          <template slot="latest_slot" slot-scope="{row}">
            {{currentTz(row.updateDate)}} {{statusContent(row.trf_header_trfStatus-0)}}
          </template>
          <!--action -->
          <template slot="action_slot" slot-scope="{row}">
            <el-tooltip :content="$t('trfList.copyTrf')"  placement="top">
              <i class="el-icon-copy-document menu-icon"
                 v-if="permissionList.copyTrfBtn && userInfo.userMgtId ==  row.trf_header_createUserId && row.trf_header_trfType!='20'  && row.trf_header_trfSourceType!=3 "
                 @click="copyTRF(row)">
              </i>
            </el-tooltip>
            <el-tooltip :content="$t('operation.modify')"  placement="top">
              <el-button type="text"
                         size="medium"
                         icon="el-icon-edit menu-icon"
                         v-if="permissionList.trfEditBtn &&
                         userInfo.userMgtId == row.trf_header_createUserId &&
                         row.trf_header_trfSourceType!=3 &&
                         row.trf_header_trfStatus-0 <= 2"
                         @click="editTrf(row)">
              </el-button>
            </el-tooltip>


            <el-tooltip :content="$t('trfList.viewEfilling')"  placement="top">
              <i  class="el-icon-view menu-icon"
                  v-if="row.efilingIds && row.efilingIds.length"
                  @click="viewEfilling(row)"></i>
            </el-tooltip>
            <el-tooltip :content="$t('trfList.createEfilling')"  placement="top">
              <i class="el-icon-files menu-icon"
                 v-if="!row.efilingIds ||!row.efilingIds.length"
                 @click="handleEfilling(row)"></i>
            </el-tooltip>
          </template>
        </HomePageTop5>
    </el-row>

    <div class="text-center">
      <router-link target="_blank" :to="{ path: '/trf/newList' }">
        <el-button plain class="more-btn">{{$t('operation.more')}}<i class="glyphicon glyphicon-menu-right" aria-hidden="true"></i>
        </el-button>
      </router-link>
    </div>

    <chatDialog ref='chatDialog' @hideChatDialog='hideChatDialog'></chatDialog>
    <el-dialog
        title="eFiling"
        :visible.sync="eFillingDialogVisible"
        class="efiling-dialog"
        width="55%"
        append-to-body
        v-if="eFillingDialogVisible"
        >
            <eFillingForm ref="eFilling" :formModel="formModel" :productInfoList="productInfoList"></eFillingForm>
            <template slot="footer">
                <el-button type="primary" @click="handleSave(1)">{{ $t('crud.saveBtn') }}</el-button>
                <el-button type="success" @click="handleSave(2)">{{ $t('work.cpscTrfInfo.column.submit') }}</el-button>
                 <el-button @click="eFillingDialogVisible=false">{{ $t('crud.cancelBtn') }}</el-button>
            </template>
      </el-dialog>
  </basic-container>
</template>

<script>
import { add,doMapping,listForSelect,tradeSelect} from "@/api/cpscTrfInfo/cpscTrfInfo";
import {getList, downLoadFile, exportTrfs, getTrf_top,queryTrfDetail,submitRelation,getReportFileList} from "@/api/trf/trf";
import TabSelect from "@/components/dff/TabSelect";
import moment from 'moment'
import { mapGetters } from "vuex";
import XLSX from "xlsx";
import FileSaver from "file-saver";
import {objectIsNull,validatenull} from "@/util/validate";
import { getReports, getAflReports } from "@/api/trf/communication";
import {ProductLineEnums} from "@/commons/enums/BuEnums";
import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
import {deepClone} from '@/util/util'
import HomePageTop5 from "./HomePageTop5";
export default {
  name: "trfList",
  components: {
      HomePageTop5,
    TRFStatus: resolve => require(['@/components/trf/TRFStatus'], resolve),
    AFLTRFStatus: resolve => require(['@/components/trf/AFLTRFStatus'], resolve),
    TabSelect,
    chatDialog: resolve => require(['../../trf/form/chatDialog'], resolve),
    eFillingForm: resolve=>require(['@/views/trf/form/eFillingForm.vue'],resolve)
  },
  created() {
    this.debouncedQuery = this.$lodash.debounce(this.onSubmit, 500);
    console.log(this.userInfo.userMgtId);
    if (this.$route.query.from == 0) {
      this.query.order.properties.trfStatus = parseInt(this.$route.query.trfStatus);
      this.query.order.properties.startDate = this.$route.query.startDate;
      this.query.order.properties.endDate = this.$route.query.endDate;
      //this.onLoadFromWel();
    } else if (this.$route.query.from == 2) {
      //首页搜索
      this.query.order.properties.queryValue = this.$route.query.queryValue;
    } else if (this.$route.query.from == 1) {
      this.query.order.properties.startDate = this.$route.query.startDate;
      this.query.order.properties.endDate = this.$route.query.endDate;
      if (this.$route.query.reportResult == 'all') {
        this.query.order.properties.actualConclusion = null;
      } else {
        this.query.order.properties.actualConclusion = this.$route.query.reportResult;
      }
    }
    //如果没有时间传递 首次默认查询近30天的数据
    if (validatenull(this.query.order.properties.startDate)) {
      this.query.order.properties.startDate = moment().subtract('days', 180).format('YYYY-MM-DD');
    }
    if (validatenull(this.query.order.properties.endDate)) {
      this.query.order.properties.endDate = moment().format('YYYY-MM-DD');
    }

    // this.initDff();
    this.initOrderList()
  },

  data() {
    return {
      productInfoList:[
                ],
      eFillingDialogVisible:false,
      longWidth: false,
      submitDateSortInfo: '',
      buyerConclusionSortInfo: '',
      reportIssuedTimeInfo: '',
      trfNoInfo: 'info',
      submitDateSort: 'desc',
      buyerReviewConclusionSort: 'desc',
      reportAppoveDateSort: 'desc',
      trfNoSort: 'desc',
      sortRedio: '3',
      reportsLoading: false,
      isCheckTime: true,
      tableFlag: true,
      downLoading: false,
      excelData: [],
      expands: [],
      loading: false,
      trfData: [],
      selectedVal: [],
      dffTemplateData: [],
      checkedTimeOutTrf: false,
      getRowKeys(row) {
        return row.id
      },

      /*  queryForm: {
            trfNo: '',
            startDate: '',
            endDate: '',
            isSubmissionTimeout: null,
            trfStatus: '',
            columns: [],
        },*/
      query: {
        isShowReviewConclusion: false,
        pageNo: 1,
        pageSize: 5,
        total: 0,
        order: {
          properties: {
            queryValue: '',
            templateName: '',
            buyerCustomerGroupName: '',
            applicationNameEn: '',
            trfStatus: null,
            startDate: null,
            endDate: null,
            trfNo: '',
            reportNo: '',
            productDescription: '',
            styleNo: '',
            poNo: '',
            articleNo: '',
            pendingReason: '',
            reportConclusion: '',
            reviewConclusion: '',
            submitDateStr: null,
            isSubmissionTimeout: null,
            sampleReceiveDateStr: null,
            reportDateStr: null,
            labName: '',
            contactName: '',
            language: '',
            sortColumn: 't.update_time',//排序字段  首页查询传tr.update_time
            sort: 'desc',//排序方式
          }
        },
      },
      data: [],
      reports: [],
      trfParamForm: {
        startDate: '',
        endDate: '',
      },
      page: {
        pageSize: 5,
        currentPage: 1,
        total: 0
      },
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      chatItemIndex: '',
      minScreen: false
    }
  },
  computed: {
    ...mapGetters(["userInfo", "permission", "language",]),
    permissionList() {
      return {
        trfAddBtn: this.vaildData(this.permission['sgs:trfList:addTrf'], false),
        trfDownLoadBtn: this.vaildData(this.permission['sgs:trfList:downLoadTrf'], false),
        trfPrintBtn: this.vaildData(this.permission['sgs:trfList:printBtn'], false),
        trfEditBtn: this.vaildData(this.permission['sgs:trfList:editTrf'], false),
        copyTrfBtn: this.vaildData(this.permission['sgs:trfList:copyTrf'], false),
      };
    },
    //监听接收到的消息
    socketTask() {
      return this.$store.getters.socketTask
    },
  },
  watch: {
    socketTask() {
      if (this.socketTask) {
        if (this.chatItemIndex && this.trfData.length > 0) {
          this.trfData[this.chatItemIndex].imHasUnread = false
        }
        this.$refs.chatDialog.show()
      }
    },
    //监听语言变化
    language: function (newVal) {
      this.initOrderList();
    }
    /*'query.order.properties': {
        deep: true,
        handler(val, old) {
            /!*if ((val.startDate && !val.endDate ) || (val.endDate && !val.startDate)) {
                this.$info({message: 'Please select the correct start date and end date'})
                return false
            }*!/
            this.debouncedQuery()
        }
    },*/
    //监听语言变化
    /* language: function (newVal) {
         //重新获取一遍selectVal
         this.initDff();
         console.log(this.$refs.tabSelect);
         console.log(this.selectedVal);
        this.debouncedQuery()
      },*/
  },
  filters: {
    formtterDate: function (value) {
      if (!value) return ''
      value = value.toString();
      return moment(value).format("YYYY-MM-DD");
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.minScreen = document.body.offsetWidth <= 1440 ? true : false
      window.addEventListener("resize", () => {
        this.minScreen = document.body.offsetWidth <= 1440 ? true : false
        console.log(this.minScreen, document.body.offsetWidth)
      });
    })
  },
  methods: {
    getNewTrfNo() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以+1
            const day = now.getDate().toString().padStart(2, '0');
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            const endStr = Math.floor(Math.random() * 900) + 100;

            // 连接成纯数字字符串
            const formattedTime = `${year}${month}${day}${hours}${minutes}${seconds}${endStr}`;
            //const formattedTime = `${minutes}${seconds}`;
            return "eFiling"+formattedTime;
          },
          checkProductInfo(productInfoList){
            for(let productInfo of productInfoList){
              if(!productInfo||!productInfo.formList.length || !productInfo.formList.find(item=>item.primaryId==='yes')){
                this.$message.error(this.$t('work.cpscCustomerTrade.tip.primary'))
                return {
                  flag:false,
                  msg:this.$t('work.cpscCustomerTrade.tip.primary')
                }
              }
              if(!productInfo||!productInfo.formList.find(item=>item.primaryId==='yes').productIdType ||!productInfo.formList.find(item=>item.primaryId==='yes').productId){
                this.$message.error(this.$t('work.cpscCustomerTrade.tip.productInfo'))
                return {
                  flag:false,
                  msg:this.$t('work.cpscCustomerTrade.tip.productInfo')
                }
              }
              const {hasDuplicateIdType,hasDuplicateId} =  this.checkDuplicateProductInfo(productInfo.formList)
              if(hasDuplicateIdType) return {flag:false,msg:'ProductIdType Repeat'}
              if(hasDuplicateId) return {flag:false,msg:'ProductId Repeat'}
            }
            return {flag:true}
          },
          checkDuplicateProductInfo(data) {
            // 创建两个对象来分别存储 productIdType 和 productId 的计数
            const productIdTypeCount = {};
            const productIdCount = {};

            // 遍历数组中的每个实体
            for (const item of data) {
              const { productIdType, productId } = item;

              // 检查 productIdType 是否重复
              productIdTypeCount[productIdType] = (productIdTypeCount[productIdType] || 0) + 1;
              if (productIdTypeCount[productIdType] > 1) {
                return { hasDuplicateIdType: true, hasDuplicateId: false };
              }

              // 检查 productId 是否重复
              productIdCount[productId] = (productIdCount[productId] || 0) + 1;
              if (productIdCount[productId] > 1) {
                return { hasDuplicateIdType: false, hasDuplicateId: true };
              }
            }

            // 没有发现重复
            return { hasDuplicateIdType: false, hasDuplicateId: false };
          },
          async handleSave(trfStatus) {
                const fun=(trfStatus)=>{

                    let productInfoList = JSON.parse(JSON.stringify(this.$refs.eFilling.productInfoList))


                    const {flag,msg} = this.checkProductInfo(productInfoList)
                    let efilingFormData =''
                    if(flag){
                        let formModel =this.$refs.eFilling.newFormModel
                        // if(!formModel.referenceNo){
                        //     formModel.referenceNo= this.getNewTrfNo()
                        // }

                        efilingFormData=JSON.stringify({formModel,productInfoList})
                    //  trfObj.efilingList=efilingList

                    }else{
                        return
                    }
                    productInfoList.forEach(productInfo=>{
                    let form = JSON.parse(JSON.stringify({...this.$refs.eFilling.newFormModel,...productInfo}))
                    form.dataSource = 2;
                    form.trfInfoStastus = trfStatus;

                    //form.referenceNo = this.getNewTrfNo()
                    //获取 primay key
                    if (form.formList && form.formList.length) {
                    const primary = form.formList.find(item => item.primaryId === 'yes');

                    form.productId = primary.productId
                    form.productIdType = primary.productIdType

                    }
                    if (form.productModelName) {
                    form.productName = form.productModelName
                    }

                    form.productJson = JSON.stringify(productInfo)

                    // 点击save 和 submit时 新增数据，所以需要将 form中的id属性设置为null
                    add(form).then((res) => {
                        this.$message({
                        type: "success",
                        message: "Success!"
                        });

                    }, error => {
                        window.console.log(error);
                    });
                    })
                    submitRelation({trfId:this.$refs.eFilling.newFormModel.trfId,efilingFormData}).then(async res=>{
                            this.eFillingDialogVisible=false
                            await this.initOrderList()
                    })

                }

                if(trfStatus==1){
                    fun(trfStatus)
                }else{
                  let formValidResult =await this.$refs.eFilling.formValidate();
                    console.log("动态表单的check--eFilling:",formValidResult)
                    if(formValidResult){
                        fun(trfStatus)
                    }
                }
             },
             viewEfilling(row){
                this.$router.push('/cpscTrfInfo/cpscTrfInfo?trfNo='+row.trfNo)
             },
            async handleEfilling(row){
              console.log(row)
              this.productInfoList=[
                { referenceNo:this.getNewTrfNo()}
              ]
              this.formModel= {

                trfStatus:0,
                attachments:[]
              }
              this.eFillingDialogVisible=true
              const {data} = await queryTrfDetail({trfNo:row.trf_trfNo,trfId:row.trf_trfId,signature:row.signature})
              console.log(data)
              let buyerName =data.data.trfCustomer.buyerCustomerNameEn||data.data.trfCustomer.buyerCustomerGroupName
              const res =await listForSelect(buyerName)
              this.$refs.eFilling.getBuyerCustomerId(buyerName)
              const buyerCustomerList = res.data.data
              const buyer = buyerCustomerList.find(item=>item.bossName ==buyerName)

              let type= this.language =='zh-CN'?'CN':'EN' //EN
              const formData = JSON.parse(data.data.dffFormData)[type]

              if( !formData ||!formData.factoryName){
                let param ={}
                param.trfId=row.trf_trfId
                param.buyerCustomerId = buyer?buyer.id:undefined
                param.basicLabAddress =data.data.trfLab.labAddress
                param.basicServiceType =data.data.serviceType
                param.basicLabId =data.data.trfLab.labCode
                param.trfReferenceNo =row.trf_trfNo
                this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =data.data.trfAttachments
                this.$refs.eFilling.newFormModel =Object.assign(this.formModel,param)
                const mapping =  await doMapping({productLineCode:data.data.productLineCode,productCategory:'',customerCode:data.data.trfCustomer.buyerCustomerGroupCode,formData:JSON.stringify(formData),formList:JSON.stringify(JSON.parse(data.data.dffGridData)[type])})
                this.productInfoList= this.productInfoList.map(item=>{
                  return{...item,...mapping.data.data}
                })
              }else{
                const resTrade =await  tradeSelect('Manufacture', buyer? buyer.customerId:'',formData?formData.factoryName:'')
                const manufactureCustomerIds = resTrade.data.data
                const manufacture=manufactureCustomerIds.find(item=>item.tradeName==formData?formData.factoryName:'')
                const mapping =  await doMapping({productLineCode:data.data.productLineCode,productCategory:'',customerCode:data.data.trfCustomer.buyerCustomerGroupCode,formData:JSON.stringify(formData),formList:JSON.stringify(JSON.parse(data.data.dffGridData)[type])})
                let param ={}
                param.buyerCustomerId = buyer?buyer.id:undefined
                param.trfId=row.trf_trfId
                param.basicLabAddress =data.data.trfLab.labAddress
                // param.basicLabContactName=data.data.trfLabContact.contactName
                param.basicServiceType =data.data.serviceType
                param.basicLabId =data.data.trfLab.labCode
                param.trfReferenceNo =row.trf_trfNo
                console.log(param)
                this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =data.data.trfAttachments
                this.$refs.eFilling.newFormModel =Object.assign(this.formModel,param)
                console.log('newFormModel',this.$refs.eFilling.newFormModel)
                manufacture&&await this.$refs.eFilling.setPOC('Manufacture', buyer?buyer.customerId:'',formData?formData.factoryName:'')
                manufacture && this.$refs.eFilling.handleTradeChange(manufacture.id,manufacture)
                let manufactureSub ={}
                if(manufacture){
                  manufactureSub.manufactureContactId =manufacture.cpscCustomerTradeContactVO.id;
                  manufactureSub.manufactureContactName=manufacture.cpscCustomerTradeContactVO.contactName;
                  manufactureSub.manufactureContactTelephone=manufacture.cpscCustomerTradeContactVO.telphone;
                  manufactureSub.manufactureCustomerAddress =manufacture.tradeAddress
                  manufactureSub.manufactureCustomerAddressLocal=manufacture.tradeAddress2
                  manufactureSub.manufactureCustomerCityId =manufacture.tradeCity
                  manufactureSub.manufactureCustomerCityName=manufacture.tradeCity
                  manufactureSub.manufactureCustomerCountryId=manufacture.tradeCountryId
                  manufactureSub.manufactureCustomerCountryName=manufacture.tradeCountryName
                  manufactureSub.manufactureCustomerId=manufacture.id
                  manufactureSub.manufactureCustomerName=manufacture.tradeName
                  manufactureSub.manufactureCustomerNameLocal =manufacture.tradeName
                  manufactureSub.manufactureContactEmaill = manufacture.cpscCustomerTradeContactVitem
                }
                this.productInfoList= this.productInfoList.map(item=>{
                  return{...item,...mapping.data.data,...manufactureSub}
                })
                buyer&&this.$refs.eFilling.handleBuyerChange(buyer.id)
              }
            },
    currentTz(value,format){
      if (!value) return ''
      if(!format){
        format='YYYY-MM-DD HH:mm:ss';
      }
      value = tzFormChina(value,format);
      value = value.toString();
      return value;
    },
    tzToChina(val){
      if (!val) return ''
      let value = tzToChina(val);
      return value;
    },
    computedWidth(filedCode) {
      if(this.minScreen) {
        switch(filedCode) {
          case 'styleNo':
            return 130
            break;
          case 'poNo':
            return 130
            break;
          case 'productDescription':
            return 160
            break;
          case 'trfSubmissionDate':
            return 160
            break;
          default:
            return 180
        }
      } else {
        switch(filedCode) {
          case 'styleNo':
            return 160
            break;
          case 'poNo':
            return 160
            break;
          case 'productDescription':
            return 250
            break;
          case 'trfSubmissionDate':
            return 200
            break;
          default:
            return 180
        }
      }
    },
    statusContent(status) {
      let content = ''
      switch (status) {
        case 1 :
          content = this.$t('trfStatus.draft')
          break
        case  2 :
          content = this.$t('trfStatus.submitted')
          break
        case  3 :
          content = this.$t('trfStatus.preparation')
          break
        case  4 :
          content = this.$t('trfStatus.testing')
          break
        case  5 :
          content = this.$t('trfStatus.completed')
          break
        case  6 :
          content = this.$t('trfStatus.cancel')
          break
        case  11 :
          content = this.$t('trfStatus.pending')
          break
        case  12 :
          content = this.$t('trfStatus.pending')
          break
        default:
          content = null
      }
      return content
    },
    async sortChange(val) {
      console.log(val)
      if (val == '1' || val == 1) {
        this.query.order.properties.sortColumn = ' t.trf_submission_date ';
      } else if (val == '2' || val == 2) {
        this.query.order.properties.sortColumn = ' t.buyer_review_conclusion ';
      } else {
        this.query.order.properties.sortColumn = '';
      }
      await this.initOrderList();
    },
    async cancelSort() {
      this.query.order.properties.sortColumn = '';
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async submitDateSortClick() {
      this.submitDateSortInfo = 'info';
      this.buyerConclusionSortInfo = '';
      this.reportIssuedTimeInfo = '';
      this.trfNoInfo = '';
      if (this.submitDateSort == 'desc') {
        this.submitDateSort = 'asc'
      } else {
        this.submitDateSort = 'desc'
      }
      this.buyerReviewConclusionSort = 'desc';
      this.reportAppoveDateSort = 'desc';
      this.trfNoSort = 'desc';
      this.query.order.properties.sortColumn = ' t.trf_submission_date ';
      this.query.order.properties.sort = this.submitDateSort;
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async buyerConclusionSortClick() {
      this.submitDateSortInfo = '';
      this.buyerConclusionSortInfo = 'info';
      this.reportIssuedTimeInfo = '';
      this.trfNoInfo = '';
      if (this.buyerReviewConclusionSort == 'desc') {
        this.buyerReviewConclusionSort = 'asc'
      } else {
        this.buyerReviewConclusionSort = 'desc'
      }
      this.submitDateSort = 'desc'
      this.reportAppoveDateSort = 'desc';
      this.trfNoSort = 'desc';
      this.query.order.properties.sortColumn = ' t.buyer_review_conclusion ';
      this.query.order.properties.sort = this.buyerReviewConclusionSort;
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async reportDateSortClick() {
      this.submitDateSortInfo = '';
      this.buyerConclusionSortInfo = '';
      this.reportIssuedTimeInfo = 'info';
      this.trfNoInfo = '';
      if (this.reportAppoveDateSort == 'desc') {
        this.reportAppoveDateSort = 'asc'
      } else {
        this.reportAppoveDateSort = 'desc'
      }
      this.submitDateSort = 'desc'
      this.trfNoSort = 'desc';
      this.buyerReviewConclusionSort = 'desc'
      this.query.order.properties.sortColumn = ' tr.approved_date ';
      this.query.order.properties.sort = this.reportAppoveDateSort;
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async trfNoSortClick() {
      this.submitDateSortInfo = '';
      this.buyerConclusionSortInfo = '';
      this.reportIssuedTimeInfo = '';
      this.trfNoInfo = 'info';
      if (this.trfNoSort == 'desc') {
        this.trfNoSort = 'asc'
      } else {
        this.trfNoSort = 'desc'
      }
      this.submitDateSort = 'desc'
      this.reportAppoveDateSort = 'desc';
      this.buyerReviewConclusionSort = 'desc'
      this.query.order.properties.sortColumn = '';
      this.query.order.properties.sort = this.trfNoSort;
      this.query.pageNo = 1;
      await this.initOrderList();
    },


    //排序事件
    // async sortChange(val) {
    //     this.query.pageNo = 1;
    //     await this.initOrderList();
    // },
    // 打开聊天弹框
    openChat(item, index) {
      let trfNo = item.trfNo
      this.chatItemIndex = index
      console.log(this.chatItemIndex)
      let userInfo = window.localStorage.getItem('SGS-userInfo')
      userInfo = userInfo ? JSON.parse(userInfo) : ''
      if (userInfo) {
        let userId = userInfo.content.userMgtId
        this.$store.dispatch('webSocketInit', { trfNo: trfNo, userId: userId });
      }
    },
    // 监听沟通弹框隐藏
    async hideChatDialog() {
      this.chatItemIndex = ''
      await this.initOrderList()
    },
    trfDetailClick(row) {
      let hash = new Date().getTime() + '';
      //console.log(row)
      if (row.trf_header_trfType == 30) {
        window.open('/#/afl/trf/trfDetail?id=' + row.trf_trfId + '&title=' + row.trf_trfNo + '&trfNo=' + row.trf_trfNo + '&hash=' + hash + '&actionType=detail' + '&signature=' + row.signature, '_blank');
      } else {
        window.open('/#/trf/trfDetail?id=' + row.trf_trfId + '&title=' + row.trf_trfNo + '&trfNo=' + row.trf_trfNo + '&hash=' + hash + '&actionType=detail' + '&signature=' + row.signature, '_blank');
      }
    },
    trfReportChange(row, expandedRows) {
      this.$refs.trfTable.setCurrentRow();
      this.currentRow = row
      if (this.expands.join(',').indexOf(row.id) === -1) {
        this.expands = [this.currentRow.id]
        this.searchTrfReportList(row.id, row.productLineCode);
      } else {
        this.expands.splice(0, this.expands.length)
      }
    },
    async searchTrfReportList(trfId, productLineCode) {
      if (productLineCode ==  ProductLineEnums.AFL.CODE) {
        this.reportsLoading = true;
        let res = await getAflReports(trfId);
        this.reports = res.data.data;
        this.reportsLoading = false;
      } else {
        this.reportsLoading = true;
        let res = await getReports(trfId);
        this.reports = res.data.data;
        this.reportsLoading = false;
      }
    },
    async handelSubmit() {
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async trfStatusChange() {
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async reportConclusionChange() {
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async currentChange(pageCurrent) {
      this.query.pageNo = pageCurrent;
      await this.initOrderList();
    },
    getRowClassName({ row, rowIndex }) {
      if (row.trfStatus != 5) {
        return 'row-expand-cover';
      }
    },
    downloadReport(cloudId) {
      if (cloudId != '' && cloudId != null) {
        downLoadFile(cloudId).then(res => {
          var pdfUrl = res.data.data;
          this.downLoadReportOts(pdfUrl);
          /*this.$message({
              type: 'success',
              message: this.$t('api.success')
          });*/
        }, error => {
          this.$message.error(this.$t('api.error'));
        });
      }
      //window.open(pdfUrl, '_blank')
    },
    downLoadReportOts(pdfUrl) {
      window.open(pdfUrl, '_blank')
    },
    getReportColor(reportConclusion) {
      if (reportConclusion === 'Pass') {
        return 'green'
      } else if (reportConclusion === 'Fail') {
        return 'red'
      } else {
        return null
      }
    },
    downloadReportFile(reportId){
      if(!reportId){
        return
      }
      getReportFileList({reportIds:[reportId]}).then(res=>{
        if(res.status==200 && res.data && res.data.data && res.data.data.length>0){
          res.data.data.forEach(da=>{
            let {cloudId} = da;
            downLoadFile(cloudId).then(res => {
              var pdfUrl = res.data.data;
              window.open(pdfUrl, '_blank')
            }, error => {
              this.$message.error(this.$t('api.error'));
            });
          })
        }else{
          this.$message.error(this.$t('api.error'));
        }
      }).catch(err=>{
        this.$message.error(this.$t('api.error'));
      })
    },
    copyTRF(row) {
      this.actionTRF(row, 'Copy TRF', 'copy')
    },
    editTrf(row) {
      this.actionTRF(row, row.trf_trfNo, 'edit');
    },
    actionTRF(row, title, action) {
      let url = row.trf_header_trfType == 30 ? '/afl/trf/trfDetail' : '/trf/trfDetail';
      let flag = row.trf_header_trfType == 30 ? 3 : 1;
      let params = {
        id: row.trf_trfId,
        title: title,
        trfNo: row.trf_trfNo,
        signature: row.signature,
        actionType: action,
        flag: flag
      }
      this.$router.push({
        path: url,
        query: params
      });
    },
    retestFormtter(row, column) {
      var reTest = row[column.property];
      var reTestStr = this.$t('common.yes');
      if (reTest == 1) {
        reTestStr = this.$t('common.no');
      }
      return reTestStr;
    },
    trfStatusFormatter(row, column) {
      var trfStatus = row[column.property];
      var statusName = "Drafting";
      switch (trfStatus) {
        case 2:
          statusName = 'Submitted'
          break
        case 3:
          statusName = 'Application Accepted'
          break
        case 4:
          statusName = 'Testing'
          break
        case 5:
          statusName = 'Report Issued'
          break
        case 6:
          statusName = 'Cancel'
          break
        case 11:
          statusName = 'Pending'
          break
        case 12:
          statusName = 'Pending'
          break
        default:
          statusName = "Drafting";
          break
      }

      return statusName;
    },
    async clear() {
      this.query.order.properties.queryValue = '';
      this.query.order.properties.buyerCustomerGroupName = '';
      this.query.order.properties.trfStatus = null;
      this.query.order.properties.startDate = null;
      this.query.order.properties.endDate = null;
      this.query.order.properties.trfNo = '';
      this.query.order.properties.reportNo = '';
      this.query.order.properties.templateName = '';
      this.query.order.properties.pendingReason = '';
      this.query.order.properties.productDescription = '';
      this.query.order.properties.styleNo = '';
      this.query.order.properties.poNo = '';
      this.query.order.properties.articleNo = '';
      this.query.order.properties.reportConclusion = '';
      this.query.order.properties.submitDateStr = null;
      this.query.order.properties.sampleReceiveDateStr = null;
      this.query.order.properties.reportDateStr = null;
      this.query.order.properties.labName = '';
      this.query.order.properties.labName = '';
      this.query.order.properties.contactName = '';
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async changeSubmissionDateDateQuery(val) {
      let trfSubmissionDate = null
      if (val) {
        trfSubmissionDate = moment(val).format('YYYY-MM-DD')
      }
      this.query.order.properties.submitDateStr = trfSubmissionDate;
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async changeReportDateQuery(val) {
      let reportDate = null;
      if (val) {
        reportDate = moment(val).format('YYYY-MM-DD')
      }
      this.query.order.properties.reportDateStr = reportDate;
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async sampleReceiveDateQuery(val) {
      let sampleReceiveDate = null
      if (val) {
        sampleReceiveDate = moment(val).format('YYYY-MM-DD')
      }
      this.query.order.properties.sampleReceiveDateStr = sampleReceiveDate;
      this.query.pageNo = 1;
      await this.initOrderList();
    },
    async updateSelectedHandle(val) {
      this.query.pageNo = 1;
      this.selectedVal = val
      await this.initOrderList();
    },
    async checkTimeOutHandle(val) {
      this.$set(this, 'checkedTimeOutTrf', val)
      this.query.order.properties.isSubmissionTimeout = val ? 1 : 0
      await this.initOrderList();
    },
    async exportExcel() {
      let columns = this.selectedVal.map(item => item.fieldCode)
      columns.push(
        'trfStatus',
        'id',
      )
      columns = this.$lodash.uniq(columns);
      this.query.order.propertiescolumns = columns;
      var params = {};
      this.downLoading = true;
      let res = await exportTrfs(Object.assign({
        columns: columns,
        trfQuery: this.query.order.properties,
        pageNo: -1,
        pageSize: 3000
      }));
      this.downLoading = false;
      const data = res.data.data;
      //this.page.total = data.total;
      this.excelData = data.records;
      this.$nextTick(function () {
        var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
        var wb = XLSX.utils.table_to_book(document.querySelector('#exportTable'), xlsxParam)
        var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
        try {
          FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'TestRequestList.xlsx')
        } catch (e) {
          if (typeof console !== 'undefined') {
            console.log(e, wbout)
          }
        }
        return wbout
      })

    },

    async exportExcelClick() {
      //判断当前导出条数是否>3000  >3000给出确认提示
      if (this.query.total > 3000) {
        this.$confirm(this.$t('trf.confirmExportTrfList'), this.$t('tip'), {
          confirmButtonText: this.$t('submitText'),
          cancelButtonText: this.$t('cancelText'),
          type: 'warning'
        }).then(() => {
          this.exportExcel();
        }).catch(() => {
          /* this.btnSubmitLoading=false;
           this.$message.error(this.$t('api.error'));*/
        });
      } else {
        this.exportExcel();
      }
    }
    ,
    async selectedDate(num, dateName) {
      let endDate = moment().format('YYYY-MM-DD')
      var startDate;
      if (dateName != 'w') {
        startDate = moment(new Date(new Date().setMonth(new Date().getMonth() - num))).format('YYYY-MM-DD');
      } else {
        startDate = moment().subtract(num, dateName).format('YYYY-MM-DD')
      }
      this.query.order.properties.startDate = startDate
      this.query.order.properties.endDate = endDate
      await this.initOrderList();
    }
    ,

    async updateStartDate(val) {
      let date = null
      if (val) {
        date = moment(val).format('YYYY-MM-DD')
      }
      this.query.order.properties.startDate = date;
      await this.initOrderList();
    }
    ,
    async updateEndDate(val) {
      let date = null
      if (val) {
        date = moment(val).format('YYYY-MM-DD')
      }
      this.query.order.properties.endDate = date;
      await this.initOrderList();

    }
    ,
    checkTime() {
      var begintime = this.query.order.properties.startDate;
      var endtime = this.query.order.properties.endDate;
      if (validatenull(endtime)) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('dateValidate.endDateValidate'),
          type: 'warning'
        });
        return false;
      }

      var time1 = new Date(begintime).getTime();
      var time2 = new Date(endtime).getTime();
      if (validatenull(begintime)) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('dateValidate.startDateValidate'),
          type: 'warning'
        });
        return false;
      }
      if (validatenull(endtime == '')) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('dateValidate.endDateValidate'),
          type: 'warning'
        });
        return false;
      }
      if (time1 > time2) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('dateValidate.endDateErrorValidate'),
          type: 'warning'
        });
        return false;
      }

      //判断时间跨度是否大于6个月  修改为12个月
      var arr1 = begintime.split('-');
      var arr2 = endtime.split('-');
      arr1[1] = parseInt(arr1[1]);
      arr1[2] = parseInt(arr1[2]);
      arr2[1] = parseInt(arr2[1]);
      arr2[2] = parseInt(arr2[2]);
      var flag = true;
      if (arr1[0] == arr2[0]) {//同年
        if (arr2[1] - arr1[1] > 12) { //月间隔超过6个月
          flag = false;
        } else if (arr2[1] - arr1[1] == 12) { //月相隔3个月，比较日
          if (arr2[2] > arr1[2]) { //结束日期的日大于开始日期的日
            flag = false;
          }
        }
      } else { //不同年
        if (arr2[0] - arr1[0] > 1) {
          flag = false;
        } else if (arr2[0] - arr1[0] == 1) {
          if (arr1[1] < 1) { //开始年的月份小于1时，不需要跨年
            console.log("arr1[1] < 7");
            flag = false;
          } else if (arr1[1] + 12 - arr2[1] < 12) { //月相隔大于12个月
            console.log("arr1[1]+12-arr2[1] < 12");
            flag = false;
          } else if (arr1[1] + 12 - arr2[1] == 12) { //月相隔3个月，比较日
            if (arr2[2] > arr1[2]) { //结束日期的日大于开始日期的日
              console.log("截止日 arr2[2] > " + arr2[2]);
              console.log("开始日 arr1[2] > " + arr1[2]);
              flag = false;
            }
          }
        }
      }
      if (!flag) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('dateValidate.betweenDateValidate'),
          type: 'warning'
        });
        return false;
      }
      return true;
    }
    ,


    changeAccountDate() {
      if (!validatenull(this.query.order.properties.startDate) && !validatenull(this.query.order.properties.endDate)) {
        //获取开始时间和结束时间的时间差
        const oneYearDays = moment(this.query.order.properties.endDate).diff(moment(this.query.order.properties.startDate), 'd')
        console.log(oneYearDays);
      }
    }
    ,
    addTrf() {
      this.$router.push({ path: '/trf/trfDetail', query: { id: '', flag: 1, title: 'New TRF', } });
      //this.$router.push( {path: '/trf/trfForm',query:{ id:''}});
    },
    statusFormat: function (row, column) {
      var status = row.status;
      var statusStr = '';
      if (status == 1 || status == '1') {
        statusStr = '正常'
      } else {
        statusStr = '禁用'
      }
      return statusStr;
    }
    ,
    customerGroupFormat: function (row, column) {
      var customerGroupId = row.customerGroupId;
      var dataList = this.customerGroupData;
      var customerGroupStr = '';
      dataList.forEach(function (value, key, dataList) {
        if (customerGroupId == value.customerGroupId) {
          customerGroupStr = value.groupName;
        }
      });
      return customerGroupStr;
    }
    ,
    beforeRouteEnter(to, from, next) {
      next(vm => vm.init())
    }
    ,
    async init() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.3)',
      })
      try {
        // 初始化supplier 对应的 buyer 数组
        //  await this.initBuyer()
        //  await this.initRouteParam()
        this.initDff();
        await this.initOrderList()
      } catch (e) {
        this.$error({ message: e.message || 'Loading Data Is Fail!' })
      }
      loading.close()
    }
    ,
    initDff() {
      this.$nextTick(() => {
        this.$refs.tabSelect.init('all');
      })
    }
    ,
    async onSubmit() {
      this.query.pageNo = 1;
      await this.initOrderList()
    }
    ,
    async initOrderList() {
      //功能迁移到 HomePageTop5
      if(1==1){
        return;
      }
      var submitFlag = true;
      if (validatenull(this.query.order.properties.queryValue)) {
        if (validatenull(this.query.order.properties.startDate) && validatenull(this.query.order.properties.endDate)) {
          this.$notify({
            title: this.$t('tip'),
            message: this.$t('dateValidate.startDateAndEndDateValidate'),
            type: 'warning'
          });
          return false;
        }
        submitFlag = this.checkTime();
      }
      if (!submitFlag) {
        return false;
      }
      this.selectedVal = [
        { "fieldCode": "styleNo", "displayName": "trfList.styleNo", "templateId": null, "sortIndex": 1, width:160 },
        { "fieldCode": "poNo", "displayName": "trfList.poNo", "templateId": null, "sortIndex": 2, width:160 },
        { "fieldCode": "productDescription", "displayName": "trfList.productDescription", "templateId": null, "sortIndex": 3, width:250},
        { "fieldCode": "trfSubmissionDate", "displayName": "trfList.submitDate", "templateId": null, "sortIndex": 4, width: 260 }
      ]
      let columns = this.selectedVal.map(item => item.fieldCode)
      columns.push(
        'trfStatus',
        'id',
      )
      columns = this.$lodash.uniq(columns);
      if (columns.indexOf('reviewConclusion') > -1) {
        this.isShowReviewConclusion = true;
      } else {
        this.isShowReviewConclusion = false;
      }

      this.query.order.properties.columns = columns;
      this.query.order.properties.language = this.language;
      var params = {};
      this.loading = true;
      debugger;
      let queryParam = deepClone(this.query);
      if(!objectIsNull(queryParam.order.properties.startDate)){
        queryParam.order.properties.startDate=this.tzToChina( moment( queryParam.order.properties.startDate).format("YYYY-MM-DD HH:mm:ss"))
      }
      if(!objectIsNull(queryParam.order.properties.endDate)){
        queryParam.order.properties.endDate=this.tzToChina(moment( queryParam.order.properties.endDate).format("YYYY-MM-DD HH:mm:ss"))
      }
      queryParam.order.properties.productLineCode=this.userInfo.productLineCode
      let res = await getTrf_top(Object.assign({
        columns: columns,
        trfQuery: queryParam.order.properties,
        pageNo: this.query.pageNo,
        pageSize: this.query.pageSize
      }));
      this.loading = false;
      const data = res.data.data;
      this.trfData = data;
      console.log('initOrderList', this.trfData)
      this.trfData.forEach(item => {
        if(item.trfStatus == 13) this.longWidth = true
      })
    }

    ,
    displayNameWidth(fieldCode) {
      if (fieldCode == 'customerNameEn') {
        return 300
      } else if (fieldCode == 'buyerCustomerGroupName') { //buyer
        return 250
      } else {
        return 170
      }
    }
    ,
    onLoad(page) {
      this.loading = true;
      var params = {};
      getList(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
        this.loading = false;
        const data = res.data.data;
        this.page.total = data.total;
        console.log('初始获取TRF LIST', data.records)
        this.trfData = data.records;
      });
    }
    ,
    onLoadFromWel() {
      this.loading = true;
      var params = {};
      this.query.order.properties.trfStatus = this.$route.query.trfStatus;
      this.query.order.properties.startDate = this.$route.query.startDate;
      this.query.order.properties.endDate = this.$route.query.endDate;
      getList(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
        this.loading = false;
        const data = res.data.data;
        this.page.total = data.total;
        this.trfData = data.records;
      });
    }
    ,
    trfTableDateFormtter(row, column) {
      var date = row[column.property];
      if (date == undefined || date == '') {
        return this.$t('trf.notSubmit');
      }
      ;
      return moment(date).format("YYYY-MM-DD")
    }
    ,
    //分页查询
    async sizeChange(pageSize) {
      this.query.pageSize = pageSize;
      await this.initOrderList();
    }
    ,
    selectLabTypeChange(value) {
      this.labForm.labType = value;
    }
    ,
  },
};
</script>
<style lang="scss">
.sgs_smart_newTestRequestForm{
  button.is-disabled>i.menu-icon{
    color: #999;
    cursor: not-allowed;
  }
  .menu-icon{
    font-size: 20px;
    cursor: pointer;
    margin: 0px 10px;
    color:#ff6600
  }
}
</style>
<style scoped lang="scss">
.new-test-table {
  td {
    .cell {
      white-space: nowrap;
    }
  }
}

.more-btn {
  border-radius: 0;
  margin-top: 20px;
  padding: 0;
  height: 40px;
  line-height: 40px;
  width: 122px;
  border-color: #ccc;

  .glyphicon {
    font-size: 12px;
  }
}

.f-sort a {
  float: left;
  padding: 0 9px;
  height: 23px;
  border: 1px solid #CCC;
  line-height: 23px;
  margin-right: -1px;
  background: #FFF;
  color: #333;
}
a.curr {
  border-color: #919191;
  background: #919191;
  color: #FFF;
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner {
  color: #FFFFFF;
  background-color: #919191;
  border-color: #919191;
  -webkit-box-shadow: -1px 0 0 0 #919191;
  box-shadow: -1px 0 0 0 #919191;
}

.el-table__fixed-right {
  height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
  bottom: 17px; // 固定列默认设置了定位，    position: absolute;top: 0;left: 0;只需要再设置一下bottom的值，让固定列和父元素的底部出现距离即可
  background: #fff;
}

.otherActiveClass {
  color: #FFF;
  background-color: #ebeef5;
}

.row-expand-cover {
  .el-table__expand-icon {
    visibility: hidden;
  }
}

.otherActiveClass:hover {
  background: #ebeef5;
  color: #FFF;
}

.newIcon {
  position: absolute;
  right: 0;
  top: 0;
}
</style>
