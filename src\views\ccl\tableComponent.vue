<template>
    <div>

        <el-row :gutter="20">
            <el-col :span="12">
                <el-button type="primary"  @click="addRow">{{ $t('ccl.add') }}</el-button>
                <el-button type="primary"  @click="batchAdd">{{ $t('ccl.batchAdd') }}</el-button>
            </el-col>
        </el-row>
        <el-form  ref="form" status-icon  label-width="160px" :model="form"
                 class="demo-ruleForm" size="small">
        <el-table
                :data="form.tableData"
                style="width: 100%"
                class="component"
                size="medium">
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="chemicalEnName"
                    :label="$t('ccl.chemicalEnName')">
                <template slot-scope="scope">
                    <el-form-item >
                        <el-input size="medium" :placeholder="$t('ccl.chemicalEnName')" v-model="scope.row.chemicalEnName"></el-input>
                    </el-form-item>
                </template>
            </el-table-column>
            <el-table-column
                    prop="chemicalZhName"
                    :label="$t('ccl.chemicalZhName')" >
                <template slot-scope="scope">
                    <el-form-item >
                        <el-input size="medium" :placeholder="$t('ccl.chemicalZhName')" v-model="scope.row.chemicalZhName"></el-input>
                    </el-form-item>
                </template>
            </el-table-column>
            <el-table-column class="red_class"
                    :label="'*'+$t('ccl.casNo')" >
                <template slot-scope="scope">
                    <!--<el-input @blur.prevent="changeCount(scope.row)" :placeholder="$t('ccl.casNo')" v-model="scope.row.casNo"></el-input>
                    <span v-if="scope.row.isError === 0">
                        <i class="el-icon-success" style="color: #5daf34"></i>
                    </span>
                    <span v-if="scope.row.isError !== 0">
                        <i class="el-icon-error" style="color: red"></i>
                    </span>-->
                    <el-form-item   ref="casNo" :prop="'tableData.' + scope.$index + '.casNo'"  :rules="rules.casNo" :required="true">
                        <el-input size="medium" :placeholder="$t('ccl.casNo')" v-model="scope.row.casNo" style="width: 100%;" @change="caseNoChangeWays(scope.row.casNo,scope.$index)"></el-input>
                    </el-form-item>
                </template>
            </el-table-column>
            <el-table-column
                    prop="composition"
                    :label="'*'+$t('ccl.composition')" >
                <template slot-scope="scope">
                    <el-form-item ref="composition" :prop="'tableData.' + scope.$index + '.composition'" :rules="rules.composition" :required="true">
                        <el-input size="medium" :placeholder="$t('ccl.composition')" v-model="scope.row.composition" @change="compositionChangeWays(scope.row.composition,scope.$index)" ></el-input>
                    </el-form-item>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')" >
                <template slot-scope="scope">
                    <el-form-item >
                        <el-button @click="removeRow(scope)" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>
                    </el-form-item>
                </template>
            </el-table-column>
        </el-table>
        </el-form>
    </div>
</template>

<script>
    import { mapGetters, mapState } from "vuex";
    import {validatenull} from "@/util/validate";
    const baseRule = [
      { required: true, message: '请输入必填项值', trigger: 'blur' }
    ]
    export default {
        props:{
            customerId: {
                type: Number,
                default: null,
            },
            contentTableData:{
                type:Array,
                default: [],
            }
        },
        name: "tableComponent",
        data(){
          var validateAccount = (rule, value, callback) => {
            let b = rule.field;
            let tableIndex = b.split('.');
            if (tableIndex.length === 3){
              let casNo =  this.form.tableData[tableIndex[1]];
                if (this.checkDataNew(casNo)) {
                    this.validateCasNo=false;
                  callback(new Error(this.$t('ccl.casNoCheck')));
                }else{
                    this.validateCasNo=true;
                }
                callback();
            }
          };
            var validateComponent = (rule, value, callback) => {
                let b = rule.field;
                let tableIndex = b.split('.');
                if (tableIndex.length === 3){
                    let component =  this.form.tableData[tableIndex[1]][tableIndex[2]];
                    let that=this;
                    if (validatenull(component)) {
                           that.validateCasNo=false;
                        callback(new Error(that.$t('ccl.validate.componentNullError')));
                    }else if(!(/^((100|[1-9]?[0-9]([.][0-9]{1,4})?)?(<|>|<=|>=|-))?(100|[1-9]?[0-9]([.][0-9]{1,4})?)?$/.test(component))){//^(100|[1-9]\d|\d)?(<|>|<=|>=|-\d{1})?(100|[1-9]\d|\d)?%$
                           that.validateCasNo=false;
                        callback(new Error(that.$t('ccl.validate.componentFormatError')))
                    }else{
                          that.validateCasNo=true;
                    }
                    callback();
                }
            };
            return{
              validateCasNo:true,
              name: "tableComponent",
              form:{tableData:[]},
              inputRules: {//设置好需要的校验规则
                casNo: {validator: validateAccount, trigger: 'blur'},
                composition: { pattern: /^((100|[1-9]?[0-9]([.][0-9]{1,4})?)?(<|>|<=|>=|-))?(100|[1-9]?[0-9]([.][0-9]{1,4})?)?$/, message: this.$t('ccl.validate.componentFormatError'), trigger: 'blur' }
              },
              rules: {
                casNo: [
                   /*   { required: true, message: this.$t('ccl.casNoCheck'),trigger: 'blur'},*/
                        {validator: validateAccount, trigger: ['blur','change']}
                ],
                composition: [
                  {validator: validateComponent, trigger: ['blur','change']},
                ],
              },
              tableData:[],
            }
        },
      mounted(){
        this.initDomains()
      },
        computed: {
            watch1() {
                const { form } = this
                return {
                    form,
                }
            },
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    deleteBtn: this.vaildData(this.permission['sgs:customer:relationship:conclusionship:delete'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:relationship:conclusionship:edit'],false),
                };
            },

        },
      watch:{
          contentTableData:{
              handler(){   //注意此处就是handler
                   console.log(this.contentTableData);
                   this.form.tableData=this.contentTableData;
                   if(this.form.tableData==null||this.form.tableData==undefined|| this.form.tableData.length==0){
                        let row = {};
                        row['chemicalEnName']='';
                        row['chemicalZhName']='';
                        row['casNo']='';
                        row['rules']=baseRule;
                        this.form.tableData.push(row);
                   }
              },
              deep:true,
              immediate: true
          },
          watch1:{
              handler(){   //注意此处就是handler
                  this.$emit('getChemicalData',this.form.tableData);
              },
              deep:true,
              //immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
          },
      },
        methods:{
          initDomains(){
            this.tableData=[
              {
                casNo: undefined,
                desc: undefined
              },
              {
                casNo:undefined,
                desc:undefined
              }
            ]
          },
          addRow(){
            let row = {};
            row['chemicalEnName']='';
            row['chemicalZhName']='';
            row['casNo']='';
            row['rules']=baseRule;
            this.form.tableData.push(row);
          },
        batchAdd(){
              let that = this;
             that.$prompt(that.$t('ccl.batchAddBlurMsg'), that.$t('tip'), {
                      confirmButtonText: that.$t('operation.confirm'),
                      cancelButtonText: that.$t('operation.cancel'),
                      inputPattern:/^([1]?\d{1,2})$/,
                      inputErrorMessage: that.$t('ccl.batchAddErrorMsg') ,
                    }).then(({ value }) => {

                        for(var i=0;i<value;i++){
                             let row = {}
                            that.form.tableData.push(row)
                        }
                         this.$refs['form'].validate((valid) => {
                     })
                    }).catch(() => {
                        //取消输入
                    });
         },
            caseNoChangeWays(data,index){
                /*if (data) {
                    this.$refs.casNo.clearValidate();
                  }
                  this.form.tableData[index].rules = [this.inputRules.casNo].concat(baseRule);*/
            },
            compositionChangeWays(data,index){
               /* if (data) {
                    this.$refs.composition.clearValidate();
                  }
                  this.form.tableData[index].rules = [this.inputRules.composition].concat(baseRule);*/
            },
          removeRow(data){
             if(this.form.tableData.length==1){
                  this.$notify({
                        title: this.$t('tip'),
                        message: "Please keep one row of data",
                        type: 'warning'
                    });
                    return
             }
            this.form.tableData.splice(data.$index,1);
          },
          checkDataNew(data1) {
            let data = data1.casNo
            if (data !== undefined){
              let array = data.split('-');
              if (array.length === 3){
                let arr1 = array[1].split('');
                let arr0 = array[0].split('');
                let count = 0;

                for (let i = 0; i < arr1.length; i++) {
                  count += arr1[arr1.length - i - 1] * (i + 1)
                }
                let current = arr1.length;
                for (let ii = 0; ii < arr0.length; ii++) {
                  count += arr0[arr0.length - ii - 1] * (ii + 1 + current)
                }
                if (array[2] === count % 10 + "") {
                 return false;
                }else {
                  return true;
                }
              }else {
                return true;
              }
            }else {
              return true;
            }
          },
          getChemicalData(){
            this.$emit('getChemicalData',this.tableData);
          },
         checkCclTable:function checkCclTable(){
          //触发校验
             debugger;
            this.$refs['form'].validate((valid)=>{
                if(valid){
                    //判断是否最少填写了一行数据
                    if(this.form.tableData.length==0){
                        this.$notify({
                        title: this.$t('tip'),
                        message: "Please keep a row of data",
                        type: 'warning'
                    });
                        this.validateCasNo=false;
                    }else{
                         this.validateCasNo=true;
                    }

                }else{
                    this.validateCasNo=false;
                }
            });
           this.$emit("validateCasNo",this.validateCasNo);
         },
          getRow(data){
            this.$emit('getRowData',data);
          }
        },
        created() {
            //this.checkData('7732-18-5');
        },

    }
</script>

<style lang="scss">
    .el-icon-cricle-check:before {
        color: #67C23A
    }
    .component {
        .el-form-item {
            border: 0px;
            .el-form-item__content{
                margin-left: 0px !important;
            }
        }
        .cell {
            overflow: visible !important;
        }
    }
    .red_class{
        color: red;
    }
</style>
