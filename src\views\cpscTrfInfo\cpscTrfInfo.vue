<template>
  <div v-if="show">
    <avue-crud :option="optionCom" :search.sync="search" :page.sync="page" v-model="form" :table-loading="loading" :data="data"
      :permission="permissionList" :before-open="beforeOpen" ref="crud" @row-update="rowUpdate" @row-save="rowSave"
      @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="trfInfoStastus" slot-scope="scope">
        <column-dots :trfInfoStatus="scope.row.trfInfoStastus" :status="scope.row.status" />
      </template>
      <template slot="menuLeft">
        <el-button type="primary" icon="el-icon-plus" plain v-if="permission.cpscTrfInfo_add" @click="handleAdd">{{
    $t('crud.addBtn') }}
        </el-button>

      </template>
      <template slot="menu" slot-scope="scope">

      <el-dropdown trigger="click">
      <span class="el-dropdown-link">
        {{$t('work.cpscTrfInfo.column.action')}}<i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_cancel &&scope.row.status!=2 &&scope.row.trfInfoStastus <= 2" >
         <el-button style="width:100%;padding:20px 20px" type="text"  @click.stop="handleCancel(scope.row)"> {{ cancel }}</el-button>

        </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_edit" >
          <el-button style="width:100%;padding:20px 20px" type="text" @click.stop="handleEdit(scope.row, 'tab1')">
            {{
    scope.row.trfInfoStastus > 2||scope.row.status==2 ? $t('crud.viewBtn') : $t('work.cpscTrfInfo.column.crudEdit') }}
          </el-button>
        
    </el-dropdown-item> 
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_entry && scope.row.trfInfoStastus == 2&&scope.row.status!=2" >
         <el-button style="width:100%;padding:20px 20px" type="text"  @click.stop="handleEdit(scope.row, 'tab2')"> {{ entry }}</el-button>

        </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_delete && scope.row.trfInfoStastus == 1&&scope.row.status!=2"
       >
        <el-button style="width:100%;padding:20px 20px" type="text"  @click="$refs.crud.rowDel(scope.row, index)">
          {{ delBtn }}
        </el-button>
       </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;"  v-if="permission.cpscTrfInfo_csv && (scope.row.trfInfoStastus != 1 && scope.row.trfInfoStastus != 2&&scope.row.status!=2)"
       >
       <el-button style="width:100%;padding:20px 20px" type="text"  @click.stop="handleCsv(scope.row)">
        {{ exportCsv }}
       </el-button>
      </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_push && scope.row.trfInfoStastus == 3&&scope.row.status!=2"
       >
        <el-button style="width:100%;padding:20px 20px" type="text" @click.stop="handlePush(scope.row)">
          {{ toCpsc }}
        </el-button>
      </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_word && (scope.row.trfInfoStastus != 1 && scope.row.trfInfoStastus != 2&&scope.row.status!=2)"
       >
       <el-button style="width:100%;padding:20px 20px"  type="text"
     
            v-if="permission.cpscTrfInfo_word && (scope.row.trfInfoStastus != 1 && scope.row.trfInfoStastus != 2&&scope.row.status!=2)"
            @click.stop="handleWord(scope.row)">
            {{ exportWord }}
        </el-button>
      

      </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_copy && scope.row.trfInfoStastus != 1&&scope.row.status!=2">
          <el-popconfirm :cancel-button-text="$t('work.cpscTrfInfo.btn.copyAndEdit')" cancel-button-type="success"
            :confirm-button-text="$t('work.cpscTrfInfo.btn.copyAndAdd')" confirm-button-type="primary"
            :title="$t('work.cpscTrfInfo.confirmCopyMsg')" width="250px" @confirm="copyToAdd(scope.row)"
            @cancel="copyToEdit(scope.row)">
            <template slot="reference">
             <el-button style="width:100%;padding:20px 20px" type="text"  >{{ copy }}</el-button> 
            </template>
          </el-popconfirm>
        </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_pending && scope.row.isPending === '1' && scope.row.trfInfoStastus == 2&&scope.row.status!=2"
        >
        <el-button style="width:100%;padding:20px 20px" type="text" @click.stop="handlePending('2', scope.row)">
          {{ pending }}
        </el-button>
    </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;"  v-if="permission.cpscTrfInfo_pending && scope.row.isPending === '2' && scope.row.trfInfoStastus == 2&&scope.row.status!=2"
       >
       <el-button style="width:100%;padding:20px 20px" type="text"  @click.stop="handlePending('1', scope.row)">
        {{ unPending }}
       </el-button>
      </el-dropdown-item>
        <el-dropdown-item style="padding: 0 0;" v-if="permission.cpscTrfInfo_push && scope.row.trfInfoStastus == 3 && scope.row.importLog&&scope.row.status!=2"
        >
          <el-button style="width:100%;padding:20px 20px" type="text" @click.stop="handleImportLog(scope.row)">
            {{ toCpscLog }}
          </el-button>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

      </template>
    </avue-crud>
    <el-dialog :title="toCpscLog" append-to-body :visible.sync="importLogDialog"  width="700px"  destroy-on-close>
      <div style="height:400px;overflow-y: auto;" v-html="importLogHtml"></div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getDetail, add, update, remove, copy, doEfillingImport } from "@/api/cpscTrfInfo/cpscTrfInfo";
import { getStore } from '@/util/store'
import { mapGetters } from "vuex";
import { exportBlob } from "@/api/common/index.js";
import { getToken } from '@/util/auth';
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import ColumnDots from './columnDots.vue'
import Add from './add.vue'

export default {
  data() {
    return {
      importLogDialog:false,
      importLogHtml:null,
      show: true,
      delBtn: this.$t('crud.delBtn'),
      btnEdit: this.$t('work.cpscTrfInfo.column.crudEdit'),
      exportCsv: this.$t('work.cpscTrfInfo.column.exportCsv'),
      cancel:this.$t('work.cpscTrfInfo.column.cancel'),
      toCpsc: this.$t('work.cpscTrfInfo.column.toCpsc'),
      toCpscLog: this.$t('work.cpscTrfInfo.column.toCpscLog'),
      exportWord: this.$t('work.cpscTrfInfo.column.exportWord'),
      copy: this.$t('work.cpscTrfInfo.column.copy'),
      entry: this.$t('work.cpscTrfInfo.column.entry'),
      pending: this.$t('work.cpscTrfInfo.column.pending'),
      unPending: this.$t('work.cpscTrfInfo.column.unPending'),
      form: {},
      query: {},
      search: {
        trfReferenceNo:this.$route.query.trfNo
      },
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        menuTitle: this.$t('work.columnAction'),
        searchMenuPosition: 'right',
        arrow: false,
        columnBtn: false,
        refreshBtn: false,
        searchShowBtn: false,
        height: 500,
        calcHeight: 30,
        tip: false,
        delBtn: false,
        menuWidth: 100,
        searchShow: true,
        searchMenuSpan: 24,
        searchLabelWidth: getStore({ name: 'language' }) == "en-US" ? 150 : 100,
        border: true,
        index: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        emptyBtnIcon: 'el-icon-refresh',
        emptyBtnText: this.$t('operation.reset'),

        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "组织id",
            prop: "tenantId",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "数据来源;1.为eFiling 2.为smart",
            prop: "dataSource",
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=sex",
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.trfInfoStastus'),
            // searchLabelWidth:getStore({ name: 'language' }) == "en-US"?'100':'100',
            prop: "trfInfoStastus",
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=cpsc_trf_efilling_status",
            dataType: "number",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            width: 150,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            overHidden: false,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.trfInfoStastus')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.referenceNo'),
            prop: "referenceNo",
            width: getStore({ name: 'language' }) == "en-US" ? 110 : 100,
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.referenceNo'),
            overHidden: true,
          },
          {
            label: "买家客户交易信息ID",
            prop: "buyerCustomerId",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.buyerCustomerName'),
            prop: "buyerCustomerName",
            type: "input",
            overHidden: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            rules: [{
              required: true,
              message: "请输入买家客户名称",
              trigger: "blur"
            }],
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.buyerCustomerName')

          },
          {
            label: "买家客户名称（本地）",
            prop: "buyerCustomerNameLocal",
            type: "input",
            rules: [{
              required: true,
              message: "请输入买家客户名称（本地）",
              trigger: "blur"
            }],
            hide: true,
          },
          {
            label: "买家客户国家",
            prop: "buyerCustomerCountryId",
            type: "input",
            rules: [{
              required: true,
              message: "请输入买家客户国家",
              trigger: "blur"
            }],
            hide: true,
          },
          {
            label: "买家客户国家名称",
            prop: "buyerCustomerCountryName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            rules: [{
              required: true,
              message: "请输入买家客户国家名称",
              trigger: "blur"
            }],
            hide: true,
          },
          {
            label: "买家客户城市id",
            prop: "buyerCustomerCityId",
            type: "input",
            hide: true,
          },
          {
            label: "买家客户城市名称",
            prop: "buyerCustomerCityName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "买家客户地址",
            prop: "buyerCustomerAddress",
            type: "input",
            hide: true,
          },
          {
            label: "买家客户地址（本地）",
            prop: "buyerCustomerAddressLocal",
            type: "input",
            hide: true,
          },
          {
            label: "买家联系人id",
            prop: "buyerContactId",
            type: "input",
            hide: true,
          },
          {
            label: "买家联系人名称",
            prop: "buyerContactName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "买家联系人电话",
            prop: "buyerContactTelephone",
            type: "input",
            hide: true,
          },
          {
            label: "买家联系人邮箱",
            prop: "buyerContactEmaill",
            type: "input",
            hide: true,
          },
          {
            label: "制造商客户交易信息ID",
            prop: "manufactureCustomerId",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.manufactureCustomerName'),
            // searchLabelWidth:getStore({ name: 'language' }) == "en-US"?'100':'100',
            overHidden: true,
            prop: "manufactureCustomerName",
            width: 150,
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.manufactureCustomerName')

          },
          {
            label: this.$t('work.cpscTrfInfo.column.Product_ID'),
            //  searchLabelWidth:getStore({ name: 'language' }) == "en-US"?'100':'100',
            prop: "productId",
            width: getStore({ name: 'language' }) == "en-US" ? 110 : 100,
            type: "input",

            search: true,
            overHidden: true,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.Product_ID')
          }
          ,
          {
            label: this.$t('work.cpscTrfInfo.column.ProductName'),
            prop: "productName",
            type: "input",
            search: true,
            overHidden: true,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.ProductName')
          },
          {
            label: "制造商客户名称（本地）",
            prop: "manufactureCustomerNameLocal",
            type: "input",
            hide: true,
          },
          {
            label: "制造商客户国家",
            prop: "manufactureCustomerCountryId",
            type: "input",
            hide: true,
          },
          {
            label: "制造商客户国家名称",
            prop: "manufactureCustomerCountryName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "制造商客户城市id",
            prop: "manufactureCustomerCityId",
            type: "input",
            hide: true,
          },
          {
            label: "制造商客户城市名称",
            prop: "manufactureCustomerCityName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "制造商客户地址",
            prop: "manufactureCustomerAddress",
            type: "input",
            hide: true,
          },
          {
            label: "制造商客户地址（本地）",
            prop: "manufactureCustomerAddressLocal",
            type: "input",
            hide: true,
          },
          {
            label: "制造商联系人id",
            prop: "manufactureContactId",
            type: "input",
            hide: true,
          },
          {
            label: "制造商联系人名称",
            prop: "manufactureContactName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "制造商联系人电话",
            prop: "manufactureContactTelephone",
            type: "input",
            hide: true,
          },
          {
            label: "制造商联系人邮箱",
            prop: "manufactureContactEmaill",
            type: "input",
            hide: true,
          },
          {
            label: "服务类别",
            prop: "basicServiceType",
            type: "input",
            hide: true,
          },
          {
            label: "服务类别名称",
            prop: "basicServiceTypeName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "实验室ID",
            prop: "basicLabId",
            type: "input",
            hide: true,
          },
          {
            label: "实验室名称",
            prop: "basicLabName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "实验室联系人ID",
            prop: "basicLabContactId",
            type: "input",
            hide: true,
          },
          {
            label: "实验室联系人名称",
            prop: "basicLabContactName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "实验室地址",
            prop: "basicLabAddress",
            type: "input",
            hide: true,
          },
          {
            label: "联络人类型id",
            prop: "pocTypeId",
            type: "input",
            hide: true,
          },
          {
            label: "联络人类型名称",
            prop: "pocTypeName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "联络人客户交易信息ID",
            prop: "pocCustomerId",
            type: "input",
            hide: true,
          },
          {
            label: "联络人客户交易信息名称",
            prop: "pocCustomerName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "测试排除项id;json存储",
            prop: "exclusionsId",
            type: "input",
            hide: true,
          },
          {
            label: "测试排除项编码;json存储",
            prop: "exclusionsCode",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.certVersion'),
            // searchLabelWidth:getStore({ name: 'language' }) == "en-US"?'130':'130',
            prop: "certVersion",
            type: "input",
            width: getStore({ name: 'language' }) == "en-US" ? 160 : 130,
            search: true,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.certVersion')
          },
          {
            label: "CPSC数据",
            prop: "certCpscCollection",
            type: "input",
            hide: true,
          },
          {
            label: "认证报告联络人类型",
            prop: "entryPocType",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "认证报告联络人类型id",
            prop: "entryPocTypeId",
            type: "input",
            hide: true,
          },
          {
            label: "认证报告联络人客户交易信息ID",
            prop: "entryCustomerTradePocId",
            type: "input",
            hide: true,
          },
          {
            label: "认证报告联络人客户交易信息名称",
            prop: "entryCustomerTradePocName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "自定义字段json信息;json存储",
            prop: "customInfoJson",
            type: "input",
            hide: true,
          },
          {
            label: "删除标识",
            prop: "isDeleted",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建人部门",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        
          // {
          //   label:  this.$t('work.cpscTrfInfo.column.createTime'),
          //   prop: "createTime",
          //   type: "input",
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          //   hide: true,
          // },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.updateTime'),
            prop: "updateTime",
            type: "date",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,

            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            searchRange: true,
          },
          {
            label: "数据版本",
            prop: "trfInfoVersion",
            type: "input",
            hide: true,
          },
          {
            label: "数据编号",
            prop: "trfInfoId",
            type: "input",
            hide: true,
          },
          {
            label: "数据时效性;0为使用中，1为过时，2为已失效",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "附件id集合",
            prop: "attachIds",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.trfReferenceNo'),
            prop: "trfReferenceNo",
            width: getStore({ name: 'language' }) == "en-US" ? 110 : 100,
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+ this.$t('work.cpscTrfInfo.column.trfReferenceNo') ,
            overHidden: true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.createUser'),
            prop: "createUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            width:130,
            hide: false,
            search:true,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.createTime'),
            prop: "createTime",
            width: getStore({ name: 'language' }) == "en-US" ? 110 : 100,
            type: "date",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            format:'yyyy-MM-dd',
            valueFormat:'yyyy-MM-dd',
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+ this.$t('work.cpscTrfInfo.column.createTime') ,
            overHidden: true,
            searchRange:true,
          },
        ]
      },
      data: [],
      // rowTrfId: null,
      // 复制类型 新增 or 更新
      // copyType: '',
      // editDialog: false
    };
  },
  components: {
    ColumnDots,
    Add,
  },
  computed: {
    optionCom() {
      return this.option
    },
    ...mapGetters(["permission", "language"]),
    permissionList() {
      return {
        // addBtn: this.validData(this.permission.cpscTrfInfo_add, false),
        // viewBtn: this.validData(this.permission.cpscTrfInfo_view, false),
        // delBtn: this.validData(this.permission.cpscTrfInfo_delete, false),
        // editBtn: this.validData(this.permission.cpscTrfInfo_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  mounted() {
    console.log(this.$route.query.trfNo)
   
    // this.setMenuWidth()
  },
  methods: {
    handleCancel(row){
  let obj = {}
  obj.id = row.id
  obj.status = 2
  update(obj).then(() => {
    this.onLoad(this.page);
    this.$message({
      type: "success",
      message: "Success!"
    });
   
  }, error => {
    loading();
    console.log(error);
  });
},
    handleWord(row) {
        if (row.trfInfoStastus < 3){
          this.$message({
            type: "error",
            message: "This info is not Certificating Status"
          });
          return
        }
        let id = row.id;
        let downloadUrl = `/api/sgs-e-filling/sgs-work/cpscTrfInfo/export-word?sgsToken=${getToken()}`;
        let values = {
          id: id,
        };
        this.$confirm(this.$t('work.cpscTrfInfo.export'), "", {
          confirmButtonText: this.$t('submitText'),
          cancelButtonText: this.$t('cancelText'),
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, row.referenceNo+`${dateNow()}.docx`);
            NProgress.done();
          })
        });
      },
    handleAdd() {
      /*this.$router.push({
        path: '/cpscTrfInfo/add',
      });*/
      //  this.rowTrfId = null;
      // this.editDialog = true;
      window.open(`/#/eFiling/index?tab=tab1`, '_blank')
    },
    handleEdit(row, tab) {
      // this.rowTrfId = row.id;
      // this.editDialog = true;
      window.open(`/#/eFiling/index?trfId=${row.id}&tab=${tab}`, '_blank')
      // setTimeout(()=>{
      //   this.$refs.add.activeName =tab
      // },100)

      /*this.$router.push({
        path: '/cpscTrfInfo/add',
        query: { trfId: row.id }
      });*/
    },
    rowSave(row, done, loading) {
      add(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "Success!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "Success!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm(this.$t('operation.confirmDelete'), {
        confirmButtonText: this.$t('submitText'),
        cancelButtonText: this.$t('cancelText'),
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "Success!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning(this.$t('work.cpscTrfInfo.lastOneData'));
        return;
      }
      this.$confirm(this.$t('operation.confirmDelete'), {
        confirmButtonText: this.$t('submitText'),
        cancelButtonText: this.$t('cancelText'),
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "Success!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleExport() {
      let downloadUrl = `/api/api/sgs-e-filling/sgs-work/cpscTrfInfo/export-cpscTrfInfo?sgsToken=${getToken()}`;
      const {
        referenceNo,
        buyerCustomerName,
        manufactureCustomerName,
        trfInfoStastus,
        updateTime,
      } = this.query;
      let values = {
        referenceNo_like: referenceNo,
        buyerCustomerName_like: buyerCustomerName,
        manufactureCustomerName_like: manufactureCustomerName,
        trfInfoStastus_equal: trfInfoStastus,
        certVersion_like: certVersion,
        productId_like: productId,
        updateTime_: updateTime,
      };
      this.$confirm(this.$t('work.cpscTrfInfo.export'), "", {
        confirmButtonText: this.$t('submitText'),
        cancelButtonText: this.$t('cancelText'),
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then(res => {
          downloadXls(res.data, `申报信息${dateNow()}.xlsx`);
          NProgress.done();
        })
      });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
  
      this.loading = true;
      //this.editDialog = false;
      const {
        referenceNo,
        buyerCustomerName,
        manufactureCustomerName,
        trfInfoStastus,
        updateTime,
        certVersion,
        productId,
        createTime,
        createUser,
        trfReferenceNo
      } = this.search;

      let values = {
        referenceNo_like: referenceNo,
        buyerCustomerName_like: buyerCustomerName,
        manufactureCustomerName_like: manufactureCustomerName,
        trfInfoStastus_equal: trfInfoStastus,
        
       // updateTime_datege: updateTime == undefined ? null : updateTime[0]+' 00:00:00',
       // updateTime_datelt: updateTime == undefined ? null : updateTime[1]+' 23:59:59',
        certVersion_like: certVersion,
        productId_like: productId,
     
        //createTime_datege: createTime == undefined ? null : createTime[0]+' 00:00:00',
        //createTime_datelt: createTime == undefined ? null : createTime[1]+' 23:59:59',
        createUser_like:createUser,
        trfReferenceNo_like:trfReferenceNo
      };
      if (updateTime && updateTime.length >= 2) {
        values.updateTime_datege = updateTime[0]+' 00:00:00'
        values.updateTime_datelt = updateTime[1]+' 23:59:59'
      } else {
        values.updateTime_datege = null
        values.updateTime_datelt = null
      }
      if (createTime && createTime.length >= 2) {
        values.createTime_datege = createTime[0]+' 00:00:00'
        values.createTime_datelt = createTime[1]+' 23:59:59'
      } else {
        values.createTime_datege = null
        values.createTime_datelt = null
      }
      getList(page.currentPage,page.pageSize, values).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    // 复制并新增
    copyToAdd(row) {
      this.handleCopy(row, 'add');
    },
    // 复制并编辑
    copyToEdit(row) {
      this.handleCopy(row, 'edit');
    },
    // 执行复制动作
    handleCopy(row, copyType,tab) {
      this.refreshChange()
      // this.rowTrfId = row.id;
      //this.copyType = copyType;
      window.open(`/#/eFiling/index?trfId=${row.id}&copyType=${copyType}&tab=tab1`, '_blank')
      //this.editDialog = true;
    },
    handlePush(row) {
      doEfillingImport(row.id).then(() => {
        this.$message({
          type: "success",
          message: "Success!"
        });
        // done();
        this.refreshChange()
      }, error => {
        window.console.log(error);
      });
    },
    handleCsv(row) {

      if (row.trfInfoStastus < 3) {
        this.$message({
          type: "error",
          message: "This info is not Certificating Status"
        });
        return
      }
      let id = row.id;
      let downloadUrl = `/api/sgs-e-filling/sgs-work/cpscTrfInfo/export-csv?sgsToken=${getToken()}`;
      let values = {
        id: id,
      };
      this.$confirm(this.$t('work.cpscTrfInfo.export'), "", {
        confirmButtonText: this.$t('submitText'),
        cancelButtonText: this.$t('cancelText'),
        type: "warning"
      }).then(() => {
        NProgress.start();
        exportBlob(downloadUrl, values).then(res => {
          downloadXls(res.data, row.referenceNo + `${dateNow()}.csv`);
          NProgress.done();
        })
      });
    },
    handlePending(isPending, row) {
      let obj = {}
      obj.id = row.id
      obj.isPending = isPending
      update(obj).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "Success!"
        });
        done();
      }, error => {
        loading();
        console.log(error);
      });
    },
    handleImportLog(row){
      let importLogs = JSON.parse(row.importLog)
      let divs =''
      for(let importLog of importLogs){
        divs+=`<div><strong>ErrorCode:</strong> ${importLog.errorCode},<strong>ErrorMessage:</strong>${importLog.errorMessage}</div>`
      }
      this.importLogDialog=true
      this.importLogHtml =divs
    }
  },
  watch: {
    // editDialog(newValue,oldValue){
    //   if(!newValue){
    //     this.copyType=null
    //   }
    // }
    //监听语言变化
    // language: function (newVal) {
    //   this.show=false
    //   this.$nextTick(()=>{
    //     this.show=true
    //   })
    // },
  }
};
</script>

<style lang="scss" scoped>
.el-icon-search {
  display: none;
}

.el-date-editor {
  width: 100% !important
}

.avue-form__menu {
  .el-form-item__content {
    display: flex;
    justify-content: end;
  }
}
</style>
