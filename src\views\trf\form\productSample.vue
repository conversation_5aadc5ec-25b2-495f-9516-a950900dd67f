<template>
  <div class="productSample_">
    <div class="el-row">
      <div>
        <sgs-dff-form-and-grid ref="dffFormandgridRef"
                               system-id="1"
                               :bu-code="buCode"
                               :location-code="locationCode"
                               :order-id="'1'"
                               :object-id="'2'"
                               :object-type="'1'"
                               :form-system-code="'PreOrder'"
                               :form-module-code="'TRF-Product-Header'"
                               :grid-system-code="'PreOrder'"
                               :grid-module-code="'TRF-Product-Header'"
                               :req-obj="reqObj"
                               :show-dff-field="'1'"
                               :form-disabled-flag="disabledFlag"
                               :grid-auto-copy-values="false"
                               :rich-text-component="'quillEditor'"
                               :show-word-limit="true"
                               :grid-edit-model="'1'"
                               :form-auto-copy-values="false"
                               :form-init-btns-show="{btnBusinessType:false,
                                  btnClearShow:false,
                                  btnTranslateShow:false,
                                  btnLanguageSelectShow:languageSelectShow,
                         btnDffTemplateSelectShow:false}"
                               :show-product-item-no="true"
                               :gridFixed="false"
                               :grid-disabled-flag="disabledFlag"
                               :grid-init-btns-show="{
                                 btnCombineShow:false,
                                 btnClearShow:false,
                                 cancelSampleShow:false,
                                 btnLanguageSelectShow:false,
                                 btnAddShow:!disabledFlag,
                                 btnOptionShow:true,
                                 btnBatchAddShow:false,
                                 btnUpdateSampleNoShow:false,
                                 btnGridTemplateSelectShow:false,
                                 btnImportShow:true,
                                 addAxSampleShow:false
                               }"
                               @importSample="importSample"
                               :form-title="' '"
                               :grid-title="' '"
                               :env="env"
                               :auto-save="false"
                               :show-column-tips="false"
                               :show-save-btn="false"
                               :show-report-tip-img="false"
                               @addProductSample="addProductSample"
                               @copySample="copySample"
                               @deleteProductSample="deleteGridData"
                               @saveSuccess="saveSuccess"
                               @onChangeLanguage="changeLanguage"
                               @saveFailed="saveFailed"/>
        <el-row v-if="isCareLabelLoadFlag" >
          <sgs-care-label
              ref="careRef"
              :order-id="1"
              :disabled-flag="disabledFlag"
              :data-disabled-flag="editDffInput"
              :care-instructions="careInstructions"
              :product-item-nos-list="productItemNosList"
              :buyer-customer-group-code="customerGroupCode"
              :buyer-or-agent-name="customerGroupName"
              :interfaces-object="interfacesObject"
              :language-id="languageId"
              :local-language = "languageId"
              @addCare="addCare"
              @delCare="delCare"/>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import { objectIsNull } from "../../../util/validate";

export default {
  name: "productSample",
  components: {},
  data() {
    return {
      reqObj: {
        url: 'api/sgsapi',
        //outQueryCountryUrl: '/api/sgsapi/FrameWorkApi/trims/api/v3/queryCountryAndRegion',
      },
      editDffInput: true,
      careInstructions: [],
      careInstructionsDefault: [
        {
          "careLabelSeq": 1, "productItemNo": ['SupplierCode_1'],
          "imgArray": [],
          "radioType": "",
          "selectCountry": "",
          "selectImgIds": "",//"623,638,641,652,656",
          "careInstruction": "",
          "imgPathYun": []
        },
      ],
      careLanguageList: {},
      productItemNosList: ["SupplierCode_1"],
      interfacesObject: {
        queryFileUrl: '/api/sgs-mart/sgs-api/dowonload-file',
        deleteFileUrl: '/api/sgs-trims/FrameWorkApi/file/delete',
        queryCareLabelUrl: '/api/sgs-mart/sgs-trimsClient/queryCareLabel',
        queryCareLabelRegionCountryUrl: '/api/sgs-trims/trimsApi/queryCareLabelRegionCountry',
        queryWarningUrl: '/api/sgs-trims/trimsApi/queryWarning',
        uploadFileUrl: '/api/sgs-mart/trfApi/uploadCareLabel',
        //TODO 需确认
        // queryCareLabelRegionCountryUrl: host.orderUrl+'/trims/queryAllCountry',
        methodName: 'post'
      },
      languageId: '',
    }
  },

  computed: {
    ...mapGetters(["permission", "language"]),
    permissionList() {
      return {};
    },
    showLanuageSwitcher() {
      return this.dffFormDataObject.languageId == 1 ? true : false;
    }
  },
  watch: {
    //监听语言变化
    /*language: function (newVal) {
      debugger;
      if(newVal=='zh-CN'){
        this.languageId=2;
      }else{
        this.languageId=1;
      }
    },*/
    language: {
      handler: function(newVal, oldVal) {
        if(newVal=='zh-CN'){
          this.languageId=2;
        }else{
          this.languageId=1;
        }
      },
      immediate: true
    },
    isCareLabelLoadFlag: {
      handler(newValue, oldValue) {
        // 处理值变化
        if(newValue){
          this.initFormGridCareLabelsValueObj();
        }else{
          this.careInstructions=this.careInstructionsDefault;
        }

      },
      immediate: true
    }

  },
  props: {
    trfId: {
      type: String,
      default: ''
    },

    buCode:
        {
          type: String,
          default: ''
        },
    locationCode:
        {
          type: String,
          default: ''
        },


    env: {
      type: String,
      default: ''
    },
    customerGroupCode:
        {
          type: String,
          default: ''
        },
    customerGroupName:
        {
          type: String,
          default: ''
        },
    dffFormDataObject:
        {
          type: Object,
          default: () => ({
            dffFormId: '',
            dffGridId: '',
            dffFormData: '',
            dffGridData: '',
            careLabelData:'',
            languageId: 1,
          }),
        },
    disabledFlag: {
      type: Boolean,
      default: () => false
    },
    isCareLabelLoadFlag:{
      type: Boolean,
      default: () => false
    },
    languageSelectShow:{
      type: Boolean,
      default: () => false
    }
  },
  methods: {
    //DFF Grid导入数据
    importSample(importData){
      debugger
      //初始化productItemNos
      this.addProductSample();
      //重新处理关联
      this.careInstructions.forEach((item, index, array) => {
        debugger;
        item.productItemNo =  this.removeIntersection(this.productItemNosList,item.productItemNo);
      });

    },
    removeIntersection(arr1, arr2) {
    // 找到两个数组的交集
      const intersection = arr1.filter(item => arr2.includes(item));
      // 从arr1中删除交集元素
      //arr2 = arr2.filter(item => !intersection.includes(item));
      return intersection;
    },
    changeLanguage(languageId){
      console.log("切换语言--》",languageId)
      //切换语言后重新计算必填
      setTimeout(() => {
        this.$emit('computedRequired', '')
      }, 600)

    },
    saveSuccess(formValues) {
      console.log(JSON.stringify(formValues));
    },
    saveFailed(errorMsg) {
      console.log(errorMsg);
    },
    initFormGridCareLabelsValueObj() {
      var _self=this;
      //判断是为回显数据
      if(_self.dffFormDataObject.careLabelData){
        if(_self.dffFormDataObject.careLabelData.length>0){//回显
          _self.careInstructions=_self.dffFormDataObject.careLabelData;
        }else{//置为默认
          _self.careInstructions=_self.careInstructionsDefault;
        }
      }else{
        _self.careInstructions=_self.careInstructionsDefault;
      }

    },
    productItemNoSelect(careIndex, careItem){
      this.careLabelUploadCallBack(this.languageID);
    },
    careLabelUploadCallBack(languageId,seq,data){
      if(languageId==1){
        if(this.showLanguage==true){
          this.buildLanguageObj(2)
          this.buildLanguageObj(1)
        }
      }
      //otherlanguage data
      let _self=this;
      let careInstructionTemp=_self.$lodash.cloneDeep(_self.careLanguageList[languageId]);
      let lanuageIdOther=languageId
      if(languageId==1){
        lanuageIdOther=2;
      }else{
        lanuageIdOther=1;
      }
      if(!_self.careLanguageList[lanuageIdOther]){
        return;
      }
      careInstructionTemp.forEach((item,index)=>{
        let  careInstructionObj= _self.careLanguageList[lanuageIdOther].filter(it=>it.careLabelSeq==item.careLabelSeq);
        item.id=careInstructionObj[0].id;
        if(careInstructionObj[0].careLabelSeq==seq){
          let sTemp =''
          if(lanuageIdOther==1){
            if(data.en && data.en!="" && data.en!=undefined){
              sTemp = data.en.replace(/<.*?>/ig,"");
              if(careInstructionObj[0].careInstruction &&  careInstructionObj[0].careInstruction!="" && careInstructionObj[0].careInstruction!=undefined){
                item.careInstruction = careInstructionObj[0].careInstruction +'/'+sTemp
              }else{
                item.careInstruction = sTemp
              }
            }
          }else{
            if(data.cn && data.cn!="" && data.cn!=undefined){
              sTemp = data.cn.replace(/<.*?>/ig,"");
              if(careInstructionObj[0].careInstruction &&  careInstructionObj[0].careInstruction!="" && careInstructionObj[0].careInstruction!=undefined){
                item.careInstruction = careInstructionObj[0].careInstruction +'/'+sTemp
              }else{
                item.careInstruction = sTemp
              }
            }
          }
          return
        }
        item.careInstruction=careInstructionObj[0].careInstruction;
      });
      _self.careLanguageList[lanuageIdOther]=careInstructionTemp;
    },
    initProductItemNoList(){
      var productItemNos = new Array();
      if(this.dffFormDataObject.languageId===2){
        if(this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.CN) {
          this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.CN.forEach((item, index, array) => {
            productItemNos.push(item.productItemNo);
          })
        }
      }else{
        if(this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.EN){
          this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.EN.forEach((item, index, array) => {
            productItemNos.push(item.productItemNo);
          })
        }
      }
      if(productItemNos.length>0){
        this.productItemNosList = productItemNos;
      }
    },

    addCare(addCare){
      let _self=this;
    /*  let languageId = _self.getOtherLanguageId();
      if(_self.careLanguageList[languageId]){
        let addDataSource=JSON.parse(JSON.stringify(addCare));
        _self.careLanguageList[languageId].push(addDataSource);
      }*/
      this.$emit('computedRequired', '')
    },
    delCare(careIndex){
      let _self=this;
    /*  let languageID = _self.getOtherLanguageId();
      if(_self.careLanguageList[languageID]){
        _self.careLanguageList[languageID].splice(careIndex,1);
      }*/
      this.$emit('computedRequired', '')
    },
    getOtherLanguageId(){
      let otherLanguages= this.languageList.filter(it=>it.value!=this.languageID);
      return otherLanguages[0].value;
    },
    addProductSample() {
      var productItemNos = new Array();
      //获取当前语言的DFF数据
      if(this.dffFormDataObject.languageId===2){
        this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.CN.forEach((item, index, array) => {
          productItemNos.push(item.productItemNo);
        })
      }else{
        this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.EN.forEach((item, index, array) => {
          productItemNos.push(item.productItemNo);
        })
      }

      this.productItemNosList = productItemNos;
      // 动态新增时获取新增 input
      setTimeout(() => {
        this.$emit('computedRequired', '')
      }, 600)
    },
    copySample(instanceItem, instanceIndex) {
    /*  var productItemNos = new Array();
      this.$refs.dffFormandgridRef.getDffFormFieldValues().gridValues.EN.forEach((item, index, array) => {
        productItemNos.push(item.productItemNo);
      })
      this.productItemNosList = productItemNos;*/
      this.initProductItemNoList();
      this.$emit('computedRequired', '')
    },
    deleteGridData(instanceItem, instanceIndex) {
      var deletedItemNo =  instanceItem.productItemNo;
      var removeIndex = [];
      this.careInstructions.forEach((item, index, array) => {
        var productItemNoIndex = item.productItemNo.indexOf(instanceItem.productItemNo);
        if (item.productItemNo.length == 1 && productItemNoIndex > -1) {
          item.productItemNo.splice(productItemNoIndex, 1);
          removeIndex.push(index);
        } else if (productItemNoIndex != -1) {
          item.productItemNo.splice(productItemNoIndex, 1);
        }
      });
      removeIndex.forEach((item, index, array) => {
        if (this.careInstructions.length > 1) {
          this.careInstructions.splice(item, 1);
        }
      })
      this.productItemNosList = this.productItemNosList.filter(item => item !== deletedItemNo);
      //this.productItemNosList= this.productItemNosList.remove(deletedItemNo);
      // 删除样品时重新计算
      setTimeout(() => {
        this.$emit('computedRequired', '')
      }, 600)

    },
    dffFormLoad() {
      debugger;
      var _this = this;
      this.$nextTick(function () {  
        var dffFromMultLangValues = {
          "EN": {},
          "CN": {}
        };
        var dffGridMultLangValues = {
          /*'EN': [{
            productItemNo: 'Sample_1',
            dFFFormID: '',
            sampleID: 'A'
          }],
          'CN': [{
            productItemNo: 'Sample_1',
            dFFFormID: '',
            sampleID: 'A'
          }]*/
        };
        var initData = {
          customerGroupCode: _this.customerGroupCode,
          languageId: _this.dffFormDataObject.languageId == 2 ? 2 : _this.dffFormDataObject.languageId,
          dffFromMultLangValues: _this.dffFormDataObject.dffFormData == '' ? dffFromMultLangValues : _this.dffFormDataObject.dffFormData,
          dffGridMultLangValues: _this.dffFormDataObject.dffGridData == '' ? dffGridMultLangValues : _this.dffFormDataObject.dffGridData,
          dffFormId: _this.dffFormDataObject.dffFormId,
          dffGridId: _this.dffFormDataObject.dffGridId,
        };
        console.log('init dff form ', initData);
        console.log('init dff form BU Code ', _this.buCode);
        console.log('init dff form locationCode ', _this.locationCode);
        _this.$refs.dffFormandgridRef.initFormAndGrid(initData);
        _this.initProductItemNoList();
        setTimeout(() => {
          this.$emit('computedRequired', '')
          this.initSelect2ValidListener();
        }, 600)
      });
    },
    initSpanCloseListener(){
      let select2RequireSpan = document.querySelectorAll(".is-required [data-select-ref] .el-select__tags span i.el-tag__close");
      if(!select2RequireSpan || select2RequireSpan.length==0){
        this.$emit('computedRequired', '')
        return;
      }
      select2RequireSpan.forEach(ele=>{
        ele.onclick =(event)=>{
          setTimeout(()=>{
            this.$emit('computedRequired', '')
          },500)
        }
      })
    },
    initSelect2ValidListener(){
      let select2RequireInput = document.querySelectorAll(".is-required [data-select-ref] input.el-select__input");
      select2RequireInput.forEach(ele=>{
        ele.onblur =  (event)=>{
          this.$emit('computedRequired', '')
          this.initSpanCloseListener();
        }
        ele.onchange =  (event)=>{
          this.$emit('computedRequired', '')
          this.initSpanCloseListener();
        }
        ele.onfocus =  (event)=>{
          this.$emit('computedRequired', '')
          this.initSpanCloseListener();
        }
      })
    },

    //切换可编辑状态
    changeEditState(state) {
      this.disabledFlag = state === 5
    },

    async saveForm() {
      return await this.$refs.dffFormandgridRef.saveDffForm();
    },
    getDatas() {
      return this.$refs.dffFormandgridRef.getDffFormFieldValues();
    },
    getCareLabelDatas() {
      return this.careInstructions;
    },
    checkFormAndGridValidate(){
      debugger;
     // let checkData = this.$refs.dffFormandgridRef.checkAbleSaved();
      return this.$refs.dffFormandgridRef.checkAbleSaved();
    }
  },
  mounted() {
    if (this.language == 'zh-CN'){
      this.languageId=2;
    }else{
      this.languageId=1;
    }
    this.dffFormLoad();
  }

}
</script>

<style scoped lang="scss">

</style>
