<template>
  <br />
  <div>角色权限控制演示</div>
  <el-button
    type="primary"
    size="small"
    v-role="[
      ['SGSUserRole', 'SgsAdmin'],
      ['LOCATION', 'HK'],
    ]"
  >
    角色权限
  </el-button>
  <div>按钮权限控制演示</div>
  <el-button type="primary" size="small" v-permission="'sgs:main:user:setting'">
    有按钮权限
  </el-button>
  <el-button type="primary" size="small" v-permission="'layout:new:page'">
    无按钮权限
  </el-button>
  <el-input v-model="inputValue" placeholder="请输入内容" type="textarea" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const inputValue = ref('')
</script>
<style scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>
