<template>
    <el-tree v-loading="loadTree" ref="tree"
             :data="data"
             :props="treeProps"
             :default-checked-keys="choiceNode"
             default-expand-all="true"
             show-checkbox node-key="id"
             @check-change="handlerCheckChange"/>
</template>

<script>
import {mapGetters} from "vuex";
import {getComponentsTree} from "@/api/system/settings/components/components";

export default {
        //组件树
        name: "ComponentTree",
        data() {
            return {
                loadTree: true,
                data:[],
                choiceNode: []
            }
        },

        computed: {
          ...mapGetters(["language"]),
          treeProps() {
            return {
              label: (node) => {
                return this.language == 'zh-CN' ? node.componentNameCn : node.componentNameEn;
              }
            }
          }
        },

        mounted() {
          getComponentsTree().then(res => {
            this.data = res.data.data;
            console.log("组件树", this.data);
          }).finally(() => {
            this.loadTree = false;
          });
        },

        methods: {
          handlerCheckChange(data, checked, indeterminate) {
            if(!data.configData) return;
            this.$emit("toggleComponents", data, checked);
          },

          loadPageConfig(pageConfig) {
            let nodes = [];
            if(pageConfig && pageConfig.configData) {
              pageConfig.configData = JSON.parse(pageConfig.configData);
              pageConfig.configData.forEach(item => {
                nodes.push(item.i);
              });
            }
            this.$refs.tree.setCheckedKeys(nodes);
          }
        }
    }
</script>

<style scoped>

</style>
