<template>
    <basic-container>
        <div class="smart_todoListForLululemon" id="smart_todoListForLululemon">
            <sgs-customer-to-do-list
                    productLineCode="SL"
                    noNeedSelectCustomer
                    :defaultRefSystemId="10030"
                    :hideRefSystemId="[2]"
                    mode="REMOTE"
                    :createTRFOption="{
                        productLineCodes:['SL','HL'],
                        buyerName: 'LULULEMON ATHLETICA',
                        customerGroupCode:'CG0000505',
                        refSystemId:10030,
                        useCustomerDetail:true,
                        userCustomerApi:true,
                        customerHttp:'/api/sgsapi/',
                        config:{
                            customerGroupCode: 'CG0000505',
                            refSystemId : 10030,
                            refSystemLabelName : 'LULULEMON ATHLETICA',
                        },
                        btnUseCustomer:true,
                        btnShow:{
                            confirmTrf:false,
                            printTrf:false,
                            filterExport:false
                        }
                    }"
                    useCustomerConfig
                    :showTabs="false"
            ></sgs-customer-to-do-list>
        </div>
    </basic-container>
</template>

<script>
    export default {
        name: "todoList",
        data() {
            return {}
        },
        methods: {},
        mounted() {
        },
        created() {
        },
        watch: {},
        computed: {},
        props: {},
        updated() {
        },
        beforeDestory() {
        },
        destoryed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .smart_todoListForLululemon {
        background: #fff;
        padding: 24px 32px;
    }
</style>