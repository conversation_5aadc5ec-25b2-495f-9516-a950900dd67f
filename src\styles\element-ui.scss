/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";
@import "./unit.scss";

.el-dropdown-menu__item {
  font-size: 12px !important;
  line-height: 28px !important;
}

.el-card.is-always-shadow {
  // box-shadow: none;
  // border: none !important;
  box-shadow: 0px 8px 24px 0px rgba(27, 27, 27, 0.02);
}
.el-tabs--border-card {
  box-shadow: 0px 8px 24px 0px rgba(27, 27, 27, 0.02);
  border: none !important;
}
.el-tabs--border-card > .el-tabs__header {
  background-color: #EBEBEB;
  border-bottom: 0;
}
.el-tabs__item {
  height: 48px !important;
  line-height: 48px !important;
  font-size: 16px;
  font-weight: 400;
  color: #656565;
}

 .el-menu--horizontal .el-menu .el-submenu__title{
   min-width: 260px;
 }
.el-menu--horizontal {
    .el-menu-item,.el-submenu__title {
        height: 80px;
        line-height: 76px;
        padding-right: 30px;
        vertical-align: baseline;
        border-bottom: 2px solid transparent;
        i, span {
          // font-size: 16px !important;
          // color: #1b1b1b !important;
        }

        i {
            margin-right: 5px;
        }
        .el-submenu__icon-arrow {
            right: 6px;
            margin-top: -5px;
        }
        &:hover {
            i, span {
                color: rgb(255, 102, 0);
            }
        }
        &.is-active {
            i, span {
                color: rgb(255, 102, 0);
            }
        }
    }
    .el-submenu {
        //&:hover {
        //    i, span {
        //        color: rgb(255, 102, 0);
        //    }
        //}
        &.is-active {
            i, span {
                color: rgb(255, 102, 0);
            }
        }
    }
    .is_opened{
      min-width: 260px;
    }
}


.el-message__icon,
.el-message__content {
  display: inline-block;
}

.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
  height: auto;
  overflow: hidden;
}

.el-dialog__wrapper {
  z-index: 2048;
}


.el-col {
  margin-bottom: 8px;
}

.el-main {
  padding: 0 !important;
}

.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background-color: transparent;
}


.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background-color: transparent !important;
}

.el-table {
  th {
    color: rgba(0, 0, 0, .85);
    background-color: #fafafa;
  }

  .cell {
    word-break: keep-all !important;
  }
}

.el-pagination {
  text-align: right;
  margin-top: 15px;
  .el-select .el-input .el-input__inner {
    border-radius: 0;
  }
}

.el-drawer__header {
  margin-bottom: 10px;
  font-size: 18px;
}

.el-drawer__body {
  padding: 10px;
  overflow-y: scroll;
}

.el-upload__input {
  display: none !important;
}


.el-form-item__label {
  font-weight: normal;
  // line-height: 28px !important;
}

.el-form-item__content {
  line-height: 28px !important;
}

.el-popconfirm__action {
  text-align: center;
}

.el-breadcrumb {
  border-radius: 2px;
  padding: 8px 15px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
}

.el-tabs__nav-wrap::after {
  position: initial;
}

.el-table th {
  height: 50px!important; 
}
.el-picker-panel__shortcut.selected-shortcut {
  color: $primary-color;
}