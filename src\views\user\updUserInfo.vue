<template>
    <basic-container>
        <br>
        <el-row>
            <el-col :span="12">
                <el-form :model="form" ref="form" size="small" :rules="rules" label-width="140px">
                    <el-form-item :label="$t('user.phone')" prop="contactMobile">
                        <el-input maxlength="50" type="number" v-model="form.contactMobile" autocomplete="off">
                            <!--<el-button slot="append" >{{ $t('login.msgText') }}</el-button>-->
                            <template slot="append">
                              <span @click="handleSend"
                                    class="user-info-msg-text"
                                    :class="[{display:msgKey}]">{{msgText}}</span>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item :label="$t('user.checkCode')" prop="checkCode">
                        <el-input maxlength="50" type="number" v-model="form.checkCode" autocomplete="off"></el-input>
                    </el-form-item>

                    <el-form-item class="text-left">
                        <el-button type="primary" @click="updatePhone">{{$t('operation.submit')}}</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </basic-container>
</template>

<script>

    import {updatePhone} from "@/api/system/user";
    import {mapGetters, mapState} from "vuex";
    import {sendVerificationCode} from "@/api/customer/customerRegister";
    import {isvalidatemobile} from "@/util/validate";

    export default {
        name: "updUserInfo",
        data() {
            const validatePhone = (rule, value, callback) => {
                if (isvalidatemobile(value)[0]) {
                    callback(new Error(isvalidatemobile(value)[1]));
                } else {
                    callback();
                }
            };
            const validateCode = (rule, value, callback) => {
                if (value.length != 6) {
                    callback(new Error("Please input 6 digits validation code."));
                } else {
                    callback();
                }
            };
            return {
                msgText: "",
                msgTime: "",
                msgKey: false,
                loginForm: {
                    contactMobile: "",
                    checkCode: ""
                },
                form: {
                    customer: {contactMobile: "",}
                },
                rules: {
                    contactMobile: [{required: true, trigger: "blur", validator: validatePhone}],
                    checkCode: [{required: true, trigger: "blur", validator: validateCode}]
                }
            };
        },
        created() {
            this.msgText = this.config.MSGINIT;
            this.msgTime = this.config.MSGTIME;
        },
        computed: {
            ...mapGetters([
                "userInfo"
            ]),
            config() {
                return {
                    MSGINIT: this.$t("login.msgText"),
                    MSGSCUCCESS: this.$t("login.msgSuccess"),
                    MSGTIME: 60
                };
            }
        },
        methods: {
            updatePhone() {
                this.$refs['form'].validate((valid) => {
                    if (!valid) {
                        return false;
                    } else {
                        this.form.customer.contactMobile = this.form.contactMobile
                        updatePhone(this.form).then(res => {
                            if (res.data.success) {
                                this.$message({
                                    type: "success",
                                    message: this.$t('register.updPhoneSuccess')
                                });
                                this.$refs['form'].resetFields();
                            } else {
                                this.$message({
                                    type: "error",
                                    message: res.data.msg
                                });
                            }
                        })
                    }
                })
            },
            handleSend() {
                if (this.msgKey) return;
                this.msgText = this.msgTime + this.config.MSGSCUCCESS;
                this.msgKey = true;
                const time = setInterval(() => {
                    this.msgTime--;
                    this.msgText = this.msgTime + this.config.MSGSCUCCESS;
                    if (this.msgTime == 0) {
                        this.msgTime = this.config.MSGTIME;
                        this.msgText = this.config.MSGINIT;
                        this.msgKey = false;
                        clearInterval(time);
                    }
                }, 1000);
                if (this.msgKey) {
                    this.sendSMSCode()
                }

            },
            sendSMSCode() {
                console.log(this.form.contactMobile);
                sendVerificationCode(this.form.contactMobile, 2).then(res => {
                });
            },
        }
    };
</script>


<style>
    .user-info-msg-text {
        display: block;
        /* width: 60px; */
        font-size: 12px;
        text-align: center;
        cursor: pointer;
    }

    .user-info-msg-text.display {
        color: #ccc;
    }
    .el-input-group__append {
        border-radius: 0;
    border: 1px solid #dfdfdf;
    }
</style>
