<template>
  <el-dialog modal="false" close-on-click-modal="false" close-on-press-escape="false" width="1100px"
             :title="$t('trf.applicant')"
             :visible.sync="dialogVisible" :before-close="handleClose">
    <el-table highlight-current-row :data="authList" @current-change="handleSelectionChange">
      <el-table-column width="55">
        <template slot-scope="scope">
          <el-radio @change.native="handleSelectionChange(scope.row)" v-model="selected" :label="scope.row.id"><span /></el-radio>
        </template>
      </el-table-column>
      <el-table-column :label="$t('account.userName')" show-overflow-tooltip width="150px">
        <template slot-scope="scope">
          <span>{{scope.row.authUserName}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('account.code')" show-overflow-tooltip  width="240px">
        <template slot-scope="scope">
          <span>{{scope.row.authAccount}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.companyNameCn')" show-overflow-tooltip width="300px">
        <template slot-scope="scope">
          <span>{{scope.row.customerNameZh}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.companyNameEn')" show-overflow-tooltip width="300px">
        <template slot-scope="scope">
          <span>{{scope.row.customerNameEn}}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="footer">
      <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChange"
          @current-change="currentChange"
          :current-page="query.pageNo"
          :page-sizes="[5, 10]"
          :page-size="query.pageSize"
          :total="query.total">
      </el-pagination>
      <div class="footer-btn">
        <el-button size="small" type="primary" plain  @click="handleClose">{{$t('operation.cancel')}}</el-button>
        <el-button size="small" type="primary"   @click="choiceCustomer">{{$t('operation.submit')}}</el-button>
      </div>
    </div>


  </el-dialog>
</template>

<script>
import {proxyCustomers} from "@/api/customer/customer";
import { objectIsNull } from "@/util/validate";

const CUSTOMER_AUTH_TYPE = "CREATE_TRF";     //授权业务类型
const CUSTOMER_AUTH_STATUS = 1;             //授权状态

export default {
  name: "CustomerAuthDialog",
  props: {
    dialogShow: {
      type: Boolean,
      required: true,
      default: false,
      description: 'dialog 可见状态',
    },
    createUserId: {
      type: String,
      required: true,
      default: null,
      description: '创建人id'
    }
  },
  watch: {
    dialogShow(val) {
      this.dialogVisible = val;
      if(val) {
        this.listCustomerAuthList();
      }
    },
  },
  data() {
    return {
      dialogVisible: false,
      selected: null,
      authList:[],
      query: {
        pageNo: 1,
        pageSize: 5,
        total: 0
      }
    }
  },

  methods: {
    handleClose() {
      this.dialogShow = !this.dialogShow;
      this.$emit('closeDialog', this.dialogShow);
    },

    async sizeChange(pageSize) {
      this.query.pageSize = pageSize;
      await this.listCustomerAuthList();
    },

    async currentChange(pageCurrent) {
      this.query.pageNo = pageCurrent;
      await this.listCustomerAuthList();
    },

    handleSelectionChange(row) {
      if(objectIsNull(row)) return;
      this.selected = row.id;
      console.log("选中了授权人", row);
    },

    choiceCustomer() {
      if(!this.selected) return;
      let applicant = this.authList.find(item => item.id === this.selected);
      this.$emit('choiceCustomer', applicant);
      this.handleClose();
    },

    listCustomerAuthList() {
      if(objectIsNull(this.createUserId)) return;
      proxyCustomers({
        applyUserId: this.createUserId,
        authStatus: CUSTOMER_AUTH_STATUS,
        authType: CUSTOMER_AUTH_TYPE,
        ...this.query
      }).then(res => {
        if(res.data && res.data.data) {
          let result = res.data.data;
          this.query.total = result.total;
          this.query.pageSize = result.size;
          this.authList = result.records;
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
  .footer {
    display: flex;
    justify-content: space-between;
    .footer-btn {
      padding-top: 12px;
    }
  }
</style>
