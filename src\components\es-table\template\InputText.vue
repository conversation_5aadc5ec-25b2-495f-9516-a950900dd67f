<template>
    <div id="InputText" class="InputText">
        <el-row>
            <el-col :span="8">
                <el-select
                        v-model="selectValue"
                        style="width: 100%"
                        filterable
                >
                    <el-option
                            v-for="(op,ind) in selectList"
                            :key="'col_'+ind"
                            :label="$t(op.i18n)"
                            :value="op.value"
                    ></el-option>
                </el-select>
            </el-col>
            <el-col :span="16">
                <el-input
                        style="width: 100%"
                        type="text"
                        v-model="dataValue"
                        v-bind="$attrs"
                        :clearable="getClearable"
                        :show-word-limit="allOptions.length-0>0"
                        :maxlength="(allOptions.length-0==0?9999:allOptions.length)">
                </el-input>
            </el-col>
        </el-row>
        <!--<el-tooltip :content="dataValue" effect="light">
        </el-tooltip>-->
    </div>
</template>

<script>
    export default {
        name: "InputText",
        data() {
            return {
                dataValue: '',
                selectValue:'',
                selectList:[
                    {i18n:'SEComment.in',value:'in'},
                    {i18n:'SEComment.notIn',value:'notIn'},
                    {i18n:'SEComment.eq',value:'eq'},
                    {i18n:'SEComment.notEq',value:'notEq'},
                ],
                options:{}
            }
        },
        methods: {
        },
        created() {
            let includeCondition = false
            this.selectList.forEach(s=>{
                if(s.value == this.selectValue){
                    includeCondition = true;
                }
            })
            this.selectValue = includeCondition ?  this.formObj.condition : 'eq';
            this.dataValue = this.formObj ? this.formObj.conditionValue : '';
            if(!this.allOptions.options){
                this.options = Object.assign({},this.allOptions,{options:{}});
            }else {
                this.options = Object.assign({},this.allOptions);
            }
        },
        computed:{
            getClearable:{
                get(){
                    let cl = false;
                    try{
                        let op = JSON.parse(this.options.options)
                        cl = op.clearable;
                    }catch (e) {
                        //console.log(e);
                    }
                    return cl;
                },
                set(){}
            }
        },
        mounted() {
        },
        watch: {
            selectValue:{
              immediate:true,
              handler(newV){
                  let obj = {
                      condition:newV,
                      conditionValue:this.dataValue
                  }
                  this.$emit("update:value", obj)
              }
            },
            dataValue: {
                deep: true,
                immediate: true,
                handler(newV, oldV) {
                    let obj = {
                        condition:this.selectValue,
                        conditionValue:newV
                    }
                    this.$emit("update:value", obj)
                }
            },
            formObj:{
                deep:true,
                handler(newV,oldV){
                    if(newV){
                        let {conditionValue} = newV;
                        this.$set(this,'dataValue',conditionValue)
                    }
                }
            }
        },
        components: {},
        props: {
            value: null,
            allOptions: {},
            defaultFilter: String,
            formObj:{}
        }
    }
</script>

<style scoped>
    .InputText {
    }
</style>