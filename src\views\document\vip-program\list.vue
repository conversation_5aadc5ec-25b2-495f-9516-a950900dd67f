<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.vipservice')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.vipservice')}}</h1>
        <el-card shadow="never" class="sgs-box">
            <el-row>
                <el-form :inline="true" :model="formInline" size="medium" label-width="200px" label-position="left">
                    <el-form-item >
                        <el-select clearable v-model="query.productLineCode"
                                :placeholder="$t('productLine.name')"
                                style="width:100%" >
                            <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                    :value="productLine.productLineCode"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-input clearable
                                v-model="query.programName"
                                :placeholder="$t('vipProgram.name')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                        <el-button type="primary" @click="addRow">{{$t('operation.add')}}</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row v-loading="loading">
                <el-table
                        :data="tableData"
                        style="width: 100%"
                        size="medium">
                    <el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            fixed
                            prop="productLineName"
                            :label="$t('productLine.name')"
                            width="260">
                    </el-table-column>
                    <el-table-column
                            fixed
                            prop="programName"
                            :label="$t('vipProgram.name')" >
                    </el-table-column>
                    <el-table-column
                            fixed
                            prop="sort"
                            :label="$t('documentLibrary.documentSort')"
                            width="80"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="updateUser"
                            :label="$t('common.operator')"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="updateTime"
                            :label="$t('common.operationTime')"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="status"
                            :label="$t('common.status.title')"
                            width="80"
                            align="center">
                        <template slot-scope="scope">
                            <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                                <el-switch
                                        v-model="scope.row.status"
                                        active-color="#ff6600"
                                        inactive-color="#ff4949"
                                        :active-value = "1"
                                        :inactive-value = "0"
                                        @change="changeStatus(scope.row)">
                                </el-switch>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="150"
                            align="center">
                        <template slot-scope="scope">
                            <el-button type="text" @click="detailRow(scope.row)">{{$t('operation.edit')}}</el-button>
                            <el-button @click="removeRow(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
        <el-drawer :title="title" :visible.sync="dialogFormVisible" size="60%">
            <el-form ref="form" :model="form" label-width="200px" label-position="left"  size="medium" class="sgs-form" :rules="rules">
                <el-form-item  :label="$t('term.productLine')">
                    <el-select filterable clearable v-model="form.productLineCode" style="width:100%" @change="selecProductLineCodeChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
<!--                <el-form-item v-show="false" :label="$t('vipProgram.name')" prop="programName">
                    <el-input v-model="form.programName"
                            maxlength="200"></el-input>
                </el-form-item>-->
                <el-form-item :label="$t('vipProgram.showWhichMenu')" prop="showWhichMenu">
                  <el-checkbox-group v-model="showOnWhichMenu" style="padding: 4px;" @change="showMenuChange()">
                    <el-checkbox  v-for="menu in menus" :label="menu.sysKey" :key="menu.sysKey">{{menu.sysValue}}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item :label="$t('documentLibrary.documentSort')">
                    <el-input  v-model.number="form.sort" type="number"
                               maxlength="6"></el-input>
                </el-form-item>
                <el-form-item :label="$t('vipProgram.layoutType')">
                    <el-radio-group v-model="form.layoutType">
                        <el-tooltip placement="top">
                            <div slot="content">
                                <el-card :body-style="{ padding: '0px' }">
                                    <el-row>
                                        <el-col :span="12">
                                            <p>Document Title 1</p>
                                            <p>Document Title 2</p>
                                            <p>Document Title 3</p>
                                        </el-col>
                                        <el-col :span="12">
                                            <img src="/img/slides/slide01.jpg" style="width: 220px;">
                                        </el-col>
                                    </el-row>
                                </el-card>
                            </div>
                            <el-radio-button label="10">Layout A</el-radio-button>
                        </el-tooltip>
                        <el-tooltip placement="top">
                            <div slot="content">
                                <el-card :body-style="{ padding: '0px' }">
                                    <img src="/img/slides/slide01.jpg" style="width: 180px;">
                                    <div style="padding: 2px;">
                                        <p>Document Title 1</p>
                                        <p>Document Title 2</p>
                                        <p>Document Title 3</p>
                                    </div>
                                </el-card>
                            </div>
                            <el-radio-button label="20">Layout B</el-radio-button>
                        </el-tooltip>
                        <el-tooltip placement="top">
                            <div slot="content" style="width: 180px">
                                <el-card :body-style="{ padding: '0px' }">
                                    <div slot="header" class="clearfix">
                                        <span>2020</span>
                                    </div>
                                    <div style="padding: 2px;">
                                        <p>Document Title 1</p>
                                        <p>Document Title 2</p>
                                        <p>Document Title 3</p>
                                    </div>
                                </el-card>
                            </div>
                            <el-radio-button label="30">Layout C</el-radio-button>
                        </el-tooltip>
                    </el-radio-group>
                </el-form-item>
                <!--<el-form-item :label="$t('common.status.title')">-->
                    <!--<el-radio-group v-model="form.status">-->
                        <!--<el-radio :label="1">{{$t('common.status.enable')}}</el-radio>-->
                        <!--<el-radio :label="0">{{$t('common.status.disable')}}</el-radio>-->
                    <!--</el-radio-group>-->
                <!--</el-form-item>-->
              <!--语言管理 -->
              <el-row>
                <el-col :span="16">
                  <h5 class="top-title">{{$t('language.manage')}}</h5>
                </el-col>
                <el-col :span="8">
                  <div class="text-right">
                    <el-button type="primary" @click="addLanguage" size="small">{{$t('language.add')}}</el-button>
                  </div>
                </el-col>
              </el-row>
              <el-table
                  :data="vipProgramLangDatas"
                  style="width: 100%">
                <el-table-column
                    prop="languageId"
                    :label="$t('language.name')"
                    width="180">
                  <template slot-scope="scope">
                    <span>{{ scope.row.languageId === LanguageEnums.EN.code?
                        language=== LanguageEnums.EN.name? LanguageEnums.EN.enLabel:LanguageEnums.EN.cnLabel
                        :  language=== LanguageEnums.CN.name? LanguageEnums.CN.cnLabel:LanguageEnums.CN.enLabel  }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                    prop="programName"
                    :label="$t('vipProgram.name')"
                    >
                </el-table-column>
                <el-table-column  :label="$t('language.default')"
                                  width="120">
                  <template slot-scope="scope">
                    <el-tooltip
                        :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')"
                        placement="top">
                      <el-switch
                          v-model="scope.row.isDefault"
                          active-color="#ff6600"
                          inactive-color="#D9D9D9"
                          :active-value="1"
                          :inactive-value="0"
                          @change="changeDefault(scope.row)">
                      </el-switch>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column
                    :label="$t('operation.title')"
                    width="120">
                  <template slot-scope="scope">
                    <el-button @click="handleLanguageEditClick(scope.row)" type="text">{{$t('operation.edit')}}</el-button>
                    <el-button @click="handleLanguageDeleteClick(scope.row)" type="text">
                      {{$t('operation.remove')}}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
                <div class="sgs-bottom">
                    <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">{{$t('operation.submit')}}</el-button>
                </div>
            </el-form>
          <el-dialog
              :close-on-click-modal="false"
              :close-on-press-escape="false"
              :title="$t('language.add')"
              :visible.sync="langaugeDialogVisible"
              width="600"
              append-to-body>
            <el-form ref="languageForm" :model="languageForm" label-width="200px" label-position="left"  size="medium" class="sgs-form" :rules="languageRules">

              <el-form-item :label="$t('language.curLanguage')" prop="languageId" v-if="langaugeDialogVisible">
                <el-select filterable clearable v-model="languageForm.languageId" style="width:100%"
                           :placeholder="$t('operation.pleaseSelect')"
                           :no-data-text="$t('NoData')">
                  <el-option v-for="(languageObj,index) in languageDatas" :label="language === 'zh-CN'? languageObj.cnLabel:languageObj.enLabel"
                             :value="languageObj.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('vipProgram.name')" prop="programName" v-if="langaugeDialogVisible">
                <el-input v-model="languageForm.programName"
                          maxlength="200"></el-input>
              </el-form-item>
              <el-form-item :label="$t('language.default')" v-if="langaugeDialogVisible">
                <el-tooltip
                    :content="languageForm.isDefault==1?$t('common.yes'):$t('common.no')"
                    placement="top">
                  <el-switch
                      v-model="languageForm.isDefault"
                      active-color="#ff6600"
                      inactive-color="#D9D9D9"
                      :active-value="1"
                      :inactive-value="0"
                      >
                  </el-switch>
                </el-tooltip>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="langaugeDialogVisible=false">{{$t('operation.cancel')}}</el-button>
              <el-button type="primary" @click="submitLanguageForm('languageForm')" >
                {{$t('operation.submit')}}
              </el-button>
            </div>
          </el-dialog>
        </el-drawer>

    </basic-container>
</template>

<script>
    import {getList,add,remove,detail} from "@/api/document/vipProgram";
    import {getProductLine, queryDictionary} from "@/api/common/index";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {deepClone} from '@/util/util'
    import {mapGetters} from "vuex";
    export default {

        data(){
          var validateProgramName = (rule,value,callback) => {
            if(!value){
              callback(new Error(this.$t('vipProgram.validate.nameBlur')))
            }else {
              callback();
            }
          }
          var validateLanguageId = (rule,value,callback) => {
            if(!value){
              callback(new Error(this.$t('language.validate.selLanguageBlur')))
            }else {
              callback();
            }
          }
            return{
                removeLangIds:[],
                loading: false,
                editData:{},
                editFlag:false,
                LanguageEnums:LanguageEnums,
                vipProgramLangDatas:[],
                saveVipProgramLangDatas:[],//需要入库的saveVipProgramLangDatas
                languageForm:{
                  id:'',
                  newId:'',
                  languageId:'',
                  programName:'',
                  isDefault:0
                },
                langaugeDialogVisible:false,
                name: "list",
                title: '',
                dialogFormVisible: false,
                tableData: [],
                productLineData: [],
                btnGuestbookSubmit: false,
                form: {},
                query:{},
                sort: {ascs:'sort'},
                showOnWhichMenu:[],
                menus:[{sysKey: "1",sysValue:'Knowledge'},{sysKey: '2',sysValue: 'Data Insight'}],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
              rules: {
                programName: [
                  { required: true, validator:validateProgramName, trigger: 'blur' }
                ]
              },
              languageRules: {
                languageId:[
                  { required: true, validator:validateLanguageId, trigger: 'change' }
                ],
                programName: [
                  { required: true, validator:validateProgramName, trigger: 'blur' }
                ]
              },
              languageDatas:[
                LanguageEnums.EN,
                LanguageEnums.CN,
              ]
            }
        },
       methods:{
          //删除语言
           handleLanguageDeleteClick(row){
             //this.saveVipProgramLangDatas=deepClone(this.vipProgramLangDatas);
             //判断当前行是否有id，有id的话将isDeleted置为1
             if(!objectIsNull(row.id)){//存在ID
               row.isDeleted=1;
               this.vipProgramLangDatas=this.vipProgramLangDatas.filter(item => item.isDeleted === 0);
               this.removeLangIds.push(row.id);
               //将需要入库的Data数据中的IS_deleted做更新处理
              /* this.saveVipProgramLangDatas.forEach(obj=>{
                 if(row.id===obj.id){
                   obj.isDeleted=1
                 }
               });*/
             }else{//新增删除 使用newId处理删除
               this.vipProgramLangDatas=this.vipProgramLangDatas.filter(item => item.newId != row.newId);
             }
           },
           generateRandomId() {
             const uuid = require('uuid'); // 导入uuid库
             return uuid.v4(); // 使用uuid库生成随机ID并赋值给data中的变量
           },
           changeDefault(row){
             if(objectIsNull(row.id)){//用NewId
              this.vipProgramLangDatas.filter(item => item.newId != row.newId).forEach(obj=>{
                if(row.isDefault===1){
                  obj.isDefault=0
                }
              });
             }else{
               this.vipProgramLangDatas.filter(item => item.id != row.id).forEach(obj=>{
                 if(row.isDefault===1){
                   obj.isDefault=0
                 }
               });
             }

             if(row.isDefault===1){
               this.$set(this.form, 'programName', row.programName);
             }else{
               this.$set(this.form, 'programName', '');
             }
             //this.saveVipProgramLangDatas=deepClone(this.vipProgramLangDatas);
           },
         handleLanguageEditClick(row){
             this.editFlag=true;
             this.editData=row;
             this.languageForm=deepClone(row);
             this.$set(this.languageForm, 'editFlag', true)
             this.langaugeDialogVisible=true;
           },

         submitLanguageForm(form) {
           this.$refs[form].validate((valid) => {
             if(valid){
               debugger
               let newData = deepClone(this.languageForm);
               if (!this.editFlag) {
                 newData.id=null;
                 newData.newId = this.generateRandomId();
                 //校验是否已存在相同语种
                 console.log( '校验==',this.vipProgramLangDatas)
                 if (!objectIsNull(this.vipProgramLangDatas)) {
                   let repeatLanguaugeData = this.vipProgramLangDatas.filter(item => item.languageId === newData.languageId);
                   if (!objectIsNull(repeatLanguaugeData)) {//存在已有语种
                     this.$message({
                       message: this.$t('language.repeatLanguageMsg'),
                       type: 'warning'
                     });
                     return false;
                   }
                 }
                 this.setVipProgramData(newData);
                 this.vipProgramLangDatas.push(newData);
                 //this.saveVipProgramLangDatas=deepClone(this.vipProgramLangDatas);
                 console.log( '新增==',this.vipProgramLangDatas)
                 this.langaugeDialogVisible = false;
               }else{//编辑 校验是否已存在相同语种
                 //校验
                 let r = this.checkLangauge(newData);
                 if (!r) {
                   return false;
                 }
                 let newGridDatas=[];
                 this.vipProgramLangDatas.forEach(vipProgramLangData => {
                   if (objectIsNull(vipProgramLangData.id)) {
                     if (vipProgramLangData.newId === newData.newId) {
                       vipProgramLangData = newData;
                     }
                   } else {
                     if (vipProgramLangData.id === newData.id) {
                       vipProgramLangData = newData;
                     }
                   }
                   newGridDatas.push(vipProgramLangData);
                 })
                 this.setVipProgramData(newData);
                 this.vipProgramLangDatas = newGridDatas;
                 //this.saveVipProgramLangDatas=deepClone(this.vipProgramLangDatas);
                 this.langaugeDialogVisible = false;
               }


             }else{
               this.$notify({
                 title: this.$t('tip'),
                 message: this.$t('trf.trfValidateError'),
                 type: 'warning'
               });
               return false;
             }
           })
         },
         setVipProgramData(langForm){
           //赋值默认
           if(langForm.isDefault){
             this.$set(this.form, 'programName', langForm.programName);
           }
           //处理原数据的默认状态
           this.vipProgramLangDatas.forEach(vipProgramLangData => {
             debugger;
             if (objectIsNull(langForm.id)) {//新增
               if (vipProgramLangData.newId != langForm.newId && langForm.isDefault===1) {
                 vipProgramLangData.isDefault=0;
               }
             } else {
               if (vipProgramLangData.id != langForm.id && langForm.isDefault===1) {
                   vipProgramLangData.isDefault=0;
               }
             }
           })
         },
         /*submitLanguageForm(form) {
           //校验必填
           this.$refs[form].validate((valid) => {
             if (valid) {
               let newData = deepClone(this.languageForm);
               //新增
               if (!this.editFlag) {
                 newData.newId = this.generateRandomId();
                 //校验是否已存在相同语种
                 if (!validatenull(this.vipProgramLangDatas)) {
                   let repeatLanguaugeData = this.vipProgramLangDatas.filter(item => item.languageId === newData.languageId);
                   if (!objectIsNull(repeatLanguaugeData)) {//存在已有语种
                     this.$message({
                       message: this.$t('language.repeatLanguageMsg'),
                       type: 'warning'
                     });
                     return false;
                   }
                 }
                 this.setFormProgramName(newData);
                 if(newData.isDefault){
                   this.$set(this.form, 'programName', newData.programName);
                 }
                 //如果当前语言为default，则将原有的default置为非默认
                 this.setLanguageDefault(newData);

                 this.vipProgramLangDatas.push(newData);
                 this.saveVipProgramLangDatas=deepClone(this.vipProgramLangDatas);
                 this.langaugeDialogVisible = false;
               } else {//编辑 校验是否已存在相同语种
                 console.log('edit languageForm', this.languageForm);
                 //校验
                 let r = this.checkLangauge(newData);
                 if (!r) {
                   return false;
                 }
                 let newGridDatas=[];
                 this.vipProgramLangDatas.forEach(vipProgram => {
                   if (objectIsNull(vipProgram.id)) {
                     if (vipProgram.newId === newData.newId) {
                       vipProgram = newData;
                     }
                   } else {
                     if (vipProgram.id === newData.id) {
                       vipProgram = newData;
                     }
                   }
                   newGridDatas.push(vipProgram);
                 })

                 this.setFormProgramName(newData);
                 this.vipProgramLangDatas = newGridDatas;
                 this.setLanguageDefault(newData);
                 this.langaugeDialogVisible = false;
               }
             } else {
               return false;
             }
           });
         },*/
         setLanguageDefault(formData){
             if(!objectIsNull(formData)){
               this.vipProgramLangDatas.forEach(vipProgram => {
                 if (objectIsNull(vipProgram.id)) {
                   if (vipProgram.newId != formData.newId) {
                     vipProgram.isDefault=0;
                   }
                 } else {
                   if (vipProgram.id != formData.id) {
                     vipProgram.isDefault=1;
                   }
                 }
               })
             }
         },
         setFormProgramName(newLanguageForm){
           if(newLanguageForm.isDefault){
             this.$set(this.form, 'programName', newLanguageForm.programName);
           }
         },
           //添加语言
           addLanguage(){
             this.editFlag=false;
             this.languageForm.languageId='';
             this.languageForm.programName='';
             this.languageForm.isDefault=1;
             this.languageForm.isDeleted=0;
             this.$set(this.languageForm, 'editFlag', false)
             this.langaugeDialogVisible=true;
           },
           onSearch() {
               this.page.currentPage=1;
             this.onLoad();
           },
           onLoad() {
             this.loading = true;
             //将当前语言放入请求中
             if(LanguageEnums.EN.name==this.language){
               this.query.languageId=LanguageEnums.EN.code;
             }else{
               this.query.languageId=LanguageEnums.CN.code;
             }
             this.query.pageNo=this.page.currentPage;
             this.query.pageSize=this.page.pageSize;
               getList( this.query).then(res => {
                   this.loading = false;
                   this.tableData = res.data.data.records;
                   this.page.total = res.data.data.total;
               }).catch(() => {
                 this.loading = false;
               });
           },
           submitForm(form){
               this.$refs[form].validate((valid) => {
                 if (valid) {
                   let b = this.validatelanguage();
                   if (!b) {
                     return false;
                   }
                   debugger;
                  /* if(objectIsNull(this.saveVipProgramLangDatas)){
                     this.saveVipProgramLangDatas=this.vipProgramLangDatas;
                   }else{
                     for(let vipDate of this.vipProgramLangDatas){
                       for(let saveVipDate of this.saveVipProgramLangDatas){
                          if(objectIsNull(vipDate.id)){
                            continue;
                          }
                          if(vipDate.id==saveVipDate.id){
                            saveVipDate=vipDate;
                          }
                       }
                       //this.saveVipProgramLangDatas.push(vipDate);
                     }
                     if(!objectIsNull(this.removeLangIds)){
                         for(let saveVipProgram of this.saveVipProgramLangDatas){
                           if(!objectIsNull(saveVipProgram.id)){
                             if(this.removeLangIds.includes(saveVipProgram.id)){
                               saveVipProgram.isDeleted=1;
                             }
                           }
                         }

                     }
                     console.log('保存数据==',this.saveVipProgramLangDatas);
                   }*/
                   this.form.vipProgramLangDatas=this.vipProgramLangDatas;
                   this.btnGuestbookSubmit = true;
                   add(this.form).then(res => {
                     this.$message({
                       type: "success",
                       message: this.$t('api.success')
                     });
                     this.btnGuestbookSubmit = false;
                     this.dialogFormVisible = false;
                     this.onLoad();
                   }).catch(() => {
                     this.btnGuestbookSubmit = false;
                   });
                 } else {
                   this.$notify({
                     title: this.$t('tip'),
                     message: this.$t('trf.trfValidateError'),
                     type: 'warning'
                   });
                   return false;
                 }
               });
           },
           validatelanguage() {
              //验证是否存在语言数据
             if (objectIsNull(this.vipProgramLangDatas)) {
               this.$message({
                 type: "warning",
                 message: this.$t('language.validate.selLanguageBlur')
               });
               return false;
             }
             //验证是否有语言默认  去除设置default校验
             /*let repeatLanguaugeData = this.vipProgramLangDatas.filter(item => item.isDefault === 1);
             if(objectIsNull(repeatLanguaugeData)){
               this.$message({
                 type: "warning",
                 message: this.$t('language.validate.setDefaultLanguage')
               });
               return false;
             }*/
             return true;
           },
           removeRow(row){
               this.$confirm(this.$t('operation.confirmDelete'), {
                   confirmButtonText: this.$t('operation.confirm'),
                   cancelButtonText: this.$t('operation.cancel'),
                   type: "warning"
               }).then(() => {
                 remove(row.id).then(() => {
                   this.$message({
                     type: "success",
                     message: this.$t('api.success')
                   });
                   this.onLoad(this.page);
                 });


                   });
           },
           detailRow(row){
               this.title = this.$t('vipProgram.title.edit');
               detail(row.id).then(res => {
                   //获取后台数据付给页面，并打开
                   this.dialogFormVisible = true;
                   this.form=res.data.data;
                   this.vipProgramLangDatas= this.form.vipProgramLangDatas;
                   //this.saveVipProgramLangDatas=  this.form.vipProgramLangDatas;
                   if(this.form.showOnWhichMenu){
                      this.showOnWhichMenu = this.form.showOnWhichMenu.split(',')
                   }
                   this.removeLangIds=[];
                   console.log(this.menus)
               });

           },
            changeStatus(row){
                const modifyForm = {};
                this.form=row;
                modifyForm.modifiedby=this.userInfo.account;
                modifyForm.id = this.form.id;
                modifyForm.status = this.form.status;
                add(modifyForm).then(res =>{
                    if(res.data.success==1){
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    }else{
                        this.$message({
                            type: "fail",
                            message: res.data.message,
                        });
                    }
                });
            },
           selecProductLineCodeChange(val){
               let obj = {};
               obj = this.productLineData.find((item)=>{
                   return item.productLineCode === val;
               });
               this.form.productLineName=obj.productLineName;
           },
           addRow(){
               this.removeLangIds=[];
               this.title = this.$t('vipProgram.title.add');
               this.form = {};
               this.vipProgramLangDatas=[];
               //this.saveVipProgramLangDatas=[];
               this.dialogFormVisible =true;
           },
           showMenuChange(){
               if(this.showOnWhichMenu == null || this.showOnWhichMenu.length < 1){
                 this.form.showOnWhichMenu = null
               }else {
                 this.showOnWhichMenu.sort()
                 this.form.showOnWhichMenu = this.showOnWhichMenu.join(',')
               }
           },
         queryDictionary(){
             let params = {"sysKeyGroup":"ShowOnWhichMenu","systemID":1}
             queryDictionary(params).then(res=>{
               this.menus = res.data.data;
             });
             console.log(this.menus)
         },
         //分页查询
         async sizeChange(pageSize) {
           this.page.pageSize = pageSize;
           await this.onLoad();
         },
         async currentChange(pageCurrent) {
           this.page.currentPage=pageCurrent
           await this.onLoad();
         },

         checkLangauge(newData) {
           //先根据ID判断，如果不存在iD的话，认为页面新增数据，再根据newId判断
           if(!objectIsNull(newData.id)){
             let repeatLanguaugeData = this.vipProgramLangDatas.filter(item => item.id != newData.id);
             if(!objectIsNull(repeatLanguaugeData)){
               if(repeatLanguaugeData[0].languageId===newData.languageId){
                 this.$message({
                   message: this.$t('language.repeatLanguageMsg'),
                   type: 'warning'
                 });
                 return false;
               }
             }
           }else{
             let repeatLanguaugeData = this.vipProgramLangDatas.filter(item => item.newId != newData.newId);
             if(!objectIsNull(repeatLanguaugeData)){
               if(repeatLanguaugeData[0].languageId===newData.languageId){
                 this.$message({
                   message: this.$t('language.repeatLanguageMsg'),
                   type: 'warning'
                 });
                 return false;
               }
             }
           }
           return true;
         }
       },
        created() {
          this.removeLangIds=[];
            this.onLoad();
            getProductLine().then(res =>{
                const data = res.data.data;
                this.productLineData = data;
            });
            this.queryDictionary();

        },
        computed: {
            ...mapGetters([
                "userInfo",
                "language"
            ])
        },
      watch: {
        //监听语言变化
        language: function (newVal) {
          if (this.$refs['form'] != undefined) {
            this.$refs['form'].fields.forEach(item => {
              if (item.validateState === 'error') {
                this.$refs['form'].validateField(item.labelFor)
              }
            })
          }
          //重新加载table
          this.onLoad();
        },
      },
    }
</script>

<style scoped>
    .el-drawer__body{
        overflow-y: auto;
    }
</style>
