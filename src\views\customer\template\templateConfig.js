const productTemplateConfig = [
    {
    sectionLabel: "Supplier",
    sectionName: "Supplier",
    sectionCode: 'Supplier',
    sectionType: 'normal',
    display: 0,
    mandatory: 1,
    extend: true,
    fieldConfig:true,
    seq: 1,
    fieldList: [
        {
            "isActive": 1,
            "fieldCode": "supplierCode",
            "fieldLabel": "Vendor Code",
            "isRequired": 0,
            "configurable": 1,
            "defaultValue": "",
            "display":1
        }, {
            "isActive": 1,
            "fieldCode": "supplierName",
            "fieldLabel": "Vendor Name",
            "isRequired": 0,
            "configurable": 1,
            "defaultValue": "",
            "display":1
        },
        {
            "isActive": 1,
            "fieldCode": "supplierAddress",
            "fieldLabel": "Supplier Address",
            "isRequired": 0,
            "configurable": 0,
            "defaultValue": "",
            "display":1
        },
        {
            "isActive": 1,
            "fieldCode": "supplierContact",
            "fieldLabel": "Supplier Contact",
            "isRequired": 0,
            "configurable": 0,
            "defaultValue": "",
            "display":1
        },
        {
            "isActive": 1,
            "fieldCode": "supplierTel",
            "fieldLabel": "Supplier Tel",
            "isRequired": 0,
            "configurable": 0,
            "defaultValue": "",
            "display":1,
            "validation":{
                "required": 1,
                "rules": "^[0-9A-Za-z]{8}$",
                "error": [
                    {
                        "errorCode": "1",
                        "languageList": [
                            {
                                "languageId": 1,
                                "errorMsg": ""
                            },
                            {
                                "languageId": 2,
                                "errorMsg": ""
                            }
                        ]
                    }
                ]
            }
        },
        {
            "isActive": 1,
            "fieldCode": "supplierEmail",
            "fieldLabel": "Supplier Email",
            "isRequired": 0,
            "configurable": 0,
            "defaultValue": "",
            "display":1
        }
    ]
},
    {
        sectionLabel: "Manufacturer",
        sectionName: "Manufacturer",
        sectionCode: 'Manufacturer',
        sectionType:'normal',
        display: 0,
        mandatory: 1,
        extend: true,
        fieldConfig:true,
        seq: 2,
        fieldList: [
            {
                "isActive": 1,
                "fieldCode": "manufacturerCode",
                "fieldLabel": "Manufacturer Code",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1
            }, {
                "isActive": 1,
                "fieldCode": "manufacturerName",
                "fieldLabel": "Manufacturer Name",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerAddress",
                "fieldLabel": "Manufacturer Address",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": "",
                "display":1
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerContact",
                "fieldLabel": "Manufacturer Contact",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": "",
                "display":1
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerTel",
                "fieldLabel": "Manufacturer Tel",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": "",
                "display":1
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerEmail",
                "fieldLabel": "Manufacturer Email",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": "",
                "display":1
            }
        ]
    },
    {
        sectionLabel: "Service",
        sectionName: "Service",
        sectionCode: 'Service',
        sectionType:'normal',
        display: 0,
        mandatory: 0,
        extend: true,
        fieldConfig:false,
        seq: 3,
        fieldList: [
            {
                "isActive": 1,
                "fieldCode": "serviceType",
                "fieldLabel": "Service Type",
                "isRequired": 0,
                "fieldType":"select",
                "configurable": 1,
                "defaultValue": "",
                "display":1
            },
            {
                "isActive": 1,
                "fieldCode": "labCode",
                "fieldLabel": "Lab Name",
                "fieldType":"select",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1
            },
            {
                "isActive": 1,
                "fieldCode": "labContact",
                "fieldLabel": "Lab Contact Email",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1
            }
        ]
    },
    {
        sectionLabel: 'Care Label',
        sectionName: 'Care Label',
        sectionCode: 'CareLabel',
        sectionType: 'component',
        display: 0,
        mandatory: 0,
        extend: true,
        fieldConfig:false,
        seq: 5
    },
    {
        sectionLabel: 'Product Info',
        sectionName: 'Product Info',
        sectionCode: 'ProductInfo',
        sectionType: 'normal',
        display: 0,
        mandatory: 0,
        extend: true,
        fieldConfig:true,
        seq: 6,
        useMapping:1,
        fieldList: [
            {
                "isActive": 1,
                "fieldCode": "sampleCode",
                "fieldLabel": "Product Code",
                "isRequired": 0,
                "fieldType":"input",
                "configurable": 1,
                "defaultValue": "",
                "display":1,
                "customerFieldCode":""
            },
            {
                "isActive": 1,
                "fieldCode": "sampleCategory",
                "fieldLabel": "Product Category",
                "fieldType":"select",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1,
                "onlyShow":1,
                "customerFieldCode":""
            },
            {
                "isActive": 1,
                "fieldCode": "department",
                "fieldLabel": "Department",
                "fieldType":"input",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1,
                "customerFieldCode":""
            },
            {
                "isActive": 1,
                "fieldCode": "sampleDescription",
                "fieldLabel": "Product Description",
                "fieldType":"textArea",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": "",
                "display":1,
                "customerFieldCode":""
            }
        ]
    },
    {
        sectionLabel: 'Product Detail',
        sectionName: 'Product Detail',
        sectionCode: 'ProductDetail',
        sectionType: 'component',
        display: 0,
        mandatory: 0,
        extend: true,
        fieldConfig:false,
        seq: 7
    },
    {
        sectionLabel: 'Workbooks',
        sectionName: 'Workbooks',
        sectionCode: 'Workbooks',
        sectionType: 'component',
        display: 0,
        mandatory: 0,
        extend: true,
        fieldConfig:false,
        seq: 8
    },
    {
        sectionLabel: 'Report & Certificate',
        sectionName: 'Report & Certificate',
        sectionCode: 'ReportCertificate',
        sectionType: 'component',
        display: 1,
        disabled:true,
        mandatory: 0,
        extend: true,
        fieldConfig:false,
        seq: 9
    },
    {
        sectionLabel: 'Bill of Materials',
        sectionName: 'Bill of Materials',
        sectionCode: 'BillOfMaterials',
        sectionType: 'component',
        display: 0,
        mandatory: 0,
        extend: true,
        fieldConfig:false,
        seq: 10
    },
]
const materialConfig = [
    {
        sectionName: "Supplier",
        sectionCode:'Supplier',
        sectionLabel:'Supplier',
        display:0,
        mandatory:1,
        extend:true,
        fieldConfig:true,
        seq:1,
        fieldList: [
            {
                "isActive": 1,
                "fieldCode": "supplierCode",
                "fieldLabel": "VendorCode",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            },{
                "isActive": 1,
                "fieldCode": "supplierName",
                "fieldLabel": "Vendor Name",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "supplierAddress",
                "fieldLabel": "Supplier Address",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "supplierContact",
                "fieldLabel": "Supplier Contact",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "supplierTel",
                "fieldLabel": "Supplier Tel",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "supplierEmail",
                "fieldLabel": "Supplier Email",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            }
        ]
    },
    {
        sectionName: "Manufacturer",
        sectionCode:'Manufacturer',
        sectionLabel:'Manufacturer',
        display:0,
        mandatory:1,
        extend:true,
        fieldConfig:true,
        seq:2,
        fieldList: [
            {
                "isActive": 1,
                "fieldCode": "manufacturerCode",
                "fieldLabel": "Manufacturer Code",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            },{
                "isActive": 1,
                "fieldCode": "manufacturerName",
                "fieldLabel": "Manufacturer Name",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerAddress",
                "fieldLabel": "Manufacturer Address",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerContact",
                "fieldLabel": "Manufacturer Contact",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerTel",
                "fieldLabel": "Manufacturer Tel",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "manufacturerEmail",
                "fieldLabel": "Manufacturer Email",
                "isRequired": 0,
                "configurable": 0,
                "defaultValue": ""
            }
        ]
    },
    {
        sectionName: "Service",
        sectionCode:'Service',
        sectionLabel:'Service',
        display:0,
        mandatory:0,
        extend: true,
        fieldConfig:false,
        seq:3,
        fieldList: [
            {
                "isActive": 1,
                "fieldCode": "serviceType",
                "fieldLabel": "Service Type",
                "fieldType":"select",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "labCode",
                "fieldLabel": "Lab Name",
                "fieldType":"select",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            },
            {
                "isActive": 1,
                "fieldCode": "labContact",
                "fieldLabel": "Lab Contact Email",
                "isRequired": 0,
                "configurable": 1,
                "defaultValue": ""
            }
        ]
    },
    {
        sectionName:'Material header',
        sectionCode:'Material Info',
        sectionLabel:'Material Info',
        display:0,
        mandatory:0,
        extend: true,
        fieldConfig:true,
        seq:4,
        useMapping:1,
        fieldList:  [
            {
                "fieldCode": "sampleCode",
                "defaultValue": "",
                "fieldLabel": "Material Code",
                "isRequired": 0,
                "isActive": 1,
                "configurable": 1,
                "customerFieldCode":""
            },
            {
                "fieldCode": "sampleType",
                "defaultValue": "",
                "fieldLabel": "Material Type",
                "isRequired": 0,
                "fieldType":'select',
                "isActive": 1,
                "configurable": 1,
                "onlyShow":1,
                "customerFieldCode":""
            },
            {
                "fieldCode": "sampleName",
                "defaultValue": "",
                "fieldLabel": "Material Name",
                "isRequired": 0,
                "isActive": 1,
                "configurable": 1,
                "customerFieldCode":""
            },
            {
                "fieldCode": "sampleDescription",
                "defaultValue": "",
                "fieldLabel": "Material Description",
                "isRequired": 0,
                "isActive": 1,
                "configurable": 1,
                "customerFieldCode":""
            }
        ]},
]

let config = {materialConfig,productTemplateConfig}
export default config;