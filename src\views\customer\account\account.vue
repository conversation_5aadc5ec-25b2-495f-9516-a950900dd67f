<template>
    <div>
        <el-form :inline="true" :model="query" size="medium" class="text-right" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="query.contactName" @input="onSearch" @keyup.enter.native="onSearch" @clear="onSearch" :placeholder="$t('account.userName')" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-button @click="addRow" class="line-btn">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
                stripe
                :data="tableData"
                style="width: 100%">
            <el-table-column
                    type="index"
                    fixed
                    label="#"
                    width="30">
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true"
                    prop="name"
                    width="140"
                    :label="$t('account.userName')">
            </el-table-column>
            <el-table-column
                    prop="code"
                    :label="$t('account.code')"
                    width="170">
            </el-table-column>
            <el-table-column
                    prop="email"
                    :label="$t('account.email')"
                    width="170">
            </el-table-column>
            <el-table-column
                    prop="department"
                    :label="$t('department.label')"
                    :formatter="departmentFormat"
                    width="130">
                <template slot-scope="scope">
                    <el-tag size="small" type="info">{{scope.row.department}}</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="postEntityList"
                    :label="$t('account.post')">
                <template slot-scope="scope">
                    <el-tag size="small" type="info" v-for="post in scope.row.postEntityList" :key="post.postName">
                        {{post.postName}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="status"
                    :label="$t('common.status.title')"
                    width="80">
                <template slot-scope="scope">
                    <el-tooltip
                            :content="scope.row.enableStatus==1 && scope.row.deleteFlag!=1?$t('common.status.enable'):$t('common.status.disable')"
                            placement="top">
                        <el-switch
                            v-model="scope.row.enableStatus"
                            :disabled="scope.row.deleteFlag==0"
                            active-color="#ff6600"
                            inactive-color="#D9D9D9"
                            :active-value="1"
                            :inactive-value="0"
                            @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="80">
                <template slot-scope="scope">
                    <el-button type="text" @click="detailRow(scope.row)" :disabled="scope.row.deleteFlag==0">
                        {{$t('operation.edit')}}
                    </el-button>
                    <!--<el-button @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>-->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog :title="$t('account.management')"  :visible.sync="dialogFormVisible" @close="closeDialog" :close-on-click-modal="false"  height="600px">
            <el-form ref="form" :model="form"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     class="sgs-form"
            >

                <!--<el-form-item :label="$t('account.code')"  autocomplete="off">
                    <el-input
                            clearable
                            v-model="form.code"
                            :placeholder="$t('register.emailBlur')"
                            maxlength="150"
                            :readonly="readonlyInput"
                            @focus="cancelReadOnly()"
                            autoComplete="off">
                    </el-input>
                </el-form-item>-->

                <el-form-item :label="$t('account.email')"
                              :rules="[{ required: true, message: this.$t('register.emailBlur'), trigger: 'blur' },{ type: 'email',message: this.$t('register.emailRigthBlur'),trigger: ['blur', 'change']}]"
                              prop="email" autocomplete="off">
                    <el-input
                            clearable
                            v-model="form.email"
                            :placeholder="$t('register.emailBlur')"
                            maxlength="150"
                            :readonly="readonlyInput"
                            @focus="cancelReadOnly()"
                            autoComplete="off">
                    </el-input>
                </el-form-item>
                <el-form-item :label="$t('contact.phone')"
                              prop="telephone"
                              :rules="[{ required: true, message: this.$t('contact.validate.phoneValidate'), trigger: 'blur' }]"
                              autocomplete="off">
                    <el-input
                            clearable
                            v-model="form.telephone"
                            maxlength="30"
                            autoComplete="off">
                    </el-input>
                </el-form-item>

                <el-form-item v-if="!editModel" :label="$t('account.password')"
                              :rules="{ required: true, message: $t('account.validate.passwordBlur'), trigger: 'blur' }"
                              prop="password">
                    <el-input
                            clearable
                            type="password"
                            v-model="form.password"
                            maxlength="50"
                            :readonly="readonlyInput"
                            @focus="cancelReadOnly()"
                            autocomplete="off">
                    </el-input>
                </el-form-item>
                <el-form-item :label="$t('account.userName')"
                              :rules="{ required: true, message: $t('account.validate.userNameBlur'), trigger: 'blur' }"
                              prop="name">
                    <el-input
                            clearable
                            v-model="form.name"
                            maxlength="100"
                    ></el-input>
                </el-form-item>
                <!--<el-form-item :label="$t('customer.account.mobile')" :rules="{ required: true, message: $t('account.validate.mobileBlur'), trigger: 'blur' }" prop="telephone">-->
                <!--<el-input v-model="form.telephone"></el-input>-->
                <!--</el-form-item>-->
                <!--部门无数据时给出提示-->
                <span v-if="isShowDeptMessageFlag" style="color:red; margin-top: 1px;margin-bottom: 2px">
                            {{$t('reminder')}} : {{$t('account.noDeptMessage')}}
                        </span>
                <el-form-item :label="$t('department.label')"
                              :rules="{ required: true, message: $t('account.validate.departmentBlur'), trigger: 'blur' }"
                              prop="customerDepartmentId">
                    <el-select clearable v-model="form.customerDepartmentId" @change="selectDeptChange"
                               style="width:100%" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(dept) in deptData"
                                   :key="dept.id"
                                   :label="dept.departmentName"
                                   :value="dept.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item :label="$t('beLongTo.title')">
                    <el-select v-model="belongIds" @change="selectBeLongChange" :placeholder="$t('beLongTo.title')"
                               multiple
                               reserve-keyword filterable style="width: 100%;" clearable :no-data-text="$t('NoData')">
                        <el-option v-for="(beLang,index) in beLongData" :label="beLang.beLongName"
                                   :value="beLang.beLongId+''"></el-option>
                    </el-select>
                </el-form-item>


                <el-form-item :label="$t('account.post')"
                              :rules="[{ required: true, message: $t('account.validate.postBlur'), trigger: 'change' }]"
                              prop="posts">
                    <el-select multiple clearable v-model="form.posts" style="width:100%" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(post) in postData"
                                   :key="post.postId"
                                   :label="post.postName"
                                   :value="post.postId"></el-option>
                    </el-select>
                </el-form-item>

              <el-form-item :label="$t('register.serviceUnit')"
                            required
                            :rules="[{ required: true, message: $t('account.validate.serviceUnitBlur'), trigger: 'change' }]"
                            prop="selServiceUnits">
                <el-select v-model="form.selServiceUnits"
                           multiple
                           collapse-tags
                           :placeholder="$t('register.serviceUnitBlur')"
                           @change="selectServiceTypeChange" style="width: 100%;">
                  <el-option v-for="(serviceUnit, index) in serviceUnits" :key="serviceUnit.serviceUnitCode" :label="serviceUnit.serviceUnitName"
                             :value="serviceUnit.serviceUnitCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <span  style="color:red; margin-top: 1px;margin-bottom: 2px">
                            {{$t('reminder')}} : {{$t('account.editServiceUnitMessage')}}
                        </span>

            </el-form>
            <div class="bottom clearfix " style="text-align: center;margin-top:100px">
                <el-button size="small" @click="closeDialog">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="submitForm('form')" :loading="antiDuplicateSubmission">
                    {{$t('operation.submit')}}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {validatenull,objectIsNull} from "@/util/validate";
    import { getUsers,add} from "@/api/customer/externalAccount";
    import { createAccount,updateAccount } from "@/api/customer/account";
    import {queryCustomerForId} from "@/api/customer/customer";
    import {getDepts} from "@/api/customer/customerDept";
    import {addAccountDept, getAccountDeptIds} from "@/api/customer/accountDept";
    import {getDeptBeLongTos} from "@/api/customer/customerDeptBeLongTo";
    import {mapGetters} from "vuex";
    import {deepClone} from '@/util/util';
    import { encryptor } from "@/util/auth";
    import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";


    export default {

        data() {
            return {
              serviceUnits:[],
                //selServiceUnits:'',
                belongIds: [],
                isShowDeptMessageFlag: false,
                name: "externalAccount",
                dialogFormVisible: false,
                antiDuplicateSubmission: false,
                beLongData: [],
                deptData: [],
                postData: [],
                tableData: [],
                accountForm: {},
                readonlyInput: true,
                form: {
                    code: '',
                    password: '',
                    name: '',
                    telephone:'',
                    posts: [],
                    customerDepartmentId: '',
                    customerDepartmentIds: [],
                    email: '',
                    belongIds: '',
                    serviceUnitCode:[],
                    defaultServiceUnitCode:'',
                    selServiceUnits:[],
                },
                query: {
                    contactName:''
                },
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                editModel: false,
            }
        },
      watch: {
        'language': function () {
          this.initData();
        }
      },
        methods: {
            async queryServiceType() {
             
              let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
              //去除other的Serviceunit
              serviceUnitsDatas = serviceUnitsDatas.filter(item => item.productLineCode != 'other');
              this.serviceUnits=serviceUnitsDatas;
              console.log(serviceUnitsDatas);
            },
            selectServiceTypeChange(values){
              //将默认的设置为null
              this.selDefaultServiceUnit=null;
              let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, values,this.form.selServiceUnits);
              this.mergeFormServiceUnitData(serviceUnitObj);
            },
            mergeFormServiceUnitData(serviceUnitObj){
             
              this.form.serviceUnitCode=this.form.selServiceUnits;
              if(!objectIsNull(serviceUnitObj)){
                let serviceUnits = serviceUnitObj.serviceUnits;
                let defaultServiceUnitsCode='';
                for(let serviceUnit of serviceUnits){
                  if(serviceUnit.isDefault){
                    defaultServiceUnitsCode=serviceUnit.serviceUnit;
                    break;
                  }
                }
                this.form.defaultServiceUnitCode= defaultServiceUnitsCode;
              }
            },
            selectBeLongChange(val) {
                console.log(this.belongIds);
                
                /*this.form.belongIds = '';
                if(!validatenull(val)){
                    this.$set(this.form, 'belongIds', this.$lodash.join(val, ','));
                }*/
            },
            selectDeptChange(val) {
                //清空原来选择的Belong
                this.belongIds = [];
                //查询所属部门的belong
                this.queryBelongByDeptId(val);
            },

            queryBelongByDeptId(deptId) {
                getDeptBeLongTos(deptId).then(res => {
                    const data = res.data.data;
                    this.beLongData = data;
                });
            },
            addRow() {
              //this.form = {};
              this.form.selServiceUnits = [];
              this.editModel = false;
              this.beLongData = [];
              this.belongIds = [];

              this.form.code = '';
              this.form.password = '';
              this.form.name = '';
              this.form.telephone = '';
              this.form.posts = [];
              this.form.customerDepartmentId = '';
              this.form.customerDepartmentIds = [];
              this.form.email = ''
              this.form.belongIds = ''
              this.form.serviceUnitCode = [];
              this.form.defaultServiceUnitCode = '';
              this.form.selServiceUnits = [];


              this.dialogFormVisible = true;
              //this.$refs['form'].resetFields();
            },
            closeDialog() {
                this.$refs['form'].resetFields();
                this.form.id=null
                this.dialogFormVisible = false;
            },

            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page) {
                this.query.page = this.page.currentPage;
                this.query.rows = this.page.pageSize;
                this.query.total = this.page.total;
                this.query.companyId = this.userInfo.userMgtCompanyId;
                this.query.name = this.query.contactName;
                getUsers(this.query).then(res => {
                    this.tableData = res.data.rows;
                    this.page.total = res.data.records;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.antiDuplicateSubmission = true;
                        if(validatenull(this.form.code)){
                            this.form.code = this.form.email;
                        }
                        this.form.companyId = this.userInfo.userMgtCompanyId;

                        // 部门逻辑处理
                        let deptNames = '';
                        const customerDepartments = [];
                        var deptId = [];
                        deptId.push(this.form.customerDepartmentId)
                        this.form.customerDepartmentIds = deptId;
                        this.form.customerDepartmentIds.forEach((deptId) => {
                            let customerDept = {'id': deptId, 'departmentName': this.departmentName(deptId)};
                            customerDepartments.push(customerDept);
                            deptNames = deptNames + "," + customerDept.departmentName
                        })
                        this.form.department = deptNames.substring(1, deptNames.length);

                        this.form.createdBy = this.userInfo.account
                        this.form.modifiedby = this.userInfo.account;
                        const accountPosts = [];
                        this.form.posts.forEach((value) => {
                            const accountPost = {
                                postId: value
                            }
                            accountPosts.push(accountPost);
                        })
                        this.form.postEntityList = accountPosts;
                        let params = Object.assign({}, this.form);
                        params.password = encryptor(params.password);
                        this.antiDuplicateSubmission = false;
                        if(params.id){
                            updateAccount(params).then(res=>{
                                if (res.data.success) {
                                    this.postUpdate(res,customerDepartments)           
                                } else {
                                    this.$message.error(res.data.errorMessage);
                                }
                                this.onLoad(this.page);
                            })
                        }else{
                            createAccount(params).then(res => {
                            
                                if (res.data.success) {
                                    this.postUpdate(res,customerDepartments)
                                } else {
                                    this.$message.error(res.data.errorMessage);
                                }
                                this.onLoad(this.page);          
                            
                        }); 
                        }
                       
                    } else {
                        return false;
                    }
                });

            },
            postUpdate(res,customerDepartments){
                this.dialogFormVisible = false;
                // 保存账号及部门关系
                this.accountForm.customerDepartments = customerDepartments;
                this.accountForm.customerAccountId = res.data.data.id;
                
                console.log(this.belongIds);
                if(validatenull(this.belongIds)){
                this.$set(this.form, 'belongIds', null);
                }else{
                    this.$set(this.form, 'belongIds', this.$lodash.join(this.belongIds, ','));
                }
                this.accountForm.belongIds=this.form.belongIds;
                this.addAccountDepts(this.accountForm);
                this.$message({
                    type: "success",
                    message: this.$t('api.success')
                });
            },
           async detailRow(rowObj) {
                 let row = deepClone(rowObj);
                this.belongIds=[];
                this.form = row;
                //赋值ServiceUnit回显
                 if(!objectIsNull(row.serviceUnitInfo)){
                   this.$set(this.form, 'selServiceUnits', row.serviceUnitInfo.serviceUnitCode);
                   this.$set(this.form, 'serviceUnitCode', this.form.selServiceUnits);
                 }else{
                   this.$set(this.form, 'selServiceUnits', []);
                   this.$set(this.form, 'serviceUnitCode', []);
                 }
                getAccountDeptIds(row.id).then(res => {
                    this.dialogFormVisible = true;
                    this.editModel = true;
                    try {
                        this.$set(this.form, 'customerDepartmentId', res.data.data.customerDepartmentId);
                    } catch (e) {
                        this.$set(this.form, 'customerDepartmentId', 0);
                    }
                    //根据deptId查询belong
                    this.queryBelongByDeptId(this.form.customerDepartmentId);
                    //回显belong
                    try {
                        this.$set(this.form, 'belongIds', res.data.data.belongIds);
                    } catch (e) {
                        this.$set(this.form, 'belongIds', null);
                    }
                      getDeptBeLongTos(this.form.customerDepartmentId).then(res => {
                            const data = res.data.data;
                            this.beLongData = data;
                            if(!validatenull(this.form.belongIds)){
                                 this.belongIds=this.$lodash.split(this.form.belongIds, ',');
                            }
                            console.log(this.belongIds);
                      });

                    if (!validatenull(row.postEntityList)) {
                        const postList = [];
                        row.postEntityList.forEach((item) => {
                            this.postData.forEach((post) => {
                                if(item.postId==post.postId){
                                      postList.push(item.postId);
                                }
                            });
                        });
                        this.$set(this.form, 'posts', postList)
                    } else {
                        this.$set(this.form, 'posts', [])
                    }
                });
            },
            addAccountDepts(accountForm) {
                addAccountDept(accountForm).then(res => {

                });
            },
            cancelReadOnly() {
                this.readonlyInput = false;
            },
            changeStatus(row) {
                console.log(row);
                this.form = row;
                const modifiedForm = {
                    id: this.form.id,
                    enableStatus: this.form.enableStatus,
                    modifiedby: this.userInfo.account
                };
                add(modifiedForm).then(res => {
                    if (res.data.success == 1) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    } else {
                        this.$message.error(res.data.errorMessage);
                    }
                    // this.antiDuplicateSubmission = false;
                    // this.dialogFormVisible=false;
                    // this.onLoad(this.page);
                });
            },
            departmentName(id) {
                let dept = this.deptData.filter((item) => {
                    return item.id == id
                })
                if (!validatenull(dept)) {
                    return dept[0].departmentName;
                } else {
                    return ''
                }
            },
          async queryCustomerInfo(){
            if(!objectIsNull(this.userInfo.companyId)){
              await queryCustomerForId(this.userInfo.companyId).then(res => {
                
                const data = res.data.data;
                if(!objectIsNull(data)){
                  let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits, data.serviceUnits);
                  
                  this.serviceUnitDataItems = resultObj.serviceUnitDataItems;

                  if(!objectIsNull(this.serviceUnitDataItems)){
                     let newServiceUnits=[];
                     for (let serviceUnitObj of this.serviceUnits) {
                         if( this.serviceUnitDataItems.includes(serviceUnitObj.serviceUnitCode)){
                           newServiceUnits.push(serviceUnitObj)
                         }
                      }
                    //去除other的Serviceunit
                      newServiceUnits = newServiceUnits.filter(item => item.productLineCode != 'other');
                     this.serviceUnits=newServiceUnits;
                  }
                }
              });
            }
          },
          async initData(){
            await  this.queryServiceType();
            //加载该公司的Service Unit
            await  this.queryCustomerInfo();
          }
        },
        created() {
            this.initData();
            this.onLoad(this.page);
            const customerId = this.userInfo.companyId;
            getDepts(customerId).then(res => {
                const data = res.data.data;
                this.deptData = data;
                //判断部门是否有数据
                if (validatenull(data)) {
                    this.isShowDeptMessageFlag = true;
                } else {
                    this.isShowDeptMessageFlag = false;
                }
            });
            getUsers({"companyId": this.userInfo.userMgtCompanyId, "id": this.userInfo.userMgtId}).then(res => {
                const data = res.data.rows;
                if (!validatenull(data)) {
                    this.postData = data[0].postEntityList;
                }
            });
        },
        computed: {
            ...mapGetters([
                "userInfo",
                "language"
            ])
        },

    }
</script>

<style lang="scss" scoped>

</style>
