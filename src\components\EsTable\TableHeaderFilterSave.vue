<template>
  <div class="TableHeaderFilterSave" id="TableHeaderFilterSave">
    <el-row>
      <el-col style="text-align: right" :span="24">
        <el-popover title="" placement="bottom" width="300" trigger="click">
          <el-table
            :data="filterData"
            style="width: 100%"
            :show-header="false"
            size="small"
            @row-click="filterTableRowClick"
          >
            <el-table-column prop="name"></el-table-column>
            <el-table-column align="right" width="100" prop="id">
              <template #default="{ row }">
                <el-button type="primary" link @click="setFilterDefault(row)">
                  {{ row.defaultFlag - 0 == 1 ? '' : $t('filter.set') }}
                  {{ $t('filter.default') }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column width="65" prop="id">
              <template #default="{ row }">
                <el-popconfirm
                  confirm-button-text="Submit"
                  cancel-button-text="Cancel"
                  icon="el-icon-info"
                  icon-color="red"
                  width="170"
                  title="Delete the filter?"
                  @confirm="deleteFilter(row.id)"
                >
                  <template #reference>
                    <el-button v-if="row.self" type="primary" link @click.stop>
                      {{ $t('filter.delete') }}
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <template #reference>
            <div class="filter_select_btn">
              <el-input
                style="width: 100%"
                :size="size || 'medium'"
                readonly
                v-model="filterClickName"
                type="text"
                :placeholder="$t('filter.filterPlaceHolder')"
                @clear="clearFilterObj"
              ></el-input>
              <u
                v-if="filterClickName"
                style="
                  position: absolute;
                  right: 10px;
                  line-height: 40px;
                  cursor: pointer;
                "
              >
                <el-icon @click="clearFilterObj"><CircleClose /></el-icon>
              </u>
            </div>
          </template>
        </el-popover>
        <el-popover
          :title="$t('filter.saveFilter')"
          placement="bottom"
          width="250"
          @show="showSaveFilter"
          trigger="click"
        >
          <el-form
            ref="filterSaveForm"
            @submit.prevent
            :model="filterObj"
            label-width="20px"
            size="small"
          >
            <el-form-item prop="replaceOrNew">
              <el-radio
                v-model="filterObj.replaceOrNew"
                label="0"
                class="radio-wrap"
                @change="changeSaveType"
              >
                {{ $t('filter.replaceAnExisting') }}
              </el-radio>
              <el-radio
                v-model="filterObj.replaceOrNew"
                label="1"
                @change="changeSaveType"
              >
                {{ $t('filter.saveAsNew') }}
              </el-radio>
            </el-form-item>
            <el-form-item
              prop="name"
              required
              label=" "
              :rules="[
                {
                  required: true,
                  message: 'Name is required',
                  trigger: ['change', 'blur'],
                },
              ]"
            >
              <el-input
                type="text"
                :size="size || 'small'"
                maxlength="50"
                show-word-limit
                v-model="filterObj.name"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="filterObj.replaceOrNew - 0 == 1"
              prop="publicType"
            >
              <el-radio-group v-model="filterObj.publicType" class="radio-wrap">
                <el-radio label="0">{{ $t('filter.saveAsLocal') }}</el-radio>
                <el-radio label="1">{{ $t('filter.saveAsPublic') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :size="size || 'small'"
                class="save-button"
                @click="save"
              >
                {{ $t('filter.save') }}
              </el-button>
            </el-form-item>
          </el-form>
          <template #reference>
            <el-button
              type="primary"
              class="filter_save_btn"
              :size="size || 'small'"
            >
              {{ $t('filter.saveFilter') }}
            </el-button>
          </template>
        </el-popover>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { filterSetDefault, filterDelete, filterSave } from '@/api/search'
import { ElNotification } from 'element-plus'
import { CircleClose } from '@element-plus/icons-vue'

// 注入依赖
const { t } = useI18n()
const route = useRoute()

// 定义 props
const props = defineProps({
  sourceType: {
    type: String,
    default: 'TRF',
  },
  filterConfigObj: {
    type: Object,
    required: true,
  },
  tableObj: {
    type: Object,
    required: true,
  },
  resetQueryForm: {
    type: Function,
    required: true,
  },
  size: {
    type: String,
    default: 'medium', // 设置默认值
  },
})

// 定义响应式数据
const filterObj = ref({
  id: '',
  replaceOrNew: '0',
  publicType: '0',
  name: '',
  content: {},
})
const filterClickName = ref('')
const filterData = ref([])
const filterSaveForm = ref(null)

// 定义方法
const filterTableRowClick = (row, column, event) => {
  const { name, id } = row
  const { currentId } = props.filterConfigObj.getFilterContentObj()
  if (id === currentId) {
    return
  }
  filterClickName.value = name
  props.filterConfigObj.changeFilter(row.id)
}

const setFilterDefault = (row) => {
  if (row.defaultFlag - 0 === 1) {
    return
  }
  filterSetDefault(row.id)
    .then((res) => {
      ElNotification.success('Success')
      props.filterConfigObj.updateFilterDefault(row.id)
      initFilterDataAndName()
    })
    .catch((error) => {
      console.log('set detault error', error)
      ElNotification.error('Set default error')
    })
}

const deleteFilter = (id) => {
  filterDelete(id)
    .then((res) => {
      ElNotification.success('Delete Success')
      props.filterConfigObj.delFilterConfig(id)
      initFilterDataAndName()
    })
    .catch((err) => {
      ElNotification.error(t('api.error'))
      console.log('delete filter error', err)
    })
}

const changeSaveType = () => {
  filterObj.value.publicType = '0'
  filterObj.value.content = ''
  if (filterObj.value.replaceOrNew - 0 === 1) {
    filterObj.value.id = ''
    filterObj.value.name = ''
  } else {
    const { currentConfig } = props.filterConfigObj.getFilterContentObj()
    if (currentConfig) {
      const { id, name } = currentConfig
      filterObj.value.id = id
      filterObj.value.name = name
    }
  }
}

const save = () => {
  filterSaveForm.value.validate((valid) => {
    if (!valid) {
      return
    }
    const columns = props.tableObj.getColumns()
    const cols = (columns || []).forEach((col, seq) => {
      const { hide, prop } = col
      cols.push({ hide, prop, seq })
    })
    const queryModal = props.tableObj.getQueryModal()
    const content = JSON.stringify({
      filter: queryModal,
      columns: cols,
    })
    const { id } = filterObj.value
    const create = !id
    const param = {
      ...filterObj.value,
      content,
      sourceType: props.sourceType,
    }
    filterSave(param)
      .then((res) => {
        ElNotification.success('Save Success')
        const { data } = res
        if (create) {
          props.filterConfigObj.addFilterConfig(data)
        } else {
          props.filterConfigObj.updFilterConfig(data)
        }
        initFilterDataAndName()
      })
      .catch((err) => {
        console.log('saveFilter err', err)
        ElNotification.error(t('api.error'))
      })
  })
}

const showSaveFilter = () => {
  const { currentConfig } = props.filterConfigObj.getFilterContentObj()
  filterObj.value.replaceOrNew = '0'
  filterObj.value.publicType = '0'
  filterObj.value.content = ''
  if (!currentConfig) {
    filterObj.value.id = ''
    filterObj.value.name = ''
    return
  }
  const { id, name } = currentConfig
  filterObj.value.id = id
  filterObj.value.name = name
}

const initFilterDataAndName = () => {
  nextTick(() => {
    const { filterList, currentConfig } =
      props.filterConfigObj.getFilterContentObj()
    filterData.value = filterList || []
    let cacheName = ''
    if (props.sourceType === 'TRF') {
      const cacheFilterId = route.query.filterId
      try {
        const cacheFilter =
          (filterList || []).find((f) => f.id === cacheFilterId) || {}
        cacheName = cacheFilter.name
        props.filterConfigObj.updFilterConfig(cacheFilter)
      } catch (e) {}
    }
    const { name } = currentConfig || {}
    filterClickName.value = name || cacheName || ''
  })
}

const clearFilterObj = () => {
  filterClickName.value = ''
  props.resetQueryForm()
}

// 生命周期钩子
onMounted(() => {
  initFilterDataAndName()
})
</script>

<style lang="scss" scoped>
@use '@/assets/style/unit.module.scss' as *;

.TableHeaderFilterSave {
  width: 345px;
  .select_filter_cell.el-table__cell {
    color: $primary-color !important;
  }
  .el-input__inner {
    padding-left: 0px;
    padding-right: 40px;
    cursor: pointer;
  }
  .filter_save_btn {
    background: #ffffff;
    color: $primary-color;
    border-color: $primary-color;
    margin-left: 6px;
  }
  .filter_select_btn {
    position: relative;
    width: 223px;
    display: inline-block;
  }
}
.save-button {
  margin: 0 auto;
}
.radio-wrap .el-radio {
  width: 200px;
}
</style>
