<template>
    <div v-if="pageLoad">
        <div class="smart_views_company_info_index" id="smart_views_company_info_index">
            <el-row>
                <el-col :span="18" :offset="3">
                    <el-card class="sgs-box content-item" id="company-info">
                        <p class="card-title">{{t('companyInfo.title')}}</p>
                        <el-divider style="margin: 20px 0"></el-divider>
                        <template v-if="!editPage">
                            <HorizontalDivider paddingTop="0px" paddingBottom="10px" />
                            <el-descriptions
                                    :column="2"
                                    label-width="150"
                                    class="company-detail-descriptions"
                            >
                                <el-descriptions-item
                                        :label="t('register.companyName')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.customerNameEn}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('register.companyNameCN')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.customerNameZh}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('register.companyAddress')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.customerAddressEn}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('register.companyAddressCN')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.customerAddressZh}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('register.taxNo')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.taxNo}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('companyInfo.customerNo')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.bossNo}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('companyInfo.reportNo')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.reportNo}}
                                </el-descriptions-item>

                                <el-descriptions-item
                                        :label="t('companyInfo.email')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    {{companyInfoForm.contacts}}
                                </el-descriptions-item>
                                <el-descriptions-item
                                        :label="t('register.productCategory')"
                                        style="width: 50%"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    <el-tag
                                            v-for="(su,index) in getServiceUnit()"
                                            :key="'tag_'+index"
                                            type="info"
                                            class="tag"
                                    >
                                        {{su}}
                                    </el-tag>
                                </el-descriptions-item>


                                <el-descriptions-item
                                        style="width: 50%"
                                        :label="t('companyInfo.businessLicense')"
                                        label-class-name="item-label"
                                        class-name="item-content"
                                >
                                    <el-image
                                            v-if="companyInfoForm.qualificationUrl"
                                            ref="imageRef"
                                            style="width: 200px;"
                                            :src="companyInfoForm.qualificationUrl"
                                            show-progress
                                            :preview-src-list="[companyInfoForm.qualificationUrl]"
                                            fit="cover"
                                    />
                                </el-descriptions-item>
                            </el-descriptions>
                            <br/>
                            <br/>
                            <el-row>
                                <el-col style="text-align: center">
                                    <el-button type="primary" v-if="OnlyShow" @click="editPage = true">{{t('operation.edit')}}</el-button>
                                </el-col>
                            </el-row>
                        </template>
                        <el-form v-if="editPage"
                                 :model="companyInfoForm"
                                 ref="companyInfoRef"
                                 label-width="140px"
                                 label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item :label="t('register.companyName')"  prop="customerNameEn"
                                                  :rules=" [{ required: true, message: t('register.validate.companyNameEN'), trigger: ['change', 'blur'] }]">
                                        <el-autocomplete v-model="companyInfoForm.customerNameEn"
                                                         :fetch-suggestions="remoteTianyanchaCustomer"
                                                         :trigger-on-focus="false" maxlength="200" show-word-limit clearable value-key="name"
                                                         @select="handleCompanyChangeForDetail"
                                                         :placeholder="t('search')"
                                                         @clear="handleCompanyClearForDetail"></el-autocomplete>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item :label="t('register.companyNameCN')" prop="customerNameZh">
                                        <el-input maxlength="200" show-word-limit v-model="companyInfoForm.customerNameZh" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item :label="t('register.companyAddress')" prop="customerAddressEn" :rules=" [{ required: true, message: t('register.validate.companyAddress'), trigger: 'blur' }]">
                                        <el-input maxlength="300" show-word-limit v-model="companyInfoForm.customerAddressEn" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item :label="t('register.companyAddressCN')" prop="customerAddressZh">
                                        <el-input maxlength="300" show-word-limit v-model="companyInfoForm.customerAddressZh" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item :label="t('register.taxNo')"  prop="taxNo"
                                                  :rules=" [{ required: false, message: t('register.validate.taxNo'), trigger: 'blur' }]">
                                        <el-input maxlength="50" show-word-limit v-model="companyInfoForm.taxNo" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item :label="t('companyInfo.customerNo')">
                                        <el-tag type="info">{{companyInfoForm.bossNo}}</el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item :label="t('register.sgsReportNo')" prop="reportNo"
                                                  :rules=" [{message: t('register.validate.sgsReportNo'), trigger: 'blur' }]">
                                        <el-input maxlength="50" show-word-limit v-model="companyInfoForm.reportNo" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item :label="t('register.sgsContacts')" prop="contacts"
                                                  :rules="[{ type: 'email', message: t('register.validate.emailFormat'), trigger: 'blur' }]">
                                        <el-input maxlength="200" show-word-limit  v-model="companyInfoForm.contacts" clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item :label="t('register.productCategory')"
                                                  prop="serviceUnitArray"
                                                  :rules=" [{ required: true, message: t('register.validate.serviceUnits'), trigger: 'change' }]">
                                        <el-select v-model="companyInfoForm.serviceUnitArray"
                                                   multiple
                                                   collapse-tags
                                                   collapse-tags-tooltip
                                                   placeholder="  "
                                                   filterable clearable class="register-service-unit-select">
                                            <el-option
                                                    v-for="(item,index) in serviceUnitList"
                                                    :key="index"
                                                    :label="item.serviceUnitName"
                                                    :value="item.serviceUnitCode">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item  :label="t('companyInfo.businessLicense')">
                                        <el-image
                                                v-if="companyInfoForm.qualificationUrl"
                                                ref="imageRef"
                                                style="width: 200px; "
                                                :src="companyInfoForm.qualificationUrl"
                                                show-progress
                                                :preview-src-list="[companyInfoForm.qualificationUrl]"
                                                fit="cover"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col style="text-align: center">
                                    <el-button type="primary" @click="handleSubmit" v-loading="submitLoading" :disabled="submitLoading">{{t('operation.submit')}}</el-button>
                                </el-col>
                            </el-row>
                        </el-form>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>

</template>

<script setup>
import {ref,onBeforeMount,watch,computed} from 'vue'
import registerApi from '../../../api/register';
import HorizontalDivider from '@/components/HorizontalDivider/index.vue'
import CollapseCard from '@/components/CollapseCard/index.vue'
import {useI18n} from "vue-i18n";
import {getFileUrlByCloudId} from '../../../api/common';
import {ElNotification} from "element-plus";

const { t,locale } = useI18n();

const pageLoad = ref(false);
const OnlyShow = ref(true);
const editPage = ref(false);
const companyInfoRef = ref()
const companyInfoForm = ref({
    customerNameEn: '',
    customerNameZh: '',
    taxNo: '',
    customerAddressEn:'',
    customerAddressZh:'',
    id: '',
    qualificationUrl:'',
    account:{
        id:''
    }

})

const serviceUnitList = ref([]);
const loadServiceUnit = ()=>{
    serviceUnitList.value = [];
    let language = locale.value == 'en-US'?'EN':'CHI';
    registerApi.queryServiceUnit(locale.value).then(res => {
        console.log("queryServiceUnit",res)
        if (res.status == 200) {
            res.data.forEach(item => {
                let { languages } = item;
                const serviceObj = (languages || []).find(lan => lan.languageCode == language);
                if (serviceObj && serviceObj.serviceUnitName) {
                    item.serviceUnitName = serviceObj.serviceUnitName;
                }
            });
            serviceUnitList.value = res.data.filter(suit => (suit.serviceUnitCode|| '').toLowerCase() != 'other');
            console.log("queryServiceUnit",serviceUnitList.value)
        }
    })
}
const onLoad = ()=>{
    registerApi.getCompanyInfo().then(res => {
        console.log("getCompanyInfo",res)
        //companyInfoForm.value = res.data
        if (res.status == 200 && res.data.customer) {
            companyInfoForm.value = res.data.customer
            let {approveStatus} = companyInfoForm.value;
            OnlyShow.value = [1,10,80].includes(approveStatus);
            if (companyInfoForm.value.qualification.attachmentId) {
                getFileUrlByCloudId(companyInfoForm.value.qualification.attachmentId).then(res => {
                    console.log('qualificationUrl', res.data);
                    companyInfoForm.value.qualificationUrl = res.data;
                });
            }
        }
        pageLoad.value = true;
    })
}
const getServiceUnit = computed(()=>{
    return ()=>{
        let arr = []
        let su = [];
        if(companyInfoForm.value.serviceUnits){
            try{
                let formSuList = JSON.parse(companyInfoForm.value.serviceUnits);
                su = formSuList.map(item=>item.serviceUnit);
                companyInfoForm.value.serviceUnitArray = su;
            }catch (e){}
        }
        serviceUnitList.value.forEach(item => {
            let { serviceUnitCode,serviceUnitName } = item;
            if(su.includes(serviceUnitCode)){
                arr.push(serviceUnitName);
            }
        });
        return arr
    }
})
//编辑区域
const companyList = ref([]);
const remoteTianyanchaCustomer = (keyWord,cb) => {
    //console.log('query', keyWord);
    companyList.value = [];
    companyInfoForm.value.taxNo = '';
    if (!keyWord || keyWord.length<=2) {
        return;
    }
    registerApi.queryTianYanCha(keyWord).then(res => {
        let currentInput = { id: Math.random(), name: keyWord, creditCode: '' };
        if (res.status == 200 && res.data && res.data.result) {
            companyList.value = res.data.result.items || [];
            let exisKeyWord = companyList.value.find(c => c.name === keyWord);
            if (!exisKeyWord) {
                companyList.value.unshift(currentInput);
            }
        } else {
            companyList.value = [currentInput];
        }
        cb(companyList.value);
    }).catch(err => {
        //console.log('err', err);
    });
}
const handleCompanyChangeForDetail = (value) => {
    const company  = companyList.value.find(item => item.id === value.id) || {};
    companyInfoForm.value.taxNo = company.creditCode;
}

const handleCompanyClearForDetail = (value) => {
    companyInfoForm.value.taxNo = '';
}
//编辑区域结束
//保存
const mergeServiceUnits = () => {
    let productLineCodes = [];
    let serviceDomains = [];
    let serviceUnits = [];
    let setDefault  = false;
    serviceUnitList.value.forEach(item => {
        if (companyInfoForm.value.serviceUnitArray.includes(item.serviceUnitCode)) {
            let {productLineCode, serviceDomain, serviceUnitCode} = item;
            let isDefault = false;
            //默认第一个设置为default
            if(!setDefault){
                isDefault = true;
                setDefault = true;
            }
            let productLineObj = {productLineCode, isDefault};
            let serviceDomainObj = {serviceDomain, isDefault};
            let serviceUnitObj = {serviceUnit:serviceUnitCode, isDefault};

            productLineCodes.push(productLineObj);
            serviceDomains.push(serviceDomainObj);
            serviceUnits.push(serviceUnitObj);
        }
    });
    return {
        productLineCodes : JSON.stringify(productLineCodes),
        serviceDomains : JSON.stringify(serviceDomains),
        serviceUnits : JSON.stringify(serviceUnits)
    }
}
const submitLoading = ref(false)
const handleSubmit = async () => {
    try {
        await companyInfoRef.value.validate();
    }catch (e){
        return
    }
    let {productLineCodes,serviceDomains,serviceUnits } = mergeServiceUnits();
    let {account,id,customerNameEn,customerNameZh,customerAddressEn,customerAddressZh,taxNo,reportNo,contacts} = companyInfoForm.value;
    let param = {
        id:account.id,
        productLineCodes,serviceDomains,serviceUnits,
        accountCompany:{
            id,
            companyNameEn: customerNameEn,
            companyNameZh:customerNameZh,
            customerAddressEn,customerAddressZh,taxNo,reportNo,contacts
        }
    }
    submitLoading.value = true;
    registerApi.updateCompanyInfo(param).then(res=>{
        if(res.status == 200){
            ElNotification.success(t('success'))
            window.location.reload();
        }
        submitLoading.value = false;
    }).catch(err=>{
        submitLoading.value = false;
    })
}


onBeforeMount(() => {
    loadServiceUnit();
    onLoad();
})

//监听语言切换
watch(locale, (newValue, oldValue) => {
    loadServiceUnit();
})
</script>

<style lang="scss" scoped>
.smart_views_company_info_index {
  .company-detail-descriptions{
    .tag {
      margin-right: 10px;
      margin-bottom: 4px;
    }
  }

  .card-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
  }
  .sgs-box {
    margin-top: 20px;
  }

  :deep(.el-descriptions__cell){
    vertical-align: top;
  }
  :deep(.item-label) {
    vertical-align: top;
  }
  :deep(.item-content) {
    display: inline-block;
    max-width: 250px;
    word-break: break-word;
    white-space: normal;
  }

}
</style>