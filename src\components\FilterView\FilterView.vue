<template>
  <div
    class="sgs_smart_customer_template_filterView"
    id="sgs_smart_customer_template_filterView"
  >
    <filter-save
      v-if="loadFilter"
      ref="filterSave"
      :source-type="sourceType"
      :filterConfigObj="filterConfigObj"
      :tableObj="tableObj"
      :resetQueryForm="resetFilter"
      size="default"
    ></filter-save>
  </div>
</template>

<script setup>
import { ref, onMounted, provide, inject } from 'vue'
import FilterSave from '@/components/EsTable/TableHeaderFilterSave.vue'
import { filterQuery } from '@/api/search'

// 定义 props
const props = defineProps({
  sourceType: {
    type: String,
    default: 'MATERIAL',
  },
})

// 定义 emits
const emit = defineEmits(['changeFilter', 'resetFilter'])

// 注入依赖
const loadSearchForm = inject('loadSearchForm')

// 定义响应式数据
const pageLoading = ref(false)
const loadFilter = ref(false)
const filterSave = ref(null)
const filterContentObj = ref({
  filterList: [],
  currentContent: {},
  currentConfig: {},
  currentId: '',
})

const tableObj = {
  getQueryModal: () => getParentQueryModal(),
  getColumns: () => [],
}

// 定义方法
const initPage = () => {
  loadFilter.value = false
  filterQuery(props.sourceType)
    .then((res) => {
      loadFilter.value = true
      const { data } = res
      if (!data || data.length === 0) {
        return
      }
      filterContentObj.value.filterList = data
      const defaultData = data.filter((da) => da.defaultFlag - 0 === 1)
      if (!defaultData || defaultData.length === 0) {
        return
      }
      const firstDefaultData = defaultData[0]
      const { content, id } = firstDefaultData
      const filterContent = JSON.parse(content)
      filterContentObj.value.currentConfig = firstDefaultData
      filterContentObj.value.currentContent = filterContent
      filterContentObj.value.currentId = id
      emit('changeFilter', filterContentObj.value.currentContent.filter)
    })
    .catch((err) => {
      loadFilter.value = true
      console.log('filterQuery err', err)
    })
}

const getParentQueryModal = () => {
  return loadSearchForm()
}

const resetFilter = () => {
  filterContentObj.value.currentContent = {}
  filterContentObj.value.currentConfig = {}
  filterContentObj.value.currentId = ''
  emit('resetFilter')
}

const changeFilter = (rowId) => {
  const changeFilter = filterContentObj.value.filterList.find(
    (f) => f.id === rowId,
  )
  if (changeFilter) {
    const { content, id } = changeFilter
    filterContentObj.value.currentConfig = changeFilter
    filterContentObj.value.currentContent = content ? JSON.parse(content) : {}
    filterContentObj.value.currentId = id
    emit('changeFilter', filterContentObj.value.currentContent.filter)
  }
}

const addFilterConfig = (config) => {
  console.log('addFilterConfig', config)
  filterContentObj.value.filterList.push(config)
  filterContentObj.value.currentConfig = config
  filterContentObj.value.currentId = config.id
}

const delFilterConfig = (id) => {
  filterContentObj.value.filterList = filterContentObj.value.filterList.filter(
    (f) => f.id !== id,
  )
  if (filterContentObj.value.currentId === id) {
    filterContentObj.value.currentConfig = {}
    filterContentObj.value.currentId = ''
  }
}

const updFilterConfig = (config) => {
  const index = filterContentObj.value.filterList.findIndex(
    (f) => f.id === config.id,
  )
  if (index > -1) {
    filterContentObj.value.filterList.splice(index, 1, config)
    filterContentObj.value.currentConfig = config
  }
}

const updateFilterDefault = (id) => {
  filterContentObj.value.filterList.forEach((f) => {
    f.defaultFlag = f.id === id ? 1 : 0
  })
}

const filterConfigObj = {
  getFilterContentObj: () => filterContentObj.value,
  changeFilter,
  addFilterConfig,
  updateFilterDefault,
  delFilterConfig,
  updFilterConfig,
}

// 生命周期钩子
onMounted(() => {
  initPage()
})
</script>

<style lang="scss">
.sgs_smart_customer_template_filterView {
}
</style>
