<template>
    <div class="auditLogTable" v-loading="loading">
        <el-card v-if="dataList && dataList.length>0">
            <el-row >
                <el-col style="text-align: left">
                    <h3>
                        {{title}}
                    </h3>
                </el-col>
                <el-col>
                    <el-table :data="dataList" border size="mini" :max-height="380">
                        <el-table-column prop="operationBy" label="Operation User"></el-table-column>
                        <el-table-column prop="operation" label="Operation"></el-table-column>
                        <el-table-column prop="objectNo" :label="handlerObjectNoTitle"></el-table-column>
                        <el-table-column prop="originalValue" label="Original Value">
                            <template slot-scope="{row}">
                                <el-tooltip placement="top">
                                    <div slot="content">
                                        <div v-html="row.originalValue"></div>
                                    </div>
                                   <span v-html="row.originalValue"></span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column prop="newValue" label="New Value">
                            <template slot-scope="{row}">
                                <el-tooltip placement="top">
                                    <div slot="content">
                                        <div v-html="row.newValue"></div>
                                    </div>
                                    <span v-html="row.newValue"></span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column prop="operationTime" label="Operation Time">
                            <template slot-scope="{row}">
                                {{getTime(row.operationTime)}}
                            </template>
                        </el-table-column>
                        <!--
                        <el-table-column prop="createdDate" label="Created Date">
                            <template slot-scope="{row}">
                                {{getTime(row.createdDate)}}
                            </template>
                        </el-table-column>-->
                    </el-table>
                </el-col>
                <el-col>
                    <el-pagination
                            v-if="logFrom-0==0"
                            @size-change="initSmart"
                            @current-change="initSmart"
                            :current-page.sync="page.page"
                            :page-sizes="page.sizes"
                            :page-size.sync="page.rows"
                            :layout="page.layout"
                            :total="page.total"
                    >
                    </el-pagination>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
    import auditLogApi from '@/api/auditlog'
    export default {
        name: "auditLogTable",
        data() {
            return {
                loading:false,
                dataList:[],
                page:{//分页对象
                    show: true,
                    page: 1,
                    rows: 20,
                    small: true,
                    sizes: [10, 20, 50, 100],
                    layout: 'total, sizes, prev, pager, next',
                    total: 0
                }
            }
        },
        methods: {
            getTime(time){
                if(!time){
                    return time;
                }
                return tzFormChina(time);
            },
            initSmart(){
                if(!this.objectNo || !this.objectType){
                    return;
                }
                this.loading = true;
                let param = {
                    objectType:this.objectType,
                    objectNo:this.objectNo,
                    objectDate:this.objectDate,
                    rootObjectId:this.rootObjectId
                }
                let limit = this.page.rows;
                let offset = limit * (this.page.page-1);
                auditLogApi.queryAuditLogPage(limit,offset,param).then(res=>{
                    this.loading = false;
                    if(res.status==200 && res.data && res.data.data){
                        let {total,records} = res.data.data;
                        this.page.total = total;
                        this.dataList = records;
                    }else{
                        this.$notify.error(this.$t('api.error'))
                    }
                }).catch(err=>{
                    this.loading = false;
                    this.$notify.error(this.$t('api.error'))
                })
            },
            initSci(){
                let param = [];
                this.refSystemId.split(",").forEach(s=>{
                    param.push({
                        trfNo: this.objectNo,
                        refSystemId:s
                    })
                })
                auditLogApi.queryLogFromSci(param).then(res=>{
                    if(res.status==200 && res.data && res.data.result && res.data.result.length>0){
                        let {traceability} = res.data.result[0];
                        //转换为统一结构
                        let list = [];
                        traceability.forEach(t=>{
                            let {bizNo,action,createDate} = t;
                            let data = {
                                operationBy:'SCI System',
                                operation:action,
                                objectNo:bizNo,
                                operationTime:createDate
                            }
                            list.push(data);
                        })
                        this.dataList = list;
                    }
                }).catch(err=>{

                })
            },
        },
        mounted() {
        },
        created() {
            if(this.logFrom==0){
                this.initSmart();
            }
            if(this.logFrom==1){
                this.initSci();
            }
            if(this.logFrom==2){}
        },
        watch: {},
        computed: {
            handlerObjectNoTitle(){
                if(this.logFrom-0==0){
                    return this.title + ' No';
                }
                if(this.logFrom-0==1){
                    return 'TRF/Order No';
                }
                if(this.logFrom-0==2){
                    return 'Ilayer No.';
                }
                return 'Object No';
            },
        },
        props: {
            objectNo:'',
            objectType:'',
            title:'',
            logFrom:'',//0 smart,1 sci 2 localilayer
            refSystemId: '',
            objectDate:'',
            rootObjectId:''
        },
        components: {}
    }
</script>

<style scoped>
    .auditLogTable {
        padding-bottom: 10px;
    }
</style>