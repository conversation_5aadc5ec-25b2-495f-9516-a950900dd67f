<template>
    <div class="pageContainer" style="margin: 0px 140px;" v-if="showFlag">

        <!-- 工具栏 -->
        <div class="tool-bar">
            <div class="nav-bar-left">
                <div class="nav-bar-left-item" @click="toContentPage()">
                    <img src="/detailedArticle/navBacktosearch.svg" alt="">
                    <span>{{ $t('work.detailedArticle.returnToSearch') }}</span>
                </div>
            </div>

            <div class="nav-bar-right">
                <div class="nav-bar-right-item" @click="downloadPackedFile()">
                    <img src="/detailedArticle/navDownload.svg" alt="">
                    <span>{{ $t('work.detailedArticle.downloadAttachment') }}</span>
                </div>
                <div class="nav-bar-right-item" @click="toContentPage(true)">
                    <img src="/detailedArticle/navFav.svg" alt="">
                    <span>{{ $t('work.detailedArticle.myFavorites') }}</span>
                </div>
                <div class="nav-bar-right-item" @click="openContactDialog">
                    <img src="/detailedArticle/navContact.svg" alt="">
                    <span>{{ $t('work.detailedArticle.contactUs') }}</span>
                </div>
            </div>
        </div>




        <div class="content-container">

            <!-- 页面中心内容 -->
            <div class="main-content-container">
                <div class="main-content-container-inner">

                    <!-- 面包屑以及收藏状态 -->
                    <div class="breadcrumb-container">
                        <div class="crumbs">
                            <span class="breadcrumb">
                                {{ $t('work.detailedArticle.home') }}>
                            </span>
                            <span class="breadcrumb"> {{ breadcrumbs.categoryDescCn }}</span>
                        </div>



                        <span v-if="previewFlag !== 1" class="favStatus" @click="debounceCollectSwitch">
                            <img v-if="!isCollectLoading" :src=starIcon style="margin-right: 5px;">
                            <el-icon v-else class="is-loading"
                                style="font-size: 18px; margin-right: 5px; color: #F4B587;">
                                <Loading />
                            </el-icon>
                            <span :style="isFavourte ? 'color: #F4B587;' : 'color: #999999'">{{ isFavourte ?
                                $t('work.detailedArticle.alreadyFavorite') :
                                $t('work.detailedArticle.addToFavorites') }}</span>
                        </span>
                    </div>

                    <!-- if the title display more than 2 lines, then display hover bubble to show the full title -->
                    <el-tooltip :content="title" :disabled="!showTitleTooltip" placement="bottom" effect="light"
                        popper-class="custom-tooltip" :enterable="false" :offset="10" transition="el-fade-in-linear"
                        :show-after="200">
                        <div v-if="!isLaw" class="article-title" ref="titleRef" @mouseenter="checkTitleOverflow">
                            {{ title }}
                        </div>

                        <div v-if="isLaw" class="article-title" ref="titleRef" @mouseenter="checkTitleOverflow">
                            {{ standardName }}
                        </div>

                    </el-tooltip>

                    <!-- 文章标签 -->
                    <div class="tag-container">
                        <span class="article-tag" v-for="(tag, index) in tags" :key="index">
                            {{ tag.tagNameCn }}
                        </span>
                    </div>

                    <!-- 文章详情 -->
                    <div class="article-detail">
                        <div class="article-detail-left">
                            <!--  状态 -->
                            <span v-if="isLaw" class="status">{{ $t('work.detailedArticle.status') }}: {{ $t(status)
                            }}</span>
                            <span v-if="isLaw" style="color: rgba(0, 0, 0, 0.1);">|</span>
                            <!-- 发布时间 for 非法律(publishTime为空时，不显示) -->
                            <span v-if="!isLaw && publishTime !== null" class="publishTime">{{
                                $t('work.detailedArticle.publishTimeTrival') }}:
                                {{
                                    publishTime.split(' ')[0] }}</span>
                            <span v-if="!isLaw && publishTime !== null" style="color: rgba(0, 0, 0, 0.1);">|</span>
                            <!-- 创建时间 -->
                            <span v-if="!isLaw" class="time">{{ $t('work.detailedArticle.createTime') }}: {{
                                createTime.split(' ')[0] }}</span>
                            <span v-if="!isLaw" style="color: rgba(0, 0, 0, 0.1);">|</span>
                            <!-- 颁布时间 -->
                            <span v-if="isLaw && publishTime !== null" class="publishTime">{{
                                $t('work.detailedArticle.publishTime') }}: {{
                                    publishTime.split(' ')[0] }}</span>
                            <span v-if="isLaw && publishTime !== null" style="color: rgba(0, 0, 0, 0.1);">|</span>
                            <!-- 生效时间 -->
                            <span v-if="isLaw" class="validTime">{{ $t('work.detailedArticle.validTime') }}: {{
                                validTime?.split(' ')[0] }}</span>
                            <span v-if="isLaw" style="color: rgba(0, 0, 0, 0.1);">|</span>
                            <!-- 作者 -->
                            <span class="source">{{ $t('work.detailedArticle.source') }}: {{ source }}</span>
                            <span style="color: rgba(0, 0, 0, 0.1);">|</span>
                            <!-- 阅读量 -->
                            <span class="viewCount">{{ $t('work.detailedArticle.viewCount') }}: {{ viewCount }}</span>
                        </div>
                    </div>


                    <el-divider
                        style="border-width: 2px; border-color: rgba(0, 0, 0, 0.08); margin:20px 0px 0px 0px;" />

                    <div class="main-article-container">
                        <div v-if="isLaw">
                            <!-- title-En -->
                            <div class="title-en" style="margin: 10px 0px; font-size: 20px; font-weight: 400;">
                                <span>{{ knowledgeVO.ltsTitle }}</span>
                            </div>
                            <!-- title-local -->
                            <div class="title-local" style="margin: 10px 0px; font-size: 20px; font-weight: 400;">
                                <span>{{ knowledgeVO.localTitle }}</span>
                            </div>
                            <!-- keyword -->
                            <div class="keyword"
                                style="margin: 10px 0px; font-size: 20px; display: flex; justify-content: center; align-items: center;">
                                <span style="font-weight: 100; color: rgba(0, 0, 0, 0.65);">{{ keyword }}</span>
                            </div>
                            <!-- source link: 可跳转 -->
                            <div v-if="isLaw && isVip" class="source-link"
                                style="display: flex; flex-direction: row; justify-content: center; flex-wrap: wrap; gap: 10px; font-size: 19px; font-weight: 600;">
                                <a class="hover-underline" :href="sourceLink" target="item">{{ sourceLink }}</a>
                            </div>
                        </div>
                        <!-- 文章内容 -->
                        <div v-if="knowledgeVO.contentType !== 'image'" class="article-content-container">
                            <div class="article-content" v-html="mainContent"></div>
                        </div>

                        <!-- 图片内容 -->
                        <div v-if="knowledgeVO.contentType === 'image'">
                            <Carousel :carouselList="getCarouselData(knowledgeVO)"></Carousel>
                        </div>
                    </div>
                </div>


                <!-- 附件信息 -->
                <div class="display-panel" v-if="attachment.length > 0">

                    <div class="title-conatiner" style="margin: 10px 0px 10px 0px;">
                        <div class="title" id="attachment">{{ $t('work.detailedArticle.attachmentInfo') }}</div>
                    </div>

                    <div class="content" style="border: 0px; margin: 10px 0px 10px 0px;">
                        <div class="attachment" style="display: flex; flex-direction: column;">
                            <div v-for="(item, index) in attachment" :key="index"
                                style="display: flex; margin-bottom: 10px;">
                                <div style="display: flex; align-items: center; justify-content: center; margin: 0px 15px 0px 0px;"
                                    class="hover-underline">
                                    <div class="logo"
                                        style="width: 15px; height: 15px; margin: 0px 10px 5px 0px; cursor: pointer;">
                                        <img :src="getFileIcon(item.fileName)" alt="">
                                    </div>
                                    <div class="name" style="cursor: pointer; margin-right: 10px;"
                                        @click="downloadFile(item.cloudId)">{{ item.fileName }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 扩展内容 -->
                <!-- todo: 列数更改为4 -->
                <div class="display-panel" v-if="attachmentExtension.length > 0">
                    <div class="title-conatiner">
                        <div class="title" id="model">{{ $t('work.detailedArticle.extensionContent') }}</div>
                        <div class="divider"></div>
                    </div>
                    <div class="content">
                        <el-row v-if="attachmentExtensionFree.length > 0">
                            <el-col v-for="(item, index) in attachmentExtensionFree" :key="index" :span="8">
                                <div
                                    style="display: flex;flex-direction:column;justify-content:center; align-items: start; margin:4px 10px;">

                                    <ContentTooltip :content="getLocalizedName(item)" :id="`item-model-name-${index}`">
                                        <template #content>
                                            <div class="lable-item"
                                                :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">
                                                {{ getLocalizedName(item) }}
                                            </div>
                                        </template>
                                    </ContentTooltip>

                                    <a v-if="item.modelType === 'url'"
                                        style="font-size:15px; color:#F4B587; cursor:pointer; max-width: 80%; "
                                        class="hover-underline" :href="item.modelValue"> {{
                                            item.modelValue !== null && item.modelValue !== '' ?
                                                $t('work.detailedArticle.packageDownload') : ' ' }} </a>

                                    <a v-if="item.modelType === 'file'"
                                        style="font-size:15px; color:#F4B587; cursor:pointer; max-width: 80%; "
                                        class="hover-underline" @click="downloadFile(item.modelValue)">{{
                                            item.modelValue !== null && item.modelValue !== '' ?
                                                $t('work.detailedArticle.packageDownload') : ' ' }} </a>

                                    <ContentTooltip :content="item.modelValue" :id="`item-model-value-${index}`">
                                        <template #content>
                                            <span v-if="item.modelType !== 'url' && item.modelType !== 'file'"
                                                style="font-size:15px; overflow: hidden; text-wrap: nowrap; text-overflow: ellipsis; display: block;  max-width: 80%;"
                                                :id="`item-model-value-${index}`">
                                                {{ item.modelValue }}
                                            </span>
                                        </template>
                                    </ContentTooltip>

                                    <!-- <div v-if="item.modelType !== 'url' && item.modelType !== 'file'"
                                        style="font-size:18px; overflow: hidden; text-wrap: nowrap; text-overflow: ellipsis; display: block; ">
                                        {{ item.modelValue }}
                                    </div> -->
                                </div>
                            </el-col>
                        </el-row>


                        <!-- 屏蔽层 -->
                        <div v-if="!isVip" class="title" style="margin-top: 30px;">
                            <div class="tips">
                                <span style="color: #FF0000; font-size: 16px; font-weight: 600;">{{
                                    $t('work.detailedArticle.attachmentExtensionVip') }}</span>
                            </div>
                            <img src="/detailedArticle/shadowEye.svg" style="margin: 20px; width: 50px; height: 50px;"
                                alt="">
                        </div>

                        <el-row v-if="attachmentExtensionVip.length > 0 && isVip">
                            <el-col v-for="(item, index) in attachmentExtensionVip" :key="index" :span="8">
                                <div
                                    style="display: flex;flex-direction:row;justify-content:flex-start; align-items: center; margin:4px 10px;">
                                    <div style="font-size:18px; font-weight: 350; margin-right: 2px">{{
                                        item.modelNameCn }}:</div>

                                    <a v-if="item.modelType === 'url'"
                                        style="font-size:18px; color:#F4B587; cursor:pointer;  " class="hover-underline"
                                        :href="item.modelValue"> {{
                                            item.modelValue !== null && item.modelValue !== '' ?
                                                $t('work.detailedArticle.packageDownload') : ' ' }} </a>

                                    <a v-if="item.modelType === 'file'"
                                        style="font-size:18px; color:#F4B587    ; cursor:pointer;"
                                        class="hover-underline" @click="downloadFile(item.modelValue)"> {{
                                            item.modelValue !== null && item.modelValue !== '' ?
                                                $t('work.detailedArticle.packageDownload') : ' ' }} </a>

                                    <div v-if="item.modelType !== 'url' && item.modelType !== 'file'"
                                        style="font-size:18px; overflow: hidden; text-wrap: nowrap; text-overflow: ellipsis; display: block; ">
                                        {{ item.modelValue }}</div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>

                <!-- 推荐内容 -->
                <div class="display-panel" v-if="recContent.length > 0">
                    <div class="title-conatiner">
                        <div class="title" id="rec">{{ $t('work.detailedArticle.recommendedContent') }}</div>
                        <div class="divider"></div>
                    </div>

                    <div class="content" style="border: 0px; padding: 0px;">
                        <div
                            style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 4%;  align-content: center; justify-content: flex-start;">

                            <div v-for="(item, index) in recContent" :key="index" style="width: 22%; cursor: pointer; "
                                @click="rePushToDetail(item)">

                                <div style="width: 100%; height: 150px; ">
                                    <img :src="item.coverPlan" alt="CoverImg" object-fit="cover"
                                        style="aspect-ratio: 16/9; width: 100%; height: 100%;">
                                </div>

                                <ContentTooltip :content="item.title" :id="`item-rec-${index}`">
                                    <template #content>
                                        <span
                                            style="margin: 5px 0px; font-size: 20px; font-weight: 600;  overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical; "
                                            :id="`item-rec-${index}`"
                                            :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                                item.title }}</span>
                                    </template>
                                </ContentTooltip>

                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 侧边栏 -->
            <div class="sidebar-container" :style="{ top: sidebarTop + 'px' }">

                <!-- 知识推荐 -->
                <div class="sidebar-card" style="height: 260px;">
                    <div class="title-container">
                        <img class="title-icon" src="/detailedArticle/md-whatshot aEJMtVQ.svg" alt="">
                        <span class="title">{{ $t('work.detailedArticle.knowledgeRecommendation') }}</span>
                    </div>
                    <div class="knowledge-card-content">

                        <div class="item" v-for="(item, index) in hotViewKnowledges.slice(0, 5)" :key="index"
                            @click="rePushToDetail(item)" style="margin: 5px 0px;">
                            <span class="index" :style="{ color: getColor(index), marginTop: '3px' }">{{ index + 1
                                }}</span>
                            <ContentTooltip :content="item?.title" :id="`item-hotview-${index}`">
                                <template #content>
                                    <span class="title" :id="`item-hotview-${index}`"
                                        :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                            item?.title }}</span>
                                </template>
                            </ContentTooltip>
                            <!-- <div class="title">{{ item.title }}</div> -->
                        </div>
                    </div>
                </div>


                <!-- 历史版本 -->
                <div v-if="isLaw" class="sidebar-card" style="height: 250px;">
                    <div class="title-container">
                        <img class="title-icon" src="/detailedArticle/if-history 1.svg" alt=""
                            style="overflow-y:hidden;">
                        <span class="title">{{ $t('work.detailedArticle.historyVersion') }}</span>
                    </div>

                    <div class="history-card-content">
                        <div v-if="historyVersion.length > 0">
                            <div class="item" v-for="(item, index) in historyVersion" :key="index"
                                @click="rePushToDetail(item)">
                                <div class="left-part">
                                    <span class="index" :style="{ color: getColor(index) }">{{ item.index }}</span>
                                    <!-- <el-tooltip :content="item.title" placement="bottom"> -->
                                    <ContentTooltip :content="item.title" :id="`item-history-${index}`">
                                        <template #content>
                                            <span class="title" :id="`item-history-${index}`"
                                                :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                                    item.title }}</span>
                                        </template>
                                    </ContentTooltip>
                                    <!-- </el-tooltip> -->
                                </div>
                                <div class="right-part">
                                    <span class="date">{{ item.date.split(' ')[0] }}</span>
                                </div>
                            </div>
                        </div>
                        <div v-if="historyVersion.length === 0" style="display: flex; height: 180px;">
                            <img src="/detailedArticle/no-data.svg"
                                style="position: relative; top:50%; left:50%; transform: translate(-50%, -50%) ; height: 150px; width: 150px;">
                        </div>
                    </div>
                </div>

                <!-- 快速导航 -->
                <div class="sidebar-card" style="height: 250px;">
                    <div class="title-container">
                        <img class="title-icon" src="/detailedArticle/iconPark-game-two.svg" alt="">
                        <span class="title">{{ $t('work.detailedArticle.quickNavigation') }}</span>
                    </div>
                    <div class="nav-card-content">
                        <div class="nav-button" @click="backtoContent()">
                            <img class="nav-button-icon" src="/detailedArticle/ze-orders-o.svg" alt="">
                            <span class="nav-button-text">{{ $t('work.detailedArticle.contentInfo') }}</span>
                        </div>
                        <div v-if="attachment.length > 0" class="nav-button" @click="backtoAttachment()">
                            <img class="nav-button-icon" src="/detailedArticle/riLine-attachment-2.svg" alt="">
                            <span class="nav-button-text">{{ $t('work.detailedArticle.attachmentInfo') }}</span>
                        </div>
                        <div v-if="attachmentExtension.length > 0" class="nav-button" @click="backtoModel()">
                            <img class="nav-button-icon" src="/detailedArticle/iconPark-extend.svg" alt="">
                            <span class="nav-button-text">{{ $t('work.detailedArticle.extensionContent') }}</span>
                        </div>
                        <div v-if="recContent.length > 0" class="nav-button" @click="backtoRec()">
                            <img class="nav-button-icon" src="/detailedArticle/thumbup.svg" alt="">
                            <span class="nav-button-text">{{ $t('work.detailedArticle.recommendedContent') }}</span>
                        </div>

                        <div v-if="previewFlag !== 1" class="nav-button" @click="collectAdd()">
                            <img class="nav-button-icon" src="/detailedArticle/iconPark-star.svg" alt="">
                            <span class="nav-button-text">{{ $t('work.detailedArticle.addToFavorites') }}</span>
                        </div>
                        <div class="nav-button" @click="scrollToTop">
                            <img class="nav-button-icon" src="/detailedArticle/iconPark-to-top-one.svg" alt="">
                            <span class="nav-button-text">{{ $t('work.detailedArticle.backToTop') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- </el-col> -->
        </div>
    </div>

    <dialog ref="iframeDialog" class="contact-dialog">
        <button @click="closeContactDialog"
            style="position: absolute; top: 20px; right: 20px; border: none; background-color: transparent; cursor: pointer;"><img
                src="/detailedArticle/ze-cross 1.svg" style="height: 20px; width: 20px;"></button>
        <iframe v-if="showIframe" :src="iframeSrc" width="100%" height="100%" style="border: none;">
        </iframe>
    </dialog>
</template>



<script lang="ts" setup>

import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import type { Ref } from 'vue'
import { throttle } from 'lodash-es'
import { knowledgeDetailsGet } from '@/api/detailedArticle/detailedArticle'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { knowledgeFavoritesSave } from '@/api/detailedArticle/knowledgeFavoritesSave'
import { knowledgeFavoritesRemove } from '@/api/detailedArticle/knowledgeFavoritesRemove'
import { getCloudFileURL, getPackedCloudFileURL, attachmentviewcount } from '@/api/getCloudFileUrl'
import Carousel from '../Carousel/Carousel.vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router';
import { getCategoryName } from '@/api/detailedArticle/knowledgeCategoryGet'
import { Loading } from '@element-plus/icons-vue'
import { getRegulationHistory } from '@/api/detailedArticle/knowledgeRegulationHistory'
import ContentTooltip from '@/components/Tooltip/Tooltip.vue';
const showFlag = ref(false)

const userInfo = ref(JSON.parse(localStorage.getItem('SGS-userInfo') || '{}'))

//userInfo.value.isVip //0/1 空值代表0
//userInfo.value.isInternalUser  0/1 空值代表0
// import { ca } from 'element-plus/es/locales.mjs'
const router = useRouter();

//Sidebar layout control Constants

const SCROLL_THROTTLE_MS = 16 // ~60fps
const sidebarTop = ref(120)
const { t, locale } = useI18n()

// Add the missing titleRefs declaration
const titleRefs = ref<{ [key: string]: HTMLElement }>({})

//文件下载接口
interface AttachmentItem {
    fileName: string;
    downloadCount?: number;
    cloudId: string;
    [key: string]: unknown;
}

interface HistoryVersionItem {
    index: number;
    title: string;
    date: string;
    id?: string;
}

interface Regulation {
    id?: string;
    title: string;
    tag?: string[];
    url?: string;
    coverPlan?: string;
    status?: number;
    createTime?: string;
    updateTime?: string;
    viewCount?: number;
    labels?: string;
    createUser?: string;
    content?: string;
    description?: string;
    knowledgeTagEntityList?: { name: string; value: string; tagNameCn: string; }[];
    categoryId?: number;
}

// Interface for regulation history item from API
interface RegulationHistoryItem {
    id: string;
    title: string;
    createTime: string;
    [key: string]: unknown;
}

const historyVersion = ref<HistoryVersionItem[]>([]);

//content
const breadcrumbs: Ref<{ categoryDescCn: string }> = ref({ categoryDescCn: '' })
const mainContent = ref('')
const title = ref('')
const createTime = ref('')
const source = ref('')
const status = computed(() => {
    if (!isLaw.value) return ''
    const statusItem = attachmentExtension.value.find(item =>
        item.modelCode === 'status' && item.modelValue
    )
    if (statusItem?.modelValue === 'Active') {
        // return t('work.detailedArticle.statusActive')
        return 'Active'
    } else if (statusItem?.modelValue === 'Inactive') {
        // return t('work.detailedArticle.statusInactive')
        return 'Inactive'
    }
    return ''
})
const validTime = ref('')
const publishTime = ref('')
const viewCount = ref(0)
const isFavourte = ref(false)
const starIcon = ref('/detailedArticle/ze-star.svg')
const isCollectLoading = ref()
const recContent: Ref<{ coverPlan: string, title: string, description: string }[]> = ref([])
const isLaw = ref(false)
const isVip = ref(false)
const isInnerUser = ref(false)
const keyword = ref<string>()
const standardName = ref<string>()
const sourceLink = ref<string>()
const previewFlag = ref<number>(0)
const knowledgeVO = ref({
    ltsTitle: '',
    contentType: '',
    id: '',
    title: '',
    localTitle: '',
    knowledgeExtList: [] as Array<{ extCover: string, extAbstract: string, authorization: number }>,
    knowledgeAttachmentList: [] as Array<{ cloudId: string, id: string }>,
    categoryId: ''
})

const tags = ref([
    {
        categoryId: null,
        createDept: null,
        createTime: null,
        createUser: null,
        id: null,
        isDeleted: 0,
        sortKey: null,
        status: "",
        tagCode: null,
        tagDescCn: null,
        tagDescEn: null,
        tagNameCn: null,
        tagNameEn: null,
        tenantId: null,
        updateTime: null,
        updateUser: null,
        version: 0
    }])


const attachment: Ref<AttachmentItem[]> = ref([])
const attachmentExtension: Ref<{ modelNameCn: string, modelNameEn: string, modelType: string, modelValue: string, modelCode: string, status: string }[]> = ref([])

const attachmentExtensionFree: Ref<{ modelNameCn: string, modelNameEn: string, modelType: string, modelValue: string }[]> = ref([])
const attachmentExtensionVip: Ref<{ modelNameCn: string, modelNameEn: string, modelType: string, modelValue: string }[]> = ref([])

const titleRef = ref<HTMLDivElement | null>(null)
const showTitleTooltip = ref(false)
const showIframe = ref(false)
const iframeSrc = ref("https://forms.office.com/pages/responsepage.aspx?id=K6dv6dbIV0mWvcf43DDMiPZCsk849ixCvbyG7FqBI9RUMkhaRUE1MFpKWUhSU0tLUTRPMkFNQTBMTy4u&route=shorturl");

const handleScroll = throttle(() => {
    //获取main-content-container的高度，使sidebar的 top 与 main-content-container的 高度 一致
    const toolBar = document.querySelector('.tool-bar')
    if (!toolBar) return

    const toolBarRect = toolBar.getBoundingClientRect()
    console.log(toolBarRect)
    if (toolBarRect.top <= 44) {
        sidebarTop.value = 80
    } else {
        sidebarTop.value = toolBarRect.top + 40;
    }
}, SCROLL_THROTTLE_MS)

onMounted(() => {
    //侧边栏位置控制
    window.addEventListener('scroll', handleScroll)

    //页面跳转信息
    const route = useRoute();
    const knowledgeId = route.query.id;
    const categoryId = route.query.categoryId;
    previewFlag.value = Number(route.query.previewFlag || 0);
    getCategoryName({ categoryId: categoryId }).then((res) => {
        breadcrumbs.value = res.data;
    })



    //获取url上的id(不一定)参数
    //res.data.content <h1>123</hi>

    //页面信息获取 id:knowledgeId   
    knowledgeDetailsGet({ id: knowledgeId }).then((res) => {

        // 判断是否为法律法规
        if (res.data.knowledgeVO.categoryId === 1) {
            isLaw.value = true;
        }

        knowledgeVO.value = res.data.knowledgeVO;
        console.log(res.data.knowledgeVO.content)
        try {
            mainContent.value = decodeURIComponent(res.data.knowledgeVO.content);
        } catch (e) {
            mainContent.value = res.data.knowledgeVO.content
        }

        hotViewKnowledges.value = res.data.hotViewKnowledges;
        title.value = res.data.knowledgeVO.title;
        createTime.value = res.data.knowledgeVO.createTime;
        source.value = res.data.knowledgeVO.author;
        tags.value = res.data.knowledgeVO.knowledgeTagEntityList;
        isFavourte.value = res.data.knowledgeVO.isFavorite;
        attachment.value = res.data.knowledgeVO.knowledgeAttachmentList;
        attachmentExtension.value = attachmentExtensionFilter(res.data.knowledgeVO.knowledgeModelRelationList);
        recContent.value = res.data.recknowledgeList;
        validTime.value = res.data.knowledgeVO.approveTime;
        publishTime.value = res.data.knowledgeVO.publishTime;
        viewCount.value = res.data.knowledgeVO.viewCount;


        checkIdentity()
        //根据是否有权限对附件扩展进行处理
        attachmentExtension.value.forEach((item) => {
            if (item.modelValue === "暂无权限") {
                //do nothing
            } else {
                attachmentExtensionFree.value.push(item)
            }

        })






        // 收藏图标控制
        if (isFavourte.value) {
            starIcon.value = '/knowledge/detailedArticle/star-selected.svg'
        } else {
            starIcon.value = '/knowledge/detailedArticle/ze-star.svg'
        }

        // Check title overflow after title is loaded
        setTimeout(checkTitleOverflow, 100)


        if (isLaw.value) {
            // 获取法规历史版本
            fetchRegulationHistory()
        }
        showFlag.value = true
    })



    //收藏图标控制
    if (isFavourte.value) {
        starIcon.value = '/knowledge/detailedArticle/star-selected.svg'
    } else {
        starIcon.value = '/knowledge/detailedArticle/ze-star.svg'
    }


})

const attachmentExtensionFilter = (attachmentExtension: Array<{ modelNameCn: string, modelNameEn: string, modelType: string, modelValue: string, modelCode: string, status: string, viewFlag: number }>) => {

    // 获取唯一的keywords
    keyword.value = attachmentExtension.find(item => item.modelCode === 'keywords')?.modelValue


    // 获取唯一的standardName 
    standardName.value = attachmentExtension.find(item => item.modelCode === 'standardName')?.modelValue


    // 获取唯一的sourceLink 
    sourceLink.value = attachmentExtension.find(item => item.modelCode === 'sourceLink')?.modelValue

    // 获取唯一的promulgationDate 如果存在的话对格式进行处理 仅保留年月日
    attachmentExtension.forEach(item => {
        if (item.modelType === 'dateRange') {
            item.modelValue = item.modelValue.split('T')[0]
        }
    })

    // 过滤掉keywords、standardName、sourceLink、promulgationDate，并且viewFlag为1的附件扩展
    let filtered = [...attachmentExtension];
    if (isLaw.value) {
        filtered = filtered.filter(item => !['keywords', 'standardName', 'sourceLink'].includes(item.modelCode)).filter(item => item.viewFlag === 1)

    }
    return filtered;
}

const checkIdentity = () => {
    //获取当前用户,mock信息
    console.log("userInfo.value====>", userInfo.value.content.isInternalUser)

    if (userInfo.value.content.isInternalUser == 1) {
        isVip.value = true
        isInnerUser.value = true
        return
    }
    if (userInfo.value.content.isVip == 1) {
        isVip.value = true
        isInnerUser.value = false
        return
    }
}

onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScroll)
    // Clean up throttled function
    handleScroll.cancel()
})

// const language = ref('Chinese')
// const languageOptions = [
//     {
//         value: 'Chinese',
//         label: 'Chinese'
//     },
//     {
//         value: 'English',
//         label: 'English'
//     }
// ]

const hotViewKnowledges = ref([
    {
        "id": "",
        "createUser": "",
        "createDept": null,
        "createTime": "",
        "updateUser": "",
        "updateTime": "",
        "status": 0,
        "isDeleted": 0,
        "tenantId": null,
        "title": "",
        "description": "",
        "author": "",
        "editor": "",
        "categoryId": 0,
        "contentType": "",
        "coverPlan": "",
        "content": "",
        "paymentFlag": "0",
        "viewCount": 0,
        "favorCount": 0,
        "publishFlag": null,
        "publishTime": null,
        "approveFlag": "0",
        "approveUser": null,
        "approveTime": null
    }])



const getColor = (index: number) => {
    const colors = ["red", "orange", "green"];
    return index < 3 ? colors[index + 1] : 'black';
}

const rePushToDetail = (item: Regulation) => {
    console.log(item);

    router.push({ name: 'detailedArticle', query: { id: item.id, categoryId: item.categoryId } });

    //refresh
    knowledgeDetailsGet({ id: item.id, categoryId: item.categoryId }).then((res) => {
        knowledgeVO.value = res.data.knowledgeVO;
        try {
            mainContent.value = decodeURIComponent(res.data.knowledgeVO.content);
        } catch (e) {
            mainContent.value = res.data.knowledgeVO.content;
        }
        hotViewKnowledges.value = res.data.hotViewKnowledges;
        title.value = res.data.knowledgeVO.title;
        createTime.value = res.data.knowledgeVO.createTime;
        source.value = res.data.knowledgeVO.author;
        tags.value = res.data.knowledgeVO.knowledgeTagEntityList;
        isFavourte.value = res.data.knowledgeVO.isFavorite;
        attachment.value = res.data.knowledgeVO.knowledgeAttachmentList;
        attachmentExtension.value = attachmentExtensionFilter(res.data.knowledgeVO.knowledgeModelRelationList);
        recContent.value = res.data.recknowledgeList;
        validTime.value = res.data.knowledgeVO.approveTime;
        publishTime.value = res.data.knowledgeVO.publishTime;
        viewCount.value = res.data.knowledgeVO.viewCount;

        // Update status and other fields
        if (res.data.knowledgeVO.categoryId === 1) {
            isLaw.value = true;
            fetchRegulationHistory();
        } else {
            isLaw.value = false;
        }

        // Update star icon
        if (isFavourte.value) {
            starIcon.value = '/detailedArticle/star-selected.svg';
        } else {
            starIcon.value = '/detailedArticle/ze-star.svg';
        }

        checkIdentity()
        //根据是否有权限对附件扩展进行处理
        attachmentExtension.value = attachmentExtensionFilter(res.data.knowledgeVO.knowledgeModelRelationList);

        // 收藏图标控制
        if (isFavourte.value) {
            starIcon.value = '/detailedArticle/star-selected.svg'
        } else {
            starIcon.value = '/detailedArticle/ze-star.svg'
        }


        // Check title overflow after title is loaded
        setTimeout(checkTitleOverflow, 100);

        if (isLaw.value) {
            // 获取法规历史版本
            fetchRegulationHistory();
        }
        showFlag.value = true
    });
}

const iframeDialog = ref<HTMLDialogElement | null>(null);
const openContactDialog = () => {
    console.log("openContactDialog==>", iframeDialog)
    if (iframeDialog.value) {
        showIframe.value = true;
        iframeDialog.value.showModal();
    } else {
        console.error("iframeDialog is null");
    }
}

const closeContactDialog = () => {
    if (iframeDialog.value) {
        iframeDialog.value.close();
        setTimeout(() => {
            showIframe.value = false;
        }, 300);
    } else {
        console.error("iframeDialog is null");
    }
};

const collectSwitch = async () => {


    //没有收藏=>收藏
    if (!isFavourte.value) {
        try {
            await knowledgeFavoritesSave({ knowledgeId: knowledgeVO.value.id });
            isFavourte.value = true;
            ElMessage.success(t('work.detailedArticle.addFavoriteSuccess'));
            starIcon.value = "/knowledge/detailedArticle/star-selected.svg";
        } catch (error) {
            console.error(error);
        }

        //已经收藏=>取消收藏
    } else {
        try {
            await knowledgeFavoritesRemove({ id: knowledgeVO.value.id });
            isFavourte.value = false;
            ElMessage.success(t('work.detailedArticle.cancelFavoriteSuccess'));
            starIcon.value = "/knowledge/detailedArticle/ze-star.svg";
        } catch (error) {
            console.error(error);
        }
    }

}

const debounceCollectSwitch = throttle(collectSwitch, 400)

const collectAdd = async () => {
    if (isCollectLoading.value) {
        ElMessage.warning(t('work.detailedArticle.pleaseDoNotOperateFreely'))
        return
    }

    if (isFavourte.value) {
        ElMessage.warning(t('work.detailedArticle.alreadyFavorite'));
        return;
    }

    isCollectLoading.value = true
    try {
        await knowledgeFavoritesSave({ knowledgeId: knowledgeVO.value.id });
        isFavourte.value = true;
        ElMessage.success(t('work.detailedArticle.addFavoriteSuccess'));
        starIcon.value = "/knowledge/detailedArticle/star-selected.svg";
    } catch (error) {
        console.error(error);
    } finally {
        isCollectLoading.value = false
    }
}

const toContentPage = (isFav: boolean = false) => {
    if (isFav) {
        router.push({
            name: 'favPage',
            query: {},
        })
    } else {
        router.push({
            name: 'contentPage',
            query: { query: '', isFav: 'false' }
        });
    }
}

// const toArticlePage = () => {

// }

const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}


const getCarouselData = (knowledgeData: { knowledgeExtList: Array<{ extCover: string, extAbstract: string, authorization: number }> }) => {
    const CarouselList = knowledgeData.knowledgeExtList.filter((CarouselItem) => {
        if (CarouselItem.authorization === 0) {
            return false
        }
        return true
    }).map((CarouselItem) => {
        return {
            url: CarouselItem.extCover,
            description: CarouselItem.extAbstract
        }
    })
    return CarouselList;
}

const backtoContent = () => {
    //get the height of el-divider and scroll to it
    const elDivider = document.querySelector('.el-divider');
    if (elDivider) {
        elDivider.scrollIntoView({ behavior: 'smooth' });
    }

}

const backtoAttachment = () => {
    const attachment = document.querySelector('#attachment')
    if (attachment) {
        attachment.scrollIntoView({ behavior: 'smooth' });
    }
}

const backtoModel = () => {
    const model = document.querySelector('#model')
    if (model) {
        model.scrollIntoView({ behavior: 'smooth' });
    }
}

const backtoRec = () => {
    const rec = document.querySelector('#rec')
    if (rec) {
        rec.scrollIntoView({ behavior: 'smooth' });
    }
}

// 文件下载函数
const downloadFile = async (cloudId: string) => {
    try {
        getCloudFileURL({ cloudID: cloudId, systemID: 1, networkType: 2 }).then((res: unknown) => {
            const ids: object[] = [{ id: knowledgeVO.value.knowledgeAttachmentList.find((item) => item.cloudId === cloudId)?.id }]
            console.log("ids=>", ids)
            attachmentviewcount(ids).then((res) => {
                if (res.data.code === 200) {
                    console.log("file download count=>", res);
                } else {
                    console.error("file download count error=>", res);
                }
            })
            window.open(res as string)
        })
    } catch (error) {
        console.error('Download file error:', error);
    }
}

const downloadPackedFile = async () => {

    const cloudIDs: string[] = knowledgeVO.value.knowledgeAttachmentList.map((item) => item.cloudId).filter((id) => typeof id === "string" && id.trim() !== "");
    console.log("knowledgeVO=>", knowledgeVO.value.knowledgeAttachmentList)
    if (cloudIDs.length === 0) {
        ElMessage.warning(t('work.detailedArticle.noAttachment'));
        return;
    }
    try {
        getPackedCloudFileURL({ cloudIDs: cloudIDs, systemId: "2" }).then((res: { data: Blob }) => {
            const ids: object[] = knowledgeVO.value.knowledgeAttachmentList.map((item) => { return { id: item.id } });
            console.log("ids=>", ids, "cloudids=>", cloudIDs)
            attachmentviewcount(ids).then((res) => {
                if (res.data.code === 200) {
                    console.log("file download count=>", res);
                } else {
                    console.error("file download count error=>", res);
                }
            })
            // Create a blob from the response data
            const blob = new Blob([res.data], { type: 'application/zip' });
            const url = window.URL.createObjectURL(blob);
            window.open(url);
        })

    } catch (error) {
        console.error('Download file error:', error);
    }
}

const getFileIcon = (filename: string) => {
    const fileType = filename.split(".").pop()?.toLowerCase();
    switch (fileType) {
        case "docx": return "/knowledge/knowledgeBaseHome/worddocicon.svg"
        case "doc": return "/knowledge/knowledgeBaseHome/worddocicon.svg"
        case "pdf": return "/knowledge/knowledgeBaseHome/pdfdocicon.svg"
        case "pptx": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg"
        case "ppt": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg"
        case "xls": return "/knowledge/knowledgeBaseHome/exceldocicon.svg"
        case "xlsx": return "/knowledge/knowledgeBaseHome/exceldocicon.svg"
        case "txt": return "/knowledge/knowledgeBaseHome/txtdocicon.svg"
        case "md": return "/knowledge/knowledgeBaseHome/xmldocicon.svg"
        case "png": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "jpg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "jpeg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "gif": return "/knowledge/knowledgeBaseHome/videodocicon.svg"
        case "bmp": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "ico": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "zip": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "rar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "7z": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "tar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "gz": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "bz2": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "iso": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "dmg": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        default: return "/knowledge/knowledgeBaseHome/generaldocicon.svg"
    }
}

const checkTitleOverflow = () => {
    if (titleRef.value) {
        const lineHeight = parseInt(window.getComputedStyle(titleRef.value).lineHeight)
        const titleHeight = titleRef.value.offsetHeight
        // Check if title has more than 2 lines
        showTitleTooltip.value = titleHeight > lineHeight * 2
    }
}

// Watch for title changes
watch(title, () => {
    // Check title overflow after title changes
    setTimeout(checkTitleOverflow, 100)
})

/**
 * Fetch regulation history when the component is a law type
 */
const fetchRegulationHistory = async () => {
    if (!isLaw.value || !knowledgeVO.value.id) return;

    try {
        const response = await getRegulationHistory(knowledgeVO.value.id);
        if (response.data && Array.isArray(response.data)) {
            // Convert API response to HistoryVersionItem array
            historyVersion.value = response.data.map((item: RegulationHistoryItem, index) => ({
                index: index + 1,
                title: item.title || 'N/A',
                date: item.createTime || 'N/A',
                id: item.id // Ensure we capture the id for navigation
            }));
        }
    } catch (error) {
        console.error('Error fetching regulation history:', error);
    }
}

// Add the computed property for localized name
const getLocalizedName = (item: { modelNameCn: string, modelNameEn: string }) => {
    return locale.value === 'zh-CN' ? item.modelNameCn : item.modelNameEn
}

</script>

<style lang="scss">
.contact-dialog {
    width: 800px;
    height: 600px;
    overflow: initial;
    border: none;
    border-radius: 25px;
    padding: 0;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    button {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    iframe {
        width: 100%;
        height: 100%;
        border-radius: 25px;
    }
}

.tool-bar {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nav-bar-left {
        display: flex;
        align-items: center;
        justify-content: center;

        .nav-bar-left-item {
            font-size: 14px;
            color: #6C6C6C;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: 12px;
                height: 12px;
            }

            &:hover {
                background-color: rgba(0, 0, 0, 0.1);
                cursor: pointer;
                border-radius: 5px;
                text-decoration: underline;
            }
        }


    }

    .nav-bar-right {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;

        .nav-bar-right-item {
            display: flex;
            align-items: center;
            justify-content: center;

            span {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.8);
            }

            img {
                width: 12px;
                height: 12px;
                margin-right: 5px;
            }

            &:hover {
                background-color: rgba(0, 0, 0, 0.1);
                cursor: pointer;
                border-radius: 5px;
                text-decoration: underline;
            }
        }
    }
}

.content-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    overflow-y: visible;
    position: relative;
    overflow-x: auto;


    .main-content-container {
        width: 69%;

        .main-content-container-inner {
            width: 100%;
            background-color: #FFFFFF;
            padding: 15px 20px;
            margin: 0px 10px 20px 0px;

            .breadcrumb-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;

                .crumbs {
                    .breadcrumb {
                        font-size: 16px;
                        color: rgba(0, 0, 0, 0.4);
                        margin: 10px 0px;
                    }
                }



                .favStatus {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 18px;
                        height: 18px;
                        cursor: pointer;
                    }

                    span {
                        font-size: 13px;
                        color: rgba(0, 0, 0, 0.4);
                        margin: 10px 0px;
                    }
                }
            }

            .article-title {
                font-size: 32px;
                margin: 10px 10px 0px 0px;
                font-weight: 800;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.3;
            }

            .tag-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;
                margin: 10px 0px 10px 0px;

                .article-tag {
                    border-radius: 5px;
                    margin: 0px 15px 0px 0px;
                    padding: 3px;
                    height: 25px;
                    background-color: #F4B587;
                    color: #FFFFFF;
                    font-size: 16px;
                }
            }

            .article-detail {
                font-size: 17px;
                display: flex;
                flex-direction: row;
                color: rgba(0, 0, 0, 0.4);
                margin-bottom: 3px;
                justify-content: space-between;

                .article-detail-left {
                    span {
                        margin-right: 20px;
                    }
                }

            }

            .main-article-container {
                .unauthorization {
                    display: none;
                }

                div {
                    margin: 10px 0px;
                    display: flex;
                    flex-direction: column;
                    //内容超出添加滑动条
                    white-space: pre-wrap;
                }
            }
        }



        .display-panel {
            width: 100%;
            background-color: #FFFFFF;
            padding: 15px 20px;
            margin: 0px 10px 20px 0px;



            .title-conatiner {
                display: flex;
                align-items: center;
                margin-bottom: 10px;

                .title {
                    font-size: 20px;
                    font-weight: 600;
                }
            }

            .content {
                margin: 10px 10px;

                .attachment {
                    display: flex;
                    flex-direction: row;
                }
            }
        }
    }


    .sidebar-container {
        position: fixed;

        right: 100px;

        width: 25%;
        height: 100vh;
        margin-right: 40px;


        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        .sidebar-card {
            padding: 15px;
            margin-bottom: 20px;
            background-color: #FFFFFF;

            .title-container {
                margin-bottom: 6px;

                .title-icon {
                    margin-right: 7px;
                }

                .title {
                    font-size: 17px;
                    font-weight: 500;
                    background-color: #FFFFFF;
                    margin-bottom: 0px;
                }

                padding-bottom: 10px;
                border-bottom: 2px solid rgba(0, 0, 0, 0.07);
                display: flex;
                align-self: center;

            }


            .knowledge-card-content {
                display: flex;
                flex-direction: column;
                user-select: none;
                overflow-y: hidden;
                height: 220px;

                .item {
                    display: flex;
                    flex-direction: row;
                    margin: 6px 0px;
                    padding-bottom: 10px;

                    .index {
                        font-size: 14px;
                        font-weight: bold;
                        font-family: '微软雅黑';
                        margin-right: 10px;
                        cursor: pointer;
                    }

                    .title {
                        font-size: 14px;
                        font-family: '微软雅黑';
                        cursor: pointer;
                        overflow: hidden;
                        width: 100%;
                        text-overflow: ellipsis;
                        display: block;
                        white-space: nowrap;
                    }

                    &:hover {
                        color: rgba(255, 102, 0, 0.7);
                        text-decoration: underline;
                    }
                }
            }

            .history-card-content {
                display: flex;
                flex-direction: column;
                user-select: none;

                height: 190px;

                div {
                    .item {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: space-between;
                        margin: 6px 0px;
                        padding-bottom: 10px;

                        .left-part {
                            display: flex;
                            flex-direction: row;
                            align-items: center;

                            .index {
                                font-size: 14px;
                                font-weight: bold;
                                font-family: '微软雅黑';
                                margin-right: 10px;
                                cursor: pointer;
                            }

                            .title {
                                font-size: 14px;
                                font-family: '微软雅黑';
                                cursor: pointer;
                                display: inline-block;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                max-width: 200px;
                            }
                        }

                        .right-part {
                            font-size: 14px;
                            font-family: '微软雅黑';
                            color: rgba(0, 0, 0, 0.4);
                            cursor: pointer;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            text-decoration: none;


                        }

                        &:hover,
                        &:hover .right-part,
                        &:hover .right-part .date {
                            color: rgba(255, 102, 0, 0.7);
                            text-decoration: underline;
                        }

                    }
                }

            }

            .nav-card-content {
                display: flex;
                flex-direction: column;
                user-select: none;
                overflow-x: hidden;
                overflow-y: auto;
                height: 85%;

                .nav-button {
                    margin: 6px 0px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    width: 100%;
                    height: 40px;
                    margin: 5px 10px;

                    .nav-button-icon {
                        width: 20px;
                        height: 20px;
                        margin-right: 10px;
                    }

                    .nav-button-text {
                        font-size: 14px;
                        font-family: '微软雅黑';
                        font-weight: bold;
                        color: rgba(0, 0, 0, 0.8);
                    }

                    &:hover {
                        color: rgba(255, 102, 0, 0.7);
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}




.hover-underline:hover {
    text-decoration: underline;
    color: #87c1f4;
}

.attachment .hover-underline:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.source-link {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin: 10px 0px;
}

.source-link a {
    font-size: 18px;
    text-decoration: none;
    transition: color 0.2s ease;
    display: inline-block;
}

.source-link a:hover {
    color: #f79955;
    text-decoration: underline;
}

/* Custom tooltip styling */
.custom-tooltip {
    max-width: 400px !important;
    font-size: 16px !important;
    padding: 12px 16px !important;
    line-height: 1.5 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    font-weight: 500 !important;
    color: #333 !important;
    background-color: #fff !important;
    border: 1px solid #eaeaea !important;
}

.article-content-container {

    img {
        position: relative;
        max-width: 100%;
        width: 100%;
        height: auto;
    }
}
</style>