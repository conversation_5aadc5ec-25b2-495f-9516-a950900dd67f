<template>
    <div class='sgs_wrap'>
        <div class='container-fluid'>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-sm-6">
                    <h2 style="margin-top:0">{{$t('dataAnalytics.chemicalTestPerformance')}}</h2>
                </div>
              <div class="col-sm-3 text-right" v-if="false">
                <el-select v-model="productLineCode" clearable filterable
                           @change="productLineChange" style="width: 30%;">
                  <el-option v-for="(productLine,index) in productLineCodes"
                             :label="productLine.key"
                             :value="productLine.value"></el-option>
                </el-select>
              </div>
                <div class="col-sm-6 text-right">
                    <el-date-picker
                        style="float: right; margin-top: -15px; width: 272px;"
                        class="time-select"
                        @change="rangeChange"
                        @focus="handleDateFocus"
                        v-model="valueData"
                        type="monthrange"
                        align="right"
                        unlink-panels
                        range-separator="-"
                        :start-placeholder="$t('datePicker.startTime')"
                        :end-placeholder="$t('datePicker.endTime')"
                        value-format="yyyy-MM-dd"
                        :picker-options="pickerOptionsMonth">
                    </el-date-picker>
                </div>
            </div>
            <div class="row" style="margin-top: 10px">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins" style="padding-bottom: 0px;">
                        <div id="chemicalPerformanceDiv" class="ibox-content" style="padding-bottom: 0px;">
                            <!-- <div class="row">
                                <div class="col-sm-12">
                                    <SliderDate :value-data="valueData" @input="rangeChange"/>
                                </div>
                            </div> -->
                            <div class="row">
                                <div class="col-lg-12">
                                    <div style="overflow-y:auto;margin-bottom: 0px;margin-top: 15px;">
                                        <div id="bar" style="height:500px;" class="col-lg-12">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-12">
                                    <h4>{{$t('dataAnalytics.chemicalTestPerformanceByCountry')}}</h4>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12" v-for="item in countryArray" :key="item">
                                    <div style="overflow-y:auto;margin-bottom: 20px;margin-top: 15px;">
                                        <div :id="item+'Bar'" style="height:500px;" class="col-lg-12">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <supplier-drawer :visible.sync="visible" :key="visible" :title="this.$t('dataAnalytics.supplier.title')"
                                 :optionData="supplierData"></supplier-drawer>
            </div>
        </div>
    </div>
</template>
<script>
    import * as utils from '@/views/util'
    import moment from 'moment'
    import {selectCountTestPerformance, getSupplierByTestLine} from '@/api/statistics/cistatistics'
    import {mapGetters} from 'vuex'
    import {saveLog} from "@/api/common/index";
    import _ from 'lodash';// 导入loadsh
    Vue.prototype.$lodash = _;//注入工具
    import i18n from '@/lang'


    export default {
        data() {
            let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD') //moment().format('YYYY-MM-DD');
            return {
                valueData: [startDate, endDate],
                visible: false,
                supplierData: {
                    yAxis: {
                        type: 'category',
                        data: [],
                        axisLabel: {
                            interval: 0,
                            formatter: function (name) {
                                if (!name) return ''
                                if (name.length > 20) {
                                    name = name.slice(0, 20) + '...'
                                }
                                return name
                            }
                        },
                        triggerEvent: true
                    },
                    series: null
                },
                countryArray: [],
                statisticsData: [],
                statisticsDataCountry: [],
                queryParam: {
                    startDate: startDate,
                    endDate: endDate,
                    categoryName: 'Chemical',
                    buyerGroupCode: null,
                    productLineCode:null
                },
                pickerOptionsMonth: {
                    shortcuts: [{
                    text: this.$t('datePicker.lastWeek'),
                    onClick(picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                    }, {
                    text: this.$t("datePicker.lastMonth"),
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      start.setMonth(start.getMonth() - 1);
                      picker.$emit('pick', [start, end]);
                    }
                    }, 
                    // {
                    //   text: '今年至今',
                    //   onClick(picker) {
                    //     const end = new Date();
                    //     const start = new Date(new Date().getFullYear(), 0);
                    //     picker.$emit('pick', [start, end]);
                    //   }
                    // }, 
                    {
                    text: this.$t("datePicker.lastHalfYear"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        picker.$emit('pick', [start, end]);
                    }
                    }, {
                    text: this.$t("datePicker.lastYear"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 12);
                        picker.$emit('pick', [start, end]);
                    }
                    }]
                },
                productLineCode:"",
                productLineCodes:[
                  {key:'SL',value:'SL'},
                  {key:'HL',value:'HL'}
                ]
            }
        },
        beforeRouteEnter(to, from, next) {
            next(async vm => {
                await vm.initData()
                vm.saveLog();
            })
        },
        components: {
            SliderDate: resolve => require(['@/components/statistics/SliderDate'], resolve),
            SupplierDrawer: resolve => require(['@/components/statistics/SupplierDrawer'], resolve)
        },
        computed: {
            ...mapGetters({
                companyType: 'companyType',
                user: 'userInfo',
                language: 'language'
            })
        },
        watch: {
            "$i18n.locale": function () {
                var startDate = this.getCurrentMonthFirst(this.valueData[0]);
                var endDate = this.getCurrentMonthLast(this.valueData[1]);
                this.$set(this.queryParam, 'startDate', startDate);
                this.$set(this.queryParam, 'endDate', endDate);
                this.initData();
                this.initEchart()
            },
        },
        methods: {
            handleDateFocus(e) {
                let nodes = document.querySelectorAll('.el-picker-panel__shortcut')
                let lang = ["datePicker.lastWeek", "datePicker.lastMonth", "datePicker.lastHalfYear", "datePicker.lastYear"]
                nodes.forEach((item, index) => {
                    item.innerText = this.$t(lang[index%4])
                })
            },
            /**
             * 构建成功失败横向柱状图
             */
            initTestPerformanceOption(titleText, yData, yFailValue, yPassValue, ySeeValue, bWidth) {
                return utils.initPerformanceOption(titleText, yData, yFailValue, yPassValue, ySeeValue, bWidth, 11, i18n)
            },
            saveLog() {
                saveLog("chemicalLog").then(res => {

                })
            },
            async rangeChange(data) {
                let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                    endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD')
                if(data){
                  startDate = moment(data[0]).format("YYYY-MM-DD")
                  endDate = moment(data[1]).format("YYYY-MM-DD")
                }
                this.$set(this.queryParam, 'startDate', startDate);
                this.$set(this.queryParam, 'endDate', endDate);
                this.valueData = data
                await this.initData();
                /* this.$set(this.queryParam, 'startDate', data[0]+"-01")
                 this.$set(this.queryParam, 'endDate', data[1]+"-31")
                 this.valueData = data
                 await this.initData()*/
            },
            getCurrentMonthFirst(date) {
                var startdate = new Date(date);
                startdate.setDate(1);
                return moment(startdate).format("YYYY-MM-DD");
            },
            //获取所选月份的最后一天
            getCurrentMonthLast(date) {
                var endDate = new Date(date);
                var month = endDate.getMonth();
                var nextMonth = month;
                var nextMonthFirstDay = new Date(endDate.getFullYear(), nextMonth, 1);
                var oneDay = 1000 * 60 * 60 * 24;
                var newEndDate = new Date(nextMonthFirstDay - oneDay);
                return moment(newEndDate).format("YYYY-MM-DD");
            },
            async initData() {
                if (!this.$lodash.isEmpty(this.$route.query.customerGroupCode)) {
                    this.queryParam.buyerGroupCode = this.$route.query.customerGroupCode
                }

                if (this.companyType === 5) {
                    this.queryParam.agentGroupCode = this.user.customer.customerGroupCode
                    this.queryParam.customerUsage = 'agent'
                }

                if (this.companyType === 1) {
                    this.queryParam.buyerGroupCode = this.user.customer.customerGroupCode
                    this.queryParam.customerUsage = 'buyer'
                }
                this.queryParam.buyerGroupCode = this.user.customerGroupCode;
                this.queryParam.language = this.language;
              let productLineCode= this.user.productLineCode;
              if(productLineCode==='all'){
                productLineCode='';
              }
                this.queryParam.productLineCode = productLineCode;
                let loadData = async () => {
                    //let {data} = await selectCountTestPerformance(this.queryParam)
                    selectCountTestPerformance(this.queryParam).then(res => {
                        this.statisticsData = res.data.data.all;
                        this.statisticsDataCountry = res.data.data.country
                        this.initEchart()
                    });
                };
                await loadData()
                this.$nextTick(() => {
                    this.initEchart()
                })
            },
            initEchart() {
                var yData = []
                var countryArray = []
                const self = this
                this.$lodash.forEach(this.statisticsData, item => {
                    if (item.TestLineName && yData.indexOf(item.TestLineName) == -1) {
                        yData.push(item.TestLineName)
                    }
                })

                var yPassValue = []
                var yFailValue = []
                var ySeeValue = []
                yData.forEach(item => {
                    let pass = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: item,
                        TestConclustion: "1"
                    }), 'count', 0)
                    let fail = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: item,
                        TestConclustion: "0"
                    }), 'count', 0)
                    let see = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: item,
                        TestConclustion: "3"
                    }), 'count', 0)

                    /*var yPassValueObj = {};
                    yPassValueObj.value = pass;
                    yPassValueObj.groupId = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: item,
                        TestConclustion: "1"
                    }), 'testLineId', null)
                    yPassValue.push(yPassValueObj)
                    var yFailValueObj = {};
                    yFailValueObj.value = fail;
                    yFailValueObj.groupId = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: item,
                        TestConclustion: "0"
                    }), 'testLineId', null)
                    yFailValue.push(yFailValueObj);
                    var ySeeValueObj = {};
                    ySeeValueObj.value = see;
                    ySeeValueObj.groupId = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: item,
                        TestConclustion: "3"
                    }), 'testLineId', null)
                    ySeeValue.push(ySeeValueObj)*/
                    yPassValue.push(pass)
                    yFailValue.push(fail)
                    ySeeValue.push(see)
                })
                var overAll = echarts.init(document.getElementById('bar'))
                this.$once('hook:beforeDestroy', function () {
                    // echarts.dispose(overAll)
                    overAll.clear()
                })

                yData = this.$lodash.map(yData, item => this.$lodash.trim(item))

                let containerHeight = 500;
                if (yData.length > 35) {
                    containerHeight = yData.length * 15
                }
                //TODO
                //$('#bar').css("height",containerHeight)
                let packageData = utils.packageData(yData, yPassValue, yFailValue, ySeeValue)
                var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.overallChemicalTestPerf'),
                    this.$lodash.map(packageData, 'name'),
                    this.$lodash.map(packageData, 'fail'),
                    this.$lodash.map(packageData, 'pass'),
                    this.$lodash.map(packageData, 'see'),
                    10)
                overAll.setOption(overAllOption)
                overAll.off('click')
                overAll.on('click', (params) => {
                     var tlName= params.name;
                     var testLineId = this.$lodash.result(this.$lodash.find(this.statisticsData, {
                        TestLineName: tlName,
                    }), 'testLineId', null)
                    this.initTestLineData(
                        {
                            "agentGroupCode": null,
                            //"buyerGroupCode": this.$lodash.get(this.user, 'customer.customerGroupCode'),
                            "buyerGroupCode": this.queryParam.buyerGroupCode,
                            "evaluationAlias": params.name,
                            "testLineId": testLineId,
                            "countryFieldCode": null,
                            "startDate": this.queryParam.startDate,
                            "endDate": this.queryParam.endDate,
                            "categoryName": this.queryParam.categoryName,
                            "testLineName": params.name,
                            "countryValue": null,
                            "language": this.language,
                            "productLineCode":this.productLineCode
                        }
                    )
                    this.visible = true
                });

                overAll.off('legendselectchanged')
                overAll.on('legendselectchanged', function (params) {
                    const PASS = params.selected.PASS
                    const FAIL = params.selected.FAIL
                    const SEE = params.selected['SEE RESULT']
                    const ALL = FAIL && PASS && SEE
                    if (ALL) {
                        packageData = self.$lodash.orderBy(packageData, 'all', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.overallChemicalTestPerf'), self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    } else if (PASS && !FAIL && !SEE) {
                        packageData = self.$lodash.orderBy(packageData, 'pass', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.overallChemicalTestPerf'), self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    } else if (!PASS && FAIL && !SEE) {
                        packageData = self.$lodash.orderBy(packageData, 'fail', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.overallChemicalTestPerf'), self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    } else if (!PASS && !FAIL && SEE) {
                        packageData = self.$lodash.orderBy(packageData, 'see', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.overallChemicalTestPerf'), self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    }
                }, overAll);


                // China
                countryArray = []
                this.statisticsDataCountry.forEach(item => {
                    if (item.DataCountryName && countryArray.indexOf(item.DataCountryName) == -1) {
                        countryArray.push(item.DataCountryName)
                    }
                })

                this.countryArray = countryArray

                this.$nextTick(() => {
                    var $l = this.$lodash
                    var vm = this
                    this.countryArray.forEach(item => {
                        var yChinaData = []
                        var yChinaRateValue = []
                        var yChinaFailValue = []
                        var yChinaSeeValue = []


                        $l.filter(vm.statisticsDataCountry, {DataCountryName: item}).forEach(countryItem => {
                            if (countryItem.TestLineName && $l.indexOf(yChinaData, countryItem.TestLineName) === -1) {
                                var failCount = $l.result($l.find(vm.statisticsDataCountry, {
                                    TestLineName: countryItem.TestLineName,
                                    DataCountryName: item,
                                    TestConclustion: '0'
                                }), 'count', 0)
                                var passCount = $l.result($l.find(vm.statisticsDataCountry, {
                                    TestLineName: countryItem.TestLineName,
                                    DataCountryName: item,
                                    TestConclustion: '1'
                                }), 'count', 0)
                                var seeCount = $l.result($l.find(vm.statisticsDataCountry, {
                                    TestLineName: countryItem.TestLineName,
                                    DataCountryName: item,
                                    TestConclustion: '3'
                                }), 'count', 0)
                                if (passCount + failCount + seeCount > 0) {
                                    yChinaData.push(countryItem.TestLineName);
                                }
                            }
                        })

                        $l.forEach(yChinaData, testLineItem => {
                            var failCount = $l.result($l.find(vm.statisticsDataCountry, {
                                TestLineName: testLineItem,
                                DataCountryName: item,
                                TestConclustion: '0'
                            }), 'count', 0)
                            var passCount = $l.result($l.find(vm.statisticsDataCountry, {
                                TestLineName: testLineItem,
                                DataCountryName: item,
                                TestConclustion: '1'
                            }), 'count', 0)
                            var seeCount = $l.result($l.find(vm.statisticsDataCountry, {
                                TestLineName: testLineItem,
                                DataCountryName: item,
                                TestConclustion: '3'
                            }), 'count', 0)

                           /* var yChinaPassValueObj = {};
                            yChinaPassValueObj.value = passCount;
                            yChinaPassValueObj.groupId = this.$lodash.result(this.$lodash.find(vm.statisticsDataCountry, {
                                TestLineName: testLineItem,
                                DataCountryName: item,
                                TestConclustion: "1"
                            }), 'testLineId', null)
                            yChinaRateValue.push(yChinaPassValueObj);

                            var yChinaFailValueObj = {};
                            yChinaFailValueObj.value = failCount;
                            yChinaFailValueObj.groupId = this.$lodash.result(this.$lodash.find(vm.statisticsDataCountry, {
                                TestLineName: testLineItem,
                                DataCountryName: item,
                                TestConclustion: "0"
                            }), 'testLineId', null)
                            yChinaFailValue.push(yChinaFailValueObj);

                            var yChinaSeeValueObj = {};
                            yChinaSeeValueObj.value = seeCount;
                            yChinaSeeValueObj.groupId = this.$lodash.result(this.$lodash.find(vm.statisticsDataCountry, {
                                TestLineName: testLineItem,
                                 DataCountryName: item,
                                TestConclustion: "3"
                            }), 'testLineId', null)
                            yChinaSeeValue.push(yChinaSeeValueObj)*/

                            yChinaRateValue.push(passCount)
                            yChinaFailValue.push(failCount)
                            yChinaSeeValue.push(seeCount)
                        })

                        var ChinaChart = echarts.init(document.getElementById(item + 'Bar'))
                        this.$once('hook:beforeDestroy', function () {
                            // echarts.dispose(ChinaChart)
                            ChinaChart.clear()
                        })

                        yChinaData = this.$lodash.map(yChinaData, item => this.$lodash.trim(item))

                        let china_containerHeight = 500;
                        if (yChinaData.length > 35) {
                            china_containerHeight = yChinaData.length * 15
                        }
                        //TODO
                        //$('#'+item+'Bar').css("height",china_containerHeight)


                        let packageData = utils.packageData(yChinaData, yChinaRateValue, yChinaFailValue, yChinaSeeValue)

                        var ChinaOption = this.initTestPerformanceOption(item, self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 6)
                        ChinaChart.setOption(ChinaOption)

                        ChinaChart.off('click')
                        ChinaChart.on('click', param => {
                        var tlName= param.name;
                        var testLineId = this.$lodash.result(this.$lodash.find(vm.statisticsDataCountry, {
                                TestLineName: tlName
                            }), 'testLineId', null)
                            self.initTestLineData(
                                {
                                    "agentGroupCode": null,
                                    "buyerGroupCode": self.$lodash.get(vm.user, 'customerGroupCode'),
                                    "evaluationAlias": param.name,
                                    "testLineId": testLineId,
                                    "countryFieldCode": null,
                                    "startDate": self.queryParam.startDate,
                                    "endDate": self.queryParam.endDate,
                                    "categoryName": self.queryParam.categoryName,
                                    "testLineName": param.name,
                                    "countryValue": item,
                                    "language": self.language,
                                    "productLineCode":self.productLineCode
                                }
                            )
                            self.visible = true
                        })


                        ChinaChart.off('legendselectchanged')
                        ChinaChart.on('legendselectchanged', function (params) {
                            const PASS = params.selected.PASS
                            const FAIL = params.selected.FAIL
                            const SEE = params.selected['SEE RESULT']
                            const ALL = FAIL && PASS && SEE
                            if (ALL) {
                                packageData = self.$lodash.orderBy(packageData, 'all', 'asc')
                                var overAllOption = this.initTestPerformanceOption(item, self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                                ChinaChart.setOption(overAllOption)
                            } else if (PASS && !FAIL && !SEE) {
                                packageData = self.$lodash.orderBy(packageData, 'pass', 'asc')
                                var overAllOption = this.initTestPerformanceOption(item, self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                                ChinaChart.setOption(overAllOption)
                            } else if (!PASS && FAIL && !SEE) {
                                packageData = self.$lodash.orderBy(packageData, 'fail', 'asc')
                                var overAllOption = this.initTestPerformanceOption(item, self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                                ChinaChart.setOption(overAllOption)
                            } else if (!PASS && !FAIL && SEE) {
                                packageData = self.$lodash.orderBy(packageData, 'see', 'asc')
                                var overAllOption = this.initTestPerformanceOption(item, self.$lodash.map(packageData, 'name'), self.$lodash.map(packageData, 'fail'), self.$lodash.map(packageData, 'pass'), self.$lodash.map(packageData, 'see'), 10)
                                ChinaChart.setOption(overAllOption)
                            }
                        }, ChinaChart);


                    })
                })
            },
            initTestLineData(param) {
                const vm = this
                const _ = this.$lodash

                getSupplierByTestLine(param).then((res) => {
                    if (res.data.data) {
                        let result = res.data.data || []

                        let applicantNames = _.uniq(result.map(item => item.applicantName))
                        let applicantNamesOrder = applicantNames.map(item => {
                            let pass = _.get(_.find(result, {applicantName: item, conclusion: 'Pass'}), 'countValue', 0)
                            let fail = _.get(_.find(result, {applicantName: item, conclusion: 'Fail'}), 'countValue', 0)
                            let seeResult = _.get(_.find(result, {
                                applicantName: item,
                                conclusion: 'See Result'
                            }), 'countValue', 0)
                            return {
                                applicantName: item,
                                order: seeResult + pass + fail
                            }
                        })
                        applicantNames = _.orderBy(applicantNamesOrder, ['order'], ['asc']).map(item => item.applicantName)
                        _.set(vm.supplierData, 'yAxis.data', applicantNames)
                        vm.supplierData.series = [
                            {
                                name: this.$t('dataAnalytics.pass'),
                                type: 'bar',
                                stack: 'Total',
                                barWidth: 10,
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'insideRight',
                                        formatter: function (num) {
                                            if (num.value > 0) {
                                                return num.value
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                itemStyle: {
                                    normal: {
                                        color: '#92D050'
                                    }
                                },
                                data: applicantNames.map(item => {
                                    let data = _.find(result, {applicantName: item, conclusion: 'Pass'}) || {}
                                    return data.countValue || 0
                                })
                            },
                            {
                                name: this.$t('dataAnalytics.fail'),
                                type: 'bar',
                                stack: 'Total',
                                barWidth: 10,
                                itemStyle: {
                                    normal: {
                                        color: '#FF0000'
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'insideRight',
                                        formatter: function (num) {
                                            if (num.value > 0) {
                                                return num.value
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                data: applicantNames.map(item => {
                                    let data = _.find(result, {applicantName: item, conclusion: 'Fail'}) || {}
                                    return data.countValue || 0
                                })
                            },
                            {
                                name: this.$t('dataAnalytics.seeResult'),
                                type: 'bar',
                                stack: 'Total',
                                barWidth: 10,
                                itemStyle: {
                                    normal: {
                                        color: '#adadad'
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'insideRight',
                                        formatter: function (num) {
                                            if (num.value > 0) {
                                                return num.value
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                data: applicantNames.map(item => {
                                    let data = _.find(result, {applicantName: item, conclusion: 'See Result'}) || {}
                                    return data.countValue || 0
                                })
                            },
                        ]
                    }
                }).catch(error => {
                })
            },
          async productLineChange(value){
            await this.initData();
          }
        }
    }
</script>
