import request from '@/router/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/lab/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getLabTypeList = (language) => {
    var keyGroup='LabType';
    if(language=='zh-CN'){
        keyGroup='LabTypeCN';
    }
    return request({
        url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=0&SystemID=0&sysKeyGroup='+keyGroup,
        method: 'get',
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/lab/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}
export const deleteLab = (id) => {
    return request({
        url: '/api/sgs-mart/lab/deleteLab',
        method: 'post',
        params: {
            id:id,
        }
    })
}
export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/lab/detail',
        method: 'get',
        params: {
            id,
        }
    })
}


export const add = (row) => {
    return request({
        url: '/api/nkop-system/tenant/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/sgs-mart/lab/submit',
        method: 'post',
        data: row
    })
}
export const getLabList = (params) => {
    return request({
        url: '/api/sgs-mart/lab/listLab',
        method: 'get',
        params: {
            ...params
        }
    })
}
export const queryThirdLabList = (params) => {
    return request({
        url: '/api/sgs-mart/lab/thirdLab-list',
        method: 'post',
        params: {
            ...params
        }
    })
}
/*=====================实验室来联系人方法==============================================*/


export const getLabContactList = (params) => {
    return request({
        url: '/api/sgs-mart/labContact/list',
        method: 'get',
        params: {
            ...params
        }
    })
}
export const getLabContactListPage = (current, size,params) => {
    return request({
        url: '/api/sgs-mart/labContact/listPage',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detailContact = (id) => {
    return request({
        url: '/api/sgs-mart/labContact/detail',
        method: 'get',
        params: {
            id,
        }
    })
}
export const queryCountryDatas = () => {
    return request({
        url: '/api/sgs-mart/lab/queryCountryDatas',
        method: 'get',
    })
}
export const queryLocationDatas = (countryName) => {
    return request({
        url: '/api/sgs-mart/lab/queryLocationDatas',
        method: 'get',
        params: {
            countryName,
        }
    })
}


export const updateContact = (row) => {
    return request({
        url: '/api/sgs-mart/labContact/submit',
        method: 'post',
        data: row
    })
}
export const removeContact = (id) => {
    return request({
        url: '/api/sgs-mart/labContact/deleteLabContact',
        method: 'post',
        params: {
            id:id,
        }
    })
}

export const getLabCertificateTypeList = (language) => {
    var keyGroup='LabCertificateType';
    if(language=='zh-CN'){
        keyGroup='LabCertificateTypeCN';
    }
    return request({
        url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=0&SystemID=0&sysKeyGroup='+keyGroup,
        method: 'get',
    })
}

export const uploadExcelData = (classParam) => {
    return request({
        headers:{'content-type': 'application/x-www-form-urlencoded'},
        url: '/api/sgs-mart/lab/importLab',
        method: 'post',
        data: classParam
    })
}