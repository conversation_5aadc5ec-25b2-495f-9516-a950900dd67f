<template>
    <el-date-picker
            v-bind="$attrs"
            v-on="$listeners"
            :value="value"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="-"
            :start-placeholder="$t('invoice.fapiaoIssuedStartDate')"
            :end-placeholder="$t('invoice.fapiaoIssuedEndDate')"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
            style="width:100%;">
    </el-date-picker>

</template>

<script>
    export default {
        name: 'dateRange',
        components: {},
        props: {
            value: {
                type: null
            },
            defaultDate:{
                type: null
            },
        },
        data() {
            return {
                pickerOptions: {
                    shortcuts: [{
                        text: this.$t('datePicker.lastWeek'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.$t('datePicker.lastMonth'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.$t('datePicker.lastThreeMonths'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
            }
        },
        created() {
            debugger;
            if(this.defaultDate!=null){
                this.value=this.defaultDate
            }
        }
    }
</script>
