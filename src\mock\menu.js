// import Mock from 'mockjs'
//
// const top = [{
//   label: "首页",
//   path: "/wel/index",
//   icon: 'el-icon-menu',
//   meta: {
//     i18n: 'dashboard',
//   },
//   parentId: 0
// },
// {
//   label: "创建申请单",
//   icon: 'el-icon-document',
//   path: "/test/index",
//   meta: {
//     i18n: 'createTRF',
//   },
//   parentId: 3
// }]
// export default ({mock}) => {
//   if (!mock) return;
//   Mock.mock('/user/getTopMenu', 'get', () => {
//     return {
//       data: top
//     }
//   })
//
// }
