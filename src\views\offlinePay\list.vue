<template>
    <basic-container>
        <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.documentLibrary')}}</el-breadcrumb-item>
        </el-breadcrumb>
        <el-row>
            <el-form :inline="true" :model="formInline" size="medium" label-width="200px" label-position="left">
                <el-form-item>
                    <el-input clearable
                              v-model="query.documentTitle"
                              :placeholder="$t('documentLibrary.name')"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select clearable v-model="query.documentType" @change="selectRoleChange" style="width: 100%;">
                        <el-option v-for="item in documentTypeList" :key="item.id" :lable="item.id"
                                   :value="item.name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <el-row>
            <el-row>
                <el-button type="primary" @click="addRow" size="medium">{{$t('operation.add')}}</el-button>
            </el-row>
            <el-table
                    :data="tableData"
                    style="width: 100%"
                    size="medium">
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
                <el-table-column
                        prop="programName"
                        :label="$t('documentLibrary.theme')"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="documentType"
                        :label="$t('wel1.documentType')"
                        width="200">
                    <template slot-scope="scope">
                        <span v-if="scope.row.documentType === 2">{{ $t('wel1.knowledgeSharing') }}</span>
                        <span v-if="scope.row.documentType === 1">{{ $t('wel1.documentSharing') }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="documentTitle"
                        :label="$t('documentLibrary.name')"
                        :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column
                        prop="documentUrl"
                        :label="$t('documentLibrary.linkUrl')"
                        :show-overflow-tooltip="true"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="documentSort"
                        :label="$t('documentLibrary.documentSort')"
                        width="80"
                >
                </el-table-column>
                <el-table-column
                        prop="status"
                        :label="$t('common.status.title')"
                        width="80"
                        align="center">
                    <template slot-scope="scope">
                        <el-tooltip
                                :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                placement="top">
                            <el-switch
                                    v-model="scope.row.status"
                                    active-color="#ff6600"
                                    inactive-color="#ff4949"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @change="changeStatus(scope.row)">
                            </el-switch>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                        :label="$t('operation.title')"
                        width="260"
                        align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="detailRow(scope.row)" size="small" icon="el-icon-edit">
                            {{$t('operation.edit')}}
                        </el-button>
                        <el-button @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-delete">
                            {{$t('operation.remove')}}
                        </el-button>
                        <el-button @click="authDialog(scope.row)" type="text" size="small" icon="el-icon-share">
                            {{$t('operation.auth')}}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-row>
        <el-drawer :title="title" :visible.sync="dialogFormVisible" size="70%">
            <el-form ref="form" :model="form" label-width="200px" label-position="left" size="medium" class="sgs-form">
                <div class="sgs-group">
                    <h3>{{$t('info.base')}}</h3>
                </div>

                <el-form-item :label="$t('wel1.documentType')"
                              :rules="{ required: true, message: $t('documentLibrary.validate.nameBlur'), trigger: 'blur' }"
                              prop="documentType">
                    <el-radio-group v-model="form.documentType" @change="documentTypeChange">
                        <el-radio :label='2'>{{ $t('wel1.knowledgeSharing') }}</el-radio>
                        <el-radio :label='1'>{{ $t('wel1.documentSharing') }}</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item :label="$t('documentLibrary.name')"
                              :rules="{ required: true, message: $t('documentLibrary.validate.nameBlur'), trigger: 'blur' }"
                              prop="documentTitle">
                    <el-input v-model="form.documentTitle"
                              maxlength="200"
                              type="tree"></el-input>
                </el-form-item>
                <el-form-item :label="$t('documentLibrary.linkUrl')">
                    <el-input v-model="form.documentUrl"
                              maxlength="300"></el-input>
                </el-form-item>
                <el-form-item :label="$t('vipProgram.name')">
                    <el-select v-model="form.themeId" placeholder="请选择" style="width: 100%;">
                        <el-option v-for="(vipProgram,index) in options" :label="vipProgram.programName"
                                   :value="vipProgram.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('documentLibrary.imageCloudId')" v-if="isShowPhoto"
                              :rules="{ required: false, message: $t('documentLibrary.validate.imageUrl'), trigger: 'blur' }"
                              prop="imageCloudId">
                    <el-upload
                            ref="uploadImg"
                            class="avatar-uploader"
                            action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                            drag
                            :on-success="handleAvatarSuccessOfImg"
                            multiple
                            :show-file-list="false"
                            :before-upload="beforeAvatarUpload">
                        <img v-model="form.imageCloudId" v-if="imageUrl" :src="imageUrl" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <el-input type="hidden" v-model="form.imageCloudId">{{ imageUrl }}</el-input>
                </el-form-item>
                <el-form-item :label="$t('documentLibrary.documentSort')"
                              :rules="{ required: true, message: $t('documentLibrary.documentSort'), trigger: 'blur' }"
                              prop="documentSort">
                    <el-input v-model="form.documentSort"
                              maxlength="200"
                              type="number"></el-input>
                </el-form-item>
                <el-form-item :label="$t('documentLibrary.documentDec')"
                              :rules="{ required: true, message: $t('documentLibrary.documentDec'), trigger: 'blur' }"
                              prop="documentDec">
                    <el-input v-model="form.documentDec"
                              maxlength="200"
                              type="textarea"></el-input>
                </el-form-item>
                <!--<el-form-item :label="$t('common.status.title')">-->
                <!--<el-radio-group v-model="form.status">-->
                <!--<el-radio :label="1">{{$t('common.status.enable')}}</el-radio>-->
                <!--<el-radio :label="0">{{$t('common.status.disable')}}</el-radio>-->
                <!--</el-radio-group>-->
                <!--</el-form-item>-->
                <el-row class="sgs-group">
                    <h3>{{$t('info.attachment')}}</h3>
                    <div class="right">
                        <el-upload
                                class="upload-demo"
                                action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                :on-success="uploadSuccess"
                                :on-change="uploadChange"
                                :file-list="fileList"
                                :show-file-list="false">
                            <el-button size="small" type="primary">{{$t('operation.upload')}}</el-button>
                        </el-upload>
                    </div>
                </el-row>
                <el-table :data="form.attachmentList" width="100%"
                          :element-loading-text="$t('uploadLoadingText')"
                          element-loading-spinner="el-icon-loading"
                          v-loading="uploadLoading">
                    <el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="fileName"
                            :label="$t('attachment.name')">
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="180">
                        <template slot-scope="scope">
                            <el-button type="text" @click="downloadAttachmentRow(scope.row)" size="small"
                                       icon="el-icon-download">{{$t('operation.download')}}
                            </el-button>
                            <el-button @click="removeAttachmentRow(scope.$index)" type="text" size="small"
                                       icon="el-icon-delete">{{$t('operation.remove')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="sgs-bottom">
                    <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">
                        {{$t('operation.submit')}}
                    </el-button>
                </div>
            </el-form>
        </el-drawer>
    </basic-container>
</template>

<script>
    import {getList, add, remove, detail} from "@/api/document/library";
    import {getPrograms} from "@/api/document/vipProgram";
    import {getCloudFileURL, getSgsCustomer} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import {validatenull} from "@/util/validate";

    export default {
        data() {
            return {
                isShowPhoto: true,
                isShowUploadImgFlag: true,
                uploadLoading: false,
                name: "list",
                title: '',
                imageCloudId: '',
                imageUrl: '',
                hadChangeImg: '0',//修改是，是否更换了图片
                imageCloudIdOld: '',//修改时，先把原来的cloudID存起来
                dialogFormVisible: false,
                dialogAuthFormVisible: false,
                btnGuestbookSubmit: false,
                tableData: [],
                selectRow: {},
                form: {
                    attachmentList: [],
                },
                documentTypeList: [
                    {'id': '2', 'name': this.$t('wel1.knowledgeSharing')},
                    {id: '1', name: this.$t('wel1.documentSharing')}
                ],
                fileList: [],
                documentTypeParam: "",
                query: {},
                sort: {ascs: 'document_sort', descs: 'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                options: [],
                varlue: '',
                selectedTime: {
                    disabledDate: (time) => {
                        return time.getTime() < this.form.effectiveDate;
                    },
                },
            }
        },
        methods: {
            documentTypeChange(val) {
                debugger;
                if (val == 1) {
                    this.isShowPhoto = false;
                } else {
                    this.isShowPhoto = true;
                }

            },
            selectRoleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                debugger
                var obj = {};
                obj = this.documentTypeList.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                if (obj === "" || obj === undefined) {
                    this.documentTypeParam = null
                } else {
                    this.query.documentType = obj.name;//提交数据使用，此处可忽略
                    this.documentTypeParam = obj.id;//提交数据使用，此处可忽略
                }
            },
            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                let param = this.query;
                /*if (this.query.documentType === this.$t('wel1.knowledgeSharing')) {
                  param.documentType = 1
                }else{
                  param.documentType = 2
                }*/
                //param.documentType = this.documentTypeParam;
                debugger
                getList(page.currentPage, page.pageSize, Object.assign(params, {
                    documentTitle: this.query.documentTitle,
                    documentType: this.documentTypeParam
                })).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                debugger;
                if (this.form.documentType == 1) {
                    if (validatenull(this.form.documentUrl) && validatenull(this.form.attachmentList)) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('newService.validate.urlAndFileValidate'),
                            type: 'warning'
                        });
                        return false;
                    }

                    if (!validatenull(this.form.documentUrl) && !validatenull(this.form.attachmentList)) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('newService.validate.urlAndFileError'),
                            type: 'warning'
                        });
                        return false;
                    }
                }
                if (this.hadChangeImg == '0') {//修改状态下，并且没有更换图片
                    this.form.imageCloudId = this.imageCloudIdOld;
                } else {
                    this.form.imageCloudId = this.imageCloudId;
                }
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.btnGuestbookSubmit = true;
                        add(this.form).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.btnGuestbookSubmit = false;
                            this.dialogFormVisible = false;
                            this.onLoad(this.page);
                        });
                    } else {
                        return false;
                    }
                });
            },
            addRow() {
                debugger;
                this.isShowUploadImgFlag = false;
                this.$nextTick(function () {
                    this.isShowUploadImgFlag = true;
                })
                this.imageUrl = '';
                this.title = this.$t('documentLibrary.title.add');
                this.form = {};
                this.form.attachmentList = [];
                this.fileList = [];
                this.dialogFormVisible = true
            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            authDialog(row) {
                this.selectRow = row;
                // this.$router.push("{ path: /document/authorization,query: { documentId: row.id }}");
                this.$router.push({path: "/document/authorization", query: {"documentId": row.id}});
            },
            detailRow(row) {
                this.title = this.$t('documentLibrary.title.edit');
                detail(row.id).then(res => {
                    debugger
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form = res.data.data;
                    this.form.documentType = res.data.data.documentType
                    if (this.form.documentType == 1) {
                        this.isShowPhoto = false;
                    } else {
                        this.isShowPhoto = true;
                        this.imageCloudIdOld = res.data.data.imageCloudId;//先把原来的存起来
                        this.imageUrl = res.data.data.imageCloudIdReal;
                    }


                });
            },
            handleAvatarSuccessOfImg(res, file) {
                this.hadChangeImg = '1';
                this.imageUrl = URL.createObjectURL(file.raw);
                console.log(res);

                this.imageCloudId = res.data[0].cloudID;

            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isJPG) {
                    this.$message.error(this.$t('uploadType'));
                }
                if (!isLt2M) {
                    this.$message.error(this.$t('uploadSize'));
                }
                return isJPG && isLt2M;
            },
            uploadSuccess(res, file) {
                var that = this;
                const attachment = {
                    'attachmentId': res.data[0].cloudID,
                    'fileUrl': res.data[0].path,
                    'fileName': file.name
                };
                that.form.attachmentList.push(attachment);
                console.log(JSON.stringify(that.form.attachmentList));
            },
            uploadChange(file, fileList) {
                if (file.status == 'ready') {
                    //开启loading效果
                    this.uploadLoading = true;
                } else {
                    this.uploadLoading = false;
                }
            },
            removeAttachmentRow(index) {
                this.form.attachmentList.splice(index, 1);
            },
            downloadAttachmentRow(row) {
                getCloudFileURL(row.attachmentId).then(res => {
                    window.open(res.data, "_blank");
                });
            },
            changeStatus(row) {
                const modifyForm = {};
                this.form = row;
                modifyForm.modifiedby = this.userInfo.account;
                modifyForm.id = this.form.id;
                modifyForm.status = this.form.status;
                add(modifyForm).then(res => {
                    if (res.data.success == 1) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    } else {
                        this.$message({
                            type: "fail",
                            message: res.data.message,
                        });
                    }
                });
            },
        },
        created() {
            this.onLoad(this.page);
            getPrograms().then(res => {
                this.options = res.data.data;
            });
        },
        computed: {
            ...mapGetters([
                "userInfo"
            ])
        },

    }
</script>


<style scoped>
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 148px;
        height: 148px;
        line-height: 178px;
        text-align: center;
    }

    .avatar {
        height: 178px;
        display: inline;
    }
</style>
