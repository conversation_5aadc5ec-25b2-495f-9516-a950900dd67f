<template>
    <div class="TableHeaderTabs" id="TableHeaderTabs">
        <el-form inline>
            <el-form-item v-if="sortBy">
                <el-card shadow="always"
                         :body-style="{padding:'5px 10px'}"
                >
                    {{getSortLabel()}} {{(orderBy || '').toUpperCase()}}
                    <i class="el-icon-circle-close" style="cursor: pointer;" @click="removeSort"></i>
                </el-card>
            </el-form-item>
            <el-form-item  v-for="(c,index) in tabsList" :key="index">
                <el-card shadow="always"
                    :body-style="{padding:'5px 10px'}"
                >
                    {{c.label}} {{c.condition}} {{c.conditionValue}}
                    <i class="el-icon-circle-close" style="cursor: pointer;" @click="removeFilter(c)"></i>
                </el-card>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        name: "TableHeaderTabs",
        inject:['language','getConditionList','removeSignFilter','removeSortFilter'],
        data() {
            return {
                tabsList:[],
                selectMap:{}
            }
        },
        methods: {
            getSortLabel(){
                let col = this.columns.find(col=>col.prop==this.sortBy) || {};
                let languageLab = col.languageLab || {};
                let lan = this.language();
                let colLabel = languageLab[lan || 'en'] || languageLab['en'];
                return colLabel;
            },
            updateQueryTab(){
                this.tabsList = [];
                let lan = this.language();
                this.columns.forEach(col=>{
                    let {prop,languageLab} = col;
                    let label = languageLab[lan || 'en'] || languageLab['en'];
                    let obj =  this.dynamicForm[prop];
                    if(!obj || !obj.condition){
                        return;
                    }
                    let {condition,conditionValue} = obj;
                    if(["blank","notBlank"].includes(condition)){
                        this.tabsList.push({
                            prop,
                            label,
                            condition: this.selectMap[condition],
                            conditionValue:''
                        })
                    }
                    if(conditionValue && conditionValue.length>0){
                        this.tabsList.push({
                            prop,
                            label,
                            condition: this.selectMap[condition],
                            conditionValue
                        })
                    }
                })
            },
            removeFilter(c){
                let {prop} = c;
                this.removeSignFilter(prop);
            },
            removeSort(){
                this.removeSortFilter();
            }
        },
        mounted() {
        },
        created() {
            let selectList = this.getConditionList();
            let selectMap = {}
            selectList.forEach(s=>{
                selectMap[s.value] = s.label;
            })
            this.selectMap = selectMap;
        },
        watch: {
            changeLan:{
                immediate:true,
                handler(newV){
                    this.$nextTick(()=>{
                        //console.log("watch changeLan",newV);
                        this.updateQueryTab();
                    })
                }
            },
            dynamicForm:{
                deep:true,
                immediate:true,
                handler(newV){
                    this.$nextTick(()=>{
                        //console.log("watch dynamicForm",newV)
                        this.updateQueryTab();
                    })
                }
            }
        },
        computed: {
            changeLan(){
                return this.language();
            }
        },
        props: {
            dynamicForm:{
                type:Object,
                default(){
                    return{}
                }
            },
            sortBy:'',
            orderBy:'',
            columns:{
                type:Array,
                default(){
                    return []
                }
            }
        },
        updated() {
        },
        components: {}
    }
</script>

<style scoped>
    .TableHeaderTabs {
    }
</style>