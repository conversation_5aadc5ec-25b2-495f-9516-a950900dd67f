<template>
    <div class="panel panel-default" style="border-radius: 0; border: 0; margin-top: 24px;">

        <div class="panel-body">
            <div class="sgs_portlet">
                <el-row>
                    <h4 class="sgs-title">{{ productLineCode=='HL' ?  $t('quotation.statementDetail') :$t('quotation.quotationDetail')}}</h4>
                    <div class="right">
                        <el-button type="primary" v-if="permissionList.quotationAddBtn" @click="addInvoiceTotal" size="small">
                            {{$t('operation.add')}}
                        </el-button>
                    </div>
                </el-row>
                <div class="body trfQuotation_scrollbar">
                  <el-table ref="quotationList" id="quotationList" class="trfQuotation_scrollbar" :row-key="getRowKeys" fixed :data="invoice.list" style="width: 100%">
                      <el-table-column :label="$t('quotation.referenceNo')" width="180">
                          <template slot-scope="scope">
                              <el-tooltip :content="scope.row.referenceNo" placement="top" effect="light">
                                <el-input :placeholder="$t('searchTxt')" size="mini" v-model="scope.row.referenceNo"  maxlength="200" :disabled="!permissionList.quotationAddBtn || !scope.row.quotationStatus==0 || scope.row.source!=1 "></el-input>
                              </el-tooltip>
                          </template>
                      </el-table-column>
                      <el-table-column :label="productLineCode=='HL' ? $t('quotation.statementNo') : $t('quotation.quotationNo')" width="180">
                        <template slot-scope="scope">
                          <el-tooltip :content="scope.row.quotationNo" placement="top" effect="light">
                            <el-input :placeholder="$t('searchTxt')" size="mini" v-model="scope.row.quotationNo"  maxlength="200" :disabled="!permissionList.quotationAddBtn || !scope.row.quotationStatus==0 || scope.row.source!=1 "></el-input>
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('quotation.invoiceNo')" width="180">
                        <template slot-scope="scope">
                          <el-tooltip :content="scope.row.invoiceNo" placement="top" effect="light">
                            <el-input :placeholder="$t('searchTxt')" size="mini" v-model="scope.row.invoiceNo"  maxlength="200" :disabled="!permissionList.quotationAddBtn || !scope.row.quotationStatus==0 || scope.row.source!=1 "></el-input>
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('quotation.totalTestItems')" width="160">
                        <template slot-scope="scope">
                          <el-tooltip :content="scope.row.testItemsAmount" placement="top" effect="light">
                            <el-input :placeholder="0.00" size="mini" v-model="scope.row.testItemsAmount"  maxlength="200" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" :disabled="!permissionList.quotationAddBtn || !scope.row.quotationStatus==0 || scope.row.source!=1 ">
                              <template slot="prepend" >
                                <span  v-if="!permissionList.quotationAddBtn || !scope.row.quotationStatus==0 || scope.row.source != 1 ">{{ scope.row.paymentCurrency }}</span>
                                <span v-else>
                                  <el-select  v-model="scope.row.paymentCurrency"  style="width: 70px;"><el-option v-for="(currency,index) in currencyList" :label="currency.label" :value="currency.value"></el-option></el-select>
                                </span>
                              </template>
                            </el-input>
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('quotation.estimatedTax')" width="160">
                        <template slot-scope="scope">
                          <el-tooltip :content="scope.row.estimatedTax" placement="top" effect="light">
                            <el-input :placeholder="0.00" size="mini" v-model="scope.row.estimatedTax"  maxlength="200" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" :disabled="!permissionList.quotationAddBtn || !scope.row.quotationStatus==0 || scope.row.source!=1 ">
                              <template slot="prepend" >{{ scope.row.paymentCurrency }}</template>
                            </el-input>
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('quotation.totalAmount')" width="160">
                        <template slot-scope="scope">
                          <el-tooltip :content="((isNaN(parseFloat(scope.row.testItemsAmount)) ? 0.00 : parseFloat(scope.row.testItemsAmount)) + (isNaN(parseFloat(scope.row.estimatedTax)) ? 0.00 : parseFloat(scope.row.estimatedTax))).toFixed(2)" placement="top" effect="light">
                            <el-input :placeholder="0.00" size="mini" :value=" ((isNaN(parseFloat(scope.row.testItemsAmount))?0.00:parseFloat(scope.row.testItemsAmount))  + (isNaN(parseFloat(scope.row.estimatedTax))?0.00:parseFloat(scope.row.estimatedTax)) ).toFixed(2)"  disabled="disabled">
                              <template slot="prepend" >{{ scope.row.paymentCurrency }}</template>
                            </el-input>
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column :label="productLineCode=='HL' ?$t('quotation.status.statement') :$t('quotation.status.title')" width="160">
                        <template slot-scope="scope">
                          <span>{{scope.row.quotationStatus | statusFilter(this_)}}</span>
                        </template>
                      </el-table-column>

                    <el-table-column :label="$t('quotation.quotationConfirmDate')" width="240">
                      <template slot-scope="scope">
                        <span>{{scope.row.commentTime | formatedDate(scope.row.commentTime)}}</span>
                      </template>
                    </el-table-column>


                      <el-table-column :label="productLineCode=='HL' ? $t('quotation.statementPDF') : $t('quotation.invoicePDF')" width="400">
                        <template slot-scope="scope">
                          <el-upload
                              :disabled="!permissionList.quotationUploadBtn || !scope.row.quotationStatus==0"
                              ref="upload"
                              class="customerUpload"
                              action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                              :on-success="(param) => invoiceFileChange(scope.row,param)"
                              :on-exceed="(param) => quotationFileExceed(scope.row,param)"
                              :file-list="scope.row.attachmentList"
                              :on-preview="downFile"
                              :show-file-list="true"
                              :on-change="fileProgress"
                              :on-remove = "(param) => invoiceFileRemove(scope.row,param)"
                              :before-upload="beforeAvatarUpload"
                              :limit="1">
                              <i class="el-icon-upload2" v-if="permissionList.quotationUploadBtn" ></i>
                          </el-upload>
                        </template>
                      </el-table-column>

                    <el-table-column :label="$t('quotation.quotationIssuedDate')" width="160">
                      <template slot-scope="scope">
                        <el-date-picker type="date" :placeholder="$t('operation.pleaseSelect')"
                                        :disabled="scope.row.quotationStatus == 3 ||scope.row.quotationStatus == 1 "
                                        v-model="scope.row.issuedTime"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;">
                        </el-date-picker>
                      </template>
                    </el-table-column>

                      <el-table-column :label="$t('operation.title')" class-name="action_class1" width="300">
                          <template slot-scope="scope">
                            <el-button  type="text" size="small"  @click="delInvoiceTotal(scope.row)"   v-if=" permissionList.quotationDeleteBtn && scope.row.quotationStatus==0" icon="el-icon-remove-outline">
                              {{$t('operation.remove')}}
                            </el-button>
                            <el-button v-if="permissionList.quotationConfirmBtn && scope.row.quotationStatus==0 && scope.row.id"
                                       @click="confirmQuotation(scope.row.id)"
                                       type="text" size="small" icon="el-icon-check">
                              {{$t('operation.confirm')}}
                            </el-button>
                            <el-button v-if="permissionList.quotationRejectBtn && scope.row.quotationStatus==0 && scope.row.id"
                                       @click="rejectQuotation(scope.row.id)"
                                       type="text" size="small" icon="el-icon-close">
                              {{$t('invoiceTodo.status.reject')}}
                            </el-button>
                            <el-button v-if="permissionList.quotationCancelBtn && scope.row.quotationStatus==2 && scope.row.id"
                                       @click="cancelQuotation(scope.row.id)"
                                       type="text" size="small" icon="el-icon-close">
                              {{$t('invoiceTodo.status.cancel')}}
                            </el-button>
                          </template>
                      </el-table-column>
                  </el-table>
                </div>
            </div>
        </div>

        <el-row class="sgs-footer" v-if="permissionList.quotationSaveBtn">
            <el-button type="primary" v-if="permissionList.quotationSaveBtn" :loading="savaBtnLoading" @click="saveInvoiceTotal" >{{$t('operation.save')}}</el-button>
        </el-row>

      <el-dialog :title="title" :visible.sync="dialogFormVisible" size="60%" :close-on-click-modal="false" :append-to-body="true">
        <el-form ref="form" :model="form" label-width="160px" label-position="left" size="medium">
          <el-form-item id="input-buyer-name" :label="$t('quotation.rejectReason')"
                        :rules="{ required: true, message: $t('quotation.validate.rejectReason'), trigger: 'blur' }"
                        prop="rejectReason">
            <el-input
                maxlength="200"
                type="textarea"
                v-model="form.rejectReason"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="bottom clearfix " style="text-align: center">
          <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
          <el-button size="small" type="primary" @click="confirmRejectQuotation('form')" :loading="submitLoading" id="add-buyer-confirm">
            {{$t('operation.submit')}}
          </el-button>
        </div>
      </el-dialog>
    </div>
</template>


<script>
    import {mapGetters} from 'vuex'
    import {
        saveQuotation,getQuotation,remove,saveUploadFile,confirmQuotation,rejectQuotation,cancelQuotation
    } from "@/api/trf/trf";
    import {queryFile,deleteFile} from "@/api/common/file";
    import {validatenull} from "../../../util/validate";
    import {dateFormatEnums} from "@/util/dateTimeUtil";
    import moment from 'moment';
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils';
    import {deepClone} from '@/util/util'
    export default {
        name: "trfQuotation",
        data(){
            return {
                this_:this,
                savaBtnLoading:false,
                saveBtnDisabled:false,
                invoiceSelectVisible:false,
                invoiceSelectType:'',
                invoice:{
                    index:1,
                    list:[
                        {
                            tempId:this.RandomNumBoth(1,1000),
                            testItemsAmount:null,
                            estimatedTax:null,
                            quotationStatus:0,
                            paymentCurrency:'CNY',
                            source: 1
                        }
                    ],
                    paymentCurrency:'CNY'
                },
                uploadUrl:'/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1',
                fileList:[],
                fileType: 'application/msword,application/pdf,aplication/zip,application/vnd.ms-excel,text/plain',
                attachmentParam:{
                    object:'quotation',
                    objectId:""
                },
              dialogFormVisible:false,
              title:'',
              submitLoading: false,
              form: {},
              getRowKeys(row) {
                return row.id
              },
              currencyList:[
                {value:'CNY',label:'CNY'},
                {value:'JPY',label:'JPY'},
                {value:'EUR',label:'EUR'},
                {value:'HKD',label:'HKD'},
                {value:'USD',label:'USD'},
                {value:'AFN',label:'AFN'}
              ]
            }
        },
        filters: {
            statusFilter: function (status,vue) {
                let value= vue.$t('quotation.status.toBeConfirm');
                  if (status == 0 || status == '0') {
                    value =  vue.$t('quotation.status.toBeConfirm');
                } else if (status == 1 || status == '1') {
                       value =  vue.$t('quotation.status.confirmed');
                } else if (status == 2 || status == '2') {
                       value = vue.$t('quotation.status.reject');
                }else if (status == 3 || status == '3'){
                       value = vue.$t('quotation.status.cancel');
                }else if (status == 4 || status == '4'){
                      value = vue.$t('quotation.status.toBePay')
                  }
                return value
            },
            formatedDate: function(date) {
              if(validatenull(date)){
                return ''
              }
              return  moment(date).format("yyyy-MM-DD");
            },
        },
        created() {
            this.initInvoiceTotal(this.trfId);
        },
        methods:{
            currentTz_YMD(val) {
              if (!val) return ''
              let value = tzFormChina(val, 'YYYY-MM-DD HH:mm:ss');
              return moment(value).format('YYYY-MM-DD');
            },
            tzToChina(val){
              if (!val) return ''
              let value = tzToChina(val);
              return value;
            },
            confirmQuotation(quotationId){
                if(!validatenull(quotationId)){
                     this.$confirm('Whether to confirm the quotation?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        confirmQuotation(quotationId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                        this.initInvoiceTotal(this.trfId);
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    });
                }
            },
            rejectQuotation(quotationId){
              this.form = {'quotationId':quotationId};
              this.dialogFormVisible = true
              this.title = this.$t('quotation.rejectReason');
                /*if(!validatenull(quotationId)){
                     this.$confirm('Whether to reject the quotation?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        rejectQuotation(quotationId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                        this.initInvoiceTotal(this.trfId);
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                        /!*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*!/
                    });
                }*/
            },
            confirmRejectQuotation(){
              this.$refs['form'].validate((valid) => {
                if (valid) {
                  this.submitLoading = true;
                  rejectQuotation(this.form).then(res => {
                    this.$message({
                      type: "success",
                      message: this.$t('api.success')
                    });
                    this.submitLoading = false;
                    this.dialogFormVisible = false;
                    this.initInvoiceTotal(this.trfId);
                  },error=>{
                    this.submitLoading = false;
                    this.$message.error(this.$t('api.error'));
                  }).catch(() => {
                    this.submitLoading = false;
                  });
                } else {
                  return false;
                }
              });
            },
            cancelQuotation(quotationId){
                if(!validatenull(quotationId)){
                     this.$confirm('Whether to cancel the invoice?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        cancelQuotation(quotationId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                        this.initInvoiceTotal(this.trfId);
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    });
                }
            },
            beforeAvatarUpload(file) {
                const isPDF = file.type === 'application/pdf';
                const isLt10M = file.size / 1024 / 1024 <= 20;

                if (!isPDF) {
                    this.$message.error(this.$t('uploadType_PDF'));
                }
                if (!isLt10M) {
                    this.$message.error(this.$t('uploadFileSizeError')+'20MB!');
                }
                return isPDF && isLt10M;
            },
            invoiceFileChange(invoiceItem, file, fileList){
                var that=this;
                debugger;
                /*if (this.fileType.indexOf(file.data[0].suffixes) === -1) {
                   // this.$warn({ title: 'warning',  message: 'File Type is not Support !',})
                    alert("文件格式错误");
                    this.$refs.upload.clearFiles()
                    return
                }*/
                this.savaBtnLoading=false;
                console.log(this.savaBtnLoading)
                if (file.data[0].size / 1024 / 1024 > 20) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('uploadFileSizeError') + '20MB!',
                        type: 'warning'
                    });
                    this.$refs.upload.clearFiles()
                    return
                }
                const attachment = {
                    'attachmentId': file.data[0].cloudID,
                    'fileUrl': file.data[0].path,
                    'fileName': file.data[0].attachmentName
                };
                let uploadFiles = []
                uploadFiles.push(attachment)
                this.$set(invoiceItem, 'uploadFiles', uploadFiles);

            },
            quotationFileExceed(invoiceItem, file, fileList){
                //超出最大上传数量，给出提示
                this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('uploadExceed_1'),
                    type: 'warning'
                });
            },
            //文件上传时钩子函数
            async fileProgress(event, file, fileList){
                debugger;
                if(event.status=='ready'){
                    this.savaBtnLoading=true;
                }else{
                    this.savaBtnLoading=false;
                }
                console.log(event);
                console.log(file);
                console.log(fileList);
            },
            async invoiceFileRemove(invoiceItem,file, fileList){
                console.log(file);
                //删除该账单已绑定的附件
                debugger;
                this.$set(this.attachmentParam, 'objectId', invoiceItem.id)
                //如果是已上传的文件
                if (file.id!=null  && file.id!=undefined) {
                    // 删除已上传的文件
                   // let delres = await deleteFile({key: file.id})
                    var params={};
                    let delres = await deleteFile(Object.assign(params, this.attachmentParam))
                    debugger;
                    //if (delres.data.result === 1) {
                        //this.$success({title: 'success', message: 'The file has been removed'})
                       this.fileList.splice(0, this.fileList.length)
                   // }
                }/* else {
                    // 清空未上传的文件
                    this.$refs.upload.clearFiles()
                }*/
            },
            uploadSuccess(res, file) {
                const attachment = {
                    'attachmentId': res.data[0].cloudID,
                    'fileUrl': res.data[0].path,
                    'fileName': file.name
                };
                this.trf.trfAttachments.push(attachment);
            },
            async downFile(file, fileList){
                let fileListRes = null
                let queryParam={};
                if(file.attachmentId==undefined || file.attachmentId=='' ||file.attachmentId==null){
                    return false;
                }
                queryParam.cloudID=file.attachmentId;
                try {
                    fileListRes = await queryFile(queryParam)
                } catch (e) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: 'Failed to get file',
                        type: 'warning'
                    });
                }
                if (fileListRes == null || this.$lodash.isEmpty(fileListRes.data.data)) {
                    return  false
                }
                window.open(fileListRes.data.data.path)
            },
            async initInvoiceTotal(gid){
                if (this.$route.query.isReportFlag) {
                    let anchorElement = document.getElementById("invoice")
                    if (anchorElement) {
                        anchorElement.scrollIntoView()
                    }
                }

                let queryRes = await getQuotation(gid);
                if (queryRes.data.code === 200) {
                    if (queryRes.data.data !== null && queryRes.data.data.length > 0) {
                        this.invoice.list = queryRes.data.data;
                        this.invoice.paymentCurrency = this.invoice.list[0].paymentCurrency;
                    }
                }
            },
            async saveInvoiceTotal(){
                //开启loading效果
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                let param = this.invoice.list.map(item => {
                  let date = this.$lodash.get(item, 'issuedTime', "");
                    if(!validatenull(date)){
                          date = moment(date).format('YYYY-MM-DD 00:00:00');
                      }else{
                          date = null;
                    }
                    return {
                        id:item.id,
                        trfId:this.trfId,
                        testItemsAmount:this.$lodash.result(item,'testItemsAmount',0),
                        estimatedTax:this.$lodash.result(item,'estimatedTax',0),
                        paymentCurrency:this.$lodash.result(item,'paymentCurrency',""),//this.invoice.paymentCurrency,
                        quotationNo:this.$lodash.get(item, 'quotationNo', ""),
                        referenceNo:this.$lodash.get(item, 'referenceNo', ""),
                        invoiceNo:this.$lodash.get(item, 'invoiceNo', ""),
                        source:this.$lodash.get(item,'source',0),
                        issuedTime:date,
                        //uploadFiles:this.$lodash.get(item, 'uploadFiles', []),
                    }

                })

                let saveRes = await saveQuotation(param)
                loading.close();
                if (saveRes.data.code === 200) {
                    let retunList = saveRes.data.data;
                    for (let i = 0; i < retunList.length; i++) {
                        let uploadFiles = this.invoice.list[i].uploadFiles
                        if (uploadFiles && uploadFiles.length === 1) {
                            let uploadFileObj = uploadFiles[0];
                            if (uploadFileObj) {
                                var attachment={};
                                attachment.object='quotation';
                                attachment.objectId=retunList[i].id;
                                attachment.attachmentId= uploadFileObj.attachmentId;
                                debugger;
                                attachment.fileName=uploadFileObj.fileName;
                                attachment.fileUrl=uploadFileObj.fileUrl;
                               /* let formData = new FormData()
                                formData.append('object','quotation')
                                formData.append('objectID', retunList[i].id)
                                formData.append('attachmentId', uploadFileObj.attachmentId)
                                formData.append('fileName', uploadFileObj.fileName)
                                formData.append('fileUrl', uploadFileObj.fileUrl)*/
                                try {
                                    let uploadFileRes = await saveUploadFile(attachment)
                                    if (uploadFileRes.data.code === 200) {
                                        this.$message({
                                            type: 'success',
                                            message: this.$t('api.success')
                                        });
                                    }
                                }catch (e) {
                                    this.$message.error(this.$t('api.error'));
                                }
                            }
                        }else {
                          this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                          });
                        }
                    }
                    this.initInvoiceTotal(this.trfId)
                }else {
                  this.$message({
                    type: 'error',
                    message: saveRes.data.msg || this.$t('api.success')
                  });
                }

            },
            async delInvoiceTotal(item){
                if (this.invoice.list.length === 1) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: "Please keep a row of data",
                        type: 'warning'
                    });
                    return
                }
                if(item.id){
                    let delRes = await remove(item.id);
                }
                this.invoice.list.splice(this.invoice.list.indexOf(item),1);
                /*let findItem = null
                if (item.tempId) {
                    findItem = this.$lodash.find(this.invoice.list, {tempId: item.tempId});
                } else {
                    findItem = this.$lodash.find(this.invoice.list, {id: item.id});
                    let delRes = await deleteInvoice(item.id)
                    if (delRes.data.status === 200) {
                        if (!this.$lodash.isEmpty(this.$lodash.get(item, 'uploadFiles[0]', null))) {
                            let file = item.uploadFiles[0];
                            //如果是已上传的文件
                            if (this.$lodash.has(file, 'id') && !this.$lodash.isEmpty(file.id)) {
                                // 删除已上传的文件
                                let delres = await deleteFile({key: file.id})
                            }
                        }
                        this.$success({message: "Delete Invoice Success!"})
                    }
                }*/

            },
            addInvoiceTotal(){
                let uuid = this.RandomNumBoth(1,1000);
                this.invoice.list.push(
                    {
                        tempId:uuid,
                        testItemsAmount:'',
                        estimatedTax:'',
                        paymentCurrency:'CNY',
                        quotationStatus:0,
                        source: 1
                    }
                )
            },
            RandomNumBoth:function(Min,Max){
            var Range = Max - Min;
            var Rand = Math.random();
            var num = Min + Math.round(Rand * Range); //四舍五入
            return num;
             },
            currencyChange(val,row){
              row.paymentCurrency = val;
            }

        },
        computed:{
          dateFormatEnums() {
            return dateFormatEnums
          },
            ...mapGetters({
                myErrors: 'myErrors',
                user:'user',
                companyType:'companyType',
                permission:'permission',
            }),
            permissionList() {
                return {
                    quotationSaveBtn:this.vaildData(this.permission['sgs:trf:quotation:save'],false),
                    quotationAddBtn: this.vaildData(this.permission['sgs:trf:quotation:add'],false),
                    quotationUploadBtn: this.vaildData(this.permission['sgs:trf:quotation:upload'],false),
                    quotationDeleteBtn: this.vaildData(this.permission['sgs:trf:quotation:delete'],false),
                    quotationConfirmBtn:this.vaildData(this.permission['sgs:trf:quotation:confirm'],false),
                    quotationRejectBtn:this.vaildData(this.permission['sgs:trf:quotation:reject'],false),
                    quotationCancelBtn:this.vaildData(this.permission['sgs:trf:quotation:cancel'],false),
                };
            }

        },
        props:{
            trfId: String,
            trfNo: String,
            trfFlag:String,
            productLineCode:String,
        }
    }
</script>
<style lang="scss" scoped>

.sgs_table {
  width: 100%;
  border-collapse: collapse;
}

.sgs_table>thead>tr>th {
    text-align: left !important;
}
.action_class1{
  white-space: normal !important;
  .cell{
    white-space: normal !important;
  }
}

.trfQuotation_scrollbar {
  width: 100%;
  overflow-x: auto;

  table{
    width: 100%;
    border-collapse: collapse;

    td{
      line-height: 23px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
    }
  }
}
  .trfQuotation_scrollbar .cell{
    white-space: normal !important;
  }

    .customerUpload /deep/ > div{
        display: inline;
    }

    .customerUpload /deep/ > ul{
        display: inline;
    }
    .customerUpload /deep/ > ul > li{
        display: inline;
    }
    .customerUpload /deep/ > ul > li > a{
        display: inline;
    }
    .el-upload-list__item-name {
      display: inline;
    }

    .el-upload-list__item-status-label {
      line-height: normal;
    }
</style>

