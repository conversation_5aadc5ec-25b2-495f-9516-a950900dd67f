<template>
    <div>
        <el-card shadow="never" class="sgs-box" >
            <el-row>
                <el-form ref="query" :inline="true" :model="query" label-width="0px" size="medium">
                    <!--<el-form-item>
                        <el-select v-model="materialConfigId" clearable
                                @change="selectMaterialConfigChange"
                                style="width: 100%;"
                                :placeholder="$t('customerMaterialConfig.configTemplate')">
                            <el-option v-for="(template,index) in materialConfigData"
                                    :label="template.dffFormName"
                                    :value="template.materialConfigId"></el-option>
                        </el-select>
                    </el-form-item>-->
                    <el-form-item>
                        <el-input v-model="query.trfNo" :placeholder="$t('customerMaterialConfig.applicationNo')"
                                clearable></el-input>
                    </el-form-item>
                    <el-form-item :prop="$t('term.supplier')">
                        <el-input style="width: 100%;" v-model="query.customerName"
                                :placeholder="$t('trf.applicant')"
                                clearable></el-input>
                    </el-form-item>
                    <el-form-item :prop="col.dffFieldCodeFirstLow" v-for="col in queryCols">
                        <el-input style="width: 100%;" v-model="query[col.dffFieldCodeFirstLow]"
                                :placeholder="col.dffFieldMaterialName"
                                clearable></el-input>
                    </el-form-item>
                </el-form>
            </el-row>

            <el-row>
                <el-col :span="24" style="text-align: right">
                    <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                    <el-button type="primary" @click="onReset">{{$t('operation.reset')}}</el-button>
                    <el-button type="primary" v-if="permissionList.downloadTemplateBtn" @click="onDownloadExcel">
                        {{$t('customerMaterialConfig.downloadExcel')}}
                    </el-button>
                    <el-button type="primary" v-if="permissionList.exportExcelBtn" @click="exportExcel">
                        {{$t('customerMaterialConfig.exportExcel')}}
                    </el-button>
                    <el-button type="primary" v-if="permissionList.deletedBtn" @click="onDelete">{{$t('operation.remove')}}
                    </el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="20">
                    <el-button type="primary" v-if="permissionList.uploadBtn" @click="openUpload">
                        {{$t('customerMaterialConfig.openExcel')}}
                    </el-button>
                    <el-button type="primary" v-if="permissionList.newTrfBtn" @click="newTrf">{{$t('wel1.newApplication')}}
                    </el-button>
                    <!--  <el-button type="primary" v-if="permissionList.modifyTemplateBtn" @click="updateTrfTemplateClick">
                        {{$t('customerMaterialConfig.modifyTemplate')}}
                    </el-button>-->
                </el-col>
                <el-col :span="4" style="text-align: right">
                    <el-popover
                            placement="bottom"
                            width="300"
                            height="500"
                            trigger="click">
                        <el-table row-key="dffFieldCodeFirstLow"
                                height="500" :data="isMasterList" @selection-change="masterChange"
                                style="width: 100%" size="medium">
                            <el-table-column type="selection" :reserve-selection="true" width="50"></el-table-column>
                            <el-table-column prop="dffFieldMaterialName" label="Material"></el-table-column>
                        </el-table>
                        <el-button slot="reference" icon="el-icon-s-grid"></el-button>
                    </el-popover>
                </el-col>
            </el-row>
            <el-table
                    class="scrollbar"
                    :row-key="getRowKeys"
                    ref="customerMaterialTable"
                    :data="customerMaterialTableData"
                    style="width: 100%"
                    v-loading="loading"
                    @selection-change="materialChange"
            >
                <el-table-column type="selection" fixed :reserve-selection="true" width="50"></el-table-column>
                <el-table-column label="Application No." prop="trfNo" width="120"></el-table-column>
                <el-table-column label="Template" :show-overflow-tooltip='true' prop="trfTemplateName" width="200">
                    <template slot-scope="scope">
                        <span>{{scope.row.trfTemplateName}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="Applicant" :show-overflow-tooltip='true' prop="customerName" width="200">
                    <template slot-scope="scope">
                        <span>{{scope.row.customerName}}</span>
                    </template>
                </el-table-column>

                <el-table-column label="Buyer" :show-overflow-tooltip='true' prop="customerName" width="200">
                    <template slot-scope="scope">
                        <span>{{scope.row.customerGroupName}}</span>
                    </template>
                </el-table-column>

                <el-table-column :key="col.dffFieldCodeFirstLow" :show-overflow-tooltip='true'
                                v-for="col in cols"
                                width="200"
                                :prop="col.dffFieldCodeFirstLow"
                                :label="col.dffFieldMaterialName">
                </el-table-column>

                <el-table-column label="Report Issued Date" :show-overflow-tooltip='true' prop="reportIssuedDate" width="200">
                  <template slot-scope="scope">
                    <span>{{scope.row.reportIssuedDate}}</span>
                  </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-card>

        <el-dialog :title="title" width="50%" height="80%" :visible.sync="uploadDialog">
            <el-form ref="uploadForm"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     :model="uploadForm" class="sgs-form">

                <!--<el-form-item :label="$t('customerMaterialConfig.materialFrom2')">
                    <el-radio :change="excelFromRadio" v-model="radio" label="1">{{$t('trfList.buyer')}}</el-radio>
                    <el-radio v-model="radio" label="2">{{$t('supplier.label')}}</el-radio>
                </el-form-item>
                <div v-if="show">
                    <el-form-item :label="$t('term.supplier')" prop="customerGroupCode">
                        <el-select clearable filterable v-model="uploadForm.customerGroupCode"
                                   style="width:100%" @change="customerGroupCodeChange">
                            <el-option v-for="(customerGroup,index) in supplierList"
                                       :label="customerGroup.customerNameCN"
                                       :value="customerGroup.customerNumber"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item :label="$t('trf.trfTemplate')" prop="trfTemplateId">
                        <el-select v-model="uploadForm.trfTemplateId" clearable
                                   @change="selectTemplateChange"
                                   style="width: 100%;"
                                   :placeholder="$t('trf.placeholder.template')">
                            <el-option v-for="(template,index) in templateData"
                                       :label="template.templateName"
                                       :value="template.id"></el-option>
                        </el-select>
                    </el-form-item>
                </div>-->


                <el-row>
                    <el-form-item :label="$t('customerMaterialConfig.uploadFile')" prop="customerGroupCode">
                        <el-upload
                                ref="upload"
                                action=""
                                class="upload-demo"
                                :on-change="handleChange"
                                :on-exceed="handleExceed"
                                :on-remove="handleRemove"
                                :on-success="go"
                                :file-list="fileListUpload"
                                :limit="limitUpload"
                                accept=".xlsx,.xls"
                                :auto-upload="false">
                            <el-button size="small" type="primary">{{$t('customerMaterialConfig.choseExcel')}}
                            </el-button>
                        </el-upload>
                    </el-form-item>

                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                   <el-button type="primary" size="small" :loading="btnGuestbookSubmit" @click="go">
                            {{$t('customerMaterialConfig.uploadExcel')}}
                        </el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog :title="$t('customerMaterialConfig.modifyTemplate')" width="50%" height="60%"
                   :visible.sync="updateTrfTemplateDialog">
            <el-form label-width="200px"
                     label-position="left"
                     class="sgs-form"
                     :model="updateMaterialForm"
                     size="medium">
                <el-row>
                    <el-form-item :label="$t('trf.trfTemplate')" prop="trfTemplateId">
                        <el-select v-model="updateMaterialForm.trfTemplateId" clearable
                                   @change="selectTemplateChange1"
                                   style="width: 100%;"
                                   :placeholder="$t('trf.placeholder.template')">
                            <el-option v-for="(template,index) in templateData"
                                       :label="template.templateName"
                                       :value="template.id"></el-option>
                        </el-select>
                    </el-form-item>
                </el-row>
                <!-- <el-row>
                     <el-col >
                         <el-button type="primary" size="small" @click="updateTemplateSubmit">
                             {{$t('operation.submit')}}
                         </el-button>
                     </el-col>
                 </el-row>-->
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" size="small" @click="updateTemplateSubmit">
                            {{$t('operation.submit')}}
                        </el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog :title="$t('wel1.newApplication')" width="50%" height="80%" :visible.sync="newTrfDialog">
            <el-form ref="uploadForm"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     :model="newTrfForm" class="sgs-form">

                <el-form-item :label="$t('customerMaterialConfig.materialFrom2')" v-if="role.isSGS">
                    <el-radio v-model="radio" label="1">{{$t('trfList.buyer')}}</el-radio>
                    <el-radio v-model="radio" label="2">{{$t('supplier.label')}}</el-radio>
                </el-form-item>
                <div v-if="role.isSGS && show">
                    <el-form-item :label="$t('term.supplier')" prop="customerGroupCode">
                        <el-select clearable filterable v-model="newTrfForm.customerGroupCode"
                                   style="width:100%" @change="customerGroupCodeChange">
                            <el-option v-for="(customerGroup,index) in supplierList"
                                       :label="customerGroup.customerNameCN"
                                       :value="customerGroup.customerNumber"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item :label="$t('trf.trfTemplate')" prop="trfTemplateId">
                        <el-select v-model="newTrfForm.trfTemplateId" clearable
                                   @change="selectTemplateChange"
                                   style="width: 100%;"
                                   :placeholder="$t('trf.placeholder.template')">
                            <el-option v-for="(template,index) in templateData"
                                       :label="template.templateName"
                                       :value="template.id"></el-option>
                        </el-select>
                    </el-form-item>
                </div>

            </el-form>
            <template #footer>
                <span class="dialog-footer">
                   <el-button type="primary" size="small" @click="newTrfSubmit">
                            {{$t('operation.confirm')}}
                        </el-button>
                </span>
            </template>
        </el-dialog>


        <el-table :data="showData" stripe style="width: 0px;height:0px" type="hidden" v-loading="listLoading"
                  id="exportTab">
            <el-table-column label="Application No." prop="trfNo" width="120"></el-table-column>
            <el-table-column label="Template Name" prop="trfTemplateName" width="120"></el-table-column>
           <!-- <el-table-column label="Applicant" :show-overflow-tooltip='true' prop="customerName" width="200">
                <template slot-scope="scope">
                    <span v-if="scope.row.customerName !== ''">{{scope.row.customerName}}</span>
                    <span v-if="scope.row.customerName === ''">{{scope.row.customerGroupName}}</span>
                </template>
            </el-table-column>-->
              <el-table-column label="Applicant" :show-overflow-tooltip='true' prop="customerName" width="200">
                <template slot-scope="scope">
                    <span>{{scope.row.customerName}}</span>
                </template>
            </el-table-column>

             <el-table-column label="Buyer" :show-overflow-tooltip='true' prop="customerGroupName" width="200">
                <template slot-scope="scope">
                    <span>{{scope.row.customerGroupName}}</span>
                </template>
            </el-table-column>
            <el-table-column :key="col.dffFieldCodeFirstLow"
                             v-for="col in allCols"
                             :prop="col.dffFieldCodeFirstLow"
                             :label="col.dffFieldMaterialName">
            </el-table-column>
            <el-table-column label="Report Issued Date" :show-overflow-tooltip='true' prop="reportIssuedDate" width="200">
              <template slot-scope="scope">
                <span>{{scope.row.reportIssuedDate}}</span>
              </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
    import {
        getPage,
        remove,
        downloadExcel,
        uploadExcelData,
        uploadOrAddMaterialTrfTemplate,
        exportMaterials,
        newTrfOrAddTrmTemplate,
        reportMaterialPage,
        exportReportMaterials
    } from "@/api/martin/materialListManager";
    import {detail, getConfigList} from "@/api/martin/customerMaterialConfig";
    import {validatenull} from "@/util/validate";
    import {getSupplierPage} from "@/api/customer/relationship";
    import {
        getDictByCode,
        getCustomerGroup,
        getProductLine,
        getDffList,
        getSpecificByProductLineId
    } from "@/api/common/index";
    import {
        getTemplateListNoPage
    } from "@/api/trf/trf";
    import moment from 'moment'
    import {mapGetters} from "vuex";
    import FileSaver from 'file-saver'
    import XLSX from 'xlsx'

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        name: "customerMaterialConfig",
        data() {
            return {
                getRowKeys(row) {
                    return row.id
                },
                loading: false,
                selIds: [],
                selMaterialsData: [],
                materialConfigData: [],
                materialConfig: {},
                customerMaterialTableData: [],
                isMasterList: [],
                allCols: [],//存放所有的 cols
                masterMultipleSelection: [],//存放 master 数据
                materialMultipleSelection: [],//存放 material 数据
                showData: [],//存放 excel 数据
                query: {},
                btnGuestbookSubmit: false,
                fromMaterialList: true,
                title: "",
                cols: [],
                queryCols: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                fileTemp: "",
                limitUpload: 1,
                downloadExcel: "/customer/materials/download-excel",
                materialConfigId: "",
                uploadDialog: false,
                newTrfDialog: false,
                updateTrfTemplateDialog: false,
                updateMaterialForm: {
                    trfTemplateName: '',
                },
                uploadForm: {},
                newTrfForm: {},
                show: false,
                radio: '',
                supplierList: [],
                templateData: [],
                templateDataParam: {
                    customerGroupCode: '',
                    productLineCode: '',
                },
                customerGroupCode: "",
                bossNo: "",
                trfTemplateId: '',
                trfTemplateName: '',
            }
        },
        watch: {
            "radio": function (newVal) {
                if (newVal === "2") {
                    this.show = true;
                } else {
                    this.customerId = "";
                    this.customerNameZh = "";
                    this.show = false;
                }
            }
        },
        mounted() {
        },
        computed: {
            ...mapGetters(["permission", "userInfo", "dimensions", "posts"]),
            permissionList() {
                return {
                    uploadBtn: this.vaildData(this.permission['sgs:customer:material:list:upload'], false),
                    newTrfBtn: this.vaildData(this.permission['sgs:customer:material:list:newTrf'], false),
                    modifyTemplateBtn: this.vaildData(this.permission['sgs:customer:material:list:modifyTemplate'], false),
                    downloadTemplateBtn: this.vaildData(this.permission['sgs:customer:material:downloadTemplate'], false),
                    exportExcelBtn: this.vaildData(this.permission['sgs:customer:material:list:exportExcel'], false),
                    deletedBtn: this.vaildData(this.permission['sgs:customer:material:list:delete'], false),
                };
            },
            role() {
                return {
                    isSGS: this.haseRole('SGSUserRole', 'SgsAdmin') || this.haseRole("SGSUserRole", "SgsLabUser"),
                };
            }
        },
        methods: {
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            //修改Template
            updateTemplateSubmit() {
                debugger;
                //判断是否选择template
                if (validatenull(this.updateMaterialForm.trfTemplateId)) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('trf.templateSel')
                    })
                    return false;
                }
                var materialIds = this.$lodash.split(this.selIds, ',');
                //获取选中数据的ID，再次验证选择的数据是否正确
                if (materialIds != undefined && materialIds.length != 0) {
                    var materials = [];
                    materialIds.find((id) => {
                        var materialObj = {};
                        materialObj.id = id;
                        materialObj.trfTemplateId = this.updateMaterialForm.trfTemplateId;
                        materialObj.trfTemplateName = this.updateMaterialForm.trfTemplateName;
                        materials.push(materialObj);
                    });
                    if (!validatenull(materials)) {
                        uploadOrAddMaterialTrfTemplate(materials).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            //关闭弹框
                            this.updateTrfTemplateDialog = false;
                            this.materialMultipleSelection = [];
                            this.$refs.customerMaterialTable.clearSelection();
                            this.selIds = [];
                            //重新刷新页面
                            this.onLoad(this.page);
                        }).catch(error => {
                            //关闭弹框
                            this.updateTrfTemplateDialog = false;
                        });
                    }
                }
            },
            go() {
                //判断是否选择TRF template模板
                /*if (validatenull(this.trfTemplateId)) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('trf.templateSel')
                    })
                    return false;
                }*/
                this.btnGuestbookSubmit = true;
                /*if (this.radio === "" || this.radio === undefined) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('customerMaterialConfig.materialFrom')
                    })
                    this.btnGuestbookSubmit = false;
                } else {*/
                /*if (this.radio === "1") {
                    this.bossNo = null;
                    this.customerId = null;
                }*/
                if (this.fileTemp) {
                    this.importfxx(this.fileTemp)
                    /*if((this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') || (this.fileTemp.type == 'application/vnd.ms-excel')){
                      this.importfxx(this.fileTemp)
                    } else {
                      this.$message({
                        type:'warning',
                        message:'附件格式错误，请删除后重新上传！'
                      })
                    }*/
                } else {
                    this.$message({
                        type: 'warning',
                        message: this.$t('customerMaterialConfig.materialFile')
                    })
                    this.btnGuestbookSubmit = false;
                }
                //}
            },
            openUpload() {
                if (this.materialConfigId === "" || this.materialConfigId === undefined) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('customerMaterialConfig.chooseTemplate')
                    })
                } else {
                    this.title = this.$t('customerMaterialConfig.openExcel');
                    debugger;
                    this.uploadForm = {};
                    this.uploadDialog = true;
                    this.$refs.upload.clearFiles();
                }
            },
            customerGroupCodeChange(val) {
                debugger;
                this.customerNameZh = '';
                this.bossNo = '';
                this.$set(this.newTrfForm, 'customerName', '');
                this.$set(this.newTrfForm, 'bossNo', '');
                let obj = {};
                obj = this.supplierList.find((item) => {
                    return item.customerNumber === val;
                });
                if (obj != undefined && obj != null) {
                    this.customerNameZh = obj.customerNameCN;
                    this.bossNo = obj.customerNumber
                    this.$set(this.newTrfForm, 'customerName', obj.customerNameCN);
                    this.$set(this.newTrfForm, 'bossNo', obj.customerNumber);
                }
            },
            selectTemplateChange(val) {
                this.trfTemplateId = val;
                let obj = {};
                obj = this.templateData.find((item) => {
                    return item.id === val;
                });
                if (obj != undefined && obj != null) {
                    this.trfTemplateName = obj.templateName;
                    this.$set(this.newTrfForm, 'trfTemplateName', obj.templateName);
                }
            },
            selectTemplateChange1(val) {
                let obj = {};
                obj = this.templateData.find((item) => {
                    return item.id === val;
                });
                if (obj != undefined && obj != null) {
                    this.updateMaterialForm.trfTemplateName = obj.templateName;
                }
            },

            searchTemplateData() {
                let params = {"materialConfigId": this.materialConfigId};
                detail(params).then(res => {
                    const data = res.data.data;
                    this.templateDataParam.productLineCode = data[0].productLineCode;
                    this.templateDataParam.customerGroupCode = data[0].customerGroupCode;
                    var templateParam = {};
                    getTemplateListNoPage(Object.assign(templateParam, this.templateDataParam)).then(res => {
                        const templateData = res.data.data;
                        this.templateData = templateData;
                    });
                });
            },
            newTrfSubmit() {
                debugger;
                if (this.selIds == undefined || this.selIds.length == 0) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customerMaterialConfig.selectMaterial'),
                        type: 'warning'
                    });
                    return false;
                }
                if (validatenull(this.newTrfForm.trfTemplateId)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customerMaterialConfig.hasNoTemplate'),
                        type: 'warning'
                    });
                    return false;
                }
                var customerGroupName = this.selMaterialsData[0].customerGroupName;
                if (this.radio === "1") {//买家建单
                    this.bossNo = null;
                    this.$set(this.newTrfForm, 'customerName',customerGroupName);
                    this.$set(this.newTrfForm, 'bossNo', null);
                }

                //跳转建单界面
                var materialConfigId = this.selMaterialsData[0].materialConfigId;
                if (this.role.isSGS) {
                    this.$router.push({
                        path: '/trf/trfForm',
                        query: {
                            ids: this.selIds,
                            title: 'NEW TRF',
                            actionType: 'material',
                            cid: materialConfigId,
                            customer: encodeURIComponent(JSON.stringify(this.newTrfForm)),
                            trfTemplateId: this.newTrfForm.trfTemplateId,
                            trfTemplateName: this.newTrfForm.trfTemplateName,
                            flag: 1
                        }
                    });
                } else {
                    this.$router.push({
                        path: '/trf/trfForm',
                        query: {
                            ids: this.selIds,
                            title: 'NEW TRF',
                            actionType: 'material',
                            cid: materialConfigId,
                            trfTemplateId: this.newTrfForm.trfTemplateId,
                            trfTemplateName: this.newTrfForm.trfTemplateName,
                            flag: 1
                        }
                    });
                }


                //更新material template以及bossNo等数据后 再新建TRF
                /*  this.$set(this.newTrfForm, 'ids', this.selIds);
                  newTrfOrAddTrmTemplate(this.newTrfForm).then(res => {
                      this.$message({
                          type: "success",
                          message: this.$t('api.success')
                      });
                      //关闭弹框
                      this.newTrfDialog = false;


                  }).catch(error => {
                      //关闭弹框
                      this.newTrfDialog = false;
                  });*/


            },
            newTrf() {
                debugger;
                if (this.selIds == undefined || this.selIds.length == 0) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customerMaterialConfig.selectMaterial'),
                        type: 'warning'
                    });
                    return false;
                }
                var isBindTrf = true;
                this.selMaterialsData.forEach(ele => {
                    if (!validatenull(ele.trfId)) {
                        isBindTrf = false;
                    }
                });
                if (!isBindTrf) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customerMaterialConfig.hasBoundTrf'),
                        type: 'warning'
                    });
                    return false;
                }

                //获取物料配置详情，加载template数据
                getSupplierPage(1, 1000, {"customerGroupCode": this.customerGroupCode}).then(res => {
                    this.supplierList = res.data.data.records
                })
                //给出建单数据选择弹框
                this.title = "New Application"
                this.newTrfDialog = true;
                return false;
                //获取选中的material跳转至new TRF页面
                /*if (this.selIds != undefined && this.selIds.length != 0) {
                    //判断是否存在bossNo不一致的
                    var agreeFlag = true;
                    var isBindTrf = true;
                    var isBindTemplate = true;
                    var bossNo = this.selMaterialsData[0].bossNo;
                    var templateId = this.selMaterialsData[0].trfTemplateId;
                    if (validatenull(templateId)) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: '所选择的Material未配置模板,请先配置模板',
                            type: 'warning'
                        });
                        return false;
                    }
                    this.selMaterialsData.forEach(ele => {
                        if (ele.bossNo != bossNo) {
                            agreeFlag = false;
                        }
                        if (!validatenull(ele.trfId)) {
                            isBindTrf = false;
                        }
                        if (ele.trfTemplateId != templateId) {
                            isBindTemplate = false;
                        }
                    });
                    if (!agreeFlag) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: '所选择的Material非同一客户',
                            type: 'warning'
                        });
                        return false;
                    }
                    if (!isBindTrf) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: '所选择的Material已被绑定TRF',
                            type: 'warning'
                        });
                        return false;
                    }
                    if (!isBindTemplate) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: '所选择的Material非同一个Template配置，请重新选择',
                            type: 'warning'
                        });
                        return false;
                    }

                    var materialConfigId = this.selMaterialsData[0].materialConfigId;
                    this.$router.push({
                        path: '/trf/trfForm',
                        query: {
                            ids: this.selIds,
                            title: 'NEW TRF',
                            actionType: 'material',
                            cid: materialConfigId,
                            flag: 1
                        }
                    });
                } else {
                    this.$notify({
                        title: this.$t('tip'),
                        message: '请至少选择一条物料信息',
                        type: 'warning'
                    });
                }*/
            },
            updateTrfTemplateClick() {
                if (this.selIds != undefined && this.selIds.length != 0) {
                    //判断是否已绑定TRF
                    var isBindTrf = true;
                    var materiaCOnfigId = this.selMaterialsData[0].materialConfigId;
                    this.selMaterialsData.forEach(ele => {
                        if (!validatenull(ele.trfId)) {
                            isBindTrf = false;
                        }
                    });
                    if (!isBindTrf) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('customerMaterialConfig.hasBoundTrf'),
                            type: 'warning'
                        });
                        return false;
                    }
                    //展示选择template窗口
                    this.updateTrfTemplateDialog = true;
                } else {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customerMaterialConfig.selectMaterial'),
                        type: 'warning'
                    });
                }
            },
            onSearch() {
                /*   if (this.materialConfigId === "" || this.materialConfigId === undefined) {
                       this.$message({
                           type: 'warning',
                           message: this.$t('customerMaterialConfig.chooseTemplate')
                       })
                   } else {*/
                this.page.currentPage = 1;
                this.onLoad(this.page);
                //}
            },
            onLoad(page, params = {}) {
                this.loading = true;
                this.query.materialConfigId = this.materialConfigId;
                reportMaterialPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.customerMaterialTableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                    this.loading = false;
                }).catch(error => {
                    this.loading = false;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            formtterDate(row, column) {
                var date = row[column.property];

                if (date == undefined || date == '') {
                    return ''
                }
                ;

                return moment(date).format("YYYY-MM-DD")
            },
            changeStatus(row) {
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                add(modifiedForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            onReset() {
                this.query = {};
                this.$refs['query'].resetFields();
            },
            handleChange(file, fileList) {
                this.fileTemp = file.raw
            },

            handleRemove(file, fileList) {
                this.fileTemp = null
            },
            importfxx(obj) {
                debugger;
                let _this = this;
                // 通过DOM取文件数据
                this.file = obj
                var rABS = false; //是否将文件读取为二进制字符串
                var f = this.file;
                var reader = new FileReader();
                //
                var formData = new FormData();
                var param = {};
                param.materialConfigId = _this.materialConfigId;
                param.customerGroupCode = _this.customerGroupCode;
                param.customerGroupName = _this.customerGroupName;
                param.customerName = _this.customerNameZh;
                /*param.bossNo = _this.bossNo;
                param.customerId = _this.customerId;
                param.trfTemplateId = this.trfTemplateId;
                param.trfTemplateName = this.trfTemplateName;*/
                formData.append("file", this.fileTemp);
                formData.append("param", JSON.stringify(param));
                uploadExcelData(formData).then(res => {
                    _this.uploadDialog = false;
                    if (res.data.data.success === false) {
                        window.open("/api/sgs-mart/customer/materials/download-error-excel?currentTimeMillis=" + res.data.data.currentTimeMillis + "&materialConfigId=" + res.data.data.materialConfigId, '_blank');
                    } else {
                        _this.$message({
                            type: "success",
                            //message: _this.$t('api.success') + /*",update " + res.data.data.updateCount*/ + ", Add " + res.data.data.insertCount + " data "
                            message: _this.$t('api.success') + ", Add " + res.data.data.insertCount + " data. "
                        });
                        _this.onLoad(_this.page);
                    }
                    _this.btnGuestbookSubmit = false;
                }).catch(error => {
                    _this.btnGuestbookSubmit = false;
                })

                // return false;
                //if (!FileReader.prototype.readAsBinaryString) {
                /*FileReader.prototype.readAsBinaryString = function (f) {
                    var binary = "";
                    var rABS = false; //是否将文件读取为二进制字符串
                    var pt = this;
                    var wb; //读取完成的数据
                    var outdata;
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        var bytes = new Uint8Array(reader.result);
                        var length = bytes.byteLength;
                        for (var i = 0; i < length; i++) {
                            binary += String.fromCharCode(bytes[i]);
                        }
                        var XLSX = require('xlsx');
                        if (rABS) {
                            wb = XLSX.read(btoa(fixdata(binary)), { //手动转化
                                type: 'base64'
                            });
                        } else {
                            wb = XLSX.read(binary, {
                                type: 'binary'
                            });
                        }
                        debugger
                        outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);//outdata就是你想要的东西
                        outdata.forEach(item => {
                            item.materialConfigId = _this.materialConfigId;
                            item.customerGroupCode = _this.customerGroupCode;
                            item.customerGroupName = _this.customerGroupName;
                            item.customerName = _this.customerNameZh;
                            item.bossNo = _this.bossNo;
                            item.customerId = _this.customerId;
                            item.trfTemplateId = this.trfTemplateId;
                            item.trfTemplateName = this.trfTemplateName;
                        })
                        console.log(outdata);
                        var param = {};
                        var formData = new FormData();
                        formData.append("customerMaterialConfigs", outdata);
                        formData.append("file", this.fileTemp);
                        uploadExcelData(formData).then(res => {
                            if (res.data.data.success === false) {
                                window.open("/api/sgs-mart/customer/materials/download-error-excel?currentTimeMillis=" + res.data.data.currentTimeMillis + "&materialConfigId=" + res.data.data.materialConfigId, '_blank');
                            } else {
                                _this.uploadDialog = false;
                                _this.$message({
                                    type: "success",
                                    //message: _this.$t('api.success') + /!*",update " + res.data.data.updateCount*!/ + ", Add " + res.data.data.insertCount + " data "
                                    message: _this.$t('api.success') + ", Add " + res.data.data.insertCount + " data. "
                                });
                                _this.onLoad(_this.page);
                            }
                            _this.btnGuestbookSubmit = false;
                        })
                        this.da = [...outdata]
                        let arr = []
                        this.da.map(v => {
                            let obj = {}
                            obj.code = v['设备ID']
                            obj.type = v['设备型号']
                            arr.push(obj)
                        })
                        return arr
                    }
                    reader.readAsArrayBuffer(f);
                }

                if (rABS) {
                    reader.readAsArrayBuffer(f);
                } else {
                    reader.readAsBinaryString(f);
                }*/
            },
            onDelete() {
                if (this.materialMultipleSelection.length === 0) {
                    this.$message({
                        type: "info",
                        message: this.$t('customerMaterialConfig.needChooseOne')
                    });
                } else {
                    this.$confirm(this.$t('operation.confirmDelete'), {
                        confirmButtonText: this.$t('operation.confirm'),
                        cancelButtonText: this.$t('operation.cancel'),
                        type: "warning"
                    }).then(() => {
                        remove(this.materialMultipleSelection).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.onLoad(this.page);
                            this.materialMultipleSelection = [];
                            this.$refs.customerMaterialTable.clearSelection();
                        });
                    })
                }

            },
            exportExcel() {
                if (this.materialConfigId === "" || this.materialConfigId === undefined) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('customerMaterialConfig.chooseTemplate')
                    })
                } else {
                    let params = {}
                    exportReportMaterials(1, 3000, Object.assign(params, this.query)).then(res => {
                        debugger;
                        this.showData = res.data.data;
                        this.$nextTick(() => {
                            /* generate workbook object from table */
                            var xlsxParam = {raw: true} // 导出的内容只做解析，不进行格式转换
                            var wb = XLSX.utils.table_to_book(document.querySelector('#exportTab'), xlsxParam)
                            console.log(wb);
                            /* get binary string as output */
                            var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'array'})
                            try {
                                FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), 'Material Data.xlsx')
                            } catch (e) {
                                if (typeof console !== 'undefined') {
                                    console.log(e, wbout)
                                }
                            }
                            return wbout
                        })
                    });
                }
            },
            onDownloadExcel() {
                if (this.materialConfigId === "" || this.materialConfigId === undefined) {
                    this.$message({
                        type: 'warning',
                        message: this.$t('customerMaterialConfig.chooseTemplate')
                    })
                } else {
                    window.open("/api/sgs-mart/customer/materials/download-excel?materialConfigId=" + this.$route.query.materialConfigId, '_blank');
                }
            },

            // 修改页大小
            handleSizeChange(val) {
                this.dffSize = val;
            },
            // 修改页码
            handleCurrentChange(val) {
                this.dffPage = val;
            },
            masterChange(val) {
                this.masterMultipleSelection = val;
                // 现删除所有 isMaster2 标记为1的
                for (var i = 0; i < this.cols.length; i++) {
                    if (this.cols[i].isMaster2 === 1) {
                        this.cols.splice(i--, 1)
                    }
                }
                // 循环补充全部的，这样可以保证试试同步，不用再写删除了
                val.forEach(item => {
                    item.isMaster2 = 1;
                    this.cols.push(item);
                })
            },
            materialChange(val) {
                debugger;
                this.materialMultipleSelection = val;
                this.selMaterialsData = val;
                let ids = [];
                val.forEach(ele => {
                    ids.push(ele.id);
                });
                this.selIds = ids.join(",");
            },
            selectMaterialConfigChange(val) {
                this.customerMaterialTableData = [];
                this.query = {};
                this.$refs['query'].resetFields();
                this.initCols();
            },
            initCols() {
                this.title = this.$t('customerMaterialConfig.title.edit');
                let params = {"materialConfigId": this.materialConfigId};
                this.cols = [];
                this.isMasterList = [];
                this.allCols = [];
                this.queryCols = [];
                detail(params).then(res => {
                    this.customerGroupCode = res.data.data[0].customerGroupCode
                    this.customerGroupName = res.data.data[0].customerGroupName
                    //获取后台数据付给页面，并打开
                    res.data.data.forEach(item => {
                        if (item.isMaster === 1 && item.isMaterial === 1) {
                            this.cols.push(item)
                        }
                        if (item.isMaterial === 1 && item.isMaster !== 1) {
                            this.isMasterList.push(item)
                        }
                        if (item.isMaterial === 1) {
                            this.allCols.push(item);
                        }
                    });
                    console.log(this.isMasterList);
                    res.data.data.forEach(item => {
                        if (item.isSearch === 1) {
                            this.queryCols.push(item)
                        }
                    });
                    this.onLoad(this.page);
                });
            },
            /* initLoadMaterialConfig(params = {}) {
                 params.customerGroupCode = this.userInfo.customerGroupCode
                 getConfigList(Object.assign(params, this.materialConfig)).then(res => {
                     this.materialConfigData = res.data.data;
                     //如果只有一个，默认选中这个
                     debugger
                     if (this.materialConfigData.length === 1) {
                         this.materialConfigId = this.materialConfigData[0].materialConfigId;
                         this.initCols();
                     }
                     var materialConfigId = this.$route.query.materialConfigId;
                     if (materialConfigId != null && materialConfigId != '' && materialConfigId != undefined) {
                         this.fromMaterialList = true;
                         this.materialConfigId = materialConfigId;
                         this.queryCols = [];
                         this.customerMaterialTableData = [];
                         this.initCols();
                     }
                 });
             },*/
        },
        created() {
            this.materialConfigId = this.$route.query.materialConfigId;
            //this.initLoadMaterialConfig();
            this.initCols();
            this.onSearch();
            this.searchTemplateData();
        }

    }
</script>

<style scoped>
    .inline-block {
        display: inline-block;
        padding-left: 3%;
        padding-right: 3%;
    }
</style>
