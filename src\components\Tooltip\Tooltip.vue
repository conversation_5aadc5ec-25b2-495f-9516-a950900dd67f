<template>
    <el-tooltip :disabled="isTooltipDisabled" :content="props.content" :placement="props.placement ?? 'top'"
        popper-class="content-tooltip-popper">
        <slot name="content"></slot>
    </el-tooltip>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
const isTooltipDisabled = ref(true)

onMounted(() => {
    // Access the tooltip element
    const el = document.getElementById(props.id)
    if (el) {
        if (el.scrollWidth > el.clientWidth) {
            isTooltipDisabled.value = false
        } else if (el.scrollWidth === el.clientWidth) {
            if (el.scrollHeight > el.clientHeight) {
                isTooltipDisabled.value = false
            }
        }
    }
})

defineOptions({
    name: 'ContentTooltip'
})

const props = defineProps<{
    content: string,
    id: string,
    placement?: 'top' | 'bottom' | 'left' | 'right'
}>()

</script>
<style lang="scss">
/* Using non-scoped styles to affect tooltip popper */
.content-tooltip-popper {
    max-width: 500px !important;
    word-break: break-word;
    white-space: normal;
    line-height: 1.5;
    padding: 8px 12px;
}
</style>
