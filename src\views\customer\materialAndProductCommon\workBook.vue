<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_material_product_workBook" id="sgs_smart_material_product_workBook">
            <el-row v-if="editRule">
                <el-col :span="12">
                    <section>
                        <h4>&nbsp;</h4>
                    </section>
                </el-col>
                <el-col :span="12" style="text-align: right">
                    <el-button type="text" @click="addWorkBook"><i class="el-icon-plus"></i> Add</el-button>
                </el-col>
            </el-row>
            <el-table :data="dataList" border fit ref="workbookTable" size="mini">
                <el-table-column type="index" width="60px">
                    <template slot="header">#</template>
                </el-table-column>
                <el-table-column label="Workbook" prop="fileName" >
                    <template slot-scope="{row}">
                        <el-button type="text" @click="downloadFile(row)" :disabled="false">
                            {{row.fileName}}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="Upload By" prop="createBy"></el-table-column>
                <el-table-column label="Upload Time" prop="createTime"></el-table-column>
                <el-table-column label="Action" prop="" width="150" fixed="right">
                    <template slot-scope="{row}">
                        <el-button
                                    v-if="!createNew"
                                   :disabled="false"
                                   :icon="getCommentIcon(row)"
                                   style="padding:2px 5px"
                                   type="text"
                                   @click="openComment(row.id)"
                        ></el-button>
                        <el-popconfirm
                                confirm-button-text='Delete'
                                cancel-button-text='Cancel'
                                icon="el-icon-info"
                                icon-color="red"
                                title="Delete the data?"
                                @confirm="deleteFile(row)"
                        >
                            <el-button
                                    slot="reference"
                                    icon="el-icon-close"
                                   type="text"
                                   v-if="editRule"
                                   style="padding:2px 5px"></el-button>
                        </el-popconfirm>
                        <!--<el-button v-if="showApproved" :disabled="false" icon="el-icon-chat-square" style="padding:2px 5px" type="text"></el-button>
                        <el-button v-if="showApproved" :disabled="false" icon="el-icon-success" style="color: #00d26a" type="text"></el-button>
                        <el-button v-if="showApproved" :disabled="false" icon="el-icon-circle-close" type="text"></el-button>-->
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <chat-view
                v-if="showChat"
                :object-id="chatObjectId"
                object-type="workbooks"
                @cancelDia="showChat=false"
        ></chat-view>
        <sgs-batch-upload
                v-if="isLoadUpload"
                title="Work Book"
                append-to-body
                :systemID="1"
                :limit="5"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :handle-upload-success="uploadSuccess"
                :handle-upload-error="uploadError"
                ref="batchUpload"
                upload-url="/api/sgsapi/FrameWorkApi/file/doUpload"
                accept=".pdf,.docx,.doc,.xlsx,.xls,.csv"
                :attachment-type-options="[]"
                attachment-type-default-value=""
                :file-max-sizes="20">
        </sgs-batch-upload>

    </basic-container>
</template>

<script>
    import sampleApi from '@/api/newSamples'
    import protocolApi from '@/api/protocol/protocol'
    import ChatView from "./chatView";
    export default {
        name: "workBook",
        data() {
            return {
                pageLoading: false,
                showChat: false,
                dataList:[],
                isLoadUpload:false,
                chatObjectId:'',
                batchUploadObj:{
                    type:'',
                    uploadTitle:'',
                    accept: '.txt,.ppt,.pptx,.xls,.xlsx,.doc,.docx,.png,.jpg,.jpeg,.pdf',
                    attachmentTypeOptionsForUpload:[],
                    attachmentTypeDefaultValue:''
                }
            }
        },
        methods: {
            openComment(rowId){
                this.chatObjectId = rowId;
                this.showChat = true;
            },
            initPage() {
                if(!this.objectId){
                    return;
                }
                let param = {
                    objectId:this.objectId,
                    object:'productWorkBook',
                    current:1,
                    size:1000
                }
                this.pageLoading = true;
                protocolApi.workbook(param).then(res=>{
                    this.pageLoading = false;
                    if(res.status==200 && res.data && res.data.data && res.data.data.records){
                        let dataList = res.data.data.records;
                        dataList.forEach(da=>{
                            da['edit'] = false;
                        })
                        this.dataList = dataList;
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                })
            },
            uploadSuccess(data){
                console.log("上传成功之后的data",data);
                this.$refs.batchUpload.close();
                this.isLoadUpload = false;
                if(data){
                    let objId = this.objectId;
                    let fileList = (data.data || []).map(item =>{
                        const attachment = {
                            'object': 'productWorkBook',
                            'objectId': objId,
                            'attachmentId': item.id,
                            'fileName': item.attachmentName+"."+item.suffixes,
                            'fileUrl': item.path,
                            'fileType': 'File',
                            'languageId':1
                        }
                        return attachment;
                    });
                    sampleApi.saveAttachment(fileList).then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            //this.$notify.success("Save file success")
                            this.initPage();
                        }else{
                            this.$notify.error("Save fail error")
                        }
                    })
                }

            },
            downloadFile(row){
                let {fileUrl} = row;
                sampleApi.fileApi.downloadFile(fileUrl).then(res=>{
                    let downloadURL = res.data.data;
                    if(downloadURL){
                        window.open(downloadURL, '_blank')
                    }
                })
            },
            deleteFile(row){
                let {id} = row;
                sampleApi.fileApi.deleteAttrById(id).then(res=>{
                    if(res.status==200){
                        this.$notify.success("Success");
                        let deleteIndex = this.dataList.findIndex(da=>da.id==id);
                        this.dataList.splice(deleteIndex,1);
                    }else{
                        this.$notify.error("Delete fail");
                    }
                }).catch(err=>{
                    this.$notify.error("Delete fail");
                })
            },
            uploadError(){},
            addWorkBook(){
                this.isLoadUpload = false;
                this.$nextTick(()=>{
                    this.isLoadUpload = true;
                    this.$nextTick(()=>{
                        this.$refs.batchUpload.open();
                    })
                })
            },
            getCommentIcon(rows){
                let {commentsNum} = rows;
                //没有对话信息
                if(!commentsNum || commentsNum.length==0){
                    return 'el-icon-chat-square';
                }else {
                    return 'el-icon-s-comment';
                }
            },
        },
        mounted() {
            this.initPage();
        },
        created() {
        },
        watch: {},
        computed: {},
        props: {
            showApproved:{
                type:Boolean,
                default(){
                    return false
                }
            },
            objectId:{
                type:String,
                default(){
                    return ''
                }
            },
            editRule:{
                type:Boolean,
                default(){
                    return true
                }
            },
            createNew :{
                type:Boolean,
                default(){
                    return false
                }
            }
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {ChatView}
    }
</script>

<style lang="scss">
    .sgs_smart_material_product_workBook {
        font-family: 'Arial' !important;
        background: #fff;
    }
</style>