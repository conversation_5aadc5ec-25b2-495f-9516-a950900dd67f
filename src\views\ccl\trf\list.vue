<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.trfList')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <el-row>
            <el-col :span="16">
                <h1 class="top-title">{{ $t('navbar.trfList') }}</h1>
            </el-col>
            <el-col :span="8">
                <div class="trf-o-btn text-right">
                    <el-button v-if="permissionList.cclTrfAddBtn" @click="addTrf()" class="line-btn">
                        <img style="vertical-align: text-bottom;" src="/img/icon/addTrf.png" /> {{$t('trf.createtrf')}}
                    </el-button>
                </div>
            </el-col>
        </el-row>
        <el-card shadow="never" class="box-card">
            <el-row>
                <el-form :inline="true" :model="query" size="medium" label-width="200px">
                    <el-row>
                        <el-form-item>
                            <el-input :placeholder="$t('trfList.query.searchFor')"
                                      v-model.trim="query.queryValue"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                    clearable>
                                    <i slot="prefix" class="el-input__icon el-icon-search" style="left: -10px;position: relative;"></i>
                                </el-input>
                        </el-form-item>
                        <el-form-item>
                            <!-- <el-date-picker
                                class="filter-date"
                                :value="[query.startDate, query.endDate]"
                                @input="val => updateStartDate(val)"
                                type="daterange"
                                format="yyyy-MM-dd"
                                range-separator="-"
                                :unlink-panels="true"
                                :clearable="true"
                                :start-placeholder="$t('trfList.query.selStartDate')"
                                :end-placeholder="$t('trfList.query.selEndDate')">
                            </el-date-picker> -->

                            <el-col :span="11">
                              <el-date-picker
                                  class="filter-date"
                                  :value="[query.startDate, query.endDate]"
                                  type="daterange"
                                  format="yyyy-MM-dd"
                                  range-separator="-"
                                  :unlink-panels="true"
                                  :clearable="true"
                                  @input="val => updateDate(val)"
                                  :start-placeholder="$t('trfList.query.selStartDate')"
                                  :end-placeholder="$t('trfList.query.selEndDate')">
                              </el-date-picker>
                            </el-col>
                        </el-form-item>
                        <el-form-item class="date-group">
                            <!--  <el-button type="primary" @click="onSubmit">Search</el-button>-->
                            <el-button @click="selectedDate(1, 'w')"> {{$t('trfList.query.oneWeek')}}</el-button>
                            <el-button @click="selectedDate(1, 'M')"> {{$t('trfList.query.oneMonth')}}</el-button>
                            <el-button @click="selectedDate(1, 'y')"> {{$t('trfList.query.oneYear')}}</el-button>
                            <el-button v-if="permissionList.trfDownLoadBtn" :loading="downLoading"  @click="exportExcel"> {{$t('operation.export')}}</el-button>
                        </el-form-item>
                        <el-form-item style="padding-top: 5px;">
                            <el-checkbox :value="checkedTimeOutTrf" @change="(val) => checkTimeOutHandle(val)">
                                {{$t('trfList.query.timeOutTrf')}}
                            </el-checkbox>
                        </el-form-item>
                        <reset-button @click="clear"></reset-button>
                    </el-row>
                </el-form>
            </el-row>
            <el-row>
                <!-- <el-row align="right" style="margin: 5px 0px">
                    <el-button type="primary" v-if="permissionList.cclTrfAddBtn" icon="el-icon-edit" size="medium"
                            @click="addTrf()">{{$t('trf.createtrf')}}
                    </el-button>
                </el-row> -->
                <el-table ref="labTable" class="ccl_trf_table_scrollbar" :row-key="getRowKeys" :row-class-name="getRowClassName"
                        v-loading="loading" fixed
                        :element-loading-text="$t('loading')"
                        :data="trfData" style="width: 100%">
                    <el-table-column type="expand" class-name="expand" width="20">
                        <template slot-scope="scope">
                            <div>
                                <el-row v-if="scope.row.reports" v-for="(item, index) in scope.row.reports" :key="index">
                                    <el-form label-position="right" inline :class="{'demo-table-expand': true}">
                                        <el-form-item label="Report No:">
                                            <span>{{item.reportNo}}</span>
                                            <span>
                                                <i @click="downloadReport(item.cloudId)"  v-if="item.cloudId" title="Download Report" style="cursor:pointer;color: red"
                                                class="el-icon-document"></i>
                                            </span>
                                            <span :style="{color: getReportColor(item.conclusion), 'margin-left': '30px'}">
                                            {{item.conclusion}}
                                        </span>
                                        </el-form-item>
                                    </el-form>
                                </el-row>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column align="left" label="Job Status" width="160">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.jobStatus')}}</div>
                            <el-select size="small" v-model="query.trfStatus" @change="trfStatusChange" clearable>
                                <el-option :value="null" :label="$t('trfStatus.all')"></el-option>
                                <el-option :value="1" :label="$t('trfStatus.draft')"></el-option>
                                <el-option :value="2" :label="$t('trfStatus.submitted')"></el-option>
                                <el-option :value="3" :label="$t('trfStatus.preparation')"></el-option>
                                <el-option :value="4" :label="$t('trfStatus.testing')"></el-option>
                                <el-option :value="5" :label="$t('trfStatus.completed')"></el-option>
                                <el-option :value="6" :label="$t('trfStatus.cancel')"></el-option>
                            </el-select>
                        </template>
                        <template slot-scope="scope">
                            <div :data-row-key="scope.row.id"
                                :key="scope.row.id"
                                name="expand"
                            >
                                <TRFStatus :status="scope.row.trfStatus" :reason="scope.row.pendingReason"/>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="trfNo" align="left" label="TRF No" width="160">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.trfNo')}}</div>
                            <el-input size="small" :value="query.trfNo" :placeholder="$t('trfList.trfNo')"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                      @input="val => query.trfNo=val" clearable />
                        </template>
                        <template slot-scope="scope">
                            <i v-show="scope.row.trfStatus === 2 && scope.row.isSubmissionTimeout===1"
                            class="el-icon-alarm-clock"
                            title="Submission Timeout" style="cursor:pointer;color: red"></i>
                            <a @click="cclTrfDetailClick(scope.row)" style="color: #FF6600;font-size: 14px;cursor:pointer">{{scope.row.trfNo}}</a>
                            <!--<router-link class="el-link el-link&#45;&#45;primary is-underline" target="_blank"
                                        :to="{ path:'/ccl/trf/trfDetail',
                            query: {id: scope.row.id,title:scope.row.trfNo,flag: 2, hash: new Date().getTime(),actionType:'detail'}}">
                                {{scope.row.trfNo}}
                            </router-link>-->
                        </template>
                    </el-table-column>
                <!-- <el-table-column align="center" label="Retest" prop="isRetestFlag" :formatter="retestFormtter"
                                    width="150">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.retest')}}</div>
                            <el-select size="small" v-model="query.isRetestFlag" clearable>
                                <el-option :value="2" :label="$t('common.yes')"></el-option>
                                <el-option :value="1" :label="$t('common.no')"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>-->
                    <el-table-column align="left" prop="buyerCustomerGroupName" :show-overflow-tooltip='true' label="Buyer" width="250">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.buyer')}}</div>
                            <el-input size="small" :value="query.buyerCustomerGroupName" :placeholder="$t('trfList.buyer')"
                                    @input="val => query.buyerCustomerGroupName=val"
                                    @keyup.enter.native="searchTrfList"
                                    @clear="searchTrfList"
                                    clearable />
                        </template>
                    </el-table-column>
                    <el-table-column align="left" prop="customerNameEn" :show-overflow-tooltip='true'  width="250">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.applyCustomer')}}</div>
                            <el-input size="small" :value="query.applicationNameEn" :placeholder="$t('trfList.applyCustomer')"
                                      @input="val => query.applicationNameEn=val"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                      clearable />
                        </template>
                    </el-table-column>
                    <el-table-column align="left" prop="trfSubmissionDate" label="TRF Submitted Date"
                                    :formatter="formtterDate" width="200">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.submitDate')}}</div>
                            <el-date-picker class="date-input" size="small" style="width:100%;padding-left:0px"
                                            type="date"
                                            format="yyyy-MM-dd"
                                            :placeholder="$t('trfList.submitDate')"
                                            clearable
                                            :value="query.submitDateStr"
                                            @input="(val) => changeSubmissionDateDateQuery(val)" />
                        </template>
                    </el-table-column>

                    <el-table-column align="left" prop="sampleReceiveDate" label="Sample Receive Date"
                                    :formatter="formtterDate" width="200">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.sampleDate')}}</div>
                            <el-date-picker class="date-input" size="small" style="width:100%;padding-left:0px"
                                            type="date"
                                            format="yyyy-MM-dd"
                                            :placeholder="$t('trfList.sampleDate')"
                                            clearable
                                            :value="query.sampleReceiveDateStr"
                                            @input="(val) => sampleReceiveDateQuery(val)" />
                        </template>
                    </el-table-column>
                    <el-table-column align="left" prop="contactName" label="contact name" :show-overflow-tooltip='true' width="160">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.applyContact')}}</div>
                            <el-input size="small" :value="query.contactName" :placeholder="$t('trfList.applyContact')"
                                      @input="val => query.contactName=val"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                      clearable />
                        </template>
                    </el-table-column>
                    <el-table-column align="left" prop="labName" label="Lab Name" :show-overflow-tooltip='true' width="200">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.labName')}}</div>
                            <el-input size="small" :value="query.labName" :placeholder="$t('trfList.labName')"
                                      @input="val => query.labName=val"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                      clearable />
                        </template>
                    </el-table-column>
                    <el-table-column align="left" prop="productDescription" :label="$t('trfList.productDescription')" :show-overflow-tooltip='true' width="200">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.productDescription')}}</div>
                            <el-input size="small" :value="query.productDescription" :placeholder="$t('trfList.productDescription')"
                                      @input="val => query.productDescription=val"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                      clearable />
                        </template>
                    </el-table-column>
                    <el-table-column align="left" prop="newReportNo" :label="$t('trfList.reportNo')" width="200">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.reportNo')}}</div>
                            <el-input size="small" :value="query.reportNo" :placeholder="$t('trfList.reportNo')"
                                      @input="val => query.reportNo=val"
                                      @keyup.enter.native="searchTrfList"
                                      @clear="searchTrfList"
                                      clearable />
                        </template>
                    </el-table-column>

                    <el-table-column align="left" label="Job Status" prop="reportConclusion" width="160">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('trfList.conclusion')}}</div>
                            <el-select v-model="query.reportConclusion"
                                        :placeholder="$t('trfList.conclusion')"
                                        @change="trfStatusChange"
                                        clearable>
                                <el-option value="All" label="All"></el-option>
                                <el-option value="Pass" label="Pass"></el-option>
                                <el-option value="Fail" label="Fail"></el-option>
                                <el-option value="See Result" label="See Result"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <!--<el-table-column align="left"  prop="createTime" label="TRF Create Date" :formatter="formtterDate" width="300"></el-table-column>-->
                    <el-table-column fixed="right" :label="$t('operation.title')" width="180" align="left">
                        <template slot-scope="scope">
                            <!--<i @click="copyTRF(scope.row.id)" class="el-icon-copy-document" :title="$t('trfList.copyTrf')"
                            style="cursor:pointer"></i>-->
                            <el-button v-if="permissionList.copyCclTrfBtn &&   userInfo.userMgtId ==  scope.row.createUserId"
                                    @click="copyTRF(scope.row)"
                                    type="text" size="small" icon="el-icon-copy-document">
                                {{$t('trfList.copyTrf')}}
                            </el-button>
                            <el-button :disabled="scope.row.trfStatus > 2" v-if="permissionList.cclTrfEditBtn &&  userInfo.userMgtId == scope.row.createUserId"
                                    @click="editTrf(scope.row)"
                                    type="text" size="small" icon="el-icon-edit">
                                {{$t('operation.modify')}}
                            </el-button>
                            <!--  <i v-if="showPDFIcon(scope.row)" @click="downloadReport(scope.row)" class="el-icon-document" title="Download Report" style="cursor:pointer;color: red"></i>-->
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>

            <el-table :data="excelData" stripe style="width: 0px;height:0px" type="hidden"  id="exportTable" >
                <el-table-column label="Job Status"  prop="trfStatus" width="120" :formatter="trfStatusFormatter"></el-table-column>
                <el-table-column label="Trf No"  prop="trfNo" width="120"></el-table-column>
            <!--  <el-table-column label="Is Retest"  prop="isRetestFlag" :formatter="retestFormtter"></el-table-column>-->
                <el-table-column label="Buyer"  prop="buyerCustomerGroupName" ></el-table-column>
                <el-table-column label="Appliacant" prop="customerNameEn" ></el-table-column>
                <el-table-column label="TRF Submitted Date" prop="trfSubmissionDate" :formatter="formtterDate" ></el-table-column>
                <el-table-column label="Sample Receive Date" prop="sampleReceiveDate" :formatter="formtterDate" ></el-table-column>
                <el-table-column label="Contact Name" prop="contactName" ></el-table-column>
                <el-table-column label="Lab Name" prop="labName" ></el-table-column>
                <el-table-column label="Product Description" prop="productDescription" ></el-table-column>
                <el-table-column label="Report No" prop="newReportNo" ></el-table-column>
                <el-table-column label="Overall Conclusion" prop="reportConclusion" ></el-table-column>
            </el-table>
        </el-card>
    </basic-container>
</template>


<script>
    import {downLoadFile, exportCclList, getCclListV2} from "@/api/trf/trf";
    import {validatenull} from "@/util/validate";
    import moment from 'moment'
    import {mapGetters} from "vuex";
    import XLSX from "xlsx";
    import FileSaver from "file-saver";
    import _ from "lodash";
    import resetButton from "@/components/resetButton/resetButton.vue";

    export default {
        name: "trfList",
        components: {
            TRFStatus: resolve => require(['../../../components/trf/TRFStatus'], resolve),
            resetButton
        },
        created() {
            console.log(this.userInfo.userMgtId);
            if (this.$route.query.from == 0) {
                this.query.trfStatus = parseInt(this.$route.query.trfStatus);
                this.query.startDate = this.$route.query.startDate;
                this.query.endDate = this.$route.query.endDate;
                //this.onLoadFromWel();
            } else if (this.$route.query.from == 2) {
                //首页搜索
                this.query.queryValue = this.$route.query.queryValue;
            } else if(this.$route.query.from == 1){
                if(this.$route.query.reportResult=='all'){
                    this.query.reportConclusion=null;
                }else{
                    this.query.reportConclusion = this.$route.query.reportResult;
                }

            }
            this.selectedDate(1, 'M');
        },

        data() {
            return {
                downLoading:false,
                excelData:[],
                loading: false,
                trfData: [],
                selectedVal: [],
                dffTemplateData: [],
                checkedTimeOutTrf: false,
                getRowKeys(row) {
                    return row.id
                },

                queryForm: {
                    trfNo: '',
                    startDate: '',
                    endDate: '',
                    isSubmissionTimeout: null,
                    trfStatus: '',
                },
                query: {
                    queryValue: '',
                    applicationNameEn:'',
                    buyerCustomerGroupName: '',
                    trfStatus: null,
                    startDate: null,
                    endDate: null,
                    trfNo: '',
                    reportNo: '',
                    productDescription:'',
                    reportConclusion:'',
                    submitDateStr: null,
                    isSubmissionTimeout: null,
                    sampleReceiveDateStr: null,
                    labName: '',
                    contactName: '',
                    isRetestFlag: null,
                },
                data: [],
                trfParamForm: {
                    startDate: '',
                    endDate: '',
                },
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                pickerOptions1: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                }
            }
        },
        computed: {
            ...mapGetters(["userInfo","permission"]),
            permissionList() {
                return {
                    cclTrfAddBtn: this.vaildData(this.permission['sgs:cclTrfList:addCclTrf'], false),
                    trfDownLoadBtn: this.vaildData(this.permission['sgs:cclTrfList:downLoadCclTrf'], false),
                    cclTrfEditBtn: this.vaildData(this.permission['sgs:cclTrfList:editCclTrf'], false),
                    copyCclTrfBtn: this.vaildData(this.permission['sgs:cclTrfList:copyCclTrf'], false),
                };
            }
        },
        watch: {},
        methods: {
             cclTrfDetailClick(row){
                let hash = new Date().getTime()+'';
                window.open('/#/ccl/trf/trfDetail?id='+row.id+'&title='+row.trfNo+'&flag=2&hash='+hash+'&actionType=detail&signature='+row.signature+'&trfNo='+row.trfNo, '_blank');
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.onLoad(this.page);
            },
            getRowClassName({row, rowIndex}) {
                if (row.reports.length == 0) {
                    return 'row-expand-cover';
                }
            },
            downloadReport(cloudId) {
                if(cloudId!='' && cloudId!=null){
                    downLoadFile(cloudId).then(res => {
                       var pdfUrl =  res.data.data;
                       this.downLoadReportOts(pdfUrl);
                        /*this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });*/
                    }, error => {
                        this.$message.error(this.$t('api.error'));
                    });
                }
                //window.open(pdfUrl, '_blank')
            },
            downLoadReportOts(pdfUrl){
                window.open(pdfUrl, '_blank')
            },
            getReportColor(reportConclusion) {
                if (reportConclusion === 'Pass') {
                    return 'green'
                } else if (reportConclusion === 'Fail') {
                    return 'red'
                } else {
                    return null
                }
            },
            copyTRF(row) {
                this.$router.push({path: '/ccl/trf/trfDetail', query: {id: row.id, title:'Copy TRF',flag:2, actionType: 'copy',trfNo:row.trfNo,signature:row.signature}});
            },
            retestFormtter(row, column) {
                var reTest = row[column.property];
                var reTestStr = this.$t('common.yes');
                if (reTest == 1) {
                    reTestStr = this.$t('common.no');
                }
                return reTestStr;
            },
            formtterDate(row, column) {
                var date = row[column.property];

                if (date == undefined || date == '') {
                    return ''
                }
                ;

                return moment(date).format("YYYY-MM-DD")
            },
            trfStatusFormatter(row, column){
                var trfStatus = row[column.property];
                var statusName="Drafting";
                switch (trfStatus) {
                    case 2:
                        statusName='Submitted'
                        break
                    case 3:
                        statusName='Application Accepted'
                        break
                    case 4:
                        statusName='Testing'
                        break
                    case 5:
                        statusName='Report Issued'
                        break
                    case 6:
                        statusName='Cancel'
                        break
                    default:
                        statusName="Drafting";
                        break
                }

                return statusName;
            },
            clear() {
                //this.query = {};
                this.query.queryValue = '';
                this.query.buyerCustomerGroupName = '';
                this.query.trfStatus = null;
                this.query.startDate = '';
                this.query.endDate = '';
                this.query.trfNo = '';
                this.query.reportNo = '';
                this.query.productDescription='';
                this.query.submitDateStr = null;
                this.query.sampleReceiveDateStr = null;
                this.query.labName = '';
                this.query.labName = '';
                this.query.contactName = '';
                this.onLoad(this.page);
            },
            async trfStatusChange() {
              this.page.currentPage = 1;
              await this.searchTrfList();
            },
            async changeSubmissionDateDateQuery(val) {
                let trfSubmissionDate = null
                if (val) {
                    trfSubmissionDate = moment(val).format('YYYY-MM-DD')
                }
                this.query.submitDateStr = trfSubmissionDate;
                this.page.currentPage = 1;
                await this.searchTrfList();
            },
            async sampleReceiveDateQuery(val) {
                let sampleReceiveDate = null
                if (val) {
                    sampleReceiveDate = moment(val).format('YYYY-MM-DD')
                }
                this.query.sampleReceiveDateStr = sampleReceiveDate;
                this.page.currentPage = 1;
                await this.searchTrfList();
            },
            async updateSelectedHandle(val) {
                this.selectedVal = val
                //await this.initOrderList()
            },
            checkTimeOutHandle(val) {
                this.$set(this, 'checkedTimeOutTrf', val)
                this.query.isSubmissionTimeout = val ? 1 : 0
                this.onLoad(this.page);
            },
            exportExcel() {
                this.downLoading=true;
                var params = {};
                exportCclList(this.page.currentPage, 1000, Object.assign(params, this.query, this.sort)).then(res => {
                    this.downLoading=false;
                    const data = res.data.data;
                    //this.page.total = data.total;
                    this.excelData = data.records;
                    this.$nextTick(function(){
                        var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
                        var wb = XLSX.utils.table_to_book(document.querySelector('#exportTable'), xlsxParam)
                        var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
                        try {
                            FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'Ccl TestRequestList.xlsx')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log(e, wbout)
                            }
                        }
                        return wbout
                    })
                }); 
            },
            selectedDate(num, dateName) {
              this.query.startDate = moment().subtract(num, dateName).format('YYYY-MM-DD');
              this.query.endDate =  moment().format('YYYY-MM-DD');
                this.searchTrfList();
            },
            updateDate(val) {
              console.log('DATE:::', val)
              let date = []
              if (val) {
                date[0] = moment(val[0]).format('YYYY-MM-DD')
                date[1] = moment(val[1]).format('YYYY-MM-DD')
              }
              this.query.startDate = date[0];
              this.query.endDate = date[1];
              this.searchTrfList();
            },
            addTrf() {
                this.$router.push({path: '/ccl/trf/trfDetail', query: {id: '',flag:2,title:'New TRF',}});
                //this.$router.push( {path: '/trf/trfForm',query:{ id:''}});
            },
            editTrf(e) {
                this.$router.push({path: '/ccl/trf/trfDetail', query: {id: e.id, title:e.trfNo,flag:2,actionType: 'edit',trfNo:e.trfNo,signature:e.signature}});
            },
            statusFormat: function (row, column) {
                var status = row.status;
                var statusStr = '';
                if (status == 1 || status == '1') {
                    statusStr = '正常'
                } else {
                    statusStr = '禁用'
                }
                return statusStr;
            },
            customerGroupFormat: function (row, column) {
                var customerGroupId = row.customerGroupId;
                var dataList = this.customerGroupData;
                var customerGroupStr = '';
                dataList.forEach(function (value, key, dataList) {
                    if (customerGroupId == value.customerGroupId) {
                        customerGroupStr = value.groupName;
                    }
                });
                return customerGroupStr;
            },
            beforeRouteEnter(to, from, next) {
                next(vm => vm.init())
            },
            async init() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.3)',
                })
                try {
                    // 初始化supplier 对应的 buyer 数组
                    //  await this.initBuyer()
                    //  await this.initRouteParam()
                    //   await this.initDff()
                    await this.initOrderList()
                } catch (e) {
                    this.$error({message: e.message || 'Loading Data Is Fail!'})
                }
                loading.close()
            },
            async initOrderList() {
                //验证
                this.checkTime();
                let columns = this.selectedVal.map(item => item.fieldCode)
                columns.push('trfNo',
                    'trfStatus',
                    'id',
                    'isSubmissionTimeout',
                    'reports.reportNo',
                    'reports.reportConclusion'
                )
                columns = this.$lodash.uniq(columns)
                this.queryForm.order.properties['startDate'] = this.queryForm.order.properties.startDate
                this.queryForm.order.properties['endDate'] = this.queryForm.order.properties.endDate
                this.queryForm.order.properties['companyType'] = this.companyType
                //GKAM角色设置成buyer
                if (!this.$lodash.isEmpty(this.gkamBuyer)) {
                    this.queryForm.order.properties['companyType'] = 1
                }
                let orderRes = await getList(Object.assign({
                    columns: columns,
                    trfQuery: this.queryForm.order.properties,
                    pageNo: this.queryForm.pageNo
                }));
                if (orderRes.status === 200) {
                    this.trfData = this.$lodash.get(orderRes, 'data.result.list', [])
                    this.queryForm.total = this.$lodash.get(orderRes, 'data.result.total', 0)
                    this.$nextTick(() => {
                        this.renderDomExpand()
                    })
                }
            },
            searchTrfList() {
                this.onLoad(this.page);
            },
            onLoad(page) {
                let params = {};
                if(validatenull(this.query) || validatenull(this.query.startDate) || validatenull(this.query.endDate)) {
                  this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('dateValidate.startDateAndEndDateValidate'),
                    type: 'warning'
                  });
                  return;
                }
                this.loading = true;
                getCclListV2(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
                    this.loading = false;
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.trfData = data.records;
                });
            },
            onLoadFromWel() {
                this.loading = true;
                var params = {};
                this.query.trfStatus = this.$route.query.trfStatus;
                this.query.startDate = this.$route.query.startDate;
                this.query.endDate = this.$route.query.endDate;
                getCclListV2(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
                    this.loading = false;
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.trfData = data.records;
                });
            },

            //分页查询
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },

            selectLabTypeChange(value) {
                this.labForm.labType = value;
            },
        },
    };
</script>

<style lang="scss">
    .otherActiveClass {
        color: #FFF;
        background-color: #ebeef5;
    }

    .row-expand-cover {
        .el-table__expand-icon {
            visibility: hidden;
        }
    }

    .otherActiveClass:hover {
        background: #ebeef5;
        color: #FFF;
    }
    .date-group {
        button {
            border: 0;
            border-bottom: 1px solid transparent;
            border-radius: 0;
            padding: 10px 0;
            margin: 0 10px;
            &:hover, &:focus {
                background-color: initial;
                border-color: transparent;
            }
            &.active {
                border-bottom-color: #f60;
                color: #f60;
            }
        }
        .bg_orange {
            color: #EA4336;
            margin-right: 20px;
            img {
                margin-top: -7px;
            }
        }
    }
    .ccl_trf_table_scrollbar {
        .cell {
            white-space: nowrap !important;
        }
        th {
            padding-top: 16px;

            &.is-left {
                padding-top: 22px;
            }
            .el-input__inner {
                background: transparent;
                border-color: #D8D8D8;
                padding-left: 0;
                /* color: #000; */
                &::-webkit-input-placeholder {
                    /* color: transparent; */
                }
            }
            .date-input {
                .el-input__inner {
                    padding-left: 24px;
                }
            }
            .cell {
                > div {
                    &:last-of-type {
                        margin-top: 8px;
                    }
                }
                .el-select .el-input__inner:focus {
                    border-color: #D8D8D8;
                }
                .el-select .el-input .el-select__caret {
                    color: #D8D8D8;
                }
                .el-input__prefix {
                    left: -5px;
                }
            }
            &:last-of-type {
                .cell {
                    margin-top: -49px;
                }
                .operatop > span {
                    float: right;
                }
            }
        }
    }
</style>
