<template>
  <div v-loading="pageLoading" class="sgs-content">
    <div class="sgs_smart_protocol_detail" id="sgs_smart_protocol_detail">
      <DetailHeader :headInfo="headInfo" />
      <el-row>
        <el-col :span="3">
          <NavList :navList="navList" :toTarget="toTarget" />
        </el-col>
        <el-col :span="18">
          <el-card class="sgs-box content-item" id="Protocol Info">
            <CollapseCard>
              <template #title>
                <p class="card-title">PROTOCOL HEADER</p>
                <EditControl
                  v-if="!editStatus && userEdit"
                  :isEditing="editObj.infoEdit"
                  @edit="editChange('infoEdit', '1')"
                  @save="saveProtocol"
                  @cancel="cancelChange('infoEdit')"
                />
              </template>
              <HorizontalDivider paddingTop="0px" paddingBottom="10px" />
              <ReadOnlyFieldDescriptions
                v-if="!editObj.infoEdit && !editStatus"
                :templateObj="templateObj"
                :protocolForm="initProtocolForm"
              />
              <ProtocolInfoForm
                v-if="(editObj.infoEdit || editStatus) && userEdit"
                ref="protocolFormRef"
                :formData="protocolForm"
                :readOnlyFieldList="templateObj.readOnlyFieldList"
                :generalFieldList="templateObj.generalFieldList"
                :customerFieldList="templateObj.customerFieldList"
              />
            </CollapseCard>
          </el-card>
          <el-card class="sgs-box content-item" id="Protocol Scope">
            <CollapseCard>
              <template #title>
                <p class="card-title">PROTOCOL SCOPE</p>
                <edit-control
                  v-if="!editStatus && userEdit"
                  :isEditing="editObj.scopeEdit"
                  @edit="editChange('scopeEdit', '2')"
                  @save="saveProtocol"
                  @cancel="cancelChange('scopeEdit')"
                />
              </template>
              <HorizontalDivider paddingTop="0px" paddingBottom="10px" />
              <el-row :gutter="20">
                <el-col>
                  <div
                    v-if="!editObj.scopeEdit && !editStatus"
                    style="padding: 20px"
                    v-html="initProtocolForm.protocolScope"
                  ></div>
                  <WangEditor
                    v-show="(editObj.scopeEdit || editStatus) && userEdit"
                    v-model:editorContent="protocolForm.protocolScope"
                  />
                </el-col>
              </el-row>
            </CollapseCard>
          </el-card>
          <el-card
            class="sgs-box content-item"
            id="Documents"
            v-if="pageId || protocolForm.id"
          >
            <CollapseCard>
              <template #title>
                <p class="card-title">DOCUMENTS</p>
                <edit-control
                  v-if="!editStatus && userEdit"
                  :isEditing="editObj.documentsEdit"
                  @edit="editChange('documentsEdit', '3')"
                  @save="saveProtocol"
                  @cancel="cancelChange('documentsEdit')"
                />
              </template>
              <HorizontalDivider paddingTop="0px" paddingBottom="10px" />
              <ProtocolDocuments
                ref="protocolDocumentsRef"
                :obj-id="pageId || protocolForm.id"
                :default-save="false"
                :type="(editObj.documentsEdit || editStatus) && userEdit"
              />
            </CollapseCard>
          </el-card>
        </el-col>
      </el-row>
      <el-row
        class="sgs-footer page-no-print"
        style="position: fixed"
        v-if="!pageLoading && editStatus"
      >
        <el-col style="text-align: center">
          <el-button
            type="primary"
            @click="saveProtocol"
            v-if="editStatus && userEdit"
          >
            Save
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  saveEditDetail,
  queryProtocolDetail,
  queryTemplateDetail,
} from '@/api/protocol'
import { mapGetters } from 'vuex'
import { objectIsNull } from '@/utils/validate'
import NavList from '@/components/NavList/index.vue'
import ReadOnlyFieldDescriptions from './ReadOnlyFieldDescriptions.vue'
import ProtocolDocuments from './ProtocolDocuments.vue'
import ProtocolInfoForm from './ProtocolInfoForm.vue'
import WangEditor from '@/components/WangEditor/Index.vue'
import HorizontalDivider from '@/components/HorizontalDivider/index.vue'
import DetailHeader from '@/components/DetailHeader/index.vue'
import AnchorList from '@/components/AnchorList/index.vue'
import EditControl from '@/components/EditControl/index.vue'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { Edit, Check, Close } from '@element-plus/icons-vue'
import { ElNotification, ElMessageBox } from 'element-plus'
import CollapseCard from '@/components/CollapseCard/index.vue'
// 定义组件名称
const name = 'ProtocolDetail'

// 定义响应式数据
const navList = ref([
  { name: 'Protocol Info', seq: 1, active: true, isSub: false, show: true },
  { name: 'Protocol Scope', seq: 2, active: false, isSub: false, show: true },
  { name: 'Documents', seq: 3, active: false, isSub: false, show: true },
])
const element = ref({
  nav: [],
  content: [],
})
const pageLoading = ref(false)
const formDisabled = ref(false)
const activeNames = ref(['1', '2', '3'])
const protocolForm = ref({})
const initProtocolForm = ref({})
const editorConfig = ref({
  placeholder: 'please input content...',
  MENU_CONF: {},
})
const toolbarConfig = ref({
  toolbarKeys: [],
})
const editor = ref(null)
// 数据权限控制
const userEdit = ref(true)
const pageId = ref('')
const createNew = ref(false)
const templateObj = ref({
  id: '',
  formName: '',
  readOnlyFieldList: [],
  generalFieldList: [],
  customerFieldList: [],
})
const templateDetailParam = ref({
  templateId: '',
  buCode: '',
  customerBossNo: '',
  customerGroupCode: '',
})
const minScreen = ref(false)
const protocolDocumentsRef = ref(null)
const headInfo = ref({
  headName: 'Protocol Detail',
  subHeadName: '',
  createTime: '',
  updateTime: '',
  keys: [
    {
      label: 'Product Line',
      value: '',
    },
    {
      label: 'Customer',
      value: '',
    },
  ],
})

let interval

// 获取路由和路由实例
const route = useRoute()
const router = useRouter()
// 国际化
const { t } = useI18n()

// 页面状态
const pageType = route.query.action
const editStatus = computed(() => ['edit', 'add'].includes(pageType))
const editObj = ref({
  infoEdit: editStatus.value ? true : false,
  scopeEdit: editStatus.value ? true : false,
  documentsEdit: editStatus.value ? true : false,
})
// 按钮状态切换
const editChange = (type, count) => {
  Object.keys(editObj.value).forEach((key) => {
    editObj.value[key] = false
  })
  editObj.value[type] = true
  activeNames.value = count
}
const cancelChange = (type) => {
  editObj.value[type] = false
  protocolForm.value = JSON.parse(JSON.stringify(initProtocolForm.value))
  protocolDocumentsRef.value.resetHandle()
}
const protocolFormRef = ref(null)

// 保存协议
const saveProtocol = async () => {
  if (editStatus.value) {
    const valid = await protocolFormRef.value.validateForm()
    if (!valid) {
        ElNotification.warning(t('formValidate.validateError'))
      return
    }
  }

  let param = { ...protocolForm.value }
  if (createNew.value) {
    param['id'] = pageId.value
  }
  pageLoading.value = true

  saveEditDetail(param)
    .then((res) => {
      if (editStatus.value || editObj.value.documentsEdit) {
        protocolDocumentsRef.value.saveData()
      }
        ElNotification.success('Success')
      initPage()
      editChange('')
      pageLoading.value = false
    })
    .catch((err) => {
      pageLoading.value = false
        ElNotification.success('Error' + err)
    })
}

// 保存文档
const saveProtocolDocument = () => {
  protocolDocumentsRef.value.saveData()
  editObj.value.documentsEdit = false
}

// 初始化页面
const initPage = () => {
  const { id, templateId, buCode, customerBossNo, customerGroupCode } =
    route.query
  if (id) {
    protocolForm.value.id = id
    initProtocol()
    return
  }
  if (!buCode || !templateId || (!customerBossNo && !customerGroupCode)) {
      ElNotification.error('Parameter error, can not load template')
    return
  }
  createNew.value = true
  templateDetailParam.value.buCode = buCode
  templateDetailParam.value.templateId = templateId
  templateDetailParam.value.customerBossNo = customerBossNo
  templateDetailParam.value.customerGroupCode = customerGroupCode
  getNow()
  initProtocol(true)
}

// 初始化协议
const initProtocol = (init = false) => {
  pageLoading.value = true
  let param = {
    id: protocolForm.value.id,
  }

  queryProtocolDetail(param)
    .then((res) => {
      let formObj = res.data
      let {
        id,
        templateId,
        productLineCode,
        buyCustomerBossNo,
        buyCustomerGroupCode,
      } = formObj
      if (init) {
        pageId.value = id
        queryTemplate()
        return
      }
      templateDetailParam.value.buCode = productLineCode
      templateDetailParam.value.templateId = templateId
      templateDetailParam.value.customerBossNo = buyCustomerBossNo
      templateDetailParam.value.customerGroupCode = buyCustomerGroupCode
      userEdit.value = btnRole(formObj, 'Edit')
      protocolForm.value = formObj
      initProtocolForm.value = JSON.parse(JSON.stringify(formObj))
      headInfo.value.subHeadName = formObj.protocolName
      headInfo.value.createTime = formObj.createTime
      headInfo.value.updateTime = formObj.updateTime
      headInfo.value.keys = [
        {
          label: 'Product Line',
          value: formObj.productLineName,
        },
        {
          label: 'Customer',
          value: formObj.buyCustomerGroupName,
        },
      ]

      queryTemplate()
    })
    .catch((err) => {
      pageLoading.value = false
    })
}

// 查询模板
const queryTemplate = () => {
  // 直接查询模板，渲染页面
  pageLoading.value = true
  let param = { ...templateDetailParam.value }
  queryTemplateDetail(param)
    .then((res) => {
      pageLoading.value = false
      let { id, formName, generalFieldList, customerFieldList } = res.data
      protocolForm.value.templateId = id
      templateObj.value.id = id
      templateObj.value.formName = formName
      let readOnlyFields = (generalFieldList || []).filter(
        (c) => c.isReadOnly === 1,
      )
      let generalFList = (generalFieldList || []).filter(
        (c) => c.isReadOnly !== 1,
      )
      generalFList.forEach((ele) => {
        if (protocolForm.value.id && ele.fieldCode === 'protocolName') {
          ele.isReadOnly = 1
        }
      })
      ;(generalFList || []).sort((a, b) => a.sequence - b.sequence)
      templateObj.value.readOnlyFieldList = readOnlyFields || []
      templateObj.value.readOnlyFieldList.forEach((f) => {
        let { defaultValue, fieldCode } = f
        protocolForm.value[fieldCode] = defaultValue
      })

      templateObj.value.generalFieldList = generalFList || []
      templateObj.value.customerFieldList = customerFieldList || []

      nextTick(() => {
        initNavList()
      })
    })
    .catch((err) => {
      pageLoading.value = false
    })
}

const btnRole = ({ permissions }, code) => {
  return (permissions || []).map((p) => p.action).includes(code)
}

// 初始化导航列表
const initNavList = () => {
  const nav = document.getElementsByClassName('nav-item')
  const cont = document.getElementsByClassName('content-item')
  element.value.nav = nav
  element.value.content = cont
  //导航初始化样式（默认第一个）
  nav[0].classList.add('active')
  navList.value[0].active = true
  window.addEventListener('scroll', toScroll)
}

// 导航跳转
const toTarget = (index) => {
  const { content, nav } = element.value
  navList.value.forEach((na, ind) => {
    na.active = index == ind
  })
  const scrollTop =
    document.documentElement.scrollTop || document.body.scrollTop
  switch (index) {
    case 0:
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
      return
  }
  const targetTop =
    (document.documentElement.scrollTop === 0
      ? -100
      : document.documentElement.scrollTop) +
    content[index].getBoundingClientRect().top -
    100

  window.scrollTo({
    top: targetTop,
    behavior: 'smooth',
  })
}

// 定义 toScroll 函数
const toScroll = () => {
  const navEle = document.querySelector('.nav-list')
  if (objectIsNull(navEle)) {
    return
  }
  navEle.style.width = navEle.clientWidth + 'px'
  // 获取滚动距离
  const scrollTop =
    document.documentElement.scrollTop || document.body.scrollTop
  const { content, nav } = element.value
  minScreen.value = document.body.offsetWidth <= 1366

  // 侧边栏和评论栏固定
  if (scrollTop !== undefined && scrollTop > 170) {
    navEle.style.position = 'fixed'
    navEle.style.top = '80px'
  } else {
    navEle.style.position = 'initial'
  }

  // 侧边栏菜单添加当前高亮
  const len = content.length
  let scrollIndex = 0
  for (let i = 0; i < len; i++) {
    // 获取每块内容距离顶部距离
    const offset = content[i].offsetTop
    // 当划过第一块内容 改变左侧导航样式
    if (scrollTop >= offset) {
      scrollIndex = i
    }
  }
  for (let n = 0; n < len; n++) {
    if (!nav[n]) {
      return
    }
    if (n === scrollIndex) {
      nav[n].classList.add('active')
    } else {
      nav[n].classList.remove('active')
    }
  }

  if (scrollTop === 0) {
    nav[0].classList.add('active')
    nav[1].classList.remove('active')
  }
}

// 监听窗口大小变化
watch(
  () => document.body.offsetWidth,
  (width) => {
    minScreen.value = width <= 1366
  },
)

// 在组件挂载时添加滚动事件监听器
onMounted(() => {
  window.addEventListener('scroll', toScroll)
})

// 在组件卸载时移除滚动事件监听器
onBeforeUnmount(() => {
  window.removeEventListener('scroll', toScroll)
})
onMounted(() => {
  initPage()
})
</script>

<style scoped lang="scss">
.sgs_smart_protocol_detail {
  padding-bottom: 100px;
  .card-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
  }
  .sgs-box {
    margin-top: 20px;
  }
  .sgs-footer {
    height: 70px;
    text-align: center;
    vertical-align: middle;
    line-height: 70px;
    position: fixed;
    left: 0px;
    bottom: 0px;
    width: 100%;
    z-index: 1000;
    background-color: #fff;
    border-top: 1px solid #e6e6e6;
  }
}
</style>
