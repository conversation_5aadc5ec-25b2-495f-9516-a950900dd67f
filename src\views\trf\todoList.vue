<template>
    <basic-container>
        <div class="todoList" id="todoList">
            <sgs-customer-to-do-list
                    productLineCode="SL"
                    noNeedSelectCustomer
                    :defaultRefSystemId="10028"
                    :hideRefSystemId="[2]"
                    mode="REMOTE"
                    :createTRFOption="{
                        productLineCodes:['SL','HL','EE','CPCH'],
                        buyerName: 'Target USA',
                        customerGroupCode:'CG0000219',
                        refSystemId:10028,
                        useCustomerDetail:true,
                        userCustomerApi:true,
                        customerHttp:'/api/sgsapi/',
                        config:{
                            customerGroupCode: 'CG0000219',
                            refSystemId : 10028,
                            refSystemLabelName : 'Target',
                        },
                        btnUseCustomer:true,
                        btnShow:{
                            confirmTrf:false,
                            printTrf:false,
                            filterExport:false
                        }
                    }"
                    useCustomerConfig
                    :showTabs="false"
            ></sgs-customer-to-do-list>
        </div>
    </basic-container>
</template>

<script>
    export default {
        name: "todoList",
        data() {
            return {}
        },
        methods: {},
        mounted() {
        },
        created() {
        },
        watch: {},
        computed: {},
        props: {},
        updated() {
        },
        beforeDestory() {
        },
        destoryed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .todoList {
        background: #fff;
        padding: 24px 32px;
    }
</style>