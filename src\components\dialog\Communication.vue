<template>
    <div>
        <span class="add-entry" v-if="showWithBtn" @click="addComments" style="cursor: pointer;">
            {{ $t('communicationLog.addComment') }}
        </span>
        <el-card class="sgs-box" v-else>
            <div class="sgs-group">
                <h3>{{$t('communicationLog.title')}}</h3>
                <div class="right">
                    <el-button type="primary" @click="addComments" size="small">
                        {{$t('operation.add')}}
                    </el-button>
                </div>
            </div>
            <el-table :data="communicationTableData" width="100%">
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
                <el-table-column
                        prop="newCreateTime"
                        :label="$t('common.time')" width="180" >
                  <template slot-scope="scope">
                    <span > {{ currentTz_YMD($lodash.get(scope.row, 'newCreateTime',''))  }}1122</span>
                  </template>
                </el-table-column>
                <el-table-column
                        prop="comment" :show-overflow-tooltip='true'
                        :label="$t('communicationLog.comments')" width="400">
                </el-table-column>
                <el-table-column
                        prop="contactEmail" :show-overflow-tooltip='true'
                        :label="$t('communicationLog.commentBy')">
                </el-table-column>
                <el-table-column
                        :label="$t('trf.attachment')"
                        width="200">
                    <template slot-scope="scope">
                        <img :src="'/img/attachment_icon.png'" width="20px" height="20px" style="cursor: pointer"
                            v-if="scope.row.fileUrl" @click="downAttachment(scope.row.fileUrl)"/>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="createUser"
                        :label="$t('user.createUser')" width="120">
                </el-table-column>
            </el-table>
        </el-card>

        <el-dialog :title="$t('communicationLog.addComment')" :close-on-click-modal="false" append-to-body=“true”
                    :visible.sync="dialogFormVisible" top="25vh" width="55%">
                <el-form :model="communicationLog" :rules="communicationRules" ref="communicationForm" label-width="120px">
                    <el-form-item :label="$t('communicationLog.comment')" :label-width="labelWidth" prop="comment">
                        <el-input type="textarea" v-model="communicationLog.comment" clearable
                                autocomplete="off" :maxlength="2000" show-word-limit="true"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('communicationLog.emailGroup')" :label-width="labelWidth">
                        <el-select v-model="emailGroupSelected" multiple style="width: 100%;background: white"
                                @change="handleEmailGroupSelectChange">
                            <el-option
                                    v-for="item in emailGroupList"
                                    :key="item.id"
                                    :label="item.emailGroupName"
                                    :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('communicationLog.addressBook')" :label-width="labelWidth">
                        <el-select v-model="emailArray"
                                @change="contactChange"
                                multiple
                                filterable
                                allow-create
                                default-first-option
                                size="small"
                                style="width: 100%;">
                            <el-option v-for="(contact,index) in customerContactData"
                                    :label="contact.contactEmail" :value="contact.contactEmail"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="trfStatus==5" :label="$t('communicationLog.pdfReport')" :label-width="labelWidth">
                        <el-checkbox-group v-model="reportPdfSend">
                            <el-checkbox checked="checked" title="Send To" v-for="item in reportPdf" :key="item.cloudId"
                                        :label="item">{{item.fileName }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item :label="$t('trf.attachment')" :label-width="labelWidth">
                        <el-upload
                                class="customerUpload"
                                action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                :on-success="uploadSuccess"
                                :on-remove="fileRemove"
                                :on-change="fileProgress"
                                :before-upload="beforeUpload"
                                :file-list="fileList"
                                :limit="1"
                                :show-file-list="true">
                            <el-button size="small" type="primary">{{$t('operation.upload')}}</el-button>
                        </el-upload>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary"  :loading="savaBtnLoading" @click="saveCommunicationLog">
                        {{$t('operation.confirm')}}
                    </el-button>
                </div>
            </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {getNotPageByUser} from "@/api/customer/customerEmailGroup";
    import {validatenull, validateEmail} from "@/util/validate";
    import {getCloudFileURL} from "@/api/common/index";
    import moment from 'moment'
    import {
        queryTrfComments, getReports, submitTrfComment
    } from "@/api/trf/communication";
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'


    export default {
        name: 'Communication',
        data() {
            return {
                savaBtnLoading:false,
                customerContactData: [],
                communicationTableData: [],
                sort: {descs: 'update_time'},
                dialogFormVisible: false,
                emailArray: [],
                emailSelected: [],
                emailSelectedTemp: [],
                emailGroupList: [],
                emailGroupSelected: [],//选择的邮件组数据
                communicationLog: {
                    trfId: '',
                    comment: '',
                    contactId: null,
                    contactName: '',
                    contactEmail: '',
                    emailGroup: '',
                    attachments: [],
                    reports:[],
                },
                optionsList: [],
                loading: false,
                reportPdf: [],
                reportPdfSend: [],
                fileList: [],
                userCode: "",
                emailGroupParam: {
                    status: 1,
                },
                communicationParam: {},
                communicationRules: {
                    comment: [
                        {required: true, message: this.$t('communicationLog.validate.comment'), trigger: 'blur'},
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                }
            }
        },
        created() {
            this.queryEmailGroupData();
        },
        props: {
            uploadFileMaxSize:{
              type:Number,
              default(){
                  return 10
              }
            },
            showWithBtn: {
                type: Boolean,
                default() {
                    return false
                }
            },
            communicationTableData: {
                type: Array,
                default() {
                    return []
                }
            },
            /* customerContactData:{
                 type: Array,
                 default() {
                     return []
                 }
             },*/
            communicationLogs: {
                type: Array,
                default() {
                    return []
                }
            },
            trfEmail: {
                type: String,
                default: null
            },
            trfId: {
                type: String,
                default: null
            },
          /*  trfType: {
                type: String,
                default: 10
            },*/
            productLineCode: {
              type: String,
              default: null
            },
            trfStatus: {
                type: String,
                default: null
            },
            trfNo: {
                type: String,
                default: null
            },
            orderId: String,
            buyerInstance: Object,
            applicationInstance: Object,
            orderCreatedBy: String
        },
        watch: {
           /* dialogFormVisible(val, old) {
                if (!val) {
                    this.communicationLog = {}
                }
            }*/
        },
        computed: {
            ...mapGetters(["userInfo", "language"]),
            labelWidth() {
                return this.language == 'en-US' ? '220px' : '100px'
            }
        },
        methods: {
          currentTz_YMD(val) {
            if (!val) return ''
            let value = tzFormChina(val, 'YYYY-MM-DD HH:mm:ss');
            return moment(value).format('YYYY-MM-DD');
          },
            beforeUpload(file){
                const isLt = file.size / 1024 / 1024 <= this.uploadFileMaxSize;
                if (!isLt) {
                    this.$message.error(this.$t('uploadFileSizeError')+this.uploadFileMaxSize + 'MB!');
                }
                return isLt;
            },
            //文件上传时钩子函数
            async fileProgress(event, file, fileList){
                if(event.status=='ready'){
                    this.savaBtnLoading=true;
                }else{
                    this.savaBtnLoading=false;
                }
            },
            uploadSuccess(res, file) {
                this.savaBtnLoading=false;
                this.communicationLog.attachments = [];
                const attachment = {
                    'attachmentId': res.data[0].cloudID,
                    'fileUrl': res.data[0].path,
                    'fileName': file.name
                };
                this.communicationLog.attachments.push(attachment);
            },
            contactChange(emailList) {
                let pasteEmailArr = this.emailArray;
                emailList.find((item1) => {
                    let validateRes = validateEmail(item1);
                    if (validateRes) {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1);
                        }
                    } else {
                        pasteEmailArr.splice(pasteEmailArr.indexOf(item1), 1);
                    }
                });
                this.emailArray = pasteEmailArr;
            },
            //查询当前公司的邮件组数据
            queryEmailGroupData() {
                var params = {};
                getNotPageByUser(Object.assign(params, this.emailGroupParam, this.sort)).then(res => {
                    this.emailGroupList = res.data.data;
                });
            },
            handleEmailGroupSelectChange(emailGroupIds) {
                var vm = this;
                //将选择的客户组中的email地址放入Recipient(s) from Address Book中
                if (!validatenull(emailGroupIds)) {
                    emailGroupIds.find((val) => {
                        let obj = {};
                        obj = vm.emailGroupList.find((item) => {
                            return item.id === val;
                        });
                        if (!validatenull(obj)) {
                            console.log(vm.emailArray);
                            obj.contacts.find((item) => {
                                if (vm.emailArray.indexOf(item.contactEmail) == -1) {
                                    vm.emailArray.push(item.contactEmail);
                                }
                            });
                        }
                    });
                }
                console.log(this.emailGroupSelected);
            },
            async downAttachment(cloudId) {
                getCloudFileURL(cloudId).then(res => {
                    window.open(res.data, "_blank");
                });

            },
            fileChange(file, fileList) {
                this.fileList = []
                this.fileList.push(file);
            },
            fileRemove() {
                this.savaBtnLoading=false;
                this.fileList = []
                this.$refs.upload.clearFiles()
            },
            saveCommunicationLog() {
                var emailStr = '';
                this.emailArray.find((item) => {
                    emailStr += item + ',';
                });
                if (emailStr.length > 0) {
                    emailStr = emailStr.substr(0, emailStr.length - 1);
                }
                var emailGroupStr = '';
                this.emailGroupSelected.find((item) => {
                    emailGroupStr += item + ',';
                });
                if (emailGroupStr.length > 0) {
                    emailGroupStr = emailGroupStr.substr(0, emailGroupStr.length - 1);
                }
                var reports=[];
                if (this.reportPdfSend) {
                    this.reportPdfSend.find((item) => {
                        reports.push(item)
                    });
                }
                this.communicationLog.reports=reports;
                this.communicationLog.contactEmail = emailStr;
                this.communicationLog.emailGroup = emailGroupStr;
                this.communicationLog.trfId = this.trfId;
                this.communicationParam.trfId = this.trfId;
                //this.communicationLog.trfType = this.trfType;
                this.communicationLog.productLineCode = this.productLineCode;
                this.$refs['communicationForm'].validate((valid) => {
                    if(valid){
                        //提交表单
                        this.savaBtnLoading=true;
                        this.communicationLog.trfNo = this.$route.query.trfNo;
                        this.communicationLog.signature = this.$route.query.signature;
                        submitTrfComment(this.communicationLog).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            // this.communicationTableData.push();
                            this.queryCommunicationLog();
                            this.savaBtnLoading=false;
                            this.dialogFormVisible = false;
                        }, error => {
                            this.savaBtnLoading=false;
                            this.$message.error(this.$t('api.error'));
                        });
                    }else{
                        this.savaBtnLoading=false;
                    }
                });

            },
            queryCommunicationLog() {
                let communicationParams = {};
                this.communicationParam.trfNo = this.$route.query.trfNo;
                this.communicationParam.signature = this.$route.query.signature;
                queryTrfComments(Object.assign(communicationParams, this.communicationParam)).then(res => {
                    this.communicationTableData = res.data.data;
                    this.$emit('closeDialog', true)
                });
            },
            async addComments() {
                this.communicationLog.comment='';
                this.communicationLog.emailGroup='';
                this.communicationLog.contactEmail='';
                this.communicationLog.attachments=[];
                this.communicationLog.reports=[];
                this.reportPdf = [];
                this.reportPdfSend = []
                this.emailGroupSelected = []
                this.emailSelectedTemp = []
                this.fileList = []
                this.emailArray = [];
                this.emailSelected = [];
                //默认带入申请单的email地址
                if (!validatenull(this.trfEmail)) {
                    debugger;
                    this.emailArray=this.$lodash.split(this.trfEmail, ',');
                }
                //查询PDF报告单放入reportPdf中
                if (this.trfStatus == 5) {
                    //获取报告数据
                    let res = await getReports(this.trfId);
                    this.reportPdf = res.data.data;
                }

                this.dialogFormVisible = true
            },
        }
    }
</script>

<style lang="scss">
    .el-tooltip__popper{max-width:80%}
</style>

<style scoped>
    .add-entry {
        color: #f60;
        border-bottom: 1px solid #ff6600;
    }

    .el-select__input:focus {
        color: white;
        background-color: white;
    }

    .el-select__input:focus, input[class='el-select__input']:focus {
        background: white;
        border: 1px solid #ccc;
        outline: none;
        box-shadow: none;
    }

    .customerUpload /deep/ > div {
        display: inline;
    }

    .customerUpload /deep/ > ul {
        display: inline-block;
        margin-left: 10px;
        height: 32px;
        vertical-align: top;
    }

    .customerUpload /deep/ > ul > li {
        display: inline-block;
    }

    .customerUpload /deep/ > ul > li > a {
        display: inline;
    }
</style>

