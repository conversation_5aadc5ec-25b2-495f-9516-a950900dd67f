import request from '@/router/axios';



//查询客户列表 分页
export function queryCompanyListByPage(params) {
    return request({
        url: '/api/sgs-mart/sgs-api/getCompanyListPage',
        method: 'post',
        data: params
    })
}


export const queryAccountPerListByPage = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/accountPer/getAccountPerListPage',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const add = (form) => {
    return request({   
        url: '/api/sgs-mart/accountPer/submit',
        method: 'post',
        data: form
    })
}
export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/accountPer/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}