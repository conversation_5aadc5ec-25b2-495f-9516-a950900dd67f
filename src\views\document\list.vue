<template>
    <basic-container>
        <!--<div v-for="(value,key,index) in programAnds">
            <h3>{{ key }}</h3>
            <div v-for="doc in value">
                <h5>{{ doc.documentTitle}}</h5>
                <h5>{{ doc.documentUrl}}</h5>
            </div>
        </div>-->
        <div v-for="item  in programs">
            <h3>{{ item.programName }}</h3>
                <h5>{{ item.documentTitle}}</h5>
                <h5>{{ item.documentUrl}}</h5>

        </div>
    </basic-container>
</template>

<script>
    import {getPrograms} from "@/api/document/authorization";
    import {getLibrarys} from "@/api/document/library";
    export default {
        data(){
            return{
                name: "list",
                programs: [],
                programAnds: {},
            }
        },
        methods:{
            onLoad() {
                getPrograms().then(res =>{
                    const  data=res.data.data;
                    this.programs=data;
                    /*this.programs.forEach((item) => {
                        item.id = item.id;
                        item.programName =item.programName;
                        getLibrarys(item.id).then(res =>{
                            this.programAnds[item.programName]=res.data.data;
                        });
                    })*/
                });
            }
        },
        created() {
            this.onLoad();
        }

    }
</script>

<style scoped>

</style>
