<template>
  <div class="register-container" >
    <el-card>
      <ul style="margin: 50px;text-align: left;list-style: decimal;">
        <li>{{$t('register.tip.emailconfim')}}</li>
        <li>{{$t('register.tip.sgsapprove')}}</li>
      </ul>
      <!--<div style="text-align: right">
        <router-link type="text" :to="{path:'/'}" style="padding-right: 20px;">{{$t('login.submit')}}</router-link>
      </div>-->
    </el-card>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  components: {
  },
  name: "index",
  data() {
    return {
      //刷新token锁
      refreshLock: false,
      //刷新token的时间
      refreshTime: ""
    };
  },
  created() {

  },
  mounted() {

  },
  computed: mapGetters(["language"]),
  props: [],
  methods: {


  }
};
</script>
