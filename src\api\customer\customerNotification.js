import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/notifications/add',
        method: 'post',
        data: form
    })
}
export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/notifications/detail',
        method: 'get',
        params: {
            id,
        }
    })
}
export const addUserNotification = (form)=>{
    return request({
        url: '/api/sgs-mart/userSetting/updateUserNotification',
        method: 'post',
        data: form
    })
}
export const queryUserNotification = ()=>{
    return request({
        url: '/api/sgs-mart/userSetting/queryUserNotification',
        method: 'get',
    })
}
