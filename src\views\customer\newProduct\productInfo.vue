<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_product_productInfo" id="sgs_smart_product_productInfo">
            <el-row>
                <el-col :span="4" v-if="!viewPage">
                   <nav-list :nav-array="navList" v-if="showNavList"></nav-list>
                </el-col>
                <el-col :offset="viewPage?1:0" :span="viewPage?22:16" v-if="showCompoment">
                    <el-form :model="productForm"
                             ref="productForm"
                             :disabled="!viewPage && !(createNew ||btnRole(productForm,'Edit'))"
                             label-position="top"
                             label-width="180px">
                        <el-collapse v-model="activeNames">
                            <el-card class="sgs-box content-item" v-for="(config,index) in (createNew? generalFieldConfig.filter(g=>g.sectionCode!='BillOfMaterials'):generalFieldConfig)" :key="'pgen_'+index">
                                <el-collapse-item style="position: relative" :title="config.sectionLabel" :name="config.sectionCode">
                                    <template slot="title">
                                        <div class="title-wrap">
                                            <h4 :class="('title_'+config.sectionCode) + ' sgs-title'">
                                                {{config.sectionLabel || config.sectionName}}
                                            </h4>
                                            <span class="productInfoApproved" v-if="config.sectionCode=='ProductInfo'">
                                                <el-button :icon="getCommentIcon(productForm,'product')"
                                                    v-if="!createNew && viewPage != 'preview'"
                                                    :disabled="false"
                                                    type="text"
                                                    style="padding:2px 5px"
                                                    @click="showReportComment(productForm)"></el-button>
                                                <i  v-if="permissionList.approvedBtn && [0,1].includes(productForm.approvedStatus) && btnRole(productForm,'Approved')"
                                                :disabled="false"
                                                class="el-icon-success approved_icon"
                                                :class="getApprovedClass('Approved')"
                                                style="color: #00d26a;"
                                                @click="approvedObj('product',1)"
                                                ></i>
                                                <i  v-if="permissionList.approvedBtn && [0,2].includes(productForm.approvedStatus) && btnRole(productForm,'Approved')"
                                                :disabled="false"
                                                class="el-icon-error approved_icon"
                                                :class="getApprovedClass('Reject')"
                                                style="color:#ff6600;"
                                                @click="approvedObj('product',2)"
                                                ></i>
                                                <i  v-if="permissionList.approvedBtn && [1,2].includes(productForm.approvedStatus) && btnRole(productForm,'Approved')"
                                                :disabled="false"
                                                class="el-icon-more approved_icon more_icon"
                                                @click="approvedObj('product',0)"
                                                ></i>
                                                    
                                            </span>
                                        </div>
                                    </template>
                                            
                                    <el-row :gutter="20" v-if="config.sectionType=='normal' && config.sectionCode!='ProductInfo'">
                                        <el-col :span="8" v-for="(field,ind) in config.fieldList" :key="'pcfl_'+ind">
                                            <el-form-item
                                                :rules="{ required: field.isRequired==1 , message: 'please input', trigger: ['blur', 'change'] }"
                                                :label="field.fieldLabel || 'No label name'"
                                                :prop="lowerField(field.fieldCode)">
                                                <el-input v-if="!field.fieldType || field.fieldType!='select'"
                                                          placeholder="Please input"
                                                          :type="field.fieldType=='textArea'?'textarea':''"
                                                          show-word-limit
                                                          :rows="3"
                                                          clearable
                                                          v-model="productForm[lowerField(field.fieldCode)]"></el-input>
                                                <el-select
                                                        v-if="field.fieldType=='select'"
                                                        style="width: 100%"
                                                        clearable
                                                        filterable
                                                        placeholder="Please select"
                                                        v-model="productForm[lowerField(field.fieldCode)]">
                                                    <el-option
                                                            v-for="(op,opIndex) in field.sourceValue"
                                                            :key="field.fieldCode+'_'+opIndex"
                                                            :label="op.name"
                                                            :value="op.code"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="20" v-if="config.sectionCode=='ProductInfo'">
                                        <el-col :span="8" v-for="(field,ind) in config.fieldList" :key="'pcfl_'+ind">
                                            <el-form-item
                                                    :rules="{ required: field.isRequired==1 , message: 'please input', trigger: ['blur', 'change'] }"
                                                    :label="field.fieldLabel || 'No label name'"
                                                    :prop="lowerField(field.fieldCode)">
                                                <el-input v-if="!field.fieldType || field.fieldType!='select'"
                                                          placeholder="Please input"
                                                          :type="field.fieldType=='textArea'?'textarea':''"
                                                          show-word-limit
                                                          :rows="3"
                                                          clearable
                                                          v-model="productForm[lowerField(field.fieldCode)]"></el-input>
                                                <el-select
                                                        v-if="field.fieldType=='select'"
                                                        :disabled="field.fieldCode=='sampleCategory'"
                                                        style="width: 100%"
                                                        clearable
                                                        filterable
                                                        placeholder="Please select"
                                                        v-model="productForm[lowerField(field.fieldCode)]">
                                                    <el-option
                                                            v-for="(op,opIndex) in field.sourceValue"
                                                            :key="field.fieldCode+'_'+opIndex"
                                                            :label="op.name"
                                                            :value="op.code"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-divider></el-divider>
                                        <!-- 下面开始是动态的了-->
                                        <el-col :span="12" v-for="(f,index) in productForm.fieldList" :key="'dff_'+index">
                                            <el-form-item
                                                    :rules="{ required: f.mandatoryFlag==1 , message: ((!f.fieldType || ['TextArea','Input'].includes(f.fieldType))? 'please input' : 'please select'), trigger: ['blur', 'change']}"
                                                    :label="f.dffFormFieldNameEn || ('No Name'+index)"
                                                    :prop="'fieldList.'+index+'.materialExtendFieldValue'">

                                                <template slot="label">
                                                    {{f.dffFormFieldNameEn || ''}}
                                                    <el-tooltip effect="dark"
                                                                :content="f.tips"
                                                                placement="right"
                                                                v-if="f.tips">
                                                        <i class="el-icon-info" style="color: #c8c8c8"></i>
                                                    </el-tooltip>
                                                </template>
                                                <template  v-if="!['Percentage'].includes(f.fieldType)">
                                                    <el-input v-if="!f.fieldType || ['RichText','TextArea','Input'].includes(f.fieldType)"
                                                              :type="['RichText','TextArea'].includes(f.fieldType)?'textarea':'text'"
                                                              placeholder="Please input"
                                                              :rows="3"
                                                              clearable
                                                              v-model="f.materialExtendFieldValue"></el-input>
                                                    <el-select
                                                            v-if="['Select','Select2'].includes(f.fieldType)"
                                                            style="width: 100%;"
                                                            :multiple="['Select2'].includes(f.fieldType)"
                                                            placeholder="Please select"
                                                            clearable
                                                            filterable
                                                            v-model="f.materialExtendFieldValue">
                                                        <el-option
                                                                v-for="(o,ind) in getFieldSourceValues(index)"
                                                                :key="'o_sel'+o.code+ind"
                                                                :label="o.name"
                                                                :value="o.code"
                                                        ></el-option>
                                                    </el-select>
                                                    <el-checkbox-group
                                                            v-if="['Checkbox'].includes(f.fieldType)"
                                                            v-model="f.materialExtendFieldValue"
                                                    >
                                                        <el-checkbox
                                                                v-for="(o,ind) in getFieldSourceValues(index)"
                                                                :key="'check_'+o.code"
                                                                :label="o.code"
                                                                :value="o.code"
                                                        >{{o.name}}</el-checkbox>
                                                    </el-checkbox-group>
                                                    <el-radio-group
                                                            v-if="['Radio'].includes(f.fieldType)"
                                                            v-model="f.materialExtendFieldValue"
                                                    >
                                                        <el-radio
                                                                v-for="(o,ind) in getFieldSourceValues(index)"
                                                                :key="'radio_'+o.code"
                                                                :label="o.code"
                                                                :value="o.code"
                                                        >{{o.name}}</el-radio>
                                                    </el-radio-group>
                                                    <el-date-picker
                                                            v-if="['Date','DateRange'].includes(f.fieldType)"
                                                            v-model="f.materialExtendFieldValue"
                                                            :type="['Date'].includes(f.fieldType)?'date':'daterange'"
                                                            range-separator="-"
                                                            format="yyyy-MM-dd"
                                                            value-format="yyyy-MM-dd"
                                                            placeholder="Please select">
                                                    </el-date-picker>
                                                </template>
                                                <percentage
                                                    v-if="['Percentage'].includes(f.fieldType)"
                                                    :field-obj="f"
                                                    :ref="'percentage_'+f.fieldCode"
                                                ></percentage>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <care-label-info
                                            v-if="config.sectionCode=='CareLabel' && showCare"
                                            ref="care_label_info"
                                            :sample-id="productForm.id"
                                            :create-new="createNew"
                                            :customerGroupCode="productForm.customerGroupCode"
                                            :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                            :edit-rule="createNew || btnRole(productForm,'Edit')"
                                            :reload-page="reloadCarelabel"
                                            :care-label-data="productForm.id?(productForm.careLabelList[0] || {}):{}"
                                    ></care-label-info>
                                    <work-book
                                            v-if="(viewPage == 'preview' || productForm.id) && config.sectionCode=='Workbooks'"
                                            :object-id="productForm.id"
                                            :create-new="createNew"
                                            :edit-rule="createNew ||btnRole(productForm,'Edit')"
                                            :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                            ref="workBook"
                                    ></work-book>
                                    <el-form>
                                        <report-certificate
                                                v-if="(viewPage == 'preview' || productForm.id) && config.sectionCode=='ReportCertificate'"
                                                :object-id="productForm.id"
                                                :edit-rule="createNew ||btnRole(productForm,'Edit')"
                                                :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                                :create-new="createNew"
                                                test-from="product"
                                                file-test-from="customer"
                                        ></report-certificate>
                                    </el-form>

                                    <bill-of-materials
                                            v-if="(viewPage == 'preview' || productForm.id) && pageId && config.sectionCode=='BillOfMaterials'"
                                            :edit-rule="createNew ||btnRole(productForm,'Edit')"
                                            :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                            ref="billOfMaterials"
                                            :sample-id="productForm.id"
                                            :customer-obj="{
                                               customerGroupCode: productForm.buyerCustomerGroupCode,
                                               customerGroupName: productForm.buyerCustomerGroupName,
                                               productLineCode: productForm.productLineCode,
                                               productLineName: productForm.productLineName
                                            }"
                                    ></bill-of-materials>
                                </el-collapse-item>
                            </el-card>
                        </el-collapse>
                    </el-form>
                </el-col>
                <el-col :span="4">
                    <div class="product-upload">
                        <div class="product-sub-title">
                            Product Image
                        </div>
                        <el-upload
                            v-if="dataList.uploadImgList.length<6 && (createNew ||btnRole(productForm,'Edit'))"
                            :class="dataList.uploadImgList.length==0?'avatar-uploader':'avatar-uploader-small'"
                            action="/api/sgsapi/FrameWorkApi/file/doUpload"
                            :show-file-list="false"
                            :limit="5"
                            multiple
                            :data="{systemID:1}"
                            accept=".jpg,.jpeg,.png"
                            :on-exceed="fileExceed"
                            :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload">
                            <i :class="'el-icon-upload2 '+(dataList.uploadImgList.length==0? 'avatar-uploader-icon':'avatar-uploader-mini-icon')"></i>
                        </el-upload>
                        <el-carousel
                            height="150px"
                            trigger="click"
                            indicator-position="outside"
                            :autoplay="false"
                            arrow="never"
                            v-if="dataList.uploadImgList && dataList.uploadImgList.length>0"
                        >
                            <el-carousel-item
                                v-for="(item,imgIndex) in dataList.uploadImgList"
                                :key="'img_'+imgIndex">
                                <div
                                    style="position: relative;"
                                    @mouseenter="showDeleteIcon = true"
                                    @mouseleave="showDeleteIcon = false"
                                >
                                    <img class="carousel-img" :src="item.imageUrl">

                                    <div
                                        v-if="(createNew || btnRole(productForm,'Edit')) && showDeleteIcon"
                                        class="mask"
                                    >
                                    <div slot="reference">
                                        <i class="el-icon-delete img_del" style="color:#ff6600" @click=delImgDialog(item,imgIndex)></i>
                                        <i class="el-icon-zoom-in img_view" style="color:#ff6600" @click=openImgDialog(item,imgIndex)></i>
                                    </div>
                                    </div>
                                </div>
                            </el-carousel-item>
                        </el-carousel>
                    </div>
                    <div class="product-comment">
                        <div class="product-sub-title">
                            Product Comment
                        </div>
                        <div>
                            <comment-list
                                v-if="productForm.id"
                                ref="commentList"
                                :object-id="productForm.id"
                                object-type="product"
                            />
                        </div>
                    </div>
                </el-col>
            </el-row>
            <el-row class="sgs-footer page-no-print" style="position: fixed" v-if="!pageLoading && !viewPage">
                <el-col style="text-align: center">
                    <el-button class="custom-primary-button" type="primary" @click="saveProduct()" :disabeld="disabledSaveBtn" v-if="createNew || btnRole(productForm,'Edit')">Save</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="saveProduct('Submit')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Submit')">Submit</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="createTRF" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'ToTrf')">Create TRF</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('Return')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Return')">Return</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('Approved')" :disabeld="disabledSaveBtn" v-if="permissionList.approvedBtn && btnRole(productForm,'Approved')">Approved</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('NotInUse')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'NotInUse')">Not In Use</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('InUse')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'InUse')">In Use</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="changeAssign" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Edit')">Change Assignee</el-button>
                    <el-button class="custom-info-button" type="info" @click="handlerCancelProduct" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Cancel')">Cancel</el-button>
                </el-col>
            </el-row>
        </div>
        <el-dialog
                :visible.sync="showChangeAssignee"
                width="30%"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
        >
            <el-row>
                <el-col :span="8">Change Assignee</el-col>
                <el-col :span="16">
                    <el-select
                        style="width: 100%"
                        v-model="assigneeId"
                    >
                        <el-option
                                v-for="(ass,index) in dataList.assigneeList"
                                :key="'ass_'+index"
                                :label="ass.email?(ass.name+'('+ass.email+')'):ass.name"
                                :value="ass.id"
                        ></el-option>
                    </el-select>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showChangeAssignee = false">Cancel</el-button>
                <el-button type="primary" @click="saveAssigneeId">Save</el-button>
            </span>
        </el-dialog>
        <reject-provided v-if="rejectShow"
                         :object-id="productForm.id"
                         object-type="product"
                         @cancelDia="rejectShow=false"
                         @rejectSuccess="rejectSuccess"
        ></reject-provided>
        <chat-view
            v-if="showChat"
            :object-id="productForm.id"
            object-type="product"
            @cancelDia="showChat=false"
            @update="updateCommentList"
        ></chat-view>
        <el-dialog
            :visible.sync="deleteDialogVisible"
            width="300px">
            <span>Delete the image?</span>
            <span slot="footer" class="dialog-footer">
                <el-button size="medium" @click="deleteDialogVisible = false">Cancel</el-button>
                <el-button size="medium" type="primary" @click="delImgConfirm">Delete</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="imgDialogVisible">
            <div class="img-zoom">
                <img :src="imgDialogUrl">
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
    import {mapGetters} from "vuex";
    import CareLabelInfo from "../commonComponent/careLabelInfo";
    import ReportCertificate from "../commonComponent/reportCertificate";
    import NavList from "../commonComponent/navList";
    import BillOfMaterials from "../materialAndProductCommon/billOfMaterials";
    import WorkBook from '../materialAndProductCommon/workBook'
    import api from "../../../api/newSamples";
    import _ from "lodash";
    import ChatView from "../materialAndProductCommon/chatView";
    import RejectProvided from "../materialAndProductCommon/rejectProvided";
    import CommentList from "../commonComponent/commentList.vue";
    import Percentage from "../materialAndProductCommon/percentage";
    import {queryBuSetting} from "@/api/common";

    export default {
        name: "productInfo",
        provide(){
            return {
                getMainObjectId:()=>{return this.productForm.id || this.pageId}
            }
        },
        data() {
            return {
                pageLoading: false,
                disabledSaveBtn:false,
                showChangeAssignee:false,
                showChat:false,
                createNew:false,
                rejectShow:false,
                showCare:true,
                reloadCare:false,
                assigneeId:'',
                temp:'',
                activeNames:[],
                productForm:{
                    sampleCategory:'',
                    productLineCode:'',
                    templateId:'',
                    customerGroupCode:'',
                },
                pageId:'',
                fieldList:[],
                generalFieldConfig:[],
                imageUrl:'',
                pageSelectList:{
                    categoryList:[]
                },
                dataList:{
                    billOfMaterialList:[],
                    workbookList:[],
                    assigneeList:[],
                    uploadImgList:[]
                },
                showNavList:false,
                showCompoment:false,
                navList:[],
                templateParam:{
                    templateId:''
                },
                commentInterVal:null,
                showDeleteIcon: false,
                delImgItem: {},
                delImgIndex: '',
                deleteDialogVisible: false,
                imgDialogVisible: false,
                imgDialogUrl: '',
            }
        },
        methods: {
            initPage() {
                this.loadPage();
            },
            lowerField(fieldCode){
                return _.lowerFirst(fieldCode)
            },
            reloadCarelabel(){
                this.showCare = false;
                this.reloadCare = true;
                this.queryProductDetail();
            },
            loadPage(){
                let query = this.$route.query;
                let {action,templateId,id} = query;
                this.action = action;
                //处理页面select
                if(this.viewPage && this.viewPage == 'preview'){
                    this.initPreview();
                }else if(action=='copy'){
                    this.handlerCopy(id);
                }else{
                    this.productForm.id = id;
                    this.pageId = id;
                    this.templateParam.templateId = templateId;
                    this.queryProductDetail(!id);
                }
            },
            initPreview(){
                let {fieldList,generalFieldConfig} = this.templateData || {};
                if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                    this.$notify.warning("There has no preview data")
                    return;
                }
                this.fieldList = fieldList;
                this.generalFieldConfig = generalFieldConfig;
                this.activeNames = generalFieldConfig.map(g=>g.sectionCode);
                this.renderPageComponent();
            },
            getShowValueLabel(field){
                console.log("field",field)
                this.$nextTick(()=>{

                })
            },
            initSelectList(){
                let cusParam = {
                    customerGroupCode: this.productForm.customerGroupCode
                }
                api.queryEmployee(cusParam).then(res=>{
                    //console.log("query comp id ",res)
                    if(res.status==200 && res.data && res.data.data){
                        this.dataList.assigneeList = res.data.data || [];
                        let ass = (res.data.data || []).find(ass=>ass.id==this.productForm.assigneeId) || {};
                        this.$emit("assigneeObj",ass)
                    }
                }).catch(err=>{
                    console.log("query comp id err",err)
                })
                //查询categorylist
                this.initCategoryType();
            },
            initCategoryType(){
                let paramCode = 'ProductCategory';
                let params ={
                    systemId:1,
                    groupCode:'Template_Product_Material_Type',
                    productLineCode:this.productForm.productLineCode,
                    paramCode
                }
                this.pageSelectList.categoryList = [];
                queryBuSetting(params).then(res=>{
                    console.log("query type list",res);
                    if(res.status==200 && res.data && res.data.data){
                        let {data} = res.data;
                        if(!data || data.length==0){
                            return;
                        }

                        data = data[0];
                        let {paramValue} = data;
                        try{
                            let values = JSON.parse(paramValue);
                            this.pageSelectList.categoryList =values;
                            this.changeCategorySelect();
                        }catch (e) {}
                    }

                }).catch(err=>{

                })
            },
            changeCategorySelect(){
                if(!this.pageSelectList.categoryList || Object.keys(this.pageSelectList.categoryList)==0){
                    return
                }
                let categoryList = this.pageSelectList.categoryList[this.language];
                let sourceValueList = (categoryList || []).map(f=>{
                    return {name:f.label,code:f.code};
                })
                //特殊处理仅显示
                this.generalFieldConfig.forEach(c=>{
                    let {sectionCode} = c;
                    if(sectionCode != 'ProductInfo'){
                        return
                    }
                    c.fieldList.forEach(field=>{
                        let {fieldCode} = field;
                        if(fieldCode!='sampleCategory'){
                            return
                        }
                        field.sourceValue = sourceValueList;
                    })
                })
                
            },
            getFieldSourceValues(index){
                let {sourceValueLanguage} = this.productForm.fieldList[index];
                if(!sourceValueLanguage || sourceValueLanguage.length==0){
                    return []
                }
                if(this.viewPage == 'preview'){
                    return this.handlerSourceValue(sourceValueLanguage[0].sourceValue);
                }else{
                    return sourceValueLanguage[0].coverSourceValue || [];
                }
            },
            handlerSourceValue(sourceValue){
                let listOfValuesDataList = [];
                if(!sourceValue || sourceValue.length==0){
                    return listOfValuesDataList;
                }
                try{
                    listOfValuesDataList = sourceValue.map(v=>JSON.parse(v));
                    //console.log("listOfValuesDataList",listOfValuesDataList)
                }catch(e){
                    console.log("转换json异常",e)
                }
                return listOfValuesDataList;
            },
            queryTemplate(templateId,useNew=false){
                this.pageLoading = true;
                let formPurpose = 'Product';
                let apiReq = useNew?api.getSampleNewTemplateList({templateId,formPurpose}) : api.geSampleTemplateList({templateId,formPurpose});
                apiReq.then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        if(res.data.data.length==0){
                            this.pageLoading = false;
                            this.disabledSaveBtn = true;
                            this.$notify.error("Load Template Fail!");
                            return;
                        }
                        let {fieldList,generalFieldConfig,customerGroupCode,buCode,customerCategory} = res.data.data[0];
                        if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                            this.$notify.warning("No template data")
                            return;
                        }
                        this.$emit("getTemplateInfo",res.data.data[0]);
                        this.fieldList = fieldList;
                        // this.generalFieldConfig = generalFieldConfig
                        this.generalFieldConfig = this.getSpecificSections(generalFieldConfig);
                        this.activeNames = generalFieldConfig.map(g=>g.sectionCode);
                        this.productForm.templateId= useNew ? res.data.data[0]['templateId'] : templateId;
                        this.productForm.customerGroupCode = customerGroupCode;
                        this.productForm.productLineCode = buCode;
                        this.renderPageComponent();
                        this.renderNavList();
                        this.initSelectList();
                        this.productForm.sampleCategory = customerCategory;
                    }else{
                        this.disabledSaveBtn = true;
                        this.$notify.error("Load Template Fail!");
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    this.pageLoading = false;
                })
            },
            btnRole({permissions},code){
                if(!this.productForm.id && this.viewPage!='preview' && code=='Edit' ){
                    return true;
                }
                return (permissions || []).map(p=>p.action).includes(code);
            },
            renderNavList(){
                let navList = [];
                (this.generalFieldConfig || []).forEach((gen,index)=>{
                    let {sectionLabel,sectionCode, fieldList} = gen;
                    if(this.createNew && sectionCode=='BillOfMaterials'){
                        return
                    }
                    // 检查是否有必填项
                    let isRequired = (fieldList || []).some(field => field.isRequired == 1);
                    let nav = {name:sectionLabel,id:"#"+sectionLabel,seq:index+1,active:false,isSub:false,show:true,isRequired:isRequired, pass: false};
                    navList.push(nav);
                })
                this.navList = navList;
                this.showNavList = true;
            },
            renderPageComponent(){
                let list = [];
                this.fieldList.forEach(fl=>{
                    let {tips,fieldCode,id,dispalyName,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = fl;
                    let materialExtendFieldValue = ['Checkbox','Select2','DateRange'].includes(fieldType)?[]: '';
                    list.push({
                        fieldCode,
                        dffFormFieldId: id,
                        dffFormFieldNameEn:dispalyName,
                        materialExtendFieldValue ,
                        mandatoryFlag,
                        fieldType,
                        sequence,
                        sourceValueLanguage,
                        tips
                    })
                    if(this.pageId){
                        let dbObj = this.productForm.fieldList.find(l=>l.dffFormFieldId==id);
                        if(!dbObj){
                            this.productForm.fieldList.push({
                                fieldCode,
                                dffFormFieldId: id,
                                dffFormFieldNameEn:dispalyName,
                                materialExtendFieldValue,
                                fieldType,
                                sequence,
                                sourceValueLanguage,
                                tips
                            });
                        }
                    }
                });
                list.sort((a,b)=> {
                    return a.sequence - b.sequence
                })
                if(this.pageId){
                    this.productForm.fieldList.forEach(f=>{
                        let {dffFormFieldId,materialExtendFieldValue} = f;
                        let templateObj = list.find(l=>l.dffFormFieldId==dffFormFieldId);
                        if(templateObj){
                            let {tips,dffFormFieldNameEn,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = templateObj;
                            this.$set(f,'dffFormFieldNameEn',dffFormFieldNameEn);
                            this.$set(f,'sourceValueLanguage',sourceValueLanguage);
                            this.$set(f,'mandatoryFlag',mandatoryFlag);
                            this.$set(f,'fieldType',fieldType);
                            this.$set(f,'sequence',sequence);
                            this.$set(f,'tips',tips);
                            let defaultMaterialExtendFieldValue = ['Checkbox','Select2','DateRange'].includes(fieldType)?[]: '';
                            if(!materialExtendFieldValue){
                                this.$set(f,'materialExtendFieldValue',defaultMaterialExtendFieldValue);
                            }
                        }
                    })
                    this.productForm.fieldList.sort((a,b)=> {
                        return a.sequence - b.sequence;
                    })
                }else{
                    //设置默认值
                    this.generalFieldConfig.forEach(gf=>{
                        let {fieldList} = gf;
                        (fieldList || []).forEach(f=>{
                            let {fieldCode,defaultValue} = f;
                            this.$set( this.productForm,this.lowerField(fieldCode),defaultValue);
                        })
                    })
                    this.$set(this.productForm,'fieldList',list);
                }
                this.showCompoment = true;
            },
            createTRF(){
                let customerGroupCode = this.productForm.buyerCustomerGroupCode;
                let bossNo = this.productForm.buyerCustomerBossNo;
                let customer = {
                    bossNo,
                    customerGroupCode
                }
                customer = encodeURIComponent(JSON.stringify(customer));
                let ids = [this.productForm.id];
                let bu = this.productForm.productLineCode;
                let actionType="individualSample";
                let flag =1;
                this.$router.push({
                    path:'/trf/trfForm',
                    query:{flag,actionType,ids,customer,bu}
                })
            },
            handlerCancelProduct(){
                this.actionProduct('Cancel',()=>{
                    this.backToList(false);
                });
            },
            changeAssign(){
                this.showChangeAssignee = true;
            },
            saveAssigneeId(){
                this.productForm.assigneeId = this.assigneeId;
                this.showChangeAssignee = false;
                this.saveProduct();
            },
            rejectSuccess(approvedType){
                let param ={
                    sampleId: this.productForm.id,
                    objectId : this.productForm.id,
                    approvedType:'product',
                    approvedStatus:2
                }
                this.rejectShow = false;
                api.approved(param).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        this.$notify.success("Success")
                        this.queryProductDetail();
                    }else{
                        this.$notify.error("Fail")
                    }
                }).catch(err=>{
                    this.$notify.error("Fail")
                })
            },
            approvedObj(approvedType,approvedStatus){
                if(this.productForm.approvedStatus == approvedStatus){
                    //同状态的不需要再次操作
                    return;
                }
                let tips = "";
                if(approvedStatus==0){
                    tips = "Return"
                }
                if(approvedStatus==1){
                    tips = "Approve"
                }
                if(approvedStatus==2){
                    tips = "Reject"
                }
                this.$confirm(tips+' the product?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    if(approvedStatus==2){
                        this.rejectShow = true;
                        return;
                    }
                    let param ={
                        sampleId: this.productForm.id,
                        objectId : this.productForm.id,
                        approvedType,
                        approvedStatus
                    }
                    this.handlerApproved(param);
                }).catch(err=>{})
            },
            handlerApproved(param,refreshPage=false){
                api.approved(param).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        let {data} = res.data;
                        if(data=='confirm'){
                            //需要弹窗确认，再次请求，然后刷新整个页面
                            this.$confirm('All info has been approved. Please confirm you would like to set the product status to "Approved"',
                                'Tips', {
                                    confirmButtonText: 'Confirm',
                                    cancelButtonText: 'Cancel',
                                    type: 'warning'
                                }).then(() => {
                                param['checkApproved'] = false;
                                this.handlerApproved(param,true);
                            }).catch(err=>{})
                            return;
                        }
                        this.$notify.success("Success")
                        this.queryProductDetail();
                    }else{
                        this.$notify.error("Fail")
                    }
                }).catch(err=>{
                    this.$notify.error("Fail")
                })
            },
            actionProduct(action,callback){
                this.$confirm(action+' the product?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    let {id} = this.productForm;
                    api.actionSamples({ids:[id],action}).then(res=>{
                        if(res.status==200 && res.data){
                            this.$notify.success("Success");
                            if(callback){
                                callback();
                            }else{
                                window.location.reload();
                            }
                        }else{
                            this.$notify.error(res.message || "Operation Fail");
                        }
                    }).catch(err=>{
                    })
                }).catch(() => {
                });

            },
            handlerImage(cloudIds){
                cloudIds.forEach(cloudID=>{
                    api.fileApi.downloadFile(cloudID).then(res=>{
                        if(res.data && res.data.data){
                            let imageUrl = res.data.data;
                            this.dataList.uploadImgList.push({
                                id:Math.random(),
                                cloudID,
                                imageUrl
                            })
                        }
                    })
                })
            },
            handlerCopyData(oldData,newData){
                let {id} = newData;
                oldData['id'] = id;
                oldData.permissions = [{action:'Edit'}];
                return oldData;
            },
            handlerCopy(id){
                this.pageLoading = true;
                api.copySample({id}).then(res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        let dbData = res.data.data;
                        let {assigneeId, cloudId,id} = dbData;
                        this.pageId = id;
                        this.createNew = true;
                        this.assigneeId = assigneeId;
                        if(cloudId && cloudId.length>0){
                            this.handlerImage(cloudId);
                        }
                        this.productForm = dbData;
                        this.$emit("getInfo",this.productForm);
                        this.queryTemplate(this.productForm.templateId,true)
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no product detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    console.log("queryMaterialDetail err",err)
                    this.$notify.warning("Query no product detail data");
                })
            },
            queryProductDetail(createNew = false){
                this.pageLoading = true;
                let {id} = this.productForm;
                api.querySampleDetail({id,dataType:'product'}).then(async res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        if(createNew){
                            this.createNew = true;
                            this.productForm = res.data.data;
                            this.queryTemplate(this.templateParam.templateId)
                            return;
                        }
                        let dbData = res.data.data;
                        if(this.action == 'copy'){
                            delete res.data.data.id;
                            let copyObj = await api.querySampleDetail({id:'',dataType:'product'});
                            if(copyObj.status==200 && copyObj.data && copyObj.data.data){
                                dbData = this.handlerCopyData(res.data.data,copyObj.data.data)
                            }
                        }
                        let {assigneeId, cloudId} = dbData;
                        this.assigneeId = assigneeId;
                        if(cloudId && cloudId.length>0){
                            this.handlerImage(cloudId);
                        }
                        this.productForm = dbData;
                        this.$emit("getInfo",this.productForm);
                        if(this.reloadCare){
                            this.showCare = true;
                        }
                        this.queryTemplate(this.productForm.templateId,this.action == 'copy')
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no product detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    console.log("queryMaterialDetail err",err)
                    this.$notify.warning("Query no product detail data");
                })
            },
            addCare(addCare){},
            delCare(careIndex){},
            fileExceed(files,fileList){
                this.$notify({title: 'Message', message: 'You can upload 5 files at most', type: 'warning'});
            },
            handleAvatarSuccess(res, file,fileList) {
                //console.log("fl,",res,file,fileList);
                if(res.status!=200){
                    this.$notify.error("Upload fail");
                    return;
                }
                res.data.forEach(f=>{
                    let {id,cloudID} = f;
                    let imageUrl = URL.createObjectURL(file.raw);
                    let obj = {
                        id,cloudID,imageUrl
                    }
                    this.dataList.uploadImgList.push(obj);
                })
            },
            delImg(img,imgIndex){
                this.dataList.uploadImgList.splice(imgIndex,1);
            },
            beforeAvatarUpload(file) {
                const isJPG = ['image/png','image/jpeg'].includes(file.type);
                const isLt20M = file.size / 1024 / 1024 < 20;

                if (!isJPG) {
                    this.$message.error('The file type must be .jpeg,.jpg,.png');
                }
                if (!isLt20M) {
                    this.$message.warning('file size should be less than 20 MB');
                }
                return isJPG && isLt20M;
            },
            backToList(showTips = true){
                if(!showTips || (!this.createNew && !this.btnRole(this.productForm,'Edit'))){
                    window.location.href = '/web/customer/newProduct/list';
                }else{
                    this.$confirm("Please make sure you have saved the information before you close the window.",'Tips',{
                        confirmButtonText: 'Confirm',
                        cancelButtonText: 'Cancel',
                        type: 'warning'
                    }).then(res=>{
                        window.location.href = '/web/customer/newProduct/list';
                    })
                }
            },
            checkDffPercentage(){
                let validPercentage = true;
                let scRef = '';
                this.productForm.fieldList.forEach(field=>{
                    let {fieldCode,fieldType} = field;
                    if((fieldType||'').toUpperCase() =='PERCENTAGE'){
                        let refCode = 'percentage_'+fieldCode;
                        let checked = this.$refs[refCode][0].validatePercentage();
                        if(!checked){
                            scRef = refCode;
                            validPercentage = false;
                        }
                    }
                });
                if(!validPercentage){
                    this.$refs[scRef][0].$el.scrollIntoView({
                        block: 'center',
                        behavior: 'smooth'
                    });
                }
                return validPercentage;
            },
            saveProduct(submitAction){
                this.$refs['productForm'].validate((valid) => {
                    if(!valid){
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.trfValidateError'),
                            type: 'warning'
                        });
                        return
                    }
                    //单独校验所有percentage
                    let validPercentage = this.checkDffPercentage();
                    if(!validPercentage){
                        return;
                    }
                    let param = Object.assign({},this.productForm);
                    param['dataType'] = 'product';
                    this.pageLoading = true;
                    if(this.$refs.care_label_info){
                        let careLabelData = this.$refs.care_label_info[0].getSaveData();
                        if(careLabelData){
                            param['careLabelList'] = [careLabelData];
                        }
                    }

                    if(this.dataList.uploadImgList){
                        param['cloudId'] = this.dataList.uploadImgList.map(img=>img.cloudID);
                    }

                    api.createMaterial(param).then(res=>{
                        this.pageLoading = false;
                        if(res.status==200 && res.data && res.data.data){
                            if(submitAction){
                                this.actionProduct('Submit');
                                return;
                            }
                            let {id} = res.data.data;
                            window.location.href = '/#/customer/newProduct/detail?id='+id;
                            window.location.reload()
                        }else{
                            this.$notify.error(res.message || 'Save Fail');
                        }
                    }).catch(err=>{
                        this.pageLoading = false;
                        console.log("save product err",err)
                    })
                })
            },
            getCommentIcon(productForm,type){
                let {commentsNum} = productForm;
                //没有对话信息
                if(!commentsNum || commentsNum.length==0){
                    return 'el-icon-chat-square';
                }else {
                    return 'el-icon-s-comment';
                }
            },
            showReportComment(type){
                //对应类型的chat数据
                this.showChat = true;
            },
            getApprovedClass(btnType){
                let {approvedStatus} = this.productForm;
                if(!approvedStatus || approvedStatus==0){
                    return 'approve_gary';
                }
                if(approvedStatus==1){
                    if(btnType=='Approved'){
                        return 'approve_green';
                    }
                    if(btnType=='Reject'){
                        return 'approve_gary';
                    }
                }
                if(approvedStatus==2){
                    if(btnType=='Approved'){
                        return 'approve_gary';
                    }
                    if(btnType=='Reject'){
                        return 'approve_red';
                    }
                }
            },
            calcCommentStyle(){
                setTimeout(()=>{
                    let ProductInfo = this.generalFieldConfig.find(g=>g.sectionCode=='ProductInfo');
                    if(!ProductInfo || !ProductInfo.sectionCode){
                        return
                    }
                    let titleDom = document.querySelector(".title_ProductInfo");
                    let leftPx = titleDom.offsetWidth-0+5;
                    document.querySelector("span.productInfoApproved").style.left = leftPx - 5 +'px';
                },500)
            },
            // 获取Document tests 中特定的section
            getSpecificSections(generalFieldConfig) {
                let filtered = generalFieldConfig.filter(item =>
                    ['Supplier', 'Manufacturer', 'Service', 'CareLabel', 'ProductInfo', 'ProductDetail'].includes(item.sectionCode)
                );

                // 合并 ProductInfo 和 ProductDetail 的 fieldList
                let productInfo = filtered.find(item => item.sectionCode === 'ProductInfo');
                let productDetail = filtered.find(item => item.sectionCode === 'ProductDetail');
                if (productInfo && productDetail) {
                    productInfo.fieldList = [...(productInfo.fieldList || []), ...(productDetail.fieldList || [])];
                    filtered = filtered.filter(item => item.sectionCode !== 'ProductDetail');
                }

                // 将合并后的 ProductInfo 放在第一位，CareLabel 放在第二位
                filtered.sort((a, b) => {
                    if (a.sectionCode === 'ProductInfo') return -1;
                    if (b.sectionCode === 'ProductInfo') return 1;
                    if (a.sectionCode === 'CareLabel') return -1;
                    if (b.sectionCode === 'CareLabel') return 1;
                    return 0;
                });

                return filtered;
            },
            // 更新comment
            updateCommentList() {
                this.$refs.commentList.getCommentList(); 
            },
             // 检查必填项是否已填写
            checkRequiredFields(sectionCode) {
                let config = this.generalFieldConfig.find(g => g.sectionCode === sectionCode);
                if (!config || !config.fieldList) return true;

                return config.fieldList.every(field => {
                    if (field.isRequired == 1) {
                        let fieldValue = this.productForm[this.lowerField(field.fieldCode)];
                        return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
                    }
                    return true;
                });
            },

            // 更新 navList 的 pass 字段
            updateNavListPass() {
                this.navList.forEach(nav => {
                    nav.pass = this.checkRequiredFields(nav.id.replace('#', ''));
                });
            },

            // 在表单值变化时调用
            handleFormChange() {
                this.updateNavListPass();
            },
            delImgDialog(item,imgIndex){
                this.delImgItem = item;
                this.delImgIndex = imgIndex;
                this.deleteDialogVisible = true;
            },
            delImgConfirm(){
                this.delImg(this.delImgItem,this.delImgIndex);
                this.deleteDialogVisible = false;
            },
            openImgDialog(item){
                this.imgDialogVisible = true;
                this.imgDialogUrl = item.imageUrl; 
                console.log('imgDialogUrl',this.imgDialogUrl)
            }
            
        },
        mounted() {

        },
        created() {
            this.initPage();
        },
        watch: {
            showCompoment:{
              immediate:false,
              handler(newV){
                  if(newV){
                      this.calcCommentStyle();
                  }
              }
            },
            language: {
                handler: function(newVal, oldVal) {
                    if(newVal=='zh-CN'){
                        this.languageId=2;
                    }else{
                        this.languageId=1;
                    }
                    this.changeCategorySelect();
                },
                immediate: true
            },
             // 监听表单变化
            productForm: {
                handler: 'handleFormChange',
                deep: true
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
            permissionList() {
                return {
                    approvedBtn:this.vaildData(this.permission['sgs:samples:approved'], true)
                };
            },
        },
        props: {
            viewPage:'',
            templateData:{}
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {
            Percentage,
            RejectProvided, ChatView, BillOfMaterials,
            NavList, ReportCertificate,
            CareLabelInfo,WorkBook,
            CommentList
        }
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";
    
    .sgs_smart_product_productInfo {
        .el-collapse{
            border-bottom: none !important;
            border-top: none !important;
        }
        .el-collapse-item__wrap,.el-collapse-item__header{
            border-bottom: none !important;
        }
        p.sample_tips{
            color:#c8c8c8;
        }
        .title-wrap{
            display: flex;
            align-items: baseline;
        }
        i.approved_icon{
            font-size: 18px;
            cursor: pointer;
            padding:2px 5px
        }
        .approve_gary{
            color: #8c8c8c !important;
        }
        i.more_icon{
            border: solid 1px #8c8c8c;
            border-radius: 50%;
            padding: 0;
            font-size: 14px;
            position: relative;
            top: -2px;
            color: white;
            background: #8c8c8c;
        }

        .img_del{
            position: absolute;
            right: calc(50% - 27px);
            top: calc(50% - 7px);
            cursor: pointer;
        }
        .img_add{
            position: absolute;
            right: 25px;
            top: 5px;
            cursor: pointer;
        }
        .img_view{
            position: absolute;
            right: calc(50% + 17px);
            top: calc(50% - 7px);
            cursor: pointer;
        }
       
        span.productInfoApproved{
            // position: absolute;
            // top: 10px;
        }
        .product-sub-title{
            font-size: 18px;
            font-weight: bold;
            padding-bottom: $inline-element-spacing;
        }
        .product-upload{
            background-color: #ffffff;
            margin-top: $module-margin-vertical;
            margin-left: $module-padding-horizontal;
            padding: $module-padding-horizontal;
            position: relative;
            
            .avatar-uploader-small{
                position: absolute;
                right: 20px;
                top: 24px;
                z-index: 35;
            }
            .avatar-uploader .el-upload {
                border: 1px dashed #d9d9d9;
                border-radius: 10px;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }
            .avatar-uploader .el-upload:hover {
                border-color: #FF6600;
                color: #FF6600;
            }
            .avatar-uploader-icon {
                font-size: 28px;
                color: #8c939d;
                width: 100%;
                height: 160px;
                line-height: 160px;
                text-align: center;
            }
            .avatar-uploader-mini-icon{
                color: #FF6600;
            }
            .avatar {
                width: 100%;
                height: 160px;
                display: block;
            }
            .el-upload{
                width: 100%;
            }

        }
        .product-comment{
            margin-top: $module-margin-vertical;
            margin-left: $module-padding-horizontal;
            padding: $module-padding-horizontal;
            background-color: #ffffff;
        }
        /* 重置input边距 */
        .el-input__inner {
            padding: 0;
        }
        .el-form-item__label {
            color: $text-color-value;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .el-collapse-item__content {
            padding-bottom: 0;
        }
        .el-row {
            display: flex;
            flex-wrap: wrap;
        }
        .el-date-editor {
            width: 100%;
        }
        .el-form-item__content {
            line-height: 40px;
            display: flex;
            align-items: flex-end;
            // height: 40px;
        }
        .el-date-editor i.el-range__icon {
            left: 0;
        }
        .mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center; 
        }
        .carousel-img{
            object-fit: contain; 
            height: 150px; 
            width: 100%;
        }
        .el-carousel__indicators--outside button {
            background-color: #999!important; 
        }
    }
    .img-zoom{
       text-align: center;
       width: 100%;
       img{
            max-width: 100%;
       }
    }
</style>