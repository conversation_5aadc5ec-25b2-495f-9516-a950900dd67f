<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.documentLibrary')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.documentLibrary')}}</h1>
        <el-card shadow="never" class="sgs-box">
            <el-row>
                <el-form :inline="true" size="medium" label-width="200px" label-position="left">
                    <el-form-item>
                        <el-input clearable
                                v-model="query.documentTitle"
                                :placeholder="$t('documentLibrary.name')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-select clearable v-model="query.documentType" @change="selectRoleChange" style="width: 100%;" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                            <el-option v-for="item in documentTypeList" :key="item.id" :lable="item.id"
                                    :value="item.name"></el-option>
                        </el-select>
                    </el-form-item>
                  <el-form-item>
                    <el-input clearable
                              v-model="query.updateUser"
                              :placeholder="$t('common.operator')"
                    ></el-input>
                  </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <el-row>
                    <el-button type="primary" @click="addRow" size="medium">{{$t('operation.add')}}</el-button>
                </el-row>
                <el-table
                        :data="tableData"
                        style="width: 100%"
                        size="medium">
                    <el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="programName"
                            :label="$t('documentLibrary.theme')"
                            width="200">
                    </el-table-column>
                    <el-table-column
                            prop="documentType"
                            :label="$t('wel1.documentType')"
                            width="200">
                        <template slot-scope="scope">
                            <span v-if="scope.row.documentType === 2">{{ $t('wel1.knowledgeSharing') }}</span>
                            <span v-if="scope.row.documentType === 1">{{ $t('wel1.documentSharing') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="documentTitle"
                            :label="$t('documentLibrary.name')"
                            :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="documentUrl"
                            :label="$t('documentLibrary.linkUrl')"
                            :show-overflow-tooltip="true"
                            width="180">
                    </el-table-column>
                  <el-table-column
                      prop="updateUser"
                      :label="$t('common.operator')"
                      width="120">
                  </el-table-column>
                  <el-table-column
                      prop="updateTime"
                      :label="$t('common.operationTime')"
                      width="120">
                  </el-table-column>
                    <el-table-column
                            prop="documentSort"
                            :label="$t('documentLibrary.documentSort')"
                            width="80"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="status"
                            :label="$t('common.status.title')"
                            width="80"
                            align="center">
                        <template slot-scope="scope">
                            <el-tooltip
                                    :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                    placement="top">
                                <el-switch
                                        v-model="scope.row.status"
                                        active-color="#ff6600"
                                        inactive-color="#c8c8c8"
                                        :active-value="1"
                                        :inactive-value="0"
                                        @change="changeStatus(scope.row)">
                                </el-switch>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="pin"
                        label="Pin"
                        width="80"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-tooltip
                                    :content="scope.row.pin==1?$t('common.status.enable'):$t('common.status.disable')"
                                    placement="top">
                                <el-switch
                                        :disabled="scope.row.status-0!=1"
                                        v-model="scope.row.pin"
                                        active-color="#ff6600"
                                        inactive-color="#c8c8c8"
                                        :active-value="1"
                                        :inactive-value="0"
                                        @change="changePin(scope.row)">
                                </el-switch>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="260"
                            align="center">
                        <template slot-scope="scope">
                            <el-button type="text" @click="detailRow(scope.row)">
                                {{$t('operation.edit')}}
                            </el-button>
                            <el-button @click="removeRow(scope.row)" type="text">
                                {{$t('operation.remove')}}
                            </el-button>
                            <el-button @click="authDialog(scope.row)" type="text">
                                {{$t('operation.auth')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>

        <el-drawer :title="title"  :before-close="closeDrawer" :visible.sync="dialogFormVisible" size="70%">
            <el-form ref="form" :model="form" label-width="200px" label-position="left" size="medium" class="sgs-form">
                <div class="sgs-group">
                    <h3>{{$t('info.base')}}</h3>
                </div>

                <el-form-item :label="$t('wel1.documentType')"
                              :rules="{ required: true, message: $t('documentLibrary.validate.typeBlur'), trigger: 'blur' }"
                              prop="documentType">
                    <el-radio-group v-model="form.documentType" @change="documentTypeChange">
                        <el-radio :label='2'>{{ $t('wel1.knowledgeSharing') }}</el-radio>
                        <el-radio :label='1'>{{ $t('wel1.documentSharing') }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item :label="$t('vipProgram.name')" v-if="isShowPhoto">
                    <el-select v-model="form.themeId" clearable  filterable  style="width: 100%;" @change="selectProgram" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(vipProgram,index) in options" :label="vipProgram.programName"
                                   :value="vipProgram.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item :label="$t('wel1.pbiType')" prop="pbiType" v-if="isShowPhoto">
                    <el-radio name="pbiType" v-model="form.pbiType" :label='0'>{{ $t('wel1.defaultPbiType') }}</el-radio>
                    <el-radio name="pbiType" v-model="form.pbiType" :label='1'>{{ $t('wel1.AflPbiType') }}</el-radio>
                </el-form-item>

                <el-form-item label="Parameters" v-if="isShowPhoto && form.pbiType==1">
                    <el-form  :model="form" inline>
                        <el-form-item label="Role" prop="role">
                            <el-select
                                    filterable clearable
                                    v-model="form.role"
                                    @change="changeRole"
                            >
                                <el-option
                                    v-for="(op,ind) in roleObj.roleList"
                                    :key="'role_'+ind"
                                    :label="op.name"
                                    :value="op.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="Role value" prop="roleCode">
                            <el-select
                                    filterable clearable
                                    v-model="form.roleCode"
                                    @change="changeRoleValue"
                            >
                                <el-option
                                    v-for="(op,ind) in roleObj.roleValueMap[form.role]"
                                    :key="'role_'+ind"
                                    :label="op.name"
                                    :value="op.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item lable="" prop="roleValue.value">
                            <el-input clearable placeholder="Please input Role value" v-if="form.roleCode=='Customize'" v-model="form.roleValue.value"></el-input>
                        </el-form-item>
                    </el-form>
                </el-form-item>

                <el-form-item :label="$t('wel1.cpCustomerReport')"  v-if="isShowPhoto">
                    <el-checkbox-group v-model="isCustomerReportFlag" style="padding: 4px;" >
                      <el-checkbox name="type"></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item :label="$t('documentLibrary.imageCloudId')" v-if="isShowPhoto"
                              :rules="{ required: false, message: $t('documentLibrary.validate.imageUrl'), trigger: 'blur' }"
                              prop="imageCloudId">
                    <el-upload
                            ref="uploadImg"
                            class="avatar-uploader"
                            action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                            drag
                            :on-success="handleAvatarSuccessOfImg"
                            :show-file-list="false"
                            :before-upload="beforeAvatarUpload"
                            :on-remove="handleRemove">
                            <div class="">
                                <i class="el-icon-close avatar-close-icon"
                                   v-if="currentProgram && imageUrl && currentProgram.layoutType === 30"
                                   @click.stop="handleRemove(form.imageCloudId);"></i>
                                <img v-model="form.imageCloudId" v-if="imageUrl" :src="imageUrl" class="avatar" />
                                <i class="el-icon-plus avatar-uploader-icon"></i>
                            </div>
                    </el-upload>
                    <el-input type="hidden" v-model="form.imageCloudId">{{ imageUrl }}</el-input>
                </el-form-item>
                <el-form-item :label="$t('documentLibrary.documentSort')"
                              :rules="{ required: true, message: $t('documentLibrary.documentSort'), trigger: 'blur' }"
                              prop="documentSort">
                    <el-input v-model="form.documentSort"
                              maxlength="200"
                              type="number"></el-input>
                </el-form-item>
<!--                <el-form-item :label="$t('documentLibrary.documentDec')"
                              :rules="{ required: true, message: $t('documentLibrary.documentDec'), trigger: 'blur' }"
                              prop="documentDec">
                    <el-input v-model="form.documentDec"
                              maxlength="200"
                              type="textarea"></el-input>
                </el-form-item>-->
                <el-row>
                  <el-col :span="16">
                    <h5 class="top-title">{{$t('language.manage')}}</h5>
                  </el-col>
                  <el-col :span="8">
                    <div class="text-right">
                      <el-button type="primary" @click="addLanguage" size="small">{{$t('language.add')}}</el-button>
                    </div>
                  </el-col>
                </el-row>
              <!--多语言Table-->
              <el-table
                  :data="libraryLangDatas"
                  style="width: 100%">
                <el-table-column
                    prop="languageId"
                    :label="$t('language.name')"
                    width="180">
                  <template slot-scope="scope">
                    <span>{{ scope.row.languageId === LanguageEnums.EN.code?
                        language=== LanguageEnums.EN.name? LanguageEnums.EN.enLabel:LanguageEnums.EN.cnLabel
                        :  language=== LanguageEnums.CN.name? LanguageEnums.CN.cnLabel:LanguageEnums.CN.enLabel  }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                    prop="documentTitle"
                    :label="$t('documentLibrary.name')"
                >
                </el-table-column>
                <el-table-column
                    prop="documentDec"
                    :label="$t('documentLibrary.documentDec')"
                >
                </el-table-column>
                <el-table-column
                    prop="documentUrl"
                    :label="$t('documentLibrary.linkUrl')"
                >
                </el-table-column>
                <el-table-column
                    prop="attachmentList"
                    :label="$t('attachment.title')"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.attachmentList.length>0">{{scope.row.attachmentList.length}}{{$t('attachment.numberAttachments')}}  </span>
                  </template>
                </el-table-column>


                <el-table-column  :label="$t('language.default')"
                                  width="120">
                  <template slot-scope="scope">
                    <el-tooltip
                        :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')"
                        placement="top">
                      <el-switch
                          v-model="scope.row.isDefault"
                          active-color="#ff6600"
                          inactive-color="#D9D9D9"
                          :active-value="1"
                          :inactive-value="0"
                          @change="changeDefault(scope.row)">
                      </el-switch>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column
                    :label="$t('operation.title')"
                    width="120">
                  <template slot-scope="scope">
                    <el-button @click="handleLanguageEditClick(scope.row)" type="text">{{$t('operation.edit')}}</el-button>
                    <el-button @click="handleLanguageDeleteClick(scope.row)" type="text">
                      {{$t('operation.remove')}}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

                <div class="sgs-bottom">
                    <el-button @click="closeDrawer">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">
                        {{$t('operation.submit')}}
                    </el-button>
                </div>
            </el-form>

          <!--多语言新增/编辑-->
          <el-dialog
              :title="$t('language.add')"
              :close-on-click-modal="false"
              :close-on-press-escape="false"
              :visible.sync="langaugeDialogVisible"
              width="600"
              append-to-body>
            <el-form ref="languageForm" :model="languageForm" label-width="200px" label-position="left"  size="medium" class="sgs-form" :rules="languageRules">

              <el-form-item :label="$t('language.curLanguage')" prop="languageId" v-if="langaugeDialogVisible">
                <el-select filterable clearable v-model="languageForm.languageId" style="width:100%"
                           :placeholder="$t('operation.pleaseSelect')"
                           :no-data-text="$t('NoData')" clearable>
                  <el-option v-for="(languageObj,index) in languageDatas" :label="language === 'zh-CN'? languageObj.cnLabel:languageObj.enLabel"
                             :value="languageObj.code"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('documentLibrary.name')" v-if="langaugeDialogVisible"
                            :rules="{ required: true, message: $t('documentLibrary.validate.nameBlur'), trigger: 'blur' }"
                            prop="documentTitle" >
                <el-input v-model="languageForm.documentTitle"
                          maxlength="200"
                          type="tree" clearable></el-input>
              </el-form-item>
<!--              <el-form-item :label="$t('vipProgram.name')" prop="programName">
                <el-input v-model="languageForm.programName"
                          maxlength="200"></el-input>
              </el-form-item>-->
              <el-form-item :label="$t('documentLibrary.linkUrl')" v-if="langaugeDialogVisible">
                <el-input v-model="languageForm.documentUrl"
                          maxlength="300" clearable></el-input>
              </el-form-item>
              <el-form-item :label="$t('documentLibrary.documentDec')" v-if="langaugeDialogVisible"
                            :rules="{ required: true, message: $t('documentLibrary.documentDec'), trigger: 'blur' }"
                            prop="documentDec">
                <el-input v-model="languageForm.documentDec"
                          maxlength="200"
                          type="textarea" clearable></el-input>
              </el-form-item>
              <el-form-item :label="$t('language.default')" v-if="langaugeDialogVisible">
                <el-tooltip
                    :content="languageForm.isDefault==1?$t('common.yes'):$t('common.no')"
                    placement="top">
                  <el-switch
                      v-model="languageForm.isDefault"
                      active-color="#ff6600"
                      inactive-color="#D9D9D9"
                      :active-value="1"
                      :inactive-value="0"
                  >
                  </el-switch>
                </el-tooltip>
              </el-form-item>
              <el-row class="sgs-group" style="min-height: 40px">
                <h5>{{$t('info.attachment')}}</h5>
                <div class="right">
                  <el-upload
                      class="upload-demo"
                      action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                      :on-success="uploadSuccess"
                      :on-change="uploadChange"
                      :file-list="fileList"
                      :show-file-list="false">
                    <el-button size="small" type="primary">{{$t('operation.upload')}}</el-button>
                  </el-upload>
                </div>
              </el-row>
              <el-table :data="languageForm.attachmentList" width="100%"
                        :element-loading-text="$t('uploadLoadingText')"
                        element-loading-spinner="el-icon-loading"
                        v-loading="uploadLoading">
                <el-table-column
                    type="index"
                    fixed
                    label="#"
                    width="50">
                </el-table-column>
                <el-table-column
                    prop="fileName"
                    :label="$t('attachment.name')">
                </el-table-column>
                <el-table-column
                    :label="$t('operation.title')"
                    width="180">
                  <template slot-scope="scope">
                    <el-button type="text" @click="downloadAttachmentRow(scope.row)" size="small"
                               icon="el-icon-download">{{$t('operation.download')}}
                    </el-button>
                    <el-button @click="removeAttachmentRow(scope.$index)" type="text" size="small"
                               icon="el-icon-delete">{{$t('operation.remove')}}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="langaugeDialogVisible=false">{{$t('operation.cancel')}}</el-button>
              <el-button type="primary" v-loading="uploadLoading" @click="submitLanguageForm('languageForm')" >
                {{$t('operation.submit')}}
              </el-button>
            </div>
          </el-dialog>

        </el-drawer>
    </basic-container>
</template>

<script>
    import {getList, add, remove, detail,changePin,pbiLibraryDictionary} from "@/api/document/library";
    import {getPrograms} from "@/api/document/vipProgram";
    import {getCloudFileURL, getSgsCustomer} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {deepClone} from '@/util/util'
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";

    export default {
        data() {
          var validateLanguage = (rule,value,callback) => {
            if(!value){
              callback(new Error(this.$t('language.validate.selLanguageBlur')))
            }else {
              callback();
            }
          }
          var validateDocumentTitleName = (rule,value,callback) => {
            if(!value){
              callback(new Error(this.$t('documentLibrary.validate.nameBlur')))
            }else {
              callback();
            }
          }
          var validateDocumentDesc = (rule,value,callback) => {
            if(!value){
              callback(new Error(this.$t('documentLibrary.documentDec')))
            }else {
              callback();
            }
          }
            return {
                isCustomerReportFlag:false,
                editFlag:false,
                libraryLangDatas:[],
                langaugeDialogVisible:false,
                languageForm:{
                  id:'',
                  newId:'',
                  languageId:'',
                  documentTitle:'',
                  documentUrl:'',
                  documentDec:'',
                  isDefault:0,
                  attachmentList: [],
                },
                LanguageEnums:LanguageEnums,
                isShowPhoto: true,
                isShowUploadImgFlag: true,
                uploadLoading: false,
                name: "list",
                title: '',
                imageCloudId: '',
                imageUrl: '',
                hadChangeImg: '0',//修改是，是否更换了图片
                imageCloudIdOld: '',//修改时，先把原来的cloudID存起来
                dialogFormVisible: false,
                dialogAuthFormVisible: false,
                btnGuestbookSubmit: false,
                tableData: [],
                selectRow: {},
                currentProgram: {},
                roleObj:{
                    roleList:[],
                    roleValueMap:{}
                },
                form: {
                    attachmentList: [],
                    role: "",
                    roleCode:'',
                    roleValue: {
                        code: "",
                        value: ""
                    }
                },
                documentTypeList: [
                    {'id': '2', 'name': this.$t('wel1.knowledgeSharing')},
                    {id: '1', name: this.$t('wel1.documentSharing')}
                ],
                fileList: [],
                documentTypeParam: "",
                query: {},
                sort: {ascs: 'document_sort', descs: 'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                options: [],
                varlue: '',
                selectedTime: {
                    disabledDate: (time) => {
                        return time.getTime() < this.form.effectiveDate;
                    },
                },
              languageDatas:[
                LanguageEnums.EN,
                LanguageEnums.CN,
              ],
              languageRules: {
                languageId:[
                  { required: true, validator:validateLanguage, trigger: 'change' }
                ],
                documentTitle: [
                  { required: true, validator:validateDocumentTitleName, trigger: 'blur' }
                ],
                documentDec: [
                  { required: true, validator:validateDocumentDesc, trigger: 'blur' }
                ]
              },
            }
        },
        watch: {
          //监听语言变化
          language: function (newVal) {
            this.onLoad(this.page);
            this.onLoadVipProgramList();
          },
          isCustomerReportFlag: function (newVal) {
              this.$set(this.form, 'customerReportFlag', 0);
              if (newVal) {
                  this.$set(this.form,'customerReportFlag', 1);
              }
          },
        },
        methods: {
          generateRandomId() {
            const uuid = require('uuid'); // 导入uuid库
            return uuid.v4(); // 使用uuid库生成随机ID并赋值给data中的变量
          },
          setDocumentData(langForm){
            //赋值默认
            if(langForm.isDefault){
              this.$set(this.form, 'documentTitle', langForm.documentTitle);
              this.$set(this.form, 'documentDec', langForm.documentDec);
              this.$set(this.form, 'documentUrl', langForm.documentUrl);
            }
            //处理原数据的默认状态
            this.libraryLangDatas.forEach(libraryLangData => {
              debugger;
              if (objectIsNull(langForm.id)) {
                if (libraryLangData.newId != langForm.newId && langForm.isDefault===1) {
                  libraryLangData.isDefault=0;
                }
              } else {
                if (libraryLangData.id != langForm.id && langForm.isDefault===1) {
                  libraryLangData.isDefault=0;
                }
              }
            })
          },
          changeDefault(row){
            if(objectIsNull(row.id)){//用NewId
              this.libraryLangDatas.filter(item => item.newId != row.newId).forEach(obj=>{
                if(row.isDefault===1){
                  obj.isDefault=0
                }
              });
            }else{
              this.libraryLangDatas.filter(item => item.id != row.id).forEach(obj=>{
                if(row.isDefault===1){
                  obj.isDefault=0
                }
              });
            }

            if(row.isDefault===1){
              this.$set(this.form, 'documentTitle', row.documentTitle);
              this.$set(this.form, 'documentDec', row.documentDec);
              this.$set(this.form, 'documentUrl', row.documentUrl);
            }else{
              this.$set(this.form, 'documentTitle', '');
              this.$set(this.form, 'documentDec', '');
              this.$set(this.form, 'documentUrl', '');
            }
          },
          handleLanguageEditClick(row){
            this.editFlag=true;
            this.editData=row;
            this.languageForm=deepClone(row);
            this.$set(this.languageForm, 'editFlag', true)
            this.langaugeDialogVisible=true;
          },
          //删除语言
          handleLanguageDeleteClick(row){
            //判断当前行是否有id，有id的话将isDeleted置为1
            if(!objectIsNull(row.id)){//存在ID
              row.isDeleted=1;
              this.libraryLangDatas=this.libraryLangDatas.filter(item => item.isDeleted === 0);
            }else{//新增删除 使用newId处理删除
              this.libraryLangDatas=this.libraryLangDatas.filter(item => item.newId != row.newId);
            }
          },
          submitLanguageForm(form) {
            this.$refs[form].validate((valid) => {
              if(valid){
                debugger
                if (this.form.documentType == 1) {
                  let attachmentList = this.languageForm.attachmentList;
                // if(!objectIsNull(this.languageForm.attachmentList)){
                //   this.libraryLangDatas.forEach(library=>{
                //     if(!objectIsNull(library.attachmentList)){
                //       attachmentList.push(library.attachmentList);
                //     }
                //   })
                // }
                    if (validatenull(this.languageForm.documentUrl) && validatenull(attachmentList)) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('newService.validate.urlAndFileValidate'),
                            type: 'warning'
                        });
                        return false;
                    }

                    if (!validatenull(this.languageForm.documentUrl) && !validatenull(attachmentList)) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('newService.validate.urlAndFileError'),
                            type: 'warning'
                        });
                        return false;
                    }
                }



                let newData = deepClone(this.languageForm);
                if (!this.editFlag) {
                    newData.id=null;
                    newData.newId = this.generateRandomId();
                  //校验是否已存在相同语种
                  if (!validatenull(this.libraryLangDatas)) {
                    let repeatLanguaugeData = this.libraryLangDatas.filter(item => item.languageId === newData.languageId);
                    if (!objectIsNull(repeatLanguaugeData)) {//存在已有语种
                      this.$message({
                        message: this.$t('language.repeatLanguageMsg'),
                        type: 'warning'
                      });
                      return false;
                    }
                  }
                  this.setDocumentData(newData);
                  this.libraryLangDatas.push(newData);
                  this.langaugeDialogVisible = false;
                }else{//编辑 校验是否已存在相同语种
                  //校验
                  let r = this.checkLangauge(newData);
                  if (!r) {
                    return false;
                  }
                  let newGridDatas=[];
                  this.libraryLangDatas.forEach(libraryLangData => {
                    if (objectIsNull(libraryLangData.id)) {
                      if (libraryLangData.newId === newData.newId) {
                        libraryLangData = newData;
                      }
                    } else {
                      if (libraryLangData.id === newData.id) {
                        libraryLangData = newData;
                      }
                    }
                    newGridDatas.push(libraryLangData);
                  })
                  this.setDocumentData(newData);
                  this.libraryLangDatas = newGridDatas;
                  this.langaugeDialogVisible = false;
                }


              }else{
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('trf.trfValidateError'),
                  type: 'warning'
                });
                return false;
              }
            })
          },
          checkLangauge(newData) {
            //先根据ID判断，如果不存在iD的话，认为页面新增数据，再根据newId判断
            if(!objectIsNull(newData.id)){
              let repeatLanguaugeData = this.libraryLangDatas.filter(item => item.id != newData.id);
              if(!objectIsNull(repeatLanguaugeData)){
                if(repeatLanguaugeData[0].languageId===newData.languageId){
                  this.$message({
                    message: this.$t('language.repeatLanguageMsg'),
                    type: 'warning'
                  });
                  return false;
                }
              }
            }else{
              let repeatLanguaugeData = this.libraryLangDatas.filter(item => item.newId != newData.newId);
              if(!objectIsNull(repeatLanguaugeData)){
                if(repeatLanguaugeData[0].languageId===newData.languageId){
                  this.$message({
                    message: this.$t('language.repeatLanguageMsg'),
                    type: 'warning'
                  });
                  return false;
                }
              }
            }
            return true;
          },
          //添加语言
            addLanguage(){
              this.editFlag=false;
              this.languageForm.attachmentList=[];
              this.languageForm.documentTitle='';
              this.languageForm.documentUrl='';
              this.languageForm.documentDec='';
              this.languageForm.LanguageId='';
              this.languageForm.isDefault=1;
              this.languageForm.isDeleted=0;
              this.$set(this.languageForm, 'editFlag', false)
              this.langaugeDialogVisible=true;
            },
            closeDrawer(done){
                this.$refs['form'].resetFields();
                this.dialogFormVisible=false;
            },
            documentTypeChange(val) {
                if (val == 1) {
                     this.$set(this.form, 'themeId', null);
                    this.isShowPhoto = false;
                } else {
                    this.isShowPhoto = true;
                }
            },

            selectRoleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.documentTypeList.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                if (obj === "" || obj === undefined) {
                    this.documentTypeParam = null
                } else {
                    this.query.documentType = obj.name;//提交数据使用，此处可忽略
                    this.documentTypeParam = obj.id;//提交数据使用，此处可忽略
                }
            },
            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                let param = this.query;
                //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                   languageId=LanguageEnums.EN.code;
                }else{
                   languageId=LanguageEnums.CN.code;
                }
                getList(page.currentPage, page.pageSize, Object.assign(params, {
                    documentTitle: this.query.documentTitle,
                    documentType: this.documentTypeParam,
                    languageId: Number(languageId),
                    updateUser:this.query.updateUser
                })).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            handleRemove() {
                this.imageCloudIdOld = "";
                this.form.imageCloudId = "";
                this.imageCloudId = "";
                this.imageUrl = "";
            },

            selectProgram(program) {
                debugger
                if(program) {
                    this.currentProgram = this.options.filter(item => item.id === this.form.themeId)[0];
                }
            },

            //添加/编辑提交
            submitForm: function (form) {

                //判断是否存在默认语言 如没有默认语言 则将form数据清空
                let defaultData = this.libraryLangDatas.filter(item=>item.isDefault===1);
                if(objectIsNull(defaultData)){
                  this.$set(this.form, 'documentTitle', '');
                  this.$set(this.form, 'documentDec', '');
                  this.$set(this.form, 'documentUrl', '');
                }

                //修改状态下，未更换图片: 取值this.imageCloudIdOld, 更换图片：取值this.imageCloudId
                this.form.imageCloudId = this.hadChangeImg == '0' ? this.imageCloudIdOld : this.imageCloudId;

                let program = this.options.filter(item => item.id === this.form.themeId)[0];
                if(program && program.layoutType !== 30 && !this.form.imageCloudId) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('newService.validate.uploadImageError'),
                        type: 'warning'
                    });
                    return false;
                }
                if(this.form.documentType == 1){
                  this.form.pbiType=0;
                }

                this.$refs[form].validate((valid) => {
                    if (valid) {
                      let b = this.validatelanguage();
                      if (!b) {
                        return false;
                      }

                      this.form.libraryLangDatas=this.libraryLangDatas;
                        this.btnGuestbookSubmit = true;
                        add(this.form).then(res => {
                            this.btnGuestbookSubmit = false;
                            if(res.status==200){
                                this.$message({
                                    type: "success",
                                    message: this.$t('api.success')
                                });
                                this.dialogFormVisible = false;
                                this.onLoad(this.page);
                            }
                        }).catch(err=>{
                            this.btnGuestbookSubmit = false;
                        });
                    } else {
                        return false;
                    }
                });
            },
          validatelanguage() {
            //验证是否存在语言数据
            if (objectIsNull(this.libraryLangDatas)) {
              this.$message({
                type: "warning",
                message: this.$t('language.validate.selLanguageBlur')
              });
              return false;
            }
            //验证是否有语言默认
           /* let repeatLanguaugeData = this.libraryLangDatas.filter(item => item.isDefault === 1);
            if(objectIsNull(repeatLanguaugeData)){
              this.$message({
                type: "warning",
                message: this.$t('language.validate.setDefaultLanguage')
              });
              return false;
            }*/
            return true;
          },
            addRow() {
                this.isShowUploadImgFlag = false;
                this.$nextTick(function () {
                    this.isShowUploadImgFlag = true;
                })
                this.imageUrl = '';
                this.title = this.$t('documentLibrary.title.add');
                this.form = {};
                this.$set(this.form, 'pbiType', 0);//默认 default PBI
                this.isCustomerReportFlag=false;
                this.form.attachmentList = [];
                this.$set(this.form,'role','')
                this.$set(this.form,'roleCode','')
                this.$set(this.form,'roleValue',{code:'',value:''})
                this.libraryLangDatas=[];
                this.fileList = [];
                this.dialogFormVisible = true;
                this.imageCloudId = ''
            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            authDialog(row) {
                this.selectRow = row;
                // this.$router.push("{ path: /document/authorization,query: { documentId: row.id }}");
                this.$router.push({path: "/document/authorization", query: {"documentId": row.id}});
            },
            detailRow(row) {
                this.title = this.$t('documentLibrary.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form = res.data.data;
                    let {role,roleValue} = res.data.data;
                    this.form.documentType = res.data.data.documentType;
                    this.$set(this.form, 'pbiType', res.data.data.pbiType);
                    this.$set(this.form,'roleCode','');
                    this.$set(this.form,'role',role || '');
                    let roleValueCode = ((roleValue || {code:'',value:''}).code) || '';
                    let roleValueValue = ((roleValue || {code:'',value:''}).value) || '';
                    this.$set(this.form,'roleValue', {code:roleValueCode,value:roleValueValue});
                    if(this.form.roleValue && this.form.roleValue.code){
                        this.$set(this.form,'roleCode',this.form.roleValue.code);
                    }
                    this.$set(this.form, 'customerReportFlag', res.data.data.customerReportFlag);//默认 default PBI
                    this.isCustomerReportFlag=res.data.data.customerReportFlag===1?true:false;
                    this.libraryLangDatas = this.form.libraryLangDatas;
                    if (this.form.documentType == 1) {
                        this.isShowPhoto = false;
                    } else {
                        this.isShowPhoto = true;
                        this.imageCloudIdOld = res.data.data.imageCloudId;//先把原来的存起来
                        this.imageUrl = res.data.data.imageCloudIdReal;
                    }
                    this.selectProgram(this.form.themeId);
                });
            },
            handleAvatarSuccessOfImg(res, file) {
                this.hadChangeImg = '1';
                this.imageUrl = URL.createObjectURL(file.raw);
                console.log(res);

                this.imageCloudId = res.data[0].cloudID;

            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isJPG) {
                    this.$message.error(this.$t('uploadType'));
                }
                if (!isLt2M) {
                    this.$message.error(this.$t('uploadSize'));
                }
                return isJPG && isLt2M;
            },
            uploadSuccess(res, file) {
                var that = this;
                const attachment = {
                    'attachmentId': res.data[0].cloudID,
                    'fileUrl': res.data[0].path,
                    'fileName': file.name,
                    'languageId': this.languageForm.languageId,
                };
                that.languageForm.attachmentList.push(attachment);
                console.log(JSON.stringify(that.languageForm.attachmentList));
            },
            uploadChange(file, fileList) {
                if (file.status == 'ready') {
                    //开启loading效果
                    this.uploadLoading = true;
                } else {
                    this.uploadLoading = false;
                }
            },
            removeAttachmentRow(index) {
                this.languageForm.attachmentList.splice(index, 1);
            },
            downloadAttachmentRow(row) {
                getCloudFileURL(row.attachmentId).then(res => {
                    window.open(res.data, "_blank");
                });
            },
            changePin(row){
              let {id,pin} = row;
                changePin({id,pin}).then(res=>{
                    if (res.data.success == 1) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onSearch();
                    } else {
                        this.$message({
                            type: "fail",
                            message: res.data.message || this.$t('api.error'),
                        });
                    }
                })

            },
            changeStatus(row) {
                const modifyForm = {};
                this.form = row;
                modifyForm.modifiedby = this.userInfo.account;
                modifyForm.id = this.form.id;
                modifyForm.status = this.form.status;
                add(modifyForm).then(res => {
                    if (res.data.success == 1) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onSearch();
                    } else {
                        this.$message({
                            type: "fail",
                            message: res.data.message,
                        });
                    }
                });
            },
          onLoadVipProgramList(){
            let query={};
            //将当前语言放入请求中
            if(LanguageEnums.EN.name==this.language){
              query.languageId=LanguageEnums.EN.code;
            }else{
              query.languageId=LanguageEnums.CN.code;
            }
            getPrograms(query).then(res => {
              this.options = res.data.data;
            });
          },
            loadDocumentDicData(){
                pbiLibraryDictionary().then(res=>{
                    if(res.status==200 && res.data){
                        let {data} = res.data;
                        let roleList = [];
                        let roleValueMap = {};
                        (data || []).forEach(da=>{
                            let {code,name,rule} = da;
                            roleList.push({code,name});
                            let roleValueList = [];
                            (rule || []).forEach(r=>{
                                let vCode = r.code;
                                let vName = r.name;
                                roleValueList.push({code:vCode,name:vName});
                            })
                            roleValueMap[code] = roleValueList;
                        });
                        this.roleObj.roleList = roleList;
                        this.roleObj.roleValueMap = roleValueMap;
                    }
                })
            },
            changeRole(){
              this.form.roleCode = '';
              this.form.roleValue.code = '';
              this.form.roleValue.value = '';
              this.handlerRoleValue();
            },
            changeRoleValue(){
              console.log("rolecode",this.form.roleCode);
              let {roleCode} = this.form;
              this.$set(this.form.roleValue,'code',roleCode);
              this.$set(this.form.roleValue,'value',roleCode);
              if((roleCode || '').toUpperCase() =='CUSTOMIZE'){
                  this.$set(this.form.roleValue,'code','Customize');
                  this.$set(this.form.roleValue,'value','');
              }
            },
            handlerRoleValue(){
              if(!this.form.role){
                  return
              }
              let roleValueList = this.roleObj.roleValueMap[this.form.role];
              let {code,name} = roleValueList[0];
              this.$set(this.form,'roleCode',code);
              this.changeRoleValue();
            },
        },

        created() {
            this.onLoad(this.page);
            this.onLoadVipProgramList();
            this.loadDocumentDicData();
        },
        computed: {
            ...mapGetters([
                "userInfo",
                "language"
            ])
        },

    }
</script>


<style scoped>
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 148px;
        height: 148px;
        line-height: 148px;
        text-align: center;
    }

    .avatar-close-icon {
        font-size: 20px;
        color: #8c939d;
        text-align: right;
        z-index: 999;
        padding: 5px 5px;
        position: absolute;
    }

    .avatar-close-icon:hover {
        color: #FF6600;
        background-color: rgba(136, 136, 136, 0.4);
    }


    .avatar {
        height: 178px;
        display: inline;
    }
</style>
