<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_careLabelInfo" id="sgs_smart_customer_careLabelInfo">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-table
                            :data="dataList.careList"
                            border
                            fit
                            size="mini"
                    >
                        <el-table-column
                                prop="part"
                                label="Part"
                                width="100px"
                                style="padding: 20px 10px">
                        </el-table-column>
                        <el-table-column prop="imgPathYun" label="Care Image" style="padding: 20px 10px">
                            <template #header>
                                <div class="custom-table-header">
                                    <el-col :span="18">
                                        <span>Care Image(后面图标需要重新定义，现在显示不出来)</span>
                                        <el-button
                                                v-if="!createNew && careLabelResult.id"
                                                :disabled="false"
                                                type="text"
                                                style="padding:2px 5px"
                                                @click="openComment('label')"
                                        >
                                            <el-icon :class="getCommentIcon('careImage')"></el-icon>
                                        </el-button>
                                        <el-icon
                                                v-if="showApproved && [0,1].includes(labelApprovedStatus)"
                                                :disabled="false"
                                                class="el-icon-success approved_icon"
                                                :class="getApprovedClass('label','Approved')"
                                                style="color: #00d26a;"
                                                @click="approvedObj('label',1)"
                                        ></el-icon>
                                        <el-icon
                                                v-if="showApproved && [0,2].includes(labelApprovedStatus)"
                                                :disabled="false"
                                                class="el-icon-error approved_icon"
                                                :class="getApprovedClass('label','Reject')"
                                                style="color:#ff6600;"
                                                @click="approvedObj('label',2)"
                                        ></el-icon>
                                        <el-icon
                                                v-if="showApproved && [1,2].includes(labelApprovedStatus)"
                                                :disabled="false"
                                                class="el-icon-more approved_icon more_icon"
                                                @click="approvedObj('label',0)"
                                        ></el-icon>
                                    </el-col>
                                    <el-col :span="6" style="text-align: right;">
                                        <el-button type="text" :icon="Plus" @click="addCares" >
                                            Add
                                        </el-button>
                                    </el-col>
                                </div>
                            </template>
                            <template #default="{row}">
                                <img :src="row.path"/>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
                <el-col :span="12">
                    <el-table
                            :data="dataList.labelList"
                            border
                            fit
                            size="mini"
                    >
                        <el-table-column
                                prop="part"
                                label="Part"
                                width="100px"
                                style="padding: 20px 10px">
                        </el-table-column>
                        <el-table-column prop="careInstruction" label="Care Wording">
                            <template #header>
                                <div class="custom-table-header">
                                    <el-col :span="18">
                                        <span>Care Wording(后面图标需要重新定义，现在显示不出来)</span>
                                        <el-button
                                                v-if="!createNew && careLabelResult.id"
                                                :disabled="false"
                                                type="text"
                                                style="padding:2px 5px"
                                                @click="openComment('wording')"
                                        >
                                            <el-icon :class="getCommentIcon('careWording')"></el-icon>
                                        </el-button>
                                        <el-icon
                                                v-if="showApproved && [0,1].includes(wordingApprovedStatus)"
                                                :disabled="false"
                                                class="el-icon-success approved_icon"
                                                :class="getApprovedClass('wording','Approved')"
                                                style="color: #00d26a;"
                                                @click="approvedObj('wording',1)"
                                        ></el-icon>
                                        <el-icon
                                                v-if="showApproved && [0,2].includes(wordingApprovedStatus)"
                                                :disabled="false"
                                                class="el-icon-error approved_icon"
                                                :class="getApprovedClass('wording','Reject')"
                                                style="color:#ff6600;"
                                                @click="approvedObj('wording',2)"
                                        ></el-icon>
                                        <el-icon
                                                v-if="showApproved && [1,2].includes(wordingApprovedStatus)"
                                                :disabled="false"
                                                class="el-icon-more approved_icon more_icon"
                                                @click="approvedObj('wording',0)"
                                        ></el-icon>
                                    </el-col>
                                    <el-col :span="6" style="text-align: right">
                                        <el-button type="text" :icon="Plus" @click="addWording">
                                           Add
                                        </el-button>
                                    </el-col>
                                </div>
                            </template>
                            <template #default="{row}">
                                <el-input
                                        type="textarea"
                                        show-word-limit
                                        maxlength="500"
                                        clearable
                                        :rows="3"
                                        v-model="row.careInstruction"
                                ></el-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-col>
            </el-row>
        </div>

        <reject-provided
                v-if="rejectShow"
                :object-id="careLabelResult.id"
                :object-type="objectType"
                @cancelDia="rejectShow = false"
                @rejectSuccess="rejectSuccess"
        />

        <chat-view
                v-if="showChat"
                :object-id="careLabelResult.id"
                :object-type="objectType"
                @cancelDia="showChat = false"
        />

        <el-dialog
                title="Select Care Instruction"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                v-dialog-drag
                :lock-scroll="false"
                show-close
                v-model="showAddCareDia"
                width="70%"
        >
            <care-label-info-dia
                    v-if="showAddCareDia"
                    ref="careLabelInfoDiaRef"
                    :customer-code="customerGroupCode"
                    :care-label-object="careLabelResult"
                    :show-img="showImg"
                    :show-desc="showDesc"
            />
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="info" @click="showAddCareDia = false">Cancel</el-button>
                    <el-button type="primary" v-loading="saveLoading" @click="saveCareInstruction">Save</el-button>
                </div>
            </template>
        </el-dialog>
    </basic-container>
</template>
<script setup>
import {ref, onBeforeMount} from 'vue'
import CareLabelInfoDia from "./CareLabelInfoDia.vue"
import productApi from '@/api/product.ts'
import {Plus} from '@element-plus/icons-vue'




// 响应式状态
const pageLoading = ref(false)
const showAddCareDia = ref(false)
const saveLoading = ref(false)
const showImg = ref(false)
const showDesc = ref(false)
const rejectShow = ref(false)
const showChat = ref(false)
const careLabelResult = ref({})
const objectType = ref('')
const labelApprovedStatus = ref(0)
const wordingApprovedStatus = ref(0)
const dataList = ref({
    careList: [],
    labelList: [{careInstruction: ""}]
})

// Props
const props = defineProps({
    customerGroupCode: {
        type: String,
        default: ''
    },
    careLabelData: {
        type: Object,
        default: () => ({})
    },
    sampleId: {
        type: [String, Number],
        default: ''
    },
    showApproved: {
        type: Boolean,
        default: false
    },
    createNew: {
        type: Boolean,
        default: false
    },
    editRule: {
        type: Boolean,
        default: true
    },
    reloadPage: {
        type: Function,
        default: null
    }
})

// Methods
function openComment(objectTypeParam) {
    objectType.value = objectTypeParam
    showChat.value = true
}

function getApprovedClass(careType, btnType) {
    const approvedStatus = careType === 'label' ? labelApprovedStatus.value : wordingApprovedStatus.value

    if (!approvedStatus || approvedStatus === 0) {
        return 'approve_gary'
    }
    if (approvedStatus === 1) {
        return btnType === 'Approved' ? 'approve_green' : 'approve_gary'
    }
    if (approvedStatus === 2) {
        return btnType === 'Reject' ? 'approve_red' : 'approve_gary'
    }
    return ''
}

function handlerApproved(param, refreshPage = false) {
    api.approved(param).then(res => {
        if (res.status === 200 && res.data?.status === 200) {
            const {data} = res.data

            if (data === 'confirm') {
                // 需要弹窗确认，再次请求，然后刷新整个页面
                ElMessageBox.confirm(
                    'All info has been approved. Please confirm you would like to set the product status to "Approved"',
                    'Tips',
                    {
                        confirmButtonText: 'Confirm',
                        cancelButtonText: 'Cancel',
                        type: 'warning'
                    }
                ).then(() => {
                    param.checkApproved = false
                    handlerApproved(param, true)
                }).catch(() => {
                })
                return
            }

            ElNotification.success("Success")

            if (refreshPage) {
                window.location.reload()
                return
            }

            if (props.reloadPage) {
                props.reloadPage()
            } else {
                window.location.reload()
            }
        } else {
            ElNotification.error("Fail")
        }
    }).catch(() => {
        ElNotification.error("Fail")
    })
}

function rejectSuccess(approvedType) {
    const param = {
        sampleId: props.sampleId,
        objectId: props.careLabelData.id,
        approvedType,
        approvedStatus: 2
    }
    rejectShow.value = false
    handlerApproved(param)
}

function approvedObj(approvedType, approvedStatus) {
    if (
        (approvedType === 'label' && labelApprovedStatus.value === approvedStatus) ||
        (approvedType === 'wording' && wordingApprovedStatus.value === approvedStatus)
    ) {
        return
    }

    let tips = ""
    switch (approvedStatus) {
        case 0:
            tips = "Return"
            break
        case 1:
            tips = "Approve"
            break
        case 2:
            tips = "Reject"
            break
    }

    ElMessageBox.confirm(
        `${tips} the care ${approvedType}?`,
        'Tips',
        {
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            type: 'warning'
        }
    ).then(() => {
        if (approvedStatus === 2) {
            objectType.value = approvedType
            rejectShow.value = true
            return
        }

        const param = {
            sampleId: props.sampleId,
            objectId: props.careLabelData.id,
            approvedType,
            approvedStatus
        }

        handlerApproved(param)
    }).catch(() => {
    })
}

function initPage() {
    careLabelResult.value = {...props.careLabelData}

    const {
        careLabelFileId,
        careInstruction,
        labelApprovedStatus: labelApproved,
        wordingApprovedStatus: wordingApproved
    } = careLabelResult.value

    labelApprovedStatus.value = labelApproved ?? 0
    wordingApprovedStatus.value = wordingApproved ?? 0

    if (careLabelFileId) {
        productApi.careLabelApi.downloadPathById({key: careLabelFileId}).then(imgPathResp => {
            const imgPathYun = []

            if (imgPathResp.status === 200 && imgPathResp.data) {
                const path = imgPathResp.data
                imgPathYun.push({
                    path,
                    key: careLabelFileId
                })
            }

            dataList.value.careList = [...imgPathYun]
        })
    }

    if (careInstruction) {
        dataList.value.labelList = [{careInstruction}]
    }
}

let saveCareInstruction = async () => {
    saveLoading.value = true
    const saveResult = await careLabelInfoDiaRef.value.getSaveData()
    saveLoading.value = false
    if (saveResult === 'error') {
        return
    }
    // 根据fileId 获取图片base64
    const {careLabelFileId: newCareLabelFileId, imgArray, careInstruction: newCareInstruction, imgPathYun} = saveResult

    if (showImg.value) {
        dataList.value.careList = [...imgPathYun]
    }

    if (showDesc.value) {
        let preInstruction = dataList.value.labelList[0].careInstruction
            ? `${dataList.value.labelList[0].careInstruction}/`
            : ''

        let finalInstruction = preInstruction + newCareInstruction

        if (finalInstruction.length > 500) {
            finalInstruction = finalInstruction.slice(0, 500)
        }

        dataList.value.labelList = [{careInstruction: finalInstruction}]
    }

    careLabelResult.value = {
        ...careLabelResult.value,
        ...saveResult,
        careInstruction: newCareInstruction
    }

    showAddCareDia.value = false
}

function addCares() {
    showImg.value = true
    showDesc.value = false
    showAddCareDia.value = true
}

function addWording() {
    showDesc.value = true
    showImg.value = false
    showAddCareDia.value = true
}

function getSaveData() {
    const {careInstruction} = dataList.value.labelList[0]
    return {
        ...careLabelResult.value,
        careInstruction
    }
}

function getCommentIcon(type) {
    const commentsNum = type === 'careImage'
        ? props.careLabelData.commentsNum
        : props.careLabelData.wordingCommentsNum

    // 没有对话信息
    if (!commentsNum || commentsNum.length === 0) {
        return 'el-icon-chat-square'
    } else {
        return 'el-icon-s-comment'
    }
}

// 生命周期钩子
onBeforeMount(() => {
    initPage()
})

// Refs
const careLabelInfoDiaRef = ref(null)
</script>


<style lang="scss">
.sgs_smart_customer_careLabelInfo {
  .el-table__empty-block {
    padding: 15px 10px !important;
  }
  .dialog-footer{
    text-align: right;
  }

  .custom-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
