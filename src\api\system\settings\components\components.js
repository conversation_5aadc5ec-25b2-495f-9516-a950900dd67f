import request from '@/router/axios';

//获取组件树
export const getComponentsTree = () => {
    return request({
        url: '/api/sgs-mart/settings/components/tree',
        method: 'get',
        params: {}
    });
}

//获取组件列表
export const getComponentsList = () => {
    return request({
        url: '/api/sgs-mart/settings/components/list',
        method: 'get',
        params: {}
    });
}

//获取指定bu和page的配置详细
export const getPageConfigDetail = (params) => {
    return request({
        url: '/api/sgs-mart/settings/components/bu/page_config',
        method: 'post',
        data: params
    });
}

//保存配置
export const savePageConfig = (params) => {
    return request({
        url: '/api/sgs-mart/settings/components/bu/page_save',
        method: 'post',
        data: params
    });
}

