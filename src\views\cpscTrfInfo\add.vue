<template>
<el-card>
  <el-tabs v-model="tab" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="CPSC TRF Info" name="tab1">
      <keep-alive>
        <add-info ref="addInfo" v-if="show" :form-model="formModel"
        :trf-info="trfInfo" :copyType="copyType" :loading="loading" @changeLabOption="changeLabOption($event)"/>
      </keep-alive>
     
    </el-tab-pane>
    <el-tab-pane label="Certificate Entry" name="tab2" v-if="showTab2&&permission.cpscTrfInfo_entry">
      <keep-alive>
        <add-lab ref="addLab" v-if="show" :form-model="formModel" :loading="loading" :trf-info="trfInfo" />
      </keep-alive>
    </el-tab-pane>
  </el-tabs>
</el-card>
</template>
<script>
import AddInfo from "@/views/cpscTrfInfo/addInfo.vue";
import AddLab from "@/views/cpscTrfInfo/addLab.vue";
import {getDetail} from "@/api/cpscTrfInfo/cpscTrfInfo";
import {mapGetters} from "vuex";
export default {
  props: {
    // trfId: {
    //   type: String,
    //   default: null
    // },
    // // 用来区别用户是想新增复制还是更新复制
    // // 新增 ： add   更新：edit
    // copyType: {
    //   type: String,
    //   default: null
    // }
  },
  components: {AddInfo,AddLab},
  data () {
    return {
      tab:'tab1',
      loading: true,
      showTab2:false,
      show:false,
      trfInfo: {},
      trfId:null,
      copyType:null,
      
      // type: {},
      // option: {
      //   column: [{
      //     icon: 'el-icon-info',
      //     label: 'CPSC TRF Info',
      //     prop: 'tab1',
      //   }]
      // },
      formModel:{
        cpscTrfAttachList:[{}]
      },
    }
  },
  mounted() {
    this.trfId = this.$route.query.trfId
    this.copyType =this.$route.query.copyType
    this.tab =this.$route.query.tab
    debugger
    this.checkStatus();
    
  },
  created() {
    this.checkReload();
  },
  computed:{
    ...mapGetters(["permission"]),
  },
  methods: {
    changeLabOption(e){
      if(this.$refs.addLab){
        this.$refs.addLab.setPOC('POC',e.customerId)
      this.$refs.addLab.setPOC('Laboratories',e.customerId)
      this.$refs.addLab.getCollections(e.id)
      this.formModel.certCpscCollection=null
      }
    
    },
    // changeAddInfoPocCustomerId(val){
    //   this.$refs.addInfo.formModelDialog.pocCustomerId =val
    // },
    // changeAddLabPocCustomerId(val){
    //   this.$refs.addLab.formModelDialog.pocCustomerId =val
    // },
    // changeAddInfoPocTypeId(val){
    //   this.$refs.addInfo.formModelDialog.pocTypeId =val
    // },
    // changeAddLabPocTypeId(val){
    //   this.$refs.addLab.formModelDialog.pocTypeId =val
    // },
    handleChange (column) {
      this.type = column
    },
    checkStatus(){
      if (this.trfId){
        this.loading = false;
        getDetail(this.trfId).then(res => {
            
          this.formModel = res.data.data
          if(!this.formModel.cpscTrfLabInfoList.length)this.formModel.cpscTrfLabInfoList=[{}]
          if (res.data.data.trfInfoStastus && res.data.data.trfInfoStastus >= 2){
            // let obj = {
            //   icon: 'el-icon-warning',
            //   label: 'Certificate Entry',
            //   prop: 'tab2'
            // };
            // this.option.column.push(obj)
            this.showTab2=true
            //this.tab='tab2'
          }
          //主键等数据
          const {
            id,referenceNo,trfInfoStastus,createTime,updateTime,trfInfoVersion,trfInfoId,isPending,cpscCustomer,status
          } = res.data.data;
          this.trfInfo = {
            id,referenceNo,trfInfoStastus,createTime,updateTime,trfInfoVersion,trfInfoId,isPending,cpscCustomer,status
          };

          this.loading = true;
          this.show =true
          //复制操作
          if (this.copyType){
            this.trfInfo = {
              referenceNo: this.getNewTrfNo(),
              trfInfoStastus: 0,
              isPending: '1'
            }
            this.formModel.referenceNo = this.trfInfo.referenceNo
            this.formModel.trfInfoStastus = this.trfInfo.trfInfoStastus
            this.formModel.isPending = this.trfInfo.isPending
          }
        });
      }else {
        this.trfInfo = {
          referenceNo: this.getNewTrfNo(),
          trfInfoStastus: 0,
          isPending: '1'
        }
        this.show =true
      }
    },
    getNewTrfNo() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以+1
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      const endStr = Math.floor(Math.random() * 900) + 100;

      // 连接成纯数字字符串
      const formattedTime = `${year}${month}${day}${hours}${minutes}${seconds}${endStr}`;
      //const formattedTime = `${minutes}${seconds}`;
      return "eFiling"+formattedTime;
    },
    closeEditPage(){
      //this.$emit("closeEditPage")
    },
    checkReload() {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('refresh')) {
        window.location.reload();
      }
    }
  }
}
</script>