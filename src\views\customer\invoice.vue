<template>
    <div>
        <el-form :inline="true" :model="formInline" size="medium">
            <el-form-item>
                <el-input
                        v-model="query.invoiceTitle"
                        :placeholder="$t('invoice.invoiceTitle')"
                        clearable></el-input>
            </el-form-item>
            <el-form-item   prop="taxNo">
                <el-select v-model="query.invoiceType" :placeholder="$t('invoice.invoiceType')"  clearable >
                    <el-option v-for="(reportLanguage,index) in invoiceTypeData"
                               :label="reportLanguage.sysValue"
                               :value="reportLanguage.sysKey"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                <el-button type="primary" v-if="permissionList.addBtn" @click="addRow">{{$t('operation.add')}}</el-button>
            </el-form-item>
        </el-form>
        <el-table
                :data="tableData"
                style="width: 100%"
                size="medium">
            <el-table-column
                    fixed="left"
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    fixed="left"
                    prop="invoiceTitle"
                    :label="$t('invoice.invoiceTitle')"
                    width="300">
            </el-table-column>
            <el-table-column
                    prop="invoiceTypeName"
                    :label="$t('invoice.invoiceType')"
                    width="200">
            </el-table-column>
            <el-table-column
                    prop="taxNo"
                    :label="$t('invoice.taxNo')"
                    width="200">
            </el-table-column>
            <el-table-column
                    prop="invoiceTel"
                    :label="$t('invoice.tel')"
                    width="200">
            </el-table-column>
            <el-table-column
                    width="300"
                    prop="invoiceAddress" title="invoiceAddress"
                    :label="$t('invoice.address')">
            </el-table-column>

            <el-table-column
                    width="300"
                    prop="bankName"
                    :label="$t('invoice.bankName')">
            </el-table-column>
            <el-table-column
                    width="200"
                    prop="bankAccount"
                    :label="$t('invoice.bankAccount')">
            </el-table-column>
            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')"
                    width="120"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="updateUser"
                    :label="$t('common.operator')"
                    width="120">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    prop="isDefault"
                    :label="$t('common.isDefault')"
                    width="80">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')" placement="top">
                        <el-switch
                                v-model="scope.row.isDefault"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9"
                                :active-value = "1"
                                :inactive-value = "0"
                                @change="changeDefault(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    fixed="right"
                    prop="status"
                    :label="$t('common.status.title')"
                    width="80"
                    align="center">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                        <el-switch
                                v-model="scope.row.status"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9"
                                :active-value = "1"
                                :inactive-value = "0"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    fixed="right"
                    :label="$t('operation.title')"
                    width="150"
                    align="center">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)" size="small" icon="el-icon-edit">{{$t('operation.edit')}}</el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog :title="title" :visible.sync="dialogFormVisible">
            <el-form ref="form" :model="form"  label-width="160px" label-position="left"  size="medium" class="sgs-form">
                <el-form-item :label="$t('invoice.invoiceTitle')" :rules="{ required: true, message: $t('invoice.validate.invoiceTitleBlur'), trigger: 'blur' }" prop="invoiceTitle">
                    <el-input  maxlength="150" v-model="form.invoiceTitle"></el-input>
                </el-form-item>
                <el-form-item :label="$t('invoice.invoiceType')" :rules="{ required: true, message: $t('invoice.validate.taxNoBlur'), trigger: 'blur' }" prop="taxNo">
                  <!--  <el-select v-model="form.invoiceType" @change="invoiceTypeChange" clearable style="width: 100%;">
                        <el-option v-for="(reportLanguage,index) in invoiceTypeData"
                                   :label="reportLanguage.sysValue"
                                   :value="reportLanguage.sysKey"></el-option>
                    </el-select>-->
                    <el-radio-group v-model="form.invoiceType" @change="invoiceTypeChange" >
                        <el-radio  v-for="(obj,index) in invoiceTypeData"
                                   :label="obj.sysKey"
                                   :value="obj.sysKey" >{{obj.sysValue}}
                        </el-radio>
                    </el-radio-group>

                </el-form-item>
                <el-form-item :label="$t('invoice.taxNo')" :rules="{ required: true, message: $t('invoice.validate.taxNoBlur'), trigger: 'blur' }" prop="taxNo">
                    <el-input  maxlength="50" v-model="form.taxNo"></el-input>
                </el-form-item>
                <el-form-item :label="$t('invoice.address')" :rules="{ required: true, message: $t('invoice.validate.addressBlur'), trigger: 'blur' }" prop="invoiceAddress">
                    <el-input  maxlength="150" v-model="form.invoiceAddress"></el-input>
                </el-form-item>
                <el-form-item :label="$t('invoice.tel')" :rules="{ required: true, message: $t('invoice.validate.telBlur'), trigger: 'blur' }" prop="invoiceTel">
                    <el-input  maxlength="50" v-model="form.invoiceTel"></el-input>
                </el-form-item>
                <el-form-item :label="$t('invoice.bankName')" :rules="{ required: true, message: $t('invoice.validate.bankNameBlur'), trigger: 'blur' }" prop="bankName">
                    <el-input  maxlength="100" v-model="form.bankName"></el-input>
                </el-form-item>
                <el-form-item :label="$t('invoice.bankAccount')" :rules="{ required: true, message: $t('invoice.validate.bankAccountBlur'), trigger: 'blur' }" prop="bankAccount">
                    <el-input  maxlength="50" v-model="form.bankAccount"></el-input>
                </el-form-item>
            </el-form>
            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="submitForm('form')" :loading="submitLoading">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {getPageByUser,add,detail,remove,setDefault,getInvoiceType} from "@/api/customer/customerInvoice";
    import {mapGetters} from "vuex";
    export default {
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        data(){
            return{
                name: "customerInvoice",
                dialogFormVisible: false,
                submitLoading: false,
                title: '',
                invoiceTypeData:[],
                tableData: [],
                form: {},
                query: {},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
            }
        },
        computed: {
            ...mapGetters(["permission","userInfo","language"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:invoice:add'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:invoice:edit'],false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:invoice:delete'],false),
                };
            }
        },
        watch: {
            //监听语言变化
            language: function (newVal) {
                //触发查询
                this.queryInvoiceType();
            },
        },
        methods:{
            invoiceTypeChange(val){
                let obj = {};
                obj = this.invoiceTypeData.find((item) => {
                    return item.sysKey === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.form,'invoiceTypeName',obj.sysValue);
                    //this.trf.servicRequire.reportLanguageName = obj.sysValue;
                }
            },
            onSearch() {
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query,this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.submitLoading=true;
                        add(this.form).then(res =>{
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.submitLoading = false;
                            this.dialogFormVisible=false;
                            this.onLoad(this.page);
                        }, error => {
                            this.submitLoading = false;
                        });
                    } else {
                        return false;
                    }
                });

            },
            removeRow(row){
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                .then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            detailRow(row){
                this.title = this.$t('invoice.editInvoice');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    getInvoiceType(this.language).then(res => {
                        const data = res.data;
                        if (data.length > 0) {
                            this.invoiceTypeData = data;
                        }
                    });
                    this.form=res.data.data;
                    this.form.invoiceType=this.form.invoiceType+'';
                });

            },
            addRow(){
                this.form = {};
                this.title = this.$t('invoice.addInvoice');
                this.dialogFormVisible =true;
            },
            changeStatus(row){
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                add(modifiedForm).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            changeDefault(row){
                setDefault(row.id,row.isDefault).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            queryInvoiceType(){
                getInvoiceType(this.language).then(res => {
                    const data = res.data;
                    if (data.length > 0) {
                        this.invoiceTypeData = data;
                    }
                });
            },
        },
        created() {
            //查询发票类型
            this.queryInvoiceType();
            this.onLoad(this.page);
        }

    }
</script>

<style scoped>

</style>
