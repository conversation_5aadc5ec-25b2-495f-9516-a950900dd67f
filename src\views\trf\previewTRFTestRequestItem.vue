<template>
    <div class="previewTRFTestRequestItem" id="previewTRFTestRequestItem">
        <ul>
            <li v-for="(item,indexLi) in data"
                :key="indexLi"
                style="margin: 5px;padding-left: 30px;list-style: none;">
                {{item.testPackageName}}
                <span style="margin-left: 20px" v-if="item.remark">  --- {{(item.remark || '').trim()}}</span>
                <preview-t-r-f-test-request-item
                    v-if="item.children && item.children.length>0"
                    :data="item.children"
                ></preview-t-r-f-test-request-item>
            </li>
        </ul>
    </div>
</template>

<script>

    export default {
        name: "previewTRFTestRequestItem",
        data() {
            return {}
        },
        methods: {},
        mounted() {
        },
        created() {
        },
        watch: {},
        computed: {},
        props: {
            data:[]
        },
        updated() {
        },
        beforeDestory() {
        },
        destoryed() {
        },
        components: {}
    }
</script>

<style scoped>
    .previewTRFTestRequestItem {
    }
</style>