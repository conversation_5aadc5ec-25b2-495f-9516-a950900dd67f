<template>
    <div class="smart_views_CommonTable" id="smart_views_CommonTable">
        <el-row v-if="title">
            <el-col>
                <p class="card-title">
                    {{ title }}
                </p>
            </el-col>
        </el-row>
        <el-row class="table_bar" v-if="menuShow">
            <el-col :span="12">
                <slot name="menuLeft"></slot>
            </el-col>
            <el-col :span="12" style="text-align: right">
                <slot name="menuRight"></slot>
            </el-col>
        </el-row>
        <el-table v-bind="$attrs"
                  ref="tableRef"
                  :data="data"
                  :height="height || tableHeight"
                  style="width: 100%"
                  :size="tableOption.size || 'default'"
                  :row-class-name="rowClassFN"
                  :span-method="spanMethod"
                  :cell-style="customCellStyle"

        >
            <!-- 必选框 -->
            <el-table-column
                    v-if="option.selection"
                    :selectable="option.selectionDis"
                    type="selection"
                    width="35"
                    align="center"
                    fixed>
            </el-table-column>
            <!-- 序号 -->
            <el-table-column v-if="option.index" type="index" label="#" align="center" width="45px"
                             :fixed="option.indexFixed">
                <template #header="scope">
                    <div class="header_slot">
                        <div class="header_slot_span">
                           <span>#</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <!-- 显示列 -->
            <el-table-column v-for="(column,index) in columnOption.filter((item)=>{return item.hide!=true})"
                             :prop="column.prop"
                             :label="column.label"
                             :width="column.width"
                             :min-width="column.minWidth || 220"
                             :fixed="column.fixed"
                             :align="column.align || 'left'"
                             show-overflow-tooltip
                             :key="index">
                <template #header="scope">
                    <slot v-if="column.headerslot"
                          v-bind="scope"
                          :prop="column.prop"
                          :label="column.label"
                          :name="'header-'+column.prop">
                    </slot>
                    <div class="header_slot">
                        <el-select
                                style="width: 100%;margin-top: 1px;padding-bottom: 1px"
                                :placeholder=" column.label ? t(column.label) : column.label"
                                v-if="columnHeaderShow[column.prop] && column.filter && column.type === 'Select'"
                                v-model="filters[column.prop]"
                                :multiple="column.multiple"
                                clearable
                                @clear="changeHeader(column,false)">
                            <el-option v-for="(dic,index) in column.dicData"
                                       :value="dic.value"
                                       :label="column.i18n ? t(dic.label) : dic.label"
                                       :key="index">
                            </el-option>
                        </el-select>
                        <!-- 不能换成v-show 因为v-focus 绑定的是insert驱动 ，如果换成update驱动，会导致不能失去焦点-->
                        <el-input
                                v-if="columnHeaderShow[column.prop] && column.filter && column.type === 'Input'"
                                style="margin-top: 1px;padding-bottom: 1px"
                                size="small"
                                :placeholder=" column.label ? t(column.label) : column.label"
                                v-model="filters[column.prop]"
                                v-focus
                                :key="column.prop"
                                clearable
                                @clear="changeHeader(column,false)"
                                @blur="headerColumnBlur(column)"
                        ></el-input>
                        <el-date-picker
                            v-if="columnHeaderShow[column.prop] && column.filter && column.type === 'DateRange'"
                            v-model="filters[column.prop]"
                            type="daterange"
                            range-separator="To"
                            start-placeholder="Start date"
                            end-placeholder="End date"
                            @clear="changeHeader(column,false)"
                        />
                        <div class="header_slot_span" v-show="!columnHeaderShow[column.prop]" @click="changeHeader(column,true)">
                           <span>
                               {{ column.label ? t(column.label) : column.label }}
                               <span v-show="column.filter" style="margin-left: 5px;">
                                <i class="el-icon-caret-bottom"></i>
                               </span>
                           </span>
                        </div>
                        <div class="header_slot_sort" v-if="tableOption.showSortIcon && !column.notSort">
                            <el-icon v-if="!sortObj[column.prop] || !sortObj[column.prop].sort || [0].includes(sortObj[column.prop].sort)" @click="sortColumn(column,'Asc')">
                                <el-tooltip placement="top" content="To sort in ascending order">
                                    <DCaret/>
                                </el-tooltip>
                            </el-icon>
                            <el-icon v-if="[1].includes(sortObj[column.prop].sort)" @click="sortColumn(column,'Desc')">
                                <el-tooltip placement="top" content="To sort in descending order">
                                    <CaretTop style="color: #3351ff"/>
                                </el-tooltip>
                            </el-icon>
                            <el-icon v-if="[-1].includes(sortObj[column.prop].sort)" @click="sortColumn(column,'')">
                                <el-tooltip placement="top" content="To clear sorting">
                                    <CaretBottom style="color: #3351ff"/>
                                </el-tooltip>
                            </el-icon>
                        </div>

                    </div>
                </template>
                <template #default="scope">
                    <slot v-if="column.slot"
                          v-bind="scope"
                          :prop="column.prop"
                          :label="column.label"
                          :name="column.prop"></slot>
                    <span v-else-if="column.dicData!=null&&column.dicData.length>0">
                        <el-tag :style="{width: (column.tagWidth?column.tagWidth:'auto'), textAlign:'center'}" v-if="getDicLabel(column.dicData, scope.row[column.prop]).tag"
                            :type="getDicLabel(column.dicData, scope.row[column.prop]).type"
                        >
                            <span :class="isLineThrough(scope.row,column)?'strikethrough':''">
                                {{ t(getDicLabel(column.dicData, scope.row[column.prop]).label) }}
                            </span>
                        </el-tag>
                        <span v-else :class="isLineThrough(scope.row,column)?'strikethrough':''">
                            {{ t(getDicLabel(column.dicData, scope.row[column.prop]).label) }}
                        </span>
                    </span>
                    <span v-else :class="isLineThrough(scope.row,column)?'strikethrough':''">{{ scope.row[column.prop] }}</span>
                </template>
            </el-table-column>
            <!-- 操作列 -->
            <el-table-column v-if="option.action" fixed="right" :width="option.actionWidth||'200px'" :align="option.actionAlign || 'center'">
                <template #header>
                    <div class="header_slot">
                        <div class="header_slot_span">
                           <span>
                                {{ t('action') }}
                           </span>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <slot v-bind="scope" name="actionColumn"></slot>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                v-if="page && page.show"
                class="commontable-pagination"
                v-model:current-page="page.page"
                v-model:page-size="page.rows"
                :page-sizes="[10, 20, 50, 100]"
                size="small"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total"
                @size-change="sizeChange"
                @current-change="currentChange"
        />
    </div>

</template>

<script>
export default {
    directives: {
        focus: {
            inserted: (el) => {
                el.querySelector('input')?.focus();
            },
            mounted(el) {
                el.querySelector('input')?.focus();
            }
        }
    }
}
</script>
<script setup>
import Tag from '../Tag/Tag.vue'
import {
    computed,
    onMounted,
    onBeforeMount,
    onBeforeUnmount,
    ref,
    reactive,
    watch,
    getCurrentInstance,
} from 'vue';
import {useI18n} from 'vue-i18n';
const {t} = useI18n();

defineOptions({
    name: 'CommonTable'
});
const sortObj = reactive({});
const emit = defineEmits(['changePage','sortBy'])

const props = defineProps({
    menuShow: {
        type: null,
        default: false
    },
    title: {
        type: null,
        default: ''
    },
    data:{
        type: Array,
        default: () => []
    },
    option: {
        type: Object,
        default: {
            hideRowColor: false,//隐藏row的背景色是否透明
            showSortIcon: false,//是否显示排序图标
            selection: false,//是否显示勾选框
            selectionDis: () => {
            },//某行dis、或者不disabled,入参 function
            index: false,//是否显示序号
            action: false,//是否显示最后action操作列
            sortSelectionFiledName: '',//按照数据顺序返回勾选的数据，需要传递能唯一标识数据值的filedName
            column: [],//列 ，示例：{prop: '属性名', label: '展示的名称', hide: false, filter: true, slot: false,width:180 ,fixed:'right/left/null'},
            disableOption:{//解释说明 当 disableNeedStrikethrough=true 且 prop = disableValue 时，整行内容文本添加删除线
                disableNeedStrikethrough:false,
                disableValue:0,
                disableProp:''
            }
        }
    },
    filters: {
        type: Object
    },
    sort: {
        type: Object
    },
    page: {
        type: Object,
        default: {
            show: false,
            page: 1,
            rows: 10,
            total: 0,
            small: false,
            layout: 'total, sizes, prev, pager, next,jumper',
            pagerCount: 7,
            size: 1,
            sizes: [10, 20, 50, 100],
        }
    },
    height: {
        type: null
    },
    onLoad: {
        type: Function
    }
})

const layout = ref('total, sizes, prev, pager, next');
const tableOption = ref({});
const firstLoad = ref(true);
const defaultColumn = reactive({column: {hide: false}});
const columnHeaderShow = ref({});
const tableRef = ref();
const tableHeight = ref(0);

/**监听屏幕宽高变化*/
let resizeTimer;
const handleResize = () => {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
        tableHeight.value = calculateHeight();
    }, 200);
};

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
});
const isLineThrough = (row)=>{
    let {disableOption} = tableOption.value;
    if(!disableOption ){
        return false;
    }
    let {disableNeedStrikethrough,disableValue,disableProp} = disableOption;
    if(!disableNeedStrikethrough){
        return false;
    }
    let cellData = row[disableProp];
    return cellData == disableValue;
}
// 计算表格可用高度的方法
const calculateHeight = () => {
    const baseHeight = window.innerHeight;
    const headerHeight = 300; // 根据实际标题栏高度调整
    const paginationHeight = props.page?.show ? 50 : 0;
    let height = baseHeight - headerHeight - paginationHeight-100; // 100为预留空间
    return height <=400 ? 400 : height;
};

onBeforeMount(() => {
    //计算当前屏幕分辨率，用来展示表格高度
    props.option.column.forEach(item => {
        if (!item.hide) {
            item['hide'] = defaultColumn.column.hide;
        }
    })
    tableOption.value = props.option;
    tableOption.value.column.forEach(item => {
        columnHeaderShow.value[item.prop] = false;
        sortObj[item.prop] = {
            orderBy: item.prop,
            sort: 0
        }
    })
})

onMounted(() => {
    window.addEventListener('resize',handleResize);
    tableHeight.value = calculateHeight();
})
const columnOption = computed(() => {
    return tableOption.value.column || [];
});
const showColumn = computed(() => {
    return columnOption.value.filter(item => {
        return !item.hide
    }) || []
});

const changeHeader = (column, flag) => {
    if (!column.filter) {
        return;
    }
    let {prop} = column;
    columnHeaderShow.value[prop] = flag;
    //getCurrentInstance().proxy.$forceUpdate();
}
const headerColumnBlur = (column) => {
    const filterValue = props.filters[column.prop];
    if (filterValue == null ||
        filterValue == 'undefined' ||
        filterValue == undefined ||
        filterValue == '' ||
        filterValue.length == 0) {
        changeHeader(column, false);
    }
}
const getSelection = () => {
    if (!props.option.selection) {
        return [];
    }
    let data = tableRef.value.data;
    if (!data || data.length == 0) {
        return [];
    }
    if (props.option.sortSelectionFiledName) {
        return tableRef.value.selection;
    }
    let filedName = props.option.sortSelectionFiledName;
    let currentSelect = tableRef.value.selection;
    let sortSelection = [];
    let selectFiledValue = currentSelect.map(cs => cs[filedName]);
    if (!selectFiledValue) {
        return tableRef.value.selection;
    }
    data.forEach((da, ind) => {
        let dataFiledValue = da[filedName];
        if (selectFiledValue.includes(dataFiledValue)) {
            sortSelection.push(da)
        }
    })
    return sortSelection;
}
const clearAllSelection = () => {
    tableRef.value.clearSelection();
}

const rowClassFN = (row) => {
    if (props.option.hideRowColor) {
        return 'hide-row-color';
    }
    return 'normal-row-color';
}

const getDicLabel = (dicData, value) => {
    let dic = dicData.filter((dic) => {
        return dic.value == value
    })
    return dic?.[0] || {label:''};

}
const columnChange = (val) => {
    if (firstLoad.value) {
        return true;
    }
    tableOption.value.column.forEach(item => {
        const t = val.filter((v) => {
            return v.prop == item.prop;
        })
        item.hide = t.length <= 0;
    })
}
const currentChange = (currentPage) => {
    props.page.page = currentPage;
    emit('changePage', props.page);
}
const sizeChange = (pageSize) => {
    props.page.rows = pageSize;
    emit('changePage', props.page);
}

const sortColumn = (column, order)=>{
    let {prop} = column;
    let keys = Object.keys(sortObj);
    keys.forEach(key=>{
        sortObj[key].sort = 0;
    })
    if(order){
        sortObj[prop].sort = order == 'Asc' ? 1 : -1;
    }
    emit("sortBy",{
        prop : order? prop : '',
        orderBy:order
    })
}

const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if(!tableOption.value.combineProp || tableOption.value.combineProp.length == 0){
        return [1, 1];
    }
    if(columnIndex > 0){}
    const data = props.data;
    for(let propName of tableOption.value.combineProp){
        if(propName == column.property){
            let spanCount = 0;
            for (let i = rowIndex; i < data.length; i++) {
                if (data[i][propName] === row[propName]) {
                    spanCount++;
                } else {
                    break;
                }
            }
            if (spanCount > 0) {
                // 如果是合并起始行，则返回合并范围
                if (rowIndex === 0 || data[rowIndex - 1][propName] !== row[propName]) {
                    return  { rowspan: spanCount, colspan: 1 };
                } else {
                    // 非起始行，返回 [0, 1] 隐藏该单元格
                    return  { rowspan: 0, colspan: 1 };
                }
            } else {
                return  { rowspan: 1, colspan: 1 };
            }
        }
    }
};
const customCellStyle = ({ row, column, rowIndex, columnIndex }) => {
    if(!tableOption.value.combineProp || tableOption.value.combineProp.length==0){
        return {};
    }
    for(let propName of tableOption.value.combineProp){
        if (propName && column.property === propName) {
            const data = props.data;
            const currentVal = row[propName];
            const prevVal = data[rowIndex + 1]?.[propName];
            // 如果当前行是合并起始行，则添加 merged-cell 类
            if (currentVal == prevVal) {
                return {background:tableOption.value.combineColor};
            }
        }
    }
    return {};
};
</script>

<style lang="scss" scoped>
.smart_views_CommonTable {
  p.card-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
    padding: 10px 0 5px 0;
  }
  .table_bar{
    padding: 5px 0;
  }


  .header_slot {
    display: flex;
    justify-content: flex-start;

    .header_slot_span {
      height: 32px;
      text-align: center;
      line-height: 32px;
      padding: 0;
      margin: 0;
      font-weight: 100;
    }

    .header_slot_sort {
      line-height: 36px;
      cursor: pointer;
    }

    div.el-select__placeholder span{
        font-weight: normal !important;
    }
  }

}
</style>