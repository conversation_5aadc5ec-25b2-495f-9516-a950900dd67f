<template>
    <el-container class="container">
        <el-menu router="false" :default-openeds="[MAIN_MENU_KEY]" class="el-menu-vertical" :collapse="isCollapse">
            <el-submenu :index="MAIN_MENU_KEY" v-show="!isCollapse">
                <template #title>
                    <i class="el-icon-s-cooperation"></i>
                    <el-label><b>{{$t('settings.componentsList')}}</b></el-label>
                </template>
                <component-tree ref="layoutTree" @toggleComponents="changeComponent"></component-tree>
            </el-submenu>
        </el-menu>
        <el-container direction="vertical" class="right-container">
            <component-top @closeMenu="hiddenMenu" @lock="toggleEdit" @save="submit"
                           @loadChoiceComponent="loadPageConfig"
                           @loadPageComponent="loadPageComponent"
                           @showLoading="showLoading"
                           @hiddenLoading="hiddenLoading"/>
            <component-main class="main-background" :lock="lock" ref="layoutMain" @showLoading="showLoading" @hiddenLoading="hiddenLoading" />
        </el-container>
    </el-container>
</template>

<script>
    const MAIN_MENU_KEY = 'components_group';
    export default {
        name: "Component Edit",
        components: {
            ComponentTree: resolve => require(['@/components/settings/framework/ComponentTree'], resolve),
            ComponentTop: resolve => require(['@/components/settings/framework/ComponentTop'], resolve),
            ComponentMain: resolve => require(['@/components/settings/framework/ComponentMain'], resolve),
        },
        data() {
            return {
                loading:true,
                isCollapse: false,
                lock: false           //是否可编辑
            }
        },
        methods: {
            hiddenMenu(val) {
                this.isCollapse = val;
            },

            toggleEdit(val) {
                this.lock = val;
            },

            submit(config) {
              this.$refs.layoutMain.submit(config);
            },

            changeComponent(data, checked) {
              this.$refs.layoutMain.toggleComponent(data, checked);

            },

            //传入当前选中页面的配置数据, 覆盖默认组件默认位置
            loadPageComponent(data) {
              this.$refs.layoutMain.showPageComponents(data.configData);
            },

            //传入当前选中页面的组件数据,自动选中或取消
            loadPageConfig(data) {
              this.$refs.layoutTree.loadPageConfig(data);
            },

            showLoading() {
              this.$loading({
                lock: true,         // 锁定背景滚动
                text: 'loading...',   // 加载文本
                spinner: 'el-icon-loading',         // 自定义加载图标，可以使用Element UI的图标
                background: 'rgba(0, 0, 0, 0.7)'    // 背景颜色
              });
            },

            hiddenLoading() {
              this.$loading().close();
            }

        }
    }
</script>

<style scoped lang="scss">
    .container {
      height: auto;
      min-height: 400px;
      width: auto;
      background: #fff;
    }

    .el-menu-vertical:not(.el-menu--collapse) {
      width: 200px;
    }

    .right-container {
      padding: 0 16px;
    }

    .main-background {
      background: #f5f5f5;
        overflow-y: auto;
    }

</style>
