<template>
  <basic-container>
    <el-form ref="form" :model="form"
             label-width="200px"
             class="sgs-form"
             size="medium"
             label-position="left"
             :rules="rules">
      <el-card>
      <div class="sgs-group">
        <h3>{{ $t('training.ClassInfo') }}</h3>
      </div>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item :label="$t('training.CourseTitle')" prop="courseTitle">
            <el-select v-model="form.courseTitle" @change="selectTitleChange" :placeholder="$t('training.CourseTitle')" style="width: 100%"  :disabled="isDisabled">
              <el-option v-for="item in courseList" :key="item.id" :lable="item.name" :value="item.nameAndC">{{ item.nameAndC }}</el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('training.TrainingCourseLevel')">
            <el-select  v-model="form.courseLevel" @change="selectRoleChange" :placeholder="$t('training.TrainingCourseLevel')" style="width: 100%" disabled>
              <el-option v-for="item in pris" :key="item.id" :lable="item.id" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('training.CourseModule')" prop="courseModule">
            <el-select  v-model="form.courseModule"  @change="selectModuleChange"  :placeholder="$t('training.CourseModule')" style="width: 100%"  :disabled="isDisabled">
              <el-option v-for="item in courseModuleList" :key="item.id" :lable="item.id" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-upload
                  class="avatar-uploader"
                  action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                  drag
                  :on-success="handleAvatarSuccessOfImg"
                  multiple
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload">
            <img  v-model="form.uploadCourseImage" v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <el-input type="hidden" v-model="form.uploadCourseImage">{{ imageUrl }}</el-input>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('training.ClassIntroduction')" prop="classIntroduction" >
            <el-input clearable  type="textarea" :rows="6"  :maxlength="500" show-word-limit v-model="form.classIntroduction"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('training.EnrollmentStartDate')"  prop="enrollmentStartDate">
            <el-date-picker
                    v-model="form.enrollmentStartDate"
                    type="date"
                    style="width: 100%"
                    :placeholder="$t('training.EnrollmentStartDate')"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions1">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('training.EnrollmentExpiredDate')"  prop="enrollmentExpiredDate">
            <el-date-picker
                    v-model="form.enrollmentExpiredDate"
                    type="date"
                    style="width: 100%"
                    :placeholder="$t('training.EnrollmentExpiredDate')"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions1">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('training.TrainingDate')"  prop="trainingDate">
            <el-date-picker
                    v-model="form.trainingDate"
                    type="date"
                    style="width: 100%"
                    :placeholder="$t('training.TrainingDate')"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptions1">
            </el-date-picker>
          </el-form-item>
        </el-col>
          <el-col :span="12">
              <el-form-item :label="$t('training.Trainer')"  prop="trainerName">
                  <el-input clearable   v-model="form.trainerName"></el-input>
              </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('training.TrainingLocation')"  prop="trainingLocation">
            <el-select
                    v-model="form.trainingLocation"
                    filterable
                    remote
                    reserve-keyword
                    style="width: 100%"
                    :placeholder="$t('training.TrainingLocation')"
                    :loading="loading">
              <el-option
                      v-for="item in list"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
          <el-col :span="12">
              <el-form-item :label="$t('training.TrainingAddress')"  prop="trainingAddress">
                  <el-input clearable   v-model="form.trainingAddress"></el-input>
              </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('training.SelectTrainee')" @click="openDialog">
            <el-input  v-model="form.trainee" @click.native="openDialog"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('training.Remark')"  prop="remark">
            <el-input clearable   v-model="form.remark"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <el-card>
      <el-row class="sgs-group">
        <h3>{{$t('training.TrainingMaterials')}}</h3>
        <div class="training-right-block" >

          <el-row>
            <el-col :span="11"><el-button size="small" type="primary" @click="openModuleFileList">{{ $t('training.ModuleFile') }}</el-button></el-col>
            <el-col :span="3"></el-col>
            <el-col :span="10"><el-upload
                    class="upload-demo"
                    action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                    :on-change="uploadChange"
                    :on-success="handleAvatarSuccess"
                    :show-file-list="false"
            >
              <el-button style="margin-left: 2px;" size="small" type="primary">{{ $t('training.Browse') }}</el-button>
              <!--<div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
            </el-upload></el-col>


          </el-row>
        </div>
      </el-row>
      <el-table :data="fileList"
                width="100%"
                :element-loading-text="$t('uploadLoadingText')"
                element-loading-spinner="el-icon-loading"
                v-loading="uploadLoading">
        <el-table-column
                type="index"
                fixed
                label="#"
                width="50">
        </el-table-column>
        <el-table-column
                prop="documentName"
                :label="$t('attachment.name')">
        </el-table-column>
        <el-table-column
                prop="documentFrom"
                :label="$t('attachment.documentFrom')">
        </el-table-column>
        <el-table-column
                :label="$t('operation.title')"
                width="180">
          <template slot-scope="scope">
            <el-button type="text" @click="downloadAttachmentRow(scope.row)" size="small" icon="el-icon-download">{{$t('operation.download')}}</el-button>
            <el-button @click="removeAttachmentRow(scope.$index)" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--<el-row>
        <el-col :span="4">

        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('training.DocumentName')">

          </el-form-item>
        </el-col>
        <el-col :span="1">
          <el-form-item label="">

          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('training.UploadedBy')">

          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item :label="$t('training.Action')">

          </el-form-item>
        </el-col>
      </el-row>-->

      <!--<el-row>
        <el-card v-for="item in fileList">
          <el-col :span="2">

          </el-col>
          <el-col :span="7">
            <el-tag type="warning">{{ item.fileName }}</el-tag>
          </el-col>
          <el-col :span="7">
            <el-tag type="warning">{{ item.uploadedBy }}</el-tag>
          </el-col>
          <el-col :span="7">
            <el-tag type="warning">
            <a class='download' :href='item.fileUrl' download=""  title="下载">{{ $t('training.Download') }}</a></el-tag>
            <el-tag type="danger" @click="delFileList(item.fileUrl)">{{ $t('training.Delete') }}删除</el-tag>
          </el-col>
        </el-card>
      </el-row>-->
      <div class="sgs-bottom">
        <el-button type="primary" @click="onSubmit('0')"  v-if="completeFlag" v-loading.fullscreen.lock="fullscreenLoading">{{ $t('training.Save') }}</el-button>
        <el-button type="primary" @click="onSubmit('1')"  v-if="completeFlag" v-loading.fullscreen.lock="fullscreenLoading">{{ $t('training.PublishCourse') }}</el-button>
        <el-button type="primary" @click="toClassList('0')">{{ $t('training.Cancel') }}</el-button>
      </div>
    </el-card>
    </el-form>

    <el-drawer size="70%" :title="FileList" :visible.sync="dialogVisibleModuleFileList">
      <div class="sgs-group">
        <h3>{{$t('info.base')}}</h3>
      </div>
      <el-form ref="formModuleFileList" :model="formModuleFileList" label-width="200px"  class="sgs-form" size="medium">
        <el-table :data="formModuleFileList.attachmentList"  width="100%"  @selection-change="changeFun">
          <el-table-column
                  type="selection"
                  label="#"
                  width="50">
          </el-table-column>
          <el-table-column
                  prop="documentName"
                  :label="$t('attachment.name')">
          </el-table-column>
          <!--<el-table-column
                  :label="$t('operation.title')"
                  width="180">
            <template slot-scope="scope">
              <el-button type="text" @click="addToClassFileList(scope.row)" size="small" icon="el-icon-download">{{$t('operation.selected')}}</el-button>
            </template>
          </el-table-column>-->
        </el-table>
        <div class="sgs-bottom">
          <!--<el-button size="small" type="primary" @click="addModule" v-loading.fullscreen.lock="moduleSave">{{ $t('operation.save') }}</el-button>
          <el-button size="small" type="" @click="resetForm('formModuleAdd')">{{ $t('operation.reset') }}</el-button>-->
          <el-button type="" @click="closeModuleFileList">{{ $t('operation.cancel') }}</el-button>
          <el-button type="primary" @click="addToClassFileList">{{ $t('operation.selected') }}</el-button>
        </div>
      </el-form>
    </el-drawer>
    <el-dialog title="Select Trainee" :visible.sync="dialogFormVisible">
      <el-row>
        <el-col :span="12">
        </el-col>
      </el-row>
      <el-row>

        <div class="box">
          <!-- lazy -->
          <!-- :lazyFn="lazyFn" -->
          <tree-transfer
                  ref="wl-tree-transfer"
                  filter
                  high-light
                  default-transfer
                  :mode="mode"
                  :title="title"
                  :to_data="toData"
                  :from_data="fromData"
                  :placeholder="$t('training.KeywordSearch')"
                  :filterNode="filterNode"
                  :defaultProps="defaultProps"
                  :defaultCheckedKeys="defaultCheckedKeys"
                  :defaultExpandedKeys="[2,3]"
                  @right-check-change="rightCheckChange"
                  @left-check-change="leftCheckChange"
                  @removeBtn="remove"
                  @addBtn="add"
                  height="540px"
                  node_key="id"
          >
          </tree-transfer>
          <el-form-item style="text-align: right">
            <el-button size="small" type="primary"  style="text-align: center" @click="closeDialog">{{ $t('training.Close') }}</el-button>
          </el-form-item>

        </div>
      </el-row>

    </el-dialog>
  </basic-container>
</template>



<script>
    import {Get_CourseLevel} from "@/api/training/course";
    import {add, Get_Course, selCourseModule, uploadClassMaterials, selLocationList, selectClassById, selSuppliesList, selModuleById} from "@/api/training/class";
    import {getCloudFileURL} from "@/api/common/index";
    import {mapGetters} from "vuex";
    export default {
       components: { treeTransfer: resolve => require(['el-tree-transfer'], resolve)
       },
        data() {
            return {
                completeFlag:true,
                uploadLoading:false,
                name: "course",
                form: {
                    attachmentList:[]
                },
                formModuleFileList:{
                    attachmentList:[]
                },
                customerGroupCode:'',
                imageUrl: '',
                uploadCourseImage: '',
                uploadCourseImageOld: '',//修改时，先把原来的cloudID存起来
                hadChangeImg:'0',//修改是，是否更换了图片
                isDisabled:false,//修改状态，不允许修改题目和模块

                editForm: '',
                pris: '',//等级list
                courseLevelId2:'',
                courseLevel1: '',
                courseList:'',   //课程标题select使用
                courseId:'',   //课程标题select使用
                courseName:'',   //课程标题select使用
                courseModuleList:'',   //课程模块select使用
                courseModuleId:'',   //课程模块select使用
                fileList:[],   //文件列表展示

                value: [],//location 使用
                list: [],//location 使用
                loading: false,//location 使用
                states: [],//location 使用
                dialogFormVisible: false,
                fullscreenLoading: false,
                dialogVisibleModuleFileList:false,//module file List
                multipleSelection:[],//module file list selected
                pickerOptions1: {
                    /*disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },*/
                },
                rules:{
                    courseTitle :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    courseModule :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    enrollmentStartDate :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    enrollmentExpiredDate :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    /*classIntroduction :[{required: true, message: 'Please enter', trigger: 'blur'}],*/
                    trainingDate :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    trainingAddress :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    trainingLocation :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    trainerName :[{required: true, message: 'Please enter', trigger: 'blur'}],
                },

                mode: "transfer", // transfer addressList
                defaultProps: { label: "name", children: "children" },
                fromData: [], // 穿梭框 - 源数据 - 树形
                toData: [
                    /*{
                        id: 1,
                        pid: 0,
                        name: "供应商1",
                        children: [
                            {
                                id: 2,
                                pid: 1,
                                name: "张三",
                                code:'123',
                                // disabled: true,
                                children: []
                            }
                        ]
                    }*/
                ],

                defaultCheckedKeys: [], // 左侧默认选中数据
                toData2: [
                ],

            }
        },
        props:['classId'],
        mounted() {
            this.list = this.states.map(item => {
                return { value: `value:${item}`, label: `label:${item}` };
            });
        },
        created: function () {
            this.remoteMethod("");
            this.selectRoleList();//获取等级信息
            this.selectCourseList();//课程列表
            if (this.$route.query.classId != null) {
                this.isDisabled = true;
                this.selectClassById(this.$route.query.classId);
            };
            /*selSuppliesList(this.form.moduleId).then(res => {
                this.fromData = res.data;
                console.log(res.data);
            });*/
        },

        methods: {
            //打开module File List
            openModuleFileList(){
                selModuleById(Object.assign({id: this.courseModuleId})).then(res => {
                    this.formModuleFileList.attachmentList = res.data.trainingMaterials;
                });
              this.dialogVisibleModuleFileList = true;
            },
            closeModuleFileList(){
                this.dialogVisibleModuleFileList = false;
            },
            changeFun(val){
                this.multipleSelection = val;
            },
            addToClassFileList(row){
                console.log(this.multipleSelection);
                var flag = true;
                this.multipleSelection.forEach(itemList =>{
                    this.fileList.forEach(item =>{
                        if (itemList.documentName === item.documentName){
                            flag = false;
                        }
                    });
                    if (flag){
                        const attachment = {
                            'cloudId':itemList.cloudId,
                            'fileUrl':itemList.path,
                            'documentName':itemList.documentName,
                            'documentFrom':'module',
                            'fileName':itemList.documentName,
                        };
                        this.fileList.push(attachment)
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    }
                    /*else {
                        this.$message({
                            type: "info",
                            message: this.$t('training.ModuleFileRepeatToAdd')
                        });
                    }*/
                });
                this.closeModuleFileList()
            },
            selectClassById(classId){//更改，填充数据
              const initLoading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
                selectClassById(classId).then(result => {
                   initLoading.close();
                  debugger;
                    this.form = result.data.trainingClass;
                    if(result.data.trainingClass.isComplete==1){
                      this.completeFlag=false;
                    }else{
                       this.completeFlag=true;
                    }
                    this.form.courseTitle = result.data.course.courseTitle;
                    this.form.courseLevel = result.data.course.courseLevel;
                    this.form.courseModule = result.data.module.moduleName;
                    this.customerGroupCode = result.data.module.customerGroupId;
                    this.courseModuleId = result.data.trainingClass.moduleId;
                    this.uploadCourseImageOld = result.data.trainingClass.uploadCourseImage;//先把原来的存起来
                    this.imageUrl = result.data.uploadCourseImageUrl;
                    this.toData = result.data.supplies;
                    this.toData2 = result.data.supplies;
                    console.log(result.data);
                    result.data.materials.forEach( (productLineId) => {
                        this.fileList.push(productLineId)
                    })
                    selSuppliesList(this.customerGroupCode,this.language).then(res => {
                        this.fromData = res.data;
                        console.log(res.data);
                    });
                }, error => {
                  initLoading.close();
                  console.log(error);
                });
            },
            remoteMethod(locationName) {
                this.list = [];
                selLocationList({
                    locationName,
                }).then(res => {
                    let aa = res.data.data;
                    //console.log(aa);
                    aa.map(item=>{
                        this.list.push({
                            label:item.locationName,
                            //value:item.locationCode
                            value:item.locationName
                        })
                    })
                });
            },
            selectRoleList() {//初始化下拉框动态数据
                let obj = [];
                Get_CourseLevel({//

                }).then(res => {
                    res.data.forEach((item, index) => {
                        obj.push({
                            id: item.id,//id必须品啊
                            name: item.levelName//name必须品啊
                        });
                    });
                });
                this.pris = obj;
                return obj;
            },
            selectCourseList() {//初始化课程下拉框动态数据
                let obj = [];
                Get_Course({

                }).then(res => {
                    console.log(res);
                    res.data.data.forEach((item, index) => {
                        obj.push({
                            id: item.id,
                            name: item.courseTitle,
                            nameAndC : item.courseTitle + " (" + item.courseLevel + ")" ,
                            courseLevel : item.courseLevel
                        });
                    });
                });
                this.courseList = obj;
                return obj;
            },
            resetForm(searchForm) { //清空方法
                this.$refs[searchForm].resetFields()
            },
            onSubmit(val) { //提交
                this.$refs['form'].validate((valid) => {
                    if (!valid) {
                        return false;
                    } else {
                        this.fullscreenLoading = true;
                        if(this.$route.query.classId != null && this.hadChangeImg == '0'){//修改状态下，并且没有更换图片
                            this.form.uploadCourseImage = this.uploadCourseImageOld;
                        }else{
                            this.form.uploadCourseImage = this.uploadCourseImage;
                        }
                        this.form.courseLevel = this.courseLevelId2;
                        this.form.courseLevelId = this.courseLevel1;
                        this.form.courseTitle = this.courseName;
                        this.form.moduleId = this.courseModuleId;
                        this.form.isPublish = val;
                        this.form.isComplete = 0;
                        this.form.id = this.$route.query.classId;//根据id判断新增或者更新
                        /*console.log(this.fileList);
                        console.log(this.toData2);*/
                        debugger;
                        this.form.classTrainees = this.toData2;
                        this.form.materialParam = this.fileList;
                        this.form.classTrainees.forEach(item =>{
                            item.children.forEach(item2 =>{
                                item2.id = null
                            })
                        });
                        console.log(this.fileList);
                        add(this.form,this.fileList,this.toData2).then(() => {
                            this.isDisabled = false;
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.toClassList();
                        }, error => {
                            console.log(error);
                          this.isDisabled = false;
                          this.fullscreenLoading = false;
                          this.toClassList();
                        });
                    }
                })

            },
            handleAvatarSuccessOfImg(res, file) {
                this.hadChangeImg = '1';
                this.imageUrl = URL.createObjectURL(file.raw);
                console.log(res);

                this.uploadCourseImage = res.data[0].cloudID;
                this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('uploadSuccess'),
                    type: 'warning'
                });
            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isJPG) {
                    this.$message.error(this.$t('uploadType'));
                }
                if (!isLt2M) {
                    this.$message.error(this.$t('uploadSize'));
                }
                return isJPG && isLt2M;
            },
            selectRoleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.pris.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.courseLevel1 = obj.id;//提交数据使用，此处可忽略
                this.courseLevelId2 = obj.name;//用于回显名称
            },
            selectModuleChange(val) {//
                console.log(val);
                console.log(this.courseModuleList);
                this.form.courseModule == '';
                var obj = {};
                obj = this.courseModuleList.find(function (item) {
                    return item.name === val;
                });
                console.log(obj);
                this.courseModuleId = obj.id;
                this.form.courseModule = obj.name;
                this.customerGroupCode = obj.customerGroupCode
            },
            selectTitleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.courseList.find(function (item) {//obj是选中的对象
                    return item.nameAndC === val;
                });
                this.courseId = obj.id;
                this.courseName = obj.name;
                this.form.courseTitle = obj.nameAndC;
                this.form.courseLevel = obj.courseLevel;
                var objOfModule = [];
                selCourseModule({
                    courseId:this.courseId
                }).then(res => {
                    //console.log(res);
                    res.data.data.forEach((item, index) => {
                        objOfModule.push({
                            id: item.id,
                            name: item.moduleName,
                            courseLevel : item.courseLevel,
                            customerGroupCode : item.customerGroupId
                        });
                    });
                });
                this.courseModuleList = objOfModule;
                //this.$refs[form].courseModule.resetFields();
                return objOfModule;
                //this.courseLevelId2 = obj.name;//用于回显名称*/
            },
            handleAvatarSuccess(res, file) {//上传成功以后，去后台算下载的地址和匹配文件信息，返回前端，循环展示
                console.log(res);
                uploadClassMaterials({
                    cloudId:res.data[0].cloudID,
                    documentName:res.data[0].attachmentName
                }).then(res => {
                    console.log(res.data);
                    this.fileList.push(res.data);
                });
                this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('uploadSuccess'),
                    type: 'warning'
                });
                /*const attachment = {
                    'cloudId':res.data[0].cloudID,
                    'fileUrl':res.data[0].path,
                    'documentName':file.name
                };
                this.form.attachmentList.push(attachment);*/
            },
           uploadChange(file, fileList) {
                if(file.status == 'ready'){
                    //开启loading效果
                    this.uploadLoading=true;
                }else{
                      this.uploadLoading=false;
                }
            },
            removeAttachmentRow(index){
                this.fileList.splice(index,1);
            },
            downloadAttachmentRow(row){
                getCloudFileURL(row.cloudId).then(res => {
                    window.open(res.data,"_blank");
                });
            },
            delFileList(url) { // 移除上传的文件列表
                for (let i = this.fileList.length - 1; i >= 0; i--) {
                    if (this.fileList[i].fileUrl == url) {
                        this.fileList.splice(i, 1);
                    }
                }
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            toClassList(){
                this.$router.push({path: '/training/classManager'})
            },
            openDialog(){

                this.dialogFormVisible = true;
                selSuppliesList(this.customerGroupCode,this.language).then(res => {
                    this.fromData = res.data;
                    console.log(res.data);
                });
            },
            closeDialog(){
                this.dialogFormVisible = false;
            },
            noFinash(){
                //alert("Coding....")
            },



            // 清除选中
            clearChecked(){
                this.$refs['wl-tree-transfer'].clearChecked()
            },
            // 自定义筛选函数
            filterNode(value, data, where) {
                console.log(value, data, where);
                if (!value) return true;
                return data[this.defaultProps.label].indexOf(value) !== -1;
            },
            // 懒加载回调
            lazyFn(node, resolve) {
                setTimeout(() => {
                    resolve([
                        {
                            id: 71272,
                            pid: 7127,
                            name: "debug22",
                            // disabled: true,
                            children: []
                        },
                        {
                            id: 71273,
                            pid: 7127,
                            name: "debug11",
                            children: []
                        }
                    ]);
                }, 500);
            },
            // 切换模式 现有树形穿梭框模式transfer 和通讯录模式addressList
            changeMode() {
                if (this.mode == "transfer") {
                    this.mode = "addressList";
                } else {
                    this.mode = "transfer";
                }
            },
            // 添加按钮
            add(fromData, toData, obj) {
                // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
                // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
                console.log("fromData:", fromData);
                console.log("toData:", toData);
                console.log("obj:", obj);
                this.toData2 = toData;
                let count = '';
                count = Number(count);
                toData.forEach((item, index) => {
                    /*item.children.forEach((item2, index) => {
                        count = item2
                    })*/
                    count += Number(item.children.length)
                });
                this.form.trainee = count + " " + "people";
            },
            // 移除按钮
            remove(fromData, toData, obj) {
                // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
                // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
                /*console.log("fromData:", fromData);
                console.log("toData:", toData);
                console.log("obj:", obj);*/
                let count = '';
                count = Number(count);
                toData.forEach((item, index) => {
                    /*item.children.forEach((item2, index) => {
                        count = item2
                    })*/
                    count += Number(item.children.length)
                });
                this.form.trainee = count + " " + "people";
                this.toData2 = toData;
            },
            // 左侧源数据选中事件
            leftCheckChange(nodeObj, treeObj, checkAll) {
                /*console.log(nodeObj);
                console.log(treeObj);
                console.log(checkAll);*/
            },
            // 右侧目标数据选中事件
            rightCheckChange(nodeObj, treeObj, checkAll) {
                /*console.log(nodeObj);
                console.log(treeObj);
                console.log(checkAll);*/
            },
            // 自定义节点 仅树形结构支持
            renderContent(h, { node, data, store }) {
                return (
                    <span class="custom-tree-node">
                    <span>{node.label}</span>
                <span>
                <el-button
                size="mini"
                type="text"
                on-click={() => this.append(data)}
            >
                Append
                </el-button>
                <el-button
                size="mini"
                type="text"
                on-click={() => this.remove(node, data)}
            >
                Delete
                </el-button>
                </span>
                </span>
            );
            },
            // 标题自定义区点击事件
            handleTitleRight() {
                //alert("标题自定义区点击事件");
            }



        },
        computed: {
          ...mapGetters(["userInfo", "language"]),
            title() {
                if (this.mode == "transfer") {
                    return [this.$t('training.Supplier'), this.$t('training.hadChoose')];
                } else {
                    return ["通讯录", "收件人", "抄送人", "密送人"];
                }
            }
        }

    }


</script>

<style scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    height: 178px;
    display: inline;
  }
  .el-col{
    text-align: center;
  }
  .training-right-block{
    position: absolute;
    top: -10px;
    margin: 3px 0px;
    right: 0px;
  }
</style>
