// 外部接口地址
let host = "";
// 网站地址
let webSite = "";
switch (process.env.NODE_ENV) {
  case "prod":
    host = "https://cnsgsmart.sgs.net/api/";
    webSite = "https://sgsmart-online.com";
    break;
  case "uat":
    host = "https://cnsgsmart-uat.sgs.net/api/";
    webSite = "https://uat.sgsmart-online.com";
    break;
  default:
    host = "https://cnsgsmart-test.sgs.net/api/";
    webSite = "https://test.sgsmart-online.com";
    break;
}
// 启动端口
let port = 5000;
// 白名单域名
let domainWhiteList = [
  "https://uat.sgsmart-online.com/",
  "http://localhost:9537/",
];

const config = {
  port,
  host,
  webSite,
  domainWhiteList,
};

module.exports = config;
