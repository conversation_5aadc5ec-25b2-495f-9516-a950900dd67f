const Koa = require("koa");
// 获取post参数中间件
const bodyParser = require("koa-bodyparser");
// 解决跨域
const cors = require("koa-cors");
// 设置http头，安全相关
const helmet = require("koa-helmet");
// 设置跨站脚本攻击，安全相关
const CSRF = require("koa-csrf");
const koaBody = require("koa-body");
const moment = require("moment");
const { isAllowedOrigin } = require("./utils/util");
const { port, domainWhiteList } = require("./config");
const commonRouter = require("./router/common");
const protocolRouter = require("./router/protocol");
const productRouter = require("./router/product");
const searchRouter = require("./router/search");
const customerRouter = require("./router/customer");
const sampleRouter = require("./router/sample");

const registrerRouter = require("./router/registrer");
const customerRelationRouter = require("./router/customerRelation");
const logs = require("./log");
const app = new Koa();

const NODE_ENV = process.env.NODE_ENV;

app.use(async (ctx, next) => {
  const startTime = moment().unix();
  if (ctx.method === "OPTIONS") {
    await next();
    return;
  }
  const {loading,sgstoken} = ctx.request.headers;
  //add by vincent , 注册页面，不进行token校验
  if(loading==='false'){
    await next();
    return;
  }
  const endTime = moment().unix();
  const requestUrl = ctx.request.url
  if ((!sgstoken || sgstoken === 'undefined' || sgstoken === 'null') && requestUrl !== '/api/heart/get') {
    ctx.status = 401;
    logs.createLogs({
      reqAddress: ctx.request.header.origin + ctx.request.url,
      reqParams: ctx.request.header,
      info: "令牌异常，请登录",
      type: "fail",
      time: {
        startTime,
        endTime,
      },
    });
    ctx.body = {
      msg: "令牌异常，请登录",
      code: 401,
      status: 401,
      success: true,
      data: null,
    };
    return;
  }
  // 如果存在 sgstoken，继续处理后续中间件
  await next();
});

app.use(
  koaBody.koaBody({
    multipart: true,
  })
);
app.use(bodyParser());
app.use(
  cors({
    origin: NODE_ENV === "local" ? "*" : isAllowedOrigin,
    credentials: true,
    allowMethods: ["GET", "POST"],
    maxAge: 5, // 预检请求的有效期
    allowHeaders: ["Content-Disposition","Content-Type",  "Authorization"],
    exposeHeaders: ["WWW-Authenticate", "Content-Disposition", "Content-Length"],
  })
);
app.use(helmet());
// 挂载路由 routers
app.use(commonRouter.routes());
app.use(protocolRouter.routes());
app.use(productRouter.routes());
app.use(searchRouter.routes());
app.use(customerRouter.routes());
app.use(sampleRouter.routes());
app.use(registrerRouter.routes());
app.use(customerRelationRouter.routes());

// add body parsing
app.use(
  new CSRF({
    invalidTokenMessage: "Invalid CSRF token",
    invalidTokenStatusCode: 403,
    // fix sentry TIC-NODES-4
    excludedMethods: ["GET", "POST", "OPTIONS"],
    disableQuery: false,
  })
);
// 初始化sentry
const Sentry = require("@sentry/node");
Sentry.init({
  dsn: "https://<EMAIL>/42",
  tracesSampleRate: 1.0,
});
// sentry 监控错误
app.on("error", (err, ctx) => {
  // if (process.env.NODE_ENV === 'prod' || process.env.NODE_ENV === 'azureprod') {
  Sentry.withScope(function (scope) {
    scope.addEventProcessor(function (event) {
      return Sentry.addRequestDataToEvent(event, ctx.request);
    });
    Sentry.captureException(err);
  });
  // }
});
app.listen(port, "0.0.0.0", () => {
  console.log(`启动 http://localhost:${port}`);
});
