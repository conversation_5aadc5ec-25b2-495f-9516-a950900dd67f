// 判断是否允许跨域的公共方法
module.exports = {
  isAllowedOrigin(ctx) {
    const allowedDomains = ["sgsmart-online.com", "sgs.net"];
    const requestOrigin = ctx.header.origin;
    if (requestOrigin) {
      const url = new URL(requestOrigin);
      const hostname = url.hostname;
      // 遍历允许的域名列表，检查请求来源的域名是否是允许域名或其子域名
      for (const allowedDomain of allowedDomains) {
        if (
          hostname === allowedDomain ||
          hostname.endsWith(`.${allowedDomain}`)
        ) {
          return requestOrigin;
        }
      }
    }
    return "";
  },
};
