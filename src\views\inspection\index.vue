<template>
  <div>
  </div>
</template>

<script>
import {inspectionUrl} from '@/config/env';
import {mapGetters} from "vuex";
import {getToken} from "@/util/auth"
export default {
  name: "inspection",
  components: {
  },
  created() {
    window.open(inspectionUrl+"#/sso?redirect="+this.$route.query.redirect+"&token="+getToken());
    window.history.go(-1);
  },
  data() {
    return {
      listVisible:true,
      inputResultUrl:'',
    }
  },
  computed: {
    ...mapGetters(["userInfo","permission"]),
    permissionList() {
      return {
      };
    }
  },
  watch: {},
  methods: {

  },
};
</script>
