<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_product_detail" id="sgs_smart_product_detail">
            <div class="header_detail">
              <div class="detail-header">
                <h4 class="detail-header-title">Product Detail</h4>
                <div>
                  <div v-if="productObj.sampleNo" class="trf-number">
                    {{ productObj.sampleNo }}
                  </div>
                  <span v-else class="trf-no-data">No Data</span>
                  <material-status
                      :status="productObj.sampleStatus"
                    ></material-status>
                </div>
                <div class="info-list">
                  <div class="left">
                    <div class="item">
                      <p class="tit">Buyer：</p>
                      <el-tooltip
                        effect="dark"
                        :content="templateObj.customerGroupName || ''"
                        placement="top"
                        v-if="(templateObj.customerGroupName || '').length > 20"
                      >
                        <span class="text-value-color">{{
                          templateObj.customerGroupName.slice(0, 20) + "..."
                        }}</span>
                      </el-tooltip>
                      <span v-else>{{ templateObj.customerGroupName }}</span>
                    </div>
                    <div class="item" v-if="assigneeObj.name">
                      <p class="tit">Assignee：</p>
                      <el-tooltip
                        effect="dark"
                        :content="
                          assigneeObj.email
                            ? assigneeObj.name + '(' + assigneeObj.email + ')'
                            : assigneeObj.name
                        "
                        placement="top"
                        v-if="
                          (assigneeObj.email
                            ? assigneeObj.name + '(' + assigneeObj.email + ')'
                            : assigneeObj.name
                          ).length > 20
                        "
                      >
                        <span class="text-value-color">{{
                          (assigneeObj.email
                            ? assigneeObj.name + "(" + assigneeObj.email + ")"
                            : assigneeObj.name
                          ).slice(0, 20) + "..."
                        }}</span>
                      </el-tooltip>
                      <span v-else>{{
                        assigneeObj.email
                          ? assigneeObj.name + "(" + assigneeObj.email + ")"
                          : assigneeObj.name
                      }}</span>
                    </div>
                    <div class="item">
                      <p class="tit">Template：</p>
                      <el-tooltip
                        effect="dark"
                        :content="
                          productObj.templateName || templateObj.templateName || ''
                        "
                        placement="top"
                        v-if="
                          (productObj.templateName || templateObj.templateName || '')
                            .length > 20
                        "
                      >
                        <span class="text-value-color">{{
                          (productObj.templateName || templateObj.templateName) +
                          "..."
                        }}</span>
                      </el-tooltip>
                      <span class="text-value-color" v-else>{{
                        productObj.templateName || templateObj.templateName || ""
                      }}</span>
                    </div>
                  </div>


                  <div class="dates">
                    <div class="date">
                      <span class="tit">Create Time：</span
                      ><span class="date-value text-value-color">{{
                        currentTz_YMD(productObj.createTime || nowDate)
                      }}</span>
                    </div>
                    <div class="date" v-if="productObj.updateTime">
                      <span class="tit">Update Time：</span>
                      <span class="date-value text-value-color">{{
                        currentTz_YMD(productObj.updateTime)
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- tabs button-->
              <div class="sgs_smart_detail_button_tabs">
                  <el-button type="text" :class="{'active':detailName=='productInfo'}" @click="detailName = 'productInfo'">Product Info</el-button>
                  <el-button
                    type="text"
                    :class="{ active: detailName == 'documentsTests' }"
                    @click="detailName = 'documentsTests'"
                    >Documents & Tests</el-button
                  >
                  <el-button type="text" v-if="productObj.sampleNo" :class="{'active':detailName=='auditTrail'}" @click="detailName = 'auditTrail'">Audit Trail</el-button>
              </div>
            </div>
            <product-info
                    v-if="detailName=='productInfo'"
                    @getInfo="getInfo"
                    @getTemplateInfo="getTemplateInfo"
                    @assigneeObj="getAssigneeInfo"
            ></product-info>
            <audit-trail
                    v-if="detailName=='auditTrail'"
                    key="auditTrail"
                    :objects="[
                        {objectType:'SmartPBM-Product',name:'History',rootObjectId:productObj.id,objectDate:productObj.createTime},
                        {objectType:'SmartPBM-Product-TRF',name:'Product-TRF',rootObjectId:productObj.id,objectDate:productObj.createTime},
                    ]"
                    :object-no="productObj.sampleNo"
            ></audit-trail>
            <documents-tests
              key="documentsTests"
              v-if="detailName == 'documentsTests'"
            ></documents-tests>
        </div>
  </basic-container>
</template>

<script>
import ProductInfo from "./productInfo";
import AuditTrail from "../materialAndProductCommon/auditTrail";
import MaterialStatus from "../commonComponent/materialStatus";
import DocumentsTests from "./documentsTests.vue";
import moment from "moment";
import {tzFormChina} from '@/util/datetimeUtils'

export default {
  name: "detail",
  data() {
    return {
      pageLoading: false,
      detailName: "productInfo",
      productObj: {},
      templateObj: {},
      assigneeObj: {},
      nowDate: "",
      interval: null,
    };
  },
  methods: {
    initPage() {},
    getInfo(data) {
      this.productObj = data;
    },
    getAssigneeInfo(data) {
      this.assigneeObj = data;
    },
    getTemplateInfo(data) {
      this.getNow();
      this.templateObj = data;
    },
    getNow() {
      this.nowDate = moment(new Date()).format("YYYY-MM-DD HH:mm");
      this.interval = setInterval(() => {
        this.nowDate = moment(new Date()).format("YYYY-MM-DD HH:mm");
      }, 1000);
    },
    currentTz_YMD(val) {
      if (!val) return ''
      let value = tzFormChina(val, 'YYYY-MM-DD HH:mm:ss');
      return moment(value).format('YYYY-MM-DD');
    },
  },
  mounted() {},
  created() {
    this.initPage();
  },
  beforeDestroy() {
    this.interval && clearInterval(this.interval);
  },
  watch: {},
  computed: {},
  props: {},
  components: { ProductInfo, AuditTrail, MaterialStatus, DocumentsTests },
};
</script>

<style lang="scss">
@import "@/styles/unit.scss";

.sgs_smart_product_detail {
  .header_detail {
    padding: $module-padding;
    background: #fff;
    position: relative;
    .trf-number {
      font-size: 24px;
      font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
        "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
      color: #000000;
      padding: $inline-element-spacing 0;
      display: inline-block;
      word-break: break-all;
    }
    .trf-no-data {
      color: #999;
      font-size: 14px;
      padding: $inline-element-spacing 0;
      display: inline-block;
    }
    .tit {
      color: $text-color-value;
      margin: 0;
    }
    .detail-header{
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 14px;
    }
    .detail-header-title{
      font-size: 16px;
      color: $text-color-value;
      line-height: 20px;
    }
    .info-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #000000;

      .left{
        display: flex;
      }
      .item{
        display: flex;
        align-items: center;
        margin-right: 30px;
      } 
      .dates {
        display: flex;
      }
      .date {
        font-size: 14px;
        font-weight: 400;
        margin-left: 30px;
        display: flex;
      }
    }
  }  
  .sgs_smart_detail_button_tabs {
    border: 1px solid #e6e6e6;
    margin-top: 11px;
    padding: 4px;
    width: fit-content;
    button {
      padding: 0 16px;
      height: 32px;
      line-height: 32px;
      &:not(.is-disabled) {
        color: #1b1b1b;
        cursor: pointer;
        &.active {
            color: $primary-color;
        }
      }
    }
  }

  div.divider_no_margin {
    margin: 0 !important;
  }

  .el-card {
    margin-bottom: 20px;
    h4 {
      font-weight: bold;
    }
  }
  .section_title {
    padding: 10px 0;
    font-weight: bold;
  }
}
@media screen and (max-width: 1600px) {
  .header_detail {
    .info > div {
      .left {
        .dates {
          //   margin-top: 25px;
        }
        .date {
          .tit {
            display: block;
          }
        }
      }
      .right {
        // margin-top: 22px;
        .item {
          p {
            display: block;
            color: #656565;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .header_detail {
    padding-top: 7px;
    padding-bottom: 10px;
    .info {
      padding-bottom: 11px;
    }
  }
}
</style>
