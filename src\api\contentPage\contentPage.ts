import request from '../request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */

// 获取标签列表
export const contentPageTagPage = (params: Object) => {
  return request({
    url: `/sgs-e-filling/sgs-knowledge/knowledgeTag/page?hash=${new Date().getTime()}`,
    method: 'get',
    params
  })
}

// 获取法规分类树
/* export const contentPageTree = (params: Object) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledgeCategory/tree',
      method: 'get',
      params
    })
} */

// 获取法规详情
/* export const contentPageDetail = (params: Object) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledgeCategory/detail',
      method: 'get',
      params
    })
}
 */
// 获取法规列表
export const contentPageList = (params: Object) => {
    return request({
      url: `/sgs-e-filling/sgs-knowledge/knowledgeCategory/list?hash=${new Date().getTime()}`,
      method: 'get',
      params
    })
}


// 获取模型列表
export const contentPageGetlist = (params: { categoryId: string }) => {
  return request({
    url: `/sgs-e-filling/sgs-knowledge/knowledgeModel/getSearchlist?hash=${new Date().getTime()}`,
    method: 'get',
    params
  })
}
export const contentPagePage = (searchObj:any, paginationParams?:any) => {
  const data = {
    ...searchObj,
    query: paginationParams || { current: 1, size: 10 }
  };
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledge/page',
    method: 'post',
    data: data,
    loading: false
  })
}
/* export const contentPageSave = (params:any) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeFavorites/save',
    method: 'post',
    params,
   
  })
}
export const contentPageRemove = (params:any) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeFavorites/remove',
    method: 'post',
    params,
   
  })
} */
  export const contentPagecount = (params:any) => {
    return request({
      url: `/sgs-knowiedge/knowledge/attachmentviewcount?hash=${new Date().getTime()}`,
      method: 'get',
      params,
     
    })
}
export const contentPagemain = (params:any) => {
  return request({
    url: `/sgs-e-filling/sgs-knowledge/searchsuggestion/main?hash=${new Date().getTime()}`,
    method: 'get',
    params,
   
  })
}
export const contentPageSave = (params:any) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeFavorites/save',
    method: 'post',
    data: params,
  })
}
export const contentPageRemove = (params:any) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeFavorites/remove',
    method: 'post',
    params,
   
  })
}
