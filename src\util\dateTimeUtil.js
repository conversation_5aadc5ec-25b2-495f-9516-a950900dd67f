"use strict";

import i18n from "@/lang";
import moment from 'moment';
import Vue from "vue";

const dateUnitEnums = {
  DAY: 'day',
  MONTH: 'month',
  YEAR: 'year',
  DAYS: 'days',
  MONTHS: 'months',
  YEARS: 'years'
}

const dateFormatEnums = {
  DEFAULT_DATE_FORMAT: 'YYYY-MM-DD'
}

const dateTimeTool = {
  pickerOptions: {
    shortcuts: [{
      text: i18n.t('datePicker.lastWeek'),
      code: 'datePicker.lastWeek',
      onClick(picker) {
        picker.$emit('pick', [moment().subtract(7, dateUnitEnums.DAYS).toDate(), moment().toDate()]);
      }
    }, {
      text: i18n.t('datePicker.lastMonth'),
      code: 'datePicker.lastMonth',
      onClick(picker) {
        picker.$emit('pick', [moment().subtract(1, dateUnitEnums.MONTHS).toDate(), moment().toDate()]);
      }
    }, {
      text: i18n.t('datePicker.lastHalfYear'),
      code: 'datePicker.lastHalfYear',
      onClick(picker) {
        picker.$emit('pick', [moment().subtract(6, dateUnitEnums.MONTHS).toDate(), moment().toDate()]);
      }
    }, {
      text: i18n.t('datePicker.lastYear'),
      code: 'datePicker.lastYear',
      onClick(picker) {
        picker.$emit('pick', [moment().subtract(1, dateUnitEnums.YEARS).toDate(), moment().toDate()]);
      }
    }]
  },
  startDate: moment().startOf(dateUnitEnums.MONTH).format(dateFormatEnums.DEFAULT_DATE_FORMAT),
  endDate: moment().add(1, dateUnitEnums.MONTH).startOf(dateUnitEnums.MONTH).format(dateFormatEnums.DEFAULT_DATE_FORMAT),
  dateSpanValid: function (params) {
    if (moment(params.endDate).diff(moment(params.startDate).add(1, dateUnitEnums.YEARS), dateUnitEnums.DAYS) > 0) {
      Vue.prototype.$message({
        type: "error",
        message: i18n.t('datePicker.error.timeSpanMsg'),
      });
      return false;
    }
    return true;
  }
};

export {
  dateUnitEnums, dateFormatEnums, moment, dateTimeTool
}
