<template> <!-- 特殊申请单 -->
    <div>
        <el-header
                style="text-align: left; height:50px;font-size: 12px;position: fixed;left: 0px;top:80px;width:100%;z-index:1000;background-color:#424242;">
            <el-steps simple>
                <el-step :title="$t('trf.customerInfo')" icon="el-icon-user"
                         v-scroll-to="{el: '#customer_card', duration: 1000, offset: -120}"></el-step>
                <el-step :title="$t('trf.basicInfo')" icon="el-icon-info"
                         v-scroll-to="{el: '#basic_card', duration: 1000, offset: -120}"></el-step>
                <el-step :title="$t('service.servicRequire')" icon="el-icon-edit-outline"
                         v-scroll-to="{el: '#service_card', duration: 1000, offset: -120}"></el-step>
                <el-step :title="$t('trf.attachment')" icon="el-icon-link"
                         v-scroll-to="{el: '#attachment_card', duration: 1000, offset: -120}"></el-step>
            </el-steps>
        </el-header>

        <el-table :data="trfShowData" v-if="trf.trfStatus>=1" style="width: 100%">
            <el-table-column
                    prop="trfNo"
                    :label="$t('trfList.trfNo')" width="280">
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    :label="$t('trfList.createDate')" width="280">
            </el-table-column>
            <el-table-column
                    prop="trfSubmissionDate" :formatter="trfTableDateFormtter"
                    :label="$t('trfList.submitDate')" width="280">
            </el-table-column>
            <el-table-column
                    prop="trfStatus"
                    :label="$t('trfList.jobStatus')" :formatter="trfTableStatusFormtter">
            </el-table-column>
        </el-table>

        <el-form label-position="left" :model="trf" :rules="trfRules" ref="trf" @submit.native.prevent label-width="260px" size="medium"
                 class="demo-form-inline sgs-form">
            <el-card class="sgs-box" id="customer_card">
                <div class="sgs-group">
                    <h3>{{$t('trf.customerInfo')}}</h3>
                </div>

                <el-row>
                    <h4 class="sgs-title">{{$t('trf.applicantInfo')}}</h4>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <!--非动态校验-->
                        <el-form-item :label="$t('customer.name')" prop="trfCustomer.customerNameEn" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomer.customerNameEn" :disabled="showCustomerNameFlag"></el-input>
                        </el-form-item>
                    </el-col>
                    <!--非动态校验-->
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.address')" prop="trfCustomer.customerAddressEn"  :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                            <el-select v-model="trf.trfCustomer.customerAddressEn" clearable
                                       allow-create
                                       @change="customerAddressChange"
                                       filterable
                                       :placeholder="$t('operation.pleaseSelect')"
                                       style="width: 100%;">
                                <el-option v-for="(address,index) in customerAddressData"
                                           :label="address.addressDetail" :value="address.addressDetail"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" class="contact">
                    <!--非动态校验-->
                    <el-col :span="12">
                        <el-form-item :label="$t('contact.title.default')" prop="trfCustomerContact.applyContactName" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                            <el-select v-model="trf.trfCustomerContact.applyContactName"
                                       allow-create
                                       @change="contactrNameChange" clearable
                                       filterable
                                       :placeholder="$t('operation.pleaseSelect')"
                                       style="width: 100%;">
                                <el-option v-for="(contact,index) in customerContactData"
                                           :label="contact.contactName"
                                           :value="contact.contactName"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('contact.phone')" prop="trfCustomerContact.applyContactTel" :rules="{required: false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.applyContactTel" clearable
                                      autocomplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('contact.email')" prop="trfCustomerContact.applyContactEmail" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.applyContactEmail"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('contact.fax')" v-if="valiUsable('applyContactFax')" prop="trfCustomerContact.applyContactFax" :rules="{required:valiRequired('applyContactFax'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.applyContactFax"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <h4 class="sgs-title">{{$t('trf.payInfo')}}</h4>
                    <div style="position: relative;text-align: left; margin-left: 200px; margin-top: -50px;">
                        <el-checkbox-group v-model="identicalFlag" style="padding: 10px;">
                            <el-checkbox :label="$t('trf.sameApplicant')" name="type"></el-checkbox>
                        </el-checkbox-group>
                    </div>
                </el-row>
                <el-row :gutter="20" v-if="!identicalFlag" style="margin-top:10px">
                    <!--非动态校验-->
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.companyNameEn')" prop="trfCustomer.payCustomerNameEn" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomer.payCustomerNameEn" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('customer.addressEn')" prop="trfCustomer.payCustomerAddressEn" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomer.payCustomerAddressEn" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('customer.companyNameCn')" >
                            <el-input maxlength="200" v-model="trf.trfCustomer.payCustomerNameZh" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('customer.addressZh')">
                            <el-input maxlength="200" v-model="trf.trfCustomer.payCustomerAddressZh" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <!--非动态校验-->
                        <el-form-item :label="$t('contact.title.default')" prop="trfCustomerContact.payContactName" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.payContactName" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('contact.phone')" prop="trfCustomerContact.payContactTel" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.payContactTel" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('contact.email')"  v-if="valiUsable('payContactEmail')" prop="trfCustomerContact.payContactEmail" :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.payContactEmail" clearable
                                      autocomplete="off"></el-input>
                        </el-form-item>
                        <el-form-item :label="$t('contact.fax')" v-if="valiUsable('payContactFax')" prop="trfCustomerContact.payContactFax" :rules="{required:valiRequired('payContactFax'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input maxlength="200" v-model="trf.trfCustomerContact.payContactFax" autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-card>

            <el-card class="sgs-box" id="basic_card">
                <div class="sgs-group">
                    <h3>{{$t('trf.basicInfo')}}</h3>
                </div>
                <el-row :gutter="20">

                      <el-col :span="12">
                        <el-form-item :label="$t('service.reportLanguage')" v-if="valiUsable('reportLanguage')" prop="servicRequire.reportLanguage" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                            <el-select v-model="trf.servicRequire.reportLanguage" @change="reportChange"
                                       style="width: 100%;">
                               <!-- <el-option v-for="(reportLanguage,index) in reportLanguageData"
                                           :label="reportLanguage.sysValue"
                                           :value="reportLanguage.sysKey"></el-option>-->
                                <el-option label="EN PDF"
                                           value="1"></el-option>
                                <el-option label="CN PDF"
                                           value="2"></el-option>
                                <el-option label="EN PDF & CN PDF"
                                           value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('cclTrf.isDraftReport')" prop="isSelfRefrence" style="height: 39px">
                            <el-checkbox-group v-model="isDraftReportFlag" style="padding: 4px;">
                                <el-checkbox  name="type"></el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item :label="$t('cclTrf.sdsStandard')" prop="servicRequire.sdsStandard" >
                            <el-select v-model="sdsStandardValue" multiple  @change="sdsStandardChange"
                                       clearable
                                       :placeholder="$t('operation.pleaseSelect')"
                                       style="width: 100%;">
                                <el-option label="EU GHS-Format"
                                           value="1"></el-option>
                                <el-option label="US GHS-Format"
                                           value="2"></el-option>
                                <el-option label="GB GHS-Format"
                                           value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <!--非动态校验-->
                        <el-form-item :label="$t('service.serviceType')" prop="serviceType">
                            <el-select v-model="trf.serviceType" @change="serviceTypeChange"
                                       style="width: 100%;" :placeholder="$t('service.serviceType')"
                                       clearable>
                               <!-- <el-option v-for="(serviceType,index) in serviceTypeData"
                                           :label="serviceType.serviceTypeName"
                                           :value="serviceType.serviceTypeCode"></el-option>-->
                                  <el-option label="Regular标准服务"
                                           value="Regular标准服务"></el-option>
                                <el-option label="Express加急服务"
                                           value="Express加急服务"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <!--非动态校验-->
                        <el-form-item :label="$t('lab.labName')"  prop="trfLab.labCode">
                            <el-select v-model="trf.trfLab.labCode" :placeholder="$t('lab.selLabName')"
                                       @change="selectLabChange" filterable :filter-method="dataFilter"
                                       style="width: 100%;" clearable>
                                <el-option v-for="(lab,index) in templateLabData" :label="lab.labName"
                                           :value="lab.labCode"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="labContactFlag">
                        <el-form-item :label="$t('labContact.labContactName')" v-if="valiUsable('labContactId')"  prop="trfLabContact.labContactId"  :rules="{required:valiRequired('labContactId'),message:$t('trf.validate.requiredBlur'),trigger:'change'}" >
                            <el-select v-model="trf.trfLabContact.labContactId"
                                       filterable @change="selectLabContactChange"
                                       :placeholder="$t('operation.pleaseSelect')"
                                       style="width: 100%;" clearable>
                                <el-option v-for="(labContact,index) in templateLabContactData"
                                           :label="labContact.labContactName"
                                           :value="labContact.labContactId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('lab.labAddress')" v-if="valiUsable('labAddress')"  prop="trfLab.labAddress"  :rules="{required:valiRequired('labAddress'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                            <el-input maxlength="500" v-model="trf.trfLab.labAddress" autocomplete="off" clearable></el-input>
                        </el-form-item>
                    </el-col>

                  <el-col :span="12">
                    <el-form-item :label="$t('cclTrf.needUfiCode')" v-if="isShowUfiCode()"  prop="isNeedUfiCode">
                      <el-checkbox-group v-model="isNeedUfiCodeShow" style="padding: 4px;">
                        <el-checkbox  name="type"></el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>

                  </el-col>


                </el-row>
            </el-card>
            <!--CCL Form-->
            <el-card class="sgs-box" id="form_card" v-if="isCclFormFlag">
                <form-component ref="cclForm"
                                :cclCodes="specificCodes"
                                :formCodes="specificCodesStr"
                                :formDatas="trf"
                                :reportLanage="trf.servicRequire.reportLanguage"
                                @getCclFormData="getCclFormData"
                                @validateCclForm="getValidateCclForm"
                                >
                </form-component>
            </el-card>

            <el-card class="sgs-box" id="composition_card">
                <div class="sgs-group">
                    <h3>{{$t('trf.composition')}}</h3>
                </div>
                <table-component ref="cclTable"  :contentTableData="trf.compositionIngredients" @validateCasNo="getValidateCasNo" @getChemicalData="getChemicalData"></table-component>
            </el-card>

            <el-card class="sgs-box" id="service_card">
                <div class="sgs-group">
                    <h3>{{$t('service.servicRequire')}}</h3>
                </div>
                <el-row :gutter="20">
                    <el-col :span="24" v-if="trf.servicRequire.reportLanguage=='1' || trf.servicRequire.reportLanguage=='3'">
                        <el-form-item :label="$t('service.reportHeaderEn')" v-if="valiUsable('reportHeader')" prop="servicRequire.reportHeader" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportHeader"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="trf.servicRequire.reportLanguage=='1' || trf.servicRequire.reportLanguage=='3'">
                        <el-form-item :label="$t('service.reportAddressEn')" v-if="valiUsable('reportAddress')" prop="servicRequire.reportAddress" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportAddress"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24" v-if="trf.servicRequire.reportLanguage=='2' || trf.servicRequire.reportLanguage=='3'">
                        <el-form-item :label="$t('service.reportHeaderZh')" v-if="valiUsable('reportHeaderZh')" prop="servicRequire.reportHeaderZh" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportHeaderZh"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="trf.servicRequire.reportLanguage=='2' || trf.servicRequire.reportLanguage=='3'">
                        <el-form-item :label="$t('service.reportAddressZh')" v-if="valiUsable('reportAddressZh')" prop="servicRequire.reportAddressZh" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportAddressZh"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('contact.phone')"  prop="servicRequire.reportPhone" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input  maxlength="20" v-model="trf.servicRequire.reportPhone"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('contact.email')"  prop="servicRequire.reportEmail" :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                            <el-input  maxlength="200" v-model="trf.servicRequire.reportEmail"
                                      autocomplete="off"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <!--<el-col span="24">
                        <el-form-item :label="$t('service.reportSuccessSend')" >
                            <el-select v-model="emailAddressesValue"
                                       @change="reportDeliveredToChange"
                                       multiple
                                       filterable
                                       allow-create
                                       default-first-option
                                       style="width: 100%;">
                                <el-option v-for="(obj,index) in emailGroupData"
                                           :label="obj.emailGroupName" :key="obj.id"
                                           :value="obj.emailGroupName"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col span="24">
                        <el-form-item :label="$t('service.reportErrorSend')" >
                            <el-select v-model="failEmailAddressesValue"
                                       @change="failReportDeliveredToChange"
                                       multiple
                                       filterable
                                       allow-create
                                       default-first-option
                                       style="width: 100%;">
                                <el-option v-for="(obj,index) in emailGroupData"
                                           :label="obj.emailGroupName" :key="obj.id"
                                           :value="obj.emailGroupName"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col span="12">
                        <el-form-item :label="$t('service.returnSample')" >
                            <el-checkbox-group v-model="returnSampleArry" @change="returnSampleChange">
                                <el-checkbox name="returnSampleCheckbox"
                                             v-for="(returnSample,index) in returnSampleData"
                                             :label="returnSample.sysKey" :key="returnSample.sysKey">
                                    {{returnSample.sysValue}}
                                </el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>-->
                    <el-col :span="24">
                        <el-form-item :label="$t('service.otherRequest')"  v-if="valiUsable('otherRequire')" prop="servicRequire.otherRequire" :rules="{required:valiRequired('otherRequire'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                            <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.otherRequire" :placeholder="$t('service.otherTip')" clearable
                                      autocomplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            <el-card class="sgs-box" id="attachment_card">
                <div class="sgs-group">
                    <h3 style="margin-bottom: 8px;">
                        {{$t('trf.attachment')}}
<!--                        <el-upload
                                style="margin-top: -4px;"
                                class="upload-demo pull-right"
                                action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                :on-success="uploadSuccess"
                                :on-change="uploadChange"
                                :file-list="fileList"
                                :show-file-list="false">
                            <el-button size="small" v-if="trf.trfStatus < 2" type="primary">
                                {{$t('operation.upload')}}
                            </el-button>
                        </el-upload>-->
                      <el-button size="small" v-if="trf.trfStatus < 2" type="primary" @click="openUpload">
                        {{$t('operation.upload')}}
                      </el-button>
                      <sgs-batch-upload :systemID="1" :limit="5" :handle-upload-success="uploadSuccess" :handle-upload-error="handleUploadError"  ref="batchUpload" :upload-url="uploadUrl" :file-max-sizes="5"></sgs-batch-upload>
                    </h3>
                </div>
                <el-table :data="trf.trfAttachments"
                          :element-loading-text="$t('uploadLoadingText')"
                          element-loading-spinner="el-icon-loading"
                          v-loading="uploadLoading"
                          width="100%">
                    <el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="fileName"
                            :label="$t('attachment.name')">
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="180">
                        <template slot-scope="scope">
                            <el-button type="text" @click="downloadAttachmentRow(scope.row)"
                                       size="small" icon="el-icon-download">{{$t('operation.download')}}
                            </el-button>
                            <el-button @click="removeAttachmentRow(scope.$index)" type="text"
                                       size="small" icon="el-icon-delete">{{$t('operation.remove')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
            <el-row>
                <span style="margin-left: 18px">{{$t('trf.serviceClause')}} <a target="_blank" style="color: #ff6600" href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a></span>
            </el-row>
            <el-row class="sgs-footer">
                <!--<el-button v-if="permissionList.confirmSampleBtn && trf.trfStatus==2"
                           type="primary" :loading="btnSubmitLoading"
                           @click="confirmSampleClick()">{{$t('trf.confirmSample')}}
                </el-button>-->
                <el-button type="primary" v-if="saveTrfBtnIsShow"
                           :loading="btnSubmitLoading"
                           @click="onSubmit('1')">
                    {{$t('operation.save')}}
                </el-button>
                <el-button type="primary" v-if="submitTrfBtnIsShow"
                           :loading="btnSubmitLoading"
                           @click="onSubmit('2')">
                    {{$t('operation.submit')}}
                </el-button>
                <el-button type="primary" @click="toTrfList('0')" :loading="btnSubmitLoading">
                    {{$t('operation.backToList')}}
                </el-button>
                <el-button
                        v-if="permissionList.privewTemplateBtn && trf.trfStatus >1  && trf.trfStatus!=6 "
                        type="primary" @click="toPrivewTemplate('0')"
                        :loading="btnSubmitLoading">
                    {{$t('trf.print')}}
                </el-button>
            </el-row>
        </el-form>
        <el-dialog :title="$t('operation.saveAsTemplate')" :visible.sync="trfTemplateDialogFormVisible">
            <el-form :model="trfUserTemplate" ref="trfUserTemplateForm" rules="trfUserTemplateRules">
                <el-form-item :label="$t('trf.trfTemplateName')" label-width="160px" prop="trfTemplateName">
                    <el-input maxlength="200" v-model="trfUserTemplate.trfTemplateName" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="trfTemplateDialogFormVisible = false">{{$t('operation.cancel')}}
                </el-button>
                <el-button size="small" type="primary" :loading="btnSbuTrfTemplateLoading"
                           @click="saveTrfUserTemplate('trfUserTemplate')">{{$t('operation.confirm')}}
                </el-button>
            </div>
        </el-dialog>
        <el-dialog :title="$t('trf.confirmSample')" :visible.sync="trfConfirmSampleDialogFormVisible">
            <el-form :model="confirmSampleForm" ref="confirmSampleForm" rules="confirmSampleFormRules">
                <el-row>
                    <el-form-item :label="$t('trf.sampleReceivedDate')" prop="sampleReceiveDate">
                        <el-date-picker clearable
                                        v-model="confirmSampleForm.sampleReceiveDate"
                                        type="date"
                                        :placeholder="$t('trf.sampleReceivedDate')"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :picker-options="pickerOptions1"
                                        style="width: 100%;">
                        </el-date-picker>
                    </el-form-item>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="trfConfirmSampleDialogFormVisible = false">{{$t('operation.cancel')}}
                </el-button>
                <el-button size="small" type="primary" :loading="btnSbuTrfTemplateLoading"
                           @click="confirmSampleDateSubmit('trfUserTemplate')">{{$t('operation.confirm')}}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
    import {mapGetters} from "vuex";
    import { deepClone } from '@/util/util'
    import {getFieldSettingsByTemplateId} from "@/api/template/template";
    import {
        getCommunicationLogList
    } from "@/api/trf/communication";
    import {
        getServiceType,
        getCloudFileURL,
        getAgentCustomerGroupByParms
    } from "@/api/common/index";
    import {searchCustomer} from "@/api/customer/customer";
    import {
      getTemplateList,
      getTemplateLabList,
      getTemplateLabContactList,
      getCustomerAddressList,
      getCustomerContactList,
      saveTrfUserTemplate,
      getReturnSampleArryCN,
      getReportLanguageArry,
      queryCclTrfDetail,
      saveCclTrfV2,
      returnCclTrfV2,
      cancelCclTrfV2,
      confirmCclSampleDate,
    } from "@/api/trf/trf";
    import {getInvoiceType,checkExistsInvoice} from "@/api/customer/customerInvoice";
    import moment from 'moment'
    import {validatenull,validateEmail} from "@/util/validate";
    import {getMailGroupAndContactEmail} from "@/api/customer/customerEmailGroup";
    import {getCustomerGroup} from "@/api/common/index";
    import {detail} from "@/api/template/template";
    import {getToken} from "@/util/auth"
    import {ProductLineEnums} from "@/commons/enums/BuEnums";
    const  TestResult = () => import('@/views/trf/form/testResult');
    const Communication = () => import('../../../components/dialog/Communication');
    const tableComponent = () => import('@/views/ccl/tableComponent');
    const formComponent = () => import('@/views/ccl/formComponent');

    export default {
        name: 'trfDetail',
        components: {
            TestResult,
            Communication,
            tableComponent,
            formComponent
        },
        props: {
            trfNo: {
                type: String,
                default: null,
            },
            trfStatus: {
                type: String,
                default: null,
            },
        },
        created() {
        },
        data() {
            return {
                uploadUrl: '/api/sgsapi/FrameWorkApi/file/doUpload',
                uploadLoading:false,
                specificCodes:[],
                specificCodesStr:'',
                trfFlag:2,
                cclValidateTable:true,
                cclValidate:true,
                fieldSettingsData:[],
                saveTrfBtnIsShow:true,
                cancelTrfBtnIsShow:false,
                submitTrfBtnIsShow:true,
                saveAsTemplateBtnIsShow:true,
                emailArray: [],
                emailGroupData: [],
                hash: '',
                trfConfirmSampleDialogFormVisible: false,
                trfTemplateDialogFormVisible: false,
                labContactFlag: true,
                showCustmerName: '',
                showCustomerNameFlag:true,
                btnSubmitLoading: true,
                btnSbuTrfTemplateLoading: false,
                isShow: false,
                optionType: '',
                isShowSubmitBtn: true,
                trfShowData: [],
                customerParams: {
                    customerNumber: '',
                },
                isShow: false,//是否为回显数据
                templateDataLoad: false,//模板数据是否加载完成
                isShowReportNo: false,//是否为重新测试 填写repoetNo
                fileList: [],
                isCclFormFlag:false,
                //窗口选中的testPackage数据
                drawerSelTestPackageList: [],
                filterText: '',
                defaultProps: {
                    label: 'testPackageName',
                    children: 'children',
                    value: 'id'
                },
                selTestPackageTreedata: [],
                testPackageTreedata: [],
                trfId: '',
                dialogSelTestPackageFormVisible: false,
                filesData: [],
                dffFormId: '03a1f3b2-713b-413e-8d3d-ad5cbed44981',
                customerId: '',//查询客户条件
                props: {
                    label: 'testPackageName',
                    children: 'parentId',
                    isLeaf: 'leaf',
                    value: 'id'
                },
                onloadTreeFlag: false,
                templateData: [],
                templateLabData: [],
                templateLabContactData: [],
                clientHeight: '',
                buyerCustomerGroupData: [],
                agentCustomerGroupData: [],
                customerAddressData: [],
                customerContactData: [],
                returnSampleData: [],//退样要求
                serviceTypeData: [],
                reportLanguageData: [],
                returnSampleArry: [],
                communicationTableData: [],
                identicalFlag: true,//默认与申请人一样
                selSdsStandards:[],
                isSelfRefrenceFlag: false,
                isDraftReportFlag:false,
                isNeedUfiCode:true,
                isNeedUfiCodeShow:false,
                oldSdsStandard:'',
                testPackageIds: [],
                 formDatas:[],
                trfData:{},
                /*表单参数*/
                trf: {
                    cclForms:[],
                    isSelfRefrence:0,
                    trfType:1,
                    materialIds:'',
                    applicantUser:'',
                    id: '',
                    actionType: 'add',
                    remark: '',
                    retestReportNo: '',//重新测试原来的reportNo需录入
                    dffFormId: '',
                    dffGridId: '',
                    dffGridData: null,
                    dffFormData: null,
                    careLabelData: null,
                    trfStatus: '0',
                    trfTemplateId: '',
                    templateName: '',
                    productLineId: '',
                    productLineCode: '',
                    productLineName: '',
                    retest_report_no: '',//上次重测编号
                    buyerCustomerGroupId: '',
                    buyerCustomerGroupCode: 'General',
                    buyerCustomerGroupName: '',
                    agentCustomerGroupId: '',
                    agentCustomerGroupCode: '',
                    agentCustomerGroupName: '',
                    isRetestFlag: '1',
                    serviceType: '',
                    serviceTypeName: '',
                    reportDeliveredTo: '',
                    failedReportDeliveredTo: '',
                    sampleReceiveDate: '',
                    testPackageAllIds:'',
                    trfCustomer: {
                        sgsCustomerId: '',
                        sgsAccountId: '',
                        customerId: '',
                        customerAddressId: '',
                        customerAddressEn:'',
                        customerNameZh: '',
                        customerNameEn: '',
                        payCustomerNameZh: '',
                        payCustomerAddressZh: '',
                        payCustomerNameEn: '',
                        payCustomerAddressEn: '',
                        isSame: '1',
                    },
                    trfCustomerContact: {},
                    trfLab: {},
                    trfLabContact: {},
                    servicRequire: {
                        isDraftReport:0,
                        reportLanguage: '',//报告语言
                        reportLanguageName: '',
                    },
                    compositionIngredients:[],
                    trfAttachments: [],
                },
                trfUserTemplate: {
                    trfType:1,
                    trfTemplateName: '',
                    trfTemplateData: '',
                },
                confirmSampleForm: {
                    id: '',
                    sampleReceiveDate: new Date(),
                },
                page: {
                    currentPage: 1,
                    pageSize: 1000,
                },
                customerContactParam: {
                    customerId_equal: '',
                    status: '1',
                },
                customerParam: {
                    number: '',
                    taxNo: '',
                    customerName: '',
                    rows: 20
                },
                customerGroupParam: {
                    rows: 5,
                    groupName: '',
                    groupCode:'',
                },
                agentCustomerParam:{},
                templateDataParam: {
                    customerGroupCode: '',
                },
                templateLabDataParam: {
                    trfStatus:1,
                    trfTemplateId: '',
                    productLineCode: '',
                },
                templateLabContactDataParam: {
                     trfStatus:1,
                    trfTemplateId: '',
                    labCode: '',
                    labTypeFlag: '',
                },
                sgsLabParam: {
                    buCode: '',
                    labCode: ''
                },
                communicationParam: {},
                emailGroupParam: {
                    status: 1,
                },
                pickerOptions1: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                },
                trfUserTemplateRules: {
                    trfTemplateName: [
                        {required: true, message: this.$t('trf.placeholder.trfTemplateName'), trigger: 'blur'},
                    ],
                },
                trfRules: {
                    trfTemplateId: [
                        { required: true, message: this.$t('trf.templateSel'),trigger: 'change'},
                    ],
                    'trfLab.labCode': [
                        { required: true, message: this.$t('lab.selLab'),trigger: 'change' }
                    ],
                     'servicRequire.sdsStandard': [
                          {  required: true, message: '请选择SDS', trigger: 'change' }
                    ],
                },
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission"]),
            permissionList() {
                return {
                    cclTrfSaveBtn: this.vaildData(this.permission['sgs:cclTrf:saveCclTrf'], false),
                    cclTrfSubmitBtn: this.vaildData(this.permission['sgs:cclTrf:submitCclTrf'], false),
                    privewTemplateBtn: this.vaildData(this.permission['sgs:cclTrf:privewCclTemplate'], false),
                };
            },
            emailAddressesValue: {
                get() {
                    if (this.trf.reportDeliveredTo) {
                        return this.$lodash.split(this.trf.reportDeliveredTo, ',')
                    }
                    return []
                },
                set(val) {
                    this.$set(this.trf, 'reportDeliveredTo', this.$lodash.join(val, ','))
                }
            },
            failEmailAddressesValue: {
                get() {
                    if (this.trf.failedReportDeliveredTo) {
                        return this.$lodash.split(this.trf.failedReportDeliveredTo, ',')
                    }
                    return []
                },
                set(val) {
                    this.$set(this.trf, 'failedReportDeliveredTo', this.$lodash.join(val, ','))
                }
            },
            sdsStandardValue: {
                get() {
                    if (this.trf.servicRequire.sdsStandard) {
                        return this.$lodash.split(this.trf.servicRequire.sdsStandard, ',')
                    }
                    return []
                },
                set(val) {
                    this.$set(this.trf.servicRequire, 'sdsStandard', this.$lodash.join(val, ','))
                }
            },
        },
        mounted() {
            //判断当前登录用户是否为SGS
            console.log("加载了组件", "===> ccl_trfDetail");
            let isSgs = false;
            if(!validatenull(this.userInfo.dimensions)&&!validatenull(this.userInfo.dimensions.SGSUserRole)) {
                let role = this.userInfo.dimensions.SGSUserRole;
                role.forEach(item => {
                    if (item === 'SgsAdmin' || item=== 'SgsLabUser'){
                        isSgs = true;
                    }
                });
            }
            if (isSgs) {
                this.trf.trfType = 3
            }
            // this.loading();
            //获取路由参数
            this.getParams();
            //查询客户组邮件和联系人邮件数据
            this.searchEmailGroup();
            //获取退货要求
            this.getReturnSampleArry();
            //接口获取报告语言
            this.queryReportLanguage();

            this.fieldSettingsData=null;
            Promise.all([
              /*TestResult().then(()=>{}),
              Communication().then(()=>{}),*/
              tableComponent().then(()=>{}),
              formComponent().then(()=>{})
            ]).then(()=>{debugger;
              // 所有组件都已加载完成，在这里显示操作按钮
              this.btnSubmitLoading = false;
            });
        },
        watch: {
            'hash': function (newVal) {
                //获取路由参数
                this.getParams();
            },
            filterText(val) {
                this.$refs.selTree.filter(val);
            },
            //监听语言变化
            language: function (newVal) {
                //触发查询
                this.getReturnSampleArry();
                //this.queryCustomer();
                this.queryReportLanguage();
                //重新触发查询communication log
                if(this.trf.trfStatus>=2){
                    this.queryCommunicationLog();
                }
            },
            //监听客户群组值是否发生变化
            'trf.buyerCustomerGroupCode': {
                deep: true,
                handler: function (newVal, oldVal) {

                }
            },
            // 监听`clientHeight`发生改变
            clientHeight: function () {
                this.changeFixed(this.clientHeight)
            },
            identicalFlag: function (newVal) {
                this.trf.trfCustomer.isSame = 0;
                this.$set(this.trf.trfCustomerContact,'isSame',0);
                if (newVal) {
                    this.trf.trfCustomer.isSame = 1;
                    this.$set(this.trf.trfCustomerContact,'isSame',1);
                }
            },
            isSelfRefrenceFlag: function (newVal) {
                this.$set(this.trf,'isSelfRefrence',0);
                if (newVal) {
                    this.$set(this.trf,'isSelfRefrence',1);
                }
            },
            isDraftReportFlag: function (newVal) {
                this.$set(this.trf.servicRequire,'isDraftReport',0);
                if (newVal) {
                    this.$set(this.trf.servicRequire,'isDraftReport',1);
                }
            },
          isNeedUfiCodeShow: function (newVal) {
              this.$set(this.trf.servicRequire,'isNeedUfiCode',0);
              if (newVal) {
                this.$set(this.trf.servicRequire,'isNeedUfiCode',1);
              }
            },
        },
        methods: {
            openUpload(){
              let _self = this;
              _self.$nextTick(()=>{
                this.$refs.batchUpload.open();
              });
            },
            handleUploadError(){},
            //获取组成成分数据
            getChemicalData(obj) {
                this.trf.compositionIngredients = obj;
            },
            getCclFormData(obj) {
                this.trf.cclForm.data = JSON.stringify(obj);
            },
            //获取ccl 成分table校验结果
            getValidateCasNo(val) {
                this.cclValidateTable = val;
            },
            //获取CCL表单验证结果
            getValidateCclForm(val) {
                this.cclValidate = val;
            },
            //验证表单项是否展示方法
            valiUsable(code) {
                var usableFlag = true;
                if (this.fieldSettingsData != null && this.fieldSettingsData != undefined) {
                    if (this.fieldSettingsData[code] != null && this.fieldSettingsData[code] != undefined) {
                        usableFlag = this.fieldSettingsData[code].usable != 1 ? false : true
                    }
                }
                return usableFlag;
            },
            //验证必填项方法
            valiRequired(code) {
                var requiredFlag = false;
                if (this.fieldSettingsData != null && this.fieldSettingsData != undefined) {
                    if (this.fieldSettingsData[code] != null && this.fieldSettingsData[code] != undefined) {
                        requiredFlag = this.fieldSettingsData[code].required == 1 ? true : false
                    }
                }
                return requiredFlag;
            },
            searchEmailGroup() {
                var params = {};
                getMailGroupAndContactEmail(Object.assign(params, this.emailGroupParam)).then(res => {
                    this.emailGroupData = res.data.data;
                });
            },
            reportDeliveredToChange(emailList) {
                let obj = {};
                let pasteEmailArr = [];
                emailList.find((item1) => {
                    obj = this.emailGroupData.find((item) => {
                        if (item1 == item.emailGroupName) {//判断当前邮件是否和
                            return item;
                        }
                    });
                    if (validatenull(obj)) {//手动输入邮箱验证
                        let validateRes = validateEmail(item1);
                        if (validateRes) {
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    } else {
                        if (obj.type != 2) {//邮件组数据
                            var contactEmail = obj.contacts;
                            contactEmail.find((item2) => {
                                if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
                                    pasteEmailArr.push(item2.contactEmail);
                                }
                            });
                        } else {
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    }
                });
                this.emailAddressesValue = pasteEmailArr;
                if (this.emailAddressesValue.length != 0) {
                    this.trf.reportDeliveredTo = this.emailAddressesValue.join(',')
                } else {
                    this.trf.reportDeliveredTo = '';
                }
            },
            failReportDeliveredToChange(emailList) {
                let obj = {};
                let pasteEmailArr = []
                emailList.find((item1) => {
                    obj = this.emailGroupData.find((item) => {
                        if (item1 == item.emailGroupName) {//判断当前邮件是否和
                            return item;
                        }
                    });
                    if (validatenull(obj)) {//手动输入邮箱验证
                        let validateRes = validateEmail(item1);
                        if (validateRes) {
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    } else {
                        if (obj.type != 2) {//邮件组数据
                            var contactEmail = obj.contacts;
                            contactEmail.find((item2) => {
                                if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
                                    pasteEmailArr.push(item2.contactEmail);
                                }
                            });
                        } else {
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    }
                });
                this.failemailAddressesValue = pasteEmailArr;
                if (this.failemailAddressesValue.length != 0) {
                    this.trf.failedReportDeliveredTo = this.failemailAddressesValue.join(',')
                } else {
                    this.trf.failedReportDeliveredTo = '';
                }
            },
            serviceTypeChange(val) {
               this.trf.serviceTypeName = val;
            },
            sdsStandardChange(sdsStandard) {
                  this.trf.servicRequire.sdsStandard = sdsStandard.join(',')
                  if(this.trf.servicRequire.sdsStandard.indexOf('1') != -1 ){
                    if(this.oldSdsStandard.indexOf('1') == -1){
                      this.isNeedUfiCodeShow = true;
                      this.trf.servicRequire.isNeedUfiCode = 1;
                    }
                  }else {
                    this.isNeedUfiCodeShow = false;
                    this.trf.servicRequire.isNeedUfiCode = 0;
                  }
                  this.oldSdsStandard = this.trf.servicRequire.sdsStandard
            },
            reportChange(val) {
                let obj = {};
                obj = this.reportLanguageData.find((item) => {
                    return item.sysKey === val;
                });
                if (obj != undefined && obj != null) {
                    this.trf.servicRequire.reportLanguageName = obj.sysValue;
                }
                //如果是CCL自定义表单的话 重新加载验证前数据
                if (this.isCclFormFlag) {
                    this.$refs.cclForm.initOption();
                    if (this.trf.servicRequire.reportLanguage != null && this.trf.servicRequire.reportLanguage != undefined) {
                        this.$refs.cclForm.checkCclFormByReportLanguage(this.trf.servicRequire.reportLanguage);
                    }
                }
            },
            queryCommunicationLog() {
                var communicationParams = {};
                getCommunicationLogList(Object.assign(communicationParams, this.communicationParam)).then(res => {
                    const data = res.data.data;
                    this.communicationTableData = data;
                });
            },
            toPrivewTemplate() {
              let sgsToken = getToken();
                window.open("/api/sgs-mart/v2/ccl/trf/printCclTrf?trfId=" + this.trf.id+"&trfNo="+this.trf.trfNo+"&signature="+this.$route.query.signature+"&sgsToken="+sgsToken);

            },
            loading() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                setTimeout(() => {
                    loading.close();
                }, 2500);
            },
            queryReportLanguage() {
                getReportLanguageArry(this.language).then(res => {
                    const data = res.data;
                    if (data.length > 0) {
                        this.reportLanguageData = data;
                        //默认设置为英文报告语言
                        if (validatenull(this.trf.servicRequire.reportLanguage)) {
                            this.$set(this.trf.servicRequire, 'reportLanguage', '1')
                            let obj = {};
                            obj = this.reportLanguageData.find((item) => {
                                return item.sysKey === 1 || item.sysKey === '1';
                            });
                            if (obj != undefined && obj != null) {
                                this.$set(this.trf.servicRequire, 'reportLanguageName', obj.sysValue)
                            }
                        }
                    }
                });
            },
            queryCustomer(bossNo) {
                this.$set(this.customerParam, 'number', bossNo)
                var params = {};
                searchCustomer(Object.assign(params, this.customerParam)).then(res => {
                    const data = res.data.data;
                    if (data.length > 0) {
                        this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                        if (this.trf.servicRequire.reportHeader == '' || this.trf.servicRequire.reportHeader == null) {
                            this.$set(this.trf.servicRequire, 'reportHeader', data[0].nameEN)
                        }
                        this.$set(this.trf.trfCustomer, 'sgsCustomerId', data[0].customerId);
                        this.$set(this.trf.trfCustomer, 'sgsAccountId', data[0].accountId);
                        this.$set(this.trf.trfCustomer, 'customerNameZh', data[0].nameCN);
                        this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                        if (this.language == 'zh-CN') {
                            this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                            if (this.trf.servicRequire.reportHeader == '' || this.trf.servicRequire.reportHeader == null) {
                                this.$set(this.trf.servicRequire, 'reportHeader', data[0].nameCN)
                            }
                        }
                    }
                });
            },
            trfTableDateFormtter(row, column) {
                var date = row[column.property];
                if (date == undefined || date == '') {
                    return this.$t('trf.notSubmit');
                }
                ;
                return moment(date).format("YYYY-MM-DD")
            },
            trfTableStatusFormtter(row, column) {
                var status = row[column.property];
                if (status == 1 || status == '1') {
                    return this.$t('trfStatus.draft');
                } else if (status == 2 || status == '2') {
                    return this.$t('trfStatus.submitted');
                } else if (status == 3 || status == '3') {
                    return this.$t('trfStatus.preparation');
                } else if (status == 4 || status == '4') {
                    return this.$t('trfStatus.testing');
                } else if (status == 5 || status == '5') {
                    return this.$t('trfStatus.completed');
                } else if (status == 6 || status == '6') {
                    return this.$t('trfStatus.cancel');
                } else {
                    return '';
                }
            },
            //返回列表
            toTrfList() {
                this.$router.push({path: '/ccl/trf/list', query: {}});
            },
            treeCheckChange(data, checked, indeterminate) {
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.testPackageName.toLowerCase().indexOf(value) !== -1;
            },
            returnSampleChange(value) {
                //returnSampleData;
                var returnSampleName = '';
                var returnSample = this.returnSampleArry.join(',');
                var returnSampleArray1 = returnSample.split(",");
                if (returnSampleArray1 != '' && returnSampleArray1 != undefined) {
                    returnSampleArray1.find((item) => {
                        this.returnSampleData.find((item1) => {
                            if (item == item1.sysKey) {
                                returnSampleName += item1.sysValue + ',';
                            }
                        });
                    });
                }
                if (returnSampleName.length > 0) {
                    returnSampleName = returnSampleName.substr(0, returnSampleName.length - 1);
                }
                this.trf.servicRequire.returnSampleName = returnSampleName;
                this.trf.servicRequire.returnSampleRequire = returnSample; // 记录所有被选中项的下标
            },
            //初始化页面需加载的数据
            initMethod(customerId) {
                //查询申请人数据
                this.customerId = customerId;
                this.trf.trfCustomer.customerId = customerId;
                this.customerContactParam.customerId_equal = customerId;
                //查询公司地址
                this.searchCustomerAddressData();
                //查询联系人
                this.searchCustomerContactData();
                //接口获取当前登录用户查询客户信息
                this.queryCustomer(this.userInfo.bossNo);
            },
            getParams() {
                // 取到路由带过来的参数
                const templateId = this.$route.query.templateId;
                const routerParams = this.$route.query.id;
                const actionType = this.$route.query.actionType;
                this.optionType = actionType;
                //判断是否存在bossNo
                if (validatenull(this.userInfo.bossNo)) {
                    //申请方可手动输入
                    this.showCustomerNameFlag = false;
                }
                var title = this.$t('route.trf');
                if (this.$route.query.title != null && this.$route.query.title != undefined) {
                    title = this.$route.query.title;
                }
                //document.title = title;
                if (actionType == 'add' || actionType == '' || actionType == undefined) {
                    if (!validatenull(this.userInfo.companyId)) {
                        this.initMethod(this.userInfo.companyId);
                    }
                }
                //触发选择模板事件
                if(!validatenull(templateId)){
                    this.selectTemplateChange(templateId);
                }
                if (actionType != '' && actionType != null) {
                    this.trf.actionType = actionType;
                } else {
                    this.trf.actionType = 'add';
                }

                // 将数据放在当前组件的数据内
                this.trfId = routerParams;
                if (routerParams != '' && routerParams != undefined) {
                    this.queryTrf(this.trfId);
                }

            },


            queryTrf(trfId) {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                let param = {
                  trfId: this.$route.query.id,
                  trfNo: this.$route.query.trfNo,
                  signature: this.$route.query.signature
                };
                queryCclTrfDetail(param).then(res => {
                    loading.close();
                    this.isShow = true;
                    const data = res.data.data;
                    this.trf = data;
                      //this.selectTemplateChange(this.trf.trfTemplateId);
                      //处理CCL数据

                    console.log(this.trf);
                    this.saveTrfBtnIsShow = true;
                    this.submitTrfBtnIsShow = true;
                    this.saveAsTemplateBtnIsShow = true;
                    this.cancelTrfBtnIsShow = false;
                    if (this.optionType == 'copy') {
                        this.$set(this.trf, "trfStatus", 1);
                    }
                    if (!this.permissionList.cclTrfSaveBtn || this.trf.trfStatus >= 2) {
                        this.saveTrfBtnIsShow = false;
                    }
                    if (!this.permissionList.cclTrfSubmitBtn || this.trf.trfStatus >= 2) {
                        this.submitTrfBtnIsShow = false;
                    }

                    //取消按钮控制  1、有该权限，提交状态下当前用户可以操作自己的 2、已提交 < 进行中 sgs可操作 >2 && <5
                    //permissionList.cancelBtn && trf.trfStatus<=4 && trf.trfStatus>1 && userInfo.userMgtId == trf.applicantUser
                   /* if (this.permissionList.cancelBtn) {
                        if (this.trf.trfStatus == 2) {
                            if (this.trf.applicantUser == this.userInfo.userMgtId) {
                                this.cancelTrfBtnIsShow = true;
                            }
                        } else if (this.trf.trfStatus > 2 && this.trf.trfStatus < 5) {//需配置SGS管理员 userManage权限
                            let isSgs = false;
                            if (!validatenull(this.userInfo.dimensions) && !validatenull(this.userInfo.dimensions.SGSUserRole)) {
                                let role = this.userInfo.dimensions.SGSUserRole;
                                role.forEach(item => {
                                    if (item === 'SgsAdmin') {
                                        isSgs = true;
                                    }
                                });
                            }
                            if (isSgs) {//SGS管理员才可展示取消按钮
                                this.cancelTrfBtnIsShow = true;
                            }
                        }
                    }*/

                    if (this.optionType != 'add') {
                        if (this.trf.applicantUser != this.userInfo.userMgtId) {
                            this.saveTrfBtnIsShow = false;
                            this.submitTrfBtnIsShow = false;
                            this.saveAsTemplateBtnIsShow = false;
                        }
                    }

                    // 回传父类
                    this.trfNo = this.trf.trfNo;
                    this.trfStatus = this.trf.trfStatus;
                    this.$emit('update:trfNo', this.trfNo);
                    this.$emit('update:trfStatus', this.trfStatus);

                    this.trf.actionType = this.optionType;
                    if (this.optionType == 'copy' || this.optionType == 'add') {
                        this.trf.trfStatus = 0;
                    }
                    //将trfId放入页面传值
                    this.$emit('trfId', this.trf.id);
                    //查询communicationnLog
                    if (this.trf.trfStatus > 1) {
                        this.communicationParam.trfId = this.trf.id;
                        //this.communicationParam.trfType = this.trfFlag;
                        this.queryCommunicationLog();
                    }
                    this.displayTrf();
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
           async displayTrf() {
                if (this.trf.trfStatus >= 1 && this.trf.actionType != 'copy') {
                    this.isShowSubmitBtn = false;
                    this.trfShowData = [];
                    var trfTable = {};
                    trfTable.trfNo = this.trf.trfNo;
                    trfTable.createTime = this.trf.createTime;
                    trfTable.trfSubmissionDate = this.trf.trfSubmissionDate;
                    trfTable.trfStatus = this.trf.trfStatus;
                    this.trfShowData.push(trfTable);
                }
                this.isShowReportNo = false;
                if (this.trf.isRetestFlag == 2) {
                    this.isShowReportNo = true;
                }
                if (this.trf.trfCustomer.isSame == 1 || this.trf.servicRequire.isSame == '1') {
                    this.identicalFlag = true;
                } else {
                    this.identicalFlag = false;
                }
                if (this.trf.servicRequire.isDraftReport== 1 || this.trf.servicRequire.isDraftReport == '1') {
                    this.isDraftReportFlag = true;
                } else {
                    this.isDraftReportFlag = false;
                }
                if(this.trf.servicRequire.isNeedUfiCode== 1 || this.trf.servicRequire.isNeedUfiCode == '1'){
                    this.isNeedUfiCodeShow = true;
                }else {
                    this.isNeedUfiCodeShow = false;
                }

                //回显地址
                if (!validatenull(this.trf.trfCustomer.customerId)) {
                    var customerAddressParams = {};
                    this.customerContactParam.customerId_equal = this.trf.trfCustomer.customerId;
                    if (this.trf.trfCustomer.customerId == -1) {
                        this.trf.trfCustomer.customerId = '';
                    }
                    getCustomerAddressList(Object.assign(customerAddressParams, this.customerContactParam)).then(res => {
                        const data = res.data.data;
                        this.customerAddressData = data;
                    });
                    //回显customerContact
                    this.customerContactData.customerId = this.trf.trfCustomer.customerId;
                    if (this.trf.trfCustomer.customerId == -1 || this.trf.trfCustomer.customerId == undefined) {
                        this.customerContactData.customerId = '';
                    }
                    var customerContactparams = {};
                    getCustomerContactList(Object.assign(customerContactparams, this.customerContactParam)).then(res => {
                        const data = res.data.data;
                        this.customerContactData = data;
                    });
                }

                //回显联系人数据
                if (!validatenull(this.userInfo.companyId)) {
                    this.customerContactParam.customerId_equal = this.userInfo.companyId;
                    var addresParams = {};
                    getCustomerAddressList(Object.assign(addresParams, this.customerContactParam)).then(res => {
                        const data = res.data.data;
                        this.customerAddressData = data;
                    });
                    var contactParams = {};
                    getCustomerContactList(Object.assign(contactParams, this.customerContactParam)).then(res => {
                        const data = res.data.data;
                        this.customerContactData = data;
                    });
                }
                var agentParams = {};
                this.$set(this.customerGroupParam, 'groupName', this.trf.agentCustomerGroupName);
                var agentParams = {};
                this.$set(this.agentCustomerParam, 'customerName', this.trf.trfCustomer.agentCustomerNameEn);
                this.$set(this.agentCustomerParam, 'rows', 5);
                getAgentCustomerGroupByParms(Object.assign(agentParams, this.agentCustomerParam)).then(res => {
                    const data = res.data.data;
                    this.agentCustomerGroupData = data;
                    this.trf.agentCustomerName = this.trf.trfCustomer.agentCustomerNameEn;
                });

                //回显template下拉数据
                //getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
                   let obj = {};
                   let templateRes = await detail(this.trf.trfTemplateId);
                    obj =templateRes.data.data;
                    this.templateDataLoad = true;
                    this.templateData = obj;
                    //处理模板中的SpecificCodes
                    this.specificCodesStr=obj.specificCode;
                    this.specificCodes=obj.specificCode.split(',');
                    this.trf.cclForms.find((item) => {
                           this.trf[item.formCode]=JSON.parse(item.data);
                    });
                    //加载自定义表单组件
                    this.isCclFormFlag = true;
                    //回显服务类型
                    if (obj != null && obj != undefined) {
                        //回显必填校验
                        getFieldSettingsByTemplateId(obj.id).then(res => {
                            if (res.data.code == 200) {
                                this.fieldSettingsData = res.data.data;
                            }
                        });

                        getServiceType(obj.productLineId).then(res => {
                            const data = res.data.data;
                            this.serviceTypeData = data;
                        });
                        //回显实验室以及实验室联系人
                        var labParams = {};
                        this.$set(this.templateLabDataParam, 'trfTemplateId', obj.id);
                        this.$set(this.templateLabDataParam, 'productLineCode', obj.productLineCode);
                        this.$set(this.templateLabDataParam, 'trfStatus', this.trf.trfStatus);
                        getTemplateLabList(Object.assign(labParams, this.templateLabDataParam)).then(res => {
                            const data = res.data.data;
                            this.templateLabData = data;
                        });
                        //查询实验室联系人
                        this.$set(this.templateLabContactDataParam, 'labCode', this.trf.trfLab.labCode);
                        this.$set(this.templateLabContactDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                        var labContactParams = {};
                        getTemplateLabContactList(Object.assign(labContactParams, this.templateLabContactDataParam)).then(res => {
                            const data = res.data.data;
                            this.templateLabContactData = data;
                            if (this.templateLabContactData == null || this.templateLabContactData == undefined || this.templateLabContactData.length == 0) {//无联系人 不需要选择
                                this.labContactFlag = false;
                                return false;
                            }
                        });
                    }
                //});
                var returnSampleRequireStr = this.trf.servicRequire.returnSampleRequire;
                if (returnSampleRequireStr != null && returnSampleRequireStr != undefined) {
                    this.returnSampleArry = returnSampleRequireStr.split(',');
                }

                this.trf.servicRequire.reportLanguage = this.trf.servicRequire.reportLanguage + '';
                this.trf.isRetestFlag = this.trf.isRetestFlag + '';
                if (this.trf.trfStatus == 2) {//自动触发撤销TRF操作
                    if (this.$route.query.actionType == 'edit') {
                        //alert("//自动触发撤销TRF操作");
                        this.returnTrfStatusClick();
                    }
                }

            },
            getReturnSampleArry() {
                getReturnSampleArryCN(this.language).then(res => {
                    const data = res.data;
                    if (data.length > 0) {
                        this.returnSampleData = data;
                    }
                });
            },
            changeRetest(value) {
                this.trf.isRetestFlag = value;
                if (value == 1) {
                    this.isShowReportNo = false;
                    this.trf.retestReportNo = '';
                } else {
                    this.isShowReportNo = true;
                }
            },
            //获取服务类型
            searchServiceType: function (BUID) {
                getServiceType(BUID).then(res => {
                    const data = res.data.data;
                    this.serviceTypeData = data;
                    //默认服务类型为Regular
                    if (this.serviceTypeData != null && this.serviceTypeData != undefined && this.serviceTypeData.length > 0) {
                        this.trf.serviceType = '1';
                        this.trf.serviceTypeName = 'Regular';
                    } else {
                        this.trf.serviceType = '';
                        this.trf.serviceTypeName = '';
                    }
                });
            },
            handleNodeClick(data) {
                this.trf.testPackage.testPackageId = data.id;
            },
            //另存为TRF模板保存方法
            saveTrfUserTemplate() {
                if (this.trfUserTemplate.trfTemplateName == '' || this.trfUserTemplate.trfTemplateName == null) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('trf.placeholder.trfTemplateName'),
                        type: 'warning'
                    });
                    return false;
                }
                this.btnSbuTrfTemplateLoading = true;
                //模板需要将trf ID和trfNo清空
                // this.trf.id = null;
                // this.trf.trfNo = null;
                // this.trf.trfStatus = 0;//将模板状态设置为初始状态
                let templateDate = deepClone(this.trf);
                templateDate.id = null;
                templateDate.trfNo = null;
                templateDate.trfStatus = 0;
                this.$set(this.trfUserTemplate, 'trfType', this.trfFlag);
                this.$set(this.trfUserTemplate, 'productLineCode', ProductLineEnums.CCHEMLAB.CODE);
                this.$set(this.trfUserTemplate, 'trfTemplateData', JSON.stringify(templateDate));
                saveTrfUserTemplate(this.trfUserTemplate).then(res => {
                    this.trfTemplateDialogFormVisible = false;
                    this.btnSbuTrfTemplateLoading = false;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                }, error => {
                    this.btnSbuTrfTemplateLoading = false;
                    //this.$message.error(this.$t('api.error'));
                });
            },
            //另存为TRF模板
            saveAsTrfTemplate() {
                this.trfUserTemplate.trfTemplateName = '';
                this.trfTemplateDialogFormVisible = true;
            },
            //return 状态回退点击事件
            returnTrfStatusClick() {
                this.$confirm(this.$t('trf.confirmReturnTrf'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    this.btnSubmitLoading = true;
                    returnCclTrfV2(this.trf.id, this.$route.query.signature).then(res => {
                        debugger
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        this.btnSubmitLoading = false;
                        let hashVal = new Date().getTime()
                        this.$router.push({
                            path: '/ccl/trf/trfDetail',
                            query: Object.assign(this.$route.query, {actionType: 'edit', hash: hashVal, signature: res.data.data})
                        })
                        this.hash = hashVal;
                        //this.$router.push({path: '/trf/list', query: {}});
                    }, error => {
                        this.btnSubmitLoading = false;
                        // this.$message.error(this.$t('api.error'));
                    });
                }).catch(() => {
                    /* this.btnSubmitLoading=false;
                     this.$message.error(this.$t('api.error'));*/
                });
            },
            //确认收样点击事件
            confirmSampleClick() {
                this.confirmSampleForm.id = this.trf.id;
                if (this.confirmSampleForm.sampleReceiveDate == '' || this.confirmSampleForm.sampleReceiveDate == null) {
                    this.confirmSampleForm.sampleReceiveDate = dateVal;
                }
                this.trfConfirmSampleDialogFormVisible = true;
            },
            //确认收样表单提交
            confirmSampleDateSubmit() {
                this.confirmSampleForm.sampleReceiveDate = moment(this.confirmSampleForm.sampleReceiveDate).format("YYYY-MM-DD HH:mm:ss");
                confirmCclSampleDate(this.confirmSampleForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.trfConfirmSampleDialogFormVisible = false;
                    let hashVal = new Date().getTime()
                    this.$router.push({
                        path: '/ccl/trf/trfDetail',
                        query: {id: this.trf.id, flag: this.trfFlag, actionType: 'detail', hash: hashVal,trfNo:this.trf.trfNo,signature:res.data.signature}
                    })
                    this.hash = hashVal;
                }, error => {
                    this.$message.error(this.$t('api.error'));
                });
            },
            //取消申请单
            cancelTrf(status) {
                this.$confirm(this.$t('trf.confirmCancelTrf'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    this.btnSubmitLoading = true;
                    cancelCclTrfV2(this.trf.id).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        this.btnSubmitLoading = false;
                        let hashVal = new Date().getTime()
                        this.$router.push({
                            path: '/ccl/trf/trfDetail',
                            query: {id: this.trf.id, flag: this.trfFlag, actionType: 'edit', hash: hashVal,trfNo:res.data.trfNo,signature:res.data.data}
                        })
                        this.hash = hashVal;
                        //this.$router.push({path: '/trf/list', query: {}});
                    }, error => {
                        this.btnSubmitLoading = false;
                        //this.$message.error(this.$t('api.error'));
                    });
                }).catch(() => {
                    /* this.btnSubmitLoading=false;
                     this.$message.error(this.$t('api.error'));*/
                });
            },
            async onSubmit(submitFlag) {
                  console.log(this.trf);
                //没有模板的话 禁止保存和提交
                if(validatenull(this.trf.trfTemplateId)){
                      this.$notify({
                          title: this.$t('tip'),
                          message: 'Corresponding CCL template not found!',
                          type: 'warning'
                      });
                      return false;
                }
                //验证 只有提交的时候做验证
                if (submitFlag == '2' || submitFlag == 2) {
                        if (this.isCclFormFlag) {
                            //自定义表单的话加入报告语言校验
                            if (this.trf.servicRequire.reportLanguage != null && this.trf.servicRequire.reportLanguage != undefined) {
                                this.$refs.cclForm.checkCclFormByReportLanguage(this.trf.servicRequire.reportLanguage);
                            }
                            await this.$refs.cclForm.checkCclForm();
                            //校验ccl table
                            this.$refs.cclTable.checkCclTable();
                            if (!this.cclValidate || !this.cclValidateTable) {
                                this.$notify({
                                    title: this.$t('tip'),
                                    message: this.$t('trf.validate.requiredBlur'),
                                    type: 'warning'
                                });
                                return false;
                            }
                        }
                    //验证申请单数据
                    this.$refs['trf'].validate((valid) => {
                        if (valid) {
                            //验证CCL 自定义表单
                            this.saveTrfSummit(submitFlag);
                        } else {
                            this.$notify({
                                title: this.$t('tip'),
                                message: this.$t('trf.trfValidateError'),
                                type: 'warning'
                            });
                            return false;
                        }
                    });
                } else {
                    this.saveTrfSummit(submitFlag);
                }
            },
            saveTrfSummit(submitFlag) {
                //处理CCL自定义表单
                   this.saveCclData();
                //07-13添加factory回写 函数调用；
                this.trf.trfStatus = submitFlag;
                //提交表单
                this.btnSubmitLoading = true;
                this.trf.signature = this.$route.query.signature
                saveCclTrfV2(this.trf).then(res => {
                    this.btnSubmitLoading = false;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    var actionType = 'detail';
                        let hashVal = new Date().getTime()
                        this.$router.push({
                            path: '/ccl/trf/trfDetail',
                            query: {
                                id: res.data.data.data.id,
                                actionType: actionType,
                                flag: this.trfFlag,
                                hash: hashVal,
                                trfNo: res.data.data.data.trfNo,
                                signature:res.data.data.data.signature
                            }
                        })
                        this.hash = hashVal;
                    /*if (submitFlag == '2' || submitFlag == 2) {

                        this.$alert(this.$t('trf.printTrfTip'), this.$t('tip'), {
                            confirmButtonText: this.$t('operation.confirm'),
                            callback: action => {
                                let hashVal = new Date().getTime()
                                this.$router.push({
                                    path: '/ccl/trf/trfDetail',
                                    query: {
                                        id: res.data.data.data.id,
                                        actionType: actionType,
                                        flag: this.trfFlag,
                                        hash: hashVal
                                    }
                                })
                                this.hash = hashVal;
                            }
                        });
                    }*/
                }, error => {
                    this.trf.trfStatus = '0';//进入异常的话 状态初始化
                    this.btnSubmitLoading = false;
                    this.$message.error(this.$t('api.error'));
                });
            },
            saveCclData(){
                   if(this.specificCodes!=null && this.specificCodes!=undefined && this.specificCodes.length>0){
                       var  that=this;
                    var  cclForms=[];
                    that.specificCodes.find((item) => {
                        var  cclform={};
                        cclform['formCode']=item;
                        cclform['data'] = JSON.stringify(that.trf[item]);
                        cclForms.push(cclform);
                        delete that.trf[item];
                    });
                    that.trf.cclForms=cclForms;
                    console.log(cclForms);
                }
            },
            contactrNameChange(val) {
                let obj = {};
                obj = this.customerContactData.find((item) => {
                    return item.contactName === val;
                });
                if (obj != undefined && obj != null) {
                    //获取联系人详情
                    this.$set(this.trf.trfCustomerContact, 'applyContactTel', obj.contactTel);
                    this.$set(this.trf.trfCustomerContact, 'applyContactEmail', obj.contactEmail);
                }
            },
            customerAddressChange(val) {
                let obj = {};
                obj = this.customerAddressData.find((item) => {
                    return item.addressDetail === val;
                });
                if (obj != undefined && obj != null) {
                    if (this.trf.servicRequire.reportAddress == '' || this.trf.servicRequire.reportAddress == null) {
                        this.$set(this.trf.servicRequire, 'reportAddress', obj.addressDetail);
                    }
                } else {
                    this.$set(this.trf.servicRequire, 'reportAddress', val);
                }
            },
            selectLabChange(val) {
                this.$set(this.trf.trfLab, 'labAddress', '');
                this.$set(this.trf.trfLab, 'labName', '');
                this.$set(this.trf.trfLabContact, 'labContactId', '');
                this.templateLabContactData = [];
                let obj = {};
                obj = this.templateLabData.find((item) => {
                    return item.labCode === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.trf.trfLab, 'labCode', obj.labCode);
                    this.$set(this.trf.trfLab, 'labName', obj.labName);
                    this.$set(this.trf.trfLabContact, 'labCode', obj.labName);
                    this.$set(this.trf.trfLabContact, 'labAddress', obj.labName);
                    this.$set(this.trf.trfLabContact, 'labName', obj.labName);
                    this.$set(this.templateLabContactDataParam, 'labTypeFlag', obj.labTypeFlag);
                    this.$set(this.trf.trfLab, 'labAddress', obj.labAddress);
                }
                this.$set(this.templateLabContactDataParam, 'labCode', val);
                this.$set(this.templateLabContactDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                //查询实验室联系人
                this.searchTemplateLabContactData();
            },
            selectLabContactChange(val) {
                let obj = {};
                obj = this.templateLabContactData.find((item) => {
                    return item.labContactId === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.trf.trfLabContact, 'contactName', obj.labContactName);
                    this.$set(this.trf.trfLabContact, 'contactEmail', obj.contactEmail);
                    this.$set(this.trf.trfLabContact, 'contactTel', obj.contactTel);
                    //this.trf.trfLab.labAddress = obj.labAddress;//改为从lab中取地址
                }
            },
            //查询模板实验室下拉数据
            searchTemplateLabContactData() {
                var params = {};
                getTemplateLabContactList(Object.assign(params, this.templateLabContactDataParam)).then(res => {
                    const data = res.data.data;
                    this.templateLabContactData = data;
                    if (this.templateLabContactData == null || this.templateLabContactData == undefined || this.templateLabContactData.length == 0) {//无联系人 不需要选择
                        this.labContactFlag = false;
                        return false;
                    }
                    this.labContactFlag = true;
                    let obj = {};
                    obj = this.templateLabContactData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        this.$set(this.trf.trfLabContact, 'labContactId', obj.labContactId);
                        this.$set(this.trf.trfLabContact, 'contactName', obj.labContactName);
                        this.$set(this.trf.trfLabContact, 'contactEmail', obj.contactEmail);
                        this.$set(this.trf.trfLabContact, 'contactTel', obj.contactTel);

                        //加载出实验室数据
                        this.$set(this.trf.trfLab, 'labCode', obj.labCode);
                        let labObj = {};
                        labObj = this.templateLabData.find((item) => {
                            return item.labCode === obj.labCode;
                        });
                        if (labObj != undefined && labObj != null) {
                            //赋值实验室地址
                            this.$set(this.trf.trfLab, 'labAddress', labObj.labAddress);
                            this.$set(this.trf.trfLabContact, 'labName', labObj.labName);
                            this.$set(this.trf.trfLabContact, 'labCode', labObj.labCode);
                            this.$set(this.trf.trfLabContact, 'labAddressb', labObj.labAddress);
                        }
                    }
                });
            },
            async selectTemplateChange(val) {
                this.trf.trfLab = {};
                this.trf.trfLabContact = {};
                this.trf.serviceType = '';
                this.trf.dffFormId = '';
                this.trf.dffGridId = '';
                this.dataList = this.defaultDataList;
                this.isCclFormFlag = false;
                let obj = {};
                let templateRes = await detail(val);
                obj =templateRes.data.data;
                //加载dff form组件
                if (obj != undefined && obj != null) {
                    this.$set(this.trf, 'trfTemplateId', val);//为Trf赋值templateId
                    if (this.trfFlag == 2) {
                        let fieldData = await getFieldSettingsByTemplateId(obj.id);
                        if (fieldData.data.code == 200) {
                            this.fieldSettingsData = fieldData.data.data;
                        }
                    }
                    //处理模板中的SpecificCodes
                    this.specificCodesStr=obj.specificCode;
                    this.specificCodes=obj.specificCode.split(',');

                    //加载自定义表单组件
                    this.isCclFormFlag = true;
                    this.$set(this.trf, 'templateName', obj.templateName);
                    this.$set(this.trf, 'productLineId', obj.productLineId);
                    this.$set(this.trf, 'productLineCode', obj.productLineCode);
                    this.$set(this.templateLabDataParam, 'productLineCode', obj.productLineCode);
                    this.$set(this.trf, 'productLineName', obj.productLineName);
                    //查询服务类型sel
                    this.searchServiceType(obj.productLineId);
                }
                //查询该模板下的实验室及联系人
                this.$set(this.templateLabDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                //查询该模板下的实验室
                this.searchTemplateLabData();
            },
            //查询模板实验室下拉数据
            searchTemplateLabData() {
                var params = {};
                getTemplateLabList(Object.assign(params, this.templateLabDataParam)).then(res => {
                    const data = res.data.data;
                    this.templateLabData = data;
                    if (data.length == 1) {
                        this.selectLabChange(data[0].labCode);
                    }
                });
            },
            searchTemplateData() {
                var params = {};
                getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
                    this.templateDataLoad = true;
                    const data = res.data.data;
                    this.templateData = data;
                });
            },
            uploadSuccess(data) {
                if(data){
                  data.data.forEach(item =>{
                    const attachment = {
                      'attachmentId': item.cloudID,
                      'fileUrl': item.path,
                      'fileName': item.attachmentName+"."+item.suffixes,
                      'size': item.size
                    }
                    this.trf.trfAttachments.push(attachment);
                  });
                }
                this.$refs.batchUpload.close();
            },
             uploadChange(file, fileList) {
                if(file.status == 'ready'){
                    //开启loading效果
                    this.uploadLoading=true;
                }else{
                      this.uploadLoading=false;
                }
            },
            downloadAttachmentRow(row) {
                getCloudFileURL(row.attachmentId).then(res => {
                    window.open(res.data, "_blank");
                });
            },
            removeAttachmentRow(index) {
                this.trf.trfAttachments.splice(index, 1);
            },
            selectParentPackage() {
                this.dialogSelTestPackageFormVisible = true;
            },


            searchCustomerContactData() {
                var params = {};
                getCustomerContactList(Object.assign(params, this.customerContactParam)).then(res => {
                    const data = res.data.data;
                    this.customerContactData = data;
                    let obj = {};
                    obj = this.customerContactData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        this.$set(this.trf.trfCustomerContact, 'applyContactName', obj.contactName)
                        this.contactrNameChange(obj.contactName);
                    }
                });
            },
            searchCustomerAddressData() {
                var params = {};
                getCustomerAddressList(Object.assign(params, this.customerContactParam)).then(res => {
                    const data = res.data.data;
                    this.customerAddressData = data;
                    let obj = {};
                    obj = this.customerAddressData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        this.$set(this.trf.trfCustomer, 'customerAddressEn', obj.addressDetail);
                        if (this.trf.servicRequire.reportAddress == '' || this.trf.servicRequire.reportAddress == null) {
                            this.$set(this.trf.servicRequire, 'reportAddress', obj.addressDetail);
                        }
                    }
                });
            },
            changeFixed(clientHeight) { //动态修改样式
                this.$refs.homePage.$el.style.height = clientHeight - 20 + 'px';
            },

            //代理商客户组查询 输入3个字符后再执行查询匹配 最大查询五条数据，防止全部客户组暴露
            searchAgentCustomerGroup(val) {
                this.$set(this.agentCustomerParam, 'customerName', val);
                this.$set(this.agentCustomerParam, 'rows', 5);
                if (val.length >= 3) {//输入满足三个字符后查询客户组数据
                    this.queryAgentCustomerGroupData();
                }
            },
            queryAgentCustomerGroupData() {
                var params = {};
                getAgentCustomerGroupByParms(Object.assign(params, this.agentCustomerParam)).then(res => {
                    const data = res.data.data;
                    this.agentCustomerGroupData = data;
                });
            },

          isShowUfiCode(){
            let isShow = false
            if(this.sdsStandardValue != null && this.sdsStandardValue != undefined && this.sdsStandardValue.indexOf('1') != -1){
              isShow = true;
            }
            return isShow;
          }
        }
    };
</script>
<style lang="scss">

    .el-steps--simple {
        height: 50px;
        background: #424242;

        .el-step {
            cursor: pointer;

            i {
                color: #FFFFFF;
            }

            .el-step__title {
                color: #FFFFFF;
                font-size: 12px;
            }
        }
    }

    .modal {
        z-index: 2010;
    }

    .contact {
        table {
            th {
                padding: 5px;
                text-align: center;
            }

            td {
                padding: 5px 20px;
            }
        }
    }

</style>
