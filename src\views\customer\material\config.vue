<template>
    <div class="maaterial-box">
        <h1 class="top-title">{{$t('navbar.materialManagement')}}</h1>
        <el-card shadow="never" class="sgs-box" >
            <el-form :inline="true" :model="query" size="medium">
                <el-form-item prop="productLineCode">
                    <el-select :placeholder="$t('template.productLine')" clearable filterable
                            v-model="query.productLineCode"
                            style="width:100%" @change="selecProductLineChangeForQuery">
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="customerGroupCode">
                    <el-select
                            :placeholder="$t('customerGroup.name')"
                            clearable filterable v-model="query.customerGroupCode" @change="customerGroupCodeChangeForQuery"
                            style="width:100%">
                        <el-option v-for="(customerGroup,index) in customerGroupData"
                                :label="customerGroup.customerGroupName"
                                :value="customerGroup.customerGroupCode"></el-option>
                    </el-select>
                </el-form-item>
                <!--<el-form-item prop="productCategory">
                    <el-select :placeholder="$t('template.productCategory')" clearable filterable v-model="query.productCategoryId" style="width:100%"   @change="selecProductCategoryChangeForQuery">
                        <el-option v-for="(dict,index) in productCategoryData" :key="dict" :label="dict.productCategoryName"
                                :value="dict.productCategoryId"></el-option>
                    </el-select>
                </el-form-item>-->
<!--                <el-form-item prop="dffFormId">
                    <el-select ref="dffSel" clearable filterable
                            :placeholder="$t('customerMaterialConfig.dffFieldName')"
                            v-model="query.dffFormId" style="width:100%" @change="dffChangeForQuery">
                        <el-option v-for="(dff,index) in dffDataForQuery" :label="dff.name"
                                :value="dff.formId">{{ dff.name }}
                        </el-option>
                    </el-select>
                </el-form-item>-->
                <el-form-item prop="materialListName">
                    <el-input clearable :placeholder="$t('customerMaterialConfig.materialListName')" v-model="query.materialListName">
                    </el-input>
                </el-form-item>
                <!--<el-form-item>
                    <el-input v-model="query.customerName"
                            :placeholder="$t('account.customerName')"
                            clearable></el-input>
                </el-form-item>-->
                <el-form-item>
                    <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                    <el-button type="primary" v-if="permissionList.addBtn" @click="addRow">{{$t('operation.add')}}
                    </el-button>
                </el-form-item>
            </el-form>
            <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                    :element-loading-text="$t('loading')"
                    size="medium" :empty-text="$t('NoData')">
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
                <el-table-column
                        fixed
                        prop="productLineName"
                        :label="$t('template.productLine')">
                </el-table-column>
                <el-table-column
                        fixed
                        prop="customerGroupName"
                        :label="$t('customerMaterialConfig.buyer')">
                </el-table-column>
                <!-- <el-table-column
                        fixed
                        prop="productCategoryName"
                        :label="$t('template.productCategory')">
                </el-table-column>-->
<!--                <el-table-column
                        prop="dffFormName"
                        :label="$t('customerMaterialConfig.dffFieldName')">
                </el-table-column>-->
                <el-table-column prop="materialListName" :label="$t('customerMaterialConfig.materialListName')"></el-table-column>
                <!--<el-table-column
                        prop="status"
                        :label="$t('common.status.title')"
                        align="center">
                    <template slot-scope="scope">
                        <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                    placement="top">
                            <el-switch
                                    v-model="scope.row.status"
                                    active-color="#ff6600"
                                    inactive-color="#ff4949"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @change="changeStatus(scope.row)">
                            </el-switch>
                        </el-tooltip>
                    </template>
                </el-table-column>-->
                <el-table-column
                        :label="$t('operation.title')"
                        width="360">
                    <template slot-scope="scope">
                        <el-button type="text" v-if="permissionList.editBtn" @click="detailRow(scope.row)">
                            {{$t('operation.edit')}}
                        </el-button>
                        <el-button @click="removeRow(scope.row)" v-if="permissionList.deleteBtn" type="text">
                            {{$t('operation.remove')}}
                        </el-button>
                        <el-button @click="toMaterialListManager(scope.row.materialConfigId)" v-if="permissionList.listBtn"
                                type="text">{{$t('customerMaterialConfig.material')}}
                        </el-button>
                        <el-button @click="toMaterialReport(scope.row.materialConfigId)" v-if="permissionList.reportBtn"
                                type="text">{{$t('customerMaterialConfig.materialReport')}}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-card>
        <el-dialog :title="title" width="80%" height="80%" :visible.sync="dialogFormVisible">
            <el-form ref="templateform" :model="templateform"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     :rules="rules"
                     class="sgs-form"
            >
                <el-form-item :label="$t('template.productLine')" prop="productLineCode">
                    <el-select :disabled="editMateral" clearable filterable v-model="templateform.productLineCode"
                               style="width:100%" @change="selecProductLineChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                   :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('customerMaterialConfig.materialListName')" prop="materialListName">
                  <el-input  v-model="templateform.materialListName"/> <!--:disabled="editMateral" 先暂时放开 -->
                </el-form-item>
                <!--<el-form-item :label="$t('template.productCategory')" prop="productCategoryId">
                    <el-select :disabled="editMateral" clearable filterable v-model="templateform.productCategory" style="width:100%"   @change="selecProductCategoryChange">
                        <el-option v-for="(dict,index) in productCategoryData" :key="dict" :label="dict.productCategoryName"
                                   :value="dict.productCategoryId"></el-option>
                    </el-select>
                </el-form-item>-->

                <el-form-item :label="$t('template.customerGroup')" prop="customerGroupCode">
                    <el-select :disabled="editMateral" clearable filterable v-model="templateform.customerGroupCode"
                               style="width:100%" @change="customerGroupCodeChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(customerGroup,index) in customerGroupData"
                                   :label="customerGroup.customerGroupName"
                                   :value="customerGroup.customerGroupCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('customerMaterialConfig.dffFieldName')" prop="dffFormId">
                    <el-select :disabled="editMateral" ref="dffSel" clearable filterable
                               v-model="templateform.dffFormId" style="width:100%" @change="dffChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(dff,index) in dffData" :label="dff.name"
                                   :value="dff.formId">{{ dff.name }}
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-row>
                    <el-table
                            class="classOfDff"
                            :data="tableDataOfDff"
                            style="width: 100%"
                            border
                            size="medium"
                            row-key="dffFieldCode"
                            align="left">
                        <el-table-column prop="dffFormType" align="center" width="120"
                                         :label="$t('customerMaterialConfig.dffFormType')">
                        </el-table-column>

                        <el-table-column prop="dffFieldName" align="center"
                                         :label="$t('customerMaterialConfig.dffFieldName')">
                            <template slot-scope="scope">
                                <span v-if="scope.row.isChange === 1 " style="color: red">
                                        <s>{{ scope.row.dffFieldName }}</s>
                                </span>
                                <span v-if="scope.row.isChange !== 1 ">
                                    {{ scope.row.dffFieldName }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="dffFieldMaterialName" align="center"
                                         :label="$t('customerMaterialConfig.dffFieldMaterialName')">
                            <template slot-scope="scope">
                                <el-input
                                        :placeholder="$t('customerMaterialConfig.dffFieldMaterialName')"
                                        v-show="show"
                                        maxlength="50"
                                        v-model="scope.row.dffFieldMaterialName">
                                </el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isMaterial" width="120" align="center"
                                         :label="$t('customerMaterialConfig.isMaterial')">
                            <template slot-scope="scope">
                                <el-switch

                                        v-model="scope.row.isMaterial"
                                        active-color="#ff6600"
                                        inactive-color="#ff4949"
                                        active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isMaster" width="120" align="center"
                                         :label="$t('customerMaterialConfig.isMaster')">
                            <template slot-scope="scope">
                                <el-switch
                                        v-if="scope.row.isMaterial === 1"
                                        v-model="scope.row.isMaster"
                                        active-color="#ff6600"
                                        inactive-color="#ff4949"
                                        active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isSearch" width="120" align="center"
                                         :label="$t('customerMaterialConfig.isSearch')">
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.isSearch"
                                           v-if="scope.row.isMaterial === 1"
                                           active-color="#ff6600"
                                           inactive-color="#ff4949"
                                           active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isUnique" width="120" align="center"
                                         :label="$t('customerMaterialConfig.isUnique')">
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.isUnique"
                                           v-if="scope.row.isMaterial === 1"
                                           active-color="#ff6600"
                                           inactive-color="#ff4949"
                                           active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isMr" width="120" align="center"
                                         :label="$t('customerMaterialConfig.isMr')">
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.isMr"
                                           v-if="scope.row.isMaterial === 1"
                                           active-color="#ff6600"
                                           inactive-color="#ff4949"
                                           active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                                </el-switch>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--<el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="dffPage"
                            :page-sizes="[5,10, 20, 50, 100]"
                            :page-size="dffSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="this.tableDataOfDff.length">
                    </el-pagination>-->
                </el-row>
            </el-form>
            <div class="sgs-bottom">
                <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button type="primary" @click="submitForm('templateform')" :loading="btnGuestbookSubmit">
                    {{$t('operation.submit')}}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {
        queryDffDisplayData,
        changeStatus,
        add,
        getPage,
        detail,
        remove,
        getListDff
    } from "@/api/martin/customerMaterialConfig";
    import {queryTrimsProductCategory} from "@/api/template/template";
    import {
        getDictByCode,
        getCustomerGroup,
        getProductLine,
        getDffList,
        getSpecificByProductLineId
    } from "@/api/common/index";
    import moment from 'moment'
    import {mapGetters} from "vuex";
    import Sortable from 'sortablejs'

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        name: "customerMaterialConfig",
        data() {
            return {
                loading: false,
                col: [
                    /*{
                      label: this.$t('customerMaterialConfig.dffFormType'),
                      prop: 'dffFormType',
                      type: 'span'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.dffFieldName'),
                      prop: 'dffFieldName',
                      type: 'span',
                      isColor: 'red'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.dffFieldMaterialName'),
                      prop: 'dffFieldMaterialName',
                      type: 'input'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.isMaterial'),
                      prop: 'isMaterial',
                      type: 'switch'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.isMaster'),
                      prop: 'isMaster',
                      type: 'switch'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.isSearch'),
                      prop: 'isSearch',
                      type: 'switch'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.isUnique'),
                      prop: 'isUnique',
                      type: 'switch'
                    },*/
                    /*{
                      label: this.$t('customerMaterialConfig.isMr'),
                      prop: 'isMr',
                      type: 'switch'
                    },*/
                ],
                dropCol: [
                    {
                        label: '日期',
                        prop: 'dffFieldName'
                    },
                    {
                        label: '姓名',
                        prop: 'dffFormType'
                    },
                    {
                        label: '地址',
                        prop: 'address'
                    }
                ],
                name: "customerMaterialConfig",
                dialogFormVisible: false,
                btnGuestbookSubmit: false,
                title: '',
                testPackageCustomerGroupData: [],
                testPackageFlag: false,
                productCategoryObj: '',
                dffObj: '',
                dffName: '',
                templateId: '',
                isDisabled: false,
                insertTestPackagedialogVisible: false,
                testPackageData: [],
                specificData: [],
                productLineSpecificFlag: false,
                tableData: [],
                tableDataOfDff1: [],
                tableDataOfDff2: [],
                tableDataOfDff: [],
                form: {},
                query: {
                    id: '',
                    customerGroupCode: '',
                    customerGroupName: '',
                    productLineSpecificId: '',
                    productLineId: '',
                    productLineCode: '',
                    productLineName: '',
                    dffFormId: null,
                    dffGridId: '',
                    dffName: '',
                    sortIndex: '',
                    productCategoryId: "",
                    productCategoryName: '',
                    templateName: '',
                    testPackageId: '',
                    testPackageName: '',
                    labId: '',
                    labCode: '',
                    labName: '',
                    labContacts: [],
                    defaultContactIds: [],

                },
                dffPage: 1,
                dffSize: 5,
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                show: true,
                productLineData: [],
                customerGroupData: [],
                productCategoryData: [],
                dffData: [],
                editMateral: false,
                productLineSpecific: {
                    productLineCode: '',
                },
                templateform: {
                    id: '',
                    customerGroupCode: '',
                    customerGroupName: '',
                    productLineSpecificId: '',
                    productLineId: '',
                    productLineCode: '',
                    productLineName: '',
                    dffFormId: null,
                    dffGridId: '',
                    dffName: '',
                    sortIndex: '',
                    productCategoryId: '',
                    productCategoryName: '',
                    templateName: '',
                    testPackageId: '',
                    testPackageName: '',
                    labId: '',
                    labCode: '',
                    labName: '',
                    labContacts: [],
                    defaultContactIds: [],
                    materialListName:''

                },
                queryDffDataParam: {
                    selectedFormId: '',
                    customerGroupID: '',
                    buCode: '',
                    systemCode: "PreOrder",
                    //moduleCode: "TRF-Product"
                },
                queryDffDataParamForQuery: {
                    selectedFormId: '',
                    customerGroupID: '',
                    buCode: '',
                    systemCode: "PreOrder",
                    //moduleCode: "TRF-Product"
                },
                productCategoryParam: {},
                dffDataForQuery: [],

                rules: {
                    productLineCode: [{required: true, message: 'Please enter', trigger: 'blur'}],
                    materialListName: [{required: true, message: 'Please enter', trigger: 'blur'}],
                    customerGroupCode: [{required: true, message: 'Please enter', trigger: 'blur'}],
                    dffFormId: [{required: true, message: 'Please enter', trigger: 'blur'}],
                },
            }
        },
        watch: {
            'templateForm.productLineCode': function (val) {//产品线重新选择的话则清空下面关联数据
                this.templateform.testPackageId = '';
            },
            'templateForm.customerGroupCode': function (val) {//客户组重新选择的话则清空下面关联数据
                this.templateform.testPackageId = '';
            },
            'query.productLineCode': function (val) {//产品线重新选择的话则清空下面关联数据
                this.query.testPackageId = '';
            },
            'query.customerGroupCode': function (val) {//客户组重新选择的话则清空下面关联数据
                this.query.testPackageId = '';
            }

        },
        mounted() {
            /* this.rowDrop();*/
            /*this.columnDrop()*/
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:material:add'], false),
                    editBtn: this.vaildData(this.permission['sgs:customer:material:edit'], false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:material:delete'], false),
                    listBtn: this.vaildData(this.permission['sgs:customer:material:list'], false),
                    reportBtn: this.vaildData(this.permission['sgs:customer:material:report'], false),
                };
            }
        },
        methods: {
            onSearch() {
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                this.loading = true;
                getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.loading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                }).catch(() => {
                    this.loading = false;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            rowDrop() {
                const tbody = document.querySelector('.classOfDff tbody')
                const _this = this
                Sortable.create(tbody, {
                    onEnd({newIndex, oldIndex}) {
                        const currRow = _this.tableDataOfDff.splice(oldIndex, 1)[0]
                        _this.tableDataOfDff.splice(newIndex, 0, currRow)
                    }
                })
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.btnGuestbookSubmit = true;
                        debugger
                        let isGo = true

                        let nameList = [];
                        this.tableDataOfDff.forEach(item => {
                            if (item.isMaterial === 1) {
                                nameList.push(item.dffFieldMaterialName)
                            }
                        })

                        var s = "," + nameList.join(",") + ",";
                        for (var i = 0; i < nameList.length; i++) {
                            if (s.replace("," + nameList[i] + ",", ",").indexOf("," + nameList[i] + ",") > -1) {
                                console.log(("数组中有重复元素：" + nameList[i]));
                                ;
                                isGo = false
                            }
                        }
                        if (isGo) {
                            this.tableDataOfDff.forEach(item => {
                                item.productLineCode = this.templateform.productLineCode;
                                item.productLineName = this.templateform.productLineName;
                                item.customerGroupName = this.templateform.customerGroupName;
                                item.customerGroupCode = this.templateform.customerGroupCode;
                                item.productCategoryName = this.templateform.productCategoryName;
                                item.productCategoryId = this.templateform.productCategoryId;
                                item.dffFormName = this.templateform.dffName;
                                item.materialListName = this.templateform.materialListName;
                                if (item.isChange !== 1) {
                                    item.isChange = 0;
                                }
                            });
                            add(this.tableDataOfDff).then(res => {debugger;
                                /*if (res.data.data == 'Double') {
                                  this.$message({
                                    type: "info",
                                    message: this.$t('reviewConclusion.RepeatToAdd')
                                });
                                } else {*/
                                this.$message({
                                    type: "success",
                                    message: this.$t('api.success')
                                });
                                this.btnGuestbookSubmit = false;
                                this.dialogFormVisible = false;
                                this.onLoad(this.page);
                                this.btnGuestbookSubmit = false;
                            }).catch(fail=>{debugger;
                              console.log(fail)
                              /*this.$message({
                                type: "info",
                                message: fail
                              });*/
                              this.btnGuestbookSubmit = false;
                            });
                        } else {
                            this.$message({
                                type: "info",
                                message: this.$t('customerMaterialConfig.dffFieldMaterialNameRepeatToAdd')
                            });
                            this.btnGuestbookSubmit = false;
                        }

                    } else {
                        return false;
                    }
                });
            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    remove(row.materialConfigId).then(res => {
                        debugger
                        if (res.data.data === true) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                        } else {
                            this.$message.error(this.$t('customerMaterialConfig.noDelete'));
                        }
                    });
                })
            },
            detailRow(row) {
                this.toDetailRow(row);
                setTimeout(() => {
                    this.rowDrop();
                }, 3000);
            },
            toDetailRow(row) {
                this.editMateral = true;
                console.log(row);
                this.title = this.$t('customerMaterialConfig.title.edit');
                let that = this;
                let params = {"materialConfigId": row.materialConfigId};
                detail(params).then(res => {
                    //获取后台数据付给页面，并打开
                    that.dialogFormVisible = true;
                    that.tableDataOfDff = res.data.data
                    that.templateform.productLineCode = res.data.data[0].productLineCode;
                    that.templateform.productLineName = res.data.data[0].productLineName;
                    that.templateform.customerGroupName = res.data.data[0].customerGroupName;
                    that.templateform.customerGroupCode = res.data.data[0].customerGroupCode;
                    that.templateform.productCategoryName = res.data.data[0].productCategoryName;
                    that.templateform.productCategoryId = res.data.data[0].productCategoryId;
                    that.templateform.productCategory = res.data.data[0].productCategoryName;
                    that.templateform.dffFormName = res.data.data[0].dffFormName;
                    that.templateform.dffFormId = res.data.data[0].dffFormId;
                    that.templateform.materialListName = res.data.data[0].materialListName;
                    // that.queryDffData();
                    getCustomerGroup().then(res => {
                        const data = res.data.data;
                        this.customerGroupData = data;
                        let obj = {};
                        obj = this.customerGroupData.find((item) => {
                            return item.customerGroupCode === res.data.data[0].customerGroupCode;
                        });
                        //查询dff数据
                        this.queryDffDataParam.customerGroupID = obj.customerGroupId;
                        this.queryDffDataParam.buCode = res.data.data[0].productLineCode;
                        this.queryDffDataParam.selectedFormId = that.templateform.dffFormId;
                        getDffList(this.queryDffDataParam).then(res => {
                            const data = res.data.data;
                            this.dffData = data;
                        })
                    });

                    let a = 0;
                    //that.rowDrop()
                    setTimeout(() => {
                        that.rowDrop();
                    }, 3000);
                });
            },
            addRow(row) {
                this.toAddRow(row);
                /*setTimeout(() => {
                  this.rowDrop();
                }, 3000);*/
            },
            toAddRow() {
                this.form = {};
                this.form = {};
                this.templateform = {};
                this.editMateral = false;
                this.tableDataOfDff = []
                this.title = this.$t('customerMaterialConfig.title.add');
                this.dialogFormVisible = true;
            },
            formtterDate(row, column) {
                var date = row[column.property];

                if (date == undefined || date == '') {
                    return ''
                }
                ;

                return moment(date).format("YYYY-MM-DD")
            },
            changeStatus(row) {
                const modifiedForm = {
                    status: row.status,
                    materialConfigId: row.materialConfigId
                };
                changeStatus(modifiedForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            selecProductCategoryChange(val) {
                let obj = {};
                obj = this.productCategoryData.find((item) => {
                    return item.productCategoryId === val;
                });
                let getName = ''
                getName = obj.productCategoryName;
                this.templateform.productCategoryName = getName;
                this.templateform.productCategoryId = val;
            },
            selecProductCategoryChangeForQuery(val) {
                debugger
                let obj = {};
                obj = this.productCategoryData.find((item) => {
                    return item.productCategoryId === val;
                });
                let getName = ''
                getName = obj.productCategoryName;
                this.query.productCategoryName = getName;
            },
            selecProductLineChange(value) {
                this.dffData = [];
                this.templateform.dffFormId = null;
                this.templateform.productCategoryId = '';
                this.templateform.testPackageId = '';
                this.templateform.testPackageName = '';
                let obj = {};
                obj = this.productLineData.find((item) => {
                    return item.productLineCode === value;
                });
                if (obj != undefined && obj != null) {
                    this.templateform.productLineId = obj.productLineID;
                    this.templateform.productLineName = obj.productLineName;
                    this.productCategoryParam.productLineId = parseInt(obj.productLineID);
                }

                this.templateform.productLineCode = value;

                this.specificData = [];
                this.templateform.productCategoryId = '';
                //根据产品线ID查询产品类别
                this.queryProductCategoryData();
                //查询产品线定制化标签
                this.queryProductLineSpecificData(value);
                //查询dff数据
                this.queryDffDataParam.buCode = value;
                if (this.templateform.customerGroupCode != '' && this.templateform.customerGroupCode != undefined) {
                    //查询DFF表格数据
                    this.queryDffData();
                }
            },
            queryProductLineSpecificData(value) {
                var params = {};
                this.productLineSpecific.productLineCode = value;
                getSpecificByProductLineId(Object.assign(params, this.productLineSpecific)).then(res => {
                    const data = res.data.data;
                    this.specificData = data;
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                })
            },
            selecProductLineChangeForQuery(value) {
                debugger
                this.dffDataForQuery = [];
                this.query.dffFormId = null;
                this.query.productCategoryId = '';
                this.query.testPackageId = '';
                this.query.testPackageName = '';
                let obj = {};
                obj = this.productLineData.find((item) => {
                    return item.productLineCode === value;
                });
                if (obj != undefined && obj != null) {
                    this.query.productLineId = obj.productLineID;
                    this.query.productLineName = obj.productLineName;
                    this.productCategoryParam.productLineId = parseInt(obj.productLineID);
                }

                this.query.productLineCode = value;

                this.specificData = [];
                this.query.productCategoryId = '';
                //根据产品线ID查询产品类别
                this.queryProductCategoryData();
                //查询产品线定制化标签
                this.queryProductLineSpecificData(value);
                //查询dff数据
                this.queryDffDataParamForQuery.buCode = value;
                if (this.query.customerGroupCode != '' && this.query.customerGroupCode != undefined) {
                    //查询DFF表格数据
                    this.queryDffDataForQuery();
                }
            },

            // 修改页大小
            handleSizeChange(val) {
                this.dffSize = val;
            },
            // 修改页码
            handleCurrentChange(val) {
                this.dffPage = val;
            },
            dffChangeForQuery(val) {
                let obj = {};
                obj = this.dffData.find((item) => {
                    return item.formId === val;
                });
                this.$set(this.query, "dffName", obj.name);
                this.$set(this.query, "dffGridId", obj.gridId);
                this.$set(this.query, "dffFormId", val);
            },

            dffChange(val) {
                this.tableDataOfDff = [];
                let obj = {};
                obj = this.dffData.find((item) => {
                    return item.formId === val;
                });
                this.templateform.dffName = obj.name;
                this.templateform.dffGridId = obj.gridId;
                this.templateform.dffFormId = val;

                let isGo = true;
                if (this.templateform.dffFormId !== '') {
                    let materialConfigId = '';
                    getListDff({"dffFormId": this.templateform.dffFormId}).then(res => {debugger;
                        let localDffData = res.data.data;
                        if (localDffData.length !== 0) {
                            materialConfigId = localDffData[0].materialConfigId;
                            this.$confirm('The data already exists. Are you sure to modify it?', '提示', {
                                customClass: 'message-logout',
                                confirmButtonText: 'Confirm',
                                cancelButtonText: 'Cancel',
                                type: 'warning'
                            }).then(() => {
                                queryDffDisplayData(this.templateform.dffFormId, 1).then(res => {
                                    debugger
                                    // 包含 1 的展示 displayInSystem
                                    let resNew = []
                                    res.data.data.forEach(item => {
                                        if (item.displayInSystem.indexOf("1") >= 0) {
                                            resNew.push(item)
                                        }
                                    })

                                    let datares = [];
                                    (resNew).forEach(item => {
                                        let dff = {};
                                        dff.dffFormType = 'Form'
                                        dff.fieldType = item.fieldType;
                                        dff.dffFieldName = item.dispalyName;
                                        dff.dffFieldMaterialName = item.dispalyName;
                                        dff.dffFormId = this.templateform.dffFormId;
                                        dff.dffFieldCode = item.fieldCode;
                                        dff.materialConfigId = materialConfigId;
                                        datares.push(dff)
                                    })
                                    datares.forEach(item => {
                                        this.tableDataOfDff.push(item)
                                    });
                                    queryDffDisplayData(this.templateform.dffGridId, 1).then(res => {

                                        // 包含 1 的展示 displayInSystem
                                        let resNew2 = []
                                        res.data.data.forEach(item => {
                                            if (item.displayInSystem.indexOf("1") >= 0) {
                                                resNew2.push(item)
                                            }
                                        })

                                        let datares = [];
                                        resNew2.forEach(item => {
                                            let dff = {};
                                            dff.dffFormType = 'Grid'
                                            dff.fieldType = item.fieldType;
                                            dff.dffFieldName = item.dispalyName;
                                            dff.dffFieldMaterialName = item.dispalyName;
                                            dff.dffFormId = this.templateform.dffGridId;
                                            dff.dffFieldCode = item.fieldCode;
                                            dff.materialConfigId = materialConfigId;
                                            datares.push(dff)
                                        })
                                        datares.forEach(item => {
                                            this.tableDataOfDff.push(item)
                                        });
                                        console.log(this.templateform.dffFormId);
                                        getListDff({"dffFormId": this.templateform.dffFormId}).then(res => {
                                            let localDffData = res.data.data;
                                            let that = this
                                            localDffData.forEach(localDffItem => {
                                                that.tableDataOfDff.forEach(item => {
                                                    if (localDffItem.dffFieldCode === item.dffFieldCode && localDffItem.dffFormId === item.dffFormId) {
                                                        // 接口 和 本地都有
                                                        item.isChange = 0;
                                                        item.dffFieldMaterialName = localDffItem.dffFieldMaterialName;
                                                        item.isMaterial = localDffItem.isMaterial;
                                                        item.isMaster = localDffItem.isMaster;
                                                        item.isSearch = localDffItem.isSearch;
                                                        item.isUnique = localDffItem.isUnique;
                                                        item.isMr = localDffItem.isMr;
                                                        localDffItem.isChange = 0;
                                                    }
                                                })
                                            });
                                            localDffData.forEach(localDffItem => {
                                                if (localDffItem.isChange !== 0) {
                                                    localDffItem.isChange = 1;
                                                    that.tableDataOfDff.unshift(localDffItem);
                                                }
                                            })
                                            this.tableDataOfDff.forEach(item => {
                                                if (item.isChange === 0) {
                                                    localDffData.forEach(localItem => {
                                                        if (localItem.dffFieldCode === item.dffFieldCode) {
                                                            item = localItem
                                                        }
                                                    })
                                                }
                                            })
                                        })
                                    })
                                })
                            }).catch(() => {
                                debugger
                                this.templateform.dffFormId = "";
                                this.templateform.dffName = "";
                                this.dffChange("")
                            });
                        } else {
                            queryDffDisplayData(this.templateform.dffFormId, 1).then(res => {
                                debugger
                                // 包含 1 的展示 displayInSystem
                                let resNew = []
                                res.data.data.forEach(item => {
                                    if (item.displayInSystem.indexOf("1") >= 0) {
                                        resNew.push(item)
                                    }
                                })

                                let datares = [];
                                (resNew).forEach(item => {
                                    let dff = {};
                                    dff.dffFormType = 'Form'
                                    dff.fieldType = item.fieldType;
                                    dff.dffFieldName = item.dispalyName;
                                    dff.dffFieldMaterialName = item.dispalyName;
                                    dff.dffFormId = this.templateform.dffFormId;
                                    dff.dffFieldCode = item.fieldCode;
                                    datares.push(dff)
                                })
                                datares.forEach(item => {
                                    this.tableDataOfDff.push(item)
                                });
                                queryDffDisplayData(this.templateform.dffGridId, 1).then(res => {

                                    // 包含 1 的展示 displayInSystem
                                    let resNew2 = []
                                    res.data.data.forEach(item => {
                                        if (item.displayInSystem.indexOf("1") >= 0) {
                                            resNew2.push(item)
                                        }
                                    })

                                    let datares = [];
                                    resNew2.forEach(item => {
                                        let dff = {};
                                        dff.dffFormType = 'Grid'
                                        dff.fieldType = item.fieldType;
                                        dff.dffFieldName = item.dispalyName;
                                        dff.dffFieldMaterialName = item.dispalyName;
                                        dff.dffFormId = this.templateform.dffGridId;
                                        dff.dffFieldCode = item.fieldCode;
                                        datares.push(dff)
                                    })
                                    datares.forEach(item => {
                                        this.tableDataOfDff.push(item)
                                    });
                                    console.log(this.templateform.dffFormId);
                                    getListDff({"dffFormId": this.templateform.dffFormId}).then(res => {
                                        let localDffData = res.data.data;
                                        let that = this
                                        localDffData.forEach(localDffItem => {
                                            that.tableDataOfDff.forEach(item => {
                                                if (localDffItem.dffFieldCode === item.dffFieldCode && localDffItem.dffFormId === item.dffFormId) {
                                                    // 接口 和 本地都有
                                                    item.isChange = 0;
                                                    item.dffFieldMaterialName = localDffItem.dffFieldMaterialName;
                                                    item.isMaterial = localDffItem.isMaterial;
                                                    item.isMaster = localDffItem.isMaster;
                                                    item.isSearch = localDffItem.isSearch;
                                                    item.isUnique = localDffItem.isUnique;
                                                    item.isMr = localDffItem.isMr;
                                                    localDffItem.isChange = 0;
                                                }
                                            })
                                        });
                                        localDffData.forEach(localDffItem => {
                                            if (localDffItem.isChange !== 0) {
                                                localDffItem.isChange = 1;
                                                that.tableDataOfDff.unshift(localDffItem);
                                            }
                                        })
                                        this.tableDataOfDff.forEach(item => {
                                            if (item.isChange === 0) {
                                                localDffData.forEach(localItem => {
                                                    if (localItem.dffFieldCode === item.dffFieldCode) {
                                                        item = localItem
                                                    }
                                                })
                                            }
                                        })
                                    })
                                })
                            })
                        }
                    })
                }
            },
            customerGroupCodeChange(val) {
                this.dffData = [];
                var dffSel = this.$refs.dffSel;
                this.templateform.dffFormId = null;
                this.templateform.testPackageId = '';
                this.templateform.testPackageName = '';
                let obj = {};
                obj = this.customerGroupData.find((item) => {
                    return item.customerGroupCode === val;
                });
                let newCustomerGroup = [];
                if (obj != undefined && obj != null) {
                    newCustomerGroup.push(obj);
                    this.testPackageCustomerGroupData = newCustomerGroup;
                    if (val != 'General') {
                        var generalObj = {};
                        generalObj.customerGroupName = 'General';
                        generalObj.customerGroupCode = 'General';
                        this.testPackageCustomerGroupData.push(generalObj);
                    }
                    this.templateform.customerGroupName = obj.customerGroupName;
                    this.queryDffDataParam.customerGroupID = obj.customerGroupId;
                }
                if (val == 'General') {
                    this.templateform.customerGroupName = val;
                }
                //查询DFF表格数据
                if (this.templateform.productLineCode != '' && this.templateform.productLineCode != undefined && val !== '') {
                    this.queryDffData();
                } /*else {
          this.$notify({
            title: this.$t('tip'),
            message: this.$t('template.selProductLine'),
            type: 'warning'
          });
        }*/

            },
            customerGroupCodeChangeForQuery(val) {
                this.dffDataForQuery = [];
                this.queryDffDataParamForQuery.customerGroupID = ''
                var dffSel = this.$refs.dffSel;
                this.query.dffFormId = null;
                this.query.testPackageId = '';
                this.query.testPackageName = '';
                let obj = {};
                obj = this.customerGroupData.find((item) => {
                    return item.customerGroupCode === val;
                });
                let newCustomerGroup = [];
                if (obj != undefined && obj != null) {
                    newCustomerGroup.push(obj);
                    this.testPackageCustomerGroupData = newCustomerGroup;
                    if (val != 'General') {
                        var generalObj = {};
                        generalObj.customerGroupName = 'General';
                        generalObj.customerGroupCode = 'General';
                        this.testPackageCustomerGroupData.push(generalObj);
                    }
                    this.query.customerGroupName = obj.customerGroupName;
                    debugger
                    this.queryDffDataParamForQuery.customerGroupID = obj.customerGroupId;
                }
                if (val == 'General') {
                    this.query.customerGroupName = val;
                }
                //查询DFF表格数据
                if (this.query.productLineCode != '' && this.query.productLineCode != undefined && val !== '') {
                    this.queryDffDataForQuery();
                } else {
                    /*this.$notify({
                      title: this.$t('tip'),
                      message: this.$t('template.selProductLine'),
                      type: 'warning'
                    });*/
                }

            },
            queryProductCategoryData() {
                var params = {};
                queryTrimsProductCategory(this.productCategoryParam.productLineId).then(res => {
                    const data = res.data.result;
                    this.productCategoryData = data;
                });
            },
            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.customerGroupData = data;
                });
            },
            queryproductLineData() {
                getProductLine().then(res => {
                    const data = res.data.data;
                    this.productLineData = data;
                });
            },
            queryDffData() {
                console.log(this.queryDffDataParam);
                var params = {};
                getDffList(this.queryDffDataParam).then(res => {
                    const data = res.data.data;
                    this.dffData = data;
                })
            },
            queryDffDataForQuery() {
                this.dffDataForQuery = [];
                console.log(this.queryDffDataParamForQuery);
                var params = {};
                getDffList(this.queryDffDataParamForQuery).then(res => {
                    const data = res.data.data;
                    this.dffDataForQuery = data;
                })
            },
            columnDrop() {
                const wrapperTr = document.querySelector('.el-table__header-wrapper tr')
                this.sortable = Sortable.create(wrapperTr, {
                    animation: 180,
                    delay: 0,
                    onEnd: evt => {
                        const oldItem = this.dropCol[evt.oldIndex]
                        this.dropCol.splice(evt.oldIndex, 1)
                        this.dropCol.splice(evt.newIndex, 0, oldItem)
                    }
                })
            },
            toMaterialListManager(materialConfigId) {
                this.$router.push({path: '/customer/material/list?materialConfigId=' + materialConfigId})
            },
            toMaterialReport(materialConfigId) {
                this.$router.push({path: '/customer/material/materialReport?materialConfigId=' + materialConfigId})
            },
        },
        created() {
            this.onLoad(this.page);
            this.queryproductLineData();
            this.queryCustomerGroupData();
        }

    }
</script>

<style lang="scss" scoped>
.maaterial-box {
    /* padding-top: 24px; */
    /deep/ .el-form {
        .el-input__inner {
            background: transparent !important;
        }
    }
}
</style>
