<template>
    <span
      class="custom-tag"
      :style="{
        color: color,
        borderColor: color,
      }"
    >
      <slot></slot>
    </span>
  </template>
  
  <script>
  export default {
    name: "Tag",
    props: {
      color: {
        type: String,
        default: "#909399",
      },
    },
  };
  </script>
  
  <style scoped>
  .custom-tag {
    height: 21px;
    line-height: 21px;
    display: inline-block;
    padding: 0 8px;
    font-weight: bold;
    font-size: 12px;
  }
  </style>