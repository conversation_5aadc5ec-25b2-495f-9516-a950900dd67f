<template>
    <basic-container v-loading="tableLoading">
        <div class="list_header">
            <div class="list-header-title">Material List</div>

        </div>
        <div class="sgs_smart_material_list" id="sgs_smart_material_list">
            <div class="serach_block">
                <!-- <el-button size="small" type="" v-loading="exportLoading" class="export_btn" @click="exportMaterials">
                  Export
                  <i class="el-icon-download el-icon--right"></i>
                </el-button> -->
                <div class="header_opreate">
                    <el-dropdown>
                        <el-button type="primary" size="medium">
                            Create Material
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native="showDownloadTemplate">New Material</el-dropdown-item>
                            <el-dropdown-item @click.native="showImportTemplate">Import Materials</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-dropdown>
                        <el-button type="primary" size="medium">
                            Create TRF
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native="createTrf('individualSample')">Individual Creation
                            </el-dropdown-item>
                            <el-dropdown-item @click.native="createTrf('groupSample')">Group Creation</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
                <div class="serach_block_group">
                    <filter-view source-type="MATERIAL" @changeFilter="changeView"
                                 @resetFilter="resetForm"></filter-view>
                    <reset-button @click="resetForm"></reset-button>
                    <el-button size="small" icon="el-icon-download" v-loading="exportLoading" @click="exportMaterials">
                        Export
                    </el-button>
                </div>
            </div>
            <div class="table-scroll" :style="{'height': tableMinHeight + 'px'}">
                <el-table ref="materialTable" :data="dataList" :max-height="tableMaxHeight"
                          class="sgs_table_material_table table-header-no-filter" fit
                          border resizable size="small">
                    <el-table-column type="selection" fixed="left" :selectable="disabledRow"></el-table-column>
                    <el-table-column v-for="item in tableHeaderOptions"
                                     :label="item.displayName"
                                     :prop="item.fieldCode"
                                     min-width="220px">
                        <template slot="header" slot-scope="scope">
                            <table-filter :title="item.displayName">
                                <el-input v-if="item.fieldType === 'input'"
                                          :placeholder="item.displayName"
                                          clearable
                                          size="mini"
                                          v-model="searchForm[item.fieldCode]">
                                    <template slot="append">
                                        <i class="el-icon-search filter-search-icon"></i>
                                    </template>
                                </el-input>
                                <el-select v-if="item.fieldType === 'select'" style="width: 100%" size="mini"
                                           v-model="searchForm[item.fieldCode]" filterable clearable
                                           :placeholder="item.displayName">
                                    <el-option v-for="(st, index) in item.sourceValueList" :key="'mt_' + index"
                                               :label="st.name" :value="st.code"></el-option>
                                </el-select>
                                <el-select v-if="item.fieldType === 'select2'" style="width: 100%" size="mini"
                                           v-model="searchForm[item.fieldCode]" filterable clearable multiple
                                           :placeholder="item.displayName">
                                    <el-option v-for="(st, index) in item.sourceValueList" :key="'mt_' + index"
                                               :label="st.name" :value="st.code"></el-option>
                                </el-select>
                                <el-select v-if="item.fieldCode === 'buyerCustomerGroupName'" style="width: 100%"
                                           v-model="searchForm.sampleStatus" size="mini" filterable clearable
                                           multiple placeholder="Approval Status">
                                    <el-option v-for="(st, index) in filterList.sampleStatus" :key="'as_' + index"
                                               :label="st.label"
                                               :value="st.value"></el-option>
                                </el-select>
                                <el-select v-if="item.fieldCode === 'supplierName'" style="width: 100%"
                                           v-model="searchForm.supplierName" size="mini" filterable clearable
                                           placeholder="Material Supplier">
                                    <el-option v-for="(ms, index) of filterList.supplierList" :key="'ms_' + index"
                                               :label="ms.scmCustomerName" :value="ms.scmCustomerNo"></el-option>
                                </el-select>
                                <!-- <el-select v-if="item.fieldCode === 'sampleType'" style="width: 100%" size="mini" v-model="searchForm.sampleType" filterable clearable
                                  placeholder="Material Type">
                                  <el-option v-for="(st, index) in filterList.sampleType" :key="'mt_' + index" :label="st.label"
                                    :value="st.value"></el-option>
                                </el-select> -->
                            </table-filter>
                        </template>
                        <template slot-scope="{ row }">
                            <a @click="toDetail('detail', row)"
                               style="color: #ff6600; font-size: 14px; cursor: pointer"
                               v-if="item.fieldCode === 'sampleNo'">
                                {{ row.sampleNo }}
                            </a>
                            <template v-else-if="item.fieldCode==='sampleType'">
                                {{row.sampleTypeName}}
                            </template>
                            <template v-else-if="item.fieldCode === 'relationships'">
                            <span v-if="row.relationships &&  row.relationships.length==1"
                              @click="toTrfDetail(row.relationships[0])"
                              style="color: #ff6600; font-size: 14px; cursor: pointer"> {{ row.relationships[0].relNo }}</span>
                                <el-popover
                                        v-if="row.relationships &&  row.relationships.length>1"
                                        placement="top-start"
                                        title="TRF No"
                                        width="150"
                                        trigger="hover"
                                        content="">
                                    <ul style="width: 100%; list-style: none;padding:0;margin:0;">
                                        <li v-for="(rel,reIndex) in row.relationships"
                                            :key="'rel_'+reIndex"
                                            style="padding: 5px"
                                        >
                              <span @click="toTrfDetail(rel)"
                                    style="color: #ff6600; font-size: 14px; cursor: pointer">
                                  {{ rel.relNo }}</span>
                                        </li>
                                    </ul>
                                    <span slot="reference" style="cursor: pointer">
                          <span @click="toTrfDetail(row.relationships[0])"
                                style="color: #ff6600; font-size: 14px; cursor: pointer">
                                      {{ row.relationships[0].relNo }}</span>
                      </span>
                                </el-popover>
                            </template>
                            <template v-else-if="item.fieldCode === 'sampleStatus'">
                                <tag :color="setStatusColor(row.sampleStatus)">
                                    {{
                                        (
                                                filterList.sampleStatus.find(
                                                        (m) => m.value == row.sampleStatus
                                                ) || {label: ""}
                                        ).label
                                    }}
                                </tag>
                            </template>
                            <template v-else-if="item.fieldCode === 'trfNo'">
                <span v-if="row.trfNo &&  row.trfNo.length==1"
                      @click="toTrfDetail(row.trfNo[0])"
                      style="color: #ff6600; font-size: 14px; cursor: pointer">
                    {{ row.trfNo[0].relNo }}</span>
                                <el-popover
                                        v-if="row.trfNo &&  row.trfNo.length>1"
                                        placement="top-start"
                                        title="TRF No"
                                        width="150"
                                        trigger="hover"
                                        content="">
                                    <ul style="width: 100%; list-style: none;padding:0;margin:0;">
                                        <li v-for="(rel,reIndex) in row.trfNo"
                                            :key="'rel_'+reIndex"
                                            style="padding: 5px"
                                        >
                            <span @click="toTrfDetail(rel)"
                                  style="color: #ff6600; font-size: 14px; cursor: pointer">
                                {{ rel.relNo }}</span>
                                        </li>
                                    </ul>
                                    <span slot="reference" style="cursor: pointer">
                        <span @click="toTrfDetail(row.trfNo[0])"
                              style="color: #ff6600; font-size: 14px; cursor: pointer">
                                    {{ row.trfNo[0].relNo }}</span>
                    </span>
                                </el-popover>
                            </template>
                            <template v-else>
                                <template v-if="item.configType === 'dff'">
                                    {{ getMaterialExtendFieldValue(row, item.fieldCode) }}
                                </template>
                                <template>
                                    {{ row[item.fieldCode] }}
                                </template>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" prop="id" label="Action" width="150px" align="center">
                        <template slot-scope="{ row }">
                            <el-tooltip content="Edit" placement="top" v-if="btnRole(row, 'Edit')">
                                <i class="el-icon-edit menu-icon" @click="toDetail('detail', row)"></i>
                            </el-tooltip>
                            <el-tooltip content="Copy" placement="top">
                                <i class="el-icon-copy-document menu-icon" @click="toDetail('copy', row)"></i>
                            </el-tooltip>
                            <el-tooltip content="Cancel" placement="top">
                                <el-popconfirm v-if="btnRole(row, 'Cancel')" confirm-button-text="Confirm"
                                               cancel-button-text="Cancel"
                                               icon="el-icon-info" icon-color="red" title="Cancel the data?"
                                               @confirm="cancelMaterial(row)">
                                    <i slot="reference" class="el-icon-delete delete-icon"></i>
                                </el-popconfirm>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-pagination @size-change="sizeChange" @current-change="currentChange" :current-page.sync="page.page"
                           :page-sizes="page.sizes" :page-size.sync="page.rows" :layout="page.layout"
                           :total="page.total">
            </el-pagination>
            <!-- download template-->
            <el-dialog :visible.sync="downloadTemplateDia" append-to-body :close-on-click-modal="false"
                       :close-on-press-escape="false" :show-close="false" title="Material Template">
                <download-template v-if="downloadTemplateDia" :supplier-list="filterList.supplierList"
                                   :template-show-download="templateShowDownload"
                                   :customer-list="filterList.customerList"
                                   :product-line-list="filterList.productLineList" :form-purpose="'Material'"
                                   @cancelDia="closeDia('downloadTemplateDia')">
                    <template slot="downloadTemplateSlot" v-if="templateShowDownload">
                        <el-button type="primary" size="small" @click="showImportMaterials">Import Materials</el-button>
                    </template>
                </download-template>
            </el-dialog>

            <sgs-batch-upload v-if="isLoadUpload" title="Import Material" append-to-body :systemID="1" :limit="1"
                              :handle-upload-success="uploadSuccess" :handle-upload-error="uploadError"
                              ref="batchUpload" accept=".xls,.xlsx"
                              upload-url="/api/sgs-pbm/sample/web/v1/upload?dataType=material"
                              :attachment-type-options="[]"
                              attachment-type-default-value="" :file-max-sizes="20">
            </sgs-batch-upload>
        </div>
    </basic-container>
</template>

<script>
import api from "../../../api/newSamples";
import {queryScmCustomer} from "@/api/customer/scmCustomer";
import {getProductLine} from "@/api/common/index";
import DownloadTemplate from "../materialAndProductCommon/downloadTemplate";
import filterView from "../materialAndProductCommon/filterView";
import {mapGetters} from "vuex";
import {validatenull} from "@/util/validate";
import {getStatusColor} from '@/util/status';
import tableFilter from "../../../components/tableFilter/tableFilter.vue";
import tag from "@/components/tag/tag.vue";
import resetButton from "@/components/resetButton/resetButton.vue";

import {debounce} from "lodash";

let vm;
export default {
    name: "MaterialList",
    provide() {
        return {
            loadSearchForm: () => {
                return this.searchForm;
            },
        };
    },
    data() {
        return {
            isLoadUpload: false,
            showDetailDia: false,
            dataList: [],
            categoryBuList: {},
            tableLoading: false,
            templateShowDownload: false,
            downloadTemplateDia: false,
            exportLoading: false,
            viewValue: "",
            searchForm: {
                sampleNo: "",
                sampleCode: "",
                sampleType: "",
                templateName: "",
                sampleStatus: [],
                sampleName: "",
                supplierName: "",
                trfNo: '',
            },
            filterList: {
                buyerList: [],
                supplierList: [],
                customerList: [],
                productLineList: [],
                sampleType: [
                    {label: "Textiles", value: "textiles"},
                    {label: "Knit Wear", value: "knitWear"},
                    {label: "Woven", value: "woven"},
                ],
                sampleStatus: [
                    {label: "In-Progress", value: "1"},
                    {label: "Submitted", value: "2"},
                    {label: "Approved", value: "3"},
                    {label: "Cancelled", value: "4"},
                    {label: "Not In Use", value: "5"},
                ],
            },
            page: {
                //分页对象
                show: true,
                page: 1,
                rows: 20,
                small: true,
                sizes: [10, 20, 50, 100],
                layout: "total, sizes, prev, pager, next,jumper",
                total: 0,
            },
            tableMaxHeight: 0, // 根据试图动态设置表格高度
            tableMinHeight: 500, // 表格最小高度
            tableHeaderOptions: [], // 表格表头配置
            allBuSampleList: [], // 表格表头sampleType对应下拉框
        };
    },
    methods: {
        changeView(filterForm) {
            let defaultSearchForm = {
                sampleNo: "",
                sampleCode: "",
                sampleType: "",
                templateName: "",
                sampleStatus: [],
                sampleName: "",
                supplierName: "",
            };
            let newFilter = Object.assign({}, defaultSearchForm, filterForm);
            this.$set(this, "searchForm", newFilter);
        },
        deboundceSearch: debounce(function (val) {
            vm.initTable();
        }, 500),
        closeDia(flag) {
            this[flag] = false;
        },
        // 切换当前页
        currentChange(currentPage) {
            //this.page.page = currentPage;
            this.initTable();
        },
        // 调整每页显示行数
        sizeChange(pageSize) {
            //this.page.rows = pageSize;
            this.initTable();
        },
        disabledRow(row, index) {
            let {permissions} = row;
            return (permissions || []).map((p) => p.action).includes("toTrf");
        },
        resetForm() {
            this.searchForm = {
                sampleNo: "",
                sampleCode: "",
                sampleType: "",
                templateName: "",
                sampleStatus: [],
                sampleName: "",
                supplierName: "",
            };
            //this.initTable();
        },
        async initTemplateCustomerListForSupplier() {
            //再查询所有的模板 + 自身关联的buyer - 自身的模板
            let buyerList = await this.initClientRelationCustomerList("buyer");
            let buyerGroupCode = (buyerList || [])
                .filter((b) => b.scmCustomerGroupCode)
                .map((b) => b.scmCustomerGroupCode);

            let currentUser = this.userInfo.userName;
            let param = {
                currentUser, //必传
                formPurpose: "Material", //必传
            };
            //查询自身的模板
            api.queryProductViewCustomerList(param).then((res) => {
                let supplierCustomerList = [];
                if (res.status == 200 && res.data && res.data.status == 200) {
                    supplierCustomerList = res.data.data || [];
                }
                api
                    .queryProductViewCustomerList({formPurpose: "Material"})
                    .then((res) => {
                        if (res.status == 200 && res.data && res.data.status == 200) {
                            let customerList = res.data.data || [];
                            if (this.role.isSupplier) {
                                customerList = customerList.filter((c) =>
                                    buyerGroupCode.includes(c.customerGroupCode)
                                );
                            }
                            customerList = [
                                ...new Set([...customerList, ...supplierCustomerList]),
                            ];
                            this.resetCustomerList(customerList);
                        }
                    });
            });
        },
        initTemplateCustomerList() {
            let currentUser = this.role.isSGS ? "" : this.userInfo.userName;
            let param = {
                currentUser, //必传
                formPurpose: "Material", //必传
            };
            api.queryProductViewCustomerList(param).then((res) => {
                if (res.status == 200 && res.data && res.data.status == 200) {
                    let customerList = res.data.data || [];
                    this.resetCustomerList(customerList);
                }
            });
        },
        resetCustomerList(customerList) {
            let newList = [];
            let cgs = [];
            customerList
                .filter((c) => c.customerGroupCode)
                .forEach((c) => {
                    let {customerGroupCode} = c;
                    if (!cgs.includes(customerGroupCode)) {
                        newList.push(c);
                        cgs.push(customerGroupCode);
                    }
                });
            this.filterList.customerList = newList;
        },
        async initClientRelationCustomerList(relationshipType) {
            return new Promise((resolve) => {
                const params = {};
                const list = [];
                const item = {};
                item.relationshipType = relationshipType;
                /*if(customerName != null){
                                item.customerName = customerName;
                            }*/
                params.page = 1;
                params.rows = 1000;
                item.buCode =
                    this.userInfo.productLineCode === "all"
                        ? this.userInfo.defaultProductLineCode
                        : this.userInfo.productLineCode;
                item.customerNo = this.userInfo.bossNo;
                list.push(item);
                params.list = list;
                queryScmCustomer(params)
                    .then(
                        (res) => {
                            if (res.status == 200 && res.data) {
                                resolve(res.data.rows);
                            } else {
                                resolve(false);
                            }
                        },
                        (error) => {
                            resolve(false);
                        }
                    )
                    .catch((err) => {
                        resolve(false);
                    });
            });
        },
        initCategoryType(cb) {
            let lan = this.language;
            this.allBuSampleList = [];
            api.queryCategoryType({paramCode: 'MaterialType'}).then(res => {
                if (res.status == 200 && res.data.data) {
                    this.categoryBuList = res.data.data || {};
                }
                if (cb) {
                    cb();
                }
            })
        },
        reloadCategorySelect() {
            if (!this.categoryBuList || Object.keys(this.categoryBuList) == 0) {
                return
            }

            let lan = this.language;
            this.allBuSampleList = [];
            let allBuKeys = Object.keys(this.categoryBuList);
            allBuKeys.forEach(bu => {
                let allLanTypeList = {}
                try {
                    allLanTypeList = JSON.parse(this.categoryBuList[bu]);
                } catch (e) {
                }
                let typeList = allLanTypeList[lan] || [];
                typeList.forEach(tl => {
                    let {label, code} = tl;
                    this.allBuSampleList.push({name: label, code});
                })
            })
            //重置sampleType的类型
            this.tableHeaderOptions.forEach(item => {
                let {fieldCode} = item;
                if (fieldCode == 'sampleType') {
                    this.$set(item, 'fieldType', 'select');
                    this.$set(item, 'solt', true);
                    this.$set(item, 'sourceValueList', this.allBuSampleList);
                }
            })
            this.dataList.forEach(da => {
                let {productLineCode, sampleType} = da;
                let lanTypeLis = {};
                try {
                    lanTypeLis = JSON.parse(this.categoryBuList[productLineCode]);
                } catch (e) {
                }
                let categoryList = lanTypeLis[lan];
                let sourceValue = (categoryList || []).find(f => f.code == sampleType) || {label: ''};
                da.sampleTypeName = sourceValue.label || sampleType;
            })

        },
        initProductLine() {
            getProductLine().then((res) => {
                if (res.status == 200 && res.data) {
                    const data = res.data.data || [];
                    let currentUserPL = (this.userInfo.productLineCodes || "").split(",");
                    if (
                        !this.userInfo.productLineCodes &&
                        this.userInfo.productLineCode == "all"
                    ) {
                        this.filterList.productLineList = data;
                        return;
                    }
                    this.filterList.productLineList = data.filter((da) =>
                        currentUserPL.includes(da.productLineCode)
                    );
                }
            });
        },
        initTable() {
            this.dataList = [];
            this.initTableHeader()
            //this.page.total = 0;
            let queryForm = Object.assign({}, this.searchForm, {
                current: this.page.page,
                size: this.page.rows,
            });
            queryForm["dataType"] = "material";
            queryForm["userProductLineCode"] = this.userInfo.productLineCode;
            this.tableLoading = true;
            api.querySamplesListPage(queryForm).then((res) => {
                    this.tableLoading = false;
                    if (res.status == 200 && res.data && res.data.data) {
                        let {records, total} = res.data.data;
                        this.dataList = records || [];
                        this.page.total = total;
                    } else {
                        this.page.total = 0;
                    }
                    this.calculateTableHeight();
                    this.reloadCategorySelect();
                },
                (error) => {
                    this.page.total = 0;
                    this.tableLoading = false;
                    this.calculateTableHeight();
                }
            )
                .catch((err) => {
                    this.page.total = 0;
                    this.tableLoading = false;
                    this.calculateTableHeight();
                });
        },
        // 获取table 头部配置项
        initTableHeader() {
            let lan = this.language;
            api.filedList({dataType: "material", userProductLineCode: this.userInfo.productLineCode}).then((res) => {
                if (res.status == 200 && res.data && res.data.data) {
                    if (this.tableHeaderOptions.length > 0) {
                        this.tableHeaderOptions.forEach((item) => {
                            let {fieldCode} = item;
                            let findItem = res.data.data.find((r) => r.fieldCode == fieldCode);
                            this.$set(item, "displayName", findItem.displayName);
                        })
                    } else {
                        this.tableHeaderOptions = res.data.data;
                    }
                }
            })
        },
        createTrf(action) {
            let {selection} = this.$refs.materialTable;
            if (!selection || selection.length == 0) {
                this.$message.error("Please select at least one piece of data");
                return;
            }
            if (action == "individualSample" && selection.length > 1) {
                this.$message.error("Only support select one piece of data");
                return;
            }

            if (selection.length > 5) {
                this.$message.error("Supports up to 5 pieces of data");
                return;
            }
            let objectIds = [...new Set(selection.map((row) => row.id))];
            let param = {
                action, objectIds
            };
            api
                .checkToTrf(param)
                .then((res) => {
                    if (res.status == 200 && res.data && res.data.status == 200) {
                        let testId = res.data.data;
                        let customerGroupCode = selection[0].buyerCustomerGroupCode;
                        let bossNo = selection[0].buyerCustomerBossNo;
                        let customer = {
                            bossNo,
                            customerGroupCode,
                        };
                        customer = encodeURIComponent(JSON.stringify(customer));
                        let bu = selection[0].productLineCode;
                        let routeUrl = this.$router.resolve({
                            path: "/trf/trfForm",
                            query: {
                                actionType: action,
                                flag: 1,
                                customer,
                                bu,
                                testId
                            },
                        });
                        window.open(routeUrl.href, "_blank");
                    }
                })
                .catch((err) => {
                    console.log("err", err);
                    //this.$notify.warning(err);
                });
        },
        cancelMaterial(row) {
            let {id} = row;
            api
                .actionSamples({ids: [id], action: "Cancel"})
                .then((res) => {
                    if (res.status == 200 && res.data) {
                        this.$notify.success("Success");
                        this.initTable();
                    } else {
                        this.$notify.error(res.message || "Delete Fail");
                    }
                })
                .catch((err) => {
                });
        },
        btnRole({permissions}, code) {
            return (permissions || []).map((p) => p.action).includes(code);
        },
        toTrfDetail(row) {
            let {relId, relNo, signature} = row;
            window.open('/#/trf/trfDetail?id=' + relId + '&title=' + relNo + '&trfNo=' + relNo + '&hash=' + new Date().getTime() + '' + '&actionType=detail' + '&signature=' + signature, '_blank');
        },
        toDetail(action, obj) {
            let query = {action};
            if (obj) {
                let {id} = obj;
                query["id"] = id;
            }
            let url = this.$router.resolve({
                path: "/customer/newMaterial/detail",
                query,
            });
            window.open(url.href, "_blank");
        },
        showDownloadTemplate() {
            this.templateShowDownload = false;
            this.downloadTemplateDia = true;
        },
        showImportTemplate() {
            this.templateShowDownload = true;
            this.downloadTemplateDia = true;
        },
        showImportMaterials() {
            this.isLoadUpload = false;
            this.$nextTick(() => {
                this.isLoadUpload = true;
                this.$nextTick(() => {
                    this.$refs.batchUpload.open();
                });
            });
        },
        exportMaterials() {
            this.searchForm.userProductLineCode = this.userInfo.productLineCode;
            let queryForm = Object.assign({}, this.searchForm);
            this.exportLoading = true;
            queryForm["dataType"] = "material";
            api.exportSampleData(
                queryForm,
                () => {
                    this.exportLoading = false;
                },
                (err) => {
                    this.exportLoading = false;
                    this.$notify.error(err || "Export fail");
                }
            );
        },
        uploadError() {
        },
        uploadSuccess(fileData) {
            this.isLoadUpload = false;
            this.downloadTemplateDia = false;
            this.initTable();
        },
        haseRole(type, role) {
            if (validatenull(type) || validatenull(role)) {
                return false;
            }
            if (validatenull(this.userInfo.dimensions)) {
                return false;
            } else {
                if (this.userInfo.dimensions.hasOwnProperty(type)) {
                    if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }
        },
        calculateTableHeight() {
            const headerHeight = document.querySelector('.list_header').offsetHeight;
            const searchBlockHeight = document.querySelector('.serach_block').offsetHeight;
            const paginationHeight = document.querySelector('.el-pagination').offsetHeight;
            const containerHeight = document.querySelector('.sgs_smart_material_list').offsetHeight;
            const vhHeight = window.innerHeight * 1 - 438;
            if (vhHeight < 500) {
                this.tableMinHeight = 500
            } else {
                this.tableMinHeight = vhHeight
            }
            this.tableMaxHeight = containerHeight - headerHeight - searchBlockHeight - paginationHeight; // 40 is for padding/margin
        },
        setStatusColor(status) {
            return getStatusColor(status);
        },
        getMaterialExtendFieldValue(row, fieldCode) {
            if (row.fieldList && row.fieldList.length > 0) {
                const foundField = row.fieldList.find(f => f.fieldCode === fieldCode);
                return foundField ? foundField.materialExtendFieldValue : '';
            }
            return '';
        },
    },
    mounted() {
        if (this.role.isSupplier) {
            this.initTemplateCustomerListForSupplier();
        } else {
            this.initTemplateCustomerList();
        }
        this.initProductLine();
        this.initCategoryType(() => {
            this.initTable();
        });
        window.addEventListener('resize', this.calculateTableHeight);
    },
    created() {
        vm = this;
    },
    watch: {
        searchForm: {
            immediate: false,
            deep: true,
            handler(newV) {
                this.page.page = 1;
                this.deboundceSearch();
            },
        },
        language(newVal, oldVal) {
            this.initTableHeader();
            this.reloadCategorySelect();
        }
    },
    computed: {
        ...mapGetters(["permission", "userInfo", "language"]),
        role() {
            return {
                isSGS:
                    this.haseRole("SGSUserRole", "SgsAdmin") ||
                    this.haseRole("SGSUserRole", "SgsLabUser"),
                isSupplier: this.haseRole("UserRole", "Supplier"),
                isThirdPartyLab: this.haseRole("UserRole", "ThirdPartyLab"),
            };
        },
    },
    props: {},
    updated() {
    },
    beforeDestory() {
        window.removeEventListener('resize', this.calculateTableHeight);
    },
    destoryed() {
    },
    components: {DownloadTemplate, filterView, tableFilter, tag, resetButton},
};
</script>

<style lang="scss">
@import "@/styles/unit.scss";

.sgs_smart_material_list {
  font-family: "Arial" !important;
  background: #fff;
  padding: $module-padding;

  .icon-base {
    font-size: 20px;
    cursor: pointer;
    margin: 0 10px;
  }

  .menu-icon {
    @extend .icon-base;
    color: $primary-color;
  }

  .delete-icon {
    @extend .icon-base;
    color: $icon-color-default;
  }

  .el-icon-arrow-down {
    font-size: 12px;
  }

  .title_justify {
    text-align: justify;
    display: flex;
    justify-content: space-between;
    padding: 0;
    align-items: center;
  }

  .serach_block {
    display: flex;
    justify-content: space-between;
    padding-bottom: $module-padding-vertical;

    .serach_block_group {
      display: flex;

      .reset_btn {
        margin-left: $module-padding-horizontal;
      }
    }
  }

  .table-scroll {
    // height: min(100vh - 438px, 500px);
  }

  .sgs_table_material_table {
    .el-table--mini .el-table__cell {
      padding: 0 !important;
    }

    label {
      margin-bottom: 4px;
    }
  }

  .TableHeaderFilterSave {
    .el-col {
      margin-bottom: 0;
    }
  }

  .el-table__cell {
    padding: 8px 0;
  }
}

.list_header {
  padding-bottom: 11px;

}

.header_opreate {
  display: flex;

  .el-dropdown {
    margin-right: $inline-element-spacing;
  }

  .export_btn {
    color: #ff6600;
    border-bottom: solid 1px #ff6600;
    padding-bottom: 5px;
    margin-right: $inline-element-spacing;
    cursor: pointer;
  }
}

.filter-search-icon {
  width: 18px;
  cursor: pointer;
  border: none;
  left: 0;
  top: 0;
}
</style>