<template>
    <div class="error-page">
        <div class="img"
             style=" background-image: url('/img/bg/paySuccess.svg');"></div>
        <div class="content">
            <h2>支付成功</h2>
            <div class="desc">系统将在<span style="color: #ff6600">{{counter}}</span>秒后自动跳转</div>
            <div class="actions">
                <router-link :to="{path:'/invoice/list'}">
                    <el-button type="primary">返回账单界面</el-button>
                </router-link>
            </div>
        </div>
    </div>
</template>

<script>
  const TIME_COUNT = 10; //更改倒计时时间
    export default {
        name: "payment-success",
        data() {
            return {
              counter: 10,
              timer: null,
            }
        },
        created() {
            //发送登录请求
            this.init();
        },
        methods: {
          init() {
                this.counter = TIME_COUNT;
                this.timer = setInterval(() => {
                    if (this.counter > 0 && this.counter <= TIME_COUNT) {
                        this.counter--;
                    } else {
                        clearInterval(this.timer);  // 清除定时器
                        this.timer = null;
                        this.$router.push({
                            path: '/invoice/list',
                        })
                    }
                }, 1000)
            },
        }
    };
</script>
<style lang="scss" scoped>
    @import "../error-page/style.scss";
</style>
