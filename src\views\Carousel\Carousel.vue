<template>
  <div class="carousel-container">

    <el-carousel :interval="null" arrow="always" indicator-position="none" height="500px"
      @change="handleCarouselChange">
      <el-carousel-item v-for="(item, index) in props.carouselList" :key="index">
        <img class="carousel-image" :src="item.url" />
      </el-carousel-item>
    </el-carousel>

    <div class="carousel-footer">
      <el-row :gutter="24" style="width: 100%; display: flex; flex-direction: row; justify-content: space-between;">
        <el-col :span="2">
          <div class="page-info">
            <span class="currentpage">{{ activeIndex + 1 }}</span>
            <div class="page-length">/ {{ props.carouselList.length }}</div>
          </div>
        </el-col>
        <el-col :span="1" class="divider-section">
          <div class="vertical-divider"></div>
        </el-col>
        <el-col :span="21">
          <div class="image-description">图片描述：{{ props.carouselList[activeIndex]?.description || "暂无描述" }}</div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>



<script setup lang="ts">
/* import { getActiveResourcesInfo } from 'process'; */
import { ref } from 'vue';

interface CarouselItem {
  url: string;
  description: string;
}

const props = defineProps<{
  carouselList: CarouselItem[];
}>();


const activeIndex = ref(0);

const handleCarouselChange = (index: number) => {
  activeIndex.value = index;
};
</script>





<style lang="scss" scoped>
:deep(.el-carousel__arrow) {
  position: absolute;
  top: 40%;
  width: 30px;
  /* 调整矩形按钮的宽度 */
  height: 20%;
  /* 调整矩形按钮的高度 */
  background-color: rgba(0, 0, 0, 0.4);
  /* 背景颜色 */
  border-radius: 5px;
  /* 让它更像矩形 */
}

:deep(.el-carousel__arrow i) {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.735);

}

:deep(.el-carousel__arrow--left) {
  left: 0;
  /* 让左箭头贴紧轮播图左侧 */
  transform: translateX(0);
  /* 取消默认偏移 */
}

:deep(.el-carousel__arrow--right) {
  right: 0;
  /* 让右箭头贴紧轮播图右侧 */
  transform: translateX(0);
  /* 取消默认偏移 */
}

.carousel-container {
  // position: relative;
  width: 100%;
  height: 100%;
  // max-width: 600px;
  //margin: auto;
}

.carousel-image {
  height: 100%;
  width: 100%;
  object-fit: contain;
  /* 保持图片原始比例 */
  margin: 0 0;

}

:deep(.el-carousel__item) {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0);
  /* 图片背景颜色 */
}

:deep(.el-carousel) {
  height: 500px !important;
  /* 固定轮播图高度 */
}

.carousel-footer {
  padding: 10px 15px;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.page-info {
  font-size: 18px;
  color: rgb(105, 105, 105);
  font-style: italic;
}

.page-length {
  position: relative;
  top: -30px;
  left: 20px;
  font-size: 18px;
  color: rgb(105, 105, 105);
  font-style: italic;

}

.image-description {
  font-size: 14px;
  color: #666;
}

.currentpage {
  color: red;
  font-size: 22px;
}

.vertical-divider {
  height: 30px;
  /* 控制分割线高度 */
  width: 0.5px;
  background-color: #ccc;
  /* 颜色 */
  margin: auto;
}
</style>