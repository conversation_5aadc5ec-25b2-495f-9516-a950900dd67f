<template>
    <div class="smart_views_customer_relationship_supplier" id="smart_views_customer_relationship_supplier">
        <common-table
                border
                v-loading="tableOption.loading"
                ref="supplierInfoTable"
                style="width:100%"
                row-key="id"
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
                @sortBy="changeSort"
                @selection-change="selectionChange"
                @changePage="changePage"
        >
            <template #menuRight>
                <el-button plain type="primary" icon="Plus" @click="handlerOpenLinkSupplier">{{t('scm.linkName.linkSupplier')}}</el-button>
            </template>
            <template #childAssignCode="{row}">
                <div class="table_column_slot">
                    <div class="table_column_left">
                        <span v-if="!row.edit" :class="row.status==0?'strikethrough':''">
                            {{row.childAssignCode}}
                        </span>
                        <el-input :ref="(el)=>setAssignCodeInputRef(el, row.id)" show-word-limit maxlength="50" size="small" clearable v-model="row.childAssignCode" @blur="updateAssignCode(row)" v-if="row.edit" v-loading="row.saveLoading"></el-input>
                    </div>
                    <div class="table_column_right_icon" v-show="!row.edit">
                        <el-icon @click="editAssignCode(row)" v-if="btnRole(row,'Edit')"><EditPen/></el-icon>
                    </div>
                    <div class="table_column_right_icon" v-show="row.edit">
                        <el-icon @click="updateAssignCode(row)" v-if="btnRole(row,'Edit') && !row.saveLoading"><Finished/></el-icon>
                    </div>
                </div>
            </template>
            <template #actionColumn="{row}">
                <div class="action_icon">
                    <el-row :gutter="5">
                            <el-col :span="8" v-if="btnRole(row,'Query')">
                                <el-tooltip placement="top" :content="t('scm.manufacturer')">
                                    <el-badge class="manufacturer-badge" :style="{'margin-right': (row.badge>100?20:(row.badge>10?10:5)) +'px'}" :value="row.badge" :max="99">
                                        <el-icon class="icon-rel-manufacture"  @click="handlerOpenManufacture(row)">
                                            <Histogram />
                                        </el-icon>
                                    </el-badge>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="8" v-if="btnRole(row,'Contacts')"  class="contact-div">
                                <el-tooltip placement="top" :content="t('scm.contact.title')">
                                    <el-icon class="contacts-user"  @click="handlerOpenEditContact(row)">
                                        <Avatar />
                                    </el-icon>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="8" v-if="row.status=='1' && btnRole(row,'Disable')">
                                <el-tooltip placement="top" :content="t('scm.btnName.disable')">
                                    <el-icon  class="icon-disabled disabled-color" @click="handlerChangeStatus(row,'0')">
                                        <Remove/>
                                    </el-icon>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="8" v-if="row.status=='0' && btnRole(row,'Enable')" >
                                <el-tooltip placement="top" :content="t('scm.btnName.enable')">
                                    <el-icon @click="handlerChangeStatus(row,'1')">
                                        <Remove/>
                                    </el-icon>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="8" v-if="btnRole(row,'Approve')" >
                                <el-tooltip placement="top" :content="t('scm.btnName.approve')">
                                    <el-icon @click="handlerApproveReject(row,'APPROVED')">
                                        <CircleCheck/>
                                    </el-icon>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="8" v-if="btnRole(row,'Reject')">
                                <el-tooltip placement="top" :content="t('scm.btnName.reject')">
                                    <el-icon @click="handlerApproveReject(row,'REJECTED')">
                                        <CircleClose/>
                                    </el-icon>
                                </el-tooltip>
                            </el-col>
                    </el-row>
                </div>
            </template>
        </common-table>
    </div>

    <SearchCustomer
            v-if="openLinkSupplier"
            :title="t('scm.searchSupplier')"
            showCreatedBtn
            :createdBtnName="t('scm.createSupplier')"
            roleType="SUPPLIER"
            systemId="SMART"
            @cancelDia="cancelSeachCustomer"
            @saveSuccess="selectCustomer"
    ></SearchCustomer>

    <el-dialog
            v-model="openManufacture"
            :title="t('scm.manufacturer')"
            width="75%"
            lock-scroll
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            draggable
    >
        <Manufacture
            v-if="openManufacture"
            :parentId="linkManufactureProps.parentId"
            :tier="linkManufactureProps.tier"
            :currentRole="linkManufactureProps.currentRole"
            :targetRole="linkManufactureProps.targetRole"
            onlyQueryRelationCustomer
            :showLinkBtn="false"
            :showContacts="false"
            :showLinkBuyer="false"
        ></Manufacture>
    </el-dialog>

    <EditContactDialog
            v-if="openEditContactFlag"
            :companyId="queryCompanyId"
            @cancelDia="cancelEditContact"
    ></EditContactDialog>

</template>

<script setup>
import {mapGetters} from 'vuex'
import {ref, defineOptions, reactive, onBeforeMount, watch, nextTick} from 'vue';
import CommonTable from "../../../components/TableList/CommonTable.vue";
import Manufacture from './ManufactureInfo.vue'
import SearchCustomer from './innerComponent/SearchCustomer.vue'
import customerRelationApi from "@/api/customerRelation";
import EditContactDialog from "./innerComponent/EditContactDialog.vue";
import { ElNotification, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
const {t,locale} = useI18n();
import dayjs from 'dayjs';

watch(locale, (newV)=>{
    initDataList();
})

const props = defineProps({
    tier:{
        type:String,
        default:'T1'
    },
    currentRole:{
        type:String,
        default:'BUYER'
    },
    targetRole:{
        type:String,
        default:'SUPPLIER'
    }
})

defineOptions({
    name:'SupplierInfo'
})

const assignCodeInputRefs = ref({});
const setAssignCodeInputRef = (el,id)=>{
    console.log("setAssignCodeInputRef",el,id)
    assignCodeInputRefs.value[id] = el;
}
const editAssignCode = (row)=>{
    row.edit=true
    let {id} = row;
    nextTick(()=>{
        assignCodeInputRefs.value[id].focus();
    })
}

const buyerInfoTable = ref();

const tableOption = reactive({
    dataList: [],
    loading: false,
    menuShow: true,
    size:'small',
    option: {
        hideRowColor: true,
        selection: false,
        selectionDis: false,
        sortSelectionFiledName:'',
        showSortIcon:true,
        index: true,
        action: true,
        actionWidth: 150,
        disableOption:{
            disableNeedStrikethrough:true,
            disableValue:'0',
            disableProp:'status'
        },
        column: [
            {prop: 'companyName', label: 'scm.supplierName', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'companyAddress', label: 'scm.address', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'childAssignCode', label: 'scm.assignedCode', hide: false, filter: true, slot: true, type: 'Input'},
            {prop: 'sgsCode', label: 'scm.sgsCode', hide: false, filter: true, slot: false, type: 'Input'},
            {
                prop: 'approvalStatus',
                label: 'scm.approveStatus',
                multiple:true,
                i18n: true,
                hide: false,
                filter: true,
                slot: false,
                dicData: [
                    {label: 'scm.approveStatusEnumName.approve', value: 'APPROVED',tag:true,type:'success'},
                    {label: 'scm.approveStatusEnumName.inProgress', value: 'PENDING',tag:true,type:'warning'},
                    {label: 'scm.approveStatusEnumName.noRequired', value: 'NOT_REQUIRED',tag:true,type:'info'},
                    {label: 'scm.approveStatusEnumName.reject', value: 'REJECTED',tag:true,type:'primary'},
                ],
                type: 'Select'
            },
            {prop: 'updateUserRole', label: 'scm.updateUser', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateTime', label: 'scm.updateTime', hide: false, filter: true, slot: false, type: 'DateRange', minWidth: 280},
        ]
    },
    sort:{
        sortBy:"",
        sortOrder:""
    },
    filters: {},
    page: {
        show: true,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})

const changePage = (page)=>{
    tableOption.page.page = page.page;
    tableOption.page.rows = page.rows;
    initDataList();
}
const selectionChange = (val)=>{
}
const changeSort=(sort)=>{
    tableOption.sort.sortOrder = sort.prop;
    tableOption.sort.sortBy = sort.orderBy;
    initDataList();
}
const openManufacture = ref(false);
const linkManufactureProps = reactive({
    parentId:'',
    tier:'',
    currentRole:'',
    targetRole:''
});
const handlerOpenManufacture = (row)=>{
    let {id} = row;
    linkManufactureProps.parentId = id;
    linkManufactureProps.tier = 'T2';
    linkManufactureProps.currentRole = 'BUYER';
    linkManufactureProps.targetRole = 'MANUFACTURER';
    openManufacture.value = true;
}

const updateAssignCode = (row)=>{
    let {id,childAssignCode,oldChildAssignCode} = row;
    if(oldChildAssignCode == childAssignCode){
        row.saveLoading=false;
        row.edit = false;
        return;
    }
    row.saveLoading = true;
    customerRelationApi.changAssignCode({id,childAssignCode}).then(res=>{
        row.saveLoading=false;
        row.edit = false;
        row.oldChildAssignCode = childAssignCode;
    }).catch(err=>{
        row.saveLoading=false;
    })
}

const openLinkSupplier = ref(false)
const handlerOpenLinkSupplier = ()=>{
    openLinkSupplier.value = true;
}
const selectCustomer = (customer)=>{
    initDataList();
}
const cancelSeachCustomer = ()=>{
    openLinkSupplier.value = false
}

const openEditContactFlag = ref(false);
const queryCompanyId = ref('')
const handlerOpenEditContact = (row)=>{
    let {id} = row;
    queryCompanyId.value = id;
    openEditContactFlag.value = true;
}
const cancelEditContact = ()=>{
    queryCompanyId.value ='';
    openEditContactFlag.value=false;
}

const handlerChangeStatus = (row,status)=>{
    let {id} = row;
    customerRelationApi.changeStatus({id,status}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    }).catch(err=>{

    })
}

const handlerApproveReject = (row,approvalStatus)=>{
    let {id} = row;
    customerRelationApi.approveReject({shipId:id,approvalStatus}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    })
}



const initDataList = ()=>{
    let startUpdateTime = '';
    let endUpdateTime = '';
    if (tableOption.filters.updateTime && tableOption.filters.updateTime.length === 2) {
        startUpdateTime = dayjs(tableOption.filters.updateTime[0]).format('YYYY-MM-DD');
        endUpdateTime = dayjs(tableOption.filters.updateTime[1]).format('YYYY-MM-DD');
    }
    let param = {
        "currentRole": props.currentRole,
        "targetRole": props.targetRole,
        "tier": props.tier,
        ...tableOption.sort,
        ...tableOption.filters,
        current:tableOption.page.page,
        size:tableOption.page.rows,
        startUpdateTime,
        endUpdateTime
    }
    //tableOption.loading=true;
    customerRelationApi.querySCM(param).then(res=>{
        //tableOption.loading=false;
        if(res.status==200 && res.data){
            let {records,total} = res.data;
            records.forEach(re=>{
                re['oldChildAssignCode'] = re.childAssignCode;
            })
            tableOption.dataList =  records;
            tableOption.page.total = total;
        }
    }).catch(err=>{

    })
}
const btnRole = ({permissions},code)=>{
    return (permissions || []).includes(code);
}

watch(()=>tableOption.filters,()=>{
    initDataList();
},{deep:true})
onBeforeMount(()=>{
    initDataList();
})

</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_supplier {

}
</style>