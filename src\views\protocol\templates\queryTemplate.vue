<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_protocol_queryTemplate" id="sgs_smart_protocol_queryTemplate">
            <el-form :model="formObj" label-width="180px" label-position="left">
                <el-form-item prop="buCode" label="Product Line">
                    <el-select
                        style="width:100%"
                        v-model="formObj.buCode"
                        filterable
                        @change="changeBu"
                    >
                        <el-option
                                v-for="(pl,index) of productLineList"
                                :key="'pl_'+index"
                                :label="pl.productLineName"
                                :value="pl.productLineCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="bossNo" label="Customer">
                    <el-select
                            style="width:100%"
                            filterable
                            v-model="formObj.customer"
                            value-key="customerGroupCode"
                            @change="changeCus"
                    >
                        <el-option
                            v-for="(cus,index) in customerList || []"
                            :key="'cus_'+index"
                            :label="cus.customerGroupName"
                            :value="cus"
                        >
                            <span style="float: left;" v-if="cus.bossNo">
                                <i class="icon-all icongongsi" style="font-size:20px!important;"></i>
                            </span>
                            <span style="float: left;" v-if="!cus.bossNo">
                                <i class="icon-all iconjituangongsi" style="font-size:20px!important;"></i>
                            </span>
                            <span style="float: left;">{{cus.customerGroupName}}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="templateId" label="Template">
                    <el-select
                            style="width:100%"
                            filterable
                            v-model="formObj.templateId"
                    >
                        <el-option
                            v-for="(temp,index) in (templateList || [])"
                            :key="'temp_'+index"
                            :label="temp.formName"
                            :value="temp.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-row>
                <el-col style="text-align: right">
                    <el-button type="primary" size="small" @click="confirmTemplate">Confirm</el-button>
                    <el-button type="info" size="small" @click="$emit('cancelDia')">Cancel</el-button>
                </el-col>
            </el-row>
        </div>
    </basic-container>
</template>

<script>
    import protocolApi from '../../../api/protocol/protocol'
    export default {
        name: "queryTemplate",
        data() {
            return {
                pageLoading: false,
                formObj:{
                    buCode:'',
                    customer:{},
                    templateId:''
                },
                templateList:[]
            }
        },
        methods: {
            initPage() {},
            changeBu(){
                if(!this.formObj.buCode){
                    this.templateList = [];
                    this.formObj.templateId = '';
                    return;
                }
                if(!this.formObj.customer
                    || (!this.formObj.customer.number && !this.formObj.customer.customerGroupCode)){
                    return;
                }
                this.queryTemplate();
            },
            changeCus(){
                if(!this.formObj.customer
                    || (!this.formObj.customer.number && !this.formObj.customer.customerGroupCode)){
                    this.templateList = [];
                    this.formObj.templateId = '';
                    return;
                }
                if(!this.formObj.buCode){
                    return;
                }
                this.queryTemplate();
            },
            queryTemplate(){
                let param = {
                    buCode:this.formObj.buCode,
                    customerBossNo:this.formObj.customer.number
                }
                this.templateList = [];
                protocolApi.queryTemplateList(param).then(res=>{
                    console.log("query template",res);
                    if(res.status==200 && res.data && res.data.data){
                        this.templateList = res.data.data;
                    }
                }).catch(err=>{
                    console.log("query template err",err);
                })
            },
            confirmTemplate(){
                let { buCode,customer,templateId} = this.formObj;
                let {number,customerGroupCode,bossNo} = customer;
                let routeUrl = this.$router.resolve({
                    path:'/protocol/detail',
                    query:{
                        templateId,
                        buCode,
                        customerBossNo:number || bossNo,
                        customerGroupCode,
                        action:'new'
                    }
                })
                window.open(routeUrl.href,'_blank');
                this.$emit('cancelDia');
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            productLineList:[],
            customerList:[]
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_protocol_queryTemplate {
        font-family: 'Arial' !important;
        background: #fff;
        padding: 24px 32px;
    }
</style>