<template>
  <div
    class="horizontal-divider"
    :style="{ paddingTop: paddingTop, paddingBottom: paddingBottom }"
  >
    <hr />
  </div>
</template>

<script setup>
const props = defineProps({
  // 自定义上边距，默认值为 0px
  paddingTop: {
    type: String,
    default: '0px',
  },
  // 自定义下边距，默认值为 0px
  paddingBottom: {
    type: String,
    default: '0px',
  },
})
</script>

<style scoped>
.horizontal-divider hr {
  border: none;
  border-top: 1px solid var(--el-collapse-border-color);
}
</style>
