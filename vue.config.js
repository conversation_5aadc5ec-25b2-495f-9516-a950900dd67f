const CompressionPlugin = require("compression-webpack-plugin");
const path = require("path");

const webpack = require("webpack");
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = ["js", "css"];
const env = process.env.NODE_ENV;
//const env = 'test'
const isProduction = env === "production";
const timestamp = new Date().getTime();

const proxyConfig = {
  ws: true,
  target: "https://cnsgsmart-uat.sgs.net",
};

switch (env) {
  case 'development':
    // proxyConfig.target = 'http://127.0.0.1:8100';
    proxyConfig.target = ' https://cnsgsmart-uat.sgs.net/api';
    proxyConfig.pathRewrite = {
      "^/api": "/",
    };
    break;

  default:
    break;
}

module.exports = {
  lintOnSave: true,
  productionSourceMap: false,
  //暂时去除webpack压缩，等F5修改完成后再发布
  /*  configureWebpack: {
    plugins: [
      new CompressionPlugin({
        test: /\.(js|css)?$/i, // 哪些文件要压缩
        filename: '[path].gz[query]',　// 压缩后的文件名
        algorithm: 'gzip',　// 使用gzip压缩
        minRatio: 1,　// 压缩率小于1才会压缩
        deleteOriginalAssets: true // 删除未压缩的文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false
      })
    ]
  },*/
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      vue: "Vue",
      "vue-router": "VueRouter",
      vuex: "Vuex",
      axios: "axios",
      // 'jspdf': 'jspdf',
      "biz-component": "biz-component",
      // 'element-ui': 'ELEMENT',
      echarts: "echarts",
    });

    // 配置包分析器
    // config.plugin('webpack-bundle-analyzer')
    // .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)

    const version = new Date().getTime();
    config.output.filename(`[name].${version}.${timestamp}.js`).end();
    config.output.chunkFilename(`[name].${version}.${timestamp}.js`).end();

    const entry = config.entry("app");
    entry.add("babel-polyfill").end();
    entry.add("classlist-polyfill").end();
    entry
      //.add('@/mock')
      .end();
  },
  devServer: {
    // 端口配置
    port: 1888,
    open: true,
    // 反向代理配置 http://sgsmart.com/api/.......
    proxy: {
      "/api": proxyConfig,
      /*,
      '/tApi':{
        target:'https://uat-trimschina.sgs.net/trimsApi/',
        changeOrigin: true,//是否允许开启代理
        pathRewrite: {//代理地址重写
          '^/tApi': ''
        }
      }*/
    },
  },
  configureWebpack: {
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@i": path.resolve(__dirname, "./src/assets"),
      },
    },
    plugins: [
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
      // 下面是下载的插件的配置
      new CompressionWebpackPlugin({
        algorithm: "gzip",
        test: new RegExp("\\.(" + productionGzipExtensions.join("|") + ")$"),
        threshold: 10240,
        minRatio: 0.8,
      }),
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 5,
        minChunkSize: 100,
      }),
    ],
  },
};
