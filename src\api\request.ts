import axios from 'axios'
import { ElNotification, ElLoading} from 'element-plus'
import Cookies from 'js-cookie'
import { RequestOptions } from '../type/common'
import { local, redirect,apiHost } from '../utils/util'
import crypto from '../utils/crypto'
import store from '../store'
import { RootState } from '../type/store'


// 定义请求配置
const createRequestOptions = (options: RequestOptions) => {
  const timestamp = new Date().valueOf()
  const sign = crypto.encrypt(timestamp, 'cnsgsmart-uat.sgs.net')
  const sgstoken = Cookies.get('sgsToken')
  const sgsLanguage = (store.state as RootState).common.language; ;
  const headers = options.headers || {};
  //接收外部自定义header，但是不能覆盖默认的属性
  if(headers){
    delete headers['Content-Type'];
    delete headers.sign;
    delete headers.timestamp;
    delete headers.sgstoken;
  }
  // const originAlias = window.location.origin
  return {
    url: `${apiHost}${options.url}`,
    method: options.method,
    params: options.params,
    data: options.data,
    responseType: options.responseType,
    credentials: 'include', // 允许携带 Cookie
    headers: {
      'Content-Type': 'application/json',
      sign,
      timestamp,
      sgstoken,
      sgsLanguage,
      ...headers
    },
  }
}

// 定义加载状态
let loadingInstance = null
let count = 0

const showLoading = () => {
  if (!count) {
    loadingInstance = ElLoading.service({
      fullscreen: true,
      body: true,
      lock: true,
      text: 'loading...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    count++
  }
}

const hideLoading = () => {
  setTimeout(() => {
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    count = 0
  }, 1000)
}

// 创建 Axios 实例
const axiosInstance = axios.create()

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    //部分页面不需要每次请求都要loading
    if('false'===config.headers.loading){
      return config
    }
    showLoading()
    return config
  },
  (error) => {
    hideLoading()
    return Promise.reject(error)
  },
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    hideLoading()
    // 判断是否为文件流
    const fileStreamContentTypes = [
      'application/octet-stream',
      'application/vnd.ms-excel',
    ]
    const contentType = response.headers['content-type']
    if (
      contentType &&
      fileStreamContentTypes.some((type) => contentType.includes(type))
    ) {
      return response // 直接返回响应，让调用者处理文件流
    }
    if ( (
            (response.status==200)
            &&
            ( !response.data.data && (response.data.length>=0 || Object.keys(response.data).length>0))
        )
        || response.data.code === 200 || response.data.status === 200) {
      return response.data
    } else if (response.status === 401 || response.data.code === 401) {
      ElNotification({
        message:
          response.data.message ||
          response.data.msg ||
          '登录已失效，正在跳转到登录页',
        type: 'info',
        duration: 2000,
        onClose: () => {
          local.clear()
          redirect('')
        },
      })
      return response.data
    } else {
      console.log("提示错误了？？？？",response)
      ElNotification.error(response.data.message || response.data.msg)
      return response.data
    }
  },
  (error) => {
    console.log("异常了",error)
    hideLoading()
    if (error.response && error.response.status === 401) {
      ElNotification.error('token认证失败，重新登录')
    } else if (error.response && error.response.status === 400) {
      ElNotification.error('非法输入拦截')
      return {
        resultCode: error.response.data.status,
        resultMsg: error.response.data.message,
      }
    } else {
      ElNotification.error(error.message || '网络异常，请稍后重试')
      return Promise.reject(error)
    }
  },
)

// 定义请求函数
const request = async (options: RequestOptions) => {
  return axiosInstance({
    ...createRequestOptions(options),
  })
}

export default request
