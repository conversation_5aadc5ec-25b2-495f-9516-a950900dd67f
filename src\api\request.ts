import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import Cookies from 'js-cookie'
import { RequestOptions } from '../type/common'
import { local, redirect, apiHost } from '../utils/util'
import crypto from '../utils/crypto'

const userInfo =JSON.parse(localStorage.getItem('SGS-userInfo') || '{}') 

let isVip = userInfo?.content?.isVip==1?1:0
let isInternalUser = userInfo?.content?.isInternalUser==1?1:0
declare module 'axios' {
  export interface AxiosRequestConfig {
    loading?: boolean;
  }
}

// 定义请求配置
const createRequestOptions = (options: RequestOptions) => {
  const timestamp = new Date().valueOf()
  const sign = crypto.encrypt(timestamp, 'cnsgsmart-uat.sgs.net')
  const sgstoken = Cookies.get('sgsToken')
  return {
    url: `${apiHost}${options.url}`,
    method: options.method,
    params: options.params,
    data: options.data,
    loading:options.loading,
    credentials: 'include', // 允许携带 Cookie
    headers: {
      'Content-Type': 'application/json',
      'sgs-system-id' : 59,
      'sgs-system-name': 'CPSC E-Filing',
      'is_payment':isVip,
      'is_inner':isInternalUser,
      sign,
      timestamp,
      sgstoken,
    },
  }
}

// 定义加载状态
let loadingInstance: any = null
let count = 0

const showLoading = () => {
  if (!count) {
    loadingInstance = ElLoading.service({
      fullscreen: true,
      body: true,
      lock: true,
      text: 'loading...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    count++
  }
}

const hideLoading = () => {
  setTimeout(() => {
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    count = 0
  }, 1000)
}

// 创建 Axios 实例
const axiosInstance = axios.create()

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    if(config.loading===false){
      return config
    }
    showLoading()
    return config
  },
  (error) => {
    hideLoading()
    return Promise.reject(error)
  },
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    hideLoading()
    if (response.data.code === 200||response.data.isSuccess === true||response.data.status === 200|| typeof response.data==='string') {
      return response.data
    } else if (response.data.code === '9978') {
      ElMessage({
        message: response.data.resultMsg || '登录已失效，正在跳转到登录页',
        type: 'info',
        duration: 2000,
        onClose: () => {
          local.clear()
          redirect('')
        },
      })
      return response.data
    } else {
      ElMessage.error(response.data.resultMsg || '网络异常，请稍后重试')
      return response.data
    }
  },
  (error) => {
    hideLoading()
    if (error.response && error.response.status === 401) {
      ElMessage.error('token认证失败，重新登录')
    } else if (error.response && error.response.status === 400) {
      ElMessage.error('非法输入拦截')
      return {
        resultCode: error.response.data.status,
        resultMsg: error.response.data.message,
      }
    } else {
      ElMessage.error(error.message || '网络异常，请稍后重试')
      return Promise.reject(error)
    }
  },
)

// 定义请求函数
const request = async (options: RequestOptions) => {
  const { url, method, params, data, loading } = options
  return axiosInstance({
    ...createRequestOptions({ url, method, params, data,loading }),
  })
}

export default request
