<template>
    <div class="tcContent" id="tcContent">
        <div :is="componentObj.currentView"></div>
    </div>
</template>

<script>
    import tcContentenus from "./tcContentenus";
    import tcContentzhcn from "./tcContentzhcn";
    import {mapGetters} from "vuex";

    export default {
        name: "tcContent",
        inject:['calculateHeight'],
        data() {
            return {
                componentObj:{
                    currentView:tcContentenus,
                    tcContentenus:tcContentenus,
                    tcContentzhcn:tcContentzhcn,
                }
            }
        },
        methods: {},
        mounted() {
            this.calculateHeight('tc');
        },
        created() {
            this.componentObj.currentView = this.componentObj['tcContent'+ (this.language.replace("-","").toLowerCase())]
        },
        watch: {
            language:{
                immediate:true,
                handler(newV){
                    this.componentObj.currentView = this.componentObj['tcContent'+ (newV.replace("-","").toLowerCase())]
                }
            }
        },
        computed: {
            ...mapGetters(["language"])
        },
        props: {},
        components: {}
    }
</script>

<style lang="scss">
    .tcContent {
        word-wrap: break-word;
        white-space: normal;
        word-break: break-word;
        p.indent2{
            text-indent: 2em;
        }
        p.indent4{
            text-indent: 4em;
        }
        p.indent6{
            text-indent: 6em;
        }
        p.indent8{
            text-indent: 8em;
        }
        p.indent10{
            text-indent: 10em;
        }
        .section{
            font-weight: bold;
        }
        .textCenter{
            text-align: center;
        }
        div.underLine{
            padding-top: 1em;
            margin-bottom: 2em;
            height: 1px;
            border-bottom: solid 1px #bfbfbf;
        }
        a{
            color:#ff6600;
        }
    }
</style>