<template>
    <basic-container>
        <el-row>
            <el-col :span="8">
                <br>
                <el-form :model="form" size="small" :rules="rules" ref="ruleForm" label-width="100px">
                    <el-form-item :label="$t('user.oldPassword')" prop="oldPassword">
                        <el-input maxlength="50" type="password" v-model="form.oldPassword" autocomplete="off" show-password="true"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('user.newPassword')" prop="password">
                        <el-input maxlength="50" type="password" v-model="form.password" autocomplete="off" show-password="true"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('account.passwordConfirm')" prop="repassword">
                        <el-input maxlength="50" type="password" v-model="form.repassword" autocomplete="off" show-password="true"></el-input>
                    </el-form-item>
                    <el-form-item class="text-left">
                        <el-button type="primary" @click="updatePassword">{{$t('operation.submit')}}</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </basic-container>
</template>

<script>
    import { encryptor } from '@/util/auth';
    import {updatePassword} from "@/api/system/user";
    import { mapGetters, mapState } from "vuex";

    export default {
        name: "changePassword",
        data() {

            var validatePass = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('register.passwordBlur')));
                } else {
                    //校验密码格式
                    const passregex = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/;
                    if(!passregex.test(value)){
                        callback(new Error(this.$t('register.passwordError')));
                    }
                    if (this.form.repassword !== '') {
                        this.$refs.ruleForm.validateField('repassword');
                    }
                    callback();
                }
            };

            var validateCheckPass = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('register.doublePasswordBlur')))
                } else if (value !== this.form.password) {
                    callback(new Error(this.$t('register.checkPassNo')))
                } else {
                    callback()
                }
            };
            return {
                form: {},
                rules: {
                    oldPassword: [
                        { required: true, message: this.$t('register.passwordBlur'), trigger: 'blur' },
                    ],
                    password: [
                        { required: true,  validator:validatePass ,  message: this.$t('register.passwordBlur'), trigger: 'blur' },
                        { pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/, message: this.$t('register.passwordError') }
                    ],
                    repassword: [
                        { required: true, validator: validateCheckPass, trigger: 'blur' }
                    ]
                }
            };
        },
        created() {

        },
        computed: {
            ...mapGetters([
                "userInfo"
            ])
        },
        methods: {
            updatePassword() {
                console.log("修改密码", this.form);
                this.form.username = this.userInfo.account;
                this.form.code = this.userInfo.accountNo;
                this.$refs['ruleForm'].validate((valid) => {
                    if (!valid) {
                        return false;
                    } else {
                        let params = Object.assign({}, this.form);
                        params.oldPassword = encryptor(params.oldPassword);
                        params.password = encryptor(params.password);
                        params.repassword = encryptor(params.repassword);
                        updatePassword(params).then(res => {
                            if (res.data.success) {
                                this.$message({
                                    type: "success",
                                    message: this.$t('user.updPasswordSuccess')
                                });
                            } else {
                                this.$message({
                                    type: "error",
                                    message: res.data.msg
                                });
                            }
                        })
                    }
                })

            },
        }
    };
</script>

<style scoped>

</style>
