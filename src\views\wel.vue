<template>
  <div>
    <div v-if="productLineCode=='SL'"  @mouseenter="isActive=true" @mouseleave="isActive=false" :class="[isActive ? 'sl_door_active' : 'sl_door']">
      <img v-if="language=='zh-CN'" @click="openSlPortal()" class="sl_door_img" :src="[isDoor ? '/img/icon/SL_OPEN_CN_NEW.png' : '/img/icon/SL_STOP_CN_NEW.png']" />
      <img v-if="language=='en-US'"@click="openSlPortal()" class="sl_door_img" :src="[isDoor ? '/img/icon/SL_OPEN_EN_NEW.png' : '/img/icon/SL_STOP_EN_NEW.png']" />
    </div>
    <frame-index v-if="!(role.isSGS || role.isKam || role.isThirdPartyLab && !role.isCcl) && productLineCode && components && components.length > 0" :layout="components"></frame-index>
    <dashboard v-else-if="loadDashBoard" :components-list="componentsList"></dashboard>
  </div>
</template>


<style lang="scss">

</style>
<script>

import {getPageConfigDetail} from "@/api/system/settings/components/components";
import { pageEnums } from '@/commons/enums/PageEnums';
import {slPortalUrlEn,slPortalUrlCn} from '@/config/env';
import {mapGetters} from "vuex";
import {validatenull} from "../util/validate";
const TIME_COUNT = 10; //更改倒计时时间
export default {
  components: {
    FrameIndex: resolve => require(['./dashboard/FrameIndex'], resolve),
    Dashboard: resolve => require(['./dashboard/index'], resolve)
  },
  computed: {
    ...mapGetters(["userInfo", "language", "permission", "dimensions","posts"]),
    role() {
      return {
        isSGS: this.haseRole('SGSUserRole', 'SgsAdmin') || this.haseRole('SGSUserRole', 'SgsLabUser'),
        isKam: this.haseRole('SGSUserRole', 'KAM'),
        isThirdPartyLab: this.haseRole('UserRole', 'ThirdPartyLab'),
        isCcl: this.haseCclPost('SGSUserRole', 'CCL'),
      };
    }
  },
  data() {
    return {
      slPortalUrlEn:'',
      slPortalUrlCn:'',
      isDoor:true,
      counter: 10,//SL门户控制动态时间
      timer: null,
      isActive:false,//控制是否鼠标移入
      components: [],
      productLineCode: null,
      loadDashBoard: false,
      componentsList:[]
    }
  },
  mounted() {
    document.title='Homepage';
  },
  created() {
    this.slPortalUrlEn = slPortalUrlEn;
    this.slPortalUrlCn = slPortalUrlCn;
    this.init();
    let userInfo = this.$store && this.$store.getters && this.$store.getters.userInfo;
    this.productLineCode = userInfo && userInfo.productLineCode;

    if(this.productLineCode && !(this.role.isSGS || this.role.isKam || this.role.isThirdPartyLab && !this.role.isCcl)) {
      getPageConfigDetail({buCode: this.productLineCode, pageCode: pageEnums[0].pageCode}).then(res => {
        if(res.data.data && res.data.data.configData) {
          this.components = JSON.parse(res.data.data.configData);
        }
        this.loadDashBoard = !(this.components && this.components.length > 0);
      }).catch(() => {
        this.loadDashBoard = true;
      });
    } else {

      getPageConfigDetail({buCode: this.productLineCode, pageCode: pageEnums[0].pageCode}).then(res => {
        if(res.data.data && res.data.data.configData) {
          this.componentsList = JSON.parse(res.data.data.configData);
        }
        this.loadDashBoard = true;
      }).catch(() => {
        this.loadDashBoard = true;
      });

    }
  },
  methods: {
    init() {
      this.counter = TIME_COUNT;
      this.timer = setInterval(() => {
        if (this.counter > 0 && this.counter <= TIME_COUNT) {
          this.counter--;
          this.isDoor=!this.isDoor;
        } else {
          clearInterval(this.timer);  // 清除定时器
          this.timer = null;
          this.isActive=false;
        }
      }, 1000)
    },
    openSlPortal(){
      if(this.language=='zh-CN'){
        window.open('http://' +this.slPortalUrlCn);
      } else {
        window.open('http://' +this.slPortalUrlEn);
      }
    },
    haseRole(type, role) {
      debugger;
      if (validatenull(type) || validatenull(role)) {
        return false;
      }
      if (validatenull(this.dimensions)) {
        return false;
      } else {
        if (this.dimensions.hasOwnProperty(type)) {
          if (this.dimensions[type].indexOf(role) >= 0) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      }
    },
    haseCclPost(post) {
      if (validatenull(post) || validatenull(post)) {
        return false;
      }
      if (validatenull(this.posts)) {
        return false;
      } else {
        if (this.posts.includes("CCL Admin") || this.posts.includes("CCLUser")) {
          return true;
        } else {
          return false;
        }
      }
    },

  }
}
</script>
<style lang="scss">
.sl_door {
  position: fixed;
  width: 50px;
  right: 14.5px;
  top: 30vh;
  font-size: 28px;
  font-weight: bold;
  z-index: 1999;
  text-align: center;
}
.sl_door_active {
  position: fixed;
  width: 60px;
  right: 50px;
  top: 30vh;
  font-size: 28px;
  font-weight: bold;
  z-index: 1999;
  text-align: center;
}
.sl_door_img {
  width: 110px;
  height: 70px;
}
</style>
