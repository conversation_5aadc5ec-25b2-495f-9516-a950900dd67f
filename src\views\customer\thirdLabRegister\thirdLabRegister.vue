<template>
    <basic-container>
        <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.newCustomer')}}</el-breadcrumb-item>
        </el-breadcrumb>
        <el-card class="sgs-box">
            <el-form :model="form"
                     ref="form"
                     :rules="rules"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     class="sgs-form"
                     @submit.native.prevent>
                <div class="sgs-group">
                    <h3>{{$t('customer.title.base')}}</h3>
                </div>
                <el-row :gutter="20" class="sgs-customer-style">
                    <sgs-customer
                            v-on:customer-data='receiveApplicant'
                            displayType="account"
                            :show-customer-group=true
                            :customer-url="customerUrl"
                            :default-customer="customerUnit">
                    </sgs-customer>

                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customer.sgs.customerNo')">
                            <el-input v-model="form.bossNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customerGroup.code')">
                            <el-input v-model="form.customerGroupCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customerGroup.name')">
                            <el-input v-model="form.customerGroupName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="sgs-group">
                    <h3>{{$t('customer.title.thirdLab')}}</h3>
                </div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('lab.labName')" prop="labName">
                            <!--<el-input
                                    maxlength="200"
                                    clearable v-model="form.labName"></el-input>-->
                            <el-select v-model="form.labCode" :placeholder="$t('lab.selLabName')"
                                       @change="selectLabChange" filterable :filter-method="dataFilter"
                                       style="width: 100%;" clearable>
                                <el-option v-for="(lab,index) in labData" :label="lab.labName"
                                           :value="lab.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('lab.labAddress')" prop="labAddress">
                            <el-input
                                    maxlength="100" disabled="true"
                                    clearable v-model="form.labAddress" autoComplete="off"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item :label="$t('lab.labLocation')" prop="labAddress">
                            <el-input
                                    maxlength="100" disabled="true"
                                    clearable v-model="form.locationName" autoComplete="off"></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>

                <div class="sgs-group">
                    <h3>{{$t('customer.title.admin')}}</h3>
                </div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('register.loginAccount')" prop="account">
                            <el-input
                                    maxlength="200"
                                    clearable v-model="form.account"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.userName')" prop="userName">
                            <el-input
                                    maxlength="100"
                                    clearable v-model="form.userName" autoComplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.email')" prop="email">
                            <el-input
                                    maxlength="150"
                                    clearable v-model="form.email" :placeholder="$t('register.emailBlur')"
                                    :disabled="emailDisabled"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('register.phoneNumber')">
                            <el-input
                                    maxlength="11"
                                    clearable v-model="form.contactMobile"
                                    :disabled="contactMobileDisabled"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.password')" prop="password">
                            <el-input
                                    maxlength="50"
                                    clearable type="password" v-model="form.password"
                                    autocomplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.passwordConfirm')" prop="doublePassword">
                            <el-input
                                    maxlength="50"
                                    clearable type="password" v-model="form.doublePassword"
                                    auto-complete="new-password"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <div class="sgs-group">
                     <h3>{{$t('customer.title.other')}}</h3>
                 </div>-->
                <!--<el-col span="24">
                    <el-form-item :label="$t('common.other')">
                        <el-checkbox v-model="form.isBuyer">{{$t('customer.joinBuyerProgram')}}
                        </el-checkbox>
                        <el-checkbox v-model="form.relationshipNeedApprove">
                            {{$t('customer.customerRelationshipNeedApprove')}}
                        </el-checkbox>
                    </el-form-item>
                </el-col>-->
                <!--<el-row :gutter="20">
                    <el-col span="12">
                        <el-form-item :label="$t('account.userName')">
                            <span>{{form.customer.account.userName}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col span="12">
                        <el-form-item :label="$t('account.email')">
                            <span>{{form.customer.account.email}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>-->

                <el-row :gutter="20">
                    <div class="sgs-bottom">
                        <el-button @click="goBackList">{{$t('operation.goBack')}}</el-button>
                        <el-button v-if="submitBtnFlag" type="primary" @click="onSubmit()"
                                   :disabled="isdisabled">{{$t('operation.submit')}}
                        </el-button>
                        <!--  <el-button type="primary" v-if="permissionList.refuseBtn" @click="onrefuse()"
                                     :disabled="isdisabled">{{$t('common.reject')}}
                          </el-button>-->
                    </div>
                </el-row>
            </el-form>
        </el-card>
    </basic-container>
</template>

<script>
    import {
        addOfflineRegister,
        detail,
        checkAccount,
        validateAccount
    } from "@/api/customer/customerRegister";
    import {validatenull} from "@/util/validate";
    import {deepClone} from '@/util/util'
    import {mapGetters} from "vuex";
    import {queryThirdLabList} from "@/api/lab/lab";

    export default {
        components: {},
        created() {
            //获取路由参数
            this.getParams();
        },
        data() {
            var validateEmail = (rule, value, callback) => {
                if (!validatenull(value)) {
                    checkAccount({email: this.form.email}).then(res => {
                        //判断显示客户需要填写的信息
                        if (res.data.success === 1 || res.data.success === '1') {
                            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                                callback(new Error(this.$t('register.submitInfo')));
                            } else {
                                callback(new Error(this.$t('register.lockError')));
                            }
                        } else {
                            callback();
                        }
                    });
                }
            };
            //验证密码
            var validatePass2 = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('register.doublePasswordBlur')));
                } else if (value !== this.form.password) {
                    callback(new Error(this.$t('register.checkPassNo')));
                } else {
                    callback();
                }
            };
            return {
                submitBtnFlag: true,
                customerId: '',
                customerUnit: {
                    bossNo: '',
                    customerGroupName: '',
                    customerGroupCode: '',
                    isBuyer: false,
                    relationshipNeedApprove: false,
                },
                labQuery:{
                    //labType:2
                },
                isdisabled: false,
                labData: [],

                form: {
                    bossNo: '',
                    customerGroupCode: '',
                    customerGroupName: '',
                    account: '',
                    userName: '',
                    email: '',
                    contactMobile: '',
                    password: '',
                    doublePassword: '',
                    isBuyer: false,
                    relationshipNeedApprove: false
                },
                customerUrl: '/api/sgsapi/CustomerApi/preOrderCustomer/contactAddress/query',
                rules: {
                    account: [
                        {required: true, message: this.$t('register.loginAccountBlur'), trigger: 'blur'},
                    ],
                    email: [
                        {required: true, message: this.$t('register.emailBlur'), trigger: 'blur'},
                        {type: 'email', message: this.$t('register.emailRigthBlur'), trigger: ['blur', 'change']},
                        {validator: validateEmail, trigger: 'blur'}
                    ],
                    userName: [
                        {required: true, message: this.$t('register.accountBlur'), trigger: 'blur'},
                    ],
                    password: [
                        {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
                        {
                            pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
                            message: this.$t('register.passwordError')
                        }
                    ],
                    doublePassword: [
                        {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
                        {validator: validatePass2, trigger: 'blur'}
                    ],
                }
            };
        },
        watch: {},
        computed: {
            ...mapGetters(["permission", "language"]),
            permissionList() {
                return {
                    /*  addBtn: this.vaildData(this.permission['sgs:template:add'], false),
                      addContactBtn: this.vaildData(this.permission['sgs:template:addContact'], false),*/
                };
            },
        },
        methods: {
            //返回列表页
            goBackList() {
                this.$router.push('/customer/offlineRegister/list');
            },
            getParams() {
                // 取到路由带过来的参数
                const routerParams = this.$route.query.id
                // 将数据放在当前组件的数据内
                this.customerId = routerParams;
                if (routerParams != '' && routerParams != undefined) {
                    //查询用户详情
                    this.submitBtnFlag = false;
                    this.queryCustomer(this.customerId);
                }
                this.query3rfPartyLab();
            },
            selectLabChange(val) {
                   let obj = {};
                   obj = this.labData.find((item) => {
                    return item.id === val;
                    });
                   if(!validatenull(obj)){
                       this.form.labAddress=obj.labAddress;
                       this.form.locationName=obj.locationName;
                   }
            },
             //查询第三方实验室
            query3rfPartyLab() {
                var params={};
                queryThirdLabList(Object.assign(params, this.labQuery,this.sort)).then(res => {
                    this.labData=res.data.data;
                });
            },

            queryCustomer(id) {
                detail(id).then(res => {
                    console.log(this.customerUnit);
                    var data = res.data.data.customer;
                    this.form.customerNameCn = data.customerNameZh;
                    this.form.customerNameEn = data.customerNameEn;
                    this.form.customerAddressEn = data.customerAddressEn;
                    this.form.customerAddressZh = data.customerAddressZh;
                    this.form.qualification = data.qualification;
                    this.form.isBuyer = data.isBuyer == 1 ? true : false;
                    this.form.relationshipNeedApprove = data.relationshipNeedApprove == 1 ? true : false;
                    this.form.email = data.account.email;
                    this.form.userName = data.account.userName;
                    this.form.password = data.account.password;
                    this.form.doublePassword = data.account.password;
                    this.form.contactMobile = data.account.mobile;
                    this.form.account = data.account.account;
                    var customerUnitObj = {};
                    customerUnitObj['bossNo'] = data.bossNo;
                    customerUnitObj['customerNameEn'] = data.customerNameEn;
                    customerUnitObj['customerNameCn'] = data.customerNameZh;
                    customerUnitObj['addressCn'] = data.customerAddressZh;
                    customerUnitObj['addressEn'] = data.customerAddressEn;
                    customerUnitObj['customerGroupId'] = data.customerGroupId;
                    customerUnitObj['customerGroupCode'] = data.customerGroupCode;
                    customerUnitObj['customerGroupName'] = data.customerGroupName;
                    customerUnitObj['number'] = data.bossNo;
                    this.customerUnit = customerUnitObj;
                });
            },


            async onSubmit() {
                //验证公司地址是否填入
                if (validatenull(this.form.customerAddressEn) && validatenull(this.form.customerAddressZh)) {
                    this.$message({
                        type: "error",
                        message: this.$t('register.addressBlur')
                    });
                    return false;
                }
                //验证该账号是否在SGSmart中注册过
                let validateRes = await validateAccount(this.form.account);
                debugger;
                console.log(validateRes);
                if (!validateRes.data.data) {//该用户已注册
                    this.$message({
                        type: "error",
                        message: this.$t('register.registerError')
                    });
                    return false;
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        const register = {};
                        register.customer = deepClone(this.form);
                        register.customer.customerNameZh = this.form.customerNameCn;
                        register.customer.customerNameEn = this.form.customerNameEn;
                        register.customer.customerAddressEn = this.form.customerAddressEn;
                        register.customer.customerAddressZh = this.form.customerAddressZh;
                        register.customer.qualification = this.form.qualification;
                        register.customer.isBuyer = this.form.isBuyer ? 1 : 0;
                        register.customer.relationshipNeedApprove = this.form.relationshipNeedApprove ? 1 : 0;
                        register.customer.account = {
                            email: this.form.email,
                            userName: this.form.userName,
                            password: this.form.password,
                            mobile: this.form.contactMobile,
                            account: this.form.account,
                            language: this.language,
                            registerMode: this.registerMode
                        };
                        register.customer.lab={
                            labCode: this.form.labCode,
                            labName: this.form.labCode,
                            id:this.form.labId,
                        };
                        //加入loading效果
                        const loading = this.$loading({
                            lock: true,
                            text: this.$t('loading'),
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        addOfflineRegister(register).then(() => {
                            loading.close();
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.$router.push('/customer/offlineRegister/list?searchVal=' + this.form.customerNameCn);
                        }, error => {
                            loading.close();
                            console.log(error);
                        });

                    }
                });
                console.log(this.form);
            },
            receiveApplicant(data) {
                debugger;
                console.log(data);
                //回填客户组信息
                this.form.customerNameCn = data.customerNameCn;
                this.form.customerNameEn = data.customerNameEn;
                var companyAddress = "";
                if (!validatenull(data.addressCn)) {
                    companyAddress = data.addressCn;
                } else {
                    companyAddress = data.addressEn;
                }
                this.form.customerAddressEn = companyAddress;
                this.form.customerAddressZh = companyAddress;
                this.form.customerGroupId = data.customerGroupId;
                this.form.customerGroupName = data.customerGroupName;
                this.form.customerGroupCode = data.customerGroupCode;
                this.form.bossNo = data.number;
                this.form.number = data.number;
                console.log(this.form);
            }
        }
    };
</script>

<style lang="scss" scoped>
.sgs-customer-style {
    padding: 0 13px;
    /deep/ .col-md-6 {
        &:last-of-type {
            > h4 {
                margin: 0;
                height: 10px;
            }
        }
    }
    /deep/ .form-horizontal {
        .form-group {
            border: 0 !important;
            .control-label {
                font-weight: normal;
                color: #626066;
                font-size: 14px;
                width: 100%;
                text-align: left;
            }
            .col-sm-7 {
                width: 98%;
            }
        }
        .form-control {
            border-width: 0;
            border-bottom: 1px solid #1b1b1b;
            border-radius: 0;
            box-shadow: none;
        }
        .input-group-btn {
            border-bottom: 1px solid #1b1b1b;
        }
        .input-group-btn:last-child > .btn {
            border-radius: 0;
            background: transparent;
            color: #1b1b1b;
            border: 0;
            &:hover {
                color: #f60;
            }
        }
    }
}
</style>
