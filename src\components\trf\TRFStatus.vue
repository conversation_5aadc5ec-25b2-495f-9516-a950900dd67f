<template>
    <div class="trf-status">
        <!-- 1:待递交  2:已提交  3:已受理  4:测试中  5:完成  6:已取消 13:报价单待确认 14:已确认报价单-->

        <!-- <div class="pross" :class="'status-'+status">
          <i :class="'s-'+i" v-for="i in 5" :key="i"></i>
        </div>
        <span class="status-tag" :class="statusArr[status-1]">{{ statusContent }}</span> -->


        <el-tooltip class="tooltip-item" effect="dark" :content="statusContent" placement="top">
            <img :src="'/img/trf/icon_point_'+status+'.png'"
                 style="vertical-align: -moz-middle-with-baseline; margin-right: 20px;">
        </el-tooltip>
    </div>
</template>

<script>
    export default {
        name: "TRFStatus",
        props: {
            status: {
                required: true,
                type: Number,
                default: 1
            },
            reason: {
                type: String,
                default: ""
            }
        },
        data() {
            return {
                statusArr: ['toSubmitted', 'submitted', 'accepted', 'test', 'completed', 'cancelled']
            }
        },
        computed: {
            statusContent() {
                let content = ''
                switch (this.status) {
                    case 1 :
                        content = this.$t('trfStatus.draft')
                        break
                    case  2 :
                        content = this.$t('trfStatus.submitted')
                        break
                    case  3 :
                        content = this.$t('trfStatus.preparation')
                        break
                    case  4 :
                        content = this.$t('trfStatus.testing')
                        break
                    case  5 :
                        content = this.$t('trfStatus.completed')
                        break
                    case  6 :
                        content = this.$t('trfStatus.cancel')
                        break
                    case  11 :
                    case  12 :
                        content = this.$t('trfStatus.pending');
                        //content = content + ": [ " + this.reason + " ]";
                        break
                    case  13 :
                        content = this.$t('wel1.quotationToBeConfirmed')
                        break
                    case  14 :
                        content = this.$t('wel1.quotationConfirmed')
                        break
                    default:
                        content = this.$t('trfStatus.draft')
                }
                return content
            }
        }
    }
</script>

<style scoped lang="scss">
  .trf-status {
    white-space: nowrap;
  }

  .pross {
    display: inline-block;

    i {
      display: inline-block;
      width: 8px;
      height: 8px;
      background: #FFFFFF;
      border: 2px solid #D9D8D7;
      margin-right: 16px;
      position: relative;
      border-radius: 50%;

      &::after {
        content: ' ';
        position: absolute;
        left: 7px;
        top: 1px;
        width: 16px;
        border-top: 2px dotted #D9D8D7;
      }

      &:last-child {
        margin: 0;

        &::after {
          position: initial;
          width: 0;
        }
      }
    }

    &.status-1 {
      .s-1 {
        border-color: #34A853;
      }
    }

    &.status-2 {
      .s-1, .s-1::after, .s-2 {
        border-color: #34A853;
      }
    }

    &.status-3 {
      .s-1, .s-1::after, .s-2, .s-2::after, .s-3 {
        border-color: #34A853;
      }
    }

    &.status-4 {
      .s-1, .s-1::after, .s-2, .s-2::after, .s-3, .s-3::after, .s-4 {
        border-color: #34A853;
      }
    }

    &.status-5 {
      .s-1, .s-1::after, .s-2, .s-2::after, .s-3, .s-3::after, .s-4, .s-4::after, .s-5 {
        border-color: #34A853;
      }
    }
  }

  .status-tag {
    font-size: 14px;
    font-weight: 400;
    height: 24px;
    padding: 0 6px;
    line-height: 24px;
    display: inline-block;
    text-align: center;
    margin: 0 10px;

    &.toSubmitted {
      background: rgba(251, 189, 4, 0.08);
      color: rgb(251, 189, 4);
    }

    &.submitted {
      background: rgba(67, 133, 244, 0.08);
      color: rgb(67, 133, 244);
    }

    &.accepted {
      background: rgba(255, 102, 0, 0.08);
      color: rgb(255, 102, 0);
    }

    &.test {
      background: rgba(234, 67, 54, 0.08);
      color: rgb(234, 67, 54);
    }

    &.completed {
      background: rgba(52, 168, 83, 0.08);
      color: rgb(52, 168, 83);
    }

    &.cancelled {
      background: rgba(165, 165, 165, 0.08);
      color: rgb(165, 165, 165);
    }
  }
</style>
