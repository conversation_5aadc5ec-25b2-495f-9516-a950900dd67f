<template>
  <div class="sgs-box test_result_div">
    <!-- <div class="sgs-group">
            <h3>{{$t('testResult.title')}}</h3>
            <div class="right">
                <el-button type="primary" size="small" v-if="testResultSaveFlag" @click="saveReviewReport">
                    {{$t('operation.save')}}11
                </el-button>
            </div>
        </div> -->
    <el-form
      label-position="left"
      :model="trfReport"
      label-width="170px"
      size="medium"
      class="demo-form-inline sgs-form"
      style="margin-bottom: 30px"
      @submit.native.prevent
    >
      <el-card
        class="sgs-box"
        style="margin-top: -5px; padding: 0 12px;"
        v-for="report in reports"
        shadow="always"
      >
        <!--<div slot="header" class="clearfix">
                    <h4><span>{{$t('report.title')}}:</span> {{report.reportNo}}</h4>
                </div>-->
        <div class="sgs-group">
          <h4 class="sgs-title title-wrap">
            {{ $t("report.title") }}: <span> {{ report.reportNo }}</span>
            <report-confirm-status
              :reportInfo="report"
              :reportConfirmFlag="report.reportConfirmFlag"
              @confirmReportSuccess="confirmReportSuccess"
            />
            <span v-if="report.reportFiles" v-for="file in report.reportFiles">
              <img
                src="/img/pdf.png"
                v-if="file.languageId == 2"
                style="width: 20px; cursor: pointer; margin-left: 20px"
                title="Download Report"
                @click="downloadReport(file.cloudId)"
              />
              <img
                src="/img/pdf.png"
                v-else
                style="width: 20px; cursor: pointer; margin-left: 20px"
                title="Download Report"
                @click="downloadReport(file.cloudId)"
              />
            </span>
            <span
              style="
                border: 1px solid #e6e6e6;
                margin-left: 20px;
                border-radius: 2px;
              "
            >
              <el-button
                :icon="getCommentIcon(report)"
                type="text"
                style="padding: 2px 5px"
                @click="showReportComment(report, report, 1)"
                >{{ $t("comment.commentName") }}</el-button
              >
            </span>
          </h4>
        </div>

        <!-- <span style="display: none;">{{report.trfNo = trfNo}}</span>-->
        <el-row :gutter="20" class="report-box">
          <el-col :span="12">
            <el-form-item clearable filterable :label="$t('report.conclusion')">
              <div class="field-content">
                  <el-tag
                          size="small"
                          v-if="report.customerConclusion"
                          :type="getDicLabel2(report).type"
                  >
                      <el-tooltip :content="report.customerConclusion" placement="top">
                          <div class="conclusionTag">{{ $t(getDicLabel2(report).label)}}</div>
                      </el-tooltip>
                  </el-tag>
              </div>
            </el-form-item>
          </el-col>
          <!--<el-col :span="12">
                        <el-form-item clearable filterable :label="$t('trfList.reportNo')">
                            <span>{{report.reportNo}}</span>
                        </el-form-item>
                    </el-col>-->
          <el-col :span="12">
            <el-form-item
              clearable
              filterable
              :label="$t('report.testStartDate')"
            >
              <div class="field-content">{{report.reportStartDate | datefmt('YYYY-MM-DD')}}</div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              clearable
              filterable
              :label="$t('report.reviewConclusion')"
            >
            <div class="field-content" v-if="!saveReviewReportFlag || !testResultSaveFlag || isFinalizing">{{ getReviewValue(report.customerReviewConclusion) }}</div>
            <div class="field-content-form" v-else>
              <el-tooltip 
                :content="report.customerReviewConclusion" 
                placement="top" 
                :disabled="!report.customerReviewConclusion"
              >
                <el-select
                  clearable
                  filterable
                  v-model="report.customerReviewConclusion"
                  @change="saveReviewReportColusion(report)"
                  class="reviewConclusion"
                  style="width: 150px"
                  :placeholder="$t('operation.selected')"
                  :no-data-text="$t('NoData')"
                >
                  <el-option
                    v-for="conclusion in reviewConclusionData"
                    :label="conclusion.reviewValue"
                    :value="conclusion.reviewCode"
                  ></el-option>
                </el-select>
              </el-tooltip>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              clearable
              filterable
              :label="$t('report.reportIssuedDate')"
            >
              <div class="field-content">{{report.approvedDate | datefmt('YYYY-MM-DD')}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              clearable
              filterable
              :label="$t('report.reviewProgress')"
            >
              <div class="field-content">
                <el-tooltip :content="$t(getTagContent(report))" placement="top">
                  <el-tag
                    type="info"
                    size="small"
                  >
                    <div class="conclusionTag">
                      {{
                        $t(
                          "report.reviewConclusionStatus_" +
                            report.reviewConclusionStatus
                        )
                      }}
                    </div>
                  </el-tag>
                </el-tooltip>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              clearable
              filterable
              :label="$t('report.marketSegment')"
            >
              <div class="field-content">{{ report.marketSegment }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="report.rootReportNo">
            <el-form-item :label="$t('report.originalReportNo')">
              <div class="field-content">{{ report.rootReportNo }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="report.rslStatus">
            <el-form-item :label="$t('report.rslStatus')">
              <div class="field-content">{{ report.rslStatus }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="report.reportVersion">
            <el-form-item :label="$t('report.reportVersion')">
              <!-- 只有CG0000219 需要-1 ，其他不限制-->
              <div class="field-content">
                {{
                  trf.trfCustomer.buyerCustomerGroupCode == "CG0000219"
                    ? report.reportVersion <= 0
                      ? 0
                      : report.reportVersion - 1
                    : report.reportVersion
                }}
              </div>
            </el-form-item>
          </el-col>

          <!--
                        <el-col :span="12" v-if="report.serviceStartDate">
                                                <el-form-item :label="$t('report.serviceStartDate')">
                                                    <el-date-picker type="datetime"
                                                                    format="yyyy-MM-dd HH:mm:ss"
                                                                    :value="currentTzDate(report.serviceStartDate)" disabled="true"
                                                                    style="width: 100%;"></el-date-picker>
                                                </el-form-item>
                                            </el-col>
                     -->

          <el-col :span="12" v-if="report.serviceEndDate">
            <el-form-item :label="$t('report.serviceEndDate')">
              <div class="field-content">{{report.serviceEndDate | datefmt('YYYY-MM-DD')}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="report.remark">
            <el-form-item :label="$t('report.remark')">
              <el-tooltip :content="report.remark" placement="top">
                <div class="field-content">{{ report.remark }}</div>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          v-if="report.newTestLineList && report.newTestLineList.length > 0"
        >
          <common-table
            v-loading="loading"
            :element-loading-text="$t('loading')"
            border
            fit
            style="width: 100%"
            row-key="id"
            ref="trfReportTestLineTable"
            stripe
            height="300"
            :size="tableOption.size"
            :data="report.displayedTestLineList"
            :option="tableOption"
            :filters="tableOption.filters"
            @sortBy="changeSort"
          >
            <template slot="customerReviewConclusion" slot-scope="scope">
              <span v-if="!saveReviewReportFlag || !testResultSaveFlag || isFinalizing">
                {{ getReviewValue(scope.row.customerReviewConclusion) }}
              </span>
              <el-select
                clearable
                filterable
                v-model="scope.row.customerReviewConclusion"
                @change="changeReviewCode(report, scope.row)"
                v-else
                class="reviewConclusion"
                :placeholder="$t('operation.pleaseSelect')"
                :no-data-text="$t('NoData')"
              >
                <el-option
                  v-for="conclusion in reviewConclusionData"
                  :label="conclusion.reviewValue"
                  :value="conclusion.reviewCode"
                ></el-option>
              </el-select>
              
            </template>
            <template slot="customerConclusion" slot-scope="scope">
              <el-tag
                size="small"
                v-if="scope.row.customerConclusion"
                :type="getDicLabel2(scope.row).type"
              >
                <el-tooltip :content="scope.row.customerConclusion" placement="top">
                  <div class="conclusionTag">{{ $t(getDicLabel2(scope.row).label)}}</div>
                </el-tooltip>
              </el-tag>
            </template>
            <template slot="actionColumn" slot-scope="{row}">
              <div style="width: 100%; text-align: center" v-if="row.testId">
                  <el-button
                    type="text"
                    :icon="getCommentIcon(row)"
                    @click="showReportComment(report, row, 2)"
                  ></el-button>
                </div>
            </template>
          </common-table>
        </el-row>

        <comment-attach
          showDownloadAll
          :showUpload="permissionList.uploadFileBtn"
          :upload-limit="5"
          :upload-object-id="report.id"
          @uploadSuccess="saveReviewReport(report)"
          :trf="trf"
          :table-data="getReportAllAtt(report)"
        ></comment-attach>
      </el-card>
    </el-form>

    <el-row class="sgs-footer" v-if="permissionList.testResultReEditBtn">
      <el-button type="primary" @click="rework">
        {{
          $store.state.common.language == "zh-CN"
            ? "重新编辑测试结果"
            : "Rework"
        }}
      </el-button>
    </el-row>

    <el-dialog
      :title="$t('operation.detail')"
      :close-on-click-modal="false"
      :visible.sync="commentDetailDialogFormVisible"
      top="20vh"
      width="55%"
    >
      <el-form :model="commentDetail" ref="commentForm" label-width="160px">
        <el-form-item :label="$t('comment.emailAddress')">
          <el-select
            v-model="emailAddressesValue"
            disabled="true"
            multiple
            filterable
            allow-create
            default-first-option
            size="small"
            style="width: 100%"
            :placeholder="$t('operation.pleaseSelect')"
            :no-data-text="$t('NoData')"
          >
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('comment.comment')">
          <el-input
            type="textarea"
            v-model="commentDetail.comment"
            clearable
            disabled="true"
            autocomplete="off"
            :maxlength="5000"
            show-word-limit="true"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('attachment.title')">
          <span v-for="file in commentDetail.attachments">
            <img
              src="/img/pdf.png"
              style="width: 20px; cursor: pointer; margin-left: 20px"
              title="Download File"
              @click="downloadReport(file.fileUrl)"
            />
          </span>
          <!-- <a  @click="downloadReport(file.cloudId)" v-text="file.fileName"></a>-->
        </el-form-item>
        <el-form-item :label="$t('comment.applicantVisible')">
          <el-checkbox-group v-model="supplierShowValue" disabled="true">
            <el-checkbox name="type"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!--新增comment 弹框-->
    <el-dialog
      :title="$t('comment.addComment')"
      :close-on-click-modal="false"
      :visible.sync="commentDialogFormVisible"
      top="20vh"
      width="55%"
    >
      <el-collapse
        v-model="activeNames"
        accordion
        @change="handleChange"
        style="margin-bottom: 10px; margin-left: 40px"
      >
        <el-collapse-item :title="$t('comment.viewAllComments')">
          <el-table
            :data="hisComments"
            ref="multipleTable"
            :row-key="getRowKeys"
            width="100%"
            @selection-change="commentChange"
            :empty-text="$t('NoData')"
          >
            <el-table-column
              type="selection"
              fixed
              :reserve-selection="true"
              width="50"
            ></el-table-column>
            <el-table-column type="index" fixed label="#" width="50">
            </el-table-column>
            <el-table-column
              prop="newCreateTime"
              :label="$t('common.time')"
              width="180"
            >
              <template slot-scope="scope">
                <span>
                  {{
                    currentTz_YMD($lodash.get(scope.row, "newCreateTime", ""))
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="comment"
              :show-overflow-tooltip="true"
              :label="$t('comment.commentContent')"
            >
            </el-table-column>
            <el-table-column
              prop="createUser"
              :label="$t('user.createBy')"
              width="120"
            >
            </el-table-column>
          </el-table>
          <div class="sgs-group" style="min-height: 12px">
            <div class="right">
              <el-button type="primary" size="small" @click="copyComment">
                {{ $t("comment.copyComment") }}
              </el-button>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
      <el-form
        :model="commentForm"
        :rules="commentFormRules"
        ref="commentForm"
        label-width="160px"
      >
        <el-form-item :label="$t('comment.comment')" prop="comment">
          <el-input
            type="textarea"
            v-model="commentForm.comment"
            clearable
            autocomplete="off"
            :maxlength="5000"
            show-word-limit="true"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('comment.emailGroup')">
          <el-select
            v-model="emailGroupSelected"
            multiple
            style="width: 100%; background: white"
            @change="handleEmailGroupSelectChange"
            :placeholder="$t('operation.pleaseSelect')"
            :no-data-text="$t('NoData')"
          >
            <el-option
              v-for="item in emailGroupList"
              :key="item.id"
              :label="item.emailGroupName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('comment.emailAddress')">
          <el-select
            v-model="emailArray"
            @change="contactChange"
            multiple
            filterable
            allow-create
            default-first-option
            size="small"
            style="width: 100%"
            :placeholder="$t('operation.pleaseSelect')"
            :no-data-text="$t('NoData')"
          >
            <el-option
              v-for="(contact, index) in customerContactData"
              :label="contact.contactEmail"
              :value="contact.contactEmail"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('communicationLog.pdfReport')">
          <el-checkbox-group v-model="reportPdfSend">
            <el-checkbox
              checked="checked"
              :title="item.fileName"
              v-for="item in reportPdf"
              :key="item.cloudId"
              :label="item"
              >{{ item.fileName | reportPdfNameFilter }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('comment.applicantVisible')">
          <el-checkbox-group v-model="isSupplierShowFlag">
            <el-checkbox name="type"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('trf.attachment')">
          <el-upload
            ref="upload"
            :title="fileTitle"
            class="customerUpload"
            action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
            :on-success="uploadSuccess"
            :on-remove="fileRemove"
            :on-change="fileProgress"
            :file-list="fileList"
            :limit="1"
            :show-file-list="true"
          >
            <el-button size="small" type="primary">{{
              $t("operation.upload")
            }}</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="commentDialogFormVisible = false">{{
          $t("operation.cancel")
        }}</el-button>
        <el-button
          type="primary"
          :loading="savaBtnLoading"
          @click="saveComment"
        >
          {{ $t("operation.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <el-row class="sgs-footer">
      <el-button
        type="primary"
        :loading="isFinalizing"
        v-if="saveReviewReportFlag && testResultSaveFlag"
        @click="finalizeReviewReport"
      >
        {{ $t("operation.finalizeReview") }}
      </el-button>
      <!-- <el-button type="primary" v-if="saveReviewReportFlag && testResultSaveFlag" @click="saveReviewReport">
                {{$t('operation.save')}}
            </el-button>-->
    </el-row>

    <el-dialog
      :title="$t('comment.addComment')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-dialog-drag
      :lock-scroll="false"
      show-close
      custom-class="comment_dia"
      :visible.sync="showComment"
      width="60%"
    >
      <chat-comment
        :comment-data="commentObj"
        :email-group-list="emailGroupList"
        v-if="showComment"
        @saveSuccess="(showComment = false), onLoad()"
      >
      </chat-comment>
    </el-dialog>
  </div>
</template>

<script>
import {
  getReport,
  saveReport,
  downLoadFile,
  queryTrfReviewConclusion,
  addComment,
  queryTrfCustomerContact,
  finalizeReport,
  saveReviewConclusionReport,
  getReportMatrixMode,
} from "@/api/trf/trf";
import { getNotPageByUser } from "@/api/customer/customerEmailGroup";
import moment from "moment";
import { deepClone } from "@/util/util";
import { validatenull, validateEmail } from "../../../util/validate";
import { mapGetters } from "vuex";
import { tzFormChina } from "@/util/datetimeUtils";
import ChatComment from "./chatComment";
import CommentAttach from "./commentAttach";
import { queryBuSetting, getSgsCustomer } from "@/api/common";
import { queryCustomerByBuCodeAndCustomerId } from "../../../api/customer/customer";
import ReportConfirmStatus from "@/components/reportConfirmStatus/ReportConfirmStatus.vue";
import commonTable from "@/components/tableList/commonTable.vue";

Vue.prototype.$moment = moment;
Vue.filter("datefmt", function (input, fmtstring) {
  if (input == null || input == "" || input == undefined) {
    return "";
  }
  return moment(input).format(fmtstring);
});
export default {
  name: "testResult",
  components: { CommentAttach, ChatComment, ReportConfirmStatus, commonTable },
  props: {
    trf: Object,
    trfNo: String,
    testResultSaveFlag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showColumns: [],
      showPPNameColumn: false,
      reportSampleNoMap: {},
      dmTestResultFilter: "",
      fileTitle: "",
      fileList: [],
      commentDetail: {},
      contactEmailArray: [],
      emailArray: [],
      emailSelected: [],
      emailSelectedTemp: [],
      emailGroupList: [],
      emailGroupSelected: [], //选择的邮件组数据
      hisComments: [],
      activeNames: [],
      selcommentsData: [],
      report: {
        reportNo: "",
      },
      savaBtnLoading: false,
      isSupplierShowFlag: false,
      reportPdf: [],
      reportPdfSend: [],
      commentForm: {
        contactId: null,
        comment: "",
        isSupplierShow: "",
        contactName: "",
        contactEmail: "",
        attachments: [],
      },
      showComment: false,
      commentObj: {
        report: null,
        data: null,
        type: null,
      },
      commentDialogFormVisible: false,
      commentDetailDialogFormVisible: false,
      reports: [],
      form: {},
      trfReport: {},
      reviewConclusionData: [],
      sort: { descs: "update_time" },
      selectionList: [],
      selectRow: {},
      query: {},
      getRowKeys(row) {
        return row.createTime;
      },
      page: {
        pageSize: 100,
        currentPage: 1,
        total: 0,
      },
      commentFormRules: {
        comment: [
          {
            required: true,
            message: this.$t("communicationLog.validate.comment"),
            trigger: "blur",
          },
          /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
        ],
      },
      isFinalizing: false,

      tableOption: {
        size: "small",
        border: true,
        menuShow: false,
        filters: {},
        showSortIcon: true,
        sort: {
          sortBy: "",
          sortOrder: "",
        },
        index: true,
        action: true,
        actionWidth: 80,
        column: [
          {
            prop: "categoryCode",
            label: "report.testLine.categoryCode",
            hide: !(
              Array.isArray(this.showColumns) &&
              this.showColumns.includes("categoryCode")
            ),
            filter: true,
            slot: false,
            width: 250,
            type: "Input",
          },
          {
            prop: "ppName",
            label: "report.testLine.pp",
            hide: !this.showPPNameColumn,
            filter: true,
            slot: false,
            width: 250,
            type: "Input",
          },
          {
            prop: "evaluationAlias",
            label: "report.testLine.testName",
            hide: false,
            filter: true,
            slot: false,
            width: 250,
            type: "Input",
          },
          {
            prop: "citationName",
            label: "report.testLine.citation",
            hide: !(
              Array.isArray(this.showColumns) &&
              this.showColumns.includes("citation")
            ),
            filter: true,
            slot: false,
            width: 250,
            type: "Input",
          },
          {
            prop: "sampleNo",
            label: "report.testLine.sampleNo",
            hide:
              !this.report ||
              !this.report.reportNo ||
              !this.reportSampleNoMap[this.report.reportNo],
            filter: true,
            slot: false,
            width: 200,
            type: "Input",
          },
          {
            prop: "customerConclusion",
            label: "report.testLine.testResult",
            hide: false,
            filter: true,
            slot: true,
            width: 200,
            type: "Input",
            dicData: [
                {label: 'PASS', value: 'Pass',tag2:true,type:'success'},
                {label: 'FAIL', value: 'Fail',tag2:true,type:'danger'},
            ],
          },
          {
            prop: "customerReviewConclusion",
            label: "report.testLine.resultReview",
            hide: false,
            filter: true,
            slot: true,
            width: 200,
            type: "Select",
            dicData: (this.reviewConclusionData || []).map((item) => {
              return {
                label: item.reviewValue,
                value: item.reviewCode,
              }; 
            })
          },
          {
            prop: "componentName",
            label: "report.testLine.component",
            hide: false,
            filter: true,
            slot: false,
            width: 200,
            type: "Input",
          },
          {
            prop: "color",
            label: "report.testLine.color",
            hide: false,
            filter: true,
            slot: false,
            width: 200,
            type: "Input",
          },
          {
            prop: "remark",
            label: "report.testLine.remark",
            hide: false,
            filter: true,
            slot: false,
            type: "Input",
          },
        ],
      },
    };
  },
  filters: {
    reportPdfNameFilter: function (value) {
      value = value.toString();
      if (value.length > 50) {
        value = value.slice(0, 50) + "...";
      }
      return value;
    },
  },
  created() {
    this.onLoad();
    this.loadBuSetting();
    this.queryEmailGroupData();
  },
  watch: {
    dmTestResultFilter: {
      immediate: false,
      handler(newV) {
        this.computeShowTestLineData();
      },
    },
    isSupplierShowFlag: function (newVal) {
      this.$set(this.commentForm, "isSupplierShow", 0);
      if (newVal) {
        this.$set(this.commentForm, "isSupplierShow", 1);
      }
    },
    'tableOption.filters': {
      handler(newVal, oldVal) {
        console.log('watch----',newVal)
        this.filterTestLineList(); // 调用过滤方法
      },
      deep: true,
    }
  },
  computed: {
    ...mapGetters(["userInfo", "language", "permission"]),
    permissionList() {
      return {
        addCommentBtn: this.vaildData(
          this.permission["sgs:testResult:comment:add"],
          false
        ),
        viewCommentBtn: this.vaildData(
          this.permission["sgs:testResult:comment:view"],
          false
        ),
        testResultReEditBtn: this.vaildData(
          this.permission["sgs:trf:testResult:reEdit:btn"],
          false
        ),
        uploadFileBtn: this.vaildData(
          this.permission["sgs:trf:testResult:save:btn"],
          false
        ),
      };
    },
    emailAddressesValue: {
      get() {
        if (this.commentDetail.contactEmail) {
          return this.$lodash.split(this.commentDetail.contactEmail, ",");
        }
        return [];
      },
      set(val) {
        this.$set(
          this.commentDetail,
          "contactEmail",
          this.$lodash.join(val, ",")
        );
      },
    },
    supplierShowValue: {
      get() {
        if (this.commentDetail.isSupplierShow == 1) {
          return true;
        }
        return [];
      },
      set(val) {
        this.$set(this.commentDetail, "isSupplierShow", 0);
        if (val) {
          this.$set(this.commentDetail, "isSupplierShow", 1);
        }
      },
    },
    saveReviewReportFlag() {
      //判断当前用户是否有当前订单buyer权限
      return (
        this.userInfo.customerGroupCode ==
        this.trf.trfCustomer.buyerCustomerGroupCode
      );
    },
  },
  methods: {
    changeSort(sort){
      this.tableOption.sort.sortOrder = sort.prop;
      this.tableOption.sort.sortBy = sort.orderBy;
      // 对每个 report 的 displayedTestLineList 进行排序
      this.reports.forEach((report) => {
        if (Array.isArray(report.displayedTestLineList)) {
          if (sort.orderBy === '') {
            // 当 sort.orderBy 为空时，恢复为 newTestLineList 的默认顺序
            this.$set(report, 'displayedTestLineList', [...report.newTestLineList]);
          } else {
            report.displayedTestLineList.sort((a, b) => {
              const valueA = a[sort.prop];
              const valueB = b[sort.prop];

              if (typeof valueA === 'string' && typeof valueB === 'string') {
                // 字符串比较
                return sort.orderBy === 'Asc' 
                  ? valueA.localeCompare(valueB) 
                  : valueB.localeCompare(valueA);
              } else if (typeof valueA === 'number' && typeof valueB === 'number') {
                // 数字比较
                return sort.orderBy === 'Asc' 
                  ? valueA - valueB 
                  : valueB - valueA;
              }
              return 0;
            });
          }
        }
      });
    },
    filterTestLineList() {
      const { filters } = this.tableOption;
      this.reports.forEach((report) => {
        if (!Array.isArray(report.newTestLineList)) {
          this.$set(report, 'displayedTestLineList', []);
          return;
        }
        let filteredData = [...report.newTestLineList]; // 复制原始数据

        // 遍历过滤条件
        Object.keys(filters).forEach((key) => {
          const filterValue = filters[key];
          if (filterValue) {
            filteredData = filteredData.filter((item) => {
              const itemValue = item[key];
              if (typeof itemValue === 'string') {
                return itemValue.toLowerCase().includes(filterValue.toLowerCase());
              }
              return itemValue === filterValue;
            });
          }
        });

        this.$set(report, 'displayedTestLineList', filteredData); // 更新过滤后的数据
      });
    },
    currentTzDate(val) {
      if (!val) return "";
      let value = tzFormChina(val, "YYYY-MM-DD HH:mm:ss");
      return value;
    },
    currentTz_YMD(val) {
      if (!val) return "";
      let value = tzFormChina(val, "YYYY-MM-DD HH:mm:ss");
      return moment(value).format("YYYY-MM-DD");
    },
    rework() {
      let query = {
        trfNo: this.trf.trfNo,
        trfStatus: this.trf.trfStatus,
        createdBy: this.trf.createUser,
        id: this.trf.id,
        generalOrderID: this.trf.id,
        trfSubmissionDate: this.trf.trfSubmissionDate,
        customerGroupCode: this.trf.trfCustomer.buyerCustomerGroupCode,
        customerGroupName: this.trf.trfCustomer.buyerCustomerGroupName,
        applicantCustomerNameEn: "Applicant",
        applicantCustomerNameCn: "",
        agentCustomerNameEn: "",
        agentCustomerNameCn: "",
        labName: this.trf.trfLab.labName,
        labCode: this.trf.trfLab.labCode,
        sampleReceivedDate: this.trf.sampleReceiveDate,
        productId: this.trf.trfProduct.id,
        productLineId: this.trf.productLineId,
        productLineCode: this.trf.productLineCode,
      };
      const routerUrl = this.$router.resolve({
        path: "/detail",
        query,
      });
      window.open(routerUrl.href, "_blank");
    },
    contactChange(emailList) {
      let pasteEmailArr = this.emailArray;
      emailList.find((item1) => {
        let validateRes = validateEmail(item1);
        if (validateRes) {
          if (pasteEmailArr.indexOf(item1) == -1) {
            pasteEmailArr.push(item1);
          }
        } else {
          pasteEmailArr.splice(pasteEmailArr.indexOf(item1), 1);
        }
      });
      this.emailArray = pasteEmailArr;
    },
    viewDetail(row) {
      this.commentDetail = row;
      this.commentDetailDialogFormVisible = true;
    },
    copyComment() {
      if (!validatenull(this.selcommentsData)) {
        var comment = this.commentForm.comment;
        var date = moment().format("YYYY-MM-DD");
        if (!validatenull(comment)) {
          comment = comment + "\n";
        }
        this.selcommentsData.find((item1) => {
          comment +=
            date + " " + item1.comment + " by " + this.userInfo.userName + "\n";
        });
        comment = comment.substring(0, comment.lastIndexOf("\n"));
        this.commentForm.comment = comment;
      }
    },
    seedComment() {
      if (validatenull(this.selcommentsData)) {
        this.$notify({
          title: this.$t("tip"),
          message: "请选择需要发送的comment",
          type: "warning",
        });
      }
    },

    changeReviewCode(report, testLineItem) {
      //report里遍历testLineList中的reviewCode对比reviewConclusionData中reviewCode的priorityLevel
      // 优先级最高的并赋值给report.customerReviewConclusion
      var maxPriorityLevel = 0;
      if (report.testLineList.length > 0) {
        report.testLineList.forEach((item) => {
          const result = this.reviewConclusionData.find(
            (data) => data.reviewCode == item.customerReviewConclusion
          );
          if (!validatenull(result)) {
            if (result.priorityLevel > maxPriorityLevel) {
              maxPriorityLevel = result.priorityLevel;
              report.customerReviewConclusion = result.reviewCode;
            }
          }
        });
        const newTestLineList = [];
        // report.testLineList.map((item) => {
        //   if (item.id === testLineItem.id) {
        //     newTestLineList.push(item);
        //   }
        // });
        report.displayedTestLineList.map((item) => {
          if (item.id === testLineItem.id) {
            newTestLineList.push(item);
          }
        });
        let newReport = JSON.parse(JSON.stringify(report));
        newReport.testLineList = newTestLineList;

        // 做保存
        this.saveReviewReport(newReport);
      }
    },
    commentChange(val) {
      this.selcommentsData = val;
    },
    saveComment() {
      console.log("开始提交");
      this.$refs["commentForm"].validate((valid) => {
        console.log("验证结果" + valid);
        if (valid) {
          this.$set(
            this.commentForm,
            "createTime",
            moment().format("YYYY-MM-DD")
          );
          this.$set(this.commentForm, "trfReportId", this.report.id);
          this.$set(this.commentForm, "reportNo", this.report.reportNo);
          var emailStr = "";
          this.emailArray.find((item) => {
            emailStr += item + ",";
          });
          if (emailStr.length > 0) {
            emailStr = emailStr.substr(0, emailStr.length - 1);
          }
          var emailGroupStr = "";
          this.emailGroupSelected.find((item) => {
            emailGroupStr += item + ",";
          });
          if (emailGroupStr.length > 0) {
            emailGroupStr = emailGroupStr.substr(0, emailGroupStr.length - 1);
          }
          var reports = [];
          if (this.reportPdfSend) {
            this.reportPdfSend.find((item) => {
              reports.push(item);
            });
          }
          this.$set(this.commentForm, "reports", reports);
          this.$set(this.commentForm, "contactEmail", emailStr);
          this.$set(this.commentForm, "emailGroup", emailGroupStr);
          addComment(this.commentForm).then((res) => {
            this.$message({
              type: "success",
              message: this.$t("api.success"),
            });
            this.onLoad();
          });
          //this.report.comments.push(this.commentForm);
          this.commentDialogFormVisible = false;
        } else {
          console.log("表单验证结果" + valid);
        }
      });
    },
    addComment(val) {
      this.commentForm = {};
      this.emailGroupSelected = [];
      this.selcommentsData = [];
      this.isSupplierShowFlag = false;
      this.reportPdfSend = [];
      this.emailArray = [];
      this.$set(this.commentForm, "comment", "");
      this.$set(this.commentForm, "isSupplierShow", 0);
      this.$set(this.commentForm, "attachments", []);
      this.$set(this.commentForm, "contactEmail", "");

      this.report = val;
      this.hisComments = deepClone(val.comments);
      this.$set(this.commentForm, "trfId", val.trfId);
      this.$set(this.commentForm, "reportNo", val.reportNo);
      this.$set(this.commentForm, "trfReportId", val.id);
      this.reportPdf = val.reportFiles;
      this.fileList = [];
      this.fileTitle = "";
      this.commentDialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
      });
    },
    handleEmailGroupSelectChange(emailGroupIds) {
      var vm = this;
      //将选择的客户组中的email地址放入Recipient(s) from Address Book中
      if (!validatenull(emailGroupIds)) {
        emailGroupIds.find((val) => {
          let obj = {};
          obj = vm.emailGroupList.find((item) => {
            return item.id === val;
          });
          if (!validatenull(obj)) {
            console.log(vm.emailArray);
            obj.contacts.find((item) => {
              if (vm.emailArray.indexOf(item.contactEmail) == -1) {
                vm.emailArray.push(item.contactEmail);
              }
            });
          }
        });
      }
      console.log(this.emailGroupSelected);
    },
    //查询当前公司的邮件组数据
    queryEmailGroupData() {
      var params = {};
      getNotPageByUser(
        Object.assign(params, this.emailGroupParam, this.sort)
      ).then((res) => {
        this.emailGroupList = res.data.data;
        //查询申请方邮件，并添加至申请方邮件组
        this.queryTrfCustomerContact();
      });
    },
    queryTrfCustomerContact() {
      queryTrfCustomerContact(this.trfNo).then((res) => {
        if (!validatenull(res.data.data)) {
          if (!validatenull(res.data.data.applyContactEmail)) {
            let applicantObj = {};
            applicantObj.emailGroupName = "Applicant";
            applicantObj.id = "applicant_id";
            let applicantEmails = this.$lodash.split(
              res.data.data.applyContactEmail,
              ","
            );
            let contacts = [];
            if (!validatenull(applicantEmails)) {
              applicantEmails.find((item) => {
                let contact = {};
                contact.contactEmail = item;
                contacts.push(contact);
              });
            }
            applicantObj.contacts = contacts;
            this.emailGroupList.unshift(applicantObj);
          }
        }
      });
    },
    //文件上传时钩子函数
    async fileProgress(event, file, fileList) {
      if (event.status == "ready") {
        this.savaBtnLoading = true;
      } else {
        this.savaBtnLoading = false;
      }
    },
    uploadSuccess(res, file) {
      this.savaBtnLoading = false;
      this.commentForm.attachments = [];
      const attachment = {
        attachmentId: res.data[0].cloudID,
        fileUrl: res.data[0].path,
        fileName: file.name,
      };
      this.fileTitle = file.name;
      this.commentForm.attachments.push(attachment);
    },
    fileRemove() {
      this.savaBtnLoading = false;
      this.fileList = [];
      this.$refs.upload.clearFiles();
    },
    computeShowTestLineData() {
      this.reports.forEach((re) => {
        let newTestLineList = this.computerReportTestLineList(re.testLineList);
        this.$set(re, "newTestLineList", newTestLineList);
        this.$set(re, "displayedTestLineList", newTestLineList);
      });
    },
    computerReportTestLineList(testLineList) {
      if (!this.dmTestResultFilter) {
        return testLineList;
      }
      let conclusionShowPP = this.dmTestResultFilter.split("+");
      //目前有All,fail，No NA
      let showConclusionData = conclusionShowPP[0].toLowerCase();
      let showPPName = conclusionShowPP[1];
      this.showPPNameColumn = showPPName.toLowerCase() == "pp";
      this.tableOption.column.forEach((item) => {
        if (item.prop == "ppName") {
          item.hide = !this.showPPNameColumn;
        }
      });
      console.log('showPPNameColumn',this.showPPNameColumn)
      //开始过滤数据
      let list = testLineList.filter((tl) => {
        let { conclusion } = tl;
        conclusion = (conclusion || "").toLowerCase();
        //只展示fail
        if (showConclusionData == "fail") {
          return conclusion == "fail";
        }
        //不展示NA的测试项
        if (showConclusionData == "no na") {
          return conclusion != "na";
        }
        return true;
      });
      return list;
    },
    loadColumnConfig() {
      let { customerList } = this.trf;
      if (!customerList) {
        return;
      }
      let cus = customerList.filter((c) => c.customerUsage - 0 == 3);
      if (!cus || cus.length == 0) {
        return;
      }
      let { bossNo } = cus[0];
      if (!bossNo) {
        return;
      }
      //通过bossno 获取customerId
      let param = {
        number: bossNo,
      };
      getSgsCustomer(param).then((res) => {
        if (res.data && res.data.rows) {
          let { rows } = res.data;
          if (rows && rows.length > 0) {
            let resCstomerId = rows[0].customerId;
            this.initCustomerExt(resCstomerId);
          }
        }
      });
    },
    initCustomerExt(customerId) {
      let params = {
        buCode: this.trf.productLineCode,
        customerId,
      };
      queryCustomerByBuCodeAndCustomerId(params).then((res) => {
        if (res.data && res.data.customerId) {
          let { customerExtNewList } = res.data;
          let cust = customerExtNewList.find(
            (c) => c.customerId == customerId && c.locationCode == "CN"
          );
          if (!cust) {
            return;
          }
          let { dmTestResultFilter } = cust;
          this.dmTestResultFilter = dmTestResultFilter;
        }
      });
    },
    onLoad() {
      const loading = this.$loading({
        lock: false,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      getReport(this.trfNo)
        .then((res) => {
          loading.close();
          this.reports = res.data.data;
          this.reports.forEach((r) => {
            this.$set(r, "newTestLineList", r.testLineList);
            this.$set(r, "displayedTestLineList", r.testLineList); 
          });
          //this.loadShowSampleSetting();
          this.loadReview();
          this.loadColumnConfig();
        })
        .catch((error) => {
          loading.close();
        });
    },
    loadReview() {
      //根据TRF的买家加载对应的reviewConclusion
      queryTrfReviewConclusion(this.trfNo).then((res) => {
        this.reviewConclusionData = res.data.data.customerReviewConclusions;
        // 更新 tableOption 中 customerReviewConclusion 列的 dicData
        const customerReviewConclusionColumn = this.tableOption.column.find(
          (col) => col.prop === "customerReviewConclusion"
        );
        if (customerReviewConclusionColumn) {
          customerReviewConclusionColumn.dicData = (
            this.reviewConclusionData || []
          ).map((item) => {
            return {
              label: item.reviewValue,
              value: item.reviewCode,
            };
          });
        }
        this.reviewConclusionMappings = res.data.data.reviewConclusionMappings;
        //report.customerReviewConclusion 需要根据reports里的report.customerConclusion 与 mappings数据匹配赋值
        this.reports.forEach((report) => {
          //找到reviewConclusionMappings list中里type为report的数据并返回
          // 找到该条数据的conclusion 然后与report.customerConclusion

          const resultR = this.reviewConclusionMappings.find(
            (mapping1) =>
              mapping1.type == "report" &&
              mapping1.conclusion == report.customerConclusion
          );
          if (
            resultR &&
            (!report.customerReviewConclusion ||
              report.customerReviewConclusion == null)
          ) {
            console.log(
              `Item found in both lists with code ${report.customerReviewConclusion}`
            );
            report.customerReviewConclusion = resultR.reviewConclusion;
          }

          //判断testLine.testResult 与 reviewConclusionMapping数据是否匹配
          report.testLineList.forEach((testLine) => {
            var resultT = this.reviewConclusionMappings.find((mapping2) => {
              //console.log('mapping2',mapping2)
              return (
                mapping2.type == "testLine" &&
                mapping2.conclusion == testLine.customerConclusion
              );
            });
            if (
              resultT &&
              (!testLine.customerReviewConclusion ||
                testLine.customerReviewConclusion == null)
            ) {
              //console.log(`Item found in both lists with code ${testLine.customerConclusion}`);
              testLine.customerReviewConclusion = resultT.reviewConclusion;
            }
          });
        });
      });
    },
    saveReviewReport(report) {
      if (!report) {
        this.$notify.error(this.$t("api.error"));
      }
      if (this.isFinalizing) return;
      this.isFinalizing = true;
      saveReviewConclusionReport(report)
        .then((res) => {
          this.isFinalizing = false;
          this.$message({
            type: "success",
            message: this.$t("api.success"),
          });
          this.onLoad();
          //隐藏保存按钮
        })
        .catch((error) => {
          this.isFinalizing = false;
        });
    },
    saveReviewReportColusion(report) {
      console.log('saveReviewReportColusion0-----------',report)
      if (!report) {
        this.$notify.error(this.$t("api.error"));
      }
      if (this.isFinalizing) return;
      this.isFinalizing = true;
      const reportInfo = JSON.parse(JSON.stringify(report));
      delete reportInfo["testLineList"];
      saveReviewConclusionReport(reportInfo)
        .then((res) => {
          this.isFinalizing = false;
          this.$message({
            type: "success",
            message: this.$t("api.success"),
          });
          this.onLoad();
          //隐藏保存按钮
        })
        .catch((error) => {
          this.isFinalizing = false;
        });
    },
    finalizeReviewReport() {
      if (this.isFinalizing) return;
      this.isFinalizing = true;
      const reportInfo = JSON.parse(JSON.stringify(this.reports));
      reportInfo.forEach((item) => {
        delete item["testLineList"];
      });
      finalizeReport(reportInfo)
        .then((res) => {
          this.$message({
            type: "success",
            message: this.$t("api.success"),
          });
          this.onLoad();
          this.isFinalizing = false;
          //隐藏保存按钮
        })
        .catch((error) => {
          this.isFinalizing = false;
        });
    },
    downloadReport(cloudId) {
      if (cloudId != "" && cloudId != null) {
        downLoadFile(cloudId).then(
          (res) => {
            var pdfUrl = res.data.data;
            this.downLoadReportOts(pdfUrl);
          },
          (error) => {
            this.$message.error(this.$t("api.error"));
          }
        );
      }
    },
    downLoadReportOts(pdfUrl) {
      window.open(pdfUrl, "_blank");
    },
    getReportPdf() {
      /*getReportPdf(this.reports.reportNo).then(res=>{
                    console.log(res)
                })*/
    },
    getCommentIcon(reportOrTestLine) {
      let { comments, readStatus } = reportOrTestLine;
      //没有对话信息
      if (!comments || comments.length == 0) {
        return "el-icon-chat-square";
      }
      //有对话信息，已读
      if (readStatus - 0 == 1) {
        return "el-icon-s-comment";
      }
      //有对话信息，未读
      return "el-icon-warning";
    },
    /* report testline 的comment*/
    showReportComment(report, reportOrTestLine, type) {
      this.commentObj.type = type;
      this.commentObj.report = report;
      this.commentObj.data = reportOrTestLine;
      this.showComment = true;
    },
    getReportAllAtt(report) {
      let { reportFiles, allAttachments, approvedDate } = report;
      let attachmentIssueDate = this.currentTzDate(approvedDate);
      let rfAttList = [];
      reportFiles.forEach((rf) => {
        let { cloudId, approvedDate } = rf;
        let obj = Object.assign({}, rf, {
          fileUrl: cloudId,
          fileType: "Report",
          attachmentIssueDate,
        });
        rfAttList.push(obj);
      });
      return [...rfAttList, ...allAttachments];
    },
    loadShowSampleSetting() {
      let trfId = this.trf.id;
      let reportNos = this.reports.map((report) => report.reportNo);
      getReportMatrixMode(trfId, reportNos).then((res) => {
        if (res.status == 200) {
          this.reportSampleNoMap = res.data.data;
        }
      });
    },
    loadBuSetting() {
      let customerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
      let param = {
        systemId: 15,
        groupCode: "SgsSmartTRFConfig",
        productLineCode: "SGS",
        paramCode: "showTestLineTableColumn",
      };
      queryBuSetting(param)
        .then((res) => {
          if (res.status == 200 && res.data && res.data.data) {
            let { data } = res.data;
            if (!data || data.length == 0) {
              return;
            }

            data = data[0];
            let { paramValue } = data;
            try {
              let values = JSON.parse(paramValue);
              let columns = values[customerGroupCode];
              this.showColumns = columns || [];
              this.tableOption.column.forEach((item) => {
                this.showColumns.forEach((showColumn) => {
                  if (item.prop === "citationName"&& showColumn === "citation") {
                    item.hide = false;
                  }
                  if (item.prop === "categoryCode"&& showColumn === "categoryCode") {
                    item.hide = false;
                  }
                })
              });
            } catch (e) {}
          }
        })
        .catch((err) => {
          console.log("配置返回 err", err);
        });
    },
    confirmReportSuccess() {
      this.onLoad();
    },
    getTagType(status) {
      switch (status) {
        case 0:
          return 'info';
        case 1:
          return 'primary';
        case 2:
          return 'primary';
        case 3:
          return 'success';
        default:
          return 'info';
      }
    },
    getReviewValue(reviewCode) {
      const conclusion = this.reviewConclusionData.find(item => item.reviewCode === reviewCode);
      return conclusion ? conclusion.reviewValue : reviewCode;
    },
    getDicLabel2(row) {
      if(row.conclusion.toUpperCase() === 'PASS'){
        return { label: row.customerConclusion, type: "success" };
      }else if(row.conclusion.toUpperCase() === 'FAIL'){
        return { label: row.customerConclusion, type: "danger" }; 
      }else{
        return { label: row.customerConclusion, type: "info" }; 
      }
    },
    getTagContent(report){
      return "report.reviewConclusionStatus_" + report.reviewConclusionStatus
    }
  },
};
</script>

<style lang="scss" scoped>
.test_result_div {
  .comment_dia .el-dialog__body {
    padding: 0 20px !important;
  }
  .el-textarea__inner:not(.comment_textarea .el-textarea__inner) {
    min-height: 150px !important;
  }

  .el-table th > .cell {
    font-size: 14px !important;
  }

  .el-card__body {
    padding: 5px;
  }
  .report-box {
    .el-icon-date {
      background-position: left -2px top 4px !important;
    }
    .el-input__inner {
      padding-left: 32px !important;
    }
  }
  .title-wrap {
    display: flex;
    align-items: center;
  }
}
.field-content {
  transform: translateY(5px);
  margin-left: 16px;
  color: #000000;
}
.field-content-form {
  margin-left: 16px;
}
.conclusionTag {
  width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; 
  text-align: center;
  cursor: pointer;
}
</style>
