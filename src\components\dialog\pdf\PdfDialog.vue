<template>
    <div>
        <el-dialog :close-on-click-modal="false"
                title="PDF View"
                :visible.sync="dialogVisible"
                width="70%">
            <el-row>
                <el-button type="primary" style="margin-left: 12px;"
                           @click="prePage">{{$t('page.prePage')}}
                </el-button>
                <el-button type="primary" style="margin-left: 12px;"
                           @click="nextPage">{{$t('page.nextPage')}}
                </el-button>
                <el-button type="primary" style="margin-left: 12px;"
                           @click="clock">{{$t('page.clock')}}
                </el-button>
                <el-button type="primary" style="margin-left: 12px;"
                           @click="counterClock">{{$t('page.counterClock')}}
                </el-button>
            </el-row>
            <el-row style="margin-top: 10px;"><p style="margin-left: 12px;">
                <div class="el-input el-pagination__editor is-in-pagination"><!----><input type="number"
                                                                                           v-model.number="pageNum"
                                                                                           autocomplete="off" min="1"
                                                                                           :max="pageTotalNum"
                                                                                           class="el-input__inner"></input>
                    <!----><!----><!----><!----></div>
                / {{pageTotalNum}}</p></el-row>
            <div>
                <pdf v-if="dialogVisible"
                     ref="pdf"
                     :src="pdfUrl"
                     :page="pageNum"
                     :rotate="pageRotate"
                     @progress="loadedRatio = $event"
                     @page-loaded="pageLoaded($event)"
                     @num-pages="pageTotalNum=$event"
                     @error="pdfError($event)"
                     @link-clicked="page = $event"></pdf>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {queryFile} from "@/api/common/file";

    export default {
        name: 'Pdf',
        components: {
            pdf: resolve => require(['vue-pdf'], resolve),
        },
        name: "PdfDialog",
        data() {
            return {
                dialogVisible: false,
                imageArray: [],
                pageNum: 1,
                pageTotalNum: 1,
                pageRotate: 0,
                // 加载进度
                loadedRatio: 0,
                curPageNum: 0,
                pdfUrl: '',
            }
        },
        methods: {
            prePage() {
                var p = this.pageNum
                p = p > 1 ? p - 1 : this.pageTotalNum
                this.pageNum = p
            },
            nextPage() {
                var p = this.pageNum
                p = p < this.pageTotalNum ? p + 1 : 1
                this.pageNum = p
            },
            clock() {
                this.pageRotate += 90
            },
            counterClock() {
                this.pageRotate -= 90
            },
            pageLoaded(e) {
                this.curPageNum = e
            },
            pdfError(error) {
                console.error(error)
            },
            pdfPrintAll() {
                this.$refs.pdf.print()
            },
            pdfPrint() {
                this.$refs.pdf.print(100, [1, 2])
            },
            async open(cloudId) {
                this.pageNum = 1;
                this.pageTotalNum = 1;
                this.pageRotate = 0;
                // 加载进度
                this.loadedRatio = 0;
                this.curPageNum = 0;

                //根据cloudId查询文件详细信息
                let fileListRes = null
                let queryParam = {};
                queryParam.cloudID = cloudId;
                try {
                    fileListRes = await queryFile(queryParam)
                } catch (e) {
                    this.$info({message: 'Query PDF of image is Fail'})
                }
                if (fileListRes == null || this.$lodash.isEmpty(fileListRes.data.data)) {
                    return false
                }
                // 如果获取的是其他类型的文件，则提供下载功能
                if (this.$lodash.get(fileListRes, 'data.data.suffixes', '').toUpperCase() === 'PDF') {
                    cloudId = cloudId.replace(/#/g, '%23');
                    cloudId = cloudId.replace('+', '%2B');
                    cloudId = cloudId.replace(/&/g, '%26');
                    cloudId = cloudId.replace(/%/g, '%25');
                    cloudId = cloudId.replace(/=/g, '%3D');
                    this.pdfUrl = "api/sgs-mart/file/download?cloudId=" + cloudId;//await queryFileUrl(cloudId)
                    this.dialogVisible = true
                } else {
                    window.open(fileListRes.data.data.path)
                }
            },
        }
    }
</script>

<style scoped>
    @media print {
        img {
            display: none
        }
    }

    .el-pagination__editor.el-input {
        width: 70px;
    }

    .pdf {
        margin-bottom: 25px;
        border: 1px solid #ccc;
    }

</style>
