export const sampleTemplate = {
  "id": "fc50c544-9733-4209-a082-257cd2db8f1f",
  "activeIndicator": "A",
  "formAliasName": "c&a test",
  "buId": "",
  "buCode": "SL",
  "buName": "Softlines",
  "countryCode": "CN",
  "countryName": "China",
  "createdBy": "<PERSON><PERSON><PERSON>_<PERSON>",
  "createdDate": "2025-06-25 11:52:57",
  "customerAccountName": "",
  "customerGroupCode": "CG0000050",
  "customerGroupId": "6b59234f-61f1-4e7e-9c74-ec220f0d75db",
  "customerGroupName": "C&A",
  "customerId": "",
  "formGroupID": "27e47ab7-da46-4f34-9454-1100a2df51de",
  "formStatus": 1,
  "formUse": "",
  "formVersionNo": 2,
  "modifiedBy": "<PERSON><PERSON><PERSON>_<PERSON>",
  "modifiedDate": "2025-06-25 13:51:02",
  "moduleCode": "TRF-Product-Header",
  "moduleName": "TRF-Product-Header",
  "formName": "c&a test",
  "formSequence": 1,
  "productCategoryCode": "",
  "customerCategory": "APR00000",
  "systemCode": "SGSMart",
  "systemName": "",
  "formType": "FORM",
  "formPurpose": "Product",
  "sectionList": [
    {
      "section": "",
      "sectionCode": "HeaderInfo",
      "sectionName": "Header Info",
      "sectionLabel": "Product Info",
      "sectionType": "normal",
      "extend": true,
      "display": 1,
      "seq": 1,
      "required": "",
      "configurable": "",
      "fieldList": [
        {
          "fieldCode": "serviceType",
          "defaultValue": "",
          "fieldLabel": "Service Type",
          "isActive": 1,
          "isRequired": 1,
          "configurable": 1,
          "fieldType": "number",
          "customerFieldCode": "",
          "sourceValue": [
            {
              "id": "89d9e186-a9cb-42e1-8aa3-4628ed8a6d26",
              "code": "1",
              "name": "Regular"
            },
            {
              "id": "3454eddb-e326-419c-be27-529e3e19b0e9",
              "code": "2",
              "name": "Express"
            },
            {
              "id": "09a812ff-fa86-43e4-9d09-2bdb7eddc43d",
              "code": "3",
              "name": "Double express"
            },
            {
              "id": "5b6c49c3-665c-4ef9-9ea0-4f65df3f61f8",
              "code": "4",
              "name": "Emergency"
            }
          ]
        },
        {
          "fieldCode": "labCode",
          "defaultValue": "",
          "fieldLabel": "Lab Name",
          "isActive": 1,
          "isRequired": 1,
          "configurable": 1,
          "fieldType": "date",
          "customerFieldCode": "",
          "sourceValue": [
            {
              "id": "08abc260ad1aa2792860ec8424e67cf8",
              "code": "BOM SL",
              "name": "Mumbai Softline Lab"
            },
            {
              "id": "09810e9a589bc1d11ea93b9f6e3a10db",
              "code": "SUZ SL",
              "name": "Suzhou Softline Lab"
            },
            {
              "id": "0bf99d514eb74acdbf74b8cf637d8ada",
              "code": "HZ SL",
              "name": "Hangzhou Softline Lab"
            },
            {
              "id": "0c7eff203b3e2297284b782343635cf0",
              "code": "AIX SL",
              "name": "Aix-en-Provence Softline Lab"
            },
            {
              "id": "0dd28a4f9a8d12dd90634db12da173ec",
              "code": "KAO SL",
              "name": "Kaohsiung Softline Lab"
            },
            {
              "id": "14e4bffbe50db875662f6fc83ee79dae",
              "code": "TCH SL",
              "name": "Taichung Softline Lab"
            },
            {
              "id": "184d21dc3a9f69d02a48704c0a7be68e",
              "code": "PT SL",
              "name": "Porto Softline Lab"
            },
            {
              "id": "198cebb23b088857ed3252e801f67a99",
              "code": "GG SL",
              "name": "Gurgaon Softline Lab"
            },
            {
              "id": "21cd64a4f609cb02db29a6c6603a7c08",
              "code": "HCMC SL",
              "name": "Ho Chi Minh City Softline Lab"
            },
            {
              "id": "24b44ec0ba18fa88714f4d28a0c45568",
              "code": "CB SL",
              "name": "Casablanca Softline Lab"
            },
            {
              "id": "288e30e4c45d8b626810bb410678422b",
              "code": "KR SL",
              "name": "Karachi Softline Lab"
            },
            {
              "id": "28d87229e36c2da0de1f3b0af367e0f6",
              "code": "PHX SL",
              "name": "Phoenix Softline Lab"
            },
            {
              "id": "29fbdb4c5906b7561faec9068ed68268",
              "code": "MN SL",
              "name": "Manila Softline Lab"
            },
            {
              "id": "2a1143c5682d1271330945bb3b948b3e",
              "code": "ONB SL",
              "name": "Osnerbruck Softline Lab"
            },
            {
              "id": "2a486875cea7a0a91e1ee49df89e2d1d",
              "code": "BOGOTA SL",
              "name": "Bogota Softline Lab"
            },
            {
              "id": "30d7eb7e2a4c658156e59d6230b06f5e",
              "code": "FAI SL",
              "name": "Fairfield Softline Lab"
            },
            {
              "id": "3d70c7333028a52db7deb37505bc65ac",
              "code": "ADM SL",
              "name": "Ahmedabad Softline Lab"
            },
            {
              "id": "42f757199f1df60d58de66b180afdf6f",
              "code": "PP SL",
              "name": "Phnom Penh Softline Lab"
            },
            {
              "id": "476b63cc16e97ad4cbdd9cd4d323774",
              "code": "TUN SL",
              "name": "Tunis Softline Lab"
            },
            {
              "id": "493905c0f6396a7f21d9d7d7ebaa2eb6",
              "code": "JK SL",
              "name": "Jakarta Softline Lab"
            },
            {
              "id": "49fb56b91a0b6279271bd75853800477",
              "code": "VAR SL",
              "name": "Varna Softline Lab"
            },
            {
              "id": "5e366c4d3a201e2bdf5d9cfc27463cf3",
              "code": "BN SL",
              "name": "Bangalore Softline Lab"
            },
            {
              "id": "67683ba823504ad6a6fcb57057ada4f4",
              "code": "NJ SL",
              "name": "Nanjing Softline Lab"
            },
            {
              "id": "692953802ca649d8960ef6bae1481c00",
              "code": "GZ SL",
              "name": "Guangzhou Softline Lab"
            },
            {
              "id": "6957e613f6f0a2c41bcffe74a1620416",
              "code": "GIDG SL",
              "name": "Guatemala Softline Lab"
            },
            {
              "id": "71e3eb7db603453a9a0e493a099057ba",
              "code": "CN SL",
              "name": "Chennai Softline Lab"
            },
            {
              "id": "7a281f90b394f157bfc30d7e5fcb0329",
              "code": "NDE SL",
              "name": "New Delhi Softline Lab"
            },
            {
              "id": "7ec86615126f3aa60105748c2740897f",
              "code": "ISB SL",
              "name": "Istanbul Softline Lab"
            },
            {
              "id": "7f929b72d41978bf8c321ff969992821",
              "code": "SZ SL",
              "name": "Shenzhen Softline Lab"
            },
            {
              "id": "808d8dcba6a080028160a65d79129018",
              "code": "MKT SL",
              "name": "Makati City Softline Lab"
            },
            {
              "id": "87d48fcc3e8548f0bd3f822f6dd99cd0",
              "code": "HK SL",
              "name": "Hongkong Softline Lab"
            },
            {
              "id": "896b308f18d247a3b38e5592a752369c",
              "code": "QD SL",
              "name": "Qingdao Softline Lab"
            },
            {
              "id": "8e2e7d2174a2258aa01d452040361d40",
              "code": "NCP SL",
              "name": "Naucalpan Softline Lab"
            },
            {
              "id": "94a8fede5a4f942c3cba46fde52f8bda",
              "code": "AA SL",
              "name": "Addis Ababa Softline Lab"
            },
            {
              "id": "97cf04a3156112dfbbc0014ceca61322",
              "code": "LHR SL",
              "name": "Lahore Softline Lab"
            },
            {
              "id": "a31f56e7afd8fd13da5f8baa32253b0a",
              "code": "SEM SL",
              "name": "Semarang Softline Lab"
            },
            {
              "id": "a8b489bca6a053055d8dd35324cdc094",
              "code": "CAI SL",
              "name": "Cairo Softline Lab"
            },
            {
              "id": "acb9f38bb9ae454a86b9ad091578a1ad",
              "code": "SH SL",
              "name": "Shanghai Softline Lab"
            },
            {
              "id": "ad6dcd8d0abc4ea3b747a4cca94fe0f8",
              "code": "TJ SL",
              "name": "Tianjin Softline Lab"
            },
            {
              "id": "b530e2def2f1c7497fdfb95bd90cefe5",
              "code": "DHK SL",
              "name": "Dhaka Softline Lab"
            },
            {
              "id": "b9861e9677e34cc2b68e9ba8e75c9a34",
              "code": "XM SL",
              "name": "Xiamen Softline Lab"
            },
            {
              "id": "baed2a9c1d9e978750b6d259c6ae8d9c",
              "code": "TP SL",
              "name": "New Taipei Softline Lab"
            },
            {
              "id": "beb0d5f6e046f598b05c262eebe80ad5",
              "code": "LE SL",
              "name": "Leicester Softline Lab"
            },
            {
              "id": "c357f0ef81a1421b8b6b57243d453eff",
              "code": "NB SL",
              "name": "Ningbo Softline Lab"
            },
            {
              "id": "ca638317ab0cc647b76cfcec09d10a40",
              "code": "SP SL",
              "name": "Sao Paulo Softline Lab"
            },
            {
              "id": "ca774ba4e92a2917206e16245d323774",
              "code": "MEX SL",
              "name": "Mexico Softline Lab"
            },
            {
              "id": "cdb4468def1c2d113f1b9ca02247ccba",
              "code": "HP SL",
              "name": "Hai Phong Softline Lab"
            },
            {
              "id": "da94acf93b89054fe5bdc10a19142258",
              "code": "TNR SL",
              "name": "Antananarivo Softline Lab"
            },
            {
              "id": "dc794d32bdfb30a9b4ffd7585c372c10",
              "code": "CLB SL",
              "name": "Colombo Softline Lab"
            },
            {
              "id": "dd5267f5558e4eb09046b2e1f7f4a961",
              "code": "CZ SL",
              "name": "Changzhou Softline Lab"
            },
            {
              "id": "e4c51d9a5a4609925675dfa0240afdd3",
              "code": "ML SL",
              "name": "Milano Softline Lab"
            },
            {
              "id": "e504968b0da1f2ac89937d7756d9997a",
              "code": "TAU SL",
              "name": "Taunusstein Softline Lab"
            },
            {
              "id": "ecbb7077a5e4b77efd3853f5dcf84725",
              "code": "CTG SL",
              "name": "Chittagong Softline Lab"
            },
            {
              "id": "ee3cb1519129c9a91960e38961346a30",
              "code": "HAN SL",
              "name": "Ha Noi Softline Lab"
            },
            {
              "id": "f311a1534f2c7ddbb7226203a50ced40",
              "code": "UAE SL",
              "name": "Dubai Softline Lab"
            },
            {
              "id": "f5b9b7803ccaf5add15ba6dced28a21d",
              "code": "AY SL",
              "name": "Anyang Softline Lab"
            },
            {
              "id": "f98d4b8e7b8f6eef37b82af7a96ce102",
              "code": "TIR SL",
              "name": "Tirupur Softline Lab"
            },
            {
              "id": "f9b0ce6f54a465214d9ab16dbf712018",
              "code": "BK SL",
              "name": "Bangkok Softline Lab"
            },
            {
              "id": "f9de07d8aa7bab4e6ede4d8f5633dfc1",
              "code": "AT SL",
              "name": "Appleton Softline Lab"
            },
            {
              "id": "fa0bd66d632efa2a96f2a39530884747",
              "code": "AC SL",
              "name": "A Coruna Softline Lab"
            },
            {
              "id": "fa99adf729c4ce569a7601ad23b7c794",
              "code": "ALX SL",
              "name": "Alexandria Softline Lab"
            },
            {
              "id": "febc9896dad2c407f7f6d6ec9e2f71c7",
              "code": "SAS22 SL",
              "name": "SA Seksyen 22 Softlines"
            }
          ]
        },
        {
          "fieldCode": "labContact",
          "defaultValue": "",
          "fieldLabel": "Lab Contact Email",
          "isActive": 1,
          "isRequired": 1,
          "configurable": 1,
          "fieldType": "daterange",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "factoryID",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "input",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "factoryID",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "textArea",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "vendorNo",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "select",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "select2",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "checkboxGroup",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "checkbox",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "radioGroup",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "radio",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "percentage",
          "customerFieldCode": "",
          "sourceValue": []
        },
        {
          "fieldCode": "season",
          "defaultValue": "",
          "fieldLabel": "Label name",
          "isActive": "",
          "isRequired": "",
          "configurable": "",
          "fieldType": "percentageSelect",
          "customerFieldCode": "",
          "sourceValue": []
        }
        
      ]
    },
    {
      "section": "",
      "sectionCode": "CareLabel",
      "sectionName": "Care Label",
      "sectionLabel": "Care Label",
      "sectionType": "component",
      "extend": true,
      "display": 1,
      "seq": 5,
      "required": "",
      "configurable": "",
      "fieldList": []
    },
    {
      "section": "",
      "sectionCode": "Workbooks",
      "sectionName": "Workbooks",
      "sectionLabel": "Workbooks",
      "sectionType": "component",
      "extend": true,
      "display": 1,
      "seq": 6,
      "required": "",
      "configurable": "",
      "fieldList": []
    },
    {
      "section": "",
      "sectionCode": "ReportCertificate",
      "sectionName": "Report & Certificate",
      "sectionLabel": "Report & Certificate",
      "sectionType": "component",
      "extend": true,
      "display": 1,
      "seq": 7,
      "required": "",
      "configurable": "",
      "fieldList": []
    },
    {
      "section": "",
      "sectionCode": "BillOfMaterials",
      "sectionName": "Bill of Materials",
      "sectionLabel": "Bill of Materials",
      "sectionType": "component",
      "extend": true,
      "display": 1,
      "seq": 8,
      "required": "",
      "configurable": "",
      "fieldList": []
    }
  ],
  "templateId": "fc50c544-9733-4209-a082-257cd2db8f1f",
  "templateName": "c&a test",
  "templateType": "",
  "templateCategory": "",
  "templateGroupId": "27e47ab7-da46-4f34-9454-1100a2df51de",
  "productLineCode": "",
  "customerBossNo": "",
  "customerName": ""
};

// 模板数据解析工具函数
export const parseTemplateData = (template) => {
  if (!template || !template.sectionList) {
    return {
      templateSectionList: [],
      navList: []
    };
  }

  // 解析模板配置为页面结构数据
  const templateSectionList = template.sectionList.map(section => ({
    sectionName: section.sectionLabel || section.sectionName,
    sectionCode: section.sectionCode,
    sectionType: section.sectionType,
    active: section.display === 1,
    extend: section.extend,
    seq: section.seq,
    required: section.required,
    configurable: section.configurable,
    generalFieldList: section.fieldList || [],
    fieldList: []
  }));

  // 生成导航列表
  const navList = template.sectionList
    .filter(section => section.display === 1)
    .sort((a, b) => a.seq - b.seq)
    .map((section, index) => ({
      name: section.sectionLabel || section.sectionName,
      seq: section.seq,
      active: index === 0, // 第一个默认激活
      isSub: false,
      show: true
    }));

  return {
    templateSectionList,
    navList,
    templateInfo: {
      id: template.id,
      templateId: template.templateId,
      templateName: template.templateName,
      formName: template.formName,
      customerGroupName: template.customerGroupName,
      buName: template.buName,
      countryName: template.countryName
    }
  };
};

// 导出默认模板数据
export default sampleTemplate;