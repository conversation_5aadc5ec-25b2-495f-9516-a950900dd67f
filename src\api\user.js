import request from '@/router/axios';

import {baseUrl} from '@/config/env';

export const loginByUsername = (tenantId, account, password, type,receiveMarketingCommunication = 0) => request({
  url: '/api/sgs-auth/token',
  method: 'post',
  params: {
    tenantId,
    account,
    password,
    type,
    receiveMarketingCommunication
  }
})
export const validateToken = (token) => request({
    url: '/api/sgs-auth/token/validate',
    method: 'post',
    params: {
      token
    }
})

export const getButtons = () => request({
  url: '/api/nkop-system/menu/buttons',
  method: 'get'
});
export const routes = () => request({
  url: '/api/sgs-auth/e-filing/routes',
  method: 'get'
});
export const setGuide = () => request({
  url: '/api/sgs-mart/userSetting/saveUserGuide',
  method: 'get'
})

export const getUserInfo = () => request({
  url: baseUrl + '/user/getUserInfo',
  method: 'get'
});

export const refeshToken = () => request({
  url: baseUrl + '/api/sgs-auth/refesh',
  method: 'post'
})

export const getMenu = (languageCode) => request({
  url: '/api/sgs-auth/routes',
  method: 'get',
  params:{"languageCode":languageCode}
});

export const getTopMenu = () => request({
  url: baseUrl + '/user/getTopMenu',
  method: 'get'
});

export const sendLogs = (list) => request({
  url: baseUrl + '/user/logout',
  method: 'post',
  data: list
})

export const logout = () => request({
  url: baseUrl + '/user/logout',
  method: 'get'
})

export const queryLoginLanguage = () => {
  return request({
      url: '/api/sgs-mart/userSetting/getLanguageByRemoteIp',
      method: 'get'
  })
}
export const loginEFiling = (token) => request({
  url: '/api/sgs-e-filling/sgs-auth/oauth/sgsToken/validate',
  method: 'post',
  params:{"token":token}
});