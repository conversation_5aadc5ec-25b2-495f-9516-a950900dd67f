<template>
    <el-row :style="{height: scrollerHeight}"  class="powerbi_list">
        <el-container>
            <el-aside width="260px" style="height: 750px">
              <el-menu >
                  <el-card :body-style="{ padding: '0px' }"  v-for="(report, index) in reportList" >
                      <div style="padding: 2px 14px 2px 14px;"  @click="getToken(report)">
                          <h5 style="font-weight:bold;">{{report.documentTitle}}</h5>
                          <span>{{report.documentDec}}</span>
                          <!-- <div class="bottom clearfix" style="text-align:right">
                              <el-button @click="getToken(report)" type="text" class="button">{{$t('powerbi.viewReport')}}</el-button>
                          </div> -->
                      </div>
                  </el-card>

              </el-menu>
            </el-aside>
            <el-container>
                <el-main>
                    <div >

                        <!-- 骨架屏 -->
                        <el-skeleton style="width: 100%" :loading="loading"
                        :throttle="500">
                          <template slot="template">
                            <el-row :gutter="20">
                                <div class="grid-content bg-purple" style="padding: 6px;">
                                  <img src="/img/report.png" class="report_skeleton_class" style="height: 100%">
                                </div>
                            </el-row>
                          </template>
                          <template slot="default">
                            <div ref="reportMain">
                              <i class="el-icon-full-screen report-full-screen" @click="toggleFullScreen"></i>
                              <div ref="reportContainer" :style="{height: scrollerHeight}"></div>
                            </div>
                          </template>
                        </el-skeleton>


                        <!-- <el-empty v-if="!currentReport.reportId" image="/img/report.png" :image-size="200" :style="{height: scrollerHeight}"></el-empty> -->

                    </div>
                </el-main>
            </el-container>
        </el-container>
    </el-row>
</template>
<script>

    import * as utils from '@/views/util'
    import i18n from '@/lang'
    import moment from 'moment'
    import {vipProgramList} from "@/api/document/supplyDocument";
    import {mapGetters} from 'vuex'
    import {getReport1Param} from "@/api/common/powerbi";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {validatenull,objectIsNull} from "@/util/validate";
    import _ from 'lodash';// 导入loadsh
    Vue.prototype.$lodash = _;//注入工具

    export default {
        data() {
            return {
                loading:true,
                reportList: [
                    {
                        "title":"Service Request By Country",
                        "description": "test 02, pass/fail rate at test line level, and the detailed test data. loading time: < 20 seconds.",
                        "reportId": "c808b238-cb8f-4e17-ac09-f4e2a2209f4f",
                        "workspaceId": "9eb14095-e7ba-4ce4-bd3f-47de9d5eac7b",
                        "customerData": "{CustomerNo}:683576(||){CustmerGroupCode}:CG0000236(||)"
                    },
                    {
                        "title":"Failed Rates by Test Name",
                        "description": "test 06, ....., loading time: < 20 seconds.",
                        "reportId": "830fe196-a62a-4734-b26f-3fdde1417399",
                        "workspaceId": "9eb14095-e7ba-4ce4-bd3f-47de9d5eac7b",
                        "customerData": "{CustomerNo}:683576(||){CustmerGroupCode}:CG0000236(||)"
                    },
                    {
                        "title":"Service Request with Sample Analysis Detail (Spread Data)",
                        "description": "test 10, ....., loading time: < 20 seconds.",
                        "reportId": "b0c15048-eeee-4f81-b082-3584352c9ce0",
                        "workspaceId": "9eb14095-e7ba-4ce4-bd3f-47de9d5eac7b",
                        "customerData": "{CustomerNo}:683576(||){CustmerGroupCode}:CG0000236(||)"
                    },
                    {
                        "title":"Supplier Pass Fail Rates",
                        "description": "test 14, ....., loading time: < 20 seconds.",
                        "reportId": "dac88d4d-400e-48cd-b4cc-141f85b02388",
                        "workspaceId": "9eb14095-e7ba-4ce4-bd3f-47de9d5eac7b",
                        "customerData": "{CustomerNo}:683576(||){CustmerGroupCode}:CG0000236(||)"
                    },
                    {
                        "title":"Market Segment Analysis",
                        "description": "test 18, ....., loading time: < 20 seconds.",
                        "reportId": "a3838ed7-ea1c-4863-9ab2-cf63454b23e0",
                        "workspaceId": "9eb14095-e7ba-4ce4-bd3f-47de9d5eac7b",
                        "customerData": "{CustomerNo}:683576(||){CustmerGroupCode}:CG0000236(||)"
                    },
                    {
                        "title":"Report 06",
                        "description": "test 18, ....., loading time: < 20 seconds.",
                        "reportId": "10fc9863-d8c9-4f8d-bde0-48a6f5dfc2c5",
                        "workspaceId": "4f62c1c7-595f-4713-89ab-39702d9b1bb6",
                        "customerData": "{CustomerNo}:683576(||){CustmerGroupCode}:CG0000236(||)"
                    },
                ],
                currentReport:{},
                queryForm: {}
            }
        },
        watch: {
            //监听语言变化
          language: function (newVal) {
             //查询加载初始化数据
             this.loadPowerbiData();
          },
        },
        components: {
        },
        computed: {
            ...mapGetters({
                companyType: 'companyType',
                user: 'userInfo',
                language:'language'
            }),
            scrollerHeight: function() {
              debugger;
                return (window.innerHeight - 130) + 'px'; //自定义高度需求
            }
        },
        // beforeRouteEnter(to, from, next) {
        //     next(vm => {
        //         vm.initData()
        //         vm.saveLog();
        //     })
        // },
        created() {
            //查询加载初始化数据
            this.loadPowerbiData();
            this.preload();
        },
        methods: {
          loadPowerbiData() {
                this.queryForm.documentType = 2;//查询知识共享
                this.queryForm.showOnWhichMenu = 2;//查询菜单
                this.queryForm.customerReportFlag = 1;//查询标识为指定powerBi的数据
                this.queryForm.productLineCode=this.user.productLineCode
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                  languageId=LanguageEnums.EN.code;
                }else{
                  languageId=LanguageEnums.CN.code;
                }
                this.queryForm.languageId=languageId;
                vipProgramList(this.queryForm).then(res => {
                    console.log(res);
                    let programList = res.data.groupByProgram;
                    let reportDatas = [];
                    if(!objectIsNull(programList)){
                      for(let programValues of programList){
                        if(!objectIsNull(programValues.programValue)){
                          for(let programValue of programValues.programValue){
                            reportDatas.push(programValue);
                          }
                        }

                      }
                    }
                    this.reportList=reportDatas;
                    console.log('加载后数据',this.reportList)
                    //this.programYearList = res.data.groupByProgramAndYear;
                }, error => {
                    console.log(error);
                });
            },
            preload() {
                var config = {
                    type: 'report',
                    embedUrl: 'https://app.powerbi.com/reportEmbed',
                };
                var element = powerbi.preload(config);
            },
            async getToken(report) {
              this.loading=true;
              debugger;
              if(objectIsNull(report.documentUrl)){
                this.$warn({message: 'Loading PowerBI Report is Fail'})
                return false;
              }
              var temp2 = report.documentUrl.split('?')[1];
              var pram2 = new URLSearchParams('?'+temp2);
              let workspaceId = pram2.get('workspaceId');
              if(objectIsNull(workspaceId)){
                this.$warn({message: 'Loading PowerBI Report is Fail,workspaceId Param is null'})
                return false;
              }
              let reportId = pram2.get('reportId');
              if(objectIsNull(reportId)){
                this.$warn({message: 'Loading PowerBI Report is Fail,reportId Param is null'})
                return false;
              }
              let libraryId = pram2.get('libraryId');


                this.currentReport = report;
                this.$set(this.currentReport, 'reportId', reportId);
                this.$set(this.currentReport, 'workspaceId', workspaceId);
                // this.loading = this.$loading({
                //     lock: true,
                //     text: this.$t('loading'),
                //     spinner: 'el-icon-loading',
                //     background: 'rgba(0, 0, 0, 0.3)',
                // })
                let pbiType = report.pbiType;
                if(!pbiType){
                    pbiType = 0;
                }
                let res = await getReport1Param({"workspaceId": workspaceId,   "reportId": reportId,"moreCustomerFlag":1, "pbiType": pbiType  ,libraryId});

                if (res.data.code === 200) {
                    this.config = res.data.data;
                    this.$nextTick(() => {
                        this.renderReport()
                    })
                } else {
                    this.loading=false;
                    this.$warn({message: 'Loading PowerBI Report is Fail'})
                }
            },

            renderReport() {

                if (this.config == null) {
                    return
                }

                const accessToken = this.config.embedToken.token;

                // Read embed URL from Model
                const embedUrl = this.config.embedUrl;

                // Read report Id from Model
                const embedReportId = this.config.id;

                // Get models. models contains enums that can be used.
                const models = window['powerbi-client'].models;
                this.loading=false;
                this.$nextTick(() => {
                        this.reportInstance = powerbi.embed(this.$refs.reportContainer, {
                          type: 'report',
                          tokenType: models.TokenType.Embed,
                          accessToken: accessToken,
                          embedUrl: embedUrl,
                          id: embedReportId,
                          permissions: models.Permissions.All,
                          settings: {
                              filterPaneEnabled: true,
                              navContentPaneEnabled: true
                          }
                      })
                  })


                // Report.off removes a given event handler if it exists.
                this.reportInstance.off("rendered");

                // Report.on will add an event handler which prints to Log window.
                this.reportInstance.on("rendered", () => this.loading=false);
            },
            destory () {
                if (this.reportInstance) {
                    this.reportInstance.service.reset(this.$refs.reportContainer)
                    this.reportInstance = null
                }
            },
            toggleFullScreen() {
              if (!document.fullscreenElement) {
                this.enterFullScreen();
              } else {
                this.exitFullScreen();
              }
            },
            enterFullScreen() {
              let element = this.$refs.reportMain;
              if (element.requestFullscreen) {
                element.requestFullscreen();
              } else if (element.mozRequestFullScreen) { /* Firefox */
                element.mozRequestFullScreen();
              } else if (element.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
                element.webkitRequestFullscreen();
              } else if (element.msRequestFullscreen) { /* IE/Edge */
                element.msRequestFullscreen();
              }
            },
            exitFullScreen() {
              if (document.exitFullscreen) {
                document.exitFullscreen();
              } else if (document.mozCancelFullScreen) { /* Firefox */
                document.mozCancelFullScreen();
              } else if (document.webkitExitFullscreen) { /* Chrome, Safari and Opera */
                document.webkitExitFullscreen();
              } else if (document.msExitFullscreen) { /* IE/Edge */
                document.msExitFullscreen();
              }
            }
        }
    }
</script>
<style>
.powerbi_list{


    .el-card:hover {
      cursor: pointer;
    }
    .report_skeleton_class{
      width: 100%;
    }
    .el-aside {
        background-color: #D3DCE6;
        color: #333;
    }

    .el-main {
        background-color: #E9EEF3;
        color: #333;
    }

    .report-full-screen {
        position: fixed;
        background-color: #fff;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        color: #409eff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        box-shadow: 0 0 6px rgba(0, 0, 0, .12);
        cursor: pointer;
        z-index: 5;
        right: 35px;
    }

    .report-container {
      margin-bottom: 20px;
      border: 1px solid #ccc;
      padding: 10px;
    }

    .report-info {
      padding-bottom: 10px;
      border-bottom: 0px solid #ccc;
    }

    .report-wrapper {
      height: 100%;
      z-index: 9999;
    }

    .report-actions {
      padding-bottom: 1px;
      width: 280 px;
      z-index: 9998;
    }

    .report-actions.full-screen2 {
      position: fixed;
      top: 0;
      left: 0;
      width: 150 px;
      height: 30px;
      z-index: 1;
    }

    .report-content {
      height: 800px;
      display: none;
    }

    .fullscreen-button {
      display: none;
      width: 150px;
    }

    .showreport-button {
      width: 120px;
    }

    .showreport-button.none {
      display: none;
    }

    .report-content.full-screen1 {
      height: 100%;
      min-height: 800px;
    }

    .status-indicator {
      float: right;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin-top: 0px;
      margin-right: 15px;
      animation: blink-animation 1s infinite;
    }

    .time-cost {
      float: right;
      width: 50px;
      height: 50px;
      margin-right: 35px;
    }

    .status-indicator.yellow {
      background-color: yellow;
    }

    .status-indicator.green {
      background-color: green;
      animation: none;
    }

    .status-indicator.red {
      background-color: red;
      animation: none;
    }

    @keyframes blink-animation {
      50% {
        opacity: 0;
      }
    }
    .full-screen {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      z-index: 9999 !important;
    }
  }
</style>