import Cookies from 'js-cookie'
import {JSEncrypt} from "jsencrypt";
const TokenKey = 'sgsToken'
let inFifteenMinutes = new Date(new Date().getTime() + 120 * 60 * 1000);
export function getToken() {
     //console.log(Cookies.get(TokenKey));
    return Cookies.get(TokenKey)
}

export function setToken(token) {
    if(token){
        sessionStorage.setItem(TokenKey,token);
        return Cookies.set(TokenKey, token, { expires: new Date(new Date().getTime() + 120 * 60 * 2000) })
    }else {

    }
}

export function encryptor(password) {
    let encryptor = new JSEncrypt()
    encryptor.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJ3JpeGoxzx4VfoQu88IVlJ+0x5OUDPhAp0Ttwsvd3YpY+SsKiJrscxRU1oyPGi4pNDNuQmEYmdyv6GbNYIMxXR+CuQiLYwI1/xhHd9DcYPC/eTXNln7Q2gQ4zrEhiez+LJf7pwrXaCEq03E9zGxb9unQgY0digv0NGD5l8pTCQwIDAQAB")
    password = encryptor.encrypt(password);
    return password;
}

export function removeToken() {
    sessionStorage.removeItem(TokenKey);
    Cookies.remove(TokenKey);
    return true
}
