<template>
  <div class="register-container" v-if="showRegister">
    <el-card class="register-card">
      <el-row>
        <el-col :span="12">
          <img src="/img/SGS_logo.png" height="100px" alt="logo" class="logo">
        </el-col>
      </el-row>
      <div v-if="!showRegistrySuccess" class="welcome-text">
        <h4>{{ t('register.welcomeText') }} {{ form.userName }}, 
          <span>{{ t('register.welcomeToSGS') }}</span> 
          <span v-if="showCompanyForm">&nbsp;{{ t('register.pleaseChooseCompany') }}</span>
          <span v-if="showCompanyDetailForm">&nbsp;{{ t('register.pleaseFillCompanyInfo') }}</span>
        </h4> 
      </div>

      <!--填写基本信息 -->
      <el-form v-if="showAccountForm" :model="form" label-position="top" ref="formRef"
        label-width="120px" class="register-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.userName')" prop="userName" :rules="[
                { required: true, message: t('register.validate.userNameRequired'), trigger: 'blur' },
                { min: 3, max: 50, message: t('register.validate.userNameLength'), trigger: 'blur' }
              ]">
              <el-input disabled v-model="form.userName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.email')" prop="email" :rules="[
              { required: true, message: t('register.validate.emailRequired'), trigger: 'blur' },
              { type: 'email', message: t('register.validate.emailFormat'), trigger: 'blur' }
            ]">
              <el-input :disabled="form.applyType == 1" v-model="form.email" clearable :name="'smart_register_email'+Math.random()"
                @blur="handlerCheckEmailExist"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.password')" prop="password" :rules="[
              { min: 8, message: t('register.validate.passwordLength'), trigger: 'blur' },
              { max: 20, message: t('register.validate.passwordLength'), trigger: 'blur' },
              { validator: validatePass, trigger: 'blur' },
              { required: true, message: t('register.validate.passwordRequired'), trigger: 'blur' }
            ]">
              <el-input maxlength="20" v-model="form.password" type="password" show-password clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.confirmPassword')" prop="confirmPassword" :rules="[
              { validator: validatePass2, trigger: 'blur' },
              { required: true, message: t('register.validate.confirmPasswordRequired'), trigger: 'blur' }
            ]">
              <el-input v-model="form.confirmPassword" type="password" show-password
                clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.mobilePhone')" prop="mobile" :rules="[
              { required: false, message: t('register.validate.mobileRequired'), trigger: 'blur' },
              { pattern: /^1[2-9]\d{9}$/, message: t('register.validate.mobileFormat'), trigger: 'blur' }
            ]">
              <template #label>
                <span class="mobile-label">{{ t('register.mobilePhone') }}
                    <el-icon style="top: 2px"><Warning /></el-icon>
                    <span class="mobile-tip">{{ t('register.mobilePhoneTip') }}</span></span>
              </template>
              <el-input v-model="form.mobile" :disabled="form.applyType == 2" maxlength="11"
                clearable :formatter="(value) => value.replace(/[^\d.]/g, '')">
                <template #suffix>
                  <el-button :type="form.mobile.length == 11 ? 'primary' : 'default'" link
                     v-loading="getVerificationCodeLoading"
                    v-if="form.applyType == 1 "
                    @click="getVerificationCode" :disabled="form.applyType == 2 || isGettingCode || form.mobile.length != 11">
                    {{ codeButtonText }}
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.verificationCode')" prop="verificationCode" :rules="[
              { validator: validateMobile, trigger: 'blur' },
              { required: false, message: t('register.validate.verificationCodeRequired'), trigger: 'blur' },
              { pattern: /^[0-9]{6}$/, message: t('register.validate.verificationCodeLength'), trigger: 'blur' }
            ]">
              <el-input v-model="form.verificationCode" :disabled="form.applyType == 2"  clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center">
            <el-button type="primary" v-loading="submitBtnLoading"  @click="handleSubmit" :disabled="form.applyType == 2 && emailIsExist && submitBtnLoading">{{ t('register.next') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 选择公司信息 -->
      <el-form v-if="showCompanyForm" :model="companyForm" label-position="top"
        ref="companyFormRef" label-width="120px" class="register-company-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.companyName')" prop="companyName" :rules=" [{ required: true, message: t('register.validate.companyNameEN'), trigger: ['change', 'blur'] }]">
              <el-autocomplete v-model="companyForm.companyName" :fetch-suggestions="remoteTianyanchaCustomer"
                :trigger-on-focus="false" maxlength="200" show-word-limit clearable value-key="name"
                @select="handleCompanyChange" @blur="handleCompanyBlur" :placeholder="t('search')"
                @clear="handleCompanyClear"></el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.taxNo')" prop="taxNo" :rules=" [{ required: false, message: t('register.validate.taxNo'), trigger: 'blur' }]">
              <el-input maxlength="50" show-word-limit :disabled="taxFromTYC" v-model="companyForm.taxNo" @input="handleTaxNoInput"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center">
            <el-button v-if="showJoinCompanyBtn" type="primary" :disabled="isSubmittingDisabled" :loading="isSubmitting"  @click="handleJoinCompany">{{ t('register.joinCompany') }}</el-button>
            <el-button v-if="showFillCompanyBtn" type="primary" @click="handleFillCompany">{{ t('register.fillCompanyInformation') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 填写公司信息 -->
      <el-form v-if="showCompanyDetailForm" :model="companyDetailForm" v-loading="loadingCompanyDetial"
        label-position="top" ref="companyDetailFormRef" class="register-company-detail-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.companyName')" prop="companyNameEn" :rules=" [{ required: true, message: t('register.validate.companyNameEN'), trigger: ['change', 'blur'] }]">
              <!-- <el-input v-model="companyDetailForm.companyNameEn" clearable></el-input> -->
              <el-autocomplete v-model="companyDetailForm.companyNameEn" 
              :fetch-suggestions="remoteTianyanchaCustomerForDetail"
                :trigger-on-focus="false" 
                maxlength="200"
               show-word-limit
                clearable 
                value-key="name"
                @select="handleCompanyChangeForDetail"
               @blur="handleCompanyBlurForDetail"
                :placeholder="t('search')"
                @clear="handleCompanyClearForDetail">
              </el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.companyNameCN')" prop="companyNameZh">
              <el-input maxlength="200" @input="checkTaxExistForDetail" show-word-limit v-model="companyDetailForm.companyNameZh" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.taxNo')"  prop="taxNo" :rules=" [{ required: false, message: t('register.validate.taxNo'), trigger: 'blur' }]">
              <el-input maxlength="50" show-word-limit v-model="companyDetailForm.taxNo" clearable :disabled="taxFromTYC && detailTaxDisabled"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.companyAddress')" prop="companyAddress" :rules=" [{ required: true, message: t('register.validate.companyAddress'), trigger: 'blur' }]">
              <el-input maxlength="300" show-word-limit v-model="companyDetailForm.companyAddress" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.productCategory')" prop="serviceUnitArray" :rules=" [{ required: true, message: t('register.validate.serviceUnits'), trigger: 'change' }]">
              <el-select v-model="companyDetailForm.serviceUnitArray" multiple collapse-tags collapse-tags-tooltip
                         placeholder="  "
                filterable clearable class="register-service-unit-select">
                <el-option 
                  v-for="(item,index) in serviceUnitList" 
                  :key="index" 
                  :label="item.serviceUnitName" 
                  :value="item.serviceUnitCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col class="approve-tip">
              <p>
                <el-icon>
                  <Warning />
                </el-icon>
                  {{ t('register.approveTip') }}
              </p>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('register.sgsContacts')" prop="sgsContacts"
              :rules="[{ type: 'email', message: t('register.validate.emailFormat'), trigger: 'blur' }]">
              <el-input maxlength="200" show-word-limit  v-model="companyDetailForm.sgsContacts" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('register.sgsReportNo')" prop="sgsReportNo" 
            :rules=" [{message: t('register.validate.sgsReportNo'), trigger: 'blur' }]">
              <el-input maxlength="50" show-word-limit v-model="companyDetailForm.sgsReportNo" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center">
            <el-button type="primary" :disabled="isSubmittingDisabled" :loading="isSubmitting"
              @click="handleSubmitAllRegister">{{t('register.submit')}}</el-button>
          </el-col>
        </el-row>
      </el-form>
      <RegistrySuccess v-if="showRegistrySuccess" :emailContact="successEmailContact" />

    </el-card>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import registerApi from '../../api/register';
import { ElNotification, ElMessageBox } from 'element-plus'
import crypto from '../../utils/crypto';
import RegistrySuccess from './RegistrySuccess.vue';
import { useI18n } from 'vue-i18n'; 
import { debounce } from 'lodash';
import {useStore} from "vuex";
const { t,locale } = useI18n();

const store = useStore()

// 获取路由和路由实例
const route = useRoute()
const router = useRouter()

defineOptions({
  name: 'RegisterUser',
});

const formRef = ref();
const countdown = ref(60);
const isCounting = ref(false);
const showAccountForm = ref(true);
const showCompanyForm = ref(false);
const showJoinCompanyBtn = ref(false);
const showFillCompanyBtn = ref(false);
const showCompanyDetailForm = ref(false);
const showRegister = ref(true);
const showRegistrySuccess = ref(false);
const isSubmitting = ref(false);
const isSubmittingDisabled = ref(false);
const taxFromTYC = ref(false);
const detailTaxDisabled = ref(true);
const companyFormRef = ref();
const companyList = ref([]);
const companyListForDetail = ref([]);
const emailIsExist = ref(true);
const companyDetailFormRef = ref();
const serviceUnitList = ref([]);
const successEmailContact = ref('');
const pageToken = ref('');
//最后提交的form
const companyDetailForm = ref({
  companyNameEn: '',
  companyNameZh: '',
  taxNo: '',
  companyAddress: '',
  serviceDomains: '',
  serviceUnitArray: [],
  serviceUnits: '',
  productLineCodes: '',
  sgsContacts: '',
  sgsReportNo: ''
})

//初始化form
const form = ref({
  userName: '',
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: '',
  applyType: 1,
  mobile: '',
  language: '',
  token: '',
  receiveMarketingCommunication: 0
});

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(t('register.validate.passwordRequired')));
  } else {
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumber = /[0-9]/.test(value);
    const hasSpecialChar = /[!@#$%^&*()_\-+=:[\]{}|\\;'"<>,.?/]/.test(value);
    if (!hasUpperCase || !hasLowerCase || !hasNumber || !hasSpecialChar) {
      callback(new Error(t('register.validate.passwordFormat')));
    }  
    if (form.value.confirmPassword !== '') {
      //formRef.value?.validateField('confirmPassword');
    }
    callback();
  }
};

const validatePass2 = (rule, value, callback) => {
  //console.log('rule', rule);
  if (value === '') {
    callback(new Error(t('register.validate.confirmPasswordRequired')));
  } else if (value !== form.value.password) {
    callback(new Error(t('register.validate.passwordNotMatch')));
  } else {
    callback();
  }
};

const validateMobile = (rule, value, callback) => {
  //console.log('rule', rule);
  if (form.value.mobile.length == 11 && !value) {
    callback(new Error(t('register.validate.verificationCodeRequired')));
  } else {
    callback();
  }
}

//公司的form
const companyForm = ref({
  companyName: '',
  taxNo: ''
});

const errRegister = () => {
  ElMessageBox.alert(t('register.validate.errorRegister'), t('register.error'), {
    confirmButtonText: 'OK',
    showClose: false,
    showCancelButton: false,
    showConfirmButton: true,
    closeOnClickModal: false,
    callback: () => {
      window.location.href = "/#/login";
    }
  });
};
onBeforeMount(() => {
  //console.log('route', route);
  //console.log('router', router);
  showRegister.value = false;
  //验证参数
  const param = route.query.param;
  //console.log('pageParam', param);
  if (!param) {
    errRegister();
    return;
  }
  const decodeParam = decodeURIComponent(param);
  //验证信息
  const registerInfo = sessionStorage.getItem('registerInfo');
  //console.log('registerInfo', registerInfo);
  if(!registerInfo) {
    errRegister();
    return;
  }
  //解析参数
  const decryptParam = crypto.decrypt(decodeParam, registerApi.getKey());
  if (!decryptParam) {
    errRegister();
    return;
  }
  //获取参数
  const { email,
    userName,
    mobile,
    applyType,
    verificationCode,
    sign,
    language,
    token,
    receiveMarketingCommunication } = JSON.parse(decryptParam);
  if (!userName || (!email && !mobile) || !sign || ![1, 2].includes(applyType) || !verificationCode || !language || !token) {
    errRegister();
    return;
  }
  //设置注册页面语言
  locale.value = language;
  store.commit('SET_LANGUAGE', language)

  //验证签名
  const md5Value = crypto.md5(email + userName + mobile + applyType + verificationCode + language + token);
  if(md5Value != sign || md5Value != registerInfo) {
    errRegister();
    return;
  }
  form.value.userName = userName;
  form.value.email = email;
  form.value.mobile = mobile;
  form.value.applyType = applyType;
  form.value.verificationCode = applyType == 2 ? verificationCode : '';
  form.value.language = language;
  form.value.token = token;
  form.value.receiveMarketingCommunication = receiveMarketingCommunication;
  pageToken.value = token;
  showRegister.value = true;

  //初始化接口数据
  initServiceUnit();
});

const initServiceUnit = async () => {
  serviceUnitList.value = [];
  registerApi.queryServiceUnit(form.value.language).then(res => {
    //console.log('queryServiceUnit res', res);
    if (res.status == 200) {
      res.data.forEach(item => {
        let { languages } = item;
        let coverLan = registerApi.convertLanguageToOther(form.value.language);
        const serviceObj = (languages || []).find(lan => lan.languageCode == coverLan);
        if (serviceObj && serviceObj.serviceUnitName) {
          item.serviceUnitName = serviceObj.serviceUnitName;
        }
      });
      serviceUnitList.value = res.data.filter(suit => (suit.serviceUnitCode|| '').toLowerCase() !='other');
    }
  }).catch(err => {
    //console.log('queryServiceUnit err', err);
  });
}

const isGettingCode = computed(() => isCounting.value);
const codeButtonText = computed(() => isCounting.value ? `${countdown.value}s` : t('register.getCode'));

const startCountdown = () => {
  isCounting.value = true;
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
      isCounting.value = false;
    }
  }, 1000);
};

// 监听email
const handlerCheckEmailExist = async () => {
    try{
        await formRef.value.validateField('email');
    }catch (e){
        return;
    }
  let {email,mobile,token,language} = form.value;
  registerApi.checkEmail({email,mobile,token,language}).then(res => {
    //console.log('check account res', res);
    if (res.status == 200) {
      emailIsExist.value = false;
    } else {
      emailIsExist.value = true;
    }
  }).catch(err => {
    //console.log('check account err', err);
  })
}
const getVerificationCodeLoading = ref(false);
const getVerificationCode = async () => {
  await formRef.value.validateField('mobile');
  if (form.value.mobile === '') {
    return;
  }
    getVerificationCodeLoading.value = true;
  let { email, userName, mobile, token, language } = form.value;
  registerApi.sendSmsCode({ email, userName, mobile, token, language }).then(res => {
    //console.log('send code res', res);
      getVerificationCodeLoading.value = false;
    if (res.status == 200) {
      startCountdown();
    }
  }).catch(err => {
      getVerificationCodeLoading.value = false;
    //console.log('send code  err', err);
  });
};

const handleJoinCompany = async () => {
    try{
        await companyFormRef.value.validate();
    }catch (e){
        return
    }
  //console.log('companyForm', companyForm.value);
  isSubmittingDisabled.value = true;
  isSubmitting.value = true;

  //组装后端需要的对象数据
  let param = {
    ...form.value,
    joinCompany:1,
    accountCompany: {
      companyNameEn: companyForm.value.companyName,
      companyNameZh: companyForm.value.companyName,
      taxNo: companyForm.value.taxNo
    }
  }
  registerApi.submit(param).then(res => {
    //console.log('submit res', res);
    isSubmittingDisabled.value = false;
    isSubmitting.value = false;
    if (res.status == 200) {
      let {email} = res.data || {};
      showCompanyForm.value = false;
      successEmailContact.value = email || '';
      showRegistrySuccess.value = true;
    }
  }).catch(err => {
    //console.log('submit err', err);
    isSubmittingDisabled.value = false;
    isSubmitting.value = false;
  });
};

const submitBtnLoading = ref(false);
const handleSubmit = async () => {
  if (!formRef.value) return;
  try{
    await formRef.value.validate();
  }catch (e){
      return;
  }
  if (form.value.applyType == 2 && emailIsExist.value) {
      ElNotification.error(t('register.validate.emailExist'));
    return;
  }
  let { applyType } = form.value;
  //邮件注册
  if (!form.value.mobile || applyType == 2) {
    showCompanyForm.value = true;
    showAccountForm.value = false;
    return;
  }
    submitBtnLoading.value = true;
  let { email, userName, mobile, token, language ,verificationCode} = form.value;
  registerApi.checkSmsCode({ email, userName, mobile, token, language, verificationCode }).then(res => {
    //console.log('check sms code res', res);
      submitBtnLoading.value = false;
    if (res.status == 200) {
      showCompanyForm.value = true;
      showAccountForm.value = false;
    } else {
    }
  }).catch(err => {
    //console.log('check sms code err', err);
      submitBtnLoading.value = false;
  });
};

const handleCompanyClear = () => {
  companyFormRef.value.resetFields();
  showJoinCompanyBtn.value = false;
  showFillCompanyBtn.value = false;
}
// 需要防抖处理
const handleTaxNoInput = debounce((e) => {
  //console.log('input companyForm.value.taxNo',e);
    if(taxFromTYC.value ){
        return
    }
    showJoinCompanyBtn.value = false;
    showFillCompanyBtn.value = false;
    checkTaxExist();
}, 1000);

const remoteTianyanchaCustomer = (keyWord,cb) => {
    //console.log('query', keyWord);
    companyList.value = [];
    companyForm.value.taxNo = '';
    taxFromTYC.value = false;
    showJoinCompanyBtn.value = false;
    showFillCompanyBtn.value = false;
    if (!keyWord || keyWord.length<=2) {
        return;
    }
    let param = {
        token: form.value.token,
        companyName:keyWord
    }
    registerApi.getTianyanchaCustomer(param).then(res => {
        //console.log('getTianyanchaCustomerres', res);
        let currentInput = { id: Math.random(), name: keyWord, creditCode: '' };
        if (res.status == 200 && res.data && res.data.result) {
            companyList.value = res.data.result.items || [];
            let exisKeyWord = companyList.value.find(c => c.name === keyWord);
            if (!exisKeyWord) {
                companyList.value.unshift(currentInput);
            }
        } else {
            companyList.value = [currentInput];
        }
        cb(companyList.value);
    }).catch(err => {
        //console.log('err', err);
    });
}

const handleCompanyBlur = () => {
  if (!showJoinCompanyBtn.value && !showFillCompanyBtn.value) {
    //showFillCompanyBtn.value = true;
      checkTaxExist();
  }
}

const handleCompanyChange = (value) => {
  //console.log('handleCompanyChange value', value);
  //从companyList中找到value对应的company 并且赋值给taxNo
  const company  = companyList.value.find(item => item.id === value.id) || {};
  companyForm.value.taxNo = '';
  taxFromTYC.value = false;
  showJoinCompanyBtn.value = false;
  showFillCompanyBtn.value = false;
  if (company.creditCode) {
    companyForm.value.taxNo = company.creditCode;
    taxFromTYC.value = true;
  }
  checkTaxExist();
}
const checkTaxExist = ()=>{
    if(!companyForm.value.taxNo && !companyForm.value.companyName){
        return
    }
    //利用税号，check是否存在
    let { email, userName, mobile, applyType, language, token } = form.value;
    let accountCompany = {
        companyNameEn: companyForm.value.companyName,
        taxNo: companyForm.value.taxNo
    }
    let param = { email, userName, mobile, applyType, language, token, accountCompany };
    registerApi.taxNoExist(param).then(res => {
        //console.log('check company exist res', res);
        taxFromTYC.value = false;
        if (res.status == 200) {
            if (!res.data || Object.keys(res.data).length == 0) {
                showFillCompanyBtn.value = true;
            } else {
                companyForm.value.taxNo = res.data.taxNo;
                taxFromTYC.value = true;
                showJoinCompanyBtn.value = true
            }
        }
    }).catch(err => {
        //console.log('check company exist err', err);
    });
}

const handleFillCompany = async () => {
    if (!companyFormRef.value) return;
    try{
        await companyFormRef.value.validate();
    }catch (e){
        return
    }
  showAccountForm.value = false;
  showCompanyForm.value = false;
  companyDetailForm.value.companyNameEn = companyForm.value.companyName;
  companyDetailForm.value.taxNo = companyForm.value.taxNo;
  showCompanyDetailForm.value = true;
}
const mergeServiceUnits = () => {
  let productLineCodes = [];
  let serviceDomains = [];
  let serviceUnits = [];
  let setDefault  = false;
  serviceUnitList.value.forEach(item => {
    if (companyDetailForm.value.serviceUnitArray.includes(item.serviceUnitCode)) {
      let {productLineCode, serviceDomain, serviceUnitCode} = item;
      let isDefault = false;
      //默认第一个设置为default
      if(!setDefault){
        isDefault = true;
        setDefault = true;
      }
      let productLineObj = {productLineCode, isDefault};
      let serviceDomainObj = {serviceDomain, isDefault}; 
      let serviceUnitObj = {serviceUnit:serviceUnitCode, isDefault};

      productLineCodes.push(productLineObj); 
      serviceDomains.push(serviceDomainObj);
      serviceUnits.push(serviceUnitObj);
    }
  });
  companyDetailForm.value.productLineCodes = JSON.stringify(productLineCodes);
  companyDetailForm.value.serviceDomains = JSON.stringify(serviceDomains);
  companyDetailForm.value.serviceUnits = JSON.stringify(serviceUnits);
}

const handleSubmitAllRegister = async () => {
    try{
        await companyDetailFormRef.value.validate();
    }catch (e){
        return
    }
  //console.log('companyDetailForm', companyDetailForm.value);
  isSubmittingDisabled.value = true;
  isSubmitting.value = true;
  //合并serviceUnits  
  mergeServiceUnits();
  //组装后端需要的对象数据
  let param = {
    ...form.value,
    serviceDomains: companyDetailForm.value.serviceDomains,
    serviceUnits: companyDetailForm.value.serviceUnits,
    productLineCodes: companyDetailForm.value.productLineCodes,
    accountCompany: {
      companyNameEn: companyDetailForm.value.companyNameEn,
      companyNameZh: companyDetailForm.value.companyNameZh,
      taxNo: companyDetailForm.value.taxNo,
      companyAddress: companyDetailForm.value.companyAddress,
      contacts: companyDetailForm.value.sgsContacts,
      reportNo: companyDetailForm.value.sgsReportNo
    }
  }
  registerApi.submit(param).then(res => {
    //console.log('submit res', res);
    isSubmittingDisabled.value = false;
    isSubmitting.value = false;
    if (res.status == 200) {
      let {joinCompany,email} = res.data || {};
      if(joinCompany==1){
        companyExist();
        return;
      }
      showCompanyDetailForm.value = false;
      successEmailContact.value = email || '';
      showRegistrySuccess.value = true;
    }
  }).catch(err => {
    //console.log('submit err', err);
    isSubmittingDisabled.value = false;
    isSubmitting.value = false;
  });
}
const handleCompanyBlurForDetail = ()=>{
    if(taxFromTYC.value){
        return
    }
    checkTaxExistForDetail();
}
// 公司详情页的公司选择
const handleCompanyChangeForDetail = (value) => {
  //console.log('handleCompanyChangeForDetail value', value);
  //从companyListForDetail中找到value对应的company 并且赋值给taxNo
  const company  = companyListForDetail.value.find(item => item.id === value.id) || {};
  companyDetailForm.value.taxNo = company.creditCode;
  detailTaxDisabled.value = true;
  taxFromTYC.value = true;
  checkTaxExistForDetail();
} 
const checkTaxExistForDetail = () => {
  let { email, userName, mobile, applyType, language, token } = form.value;
  let accountCompany = {
      companyNameEn: companyDetailForm.value.companyNameEn,
      companyNameZh: companyDetailForm.value.companyNameZh,
      taxNo: companyDetailForm.value.taxNo
  }
  let param = { email, userName, mobile, applyType, language, token, accountCompany };
  isSubmittingDisabled.value = true;
  registerApi.taxNoExist(param).then(res => {
    //console.log('check company exist res', res);
      isSubmittingDisabled.value = false;
    if (res.status == 200) {
      if (!res.data || Object.keys(res.data).length == 0) {
        isSubmittingDisabled.value = false;
      } else {
        companyDetailForm.value.taxNo = res.data.taxNo;
          taxFromTYC.value=true;
          detailTaxDisabled.value = true;
        companyExist();
      }
    }
  }).catch(err => {
      isSubmittingDisabled.value = false;
    //console.log('check company exist err', err);
  });
}
const companyExist = ()=>{
  ElMessageBox.confirm(t('register.companyExist'), t('tip'), {
    confirmButtonText: t('submitText'),
    cancelButtonText:t('cancelText'),
    type: 'warning'
  }).then(()=>{
    gotojoinCompany();
  }).catch(()=>{
    isSubmittingDisabled.value = false;
  });
}

const gotojoinCompany = () => {
  //组装后端需要的对象数据
  let param = {
    ...form.value,
    joinCompany:1,
    accountCompany: {
      companyNameEn: companyDetailForm.value.companyNameEn,
      companyNameZh: companyDetailForm.value.companyNameZh,
      taxNo: companyDetailForm.value.taxNo
    }
  }
  registerApi.submit(param).then(res => {
    //console.log('submit res', res);
    isSubmittingDisabled.value = true;
    if (res.status == 200) {
      let {email} = res.data || {};
      showCompanyDetailForm.value = false;
      successEmailContact.value = email || '';
      showRegistrySuccess.value = true;
    }
  }).catch(err => {
    //console.log('submit err', err);
    isSubmittingDisabled.value = false;
  });
}

const handleCompanyClearForDetail = (value) => {
  companyDetailForm.value.taxNo = ''
  detailTaxDisabled.value = false;
}


const remoteTianyanchaCustomerForDetail = (keyWord,cb) => {
  //console.log('query', keyWord);
    companyListForDetail.value = [];
    companyDetailForm.value.taxNo = '';
    detailTaxDisabled.value = false;
    taxFromTYC.value = false;
    if (!keyWord || keyWord.length<=2) {
        return;
    }
    let param = {
        token: form.value.token,
        companyName:keyWord
    }
    registerApi.getTianyanchaCustomer(param).then(res => {
        //console.log('getTianyanchaCustomerres', res);
        let currentInput = { id: Math.random(), name: keyWord, creditCode: '' };
        if (res.status == 200 && res.data && res.data.result) {
            companyListForDetail.value = res.data.result.items || [];
            let exisKeyWord = companyListForDetail.value.find(c => c.name === keyWord);
            if (!exisKeyWord) {
                companyListForDetail.value.unshift(currentInput);
            }
        } else {
            companyListForDetail.value = [currentInput];
        }
        cb(companyListForDetail.value);
    }).catch(err => {
        //console.log('err', err);
    });
}

</script>

<style lang="scss">
.register-container {
  width: 100%;
  padding: 20px;

  .logo {
    text-align: center;
    margin-bottom: 20px;

    img {
      height: 40px;
    }
  }
  .el-form-item__content {
    padding-left: 10px !important;
  }
    .register-card {
      width: 60vw;
      min-height: 550px;
      max-height: 650px;
      margin: 0 auto;

      .el-card__body {
        padding: 40px;
      }

      .welcome-text {
        font-size: 16px;
        margin-bottom: 20px;
        color: #606266;
      }
    }

    .register-form {
      .mobile-input {
        display: flex;
        align-items: center;

        :deep(.el-input-group__append) {
          padding: 0;

          .el-button {
            margin: 0;
            border: none;
            border-radius: 0;
            height: 100%;
          }
        }
      }

      .mobile-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }

      .next-button {
        width: 100%;
        margin-top: 20px;
      }

    }

    .register-company-detail-form {
      .approve-tip {
        font-size: 12px;
        color: #909399;
        padding: 15px 0 5px 0;
        margin-bottom: 10px;
        p{
          padding-bottom: 2px;
          border-bottom: solid 1px #909399;
        }
        .el-icon{
          top:2px;
        }
      }

      div.register-service-unit-select .el-select__selected-item {
        max-width: 250px !important;
        overflow: hidden;
      }
    }
}

</style>
