<template>
  <basic-container>
    <div class="sgs_smart_new_material_detail" id="sgs_smart_new_material_detail">
        <!-- header info-->
        <div class="header_detail">
          <div class="detail-header">
            <h4 class="detail-header-title">Material Detail</h4>
            <div>
              <div v-if="materialObj.sampleNo" class="trf-number">
                {{ materialObj.sampleNo }}
              </div>
              <span v-else class="trf-no-data">No Data</span>
            </div>
            <div class="info-list">
              <div class="left">
                <div class="item">
                    <p class="tit">Buyer：</p>
                    <el-tooltip effect="dark" :content="templateObj.customerGroupName||''" placement="top" v-if="(templateObj.customerGroupName || '').length > 20">
                        <span>{{templateObj.customerGroupName.slice(0,20)+'...'}}</span>
                    </el-tooltip>
                    <span v-else>{{templateObj.customerGroupName}}</span>
                </div>
                <div class="item">
                    <p class="tit">Template：</p>
                    <el-tooltip effect="dark" :content="materialObj.templateName || templateObj.templateName || ''" placement="top" v-if="(materialObj.templateName || templateObj.templateName || '').length > 20">
                        <span>{{ (materialObj.templateName || templateObj.templateName )+'...'}}</span>
                    </el-tooltip>
                    <span v-else>{{ materialObj.templateName || templateObj.templateName || '' }}</span>
                </div>
              </div>
              <div class="dates">
                  <span class="date"><span class="tit">Create Time：</span>{{ currentTz_YMD(materialObj.createTime || nowDate)}}</span>&nbsp;&nbsp;
                  <span class="date" v-if="materialObj.updateTime"><span class="tit">Update Time：</span>{{ currentTz_YMD(materialObj.updateTime)}}</span>
              </div>
            </div>
          </div>
            <!-- tabs button-->
            <div class="sgs_smart_detail_button_tabs">
                <el-button type="text" :class="{'active':detailName=='materialInfo'}" @click="detailName = 'materialInfo'">Material Info</el-button>
                <el-button
                    type="text"
                    :class="{ active: detailName == 'documentsTests' }"
                    @click="detailName = 'documentsTests'"
                    >Documents & Tests</el-button
                  >
                <el-button type="text" v-if="materialObj.sampleNo" :class="{'active':detailName=='auditTrail'}" @click="detailName = 'auditTrail'">Audit Trail</el-button>
            </div>
        </div>
        <!-- content component-->
        <material-info key="materialInfo" v-if="detailName=='materialInfo'" @getMaterialInfo="getMaterial" @getMaterialTemplateInfo="getTemplate"></material-info>
        <audit-trail
                v-if="detailName=='auditTrail'"
                key="auditTrail"
                :objects="[
                    {objectType:'SmartPBM-Material',name:'History',rootObjectId:materialObj.id,objectDate:materialObj.createTime},
                    {objectType:'SmartPBM-Material-TRF',name:'Material-TRF',rootObjectId:materialObj.id,objectDate:materialObj.createTime},
                ]"
                :object-no="materialObj.sampleNo"
        ></audit-trail>
        <documents-tests
          key="documentsTests"
          v-if="detailName == 'documentsTests'"
        ></documents-tests>
    </div>

  </basic-container>
</template>

<script>
import MaterialInfo from "./materialInfo";
import AuditTrail from "../materialAndProductCommon/auditTrail";
import MaterialStatus from "../commonComponent/materialStatus";
import DocumentsTests from "./documentsTests.vue";
import moment from "moment";
import {tzFormChina} from '@/util/datetimeUtils'

export default {
  name: "detail",
  data() {
    return {
      detailName: "materialInfo",
      materialObj: {},
      templateObj: {},
      nowDate: "",
      interval: null,
    };
  },
  methods: {
    getMaterial(data) {
      this.materialObj = data;
    },
    getTemplate(data) {
      this.getNow();
      this.templateObj = data;
    },
    getNow() {
      this.nowDate = moment(new Date()).format("YYYY-MM-DD HH:mm");
      this.interval = setInterval(() => {
        this.nowDate = moment(new Date()).format("YYYY-MM-DD HH:mm");
      }, 1000);
    },
    currentTz_YMD(val) {
      if (!val) return ''
      let value = tzFormChina(val, 'YYYY-MM-DD HH:mm:ss');
      return moment(value).format('YYYY-MM-DD');
    },
  },
  mounted() {},
  created() {},
  beforeDestroy() {
    this.interval && clearInterval(this.interval);
  },
  watch: {},
  computed: {},
  props: {},
  components: { AuditTrail, MaterialInfo, MaterialStatus, DocumentsTests },
};
</script>

<style lang="scss">
@import "@/styles/unit.scss";

.sgs_smart_new_material_detail {
  .header_detail {
    padding: $module-padding;
    background: #fff;
    position: relative;

    .trf-number {
      font-size: 24px;
      font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
        "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
      color: #000000;
      //   line-height: 24px;
      padding: $inline-element-spacing 0;
      display: inline-block;
      word-break: break-all;
    }
    .trf-no-data {
      color: #999;
      font-size: 14px;
      padding: $inline-element-spacing 0;
      display: inline-block;
    }
    .tit {
      color: $text-color-value;
      margin: 0;
    }
    .detail-header{
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 14px;
    }
    .detail-header-title{
      font-size: 16px;
      color: $text-color-value;
      line-height: 20px;
    }
    .info-list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #000000;

      .left{
        display: flex;
      }
      .item{
        display: flex;
        align-items: center;
        margin-right: 30px;
      } 
      .dates {
        display: flex;
      }
      .date {
        font-size: 14px;
        font-weight: 400;
        margin-left: 30px;
        display: flex;
      }
    }
    .info {
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 14px;
      
      .text-value-color {
        color: #000;
        display: inline-block;
      }
      h4 {
        font-size: 16px;
        font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
          "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
        color: $text-color-value;
        line-height: 20px;
      }
      > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
          display: flex;
          align-items: center;
          .status {
            float: left;
          }
          .dates {
            display: flex;
          }
          .date {
            font-size: 14px;
            font-weight: 400;
            // color: #656565;
            // vertical-align: text-top;
            // margin-top: -2px;
            // margin-left: 30px;
            margin-left: 30px;
            display: flex;
            flex-direction: column;

            .date-value {
              padding-top: 10px;
            }
          }
        }
        .right {
          display: flex;
          .item {
            padding-left: 20px;
            font-size: 14px;
            font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
              "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
            font-weight: 400;
            color: #000;
            line-height: 22px;
            // border-right: 1px solid  #e6e6e6;
            display: flex;
            flex-direction: column;
            p {
              margin-bottom: 2px;
              display: inline-block;
            }
            span {
              padding-top: 10px;
            }
            button {
              padding: 0;
            }
          }
        }
      }
    }
  }
  .sgs_smart_detail_button_tabs {
    border: 1px solid #e6e6e6;
    margin-top: 11px;
    padding: 4px;
    width: fit-content;
    button {
      padding: 0 16px;
      height: 32px;
      line-height: 32px;
      &:not(.is-disabled) {
        color: #1b1b1b;
        cursor: pointer;
        &.active {
            color: $primary-color;
        }
      }
    }
  }

  div.divider_no_margin {
    margin: 0 !important;
  }

  .el-card {
    margin-bottom: 20px;
    h4 {
      font-weight: bold;
    }
  }
  .section_title {
    padding: 10px 0;
    font-weight: bold;
  }
}
@media screen and (max-width: 1600px) {
  .header_detail {
    .info > div {
      .left {
        .dates {
          // margin-top: 25px;
        }
        .date {
          .tit {
            display: block;
          }
        }
      }
      .right {
        // margin-top: 22px;
        .item {
          p {
            display: block;
            color: #656565;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .header_detail {
    padding-top: 7px;
    padding-bottom: 10px;
    .info {
      padding-bottom: 11px;
    }
  }
}
</style>
