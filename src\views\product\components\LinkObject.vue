<template>
    <div class="smart_views_LinkObject" id="smart_views_LinkObject">
        <CommonTable
                border
                v-loading="tableOption.loading"
                ref="linkDocTable"
                style="width:100%"
                row-key="id"
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
        >
            <template #docName="{row,$index}">
                <el-button text type="primary" @click="downloadObj(row)">{{row.docName}}</el-button>
            </template>
            <template #actionColumn="{row}">
                <el-button text type="primary" @click="selectObject(row)">Select</el-button>
            </template>
        </CommonTable>
    </div>
</template>


<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'
import CommonTable from "@/components/TableList/CommonTable.vue";
import productApi from '@/api/product.ts'

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)

const props = defineProps({
    objectId:{
        type:String,
        default:''
    }
})
defineOptions({
    name: 'LinkObject'
})

const emit = defineEmits(['linkSuccess'])

watch(language, (newVal) => {
})
onMounted(() => {
    initTableList();
})
const initTableList = ()=>{
    productApi.workbookApi.loadObjectRelationList().then(res=>{
        console.log("link initTableList",res)
        if(res.status==200 && res.data ){
            tableOption.dataList = res.data || [];
        }
    })
}


const tableOption = reactive({
    dataList: [],
    originalDataList: [],
    loading: false,
    menuShow: true,
    size: 'small',
    option: {
        hideRowColor: true,
        selection: false,
        selectionDis: ()=>{return true},
        sortSelectionFiledName: '',
        showSortIcon: false,
        index: false,
        action: true,
        actionWidth: 150,
        disableOption:{},
        column: [
            {prop: 'docNo', label: 'Workbook No', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'docName', label: 'Workbook', hide: false, filter: true, slot: true, type: 'Input'},
            {prop: 'description', label: 'Description', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'versionNo', label:  'Revision', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateTime', label: 'Update Time', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateUser', label: 'Update By', hide: false, filter: true, slot: false, type: 'Input'},

        ]
    },
    sort:{
        sortBy:"",
        sortOrder:""
    },
    filters: {},
    page: {
        show: false,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})

const selectObject = (row)=>{
    console.log("selectObject",row)
    let objectId = props.objectId;
    let {docNo} = row;
    productApi.workbookApi.linkExistDoc({objectId,docNo}).then(res=>{
        if(res.status==200){
            emit('linkSuccess');
        }
    })
}
const downloadObj = (row)=>{

}

</script>

<style lang="scss">
.smart_views_LinkObject {

}
</style>