import request from './request'
import { encryptor } from '@/utils/auth'

// 定义通用的请求参数类型
type RequestParams = Record<string, any>

const sendPost = (
  url: string,
  param: RequestParams,
  headers?: RequestParams,
): Promise<any> => {
  headers = headers ? headers : {}
  return request({
    url,
    method: 'post',
    headers: {
      loading: 'false',
      ...headers,
    },
    data: param,
  })
}
const sendGet = (
  url: string,
  param: RequestParams,
  headers?: RequestParams,
): Promise<any> => {
  url = url + '?hash=' + new Date().getTime()
  return request({
    url,
    method: 'get',
    headers: {
      loading: 'false',
      ...headers,
    },
    params: param,
  })
}

const defaultKey = 'U0dTX1NNQVJUX1JFRzFzdGVyX1BBR0VfQkFTRTY0S0VZ'

const SERVICE_UNIT_LANGUAGE_CHI = 'CHI'
const SERVICE_UNIT_LANGUAGE_EN = 'EN'
const convertLanguage = (language: string) => {
  let newLanguage = SERVICE_UNIT_LANGUAGE_EN //默认英文
  switch (language) {
    case 'zh-CN':
      newLanguage = SERVICE_UNIT_LANGUAGE_CHI
      break
    default:
      newLanguage = SERVICE_UNIT_LANGUAGE_EN
      break
  }
  return newLanguage
}

const api = {
  getKey: () => {
    return defaultKey
  },
  getTianyanchaCustomer: (param: RequestParams) => {
    return sendPost('/sgs-mart/register/account/searchCompany', param)
  },
  checkCustomer: (param: RequestParams) => {
    return sendPost('/sgs-mart/register/account/checkAccount', param)
  },
  taxNoExist: (param: RequestParams) => {
    return sendPost('/sgs-mart/register/account/companyExist', param)
  },
  checkEmail: (param: RequestParams) => {
    return sendPost('/sgs-mart/register/account/checkEmail', param)
  },
  getLanguageByRemoteIp: (param: RequestParams) => {
    return sendGet('/sgs-mart/userSetting/getLanguageByRemoteIp', param)
  },
  queryServiceUnit: (language: string) => {
    const newLanguage = convertLanguage(language)
    return sendPost('/sgsapi/FrameWorkApi/serviceUnit/queryServiceUnit', {
      languageCode: [newLanguage],
    })
  },
  updateDefaultServiceUnit: (param: RequestParams) => {
    return sendGet('/sgs-mart/customer/account/updateServiceUnit', param)
  },
  submit: (param: RequestParams) => {
    const pwd: string | null = encryptor(param.password)
    param.password = pwd
    param.confirmPassword = pwd
    return sendPost('/sgs-mart/register/account/submit', param)
  },
  //手机号验证码
  sendSmsCode: (param: RequestParams) => {
    return sendPost('/sgs-mart/register/account/sendSms', param)
  },
  //验证手机验证码
  checkSmsCode: (param: RequestParams) => {
    return sendPost('/sgs-mart/register/account/checkV2code', param)
  },
  //获取公司信息
  getCompanyInfo: () => {
    return sendGet(
      '/sgs-mart/customer/detail-for-user',
      {},
      { loading: true },
    )
  },
  queryTianYanCha: (keyWord: string) => {
    return sendGet('/sgs-mart/common/tianyancha/search', { keyWord })
  },
  updateCompanyInfo: (param: RequestParams) => {
    return sendPost('/sgs-mart/customer/updateCompanyInfo', param)
  },
}

export default api
