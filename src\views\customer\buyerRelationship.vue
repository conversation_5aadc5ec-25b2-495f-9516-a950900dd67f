<template>
    <div>
        <el-form :inline="true" :model="formInline" @submit.native.prevent size="small" class="text-right">
            <el-form-item>
                <el-input
                        :placeholder="$t('account.customerName')"
                        v-model="query.customerName" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button class="line-btn" @click="onSearch"><i class="el-icon-circle-plus-outline"></i> {{$t('operation.search')}}</el-button>
            </el-form-item>
        </el-form>
        <el-table
                :data="tableData"
                style="width: 100%"
                size="medium">
            <el-table-column
                    type="index"
                    fixed
                    label="#"
                    width="40">
            </el-table-column>
            <el-table-column
                    fixed
                    prop="customerNameEn"
                    :label="$t('account.customerName')">
            </el-table-column>
            <el-table-column
                    prop="status"
                    :label="$t('common.status.title')"
                    width="180">
                <template scope="scope">
                    <span v-if="scope.row.status=='0'">{{$t('common.status.notPush')}}</span>
                    <span v-if="scope.row.status=='1'">{{$t('common.status.push')}}</span>
                    <span v-if="scope.row.status=='2'">{{$t('common.status.reject')}}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')" :formatter="formtterDate"
                    width="180">
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="180">
                <template slot-scope="scope">
                    <el-button type="text" v-if="scope.row.status === 0 && permissionList.editBtn" @click="detailRow(scope.row)" size="small" icon="el-icon-edit">{{$t('common.pass')}}</el-button>
                    <el-button type="text" v-if="scope.row.status === 0 && permissionList.deleteBtn" @click="rejectRow(scope.row)" size="small" icon="el-icon-delete">{{$t('operation.reject')}}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
    </div>
</template>

<script>
    import {getPageByUser,add,remove, update,approve,reject} from "@/api/customer/buyerRelationship";
    import moment from 'moment'
    import { mapGetters, mapState } from "vuex";
    export default {
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        name: "buyerRelationship",
        data(){
            return{
                name: "buyerRelationship",
                dialogFormVisible: false,
                btnGuestbookSubmit: false,
                title: '',
                tableData: [],
                form: {},
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
            }
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    deleteBtn: this.vaildData(this.permission['sgs:customer:relationship:conclusionship:delete'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:relationship:conclusionship:edit'],false),
                };
            }
        },
        methods:{
            onSearch() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page) {
                console.log("userInfo",this.userInfo);
                const params = {};
                params.scmCustomerName = this.query.customerName;
                params.scmCustomerNo = this.userInfo.bossNo;
                const customerGroupCode =  this.userInfo.customerGroupCode;
                if(customerGroupCode != null && customerGroupCode !== ''){
                  params.scmCustomerGroupCode = this.userInfo.customerGroupCode;
                }
                getPageByUser(page.currentPage, page.pageSize,params).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.btnGuestbookSubmit=true;
                        add(this.form).then(res =>{
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.btnGuestbookSubmit = false;
                            this.dialogFormVisible=false;
                            this.onLoad(this.page);
                        });
                    } else {
                        return false;
                    }
                });

            },
            rejectRow(row){
                this.$confirm(this.$t('operation.confirmStatus'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                        const param = {};
                        param.id = row.id;
                        reject(param);
                    }).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
            },
            detailRow(row){
                this.$confirm(this.$t('operation.confirmStatus'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    const params = {};
                    params.id = row.id;
                    approve(params).then(res =>{
                        if("1" == res.data.success){
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.onLoad(this.page);
                        }else{
                            this.$message({
                                type: "error",
                                message: res.data.errorMessage
                            });
                        }
                    });
                })
            },
            addRow(){
                this.form = {};
                this.title = this.$t('reviewConclusion.title.add');
                this.dialogFormVisible =true;
            },
            formtterDate(row,column){
                var date = row[column.property];

                if(date == undefined || date==''){return ''};

                return moment(date).format("YYYY-MM-DD")
            },
        },
        created() {
            this.onLoad(this.page);
        },

    }
</script>

<style scoped>

</style>
