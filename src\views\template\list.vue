<template>
    <basic-container >
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.templateManagement')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{ $t('navbar.templateManagement') }}</h1>

        <el-card shadow="never" class="box-card">
            <el-row>
                <el-form :inline="true" :model="query" size="medium" label-width="200px" >
                <el-form-item>
                    <el-select clearable filterable :placeholder="$t('term.productLine')" v-model="query.productLineCode" style="width:100%" >
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select clearable :placeholder="$t('term.customerGroup')" v-model="query.customerGroupCode" filterable style="width:100%">
                        <el-option v-for="(customerGroup,index) in customerGroupData" :label="customerGroup.customerGroupName"
                                :value="customerGroup.customerGroupCode" ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input clearable :placeholder="$t('template.templateName')"  v-model="query.templateName" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchTemplateList()"  v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.search')}}</el-button>
                    <reset-button @click="clear"></reset-button>
                </el-form-item>
            </el-form>
            </el-row>
            <el-row>
                <el-row align="right" style="margin: 5px 0px">
                    <el-button  type="primary" icon="el-icon-edit" size="medium" v-if="permissionList.addBtn"  @click="addTemplate()">{{$t('operation.add')}}</el-button>
                </el-row>
                <el-table ref="labTable" :row-key="getRowKeys" v-loading="loading" :element-loading-text="$t('loading')" :data="data"  style="width: 100%" >
                    <el-table-column fixed type="index" label="#" width="50"> </el-table-column>
                    <el-table-column fixed prop="templateName" :label="$t('template.templateName')" min-width="250" :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{scope.row.templateName}}</div>
                            <!-- <el-tooltip class="item" effect="dark" :content="scope.row.templateName" placement="top">
                                <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{scope.row.templateName}}</div>
                            </el-tooltip> -->
                        </template>
                    </el-table-column>
                    <el-table-column prop="productLineName" :label="$t('template.productLine')"  min-width="200"></el-table-column>
                    <el-table-column prop="productCategoryName" :label="$t('template.productCategory')"  min-width="200"></el-table-column>
                    <el-table-column prop="customerGroupName" :label="$t('template.customerGroup')"  min-width="320" ></el-table-column>
                    <el-table-column prop="dffName" :label="$t('template.dff')" min-width="200"></el-table-column>
                    <el-table-column prop="updateUser" :label="$t('user.updateUser')" min-width="120"></el-table-column>
                    <el-table-column prop="updateTime" :label="$t('user.updateTime')" min-width="120"></el-table-column>
                    <el-table-column :label="$t('user.status')" align="center" min-width="80">
                        <template slot-scope="scope">
                            <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                                <el-switch
                                        :disabled="!permissionList.changeStatusBtn"
                                        v-model="scope.row.status"
                                        active-color="#ff6600"
                                        inactive-color="#d9d9d9"
                                        :active-value="1"
                                        :inactive-value="0"
                                        @change="isDisabledTemplate(scope.row)">
                                </el-switch>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('operation.title')" width="180" align="center">
                        <template slot-scope="scope">
                            <el-button  @click="editTemplate(scope.row)" v-if="permissionList.editTemplateBtn"  type="text">{{$t('operation.edit')}}</el-button>
                            <el-button  @click="deleteTemplate(scope.row)" v-if="permissionList.deletedTemplateBtn"  type="text">{{$t('operation.remove')}}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
    </basic-container>
</template>

<script>
    //import {getList, remove, update, add,getLabTypeList,detail,getLabContactList,updateContact,detailContact,removeContact} from "@/api/lab/lab";
    import {getList,updateTemplateStatus,remove} from "@/api/template/template";
    import {getDictByCode, getCustomerGroup, getProductLine} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import resetButton from "@/components/resetButton/resetButton.vue";

    export default {
        components: { resetButton },
        created() {
            this.queryproductLineData();
            this.queryCustomerGroupData();
            this.searchTemplateList();
            //this.queryLabTypeSelData("labType");
        },

        data() {
            return {
                getRowKeys(row) {
                    return row.id
                },
                template:{
                    id:'',
                    status:''
                },
                productLineData: [],
                customerGroupData: [],
                specificData:[],
                currentRow:{},
                expands:[],
                dialogLabFormVisible:false,
                dialogLabContactFormVisible:false,
                isDisabled: false,
                fullscreenLoading: false,
                //formLabelWidth: '120px',
                labFormTitle:this.$t('operation.add'),
                labContactFormTitle:this.$t('operation.add'),
                query: {},
                sort: {descs:'t.update_time'},
                loading:false,
                contactloading:false,
                total: '',
                modalFlag: false,
                data: [],
                tableData:[],
                labTypeData:[],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                searchForm: {
                    labName:'',
                    labType: ''
                },
                contactForm:{
                    labId:'',
                },
                labForm:{
                    labName:'',
                    labType:'',
                    id:'',
                },
                labContactForm:{
                    contactName:'',
                    contactTel:'',
                    contactEmail:'',
                    labId:'',
                    id:''
                },
                labId:'',
                rules: {
                    labName: [
                        { required: true, message: this.$t('lab.selLabName'), trigger: 'blur' },
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    labType: [
                        { required: true, message: this.$t('lab.selLabType') }
                    ]
                },
                contactRules:{
                    contactName: [
                        { required: true, message: this.$t('labContact.selContactName'), trigger: 'blur' },
                        /*{ max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' },*/
                    ],
                    contactTel: [
                        { required: true, message: this.$t('labContact.selContactTel'), trigger: 'blur' },
                        /*{ max: 20, message: '电话长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    contactEmail: [
                        { required: true, message: this.$t('labContact.selContactEmail'), trigger: 'blur' },
                        /*{ max: 30, message: '邮箱长度不可大于30个字符', trigger: 'blur' }*/
                    ],
                }
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    disableBtn:this.vaildData(this.permission['sgs:template:disable'],false),
                    editTemplateBtn: this.vaildData(this.permission['sgs:template:edit'],false),
                    deletedTemplateBtn: this.vaildData(this.permission['sgs:template:deleted'],false),
                    addBtn: this.vaildData(this.permission['sgs:template:add'], false),
                    changeStatusBtn: this.vaildData(this.permission['sgs:template:changeStatus'], false),
                };
            }
        },
        methods: {
            deleteTemplate(row){
                this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    remove(row.id).then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });

                        this.onLoad(this.page);
                    }, error => {
                        console.log(error);
                    });

                }).catch(() => {
                    /* this.$message({
                         type: 'info',
                         message: '已取消删除'
                     });*/
                });
            },
            addTemplate(){
               this.$router.push( {path: '/template/template-setting',query:{ id:''}});
                //this.$router.push( {path: '/trf/trfForm',query:{ id:''}});
            },
            editTemplate(e) {
                this.$router.push( {path: '/template/template-setting',query:{ id:e.id}});
            },
            statusFormat:function(row, column) {
                var status = row.status;
                var statusStr='';
                if(status==1 || status=='1'){
                    statusStr='正常'
                }else{
                    statusStr='禁用'
                }
                return statusStr;
            },
            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.customerGroupData = data;
                });
            },
            queryproductLineData() {
                getProductLine().then(res => {
                    const data = res.data.data;
                    this.productLineData = data;
                });
            },
            isDisabledTemplate(row){
                this.updateTemplate(row.id,row.status);
            },
            updateTemplate(id,status){
                var param=0;
                var message='是否禁用该模板?';
                if(status==1 || status=='1'){
                    message='是否启用该模板?';
                    param=1;
                }
                this.template.id=id;
                this.template.status=param;
                this.$confirm(message, this.$t('tip'), {
                    confirmButtonText:  this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    updateTemplateStatus(this.template).then(res => {
                        this.$message({
                            type: 'success',
                            message:  this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: this.$t('api.error')
                    });*/
                });

            },
            rowContactUpdate(row){
                //请求lab对象数据
                detailContact(row.id).then(res => {
                    const data = res.data.data;
                    this.labContactForm=data;
                    this.labId=row.id;
                    this.labContactFormTitle=this.$t('lab.editContact');
                    this.dialogLabContactFormVisible = true;
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
            submitLabContactForm(){
                this.isDisabled = true ;
                updateContact(this.labContactForm).then(() => {
                    this.dialogLabContactFormVisible=false;
                    this.isDisabled = false;
                    //this.onLoad(this.page);
                    this.searchLabContactList(this.labContactForm.labId);
                    this.$message({
                        type: "success",
                        message:  this.$t('api.success')
                    });
                }, error => {
                    this.isDisabled = false;
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
            labContactChange(row, expandedRows){
                this.$refs.labTable.setCurrentRow();
                this.currentRow = row
                if (this.expands.join(',').indexOf(row.id) === -1) {
                    this.expands = [this.currentRow.id]
                    this.searchLabContactList(row.id);
                } else {
                    this.expands.splice(0, this.expands.length)
                }
               /* if(vlaue2.length!=0){//展开
                    this.searchLabContactList(value1.id);
                }*/
            },
            //请求实验室联系人数据
            searchLabContactList(id){
                var params={};
                this.contactForm.labId=id;
                this.contactloading = true;
                getLabContactList( Object.assign(params, this.contactForm)).then(res => {
                    const data = res.data.data;
                    const index = this.data.findIndex(data => data.id === id);
                    if (data && data.length) {
                        data.forEach(item => {
                            item.labId = id
                        })
                    }
                    this.$set(this.data[index], 'tableData',data)
                    this.tableData = data;
                    this.contactloading = false;
                });
            },
            addLabContact(row){
                this.labContactFormTitle=this.$t('lab.addContact');
                this.labContactForm.labId=row.id;
                this.labContactForm.id='';
                this.labContactForm.contactEmail='';
                this.labContactForm.contactName='';
                this.labContactForm.contactTel='';
                this.dialogLabContactFormVisible = true;
            },
            clear(){
                this.query={};
                this.onLoad(this.page);
            },
            searchTemplateList() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page) {
                this.loading=true;
                var  params = {};
                getList(this.page.currentPage, this.page.pageSize, Object.assign(params,this.query,this.sort)).then(res => {
                    this.loading = false;
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.data = data.records;
                });
            },

            deleteLabContact(obj){
                this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText:  this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    removeContact(obj.id).then(res => {
                        this.$message({
                            type: 'success',
                            message:  this.$t('api.success')
                        });
                        this.searchLabContactList(this.labContactForm.labId);
                    });
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: this.$t('api.error')
                    });*/
                });

            },
            //删除实验室
            deleteLab(obj){
                this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    remove(obj.id).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });

                }).catch(() => {
                   /* this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });*/
                });

            },
            labTypeFormat:function(row, column) {
                var labType = row.labType;
                var dataList = this.labTypeData;
                var labTypeStr='';
                dataList.forEach(function(value, key,dataList) {
                    if(labType==value.dictKey){
                        labTypeStr= value.dictValue;
                    }
                });
                return labTypeStr;
            },
            //分页查询
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.onLoad(this.page);
            },
            selectLabTypeChange(value){
                this.labForm.labType=value;
            },
            addLab() {
                //清空表单内容
                this.labFormTitle=this.$t('operation.add');
                this.labForm.labType='';
                this.labForm.labName='';
                this.labForm.id='';
                this.dialogLabFormVisible = true;
            },
            //新增提交表单
            submitForm(){
                this.fullscreenLoading = true;
                this.isDisabled = true ;
                update(this.labForm).then(() => {
                    this.dialogLabFormVisible=false;
                    this.isDisabled = false;
                    this.fullscreenLoading = false;
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                }, error => {
                    this.isDisabled = false;
                    this.fullscreenLoading = false;
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
            //获取实验室类型下拉数据
            queryLabTypeSelData(code){
                getLabTypeList(code).then(res => {
                    const data = res.data.data;
                    this.labTypeData=data;
                });
            },
            // 编辑Lab
            rowUpdate(row) {
                //请求lab对象数据
                detail(row.id).then(res => {
                    const data = res.data.data;
                    this.labForm=data;
                    this.selectLabTypeChange(this.labForm.labType+'');
                    this.labFormTitle=this.$t('operation.edit');
                    this.dialogLabFormVisible = true;
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });

            }
        },
    };
</script>

<style scoped lang="scss">
    .otherActiveClass {
        color: #FFF;
        background-color: #ebeef5;
    }
    .otherActiveClass:hover {
        background: #ebeef5;
        color: #FFF;
    }
    .box-card {
        /deep/ .el-card__body {
            .el-form--inline .el-form-item {
                /* margin-bottom: 5px; */
            }
        }
    }
</style>
