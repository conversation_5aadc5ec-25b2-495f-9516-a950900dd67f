<template>
  <div class="guide-task">
    <!-- 新手指引任务列表 -->
    <el-dialog :visible.sync="guideVisible" :close-on-click-modal="false" top="20vh" width="890px">
      <div v-if="false" slot="title"></div>
      <div class="guide-content">
        <div class="left">
          <div class="hd">
            <h2>{{ $t('guide.title') }}</h2>
            <p>{{ $t('guide.subTitle') }}</p>
          </div>
          <div class="ft">
            <p>
              {{ $t('guide.status.start') }}<span>{{ taskCompleteNum }}</span>
              {{ $t('guide.status.endHead') }} {{ taskTotal }} {{ $t('guide.status.endFooter') }}
              <!-- {{ role.isBuyer ? $t('guide.status.endIsByuer') : $t('guide.status.end') }} -->
            </p>
            <el-progress :show-text="false" :stroke-width="8" :percentage="(taskCompleteNum/taskTotal)*100" color="#ff6600"></el-progress>
          </div>
        </div>
        <div class="right">
          <ul class="list-unstyled">
            <li @click="viewTask('addBuyer', 0)" v-if="!role.isBuyer">
              <div class="img">
                <img src="/img/icon/guide_buyer.png" alt="">
                <img v-if="taskStatus && taskStatus.length > 0 && taskStatus[0]['val']" class="complate" src="/img/icon/guide_complate.png">
              </div>
              <div class="task">
                <h5>{{ $t('guide.task.task1.title') }}</h5>
                <p>{{ $t('guide.task.task1.tip') }}</p>
              </div>
            </li>
            <template v-for="(item, index) in guideTask">
              <li :key="item.title" @click="viewTask(item.type, item.tabIndex)" v-if="item.show">
                <div class="img">
                  <img :src="item.url" alt="">
                  <img v-if="taskStatus && taskStatus.length > 0 && taskStatus[role.isBuyer ? index : item.tabIndex] && taskStatus[role.isBuyer ? index : item.tabIndex]['val']"
                       class="complate"
                       src="/img/icon/guide_complate.png">
                </div>
                <div class="task">
                  <h5>{{ $t(item.title) }}</h5>
                  <p>{{ $t(item.tip) }}</p>
                </div>
              </li>
            </template>
          </ul>
        </div>
      </div>
      <el-button class="close-btn" type="text" @click="closeDialog"><i class="el-icon-close"></i></el-button>
    </el-dialog>
  </div>
</template>

<script>
import { add } from '@/api/operation/guestbook';
import { mapGetters } from "vuex";
import {validatenull} from "../../../util/validate";

export default {
  name: "guide",
  data() {
    return {
      show: false,
      taskCompleteNum: 0,
      guideVisible: false,
      guideTask: [],
      taskStatus: [],
      tabs: ['relationship', 'contact', 'address', 'notifaction'],
      taskTotal: 0
    };
  },
  created() {
    if(localStorage.getItem('AUTO_OFF') == null) {
      localStorage.setItem('AUTO_OFF', false)
    }
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('scroll', this.scrollToTop)
      this.guideTask = [
        // {title: 'guide.task.task1.title', tip: 'guide.task.task1.tip', url: '/img/icon/guide_buyer.png', type: 'addBuyer', tabIndex: 0, isByuer: false},
        {title: 'guide.task.task2.title', tip: 'guide.task.task2.tip', url: '/img/icon/guide_contact.png', type: 'addContact', tabIndex: 1, show: this.permissionList.contactTab},
        {title: 'guide.task.task3.title', tip: 'guide.task.task3.tip', url: '/img/icon/guide_address.png', type: 'setAddress', tabIndex: 2, show: this.permissionList.addressTab},
        {title: 'guide.task.task4.title', tip: 'guide.task.task4.tip', url: '/img/icon/guide_msg.png', type: 'setNotice', tabIndex: 3, show: this.permissionList.notifactionTab}
      ]
    })
  },
  watch: {
    '$store.state.user.userInfo' (newVal, oldVal) {
      if(newVal) this.guideVisible = true
    },
    '$store.state.user.taskListDialog' (newVal, oldVal) {
      this.guideVisible = newVal
      this.taskStatus = JSON.parse(localStorage.getItem('guideTask'))
      this.computeNum()
    },
    menu(val) {
      console.log('监听菜单数据：', val)
      setTimeout(() => {
        this.init()
      }, 300)
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.scrollToTop)
  },
  computed: {
    ...mapGetters(["userInfo", "language", "menu", "dimensions", "permission",]),
    permissionList() {
      return {
        addressTab: this.vaildData(this.permission['sgs:customer:tab:address'],false),
        contactTab: this.vaildData(this.permission['sgs:customer:tab:contact'],false),
        notifactionTab: this.vaildData(this.permission['sgs:customer:tab:notification'],false),
      };
    },
    step() {
      let num = ''
      let total = 0
      if(this.role.isBuyer) {
        total = 3
      }
      return num
    },
    role() {
      return {
          isSGS: this.haseRole('SGSUserRole', 'SgsAdmin')|| this.haseRole("SGSUserRole", "SgsLabUser"),
          isKam: this.haseRole('SGSUserRole', 'KAM'),
          isBuyer: this.haseRole('UserRole', 'Buyer'),
      };
    },
  },
  methods: {
    init() {
      let menuFlag = this.validateTrfMenu();
      let sgsRole = this.role.isSGS ||this.role.isKam;
      let autoOff = JSON.parse(localStorage.getItem('AUTO_OFF'))  // true 为手动关闭任务弹窗
      let uerList = JSON.parse(localStorage.getItem('loginUserList'))

      //判断有申请单菜单,且 非SGS用户才允许提示
      console.log('判断有申请单菜单,且 非SGS用户才允许提示', this.userInfo.guide, autoOff, menuFlag, !sgsRole)
      if(this.userInfo.guide && !autoOff && menuFlag && !sgsRole && !this.role.isBuyer ) { // 需要展示新手引导
        console.log('展示任务列表')
        this.guideVisible = true
        // if(localStorage.getItem('guideTask') == null) {  // 还没操作过任务
          let tasks = [{type: 'addBuyer', val: false}]
          if(this.permissionList.contactTab) tasks.push({type: 'addContact', val: false})
          if(this.permissionList.addressTab) tasks.push({type: 'setAddress', val: false})
          if(this.permissionList.notifactionTab) tasks.push({type: 'setNotice', val: false})
          if(this.role.isBuyer) tasks.splice(0,1) // 买家账号
          this.taskTotal = tasks.length
          localStorage.setItem('guideTask', JSON.stringify(tasks))
        // } else { // 有任务操作记录，显示在任务列表的状态中~
          this.taskStatus = JSON.parse(localStorage.getItem('guideTask'))
          this.computeNum()
        // }
      } else {
        if(!this.userInfo.guide) {  // 如为 false 表示已完成新手引导
          console.log('已完成新手引导')
          let tasks = [
            {type: 'addBuyer', val: true},
          ]
          if(this.permissionList.contactTab) tasks.push({type: 'addContact', val: true})
          if(this.permissionList.addressTab) tasks.push({type: 'setAddress', val: true})
          if(this.permissionList.notifactionTab) tasks.push({type: 'setNotice', val: true})
          if(this.role.isBuyer) tasks.splice(0,1)
          this.taskTotal = tasks.length
          localStorage.setItem('guideTask', JSON.stringify(tasks))
        }
        this.taskStatus = JSON.parse(localStorage.getItem('guideTask')) || this.taskStatus
        let complete = this.taskStatus.every(item => item.val == true);
        if(complete) { // 处理小红点展示状态
          this.show = false
        } else {
          this.show = true
          this.computeNum()
        }
      }
    },
    //判断是否存在创建TRF菜单
    validateTrfMenu(){
      let result = false;
      if(!validatenull(this.menu)){
        let menuStr = JSON.stringify(this.menu);
        if(!validatenull(menuStr)){
          if(menuStr.indexOf("/ccl/trf/newTrf") != -1){
            result = true;
          }
       }
      }
      return result;
    },
    haseRole(type, role) {
        if (validatenull(type) || validatenull(role)) {
            return false;
        }
        if (validatenull(this.dimensions)) {
            return false;
        } else {
            if (this.dimensions.hasOwnProperty(type)) {
                if (this.dimensions[type].indexOf(role) >= 0) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    },
    closeDialog() {
      this.taskStatus = JSON.parse(localStorage.getItem('guideTask'))
      let complete = this.taskStatus.every(item => item.val == true);
      if(!complete) {
        this.show = true
        localStorage.setItem('AUTO_OFF', true)
      }

      this.$store.commit('SET_TASK_DIALOG', false)
      this.guideVisible = false
    },
    viewTask(type, index) {
      if(type != 'addBuyer') {
        this.$router.push({
          path: '/customer/management',
          query: {
            'tab': this.tabs[index],
          }
        })
      }
      this.$store.commit('SET_TASK_DIALOG', false)  // 关闭任务列表
      this.$store.commit('SET_TASK_TYPE', type) // 设置引导类型
      // this.$store.commit('SET_GUIDE', { name: type, val: true })
    },
    computeNum() {
      let n = 0
      this.taskStatus.forEach(item => {
        if(item.val) n++
      });
      this.taskCompleteNum = n
    }
  },
}
</script>

<style lang="scss" scoped>
.guide-task {
  .close-btn {
    font-size: 25px;
    position: absolute;
    right: 10px;
    top: -46px;
    color: #fff;
  }
  /deep/ .el-dialog__wrapper {
    .el-dialog__header {
      display: none;
      /* padding: 0 !important; */
    }

    .el-dialog__body {
      padding: 0;
    }
    .el-progress-bar__outer {
      border-radius: 0;
      background: rgba(255,255,255,.6);
      .el-progress-bar__inner {
        border-radius: 0;
      }
    }
  }

  .guide-content {
    display: flex;
    .left {
      width: 50%;
      padding: 8px 8px;
      background: url('/img/guide-left.png') no-repeat center / cover;
      text-align: center;

      .hd {
        h2 {
          font-size: 32px;
          // font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 32px;
          margin-bottom: 20px;
          word-break: break-word;
        }

        >p {
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 20px;
        }
      }
      .ft {
        position: absolute;
        bottom: 48px;
        width: calc(50% - 20px);
        > p {
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 20px;
          span {
            color: #f60;
          }
        }
      }

      .bd {
        ul {
          li {
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #9A9A9A;
            line-height: 22px;

            span {
              padding-right: 11px;
            }

            &.active {
              font-weight: 500;
              color: #1B1B1B;

              span {
                color: #f60;
              }
            }
          }
        }
      }
    }

    .right {
      width: 50%;
      padding: 56px 40px;
      box-sizing: border-box;
      background: #F5F5F5;

      ul {
        li {
          display: flex;
          padding: 15px 24px;
          height: 80px;
          background: #FFFFFF;
          margin-bottom: 16px;
          align-items: center;
          border: 1px solid #fff;
          cursor: pointer;
          transition: .2s all;
          &:last-of-type {
            margin-bottom: 0;
          }
          .img {
            width: 40px;
            height: 40px;
            border: 1px solid #e6e6e6;
            margin-right: 24px;
            text-align: center;
            line-height: 35px;
            position: relative;
            .complate {
              position: absolute;
              right: 3px;
              bottom: 3px;
            }
          }
          .task {
            h5 {
              font-size: 16px;
              // font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #000000;
              line-height: 24px;
              margin: 0 0 3px;
            }
            p {
              font-size: 14px;
              font-weight: 400;
              color: #9A9A9A;
              line-height: 20px;
              margin: 0;
              word-break: break-word;
            }
          }
          &:hover {
            border-color: #f60;
          }
        }
      }
    }
  }
  .incomplete {
    position: fixed;
    right: 30%;
    bottom: 50vh;
    width: 48px;
    height: 48px;
    /* background: #f60; */
    transition: all .3s;
    z-index: -1;
    opacity: 0;
    /* animation: slider 2s linear infinite; */
    transform: translateX(7px);
    font-size: 22px;
    color: #fff;
    text-align: center;
    line-height: 48px;
    cursor: pointer;
    &.show {
      bottom: 114px;
      right: 33px;
      opacity: 1;
      z-index: 10;
    }

    .feature-box {
      width: 48px;
      height: 48px;
		    position: relative;
		    margin: 0px auto;
		    display: flex;
		    align-items: center;
		    justify-content: center;
        }
		.pulse {
            width: 20px;
		    height: 20px;
		    background: #f60;
		    border-radius: 50%;
        }
        .pulse1, .pulse2 {
            position: absolute;
		    width: 48px;
		    height: 48px;
		    top: 0;
		    background: #f60;
		    border-radius: 50%;
		    opacity: 0;
		    z-index: -1;
		    box-shadow: 1px 1px 15px #f60;
        }
        .pulse1 {
            animation: warn1 1.5s linear;
            animation-iteration-count: infinite;
        }
        .pulse2 {
            animation: warn2 1.5s linear;
            animation-iteration-count: infinite;
        }

  }

  /* @keyframes slider {
    0%, 100% {
      transform: translateX(7);
    }
    50% {
      transform: translateX(-7px);
    }
  } */

  @keyframes warn1 {
    0% {
      transform: scale(1);
      opacity: 0.01;
    }

    25% {
      transform: scale(1.2);
      opacity: 0.1;
    }

    50% {
      transform: scale(1.4);
      opacity: 0.07;
    }

    75% {
      transform: scale(1.6);
      opacity: 0.03;
    }

    100% {
      transform: scale(1.8);
      opacity: 0.01;
    }
  }

  @keyframes warn2 {
    0% {
      transform: scale(0.8);
      opacity: 0.01;
    }

    25% {
      transform: scale(0.8);
      opacity: 0.13;
    }

    50% {
      transform: scale(1);
      opacity: 0.1;
    }

    75% {
      transform: scale(1.2);
      opacity: 0.07;
    }

    100% {
      transform: scale(1.4);
      opacity: 0.01;
    }
  }
}
</style>
