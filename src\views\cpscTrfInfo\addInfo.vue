<template>
  <div id="app">
    <div class="header" v-if="formModel.id">
      <div class="line2">
        <h2 class="line1">CPSC TRF No </h2>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h3 class="line1">{{ this.trfInfo.referenceNo }}</h3>
      </div>
      <br/>
      <div>
        <column-dots  v-if="this.trfInfo.trfInfoStastus!==0"  :trfInfoStatus="this.trfInfo.trfInfoStastus" />
      </div>
    </div>
    <el-collapse v-model="activeNames">
      <el-collapse-item   name="1">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab1 }}</h3>


        </template>
        <div class="form-content">


    <div class="form-inner">
      <avue-form :option="formOption1Com" v-model="formModel" ref="form1">
        <template slot="buyerCustomerId" slot-scope="{dic,row}">

          <el-select :disabled="disabledCheck" v-model="formModel.buyerCustomerId" clearable filterable :filter-method="filterBuyerCust" style="width:100%" @change="handleBuyerChange($event,dic)" :placeholder="`${$t('work.cpscTrfInfo.placeholder.select')+$t('work.cpscTrfInfo.column.customerName')}`">
            <el-option v-for="(item) in dic" :key="index" :label="item.bossName" :value="item.id"></el-option>
          </el-select>
        </template>
      </avue-form>
    </div>
  </div>
        </el-collapse-item>
        <el-collapse-item   name="2">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab3 }}</h3>


        </template>
        <div class="form-content">

     <div class="form-inner">
       <avue-form :option="formOption3Com" v-model="formModel" ref="form3"></avue-form>
     </div>
   </div>
        </el-collapse-item>
        <el-collapse-item   name="3">
          <template slot="title">
            <h3 style="font-weight: bold">Product Info</h3>
          </template>
          <avue-form :option="formOption7Com" v-model="productInfo" ref="form0" >
    <template slot="productIdType" slot-scope="{row,dic,size,label}">
      <el-select :size="size" v-model="row.productIdType" :placeholder="label" clearable filterable :disabled="disabledCheck || (copyType==='edit'&&row.primaryId==='yes')">
        <el-option v-for="(item,index) in dic" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </template>
    <template slot="productId" slot-scope="{row,size,label}" >
      <el-input :size="size" :placeholder="label" v-model="row.productId" clearable :disabled="disabledCheck|| (copyType==='edit'&&row.primaryId==='yes')"></el-input>
    </template>
    <template slot="primaryId" slot-scope="{row,size,index}">
      <el-radio-group :size="size" v-model="row.primaryId" class="ml-4"
                                            @change="handlePrimaryIdChange(index)" :disabled="disabledCheck|| (copyType==='edit'&&row.primaryId==='yes')">
                              <el-radio label="yes"></el-radio>
      </el-radio-group>
    </template>
    <template slot="manufactureCustomerId">
            <el-select :disabled="disabledCheck" v-model="productInfo.manufactureCustomerId" filterable
                       :no-data-text="$t('work.cpscTrfInfo.selectNoData')"
              :placeholder="$t('work.cpscTrfInfo.column.manufacture')" ref="template"
              @visible-change="(v) => visibleChange(v, 'template', 'Manufacture')" :filterMethod="filterMethod" @change="handleTradeChange">
              <el-option v-for="(item, index) in manufactureCustomerIds" :key="item.id" :label="item.tradeName"
                :value="item.id">
                {{ item.tradeName }}
                <div class="flag"></div>
              </el-option>
            </el-select>
      </template>
  </avue-form>
        </el-collapse-item>
        <!-- <el-collapse-item   name="4">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab6 }}</h3>


        </template>
        <div class="form-content">

  <div class="form-inner">
    <avue-form :option="formOption6Com" ref="form6" v-model="formModel">
      <template slot="certVersion">
        <el-input maxlength="19" :disabled="disabledCheck"  :placeholder="this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.certificateVersion')" v-model="formModel.certVersion" clearable size="small" >
          <template #append>
            <el-button type="primary" @click="handleGenerate" >Generate</el-button>
          </template>
        </el-input>
      </template>
    </avue-form>
  </div>
</div>
        </el-collapse-item> -->
        <el-collapse-item   name="5">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab4 }}</h3>


        </template>
        <div class="form-content">
      <!--      <div class="form-lab">{{ formLab4 }}</div>-->


      <div class="form-inner">
        <avue-form :option="formOption4Com" v-model="formModel" ref="form4">
          <template slot="pocCustomerId">
            <el-select :disabled="disabledCheck" v-model="formModel.pocCustomerId" filterable :placeholder="$t('work.cpscTrfInfo.column.otherPoc')"
              ref="template2" @visible-change="(v) => visibleChange(v, 'template2', 'POC')" >
              <el-option v-for="(item, index) in shipTemplates" :key="item.id" :label="item.tradeName" :value="item.id">
                {{ item.tradeName }}
                <div class="flag"></div>
              </el-option>
            </el-select>
          </template>
        </avue-form>
      </div>
    </div>
        </el-collapse-item>
        <el-collapse-item   name="6">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab5 }}</h3>


        </template>
        <div class="form-content">

    <div class="form-inner">
      <TrfAttachment ref="trfFile"
                     :trfAttachments="formModel.cpscTrfAttachList"
                     :trf="formModel"
                    :file-max-sizes="20"
                 >
        </TrfAttachment>
    </div>
  </div>
        </el-collapse-item>
    </el-collapse>
    <el-dialog v-if="isDialog" :visible.sync="isDialog" append-to-body destroy-on-close :title="$t('crud.addTitle')" width="50%">
      <commen-dialog :cpscCustomerId="selectBuyerCustomerOldId" :type="type"
        @closeCommenDialog="closeCommenDialog(arguments)"></commen-dialog>
    </el-dialog>
    <el-row type="flex" justify="center">
      <el-button type="warning" plain v-if="trfInfo.trfInfoStastus < 3 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2" @click="handleSave(1)">{{ btnSaveLab }}
      </el-button>
      <el-button type="success" plain v-if="trfInfo.trfInfoStastus < 3 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2" @click="handleSave(2)">{{ btnSubmitLab }}
      </el-button>
    </el-row>
  </div>
</template>

<script>
import ColumnDots from "@/views/cpscTrfInfo/columnDots.vue";
  import { getList } from '@/api/form/form.js'

import i18n from '../../lang/index';
import { getDetail, add, update, listForSelect, tradeSelect,getBasicLabList } from "@/api/cpscTrfInfo/cpscTrfInfo";
import {objectIsNull} from "@/util/validate";
import CommenDialog from './commenDialog.vue'
import { template } from "lodash";
export default {
  components: { ColumnDots, CommenDialog,  TrfAttachment: resolve => require(['@/components/trf/TrfAttachment'], resolve) },
  props: {
    trfInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 用来区别用户是想新增复制还是更新复制
    // 新增 ： add   更新：edit
    copyType: {
      type: String,
      default: null
    },
    formModel: Object,
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      activeNames:['1','2','3','4','5','6'],
      productInfo:{},
      option:{},
      selectBuyerCustomerOldId:"",

      type: '',
      isDialog: false,
      shipTemplates: [

      ],
      manufactureCustomerIds: [],
      btnSaveLab: this.$t('work.cpscTrfInfo.column.save'),
      btnSubmitLab: this.$t('work.cpscTrfInfo.column.submit'),
      formLab1: this.$t('work.cpscTrfInfo.column.buyInfo'),
      timer:null,
      formOption1:{
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        arrow:false,
        disabled: this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            prop: 'buyerCustomerName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.customerName'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerCustomerId',

            type: 'select',
            dataType: "string",
           // dicUrl: "/api/sgs-e-filling/sgs-work/cpscCustomer/listForSelect",
            dicData:[],
            // dicFormatter:(res)=>{
            //   return res.data.map(item=>{return{...item,bossNameWidthBossNo:`${item.bossName}                 ${item.bossNo}`}})
            // },

            props: {
              label: 'bossName', // 对象的属性，用作显示标签
              value: 'id' // 对象的属性，用作值
            },
            //cascader: ['buyerContactId', 'manufactureCustomerId', 'pocCustomerId'],
            //change: this.handleBuyerChange,
            filterable:true,
            rules: [{ required: true ,message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.customerName')}],
            placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.customerName')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.customerNameLocal'),
            labelWidth: 200,
            type: 'input',

            labelPosition: 'right',
            prop: 'buyerCustomerNameLocal',
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerNameLocal')
          },
          {
            prop: 'buyerCustomerCountryId',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.customerCountry'),
            labelWidth: 200,
            type: 'input',

            labelPosition: 'right',
            prop: 'buyerCustomerCountryName',
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input') +this.$t('work.cpscTrfInfo.column.customerCountry')
          },
          {
            prop: 'buyerCustomerCityId',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.customerCity'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerCustomerCityName',

            placeholder: this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.customerCity')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.customerAddress'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerCustomerAddress',
            row: true,
            span: 24,

            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerAddress')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.customerAddressLocal'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerCustomerAddressLocal',
            row: true,
            span: 24,

            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerAddressLocal')
          },
          {
            prop: 'buyerContactId',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.contactName'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerContactName',

            //type: 'select',
            //dataType: "string",
            //dicUrl: "/sgs-work/cpscCustomerTradeContact/listForSelect?relationId={{buyerCustomerId}}&relationType=customer",
            //props: {
            //  label: 'contactName', // 对象的属性，用作显示标签
            //  value: 'id' // 对象的属性，用作值
            //},
            //change:this.handleContactChange,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.contactName')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.contactTelephone'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerContactTelephone',

            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.contactTelephone')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.contactEmail'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'buyerContactEmaill',
            component: 'el-input',

            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.contactEmail')
          }
        ]
      },

      formLab3: this.$t('work.cpscTrfInfo.column.basicInfo'),
      formOption3:{
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            prop: 'basicServiceTypeName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.serviceType'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'basicServiceType',
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=serviceType",
            dataType: "string",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.serviceType') }],
            change: this.handleServiceTypeChange,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.serviceType')
          },
          {
            prop: 'basicLabName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.labName'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'basicLabId',
            type: 'select',
            dataType: "string",
            dicData: [],
            props: {
              label: 'labName', // 对象的属性，用作显示标签
              value: 'labCode' // 对象的属性，用作值
            },
            popperClass:'labName',
            filterable:true,
            rules: [{ required: true,trigger:'change',message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.labName') }],
            change: this.handleBasicLabChange,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.labName')
          },
          {
            prop: 'basicLabContactId',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.labContact'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'basicLabContactName',
            rules: [{ required: true,trigger:'blur',message:this.$t('work.cpscTrfInfo.column.csEmailAddressMsg') },{
              message:this.$t('trfPrint.general.validLabContact'),
            pattern:  /^[\w\.\-]+@([\w\-]+\.)+[\w\-]+$/,    //正则校验不用字符串
             trigger: "blur"
          }],

            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.labContact')
          },

          {
            label: this.$t('work.cpscTrfInfo.column.labAddress'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'basicLabAddress',


            placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.labAddress')
          },
        ]
      },
      formLab4: this.$t('work.cpscTrfInfo.column.poc'),
      formOption4:{
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            prop: 'pocTypeName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.pocType'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'pocTypeId',
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=pocType",
            dataType: "string",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            // change: this.handlePOCTypeChange,
            rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select')+ this.$t('work.cpscTrfInfo.column.pocType') }],
            placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+ this.$t('work.cpscTrfInfo.column.pocType')
          },
          {
            prop: 'pocCustomerName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.otherPoc'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'pocCustomerId',
            type: 'select',

            dataType: "string",
            dicData: [],
            // dicUrl: "/sgs-work/cpscCustomerTrade/listForSelect?tradeType=POC&cpscCustomerId={{key}}",
            props: {
              label: 'tradeName', // 对象的属性，用作显示标签
              value: 'id' // 对象的属性，用作值
            },
            change: this.handlePOCOtherChange,
            display: false,
            placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+ this.$t('work.cpscTrfInfo.column.otherPoc')
          },
        ]
      },
      //附件
      formLab5: this.$t('work.cpscTrfInfo.column.attachment'),
      formLab6: this.$t('work.cpscTrfInfo.column.certificateInfo'),
      formOption6:{

    // 表单配置项
    menuType: 'default',
    submitBtn: false,
    emptyBtn: false,

    column: [
      {
        label: this.$t('work.cpscTrfInfo.column.certificateVersion'),
        labelWidth: 200,
        labelPosition: 'right',
        prop: 'certVersion',
        suffixIcon:'Plus',
        rules: [{ required: true,message:this.$t('work.cpscTrfInfo.column.placeholderCertificateVersion') }],

        display: true,
        disabled: this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,

      },
    ]
  },
  buyerCustomerIds:[]

    };
  },
  computed:{
    disabledCheck() {
      return this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2' ||this.trfInfo.status==2
    },
    formOption1Com(){
      return this.formOption1
    },

    formOption3Com(){
      return this.formOption3
    },
    formOption4Com(){
      return this.formOption4
    },
    formOption6Com(){
      return this.formOption6
    },
    formOption7Com(){
      return this.option
    }
  },
  mounted() {
    this.listForSelect(this.formModel.cpscCustomer? this.formModel.cpscCustomer.bossName:'').finally(()=>{
      this.loadFormOption()
    this.basicLablistForSelect()
    this.setFormModel()
    })

  },

  methods: {
    filterBuyerCust(val){
      if(val){

                  clearTimeout(this.timer)
                  this.timer=setTimeout(()=>{
                    this.listForSelect(val)
                  },800)

            }
    },
    filterMethod(val){
      clearTimeout(this.timer)
                  this.timer=setTimeout(()=>{
                    const item = this.buyerCustomerIds.find(item=>item.id==this.formModel.buyerCustomerId)
                   // this.listForSelect(val)
                   this.setPOC('Manufacture', item.customerId,val)
                  },800)
    },
   async listForSelect(name=''){

     const res = await listForSelect(name)
       const column = this.findObject(this.formOption1.column,'buyerCustomerId')
       column.dicData =res.data.data
       this.buyerCustomerIds =res.data.data

    },
	  handlePrimaryIdChange(index) {
	              this.productInfo.formList.forEach((item, i) => {
	                  if (i !== index) {
	                      item.primaryId = '';
	                  }
	              });
	          },
    loadFormOption() {
            // 获取表单定义
            getList(1,10,{code:'Efilling'}).then(res => {
                const data = res.data.data.records[0];
                data.content=(data.content+'').replaceAll('YYYY','yyyy')

                this.option = JSON.parse(data.content || '{}');
                       this.option.size='default'
                this.option.disabled=this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2
                if(Object.keys(this.option).length){
 // 重新解析字段配置，替换正则表达式及方法
                this.option.column.forEach(function (column, index) {
                    // 添加正则表达式解析逻辑
                    if (column.rules && column.pattern) {
                        column.rules.forEach(function (rule) {
                            if (rule.pattern) {
                                rule.pattern = new RegExp(column.pattern)
                            }
                        })
                    }
                    if (column.method) {
                        Object.keys(column.method).forEach(function (key) {
                            const funcStr = column.method[key];
                            column[key] = new Function(funcStr);
                        })
                    }
                })
                // 重新解析字段配置，替换正则表达式及方法
                if (this.option.group) {
                    this.option.group.forEach(function (column, index) {
                        // 添加正则表达式解析逻辑
                        if (column.rules && column.pattern) {
                            column.rules.forEach(function (rule) {
                                if (rule.pattern) {
                                    rule.pattern = new RegExp(column.pattern)
                                }
                            })
                        }
                        if (column.method) {
                            Object.keys(column.method).forEach(function (key) {
                                const funcStr = column.method[key];
                                column[key] = new Function(funcStr);
                            })
                        }
                    })
                }
                const formListColumn = this.findObject(this.option.group[0].column,'formList')
                this.option.group[0].column[0].children.rowDel=(row,done)=>{

                  if((this.disabledCheck&& row.primaryId==='yes')||(this.copyType==='edit'&&row.primaryId==='yes')){
                    this.$message.error('This item cannot be deleted because the primary ID is yes');
                  }else{
                    done()
                  }
                }
                formListColumn.children.column.forEach(item=>{
                  item.slot=true
                })
                }

            });
        },
    closeCommenDialog(e) {
     const item = this.buyerCustomerIds.find(item=>item.id==this.formModel.buyerCustomerId)
      this.setPOC(e[0], item.customerId,'',e[1])

      this.isDialog = false

    },

    showShipTemplate(type) {
      if (this.formModel.buyerCustomerId) {
        this.isDialog = true
        this.type = type
      } else {
        this.$message.error(this.$t('work.cpscTrfInfo.buyerMessage'))
      }

    },
    visibleChange(visible, refName, type) {
      if (visible) {
        const ref = this.$refs[refName];
        debugger
        let popper = ref.popperElm;
        if (popper.$el) popper = popper.$el;
        if (
          !Array.from(popper.children).some(
            (v) => v.className === "el-template-menu__list"
          )
        ) {
          const el = document.createElement("ul");
          el.className = "el-template-menu__list";
          el.style =
            "border-bottom:1px solid #f5f5f5;; padding:0; color:#ff6600;font-size: 16px";
          el.innerHTML = `<li class="el-cascader-node text-center" style="height:37px;line-height: 50px;">
            <span class="el-cascader-node__label"><i class="font-blue el-icon-plus"></i>${this.$t('operation.add')}</span>
            </li>`;
          popper.insertBefore(el, popper.firstChild);
          el.onclick = () => {
            this.showShipTemplate(type);
          };
        }
      }
    },
    basicLablistForSelect() {
      /*listForSelect().then(res=>{
        const column = this.findObject(this.formOption1.column, 'buyerCustomerId');
        //column.dicData=[{id:'create',bossName:'New Customer'},...res.data.data]
        column.dicData=res.data.data

      })*/
      getBasicLabList('','').then(res=>{
        let labList = res.data.data;
        labList.forEach(lab => {
          if (lab.labContactList[0]){
            lab.contact = lab.labContactList[0].contactName + "-" + lab.labContactList[0].contactTel + "-" + lab.labContactList[0].contactEmail;
          }

        });
        const column2 = this.findObject(this.formOption3.column, 'basicLabId');
        column2.dicData = labList;
      })
    },
    setFormModel() {

      console.log(this.formModel)
      if (this.formModel.cpscCustomer){
        this.selectBuyerCustomerOldId = this.formModel.cpscCustomer.customerId;
        //判断versionid
        // if (this.formModel.cpscCustomer.versionidType){
        //   if (this.formModel.cpscCustomer.versionidType == '1'){

        //     this.$refs.form6.option.column[0].rules[0].required = false;
        //   } else if (this.formModel.cpscCustomer.versionidType == '2'){

        //     this.$refs.form6.option.column[0].rules[0].required = true;
        //   }
        // }
        const item = this.buyerCustomerIds.find(item=>item.id==this.formModel.buyerCustomerId)
        //加载根据买家的下拉
        if(item){
          this.setPOC('POC', item.customerId)
          this.setPOC('Manufacture', item.customerId,this.formModel.manufactureCustomerName)
        }

      }
      this.productInfo = Object.keys(this.formModel).length?JSON.parse(this.formModel.productJson || '{}'):{}
      const column = this.findObject(this.formOption4.column, 'pocCustomerId')

      column.display = this.formModel.pocTypeId == 'Other'
      if (!column.display) {
        this.formModel.pocCustomerId = ''
        this.formModel.pocCustomerName = ''
      }


      // const {
      //   productModelName,
      //   tradeBrandName,
      //   productModelDescription,
      //   productModelColor,
      //   productModelStyle,
      //   lotNumber,
      //   productionStartDate,
      //   lotNumberAssignedBy,
      //   manufactureDate,
      //   ProductionEndDate,
      // } = productInfoObj
      // this.productInfo.formBottom = {
      //   productModelName,
      //   tradeBrandName,
      //   productModelDescription,
      //   productModelColor,
      //   productModelStyle,
      //   lotNumber,
      //   productionStartDate,
      //   lotNumberAssignedBy,
      //   manufactureDate,
      //   ProductionEndDate,
      // }
      // this.productInfo.formList = productInfoObj.formList
    },
    handleBuyerChange(value,dic) {
      if(!value){
       // this.$refs.form6.option.column[0].rules[0].required = true;
        this.$nextTick(()=>{
            this.selectBuyerCustomerOldId =null
           // this.newFormModel.buyerCustomerId = null
            this.newFormModel.buyerCustomerName = null
            this.newFormModel.buyerCustomerNameLocal = null
            this.newFormModel.buyerCustomerCountryId = null
            this.newFormModel.buyerCustomerCountryName = null
            this.newFormModel.buyerCustomerCityId = null
            this.newFormModel.buyerCustomerCityName = null
            this.newFormModel.buyerCustomerAddress = null
            this.newFormModel.buyerCustomerAddressLocal = null
            this.newFormModel.buyerContactId = null
            this.newFormModel.buyerContactName = null
            this.newFormModel.buyerContactTelephone = null
            this.newFormModel.buyerContactEmaill = null
          })
        this.listForSelect()
        return
      }
        // 根据选择的对象ID获取对象详情
        const selectedObject =  dic.find(item=>item.id ===value)
        if (selectedObject) {
          this.selectBuyerCustomerOldId = selectedObject.customerId;
          this.clearManufactureInfo()
          this.clearLabList()
          this.clearPocInfo()
          this.setPOC('POC', selectedObject.customerId)
          this.setPOC('Manufacture', selectedObject.customerId)
          this.formModel.buyerCustomerId = selectedObject.id;
          this.$emit('changeLabOption',{customerId:selectedObject.customerId,id:selectedObject.id})
          this.formModel.buyerCustomerName = selectedObject.bossName;
          this.formModel.buyerCustomerNameLocal = selectedObject.bossNameLocal;
          this.formModel.buyerCustomerCountryId = selectedObject.bossCustomerCountryId;
          this.formModel.buyerCustomerCountryName = selectedObject.bossCustomerCountryName;
          this.formModel.buyerCustomerCityId = selectedObject.bossCustomerCityId;
          this.formModel.buyerCustomerCityName = selectedObject.bossCustomerCityName;
          this.formModel.buyerCustomerAddress = selectedObject.bossCustomerAddress;
          this.formModel.buyerCustomerAddressLocal = selectedObject.bossCustomerAddressLocal;
          this.formModel.buyerContactId = selectedObject.cpscCustomerTradeContactVO.id;
          this.formModel.buyerContactName = selectedObject.cpscCustomerTradeContactVO.contactName;
          this.formModel.buyerContactTelephone = selectedObject.cpscCustomerTradeContactVO.telphone;
          this.formModel.buyerContactEmaill = selectedObject.cpscCustomerTradeContactVO.email;
          this.productInfo.formList=[{productIdType:selectedObject.productPrimaryId,primaryId:'yes',productId:''}]
          if (selectedObject.pocType){
            this.formModel.pocTypeId = selectedObject.pocType
          }

          //判断versionid
          // if (selectedObject.versionidType){
          //   if (selectedObject.versionidType == '1'){

          //     this.$refs.form6.option.column[0].rules[0].required = false;
          //   } else if (selectedObject.versionidType == '2'){

          //     this.$refs.form6.option.column[0].rules[0].required = true;
          //   }
          // }
        }

    },
    setPOC(type, value,name='',tradeName) {
      tradeSelect(type, value,name||tradeName).then(res => {
        if (type == 'POC') {
          this.shipTemplates = res.data.data
          //const column = this.findObject(this.formOption4.column, 'pocCustomerId');
          //column.dicData = res.data.data
          if (!this.shipTemplates.some(item => item.id === this.formModel.pocCustomerId)){
            this.clearPocInfo();
          }
        } else if (type == 'Manufacture') {
          this.manufactureCustomerIds = res.data.data
          if(tradeName){
            const item = this.manufactureCustomerIds.find(item=>item.tradeName===tradeName)
            this.productInfo.manufactureCustomerId= item.id
            this.handleTradeChange(item.id)
          }
          if (!this.manufactureCustomerIds.some(item => item.id === this.formModel.manufactureCustomerId)){
            this.clearManufactureInfo();
          }
        }

      })
    },

    handleContactChange(value) {
      const selectedObject = value.item;
      if (selectedObject) {
        this.formModel.buyerContactId = selectedObject.id;
        this.formModel.buyerContactName = selectedObject.contactName;
        this.formModel.buyerContactTelephone = selectedObject.telphone;
        this.formModel.buyerContactEmaill = selectedObject.email;
      }
    },
    handleTradeChange(e) {
      const selectedObject= this.manufactureCustomerIds.find(item=>item.id===e)
      // 根据选择的对象ID获取对象详情

      if (selectedObject) {
        this.$nextTick(()=>{
          this.productInfo.manufactureCustomerId = selectedObject.id;
        this.formModel.manufactureCustomerName = selectedObject.tradeName;
        this.formModel.manufactureCustomerNameLocal = selectedObject.tradeName;
        this.formModel.manufactureCustomerCountryId = selectedObject.tradeCountryId;
        this.formModel.manufactureCustomerCountryName = selectedObject.tradeCountryName;
        this.formModel.manufactureCustomerCityId = selectedObject.tradeCity;
        this.formModel.manufactureCustomerCityName = selectedObject.tradeCity;
        this.formModel.manufactureCustomerAddress = selectedObject.tradeAddress;
        this.formModel.manufactureCustomerAddressLocal = selectedObject.tradeAddress2;
        this.formModel.manufactureContactId = selectedObject.cpscCustomerTradeContactVO.id;
        this.formModel.manufactureContactName = selectedObject.cpscCustomerTradeContactVO.contactName;
        this.formModel.manufactureContactTelephone = selectedObject.cpscCustomerTradeContactVO.telphone;
        this.formModel.manufactureContactEmaill = selectedObject.cpscCustomerTradeContactVO.email;
        })

      }
    },
    handleTradeContactChange(value) {
      const selectedObject = value.item;
      if (selectedObject) {
        this.formModel.manufactureContactId = selectedObject.id;
        this.formModel.manufactureContactName = selectedObject.contactName;
        this.formModel.manufactureContactTelephone = selectedObject.telphone;
        this.formModel.manufactureContactEmaill = selectedObject.email;
      }
    },
    checkDuplicateProductInfo(data) {
  // 创建两个对象来分别存储 productIdType 和 productId 的计数
  const productIdTypeCount = {};
  const productIdCount = {};

  // 遍历数组中的每个实体
  for (const item of data) {
    const { productIdType, productId } = item;

    // 检查 productIdType 是否重复
    productIdTypeCount[productIdType] = (productIdTypeCount[productIdType] || 0) + 1;
    if (productIdTypeCount[productIdType] > 1) {
      return { hasDuplicateIdType: true, hasDuplicateId: false };
    }

    // 检查 productId 是否重复
    productIdCount[productId] = (productIdCount[productId] || 0) + 1;
    if (productIdCount[productId] > 1) {
      return { hasDuplicateIdType: false, hasDuplicateId: true };
    }
  }

  // 没有发现重复
  return { hasDuplicateIdType: false, hasDuplicateId: false };
},
    checkProductInfo(productInfo){

             if(!productInfo.formList.length || !productInfo.formList.find(item=>item.primaryId==='yes')){
                 return {
                     flag:false,
                     msg:this.$t('work.cpscCustomerTrade.tip.primary')
                 }
             }
             if(!productInfo.formList.find(item=>item.primaryId==='yes').productIdType ||!productInfo.formList.find(item=>item.primaryId==='yes').productId){
              return {
                     flag:false,
                     msg:this.$t('work.cpscCustomerTrade.tip.productInfo')
                 }
             }
           const {hasDuplicateIdType,hasDuplicateId} =  this.checkDuplicateProductInfo(productInfo.formList)
            if(hasDuplicateIdType) return {flag:false,msg:'ProductIdType Repeat'}
            if(hasDuplicateId) return {flag:false,msg:'ProductId Repeat'}

         return {flag:true}
     },
    handleSave(trfStatus) {
      const fun =()=>{
        let form = JSON.parse(JSON.stringify({...this.formModel,...this.productInfo}))
          form.dataSource = 1;
          form.trfInfoStastus = trfStatus;
          if (this.trfInfo.id) {
            form.id = this.trfInfo.id;
            form.trfInfoVersion = this.trfInfo.trfInfoVersion
            form.trfInfoId = this.trfInfo.trfInfoId
          }
          form.referenceNo = this.trfInfo.referenceNo
          //获取 primay key
          if (this.productInfo.formList&&this.productInfo.formList.length) {
            const primary = this.productInfo.formList.find(item => item.primaryId === 'yes');
            form.productId = primary.productId
            form.productIdType = primary.productIdType
          }
          if (this.productInfo.productModelName) {
            form.productName = this.productInfo.productModelName
          }


          form.productJson = JSON.stringify(this.productInfo)
        //复制提交 点击save 和 submit时 新增数据，所以需要将 form中的id属性设置为null
        if (this.copyType){
          form.id = null
          if(this.copyType == 'edit'){
            // 如果用户选择更新，需要记录一下来源的认证编码
            form.fromCertVersion = form.certVersion;
          }
        }
          //form.id = null;
          add(form).then(() => {
            this.$message({
              type: "success",
              message: "Success!"
            });
           // this.$emit("closeEditPage")
          //  if(trfStatus==2) this.back()
          this.$router.push('/cpscTrfInfo/cpscTrfInfo')
          }, error => {
            window.console.log(error);
          });
      }
      if(trfStatus==1){
       fun()
      }else{
        debugger
        const {flag,msg} = this.checkProductInfo(this.productInfo)
        if(!flag){
                this.$message.error(msg)
                return
          }
        let formRefs = Object.keys(this.$refs)
        .filter((refName) => refName.startsWith("form"))
        .map((refName) => this.$refs[refName]);

      const promise = new Promise((r, rj) => {
        for (let ref of formRefs) {
          ref.validate(valid => {

            if (!valid) {
              r('error')
              return
            }
          })

        }
        setTimeout(() => {
          r('success')
        }, 1000)


      })
      promise.then(res => {
        if(res==='success'){
          fun()
        }
      })

      }

    },
    back(){
      if (window.history.length > 1) {
    window.history.back();
    window.addEventListener('pageshow', function(event) {
        if (event.persisted || (window.performance && window.performance.navigation.type === 2)) {
            window.location.reload();
        }
    });
} else {
    window.close();
}
    },

    handleServiceTypeChange(value) {

      const selectedObject = value.item;
      if (selectedObject) {
        this.formModel.basicServiceType = selectedObject.dictValue
        this.formModel.basicServiceTypeName = selectedObject.dictKey
      }
    },
    handleBasicLabChange(value) {

      const selectedObject = value.item;

      if (selectedObject) {
        this.formModel.basicLabId=value.value
        this.formModel.basicLabAddress = selectedObject.labAddress
       // this.formModel.basicLabContactName = selectedObject.contact
      }else{
        if(!value.value){
          this.formModel.basicLabId=null
        this.formModel.basicLabAddress = null
      //  this.formModel.basicLabContactName = null
        }

      }
    },
    handlePOCTypeChange(value) {
      const selectedObject = value.item;
      if (selectedObject && selectedObject.dictValue == 'Other') {
        this.formOption4.column[3].display = true;
        this.formModel.pocTypeId = selectedObject.dictValue
        this.formModel.pocTypeName = selectedObject.dictKey
      } else {
        this.formOption4.column[3].display = false;
        this.formModel.pocTypeId = null
        this.formModel.pocTypeName = null
      }

    },
    handlePOCOtherChange(value) {
      const selectedObject = value.item;
      if (selectedObject) {
        this.formModel.pocCustomerId = selectedObject.id
        this.formModel.pocCustomerName = selectedObject.tradeName
      }
    },

    handleGenerate(){
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      const charactersLength = characters.length;
      for (let i = 0; i < 8; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      this.formModel.certVersion = result;
    },
    clearManufactureInfo(){
      this.formModel.manufactureCustomerId = null
      this.formModel.manufactureCustomerName = null
      this.formModel.manufactureCustomerNameLocal = null
      this.formModel.manufactureCustomerCountryId = null
      this.formModel.manufactureCustomerCountryName = null
      this.formModel.manufactureCustomerCityId = null
      this.formModel.manufactureCustomerCityName = null
      this.formModel.manufactureCustomerAddress = null
      this.formModel.manufactureCustomerAddressLocal = null
      this.formModel.manufactureContactId = null
      this.formModel.manufactureContactName = null
      this.formModel.manufactureContactTelephone = null
      this.formModel.manufactureContactEmaill = null
    },
    clearPocInfo(){
      this.formModel.pocCustomerId = null
      this.formModel.pocCustomerName = null
    },
    clearLabList(){
      this.formModel.cpscTrfLabInfoList= []
    },
    clearBuyerInfo(){
      this.formModel.buyerCustomerId = null
      this.formModel.buyerCustomerName = null
      this.formModel.buyerCustomerNameLocal = null
      this.formModel.buyerCustomerCountryId = null
      this.formModel.buyerCustomerCountryName = null
      this.formModel.buyerCustomerCityId = null
      this.formModel.buyerCustomerCityName = null
      this.formModel.buyerCustomerAddress = null
      this.formModel.buyerCustomerAddressLocal = null
      this.formModel.buyerContactId = null
      this.formModel.buyerContactName = null
      this.formModel.buyerContactTelephone = null
      this.formModel.buyerContactEmaill = null
    },
  },
  watch: {
    'formModel.pocTypeId'(newVal, oldVal) {


      const column = this.findObject(this.formOption4.column, 'pocCustomerId')
      column.display = newVal == 'Other'
      if (!column.display) {
        this.formModel.pocCustomerId = ''
        this.formModel.pocCustomerName = ''
      }
    },

  },
};
</script>

<style lang='scss' scoped>
#app {
  background-color: white;
  min-height: 100vh;
  /* 确保背景颜色覆盖整个视口 */
  overflow: auto;
  /* 超出内容时显示滚动条 */
}

.header {
  background-color: #f0f2f5;
  padding: 5px;
}

.line1 {
  //padding: 15px;
  margin: 0;
}

.line2 {
  display: flex;
  align-items: flex-end;
  /* 确保元素在同一行 */
  justify-content: flex-start;
  //padding: 15px;
}

.line2>* {
  margin: 0;
  /* 移除默认的外边距 */
  //z-index: 99999;
}

span {
  padding: 5px;
  /* 在<span>元素内部添加5px的内边距 */
}

.form-content,
.form-inner {
  margin: 20px;
}

.el-collapse-item__header{
  padding: 10px;
  background-color: #f5f5f5;
  .info{
    font-size:18px;
    font-weight:500
  }
}

.el-input__wrapper {
  height: 30px;
}
</style>
