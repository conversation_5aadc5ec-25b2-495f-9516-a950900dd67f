<template>
  <div class="main">
    <div class="title">
      <h3>{{ $t("wel1.newTestRequestForm") }}</h3>
      <h3>
        <el-popover width="260" trigger="hover" placement="bottom" >
          <ul class="add-menu" v-if="menu.length > 0">
            <li v-for="item in menu[0].children[0].children[0].children" :key="item.id">
              <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                <router-link target="_blank" :to="{ path: item.path }">
                  {{ item.name.length > 30 ? (item.name.substr(0,28)+'...') : item.name }}
                </router-link>
              </el-tooltip>
            </li>
          </ul>
          <el-button class="line-btn add-trf-btn"  slot="reference"  id="add-trf-btn1" >
            <img src="/img/icon/addTrf.png"/> {{ $t("wel1.newApplication") }}
            <i class="el-icon-arrow-down pull-right" style="padding-top: 6px;font-weight: bold;"></i>
          </el-button>
        </el-popover>
      </h3>
    </div>
    <NewTestRequestForm id="last-trf"/>
  </div>
</template>

<script>
    import {mapGetters} from "vuex";

    export default {
      name: "TrfTopView",
      components: {
        NewTestRequestForm: (resolve) => require(["@/views/dashboard/components/NewTestRequestForm.vue"], resolve),
      },
      computed: {
        ...mapGetters(["menu", "language"]),
      }
    }
</script>

<style scoped lang="scss">
  .main {
    padding: 0 16px 24px 16px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    min-height: 520px;

    .title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      h3 {
        color: #1b1b1b;
        font-weight: bold;
        text-rendering: optimizeLegibility;
        text-transform: uppercase;
      }
    }
  }

  .add-menu {
    padding-inline-start: 0;
    font-size: 14px;
    font-weight: 400;
    li {
      list-style: none;
      height: 40px;
      width: 100%;
      color: #000000;
      display: inline-block;
      transition: all .2s;
      padding: 10px 10px;

      a {
        display: block;
      }

      &:hover {
        background: rgba(255, 102, 0, 0.1);
        color: #FF6600;
      }
    }
  }

  .add-trf-btn {
    width: 300px;
    text-align: start;
    font-size: 16px;
    font-weight: 500;
    &:hover {
      background-color: transparent;
    }
    img {
      vertical-align: text-bottom;
    }
  }

</style>
