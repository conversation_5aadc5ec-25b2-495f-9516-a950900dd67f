import request from '@/router/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/nkop-user/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const remove = (ids) => {
    return request({
        url: '/api/nkop-user/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const add = (row) => {
    return request({
        url: '/api/nkop-user/submit',
        method: 'post',
        data: row
    })
}

export const update = (row) => {
    return request({
        url: '/api/nkop-user/update',
        method: 'post',
        data: row
    })
}

export const getUser = (id) => {
    return request({
        url: '/api/nkop-user/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const getUserInfo = () => {
    return request({
        url: '/api/nkop-user/info',
        method: 'get',
    })
}

export const resetPassword = (userIds) => {
    return request({
        url: '/api/nkop-user/reset-password',
        method: 'post',
        params: {
            userIds,
        }
    })
}

export const updatePassword = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/user/account/update-password',
        method: 'post',
        data: form
    })
}
export const updatePhone = (form) => {
    return request({
        url: '/api/sgs-mart/customer/account/updCustomerPhone',
        method: 'post',
        data: form
    })
}
export const queryWechatBinding = () => {
    return request({
        url: '/api/sgs-mart/wechat/userBinding/selectBindingByUser',
        method: 'post',
    })
}
export const updateWechatPush = (form) => {
    return request({
        url: '/api/sgs-mart/wechat/userBinding/updateWechatPush',
        method: 'post',
        data: form
    })
}

export const queryUserNotification = () => {
    return request({
        url: '/api/sgs-mart/userSetting/queryUserNotification',
        method: 'get',
    })
}
export const updateEmailPush = (emailNotification) => {
    return request({
        url: '/api/sgs-mart/userSetting/updateUserNotification',
        method: 'get',
        params: {
            emailNotification,
        }
    })
}
