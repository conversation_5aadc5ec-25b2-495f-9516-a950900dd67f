<template>
    <div class="imageForm">
        <el-form :model="props.form" @submit.native.prevent :rules="rules" ref="formRef">

             
<el-row>
    <el-card class="article-card">
        <el-form-item :label="$t('work.pageCreate.title') + ':'" style="width:100%;" prop="title" required>
            <el-input type="textarea" :placeholder="$t('work.pageCreate.enterTitle')" :rows="2" v-model="props.form.title" maxlength="500" show-word-limit></el-input>
        </el-form-item>
    
        <div class="content-container">
            <TransitionGroup 
                name="fade-move" 
                tag="div"
                :css="true"
                @before-enter="beforeEnter"
                @enter="enter"
                @leave="leave"
                style="width: 100%;height: 100%;"
            >
                <div v-if="props.form.knowledgeExtDTOList.length === 0" class="no-data">
                    {{ $t('work.pageCreate.noData') }}
                </div>
                <div v-else v-for="(item,index) in props.form.knowledgeExtDTOList" 
                    :key="item.id || item.extCover || index" 
                    :class="{'image-item':true, 'first-item':index===0}"
                    :data-index="index"
                >
                    <el-row style="width: 100%;">
                        <el-col :span="8">
                            <div style="position: relative;width: 100%;border-radius: 10px;">
                                <el-image :src="item.extCover" fit="contain" alt="image" style="width: 100%; height: 220px;border-radius: 0 0 10px 10px;"  :preview-src-list="[item.extCover]"/>
                                <div class="image-overlay">
                                    <div class="image-overlay-info">{{ item.fileName }}<img @click.stop="handleAuthorization(item)" :src="item.authorization==1?'/pageCreate/unlock.svg':' /pageCreate/lock.svg'" alt="edit" style="width: 15px;height: 15px;margin-left: 5px;" /></div>
                                    <div class="image-overlay-info">{{ $t('work.pageCreate.size') }}：{{ getFileSize(item.fileSize) }} {{ $t('work.pageCreate.specification') }}：{{ getFileType(item) }}</div>
                                </div>
                            </div>
                            <!-- <div style="font-size: 12px;padding-left: 5px;">
                           
                        </div> -->
                        </el-col>
                        <el-col :span="16">
                            <el-form :model="item" label-position="right" label-width="80px">
                                <el-row>
                                    <el-form-item :label="$t('work.pageCreate.title')" style="width: 100%;">
                                        <el-input v-model="item.extTitle" :placeholder="$t('work.pageCreate.titlePlaceholder')"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item :label="$t('work.pageCreate.summary') + ':'" style="width: 100%;">
                                        <el-input v-model="item.extAbstract" :placeholder="$t('work.pageCreate.summaryPlaceholder')" type="textarea" :rows="5" maxlength="500" show-word-limit>
                                        </el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row   class="icon-container">
                                    <div class="icon-item move" v-if="index!=0" @click="handleTop(index)">
                                        <img src="/pageCreate/shang.svg" :alt="$t('work.pageCreate.moveUp')" />
                                        {{ $t('work.pageCreate.moveUp') }}</div>
                                    <div class="icon-item move" v-if="index!=props.form.knowledgeExtDTOList.length-1" @click="handleBottom(index)">
                                        <img src="/pageCreate/xia.svg" :alt="$t('work.pageCreate.moveDown')" />
                                        {{ $t('work.pageCreate.moveDown') }}</div>
                                    <div class="icon-item image" @click="handlePreviewImage(item.extCover)">
                                        <img src="/pageCreate/look.svg" :alt="$t('work.pageCreate.viewOriginal')"   />
                                        {{ $t('work.pageCreate.viewOriginal') }}</div>
                                    <div class="icon-item image" @click="handleReplaceImage(index)">
                                        <img src="/pageCreate/edit.svg" :alt="$t('work.pageCreate.editImage')" />
                                        {{ $t('work.pageCreate.editImage') }}</div>
                                    <div class="icon-item delete" @click="handleDelete(index)">
                                        <img src="/pageCreate/del.svg" :alt="$t('work.pageCreate.delete')" />
                                        {{ $t('work.pageCreate.delete') }}</div>
                            <!-- <el-icon @click="handleReplaceImage(index)" title="替换图片"><Edit /></el-icon>
                            <el-icon v-if="index!=0" @click="handleTop(index)" title="上移"><Top /></el-icon>
                            <el-icon v-if="index!=props.form.knowledgeExtDTOList.length-1" title="下移" @click="handleBottom(index)"><Bottom /></el-icon>
                            <el-icon @click="handleDelete(index)" title="删除"><DeleteFilled /></el-icon> -->
                                </el-row>
                                
                            </el-form>
                        </el-col>
                    </el-row>
                    <!-- <el-row type="flex" justify="space-between" style="width: 100%;" :gutter="10">
                       
                        <div class="icon">
                           
                            <el-icon @click="handleReplaceImage(index)" title="替换图片"><Edit /></el-icon>
                            <el-icon v-if="index!=0" @click="handleTop(index)" title="上移"><Top /></el-icon>
                            <el-icon v-if="index!=props.form.knowledgeExtDTOList.length-1" title="下移" @click="handleBottom(index)"><Bottom /></el-icon>
                            <el-icon @click="handleDelete(index)" title="删除"><DeleteFilled /></el-icon>
                        </div>
                    </el-row> -->
                  
                </div>
            </TransitionGroup>
        </div>
     
      
        <div class="addImage" @click="openUploadDialog">
        
        <img src="/pageCreate/tuxiang.svg" alt="addImage" style="width:85.5px;height: 73px;">
   
</div>
      
       
        <!-- <el-form-item :label="$t('work.pageCreate.attachments') + ':'" style="width: 100%;" class="attachment-form-item">
            <div class="attachment-list">
                <div v-if="!props.form.knowledgeAttachmentDTOList?.length" class="no-attachment">
                    {{ $t('work.pageCreate.noAttachments') }}
                </div>
                <div v-else v-for="(item, index) in props.form.knowledgeAttachmentDTOList" :key="index" class="attachment-item">
                    <span class="attachment-name" @click="openFile(item.cloudId)">{{ item.cloudId.substring(item.cloudId.lastIndexOf('/') + 1) }}</span>
                    <el-icon class="delete-icon" @click="handleDeleteAttachment(index)"><DeleteFilled /></el-icon>
                </div>
            </div>
            <el-upload
                class="attachment-upload"
                :show-file-list="false"
                :before-upload="beforeAttachmentUpload"
                :http-request="handleAttachmentUpload"
            >
            <el-button type="primary" size="small">{{ $t('work.pageCreate.addAttachment') }}</el-button>
            </el-upload>
        </el-form-item> -->
    </el-card>
 
</el-row>

<!-- <el-row >
    <el-card>
     
       
    </el-card>

</el-row> -->





</el-form>
<UploadDialog 
            v-model="uploadDialogVisible" 
            :attachments="props.form.knowledgeExtDTOList"
            :limit="['.png','.jpg','.jpeg','.gif','.bmp','.svg','.webp']"
            @update:attachments="updateAttachments"
            type="image"
        />
    </div>

</template>
<script lang="ts" setup>
import { uploadFile } from '@/api/upload'
import { ElMessage, ElMessageBox } from 'element-plus'
import UploadDialog from './components/UploadDialog.vue'
//import { getCloudFileURL } from '@/api/getCloudFileUrl.ts'
import { useI18n } from 'vue-i18n'
import {  reactive, watch, ref,computed } from 'vue'

const { t } = useI18n()
const uploadDialogVisible = ref(false)
// Form validation related
const formRef = ref()
const rules =computed(()=>{
  return {
  title: [
    { required: true, message: t('work.pageCreate.pleaseEnter') + t('work.pageCreate.title'), trigger: 'blur' }
  ]
}
})
const openUploadDialog = (event: Event) => {
  event.preventDefault()
  event.stopPropagation()
  uploadDialogVisible.value = true
}

const updateAttachments = (newAttachments: any[]) => {
    debugger
    props.form.knowledgeExtDTOList = newAttachments
  //props.form.knowledgeAttachmentDTOList = newAttachments
}

// Make validate method available for parent component
defineExpose({
  validate: () => {
    if (formRef.value) {
      return formRef.value.validate()
    }
    return Promise.resolve(true)
  }
})

const props=defineProps({
    form:{
        type:Object,
        default:() => ({
            knowledgeAttachmentDTOList: []
        })
    }
})

// Initialize knowledgeExtDTOList if it doesn't exist
if (!props.form.knowledgeExtDTOList) {
    props.form.knowledgeExtDTOList = []
}

// Initialize knowledgeAttachmentDTOList if it doesn't exist
if (!props.form.knowledgeAttachmentDTOList) {
    props.form.knowledgeAttachmentDTOList = []
}

// Create a reactive map to store image resolutions
const imageResolutions = reactive<Record<string, string>>({})

const getImageResolution = (url: string, index: number) => {
    if (imageResolutions[index]) return
    
    const img = new Image()
    img.onload = () => {
        imageResolutions[index] = `${img.width}px*${img.height}px`
    }
    img.onerror = () => {
        imageResolutions[index] = t('work.pageCreate.unknownResolution')
    }
    img.src = url
}

// const handleAddImage = () => {
//     const input = document.createElement('input')
//     input.type = 'file'
//     input.accept = 'image/*'
//     input.onchange = async (e: Event) => {
       
//         const file = (e.target as HTMLInputElement).files?.[0]
//         if (!file) return

//         // Check file size (2MB limit)
//         if (file.size > 2 * 1024 * 1024) {
//             ElMessage.error(t('work.pageCreate.imageSizeLimit'))
//             return
//         }

//         try {
//             const formData = new FormData()
//             formData.append('file', file)
//             formData.append('systemID', '59')
//             const res:any = await uploadFile(formData)
//             if (res?.rows && res.rows.length > 0) {
//                 // Create new knowledgeExtDTO object
//                 const newExt = {
//                    // id: 0,
//                    // createUser: "",
//                    // createDept: 0,
//                    // createTime: "",
//                    // updateUser: "",
//                    // updateTime: "",
//                     status: 1,
//                    // isDeleted: 0,
//                    // tenantId: "",
//                    // knowledgeId: 0,
//                     extTitle: "",
//                     extAbstract: "",
               
//                     extContent: "",
//                     extCover: res.rows[0],
//                     fileName:file.name,
//                     fileSize:file.size,
//                     sortKey: props.form.knowledgeExtDTOList.length+1,
//                     tenantId: "000000"
//                 }
     
//                 // Add to knowledgeExtDTOList
//                 props.form.knowledgeExtDTOList.push(newExt)
//                 console.log(props.form)
//             }
//         } catch (error) {
//             ElMessage.error(t('work.pageCreate.uploadFailed'))
//         }
//     }
//     input.click()
// }

const handleTop=(index:number)=>{
    const list = props.form.knowledgeExtDTOList;
    const item = list[index];
    list.splice(index, 1);
    list.splice(index - 1, 0, item);
    // Update sortKey for all items
    list.forEach((item:any, idx:number) => {
        item.sortKey = idx+1;
    });
}   

const handleBottom=(index:number)=>{        
    const list = props.form.knowledgeExtDTOList;
    const item = list[index];
    list.splice(index, 1);
    list.splice(index + 1, 0, item);
    // Update sortKey for all items
    list.forEach((item:any, idx:number) => {
        item.sortKey = idx+1;
    });
}

const handleDelete=(index:number)=>{
    props.form.knowledgeExtDTOList.splice(index,1);
    // Update sortKey for remaining items
    props.form.knowledgeExtDTOList.forEach((item:any, idx:number) => {
        item.sortKey = idx+1;
    });
}

const handlePreviewImage = (url: string) => {
    // Find the el-image component with this URL and programmatically trigger its preview
    const imageElements = document.querySelectorAll('.el-image__inner');
    for (const img of imageElements) {
        if ((img as HTMLImageElement).src.includes(url)) {
            // Trigger click on the image to open preview
            (img as HTMLElement).click();
            return;
        }
    }
    
    // Fallback if image not found in DOM
    ElMessageBox.alert(`<img src="${url}" style="max-width: 100%; max-height: 80vh; display: block; margin: 0 auto;">`, '', {
        dangerouslyUseHTMLString: true,
        customClass: 'image-preview-dialog',
        showClose: true,
        closeOnClickModal: true,
        closeOnPressEscape: true,
        showConfirmButton: false,
    });
}

const handleReplaceImage = (index: number) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = async (e: Event) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (!file) return

        // Check file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            ElMessage.error(t('work.pageCreate.imageSizeLimit'))
            return
        }

        try {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('systemID', '59')
            const res:any = await uploadFile(formData)
            if (res?.rows && res.rows.length > 0) {
                // Replace the image URL in the existing item
                props.form.knowledgeExtDTOList[index].extCover = res.rows[0]
                props.form.knowledgeExtDTOList[index].fileName=file.name
                props.form.knowledgeExtDTOList[index].fileSize=file.size
            }
        } catch (error) {
            ElMessage.error(t('work.pageCreate.uploadFailed'))
        }
    }
    input.click()
}

const beforeEnter = () => {
   // (el as HTMLElement).style.opacity = '0'
    //(el as HTMLElement).style.transform = 'translateY(30px)'
}

const enter = (el: Element, done: () => void) => {
    const animation = (el as HTMLElement).animate([
        {
            opacity: 0,
            //transform: 'translateY(30px)'
        },
        {
            opacity: 1,
            transform: 'translateY(0)'
        }
    ], {
        duration: 300,
        easing: 'ease-in-out'
    })
    animation.onfinish = done
}

const leave = (el: Element, done: () => void) => {
    const animation = (el as HTMLElement).animate([
        {
            opacity: 1,
            transform: 'translateY(0)'
        },
        {
            opacity: 0,
           // transform: 'translateY(30px)'
        }
    ], {
        duration: 300,
        easing: 'ease-in-out'
    })
    animation.onfinish = done
}

// const beforeAttachmentUpload = (file: File) => {
//     const isLt10M = file.size / 1024 / 1024 < 10
//     if (!isLt10M) {
//         ElMessage.error('附件大小不能超过 10MB!')
//         return false
//     }
//     return true
// }

// const handleAttachmentUpload = async (options: any) => {
//     try {
//         const formData = new FormData()
//         formData.append('file', options.file)
//         formData.append('systemID', '59')
//         const res: any = await uploadFileAttachment(formData)
//         if (res?.data && res.data.length > 0) {
//             props.form.knowledgeAttachmentDTOList.push({
//                 cloudId: res.data[0].cloudID,
//                 fileName:options.file.name,
//                 fileType:options.file.type,
//                 status:1,
//             })
//         }
//     } catch (error) {
//         ElMessage.error('上传失败')
//     }
// }

// const handleDeleteAttachment = (index: number) => {
//     props.form.knowledgeAttachmentDTOList.splice(index, 1)
// }

// const openFile = (fileId: string) => {
//     getCloudFileURL({ cloudID: fileId, systemID: 1, networkType: 2 }).then((res:any) => {
//         window.open(res)
//     })
// }

 const getFileSize = (bytes: number): string => {
    if (!bytes || bytes === 0) return '0 B';
    
    if (bytes < 1024) {
        return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
        return (bytes / 1024).toFixed(2) + ' KB';
    } else {
        return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    }
} 

 const getFileType = (item: any): string => {
    // Trigger resolution calculation if not already done
    if (item && item.extCover) {
        const index = props.form.knowledgeExtDTOList.findIndex((i: any) => i.extCover === item.extCover)
        getImageResolution(item.extCover, index)
        return imageResolutions[index] || t('work.pageCreate.loading')
    }
    return t('work.pageCreate.unknownResolution')
} 

// Watch for changes in knowledgeExtDTOList to update resolutions
watch(() => [...props.form.knowledgeExtDTOList], (newList) => {
    newList.forEach((item: any, index: number) => {
        if (item.extCover) {
            getImageResolution(item.extCover, index)
        }
    })
}, { deep: true, immediate: true })

const handleAuthorization = (item: any) => {
    debugger
  // Toggle authorization status between 0 and 1
  item.authorization = item.authorization === 1 ? 0 : 1;
}
</script>
<style lang="scss">
.imageForm{
    height: 100%;
    overflow-y: auto;
    .article-card{

padding-top: 30px !important;
padding-left:30px !important;
.el-form-item{
    margin: 20px 0px;
}
}
    .el-card{
        .attachment-form-item{
            .el-form-item__label{
                height: 80%;
                align-items: center;
            }
            .el-form-item__content{
                display: flex;
                align-items: baseline;
                gap: 10px;
            }
        }
        width: 100%;
        .el-card__body{
            .content-container {
                margin-top: 20px;
                width: 100%;
                min-height: 280px; // Minimum height
                max-height: calc(100vh); // Adjust this value based on your layout
                // min-height: 400px; // Minimum height
                // max-height: 800px; // Maximum height
                overflow-y: auto;
               
            }
            .icon-container{
                display: flex;
                justify-content: flex-end;
                gap: 20px;
               height: 40px;
              
                .icon-item {
&.move{
    color: #256DE1;
}
&.image{
    color:#FF6600
}
&.delete{
    color:#878787
}
                    cursor: pointer;
                    display: flex;
                    column-gap: 5px;
                    align-items: center;
                    font-size: 12px;
                    &:hover {
                        opacity: 0.8;
                    }
                }
            }
            .addImage{
                margin-top: 20px;
                margin-bottom: 10px;
                width: 100%;
                height: 200px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                font-size: 40px;
                //padding: 20px 0px;
                cursor: pointer;
                background-color: rgb(245,245,245);
                transition: all 0.2s ease-in-out;
                &:hover{
                    color: rgb(255,102,0);
                    background-color: #ccc;
                }
            }
            width: 100%;
            padding: 10px 10px;
            flex-wrap: wrap;
            .el-form-item{
                margin-bottom: 0px;
            }
            .content{
                height: 700px;
                .el-form-item__content{
                    display: inline;
                    div{
                        height: 98%;
                    }
                }
             
            }
            .attachment-list {
                margin-bottom: 10px;
                display: flex;
                column-gap: 10px;
                .no-attachment {
                    color: #909399;
                    font-size: 14px;
                    padding: 10px 0;
                }
                .attachment-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0px 8px;
                    background: #f5f7fa;
                    margin-bottom: 8px;
                    border-radius: 4px;
                    .attachment-name {
                        flex: 1;
                        margin-right: 10px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        color: rebeccapurple;
                        cursor: pointer;
                    }
                    .delete-icon {
                        cursor: pointer;
                        color: #f56c6c;
                        &:hover {
                            color: #f78989;
                        }
                    }
                }
            }
            .attachment-upload {
                margin-top: 10px;
            }
        }
    }
}

.image-item {
    margin-top: 20px;
    &.first-item{
        margin-top: 0px;
    }
   // margin-bottom: 16px;
    background: rgb(245,245,245);
    padding: 25px 25px;
    
  
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
   // height: 20%;
    font-size: 11px;
    display: flex;
    flex-wrap: wrap;
    
   // justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    pointer-events: auto;
    z-index: 10;
    border-radius:  0 0 10px 10px;
    .image-overlay-info{
        display: flex;
        align-items: center;
        color: #fff;
        //white-space: nowrap;
        padding-left: 10px;
      
        padding-top: 4px;
        white-space: nowrap;
        img {
            pointer-events: auto;
            cursor: pointer;
        }
    }
}

.fade-move-move {
    transition: transform 0.3s ease;
}

.fade-move-enter-active,
.fade-move-leave-active {
    transition: all 0.3s ease;
}

.fade-move-enter-from,
.fade-move-leave-to {
    opacity: 0;
    //transform: translateY(0px);
}

.fade-move-leave-active {
    position: absolute;
    width: 100%;
}

.no-data {
    background-color: rgb(245,245,245);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 14px;
}

.image-preview-dialog {
    .el-message-box__message {
        max-width: 90vw;
        max-height: 80vh;
        overflow: auto;
    }
}
</style>