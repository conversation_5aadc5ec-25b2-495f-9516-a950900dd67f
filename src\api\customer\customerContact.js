import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/add',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const getListByUser = (params) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/noPage/by-user',
        method: 'get',
        params: {
            ...params
        }
    })
}


export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const setDefault = (id,isDefault) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/set-default',
        method: 'get',
        params: {
            id,
            isDefault
        }
    })
}
