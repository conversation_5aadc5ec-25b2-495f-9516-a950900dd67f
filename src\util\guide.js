
/**
 * 新手引导
 * @openDialog 表示当前步骤需要打开 dialog 窗口
 * @end 表示当前步骤是否为最后的结束步骤
 * @dialogInput 当前步骤中是否有弹窗输入框
 * @text 是国际化里面的语言包字段名
 * @scroll 有目标元素处于可视区外需要滚动元素到可视区内
 * @attachTo 配置提示语在上方或者下方，TODO::左右暂没有实现
 * */
let offsetHeight = document.body.offsetHeight
let guide = {
  addBuyer: {
    type: 'addBuyer',
    step: [
      {
        text: 'guide.clickCompanyManage||guide.step1||guide.total5',
        attachTo: { element: '#guideSetting', on: 'bottom' },
      },
      {
        text: 'guide.selectClient||guide.step2||guide.total5',
        attachTo: { element: '#tab-relationship', on: 'bottom' }
      },
      {
        openDialog: true,
        text: 'guide.clickAdd||guide.step3||guide.total5',
        attachTo: { element: '#add-buyer', on: 'bottom' }
      },
      {
        dialogInput: true,
        text: 'guide.inputKeyword||guide.step4||guide.total5',
        attachTo: { element: '#add-buyer-dialog .el-dialog', on: 'bottom' }
        // attachTo: { element: '#input-buyer-name', on: 'bottom' }
      },
      // {
      //   end: true,
      //   text: 'guide.clickSubmit||guide.step5||guide.total5',
      //   attachTo: { element: '#add-buyer-confirm', on: 'bottom' }
      // }
    ]
  },
  addContact: {
    type: 'addContact',
    step: [
      {
        text: 'guide.clickContact||guide.step1||guide.total4',
        attachTo: { element: '#tab-contact', on: 'bottom' }
      },
      {
        openDialog: true,
        text: 'guide.clickAdd||guide.step2||guide.total4',
        attachTo: { element: '#add-contact', on: 'bottom' }
      },
      {
        dialogInput: true,
        text: 'guide.inputCOntactInfo||guide.step3||guide.total4',
        attachTo: { element: '#add-contact-dialog .el-dialog', on: 'bottom' }
        // attachTo: { element: '#add-contact-form', on: 'bottom' }
      },
      // {
      //   end: true,
      //   text: 'guide.clickSubmit||guide.step4||guide.total4',
      //   attachTo: { element: '#add-contact-confirm', on: 'bottom' }
      // },
    ]
  },
  setAddress: {
    type: 'setAddress',
    step: [
      {
        text: 'guide.clickAddress||guide.step1||guide.total4',
        attachTo: { element: '#tab-address', on: 'bottom' }
      },
      {
        openDialog: true,
        text: 'guide.clickAdd||guide.step2||guide.total4',
        attachTo: { element: '#add-address', on: 'bottom' }
      },
      {
        dialogInput: true,
        text: 'guide.inputAddress||guide.step3||guide.total4',
        attachTo: { element: '#add-address-dialog .el-dialog', on: 'bottom' }
        // attachTo: { element: '#add-address-form', on: 'bottom' }
      },
      // {
      //   end: true,
      //   text: 'guide.clickSubmit||guide.step4||guide.total4',
      //   attachTo: { element: '#add-address-confirm', on: 'bottom' }
      // },
    ]
  },
  setNotice: {
    type: 'setNotice',
    step: [
      {
        text: 'guide.ClickNotification||guide.step1||guide.total3',
        attachTo: { element: '#tab-notifaction', on: 'bottom' }
      },
      {
        text: 'guide.reportIssued||guide.step2||guide.total3',
        attachTo: { element: '#chioce-report', on: 'bottom' }
      },
      {
        end: true,
        text: 'guide.clickPass||guide.step3||guide.total3',
        attachTo: { element: '#choice-pass', on: 'top' }
      }
    ]
  },


  // 功能引导
  dashboard: {
    type: 'dashboard',
    isFunction: true, 
    step: [
      {
        text: 'guide.nav||guide.step1||guide.total7',
        subTxt: 'guide.navTxt',
        attachTo: { element: '#site-nav', on: 'bottom' }
      },
      {
        text: 'guide.trf||guide.step2||guide.total7',
        subTxt: 'guide.trfTxt',
        scroll:true,
        attachTo: { element: '#last-trf', on: 'bottom' }
      },
      {
        inRight: true,
        text: 'guide.createTrf||guide.step3||guide.total7',
        subTxt: 'guide.createTrfTxt',
        scroll:true,
        attachTo: { element: '#add-trf-btn1', on: 'bottom' }
      },
      {
        text: 'guide.trfStatus||guide.step4||guide.total7',
        subTxt: 'guide.trfStatusTxt',
        scroll:true,
        attachTo: { element: '#status-list', on: 'bottom' }
      },
      {
        text: 'guide.testResult||guide.step5||guide.total7',
        subTxt: 'guide.testResultTxt',
        scroll: true,
        // scroll: offsetHeight < 700 ? 250 : 550,
        attachTo: { element: '#test-result', on: 'bottom' }
      },
      {
        inRight: true,
        text: 'guide.switchLang||guide.step6||guide.total7',
        subTxt: 'guide.switchLangTxt',
        attachTo: { element: '#language', on: 'bottom' }
      },
      {
        end: true,
        inRight: true,
        text: 'guide.viewAgain||guide.step7||guide.total7',
        subTxt: 'guide.viewAgainTxt',
        attachTo: { element: '#viewAgain', on: 'bottom' }
      }
    ]
  },
  trfList: {
    type: 'trfList',
    isFunction: true,
    step: [
      {
        inRight: true,
        text: 'guide.trfCreate||guide.step1||guide.total4',
        subTxt: 'guide.trfCreateTxt',
        attachTo: { element: '#add-trf-btn', on: 'bottom' }
      },
      {
        text: 'guide.filterTrf||guide.step2||guide.total4',
        subTxt: 'guide.filterTrfTxt',
        attachTo: { element: '#trf-list-filter', on: 'bottom' }
      },
      {
        text: 'guide.locationTrf||guide.step3||guide.total4',
        subTxt: 'guide.locationTrfTxt',
        attachTo: { element: '#trf-list-status-filter', on: 'bottom' }
      },
      {
        end: true,
        text: 'guide.trfDetail||guide.step4||guide.total4',
        subTxt: 'guide.trfDetailTxt',
        attachTo: { element: '#trfTable table tbody tr td:nth-child(3)', on: 'top' },
      }
    ]
  },
  createTrf: {
    type: 'createTrf',
    isFunction: true,
    step: [
      {
        inRight: true,
        text: 'guide.modify||guide.step1||guide.total7',
        subTxt: 'guide.modifyTxt',
        attachTo: { element: '#trf-modify-btn', on: 'bottom' }
      },
      {
        text: 'guide.tabSwitch||guide.step2||guide.total7',
        subTxt: 'guide.tabSwitchTxt',
        attachTo: { element: '#trf-tabs', on: 'bottom' }
      },
      {
        text: 'guide.quickBar||guide.step3||guide.total7',
        subTxt: 'guide.quickBarTxt',
        attachTo: { element: '#trf-left-nav', on: 'top' }
      },
      {
        text: 'guide.contentFill||guide.step4||guide.total7',
        subTxt: 'guide.contentFillTxt',
        attachTo: { element: '#customer_card', on: 'top' }
      },
      { 
        // scroll: offsetHeight < 650 ? 260 : offsetHeight <= 830 ? 250 : offsetHeight <= 860 ? 133 : offsetHeight <= 900 ? 95 : offsetHeight <= 970 ? 70 : 0,
        scroll: true,
        text: 'guide.continue||guide.step5||guide.total7',
        subTxt: 'guide.continueTxt',
        attachTo: { element: '#create-trf-contunue', on: 'top' }
      },
      {
        isFooter: true,
        text: 'guide.actionBar||guide.step6||guide.total7',
        subTxt: 'guide.actionBarTxt',
        attachTo: { element: '.sgs-footer', on: 'top' }
      },
      {
        end: true,
        inRight: true,
        text: 'guide.reviewAgain||guide.step7||guide.total7',
        subTxt: 'guide.reviewAgainTxt',
        attachTo: { element: '#viewAgain', on: 'bottom' }
      }
    ]
  },

  
}

export default guide
