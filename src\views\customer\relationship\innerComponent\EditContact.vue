<template>
    <div class="smart_views_customer_relationship_component_EditContact" id="smart_views_customer_relationship_component_EditContact">
        <el-form :model="customerForm" ref="customerFormRef" label-position="top">
            <el-button v-if="!customerForm.contacts || customerForm.contacts.length<10" type="text" @click="addContact">+ {{t('scm.contact.addContact')}}</el-button>
            <el-table
                    :data="customerForm.contacts"
                    size="small"
                    :border="false"
                    style="width: 100%"
                    :max-height="400">
                <el-table-column type="index">
                    <template #header>#</template>
                </el-table-column>
                <el-table-column prop="contact" :label="t('scm.contact.name')">
                    <template #default="scope">
                        <el-form-item :prop="'contacts.'+scope.$index+'.contact'" :rules="[{ required: true, message: t('scm.validate.contactName'), trigger: ['blur','change'] }]">
                            <el-input clearable v-model="scope.row.contact" :maxlength="200" show-word-limit/>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="contactTelephone" :label="t('scm.contact.mobile')">
                    <template #default="scope">
                        <el-form-item :prop="'contacts.'+scope.$index+'.contactTelephone'" >
                            <el-input clearable maxlength="50" show-word-limit v-model="scope.row.contactTelephone" />
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="contactEmail" :label="t('scm.contact.email')">
                    <template #default="scope">
                        <el-form-item :prop="'contacts.'+scope.$index+'.contactEmail'"
                                      :rules="[{ type: 'email', message: t('register.validate.emailFormat'), trigger: ['blur','change'] }]"
                        >
                            <el-input clearable maxlength="50" show-word-limit v-model="scope.row.contactEmail" />
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column :label="t('action')" width="100px">
                    <template #default="scope">
                        <el-button size="mini" type="primary" plain text @click="removeContact(scope.$index)" icon="Delete"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
    </div>

</template>

<script setup>
import {mapGetters} from 'vuex';
import {ref, reactive , watch,defineExpose,onMounted} from 'vue';
const customerFormRef = ref();
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
defineOptions({
    name: 'EditContact'
})
const emit = defineEmits(['update:customerForm'])
const props = defineProps({
    contacts:{
        type:Array,
        default:[]
    }
})
const customerForm = reactive({
    contacts: []
})

onMounted(()=>{
    customerForm.contacts = JSON.parse(JSON.stringify(props.contacts || [])) ;
})


const addContact = () => {
    customerForm.contacts.push({
        contact: '',
        contactTelephone: '',
        contactEmail: '',
        languageId:'en-US'
    });
};
const removeContact = (index) => {
    customerForm.contacts.splice(index, 1);
};
const getContactSaveData = async ()=>{
    try {
        await customerFormRef.value.validate();
    }catch (e){
        return
    }
    return customerForm.contacts;
}
defineExpose({getContactSaveData})

</script>

<style lang="scss">
.smart_views_customer_relationship_component_EditContact {
  .el-table thead th{
    font-weight: normal !important;
  }

}
</style>