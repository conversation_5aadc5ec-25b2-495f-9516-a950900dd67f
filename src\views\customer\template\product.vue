<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_template_product" id="sgs_smart_template_product">
            <list-comp form-purpose="Product"></list-comp>
        </div>
    </basic-container>
</template>

<script>
    import listComp from './list'
    export default {
        name: "product",
        data() {
            return {
                pageLoading: false,
            }
        },
        methods: {
            initPage() {
            }
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {},
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {listComp}
    }
</script>

<style lang="scss">
    .sgs_smart_template_product {

    }
</style>