// 导入dayjs及两个插件
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
//import tz from 'dayjs/plugin/timezone';

import { objectIsNull } from "@/util/validate";


const shTz = 'Asia/Shanghai'
  // 继承插件能力
  dayjs.extend(utc)
	dayjs.extend(timezone)

export function tzFormChina(dateTime,format) {
  if(objectIsNull(format)){
    format='YYYY-MM-DD HH:mm:ss';
  }
  // 本地和utc差
  const os = dayjs().utcOffset()
  // 东8和utc差 8*60
  const df = os - 8 * 60
  const newDateTime = dayjs(dateTime).add(df,'m').format(format)
  // console.log("转换后：" + newDateTime)
  // console.info("转换前：" + dayjs(newDateTime).utc().utcOffset(8).format(format));
  return newDateTime;
}
export function tzToChina(dateTime,format) {
  // console.log('转为北京时间，转换前时间=='+dateTime);
  if(objectIsNull(format)){
    format='YYYY-MM-DD HH:mm:ss';
  }
  const newDateTime =  dayjs(dateTime).utc().utcOffset(8).format(format)
  // console.log('转为北京时间，转换后时间=='+newDateTime);
  return newDateTime
}


export function test() {
  // 获取当前时区字符串
	const tz = dayjs.tz.guess()
  // console.log(tz)

  // 获取当前时间，格式化输出
  const now = dayjs().format('YYYY-MM-DD HH:mm:ssD')
  // console.log(now)

  // 美国当前时间(不会改变当前时区)
  const day = dayjs(now).tz("America/Toronto").format('YYYY-MM-DD HH:mm:ss')
  console.log('美国时间:' + day)
  // 北京当前时间(不会改变当前时区)
  const day1 = dayjs(now).tz(shTz).format('YYYY-MM-DD HH:mm:ss')
  // console.log('北京时间:' + day1)

  // 当前时区时间(不会改变当前时区)
  var dae = dayjs("2024-02-02 12:00").format('YYYY-MM-DD HH:mm:ssD')
  const day2 = dayjs().utc(dae).tz(tz).format('YYYY-MM-DD HH:mm:ss')
  // console.log('当前时区时间:' + day2)

}
