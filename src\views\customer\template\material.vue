<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_template_material" id="sgs_smart_template_material">
           <list-comp form-purpose="Material"></list-comp>
        </div>
    </basic-container>
</template>

<script>
    import listComp from './list'
    export default {
        name: "material",
        data() {
            return {
                pageLoading: false,
            }
        },
        methods: {
            initPage() {
            }
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {},
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {listComp}
    }
</script>

<style lang="scss">
    .sgs_smart_template_material {
    }
</style>