<template>
  <div class="product-upload">
    <div class="module-title-container">
      <span class="module-title-text">Product Image</span>
    </div>
    <div class="product-upload-list" v-if="dataList.uploadImgList.length > 0">
      <!-- v-if="dataList.uploadImgList && dataList.uploadImgList.length > 0" -->
      <el-carousel height="150px"
                   trigger="click"
                   indicator-position="outside"
                   :autoplay="false"
                   arrow="never">
        <el-carousel-item v-for="(item, imgIndex) in dataList.uploadImgList"
                          :key="'img_' + imgIndex">
          <div class="carousel-item">
            <img class="carousel-img"
                 :src="item.imageUrl" />
            <!-- v-if="(createNew || btnRole(productForm, 'Edit')) && showDeleteIcon === imgIndex" -->
            <div class="mask">
              <div>
                <el-icon color="#ff6600"
                         @click="delImgDialog(imgIndex)">
                  <Delete />
                </el-icon>
                <el-icon color="#ff6600"
                         @click="openImgDialog(item)">
                  <ZoomIn />
                </el-icon>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div class="product-upload-add">
      <!-- v-if="dataList.uploadImgList.length < 6 && (createNew || btnRole(productForm, 'Edit'))" -->
      <el-upload :action="data.action"
                 :show-file-list="false"
                 :limit="5"
                 multiple
                 :data="{ systemID: 1 }"
                 accept=".jpg,.jpeg,.png"
                 :on-exceed="fileExceed"
                 :on-success="handleAvatarSuccess"
                 :before-upload="beforeAvatarUpload">
        <div class="product-upload-add-btn">
          <el-icon color="#000">
            <Plus />
          </el-icon>
          Upload
        </div>
      </el-upload>
    </div>
    <el-dialog v-model="data.imgDialogVisible">
      <div class="img-zoom">
        <img :src="data.imgDialogUrl" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElNotification, ElMessageBox, ElMessage } from 'element-plus'
// import { apiHost } from '@/utils/util'

// props
const props = defineProps({
  //   dataList: {
  //     type: Object,
  //     required: true,
  //   },
  createNew: Boolean,
  productForm: {
    type: Object,
    required: true,
  },
  btnRole: {
    type: Function,
    required: true,
  },
})
console.log(props)

const dataList = reactive({
  uploadImgList: [],
})
const data = reactive({
  systemID: 1,
  nodeHost: '',
  // todo 直接请求服务端，请求中间层需要处理鉴权
  // action: apiHost + '/api/sgsapi/FrameWorkApi/file/doUpload'
  // https://test.sgsmart-online.com
  action: '/api/sgsapi/FrameWorkApi/file/doUpload',
  imgDialogVisible: false,
  imgDialogUrl: '',
})
// 上传超限
const fileExceed = () => {
  ElNotification.success({
    title: 'Warning',
    message: 'You can upload 5 files at most',
    duration: 2000,
  })
}

// 上传前校验
const beforeAvatarUpload = (file) => {
  const isJPG = ['image/png', 'image/jpeg'].includes(file.type)
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isJPG) {
    ElMessage.error('The file type must be .jpeg,.jpg,.png fail')
  }
  if (!isLt20M) {
    ElMessage.error('file size should be less than 20 MB')
  }
  return isJPG && isLt20M
}

// 上传成功
const handleAvatarSuccess = (res, file) => {
  if (res.status != 200) {
    ElMessage.error('Upload fail')
    return
  }
  res.data.forEach((f) => {
    let { id, cloudID } = f
    let imageUrl = URL.createObjectURL(file.raw)
    let obj = {
      id,
      cloudID,
      imageUrl,
    }
    dataList.uploadImgList.push(obj)
  })
  //   res.data.forEach((f) => {
  //     const { id, cloudID } = f
  //     const imageUrl = URL.createObjectURL(file.raw)
  //     props.dataList.uploadImgList.push({ id, cloudID, imageUrl })
  //   })
}

// 删除图片弹窗
const delImgDialog = (imgIndex) => {
  ElMessageBox.confirm('Delete the image?', 'Warning', {
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel',
    type: 'warning',
  })
    .then(() => {
      dataList.uploadImgList.splice(imgIndex, 1)
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 图片放大
const openImgDialog = (item) => {
  data.imgDialogVisible = true
  data.imgDialogUrl = item.imageUrl
}

const emit = defineEmits(['update:uploadImgList'])
// 监听 uploadImgList 变化并通知父组件
watch(
  () => dataList.uploadImgList,
  (newVal) => {
    emit('update:uploadImgList', newVal)
  },
  { deep: true },
)
</script>

<style scoped lang="scss">
@use '@/assets/style/unit.module.scss' as *;

.product-upload {
  .product-upload-add {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 1px dashed $border-color;
    padding: $module-padding-horizontal 0;
    cursor: pointer;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .product-upload-add-btn {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
    }
  }

  .product-upload-list {
    // width: 100%;
    // height: 100%;
    // display: flex;
    // align-items: center;
    // justify-content: center;

    .carousel-item {
      position: relative;

      &:hover {
        .mask {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;

          .el-icon {
            margin: 0 20px;
            cursor: pointer;
          }
        }
      }
    }
    .carousel-img {
      object-fit: contain;
      height: 150px;
      width: 100%;
    }
  }
  .img-zoom {
    text-align: center;
    width: 100%;
    img {
      max-width: 100%;
    }
  }
}
// .product-upload {
//   background-color: #fff;
//   margin-top: 16px;
//   margin-left: 16px;
//   padding: 16px;
//   position: relative;

//   .avatar-uploader-small {
//     position: absolute;
//     right: 20px;
//     top: 24px;
//     z-index: 35;
//   }
//   .avatar-uploader .el-upload {
//     border: 1px dashed #d9d9d9;
//     border-radius: 10px;
//     cursor: pointer;
//     position: relative;
//     overflow: hidden;
//   }
//   .avatar-uploader .el-upload:hover {
//     border-color: #FF6600;
//     color: #FF6600;
//   }
//   .avatar-uploader-icon {
//     font-size: 28px;
//     color: #8c939d;
//     width: 100%;
//     height: 160px;
//     line-height: 160px;
//     text-align: center;
//   }
//   .avatar-uploader-mini-icon {
//     color: #FF6600;
//   }
//   .avatar {
//     width: 100%;
//     height: 160px;
//     display: block;
//   }
//   .el-upload {
//     width: 100%;
//   }
// }
// .product-sub-title {
//   font-size: 18px;
//   font-weight: bold;
//   padding-bottom: 8px;
// }

// .carousel-img {
//   object-fit: contain;
//   height: 150px;
//   width: 100%;
// }
// .img_del {
//   position: absolute;
//   right: calc(50% - 27px);
//   top: calc(50% - 7px);
//   cursor: pointer;
// }
// .img_view {
//   position: absolute;
//   right: calc(50% + 17px);
//   top: calc(50% - 7px);
//   cursor: pointer;
// }

// }
</style>