import toastr from 'toastr'
import 'toastr/build/toastr.min.css'
import _ from 'lodash'
const timeOut = 3000
toastr.options.timeOut = 3000
toastr.options.extendedTimeOut = 1000
toastr.options.hideDuration = 2000
toastr.options.progressBar = true
toastr.options.preventDuplicates = true
toastr.options.closeButton = true

export default {
  warn(param) {
    if (_.isPlainObject(param)) {
      return toastr.warning(param.message || '', param.title || 'Warning', {timeOut: param.timeout || timeOut})
    } else if (_.isString(param)) {
      return toastr.warning(param, 'Warning', {timeOut: timeOut})
    } else {
      return toastr.warning(_.toString(param), 'Info', {timeOut: timeOut})
    }
  },
  success(param) {
    if (_.isPlainObject(param)) {
      return toastr.success(param.message || '', param.title || 'Success', {timeOut: param.timeout || timeOut})
    } else if (_.isString(param)) {
      return toastr.success(param, 'Success', {timeOut: timeOut})
    } else {
      return toastr.success(_.toString(param), 'Info', {timeOut: timeOut})
    }
  },
  error(param) {
    if (_.isPlainObject(param)) {
      return toastr.error(param.message || '', param.title || 'Error', {timeOut: param.timeout || timeOut})
    } else if (_.isString(param)) {
      return toastr.error(param, 'Error', {timeOut: timeOut})
    } else {
      return toastr.error(_.toString(param), 'Info', {timeOut: timeOut})
    }
  },
  info(param) {
    if (_.isPlainObject(param)) {
      return toastr.info(param.message || '', param.title || 'Info', {timeOut: param.timeout || timeOut})
    } else if (_.isString(param)) {
      return toastr.info(param, 'Info', {timeOut: timeOut})
    } else {
      return toastr.info(_.toString(param), 'Info', {timeOut: timeOut})
    }
  }
}
