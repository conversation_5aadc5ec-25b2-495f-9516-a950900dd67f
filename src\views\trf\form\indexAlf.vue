<template>
    <basic-container ref="homePage" :class="isTrfTab ?'trf-active':''">
        <div class="trf-base">
            <div class="info">
                <div>
                    <div class="left">
                        <div class="status">
                            <h4>{{ $t('trfList.trfNo') }}</h4>
                            <span v-if="trf && trf.trfStatus != 0" class="trf-number">{{ trf.trfNo }}</span>
                            <span v-else class="trf-no-data">{{ $t('training.NoData') }}</span>
                            <TRFStatus v-if="trf && trf.trfStatus != 0" :status="trf.trfStatus" style="display: inline-block; vertical-align: top; margin-left: 20px;" :reason="trf.pendingReason"/>
                        </div>
                        <!-- <span v-if="trf" class="status-tag" :class="statusArr[trf.trfStatus-1]">{{ $t(status[trf.trfStatus-1]) }}</span> -->
                        <div class="dates">
                            <span class="date" v-if="trf && trf.trfStatus != 0"><span class="tit">{{ $t('user.createTime') }}：</span>{{ trf ? trf.createTime : '' }}</span>&nbsp;&nbsp;
                             <span class="date" v-if="trf && trf.trfStatus != 0"><span class="tit">{{ $t('trfList.submitDate') }}：</span>{{ trf && trf.trfSubmissionDate ? trf.trfSubmissionDate : $t('trf.notSubmit') }}</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="item">
                            <p class="tit">{{ $t('buyer.name') }}:</p>
                            <el-tooltip effect="dark" :content="trf ? trf.trfCustomer.buyerCustomerGroupName : ''" placement="top" v-if="trf && trf.trfCustomer.buyerCustomerGroupCode && trf.trfCustomer.buyerCustomerGroupName.length > 20">
                                <strong>{{ trf && trf.trfCustomer.buyerCustomerGroupCode ? trf.trfCustomer.buyerCustomerGroupName.slice(0,20)+'...' : $t('training.NoData') }}</strong>
                            </el-tooltip>
                            <strong v-else>{{ trf && trf.trfCustomer.buyerCustomerGroupCode ? trf.trfCustomer.buyerCustomerGroupName : $t('training.NoData') }}</strong>
                            <span class="is-error" v-if="trfDetail && trfDetail.trfCustomer.buyerCustomerGroupCode == ''">{{$t('trf.buyerSel')}}</span>
                        </div>
                        <div class="item">
                            <p class="tit">{{ $t('quickAccess.other') }}:</p>
                            <el-tooltip effect="dark" :content="trf ? trf.templateName : ''" placement="top" v-if="trf && trf.trfTemplateId && trf.templateName.length > 20">
                                <strong>{{ trf && trf.trfTemplateId ? trf.templateName.slice(0,20)+'...' : $t('training.NoData') }}</strong>
                            </el-tooltip>
                            <strong v-else>{{ trf && trf.trfTemplateId ? trf.templateName : $t('training.NoData') }}</strong>
                            <span class="is-error" v-if="trfDetail && trfDetail.trfTemplateId == ''">{{$t('trf.placeholder.template')}}</span>
                        </div>
                        <div class="item" style="padding-right: 0; border: 0; display: flex;">
                            <p class="tit">{{ $t('service.reportLanguage') }}:</p>
                            <strong>{{ trf && trf.servicRequire.reportLanguage ? trf.servicRequire.reportLanguageName : $t('training.NoData') }}</strong>
                            <span class="is-error" v-if="trfDetail && trfDetail.servicRequire.reportLanguage == ''">{{$t('select')}}{{$t('service.reportLanguage')}}</span>
                        </div>
                        <!-- <div class="item" style="padding-right: 0; border: 0; display: flex;">
                            <el-button @click="modify" v-if="trf && trf.trfStatus < 2 || trf == null" type="text" id="trf-modify-btn">{{ $t('operation.modify') }}</el-button>
                        </div> -->
                        <!-- <span>{{ $t('buyer.name') }}：{{ trf ? trf.trfCustomer.buyerCustomerGroupName : $t('training.NoData') }}</span>
                        <span>{{ $t('quickAccess.other') }}：{{ trf ? trf.templateName : $t('training.NoData') }}</span>
                        <span>{{ $t('service.reportLanguage') }}：{{ trf ? trf.servicRequire.reportLanguageName : $t('training.NoData') }}</span> -->
                        <!-- <span>{{ $t('user.updateTime') }}：{{ trf ? trf.updateTime : $t('training.NoData') }}</span> -->
                    </div>
                </div>
            </div>

            <div class="clearfix">
                <div class="tits pull-left" id="trf-tabs">
                    <el-button :class="{'active': tabRefresh.trfDetail}" type="text" v-if="permissionList.trfTab" @click="handleClick('trfDetail')">{{ $t('navbar.trfForm') }}</el-button>
                    <el-button :class="{'active': tabRefresh.quotation}" type="text" :disabled="trfStatus==null||trfStatus==1||trfStatus==2" @click="handleClick('quotation')">{{ $t('trfStatus.title') }}</el-button>
                    <el-button :class="{'active': tabRefresh.testResult}" type="text" :disabled="trfStatus!=4&&trfStatus!=5" @click="handleClick('testResult')">{{ $t('testResult.title') }}</el-button>
                </div>
            </div>
            <div class="top" v-if="tabRefresh.trfDetail && (trf && trf.trfStatus < 2 || trf == null)">
                <div :style="{ 'width': progress + '%' }">
                    <span class="txt">{{progress}}%</span>
                </div>
            </div>
        </div>

        <div class="content">
            <trf-detail
                v-if="tabRefresh.trfDetail"
                @trfId="getTrfId"
                @trf="getTrf"
                @trfDetail="getTrfDetail"
                @formProgress="getProgress"
                :trfNo.sync="trfNo"
                :trfStatus.sync="trfStatus"
            ></trf-detail>
            <trf-quotation-afl
                v-if="tabRefresh.quotation&&trfStatus!=null&&trfStatus!=1&&trfStatus!=2"
                approve.sync="false">
            </trf-quotation-afl>
            <test-result-afl
                v-if="tabRefresh.testResult&&(trfStatus==4 || trfStatus==5)"
                :trfNo="trfNo">
            </test-result-afl>
        </div>

        <!-- <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane name="trfDetail" v-if="permissionList.trfTab" :label="$t('navbar.trfForm')">
                <trf-detail3
                        v-if="tabRefresh.trfDetail"
                        @trfId="getTrfId"
                        :trfNo.sync="trfNo"
                        :trfStatus.sync="trfStatus"
                ></trf-detail3>
            </el-tab-pane>
            <el-tab-pane name="quotation" :disabled="trfStatus==null||trfStatus==1||trfStatus==2" :label="$t('trfStatus.title')" lazy="true">
                <trf-quotation-afl
                v-if="tabRefresh.quotation&&trfStatus!=null&&trfStatus!=1&&trfStatus!=2"
                        approve.sync="false">
                </trf-quotation-afl>
            </el-tab-pane>
            <el-tab-pane name="testResult" :disabled="trfStatus!=4&&trfStatus!=5" :label="$t('testResult.title')" lazy="true">
                <test-result-afl
                        v-if="tabRefresh.testResult&&(trfStatus==4 || trfStatus==5)"
                        :trfNo="trfNo">
                </test-result-afl>
            </el-tab-pane>
        </el-tabs> -->
    </basic-container>
</template>

<script>
    // import TrfQuotation from "./trfQuotation";
    // import TrfDetail from "./trfDetail_1";
    import {mapGetters} from "vuex";
    //import TestResult from "./testResult";
    export default {
        name: "TestRequestForm",
        components: {
            TestResultAfl: resolve => require(['@/views/trf/form/testResultAfl'], resolve),
            //TrfDetail: resolve => require(['@/views/trf/form/trfDetail'], resolve),
            TrfQuotationAfl: resolve => require(['@/views/trf/form/trfQuotationAfl'], resolve),
            TrfDetail: resolve => require(['@/views/trf/form/trfDetail_afl'], resolve),
            AFLTRFStatus: resolve => require(['@/components/trf/AFLTRFStatus'], resolve),
        },
        data() {
            return {
                isTrfTab: true,
                activeName: 'trfDetail',
                trfId: null,
                trfNo: null,
                trfStatus: null,
                tabRefresh: {
                    trfDetail: true,
                    testResult: false,
                    quotation: false
                },
                progress: 0,
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    trfTab: this.vaildData(this.permission['sgs:trf:trfTab'], false),
                    quotationTab: this.vaildData(this.permission['sgs:trf:quotationTab'], false),
                    testResultUnit: this.vaildData(this.permission['sgs:trf:testResult'], false),
                    testResultSaveBtn: this.vaildData(this.permission['sgs:trf:testResult:save:btn'], false),
                };
            }
        },
        methods: {
            getProgress(val) {
                console.log('获取填写进度：', val)
                setTimeout(() => {
                    this.progress = val
                }, 1000)
            },
            getTrf(trf) {
                this.trf = trf;
                console.log('GETTRF AFL:::', this.trf)
            },
            getTrfDetail(trf) {
                console.log('TRFDETAIL AFL::', trf)
                this.trfDetail = trf
                this.trf = trf
            },

            getTrfId(trfId) {
                this.trfId = trfId;
            },
            handleClick(tab, event) {
                let style = document.querySelector('.trf-base').style
                style.position = 'relative'
                style.width = '100%'
                style.top = '0'
                style.left = '0'

                if ('trfDetail' == tab) {
                    this.isTrfTab = true;
                } else {
                    this.isTrfTab = false;
                }

                Object.keys(this.tabRefresh).forEach(item => {
                    this.tabRefresh[item] = false;
                })
                this.tabRefresh[tab] = true;
            }
        }
    }
</script>


<style scoped lang="scss">
@import "@/styles/unit.scss";

.trf-base {
    background: #fff;
    padding: 22px 32px 11px;
    margin-top: 0;
    position: relative;
    width: 100%;
    left: 0;
    z-index: 9;
    top: 0;
    .trf-number {
        font-size: 24px;
        font-family: UniversLT;
        color: #000000;
        line-height: 24px;
    }
    .trf-no-data {
        color: #999;
        font-size: 14px;
    }
    .info {
        border-bottom: 1px solid #E6E6E6;
        padding-bottom: 24px;
        /* margin-bottom: 15px; */
        h4 {
            font-size: 16px;
            font-family: UniversLT;
            color: #656565;
            line-height: 20px;
        }
        > div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .left {
                .status {
                    float: left;
                }
                .dates {
                    float: left;
                    margin-top: 43px;
                }
                .date {
                    font-size: 14px;
                    font-weight: 400;
                    color: #656565;
                    vertical-align: text-top;
                    margin-top: -2px;
                    display: inline-block;
                }   
            }
            .right {
                display: flex;
                margin-top: 38px;
                .item {
                    padding: 0 24px;
                    font-size: 14px;
                    font-family: UniversLT;
                    font-weight: 400;
                    color: #000;
                    line-height: 22px;
                    border-right: 1px solid  #e6e6e6;
                    p {
                        margin-bottom: 2px;
                        display: inline-block;
                    }
                    strong {
                        font-weight: 400;
                        padding-left: 10px;
                    }
                    button {
                        padding: 0;
                    }
                }
            }
        }
    }
}
.tits {
    border: 1px solid #E6E6E6;
    margin-top: 11px;
    padding: 4px;
    width: fit-content;
    button {
        padding: 0 16px;
        height: 32px;
        line-height: 32px;
        &:not(.is-disabled) {
            color: #1b1b1b;
            cursor: pointer;
            &.active {
                color: $primary-color;
            }
        }
    }
}

.trf-active {
    margin-top: 0px;
    .content {
        /* margin-top: 70px; */
        padding-bottom: 200px;
    }
}

.status-tag {
  font-size: 12px;
  font-weight: 400;
  padding: 0 6px;
  height: 24px;
  line-height: 24px;
  display: inline-block;
  text-align: center;
  margin: 0 10px;

  &.toSubmitted {
    background: rgba(251, 189, 4, 0.08);
    color: rgb(251, 189, 4);
  }
  &.submitted {
    background: rgba(67, 133, 244, 0.08);
    color: rgb(67, 133, 244);
  }
  &.accepted {
    background: rgba(255, 102, 0, 0.08);
    color: rgb(255, 102, 0);
  }
  &.test {
    background: rgba(234, 67, 54, 0.08);
    color: rgb(234, 67, 54);
  }
  &.completed {
    background: rgba(52, 168, 83, 0.08);
    color: rgb(52, 168, 83);
  }
  &.cancelled {
    background: rgba(165, 165, 165, 0.08);
    color: rgb(165, 165, 165);
  }
}
.is-error {
    color: red;
    font-size: 12px;
    display: block;
}
.top {
    background: #E6E6E6;
    height: 6px;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -5px;
    > div {
        background: rgb(255, 102, 0);
        height: 100%;
        transition: all 1s;
        position: relative;
        .txt {
            background: #f60;
            height: 16px;
            position: absolute;
            right: -15px;
            top: 10px;
            font-size: 12px;
            line-height: 14px;
            padding: 2px 5px;
            color: #fff;
            &::after {
                content: ' ';
                position: absolute;
                top: -8px;
                width: 0;
                height: 0;
                border: 4px solid transparent;
                border-bottom-color: #f60;
                left: 41%;
            }
        }
    }
}
@media screen and (max-width: 1600px) {
    .trf-base {
        .info > div {
            .left {
                .dates {
                    margin-top: 25px;
                }
                .date {
                    .tit {
                        display: block;
                    }
                }
            }
            .right {
                margin-top: 22px;
                .item {
                    p {
                        display: block;
                        color: #656565;
                    }
                    strong {
                        padding-left: 0;
                    }
                }
            }
        }
    }
}
</style>
