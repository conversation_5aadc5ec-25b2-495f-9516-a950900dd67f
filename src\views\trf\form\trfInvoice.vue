<template>
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="sgs_portlet">
                <br>
                <el-row>
                    <h4 class="sgs-title">{{$t('invoice.invoiceDetail')}}</h4>
                    <div class="right">
                        <el-button type="primary"  @click="addInvoiceTotal" size="small" v-if="permissionList.invoiceAddBtn">
                            {{$t('operation.add')}}
                        </el-button>
                    </div>
                </el-row>
                <div class="body">
                    <table class="table table-striped table-hover sgs_table">
                        <thead>
                        <tr>
                            <th width="11%">{{$t('quotation.invoiceNo')}}</th>
                            <th width="11%">{{$t('invoice.toto.payer')}}</th>
                            <th width="11%">{{$t('invoice.toto.attention')}}</th>
                            <th width="10%">{{$t('quotation.totalTestItems')}}</th>
                            <th width="10%">{{$t('quotation.estimatedTax')}}</th>
                            <th width="10%">{{$t('quotation.totalAmount')}}</th>
                            <th width="10%">{{$t('invoice.status.title')}}</th>
                            <th width="13%">{{$t('invoice.invoiceDate')}}</th>
                            <th width="12%">{{$t('invoice.invoicePDF')}}</th>
                            <th width="15%">{{$t('operation.title')}}</th>
                        </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item,index) in invoice.list" :key="index">
                                <td>
                                    <el-tooltip :content="item['invoiceNo']" placement="top" effect="light">
                                        <el-input placeholder="" size="mini" v-model="item['invoiceNo']" maxlength="200" :disabled="!permissionList.invoiceAddBtn || !item.invoiceStatus==0"></el-input>
                                    </el-tooltip>
                                </td>
                                <td>
                                    <el-tooltip :content="item['payer']" placement="top" effect="light">
                                        <el-input placeholder="" size="mini" v-model="item['payer']"  maxlength="200" :disabled="!permissionList.invoiceAddBtn || !item.invoiceStatus==0"></el-input>
                                    </el-tooltip>
                                </td>
                                <td>
                                    <el-tooltip :content="item['attention']" placement="top" effect="light">
                                        <el-input placeholder="" size="mini" v-model="item['attention']" maxlength="200" :disabled="!permissionList.invoiceAddBtn || !item.invoiceStatus==0"></el-input>
                                    </el-tooltip>
                                </td>

                                <td>
                                    <el-tooltip :content="item['testItemsAmount']" placement="top" effect="light">
                                        <el-input placeholder="" v-model="item['testItemsAmount']" size="mini" :disabled="!permissionList.invoiceAddBtn || !item.invoiceStatus==0"  oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" >
                                        <!--   <template slot="prepend" v-if="invoice.paymentCurrency === 'CNY'">¥</template>
                                            <template slot="prepend" v-else>$</template>-->
                                        </el-input>
                                    </el-tooltip>
                                </td>
                                <td>
                                    <el-tooltip :content="item['estimatedTax']" placement="top" effect="light">
                                        <el-input placeholder="" v-model="item['estimatedTax']" size="mini" :disabled="!permissionList.invoiceAddBtn || !item.invoiceStatus==0" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" >
                                        <!--  <template slot="prepend" v-if="invoice.paymentCurrency === 'CNY'">¥</template>
                                            <template slot="prepend" v-else>$</template>-->
                                        </el-input>
                                    </el-tooltip>
                                </td>
                                <td>
                                    <el-tooltip :content="( (isNaN(parseFloat(item['testItemsAmount']))?0.00:parseFloat(item['testItemsAmount']))  + (isNaN(parseFloat(item['estimatedTax']))?0.00:parseFloat(item['estimatedTax'])) ).toFixed(2)" placement="top" effect="light">
                                        <el-input placeholder="" size="mini" :disabled="!permissionList.invoiceAddBtn || !item.invoiceStatus==0"  :value=" ( (isNaN(parseFloat(item['testItemsAmount']))?0.00:parseFloat(item['testItemsAmount']))  + (isNaN(parseFloat(item['estimatedTax']))?0.00:parseFloat(item['estimatedTax'])) ).toFixed(2)" disabled="disabled">
                                        <!-- <template slot="prepend" v-if="invoice.paymentCurrency === 'CNY'">¥</template>
                                            <template slot="prepend" v-else>$</template>-->
                                        </el-input>
                                    </el-tooltip>
                                </td>
                                <td>
                                    <span>{{item['invoiceStatus'] | statusFilter(this_)}}</span>
                                </td>
                                <td>
                                      <el-date-picker type="date" :placeholder="$t('operation.pleaseSelect')"
                                                      :disabled="!permissionList.invoiceAddBtn || item.invoiceStatus==1"
                                                      value-format="yyyy-MM-dd"
                                                      v-model="item['invoiceDate']"
                                                      style="width: 100%;">
                                      </el-date-picker>
                                </td>
                                <td>

                                    <el-upload
                                            ref="upload"
                                            class="myupload"
                                            action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                            :on-success="(param) => invoiceFileChange(item,param)"
                                            :on-exceed="(param) => invoiceFileExceed(item,param)"
                                            :disabled="!permissionList.invoiceUploadBtn || !item.invoiceStatus==0"
                                            :file-list="item['attachmentList']"
                                            :on-preview="downFile"
                                            :show-file-list="true"
                                            :on-change="fileProgress"
                                            :on-remove = "(param) => invoiceFileRemove(item,param)"
                                            :before-upload="beforeAvatarUpload"
                                            :limit="1">
                                        <i class="el-icon-upload2" v-if="permissionList.invoiceUploadBtn"  ></i>
                                    </el-upload>
                                    <!--展示已上传的文件，点击可以下载-->
                                </td>
                                <td>
                                    <el-button  type="text" size="small"  @click="delInvoiceTotal(item)"   v-if="permissionList.invoiceDeleteBtn && item.invoiceStatus==0" icon="el-icon-remove-outline">
                                        {{$t('operation.remove')}}
                                    </el-button>
                                    <el-button v-if="permissionList.invoiceConfirmBtn &&  item.invoiceStatus==0 && item.id"
                                            @click="confirmInvoice(item.id)"
                                            type="text" size="small" icon="el-icon-check">
                                        {{$t('invoiceTodo.paid')}}
                                    </el-button>
                                    <el-button v-if="permissionList.invoiceRejectBtn && item.invoiceStatus==0 && item.id"
                                            @click="rejectInvoice(item.id)"
                                            type="text" size="small" icon="el-icon-close">
                                        {{$t('invoiceTodo.status.reject')}}
                                    </el-button>
                                    <el-button v-if="permissionList.invoiceCancelBtn && item.invoiceStatus==2 && item.id"
                                            @click="cancelInvoice(item.id)"
                                            type="text" size="small" icon="el-icon-close">
                                        {{$t('invoiceTodo.status.cancel')}}
                                    </el-button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <el-row class="sgs-footer" v-if="permissionList.invoiceSaveBtn">
            <el-button type="primary" v-if="permissionList.invoiceSaveBtn" :loading="savaBtnLoading" @click="saveInvoiceTotal" >{{$t('operation.save')}}</el-button>
        </el-row>
    </div>
</template>


<script>
    import {mapGetters} from 'vuex'
    import {
        saveInvoice,getInvoice,removeInvoice,saveUploadFile,confirmInvoice,rejectInvoice,cancelInvoice
    } from "@/api/trf/trf";
    import {queryFile,deleteFile} from "@/api/common/file";
    import {validatenull} from "../../../util/validate";
    import moment from "moment/moment";
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
    import {deepClone} from "@/util/util";
    export default {
        name: "trfQuotation",
        data(){
            return {
                this_:this,
                savaBtnLoading:false,
                saveBtnDisabled:false,
                invoiceSelectVisible:false,
                invoiceSelectType:'',
                invoice:{
                    index:1,
                    list:[
                        {
                            tempId:this.RandomNumBoth(1,1000),
                            testItemsAmount:null,
                            estimatedTax:null,
                            invoiceStatus:0,
                            paymentCurrency:'CNY'
                        }
                    ],
                    paymentCurrency:'CNY'
                },
                uploadUrl:'/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1',
                fileList:[],
                fileType: 'application/msword,application/pdf,aplication/zip,application/vnd.ms-excel,text/plain',
                attachmentParam:{
                    object:'invoiceTodo',
                    objectId:""
                },
            }
        },
        filters: {
            statusFilter: function (status,vue) {
                let value= vue.$t('invoiceTodo.status.toBeConfirm');
                  if (status == 0 || status == '0') {
                    value =  vue.$t('invoiceTodo.status.toBeConfirm');
                } else if (status == 1 || status == '1') {
                       value =  vue.$t('invoiceTodo.status.confirmed');
                } else if (status == 2 || status == '2') {
                       value = vue.$t('invoiceTodo.status.reject');
                }else if (status == 3 || status == '3'){
                       value = vue.$t('invoiceTodo.status.cancel');
                }
                return value
            }
        },
        created() {
            this.initInvoiceTotal(this.trfId);
        },
        methods:{
            renderFileIcon() {
              //找出所有文件图标的class
              this.$nextTick(() => {
                  debugger;
                let fileElementList = document.getElementsByClassName('el-upload-list__item-name');
                if (fileElementList && fileElementList.length > 0) {
                  for (let ele of fileElementList) {
                    let fileName = ele.innerText;
                    //获取文件名后缀
                    let fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
                    let iconElement = ele.getElementsByTagName('i')[0];
                      iconElement.className = 'imgicon-default' //默认图标
                  }
                }
              })
            },
            confirmInvoice(inoviceId){
                if(!validatenull(inoviceId)){
                     this.$confirm('Do you want to confirm this invoice?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        confirmInvoice(inoviceId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                        this.initInvoiceTotal(this.trfId);
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    });
                }
            },
            rejectInvoice(inoviceId){
                if(!validatenull(inoviceId)){
                     this.$confirm('Whether to reject the invoice?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        rejectInvoice(inoviceId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                        this.initInvoiceTotal(this.trfId);
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    });
                }
            },
            cancelInvoice(invoiceId){
                if(!validatenull(invoiceId)){
                     this.$confirm('Whether to cancel the invoice?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        cancelInvoice(invoiceId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                        this.initInvoiceTotal(this.trfId);
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    });
                }
            },

            beforeAvatarUpload(file) {
                const isPDF = file.type === 'application/pdf';
                const isLt10M = file.size / 1024 / 1024 <= 20;

                if (!isPDF) {
                    this.$message.error(this.$t('uploadType_PDF'));
                }
                if (!isLt10M) {
                    this.$message.error(this.$t('uploadFileSizeError')+'20MB!');
                }
                return isPDF && isLt10M;
            },
            invoiceFileExceed(invoiceItem, file, fileList){
                //超出最大上传数量，给出提示
                this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('uploadExceed_1'),
                    type: 'warning'
                });
            },
            invoiceFileChange(invoiceItem, file, fileList){
                var that=this;
                debugger;
                /*if (this.fileType.indexOf(file.data[0].suffixes) === -1) {
                   // this.$warn({ title: 'warning',  message: 'File Type is not Support !',})
                    alert("文件格式错误");
                    this.$refs.upload.clearFiles()
                    return
                }*/
                this.savaBtnLoading=false;
                console.log(this.savaBtnLoading)
                if (file.data[0].size / 1024 / 1024 > 20) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('uploadFileSizeError')+'20MB!',
                        type: 'warning'
                    });
                    this.$refs.upload.clearFiles()
                    return
                }
                const attachment = {
                    'attachmentId': file.data[0].cloudID,
                    'fileUrl': file.data[0].path,
                    'fileName': file.data[0].attachmentName
                };
                let uploadFiles = []
                uploadFiles.push(attachment)
                this.$set(invoiceItem, 'uploadFiles', uploadFiles);

            },
            //文件上传时钩子函数
            async fileProgress(event, file, fileList){
                debugger;
                if(event.status=='ready'){
                    this.savaBtnLoading=true;
                }else{
                    this.savaBtnLoading=false;
                }
                console.log(event);
                console.log(file);
                console.log(fileList);
                  //this.renderFileIcon();
            },
            async invoiceFileRemove(invoiceItem,file, fileList){
                console.log(file);
                //删除该账单已绑定的附件
                debugger;
                this.$set(this.attachmentParam, 'objectId', invoiceItem.id)
                //如果是已上传的文件
                if (file.id!=null  && file.id!=undefined) {
                    // 删除已上传的文件
                   // let delres = await deleteFile({key: file.id})
                    var params={};
                    let delres = await deleteFile(Object.assign(params, this.attachmentParam))
                    debugger;
                    //if (delres.data.result === 1) {
                        //this.$success({title: 'success', message: 'The file has been removed'})
                       this.fileList.splice(0, this.fileList.length)
                   // }
                }/* else {
                    // 清空未上传的文件
                    this.$refs.upload.clearFiles()
                }*/
            },
            uploadSuccess(res, file) {
                const attachment = {
                    'attachmentId': res.data[0].cloudID,
                    'fileUrl': res.data[0].path,
                    'fileName': file.name
                };
                this.trf.trfAttachments.push(attachment);
                //this.renderFileIcon();
            },
            async downFile(file, fileList){
                let fileListRes = null
                let queryParam={};
                if(file.attachmentId==undefined || file.attachmentId=='' ||file.attachmentId==null){
                    return false;
                }
                queryParam.cloudID=file.attachmentId;
                try {
                    fileListRes = await queryFile(queryParam)
                } catch (e) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: 'Failed to get file',
                        type: 'warning'
                    });
                }
                if (fileListRes == null || this.$lodash.isEmpty(fileListRes.data.data)) {
                    return  false
                }
                window.open(fileListRes.data.data.path)
            },
            async initInvoiceTotal(gid){
                if (this.$route.query.isReportFlag) {
                    let anchorElement = document.getElementById("invoice")
                    if (anchorElement) {
                        anchorElement.scrollIntoView()
                    }
                }

                let queryRes = await getInvoice(gid);
                if (queryRes.data.code === 200) {
                    if (queryRes.data.data !== null && queryRes.data.data.length > 0) {
                        this.invoice.list = queryRes.data.data;
                        this.invoice.paymentCurrency = this.invoice.list[0].paymentCurrency;
                    }
                }
            },
            async saveInvoiceTotal(){
                //开启loading效果
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                let param = this.invoice.list.map(item => {
                    let date = this.$lodash.get(item, 'invoiceDate', "");
                    if(!validatenull(date)){
                      date = moment(date).format('YYYY-MM-DD 00:00:00');
                    }else{
                      date = null;
                    }
                    return {
                        id:item.id,
                        trfId:this.trfId,
                        payer:this.$lodash.result(item,'payer',''),
                        attention:this.$lodash.result(item,'attention',''),
                        testItemsAmount:this.$lodash.result(item,'testItemsAmount',0),
                        estimatedTax:this.$lodash.result(item,'estimatedTax',0),
                        paymentCurrency:this.invoice.paymentCurrency,
                        invoiceNo:this.$lodash.get(item, 'invoiceNo', ""),
                        invoiceDate:date,
                    }
                })

                let saveRes = await saveInvoice(param);
                loading.close();
                if (saveRes.data.code === 200) {
                    let retunList = saveRes.data.data;
                    for (let i = 0; i < retunList.length; i++) {
                        let uploadFiles = this.invoice.list[i].uploadFiles
                        if (uploadFiles && uploadFiles.length === 1) {
                            let uploadFileObj = uploadFiles[0];
                            if (uploadFileObj) {
                                var attachment={};
                                attachment.object='invoiceTodo';
                                attachment.objectId=retunList[i].id;
                                attachment.attachmentId= uploadFileObj.attachmentId;
                                attachment.fileName=uploadFileObj.fileName;
                                attachment.fileUrl=uploadFileObj.fileUrl;
                               /* let formData = new FormData()
                                formData.append('object','quotation')
                                formData.append('objectID', retunList[i].id)
                                formData.append('attachmentId', uploadFileObj.attachmentId)
                                formData.append('fileName', uploadFileObj.fileName)
                                formData.append('fileUrl', uploadFileObj.fileUrl)*/
                                try {
                                    let uploadFileRes = await saveUploadFile(attachment)
                                    if (uploadFileRes.data.code === 200) {
                                        this.$message({
                                            type: 'success',
                                            message: this.$t('api.success')
                                        });
                                    }
                                }catch (e) {
                                    this.$message.error(this.$t('api.error'));
                                }
                            }
                        }
                    }
                    this.initInvoiceTotal(this.trfId)
                }

            },
            async delInvoiceTotal(item){
                if (this.invoice.list.length === 1) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: "Please keep a row of data",
                        type: 'warning'
                    });
                    return
                }
                if(item.id){
                    let delRes = await removeInvoice(item.id);
                }
                this.invoice.list.splice(this.invoice.list.indexOf(item),1);
                /*let findItem = null
                if (item.tempId) {
                    findItem = this.$lodash.find(this.invoice.list, {tempId: item.tempId});
                } else {
                    findItem = this.$lodash.find(this.invoice.list, {id: item.id});
                    let delRes = await deleteInvoice(item.id)
                    if (delRes.data.status === 200) {
                        if (!this.$lodash.isEmpty(this.$lodash.get(item, 'uploadFiles[0]', null))) {
                            let file = item.uploadFiles[0];
                            //如果是已上传的文件
                            if (this.$lodash.has(file, 'id') && !this.$lodash.isEmpty(file.id)) {
                                // 删除已上传的文件
                                let delres = await deleteFile({key: file.id})
                            }
                        }
                        this.$success({message: "Delete Invoice Success!"})
                    }
                }*/

            },
            addInvoiceTotal(){
                let uuid = this.RandomNumBoth(1,1000);
                this.invoice.list.push(
                    {
                        tempId:uuid,
                        testItemsAmount:'',
                        estimatedTax:'',
                        invoiceStatus:0,
                        paymentCurrency:'CNY'
                    }
                )
            },
            RandomNumBoth:function(Min,Max){
              var Range = Max - Min;
              var Rand = Math.random();
              var num = Min + Math.round(Rand * Range); //四舍五入
              return num;
             },
            currentTz_YMD(val) {
              if (!val) return ''
              let value = tzFormChina(val, 'YYYY-MM-DD HH:mm:ss');
              return moment(value).format('YYYY-MM-DD');
            },
            tzToChina(val){
              if (!val) return ''
              let value = tzToChina(val);
              return value;
            },
        },
        computed:{
            ...mapGetters({
                myErrors: 'myErrors',
                user:'user',
                companyType:'companyType',
                permission:'permission',
            }),
            permissionList() {
                return {
                    invoiceSaveBtn:this.vaildData(this.permission['sgs:trf:invoice:save'],false),
                    invoiceAddBtn: this.vaildData(this.permission['sgs:trf:inovice:add'],false),
                    invoiceUploadBtn: this.vaildData(this.permission['sgs:trf:invoice:upload'],false),
                    invoiceDeleteBtn: this.vaildData(this.permission['sgs:trf:invoice:delete'],false),
                    invoiceConfirmBtn: this.vaildData(this.permission['sgs:trf:invoice:confirm'],false),
                    invoiceRejectBtn: this.vaildData(this.permission['sgs:trf:invoice:reject'],false),
                    invoiceCancelBtn: this.vaildData(this.permission['sgs:trf:invoice:cancel'],false),
                };
            }

        },
        props:{
            trfId: String,
            trfNo: String,
            trfFlag:String,
        }
    }
</script>
<style scoped>
    .customerUpload /deep/ > div{
        display: inline;
    }

    .customerUpload /deep/ > ul{
        display: inline;
    }
    .customerUpload /deep/ > ul > li{
        display: inline;
    }
    .customerUpload /deep/ > ul > li > a{
        display: inline;
    }
    .el-upload-list__item-name {
      display: inline;
    }

    .el-upload-list__item-status-label {
      line-height: normal;
    }
    .myupload >>> .imgicon-default {
	  display: inline-block;
	  width: 20px;
	  margin-bottom: -3px;
	  height: 20px;
	  background-size: 100% 100%;
	  margin-right: 10px;
	  background-image: url("../../../../public/img/pdf.png") !important;
	}
</style>

