<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_new_material_info" id="sgs_smart_new_material_info">
            <div>
                <el-row >
                    <el-col :span="4" v-if="!viewPage">
                        <ul class="nav-list" ref="nav" id="trf-left-nav">
                            <template v-for="(item, index) in navList">
                                <li :class="{'is-sub': item.isSub,'active': item.active}" :key="item.name" class="nav-item" @click="toTarget(index)">
                                    <h5>{{ $t(item.name) }}</h5>
                                </li>
                            </template>
                        </ul>
                    </el-col>
                    <el-col :offset="viewPage?1:0" :span="viewPage?22:20" v-if="showCompoment">
                        <el-form ref="materialDetailForm" :model="materialObj"
                                 label-position="top" label-width="300px"
                                 :disabled="!viewPage && !(createNew || btnRole(materialObj,'Edit'))">
                            <el-collapse v-model="activeNames">
                                <el-form>
                                    <report-certificate
                                        v-if="viewPage == 'preview'||materialObj.id"
                                        :object-id="materialObj.id"
                                        :edit-rule="createNew || btnRole(materialObj,'Edit')"
                                        :create-new="createNew"
                                        test-from="material"
                                        file-test-from="customer"
                                    ></report-certificate>
                                </el-form>
                            </el-collapse>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
            <el-row class="sgs-footer page-no-print" style="position: fixed" v-if="!viewPage && !pageLoading">
                <el-col style="text-align: center">
                    <el-button type="primary" @click="saveMaterial" :disabeld="disabledSaveBtn" v-if="createNew || btnRole(materialObj,'Edit')">Save</el-button>
                    <el-button type="primary" @click="actionMaterial('Submit')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'Submit')">Submit</el-button>
                    <el-button type="primary" @click="createTRF" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'ToTrf')">Create TRF</el-button>
                    <el-button type="primary" @click="actionMaterial('Return')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'Return')">Return</el-button>
                    <el-button type="primary" @click="actionMaterial('NotInUse')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'NotInUse')">Not In Use</el-button>
                    <el-button type="primary" @click="actionMaterial('InUse')" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'InUse')">In Use</el-button>
                    <el-button type="info" @click="handlerCancelMaterial" :disabeld="disabledSaveBtn" v-if="btnRole(materialObj,'Cancel')">Cancel</el-button>
                </el-col>
            </el-row>
        </div>
    </basic-container>

</template>

<script>
    import {objectIsNull} from "../../../util/validate";
    import api from '../../../api/newSamples'
    import {getServiceType} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import ReportCertificate from "../commonComponent/reportCertificate";
    import _ from 'lodash'
    export default {
        name: "materialInfo",
        provide(){
            return {
                getMainObjectId:()=>{return this.materialObj.id || this.pageId}
            }
        },
        data() {
            return {
                pageLoading:false,
                showCompoment:false,
                action:'',
                disabledSaveBtn:false,
                serviceTypeList:[
                    {
                        "serviceTypeCode": "1",
                        "serviceTypeName": "Regular"
                    },
                    {
                        "serviceTypeCode": "2",
                        "serviceTypeName": "Express"
                    },
                    {
                        "serviceTypeCode": "3",
                        "serviceTypeName": "Double express"
                    },
                    {
                        "serviceTypeCode": "4",
                        "serviceTypeName": "Emergency"
                    }
                ],
                pageId:'',
                fieldList:[],
                generalFieldConfig:[],
                templateParam:{
                    templateId:''
                },
                createNew:false,
                materialObj:{
                    id:null,
                    "templateId": "",
                    "materialName": "",
                    "materialDescription": "",
                    "materialCategory": "",
                    "materialCode": "",
                    "supplierName": "",
                    "supplierCode": "",
                    "supplierAddress": "",
                    "supplierContact": "",
                    "supplierTel": "",
                    "supplierEmail": "",
                    "manufacturerName": "",
                    "manufacturerCode": "",
                    "manufacturerAddress": "",
                    "manufacturerContact": "",
                    "manufacturerTel": "",
                    "manufacturerEmail": "",
                    "serviceType": "",
                    "labName": "",
                    "labContact": "",
                    "fieldList": []
                },
                navList:[],
                activeNames:[],
                element:{
                    nav:[],
                    content:[]
                }
            }
        },
        methods: {
            getFieldSourceValues(index){
              let {sourceValueLanguage} = this.materialObj.fieldList[index];
              if(!sourceValueLanguage || sourceValueLanguage.length==0){
                  return []
              }
              return this.handlerSourceValue(sourceValueLanguage[0].sourceValue);
            },
            handlerSourceValue(sourceValue){
                let listOfValuesDataList = [];
                if(!sourceValue || sourceValue.length==0){
                    return listOfValuesDataList;
                }
                try{
                    listOfValuesDataList = sourceValue.map(v=>JSON.parse(v));
                }catch(e){}
                return listOfValuesDataList;
            },
            lowerField(fieldCode){
              return _.lowerFirst(fieldCode)
            },
            loadPage(){
                let query = this.$route.query;
                console.log("进入query 解析",query)
                let {action,templateId,id} = query;
                this.action = action;
                //处理页面select
                this.initSelectList();

                if(this.viewPage && this.viewPage == 'preview'){
                    this.initPreview();
                }else if(action=='copy'){
                    this.handlerCopy(id);
                }else{
                    this.materialObj.id = id;
                    this.pageId = id;
                    this.templateParam.templateId = templateId;
                    this.queryMaterialDetail(!id);
                }

            },
            initSelectList(){
            },
            handlerCancelMaterial(){
                this.actionMaterial('Cancel',()=>{
                    this.backToList(false);
                });
            },
            actionMaterial(action,callback){
                this.$confirm(action+' the material?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    let {id} = this.materialObj;
                    api.actionSamples({ids:[id],action}).then(res=>{
                        if(res.status==200 && res.data){
                            this.$notify.success("Success");
                            if(callback){
                                callback();
                            }else{
                                window.location.reload();
                            }
                        }else{
                            this.$notify.error(res.message || "Operation Fail");
                        }
                    }).catch(err=>{
                    })
                });
            },
            createTRF(){
                let customerGroupCode = this.materialObj.buyerCustomerGroupCode;
                let bossNo = this.materialObj.buyerCustomerBossNo;
                let customer = {
                    bossNo,
                    customerGroupCode
                }
                customer = encodeURIComponent(JSON.stringify(customer));
                let ids = [this.materialObj.id];
                let bu = this.materialObj.productLineCode;
                let actionType="individualSample";
                let flag =1;
                this.$router.push({
                    path:'/trf/trfForm',
                    query:{flag,actionType,ids,customer,bu}
                })
            },
            saveMaterial(){
                this.$refs['materialDetailForm'].validate((valid) => {
                    if(!valid){
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.trfValidateError'),
                            type: 'warning'
                        });
                        return
                    }
                    let param = Object.assign({},this.materialObj);
                    param['dataType'] = 'material';
                    this.pageLoading = true;
                    api.createMaterial(param).then(res=>{
                        this.pageLoading = false;
                        if(res.status==200 && res.data && res.data.data){
                            let {id} = res.data.data;
                             window.location.href = '/#/customer/newMaterial/detail?id='+id;
                             window.location.reload()
                        }else{
                            this.$notify.error(res.message || 'Save Fail');
                        }
                    }).catch(err=>{
                        this.pageLoading = false;
                        console.log("save material err",err)
                    })
                })
            },
            renderNavList(){
                let navList = [];
                let activeNames = [];
                let reportNav = [
                    {
                        sectionCode:'Report',
                        sectionLabel:'Report'
                    },
                    {
                        sectionCode:'TestLine',
                        sectionLabel:'Test Line'
                    },
                    {
                        sectionCode:'Certificate',
                        sectionLabel:'Certificate'  
                    },
                ]
                if(this.createNew){
                    reportNav.splice(1,1)
                }
                reportNav.forEach((gen,index)=>{
                    let {sectionCode,sectionLabel} = gen;
                    let nav = {name:sectionLabel,id:"#"+sectionCode,seq:index+1,active:false,isSub:false,show:true}
                    navList.push(nav);
                    activeNames.push(sectionCode);
                })
                this.navList = navList;
                this.activeNames = activeNames;
                if(this.navList.length>0){
                    this.$nextTick(()=>{
                        const nav = document.getElementsByClassName('nav-item')
                        const cont = document.getElementsByClassName('content-item')
                        this.element.nav = nav
                        this.element.content = cont
                        //导航初始化样式（默认第一个）
                        //nav[0].classList.add('active')
                        this.navList[0].active = true;
                        window.addEventListener("scroll", this.toScroll)
                    })
                }
            },
            renderPageComponent(){
                let list = [];
                this.fieldList.forEach(fl=>{
                    let {tips,fieldCode,id,dispalyName,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = fl;
                    let materialExtendFieldValue = ['Checkbox','Select2','DateRange'].includes(fieldType)?[]: '';
                    list.push({
                        fieldCode,
                        dffFormFieldId: id,
                        dffFormFieldNameEn:dispalyName,
                        materialExtendFieldValue,
                        mandatoryFlag,
                        fieldType,
                        sequence,
                        sourceValueLanguage,
                        tips
                    })
                    if(this.pageId){
                        let dbObj = this.materialObj.fieldList.find(l=>l.dffFormFieldId==id);
                        if(!dbObj){
                            this.materialObj.fieldList.push({
                                fieldCode,
                                dffFormFieldId: id,
                                dffFormFieldNameEn:dispalyName,
                                materialExtendFieldValue,
                                fieldType,
                                sequence,
                                sourceValueLanguage,
                                tips
                            });
                        }
                    }
                });
                list.sort((a,b)=> {
                    return a.sequence - b.sequence
                })
                if(this.pageId){
                    this.materialObj.fieldList.forEach(f=>{
                        let {dffFormFieldId} = f;
                        let templateObj = list.find(l=>l.dffFormFieldId==dffFormFieldId);
                        if(templateObj){
                            let {tips,dffFormFieldNameEn,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = templateObj;
                            this.$set(f,'dffFormFieldNameEn',dffFormFieldNameEn)
                            this.$set(f,'sourceValueLanguage',sourceValueLanguage)
                            this.$set(f,'mandatoryFlag',mandatoryFlag)
                            this.$set(f,'fieldType',fieldType)
                            this.$set(f,'sequence',sequence)
                            this.$set(f,'tips',tips)
                        }
                    })
                    this.materialObj.fieldList.sort((a,b)=> {
                        return a.sequence - b.sequence;
                    })
                }else{
                    //设置默认值
                    this.generalFieldConfig.forEach(gf=>{
                        let {fieldList} = gf;
                        (fieldList || []).forEach(f=>{
                            let {fieldCode,defaultValue} = f;
                            this.$set( this.materialObj,this.lowerField(fieldCode),defaultValue);
                        })
                    })

                    this.materialObj.fieldList = list;
                }
                this.showCompoment = true;
            },
            initPreview(){
                let {fieldList,generalFieldConfig} = this.templateData || {};
                if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                    this.$notify.warning("No template data")
                    return;
                }
                this.fieldList = fieldList;
                this.generalFieldConfig = generalFieldConfig;
                this.renderPageComponent();
                this.renderNavList();
            },
            // 公共查询模板 useNew 目前针对copy场景
            queryTemplate(templateId,useNew=false){
                this.pageLoading = true;
                let formPurpose = 'Material';
                let apiReq = useNew?api.getSampleNewTemplateList({templateId,formPurpose}) : api.geSampleTemplateList({templateId,formPurpose});
                apiReq.then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        if(res.data.data.length==0){
                            this.pageLoading = false;
                            this.disabledSaveBtn = true;
                            this.$notify.error("Load Template Fail!");
                            return;
                        }
                        let {fieldList,generalFieldConfig} = res.data.data[0];
                        if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                            this.$notify.warning("No template data")
                            return;
                        }
                        this.$emit("getMaterialTemplateInfo",res.data.data[0]);
                        this.fieldList = fieldList;
                        this.generalFieldConfig = generalFieldConfig;
                        this.materialObj.templateId= useNew ? res.data.data[0]['templateId'] : templateId;
                        this.renderPageComponent();
                        this.renderNavList();
                    }else{
                        this.disabledSaveBtn = true;
                        this.$notify.error("Load Template Fail!");
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    this.pageLoading = false;
                })

            },
            handlerCopyData(oldData,newData){
                let {id} = newData;
                oldData['id'] = id;
                oldData.permissions = [{action:'Edit'}];
                return oldData;
            },
            handlerCopy(id){
                this.pageLoading = true;
                api.copySample({id}).then(async res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        let dbData = res.data.data;
                        this.pageId = dbData.id;
                        this.createNew = true;
                        console.log("query copy data",res.data.data)
                        this.materialObj = dbData;
                        this.$emit("getMaterialInfo",this.materialObj);
                        this.queryTemplate(this.materialObj.templateId,true)
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no material detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    console.log("queryMaterialDetail err",err)
                    this.$notify.warning("Query no material detail data");
                })
            },
            queryMaterialDetail(createNew=false){
                this.pageLoading = true;
                let {id} = this.materialObj;
                api.querySampleDetail({id,dataType:'material'}).then(async res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        if(createNew){
                            this.createNew = true;
                            this.materialObj = res.data.data;
                            this.queryTemplate(this.templateParam.templateId)
                            return;
                        }
                        let dbData = res.data.data;
                        if(this.action == 'copy'){
                            delete res.data.data.id;
                            let copyObj = await api.querySampleDetail({id:'',dataType:'material'});
                            if(copyObj.status==200 && copyObj.data && copyObj.data.data){
                                dbData = this.handlerCopyData(res.data.data,copyObj.data.data)
                            }
                        }
                        this.materialObj = dbData;
                        this.$emit("getMaterialInfo",this.materialObj);
                        this.queryTemplate(this.materialObj.templateId,this.action == 'copy')
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no material detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    console.log("queryMaterialDetail err",err)
                    this.$notify.warning("Query no material detail data");
                })
            },
            backToList(showTips = true){
                if(!showTips  || (!this.createNew && !this.btnRole(this.materialObj,'Edit'))){
                    this.$router.push({
                        path:'/customer/newMaterial/list',
                        query:{}
                    })
                }else{
                    this.$confirm("Please make sure you have saved the information before you close the window.",'Tips',{
                        confirmButtonText: 'Confirm',
                        cancelButtonText: 'Cancel',
                        type: 'warning'
                    }).then(res=>{
                        this.$router.push({
                            path:'/customer/newMaterial/list',
                            query:{}
                        })
                    });
                }
            },
            btnRole({permissions},code){
                if(!this.materialObj.id && this.viewPage!='preview' && code=='Edit'){
                    return true;
                }
                return (permissions || []).map(p=>p.action).includes(code);
            },
            toTarget(index){
                const { content, nav } = this.element;
                this.navList.forEach((na,ind)=>{
                    na.active = index==ind;
                })
                const scrollTop = document.documentElement.scrollTop|| document.body.scrollTop;
                switch (index) {
                    case 0:
                        document.documentElement.scrollTop = 0;
                        return;
                }
                document.documentElement.scrollTop =  ((document.documentElement.scrollTop===0?-100:document.documentElement.scrollTop)+ (content[index].getBoundingClientRect().top))-100;
            },
            toScroll() {
                let navEle = document.querySelector('.nav-list')
                if(objectIsNull(navEle)){
                    return;
                }
                navEle.style.width = navEle.clientWidth + 'px'
                var w;
                //获取滚动距离
                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                const { content, nav } = this.element
                this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                window.addEventListener("resize", () => {
                    this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                });
                // 侧边栏和评论栏固定
                if(scrollTop != undefined && scrollTop > 170) {
                    navEle.style.position = 'fixed'
                    navEle.style.top = '80px'
                } else {
                    navEle.style.position = 'initial'
                }
                // 侧边栏菜单添加当前高亮
                let len = content.length;
                let scrollIndex = 0;
                for (let i = 0;i < len; i++) {
                    //获取每块内容距离顶部距离
                    const offset = content[i].offsetTop;
                    //当划过第一块内容 改变左侧导航样式
                    if (scrollTop >= offset) {
                        scrollIndex = i;
                    }
                }
                for (let n = 0; n < len; n++) {
                    if(!nav[n]) {
                        return;
                    }
                    n == scrollIndex ? nav[n].classList.add('active') : nav[n].classList.remove('active')
                }

                if(scrollTop == 0) {
                    nav[0].classList.add('active')
                    nav[1].classList.remove('active')
                }
            },
        },
        mounted() {},
        created() {
            this.loadPage();
        },
        watch: {},
        computed: {
            ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
        },
        props: {
            viewPage:'',
            templateData:{}
        },
        updated() {
        },
        components: {ReportCertificate}
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";
    .sgs_smart_new_material_info {
        .el-collapse{
            border-bottom: none !important;
            border-top: none !important;
        }
        .el-collapse-item__wrap,.el-collapse-item__header{
            border-bottom: none !important;
        }
        p.sample_tips{
            color:#c8c8c8;
        }
        .nav-list {
            list-style: none;
            margin-top: 24px;
            padding-left: 0;
            li {
                cursor: pointer;
                border-left: 3px solid #D9D9D9;
                padding-left: 24px;
                font-size: 16px;
                font-weight: 400;
                color: #000000;
                &.is-sub {
                    padding-left: 40px;
                    font-size: 16px;
                    font-weight: 400;
                    color: #656565;
                    h5 {
                        font-weight: normal;
                    }
                }
                &.active {
                    color: #f60;
                    border-color: #f60;
                }
                h5 {
                    padding: 10px 0;
                    margin: 0;
                }
            }
        }
        /* 重置input边距 */
        .el-input--suffix .el-input__inner {
            padding: 0;
        }
        .el-form-item__label {
            color: $text-color-value;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .el-collapse-item__content {
            padding-bottom: 0;
        }
        .el-row {
            display: flex;
            flex-wrap: wrap;
        }
    }
</style>