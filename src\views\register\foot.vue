<template>
    <div id="footer" class="footer">
      <div class="wrap-lg text-center" style="color: #fff" v-if="isShowFiling">
        <span>© {{ currentYear }} SGS SA</span>
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="http://beian.miit.gov.cn"
          >京ICP备16004943号-7</a
        >
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802032115"
        >
          <img src="/img/gaicon.png" />
          <span>京公网安备 11010802032115号</span>
        </a>
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="https://www.sgsonline.com.cn/static/upload/2019/09/10/1tif.png"
          >增值电信业务经营许可证合字B2-20190019</a
        >
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs"
          >隐私政策</a
        >
      </div>
    </div>
</template>
<script setup>
import { ref,onBeforeMount } from 'vue';
import registerApi from '../../api/register';
import { LanguageEnums } from '../../enums/LanguageEnums';


const isShowFiling = ref(false);
//根据ip控制isShowFiling
const getIp = () => {
  registerApi.getLanguageByRemoteIp().then((res) => {
    console.log("remote ip", res);
    if(res.status == 200) {
      const language = res.data;
      isShowFiling.value = language == LanguageEnums.CN.name;
    }
  });
};
onBeforeMount(()=>{
  getIp();
});
const currentYear = ref(new Date().getFullYear());
</script>

<style lang="scss" scoped>
.footer{
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center; 
    & div,a{
        color: #fff;
    }
} 
</style>

