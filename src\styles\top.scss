.sgs-top {
    padding: 0 20px;
    position: relative;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    color: rgba(0, 0, 0, .65);
    font-size: 28px;
    height: 80px;
    box-sizing: border-box;
    white-space: nowrap;
    .el-menu-item{
        i,span{
            font-size: 13px;
        }
    }
}

.sgs-breadcrumb {
    height: 100%;
    i{
        font-size: 30px !important;
    }
    &--active {
        transform:rotate(90deg);
    }
}

.top-menu {
    line-height: 80px;
    box-sizing: border-box;
    .el-menu-item {
        padding: 0 10px;
        border:none !important;
        span{
            margin-left: 5px;
        }
    }
}

.top-search {
    line-height: 80px;
    position: absolute !important;
    left: 20px;
    top:0;
    width: 400px !important;
    .el-input__inner {
        font-size: 13px;
        border: none;
        background-color: transparent;
    }
}

.top-bar__img {
    margin: 0 8px 0 0;
    // padding: 2px;
    // width: 30px;
    // height: 30px;
    // border-radius: 100%;
    // box-sizing: border-box;
    // border: 1px solid #eee;
    // vertical-align: middle;
    // background-color: #dedede;
}

.top-bar__left,
.top-bar__right {
    height: 80px;
    position: absolute;
    top: 0;
    i{
        line-height: 80px;
    }
}

.top-bar__left {
    left: 20px;
}

.top-bar__right {
    right: 10px;
    display: flex;
    align-items: center;
}

.top-bar__item {
    position: relative;
    display: inline-block;
    height: 80px;
    margin:0 10px;
    font-size: 16px;
    &--show {
        display: inline-block !important;
    }
    .el-badge__content.is-fixed{
        top:12px;
        right: 5px;
    }
}

.top-bar__title {
    height: 100%;
    padding:0 100px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: inherit;
    font-weight: 400;
    .navigation-item-wrapper {
        .el-submenu__title {
            height: 80px !important;
            line-height: 76px;
            font-size: 20px;
            /*font-weight: 400;*/
            i {
            }
            >span {
                position: relative;
                top: 2px;
            }
        }
    }
}

.top-icon {
    color: #ffffff;
    &:hover {
        color: #fffff1;
    }
}
@media screen and (max-width: 1400px) {
    .top-bar__title {
        .navigation-item-wrapper {
            .el-submenu__title {
                height: 83px !important;
                padding-left: 10px;
                padding-right: 28px;
            }
        }
    }
}