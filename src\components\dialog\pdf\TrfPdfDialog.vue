<template>
  <div>
    <el-dialog
      title="PDF View"
      :visible.sync="dialogVisible"
      width="70%">
      <el-row>
        <el-button type="primary"  style="margin-left: 12px;"
                   @click="prePage">{{$t('page.prePage')}}
        </el-button>
        <el-button type="primary"  style="margin-left: 12px;"
                   @click="nextPage">{{$t('page.nextPage')}}
        </el-button>
        <el-button type="primary"  style="margin-left: 12px;"
                   @click="clock">{{$t('page.clock')}}
        </el-button>
        <el-button type="primary"  style="margin-left: 12px;"
                   @click="counterClock">{{$t('page.counterClock')}}
        </el-button>
      </el-row>
      <el-row> <p style="margin-left: 12px;">{{pageNum}}/{{pageTotalNum}}</p></el-row>
      <div>
        <pdf
                ref="pdf"
                :src="pdfUrl"
                :page="pageNum"
                :rotate="pageRotate"
                @progress="loadedRatio = $event"
                @page-loaded="pageLoaded($event)"
                @num-pages="pageTotalNum=$event"
                @error="pdfError($event)"
                @link-clicked="page = $event"></pdf>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    name: 'Pdf',
    components: {
      pdf: resolve => require(['vue-pdf'], resolve)
    },
    name: "PdfDialog",
    data () {
      return {
        dialogVisible: false,
        imageArray: [],
        pageNum:1,
        pageTotalNum:1,
        pageRotate:0,
        // 加载进度
        loadedRatio:0,
        curPageNum:0,
        pdfUrl:'',
      }
    },
    methods: {
      prePage(){
        var p = this.pageNum
        p = p>1?p-1:this.pageTotalNum
        this.pageNum = p
      },
      nextPage(){
        var p = this.pageNum
        p = p<this.pageTotalNum?p+1:1
        this.pageNum = p
      },
      clock(){
        this.pageRotate += 90
      },
      counterClock(){
        this.pageRotate -= 90
      },
      pageLoaded(e){
        this.curPageNum = e
      },
      pdfError(error){
        console.error(error)
      },
      pdfPrintAll(){
        this.$refs.pdf.print()
      },
      pdfPrint(){
        this.$refs.pdf.print(100,[1,2])
      },
      async open (trfId) {
        //根据cloudId查询文件详细信息
              this.pdfUrl="api/sgs-mart/statistics/previewTrfPdf?trfId="+trfId;
              this.dialogVisible = true
      },
    }
  }
</script>

<style scoped>
  @media print{
    img{display:none}
  }

  .pdf {
    margin-bottom: 25px;
    border: 1px solid #ccc;
  }

</style>
