import request from '@/router/axios';




export const getList = (data) => {
    return request({
        url: '/api/sgs-mart/invoice/list',
        method: 'post',
        data:data
    })
}
export const getPayType = () => {
    return request({
        url: '/api/sgs-mart/invoice/queryPayType',
        method: 'post'
    })
}
export const createInvoiceOrder = (data) => {
    return request({
        url: '/api/sgs-mart/invoice/createPaymentOrder',
        method: 'post',
        data:data
    })
}



