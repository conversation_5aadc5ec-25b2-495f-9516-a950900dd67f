<template>
    <div class="sgs-box">

        <div class="sgs-group">
            <h3>{{$t('service.servicRequire')}}</h3>
        </div>
        <el-row :gutter="20">
            <el-col :span="24">
                <el-form-item :label="$t('service.reportHeader')" v-if="valiUsable('reportHeader')" prop="servicRequire.reportHeader" :rules="{required:valiRequired('reportHeader'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                    <el-input type="textarea" maxlength="200" v-model="servicRequire.reportHeader"
                              autocomplete="off"
                              clearable></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item :label="$t('service.reportAddress')" v-if="valiUsable('reportAddress')" prop="servicRequire.reportAddress" :rules="{required:valiRequired('reportAddress'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                    <el-input type="textarea" maxlength="200" v-model="servicRequire.reportAddress"
                              autocomplete="off"
                              clearable></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item :label="$t('service.reportSuccessSend')" v-if="valiUsable('emailAddressesValue')" prop="emailAddressesValue" :rules="{required:valiRequired('emailAddressesValue'),message:$t('trf.validate.requiredBlur'),trigger:'change'}" >
                    <el-select v-model="emailAddressesValue"
                               @change="reportDeliveredToChange"
                               multiple
                               filterable
                               allow-create
                               default-first-option
                               style="width: 100%;">
                        <el-option v-for="(obj,index) in emailGroupData"
                                   :label="obj.emailGroupName" :key="obj.id"
                                   :value="obj.emailGroupName"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item :label="$t('service.reportErrorSend')" v-if="valiUsable('failEmailAddressesValue')" prop="failEmailAddressesValue" :rules="{required:valiRequired('failEmailAddressesValue'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-select v-model="failEmailAddressesValue"
                               @change="failReportDeliveredToChange"
                               multiple
                               filterable
                               allow-create
                               default-first-option
                               style="width: 100%;">
                        <el-option v-for="(obj,index) in emailGroupData"
                                   :label="obj.emailGroupName" :key="obj.id"
                                   :value="obj.emailGroupName"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('service.returnSample')" v-if="valiUsable('returnSample')" prop="returnSampleArry" :rules="{required:valiRequired('returnSample'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-checkbox-group v-model="returnSampleArry" @change="returnSampleChange">
                        <el-checkbox name="returnSampleCheckbox"
                                     v-for="(returnSample,index) in returnSampleData"
                                     :label="returnSample.sysKey" :key="returnSample.sysKey">
                            {{returnSample.sysValue}}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('service.reportLanguage')" v-if="valiUsable('reportLanguage')" prop="servicRequire.reportLanguage" :rules="{required:valiRequired('reportLanguage'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-select v-model="servicRequire.reportLanguage" @change="reportChange"
                               clearable
                               style="width: 100%;">
                        <el-option v-for="(reportLanguage,index) in reportLanguageData"
                                   :label="reportLanguage.sysValue"
                                   :value="reportLanguage.sysKey"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col span="24">
                <el-form-item :label="$t('service.otherRequest')" v-if="valiUsable('otherRequire')" prop="servicRequire.otherRequire" :rules="{required:valiRequired('otherRequire'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-input type="textarea" maxlength="200" v-model="servicRequire.otherRequire" :placeholder="$t('service.otherTip')" clearable
                              autocomplete="off"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import {validatenull,validateEmail} from "@/util/validate";
    import {getMailGroupAndContactEmail} from "@/api/customer/customerEmailGroup";
    import {getReportLanguageArry,getReturnSampleArryCN} from "@/api/trf/trf";
    import {mapGetters} from "vuex";
    export default {
        name: "trfBasic",
        /*props: {
            trfId:String,
            bossNo:String,
            isSgs:{
                type:Boolean,
                default:false
            },
        },*/
        props:['trfId','bossNo','isSgs','require','reportDeliveredTo','failedReportDeliveredTo','fieldSettingsData'],
        data() {
            return {
                servicRequire:{},
                reportLanguageData:[],
                emailAddressesValue:'',
                //reportDeliveredTo:'',
               // failedReportDeliveredTo:'',
                returnSampleData:[],
                returnSampleArry: [],
                emailGroupData:[],
                emailGroupParam:{},
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission"]),
            emailAddressesValue: {
                get() {
                    if (this.reportDeliveredTo) {
                        return this.$lodash.split(this.reportDeliveredTo, ',')
                    }
                    return []
                },
                set(val) {
                    this.reportDeliveredTo=this.$lodash.join(val, ',');
                    //this.$set(this.trf, 'reportDeliveredTo', this.$lodash.join(val, ','))
                }
            },
            failEmailAddressesValue: {
                get() {
                    if (this.failedReportDeliveredTo) {
                        return this.$lodash.split(this.failedReportDeliveredTo, ',')
                    }
                    return []
                },
                set(val) {
                    this.failedReportDeliveredTo=this.$lodash.join(val, ',');
                    //this.$set(this.trf, 'failedReportDeliveredTo', this.$lodash.join(val, ','))
                }
            },
        },
        watch: {
            //监听语言变化
            language: function (newVal) {
                //触发查询
                this.getReturnSampleArry();
                this.queryReportLanguage();
            },
            servicRequire:{
                handler(){   //注意此处就是handler
                    console.log('serviceRequire',this.servicRequire);
                    this.$emit('update:require',this.servicRequire);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            reportDeliveredTo:{
                handler(){   //注意此处就是handler
                    console.log('reportDeliveredTo',this.reportDeliveredTo);
                    this.$emit('update:reportDeliveredTo',this.reportDeliveredTo);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            failedReportDeliveredTo:{
                handler(){   //注意此处就是handler
                    console.log('failedReportDeliveredTo',this.failedReportDeliveredTo);
                    this.$emit('update:failedReportDeliveredTo',this.failedReportDeliveredTo);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
        },
        created() {
            var companyId = this.userInfo.companyId
            //查询客户组邮件和联系人邮件数据
            this.searchEmailGroup();
            //接口获取报告语言
            this.queryReportLanguage();
            this.getReturnSampleArry();
        },
        methods: {
            //验证表单项是否展示方法
            valiUsable(code){
                var usableFlag=true;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        usableFlag=  this.fieldSettingsData[code].usable!=1?false:true
                    }
                }
                return usableFlag;
            },
            //验证必填项方法
            valiRequired(code){
                var requiredFlag=false;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        requiredFlag=  this.fieldSettingsData[code].required==1?true:false
                    }
                }
                return requiredFlag;
            },
            reportDeliveredToChange(emailList) {
                let obj = {};
                let pasteEmailArr = [];
                emailList.find((item1) => {
                    obj = this.emailGroupData.find((item) => {
                        if (item1 == item.emailGroupName) {//判断当前邮件是否和
                            return item;
                        }
                    });
                    if (validatenull(obj)) {//手动输入邮箱验证
                        let validateRes = validateEmail(item1);
                        if(validateRes){
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    }else{
                        if (obj.type != 2) {//邮件组数据
                            var contactEmail = obj.contacts;
                            contactEmail.find((item2) => {
                                if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
                                    pasteEmailArr.push(item2.contactEmail);
                                }
                            });
                        } else {
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    }
                });
                this.emailAddressesValue = pasteEmailArr;
                if (this.emailAddressesValue.length != 0) {
                    this.reportDeliveredTo = this.emailAddressesValue.join(',')
                } else {
                    this.reportDeliveredTo = '';
                }
            },
            failReportDeliveredToChange(emailList) {
                let obj = {};
                let pasteEmailArr = []
                emailList.find((item1) => {
                    obj = this.emailGroupData.find((item) => {
                        if (item1 == item.emailGroupName) {//判断当前邮件是否和
                            return item;
                        }
                    });
                    if (validatenull(obj)) {//手动输入邮箱验证
                        let validateRes = validateEmail(item1);
                        if(validateRes){
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    }else{
                        if (obj.type != 2) {//邮件组数据
                            var contactEmail = obj.contacts;
                            contactEmail.find((item2) => {
                                if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
                                    pasteEmailArr.push(item2.contactEmail);
                                }
                            });
                        } else {
                            if (pasteEmailArr.indexOf(item1) == -1) {
                                pasteEmailArr.push(item1);
                            }
                        }
                    }
                });
                this.failemailAddressesValue = pasteEmailArr;
                if (this.failemailAddressesValue.length != 0) {
                    this.failedReportDeliveredTo = this.failemailAddressesValue.join(',')
                } else {
                    this.failedReportDeliveredTo = '';
                }
            },
            reportChange(val) {
                let obj = {};
                obj = this.reportLanguageData.find((item) => {
                    return item.sysKey === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.servicRequire, 'reportLanguageName', obj.sysValue)
                }
            },
            returnSampleChange(value) {
                var returnSampleName = '';
                var returnSample = this.returnSampleArry.join(',');
                var returnSampleArray1 = returnSample.split(",");
                if (returnSampleArray1 != '' && returnSampleArray1 != undefined) {
                    returnSampleArray1.find((item) => {
                        this.returnSampleData.find((item1) => {
                            if (item == item1.sysKey) {
                                returnSampleName += item1.sysValue + ',';
                            }
                        });
                    });
                }
                if (returnSampleName.length > 0) {
                    returnSampleName = returnSampleName.substr(0, returnSampleName.length - 1);
                }
                this.$set(this.servicRequire, 'returnSampleName', returnSampleName);
                this.$set(this.servicRequire, 'returnSampleRequire', returnSample)
               // this.servicRequire.returnSampleName = returnSampleName;
                //this.servicRequire.returnSampleRequire = returnSample; // 记录所有被选中项的下标
            },
            /*===================数据查询方法===========================*/
            searchEmailGroup() {
                var params = {};
                this.$set(this.emailGroupParam, 'status', 1)
                getMailGroupAndContactEmail(Object.assign(params, this.emailGroupParam)).then(res => {
                    this.emailGroupData = res.data.data;
                });
            },
            queryReportLanguage() {
                getReportLanguageArry(this.language).then(res => {
                    const data = res.data;
                    if (data.length > 0) {
                        this.reportLanguageData = data;
                        //默认设置为英文报告语言
                        if(validatenull(this.servicRequire.reportLanguage)){
                            this.$set(this.servicRequire,'reportLanguage','1')
                            let obj = {};
                            obj = this.reportLanguageData.find((item) => {
                                return item.sysKey === 1 || item.sysKey === '1';
                            });
                            if (obj != undefined && obj != null) {
                                this.$set(this.servicRequire,'reportLanguageName',obj.sysValue)
                            }
                        }
                    }
                });
            },
            getReturnSampleArry() {
                getReturnSampleArryCN(this.language).then(res => {
                    const data = res.data;
                    if (data.length > 0) {
                        this.returnSampleData = data;
                    }
                });
            },
        }
    }
</script>

<style scoped>

</style>
