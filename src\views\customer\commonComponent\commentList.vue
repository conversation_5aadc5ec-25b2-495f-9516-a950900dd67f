<template>
    <div class="comment-list">
        <div class="comment-item" v-for="(item,index) in commentDataList" :key="index">
            <div class="comment-base">
                <span class="user-name">{{item.createUser}}</span>
                <span class="user-time">{{item.createTime}}</span>
            </div>
            <div class="comment-content">
                <p>{{item.content}}</p>
            </div>
        </div>
        <div v-if="!commentDataList.length" class="no-data">
            <img src="/img/icon/empty-data.png" />
            <p>{{ $t('training.NoData') }}</p>
        </div>
    </div>
</template>
<script>
import api from "../../../api/newSamples";
export default {
    name: "commentList",
    data() {
        return {
            commentDataList: [] 
        } 
    },
    props: {
        objectId: {
            type: String,
            default: ""
        }, 
        objectType: {
            type: String,
            default: "" 
        }
    },
    created() {
        this.getCommentList(); 
    },
    methods: {
        getCommentList() {
            let param = {
                mainId:this.objectId,
            }
            this.loading = true;
            api.queryCommentList(param).then(res=>{
                this.loading = false;
                if(res.status==200 && res.data && res.data.data){
                    this.commentDataList = res.data.data.reverse();
                }
            }).catch(err=>{
                console.error("comment error: ",err)
            }) 
        } 
    }

}
</script>
<style lang="scss" scoped>
@import "@/styles/unit.scss";

.comment-list{
    max-height: 300px;
    overflow-y: auto;
}
.comment-base{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    font-size: 12px;
    margin: $inline-element-spacing 0;
    .user-name{
        font-weight: 600;
    }
    .user-time{
        color: $text-color-value;
    }
}
.comment-item{
    border-top: 1px solid $text-divider-color;
}
.no-data{
    text-align: center;
    color: $text-color-value;
    padding-top: $module-padding-vertical;
}
</style>