import { ref } from 'vue'
import {  useRoute } from 'vue-router'
//import { useRouter, useRoute } from 'vue-router'
//import { useStore } from 'vuex'
//import { validatenull } from './validate'
//import { queryBuSetting } from '../api/common'
import { getToken } from './auth'
// import { ElLoading } from 'element-plus'

// interface QueryBuSettingResponse {
//   data: {
//     data: {
//       paramValue: string
//     }[]
//   }
// }

// interface MenuItem {
//   id: number
//   name: string
// }

export const useAuthRedirectLogic = () => {
  // 获取路由和 store 实例
  const route = useRoute()
  // const router = useRouter()
  // const store = useStore()

  // 定义响应式数据
  const token = ref(route.query.token as string)
  // const redirect = ref(route.query.redirect as string)
  // const systemId = ref(route.query.systemId as string)
  // const loading = ref<{ close: () => void } | null>(null)
  const checkToken = ref('')

  // 查询配置的方法
  // const querySetting = () => {
  //   return new Promise((resolve) => {
  //     const params = {
  //       systemId: 15,
  //       groupCode: 'SGSSmartRedirectedUrl',
  //       productLineCode: 'SGS',
  //       paramCode: systemId.value,
  //     }
  //     queryBuSetting(params)
  //       .then((res: QueryBuSettingResponse) => {
  //         if (res.data && res.data.data && res.data.data.length > 0) {
  //           const configObj = res.data.data[0]
  //           const { paramValue } = configObj
  //           // 不存在配置地址
  //           if (!paramValue) {
  //             resolve(false)
  //             return
  //           }
  //           loading.value?.close()
  //           const newSkipUrl =
  //             encodeURIComponent(redirect.value) +
  //             '&token=' +
  //             checkToken.value +
  //             '&systemId=' +
  //             systemId.value
  //           const rootUrl = paramValue
  //           const redirectUrl = rootUrl + newSkipUrl
  //           window.location.href = redirectUrl
  //           resolve(true)
  //         } else {
  //           resolve(false)
  //         }
  //       })
  //       .catch(() => {
  //         resolve(false)
  //       })
  //   })
  // }

  // 检查并重定向的方法
  // const checkAndRedirect = async (isLogin: boolean, callback: () => void) => {
  //   // 不存在 systemId 参数，不用读取配置，按照老的逻辑进行跳转
  //   if (!systemId.value) {
  //     callback()
  //     return
  //   }
  //   // token 无效，跳转到登录页面，由登录页面接管处理
  //   if (!isLogin) {
  //     // 拼接参数到 login 页面，login 页面读取的是 redirectedFrom
  //     loading.value?.close()
  //     router.replace({
  //       path: '/login',
  //       query: {
  //         redirectedFrom: redirect.value,
  //         token: checkToken.value,
  //         systemId: systemId.value,
  //       },
  //     })
  //   } else {
  //     // token 有效，读取配置进行跳转
  //     const redirectTo = await querySetting()
  //     loading.value?.close()
  //     // 没有获取到配置，获取配置出现异常，走默认逻辑
  //     if (!redirectTo) {
  //       callback()
  //     }
  //   }
  // }

  // 处理登录的方法
  // const handleLogin = () => {
  //   loading.value = ElLoading.service({
  //     lock: true,
  //     text: `Login...`,
  //     spinner: 'el-icon-loading',
  //   })

  //   store
  //     .dispatch('LoginByToken', checkToken.value)
  //     .then(() => {
  //       checkAndRedirect(true, () => {
  //         if (!validatenull(redirect.value)) {
  //           store.dispatch('GetMenu').then((data: MenuItem[]) => {
  //             if (data.length === 0) return
  //             router.push({ path: redirect.value })
  //             loading.value?.close()
  //           })
  //         } else {
  //           router.push({ path: '/layout' })
  //           loading.value?.close()
  //         }
  //       })
  //     })
  //     .catch(() => {
  //       checkAndRedirect(false, () => {
  //         loading.value?.close()
  //         router.push({ path: '/403' })
  //       })
  //     })
  // }

  // 组件挂载时执行的初始化操作
  const init = () => {
    // 检测 token 是否存在（cookie和url）
    checkToken.value = getToken() || token.value
   // handleLogin()
  }

  return {
    init,
  }
}
