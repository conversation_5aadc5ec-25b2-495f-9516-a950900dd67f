/* 重置element ui 样式 */
/* 临时放置-处理冲突*/
@use './unit.module.scss' as *;

.el-sub-menu__title:hover {
    color: $primary-color !important;
  }
  .el-menu:not(.el-menu--collapse) .el-sub-menu__title:hover{
    background-color: rgb(48, 65, 73);
  }
  .el-sub-menu .el-sub-menu__icon-arrow{
    right: 11px;
    top: 52%;
  }
  .el-sub-menu__title i {
    color: #909399;
  }
  .el-sub-menu__title:hover i{
    color: $primary-color;
  }
  .el-sub-menu__title {
    padding-left: 10px;
    padding-right: 28px;
    line-height: normal;
  }
  .el-menu--horizontal .el-menu .el-sub-menu__title {
    min-width: 260px;
  }
  .el-menu:not(.el-menu--collapse) .el-sub-menu__title {
   padding-right: 28px; 
  }
  .top-bar__title .navigation-item-wrapper .el-sub-menu .el-sub-menu__title {
    > span {
      height: 26px;
      top: 0 !important;
      // letter-spacing: 0.4px;
    }
  }
  .el-menu--popup{
    padding: 0;
  }
  .top-bar__title {
    padding: 0 100px 0 110px!important;
  }
  .el-menu--horizontal .el-menu .el-menu-item,
  .el-menu--horizontal .el-menu .el-sub-menu__title {
    border-bottom: 1px solid transparent;
  }