import request from '@/router/axios';


import echarts from 'echarts'
import {getPage,selectTrainingClassList} from "@/api/training/performance";
const install = function(Vue) {
    console.log(this.formSelect);
    Object.defineProperties(Vue.prototype, {
        $chart: {
            get() {
                return {
                    line1: function (id) {
                        this.chart = echarts.init(document.getElementById(id));
                        this.chart.clear();

                        const optionData = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                    type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                                }
                            },
                            legend: {
                                //data: ['PASS', 'FAIL', 'NOT ATTENDED']
                                //data: this.echartsData.moduleName
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: [
                                {
                                    type: 'category',
                                    data: ['module1', 'module1', 'module1', 'module1', 'module1', 'module1', 'module1']
                                }
                            ],
                            yAxis: [
                                {
                                    type: 'value'
                                }
                            ],
                            series: [
                                {
                                    name: 'PASS',
                                    type: 'bar',
                                    stack: '广告',
                                    data: [320, 332, 301, 334, 390, 330, 320],
                                    itemStyle:{
                                        normal:{color:"#6dbb15"},
                                    },
                                    barWidth : 30
                                },
                                {
                                    name: 'FAIL',
                                    type: 'bar',
                                    stack: '广告',
                                    data: [120, 132, 101, 134, 90, 230, 210],
                                    itemStyle:{
                                        normal:{color:"#c2bac4"},
                                    }
                                },
                                {
                                    name: 'NOT ATTENDED',
                                    type: 'bar',
                                    stack: '广告',
                                    data: [220, 182, 191, 234, 290, 330, 310],
                                    itemStyle:{
                                        normal:{color:"#bb1616"},
                                    }
                                }
                            ]
                        };

                        this.chart.setOption(optionData);
                    },
                }
            }
        }
    })
}

export default {
    install
}