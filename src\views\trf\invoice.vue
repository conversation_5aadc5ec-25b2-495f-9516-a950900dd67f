<template>
    <el-card class="sgs-box" id="customer_card">
        <div class="sgs-group">
            <h3>Invoice</h3>
            <div class="right">
                <el-button type="primary" @click="addComments" size="small">
                    {{$t('operation.save')}}
                </el-button>
            </div>
        </div>
        <el-form label-position="left" :model="invoice" label-width="190px" size="small">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="Select Payment Currency">
                        <!--<el-input v-model="customer.customerGroupId" autocomplete="off"></el-input>-->
                        <el-select v-model="invoice.currency" clearable filterable
                                   :placeholder="$t('customerGroup.name')"
                                   @change="selectBuyerCustomerGroupChange" style="width: 100%;">
                            <el-option label="CNY" value="CNY"></el-option>
                            <!--<el-option v-for="(customerGroup,index) in buyerCustomerGroupData"
                                       :label="customerGroup.customerGroupName"
                                       :value="customerGroup.customerGroupCode"></el-option>-->
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row>
            <h4 class="sgs-title">Invoice Details</h4>
            <div class="right">
                <el-button type="primary" @click="add" size="small">
                    {{$t('operation.add')}}
                </el-button>
            </div>
        </el-row>

        <el-table
                size="mini"
                :data="master_user.data"
                fixed
                style="width: 100%;margin:auto"
                highlight-current-row
        >
            <el-table-column type="index"></el-table-column>
            <el-table-column
                    v-for="(item,index) in master_user.columns"
                    :label="item.label"
                    :prop="item.prop"
                    :key="index"
            >
                <template slot-scope="scope">
                    <span v-if="item.prop!='totalItems' && item.prop!= 'estimaTax' && item.prop!='totalAmount' && item.prop!='pdf'">
                   <el-input placeholder="请输入内容" v-model="master_user.sel[item.prop]"></el-input>
                    </span>
                    <span v-else-if="item.prop=='totalItems' || item.prop== 'estimaTax'">
                         <el-input placeholder="请输入金额" v-model="master_user.sel[item.prop]">
                              <template slot="prepend" v-if="invoice.currency === 'CNY'">¥</template>
                                <template slot="prepend" v-else>$</template>
                         </el-input>
                    </span>
                    <span v-else-if="item.prop=='totalAmount'">
                         <el-input placeholder="请输入金额" :value="master_user.sel['totalItems'] + master_user.sel['estimaTax']" v-model="master_user.sel[item.prop]" disabled="disabled">
                              <template slot="prepend" v-if="invoice.currency === 'CNY'">¥</template>
                                <template slot="prepend" v-else>$</template>
                         </el-input>
                    </span>
                    <span v-else><!--上传-->
                        <el-upload
                                ref="upload"
                                class="customerUpload"
                                :action="uploadUrl"
                                :file-list="item['uploadFiles']"
                                :show-file-list="true"
                                :accept="fileType"
                                :multiple="false"
                                :on-change="(param) => invoiceFileChange(item,param)"
                                :on-remove="invoiceFileRemove"
                                :auto-upload="false"
                                :on-preview="downFile"
                                :limit="1"
                                :disabled="companyType ==1 || companyType == 2"
                        >
                      <i class="el-icon-upload2" ></i>
                    </el-upload>
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button size="mini" type="danger" @click="deleteRow(scope.$index,master_user.data)">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--<el-table size="mini" :data="tableData" style="width: 100%">
            <el-table-column type="index"></el-table-column>
            <el-table-column
                    prop="reference"
                    label="Reference">
                <template slot-scope="scope">
                                     <span>
                   <el-input size="mini" placeholder="请输入内容" v-model="master_user.sel[item.prop]"></el-input>
                 </span>
                    <span v-else>{{scope.row[item.prop]}}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="quotation"
                    label="Quotation">
            </el-table-column>
            <el-table-column
                    prop="invoiceNo"
                    label="Invoice No.">
            </el-table-column>
            <el-table-column
                    prop="totalItems"
                    label="Total Test Items">
            </el-table-column>
            <el-table-column
                    prop="estimaTax"
                    label="Estima Tax">
            </el-table-column>
            <el-table-column
                    prop="totalAmount"
                    label="Total Amount">
            </el-table-column>
            &lt;!&ndash; <el-table-column
                     prop="totalItems"
                     label="Invoice PDF">
             </el-table-column>&ndash;&gt;
            <el-table-column
                    label="Action">
                <template slot-scope="scope">
                    <el-button size="mini" type="danger" @click="del(scope.row, scope.$index)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>-->
    </el-card>
</template>
<script>
    import {getCloudFileURL} from "@/api/common/index";

    export default {
        name: "detail",
        props: {
            trfId:{
                type:Object,
                default:{}
            },
            approve:{
                type:Object,
                default:{}
            }
        },
        invoiceTableData: [],
        data() {
            return {
                invoice: {
                    currency: '',
                    uploadUrl:'',
                    fileList:[],
                    fileType: 'application/msword,application/pdf,aplication/zip,application/vnd.ms-excel,text/plain',
                },
                master_user: {
                    sel: null, //选中行
                    columns: [
                        {
                            prop: "reference",
                            label: "Reference",

                        },
                        {
                            prop: "quotation",
                            label: "Quotation"
                        },
                        {
                            prop: "invoiceNo",
                            label: "Invoice No."
                        },
                        {
                            prop: "totalItems",
                            label: "Total Test Items"
                        },
                        {
                            prop: "estimaTax",
                            label: "Estima Tax"
                        },
                        {
                            prop: "totalAmount",
                            label: "Total Amount"
                        },
                        {
                            prop: "pdf",
                            label: "Invoice PDF"
                        }
                    ],
                    data: []
                },
                tableData: [
                    {
                        reference: 'reference1',
                        quotation: 'quotation1',
                        invoiceNo: 'invoiceNo1',
                        totalItems: 'totalItems1',
                        estimaTax: 'estimaTax1',
                        totalAmount: 'totalAmount1',
                    },
                    {
                        reference: 'reference2',
                        quotation: 'quotation2',
                        invoiceNo: 'invoiceNo2',
                        totalItems: 'totalItems2',
                        estimaTax: 'estimaTax2',
                        totalAmount: 'totalAmount2',
                    },
                    {
                        reference: 'reference3',
                        quotation: 'quotation3',
                        invoiceNo: 'invoiceNo3',
                        totalItems: 'totalItems3',
                        estimaTax: 'estimaTax3',
                        totalAmount: 'totalAmount3',
                    },
                    {
                        reference: 'reference4',
                        quotation: 'quotation4',
                        invoiceNo: 'invoiceNo4',
                        totalItems: 'totalItems4',
                        estimaTax: 'estimaTax4',
                        totalAmount: 'totalAmount4',
                    }

                ],
            };
        },
        methods: {
            add() {
                /*for (let i of this.master_user.data) {
                    if (i.isSet) return this.$message.error("请先保存当前编辑项");
                }*/
                let j = {
                    index: "",
                    reference: '',
                    quotation: '',
                    invoiceNo: '',
                    totalItems: '',
                    estimaTax: '',
                    totalAmount: ''
                };
                this.master_user.data.push(j);
                this.master_user.sel = JSON.parse(JSON.stringify(j));
            },
            deleteRow(index, rows) {
                //删除
                rows.splice(index, 1);
            },
        },
        created() {

        },
        watch: {}
    }
</script>

<style scoped>

</style>
