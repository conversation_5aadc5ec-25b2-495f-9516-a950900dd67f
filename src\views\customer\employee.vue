<template>
    <el-table
            ref="selectEmployee"
            :data="tableData"
            style="width: 100%"
            size="medium"
            @select-all="handleSelectionChange"
            @select="handleSelectionChange">
        <el-table-column
                type="selection"
                prop="ID"
                width="55">
        </el-table-column>
        <el-table-column
                prop="contactName"
                :label="$t('contact.name')"
                width="180" align="center">
          <!--  <template #header>
                <div>{{$t('contact.name')}}</div>
                <el-input v-model="search" size="mini" placeholder="输入关键字搜索"/>
            </template>-->
        </el-table-column>


        <el-table-column
                prop="contactTel"
                :label="$t('contact.phone')"
                align="center">
        </el-table-column>
        <el-table-column
                prop="contactEmail"
                :label="$t('contact.email')"
                align="center">
        </el-table-column>
    </el-table>
</template>

<script>
    import {
        getCustomerContactList
    } from "@/api/trf/trf";
    import {getPageByUser, getListByUser} from "@/api/customer/customerContact";

    export default {
        props: ['customerId', 'selected'],
        data() {
            return {
                name: "customerEmployee",
                tableData: [],
                form: {},
                query: {},
                page: {
                    pageSize: 100,
                    currentPage: 1,
                    total: 0
                },
                customerContactParam: {
                    status: '1',
                },
            }
        },
        methods: {
            onLoad(page, params = {}) {

                var customerContactparams = {};
                debugger;
                getListByUser(Object.assign(customerContactparams, this.customerContactParam)).then(res => {
                    const data = res.data.data;
                    this.tableData = data;
                    setTimeout(() => {
                        if (this.selected) {
                            this.selected.forEach((contact) => {
                                const selectRow = this.tableData.filter((row) => {
                                    if (row.id == contact.id) {
                                        return row;
                                    }
                                });
                                this.$refs.selectEmployee.toggleRowSelection(selectRow[0]);
                            })
                        }
                    }, 200);
                });


                /* getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                     this.tableData = res.data.data.records;
                     this.page.total = res.data.data.total;
                     setTimeout(() => {
                         if(this.selected){
                             this.selected.forEach((contact) => {
                                 const selectRow = this.tableData.filter((row) => {
                                     if(row.id==contact.id){
                                         return row;
                                     }
                                 });
                                 this.$refs.selectEmployee.toggleRowSelection(selectRow[0]);
                             })
                         }
                     },200);

                 });*/
            },
            handleSelectionChange(val) {
                this.$emit('handleSelectPerson', val);
            },

        },
        watch: {
            selected: function (data) {
                this.onLoad(this.page);
            },
        },
        created() {
            this.onLoad(this.page, {status: 1});
        }

    }
</script>

<style scoped>

</style>
