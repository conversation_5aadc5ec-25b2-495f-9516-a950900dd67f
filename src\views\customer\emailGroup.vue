<template>
    <div>
        <!-- <el-form :inline="true" :model="formInline" size="medium">
            <el-form-item>
                <el-input v-model="query.emailGroupName" :placeholder="$t('emailGroup.nameFull')"  clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onLoad">{{$t('operation.search')}}</el-button>
                <el-button v-if="permissionList.addBtn" type="primary" @click="addRow">{{$t('operation.add')}}</el-button>
            </el-form-item>
        </el-form> -->
        <el-form :inline="true" :model="formInline" size="medium" class="text-right">
            <el-form-item>
                <el-input
                    @input="onSearch"
                    @keyup.enter.native="onLoad"
                    v-model="query.emailGroupName"
                    :placeholder="$t('emailGroup.nameFull')"
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onLoad"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>

        <el-table
            ref="emailGroupTable"
            :data="tableData"
            style="width: 100%"
            size="medium"
            row-key="id"
            class="parent-table">
            <el-table-column
                type="index"
                label="#"
                width="50">
            </el-table-column>
            <el-table-column type="expand">
                <template slot-scope="scope">
                    <el-table :data="scope.row.contacts" :element-loading-text="$t('loading')" style="width: 100%" class="sub-table">
                        <el-table-column
                            prop="contactName"
                            :label="$t('contact.nameFull')"
                            width="250"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="contactTel"
                            :label="$t('contact.phone')"
                            width="250">
                        </el-table-column>
                        <el-table-column
                            prop="contactEmail"
                            :label="$t('contact.email')"
                            width="250">
                        </el-table-column>
                        <el-table-column
                            prop=""
                            label="">
                        </el-table-column>
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column
                prop="emailGroupName" 
                :label="$t('emailGroup.nameFull')"
                width="250">
            </el-table-column>
            <el-table-column
                prop="updateTime"
                :label="$t('common.operationTime')"
                width="250"
                align="left">
            </el-table-column>
            <el-table-column
                prop="updateUser"
                :label="$t('common.operator')"
                width="250">
            </el-table-column>
            <el-table-column
                prop="status"
                :label="$t('common.status.title')"
                align="left">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                        <el-switch
                            v-model="scope.row.status"
                            active-color="#FF6600"
                            inactive-color="#D9D9D9"
                            :active-value = "1"
                            :inactive-value = "0"
                            @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                :label="$t('operation.title')"
                width="180"
                align="left">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)">{{$t('operation.edit')}}</el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            @size-change="sizeChange"
            @current-change="currentChange"
            :current-page="page.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total">
        </el-pagination>
        <el-drawer :title="title" :visible.sync="dialogFormVisible" size="60%">
            <el-form ref="form" :model="form"
                label-width="200px"
                label-position="left"
                size="medium"
                class="sgs-form"
            >
                <el-form-item :label="$t('emailGroup.nameFull')" :rules="{ required: true, message: $t('emailGroup.validate.nameBlur'), trigger: 'blur' }" prop="emailGroupName">
                    <el-input v-model="form.emailGroupName" maxlength="150"></el-input>
                </el-form-item>
                <el-form-item :label="$t('emailGroup.member')">
                    <customer-employee :selected.sync="form.contacts" @handleSelectPerson="handleSelectPerson"></customer-employee>
                </el-form-item>
            </el-form>
            <div class="sgs-bottom">
                <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">{{$t('operation.submit')}}</el-button>
            </div>
        </el-drawer>
    </div>
</template>

<script>
    import {getPageByUser,add,remove,detail,emailGroupPersons} from "@/api/customer/customerEmailGroup";
    import customerEmployee from "@/views/customer/employee";
    import {validatenull} from "@/util/validate";
    import {mapGetters} from "vuex";

    export default {
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        components: {customerEmployee},
        data(){
            return{
                name: "emailGroup",
                title:'',
                dialogFormVisible: false,
                btnGuestbookSubmit: false,
                tableData: [],
                tablePersonData: [],
                expands:[],
                employeeEmail: [],
                currentRow: {},
                form: {contacts:[]},
                query: {},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:mailGroup:add'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:mailGroup:edit'],false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:mailGroup:delete'],false),
                };
            }
        },
        methods:{
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query,this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            addRow(){
                this.title = this.$t('emailGroup.title.add');
                this.dialogFormVisible =true;
                this.form = {};
            },
            submitForm(form){
                if(validatenull(this.form.contacts)) {
                    this.$message.error(this.$t('emailGroup.validate.contactBlur'));
                    return false;
                }
                this.$refs[form].validate((valid) => {
                    if (valid) {
                            this.btnGuestbookSubmit=true;
                            add(this.form).then(res =>{
                                this.btnGuestbookSubmit=false;
                                this.$message({
                                    type: "success",
                                    message: this.$t('api.success')
                                });
                                this.dialogFormVisible=false;
                                this.onLoad(this.page);
                            });
                        }else {
                        return false;
                       }
                  });
            },
            removeRow(row){
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                .then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            detailRow(row){
                this.title = this.$t('emailGroup.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form=res.data.data;
                });

            },
            handleSelectPerson(data){
                this.form.contacts = data;
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            changeStatus(row){
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                add(modifiedForm).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            }
        },
        created() {
            this.onLoad(this.page);
        }

    }
</script>

<style lang="scss" scoped>
.parent-table {
    /deep/ .el-table__body-wrapper {
        .el-table__expanded-cell {
            padding: 0 32px;
        }
    }
    
}
.sub-table {
    /deep/ table {
        th {
            background: #f2f2f2 !important;
            color: #666 !important;
        }
        th, td {
            padding-left: 68px;
            color: #888;
        }
    }
}
</style>
