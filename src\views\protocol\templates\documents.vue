<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_protocol_documents" id="sgs_smart_protocol_documents">
            <div style="text-align: right">
                <el-button type="primary" @click="uploadDoc">Upload</el-button>
            </div>
            <el-table
                    :data="dataList"
                    border
                    fit
            >
                <el-table-column label="Document Name" prop="fileName"></el-table-column>
                <el-table-column label="Created Time" prop="createTime"></el-table-column>
                <el-table-column show-overflow-tooltip label="Remark" prop="remark">
                    <template slot-scope="{row}">
                        <div v-if="!row.edit">{{row.remark}}</div>
                        <el-input show-word-limit maxlength="1000" @blur="updateRemark(row)" v-model="row.remark" v-if="row.edit"></el-input>
                    </template>
                </el-table-column>
                <el-table-column width="140px" label="Action" prop="fileName">
                    <template slot-scope="{row,$index}">
                        <el-tooltip content="Edit Remark" placement="top">
                            <el-button type="text" icon="el-icon-edit" v-if="!row.edit" @click="row.edit=true"></el-button>
                        </el-tooltip>
                        <el-tooltip content="Download" placement="top">
                            <el-button type="text" :disabled="false" icon="el-icon-download" @click="downloadFile(row)"></el-button>
                        </el-tooltip>
                        <el-tooltip content="Delete"  placement="top">
                            <el-popconfirm
                                    confirm-button-text='Confirm'
                                    cancel-button-text='Cancel'
                                    icon="el-icon-info"
                                    icon-color="red"
                                    title="Delete the data?"
                                    @confirm="deleteFile(row,$index)"
                            >
                                <el-button slot="reference" style="margin-left: 10px" type="text" icon="el-icon-delete"></el-button>
                            </el-popconfirm>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <sgs-batch-upload
                v-if="isLoadUpload"
                title="Upload"
                append-to-body
                :systemID="1"
                :limit="5"
                :handle-upload-success="uploadSuccess"
                :handle-upload-error="uploadError"
                ref="batchUpload"
                accept=".pdf,.docx,.doc,.xlsx,.xls,.csv"
                upload-url="/api/sgsapi/FrameWorkApi/file/doUpload"
                :attachment-type-options="[]"
                attachment-type-default-value=""
                :file-max-sizes="20">
        </sgs-batch-upload>
    </basic-container>
</template>

<script>
    import protocolApi from '../../../api/protocol/protocol'

    export default {
        name: "documents",
        data() {
            return {
                pageLoading: false,
                dataList:[],
                isLoadUpload:false
            }
        },
        methods: {
            initPage() {
                let param = {
                    objectId:this.objId,
                    object:'protocol',
                    current:1,
                    size:1
                }
                this.pageLoading = true;
                protocolApi.queryAttr(param).then(res=>{
                    this.pageLoading = false;
                    if(res.status==200 && res.data && res.data.data && res.data.data.records){
                        let dataList = res.data.data.records;
                        dataList.forEach(da=>{
                            da['edit'] = false;
                        })
                        this.dataList = dataList;
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                })
            },
            downloadFile(row){
                let {fileUrl} = row;
                protocolApi.downloadFile(fileUrl).then(res=>{
                    let downloadURL = res.data.data;
                    if(downloadURL){
                        window.open(downloadURL, '_blank')
                    }
                })
            },
            deleteFile(row,rowIndex){
                if(!this.defaultSave){
                    this.dataList.splice(rowIndex,1);
                    return;
                }
                let {id} = row;
                protocolApi.deleteAttrById(id).then(res=>{
                    let deleteIndex = this.dataList.findIndex(da=>da.id==id);
                    this.$notify.success("Success");
                    this.dataList.splice(deleteIndex,1);
                }).catch(err=>{
                    this.$notify.error("Delete fail");
                })
            },
            updateRemark(row){
                if(!this.defaultSave){
                    row.edit = false;
                    return;
                }
                let {id,remark} = row;
                protocolApi.updateAttrRemarkById({id,remark}).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        row.edit = false;
                        this.$notify.success("Success");
                    }else{
                        this.$notify.error("Update fail");
                    }
                }).catch(err=>{
                    this.$notify.error("Update fail");
                })
            },
            uploadDoc(){
                this.isLoadUpload = false;
                this.$nextTick(()=>{
                    this.isLoadUpload = true;
                    this.$nextTick(()=>{
                        this.$refs.batchUpload.open();
                    })
                })
            },
            uploadError(){},
            uploadSuccess(data){
                console.log("上传成功之后的data",data);
                this.$refs.batchUpload.close();
                this.isLoadUpload = false;

                if(data){
                    let objId = this.objId;
                    let fileList = (data.data || []).map(item =>{
                        const attachment = {
                            'object': 'protocol',
                            'objectId': objId,
                            'attachmentId': item.id,
                            'fileName': item.attachmentName+"."+item.suffixes,
                            'fileUrl': item.path,
                            'fileType': 'File',
                            'languageId':1,
                            'remark':'',
                            edit:false
                        }
                        return attachment;
                    });

                    if(!this.defaultSave){
                        fileList.forEach(f=>{
                            this.dataList.push(f);
                        })
                        return;
                    }

                    this.saveData();
                }
            },
            saveData(){
                if(!this.dataList || this.dataList.length==0){
                    return
                }
                protocolApi.saveAtt(this.dataList).then(res=>{
                    if(this.defaultSave){
                        if(res.status==200 && res.data && res.data.data){
                                this.$notify.success("Save file success")
                                this.initPage();
                        }else{
                            this.$notify.error("Save fail error")
                        }
                    }
                })
            }
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            objId:'',
            edit:true,
            defaultSave:true,
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_protocol_documents {
        font-family: 'Arial' !important;
        background: #fff;
        .menu-icon{
            font-size: 14px;
            cursor: pointer;
            margin: 0 10px;
            color:#ff6600
        }
    }
</style>