import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/sgs-form/form/data/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sgs-form/form/data/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const removeByDocId = (formCode, ids) => {
    return request({
        url: '/api/sgs-form/form/data/remove/docId',
        method: 'post',
        params: {
            formCode,
            ids,
        }
    })
}


export const add = (row) => {
  return request({
    url: '/api/sgs-form/form/data/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sgs-form/form/data/submit',
    method: 'post',
    data: row
  })
}
