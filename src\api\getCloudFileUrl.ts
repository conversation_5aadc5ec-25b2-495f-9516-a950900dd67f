import request from "./request";



export const getCloudFileURL = (params: any) => {
  
    return request({
        url: '/sgsapi/FrameWorkApi/file/downloadByCloudID',
        method: 'post',
        params
    })
  }


export const getPackedCloudFileURL = (data: any) => {
  
    return request({
        url: '/sgsapi/FrameWorkApi/file/batchDownloadByCloudID',
        method: 'post',
        data
    })
  }

export const attachmentviewcount = (data: any) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledge/attachmentviewcount',
    method: 'post',
    data
  })
}