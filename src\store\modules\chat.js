const chat = {
	state: {
		socketTask: null, // ws链接
		webSocketPingTimer: null, // 心跳定时器
		webSocketPingTime: 50000, // 心跳的间隔，当前为 50秒,
		serverTimeoutObj:null, // 服务器心跳定时器
		severTimeout:5000,// 服务器超时时间
		webSocketReconnectCount: 0, // 重连次数
		webSocketIsReconnect: true, // 是否重连
		webSocketIsOpen: false,
		sendMsgStatus:false,
		msg: null ,//接收到的信息
		userId:'',
		userSource:'SGSMART',
		trfNo:'',
		firstOpenTime:'',
		imGroupUser:'',
		imGroupUsers:[],
		chatList:[],
	},
	mutations: {
		setMsg(state,val) {
			state.msg = val
		},
		setuserId(state,val) {
			state.userId = val
		},
		settrfNo(state,val) {
			state.trfNo = val
			console.log(state)
		},
		setfirstOpenTime(state,val) {
			state.firstOpenTime = val
		},
		setWebSocketIsReconnect(state, val) {
			state.webSocketIsReconnect = val
		},
		setsocketTask(state, val) {
			state.socketTask = val
		},
		//初始化ws 用户登录后调用
		webSocketInit(state) {
			let that = this
			let host = ''
			if (process.env.VUE_APP_ENV === 'development') {
				host = 'test.sgsmart-online.com'
			} else {
				host = window.location.host
				// host = 'test.sgsmart-online.com'
			}
			console.log(state)
			state.socketTask = new WebSocket(`wss://${host}/ws?userId=${state.userId}&userSource=${state.userSource}&trfNo=${state.trfNo}`);
			this.commit('setfirstOpenTime',new Date())
			// ws连接开启后登录验证
			state.socketTask.onopen = ((res) => {
				//标记状态
				that.state.chat.webSocketIsOpen = true
				//开始心跳
				that.commit('webSocketPing')
				// 注：只有连接正常打开中 ，才能正常收到消息
				state.socketTask.onmessage = ((res) => {
					that.commit('webSocketPing')
					let sMsg = JSON.parse(res.data)
					this.commit('setMsg',sMsg)
					// state.webSocketReconnectCount += 1;
				});
			});
 
			state.socketTask.onerror = ((errMsg) => {
				console.log("ws连接异常")
				this.commit('webSocketClose')
			});
 
			state.socketTask.onclose = ((errMsg) => {
				console.log("ws连接关闭")
				this.commit('webSocketClose')
			});
		},
		closeWebSocket(state) {
			if (state.socketTask) {
				state.socketTask.close();
			}
			state.webSocketReconnectCount = 0
			state.webSocketIsReconnect = false
			state.webSocketIsOpen = false;
			state.webSocket = null;
			this.commit('setsocketTask',null)
		},
		// 断开连接时
		webSocketClose(state) {
			let that = this
			console.log("连接断开时")
			// 修改状态为未连接
			state.webSocketIsOpen = false;
			state.webSocket = null;
			this.commit('setsocketTask',null)
			// 判断是否重连
			let time = new Date().getTime() - state.firstOpenTime.getTime()
			console.log(state)
			if (state.webSocketIsReconnect && state.webSocketReconnectCount < 5 && time > 1000) {
				// 第一次直接尝试重连
				that.commit('webSocketReconnect');
			}
		},
		// 定时心跳
		webSocketPing() {
			let that = this
			that.state.chat.webSocketPingTimer && clearTimeout(that.state.chat.webSocketPingTimer)
			that.state.chat.serverTimeoutObj && clearTimeout(that.state.chat.serverTimeoutObj)
			that.state.chat.webSocketPingTimer = setTimeout(() => {
				console.log("心跳状态webSocketIsOpen：")
				console.log(that.state.chat.webSocketIsOpen)
				if (!that.state.chat.webSocketIsOpen) {
					return false;
				}
				console.log("心跳");
				//心跳应为非json的字符串
				const payload = "ping";
				that.commit('webSocketSend', payload);
				that.state.chat.serverTimeoutObj = setTimeout(function() {
					that.commit('closeWebSocket');
				}, that.state.chat.severTimeout);
			}, that.state.chat.webSocketPingTime);
		},
		// WebSocket 重连
		webSocketReconnect(state) {
			let that = this
			if (state.webSocketIsOpen) {
				return false;
			}
			console.log("第"+state.webSocketReconnectCount+"次重连")
			state.webSocketReconnectCount += 1;
			// 判断是否到了最大重连次数 
			if (state.webSocketReconnectCount >= 4) {
				this.webSocketWarningText = "重连次数超限";
			  return false;
			}
			// 初始化
			that.commit('webSocketInit');
			// 每过 5 秒尝试一次，检查是否连接成功，直到超过最大重连次数
			let timer = setTimeout(() => {
				that.commit('webSocketReconnect');
				clearTimeout(timer);
			}, 5000);
		},
 
		// 发送ws消息
		webSocketSend(state, payload) {
			let that = this
			if (!state.socketTask) return
			console.log(JSON.stringify(payload))
			state.socketTask.send(JSON.stringify(payload))
		}
	},
	actions: {
		webSocketInit({commit},info) {
			console.log(info)
			commit('setuserId',info.userId)
			commit('settrfNo',info.trfNo)
			commit('webSocketInit')
		},
		webSocketSend({commit}, p) {
			commit('webSocketSend', p)
		},
		webSocketClose({commit}) {
			commit('closeWebSocket')
		}
	}
}
export default chat