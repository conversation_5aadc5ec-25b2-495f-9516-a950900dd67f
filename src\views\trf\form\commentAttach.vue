<template>
    <div id="commentAttach" class="commentAttach">
        <el-row>
            <el-col :span="12" v-if="showDownloadAll">
                <label class="attachment-title">
                    {{$t('attachment.title')}}
                </label>
                &nbsp;&nbsp;
                <el-button type="text"
                           @click="downloadAllFile">
                    {{$t('attachment.downloadAll')}}<i class="el-icon-download"></i>
                </el-button>
            </el-col>
            <el-col v-if="showUpload" :span="showDownloadAll?12:24" style="text-align: right">
                <el-button  v-if="isLoadUpload"  type="text" @click="uploadFile">
                    {{$t('attachment.upload')}} <i class="el-icon-upload2"></i>
                </el-button>
            </el-col>  
        </el-row>
        <el-table
            v-if="loadTable"
            :data="tableData"
            size="medium"
            fit
            max-height="500"
        >
            <el-table-column prop="fileName" :label="$t('comment.chatDialog.documentName')"></el-table-column>
            <el-table-column prop="fileType" :label="$t('comment.chatDialog.documentType')">
                <template slot-scope="{row}">
                    {{attachmentFileCodeMap[row.fileType] || getSDDefaultFileType()}}
                </template>
            </el-table-column>
            <el-table-column prop="createUser" :label="$t('comment.chatDialog.uploadedBy')"></el-table-column>
            <el-table-column prop="attachmentIssueDate"
                             :label="$t('comment.chatDialog.attachmentIssueDate')">
                <template slot-scope="{row}">
                    {{transTime(row.attachmentIssueDate)}}
                </template>
            </el-table-column>
            <el-table-column prop="createTime" :label="$t('comment.chatDialog.date')">
                <template slot-scope="{row}">
                    {{transTime(row.createTime)}}
                </template>
            </el-table-column>
            <el-table-column prop="id" :label="$t('comment.chatDialog.action')" width="80px">
                <template slot-scope="{row}">
                    <el-button type="text" icon="el-icon-download" @click="downloadFile(row)"></el-button>
                </template>
            </el-table-column>
        </el-table>

        <!--上传组件-->
        <sgs-batch-upload
                :key="Math.random()"
                append-to-body
                :systemID="1"
                :limit="uploadLimit"
                :handle-upload-success="uploadSuccess"
                ref="batchUpload"
                :accept="batchUploadObj.accept"
                :upload-url="batchUploadObj.uploadUrl"
                :handle-upload-error="()=>{}"
                :attachment-type-options="batchUploadObj.attachmentTypeOptionsForUpload"
                :attachment-type-default-value="batchUploadObj.attachmentTypeDefaultValue"
                :file-max-sizes="20">
        </sgs-batch-upload>

    </div>
</template>

<script>
    import {downLoadFile,batchDownloadFile,batchUploadReportAttachment} from "@/api/trf/trf";
    import {tzFormChina} from '@/util/datetimeUtils'
    import {queryDictionaryByLanguage} from "@/api/common";
    import {DictionaryEnums} from "../../../commons/enums/DictionaryEnums";
    import {mapGetters} from "vuex";
    import {LanguageEnums} from "../../../commons/enums/LanguageEnums";
    export default {
        name: "commentAttach",
        data() {
            return {
                loadTable:true,
                isLoadUpload:false,
                attachmentTypes:[],
                attachmentFileCodeMap:{},
                fileList:[],
                batchUploadObj:{
                    uploadUrl: '/api/sgsapi/FrameWorkApi/file/doUpload',
                    accept: '.txt,.ppt,.pptx,.xls,.xlsx,.doc,.docx,.png,.jpg,.jpeg,.pdf,.msg,.eml',
                    attachmentTypeOptions: [],
                    attachmentTypeOptionsForUpload: [],
                    attachmentTypeDefaultValue: '',
                },
            }
        },
        methods: {
            getSDDefaultFileType(){
                return this.attachmentFileCodeMap['SD']
            },
            transTime(time){
                if(!time){
                    return '';
                }
                return tzFormChina(time, 'YYYY-MM-DD');
            },
            downloadFile(att){
                let {fileUrl} = att;
                downLoadFile(fileUrl).then(res => {
                    let downloadURL = res.data.data;
                    window.open(downloadURL, '_blank')
                }, error => {
                    this.$message.error(this.$t('api.error'));
                });
            },
            downloadAllFile(){
                let cloudIDs = [];
                this.tableData.forEach(att=>{
                    if(att.fileUrl){
                        cloudIDs.push(att.fileUrl);
                    }
                })
                let param = {
                    systemId:2,
                    cloudIDs
                }
                batchDownloadFile(param,this.trf.trfNo,error => {
                    this.$message.error(this.$t('api.error'));
                })
            },
            uploadFile(){
                this.$refs.batchUpload.open();
            },
            uploadSuccess(data){
                if(data){
                    let attachmentType=data.attachmentType;
                    let fileList = [];
                    data.data.forEach(item =>{
                        const attachment = {
                            'attachmentId': item.cloudID,
                            'fileUrl': item.path,
                            'fileName': item.attachmentName+"."+item.suffixes,
                            'size': item.size,
                            'attachmentType': attachmentType,
                            'object': 'report',
                            'fileType': attachmentType,
                            'updateUser':this.userInfo.userName,
                            objectId:this.uploadObjectId
                        }
                        fileList.push(attachment);
                    });
                    batchUploadReportAttachment(fileList).then(res=>{
                        console.log("batchUploadReportAttachment res",res)
                        this.$emit('uploadSuccess')
                    },error=>{
                        this.$notify.error(this.$t('api.error'))
                    }).catch(e=>{
                        this.$notify.error(this.$t('api.error'))
                    })
                }
                this.$refs.batchUpload.close();
            },
            initUploadDictionary(){
                console.log("attachment")
                this.batchUploadObj.attachmentTypeOptions = [];
                this.batchUploadObj.attachmentTypeOptionsForUpload = [];
                //根据当前语言查询附件类型字典项数据
                let langaugeCode = 'EN';
                if(this.language=='zh-CN'){
                    langaugeCode='CHI'
                }
                let params = {"sysKeyGroup":"OrderAttachmentType","systemID":1,"languageCode":langaugeCode}
                queryDictionaryByLanguage(params).then(res=>{
                    let  attachmentTypes = res.data;
                    if(!attachmentTypes || attachmentTypes.length == 0){
                        return;
                    }
                    this.attachmentTypes = attachmentTypes;//赋值给全局一份，用于列表显示名称
                    attachmentTypes.forEach(item => {
                        let label = item.sysValue;
                        let value = item.sysKey;
                        const option = {
                            'label': label,
                            'value': value
                        }
                        this.attachmentFileCodeMap[value] = label;  // 建立key-value对，方便查询名称
                        this.batchUploadObj.attachmentTypeOptionsForUpload.push(option);      
                    })
                    debugger;
                    let values = DictionaryEnums.Attachment.CommentAttachmentType.items.map(i=>i.code);
                    let smartFileTyleList = this.batchUploadObj.attachmentTypeOptionsForUpload.filter(d=>values.includes(d.value));
                    this.batchUploadObj.attachmentTypeOptionsForUpload=smartFileTyleList;

                    this.attachmentFileCodeMap['Report'] = 'Report';
                    console.log("attachmentFileCodeMap",this.attachmentFileCodeMap);
                    this.batchUploadObj.attachmentTypeDefaultValue=smartFileTyleList[0].value;
                    this.isLoadUpload=true;
                    this.loadTable=false;
                    this.$nextTick(()=>{
                        this.loadTable=true;
                    })
                    
                });
                // DictionaryEnums.Attachment.CommentAttachmentType.items.forEach(item => {
                //     let label = item.enKey;
                //     let value = item.code;
                //     if (this.language === LanguageEnums.CN.name) {
                //         label = item.cnKey;
                //     }
                //     const option = {
                //         'label': label,
                //         'value': value
                //     }
                //     this.batchUploadObj.attachmentTypeOptionsForUpload.push(option);
                // })
            },
        },
        created() {
            this.initUploadDictionary();
        },
        mounted() {
        },
        computed:{
            ...mapGetters(["language","userInfo"]),
            
        },
        watch:{
            language:{
                immediate:false,
                handler(newV){
                    this.initUploadDictionary();
                }
            }
        },
        components: {},
        props:{
            trf:Object,
            tableData:[],
            showDownloadAll:{
                type:Boolean,
                default:false
            },
            showUpload:{
                type:Boolean,
                default:false
            },
            uploadLimit:{
                type:Number,
                default:1
            },
            uploadObjectId:{
                type:String,
                default:''
            }
        }
    }
</script>

<style scoped lang="scss">
    .commentAttach {
        padding-bottom: 20px;
        margin-top: 20px;
    }
    .attachment-title{
        font-size: large;
    }
</style>