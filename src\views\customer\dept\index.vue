<template>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="wrap-tab">
        <el-tab-pane name="dept"  :label="$t('department.title.tab')">
            <dept v-if="tabRefresh.dept"></dept>
        </el-tab-pane>
        <el-tab-pane name="beLong"  :label="$t('beLongTo.title')" lazy="true">
            <be-long v-if="tabRefresh.beLong" ></be-long>
        </el-tab-pane>
    </el-tabs>
</template>
<script>
    import {mapGetters} from "vuex";
    export default {
        components: {
            dept: resolve => require(['./dept'], resolve),
            BeLong: resolve => require(['./beLong'], resolve)
        },
        data(){
            return{
                name: "customer-dept",
                activeName: "dept",
                tabRefresh: {
                    dept: true,
                    beLong: false,
                },
            }
        },
        created() {
                this.activeName="dept";
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                   /* approveTab: this.vaildData(this.permission['sgs:customer:relationship:tab:approve'],false),
                    buyerTab: this.vaildData(this.permission['sgs:customer:relationship:tab:buyer'],false),
                    supplierTab: this.vaildData(this.permission['sgs:customer:relationship:tab:supplier'],false),
                    factoryTab: this.vaildData(this.permission['sgs:customer:tab:factory'],false),*/
                };
            }
        },
        methods: {
            handleClick(tab) {
                debugger;
                Object.keys(this.tabRefresh).forEach(item=>{
                    this.tabRefresh[item]=false;
                })
                this.tabRefresh[tab.name]=true;
            }
        }
    }
</script>

<style lang="scss" scoped>
.wrap-tab {
    ::v-deep .el-tabs__nav-wrap {
        &::after {
            position: inherit;
        }
    }
    ::v-deep .el-tabs__content {
        margin-top: -58px;
    }
}
@import '../tab.scss';
</style>