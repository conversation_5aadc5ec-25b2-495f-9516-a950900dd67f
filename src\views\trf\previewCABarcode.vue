<template>
  <div class="row" style="background-color: #ffffff">
    <div style="position:fixed; left: 85%; top: 160px;z-index:9999; width: 300px">
      <button class="btn sgs_btn" @click="$router.go(-1)">{{$t('operation.backToList')}}</button>
    </div>
    <div style="position:fixed; left: 85%; top: 200px;z-index:9999; width: 300px">
      <button  v-loading.fullscreen.lock="fullscreenLoading" class="btn sgs_btn" @click="getPdf('pdf')">{{$t('operation.downLoad')}}</button>
    </div>
    <div style="position:fixed; left: 85%; top: 240px;z-index:9999; width: 300px">
      <button  v-loading.fullscreen.lock="fullscreenLoading" class="btn sgs_btn" @click="downTrfBarcode('barcode')">{{$t('operation.downLoadBarcode')}}</button>
    </div>
    <div id="htmlView" v-html="htmlView"></div>
  </div>
</template>
<script>

  import {detailForPrint,viewPDF,downTrfPDF,downTrfBarcode1} from '@/api/trf/trf'
  import {fileExport,fileExportData} from '@/views/util'
  import html2Canvas from 'html2canvas'
  import JsPDF from 'jspdf'
  import {mapGetters} from "vuex";
  export default {
    name: 'PreviewQVC',
    data() {
      return {
        htmlView:"",
          title:"PDF",
          fullscreenLoading:false,
      }
    },
      computed: {
          ...mapGetters(["userInfo", "language"]),
      },
    created:function () {
        this.viewPDF();
        //this.title = this.userInfo.
    },

    methods: {
        viewPDF:function () {
            viewPDF(Object.assign({'id':this.$route.query.id})).then(res =>{
                this.htmlView = res.data
                console.log(res);
            })
        },
       download:function(blobUrl) {
    const a = document.createElement('a');
    a.style.display = 'none';
    a.download = '<文件名>';
    a.href = blobUrl;
    a.click();
    document.body.removeChild(a);
  },
      downTrfBarcode:function () {

        downTrfBarcode1(Object.assign({'id':this.$route.query.id})).then(res =>{


            const req = new XMLHttpRequest();
            req.open('GET', '<接口地址>', true);
            req.responseType = 'blob';
            req.setRequestHeader('Content-Type', 'application/json');
            req.onload = function() {
              const data = req.response;
              const a = document.createElement('a');
              const blob = new Blob([data]);
              const blobUrl = window.URL.createObjectURL(blob);
              download(blobUrl) ;
            };
            req.send('<请求参数：json字符串>');
          let data = res.data;
          let filename = "TRF0C00068.pdf"
          var blob = new Blob([data], {type:  'application/json'})
          if (typeof window.navigator.msSaveOrOpenBlob !== 'undefined') {
            // IE workaround for "HTML7007: One or more blob URLs were
            // revoked by closing the blob for which they were created.
            // These URLs will no longer resolve as the data backing
            // the URL has been freed."
            window.navigator.msSaveOrOpenBlob(blob, filename)
          } else {
            var blobURL = window.URL.createObjectURL(blob)
            var tempLink = document.createElement('a')
            tempLink.style.display = 'none'
            tempLink.href = blobURL
            tempLink.setAttribute('download', filename)

            // Safari thinks _blank anchor are pop ups. We only want to set _blank
            // target if the browser does not support the HTML5 download attribute.
            // This allows you to download files in desktop safari if pop up blocking
            // is enabled.
            if (typeof tempLink.download === 'undefined') {
              tempLink.setAttribute('target', '_blank')
            }

            document.body.appendChild(tempLink)
            tempLink.click()
            document.body.removeChild(tempLink)
            window.URL.revokeObjectURL(blobURL)
          }
        })
      },
      /*downTrfBarcode:function(){
              downTrfBarcode(Object.assign({'id':this.$route.query.id})).then(res =>{

        })
      },*/
        downTrfPDF:function () {
            downTrfPDF().then(res =>{
                fileExportData(res.data, "123123" + '.pdf')
                console.log(res);
                const content = res.data
                const blob = new Blob([content])
                const fileName = '导出信息.pdf'
                if ('download' in document.createElement('a')) {
                    const elink = document.createElement('a')
                    elink.download = fileName
                    elink.style.display = 'none'
                    elink.href = URL.createObjectURL(blob)
                    document.body.appendChild(elink)
                    elink.click()
                    URL.revokeObjectURL(elink.href) // 释放URL 对象
                    document.body.removeChild(elink)
                } else { // IE10+下载
                    navigator.msSaveBlob(blob, fileName)
                }
            })
        },
        getPdf(){
            this.fullscreenLoading = true;
            window.pageYOffset = 0;
            document.documentElement.scrollTop = 0
            document.body.scrollTop = 0
            const vue = this;
            setTimeout(() => {
                this.getPdfDown(vue);
            }, 500);
        },
        getPdfDown (vue) {
            var title = this.title;
            html2Canvas(document.querySelector('#htmlView'), {
                allowTaint: true
            }).then(function (canvas) {

                    let contentWidth = canvas.width
                    let contentHeight = canvas.height
                    let pageHeight = contentWidth / 592.28 * 841.89
                    let leftHeight = contentHeight
                    let position = 0
                    let imgWidth = 595.28
                    let imgHeight = 592.28 / contentWidth * contentHeight
                    let pageData = canvas.toDataURL('image/jpeg', 1.0)
                    let PDF = new JsPDF('', 'pt', 'a4')
                    if (leftHeight < pageHeight) {
                        PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
                    } else {
                        while (leftHeight > 0) {
                            PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                            leftHeight -= pageHeight
                            position -= 841.89
                            if (leftHeight > 0) {
                                PDF.addPage()
                            }
                        }
                    }
                    PDF.save(title + '.pdf')

                    vue.fullscreenLoading = false;
                }
            )
        },

    }
  }
</script>
<style scoped>
  body {
    font-family: Arial;
    font-size: 13px;
    line-height: 1.42857143;
  }

  .buyer-supplier-h5 {
    margin-top: 5px;
    margin-bottom: 10px;
    font-weight: 600;
  }

  .logo {
    width: 150px;
  }

  label {
    max-width: 100%;
    margin-bottom: 1px;
    font-size: 10px;
    font-weight: 600;
    cursor: default;
  }

  .inline {
    border-bottom-width: 0.2px;
    border-bottom-style: solid;
    border-bottom-color: black;
    font-size: 12px;
  }

  table {
    width: 100%;
    display: table;
    border-color: 1px solid black;
    border-collapse: collapse;
  }

  tbody {
    display: table-row-group;
    border-color: inherit;
  }

  tr {
    display: table-row;
    vertical-align: inherit;
    border-color: inherit;
  }

  th {
    font-weight: 600;
  }

  td, th {
    display: table-cell;
  }

  .table-bordered > tbody > tr > td,
  .table-bordered > tbody > tr > th,
  .table-bordered > tfoot > tr > td,
  .table-bordered > tfoot > tr > th,
  .table-bordered > thead > tr > td,
  .table-bordered > thead > tr > th {
    border: 0.2px solid black;
    padding: 2px;
  }

  p {
    margin: 2px;
    font-weight: 200;
  }

  @page {
    margin: 15px;
  }

  #testRequest > table {
    border: 1px !important;
  }
</style>
