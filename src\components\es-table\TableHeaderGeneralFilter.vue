<template>
    <div class="TableHeaderGeneralFilter" id="TableHeaderGeneralFilter">
        <el-row>
            <el-col>
                <el-form-item prop="queryValue">
                    <el-input
                        :placeholder="$t('trfList.query.searchFor')"
                        v-model="formData.queryValue"
                        :size="'small'"
                        @keyup.enter.native="handelSubmit"
                        clearable
                        @clear="handelSubmit"
                    >
                        <i
                            slot="prefix"
                            class="el-input__icon el-icon-search"
                            style="left: -10px; position: relative"
                        ></i>
                    </el-input>
                </el-form-item>
                <el-form-item prop="filterDate">
                    <el-date-picker
                        class="filter-date"
                        v-model="formData.filterDate"
                        :size="'small'"
                        type="daterange"
                        :picker-options="pickerOptions"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        range-separator="-"
                        :unlink-panels="true"
                        :clearable="true"
                        @change="changeFilterDate"
                        @focus="handleDatePickerOpen"
                        :start-placeholder="$t('trfList.query.selStartDate')"
                        :end-placeholder="$t('trfList.query.selEndDate')"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item class="btn-sort" prop="currentDate">
                    <span class="check-time-out-span">
                        <div
                            :class="[formData.isSubmissionTimeout === 1 ? 'check-time-out-button button-default-focus' : 'check-time-out-button button-default']"
                            @click="toggleTimeOut"
                        >
                            {{ $t("trfList.query.timeOutTrf") }}
                        </div>
                    </span>
                </el-form-item>
                <el-form-item style="margin-bottom: 8px" class="btn-sort">
                    <div class="sort-btn-out-div">
                        <el-dropdown @command="handlerSortBtnCommand">
                            <el-button class="btn-sort noborder">
                                {{$t( (commandBtn || defaultSortBtn).i18n) }}
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                        v-for="(btn, index) in sortBtns"
                                        :command="btn"
                                        :key="index"
                                >
                                    {{ $t(btn.i18n) }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <i v-if="commandBtn && !commandBtn.notSort" @click="sortTableData(commandBtn || defaultSortBtn)"
                            :class="(commandBtn || defaultSortBtn).sort ? 'el-icon-sort-down': 'el-icon-sort-up'">
                        </i>
                    </div>
                </el-form-item>
                <div class="operate-block" v-if="windowWidth > 1355">
                    <el-form-item>
                        <filter-save ref="filterSave"></filter-save>
                    </el-form-item>
                    <el-form-item>
                        <reset-button
                            :marginLeft="0"
                            @click="clear"
                        ></reset-button>
                    </el-form-item>
                    <slot name="operate-block-right"></slot>
                </div>
            </el-col>
        </el-row>
        <div class="filter-group">
            <table-header-tabs
                v-if="showTableFlag"
                ref="tableHeaderTabs"
                :dynamic-form="dynamicForm"
                :sort-by="sortBy"
                :order-by="orderBy"
                :columns="columns"
            ></table-header-tabs>
            <div class="operate-block" v-if="windowWidth < 1355">
                <el-form-item>
                    <filter-save ref="filterSave"></filter-save>
                </el-form-item>
                <el-form-item>
                    <reset-button
                        :marginLeft="0"
                        @click="clear"
                    ></reset-button>
                </el-form-item>
                <slot name="operate-block-right"></slot>
            </div>
        </div>
    </div>
</template>

<script>
import moment from "moment";
import filterSave from "./TableHeaderFilterSave";
import resetButton from "@/components/resetButton/resetButton.vue";
import tableHeaderTabs from "./TableHeaderTabs";

export default {
  name: "tableHeaderGeneralFilter",
  inject: ["size", "searchSubmit", "resetQueryForm", "loadFilterSuccess"],
  data() {
    return {
      dateFormat: "YYYY-MM-DD",
      filterDate: "",
      filterObj: {
        startDate: "",
        endDate: "",
        filterDate: [],
        queryValue: "",
        isSubmissionTimeout: 0,
        currentDate: -1,
      },
      checkedTimeOutTrf: 1,
      commandBtn: null,
        defaultSortBtn:{
            id: 0,
            i18n: "trfList.quickSorting",
            sort: false,
            notSort: true,
            prop: "",
        },
      sortBtns: [
        {
          id: 1,
          i18n: "trfList.submitDate",
          sort: false,
          prop: "trf_header_trfSubmissionDate",
        },
        {
          id: 2,
          i18n: "trfList.buyerReviewConclusion",
          sort: false,
          prop: "report_conclusion_customerReviewConclusion",
        },
        {
          id: 3,
          i18n: "trfList.reportIssuedTime",
          sort: false,
          prop: "report_header_approveDate",
        },
        // {
        //     id: 4,
        //     i18n: "trfList.trfNo",
        //     sort: false,
        //     prop: "trf_trfNo",
        // },
      ],

      isFromShortcut: false,
      currentDate: -1,
      selectedShortcut: "",
      windowWidth: window.innerWidth, // 跟踪屏幕宽度
    };
  },
  computed: {
    pickerOptions() {
      return {
        shortcuts: [
          {
            text: this.$t("datePicker.lastWeek"),
            onClick: (picker) => {
              this.selectedShortcut = this.$t("datePicker.lastWeek");
              picker.$emit("pick", this.formatDialogDate(1, "w", 0));
            },
          },
          {
            text: this.$t("datePicker.lastMonth"),
            onClick: (picker) => {
              this.selectedShortcut = this.$t("datePicker.lastMonth");
              picker.$emit("pick", this.formatDialogDate(1, "M", 1));
            },
          },
          {
            text: this.$t("datePicker.lastHalfYear"),
            onClick: (picker) => {
              this.selectedShortcut = this.$t("datePicker.lastHalfYear");
              picker.$emit("pick", this.formatDialogDate(6, "M", 2));
            },
          },
          {
            text: this.$t("datePicker.lastYear"),
            onClick: (picker) => {
              this.selectedShortcut = this.$t("datePicker.lastYear");
              picker.$emit("pick", this.formatDialogDate(12, "M", 3));
            },
          },
        ],
      };
    },
  },
  methods: {
    handleDatePickerOpen() {
      const shortcuts = document.querySelectorAll(".el-picker-panel__shortcut");
      this.checkDateMatchShortcut(this.formData.filterDate);
      shortcuts.forEach((item) => {
        if (
          item.textContent === this.selectedShortcut 
        ) {
          item.classList.add("selected-shortcut");
        } else {
          item.classList.remove("selected-shortcut");
        }
      });
    },
    clearShortCutClass() {
      this.selectedShortcut = "";
      const shortcuts = document.querySelectorAll(".el-picker-panel__shortcut");
      shortcuts.forEach((item) => {
        item.classList.remove("selected-shortcut");
      });
    },
    handlerMoreSearch() {
      this.$emit("moreSearch");
    },
    Search() {
      this.searchSubmit.fn();
    },
    handlerSortBtnCommand(command) {
      this.commandBtn = command;
      this.searchSubmit.sortByDefault(
        command.prop,
        command.notSort ? "" : command.sort ? "Desc" : "Asc"
      );
    },
    sortTableData(btn) {
      btn.sort = !btn.sort;
      this.sortBtns.forEach((b) => {
        if (b.id != btn.id) {
          b.sort = false;
        }
      });
      this.searchSubmit.sortByDefault(btn.prop, btn.sort ? "Desc" : "Asc");
    },
    changeFilterDate() {
      let newV = this.formData.filterDate;
      let startDate = "";
      let endDate = "";
      if (newV && newV.length == 2) {
        startDate = newV[0];
        endDate = newV[1];
      }
      if (!newV) {
        // 若日期为空，清除快捷日期选中状态
        this.selectedShortcut = "";
      }
      // 检查当前日期是否匹配快捷选项
      this.checkDateMatchShortcut(newV);

      // 区分点击的日期是否来自快捷方式
      if (this.isFromShortcut || this.formData.currentDate>-1) {
        this.$set(this.formData, "currentDate", this.currentDate);
      } else {
        this.$set(this.formData, "currentDate", -1);
        this.selectedShortcut = "";
      }
      this.isFromShortcut = false; // 重置标记
      this.searchSubmit.changeDateRange(true);
      this.$set(this.formData, "startDate", startDate);
      this.$set(this.formData, "endDate", endDate);
      this.handelSubmit();
    },
    checkDateMatchShortcut(selectedDates) {
      if (!selectedDates || selectedDates.length !== 2) {
        return false;
      }
      const [selectedStart, selectedEnd] = selectedDates.map(
        (date) => new Date(date)
      );
      const today = new Date();

      // 定义四个快捷日期的计算函数
      const getLastWeekRange = () => {
        const end = new Date(today);
        const start = new Date(today);
        start.setDate(today.getDate() - 7);
        return [start, end];
      };

      const getLastMonthRange = () => {
        const end = new Date(today);
        const start = new Date(today);
        start.setMonth(today.getMonth() - 1);
        return [start, end];
      };

      const getLastHalfYearRange = () => {
        const end = new Date(today);
        const start = new Date(today);
        start.setMonth(today.getMonth() - 6);
        return [start, end];
      };

      const getLastYearRange = () => {
        const end = new Date(today);
        const start = new Date(today);
        start.setFullYear(today.getFullYear() - 1);
        return [start, end];
      };

      // 存储四个快捷日期范围
      const shortcutRanges = [
        getLastWeekRange(),
        getLastMonthRange(),
        getLastHalfYearRange(),
        getLastYearRange(),
      ];
      // 遍历快捷日期范围进行比较
      for (let i = 0; i < shortcutRanges.length; i++) {
        const [start, end] = shortcutRanges[i];
        if (
          start.toDateString() === selectedStart.toDateString() &&
          end.toDateString() === selectedEnd.toDateString()
        ) {
          this.currentDate = i;
          this.selectedShortcut = this.$t(
            [
              "datePicker.lastWeek",
              "datePicker.lastMonth",
              "datePicker.lastHalfYear",
              "datePicker.lastYear",
            ][i]
          );
          return true;
        }
      }

      this.currentDate = -1;
      this.selectedShortcut = "";
      return false;
    },

    handelSubmit() {
      this.searchSubmit.fn();
    },
    async selectedDate(num, dateName, index, isCreate) {
      let endDate = moment().format(this.dateFormat);
      var startDate;
      if (dateName != "w") {
        //月份计算
        startDate = moment().subtract(num, "months").format(this.dateFormat);
      } else {
        //一周
        startDate = moment().subtract(num, dateName).format(this.dateFormat);
      }
      this.$set(this.formData, "startDate", startDate);
      this.$set(this.formData, "endDate", endDate);
      this.$set(this.formData, "filterDate", [startDate, endDate]);
      this.$set(this.formData, "currentDate", index);
      this.changeFilterDate();
      if (!isCreate) {
        this.handelSubmit();
      }
    },
    formatDialogDate(num, dateName, index) {
      let endDate = moment();
      var startDate;
      if (dateName != "w") {
        //月份计算
        startDate = moment().subtract(num, "months");
      } else {
        //一周
        startDate = moment().subtract(num, dateName);
      }
      this.currentDate = index;
      this.isFromShortcut = true;
      return [startDate.toDate(), endDate.toDate()];
    },
    async checkTimeOutHandle(val) {
      //this.formData.isSubmissionTimeout = val ? 1 : 0
      //await this.searchSubmit.fn();
      this.handelSubmit();
    },
    toggleTimeOut() {
      this.formData.isSubmissionTimeout =
        this.formData.isSubmissionTimeout === 1 ? 0 : 1;
      this.checkTimeOutHandle(this.formData.isSubmissionTimeout === 1);
    },
    clear() {
      this.$refs.filterSave.clearFilterObj();
      this.resetQueryForm();
    },
    handlerSort() {
      this.sortBtns.forEach((btn) => {
        btn.sort = btn.prop == this.sortBy;
      });
    },
    // 更新屏幕宽度
    updateWindowWidth() {
      this.windowWidth = window.innerWidth;
    },
  },
  mounted() {
    window.addEventListener("resize", this.updateWindowWidth);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateWindowWidth);
  },
  created() {
    //初始化当前页面的obj，方便双向绑定
    let {
      startDate,
      endDate,
      filterDate,
      queryValue,
      isSubmissionTimeout,
      currentDate,
    } = this.formData;
    if (startDate == undefined) {
      this.$set(this.formData, "startDate", "");
    }
    if (endDate == undefined) {
      this.$set(this.formData, "endDate", "");
    }
    if (filterDate == undefined) {
      this.$set(this.formData, "filterDate", []);
    }
    if (queryValue == undefined) {
      this.$set(this.formData, "queryValue", "");
    }
    if (isSubmissionTimeout == undefined) {
      this.$set(this.formData, "isSubmissionTimeout", 0);
    }
    if (currentDate == undefined) {
      this.$set(this.formData, "currentDate", -1);
    }
    if (startDate && endDate) {
      this.$set(this.formData, "filterDate", [startDate, endDate]);
    } else {
      if (!this.conditionFromView) {
        let start = moment().subtract(1, "months").format(this.dateFormat);
        this.$set(this.formData, "filterDate", [
          start,
          moment().format(this.dateFormat),
        ]);
        this.$set(this.formData, "startDate", start);
        this.$set(this.formData, "endDate", moment().format(this.dateFormat));
      }
    }
    if (currentDate > -1) {
      let num = 1;
      let dateName = "w";
      let index = currentDate;
      if (index >= 1) {
        num = (index - 1) * 6;
        num = num == 0 ? 1 : num;
        dateName = "M";
      }
      this.selectedDate(num, dateName, index, true);
    }
    this.loadFilterSuccess();
    this.handlerSort();
  },
  watch: {
      sortBy:{
          immediate:false,
          handler(newV,oldV){
              if(newV && !!this.sortBtns.find(btn=>btn.prop==newV)){
                 return;
              }
              this.commandBtn = null;
          }
      }
  },
  props: {
    language: String,
    sortBy: "",
    conditionFromView: false,
    formData: {
      type: Object,
      default: () => {
        return {
          startDate: "",
          endDate: "",
          filterDate: [],
          queryValue: "",
          isSubmissionTimeout: 0,
          currentDate: -1,
        };
      },
    },
    dynamicForm: {},
    orderBy: {
      type: String,
      default: "",
    },
    columns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showTableFlag: {
      type: Boolean,
      default: false,
    },
  },
  updated() {},
  components: {
    filterSave,
    resetButton,
    tableHeaderTabs,
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/unit.scss";

.TableHeaderGeneralFilter {
  .filter-group {
    display: flex;
    justify-content: space-between;
  }
  .operate-block {
    display: flex;
    align-items: center;
    display: inline-block;
    transform: translateY(-5px);
  }
  div.sort-btn-out-div {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding-right: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      cursor: pointer;
    }
    &:hover {
      color: #ff6600;
      border-color: #ffd1b3;
      background-color: #fff0e6;
    }
  }
    .btn-sort {
      &.noborder{
        border: none;
      }
    }
    .check-time-out-button{
        width: 100px;
        height: 32px;
        line-height: 30px;
        font-size: 12px;
        text-align: center;
        border-radius: 3px;
        cursor: pointer;
    }
}
</style>
