<template>
    <div>
        <div>
            <iframe style="width: 100%; height: 100vh; border: 0;" :src="this.testResultUrl">
                <p>您的浏览器不支持  iframe 标签。</p>
            </iframe>


<!--         <trf-list ref="trfList" @trfNoClick="handleTrfNoClick" :list-visible.sync="listVisible"></trf-list>-->
        </div>
    </div>
</template>
 
<script>
    import {testResultUrl} from '@/config/env';
    import {mapGetters} from "vuex";
    import { queryDataEntryUpdate } from '@/api/common/index';

    export default {
        name: "dataEntry",
        components: {
          TrfList: resolve => require(['@/views/dataEntry/list/TrfList'], resolve)
        },
        created() {
          this.init();

        },
        data() {
            return {
                listVisible:true,
                testResultUrl:'',
            }
        },
        computed: {
            ...mapGetters(["userInfo","permission"]),
            permissionList() {
                return {
                };
            }
        },
        watch: {},
        methods: {
          init(){
            //请求判断是否系统更新
            queryDataEntryUpdate().then(res => {
              if(res.status === 200 && res.data && res.data.code === 200) {
                debugger;
                if(res.data.data){//系统维护中
                  this.testResultUrl = "/#/systemUpdate"
                 // this.$router.push({path: '/systemUpdate', query: {}});
                  return false;
                }
                this.testResultUrl = testResultUrl+this.userInfo.accessToken;
              }
            });

          }
        },
    };
</script>
