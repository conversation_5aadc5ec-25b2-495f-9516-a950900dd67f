<template>
    <el-popover
            width="600"
            placement="bottom"
            trigger="click">
        <el-tabs tab-position="left" :value="tabValue" @tab-click="tabClickHandle">
            <el-checkbox-group :value="checkList" @input="changeHandle">
                <draggable :value="dffData" @input="val => dffData = val" @start="drag=true" ghost-class="ghost"
                           @end="drag=false">
                    <el-checkbox style="display: block; margin-left: 30px;"
                                 v-for="(dItem, dIndex) in dffData"
                                 :label="dItem.fieldCode"
                                 :key="dIndex+dItem.fieldCode">{{$t(dItem.displayName)}}
                    </el-checkbox>
                </draggable>
            </el-checkbox-group>
            <el-button type="primary" @click="saveSortSetting">{{$t('operation.confirm')}}</el-button>
        </el-tabs>
        <el-button slot="reference" icon="el-icon-menu"></el-button>
    </el-popover>
</template>

<script>
    import {listSetting, saveSetting} from '@/api/trf/trfListSetting'
    import {mapGetters} from 'vuex'

    export default {
        name: "TabSelect",
        components: {
            draggable: resolve => require(['vuedraggable'], resolve)
        },
        computed: {
            ...mapGetters({
                companyType: 'companyType'
            })
        },
        data() {
            return {
                tabValue: null,
                drag: true,
                dffData: this.getDefaultTemplate(),
                trfListSettings: [],
                checkList: []
            }
        },
        props: {
            trfTemplate: {
                type: Array,
                default: () => {
                    return []
                }
            }
        },
        methods: {
            async init(tabVal) {
                await this.tabClickHandle(tabVal)
                /*  if (tabVal === 'all') {
                    this.checkList = this.getDefaultTemplate(this.companyType).map(item => item.fieldCode)
                    // 获取要获取默认的 选中的
                  }*/
                this.$emit('updateVal', this.getValue(this.checkList))
            },
            async tabClickHandle(tab) {
                // 初始化自己设置的 排序规则
                await this.initTRFListSetting()
                /* this.checkList= []
                 // 设置当前 选中的 tab 值, dff template ID
                 if (tab === 'all') {
                     this.dffData = this.getDefaultTemplate(this.companyType)
                     this.tabValue = tab
                 } else {
                   let id = null
                   if (this.$lodash.isString(tab)) {
                     id = tab
                   } else {
                     id = tab.name
                   }
                   this.tabValue = id
                    this.sortDffData();*/
                /* const dffData = await getDff({id: id})
                 if (dffData.status === 200 && !this.$lodash.isEmpty(dffData.data.result))  {
                   let dffDataMap = this.$lodash.get(dffData, 'data.result', []).filter(item => item.displayInSystem.indexOf('2') >= 0).map(item => {
                     return {
                       displayName: item.dispalyName,
                       fieldCode: 'productInstance.'+this.$lodash.lowerFirst(item.fieldCode)
                     }
                   })

                   getDefaultTemplate(this.companyType).forEach(item => {
                     if (this.$lodash.findIndex(dffDataMap, {fieldCode: item.fieldCode}) === -1) {
                       dffDataMap.unshift(item)
                     }
                   })
                   this.dffData = dffDataMap
                   console.log(id)
                   // 初始化自己设置的 排序规则
                   await this.initTRFListSetting()
                   this.sortDffData()
                 }*/
                //}
            },
            changeHandle(selectArray) {
                this.checkList = selectArray
            },
            getValue(selectArrayStr = []) {
                let currentListSortArray = [], sortIndex = 1
                this.dffData.forEach((n) => {
                    if (selectArrayStr.indexOf(n.fieldCode) !== -1) {
                        currentListSortArray.push({
                            fieldCode: n.fieldCode,
                            displayName: n.displayName,
                            templateId: this.tabValue,
                            sortIndex: sortIndex
                        })
                        sortIndex++
                    }
                })
                return currentListSortArray
            },
            getDefaultTemplate() {
                const defaultTabTemplate = [
                    /* {displayName: this.$t('trfList.trfNo'), fieldCode: 'trfNo'},*/
                    {displayName: 'trfList.buyer', fieldCode: 'buyerCustomerGroupName'},
                    {displayName: 'trfList.applicaton', fieldCode: 'customerNameEn'},
                    {displayName: 'trfList.submitDate', fieldCode: 'trfSubmissionDate'},
                    {displayName: 'trfList.sampleDate', fieldCode: 'sampleReceiveDate'},
                    {displayName: 'trfList.contactName', fieldCode: 'contactName'},
                    {displayName: 'trfList.labName', fieldCode: 'labName'},
                    {displayName: 'trfList.productDescription', fieldCode: 'productDescription'},
                    {displayName: 'trfList.styleNo', fieldCode: 'styleNo'},
                    {displayName: 'trfList.poNo', fieldCode: 'poNo'},
                    {displayName: 'trfList.articleNo', fieldCode: 'articleNo'},
                    {displayName: 'trfList.reportNo', fieldCode: 'newReportNo'},
                    {displayName: 'trfList.conclusion', fieldCode: 'reportConclusion'}
                ]
                return defaultTabTemplate
            },

            async initTRFListSetting() {
                // 初始化配置信息
                let res = await listSetting();
                if (res.data.code !== 200) {
                    return
                }
                let result = this.$lodash.get(res, 'data.data', []);
                if (result.length === 0) {
                    this.checkList = this.getDefaultTemplate().map(item => item.fieldCode)
                } else {
                    this.checkList = this.$lodash.orderBy(result, ['sortIndex'], ['asc']).map(item => item.fieldCode) || []
                }
            },
            sortDffData() {
                this.dffData = this.$lodash.orderBy(this.dffData.map(item => {
                    if (this.checkList.includes(item.fieldCode)) {
                        item['sortIndex'] = this.checkList.indexOf(item.fieldCode) + 1
                    }
                    return item
                }), ['sortIndex'], ['asc'])
            },
            saveSortSetting() {
                if (this.checkList.length === 0) {
                    return false
                }
                let currentListSortArray = this.getValue(this.checkList)
                //saveSetting(currentListSortArray)
                saveSetting(currentListSortArray).then(res => {
                    console.log("TRF setting 保存结果=>" + res.data.code)
                });
                this.$emit('updateVal', currentListSortArray)
            }
        }
    }
</script>

<style scoped>

</style>
