<template>
    <div class="sgs_wrap">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins" style="padding-bottom: 0px;">
                        <div class="ibox-content" style="padding-bottom: 0px;">

                            <div class="row">
                                <div class="col-sm-6">
                                    <h2 style="margin-top: 0">{{$t('dataAnalytics.overall')}}</h2>
                                </div>
                                <div class="col-sm-3 text-right" v-if="false">
                                  <el-select v-model="productLineCode" clearable filterable
                                             @change="productLineChange" style="width: 30%;">
                                    <el-option v-for="(productLine,index) in productLineCodes"
                                               :label="productLine.key"
                                               :value="productLine.value"></el-option>
                                  </el-select>
                                </div>
                                <div class="col-sm-6 text-right">
                                    <el-date-picker
                                        style="float: right; margin-top: -15px; width: 272px;"
                                        class="time-select"
                                        @change="rangeChange"
                                        @focus="handleDateFocus"
                                        v-model="valueData"
                                        type="monthrange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        :start-placeholder="$t('datePicker.startTime')"
                                        :end-placeholder="$t('datePicker.endTime')"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="pickerOptionsMonth">
                                    </el-date-picker>
                                </div>
                            </div>
                            <!-- <div class="row" style="margin-bottom: 30px">
                                <SliderDate :value-data="valueData" @input="rangeChange"/>
                            </div> -->

                            <div class="row">
                                <div class="col-lg-12">
                                    <div style="overflow-y:auto;margin-bottom: 20px;margin-top: 15px;">
                                        <div id="overallPie" style="height:500px;" class="col-lg-8">
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row">
                                <div class="col-lg-12">
                                    <h4>{{$t('dataAnalytics.overallTestByCountry')}}</h4>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12" v-for="item in countryArray">
                                    <div style="overflow-y:auto;margin-bottom: 20px;margin-top: 15px;">
                                        <div :id="name+'Bar'" style="height:200px;" class="col-lg-3"
                                             v-for="name in item">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    import moment from 'moment'
    import {mapGetters} from 'vuex'
    import {executiveSummaryStatistics, executiveSummaryStatisticsExcel} from '@/api/statistics/cistatistics'
    import * as utils from '@/util'
    import {saveLog} from "@/api/common/index";


    export default {
        data() {
            let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD') //moment().format('YYYY-MM-DD');
            return {
                isIndeterminate: true,
                checkAll: false,
                checkList: [
                    "ProductDescription",
                    "ProductCategory1",
                    "StyleNo",
                    "ProductColor",
                    "CountryOfDestination",
                    "ProductionStage",
                ],
                selectedDff: [],
                tabValue: null,
                statisticsData: {},
                countryArray: [],
                valueData: [startDate, endDate],
                queryParam: {
                    startDate: startDate,
                    endDate: endDate,
                    buyerGroupCode: null,
                    templateId: null,
                    productLineCode:null
                },
                selectEchats: {},
                echartsBarTitle: "",
                pickerOptionsMonth: {
                    shortcuts: [{
                    text: this.$t('datePicker.lastWeek'),
                    onClick(picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                    }, {
                    text: this.$t("datePicker.lastMonth"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 1);
                        picker.$emit('pick', [start, end]);
                    }
                    }, 
                    // {
                    //   text: '今年至今',
                    //   onClick(picker) {
                    //     const end = new Date();
                    //     const start = new Date(new Date().getFullYear(), 0);
                    //     picker.$emit('pick', [start, end]);
                    //   }
                    // }, 
                    {
                    text: this.$t("datePicker.lastHalfYear"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        picker.$emit('pick', [start, end]);
                    }
                    }, {
                    text: this.$t("datePicker.lastYear"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 12);
                        picker.$emit('pick', [start, end]);
                    }
                    }]
                },
                productLineCode:"",
                productLineCodes:[
                  {key:'SL',value:'SL'},
                  {key:'HL',value:'HL'}
                ]
            }
        },
        watch: {
            "$i18n.locale": function () {
                this.initEchart()
            },
        },

        computed: {
            ...mapGetters({
                buyer: 'buyer',
                customer: 'customer',
                trfTemplate: 'trfTemplate',
                dffHeadData: 'dffHeadData',
                companyType: 'companyType',
                userInfo:'userInfo'
            })
        },
        components: {
            SliderDate: resolve => require(['@/components/statistics/SliderDate'], resolve),
        },
        beforeRouteEnter(to, from, next) {
            next(async vm => {
                await vm.initData();
                vm.saveLog();
            })
        },
        methods: {
            handleDateFocus(e) {
                let nodes = document.querySelectorAll('.el-picker-panel__shortcut')
                let lang = ["datePicker.lastWeek", "datePicker.lastMonth", "datePicker.lastHalfYear", "datePicker.lastYear"]
                nodes.forEach((item, index) => {
                    item.innerText = this.$t(lang[index%4])
                })
            },
            initPieOption(textTitle, passData, fialData, seeResultData, legendShow) {
                let pieData = ['PASS', 'FAIL', 'SEE RESULT']
                // let pieData = [ this.$t('dataAnalytics.pass'), this.$t('dataAnalytics.fail'), this.$t('dataAnalytics.seeResult')]

                let pieColor = ['#92D050', '#FF0000', '#adadad']
                let pieOption = {
                    title: {
                        text: textTitle,
                        x: 'center'
                    },
                    legend: {
                        show: legendShow,
                        orient: 'vertical',
                        x: 'left',
                        data: pieData,
                        bottom: '25'
                    },
                    color: pieColor,
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    series: [
                        {
                            name: this.$t('dataAnalytics.count'),
                            type: 'pie',
                            radius: ['30%', '60%'],
                            label: {
                                normal: {
                                    position: 'inner',
                                    formatter: '{d}%'
                                }
                            },
                            data: [
                                {value: passData, name: this.$t('dataAnalytics.pass')},
                                {value: fialData, name: this.$t('dataAnalytics.fail')},
                                {value: seeResultData, name: this.$t('dataAnalytics.seeResult')}
                            ]
                        }
                    ]
                }
                return pieOption
            },


            saveLog() {
                saveLog("executiveSummaryLog").then(res => {

                })
            },
            downExcel() {
                if (this.companyType === 5) {
                    this.queryParam.buyerGroupCode = this.customer.customerGroupCode
                    this.queryParam.dataCustomerUsage = 'agent'
                }

                let excelMapperSuffix = [
                    {displayName: 'Supplier Name', fieldCode: 'applicantName', sortIndex: -15},
                    {displayName: 'Overall Conclusion', fieldCode: 'overallConclusion', sortIndex: -17},
                    {displayName: 'Lab Name', fieldCode: 'lab', sortIndex: -12},
                    {displayName: 'Service Country', fieldCode: 'LabCountryName', sortIndex: -11},
                    {displayName: 'Service Location', fieldCode: 'location', sortIndex: -10},
                    {displayName: 'Test Item', fieldCode: 'testLineEvaluation', sortIndex: -9},
                    {displayName: 'Test Item Result', fieldCode: 'Conclusion', sortIndex: -8},
                    {displayName: 'Component Name', fieldCode: 'ComponentName', sortIndex: -6},
                    {displayName: 'Component Name2', fieldCode: 'ComponentName2', sortIndex: -5},
                    {displayName: 'Component Name3', fieldCode: 'ComponentName3', sortIndex: -4},
                    {displayName: 'Material', fieldCode: 'Material', sortIndex: -3},
                    {displayName: 'Color', fieldCode: 'Color', sortIndex: -2},
                    {displayName: 'Remark', fieldCode: 'Remark', sortIndex: -1},
                ]
                let excelMappers = [
                    {displayName: 'TRF Number', fieldCode: 'TRFNo', sortIndex: -20},
                    {displayName: 'TRF Submitted Date', fieldCode: 'caseCreatedDate', sortIndex: -19},
                    {displayName: 'Report Number', fieldCode: 'reportNo', sortIndex: -18},
                    {displayName: 'Test Start Date', fieldCode: 'reportStartDate', sortIndex: -16},
                    {displayName: 'Report Issue Date', fieldCode: 'approverDate', sortIndex: -15},
                ].concat(this.selectedDff).concat(excelMapperSuffix)

                excelMappers = excelMappers.map((item, i) => {
                    item.sortIndex = i;
                    return item;
                })

                executiveSummaryStatisticsExcel(this.$lodash.assign({
                    dataCustomerID: this.queryParam.buyerGroupCode,
                    excelMappers: excelMappers
                }, this.queryParam))
                    .then(res => {
                        utils.fileExportData(res.data, 'SummaryStatisticsExcel.xlsx')
                    })
            },
            changeHandle(array) {
                let filterArray = this.$lodash.filter(this.dffHeadData, function (o) {
                    return array.indexOf(o.fieldCode) != -1;
                })
                let sortIndex = 1
                this.selectedDff = filterArray.map(item => {
                    let o = {}
                    this.$set(o, 'displayName', item.dispalyName || '')
                    this.$set(o, 'fieldCode', item.fieldCode || '')
                    this.$set(o, 'sortIndex', sortIndex)
                    sortIndex++
                    return o
                })
            },
            handleCheckAllChange(val) {
                this.checkList = val ? this.dffHeadData.map(item => {
                    return item.fieldCode
                }) : [];
                this.changeHandle(this.checkList)
                this.isIndeterminate = false;
            },
            async tabClickHandle(tab) {
                this.isIndeterminate = true
                this.checkAll = false
                this.checkList = [
                    "ProductDescription",
                    "ProductCategory1",
                    "StyleNo",
                    "ProductColor",
                    "CountryOfDestination"
                ]
                await this.$store.dispatch('dffHeadData', {id: tab.name})
                this.queryParam.templateId = tab.name

                this.changeHandle(this.checkList)
            },
            async initData() {
                let userInfo = localStorage.getItem("SGS-userInfo")
                let customerGroupCode = JSON.parse(userInfo).content.customerGroupCode;
                if (customerGroupCode) {
                    this.queryParam.buyerGroupCode = customerGroupCode;
                }

                if (this.$lodash.isEmpty(this.echartsBarTitle)) {
                    this.echartsBarTitle = moment(this.queryParam.endDate).format("YYYY-MM-DD") + ' - ' + moment(this.queryParam.endDate).format("YYYY-MM-DD");
                }

                if (this.companyType === 4 && !this.$lodash.isEmpty(this.$route.query.customerGroupCode)) {
                    this.$store.commit('SET_BUYER', {
                        customerGroupCode: this.$route.query.customerGroupCode,
                        customerGroupId: this.$route.query.customerGroupId
                    });
                    this.queryParam.buyerGroupCode = this.$route.query.customerGroupCode
                    this.queryParam.customerUsage = 'buyer'
                }

                if (this.companyType === 1) {
                    this.$store.commit('SET_BUYER', this.customer);
                    this.queryParam.buyerGroupCode = this.customer.customerGroupCode
                    this.queryParam.customerUsage = 'buyer'
                }

                if (this.companyType === 5) {
                    this.queryParam.agentGroupCode = this.customer.customerGroupCode
                    this.queryParam.customerUsage = 'agent'
                }
                debugger;
                let productLineCode=this.userInfo.productLineCode;
                if(productLineCode==='all'){
                  productLineCode='';
                }
                this.queryParam.productLineCode = productLineCode
                this.$store.commit('SET_CLEAR_DFF');
                await this.$store.dispatch('getDffTemplate')
                //this.tabValue = this.trfTemplate[0].templateID
                //this.tabClickHandle({name: this.trfTemplate[0].templateID})
                //this.queryParam.templateId = this.trfTemplate[0].templateID
                //let {data} = await executiveSummaryStatistics(this.queryParam);

                executiveSummaryStatistics(this.queryParam).then(res => {
                    console.log(res);
                    this.statisticsData = res.data.data
                    var countryArray = []
                    this.$lodash.forEach(this.statisticsData.countryAndPassFail, item => {
                        if (item.DataCountryName && this.$lodash.indexOf(countryArray, item.DataCountryName) === -1) {
                            countryArray.push(item.DataCountryName)
                        }
                    })
                    let chunkCountryArray = this.$lodash.chunk(countryArray, 4)
                    this.countryArray = chunkCountryArray
                    this.$nextTick(() => {
                        this.initEchart()
                    })

                })

            },
            async rangeChange(data) {
              let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                  endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD')
                if(data){
                  startDate = moment(data[0]).format("YYYY-MM-DD")
                  endDate = moment(data[1]).format("YYYY-MM-DD")
                }
                this.$set(this.queryParam, 'startDate', startDate);
                this.$set(this.queryParam, 'endDate', endDate);
                this.valueData = data
                this.$set(this, 'echartsBarTitle', moment(data[0]).format("YYYY-MM-DD") + ' - ' + moment(data[1]).format("YYYY-MM-DD"))
                await this.initData();
            },
            getCurrentMonthFirst(date) {
                var startdate = new Date(date);
                startdate.setDate(1);
                return moment(startdate).format("YYYY-MM-DD");
            },
            //获取所选月份的最后一天
            getCurrentMonthLast(date) {
                var endDate = new Date(date);
                var month = endDate.getMonth();
                var nextMonth = month;
                var nextMonthFirstDay = new Date(endDate.getFullYear(), nextMonth, 1);
                var oneDay = 1000 * 60 * 60 * 24;
                var newEndDate = new Date(nextMonthFirstDay - oneDay);
                return moment(newEndDate).format("YYYY-MM-DD");
            },
            //修改echars国际化
            initEchart() {
                const $l = this.$lodash
                const self = this

                var categoryY = []
                var countryX = []
                $l.forEach(this.statisticsData.categoryAndCountry, (item) => {
                    if ($l.indexOf(categoryY, item.DataCategory) === -1) {
                        categoryY.push(item.DataCategory)
                    }
                    if ($l.indexOf(countryX, item.DataCountryName) === -1) {
                        countryX.push(item.DataCountryName)
                    }
                })

                var dataSerialArray = []
                var color = ['#D2B48C', '#CD919E', '#BFEFFF', '#68838B', '#9AC0CD', '#AB82FF', '#98FB98', '#8B6969', '#A4D3EE', '#C6E2FF']
                $l.forEach(categoryY, (item, i) => {
                    var YDataObj = {
                        name: item,
                        type: 'bar',
                        stack: 'total',
                        barWidth: 30, // 柱图宽度
                        itemStyle: {
                            normal: {
                                color: color[i]
                            }
                        },
                        data: []
                    }
                    $l.forEach(countryX, xItem => {
                        var count = $l.result($l.find(self.statisticsData.categoryAndCountry, {
                            DataCategory: item,
                            DataCountryName: xItem
                        }), 'countValue', 0)
                        YDataObj.data.push(count)
                    })
                    dataSerialArray.push(YDataObj)
                })


                var categoryArray = []
                var categoryFailArray = []
                var categoryPassArray = []
                let categorySeeResultArray = []
                $l.forEach(this.statisticsData.categoryAndPassFail, (item) => {
                    if ($l.indexOf(categoryArray, item.DataCategory) === -1) {
                        categoryArray.push(item.DataCategory)
                    }
                })

                $l.forEach(categoryArray, item => {
                    let failCount = $l.result($l.find(this.statisticsData.categoryAndPassFail, {
                        DataCategory: item,
                        DataResult: this.$t('dataAnalytics.fail')
                    }), 'countValue', 0) || 0
                    let passCount = $l.result($l.find(this.statisticsData.categoryAndPassFail, {
                        DataCategory: item,
                        DataResult: this.$t('dataAnalytics.pass')
                    }), 'countValue', 0) || 0
                    let seeResultCount = $l.result($l.find(this.statisticsData.categoryAndPassFail, {
                        DataCategory: item,
                        DataResult: this.$t('dataAnalytics.seeResult')
                    }), 'countValue', 0) || 0
                    categoryFailArray.push(failCount)
                    categoryPassArray.push(passCount)
                    categorySeeResultArray.push(seeResultCount)
                })
                // TODO 第一个饼状图
                var overallPieChart = echarts.init(document.getElementById('overallPie'))
                this.$once('hook:beforeDestroy', function () {
                    // echarts.dispose(overallPieChart)
                    overallPieChart.clear()
                })
                let passCount = $l.result($l.find(this.statisticsData.allPassAndFail, {DataResult: 'Pass'}), 'countValue', 0)
                let failCount = $l.result($l.find(this.statisticsData.allPassAndFail, {DataResult: 'Fail'}), 'countValue', 0)
                let seeResultCount = $l.result($l.find(this.statisticsData.allPassAndFail, {DataResult: 'See Result'}), 'countValue', 0)

                // overal permance 设置项
                //let pieData = ['PASS', 'FAIL', 'SEE RESULT']
                let pieData = [this.$t('dataAnalytics.pass'), this.$t('dataAnalytics.fail'), this.$t('dataAnalytics.seeResult')]
                let pieColor = ['#92D050', '#FF0000', '#adadad']
                let pieOption = {
                    title: {
                        text: this.$t('dataAnalytics.overall'),
                        x: 'center'
                    },
                    legend: {
                        show: true,
                        orient: 'vertical',
                        x: 'left',
                        data: pieData
                    },
                    color: pieColor,
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },

                    series: [
                        {
                            name: this.$t('dataAnalytics.count'),
                            type: 'pie',
                            radius: ['30%', '60%'],
                            label: {
                                normal: {
                                    position: 'inner',
                                    formatter: '{d}%'
                                }
                            },
                            data: [
                                {value: passCount, name: this.$t('dataAnalytics.pass')},
                                {value: failCount, name: this.$t('dataAnalytics.fail')},
                                {value: seeResultCount, name: this.$t('dataAnalytics.seeResult')}
                            ]
                        }
                    ]
                }


                overallPieChart.setOption(pieOption)

                $l.forEach(self.countryArray, item => {
                    $l.forEach(item, countryName => {
                        let failCount = $l.result($l.find(this.statisticsData.countryAndPassFail, {
                            DataCountryName: countryName,
                            DataResult: 'Fail'
                        }), 'countValue', 0)
                        let passCount = $l.result($l.find(this.statisticsData.countryAndPassFail, {
                            DataCountryName: countryName,
                            DataResult: 'Pass'
                        }), 'countValue', 0)
                        let seeResultCount = $l.result($l.find(this.statisticsData.countryAndPassFail, {
                            DataCountryName: countryName,
                            DataResult: 'See Result'
                        }), 'countValue', 0)


                        // 全局变量
                        var chinaPieChart = echarts.init(document.getElementById(countryName + 'Bar'))
                        this.$once('hook:beforeDestroy', function () {
                            // echarts.dispose(chinaPieChart)
                            chinaPieChart.clear()
                        })
                        var ChinaPieoption = this.initPieOption(countryName, passCount, failCount, seeResultCount, true)
                        chinaPieChart.setOption(ChinaPieoption)

                    })
                })
            },
            async productLineChange(value){
              await this.initData();
            }
        }
    }
</script>

<style scoped>
    .line {
        display: block;
    }

    .line + .el-checkbox {
        margin-left: 0px;
    }
</style>
