// 导入dayjs及两个插件
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
//import tz from 'dayjs/plugin/timezone';

import {objectIsNull} from "./validate.ts";


// 继承插件能力
dayjs.extend(utc)
dayjs.extend(timezone)

export function tzFormChina(dateTime:any, format:string) {
    if (objectIsNull(format)) {
        format = 'YYYY-MM-DD HH:mm:ss';
    }
    // 本地和utc差
    const os = dayjs().utcOffset()
    // 东8和utc差 8*60
    const df = os - 8 * 60
    const newDateTime = dayjs(dateTime).add(df, 'm').format(format)
    // console.log("转换后：" + newDateTime)
    // console.info("转换前：" + dayjs(newDateTime).utc().utcOffset(8).format(format));
    return newDateTime;
}

export function tzToChina(dateTime:any, format:string) {
    // console.log('转为北京时间，转换前时间=='+dateTime);
    if (objectIsNull(format)) {
        format = 'YYYY-MM-DD HH:mm:ss';
    }
    const newDateTime = dayjs(dateTime).utc().utcOffset(8).format(format)
    // console.log('转为北京时间，转换后时间=='+newDateTime);
    return newDateTime
}


