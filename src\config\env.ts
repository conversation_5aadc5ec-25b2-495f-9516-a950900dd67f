// 配置编译环境和线上环境之间的切换

// 定义 baseUrl 类型为字符串
let baseUrl: string = ''

// 定义 scmBaseUrl 类型为字符串
let scmBaseUrl: string = ''

// 定义 currentEnv 类型为字符串
let currentEnv: string = 'uat'

// 定义 env 类型为字符串，用于接收环境变量
const env: string = import.meta.env.MODE
let inspectionUrl = ''
// 根据不同的环境变量设置不同的配置
if (env === 'dev') {
  baseUrl = '' // 开发环境地址
  currentEnv = 'uat'
  scmBaseUrl = 'http://127.0.0.1:9573/#'
   inspectionUrl = 'https://newinspection-uat.sgsonline.com.cn/'
   
} else if (env === 'production') {
  baseUrl = '' // 生产环境地址
  currentEnv = 'production'
  scmBaseUrl = 'https://plus.sgsmart-online.com/#'
  inspectionUrl = 'https://newinspection.sgsonline.com.cn/'
} else if (env === 'uat') {
  baseUrl = '' // 测试环境地址
  currentEnv = 'uat'
  scmBaseUrl = 'https://uat.sgsmart-online.com/#'
  inspectionUrl = 'https://newinspection-uat.sgsonline.com.cn/'
} else if (env === 'test') {
  baseUrl = '' // 测试环境地址
  currentEnv = 'test'
  scmBaseUrl = 'https://test.sgsmart-online.com/#'
   inspectionUrl = 'https://newinspection-uat.sgsonline.com.cn/'
}

// 导出配置项
export { baseUrl, scmBaseUrl, env, currentEnv,inspectionUrl }
