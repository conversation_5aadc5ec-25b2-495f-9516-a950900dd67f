<template>
    <div class="policy" id="policy">
        <el-tabs v-model="conditionActiveName">
            <el-tab-pane :label="$t('login.serviceTrems')" name="tc">

            </el-tab-pane>
            <el-tab-pane :label="$t('agreement.privacyPolicy')" name="pp" :disabled="!finishTc && !justOpenPolicy">
            </el-tab-pane>
        </el-tabs>
        <div v-if="conditionActiveName=='tc'">
            <div id="tc-modal-body" class="tc-modal-body">
                <tc-content></tc-content>
            </div>
            <el-row style="padding-top: 20px">
                <el-col style="text-align: center">
                    <el-button :disabled="tcNotRead" type="primary" @click="agree(1)">{{$t('login.agreedBtn')}}</el-button>
                </el-col>
            </el-row>
        </div>
        <div v-if="conditionActiveName=='pp'">
            <div id="policy-modal-body" class="policy-modal-body">
                <pp-content></pp-content>
            </div>
            <el-row style="padding-top: 20px">
                <el-col style="text-align: center">
                    <el-button :disabled="ppNotRead" type="primary" @click="agree(2)">{{$t('login.agreedBtn')}}</el-button>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
    import tcContent from "./policy/tcContent";
    import ppContent from "./policy/ppContent";
    export default {
        name: "policy",
        provide(){
            return{
                calculateHeight:this.calculateDivHeight
            }
        },
        data() {
            return {
                conditionActiveName:'tc',
                tcNotRead:true,
                ppNotRead:true,
                finishTc:false,
                finishPP:false,
            }
        },
        methods: {
            agree(setup){
                if(setup-0==1){
                    this.conditionActiveName='pp';
                    this.finishTc = true;
                }
                if(setup-0==2){
                    this.finishPP = true;
                    this.$emit("agree",this.finishTc && this.finishPP)
                }
            },
            calculateDivHeight(name){
                if(name=='tc'){
                    let tc = document.getElementById("tc-modal-body");
                    if(tc.clientHeight == tc.scrollHeight){
                        this.tcNotRead = false;
                    }
                    tc.addEventListener('scroll',()=>{
                        if(tc.scrollTop + tc.clientHeight +4 >= tc.scrollHeight){
                            this.tcNotRead = false;
                        }
                    });
                }
                if(name=='pp'){
                    let pp = document.getElementById("policy-modal-body");
                    //内容很少，不出现滚动条的清空下，按钮直接可点击
                    if(pp.clientHeight == pp.scrollHeight){
                        this.ppNotRead = false;
                    }
                    pp.addEventListener('scroll',()=>{
                        if(pp.scrollTop + pp.clientHeight +4 >= pp.scrollHeight){
                            this.ppNotRead = false;
                        }
                    })
                }
            }
        },
        mounted() {
        },
        created() {

        },
        watch: {
            tabsIndex:{
                immediate:true,
                handler(newV,oldV){
                    this.conditionActiveName = newV==1?'tc':'pp';
                }
            }
        },
        computed: {},
        props: {
            justOpenPolicy:{
              type:Boolean,
              default(){
                  return false
              }
            },
            tabsIndex:{
                type:Number,
                default(){
                    return 1
                }
            }
        },
        updated() {
        },
        components: {tcContent,ppContent}
    }
</script>

<style scoped lang="scss">
    .policy {
        .tc-modal-body,.policy-modal-body{
            height: 600px;
            width: 100%;
            overflow-y: auto;
        }
    }
</style>