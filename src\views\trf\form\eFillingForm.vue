<template>
    <div>
        <el-row type="flex" justify="space-between">
            <slot name="header"></slot>
        </el-row>
        <el-collapse v-model="activeNames">
      <el-collapse-item   name="1">
        <template slot="title">
          <h3 style="font-weight: bold">{{ formLab1 }}</h3>
        </template>
        <div class="form-content">
          <el-card>
            <div class="form-inner">
              <avue-form :option="formOption1Com" v-model="newFormModel" ref="form1">
                <template slot="buyerCustomerId" slot-scope="{dic,row}">

  <el-select v-model="newFormModel.buyerCustomerId" clearable filterable :filter-method="filterBuyerCust" style="width:100%" @change="handleBuyerChange($event,dic)" :placeholder="`${$t('work.cpscTrfInfo.placeholder.select')+$t('work.cpscTrfInfo.column.customerName')}`">
    <el-option v-for="(item) in dic" :key="index" :label="item.bossName" :value="item.id"></el-option>
  </el-select>
</template>
              </avue-form>
            </div>
          </el-card>
      </div>
        </el-collapse-item>
        <el-collapse-item   name="2">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab3 }}</h3>


        </template>
        <div class="form-content">
          <el-card>
            <div class="form-inner">
            <avue-form :option="formOption3Com" v-model="newFormModel" ref="form3"></avue-form>
        </div>
          </el-card>



      </div>
        </el-collapse-item>

        <el-collapse-item   name="3">
        <template slot="title">


          <h3 style="font-weight: bold">Product Info</h3>


        </template>
        <div class="form-content">
          <el-card v-for="(item,index) in productInfoList" :key="index">
            <div class="form-inner" >
              <el-row type="flex" justify="end">
                <el-button type="text" @click="addProductInfo(item)" >{{ $t('crud.copyBtn') }}</el-button>
                <el-button v-if="index !=0" type="text" @click="productInfoList.splice(index,1)" >{{ $t('crud.cancelBtn') }}</el-button>
              </el-row>
               <avue-form :option="formOption5Com" v-model="productInfoList[index]" ref="crud" :ref="`form1${index}`">
                <template slot="productIdType" slot-scope="{row,dic,size,label}">
      <el-select :size="size" v-model="row.productIdType" :placeholder="label" clearable filterable :disabled="row.primaryId==='yes' &&copyType==='edit'">
        <el-option v-for="(item,index) in dic" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </template>
    <template slot="productId" slot-scope="{row,size,label}" >
      <el-input :size="size" :placeholder="label" v-model="row.productId" clearable :disabled="row.primaryId==='yes'&&copyType==='edit'"></el-input>
    </template>
    <template slot="primaryId" slot-scope="scope">
      <el-radio-group :size="scope.size" v-model="scope.row.primaryId" class="ml-4"
                                            @change="handlePrimaryIdChange(index,scope.index)" :disabled="copyType==='edit'">
                              <el-radio label="yes"></el-radio>
      </el-radio-group>
    </template>
              <template slot="manufactureCustomerId">
                      <el-select
                              :no-data-text="$t('work.cpscTrfInfo.selectNoData')"
                              v-model="item.manufactureCustomerId"
                        :placeholder="$t('work.cpscTrfInfo.column.manufacture')" ref="template"
                        @visible-change="(v) => visibleChange(v, 'template', 'Manufacture',index)" filterable :filterMethod="filterMethod" @change="handleTradeChange($event,productInfoList[index])">
                        <el-option v-for="(item, index) in manufactureCustomerIds" :key="item.id" :label="item.tradeName"
                          :value="item.id">
                          {{ item.tradeName }}
                          <div class="flag"></div>
                        </el-option>
                      </el-select>
                </template>
              </avue-form>
          </div>

          </el-card>


     </div>
        </el-collapse-item>
        <!-- <el-collapse-item   name="4">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab6 }}</h3>


        </template>
        <div class="form-content">

  <div class="form-inner">
    <avue-form :option="formOption6Com" ref="form6" v-model="newFormModel">
      <template slot="certVersion">
        <el-input maxlength="19" :placeholder="this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.certificateVersion')" v-model="newFormModel.certVersion" clearable size="small" >
          <template slot="append">
            <el-button type="primary" @click="handleGenerate" >Generate</el-button>
          </template>
        </el-input>
      </template>
    </avue-form>
  </div>
</div>
        </el-collapse-item> -->
        <el-collapse-item   name="5">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab4 }}</h3>


        </template>
        <div class="form-content">


           <div class="form-inner">

               <avue-form :option="formOption4Com" v-model="newFormModel" ref="form4">

           <template slot="pocCustomerId" slot-scope="scope">
             <el-select style="width:100%" v-model="newFormModel.pocCustomerId" :placeholder="$t('work.cpscTrfInfo.column.otherPoc')"
               ref="template2" @visible-change="(v) => visibleChange(v, 'template2', 'POC')" >
               <el-option v-for="(item, index) in shipTemplates" :key="item.id" :label="item.tradeName" :value="item.id">
                 {{ item.tradeName }}
                 <div class="flag"></div>
               </el-option>
             </el-select>
           </template>
         </avue-form>
       </div>

     </div>
        </el-collapse-item>
      </el-collapse>
      <div class="form-content">
            <TrfAttachment ref="trfFile"
                       :trfAttachments="newFormModel.cpscTrfAttachList"
                       :trf="newFormModel"
                       :file-max-sizes="20"
                   >
            </TrfAttachment>
      </div>
      <el-dialog v-if="isDialog"  :visible.sync="isDialog" append-to-body destroy-on-close :title="$t('crud.addTitle')" width="50%">
        <commen-dialog :cpscCustomerId="selectBuyerCustomerOldId" :type="type"
          @closeCommenDialog="closeCommenDialog(arguments)"></commen-dialog>
      </el-dialog>
      <!-- <el-row type="flex" justify="center">
        <el-button type="warning" plain v-if="trfInfo.trfInfoStastus < 3" @click="handleSave(1)">{{ btnSaveLab }}
        </el-button>
        <el-button type="success" plain v-if="trfInfo.trfInfoStastus < 3" @click="handleSave(2)">{{ btnSubmitLab }}
        </el-button>
      </el-row> -->
    </div>
  </template>

  <script>
  import { getList } from '@/api/form/form.js'
  import { getDetail, add, update, listForSelect, tradeSelect,getBasicLabList } from "@/api/cpscTrfInfo/cpscTrfInfo";

  import CommenDialog from '@/views/cpscTrfInfo/commenDialog.vue'
  export default {
    components: {

            CommenDialog,
            TrfAttachment: resolve => require(['@/components/trf/TrfAttachment'], resolve),
    },
    props: {
      formModel: Object,
      productInfoList:Array
    },
    watch:{
      formModel(newVal,oldVal){
        this.$set(this.newFormModel, JSON.parse(JSON.stringify(newVal)))

      }
    },
    data() {
      return {
        newFormModel:{},
        selectBuyerCustomerOldId:null,
        activeNames:['1','2','3','4','5'],
        option:{},
        // productInfoList:[
        //   {}
        // ],

        buyerCustomerIds:[],
        type: '',
        isDialog: false,
        shipTemplates: [

        ],
        manufactureCustomerIds: [],


        btnSaveLab: this.$t('work.cpscTrfInfo.column.save'),
        btnSubmitLab: this.$t('work.cpscTrfInfo.column.submit'),
        formLab1: this.$t('work.cpscTrfInfo.column.buyInfo'),
        timer:null,
        formOption1:{
          // 表单配置项
          menuType: 'default',
          submitBtn: false,
          emptyBtn: false,
          labelPosition: 'top',
          //disabled: this.trfInfo.trfInfoStastus >=3,
          column: [
            {
              prop: 'buyerCustomerName',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.customerName'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.customerName'),
              prop: 'buyerCustomerId',

              type: 'select',
              dataType: "string",
              //dicUrl: "/api/sgs-e-filling/sgs-work/cpscCustomer/listForSelect",
              dicData:[],
              props: {
                label: 'bossName', // 对象的属性，用作显示标签
                value: 'id' // 对象的属性，用作值
              },
              // filterMethod:(val)=>{
              //   if(val){
              //     clearTimeout(this.timer)
              //     this.timer=setTimeout(()=>{
              //       this.getBuyerCustomerId(val)
              //     },800)

              //   }
              // },
             // change: this.handleBuyerChange,
              rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.customerName') }]
            },
            {
              label: this.$t('work.cpscTrfInfo.column.customerNameLocal'),
              labelWidth: 200,
              type: 'input',

              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerNameLocal'),
              prop: 'buyerCustomerNameLocal',
            },
            {
              prop: 'buyerCustomerCountryId',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.customerCountry'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerCountry'),
              type: 'input',


              prop: 'buyerCustomerCountryName',

            },
            {
              prop: 'buyerCustomerCityId',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.customerCity'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerCity'),
              prop: 'buyerCustomerCityName',

            },
            {
              label: this.$t('work.cpscTrfInfo.column.customerAddress'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerAddress'),
              prop: 'buyerCustomerAddress',
              row: true,
              span: 24,

            },
            {
              label: this.$t('work.cpscTrfInfo.column.customerAddressLocal'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.customerAddressLocal'),
              prop: 'buyerCustomerAddressLocal',
              row: true,
              span: 24,

            },
            {
              prop: 'buyerContactId',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.contactName'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.contactName'),
              prop: 'buyerContactName',

              //type: 'select',
              //dataType: "string",
              //dicUrl: "/sgs-work/cpscCustomerTradeContact/listForSelect?relationId={{buyerCustomerId}}&relationType=customer",
              //props: {
              //  label: 'contactName', // 对象的属性，用作显示标签
              //  value: 'id' // 对象的属性，用作值
              //},
              //change:this.handleContactChange,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.contactTelephone'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.contactTelephone'),
              prop: 'buyerContactTelephone',

            },
            {
              label: this.$t('work.cpscTrfInfo.column.contactEmail'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.contactEmail'),
              prop: 'buyerContactEmaill',
              component: 'el-input',

            }
          ]
      },
        formLab2: this.$t('work.cpscTrfInfo.column.manufactureInfo'),
        // formOption2:{
        //   // 表单配置项
        //   menuType: 'default',
        //   submitBtn: false,
        //   emptyBtn: false,
        //   labelPosition: 'top',
        //   //disabled: this.trfInfo.trfInfoStastus >=3,
        //   column: [
        //     {
        //       prop: 'manufactureCustomerName',
        //       display: false,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.customerName'),
        //       labelWidth: 200,

        //       prop: 'manufactureCustomerId',
        //       type: 'select',
        //       dataType: "string",
        //       //dicUrl: "/sgs-work/cpscCustomerTrade/listForSelect?tradeType=Manufacture&cpscCustomerId={{buyerCustomerId}}",
        //       dicData: [],
        //       props: {
        //         label: 'tradeName', // 对象的属性，用作显示标签
        //         value: 'id' // 对象的属性，用作值
        //       },
        //       cascader: ['manufactureContactId'],
        //       //change: this.handleTradeChange,
        //       rules: [{ required: true, trigger: 'change' }],
        //       maxlength: 50,
        //       showWordLimit: true,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.customerNameLocal'),
        //       labelWidth: 200,

        //       prop: 'manufactureCustomerNameLocal',
        //       maxlength: 50,
        //       showWordLimit: true,
        //     },
        //     {
        //       prop: 'manufactureCustomerCountryId',
        //       display: false,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.customerCountry'),
        //       labelWidth: 200,

        //       prop: 'manufactureCustomerCountryName',
        //       maxlength: 50,
        //       showWordLimit: true,
        //     },
        //     {
        //       prop: 'manufactureCustomerCityId',
        //       display: false,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.customerCity'),
        //       labelWidth: 200,

        //       prop: 'manufactureCustomerCityName',
        //       maxlength: 50,
        //       showWordLimit: true,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.customerAddress'),
        //       labelWidth: 200,

        //       prop: 'manufactureCustomerAddress',
        //       row: true,
        //       span: 24,
        //       maxlength: 100,
        //       showWordLimit: true,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.customerAddressLocal'),
        //       labelWidth: 200,

        //       prop: 'manufactureCustomerAddressLocal',
        //       row: true,
        //       span: 24,
        //       maxlength: 100,
        //       showWordLimit: true,
        //     },
        //     {
        //       prop: 'manufactureContactId',
        //       display: false,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.contactName'),
        //       labelWidth: 200,

        //       prop: 'manufactureContactName',
        //       maxlength: 50,
        //       showWordLimit: true,
        //       //type: 'select',
        //       //dataType: "string",
        //       //dicUrl: "/sgs-work/cpscCustomerTradeContact/listForSelect?relationId={{manufactureCustomerId}}&relationType=trade",
        //       //props: {
        //       //  label: 'contactName', // 对象的属性，用作显示标签
        //       //  value: 'id' // 对象的属性，用作值
        //       //},
        //       //change:this.handleTradeContactChange,
        //       rules: [{ required: true }]
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.contactTelephone'),
        //       labelWidth: 200,

        //       prop: 'manufactureContactTelephone',
        //       maxlength: 50,
        //       showWordLimit: true,
        //     },
        //     {
        //       label: this.$t('work.cpscTrfInfo.column.contactEmail'),
        //       labelWidth: 200,

        //       prop: 'manufactureContactEmaill',
        //       component: 'el-input',
        //       maxlength: 50,
        //       showWordLimit: true,
        //     }
        //   ]
        // },
        formLab3: this.$t('work.cpscTrfInfo.column.basicInfo'),
        formOption3:{
          // 表单配置项
          menuType: 'default',
          submitBtn: false,
          emptyBtn: false,
          labelPosition: 'top',
          //disabled: this.trfInfo.trfInfoStastus >=3,
          column: [
            {
              prop: 'basicServiceTypeName',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.serviceType'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.serviceType'),
              prop: 'basicServiceType',
              type: "select",
              dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=serviceType",
              dataType: "string",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.serviceType') }],
              change: this.handleServiceTypeChange
            },
            {
              prop: 'basicLabName',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.labName'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.labName'),
              prop: 'basicLabId',
              type: 'select',
              filterable:true,
              dataType: "string",
              dicData: [],
              props: {
                label: 'labName', // 对象的属性，用作显示标签
                value: 'labCode' // 对象的属性，用作值
              },

              rules: [{ required: true,trigger:'change',message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.labName') }],
              change: this.handleBasicLabChange,

            },
            {
              prop: 'basicLabContactId',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.labContact'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.labContact'),
              prop: 'basicLabContactName',
              rules: [{ required: true,trigger:'blur',message:this.$t('work.cpscTrfInfo.column.csEmailAddressMsg') },{
                message:this.$t('trfPrint.general.validLabContact'),
            pattern:  /^[\w\.\-]+@([\w\-]+\.)+[\w\-]+$/,    //正则校验不用字符串
             trigger: "blur"
          }]
              //rules: [{ required: true }],

            },

            {
              label: this.$t('work.cpscTrfInfo.column.labAddress'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.input')+this.$t('work.cpscTrfInfo.column.labAddress'),
              prop: 'basicLabAddress',
              //rules: [{ required: true }],

            },
          ]
        },
        formLab4: this.$t('work.cpscTrfInfo.column.poc'),
        formOption4:{
          // 表单配置项
          menuType: 'default',
          submitBtn: false,
          emptyBtn: false,
          labelPosition: 'top',
          //disabled: this.trfInfo.trfInfoStastus >=3,
          column: [
            {
              prop: 'pocTypeName',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.pocType'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.pocType'),
              prop: 'pocTypeId',
              type: "select",
              dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=pocType",
              dataType: "string",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              // change: this.handlePOCTypeChange,
              rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.pocType') }]
            },
            {
              prop: 'pocCustomerName',
              display: false,
            },
            {
              label: this.$t('work.cpscTrfInfo.column.otherPoc'),
              labelWidth: 200,
              placeholder:this.$t('work.cpscTrfInfo.placeholder.select')+this.$t('work.cpscTrfInfo.column.otherPoc'),
              prop: 'pocCustomerId',
              type: 'select',
              dataType: "string",
              dicData: [],
              // dicUrl: "/sgs-work/cpscCustomerTrade/listForSelect?tradeType=POC&cpscCustomerId={{key}}",
              props: {
                label: 'tradeName', // 对象的属性，用作显示标签
                value: 'id' // 对象的属性，用作值
              },
             // change: this.handlePOCOtherChange,
              display: false,
            },
          ]
        },
        formLab5: this.$t('work.cpscTrfInfo.column.attachment'),
        option:{},
        formLab6: this.$t('work.cpscTrfInfo.column.certificateInfo'),
        formOption6:{

    // 表单配置项
    menuType: 'default',
    submitBtn: false,
    emptyBtn: false,
    labelPosition:'top',
    column: [
      {
        label: this.$t('work.cpscTrfInfo.column.certificateVersion'),
        //labelWidth: 200,
       // labelPosition: 'right',
        prop: 'certVersion',
        suffixIcon:'Plus',
        rules: [{ required: true,message:this.$t('work.cpscTrfInfo.column.placeholderCertificateVersion') }],

        display: true,
      },
    ]
  },
      };
    },

    mounted() {
      this.newFormModel =JSON.parse(JSON.stringify(this.formModel))
      console.log('shang',this.newFormModel)
   
          this.selectBuyerCustomerOldId=this.newFormModel.buyerCustomerId
        
      this.listForSelect().finally(()=>{
        this.loadFormOption()
        this.setFormModel()
      })
    },
    created() {

    },
    computed: {

     formOption1Com(){
      return this.formOption1
     },
    //  formOption2Com(){

    //   return this.formOption2
    //  },
     formOption3Com(){
      return this.formOption3
     },
     formOption4Com(){
      return this.formOption4
     },
     formOption5Com(){
      return this.option
     },
     formOption6Com(){
      return this.formOption6
    },
    },
    methods: {
      addProductInfo(item){
        const productInfo =JSON.parse(JSON.stringify(item))
        productInfo.referenceNo = this.getNewTrfNo()
        this.productInfoList.push(productInfo)
      },
      formValidate(){
        const formRefs = Object.keys(this.$refs).filter(key => key.startsWith('form'));
    const validationPromises = formRefs.map(ref => {
      return new Promise(resolve => {
        let form =(this.$refs[ref])[0]||(this.$refs[ref])
        form.validate(valid => {
          resolve(valid);
        });
      });
    });

    return Promise.all(validationPromises).then(results => {
      return results.every(valid => valid);
    });
      },
      handleGenerate(){
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      const charactersLength = characters.length;
      for (let i = 0; i < 8; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      this.newFormModel.certVersion = result;
    },
      filterBuyerCust(val){
      if(val){

                  clearTimeout(this.timer)
                  this.timer=setTimeout(()=>{
                    this.getBuyerCustomerId(val)
                  },800)

            }
    },
      filterMethod(val){
      clearTimeout(this.timer)
                  this.timer=setTimeout(()=>{
                   // this.listForSelect(val)
                   this.setPOC('Manufacture', this.selectBuyerCustomerOldId,val)
                  },800)
    },
      getBuyerCustomerId(name){
        listForSelect(name).then(res=>{
       const column = this.findObject(this.formOption1.column,'buyerCustomerId')
       column.dicData =res.data.data
       this.buyerCustomerIds =res.data.data
      })
      },
      handlePrimaryIdChange(index,index2) {
	              this.productInfoList[index].formList.forEach((item, i) => {
	                  if (i !== index2) {
	                      item.primaryId = '';
	                  }
	              });
	        },
        loadFormOption() {
            // 获取表单定义
            getList(1,10,{code:'Efilling'}).then(res => {
                const data = res.data.data.records[0];
                data.content=(data.content+'').replaceAll('YYYY','yyyy')
                this.option = JSON.parse(data.content || '{}');
                       this.option.size='default'
                if(Object.keys(this.option).length){
 // 重新解析字段配置，替换正则表达式及方法
 this.option.column.forEach(function (column, index) {
                    // 添加正则表达式解析逻辑
                    if (column.rules && column.pattern) {
                        column.rules.forEach(function (rule) {
                            if (rule.pattern) {
                                rule.pattern = new RegExp(column.pattern)
                            }
                        })
                    }
                    if (column.method) {
                        Object.keys(column.method).forEach(function (key) {
                            const funcStr = column.method[key];
                            column[key] = new Function(funcStr);
                        })
                    }
                })
                // 重新解析字段配置，替换正则表达式及方法
                if (this.option.group) {
                    this.option.group.forEach(function (column, index) {
                        // 添加正则表达式解析逻辑
                        if (column.rules && column.pattern) {
                            column.rules.forEach(function (rule) {
                                if (rule.pattern) {
                                    rule.pattern = new RegExp(column.pattern)
                                }
                            })
                        }
                        if (column.method) {
                            Object.keys(column.method).forEach(function (key) {
                                const funcStr = column.method[key];
                                column[key] = new Function(funcStr);
                            })
                        }
                    })
                }
                const formListColumn = this.findObject(this.option.group[0].column,'formList')
                formListColumn.children.column.forEach(item=>{
                  item.slot=true
                })
                }

            });
        },
      closeCommenDialog(e) {

        this.setPOC(e[0], this.selectBuyerCustomerOldId,'',e[1])

        this.isDialog = false

      },

      showShipTemplate(type) {
        if (this.newFormModel.buyerCustomerId) {
          this.isDialog = true
          this.type = type
        } else {
          this.$message.error(this.$t('work.cpscTrfInfo.buyerMessage'))
        }

      },
      visibleChange(visible, refName, type,index) {
        if (visible) {
          const ref = this.$refs[refName] ;
          let popper = ref.popperElm ||ref[index].popperElm
          if (popper.$el) popper = popper.$el;
          if (
            !Array.from(popper.children).some(
              (v) => v.className === "el-template-menu__list"
            )
          ) {
            const el = document.createElement("ul");
            el.className = "el-template-menu__list";
            el.style =
              "border-bottom:1px solid #f5f5f5;; padding:0; color:#ff6600;font-size: 16px";
            el.innerHTML = `<li class="el-cascader-node text-center" style="height:37px;line-height: 50px;">
              <span class="el-cascader-node__label"><i class="font-blue el-icon-plus"></i>${this.$t('operation.add')}</span>
              </li>`;
            popper.insertBefore(el, popper.firstChild);
            el.onclick = () => {
              this.showShipTemplate(type);
            };
          }
        }
      },
      async listForSelect(value) {
       const response = await listForSelect( this.newFormModel.buyerCustomerName ||value)
       const column = this.findObject(this.formOption1.column, 'buyerCustomerId');
       this.buyerCustomerIds =response.data.data
       column.dicData =response.data.data
       //column.dicData=res.data.data
       const selectedObject =  this.buyerCustomerIds.find(item=>item.id==this.formModel.buyerCustomerId)
       if(selectedObject){
         this.setPOC('POC', selectedObject.customerId)
         this.setPOC('Manufacture', selectedObject.customerId,this.productInfoList[0]?this.productInfoList[0].manufactureCustomerName:'')
       }
        getBasicLabList('','').then(res=>{
        let labList = res.data.data;
        labList.forEach(lab => {
          if (lab.labContactList[0]){
            lab.contact = lab.labContactList[0].contactName + "-" + lab.labContactList[0].contactTel + "-" + lab.labContactList[0].contactEmail;
          }

        });
        const column2 = this.findObject(this.formOption3.column, 'basicLabId');
        column2.dicData = labList;
      })
      },
      setFormModel() {
        //判断versionid
        if(this.newFormModel.id){
        //     if (this.newFormModel.cpscCustomer.versionidType){
        //   if (this.newFormModel.cpscCustomer.versionidType == '1'){

        //     this.$refs.form6.option.column[0].rules[0].required = false;
        //   } else if (this.newFormModel.cpscCustomer.versionidType == '2'){

        //     this.$refs.form6.option.column[0].rules[0].required = true;
        //   }
        // }
        const column = this.findObject(this.formOption4.column, 'pocCustomerId')

        column.display = this.newFormModel.pocTypeId == 'Other'
        if (!column.display) {
          this.newFormModel.pocCustomerId = ''
          this.newFormModel.pocCustomerName = ''
        }
        }


      },
      getNewTrfNo() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以+1
            const day = now.getDate().toString().padStart(2, '0');
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            const endStr = Math.floor(Math.random() * 900) + 100;

            // 连接成纯数字字符串
            const formattedTime = `${year}${month}${day}${hours}${minutes}${seconds}${endStr}`;
            //const formattedTime = `${minutes}${seconds}`;
            return "eFiling"+formattedTime;
          },
      handleBuyerChange(value,dic) {

        if(!value) {
         // this.$refs.form6.option.column[0].rules[0].required = true;
          this.$nextTick(()=>{
            this.selectBuyerCustomerOldId =null
            this.newFormModel.buyerCustomerName = null
            this.newFormModel.buyerCustomerNameLocal = null
            this.newFormModel.buyerCustomerCountryId = null
            this.newFormModel.buyerCustomerCountryName = null
            this.newFormModel.buyerCustomerCityId = null
            this.newFormModel.buyerCustomerCityName = null
            this.newFormModel.buyerCustomerAddress = null
            this.newFormModel.buyerCustomerAddressLocal = null
            this.newFormModel.buyerContactId = null
            this.newFormModel.buyerContactName = null
            this.newFormModel.buyerContactTelephone = null
            this.newFormModel.buyerContactEmaill = null
            this.newFormModel.pocType=null
          })
          this.getBuyerCustomerId('')
          return
        }
          // 根据选择的对象ID获取对象详情
          let selectedObject = this.buyerCustomerIds.find(item=>item.id==value)
          this.selectBuyerCustomerOldId=selectedObject?selectedObject.customerId:null
          if(!selectedObject){
            selectedObject=  this.buyerCustomerIds.find(item=>item.bossName==this.newFormModel.buyerCustomerId)
            this.newFormModel.buyerCustomerId =selectedObject?selectedObject.id:null
          }
          // console.log(this.buyerCustomerIds)
          if (this.selectBuyerCustomerOldId) {
            this.setPOC('POC', this.selectBuyerCustomerOldId)
            this.setPOC('Manufacture', this.selectBuyerCustomerOldId)
          }
          if (selectedObject) {
            //判断versionid

            // tradeSelect('Manufacture', selectedObject.customerId).then(res => {
            //   const column = this.findObject(this.formOption2.column, 'manufactureCustomerId');
            //   column.dicData = res.data.data
            //  const data = res.data.data.find(item=>item.tradeName === this.newFormModel.manufactureCustomerId)
            //   this.productInfoList.forEach(item=>{
            //     item.manufactureCustomerId=data?data.id:null
            //   })
            // })
            // tradeSelect('POC', selectedObject.customerId).then(res => {
            //   const column = this.findObject(this.formOption4.column, 'pocCustomerId');
            //   column.dicData = res.data.data

            // })
            this.$nextTick(()=>{
              this.newFormModel.buyerCustomerId = selectedObject.id;
            this.newFormModel.buyerCustomerName = selectedObject.bossName;
            this.newFormModel.buyerCustomerNameLocal = selectedObject.bossNameLocal;
            this.newFormModel.buyerCustomerCountryId = selectedObject.bossCustomerCountryId;
            this.newFormModel.buyerCustomerCountryName = selectedObject.bossCustomerCountryName;
            this.newFormModel.buyerCustomerCityId = selectedObject.bossCustomerCityId;
            this.newFormModel.buyerCustomerCityName = selectedObject.bossCustomerCityName;
            this.newFormModel.buyerCustomerAddress = selectedObject.bossCustomerAddress;
            this.newFormModel.buyerCustomerAddressLocal = selectedObject.bossCustomerAddressLocal;
            this.newFormModel.buyerContactId = selectedObject.cpscCustomerTradeContactVO? selectedObject.cpscCustomerTradeContactVO.id:null;
            this.newFormModel.buyerContactName = selectedObject.cpscCustomerTradeContactVO?selectedObject.cpscCustomerTradeContactVO.contactName:null;
            this.newFormModel.buyerContactTelephone = selectedObject.cpscCustomerTradeContactVO?selectedObject.cpscCustomerTradeContactVO.telphone:null;
            this.newFormModel.buyerContactEmaill = selectedObject.cpscCustomerTradeContactVO?selectedObject.cpscCustomerTradeContactVO.email:null;
            this.productInfoList.forEach(item=> item.formList=[{productIdType:selectedObject.productPrimaryId,primaryId:'yes',productId:item.productId?item.productId:''}])
            if (selectedObject.pocType){
            this.newFormModel.pocTypeId = selectedObject.pocType
           }
            })
            // if (selectedObject.versionidType){
            //   if (selectedObject.versionidType == '1'){

            //     this.$refs.form6.option.column[0].rules[0].required = false;
            //   } else if (selectedObject.versionidType == '2'){

            //     this.$refs.form6.option.column[0].rules[0].required = true;
            //   }
            // }
            //console.log(this.newFormModel)
          }

      },
     async setPOC(type, value,name='',tradeName) {
      const res = await tradeSelect(type, value,name||tradeName)
          if (type == 'POC') {
            this.shipTemplates = res.data.data
              const column = this.findObject(this.formOption4.column, 'pocCustomerId');
              column.dicData = res.data.data
          } else if (type == 'Manufacture') {
              //   const column = this.findObject(this.formOption2.column, 'manufactureCustomerId');
              // column.dicData = res.data.data
            //  const data = res.data.data.find(item=>item.id === this.newFormModel.manufactureCustomerId)
            //   this.productInfoList.forEach(item=>{
            //     item.manufactureCustomerId=data?data.id:null
            //   })
            this.manufactureCustomerIds = res.data.data
            if(tradeName){
              const item = this.manufactureCustomerIds.find(item=>item.tradeName===tradeName)
              this.productInfoList.forEach(productInfo=>{
                productInfo.manufactureCustomerId = item.id
                this.handleTradeChange(item.id,productInfo)
              })
            }

          }


      },

      handleContactChange(value) {
        const selectedObject = value.item;
        if (selectedObject) {
          this.newFormModel.buyerContactId = selectedObject.id;
          this.newFormModel.buyerContactName = selectedObject.contactName;
          this.newFormModel.buyerContactTelephone = selectedObject.telphone;
          this.newFormModel.buyerContactEmaill = selectedObject.email;
        }
      },
      handleTradeChange(e,item) {
        const selectedObject= this.manufactureCustomerIds.find(item=>item.id===e)
        // 根据选择的对象ID获取对象详情

        if (selectedObject) {
          this.$nextTick(()=>{
            item.manufactureCustomerId = selectedObject.id;
          item.manufactureCustomerName = selectedObject.tradeName;
          item.manufactureCustomerNameLocal = selectedObject.tradeName;
          item.manufactureCustomerCountryId = selectedObject.tradeCountryId;
          item.manufactureCustomerCountryName = selectedObject.tradeCountryName;
          item.manufactureCustomerCityId = selectedObject.tradeCity;
          item.manufactureCustomerCityName = selectedObject.tradeCity;
          item.manufactureCustomerAddress = selectedObject.tradeAddress;
          item.manufactureCustomerAddressLocal = selectedObject.tradeAddress2;
          item.manufactureContactId = selectedObject.cpscCustomerTradeContactVO.id;
          item.manufactureContactName = selectedObject.cpscCustomerTradeContactVO.contactName;
          item.manufactureContactTelephone = selectedObject.cpscCustomerTradeContactVO.telphone;
          item.manufactureContactEmaill = selectedObject.cpscCustomerTradeContactVitem

          })

        }
      },
      handleTradeContactChange(value) {
        const selectedObject = value.item;
        if (selectedObject) {
          this.$nextTick(()=>{
            this.newFormModel.manufactureContactId = selectedObject.id;
          this.newFormModel.manufactureContactName = selectedObject.contactName;
          this.newFormModel.manufactureContactTelephone = selectedObject.telphone;
          this.newFormModel.manufactureContactEmaill = selectedObject.email;
          })

        }
      },
    //   handleSave(trfStatus) {
    //     if(trfStatus==1){
    //       let form = JSON.parse(JSON.stringify(this.newFormModel))
    //         form.dataSource = 1;
    //         form.trfInfoStastus = trfStatus;
    //         if (this.trfInfo.id) {
    //           form.id = this.trfInfo.id;
    //           form.trfInfoVersion = this.trfInfo.trfInfoVersion
    //           form.trfInfoId = this.trfInfo.trfInfoId
    //         }
    //         form.referenceNo = this.trfInfo.referenceNo
    //         //获取 primay key
    //         if (this.$refs.productInfo.formList) {
    //           const primary = this.$refs.productInfo.formList.find(item => item.primaryId === 'yes');
    //           form.productId = primary.productId
    //           form.productIdType = primary.productIdType
    //         }
    //         if (this.$refs.productInfo.formBottom.productModelName) {
    //           form.productName = this.$refs.productInfo.formBottom.productModelName
    //         }
    //         let formListObj = {
    //           formList: this.$refs.productInfo.formList
    //         }
    //         let productInfoForm = Object.assign({}, this.$refs.productInfo.form, this.$refs.productInfo.formBottom, formListObj);
    //         form.productJson = JSON.stringify(productInfoForm)
    //         add(form).then(() => {
    //           this.$message({
    //             type: "success",
    //             message: "操作成功!"
    //           });
    //           this.$emit("closeEditPage")
    //         }, error => {
    //           window.console.log(error);
    //         });
    //     }else{
    //       let formRefs = Object.keys(this.$refs)
    //       .filter((refName) => refName.startsWith("form"))
    //       .map((refName) => this.$refs[refName]);
    //     // 标记所有表单是否通过校验的变量
    //     // let isValid = true
    //     // 遍历表单数组，依次对每个表单进行校验

    //     const promise = new Promise((r, rj) => {
    //       for (let ref of formRefs) {
    //         ref.validate(valid => {

    //           if (!valid) {
    //             r('error')
    //             return
    //           }
    //         })

    //       }
    //       setTimeout(() => {
    //         r('success')
    //       }, 1000)


    //     })
    //     promise.then(res => {
    //       if (res == 'success') {
    //         // 执行提交操作
    //         let form = JSON.parse(JSON.stringify(this.newFormModel))
    //         form.dataSource = 1;
    //         form.trfInfoStastus = trfStatus;
    //         if (this.trfInfo.id) {
    //           form.id = this.trfInfo.id;
    //           form.trfInfoVersion = this.trfInfo.trfInfoVersion
    //           form.trfInfoId = this.trfInfo.trfInfoId
    //         }
    //         form.referenceNo = this.trfInfo.referenceNo
    //         //获取 primay key
    //         if (this.$refs.productInfo.formList) {
    //           const primary = this.$refs.productInfo.formList.find(item => item.primaryId === 'yes');
    //           form.productId = primary.productId
    //           form.productIdType = primary.productIdType
    //         }
    //         if (this.$refs.productInfo.formBottom.productModelName) {
    //           form.productName = this.$refs.productInfo.formBottom.productModelName
    //         }
    //         let formListObj = {
    //           formList: this.$refs.productInfo.formList
    //         }
    //         let productInfoForm = Object.assign({}, this.$refs.productInfo.form, this.$refs.productInfo.formBottom, formListObj);
    //         form.productJson = JSON.stringify(productInfoForm)
    //         add(form).then(() => {
    //           this.$message({
    //             type: "success",
    //             message: "操作成功!"
    //           });
    //           this.$emit("closeEditPage")
    //         }, error => {
    //           window.console.log(error);
    //         });
    //       }
    //     })

    //     }

    //   },
      handleServiceTypeChange(value) {

        const selectedObject = value.item;
        if (selectedObject) {
          this.newFormModel.basicServiceType = selectedObject.dictValue
          this.newFormModel.basicServiceTypeName = selectedObject.dictKey
        }
      },
      handleBasicLabChange(value) {

        const selectedObject = value.item;

        if (selectedObject) {
          this.newFormModel.basicLabId=value.value
          this.newFormModel.basicLabAddress = selectedObject.labAddress
         // this.newFormModel.basicLabContactName = selectedObject.contact
        }else{
          if(!value.value){
            this.formModel.basicLabId=null
        this.formModel.basicLabAddress = null
        //this.formModel.basicLabContactName = null
          }

      }
      },
      // handlePOCTypeChange(value) {
      //   const selectedObject = value.item;
      //   if (selectedObject && selectedObject.dictValue == 'Other') {
      //     this.formOption4.column[3].display = true;
      //     this.newFormModel.pocTypeId = selectedObject.dictValue
      //     this.newFormModel.pocTypeName = selectedObject.dictKey
      //   } else {
      //     this.formOption4.column[3].display = false;
      //     this.newFormModel.pocTypeId = null
      //     this.newFormModel.pocTypeName = null
      //   }

      // },
      handlePOCOtherChange(value) {
        const selectedObject = value.item;
        if (selectedObject) {
          this.$nextTick(()=>{
            this.newFormModel.pocCustomerId = selectedObject.id
            this.newFormModel.pocCustomerName = selectedObject.tradeName
          })

        }
      },


    },
    watch: {
      'newFormModel.pocTypeId'(newVal, oldVal) {

        this.$emit('changePocTypeId',newVal)
        const column = this.findObject(this.formOption4.column, 'pocCustomerId')
        column.display = newVal == 'Other'
        if (!column.display) {
          this.newFormModel.pocCustomerId = ''
          this.newFormModel.pocCustomerName = ''
        }
      },
      // 'newFormModel.pocCustomerId'(newVal, oldVal) {

      //     this.$emit('changeAddLabPocCustomerId',newVal)

      // },

    },
  };
  </script>

  <!-- <style scoped>


  .header {
    background-color: #f0f2f5;
    padding: 5px;
  }

  .line1 {
    padding: 15px;
    margin: 0;
  }

  .line2 {
    display: flex;
    align-items: flex-end;
    /* 确保元素在同一行 */
    justify-content: flex-start;
    padding: 15px;
  }

  .line2>* {
    margin: 0;
    /* 移除默认的外边距 */
    z-index: 99999;
  }

  span {
    padding: 5px;
    /* 在<span>元素内部添加5px的内边距 */
  }

  .form-content,
  .form-inner {
    margin: 20px;
  }

  .form-lab {
    padding: 10px;
    background-color: #f5f5f5;
  }

  .el-input__wrapper {
    height: 30px;
  }
  </style> -->
