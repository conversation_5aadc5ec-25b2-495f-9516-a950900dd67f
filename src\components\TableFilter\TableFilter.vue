<template>
  <div class="table-filter">
    <span class="table-filter-title">{{ title }}</span>
    <span>
      <el-icon
        class="table-icon-top"
        v-show="enableSort && sort === 'desc'"
        @click="handleSort('asc')"
      >
        <Top />
      </el-icon>
      <el-icon
        class="table-icon-bottom"
        v-show="enableSort && sort === 'asc'"
        @click="handleSort('desc')"
      >
        <Bottom />
      </el-icon>
      <el-popover v-if="needMore" placement="bottom" :width="300" trigger="click">
        <template #reference>
          <span>
            <el-icon class="table-icon-more"><MoreFilled /></el-icon>
          </span>
        </template>
        <slot></slot>
      </el-popover>
    </span>
  </div>
</template>

<script setup>
/**
 * 表格过滤器组件
 * @module TableFilter
 * @description 用于表格列的过滤功能，支持自定义过滤内容和标题
 * @example
 * <TableFilter
 *   title="过滤器标题"
 *   enableSort
 *   field="columnName"
 *   :defaultSort="'asc'"
 *   @sort="handleSort"
 * >
 *   <el-input placeholder="输入过滤条件" />
 * </TableFilter>
 */

import { ref, defineProps, defineEmits } from 'vue'
import { Top, Bottom, MoreFilled } from '@element-plus/icons-vue'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  field: {
    type: String,
    default: '',
  },
  defaultSort: {
    type: String,
    default: 'desc', // 可选值：'asc', 'desc', ''
  },
  enableSort: {
    type: Boolean,
    default: false,
  },
    needMore:{
      type: Boolean,
      default: true
    }
})

// 定义 emits
const emit = defineEmits(['sort'])

// 响应式数据
const showIcons = ref(false)
const sort = ref(props.defaultSort)

// 处理排序方法
const handleSort = (order) => {
  sort.value = order
  emit('sort', { field: props.field, order })
}
</script>

<style scoped>
.table-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.table-icon-more {
  font-size: 16px;
  cursor: pointer;
  transform: rotate(90deg) translateX(3px);
}
.table-icon-bottom,
.table-icon-top {
  font-size: 18px;
  cursor: pointer;
  transform: translateY(3px);
}
.table-filter-title {
  font-size: 12px;
}
</style>
