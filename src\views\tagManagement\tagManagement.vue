<template>
  <div class="card-container">
    <el-card>
      <!-- <template #header>
     <div class="card-header">
        <span>{{ $t('work.tagManagement.title') }}</span>
      </div>
    </template> -->
      <h3>{{ $t('work.tagManagement.title') }}</h3>
      <div class="card-searchTable">
        <el-form :model="search" label-width="auto" style="max-width: 100%">
          <div style="width: 100%;display: flex;justify-content: space-between;">
            <!-- <el-col :span="6"> -->
            <el-form-item style="width: 25%;">
              <el-input v-model="search.tagNameCn" popper-class="my-autocomplete"
                :placeholder="`${$t('work.tagManagement.tagName')}`" style="width: 100%;" @keyup.enter="handleSearch" />
            </el-form-item>
            <!-- </el-col> -->
            <!-- <el-col :span="6" style="display: flex; justify-content: end;"> -->
            <el-form-item>

              <!-- 搜索按钮  -->
              <el-button type="primary" @click="handleSearch" size="large" style="border-radius: 0">{{
                $t('work.tagManagement.search') }}</el-button>

              <!-- 重置按钮 -->
              <el-button @click="resetSearch" type="info" size="large" style="border-radius: 0">{{
                $t('work.tagManagement.reset') }}</el-button>
            </el-form-item>
            <!-- </el-col> -->
          </div>
          <el-row style="margin-top: 10px; margin-bottom: 18px;">
            <el-button style="border-radius: 0;" size="large" @click="outerVisible = true" type="primary">
              {{ $t('work.tagManagement.add') }}
            </el-button>
          </el-row>
        </el-form>


        <!-- 标签管理表格 -->
        <el-table :data="tableData" border style="width: 100%;margin-top: 5px;"
          :empty-text="$t('work.tagManagement.noData')">
          <el-table-column property="tagNameCn" :label="$t('work.tagManagement.tagName')" width="200" align="left">
          </el-table-column>

          <el-table-column property="tagDescCn" :label="$t('work.tagManagement.tagDescription')" width="400"
            align="left" />

          <el-table-column property="status" :label="$t('work.tagManagement.status')" width="200" align="left">
            <template #default="scope">
              <el-switch v-model="scope.row.status" :inactive-value="0" :active-value="1"
                @change="updateStatus(scope.row)" />
            </template>
          </el-table-column>

          <el-table-column property="createUser" :label="$t('work.tagManagement.creator')" width="250" align="left" />

          <el-table-column property="createTime" :label="$t('work.tagManagement.createTime')" width="200"
            align="left" />

          <el-table-column property="management" :label="$t('work.tagManagement.operation')" align="left">
            <template #default="scope">
              <div style="text-align: left;">
                <el-tooltip class="box-item" effect="dark" :content="`${$t('operation.edit')}`" placement="top">
                  <el-icon @click="editItem(scope.row)" class="menu-icon">
                    <EditPen />
                  </el-icon>
                </el-tooltip>
                <el-tooltip class="box-item" effect="dark" :content="`${$t('operation.remove')}`" placement="top">
                  <el-icon @click="deleteItem(scope.row)" class="menu-icon">
                    <Delete />
                  </el-icon>
                </el-tooltip>

                <!-- <el-button  @click="editItem(scope.row)" type="text" size="small" style="color: #ff6600;padding: 0">{{ $t('work.tagManagement.edit') }}</el-button> -->
                <!-- <el-button  @click="deleteItem(scope.row)" type="text" size="small" style="color:  #ff6600;padding: 0">{{ $t('work.tagManagement.delete') }}</el-button> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
          <el-pagination size="small" v-model:current-page="search.current" v-model:page-size="search.size"
            :page-sizes="[10, 20, 30, 40]" background layout="total, sizes, prev, pager, next, jumper"
            :total="search.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>

    <!-- 添加标签-->
    <el-dialog v-model="outerVisible"
      :title="formData.id ? $t('work.tagManagement.editTag') : $t('work.tagManagement.addTag')" width="500px">

      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item :label="$t('work.tagManagement.tagName')" prop="tagNameCn">
          <el-input v-model="formData.tagNameCn" :placeholder="$t('work.tagManagement.pleaseInputTagName')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('work.tagManagement.tagDescription')" prop="tagDescCn">
          <el-input v-model="formData.tagDescCn" :rows="5" type="textarea"
            :placeholder="$t('work.tagManagement.pleaseInputTagDescription')" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">

          <el-button @click="outerVisible = false">{{ $t('work.tagManagement.cancel') }}</el-button>
          <el-button type="primary" @click="saveTag(formRef)">{{ $t('work.tagManagement.save') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {tagManagementList,tagManagementSave,tagManagementUpdate,tagManagementRemove,tagManagementStatus} from '../../api/tagManagement/tagManagement.ts'
import { ref,onMounted,computed,watch} from 'vue'
import { ElTable,ElMessageBox, ElMessage,ElButton,FormInstance } from 'element-plus'
//import type { ComponentSize } from 'element-plus'
import { useI18n } from 'vue-i18n'
//import { Search } from '@element-plus/icons-vue'

const { t } = useI18n()
const formRef = ref();
const search = ref({
  tagNameCn: '',
  total: 0,
  current: 1,
  size: 10,
  keyword: ''
})

// 添加搜索关键字
//const searchKeyword = ref('')

onMounted(()=>{
  handleSearch()
})

interface TableItem {
  id: string | number;
  tagNameCn: string;
  tagDescCn: string;
  status: number;
  createUser: string;
  createTime: string;
  [key: string]: any;
}

let tableData = ref<TableItem[]>([])

const resetSearch = () => {
  // 只清空搜索框文字，不影响其他状态和数据
  search.value.tagNameCn = ''
  // 重置后自动刷新页面
  handleSearch()
}

/* interface SearchParams {
  current: number;
  size: number;
  tagNameCn?: string;
} */

const handleSearch = () => {
  search.value.current = 1  // 搜索时重置到第一页
  const params = {
    current: search.value.current,
    size: search.value.size,
    keyword: search.value.tagNameCn?.trim() || '',
    status: undefined  // 不过滤状态，显示所有数据
  }

  
  // 查询所有数据
  tagManagementList(params).then(res => {
    if (res?.data?.records) {
      // 按状态排序，启用的排在前面
      tableData.value = res.data.records.sort((a: TableItem, b: TableItem) => b.status - a.status)
      search.value.total = res.data.total || 0
    }
  }).catch(error => {
    console.error('搜索失败:', error)
    ElMessage.error(t('work.tagManagement.fetchDataFailed'))
  })
}

const getList = () => {
  const params = {
    current: search.value.current,
    size: search.value.size,
    keyword: search.value.tagNameCn?.trim() || '',
    status: undefined  // 不过滤状态，显示所有数据
  }

  tagManagementList(params).then(res => {
    if (res?.data?.records) {
      // 按状态排序，启用的排在前面
      tableData.value = res.data.records.sort((a: TableItem, b: TableItem) => b.status - a.status)
      search.value.total = res.data.total || 0
    }
  }).catch(error => {
    console.error('获取数据失败:', error)
    ElMessage.error(t('work.tagManagement.fetchDataFailed'))
  })
}

let editItem = (item: any) => {
  console.log(item)
  formData.value = {
    id: item.id,
    tagNameCn: item.tagNameCn,
    tagDescCn: item.tagDescCn
  }
  outerVisible.value = true
}

const deleteItem = (item: any) => {
  ElMessageBox.confirm(
    `${t('work.tagManagement.deleteConfirmMessage')}【${item.tagNameCn}】？`,
    t('work.tagManagement.deleteConfirmTitle'),
    {
      confirmButtonText: t('work.tagManagement.confirmDelete'),
      cancelButtonText: t('work.tagManagement.cancel'),
      type: 'warning'
    }
  ).then(() => {
    tagManagementRemove({ id: item.id })
      .then((res: any) => {
        if (res.success) {
          ElMessage.success(t('work.tagManagement.deleteSuccess'));
          getList();
        } else {
          ElMessage.error(res.message || t('work.tagManagement.deleteFailed'));
        }
      })
      .catch((error) => {
        console.error('Delete failed:', error);
        ElMessage.error(t('work.tagManagement.deleteFailed'));
      });
  }).catch(() => {
    ElMessage.info(t('work.tagManagement.cancel'));
  });
};

let outerVisible = ref(false)
let formData = ref({
  id: '',
  tagNameCn: '',
  tagDescCn: ''
})



let saveTag = (formEl: FormInstance | undefined) => {
  //console.log(formData.value)
  // 验证表单
  if (!formEl) return
  formEl.validate((valid: boolean) => {
    if (valid) {
      if (formData.value.id) {
        tagManagementUpdate(formData.value)
          .then((res: any) => {
            if (res.success) {
              ElMessage.success(t('work.tagManagement.updateSuccess'));
              outerVisible.value = false;
              getList();
            } else {
              ElMessage.error(res.message || t('work.tagManagement.updateFailed'));
            }
          })
          .catch((error) => {
            console.error('Update failed:', error);
            ElMessage.error(t('work.tagManagement.updateFailed'));
          });
      } else {
        tagManagementSave(formData.value)
          .then((res: any) => {
            if (res.success) {
              ElMessage.success(t('work.tagManagement.saveSuccess'));
              outerVisible.value = false;
              formData.value = { tagNameCn: '', tagDescCn: '', id: '' };
              getList();
            } else {
              ElMessage.error(res.message || t('work.tagManagement.saveFailed'));
            }
          })
          .catch((error) => {
            console.error('Save failed:', error);
            ElMessage.error(t('work.tagManagement.saveFailed'));
          });
      }
    }
  });
};

//let size = ref<ComponentSize>('default')

const handleSizeChange = (val: number) => {
  search.value.size = val
  search.value.current = 1
  getList()
}

const handleCurrentChange = (val: number) => {
  search.value.current = val
  getList()
}

const updateStatus = (row: any) => {
  const updatedStatus = row.status;
  const originalStatus = updatedStatus === 1 ? 0 : 1;
  
  tagManagementStatus({ id: row.id })
    .then((res: any) => {
      if (res.success) {
        ElMessage.success(t('work.tagManagement.updateSuccess'));
      } else {
        ElMessage.error(res.message || t('work.tagManagement.updateFailed'));
        row.status = originalStatus;
      }
    })
    .catch((error) => {
      console.error('Update failed:', error);
      ElMessage.error(t('work.tagManagement.updateFailed'));
      row.status = originalStatus;
    });
};
const rules = computed(() => {
  return {
    tagNameCn: [
      { required: true, message: t('work.tagManagement.tagNameRequired'), trigger: 'blur' }
    ]
  }

})
watch(
  () => outerVisible.value,
  (newVal) => {
    if (!newVal) {
      formData.value = { tagNameCn: '', tagDescCn: '', id: '' };
    }
  },
)
// 定义验证规则
// const rules = ref();
</script>

<style lang="scss">
.menu-icon {
  font-size: 20px;
  cursor: pointer;
  margin: 0 10px;
  color: #ff6600;
}

.card-container {
  display: flex;
  flex-direction: column;
  margin: 24px 30px 30px;

  .el-card {
    .el-card__body {
      padding: 0 !important;
    }
  }

  .card-searchTable {
    border: 1px solid rgb(237, 239, 246);
    padding: 10px;
    border-radius: 5px;
  }
}

.card-wrapper {
  margin-bottom: 0px;
}

.demo-pagination-block {
  display: flex;
  justify-content: end;
  margin-top: 16px;
}
</style>