import request from '@/router/axios';

/**
 * 查询设置
 * @param data
 */
export function listSetting() {
  return request({
    method: 'get',
    url: '/api/sgs-mart/trf/trfListTitle',
  })
}
export function queryDffRows(productLineCode) {
  return request({
    method: 'get',
    url: '/api/sgs-mart/trf/queryDffRows',
    params: {
      productLineCode,
    }
  })
}


export function saveSetting(data) {
  return request({
    method: 'post',
    url: '/api/sgs-mart/trf/saveTrfListSetting',
    data: data
  })
}
