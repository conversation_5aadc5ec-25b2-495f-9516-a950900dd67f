<template>
    <el-row :gutter="24" style="width: 100%;">
        <el-col :span="1"></el-col>
        <el-col :span="5">
            <NavigationBar ref="navigationBar" @update:regulations="updateRegulations" />
        </el-col>
        <el-col :span="17" class="content-col">
            <!-- <el-card class="demo-tabs-card"> -->
            <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">

                <el-tab-pane :label="$t('work.tabsPage.contentList')" name="first">
                    <div class="container">
                        <!-- 搜索条件区域 -->
                        <el-card class="search-box">
                            <div class="search-bar-container">
                                <div class="search-conditions" style="margin-bottom: -28px;">
                                    <div class="search-row">
                                        <div class="condition-item">
                                            <span class="condition-label">{{ $t('work.tabsPage.articleStatus') }}:</span>
                                            <el-select v-model="filter" class="filter-select" @change="handleFilterChange">
                                                <el-option :label="$t('work.tabsPage.all')" value="all"></el-option>
                                                <el-option :label="$t('work.tabsPage.published')" value="1"></el-option>
                                                <el-option :label="$t('work.tabsPage.unpublished')" value="0"></el-option>
                                            </el-select>
                                        </div>
                                        
                                        <div class="condition-item search-input-container">
                                            <span class="condition-label">{{ $t('work.tabsPage.searchKeywords') }}:</span>
                                            <div class="search-input-group" style="margin-left: 15px;">
                                                <el-input v-model="searchQuery" :placeholder="$t('work.tabsPage.pleaseEnter')" class="search-input" clearable />
                                                <el-button type="warning" class="search-button" @click="fetchData">
                                                    <el-icon><Search /></el-icon> {{ $t('work.tabsPage.search') }}
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bottom-row">
                                        <!-- 操作按钮 -->
                                        <div class="action-bar">
                                            <el-button type="primary" class="action-button orange-button" @click="handleCreate">
                                                <img src="/tabPage/xinjian.svg" class="custom-icon" />
                                                <span>{{ $t('work.tabsPage.create') }}</span>
                                            </el-button>
                                            <el-button class="action-button gray-button" @click="handleRefresh" style="width: 80px;">
                                                <el-icon><Refresh /></el-icon>
                                                <span style="margin-left: -4px;">{{ $t('work.tabsPage.refresh') }}</span>
                                            </el-button>
                                        </div>
                                        
                                        <!-- 右侧排序 -->
                                        <div class="sort-bar">
                                            <el-dropdown @command="handleCommand" size="small" trigger="click" style="margin-right: -19px;">
                                                <span class="sort-text">
                                                    {{ getSortText(searchObj.sortType) }}
                                                    <el-icon class="sort-icon"><CaretBottom /></el-icon>
                                                </span>
                                                <template #dropdown>
                                                    <el-dropdown-menu>
                                                        <el-dropdown-item command="desc">
                                                            <el-icon><SortDown /></el-icon>{{ $t('work.tabsPage.publishTimeDesc') }}
                                                        </el-dropdown-item>
                                                        <el-dropdown-item command="asc">
                                                            <el-icon><SortUp /></el-icon>{{ $t('work.tabsPage.publishTimeAsc') }}
                                                        </el-dropdown-item>
                                                        <el-dropdown-item command="relation">
                                                            <el-icon><Connection /></el-icon>{{ $t('work.tabsPage.relevance') }}
                                                        </el-dropdown-item>
                                                        <el-dropdown-item command="viewcount">
                                                            <el-icon><View /></el-icon>{{ $t('work.tabsPage.viewCount') }}
                                                        </el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </template>
                                            </el-dropdown>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-card>

                        <!-- 法规列表 -->
                        <el-card class="regulation-list">
                            <div class="regulation-list-main">
                                <!-- 添加无数据提示 -->
                                <div v-if="tableData.length === 0 && !loading" class="no-data">
                                    <el-empty :description="getEmptyDescription">
                                        <template #image>
                                            <el-icon style="font-size: 60px; color: #909399;"><Search /></el-icon>
                                        </template>
                                    </el-empty>
                                </div>

                                <div v-for="(item, index) in tableData" :key="index" class="regulation-item">
                                    <el-row :gutter="24">
                                        <!-- 左侧图片区域 -->
                                        <el-col :span="5" style="height: 150px;">
                                            <div class="image-container">
                                        <template v-if="item.coverPlan">
                                            <img :src="item.coverPlan"
                                                style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;"
                                                @error="() => item.coverPlan = item.categoryId == '1' ? '/img/law.jpg' : '/contentPage/no-data.svg'" />
                                        </template>

                                        <template v-else>
                                            <el-image
                                                :src="item.categoryId == '1' ? '/knowledge/img/law.jpg' : '/knowledge/contentPage/no-data.svg'"
                                                fit="contain" style="width: 100%; height: 100%;" />
                                        </template>
                                    </div>
                                        </el-col>

                                        <!-- 右侧内容区域 -->
                                        <el-col :span="16">
                                            <div class="content-wrapper" style="position: relative; min-height: 150px; display: flex; flex-direction: column;">
                                                <!-- 标题和标签区域 -->
                                                <div class="regulation-header">
                                                    <div class="title-container">
                                                        <div class="title-tag-row">
                                                            <div class="title" @click="item.publishFlag === '0' ? handleEdit(item) : handleView(item)">
                                                                {{ truncateText(item.title, 40) }}
                                                            </div>

                                                            <!-- 标签区域 -->
                                                            <div v-if="item.knowledgeTagEntityList && item.knowledgeTagEntityList.length != 0"
                                                                class="tag-container">
                                                                <div v-for="(tags, index2) in item.knowledgeTagEntityList"
                                                                    :key="index2" class="tag-item">
                                                                    <img src="/contentPage/label.svg" style="width: 16px; height: 12px;" alt="">
                                                                    <span>{{ tags?.tagNameCn }}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- 添加标题简写，仅当栏目为法律法规时显示 -->
                                    <div v-if="item.categoryId == '1'" class="standard-name">
                                            {{ item.ltsTitle || '' }}
                                        </div>

                                                <!-- 栏目 -->
                                                <div class="regulation-category" v-if="item.categoryName">
                                                    {{ item.categoryName }}
                                                </div>

                                                <!-- 内容区 -->
                                                <div class="middle-content" style="flex: 1; position: relative; padding-bottom: 30px;">
                                                    <!-- 描述内容 -->
                                                    <div class="regulation-content">
                                                        {{ item.description || $t('work.tabsPage.noDescription') }}
                                                    </div>
                                                    
                                                    <!-- 附件区域 -->
                                                    <div v-if="item.knowledgeAttachmentList && item.knowledgeAttachmentList.length > 0" 
                                                        class="regulation-attachments">
                                                        <div class="attachment-container">
                                                            <span v-for="(attachment, index) in item.knowledgeAttachmentList"
                                                                :key="index" class="attachment-item"
                                                                @click="downloadFile(attachment)" :title="attachment.fileName">
                                                                <img :src="getFileIcon(attachment.fileName)" 
                                                                    style="width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;" />
                                                                <span class="attachment-name">{{ attachment.fileName }}</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 元信息 -->
                                                <div class="regulation-meta" style="position: absolute; bottom: 0; width: 100%;">
                                                    <div class="meta-info">
                                                        <span v-if="item.publishFlag === '1' && item.publishTime">{{ $t('work.tabsPage.publishTime') }}：{{ formatDate(item.createTime) }}</span>
                                                        <span class="meta-divider">|</span>
                                                        <span>{{ $t('work.tabsPage.createTime') }}：{{ formatDate(item.createTime) }}</span>
                                                        <span class="meta-divider">|</span>
                                                        <span>{{ $t('work.tabsPage.author') }}：{{ item.author }}</span>
                                                        <span class="meta-divider">|</span>
                                                        <span>{{ $t('work.tabsPage.viewCountText') }}：{{ item.viewCount }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                       <!--  <el-divider direction="vertical" /> -->
                                        <!-- 右侧操作区域 -->
                                        <el-col :span="3" class="action-column">
                                            <div class="action-wrapper">
                                                <div class="status-badge">
                                                    <span v-if="Number(item.publishFlag) === 1" class="published">{{ $t('work.tabsPage.published') }}</span>
                                                    <span v-else class="unpublished">{{ $t('work.tabsPage.unpublished') }}</span>
                                                </div>
                                                
                                                <div class="action-buttons" :class="{'two-buttons': item.publishFlag === '1', 'three-buttons': item.publishFlag === '0'}">
                                                    <template v-if="item.categoryId == '1'">
                                                        <div class="action-button view-btn" @click="handleView(item)">
                                                            <el-icon><View /></el-icon>
                                                            <span>{{ $t('work.tabsPage.view') }}</span>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <template v-if="item.publishFlag === '0'">
                                                            <div class="action-button edit-btn" @click="handleEdit(item)">
                                                                <img src="/tabPage/bi.svg" class="custom-icon" />
                                                                <span>{{ $t('work.tabsPage.edit') }}</span>
                                                            </div>
                                                            
                                                            <template v-if="!item.publishTime">
                                                                <div class="action-button publish-btn" @click="handlePublish(item, 'publish')">
                                                                    <el-icon><Promotion /></el-icon>
                                                                    <span>{{ $t('work.tabsPage.publish') }}</span>
                                                                </div>
                                                            </template>
                                                            <template v-else>
                                                                <div class="action-button publish-btn" @click="handlePublish(item, 'republish')">
                                                                    <el-icon><RefreshLeft /></el-icon>
                                                                    <span>{{ $t('work.tabsPage.republish') }}</span>
                                                                </div>
                                                            </template>
                                                            
                                                            <div class="action-button delete-btn" @click="handleDelete(item)">
                                                                <img src="/tabPage/shanchu.svg" class="custom-icon" />
                                                                <span>{{ $t('work.tabsPage.delete') }}</span>
                                                            </div>
                                                        </template>
                                                        <template v-else>
                                                            <div class="action-button view-btn" @click="handleView(item)">
                                                                <el-icon><View /></el-icon>
                                                                <span>{{ $t('work.tabsPage.view') }}</span>
                                                            </div>
                                                            
                                                            <div class="action-button unpublish-btn" @click="handleUnpublish(item)">
                                                                <el-icon><ArrowDown /></el-icon>
                                                                <span>{{ $t('work.tabsPage.unpublish') }}</span>
                                                            </div>
                                                        </template>
                                                    </template>
                                                </div>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>

                            <div class="pagination-container">
                                <el-pagination v-model:current-page="contentPagination.current"
                                    v-model:page-size="contentPagination.size" :page-sizes="[5, 10, 20, 30]"
                                    :total="contentPagination.total" @size-change="handleContentSizeChange"
                                    @current-change="handleContentPageChange"
                                    :total-text="t('work.pagination.total')"
                                    :page-size-text="t('work.pagination.itemsPerPage')"
                                    :prev-text="t('work.pagination.prev')"
                                    :next-text="t('work.pagination.next')"
                                    :jumper-text="t('work.pagination.jumper')"
                                    layout="total, sizes, prev, pager, next, jumper" 
                                    background size="small" />
                            </div>
                        </el-card>
                    </div>
                </el-tab-pane>

                <el-tab-pane :label="$t('work.tabsPage.recyclebin')" name="second">
                    <div class="container">
                        <!-- 搜索条件区域 -->
                        <el-card class="search-box">
                            <div class="search-bar-container">
                                <div class="search-conditions" style="margin-bottom: -28px;">
                                    <div class="search-row">
                                        <!-- <div class="condition-item">
                                            <span class="condition-label">文章状态:</span>
                                            <el-text class="filter-select">全部</el-text>
                                        </div> -->
                                        
                                        <div class="condition-item search-input-container">
                                            <span class="condition-label">{{ $t('work.tabsPage.searchKeywords') }}:</span>
                                            <div class="search-input-group" style="margin-left: 15px;">
                                                <el-input v-model="searchQuery" :placeholder="$t('work.tabsPage.pleaseEnter')" class="search-input" clearable />
                                                <el-button type="warning" class="search-button" @click="handleSearch1">
                                                    <el-icon><Search /></el-icon> {{ $t('work.tabsPage.search') }}
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bottom-row">
                                        <!-- 操作按钮 -->
                                        <div class="action-bar">
                                            <el-button type="primary" class="action-button orange-button" 
                                                @click="handleBatchRestore" :disabled="!hasSelectedItems">
                                                <img src="/tabPage/huifu.svg" class="custom-icon" />
                                                <span>{{ $t('work.tabsPage.batchRestore') }}</span>
                                            </el-button>
                                            <el-button class="action-button gray-button" @click="handleRefresh1" style="width: 80px;">
                                                <el-icon><Refresh /></el-icon>
                                                <span style="margin-left: -4px;">{{ $t('work.tabsPage.refresh') }}</span>
                                            </el-button>
                                        </div>
                                        
                                        <!-- 右侧排序 -->
                                        <div class="sort-bar">
                                            <el-dropdown @command="handleCommand" size="small" trigger="click" style="margin-right: -19px;">
                                                <span class="sort-text">
                                                    {{ getSortText(searchObj.sortType) }}
                                                    <el-icon class="sort-icon"><CaretBottom /></el-icon>
                                                </span>
                                                <template #dropdown>
                                                    <el-dropdown-menu>
                                                        <el-dropdown-item command="desc">
                                                            <el-icon><SortDown /></el-icon>{{ $t('work.tabsPage.publishTimeDesc') }}
                                                        </el-dropdown-item>
                                                        <el-dropdown-item command="asc">
                                                            <el-icon><SortUp /></el-icon>{{ $t('work.tabsPage.publishTimeAsc') }}
                                                        </el-dropdown-item>
                                                        <el-dropdown-item command="relation">
                                                            <el-icon><Connection /></el-icon>{{ $t('work.tabsPage.relevance') }}
                                                        </el-dropdown-item>
                                                        <el-dropdown-item command="viewcount">
                                                            <el-icon><View /></el-icon>{{ $t('work.tabsPage.viewCount') }}
                                                        </el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </template>
                                            </el-dropdown>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-card>




                        <!-- 法规列表 -->
                        <el-card class="regulation-list">
                            <div class="regulation-list-main">
                                <!-- 添加无数据提示 -->
                                <div v-if="recyclebinData.length === 0 && !loading" class="no-data">
                                    <el-empty :description="getEmptyDescription">
                                        <template #image>
                                            <el-icon style="font-size: 60px; color: #909399;"><Search /></el-icon>
                                        </template>
                                    </el-empty>
                                </div>

                                <div v-for="(item, index) in recyclebinData" :key="index" 
                                     class="regulation-item" 
                                     :class="{ 'selected': item.isSelected }"
                                     @click.stop="handleSelectionChange(item)">
                                    <div class="selection-indicator" v-if="item.isSelected">
                                        <el-icon><Select /></el-icon>
                                    </div>
                                    <el-row :gutter="24" style="align-items: center;">
                                        <!-- 左侧图片区域 -->
                                        <el-col :span="5" style="height: 150px;">
                                            <div class="image-container">
                                        <template v-if="item.coverPlan">
                                            <img :src="item.coverPlan"
                                                style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;"
                                                @error="() => item.coverPlan = item.categoryId == '1' ? '/img/law.jpg' : '/contentPage/no-data.svg'" />
                                        </template>

                                        <template v-else>
                                            <el-image
                                                :src="item.categoryId == '1' ? '/knowledge/img/law.jpg' : '/knowledge/contentPage/no-data.svg'"
                                                fit="contain" style="width: 100%; height: 100%;" />
                                        </template>
                                    </div>
                                        </el-col>

                                        <!-- 右侧内容区域 -->
                                        <el-col :span="16">
                                            <div class="content-wrapper" style="position: relative; min-height: 150px; display: flex; flex-direction: column;">
                                                <!-- 标题和标签区域 -->
                                                <div class="regulation-header">
                                                    <div class="title-container">
                                                        <div class="title-tag-row">
                                                            <div class="title">
                                                                {{ truncateText(item.title, 40) }}
                                                            </div>

                                                            <!-- 标签区域 -->
                                                            <div v-if="item.knowledgeTagEntityList && item.knowledgeTagEntityList.length != 0"
                                                                class="tag-container">
                                                                <div v-for="(tags, index2) in item.knowledgeTagEntityList"
                                                                    :key="index2" class="tag-item">
                                                                    <img src="/contentPage/label.svg" style="width: 16px; height: 12px;" alt="">
                                                                    <span>{{ tags?.tagNameCn }}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- 添加标题简写，仅当栏目为法律法规时显示 -->
                                    <div v-if="item.categoryId == '1'" class="standard-name">
                                            {{ item.ltsTitle || '' }}
                                        </div>
                                                <div class="regulation-category" v-if="item.categoryName">
                                                    {{ item.categoryName }}
                                                </div>

                                                <!-- 内容区 -->
                                                <div class="middle-content" style="flex: 1; position: relative; padding-bottom: 30px;">
                                                    <!-- 描述内容 -->
                                                    <div class="regulation-content">
                                                        {{ item.description || $t('work.tabsPage.noDescription') }}
                                                    </div>
                                                    
                                                    <!-- 附件区域 -->
                                                    <div v-if="item.knowledgeAttachmentList && item.knowledgeAttachmentList.length > 0" 
                                                        class="regulation-attachments">
                                                        <div class="attachment-container">
                                                            <span v-for="(attachment, index) in item.knowledgeAttachmentList"
                                                                :key="index" class="attachment-item"
                                                                @click="downloadFile(attachment)" :title="attachment.fileName">
                                                                <img :src="getFileIcon(attachment.fileName)" 
                                                                    style="width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;" />
                                                                <span class="attachment-name">{{ attachment.fileName }}</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 元信息 -->
                                                <div class="regulation-meta" style="position: absolute; bottom: 0; width: 100%;">
                                                    <div class="meta-info">
                                                        <span v-if="item.publishTime">{{ $t('work.tabsPage.publishTime') }}：{{ formatDate(item.createTime) }}</span>
                                                        <span class="meta-divider">|</span>
                                                        <span>{{ $t('work.tabsPage.deleteTime') }}：{{ formatDate(item.recycleTime) }}</span>
                                                        <span class="meta-divider">|</span>
                                                        <span>{{ $t('work.tabsPage.author') }}：{{ item.author }}</span>
                                                        <span class="meta-divider">|</span>
                                                        <span>{{ $t('work.tabsPage.viewCountText') }}：{{ item.viewCount }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                        
                                        <!-- 右侧操作区域 -->
                                        <el-col :span="3" class="action-column">
                                            <div class="action-wrapper">
                                                <div class="action-buttons recycle-buttons">
                                                    <div class="action-button restore-btn" @click="handleRestore(item)">
                                                        <img src="/tabPage/huifu1.svg" class="custom-icon" />
                                                        <span>{{ $t('work.tabsPage.restore') }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>


                            <div class="pagination-container">
                                <el-pagination v-model:current-page="recyclePagination.current"
                                    v-model:page-size="recyclePagination.size" :page-sizes="[5, 10, 20, 30]"
                                    :total="recyclePagination.total" @size-change="handleRecycleSizeChange"
                                    @current-change="handleRecyclePageChange"
                                    :total-text="t('work.pagination.total')"
                                    :page-size-text="t('work.pagination.itemsPerPage')"
                                    :prev-text="t('work.pagination.prev')"
                                    :next-text="t('work.pagination.next')"
                                    :jumper-text="t('work.pagination.jumper')"
                                    layout="total, sizes, prev, pager, next, jumper" 
                                    background size="small" />
                            </div>
                        </el-card>
                    </div>


                </el-tab-pane>
            </el-tabs>



            <!-- </el-card> -->
        </el-col>
        <el-col :span="1"></el-col>
    </el-row>


</template>




<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { SortDown, SortUp, Connection, View, CaretBottom, Search,Refresh,Promotion, RefreshLeft,Select,ArrowDown } from '@element-plus/icons-vue'
/* import type { CollapseModelValue } from 'element-plus' */
import NavigationBar from '../../components/navigationBar/navigationBar.vue'
import { getCloudFileURL,attachmentviewcount } from '@/api/getCloudFileUrl.ts'
import { tabsPagePage, tabsPageRemove, tabsPageRecyclebinpage, tabsPageDetail, tabsPageRecyclebinupdate, tabsPageRecyclebinupdatebybatch, tabsPagePulishflagupdate } from '../../api/tabsPage/tabsPage.ts'
import { ElMessage } from "element-plus";
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter();
const navigationBar = ref()
/* const dataList = ref([]); */
const categoryId = ref(0);
/* const fetchPageData = async () => {
  try {
    // console.log(categoryId.value.categoryId);
    // return
    const params = {
      current: contentPagination.value.current,
      size: contentPagination.value.size,
      categoryId: categoryId.value,
    };

    const res = await tabsPagePage(params); // 调用分页接口
    if (res && res.data) {
      tableData.value = res.data.records; // 更新数据列表
      contentPagination.value.total = res.data.total; // 更新数据总数
    }
  } catch (error) {
    console.error('分页数据加载失败:', error);
  }
}; */
const updateRegulations = (params: any) => {
    categoryId.value = params.categoryId; // 更新分类 ID
    contentPagination.value.current = 1; // 重置分页页码
    // 不重置filter的值，保持用户选择的筛选状态
    fetchData(); // 调用接口加载数据
};
/* import { Search, List, ArrowDown } from "@element-plus/icons-vue"; */
/* import { NONAME } from 'dns' */
/* import { url } from 'inspector' */
/* const collapseActive = ref([]) */
const searchQuery = ref(""); // 搜索输入框
const activeName = ref("first"); // 选中的 Tab 名称
// 从本地存储中读取过滤条件，如果没有则默认为"all"
const filter = ref(localStorage.getItem('tabsPageFilter') || "all"); // 过滤条件
/* const tableData = ref([]); */

//const listData = ref([]);
const fetchData = async () => {
    try {
        loading.value = true;
        const params = {
            current: contentPagination.value.current,
            size: contentPagination.value.size,
            keyword: searchQuery.value || '',  // 添加关键字搜索
            publishFlag: filter.value === 'all' ? undefined : filter.value,
            orderField: searchObj.value.sortType,
            categoryId: categoryId.value // 添加分类ID参数
        };

        console.log('搜索参数:', params);
        console.log('当前过滤状态:', filter.value);
        const response = await tabsPagePage(params);
        console.log('搜索响应:', response);

        if (response?.data?.records) {
            tableData.value = response.data.records;
            contentPagination.value.total = response.data.total || 0;
        }
    } catch (error) {
        console.error('搜索失败:', error);
        ElMessage.error('搜索失败');
    } finally {
        loading.value = false;
    }
};
const contentPagination = ref({
    current: 1,
    size: 5,
    total: 0
})

const recyclePagination = ref({
    current: 1,
    size: 5,
    total: 0
})
const handleContentSizeChange = (val: number) => {
    contentPagination.value.size = val
    contentPagination.value.current = 1
    fetchData()
    // 滚动到列表顶部
    const listElement = document.querySelector('.regulation-list-main')
    if (listElement) {
        listElement.scrollTop = 0
    }
}
const handleContentPageChange = (val: number) => {
    contentPagination.value.current = val
    fetchData()
    // 滚动到列表顶部
    const listElement = document.querySelector('.regulation-list-main')
    if (listElement) {
        listElement.scrollTop = 0
    }
}
const handleRecycleSizeChange = (val: number) => {
    recyclePagination.value.size = val
    recyclePagination.value.current = 1
    fetchRecyclebinData()
    // 滚动到列表顶部
    const listElement = document.querySelector('.regulation-list-main')
    if (listElement) {
        listElement.scrollTop = 0
    }
}
const handleRecyclePageChange = (val: number) => {
    recyclePagination.value.current = val
    fetchRecyclebinData()
    // 滚动到列表顶部
    const listElement = document.querySelector('.regulation-list-main')
    if (listElement) {
        listElement.scrollTop = 0
    }
}
const downloadFile = (item: any) => {
    getCloudFileURL({ cloudID: item.cloudId, systemID: 1, networkType: 2 }).then((res: any) => {
        console.log("item=>",item);
        const ids = [{id: item.id.toString()}]
        attachmentviewcount(ids).then((res:any) => {
            if(res?.code === 200){   
                console.log("file download count=>",res);
            }
        })
        window.open(res)

    })
}
const handletime = () => {
    const now = new Date();

    // 获取年月日时分秒
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 获取月份，注意 `getMonth()` 返回的是 0 到 11，因此加 1
    const day = String(now.getDate()).padStart(2, '0'); // 获取日期，确保是两位数
    const hours = String(now.getHours()).padStart(2, '0'); // 获取小时，确保是两位数
    const minutes = String(now.getMinutes()).padStart(2, '0'); // 获取分钟，确保是两位数
    const seconds = String(now.getSeconds()).padStart(2, '0'); // 获取秒，确保是两位数

    // 组合成日期时间字符串
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
const handlePublish = async (item: tableData, type: string) => {
    try {
        const params = {
            id: item.id,
            publishFlag: '1',
            publishTime: handletime() // 总是使用当前时间作为发布时间
        };
        await tabsPagePulishflagupdate(params);
        ElMessage.success(type === 'publish' ? t('work.tabsPage.publishSuccess') : t('work.tabsPage.publishSuccess'));
        fetchData();
    } catch (error) {
        ElMessage.error(t('work.tabsPage.operationFailed'));
    }
};
const handleUnpublish = async (item: tableData) => {
    try {
        await tabsPagePulishflagupdate({
            id: item.id,
            publishFlag: '0',
            publishTime: '' // 清空发布时间
        });
        ElMessage.success(t('work.tabsPage.unpublishSuccess'));
        fetchData();
    } catch (error) {
        ElMessage.error(t('work.tabsPage.operationFailed'));
    }
};
const handleView = (item: tableData) => {
    // If the item is a URL type article, open the URL directly
    if (item.contentType === 'url' && item.content) {
        window.open(item.content, '_blank')
        return
    }
    
    // Otherwise, navigate to the detail page as usual
    const activeIndex = navigationBar.value.activeIndex;
    const menuList = navigationBar.value.menuList;

    window.open('/knowledge/detailedArticle/detailedArticle?id=' + item.id + '&categoryId=' + item.categoryId + '&menu=' + encodeURIComponent(JSON.stringify(menuList[activeIndex])));
};
const handleCreate = async () => {
    // 如果是法律法规栏目，显示提示信息并返回
    if (isLegalRegulationCategory.value) {
        ElMessage({
            message: t('work.tabsPage.noAddArticle'),
            type: 'warning',
            duration: 2000,
            showClose: true
        });
        return;
    }
    
    // 不是法律法规栏目，正常执行新建
    const activeIndex = navigationBar.value.activeIndex;
    //router.push('/knowledge/pageCreate/pageCreate?categoryId=' + (activeIndex==0?'':activeIndex));
    router.push({name:'pageCreate',query:{categoryId:activeIndex==0?'':activeIndex}})
};
const handleEdit = async (item: tableData) => {
    try {
        // 调用接口获取编辑数据
        const res = await tabsPageDetail({ id: item.id });

        // 检查接口是否返回数据
        if (res && res.data) {
            //router.push('/knowledge/pageCreate/pageCreate?id=' + item.id + '&categoryId=' + item.categoryId);
            router.push({name:'pageCreate',query:{id:item.id,categoryId:item.categoryId}})
        } else {
            ElMessage.error('获取编辑数据失败');
        }
    } catch (error) {
        console.error("获取编辑数据失败:", error);
        ElMessage.error('获取编辑数据失败');
    }
};
const handleDelete = async (item: tableData) => {
    try {
        await tabsPageRemove({ id: item.id, });
        ElMessage.success(t('work.tabsPage.deleteSuccess'));
        fetchData();
        fetchRecyclebinData();
    } catch (error) {
        ElMessage.error(t('work.tabsPage.deleteFailed'));
    }
};


const searchObj = ref({
    searchQuery: '',
    searchType: [],
    publishDateRange: [],
    selectedTags: [],
    enactmentDate: '',
    sortType: '',
    ageGroup: '',
    range: '',
    region: '',
    area: '',
    province: '',
    docType: '',
    keywords: ''

})
const tableData = ref<tableData[]>([])
interface tableData {
    id?: string;
    title: string;
    tag?: string[];
    url?: string;
    coverPlan?: string;
    status?: string;
    createTime?: string;
    updateTime?: string;
    viewCount?: number;
    labels?: string;
    createUser?: string;
    content?: string;
    contentType?: string; // Added to identify URL type
    knowledgeAttachmentList?: any[]
    knowledgeTagEntityList?: { name: string; value: string; tagNameCn: string; }[]; // 新增属性
    categoryId?: string;
    approveFlag?: string;
    publishTime?: string;
    router?: any;
    publishFlag?: string;
    description?: string;
    categoryNameCn?: string;
    categoryName?: string;
    author?: string;
    knowledgeCategoryEntity?: {
        categoryNameCn: string;
        categoryName: string;

    };
    ltsTitle?: string;
}
/* interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
} */

interface RecyclebinItem {
    id: string;
    title: string;
    content: string;
    createUser: string;
    publishTime: string;
    recycleTime: string;
    viewCount: number;
    tag: string[];
    knowledgeAttachmentList: any[];
    url?: string;
    coverPlan?: string;
    categoryId?: string;
    knowledgeTagEntityList?: { name: string; value: string; tagNameCn: string; }[];
    isSelected?: boolean;
    description?: string;
    author?: string;
    categoryName?: string;
    createTime?: string;
    ltsTitle?: string;
}

const recyclebinData = ref<RecyclebinItem[]>([])
const hasSelectedItems = ref(false);

// 处理选择变化
const handleSelectionChange = (item: RecyclebinItem) => {
    item.isSelected = !item.isSelected;
    hasSelectedItems.value = recyclebinData.value.some(item => item.isSelected);
};

// 处理单个恢复
const handleRestore = async (item: RecyclebinItem) => {
    try {
        const data = {
            id: item.id
        };
        await tabsPageRecyclebinupdate(data);
        ElMessage.success(t('work.tabsPage.restoreSuccess'));
        fetchRecyclebinData();
        fetchData(); // 刷新内容列表
    } catch (error) {
        console.error('恢复失败:', error);
        ElMessage.error(t('work.tabsPage.restoreFailed'));
    }
};

// 处理批量恢复
const handleBatchRestore = async () => {
    try {
        const selectedItems = recyclebinData.value.filter(item => item.isSelected);

        if (selectedItems.length === 0) {
            ElMessage.warning(t('work.tabsPage.selectRestoreItems'));
            return;
        }

        const data = selectedItems.map(item => { return { id: item.id } });
        await tabsPageRecyclebinupdatebybatch(data);


        ElMessage.success(t('work.tabsPage.batchRestoreSuccess'));
        fetchRecyclebinData();
        fetchData(); // 刷新内容列表
    } catch (error) {
        console.error('批量恢复失败:', error);
        ElMessage.error(t('work.tabsPage.batchRestoreFailed'));
    }
};

// 修改获取回收站数据的函数，添加选择状态
const fetchRecyclebinData = async () => {
    try {
        const params = {
            
            /* query: searchQuery.value, */
            current: recyclePagination.value.current,
            size: recyclePagination.value.size,
        };
        const res = await tabsPageRecyclebinpage(params);
        if (res && res.data) {
            recyclebinData.value = (res.data.records || []).map((item: any) => ({
                ...item,
                isSelected: false
            })) as RecyclebinItem[];
            recyclePagination.value.total = res.data.total || 0;
        } else {
            ElMessage.error("获取数据失败");
        }
    } catch (error) {
        console.error("接口请求失败", error);
        ElMessage.error("接口请求失败");
    }
};

const handleRefresh1 = () => {
    fetchRecyclebinData();
};
const handleSearch1 = async () => {
    try {
        loading.value = true;
        const params = {
            current: recyclePagination.value.current,
            size: recyclePagination.value.size,
            keyword: searchQuery.value || '',  // 添加关键字搜索
            orderField: searchObj.value.sortType
        };

        console.log('回收站搜索参数:', params);
        const response = await tabsPageRecyclebinpage(params);
        console.log('回收站搜索响应:', response);

        if (response?.data?.records) {
            recyclebinData.value = response.data.records;
            recyclePagination.value.total = response.data.total || 0;
        }
    } catch (error) {
        console.error('回收站搜索失败:', error);
        ElMessage.error('搜索失败');
    } finally {
        loading.value = false;
    }
};
/* const regulations2 = ref([
    {
        title: 'SB-253 Climate Corporate Data Accountability Act',
        tag: ['chemical', 'mangement'],
        url: "https://seopic.699pic.com/photo/50047/7161.jpg_wh1200.jpg",
        status: '生效中',
        publishDate: '2018-07-04 06:48',
        effectiveDate: '2018-07-04 06:48',
        views: 33,

        author: '系统管理员',
        content: 'In the context of greenhouse gas reduction and climate goals, the bill requires...'
    },
    {
        title: 'SB-253 Climate Corporate Data Accountability Act',
        tag: ['carbon'],
        url: "https://seopic.699pic.com/photo/50053/1248.jpg_wh1200.jpg",
        status: '生效中',
        publishDate: '2018-07-04 06:48',
        effectiveDate: '2018-07-04 06:48',
        views: 33,

        author: '系统管理员',
        content: 'In the context of greenhouse gas reduction and climate goals, the bill requires...'
    },
    {
        title: 'SB-253 Climate Corporate Data Accountability Act',
        url: 'https://img95.699pic.com/photo/50055/9941.jpg_wh860.jpg',
        status: '生效中',
        publishDate: '2018-07-04 06:48',
        effectiveDate: '2018-07-04 06:48',
        views: 33,

        author: '系统管理员',
        content: 'In the context of greenhouse gas reduction and climate goals, the bill requires...'
    },
    {
        title: 'SB-253 Climate Corporate Data Accountability Act',
        url: 'https://img95.699pic.com/photo/50055/9941.jpg_wh860.jpg',
        status: '生效中',
        publishDate: '2018-07-04 06:48',
        effectiveDate: '2018-07-04 06:48',
        views: 33,

        author: '系统管理员',
        content: 'In the context of greenhouse gas reduction and climate goals, the bill requires...'
    },


]) */

const handleCommand = (e: any) => {
    searchObj.value.sortType = e;
    fetchData(); // 当排序改变时，触发搜索
}
/* const fits = ['fill'] */
/* const labels =ref([]) */
/* const url = ['https://seopic.699pic.com/photo/50047/7161.jpg_wh1200.jpg',
    'https://seopic.699pic.com/photo/50053/1248.jpg_wh1200.jpg',
    'https://img95.699pic.com/photo/50055/9941.jpg_wh860.jpg',
    'https://img95.699pic.com/photo/50055/9941.jpg_wh860.jpg'] */




// 搜索函数
/* const handleSearch = () => {
    console.log('搜索条件:', searchObj.value)
} */
/* const handleChange = (val: CollapseModelValue) => {
    //   this.add=!this.add
} */
/* const toggleDropdown = () => {
  dropdownVisible.value = !dropdownVisible.value
} */
/* const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(10) */

/* const goToPreviousPage = () => {
    if (currentPage.value > 1) {
        currentPage.value -= 1
    }
} */
/* const goToNextPage = () => {
    if (currentPage.value < total.value) {
        currentPage.value += 1
    }
} */


/* const handleClick = (tab: TabsPaneContext) => {
    if (tab.props.name === "first") {
        fetchData();
    }
}; */
const handleClick = (tab: TabsPaneContext) => {
    console.log("切换到：", tab.props.name);
    if (tab.props.name === "first") {
        contentPagination.value.current = 1;
        fetchData(); // 加载 "内容列表"
    } else if (tab.props.name === "second") {
        recyclePagination.value.current = 1;
        fetchRecyclebinData(); // 加载 "回收站"
    }
};
const handleRefresh = () => {
    fetchData();
};
onMounted(() => {
    // 如果有本地存储的筛选条件，应用到 filter
    const savedFilter = localStorage.getItem('tabsPageFilter');
    if (savedFilter) {
        filter.value = savedFilter;
    }
    
    fetchData();
    if (activeName.value === "second") {
        fetchRecyclebinData();
    }
});

const getSortText = (type: string) => {
    switch (type) {
        case 'desc': return t('work.tabsPage.publishTimeDesc');
        case 'asc': return t('work.tabsPage.publishTimeAsc');
        case 'relation': return t('work.tabsPage.relevance');
        case 'viewcount': return t('work.tabsPage.viewCount');
        default: return t('work.tabsPage.defaultSort');
    }
}

// 添加文本截断方法
const truncateText = (text: string | undefined, length: number) => {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
};

const loading = ref(false);

// 添加获取空状态描述的计算属性
const getEmptyDescription = computed(() => {
    return searchQuery.value 
        ? t('work.tabsPage.noResultsFound', { query: searchQuery.value })
        : t('work.tabsPage.noData');
});

// 修改handleFilterChange函数保存筛选状态
const handleFilterChange = () => {
    contentPagination.value.current = 1; // 重置当前页为第一页
    // 保存当前选择的状态到本地存储
    localStorage.setItem('tabsPageFilter', filter.value);
    fetchData(); // 重新获取数据
};

const getFileIcon = (filename: string) => {
    const fileType = filename.split(".").pop()?.toLowerCase();
    switch (fileType) {
        case "docx": return "/knowledge/knowledgeBaseHome/worddocicon.svg";
        case "doc": return "/knowledge/knowledgeBaseHome/worddocicon.svg";
        case "pdf": return "/knowledge/knowledgeBaseHome/pdfdocicon.svg";
        case "pptx": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg";
        case "ppt": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg";
        case "xls": return "/knowledge/knowledgeBaseHome/exceldocicon.svg";
        case "xlsx": return "/knowledge/knowledgeBaseHome/exceldocicon.svg";
        case "txt": return "/knowledge/knowledgeBaseHome/txtdocicon.svg";
        case "md": return "/knowledge/knowledgeBaseHome/xmldocicon.svg";
        case "png": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "jpg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "jpeg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "gif": return "/knowledge/knowledgeBaseHome/videodocicon.svg";
        case "bmp": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "ico": return "/knowledge/knowledgeBaseHome/imgdocicon.svg";
        case "zip": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "rar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "7z": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "tar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "gz": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "bz2": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "iso": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        case "dmg": return "/knowledge/knowledgeBaseHome/packageddocicon.svg";
        default: return "/knowledge/knowledgeBaseHome/generaldocicon.svg";
    }
};

// 添加一个计算属性判断当前是否为法律法规栏目
const isLegalRegulationCategory = computed(() => {
    // 获取当前选中的菜单项
    const activeIndex = navigationBar.value?.activeIndex;
    const menuList = navigationBar.value?.menuList || [];
    
    // 如果没有选中项或菜单列表为空，返回 false
    if (!activeIndex || menuList.length === 0) return false;
    
    // 获取当前选中的菜单项
    const currentMenu = menuList[activeIndex];
    
    // 判断是否为法律法规栏目，可能需要根据实际数据结构调整判断条件
    // 这里假设法律法规栏目的标识是 name 或 categoryName 包含"法律法规"
    return (currentMenu?.name && currentMenu.name.includes('法律法规')) || 
           (currentMenu?.categoryName && currentMenu.categoryName.includes('法律法规'));
});
const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return '-';
    // 如果日期格式为 yyyy-MM-dd HH:mm:ss，截取前10位即可得到 yyyy-MM-dd
    if (dateString.length >= 10) {
        return dateString.substring(0, 10);
    }
    return dateString;
};
</script>



<style lang="scss" scoped>
/* 顶部导航 */
.tabs {
    display: flex;
    font-size: 16px;
    border-bottom: 2px solid #e5e5e5;
    padding-bottom: 5px;
    margin-bottom: 10px;
}

:deep(.el-tabs__header) {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin: 20px 0 0;
    padding: 0;
    position: relative;
    background: #fff;
}
:deep(.el-tabs__nav-scroll){
    padding-left: 26px;
}
 :deep(.el-input--large .el-input__wrapper) {
    padding: 1px 15px;
    margin-left: -15px;
}
::v-deep(.el-tabs__item) {
  font-size: 17px !important;
}
/* .custom-tab-pane .el-tabs__item {
  font-size: 16px;
} */
.active-tab {
    color: #e86b1f;
    font-weight: bold;
    border-bottom: 2px solid #e86b1f;
    padding-bottom: 5px;
    margin-right: 15px;
}

.inactive-tab {
    color: #666;
    margin-right: 15px;
    cursor: pointer;
}

.inactive-tab:hover {
    color: #e86b1f;
}

.container {
    background: #f5f7fa;

    .search-box {
        margin-bottom: 20px;
        padding: 20px !important;

        .el-card__body {
            padding: 15px !important;
        }
        
        .search-bar-container {
            .search-conditions {
                display: flex;
                flex-direction: column;
                gap: 15px;
                
                .search-row {
                    display: flex;
                    width: 100%;
                    
                    .condition-item {
                        display: flex;
                        align-items: center;
                        margin-right: 20px;
                        
                        .condition-label {
                            width: 80px;
                            color: #606266;
                            font-size: 14px;
                            white-space: nowrap;
                        }
                        
                        .filter-select {
                            width: 160px;
                        }
                        
                        &.search-input-container {
                            flex: 1;
                            max-width: 450px;
                            
                            .search-input-group {
                                display: flex;
                                width: 100%;
                                
                                .search-input {
                                    flex-grow: 1;
                                    max-width: 200px;
                                }
                                
                                .search-button {
                                    background-color: #e86b1f;
                                    color: #fff;
                                    margin-left: 15px;
                                }
                            }
                        }
                    }
                }
                
                .bottom-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 10px;
                    
                    .action-bar {
                        display: flex;
                        gap: 15px;
                        
                        .action-button {
                            display: flex;
                            align-items: center;
                            gap: 5px;
                            padding: 8px 16px;

            
                            
                            &.orange-button {
                                background-color: #e86b1f;
                                border-color: #e86b1f;
                                color: white;
                                
                                &:hover, &:focus {
                                    background-color: #f07c35;
                                    border-color: #f07c35;
                                }
                                
                                &[disabled] {
                                    background-color: rgba(232, 107, 31, 0.5);
                                    border-color: rgba(232, 107, 31, 0.5);
                                    color: rgba(255, 255, 255, 0.7);
                                }
                            }
                            
                            &.gray-button {
                                background-color: #f2f2f2;
                                border-color: #e4e4e4;
                                color: #606266;
                                
                                &:hover, &:focus {
                                    background-color: #e4e4e4;
                                    border-color: #d4d4d4;
                                }
                            }
                            
                            .el-icon {
                                margin-right: 5px;
                            }
                            
                            .custom-icon {
                                width: 14px;
                                height: 14px;
                                margin-right: 5px;
                            }
                        }
                    }
                    
                    .sort-bar {
                        margin-left: auto;
                        
                        .sort-text {
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                            font-size: 14px;
                            color: #606266;
                            
                            .sort-icon {
                                margin-left: 3px;
                                font-size: 12px;
                                color: #606266;
                            }
                            
                            &:hover {
                                color: #e86b1f;
                            }
                        }
                        
                        .el-dropdown-menu {
                            min-width: 120px;
                            
                            .el-dropdown-item {
                                display: flex;
                                align-items: center;
                                font-size: 14px;
                                
                                .el-icon {
                                    margin-right: 8px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .regulation-list {
        padding: 0 !important;
        
        .el-card__body {
            padding: 0 !important;
        }
        
        .regulation-list-main {
            max-height: calc(100vh - 265px);
            overflow-x: hidden;
            overflow-y: auto;
            
            .regulation-item {
                position: relative;
                padding: 15px 10px;
                border-bottom: 1px solid #ebeef5;
                transition: all 0.3s;
                height: 180px; /* 添加固定高度 */
                box-sizing: border-box;
                
                &:hover {
                    background-color: rgba(236, 245, 255, 0.3);
                }
                
                &.selected {
                    border: 2px solid #e86b1f;
                    padding: 13px 18px;
                    height: 180px; /* 确保选中状态下高度一致 */
                }

                .selection-indicator {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    width: 20px;
                    height: 20px;
                    color: #e86b1f;
                    
                    .el-icon {
                        font-size: 18px;
                    }
                }

                .el-row {
                    height: 100%;
                }

                .image-container {
                    width: 100%;
                    height: 150px !important;
                    overflow: hidden;
                    border-radius: 4px;
                    background-color: #f5f7fa;
                }

                .content-wrapper {
                    height: 150px !important;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                }

                .regulation-header {
                    margin-bottom: 10px;
                    
                    .title-container {
                        .title-tag-row {
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            flex-wrap: wrap;
                            
                            .title {
                                font-size: 16px;
                                font-weight: bold;
                                color: #303133;
                                cursor: pointer;
                                margin-right: 15px;
                                line-height: 1.5;
                                
                                &:hover {
                                    color: #409EFF;
                                }
                            }
                            
                            .tag-container {
                                display: flex;
                                flex-wrap: wrap;
                                gap: 5px;
                                
                                .tag-item {
                                    display: flex;
                                    align-items: center;
                                    padding: 0 5px;
                                    margin-right: 8px;
                                    font-size: 12px;
                                    color: #e86b1f;
                                    
                                    img {
                                        margin-right: 4px;
                                    }
                                }
                            }
                        }
                    }
                }

                .regulation-category {
                    display: inline-block;
                    background-color: rgba(255, 181, 131, 1);
                    color: white;
                    font-size: 12px;
                    padding: 2px 10px;
                    border-radius: 10px;
                    margin-bottom: 10px;
                    white-space: nowrap;
                    width: auto;
                    max-width: fit-content;
                }

                .regulation-content {
                    color: #606266;
                    font-size: 13px;
                    line-height: 1.5;
                    margin: 10px 0;
                    height: 40px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    //-webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    margin-top: -5px;
                }

                .regulation-attachments {
                    margin: 5px 0;
                    position: absolute;
                    bottom: 5px;
                    width: 100%;
                    
                    .attachment-container {
                        display: flex;
                        flex-wrap: nowrap;
                        gap: 10px;
                        white-space: nowrap;
                        padding-bottom: 5px;
                        
                        &::-webkit-scrollbar {
                            height: 4px;
                        }
                        
                        &::-webkit-scrollbar-thumb {
                            background-color: #ddd;
                            border-radius: 4px;
                        }
                        
                        &::-webkit-scrollbar-track {
                            background-color: #f5f5f5;
                        }
                        
                        .attachment-item {
                            display: inline-flex;
                            align-items: center;
                            background-color: transparent;
                            padding: 4px 10px;
                            border-radius: 3px;
                            font-size: 12px;
                            color: #606266;
                            cursor: pointer;
                            max-width: 130px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            flex-shrink: 0;
                            
                            &:hover {
                                color: #409EFF;
                            }
                            
                            .attachment-name {
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }

                .regulation-meta {
                    margin-top: 15px;
                    font-size: 11px;
                    color: #909399;
                    margin-bottom: 0px;
                    
                    .meta-info {
                        display: flex;
                        align-items: center;
                        white-space: nowrap;
                        overflow: hidden;
                        
                        span {
                            white-space: nowrap;
                            margin-right: 5px;
                        }
                        
                        .meta-divider {
                            color: #dcdfe6;
                            margin: 0 5px;
                        }
                    }
                }

                .action-column {
                    height: 100%;
                    
                    .action-wrapper {
                        height: 150px !important;
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        padding-bottom: 10px;
                        box-sizing: border-box;
                        
                        .status-badge {
                            position: relative;
                            display: flex;
                            justify-content: center;
                            width: 100%;
                            margin-bottom: 20px;
                            
                            .published {
                                color: #e86b1f;
                                background-color: rgb(255, 243, 235);
                                border: 1px solid #e86b1f;
                                padding: 3px 8px;
                                border-radius: 2px;
                                font-size: 12px;
                            }
                            
                            .unpublished {
                                color: #409EFF;
                                background-color: rgb(238, 244, 253);
                                border: 1px solid #409EFF;
                                padding: 3px 8px;
                                border-radius: 2px;
                                font-size: 12px;
                            }
                        }
                        
                        .action-buttons {
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 18px;
                            margin-left: 0;
                            height: calc(100% - 50px);
                            justify-content: space-between;
                            width: 100%;
                            
                            &.two-buttons {
                                justify-content: space-around;
                                padding: 25px 0;
                            }
                            
                            &.three-buttons {
                                justify-content: space-between;
                                padding: 15px 0;
                            }
                            
                            &.recycle-buttons {
                                justify-content: center;
                                height: 100%;
                                align-items: center;
                            }
                            
                            .action-button {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 13px;
                                cursor: pointer;
                                white-space: nowrap;
                                width: 100%;
                                
                                .el-icon {
                                    margin-right: 5px;
                                    font-size: 14px;
                                }
                                
                                .custom-icon {
                                    width: 14px;
                                    height: 14px;
                                    margin-right: 5px;
                                }
                                
                                &.edit-btn, &.publish-btn, &.view-btn {
                                    color: #e86b1f;
                                }
                                
                                &.delete-btn, &.unpublish-btn {
                                    color: #909399;
                                }
                                
                                &.restore-btn {
                                    color: #e86b1f;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        .no-data {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 300px;
        }
    }

    .pagination-container {
        display: flex;
        justify-content: flex-end;
        padding: 20px 0;
    }
}
.standard-name {
                    color: #909399;
                    font-size: 12px;
                    margin-top: -10px;
                    margin-bottom: 0px;
                    line-height: 1.2;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
/* 以下是回收站部分的样式，保留原有样式 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    gap: 10px;
}

/* 操作按钮 */
.action-bar {
    display: flex;
    margin-bottom: 10px;
}

.action-button {
    font-size: 14px;
}

/* 搜索栏 */
.search-bar {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    width: 55%;
    .filter-select {
    width: 80px;
    margin-left: 16px;
  
}
}

.search-input {
    flex-grow: 1;
   
}

.search-button {
    background-color: #e86b1f;
    color: #fff;
}

.sort-menu:hover {
    color: #e86b1f;
}

.tag {
    display: inline-block;
    vertical-align: middle;
}

.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
    
    .tag-item {
        display: flex;
        align-items: center;
        padding: 0 5px;
        margin-right: 8px;
        font-size: 12px;
        color: #666;
        
        img {
            margin-right: 4px;
        }
    }
}

.action-column {
    position: relative;
}

.right-action {
    text-align: right;
    
    .restore-btn {
        display: inline-flex;
        align-items: center;
        color: #e86b1f;
        font-size: 13px;
        cursor: pointer;
        
        .custom-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
        }
    }
}
</style>