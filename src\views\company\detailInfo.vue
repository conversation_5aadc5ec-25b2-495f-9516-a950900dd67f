<template>
  <basic-container>
    <h1 class="top-title">{{ $t("customer.title.base") }}</h1>
    
    <div class="main">
      <h4>{{ getCustomerName() }}</h4>

      <el-row>
        <el-col :span="16">
          <el-col :span="24">
            <img src="/img/icon/phone.png" style="vertical-align: bottom;" /> {{ form.customer.contactMobile }}
          </el-col>
          <el-col :span="8">
            <p class="label-tit">{{ $t('customer.taxNo') }}</p>
            {{ form.customer.taxNo }}
          </el-col>
          <el-col :span="8">
            <p class="label-tit">{{ $t('customer.sgs.customerNo') }}</p>
            {{ form.customer.bossNo }}
          </el-col>
          <el-col :span="8">
            <p class="label-tit">{{ $t('customer.sgs.reportNo') }}</p>
            {{ form.customer.reportNo }}
          </el-col>
          <el-col :span="24">
            <p class="label-tit">{{ $t('address.detail') }}</p>
            <!-- <img src="/img/icon/address.png" /> -->
            {{getCustomerAddress()}}
          </el-col>
        </el-col>
        <el-col :span="8">
          <p class="label-tit cert">
            {{ $t('customer.certificateNew') }} 
            <el-button :disabled="qualificationUrl==''" type="text" class="pull-right" @click="dialogVisible = true">
              <img src="/img/icon/full-screen.png" />
              {{ $t('customer.title.ViewLargeImage') }}
            </el-button>
          </p>
          <el-image style="width: 320px;" :src="qualificationUrl" :preview-src-list="[qualificationUrl]">
            <div slot="error" class="image-slot" style="font-size: 14px; color: #999;">
              {{ $t('customer.certificateUpload') }}
            </div>
          </el-image>
        </el-col>
      </el-row>
      <el-dialog
        :title="$t('customer.certificateNew')"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose">
        <el-image style="width: 100%" :src="qualificationUrl">
            <div slot="error" class="image-slot" style="font-size: 14px; color: #999;">
              {{ $t('customer.certificateUpload') }}
            </div>
          </el-image>
      </el-dialog>
    </div>
  </basic-container>

</template>

<script>
import { detailForCurrentUser } from "@/api/customer/customerRegister";
import { getCloudFileURL } from "@/api/common/index";
import { add } from "@/api/customer/customer";
import { validatenull } from "../../util/validate";
import {mapGetters} from "vuex";

export default {
  name: "detail",
  props: {
    customerId: {
      type: Number,
      default: null,
    }
  },
  data() {
    return {
      form: {
        customer: {},
      },
      qualificationUrl: '',
      dialogVisible: false
    };
  },
  computed:{
    ...mapGetters(["language"]),
  },
  methods: {
    getCustomerName(){
      let {customerNameEn,customerNameZh} = this.form.customer;
      let customerName = this.language=='zh-CN'? (customerNameZh || customerNameEn):(customerNameEn || customerNameZh);
      return customerName;
    },
    getCustomerAddress(){
      let {customerAddressEn,customerAddressZh} = this.form.customer;
      let address = this.language=='zh-CN'? (customerAddressZh || customerAddressEn):(customerAddressEn || customerAddressZh);
      return address;
    },
    rowSave() {
      add(this.form.customer).then(() => {
        this.onLoad();
        this.$message({
          type: "success",
          message: this.$t('api.success')
        });
      }, error => {
        console.log(error);
      });
    },
    onLoad() {
      this.customerId = this.customerId || 1;
      detailForCurrentUser(this.customerId).then(res => {
        this.form = res.data.data;
        if (!validatenull(this.form.customer.qualification.attachmentId)) {
          getCloudFileURL(this.form.customer.qualification.attachmentId).then(res => {
            this.qualificationUrl = res.data;
          });
        }

      });
    },
    uploadSuccess(res, file) {
      this.form.qualification = res.data[0].id;
    }
  },
  created() {
    this.onLoad();
  },
  watch: {
    customerId: function (data) {
      this.onLoad(data);
    },
  }
}
</script>

<style lang="scss" scoped>
.main {
  padding: 32px;
  background: #fff;
  h4 {
    margin: 0 0 32px;
    height: 30px;
    font-size: 20px;
    //font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #1B1B1B;
    line-height: 30px;
  }
  .label-tit {
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 20px;
    margin-bottom: 6px;
  }
  .cert {
    width: 320px;
    margin-bottom: 16px;
    .pull-right {
      color: #1b1b1b;
      cursor: pointer;
      padding: 0;
    }
  }
  .el-col {
    margin-bottom: 32px;
    font-size: 16px;
    font-weight: 400;
    color: #1B1B1B;
    line-height: 24px;
  }
}
h3 {
  margin: 0 24px 24px;
  height: 32px;
  font-size: 24px;
  font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif; 
  //font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 32px;
}

</style>
