<template>
    <div>
        <!-- <el-form :inline="true" :model="formInline" size="medium">
            <el-form-item>
                <el-input clearable v-model="query.customerName"
                          :placeholder="$t('supplier.placeholder.name')"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                <el-button v-if="permissionList.addBtn" type="primary" @click="addRow">{{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form> -->

        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
            <el-form-item>
                <el-input 
                    v-model="query.customerName"
                    @input="onSearch"
                    @keyup.enter.native="onSearch" 
                    @clear="onSearch"
                    :placeholder="$t('supplier.placeholder.name')" 
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn" id="add-buyer">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
                v-loading="tableLoading"
                ref="supplierTable"
                :data="tableData"
                style="width: 100%">
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="customerNameCN"
                    :label="$t('supplier.nameFullCN')">
            </el-table-column>
            <el-table-column
                    prop="customerNameEN"
                    :label="$t('supplier.nameFullEN')">
            </el-table-column>

            <el-table-column
                    prop="customerSupplier.supplierCode"
                    :label="$t('supplier.supplierCode')">
            </el-table-column>

            <el-table-column
                    prop="customerSupplier.tier"
                    :label="$t('supplier.tier')">
            </el-table-column>

            <!--<el-table-column
                    prop="customerSupplier.productCategory"
                    :label="$t('supplier.productCategory')">
            </el-table-column>-->

            <el-table-column
                    prop="customerSupplier.productCategorys"
                    :label="$t('supplier.department')">
                <template slot-scope="scope">
                    <el-tag size="small"
                            v-for="category in scope.row.customerSupplier.productCategorys"
                    > {{category.departmentName}}
                    </el-tag>
                </template>
            </el-table-column>


            <el-table-column
                    :label="$t('common.status.title')"
                    width="80px">
                <template slot-scope="scope">
                    <el-tooltip
                            :content="scope.row.activeIndicator==='A'?$t('common.status.enable'):$t('common.status.disable')"
                            placement="top">
                        <el-switch
                                v-model="scope.row.activeIndicator"
                                active-color="#ff6600"
                                inactive-color="#D9D9D9FF"
                                active-value="A"
                                inactive-value="I"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="150"
                    align="center">
                <template slot-scope="scope">
                    <el-button type="text" v-if="permissionList.editBtn" @click="detailRow(scope.row)">
                        {{$t('operation.edit')}}
                    </el-button>
                    <!-- <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>-->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog :title="title" :visible.sync="dialogFormVisible" size="60%" :close-on-click-modal="false" :append-to-body="true">
            <el-form ref="form" :model="form" label-width="160px" label-position="left" size="medium">
                <el-form-item id="input-buyer-name" :label="$t('term.supplier')"
                              :rules="{ required: true, message: $t('supplier.validate.nameBlur'), trigger: 'blur' }"
                              prop="customerName">
                    <el-autocomplete
                            v-model="form.customerName"
                            filterable
                            :fetch-suggestions="querySearch"
                            :placeholder="$t('supplier.placeholder.name')"
                            @select="handleSelect"
                            style="width: 100%"
                    ></el-autocomplete>
                </el-form-item>
            </el-form>
            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="submitForm('form')" :loading="submitLoading" id="add-buyer-confirm">
                    {{$t('operation.submit')}}
                </el-button>
            </div>
        </el-dialog>

        <el-dialog :title="title" :visible.sync="editSupplierdDialogVisible" :close-on-click-modal="false">
            <el-form ref="supplierForm" :model="supplierForm" label-width="200px" label-position="left" size="medium"
                     class="sgs-form">
                <el-form-item :label="$t('supplier.nameFullCN')">
                    <el-input
                            disabled="true"
                            type="input"
                            maxlength="200"
                            show-word-limit
                            v-model="supplierForm.customerNameCN">
                    </el-input>
                </el-form-item>

                <el-form-item :label="$t('supplier.nameFullEN')">
                    <el-input
                            disabled="true"
                            type="input"
                            maxlength="200"
                            show-word-limit
                            v-model="supplierForm.customerNameEN">
                    </el-input>
                </el-form-item>
                <el-form-item :label="$t('supplier.supplierCode')">
                    <el-input
                            type="input"
                            maxlength="200"
                            show-word-limit
                            v-model="supplierForm.customerSupplier.supplierCode">
                    </el-input>
                </el-form-item>

                <el-form-item :label="$t('supplier.tier')">
                    <el-input
                            type="input"
                            maxlength="200"
                            show-word-limit
                            v-model="supplierForm.customerSupplier.tier">
                    </el-input>
                </el-form-item>
                <el-form-item :label="$t('supplier.department')">
                    <!-- <el-input
                             type="input"
                             maxlength="200"
                             show-word-limit
                             v-model="supplierForm.customerSupplier.productCategory">
                     </el-input>-->
                    <el-select v-model="departments" multiple style="width: 100%;">
                        <el-option
                                v-for="dept in departmentList"
                                :key="dept.id"
                                :label="dept.departmentName"
                                :value="dept.id">
                        </el-option>
                    </el-select>

                </el-form-item>
            </el-form>


            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="editSupplierdDialogVisible = false">{{$t('operation.cancel')}}
                </el-button>
                <el-button size="small" type="primary" @click="submitSupplierForm('supplierForm')"
                           :loading="submitLoading">{{$t('operation.submit')}}
                </el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
    import {
        getPage,
        addRelationship,
        remove,
        submitCustomerSupplier
    } from "@/api/customer/relationship";
    import {getDepts} from "@/api/customer/customerDept";
    import {getCloudFileURL, getSgsCustomer} from "@/api/common/index";
    import {mapGetters, mapState} from "vuex";
    import {validatenull} from "@/util/validate";


    export default {
        name: "suppliers",
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        data() {
            return {
                tableLoading: false,
                departmentList: [],
                departments: [],
                title: '',
                query: {},
                form: {},
                supplierForm: {
                    customerSupplier: {},
                },
                tableData: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                dialogFormVisible: false,
                editSupplierdDialogVisible: false,
                customerGroupOptions: [],
                submitLoading: false
            }
        },
        computed: {
            ...mapGetters(["permission", "userInfo"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:relationship:supplier:add'], false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:relationship:supplier:delete'], false),
                    editBtn: this.vaildData(this.permission['sgs:customer:relationship:supplier:edit'], false),
                };
            }
        },
        watch: {
            '$store.state.user.addBuyer' (newVal, oldVal) {
                console.log('关闭添加买家引导 ', newVal)
                this.dialogFormVisible = newVal
            }
        },
        methods: {
            detailRow(obj) {
                this.supplierForm = obj;
                this.departments = [];
                obj.customerSupplier.productCategorys.find((item) => {
                    this.departments.push(item.departmentId);
                });
                this.editSupplierdDialogVisible = true;
            },
            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                if (!validatenull(this.userInfo.customerGroupCode)) {
                    this.query.customerGroupCode = this.userInfo.customerGroupCode;
                    this.tableLoading = true;
                    getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                        this.tableLoading = false;
                        this.tableData = res.data.data.records;
                        this.page.total = res.data.data.total;
                    }).catch(() => {
                        this.tableLoading = false;
                    });
                }
            },
            submitSupplierForm() {
                 this.supplierForm.customerSupplier.productCategorys =[];
                if (!validatenull(this.departments)) {
                    var depts = [];
                    this.departments.find((deptId) => {
                        var obj = this.departmentList.find((item) => {
                            return item.id === deptId;
                        });
                        var deptObj = {};
                        deptObj.departmentName = obj.departmentName;
                        deptObj.departmentId = obj.id;
                        depts.push(deptObj);
                    });
                    this.supplierForm.customerSupplier.productCategorys = depts;
                }
                submitCustomerSupplier(this.supplierForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.editSupplierdDialogVisible = false;
                    this.onLoad(this.page);
                }).catch(() => {
                    this.editSupplierdDialogVisible = false;
                    this.onLoad(this.page);
                });
            },
            submitForm() {
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        this.submitLoading = true;
                        this.form.customerGroupCode = this.userInfo.customerGroupCode;
                        this.form.createdBy = this.userInfo.account;
                        addRelationship([this.form]).then(res => {
                          if("1" == res.data.success){
                            this.$message({
                              type: "success",
                              message: this.$t('api.success')
                            });
                            this.submitLoading = false;
                            this.dialogFormVisible = false;
                            this.onLoad(this.page);
                          }else{
                            this.$message({
                              type: "error",
                              message: res.data.errorMessage
                            });
                            this.submitLoading = false;
                            this.dialogFormVisible = false;
                            //this.onLoad(this.page);
                          }
                        }).catch(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        return false;
                    }
                });

            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            removeRow(row) {

                this.form.customerGroupCode = this.userInfo.customerGroupCode;
                this.form.supplierCustomerNo = row.customerNumber;
                this.form.createdBy = this.userInfo.account;

                remove([this.form]).then(res => {
                    if ("1" == res.data.success) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.submitLoading = false;
                        this.dialogFormVisible = false;
                        this.onLoad(this.page);
                    } else {
                        this.$message({
                            type: "error",
                            message: res.data.errorMessage
                        });
                    }
                });
            },
            querySearch(query, callback) {
                this.loading = false;
                if (query != '' && query != undefined && query.length >= 3) {
                    this.loading = true;
                    getSgsCustomer({customerName: query, rows: 5}).then(res => {
                        const results = [];
                        res.data.rows.forEach(function (currentValue, index) {
                            results.push({
                                'value': currentValue.nameCN,
                                "taxNo": currentValue.taxNo,
                                "bossNo": currentValue.number
                            });
                        });
                        callback(results);
                    }, error => {
                        console.log(error);
                    });
                }
            },
            handleSelect(item) {
                this.form.supplierCustomerNo = item.bossNo;
            },
            addRow() {
                this.form = {};
                this.title = this.$t('supplier.title.add');
                this.dialogFormVisible = true;
            },
            initDepartment() {
                //获取当前登录用户的department
                const customerId = this.userInfo.companyId;
                getDepts(customerId).then(res => {
                    const data = res.data.data;
                    this.departmentList = data;
                    /*//判断部门是否有数据
                    if (validatenull(data)) {
                        this.isShowDeptMessageFlag = true;
                    } else {
                        this.isShowDeptMessageFlag = false;
                    }*/
                });

            },
            changeStatus(row) {
                // 如果禁用调用删除
                if ('A' === row.activeIndicator) {
                    this.submitLoading = true;
                    this.form.customerGroupCode = row.customerGroupCode;
                    this.form.supplierCustomerNo = row.customerNumber;
                    this.form.createdBy = this.userInfo.account;
                    addRelationship([this.form]).then(res => {
                      if("1" == res.data.success){
                        this.$message({
                          type: "success",
                          message: this.$t('api.success')
                        });
                        this.submitLoading = false;
                        this.dialogFormVisible = false;
                        this.onLoad(this.page);
                      }else{
                        this.$message({
                          type: "error",
                          message: res.data.errorMessage
                        });
                        this.submitLoading = false;
                        this.dialogFormVisible = false;
                        //this.onLoad(this.page);
                      }
                    });
                }
                // 如果启用调用创建
                else {
                    this.removeRow(row)
                }

            }
        },
        created() {
            this.initDepartment();
            this.onLoad(this.page);
        }
    }
</script>

<style scoped>

</style>
