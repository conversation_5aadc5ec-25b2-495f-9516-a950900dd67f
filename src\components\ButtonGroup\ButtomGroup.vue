<template>
  <div class="button-group">
    <div v-for="(item, index) in buttonConfigs" class="button-item" :key="index">
      <el-tooltip effect="dark" :content="item.buttonTip" placement="top" v-if="item.buttonTip">
        <el-button
          :type="item.type"
          plain
          @click="handleButtonClick(item)"
        >
        <render-icon :icon="item.icon" :text="item.text" />
          {{ item.text }}
        </el-button>
      </el-tooltip>
      <el-button
        v-else
        :type="item.type"
        plain
        @click="handleButtonClick(item)"
      >
        <render-icon :icon="item.icon" :text="item.text" />
        {{ item.text }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, h } from 'vue'
import { isString } from 'lodash'

const props = defineProps<{
  buttonConfigs: Array<{
    type?: string
    text?: string
    clickType?: 'search' | 'reset' | 'export' | 'custom'
    customClick?: () => void
    icon?: string,
    buttonTip?:string,
  }>
  searchParams?: Record<string, any>
  dateFormat?: Record<string, any>
}>()

// 定义 emits
const emit = defineEmits(['search', 'reset', 'export'])

// 处理按钮点击事件
const handleButtonClick = (item: {
  type?: string
  text?: string
  clickType?: 'search' | 'reset' | 'export' | 'custom'
  customClick?: () => void
  icon?: string
}) => {
  switch (item.clickType) {
    case 'search':
      emit('search', { ...props.searchParams, ...props.dateFormat })
      break
    case 'reset':
      emit('reset', { ...props.searchParams, ...props.dateFormat })
      break
    case 'export':
      const exportParams = {
        ...props.searchParams,
        ...props.dateFormat,
        pageRow: 9999999,
      }
      emit('export', exportParams)
      break
    case 'custom':
      if (item.customClick) {
        item.customClick()
      }
      break
    default:
      break
  }
}
// 动态渲染图标组件
const renderIcon = (props: { icon: string | any, text: string | any}) => {
  if (!props.icon) return null
  if (isString(props.icon)) {
    return h('i', {
      class: props.icon,
      style: { marginRight: '4px', marginLeft: props.text ? 0: '4px' }
    })
  } else {
    return h(props.icon, {
      style: { marginRight: '4px', width: '1em', marginLeft: props.text ? 0: '4px' }
    })
  }
}
</script>

<style scoped lang="scss">
.button-group {
  display: flex;
  .reset-icon{
      font-size: 14px!important;
      color: #606266;
  }
  .el-button:hover .reset-icon{
      color: #ff6600;
  }
  .button-item{
    margin-right: 10px;
  }
  .button-item:last-child {
    margin-right: 0;
  }
}
</style>
