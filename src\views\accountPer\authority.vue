<template>
  <div>
    <h1 class="top-title">{{$t('navbar.authorization')}}</h1>
    <el-card shadow="never" class="box-card">
    <el-row>
        <el-form :inline="true" :model="query" size="medium">
            <el-form-item :label="$t('account.title.default')">
                <el-input v-model="query.userAccountCode" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                <el-button type="primary" @click="addRow" size="medium">{{$t('operation.add')}}</el-button>
            </el-form-item>
        </el-form>

    </el-row>
     <el-row>
        <el-table
            :data="accountPerDatas"
            v-loading="tableLoading"
            style="width: 100%" >
            <el-table-column
                    type="index"
                    fixed
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    fixed
                    :show-overflow-tooltip='true'
                    prop="userAccountCode"
                    :label="$t('account.title.default')">
            </el-table-column>   
            
            <el-table-column
                    fixed
                    :show-overflow-tooltip='true'
                    prop="customerGroupName"
                    :label="$t('accountPer.customerGroup')">
            </el-table-column>

            <el-table-column
                    fixed
                    :show-overflow-tooltip='true'
                    prop="authCompanyName"
                    :label="$t('accountPer.authCompanyName')">
            </el-table-column>
            <el-table-column
                    fixed
                    :show-overflow-tooltip='true'
                    prop="bossNo"
                    :label="$t('accountPer.authCustomerNo')">
            </el-table-column>



            <el-table-column
                      :label="$t('operation.title')"
                      width="150"
                      align="center">
                  <template slot-scope="scope">
                      <el-button  @click="deleteAccountPer(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                  </template>
              </el-table-column>
          </el-table>
          <el-pagination
                  @size-change="sizeChange"
                  @current-change="currentChange"
                  :current-page="page.currentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="page.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="page.total">
          </el-pagination>
      </el-row>
      <el-row style="margin-top: 10px;text-align: center">
            <el-button type="primary" @click="goBack">{{$t('operation.goBack')}}</el-button>
        </el-row>
      <el-dialog
                :title="$t('accountPer.title')"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :visible.sync="accountPerDialogVisible"
                width="800"
                append-to-body>
          <el-form  ref="form"  :model="accountPerForm" :rules="accountPerRules"  label-width="200px" label-position="left"  size="medium" class="sgs-form" >
            <el-form-item :label="$t('account.title.default')" prop="userAccountId">
              <el-select filterable clearable v-model="accountPerForm.userAccountId" style="width:100%"
                        :placeholder="$t('operation.pleaseSelect')"
                        @change="accountChange"
                        :no-data-text="$t('NoData')" clearable>
                <el-option v-for="(account,index) in accountDatas" :label="account.code"
                          :value="account.id"></el-option>
              </el-select>
            </el-form-item>

           
            <el-form-item :label="$t('accountPer.customerGroup')" prop="customerGroupCode" >
              <el-select filterable clearable v-model="accountPerForm.customerGroupCode" style="width:100%"
                        :placeholder="$t('operation.pleaseSelect')"
                        @change="customerGroupChange"
                        :no-data-text="$t('NoData')" clearable>
                <el-option v-for="(customerGroup,index) in customerGroupData" :label="customerGroup.customerGroupName"
                          :value="customerGroup.customerGroupCode"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('accountPer.authCompanyName')" prop="bossNo">
              <el-select filterable clearable v-model="accountPerForm.bossNo" style="width:100%"
                        :placeholder="$t('operation.pleaseSelect')"
                        :no-data-text="$t('NoData')"
                        remote
                        :remote-method="loadRemoteCustomer"
                        @change="customerChange"
                        clearable>
                <el-option v-for="(customer,index) in customerDatas"
                 :label="language === LanguageEnums.CN.name ? validatenull(customer.customerNameCn)?customer.customerNameEn:customer.customerNameCn : customer.customerNameEn"
                          :value="customer.number">
                    <span style="float: left">{{ language === LanguageEnums.CN.name ? validatenull(customer.customerNameCn)?customer.customerNameEn:customer.customerNameCn : customer.customerNameEn}}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ customer.number }}</span>
                </el-option>
                      
              </el-select>
            </el-form-item>
            <div class="sgs-bottom">
                    <el-button @click="closeDrawer()">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm()" :loading="btnSubmit">{{$t('operation.submit')}}
                    </el-button>
                </div>
          </el-form>

      </el-dialog>      


    </el-card>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import {queryAccountPerListByPage,add,remove} from "@/api/user/accountPer";
import {searchCustomerAndGroup} from "@/api/customer/customer";
import {validatenull,objectIsNull} from "@/util/validate";
import { getUsers} from "@/api/customer/externalAccount";
import { LanguageEnums } from "@/commons/enums/LanguageEnums";
import {
  getCustomerGroup,
  getCustomerGroupByParms
} from "@/api/common/index";
export default {
  name: "accountPer",
  components: {
  },

  created() {
    //查询对应客户的account
    this.queryAccountByCompanyId(this.$route.query.companyId);       
    //查询已授权的数据
    this.onSearch();
    //查询客户组数据
    this.queryCustomerGroupData();
    //先默认查询所有客户
    this.loadAllCustomer();
   
  },
  data() {
    return {
      LanguageEnums: LanguageEnums,
      btnSubmit: false,
      customerDatas:[],//客户选择项
      accountPerDatas:[],//table数据
      customerGroupData:[],//客户组数据
      accountPerDialogVisible:false,
      accountDatas:[],//公司账号数据
      accountQuery:{},
      accountPerForm:{
        userAccountId:'',
        userAccountCode:'',
        userAccountName:'',
        customerGroupCode:'',
        customerGroupName:'',
        userCompanyId: this.$route.query.companyId,
        userCompanyName:'',
        authCompanyId:'',
        authCompanyName:'',
        smartCustomerId:'',
        bossNo:''
      },
      accountPerRules:{
        userAccountId:[
          {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
        ]
      },
      tableLoading:false,
      customerDatas:[],//列表数据
      page: {
            pageSize: 10,
            currentPage: 1,
            total: 0
        },
      query: {
        userCompanyId:this.$route.query.companyId
      },
      sort: {descs:'update_time'},
    }
  },
  computed: {
    ...mapGetters(["userInfo","permission","language"]),
    permissionList() {
      return {
  
      };
    }
  },
  watch: {},
  methods: {
    loadAllCustomer(){
      let searchCustomerParam = {};
      searchCustomerParam.displayType='account';
      searchCustomerParam.page = 1;
      searchCustomerParam.rows = 2000;
      this.searchCustomerData(searchCustomerParam);
    },
    loadRemoteCustomer(name){
      debugger;
      let searchCustomerParam = {};
      searchCustomerParam.customerNameEn=name;
      searchCustomerParam.displayType='account';
      searchCustomerParam.page = 1;
      searchCustomerParam.rows = 200;
      this.searchCustomerData(searchCustomerParam);
    },

    deleteAccountPer(row){
      this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    remove(row.id).then(() => {                    
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                        this.onSearch();
                    }, error => {
                        console.log(error);
                    });

                }).catch(() => {
                  //取消删除
                });
    },
    accountChange(val){
      this.accountPerForm.userAccountName=null;
      this.accountPerForm.userAccountCode=null;
      debugger;
      if(!objectIsNull(val)){
        let accountObj = this.accountDatas.find((item) => {
            if (item.id === val) {
              return item;
            }
          });
          if(!objectIsNull(accountObj)){
              this.accountPerForm.userAccountName=accountObj.name;
              this.accountPerForm.userAccountCode=accountObj.code;
          }
      }

    },
    closeDrawer(done) {
        this.$refs['form'].resetFields();
        this.accountPerDialogVisible = false;
    },
    sizeChange(){
      this.onLoad();
    },
    currentChange(){
      debugger;
      this.onLoad();
    },
    submitForm(){
      debugger;
      console.log('提交数据',this.accountPerForm);
      this.$refs['form'].validate((valid) => {
        if(valid){
          //校验客户组和客户必须有一项数据
          if(objectIsNull(this.accountPerForm.customerGroupCode) && objectIsNull(this.accountPerForm.bossNo)){
            this.$notify({
              title: this.$t('tip'),
              message: this.$t('accountPer.customerValidateError'),
              type: 'warning'
            });
            return false;
          }

          this.btnSubmit = true;
          add(this.accountPerForm).then(res => {
              this.$message({
                  type: "success",
                  message: this.$t('api.success')
              });
              this.btnSubmit = false;
              this.accountPerDialogVisible = false;
              this.onSearch();
          }).catch(() => {
              this.btnSubmit = false;
          });
        }else{
          this.btnSubmit = false;
        }
      });
    },
    customerChange(val){
      debugger;
      this.accountPerForm.authCompanyId = null;
      this.accountPerForm.authCompanyName = null;
      if(!objectIsNull(val)){
        let customerObj = this.customerDatas.find((item) => {
            if (item.number === val) {
              return item;
            }
          });
          if(!objectIsNull(customerObj)){
              this.accountPerForm.authCompanyId=customerObj.customerId;
              this.accountPerForm.authCompanyName=customerObj.customerNameEn;
          }
      }
    },
    customerGroupChange(val){
      debugger;
      this.accountPerForm.customerGroupName=null;
      //清空已加载的客户信息
      this.accountPerForm.bossNo='';
      if(!objectIsNull(val)){
        let searchCustomerParam = {};
        searchCustomerParam.displayType='account';
        searchCustomerParam.customerGroupCode=val;
        //加载customer数据
        searchCustomerParam.page = 1;
        searchCustomerParam.rows = 2000;
        this.searchCustomerData(searchCustomerParam);
       
        //重新赋值客户组名称数据
        let customerGroupObj = this.customerGroupData.find((item) => {
          if (item.customerGroupCode === val) {
            return item;
          }
        });
        if(!objectIsNull(customerGroupObj)){
              this.accountPerForm.customerGroupName=customerGroupObj.customerGroupName;
          }

      }else{
        //查询所有客户
        this.loadAllCustomer();
      }
    },
    searchCustomerData(searchCustomerParam){ 
        searchCustomerAndGroup(searchCustomerParam).then( res => {
          let data =  res.data.data;
          this.customerDatas =  data;
        });
    },
    addRow(){
        this.loadAllCustomer();//加载所有客户
        this.accountPerForm.userAccountId=null;
        this.accountPerForm.userAccountName = null;
        this.accountPerForm.customerGroupCode = null;
        this.accountPerForm.customerGroupName = null;
        this.accountPerForm.authCompanyId= null;
        this.accountPerForm.authCompanyName= null;
        this.accountPerForm.smartCustomerId = null;
        this.accountPerForm.bossNo = null;
        this.accountPerDialogVisible = true;
    },
    goBack() {
        this.$router.push({path: '/accountPer/index', query: {}});
    },
    //account授权
    // accountAuth(row){
    //   debugger;
    //     if(!objectIsNull(row)){
    //         //查询account
    //         this.queryAccountByCompanyId(row.companyId);
           
    //         this.accountPerDialogVisible = true;
    //     }
    // },

    queryAccountByCompanyId(companyId){
      if(!objectIsNull(companyId)){
        this.accountQuery.page = 1;
        this.accountQuery.rows = 500;
        this.accountQuery.companyId = companyId;
        getUsers(this.accountQuery).then(res => {
            this.accountDatas = res.data.rows;
        });
      }
    },
    queryCustomerGroupData(){
      getCustomerGroup().then(res => {
          this.customerGroupData = res.data.data;
      });
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
      this.onLoad();
    },
    currentChange(pageCurrent){
        this.page.currentPage = pageCurrent;
        this.onLoad();
    },
    onSearch() {
        this.page.currentPage=1;
        this.onLoad(this.page);
    },
    onLoad(){
      this.tableLoading=true;
      debugger;
      var  params = {};
      queryAccountPerListByPage(this.page.currentPage, this.page.pageSize, Object.assign(params,this.query,this.sort)).then(res => {
          this.tableLoading = false;
          const data = res.data.data;
          this.page.total = data.total;
          this.accountPerDatas = data.records;
      }).catch(error=>{
        this.tableLoading=false
      });
    },


  },
};
</script>
