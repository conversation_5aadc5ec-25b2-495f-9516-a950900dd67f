<template>
    <div class="registry-success">
        <div class="success-container">
            <!-- 成功信息部分 -->
            <div class="success-content">
                <h2 class="success-title">
                    <span class="success-icon">✓</span>{{ t('register.success') }}
                </h2>
                <p class="success-message">
                    {{ t('register.registrationSubmitedTo') }}
                    <span class="orange-text">{{ emailContact || t('register.adminContact') }}.</span>
                </p>
                <p class="success-message">{{ t('register.registrationSuccess') }}</p>
                <p class="success-message thank-you">{{t('register.thankYou')}}</p>
                <p class="success-message return-login-page" @click="returnLoginPage">{{t('register.returnLoginPage')}}</p>
            </div>
        </div>
    </div>
</template>

<script setup >
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
defineProps({
    emailContact:{
        type:String,
        default:''
    }
});// ... existing code ...
const returnLoginPage = () => {
    window.location.href="/#/login"
}
</script>

<style scoped>
.registry-success {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
}

.success-container {
    width: 600px;
    max-width: 90%;
    text-align: center;
}


.smart {
    color: #3A4A54;
    /* 深蓝灰色 */
}

.divider {
    border-bottom: 1px solid #ccc;
    margin: 10px 0 40px 0;
}

.success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}

.success-icon {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 2px solid #4CAF50;
    color: #4caf50;
    display: inline-block;
    /* justify-content: center; */
    align-items: center;
    font-size: 10px;
    font-weight: 700;
    margin: 0 auto;
    margin-top: 5px;
    position: absolute;
    left: -20px;
}

.success-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
    position: relative;
}

.success-message {
    margin: 5px 0;
    font-size: 16px;
    color: #333;
}

.orange-text {
    color: #FF6B00;
    /* 橙色 */
}

.thank-you {
    margin-top: 20px;
    color: #FF6B00;
    /* 橙色 */
}
.return-login-page {
    margin-top: 20px;
    color: #FF6B00;
    /* 橙色 */
    cursor: pointer;
}   
</style>