<template>
    <basic-container >
        <h1 class="top-title">{{ $t('extendData.title') }}</h1>

        <el-card shadow="never" class="box-card">

            <el-row>
              <el-form :inline="true" :model="query" size="medium" label-width="200px" >
                <el-form-item>
                  <el-select clearable filterable :placeholder="$t('term.productLine')" v-model="query.productLineCode" style="width:100%" >
                    <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                               :value="productLine.productLineCode"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-select clearable :placeholder="$t('term.customerGroup')" v-model="query.customerGroupCode" filterable style="width:100%">
                    <el-option v-for="(customerGroup,index) in customerGroupData" :label="customerGroup.customerGroupName"
                               :value="customerGroup.customerGroupCode" ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-input clearable :placeholder="$t('template.templateName')"  v-model="query.templateName" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchList()"  v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.search')}}</el-button>
                  <reset-button @click="clear"></reset-button>
                </el-form-item>
              </el-form>
            </el-row>

            <el-row>
                <el-row align="right" style="margin: 5px 0px">
                    <el-button  type="primary" icon="el-icon-edit" size="medium" v-if="permissionList.addBtn"  @click="addTemplate()">{{$t('operation.add')}}</el-button>
                </el-row>
                <el-table ref="labTable" :row-key="getRowKeys" v-loading="loading" :element-loading-text="$t('loading')" :data="data"  style="width: 100%" >
                    <el-table-column fixed type="index" label="#" width="50"> </el-table-column>
                    <el-table-column fixed prop="templateName" :label="$t('extendData.template')" width="250" :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{scope.row.templateName}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="extendSpecificCode" :label="$t('extendData.specificCode')"  width="200"></el-table-column>
                    <el-table-column prop="specificNameEn" :label="$t('extendData.specificNameEn')"  width="200"></el-table-column>
                    <el-table-column prop="specificNameCn" :label="$t('extendData.specificNameCn')"  width="260" ></el-table-column>
                    <el-table-column prop="isMenu" :label="$t('extendData.isMenu')"  width="150" :formatter="menuFormat"></el-table-column>
                    <el-table-column prop="updateUser" :label="$t('user.updateUser')" width="150"></el-table-column>
                    <el-table-column prop="updateTime" :label="$t('user.updateTime')" width="150"></el-table-column>

                    <el-table-column fixed="right" :label="$t('operation.title')" width="180" align="center">
                        <template slot-scope="scope">
                            <el-button  @click="editSpecificExtend(scope.row)" v-if="permissionList.editTemplateBtn"  type="text">{{$t('operation.edit')}}</el-button>
                            <el-button  @click="deleteSpecificExtend(scope.row)" v-if="permissionList.deletedTemplateBtn"  type="text">{{$t('operation.remove')}}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
    </basic-container>
</template>

<script>

    import {getList,remove} from "@/api/specificextend/specificextend";
    import { getCustomerGroup, getProductLine} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import resetButton from "@/components/resetButton/resetButton.vue";

    export default {
        components: { resetButton },
        created() {
             this.queryproductLineData();
             this.queryCustomerGroupData();
             this.searchList();

        },

        data() {
            return {
                customerGroupData: [],
                query: {},
                sort: {descs:'con.update_time'},
                loading:false,
                total: '',
                data: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    editTemplateBtn: this.vaildData(this.permission['sgs:extend:edit'],false),
                    deletedTemplateBtn: this.vaildData(this.permission['sgs:extend:deleted'],false),
                    addBtn: this.vaildData(this.permission['sgs:extend:add'], false),

                };
            }
        },
        methods: {
            deleteSpecificExtend(row){
                this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    remove(row.id).then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });

                        this.onLoad(this.page);
                    }, error => {
                        console.log(error);
                    });

                }).catch(() => {
                    /* this.$message({
                         type: 'info',
                         message: '已取消删除'
                     });*/
                });
            },
            addTemplate(){
               this.$router.push( {path: '/extend/specificextend-setting',query:{ id:''}});
                //this.$router.push( {path: '/trf/trfForm',query:{ id:''}});
            },
            editSpecificExtend(e) {
                this.$router.push( {path: '/extend/specificextend-setting',query:{ id:e.id}});
            },

            menuFormat(row,column){
                var isMenu = row.isMenu;
                var menuStr = '';
                if(isMenu == 1 || isMenu == '1'){
                  menuStr = '是'
                }else {
                  menuStr = '否'
                }
                return menuStr;
            },
            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.customerGroupData = data;
                });
            },
            queryproductLineData() {
                getProductLine().then(res => {
                    const data = res.data.data;
                    this.productLineData = data;
                });
            },


            clear(){
                this.query={};
                this.onLoad(this.page);
            },
            searchList() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page) {debugger
                this.loading=true;
                var  params = {};
                getList(this.page.currentPage, this.page.pageSize, Object.assign(params,this.query,this.sort)).then(res => {
                    this.loading = false;
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.data = data.records;
                });
            },


            //分页查询
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.onLoad(this.page);
            },
        },
    };
</script>

<style scoped lang="scss">
    .otherActiveClass {
        color: #FFF;
        background-color: #ebeef5;
    }
    .otherActiveClass:hover {
        background: #ebeef5;
        color: #FFF;
    }
    .box-card {
        /deep/ .el-card__body {
            .el-form--inline .el-form-item {
                /* margin-bottom: 5px; */
            }
        }
    }
</style>
