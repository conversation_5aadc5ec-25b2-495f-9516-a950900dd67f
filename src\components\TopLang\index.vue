<template>
  <div id="language">
    <a @click="handleSetLanguage('zh-CN')" ref="zh_a" style="cursor: pointer">
      <i
        class="icon-all iconzhongwen"
        :style="{ color: isLogin ? '' : 'white' }"
      ></i>
    </a>
    <a @click="handleSetLanguage('en-US')" ref="en_a" style="cursor: pointer">
      <i
        class="icon-all iconyingwen"
        :style="{ color: isLogin ? '' : 'white' }"
      ></i>
    </a>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from 'vue' // Add getCurrentInstance import
import { useStore } from 'vuex'
import { updateUserLanguage, queryUserLanguage } from '@/api/customer'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()

const store = useStore()

// Define ref variables
const zh_a = ref(null)
const en_a = ref(null)

// Define props
const props = defineProps({
  isLogin: {
    type: Boolean,
    default: false,
  },
  loadMenu: {
    type: Boolean,
    default: true,
  },
  isLoadLanageByIp: {
    type: Boolean,
    default: true,
  },
})

// Define methods
const changeLanguage = (lang) => {
  locale.value = lang
  if (lang === 'zh-CN') {
    zh_a.value.className = 'lang-active'
    en_a.value.className = ''
  } else {
    zh_a.value.className = ''
    en_a.value.className = 'lang-active'
  }
}

const handleSetLanguage = (lang) => {
  changeLanguage(lang)
  store.commit('SET_LANGUAGE', lang)
  updateUserLanguage(lang)
  store.dispatch('GetMenu', lang)
}

// Rename the local function to avoid naming conflict
const handleQueryUserLanguage = () => {
  queryUserLanguage().then((res) => {
    let lang = res.data
    changeLanguage(lang)
    store.commit('SET_LANGUAGE', lang)
    store.dispatch('GetMenu', lang)
  })
}

// Execute before the component is mounted
onBeforeMount(() => {
  if (props.isLoadLanageByIp) {
    handleQueryUserLanguage()
  }
})
</script>

<style lang="scss" scoped>
.lang-active {
  display: none;
}
</style>
