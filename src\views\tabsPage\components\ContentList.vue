<template>
    <div class="container">
        <!-- 搜索条件区域 -->
        <el-card class="search-box">
            <div class="action-bar">
                <el-button type="text" class="action-button">
                    <el-icon><Plus /></el-icon>新建
                </el-button>
                <el-button type="text" class="action-button">
                    <el-icon><Refresh /></el-icon>刷新
                </el-button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-bar">
                <el-select v-model="localFilter" class="filter-select" size="small" @change="handleFilterChange">
                    <el-option label="所有" value="all" />
                    <el-option label="草稿" value="draft" />
                    <el-option label="待发布" value="pending" />
                    <el-option label="已发布" value="published" />
                    <el-option label="我创建的" value="mine" />
                    <el-option label="重新编辑" value="editing" />
                    <el-option label="驳回" value="rejected" />
                </el-select>
                <el-input 
                    v-model="localSearchQuery" 
                    placeholder="请输入" 
                    class="search-input" 
                    size="small"
                    @input="handleSearchInput" 
                />
                <el-button type="warning" class="search-button" size="small" @click="handleSearch">
                    <el-icon><Search /></el-icon>搜索
                </el-button>
            </div>

            <!-- 排序 -->
            <div class="sort-section">
                <el-dropdown @command="handleSort">
                    <span class="el-dropdown-link">
                        <el-icon><Sort /></el-icon>
                        {{ currentSort }}
                        <el-icon><CaretBottom /></el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="timeDesc">
                                <el-icon><SortDown /></el-icon>发布时间降序
                            </el-dropdown-item>
                            <el-dropdown-item command="timeAsc">
                                <el-icon><SortUp /></el-icon>发布时间升序
                            </el-dropdown-item>
                            <el-dropdown-item command="relevance">
                                <el-icon><Connection /></el-icon>相关度
                            </el-dropdown-item>
                            <el-dropdown-item command="views">
                                <el-icon><View /></el-icon>浏览量
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </el-card>

        <!-- 内容列表 -->
        <el-card class="content-list">
            <div v-for="(item, index) in regulations" :key="index" class="content-item">
                <el-row :gutter="24">
                    <el-col :span="4">
                        <div class="image-container">
                            <el-image :src="item.url" fit="cover" />
                        </div>
                    </el-col>
                    <el-col :span="20">
                        <div class="content-header">
                            <el-row justify="space-between" align="middle">
                                <el-col :span="20">
                                    <h3 class="title">
                                        <el-icon><Document /></el-icon>
                                        {{ item.title }}
                                    </h3>
                                    <div class="tags">
                                        <el-tag 
                                            v-for="(tag, tagIndex) in item.tag" 
                                            :key="tagIndex"
                                            size="small"
                                            effect="plain"
                                        >
                                            {{ tag }}
                                        </el-tag>
                                    </div>
                                </el-col>
                                <el-col :span="4" class="views">
                                    <span>阅读量：{{ item.views }}</span>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="content-body">
                            {{ item.content }}
                        </div>

                        <div class="content-footer">
                            <el-row justify="space-between" align="middle">
                                <el-col :span="16">
                                    <el-tag 
                                        v-if="item.labels"
                                        :type="getStatusType(item.labels)"
                                        effect="plain"
                                        size="small"
                                    >
                                        {{ item.labels }}
                                    </el-tag>
                                    <div class="attachments">
                                        <el-link v-for="(_, i) in 2" :key="i" :underline="false" class="attachment-link">
                                            <el-icon><Document /></el-icon>
                                            附件{{ i + 1 }}.{{ i === 0 ? 'PDF' : 'xlsx' }}
                                        </el-link>
                                    </div>
                                </el-col>
                                <el-col :span="8" class="author">
                                    作者：<span>{{ item.author }}</span>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="meta-info">
                            <div class="dates">
                                <span>发布时间：{{ item.publishDate }}</span>
                                <span>创建时间：{{ item.effectiveDate }}</span>
                            </div>
                            <div class="actions">
                                <el-button type="text" size="small">
                                    <el-icon><Edit /></el-icon>编辑
                                </el-button>
                                <el-button type="text" size="small">
                                    <el-icon><Promotion /></el-icon>发布
                                </el-button>
                                <el-button type="text" size="small">
                                    <el-icon><Delete /></el-icon>删除
                                </el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <el-pagination
                    v-model:current-page="currentPage"
                    :page-size="10"
                    :total="total"
                    layout="prev, pager, next"
                    @current-change="handlePageChange"
                />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import type { Regulation } from '@/types/regulation'

const props = defineProps<{
    regulations: Regulation[]
    searchQuery: string
    filter: string
    currentPage: number
    totalPages: number
}>()

const emit = defineEmits<{
    (e: 'search', query: string): void
    (e: 'sort', type: string): void
    (e: 'page-change', page: number): void
}>()

// 本地状态
const localSearchQuery = ref(props.searchQuery)
const localFilter = ref(props.filter)
const currentSort = ref('默认排序')
const total = computed(() => props.totalPages * 10)

// 处理搜索
const handleSearchInput = () => {
    emit('search', localSearchQuery.value)
}

const handleFilterChange = () => {
    emit('search', localSearchQuery.value)
}

const handleSearch = () => {
    emit('search', localSearchQuery.value)
}

// 处理排序
const handleSort = (command: string) => {
    emit('sort', command)
    switch (command) {
        case 'timeDesc':
            currentSort.value = '时间降序'
            break
        case 'timeAsc':
            currentSort.value = '时间升序'
            break
        case 'relevance':
            currentSort.value = '相关度'
            break
        case 'views':
            currentSort.value = '浏览量'
            break
    }
}

// 处理分页
const handlePageChange = (page: number) => {
    emit('page-change', page)
}

// 获取状态标签类型
const getStatusType = (status: string) => {
    const types: Record<string, string> = {
        '草稿': 'info',
        '待发布': 'warning',
        '已发布': 'success',
        '驳回': 'danger'
    }
    return types[status] || 'info'
}
</script>

<style lang="scss" scoped>
.container {
    .search-box {
        margin-bottom: 16px;
        
        .action-bar {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;

            .action-button {
                color: #e86b1f;
                
                .el-icon {
                    margin-right: 4px;
                }
            }
        }

        .search-bar {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;

            .filter-select {
                width: 120px;
            }

            .search-input {
                flex: 1;
            }

            .search-button {
                background-color: #e86b1f;
                border-color: #e86b1f;

                &:hover {
                    background-color: #d35a0f;
                    border-color: #d35a0f;
                }
            }
        }

        .sort-section {
            display: flex;
            justify-content: flex-end;

            .el-dropdown-link {
                cursor: pointer;
                color: #606266;
                display: flex;
                align-items: center;
                gap: 4px;

                &:hover {
                    color: #e86b1f;
                }
            }
        }
    }

    .content-list {
        .content-item {
            padding: 20px 0;
            border-bottom: 1px solid #ebeef5;

            &:last-child {
                border-bottom: none;
            }

            .image-container {
                width: 100%;
                aspect-ratio: 16/9;
                overflow: hidden;
                border-radius: 4px;

                .el-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .content-header {
                margin-bottom: 16px;

                .title {
                    margin: 0 0 8px;
                    font-size: 18px;
                    color: #303133;
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .el-icon {
                        color: #909399;
                    }
                }

                .tags {
                    display: flex;
                    gap: 8px;
                }

                .views {
                    text-align: right;
                    color: #909399;
                }
            }

            .content-body {
                margin-bottom: 16px;
                color: #606266;
                line-height: 1.6;
            }

            .content-footer {
                margin-bottom: 16px;

                .attachments {
                    display: flex;
                    gap: 16px;
                    margin-top: 8px;
                }

                .attachment-link {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .author {
                    text-align: right;
                    color: #909399;

                    span {
                        color: #409eff;
                    }
                }
            }

            .meta-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #909399;
                font-size: 14px;

                .dates {
                    display: flex;
                    gap: 16px;
                }

                .actions {
                    display: flex;
                    gap: 16px;

                    .el-button {
                        color: #606266;

                        &:hover {
                            color: #e86b1f;
                        }

                        .el-icon {
                            margin-right: 4px;
                        }
                    }
                }
            }
        }

        .pagination {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }
    }
}
</style> 