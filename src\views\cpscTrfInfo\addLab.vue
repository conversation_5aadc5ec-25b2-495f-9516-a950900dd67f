<template>
  <div id="app">
    <div class="header" v-if="formModel.id">
      <div class="line2">
        <h2 class="line1">CPSC TRF No </h2>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <h3 class="line1">{{ this.trfInfo.referenceNo }}</h3>
      </div>
      <br />
      <div>
        <column-dots v-if="this.trfInfo.trfInfoStastus !== 0" :trfInfoStatus="this.trfInfo.trfInfoStastus" />
      </div>
    </div>
    <br />
    <avue-form :option="formOption0Com" ref="form5" v-model="formModel">

    </avue-form>
    <!-- <el-form :model="formModel"  label-position="left"  :rules="{certType:[{required:true,trigger:'change'}],lastTestDate:[{required:true,trigger:'change'}] }" ref="form5">
      <el-row >
        <el-col :span="12">
          <el-form-item label="Certificate Type:" prop="certType" style="width:100%">
            <el-select style="width:100%" v-model="formModel.certType" clearable filterable @change="handleChangeCertType" :disabled="this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'"
            >
                <el-option
                    v-for="item in certTypeDict"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="Last Test Date:" prop="lastTestDate" labelWidth="200" style="width:100%">
            <el-date-picker v-model="formModel.lastTestDate" type="date" :disabled="this.trfInfo.trfInfoStastus >=3 || this.trfInfo.isPending === '2'"
                            placeholder="Pick a day" value-format="yyyy-MM-DD HH:mm:ss" format="yyyy-MM-DD"
                            style="width:100%" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form> -->
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab1 }}</h3>


        </template>
        <div class="form-content"
          v-for="(item, index) in  formModel.cpscTrfLabInfoList.length ? formModel.cpscTrfLabInfoList : [{}]" :key="index">

          <el-row type="flex" justify="space-between">

            <div> <el-button size="small" type="danger" icon="el-icon-delete" :disabled="disabledCheck"
                @click="formModel.cpscTrfLabInfoList.splice(index, 1)" v-if="index !== 0"></el-button></div>
          </el-row>

          <div class="form-inner">
            <avue-form :option="formOption1Com" :ref="`form${10 + index}`" v-model="formModel.cpscTrfLabInfoList[index]">
              <template slot-scope="{dic}" slot="labType">
                <el-select :disabled="disabledCheck" v-model="formModel.cpscTrfLabInfoList[index].labType" clearable
                  filterable @change="formModel.cpscTrfLabInfoList[index].labId = null"
                  :placeholder="$t('work.cpscTrfInfo.column.labType')">
                  <el-option v-for="(item, index) in dic" :label="item.dictValue" :value="item.dictKey"></el-option>
                </el-select>
              </template>
              <template slot="labId">
                <el-select clearable v-show="item.labType === 'LAB'" filterable v-model="formModel.cpscTrfLabInfoList[index].labId"
                  :disabled="disabledCheck" :placeholder="$t('work.cpscTrfInfo.column.labName')" ref="template5"
                  @visible-change="(v) => visibleChange(v, 'template5', 'Laboratories', index)"
                           @change="handleOtherLabChange(index,$event)">
                  <el-option v-for="(item, index) in  Laboratories" :key="item.id"
                    :label="item.newLabel" :value="item.id">
                    {{ item.newLabel }}
                    <div class="flag"></div>
                  </el-option>
                </el-select>
                <avue-select clearable v-show="item.labType !== 'LAB'" filterable v-model="formModel.cpscTrfLabInfoList[index].labId"
                  :placeholder="$t('work.cpscTrfInfo.column.labName')" :disabled="disabledCheck"
                  :dic="basicLabList.map(item => { return { label: item.newLabel, value: item.labCode } })"
                             @change="handleLabChange(index,$event)">

                </avue-select>
              </template>

            </avue-form>
          </div>
        </div>
        <el-row type="flex" justify="center">
          <el-button size="small" type="primary" @click="formModel.cpscTrfLabInfoList.push({})"
            :disabled="disabledCheck">Add a
            Lab</el-button>
        </el-row>
      </el-collapse-item>

      <el-collapse-item name="2">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab2 }}</h3>


        </template>
        <div class="form-content">

          <div class="form-inner">
            <avue-form :option="formOption2Com" ref="form2" v-model="formModel"></avue-form>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item name="3">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab3 }}</h3>


        </template>
        <div class="form-content">

          <div class="form-inner">
            <avue-form :option="formOption3Com" ref="form3" v-model="formModel">
              <template slot="certVersion">
                <el-input maxlength="19" v-model="formModel.certVersion" clearable size="small" :disabled="disabledCheck">
                  <template slot="append">
                    <el-button type="primary" @click="handleGenerate" :disabled="disabledCheck">Generate</el-button>
                  </template>
                </el-input>
              </template>
            </avue-form>
            <!--        <el-button size="small" type="primary" @click="handleGenerate">Generate</el-button>-->
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item name="4">
        <template slot="title">


          <h3 style="font-weight: bold">{{ formLab4 }}</h3>


        </template>
        <div class="form-content">
          <div class="form-inner">
            <avue-form :option="formOption4Com" ref="form4" v-model="formModel">
              <template slot="pocCustomerId">
                <el-select v-model="formModel.pocCustomerId" :disabled="disabledCheck" :placeholder="$t('work.cpscTrfInfo.column.otherPoc')"
                  ref="template4" @visible-change="(v) => visibleChange(v, 'template4', 'POC')">
                  <el-option v-for="(item, index) in shipTemplates" :key="item.id" :label="item.tradeName"
                    :value="item.id">
                    {{ item.tradeName }}
                    <div class="flag"></div>
                  </el-option>
                </el-select>
              </template>
            </avue-form>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <el-row type="flex" justify="center">
      <el-button type="warning" plain v-if="trfInfo.trfInfoStastus == 2 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2"
        @click="handleSave(2)">{{ btnSaveLab }}
      </el-button>
      <el-button type="warning" plain v-if="trfInfo.trfInfoStastus == 2 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2"
        @click="handleSave(3)">{{ btnValidatedLab }}
      </el-button>
      <el-button type="warning" plain v-if="trfInfo.trfInfoStastus == 3 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2"
        @click="handleSave(2)">{{ btnReworkLab }}
      </el-button>
      <el-button type="warning" plain v-if="trfInfo.trfInfoStastus == 3 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2" @click="">{{
      btnExportCsvLab }}
      </el-button>
      <el-button type="warning" plain v-if="trfInfo.trfInfoStastus == 3 && this.trfInfo.isPending === '1'&&this.trfInfo.status!=2"
        @click="handlePush">{{ btnToCpscLab }}
      </el-button>
    </el-row>
    <el-dialog v-if="isDialog" :visible.sync="isDialog" append-to-body destroy-on-close :title="$t('crud.addTitle')" width="50%">
      <commen-dialog :cpscCustomerId="trfInfo.cpscCustomer.customerId" :type="type"
        @closeCommenDialog="closeCommenDialog($event)"></commen-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { submitEntry, doEfillingImport, tradeSelect, citationsSelect, getCollections } from "@/api/cpscTrfInfo/cpscTrfInfo";
import ColumnDots from "@/views/cpscTrfInfo/columnDots.vue";
import CommenDialog from './commenDialog.vue'
import { getBasicLabList } from "@/api/cpscTrfInfo/cpscTrfInfo";
export default {
  props: {
    trfInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    formModel: Object,
    loading: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      basicLabList: [],
      activeNames: ['1', '2', '3', '4'],
      shipTemplates: [],
      Laboratories: [],
      type: '',
      isDialog: false,
      // certTypeDict: [],

      btnSaveLab: this.$t('work.cpscTrfInfo.column.save'),
      btnValidatedLab: this.$t('work.cpscTrfInfo.column.validated'),
      btnReworkLab: this.$t('work.cpscTrfInfo.column.rework'),
      btnExportCsvLab: this.$t('work.cpscTrfInfo.column.exportCsv'),
      btnToCpscLab: this.$t('work.cpscTrfInfo.column.toCpsc'),
      formOption0: {
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            label: this.$t('work.cpscTrfInfo.column.certificateType'),
            labelWidth: 200,
            labelPosition: 'right',
            dicUrl: '/api/sgs-e-filling/sgs-system/dict/dictionary?code=certType',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            prop: 'certType',
            rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.certificateType') }],

            display: true,
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.certificateType')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.lastTestDate'),
            labelWidth: 200,
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.lastTestDate'),
            prop: 'lastTestDate',
            type: 'date',
            dataType: "string",
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            rules: [{ required: true ,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.lastTestDate')}]
          },
        ]
      },
      formLab1: this.$t('work.cpscTrfInfo.column.labInfo'),

      formOption1: {
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            prop: 'labTypeName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.labType'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'labType',
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=labType",
            dataType: "string",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            change: this.handleLabTypeChange,
            rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.labType') }],
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.labType')
          },
          {
            prop: 'labName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.labName'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'labId',
            type: 'select',
            dataType: "string",

            dicData: [],
            props: {
              label: 'tradeName', // 对象的属性，用作显示标签
              value: 'id' // 对象的属性，用作值
            },
            rules: [{ required: true ,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.labName')}],
            
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.labName')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.testReportId'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'testReportId',
            rules: [{ required: false ,message:this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.testReportId')}],

            placeholder: this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.testReportId')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.testReportKey'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'testReportKey',
            rules: [{ required: false,message:this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.testReportKey') }],

            placeholder: this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.testReportKey')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.reportUrl'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'reportUrl',
            rules: [{ required: false ,message:this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.reportUrl')}],
            row: true,
            span: 24,

            placeholder: this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.reportUrl')
          },
          {
            prop: 'citationsIdArray',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.citationsCode'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'citationsCodeArray',
            multiple: true,
            type: 'select',
            dataType: "string",
            //dicUrl: "/sgs-work/cpscCitation/listForSelect",
            dicData: [],
            filterable: true,
            props: {
              label: 'regulation', // 对象的属性，用作显示标签
              value: 'citationCode' // 对象的属性，用作值
            },
            
            popperClass:'testingExclusions',
            row: true,
            span: 24,

            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.citationsCode')
          },
        ]
      },
      formLab2: this.$t('work.cpscTrfInfo.column.testingExclusions'),
      formOption2: {
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            prop: 'exclusionsId',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.testingExclusions'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'exclusionsCode',
            type: 'select',
            filterable: true,
            dataType: "string",
            multiple: true,
            dicUrl: "/api/sgs-e-filling/sgs-work/cpscExclusion/listForSelect",
            props: {
              label: 'regulation', // 对象的属性，用作显示标签
              value: 'exclusionCode' // 对象的属性，用作值
            },
    
            popperClass:'testingExclusions',
            row: true,
            span: 24,

            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.testingExclusions')
          },
        ]
      },

      formLab3: this.$t('work.cpscTrfInfo.column.certificateInfo'),
      formOption3: {
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            label: this.$t('work.cpscTrfInfo.column.certificateVersion'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'certVersion',
            suffixIcon: 'Plus',
            rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.certificateVersion') }],

            display: true,
            placeholder: this.$t('work.cpscTrfInfo.placeholder.input') + this.$t('work.cpscTrfInfo.column.certificateVersion')
          },
          {
            label: this.$t('work.cpscTrfInfo.column.cpscCollection'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'certCpscCollection',
            type: 'select',
            dataType: "string",
            filterable:true,
            // dicUrl: "/api/sgs-e-filling/sgs-work/cpscTrfInfo/getCollections?customerId="+this.formModel.buyerCustomerId,
            dicData: [],
            props: {
              label: 'collectionName', // 对象的属性，用作显示标签
              value: 'collectionId' // 对象的属性，用作值
            },
            rules: [{ required: true,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.cpscCollection') }],
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.cpscCollection')
          },
        ]
      },

      formLab4: this.$t('work.cpscTrfInfo.column.poc'),
      formOption4: {
        // 表单配置项
        menuType: 'default',
        submitBtn: false,
        emptyBtn: false,
        disabled: this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2,
        column: [
          {
            prop: 'pocTypeName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.pocType'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'pocTypeId',
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=pocType",
            dataType: "string",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            // change:this.handlePOCTypeChange,
            rules: [{ required: true ,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.pocType')}],
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.pocType')
          },
          {
            prop: 'pocCustomerName',
            display: false,
          },
          {
            label: this.$t('work.cpscTrfInfo.column.otherPoc'),
            labelWidth: 200,
            labelPosition: 'right',
            prop: 'pocCustomerId',
            type: 'select',
            dataType: "string",
            //dicUrl: "/sgs-work/cpscCustomerTrade/listForSelect?tradeType=POC&cpscCustomerId="+this.formModel.cpscCustomer?.customerId,
            props: {
              label: 'tradeName', // 对象的属性，用作显示标签
              value: 'id' // 对象的属性，用作值
            },
            //change:this.handlePOCOtherChange,
            display: false,
            placeholder: this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.otherPoc')
          },
        ]
      },

    };
  },
  mounted() {
    this.setFormModel()
    this.getCollections()
    //this.initCertTypeDict()
    this.setBaseLabList()
  },
  created() {

  },
  computed: {
    disabledCheck() {
      return this.trfInfo.trfInfoStastus >= 3 || this.trfInfo.isPending === '2'||this.trfInfo.status==2
    },
    formOption0Com() {
      return this.formOption0
    },
    formOption1Com() {
      return this.formOption1
    },
    formOption2Com() {
      return this.formOption2
    },
    formOption3Com() {
      return this.formOption3
    },
    formOption4Com() {
      return this.formOption4
    }
  },

  components: {
    CommenDialog,
    ColumnDots
  },
  methods: {

    getCollections(id) {
      getCollections(id ? id : this.formModel.buyerCustomerId).then(res => {
        const column = this.findObject(this.formOption3.column, 'certCpscCollection')
        column.rules=res.data.data.length?[{required:true,trigger:'change',message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.cpscCollection')}]:[{required:false,message:this.$t('work.cpscTrfInfo.placeholder.select') + this.$t('work.cpscTrfInfo.column.cpscCollection')}]
        column.dicData = res.data.data
      })
    },
    setBaseLabList() {
      //查询SGSMart lab
      getBasicLabList('', 'CPSC').then(res => {
        let labList = res.data.data;
        labList.forEach(lab => {
          lab.id = lab.labCertificateList[0].certificateLabId
          lab.tradeName = lab.labName;
          lab.newLabel=`${lab.id}     ${lab.tradeName}`
        });
        this.basicLabList = labList;
      })
    },
    setPOC(type, value) {
      tradeSelect(type, value).then(res => {
        if (type == 'POC') {
          this.shipTemplates = res.data.data
          if (!this.shipTemplates.some(item => item.id === this.formModel.pocCustomerId)) {
            this.clearPocInfo();
          }
        } else if (type == 'Laboratories') {
          this.Laboratories = res.data.data.map(item=>{return{...item,newLabel:`${item.id}     ${item.tradeName}`}})
        }
      })
    },
    showShipTemplate(type) {
      if (this.formModel.buyerCustomerId) {
        this.isDialog = true
        this.type = type
      } else {
        this.$message.error(this.$t('work.cpscTrfInfo.buyerMessage'))
      }

    },
    visibleChange(visible, refName, type, index) {
      if (visible) {
        const ref = this.$refs[refName];

        let popper = ref.popperElm || ref[index].popperElm
        if (popper.$el) popper = popper.$el;
        if (
          !Array.from(popper.children).some(
            (v) => v.className === "el-template-menu__list"
          )
        ) {
          const el = document.createElement("ul");
          el.className = "el-template-menu__list";
          el.style =
            "border-bottom:1px solid #f5f5f5;; padding:0; color:#ff6600;font-size: 16px";
          el.innerHTML = `<li class="el-cascader-node text-center" style="height:37px;line-height: 50px;">
            <span class="el-cascader-node__label"><i class="font-blue el-icon-plus"></i>${this.$t('operation.add')}</span>
            </li>`;
          popper.insertBefore(el, popper.firstChild);
          el.onclick = () => {
            this.showShipTemplate(type);
          };
        }
      }
    },
    closeCommenDialog(e) {

      this.setPOC(e, this.formModel.cpscCustomer.customerId)

      this.isDialog = false

    },

    handleChangeCertType(value) {
      if (value) {

        const column = this.findObject(this.formOption1.column, 'citationsCodeArray');
        citationsSelect(value).then(res => {
          column.dicData = res.data.data
        })
      }
    },
    handleLabTypeChange(value) {
      const selectedObject = value.item;

      if (selectedObject) {
        this.formModel.labType = selectedObject.dictKey
        this.formModel.labTypeName = selectedObject.dictValue
        const column = this.findObject(this.formOption1.column, 'labId');
        if (selectedObject.dictKey == 'LAB') {
          this.setPOC('Laboratories', this.formModel.cpscCustomer.customerId)
        } else {
          column.dicData = []
        }
      }

    },
    // handlePOCTypeChange(value){
    //   const selectedObject = value.item;
    //   if (selectedObject ){
    //     if (selectedObject.dictValue == 'Other'){
    //       this.formOption4.column[3].display = true;
    //     }
    //   }else {
    //     this.formOption4.column[3].display = false;
    //   }
    // },
    setFormModel() {
      // this.formList = this.formModel.cpscTrfLabInfoList
      // if (this.formList = null || this.formList.length == 0){
      //   this.formList = [{}]
      // }
      const column = this.findObject(this.formOption4.column, 'pocCustomerId')
      column.display = this.formModel.pocTypeId == 'Other'
      if (!column.display) {
        this.formModel.pocCustomerId = ''
        this.formModel.pocCustomerName = ''
        //this.$emit('changeAddInfoPocCustomerId','')
      }

      if (this.formModel.cpscCustomer) {
        //判断versionid
        if (this.formModel.cpscCustomer.versionidType) {
          if (this.formModel.cpscCustomer.versionidType == '1') {
            this.formOption3.column[0].display = false;
            this.formOption1.column[4].rules[0].required = true;
          } else if (this.formModel.cpscCustomer.versionidType == '2') {
            this.formOption3.column[0].display = true;
            this.formOption1.column[4].rules[0].required = false;
          }
        }
        //加载根据买家的下拉
        this.setPOC('POC', this.formModel.cpscCustomer.customerId)
        //如果加载了实验室类型 则加载实验室下拉 需要两个select 都对应labid 根据labType 选择性展示哪一个
        //目前临时 只有系统内部实验室下拉
        this.setPOC('Laboratories', this.formModel.cpscCustomer.customerId)
      }
      //如果选择了certificate type 则加载标准类别下拉
      if (this.formModel.certType) {
        this.handleChangeCertType(this.formModel.certType);
      }
      
    },
    handleSave(trfStatus) {
      if (trfStatus == 2) {
        if (this.trfInfo.id) {
          let form = JSON.parse(JSON.stringify(this.formModel));
          form.cpscTrfLabInfoList = form.cpscTrfLabInfoList
          form.trfInfoStastus = trfStatus;
          form.id = this.trfInfo.id;

          if (this.formModel.cpscCustomer && this.formModel.cpscCustomer.versionidType) {
            if (this.formModel.cpscCustomer.versionidType == '1') {
              if (form.cpscTrfLabInfoList.length > 0) {
                let version = [];
                form.cpscTrfLabInfoList.map(item => version.push(item.testReportId));
                form.certVersion = version.join(',');
              }
            }
          }

          //labList
          //form.cpscTrfLabInfoList = this.formList;

          submitEntry(form).then(() => {
            this.$message({
              type: "success",
              message: "Success!"
            });
            // this.$emit("closeEditPage")
            this.$router.push('/cpscTrfInfo/cpscTrfInfo')
          }, error => {
            window.console.log(error);
          });
        }
      } else {
        //let arr =[]
        let formRefs = Object.keys(this.$refs)
          .filter((refName) => refName.startsWith("form"))
          .map((refName) => this.$refs[refName]);
        // formRefs[1].forEach(item=>arr.push(item))
        // formRefs.splice(0,1)

        // arr.push(...formRefs)
        // 标记所有表单是否通过校验的变量
        // let isValid = true
        //是否赋值Version
        if (this.formModel.cpscCustomer && this.formModel.cpscCustomer.versionidType) {
          if (this.formModel.cpscCustomer.versionidType == '1') {
            if (this.formModel.cpscTrfLabInfoList.length > 0) {
              let version = [];
              this.formModel.cpscTrfLabInfoList.map(item => version.push(item.testReportId));
              this.formModel.certVersion = version.join(',');
            }
          }
        }
        // 遍历表单数组，依次对每个表单进行校验
        const promise = new Promise(async (r, rj) => {
          for (let ref of formRefs) {
            const item = ref[0] || ref
            item.validate(valid => {

              if (!valid) {
                r('error')
                return
              }
            })

          }
          setTimeout(() => {
            r('success')
          }, 1000)
        })
        promise.then(res => {
          if (res == 'success') {
            if (this.trfInfo.id) {
              let form = JSON.parse(JSON.stringify(this.formModel));
              form.cpscTrfLabInfoList = form.cpscTrfLabInfoList
              form.trfInfoStastus = trfStatus;
              form.id = this.trfInfo.id;

              //labList
              //form.cpscTrfLabInfoList = this.formList;

              submitEntry(form).then(() => {
                this.$message({
                  type: "success",
                  message: "Success!"
                });
                // done();
                // this.$emit("closeEditPage")
                this.$router.push('/cpscTrfInfo/cpscTrfInfo')
              }, error => {
                window.console.log(error);
              });
            }
          }
        })
      }


    },
    handleGenerate() {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      const charactersLength = characters.length;
      for (let i = 0; i < 8; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      this.formModel.certVersion = result;
    },
    handlePush() {
      doEfillingImport(this.trfInfo.id).then(() => {
        this.$message({
          type: "success",
          message: "Success!"
        });
        // done();
        // this.$emit("closeEditPage")
        this.back()
      }, error => {
        window.console.log(error);
      });
    },
    clearPocInfo() {
      this.formModel.pocCustomerId = null
      this.formModel.pocCustomerName = null
    },
    back() {
      if (window.history.length > 1) {
        window.history.back();
        window.addEventListener('pageshow', function (event) {
          if (event.persisted || (window.performance && window.performance.navigation.type === 2)) {
            window.location.reload();
          }
        });
      } else {
        window.close();
      }
    },
    handleLabChange(index,evt){
      const selectedObject= this.basicLabList.find(item=>item.labCode==evt.value)
      const item = this.formModel.cpscTrfLabInfoList[index];
      if(selectedObject){
        item.labName = selectedObject.labName;
        item.labAddress = selectedObject.labAddress;
        if (selectedObject.labContactList[0]) {
          item.labPhone = selectedObject.labContactList[0].contactTel;
          item.labEmail = selectedObject.labContactList[0].contactEmail;
        }
      }else{
        item.labName = null
        item.labAddress = null
        item.labPhone =null
        item.labEmail = null
      }
    },
    handleOtherLabChange(index,evt) {
      const selectedObject = this.Laboratories.find(item => item.id == evt)
      const item = this.formModel.cpscTrfLabInfoList[index];
      item.labName = selectedObject.tradeName;
      item.labAddress = selectedObject.tradeAddress;
      if (selectedObject.cpscCustomerTradeContactVO[0]) {
        item.labPhone = selectedObject.cpscCustomerTradeContactVO[0].telphone;
        item.labEmail = selectedObject.cpscCustomerTradeContactVO[0].email;
      }
    },
  },
  watch: {
    'formModel.certType'(newVal, oldVal) {
      this.handleChangeCertType(newVal)
    },
    'formModel.pocTypeId'(newVal, oldVal) {
      //同步更改addInfo
      //this.$emit('changePocTypeId',newVal)
      const column = this.findObject(this.formOption4.column, 'pocCustomerId')
      column.display = newVal == 'Other'
      if (!column.display) {
        this.formModel.pocCustomerId = ''
        this.formModel.pocCustomerName = ''
        //this.$emit('changeAddInfoPocCustomerId','')
      }
    },

    //     'formModel.pocCustomerId'(newVal, oldVal) {

    //      this.$emit('changeAddInfoPocCustomerId',newVal)

    //  },
  }
};
</script>

<style lang="scss" scoped>
#app {
  background-color: white;
  min-height: 100vh;
  /* 确保背景颜色覆盖整个视口 */
  overflow: auto;
  /* 超出内容时显示滚动条 */
}

.header {
  background-color: #f0f2f5;
  padding: 5px;
}

.line1 {
  //padding: 15px;
  margin: 0;
}

.line2 {
  display: flex;
  align-items: flex-end;
  /* 确保元素在同一行 */
  justify-content: flex-start;
  //padding: 15px;
}

.line2>* {
  margin: 0;
  /* 移除默认的外边距 */
  z-index: 99999;
}

span {
  padding: 5px;
  /* 在<span>元素内部添加5px的内边距 */
}

.form-content,
.form-inner {
  margin: 20px;
}

.el-collapse-item__header {
  padding: 10px;
  background-color: #f5f5f5;

  .info {
    font-size: 18px;
    font-weight: 500
  }
}

.el-input__wrapper {
  height: 30px;
}

.el-select.el-select--small {
  width: 100%
}
</style>
