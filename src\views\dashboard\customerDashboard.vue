<template>
    <div class="dashboard-main">
        <div class="toast"></div>
        <el-row>

            <el-col :span="24">
                <el-row class="first-line" v-if="false"> <!-- 隐藏旧版的 -->
                    <!-- 买家报表(柱形图) Buyer测试结果 -->
                    <el-col v-if="permissionList.buyerOverallUnit" :span="17" class="buyer-report">
                        <el-card class="sgs-box" style="padding: 30px">
                            <div style="width: 95%; margin: 6px auto; margin-bottom: 20px">
                                <SliderDate :value-data="valueData" @input="rangeChange"
                                            :is-show-select="false"></SliderDate>
                            </div>
                            <!-- <div id="main" style="width: 100%; height: 250px"></div> -->
                        </el-card>
                    </el-col>

                    <!-- 供应商报表 Supplier -->
                    <el-col style="display:none"
                            v-if="permissionList.supplierOverallUnit && newProductLineCode != 'AFL'"
                            class="supplier-report" :span="17">
                        <el-card class="sgs-box">
                            <div class="sgs-group">
                                <h2>{{ $t("wel1.statusTracking") }}</h2>
                                <div class="right">
                                    <router-link v-if="permissionList.newTrfBtn" tag="a"
                                                 :to="{ path: '/trf/trfDetail' }">
                                        <el-button type="primary">{{ $t("wel1.newApplication") }}</el-button>
                                    </router-link>
                                </div>
                            </div>
                            <div class="slider">
                                <SliderDate :value-data="valueData" @input="rangeChange" :is-show-select="false"/>
                            </div>
                            <div id="trf-status" v-loading="loading">

                                <ul>
                                    <li>
                                        <router-link tag="a"
                                                     :to="{
                        path: '/trf/newList?from=0&trfStatus=1',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.draftTRF == null ? 0 : orderSum.draftTRF
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.draftTrf") }}</p>
                                                <p><i class="glyphicon glyphicon-edit"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=2',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.trfsubmitted == null ? 0 : orderSum.trfsubmitted
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.trfSubmitted") }}</p>
                                                <p><i class="glyphicon glyphicon-check"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=3',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.jobPreparation == null ? 0 : orderSum.jobPreparation
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.jobPreparation") }}</p>
                                                <p><i class="glyphicon glyphicon-share"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=4',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                        <span class="count">{{
                                orderSum.testingInProgress == null ? 0 : orderSum.testingInProgress
                            }}</span>
                                                <p class="status">{{ $t("wel1.testingInProgress") }}</p>
                                                <p><i class="glyphicon glyphicon-hourglass"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=5',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.reportCompleted == null ? 0 : orderSum.reportCompleted
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.reportCompleted") }}</p>
                                                <p><i class="glyphicon glyphicon-ok"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                </ul>
                            </div>
                        </el-card>
                    </el-col>

                    <!--AFL-->
                    <el-col v-if="newProductLineCode=== 'AFL'" :span="24" class="afl">
                        <el-card class="sgs-box">
                            <div class="sgs-group">
                                <h2>{{ $t("wel1.statusTracking") }}</h2>
                                <div class="right" style="margin: 0;">
                                    <router-link v-if="permissionList.newAflTrfBtn" tag="a"
                                                 :to="{ path: '/afl/trf/newTrf' }">
                                        <el-button type="primary">{{ $t("wel1.newApplication") }}</el-button>
                                    </router-link>
                                </div>
                            </div>
                            <div class="slider">
                                <SliderDate :value-data="valueData" @input="rangeChange"
                                            :is-show-select="false"></SliderDate>
                            </div>
                            <div id="afl-trf-status" v-loading="loading">
                                <ul>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=1',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.draftTRF == null ? 0 : orderSum.draftTRF
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.draftTrf") }}</p>
                                                <p><i class="glyphicon glyphicon-edit"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=2',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.trfsubmitted == null ? 0 : orderSum.trfsubmitted
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.trfSubmitted") }}</p>
                                                <p><i class="glyphicon glyphicon-check"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=3',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.jobPreparation == null ? 0 : orderSum.jobPreparation
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.jobPreparation") }}</p>
                                                <p><i class="glyphicon glyphicon-share"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <!--AFL-->
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=13',
                        query: {
                          startDate: queryParam.startDate,
                          endDate: queryParam.endDate,
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.quotationToBeConfirm == null ? 0 : orderSum.quotationToBeConfirm
                                                    }}</span>
                                                <p class="status">
                                                    <span style="width: 50px">{{
                                                            $t("wel1.quotationToBeConfirmed")
                                                        }}</span>
                                                </p>
                                                <p><i class="glyphicon glyphicon-eur"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=14',
                        query: {
                          startDate: queryParam.startDate,
                          endDate: queryParam.endDate,
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.quotationConfirm == null ? 0 : orderSum.quotationConfirm
                                                    }}</span>
                                                <p class="status">
                                                    {{ $t("wel1.quotationConfirmed") }}
                                                </p>
                                                <p><i class="glyphicon glyphicon-eur"></i></p>
                                            </div>
                                        </router-link>
                                    </li>

                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=4',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.testingInProgress == null ? 0 : orderSum.testingInProgress
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.testingInProgress") }}</p>
                                                <p><i class="glyphicon glyphicon-hourglass"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                    <li>
                                        <router-link
                                                tag="a"
                                                :to="{
                        path: '/trf/newList?from=0&trfStatus=5',
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime(),
                        },
                      }"
                                        >
                                            <div class="box">
                                                <span class="count">{{
                                                        orderSum.reportCompleted == null ? 0 : orderSum.reportCompleted
                                                    }}</span>
                                                <p class="status">{{ $t("wel1.reportCompleted") }}</p>
                                                <p><i class="glyphicon glyphicon-ok"></i></p>
                                            </div>
                                        </router-link>
                                    </li>
                                </ul>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 报告统计 即原版测试结果 Buyer & Supplier -->
                    <el-col v-if="permissionList.trfCountUnit && newProductLineCode != 'AFL'" span="7"
                            class="report" v-loading="reportLoading">
                        <el-card class="sgs-box" style="height: 100%">
                            <div id="status-count" style="height: 100%; margin-top: 23px">
                                <ul>
                                    <li v-for="item in reportCounts" :key="item.name">
                                        <router-link tag="a"
                                                     :to="{ path: item.path, query: { startDate: encodeURIComponent(queryParam.startDate), endDate: encodeURIComponent(queryParam.endDate), hash: new Date().getTime()},}">
                                            <el-row class="box" :gutter="24">
                                                <el-col :span="6" class="icon" :style="{background: item.color}">
                                                    <i :class="'el-icon-' + item.icon"></i>
                                                </el-col>
                                                <el-col :span="18" class="count" :style="{color: item.color}">
                                                    {{ $t(item.lang) }}
                                                    {{ orderSum[item.value] == null ? 0 : orderSum[item.value] }}
                                                </el-col>
                                            </el-row>
                                        </router-link>
                                    </li>
                                </ul>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 申请单状态 -->
                <!-- 申请单状态/测试结果：{{ orderSum }} -->
                <el-row>
                    <el-card class="sgs-box document requisition-status" id="status-list">
                        <div class="sgs-group">
                            <h3>
                                {{ $t("wel1.requisitionStatus") }}
                                <el-date-picker
                                        style="float: right; margin-top: -15px; width: 272px;"
                                        class="time-select"
                                        @change="loadTrfStatData(handleDateChange($event))"
                                        @focus="handleDateFocus"
                                        v-model="dateVal"
                                        type="monthrange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        :start-placeholder="$t('datePicker.startTime')"
                                        :end-placeholder="$t('datePicker.endTime')"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="pickerOptionsMonth">
                                </el-date-picker>
                            </h3>
                        </div>
                        <el-col span="24" v-if="newProductLineCode != 'AFL'">
                            <ul>
                                <template v-for="(item, index) in statusList">
                                    <li :key="item.name" v-if="item.type != 'AFL'">
                                        <router-link tag="a"
                                                     :to="{
                        path: '/trf/newList?from=0&trfStatus=' + item.trfStatus,
                        query: {
                          startDate: encodeURIComponent(queryParam.startDate),
                          endDate: encodeURIComponent(queryParam.endDate),
                          hash: new Date().getTime()
                        }
                      }">
                                            <div class="box">
                                                <img class="icon" :src="'/img/icon/status/' + item.trfStatus + '.png'"/>
                                                <p class="status" :class="language=='en-US' ? 'en-status':''">
                                                    {{ $t(item.lang) }}</p>
                                                <span class="count">{{
                                                        orderSum.trfStatusCount[item.value] || 0
                                                    }}</span>
                                            </div>
                                        </router-link>
                                    </li>
                                </template>
                            </ul>
                        </el-col>

                        <!-- AFL -->
                        <el-col span="24" v-if="newProductLineCode==='AFL'">
                            <ul>
                                <li v-for="(item, index) in statusList" :key="item.name"
                                    :class="{'afl-status': newProductLineCode === 'AFL' }">
                                    <router-link tag="a"
                                                 :to="{
                      path: '/trf/newList?from=0&trfStatus=' + item.trfStatus,
                      query: {
                        startDate: encodeURIComponent(queryParam.startDate),
                        endDate: encodeURIComponent(queryParam.endDate),
                        hash: new Date().getTime()
                      }
                    }">
                                        <div class="box">
                                            <img class="icon" :src="'/img/icon/status/' + item.trfStatus + '.png'"/>
                                            <p class="status" :style="{'height':language=='en-US'?'50px':'auto'}">
                                                {{ $t(item.lang) }}</p>
                                            <span class="count">{{ orderSum.trfStatusCount[item.value] || 0 }}</span>
                                        </div>
                                    </router-link>
                                </li>
                            </ul>
                        </el-col>
                    </el-card>
                </el-row>

                <!-- 最新申请单 -->
                <el-card class="sgs-box document test-result clearfix"  id="last-trf">
                    <div class="sgs-group">
                        <h3 stype="position: relative;">
                            {{ $t("wel1.newTestRequestForm") }}
                            <!-- v-if="permissionList.newTrfBtn" -->
                            <div class="pull-right" style="margin-top: -20px;" >
                                <el-popover
                                        width="260"
                                        trigger="hover"
                                        placement="bottom">
                                    <ul class="list-unstyled add-menu" v-if="menu.length > 0">
                                        <li v-for="item in menu[0].children[0].children[0].children" :key="item.id">
                                            <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                                                <router-link target="_blank" :to="{ path: item.path }">
                                                    {{ item.name.length > 30 ? (item.name.substr(0,28)+'...') : item.name }}
                                                </router-link>
                                            </el-tooltip>
                                        </li>
                                    </ul>
                                    <el-button class="line-btn add-trf-btn"  slot="reference">
                                        <img src="/img/icon/addTrf.png"/> {{ $t("wel1.newApplication") }}
                                        <i class="el-icon-arrow-down pull-right"
                                           style="padding-top: 6px;font-weight: bold;"></i>
                                    </el-button>
                                </el-popover>
                            </div>
                        </h3>
                    </div>
                    <NewTestRequestForm />
                </el-card>

                <!-- 测试结果 -->
                <el-row v-if="permissionList.trfCountUnit && newProductLineCode != 'AFL'">
                    <el-card class="sgs-box document test-result" style="height: 454px" id="test-result">
                        <div class="sgs-group">
                            <h3>
                                {{ $t("testResult.title") }}
                                <el-date-picker
                                        style="float: right; margin-top: -15px; width: 272px;"
                                        @change="loadAllEchart(handleDateChange($event))"
                                        @focus="handleDateFocus"
                                        v-model="testResultDate"
                                        type="monthrange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder="开始月份"
                                        end-placeholder="结束月份"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="pickerOptionsMonth">
                                </el-date-picker>
                            </h3>
                        </div>
                        <el-col span="8" class="pie-chart">
                            <el-row :gutter="0" class="text-center">
                                <el-col span="8" v-for="item in reportCounts.slice(0,3)" :key="item.name">
                                    <router-link
                                            tag="a"
                                            :to="{
                      path: item.path,
                      query: {
                        startDate: encodeURIComponent(queryParam.startDate),
                        endDate: encodeURIComponent(queryParam.endDate),
                        hash: new Date().getTime()
                      }
                    }">
                                        <p class="tit"><span :class="item.class"></span>{{ $t(item.lang) }}</p>
                                        <h2>{{ orderSum.reportStatusCount[item.value] }}</h2>
                                    </router-link>
                                </el-col>
                                <el-col span="24" class="line-chart">
                                    <TestResultPieChart :orderSum="orderSum.reportStatusCount"/>
                                </el-col>
                            </el-row>
                        </el-col>
                        <el-col span="16">
                            <!-- <TestResultLineChart :option="echartOption" /> -->
                            <div id="main" style="width: 100%; min-height: 400px"></div>
                        </el-col>
                    </el-card>
                </el-row>

                <el-row :gutter="24">
                    <!-- 资料库 -->
                    <el-col v-if="permissionList.documentUnit" span="8" class="title">
                        <el-card class="sgs-box document" style="min-height: 350px">
                            <div class="sgs-group">
                                <h3>{{ $t("wel1.documentLibrary") }}</h3>
                            </div>
                            <div class="page-example3" @click="choosLibrary($event)">
                                <vue-seamless-scroll :data="sumDocumentLibraryList" class="seamless-warp"
                                                     :class-option="classOption">
                                    <ul class="list">
                                        <li v-for="(item, index) in sumDocumentLibraryList" class="listItem" :data="JSON.stringify(item)" :key="index">
                                            <span v-if="item.isNew" class="pull-right new-tag">NEW</span>
                                            <a class="lib-a" target="_blank">
                                                {{ item.documentTitle }}
                                            </a>
                                        </li>
                                    </ul>
                                </vue-seamless-scroll>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- SGS服务 -->
                    <el-col v-if="permissionList.newServiceUnit" span="8">
                        <el-card class="sgs-box document" style="min-height: 350px">
                            <div class="sgs-group">
                                <h3>{{ $t("wel1.SGSNewService") }}</h3>
                            </div>
                            <div class="page-example3" @click="chooseService($event)">
                                <vue-seamless-scroll :data="newServiceList" class="seamless-warp"
                                                     :class-option="classOption">
                                    <ul class="list">
                                        <li v-for="(item, index) in newServiceList" class="listItem" :data="JSON.stringify(item)"  :key="index">
                                            <span v-if="item.isNew" class="pull-right new-tag">NEW</span>
                                            <a class="lib-a" target="_blank"
                                              >
                                                {{ item.textDisplay }}
                                            </a>
                                        </li>
                                    </ul>
                                </vue-seamless-scroll>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 最新资讯 -->
                    <el-col v-if="permissionList.safeguardUnit" span="8">
                        <el-card class="sgs-box document" style="min-height: 350px">
                            <div class="sgs-group">
                                <h3>{{ $t("wel1.SGSSafeguard") }}</h3>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 热点 -->
                    <el-col v-if="permissionList.testRequestFormUnit" span="8">
                        <el-card class="sgs-box document" style="min-height: 350px">
                            <div class="sgs-group">
                                <h3>{{ $t("wel1.hot") }}</h3>
                            </div>
                            <div class="page-example3" @click="chooseServiceHot($event)">
                                <vue-seamless-scroll :data="newHotList" class="seamless-warp"
                                                     :class-option="classOption">
                                    <ul class="list">
                                        <li v-for="(item, index) in newHotList" class="listItem" :data="JSON.stringify(item)"  :key="index">
                                            <span v-if="item.isNew" class="pull-right new-tag">NEW</span>
                                            <a class="lib-a" target="_blank"
                                               >
                                                {{ item.textDisplay }}
                                            </a>
                                        </li>
                                    </ul>
                                </vue-seamless-scroll>
                            </div>

                        </el-card>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>


        <el-dialog
                :title="$t('trf.trfTemplateList')"
                :visible.sync="dialogItemNumberFormVisible"
                top="20vh"
                width="55%"
        >
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">{{
                                $t("template.templateName")
                            }}</label>
                        <div class="col-sm-6">
                            <input
                                    clearable
                                    type="text"
                                    v-model="trfTemplateQuery.trfTemplateName"
                                    class="form-control"
                            />
                        </div>
                        <div class="col-sm-3">
                            <el-button type="primary" @click="search">{{
                                    $t("operation.search")
                                }}
                            </el-button>
                        </div>
                    </div>
                </div>

                <el-table :data="trfTemplateList" style="width: 100%">
                    <el-table-column
                            prop="trfTemplateName"
                            :label="$t('template.templateName')"
                            width="280"
                    >
                        <template slot-scope="scope">
                            <a
                                    style="text-decoration-line: underline; color: #aeacbb"
                                    href="javascript:void(0);"
                                    @click="trfTemplateClick(scope.row.id, scope.row.productLineCode)"
                            >
                                {{ scope.row.trfTemplateName }}
                            </a>
                            <!-- <el-button @click="showLabDetail(scope.row)" type="text" size="mini">查看</el-button>-->
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="createTime"
                            :label="$t('user.createTime')"
                            width="280"
                    >
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="240"
                            align="center"
                            fixed="right"
                    >
                        <template slot-scope="scope">
                            <!-- <el-button @click="showLabDetail(scope.row)" type="text" size="mini">查看</el-button>-->
                            <el-button
                                    @click="deleteTrfTemplate(scope.row)"
                                    type="text"
                                    size="mini"
                                    icon="el-icon-delete"
                            >{{ $t("operation.remove") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total"
                >
                </el-pagination>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogItemNumberFormVisible = false">{{
                        $t("operation.cancel")
                    }}
                </el-button>
                <el-button type="primary" @click="dialogItemNumberFormVisible = false"
                >{{ $t("operation.confirm") }}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import moment from "moment";
    import i18n from "@/lang";
    import {
        trfStatCount,
        reportStatCount,
        reportStatCountByMonth,
        documentLibrary,
        queryTrfTemplateList,
        queryNewServiceList,
        removeTrfTemplate,
    } from "@/api/welIndex";
    import {objectIsNull,validatenull} from "@/util/validate";
    import _ from "lodash";
    import {getCloudFileURL} from "@/api/common/index";
    import Menu from './components/CreateTrfMenu.vue'
    import {ProductLineEnums} from "@/commons/enums/BuEnums";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
    let color = ["#34A853", "#EA4336", "#D9D9D9"]
    import {deepClone} from '@/util/util'


    export default {
        name: "homepage",
        data() {
            let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD') //moment().format('YYYY-MM-DD');
            return {
                LanguageEnums:LanguageEnums,
                newProductLineCode:'',
                guideIndex: 0,
                guideVisible: false,
                guideStep: [
                    this.$t('guide.step.step1'),
                    this.$t('guide.step.step2'),
                    this.$t('guide.step.step3'),
                    this.$t('guide.step.step4'),
                ],
                loading: false,
                reportLoading: false,
                dialogItemNumberFormVisible: false,
                activeNames: ["1", "2", "3", "5"],
                trfTemplateQuery: {
                    trfTemplateName: "",
                },
                logActiveNames: ["13"],
                orderSum: {
                    trfStatusCount: {
                        draftTrf: 0,
                        trfSubmitted: 0,
                        jobPreparation: 0,
                        testingInProgress: 0,
                        reportCompleted: 0,
                    },
                    reportStatusCount: {
                        passCount: 0,
                        failCount: 0,
                        otherCount: 0,
                        allCount: 0,
                    },
                },
                newService: {
                    list: [],
                },
                buyerManual: {
                    list: [],
                    vipList: [],
                },
                buyerSafeguard: {
                    list: [],
                },
                buyerGroupCode: "",
                formSelect: {},
                documentLibraryList: [],
                trfTemplateList: [],
                newServiceList: [],
                newHotList: [],
                sumServiceList: [],
                sumDocumentLibraryList: [],
                valueData: [startDate, endDate],
                queryParam: {
                    startDate: startDate,
                    endDate: endDate,
                },
                sort: {descs: "update_time"},
                page: {
                    pageSize: 5,
                    currentPage: 1,
                    total: 0,
                },
                isSupplierDiv: true,
                isBuyerDiv: false,
                statusList: [
                    {name: '待递交', trfStatus: 1, lang: 'wel1.draftTrf', value: 'draftTrf', icon: '1.png'},
                    {name: '已提交', trfStatus: 2, lang: 'wel1.trfSubmitted', value: 'trfSubmitted', icon: '2.png'},
                    {name: '报价单待确认', trfStatus: 13, lang: 'wel1.quotationToBeConfirmed', value: 'quotationToBeConfirm', type: 'AFL', icon: '3.png'},
                    {name: '已确认报价单', trfStatus: 14, lang: 'wel1.quotationConfirmed', value: 'quotationConfirm', type: 'AFL', icon: '4.png'},
                    {name: '已受理', trfStatus: 3, lang: 'wel1.jobPreparation', value: 'jobPreparation', icon: '5.png'},
                    {name: '测试中', trfStatus: 4, lang: 'wel1.testingInProgress', value: 'testingInProgress', icon: '6.png'},
                    {name: '完成', trfStatus: 5, lang: 'wel1.reportCompleted', value: 'reportCompleted', icon: '7.png'}
                ],
                reportCounts: [
                    {
                        name: '通过',
                        value: 'passCount',
                        lang: 'wel1.pass',
                        path: '/trf/newList?from=1&reportResult=Pass',
                        color: '#20b426',
                        icon: 'success',
                        class: 'green'
                    },
                    {
                        name: '未通过',
                        value: 'failCount',
                        lang: 'wel1.fail',
                        path: '/trf/newList?from=1&reportResult=Fail',
                        color: '#d72926',
                        icon: 'error',
                        class: 'red'
                    },
                    {
                        name: '不做评判',
                        value: 'otherCount',
                        lang: 'wel1.seeResult',
                        path: '/trf/newList?from=1&reportResult=See Result',
                        color: '#909399',
                        icon: 'view',
                        class: 'grey'
                    },
                    {
                        name: '总计',
                        value: 'allCount',
                        lang: 'wel1.total',
                        path: '/trf/newList?from=1&reportResult=All',
                        color: '#f60',
                        icon: 's-data',
                        class: 'green'
                    }
                ],
                datePptions: [
                    // { value: 'all', label: '全部' },
                    {value: 'week', label: '最近一周'},
                    {value: 'month', label: '最近一个月'},
                    {value: 'halfYear', label: '最近半年'},
                    {value: 'year', label: '最近一年'},
                ],
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近半年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                dateVal: 'month',
                pickerOptionsMonth: {
                    shortcuts: [{
                        text: this.$t('datePicker.lastWeek'),
                        onClick(picker) {
                            const start = new Date();
                            const end = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.$t("datePicker.lastMonth"),
                        onClick(picker) {
                            const start = new Date();
                            const end = new Date();
                            start.setMonth(start.getMonth() - 1);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                        {
                            text: this.$t("datePicker.lastHalfYear"),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setMonth(start.getMonth() - 6);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: this.$t("datePicker.lastYear"),
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setMonth(start.getMonth() - 12);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                },
                testResultDate: '',
                echartOption: {},
                showAddTrfMenu: false,
                dateKey: new Date().getTime()
            };
        },
        watch: {
            "$i18n.locale": function () {
                this.initEcharts();

            },
            //监听语言变化
            language: function (newVal) {
              this.onLoad();
            },
        },
        computed: {
            ...mapGetters(["permission", "userInfo", "menu", "language", "dimensions"]),
            permissionList() {
                return {
                    buyerOverallUnit: this.vaildData(this.permission['sgs:main:buyer:overall'], false),
                    trfCountUnit: this.vaildData(this.permission['sgs:main:trfCount'], false),
                    documentUnit: this.vaildData(this.permission['sgs:main:document'], false),
                    newServiceUnit: this.vaildData(this.permission['sgs:main:newService'], false),
                    safeguardUnit: this.vaildData(this.permission['sgs:main:safeguard'], false),
                    testRequestFormUnit: this.vaildData(this.permission['sgs:main:testRequestForm'], false),
                    supplierOverallUnit: this.vaildData(this.permission['sgs:main:supplier:overall'], false),
                    newTrfBtn: this.vaildData(this.permission['sgs:main:supplier:addTrf'], false),
                    newAflTrfBtn: this.vaildData(this.permission['sgs:main:afl:addTrf'], false),
                };
            },
            classOption() {
                return {
                    step: 0.2, // 数值越大速度滚动越快
                    limitMoveNum: 8, // 开始无缝滚动的数据量 this.dataList.length
                    hoverStop: true, // 是否开启鼠标悬停stop
                    direction: 1, // 0向下 1向上 2向左 3向右
                    openWatch: true, // 开启数据实时监控刷新dom
                    singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
                    singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
                    waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
                }
            },
            role() {
                return {
                    isSGS: this.haseRole("SGSUserRole", "SgsAdmin")|| this.haseRole("SGSUserRole", "SgsLabUser"),
                    isBuyer: this.haseRole('UserRole', 'Buyer'),
                }
            }
        },
        components: {
            Menu,
            SliderDate: (resolve) => require(["@/components/statistics/SliderDate"], resolve),
            vueSeamlessScroll: (resolve) => require(["vue-seamless-scroll"], resolve),
            TestResultPieChart: (resolve) => require(["@/components/chart/TestResultPieChart"], resolve),
            TestResultLineChart: (resolve) => require(["@/components/chart/TestResultLineChart"], resolve),
            NewTestRequestForm: (resolve) => require(["./components/NewTestRequestForm.vue"], resolve),
        },
        created() {
           this.newProductLineCode=this.userInfo.productLineCode;
           if(this.userInfo.productLineCode==='all'){
             this.newProductLineCode=this.userInfo.defaultProductLineCode;
           }
            // if(localStorage.getItem('GUIDE') == null) this.guideVisible = true
            this.initStatData();
            this.onLoad();

            // 初始日期显示
            this.dateVal = [this.queryParam.startDate, this.queryParam.endDate]
            this.testResultDate = [this.queryParam.startDate, this.queryParam.endDate]
        },
        methods: {
            choosLibrary(e){
                const path = e.path || (e.composedPath && e.composedPath());
                let target = path.filter((r) => /listItem/.test(r.className));
                if (target.length) target = target[0];
                else return;
                const data = JSON.parse(target.getAttribute("data"));
                this.documentLibraryClick(data.attachments, data.documentUrl);
            },
            chooseService(e){
                const path = e.path || (e.composedPath && e.composedPath());
                let target = path.filter((r) => /listItem/.test(r.className));
                if (target.length) target = target[0];
                else return;
                const data = JSON.parse(target.getAttribute("data"));
                this.newServiceClick(data.filePath, data.serviceUrl);
          },
          chooseServiceHot(e){
                const path = e.path || (e.composedPath && e.composedPath());
                let target = path.filter((r) => /listItem/.test(r.className));
                if (target.length) target = target[0];
                else return;
                const data = JSON.parse(target.getAttribute("data"));
                this.newServiceClick(data.filePath, data.serviceUrl);
          },
          currentTz(value,format){
            if (!value) return ''
            if(!format){
              format='YYYY-MM-DD HH:mm:ss';
            }
            value = tzFormChina(value,format);
            value = value.toString();
            return value;
          },
          tzToChina(val){
            if (!val) return ''
            let value = tzToChina(val);
            return value;
          },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            //判断是否存在创建TRF菜单
            validateTrfMenu() {
                debugger;
                let result = false;
                if (!validatenull(this.menu)) {
                    let menuStr = JSON.stringify(this.menu);
                    if (!validatenull(menuStr)) {
                        if (menuStr.indexOf("/ccl/trf/newTrf") != -1) {
                            result = true;
                        }
                    }
                }
                return result;
            },
            enter() {
                this.showAddTrfMenu = true;
            },
            leave() {
                setTimeout(() => {
                    this.showAddTrfMenu = false;
                }, 500)
            },
            handleDateFocus(e) {
                let nodes = document.querySelectorAll('.el-picker-panel__shortcut')
                let lang = ["datePicker.lastWeek", "datePicker.lastMonth", "datePicker.lastHalfYear", "datePicker.lastYear"]
                nodes.forEach((item, index) => {
                    item.innerText = this.$t(lang[index % 4])
                })
            },
            handleDateChange(e) {
              debugger
                if (e === null) {
                    let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                        endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD')
                    this.queryParam.startDate = startDate
                    this.queryParam.endDate = endDate
                } else {
                    this.queryParam.startDate = moment(e[0]).format("YYYY-MM-DD")
                    this.queryParam.endDate = moment(e[1]).format("YYYY-MM-DD")
                }
                return this.queryParam;
            },
            documentLibraryClick(files, url) {
                if (validatenull(files)) {
                  if(!url){
                    this.$notify({
                      title: this.$t('tip'),
                      message: this.$t('authorization.visibleOnlyTip'),
                      type: 'warning'
                    });
                    return;
                  }
                  window.open(url);
                } else {
                    this.download(files[0].fileUrl);
                }
            },
            newServiceClick(fileUrl, serviceUrl) {
                if (validatenull(fileUrl)) {
                    window.open(serviceUrl);
                } else {
                    this.download(fileUrl);
                }
            },
            openTemplateDialog() {
                this.dialogItemNumberFormVisible = true;
                //this.initLoadTrfTemplate();
            },
            deleteTrfTemplate(row) {
                this.$confirm(this.$t("operation.confirmDelete"), this.$t("tip"), {
                    confirmButtonText: this.$t("submitText"),
                    cancelButtonText: this.$t("cancelText"),
                    type: "warning",
                })
                    .then(() => {
                        removeTrfTemplate(row.id).then(
                            () => {
                                this.$message({
                                    type: "success",
                                    message: "操作成功!",
                                });
                                //this.initLoadTrfTemplate();
                            },
                            (error) => {
                                console.log(error);
                            }
                        );
                    })
                    .catch(() => {
                        /* this.$message({
                          type: 'info',
                          message: '已取消删除'
                        });*/
                    });
            },
            search() {
                this.page.currentPage = 1;
                //this.initLoadTrfTemplate();
            },
            //分页查询
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
               // this.initLoadTrfTemplate();
            },
            currentChange(pageCurrent) {
                this.page.currentPage = pageCurrent;
               // this.initLoadTrfTemplate();
            },

            download(cloudId) {
                getCloudFileURL(cloudId).then((res) => {
                    window.open(res.data, "_blank");
                });
            },
            async rangeChange(data) {
                //开启loading
                var startDate = this.getCurrentMonthFirst(data[0]);
                var endDate = this.getCurrentMonthLast(data[1]);
                this.$set(this.queryParam, "startDate", startDate);
                this.$set(this.queryParam, "endDate", endDate);
                this.valueData = data;
                this.$set(
                    this,
                    "echartsBarTitle",
                    moment(data[0]).format("YYYY-MM-DD") +
                    " - " +
                    moment(data[1]).format("YYYY-MM-DD")
                );
                this.initStatData();
            },
            getCurrentMonthFirst(date) {
                var startdate = new Date(date);
                startdate.setDate(1);
                return moment(startdate).format("YYYY-MM-DD");
            },
            //获取所选月份的最后一天
            getCurrentMonthLast(date) {
                var endDate = new Date(date);
                var month = endDate.getMonth();
                var nextMonth = month;
                var nextMonthFirstDay = new Date(endDate.getFullYear(), nextMonth, 1);
                var oneDay = 1000 * 60 * 60 * 24;
                var newEndDate = new Date(nextMonthFirstDay - oneDay);
                return moment(newEndDate).toDate();
            },
            trfTemplateClick(trfTemplateId, productLineCode) {

                if (productLineCode == ProductLineEnums.AFL.CODE) {
                    this.$router.push({
                        path: "/afl/trf/trfDetail",
                        query: {trfTemplateId: trfTemplateId, actionType: "trfTemplate"},
                    });
                } else {
                    this.$router.push({
                        path: "/trf/trfDetail",
                        query: {
                            trfTemplateId: trfTemplateId,
                            flag: 1,
                            actionType: "trfTemplate",
                        },
                    });
                }
            },
            onLoad() {
                this.initDocumentLibrary();
                //this.initLoadTrfTemplate();
                this.initLoadNewService();
                this.initLoadHotService();
            },
            initDocumentLibrary() {
                //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                  languageId=LanguageEnums.EN.code;
                }else{
                  languageId=LanguageEnums.CN.code;
                }
                documentLibrary(this.userInfo.productLineCode,languageId).then((res) => {
                    var results = res.data.data;
                    this.sumDocumentLibraryList = results;
                });
            },
            initLoadTrfTemplate() {
            },
            initLoadNewService() {
                 //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                    languageId=LanguageEnums.EN.code;
                }else{
                    languageId=LanguageEnums.CN.code;
                }
                queryNewServiceList(0,this.userInfo.productLineCode,languageId).then((res) => {
                    this.newServiceList = res.data.data;
                });
            },
            initLoadHotService() {
                  //将当前语言放入请求中
                  let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                    languageId=LanguageEnums.EN.code;
                }else{
                    languageId=LanguageEnums.CN.code;
                }
                queryNewServiceList(1,this.userInfo.productLineCode,languageId).then((res) => {
                    this.newHotList = res.data.data;
                });
            },

            //统计数据加载
            initStatData() {
                let isSupplier = true;
                if (!validatenull(this.userInfo.dimensions) && !validatenull(this.userInfo.dimensions.UserRole)) {
                    let role = this.userInfo.dimensions.UserRole;
                    role.forEach((item) => {
                        if (item === "Buyer") {
                            isSupplier = false;
                        }
                    });
                }
                if (!isSupplier) {// buyer
                    this.customerId = this.userInfo.companyId;
                }
                this.formSelect.startDate = this.queryParam.startDate;
                this.formSelect.endDate = this.queryParam.endDate;
                this.loadTrfStatData(this.formSelect);
                this.loadAllEchart(this.formSelect);
            },

            loadTrfStatData(params) {
                if (!this.dateSpanValid(params)) return;
                this.loading = true;
                let paramNew = deepClone(params);
                if(!objectIsNull(paramNew.startDate)){
                  paramNew.startDate=this.tzToChina(moment(  paramNew.startDate).format("YYYY-MM-DD HH:mm:ss"))
                }
                if(!objectIsNull(paramNew.endDate)){
                  paramNew.endDate=this.tzToChina(moment( paramNew.endDate).format("YYYY-MM-DD HH:mm:ss"))
                }
                debugger;
                paramNew.productLineCode=this.userInfo.productLineCode
                trfStatCount(paramNew).then((res) => {
                    this.loading = false;
                    let data = res.data.data
                    if (data) {
                        this.orderSum.trfStatusCount = data;
                        this.orderSum.startDate = params.startDate;
                        this.orderSum.endDate = params.endDate;
                    }
                });
            },

            loadAllEchart(params) {
                if (!this.dateSpanValid(params)) return;
                this.loading = true;
                let paramNew = deepClone(params);
                if(!objectIsNull(paramNew.startDate)){
                  paramNew.startDate=this.tzToChina(moment(  paramNew.startDate).format("YYYY-MM-DD HH:mm:ss"))
                }
                if(!objectIsNull(paramNew.endDate)){
                  paramNew.endDate=this.tzToChina(moment( paramNew.endDate).format("YYYY-MM-DD HH:mm:ss"))
                }
                paramNew.productLineCode=this.userInfo.productLineCode;
                Promise.all([reportStatCount(paramNew), reportStatCountByMonth(paramNew)]).then(res => {
                    this.loading = false;
                    console.log("all请求", res);
                    this.orderSum.reportStatusCount = res[0].data.data;
                    this.orderSum.echartData = res[1].data.data;
                    this.orderSum.startDate = params.startDate
                    this.orderSum.endDate = params.endDate
                    this.initEcharts();
                });
            },

            initEcharts() {
                var myChart = echarts.init(document.getElementById("main"));
                this.$once("hook:beforeDestroy", function () {
                    myChart.clear();
                });
                var echartData = _.result(this.orderSum, "echartData", []);
                var endDateY = _.result(this.orderSum, "endDate", null);
                var startDateY = _.result(this.orderSum, "startDate", null);

                var monthData = [];
                for(let i = 0; i <= 12; i++) {
                    monthData.push(moment(startDateY).add(i, "M").format("YYYY/MM"));
                }
                debugger
                let passCountArray = monthData.map((item) => {
                    let filterArray = _.filter(echartData, {month: item});
                    return _.result(_.findLast(filterArray, (find) => find.overallResult === "Pass"), "countVal", 0);
                });
                // console.log('passCountArray::', passCountArray);
                let failCountArray = monthData.map((item) => {
                    let filterArray = _.filter(echartData, {month: item});
                    return _.result(_.findLast(filterArray, (find) => find.overallResult === "Fail"), "countVal", 0);
                });
                // console.log('failCountArray::', failCountArray);
                let otherCountArray = monthData.map((item) => {
                    let filterArray = _.filter(echartData, {month: item});
                    return _.result(_.findLast(filterArray, (find) => find.overallResult === "See Result"), "countVal", 0);
                });
                // console.log('otherCountArray::', otherCountArray);
                // 指定图表的配置项和数据
                this.echartOption = {
                    title: {
                        // text: i18n.tc("dataAnalytics.overall"),
                        // subtext: i18n.tc("dataAnalytics.noOfJob"),
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'line',
                            lineStyle: {
                                color: 'rgba(113, 113, 113, 1)',
                            },
                        },
                        backgroundColor: 'rgba(255, 255, 255, 1)',
                        borderColor: 'transparent',
                        formatter: function (params) {
                            let returnData = '<div style="padding: 5px 10px; box-shadow: 0px 8px 40px 0px rgba(0, 0, 0, 0.06);">'
                            // returnData += '<span style="font-family: MicrosoftYaHei; font-size: 14px; color: rgba(210, 221, 249, 1);">' + params[0].axisValue + '</span><br/>'
                            for (let i = 0; i < params.length; i++) {
                                returnData += '<div style="margin-bottom: 4px">'
                                returnData += '<span style="display:inline-block; width:12px; height:3px; margin-right: 4px; position: relative; top:-4px; background:' + color[i] + '"></span>'
                                returnData += '<span style="font-size: 12px; color: #656565; display: inline-block; width: 80px;">' + params[i].seriesName + '</span><span style="font-family: UniversLT; font-size: 16px; color: #1B1B1B">' + params[i].value + '</span><br/>'
                                returnData += '</div>'
                            }
                            returnData += '</div>'
                            return returnData
                        }
                    },
                    color,
                    legend: {
                        icon: 'roundRect',
                        itemWidth: 24,
                        itemHeight: 3,
                        itemGap: 15,
                        top: 22,
                        textStyle: {
                            fontFamily: 'MicrosoftYaHei',
                            fontSize: 14,
                            // color: 'rgba(255, 255, 255, 1)'
                        },
                        data: [
                            i18n.tc("dataAnalytics.pass"),
                            i18n.tc("dataAnalytics.fail"),
                            i18n.tc("dataAnalytics.seeResult"),
                        ]
                    },
                    grid: {
                        top: '22%',
                        left: '6%',
                        right: '3%',
                        bottom: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        axisTick: {
                            show: false // 不显示坐标轴刻度线
                        },
                        axisLine: {
                            show: false, // 不显示坐标轴线
                        },
                        splitLine: {
                            show: false // 不显示网格线
                        },
                        offset: 10,
                        data: monthData,
                    },
                    yAxis: {
                        minInterval:1,
                        name: `(${i18n.tc("dataAnalytics.noOfJob")})`,
                        type: 'value',
                        axisTick: {
                            show: false // 不显示坐标轴刻度线
                        },
                        axisLine: {
                            show: false, // 不显示坐标轴线
                        },
                        splitLine: {
                            show: false // 不显示网格线
                        },
                        offset: 25,
                    },
                    series: [
                        {
                            name: i18n.tc("dataAnalytics.pass"),
                            type: "line",
                            data: passCountArray,
                            areaStyle: {
                                //折线图颜色半透明
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(32, 180, 38, 0.1)' // 0% 处的颜色
                                    }, {
                                        offset: 1, color: 'rgba(32, 180, 38, 0)' // 100% 处的颜色
                                    }],
                                    global: false // 缺省为 false
                                }
                            },
                        },
                        {
                            name: i18n.tc("dataAnalytics.fail"),
                            type: "line",
                            data: failCountArray,
                            areaStyle: {
                                //折线图颜色半透明
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(215, 41, 38, 0.1)' // 0% 处的颜色
                                    }, {
                                        offset: 1, color: 'rgba(215, 41, 38, 0)' // 100% 处的颜色
                                    }],
                                    global: false // 缺省为 false
                                }
                            }
                        },
                        {
                            name: i18n.tc("dataAnalytics.seeResult"),
                            type: "line",
                            data: otherCountArray,
                            areaStyle: {
                                //折线图颜色半透明
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(147,147, 153, 0.1)' // 0% 处的颜色
                                    }, {
                                        offset: 1, color: 'rgba(147,147, 153, 0)' // 100% 处的颜色
                                    }],
                                    global: false // 缺省为 false
                                }
                            }
                        },
                    ],
                };
                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(this.echartOption);
                window.addEventListener("resize", () => {
                    myChart.resize();
                });
            },
            // param ： echarts的事件处理函数自带的参数，包含了本次触发对象的所有相关参数
            clickFunc(param) {
                console.log(param.data); // 当前点击对象的name
            },

            dateSpanValid: function (params) {
                if (moment(params.endDate).diff(moment(params.startDate).add(1, 'year'), 'days') > 0) {
                    this.$message({
                        type: "error",
                        message: "Only support for one year.",
                    });
                    return false;
                }
                return true;
            }
        },
        mounted() {
            this.$nextTick(() => {
                // 用户首次进入相关页面显示功能引导，但新手任务优先
                let that = this
                console.log('guide-menu', this.menu);
                console.log('guide-role', this.role.isSGS);
                let menuFlag = this.validateTrfMenu();
                let sgsRole = this.role.isSGS;
                console.log('是否sgs', menuFlag, sgsRole)

                let timer = setInterval(() => {
                    if (document.readyState === 'complete') {
                        window.clearInterval(timer)
                        let list = JSON.parse(localStorage.getItem('loginUserList'))
                        let user = list.find(item => item.userMgtId == this.userInfo.userMgtId)
                        let finish = user.fnList[0]['finish_dashboard']

                        if (this.role.isBuyer) { // buyer直接打开功能引导
                            if (!finish) this.$store.commit('SET_TASK_TYPE', 'dashboard')
                        } else {
                            let autoClose = JSON.parse(localStorage.getItem('AUTO_OFF'))
                            if ((!this.userInfo.guide || autoClose) && !finish && menuFlag && !sgsRole) {
                                this.$store.commit('SET_TASK_TYPE', 'dashboard')
                            }
                        }
                    }
                }, 300)
            })
        }
    };
</script>
<style scoped lang="scss">
  @import './index.scss';

  .add-menu {
    li {
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      display: inline-block;
      padding-left: 10px;
      transition: all .2s;

      a {
        display: block;
      }

      &:hover {
        background: rgba(255, 102, 0, 0.1);
        color: #FF6600;
      }
    }
  }

  .add-trf-btn {
    width: 272px;
    text-align: start;

    img {
      vertical-align: text-bottom;
    }
  }
</style>
