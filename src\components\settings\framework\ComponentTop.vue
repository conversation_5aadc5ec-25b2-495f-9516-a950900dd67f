<template>
  <div class="component-top">
    <div class="search-top">
        <el-button :class="collapsed ? 'el-icon-s-fold icon-size' : 'el-icon-s-unfold icon-size'" type="text" size="medium" @click="doToggle()"/>
        <el-select class="selectBackground" clearable filterable :placeholder="$t('operation.pleaseSelect')" v-model="productLineCode">
            <el-option v-for="(productLine, index) in productLineData" :key="index"
                       :value="productLine.productLineCode"
                       :label="productLine.productLineName" />
        </el-select>
        <el-select class="selectBackground" clearable filterable :placeholder="$t('settings.selectPage')" v-model="pageCode" disabled="true">
            <el-option v-for="(page, index) in $t('settings.pages')" :key="index"
                       :value="page.pageCode"
                       :label="page.pageName" />
        </el-select>
        <el-button class="search-btn" type="primary" size="medium" @click="search">{{$t('operation.search')}}</el-button>
    </div>
    <div class="btn-top">
      <el-button type="text" size="medium" @click="lockToggle(!editEnabled);"
          :class="editEnabled ? 'el-icon-unlock icon-size' : 'el-icon-lock icon-size'"
          :title="editEnabled ? $t('settings.unLock') : $t('settings.lock')" />
      <el-button type="text" size="medium" @click="save()"
          class="el-icon-finished icon-size" :title="$t('operation.submit')" />
    </div>
  </div>
</template>

<script>
    import { getProductLine } from "@/api/common/index";
    import { getPageConfigDetail } from "@/api/system/settings/components/components";
    import { pageEnums } from '@/commons/enums/PageEnums';

    export default {
        //组件顶部操作栏
        name: "ComponentTop",
        data() {
            return {
                collapsed: true,
                productLineData: [],
                pageList: [],
                pageCode: '',
                productLineCode: '',
                editEnabled: false,
                currentPageConfig: {},
            }
        },
        mounted() {
            this.pageList = pageEnums;
            this.pageCode = this.pageList[0].pageCode;
            this.queryProductLineData();
        },
        methods: {
            doToggle() {
                this.$emit('closeMenu', this.collapsed);
                this.collapsed = !this.collapsed;
            },

            lockToggle(lock) {
              this.$emit('lock', lock);
              this.editEnabled = lock;
            },

            queryProductLineData() {
                getProductLine().then(res => {
                    this.productLineData = res.data.data;
                });
            },

            search() {
              if(!this.validate()) return;
              let params = {buCode: this.productLineCode, pageCode: this.pageCode};
              this.$emit('showLoading', null)
              debugger;
              getPageConfigDetail(params).then(res => {
                this.currentPageConfig = Object.assign(res.data.data, params);
                this.$emit('loadPageComponent', this.currentPageConfig);
                this.$emit('loadChoiceComponent', this.currentPageConfig);
                console.log(this.pageCode + "页面配置: ", res);
              }).finally(() => {
                this.$emit('hiddenLoading', null);
              });
            },

            save() {
              if(!this.validate()) return;
              let params = {buCode: this.productLineCode, pageCode: this.pageCode};
              this.currentPageConfig = Object.assign(this.currentPageConfig, params);
              this.$emit('save', this.currentPageConfig);
            },

            validate() {
              if(!this.productLineCode) {
                this.$message({ type: "error", message: this.$t('settings.error.noBuCode') });
                return false;
              }

              if(!this.pageCode) {
                this.$message({ type: "error", message: this.$t('settings.error.noPageCode') });
                return false;
              }
              return true;
            }
        }
    }
</script>

<style scoped lang="scss">
    .icon-size {
      font-size: 32px;
    }

    .component-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .btn-top {
      display: flex;
      align-items: center;
      margin-right: 20px;
    }

    .search-top {
      display: flex;
      align-items: center;
      .selectBackground {
        margin-left: 24px;
      }
      .search-btn {
        height: 36px;
        margin-left: 24px;
      }
    }



</style>
