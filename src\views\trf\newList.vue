<template>
    <basic-container>
        <div class="newList" id="newList" v-loading="downLoading">
            <el-row>
                <el-col :span="16">
                    <h1 class="top-title">
                        {{ $t("wel1.myTrf") }}
                        <!--<el-tooltip content="Back to old trf list">
                            <router-link style="font-size: 18px;color: #ff6600" :to="{ path: '/trf/list' }">
                                <i class="icon-all iconfanhui1" style="font-size: 20px !important;"></i>
                            </router-link>
                        </el-tooltip>-->
                    </h1>
                </el-col>
                <el-col :span="8">
                    <div class="trf-o-btn text-right">
                        <a
                            style="display: none"
                            id="batchDownloadA"
                            :href="batchDownloadHref"
                            download="Report List.zip"
                            target="_blank"
                        ></a>
                        <el-popover
                            v-if="
                                menu.length > 0 && menu[0].children.length > 0
                            "
                            width="260"
                            trigger="hover"
                            placement="bottom"
                        >
                            <ul
                                class="list-unstyled add-menu"
                                v-if="menu.length > 0"
                            >
                                <li
                                    v-for="item in menu[0].children[0]
                                        .children[0].children"
                                    :key="item.id"
                                >
                                    <el-tooltip
                                        class="item"
                                        effect="dark"
                                        :content="item.name"
                                        placement="top"
                                    >
                                        <router-link
                                            target="_blank"
                                            :to="{ path: item.path }"
                                        >
                                            {{
                                                item.name.length > 30
                                                    ? item.name.substr(0, 28) +
                                                      "..."
                                                    : item.name
                                            }}
                                        </router-link>
                                    </el-tooltip>
                                </li>
                            </ul>
                            <el-button
                                class="line-btn add-trf-btn"
                                id="add-trf-btn"
                                slot="reference"
                            >
                                <img
                                    style="vertical-align: text-bottom"
                                    src="/img/icon/addTrf.png"
                                />
                                {{ $t("trf.createtrf") }}
                                <i
                                    class="el-icon-arrow-down pull-right"
                                    style="padding-top: 6px; font-weight: bold"
                                ></i>
                            </el-button>
                        </el-popover>
                    </div>
                </el-col>
            </el-row>
            <div class="wrap">
                <es-table
                    :option="option"
                    :page-query-param="queryModel"
                    ref="esTable"
                    v-if="loadEsTable"
                >
                    <template
                        slot="trf_header_trfStatusName"
                        slot-scope="{ row }"
                    >
                        <AFLTRFStatus
                            :key="row.trf_trfId"
                            v-if="
                                row.trf_header_productLineCode == 'AFL' &&
                                row.trf_header_trfType == '30'
                            "
                            :status="row.trf_header_trfStatus - 0"
                        />
                        <TRFStatus
                            v-else
                            :key="row.trf_trfId"
                            :status="row.trf_header_trfStatus - 0"
                            :reason="row.trf_header_pendingReason"
                        />
                    </template>
                    <template slot="trf_trfNo" slot-scope="{ row }">
                        <a
                            @click="trfDetailClick(row)"
                            style="
                                color: #ff6600;
                                font-size: 14px;
                                cursor: pointer;
                            "
                        >
                            {{
                                row.trf_header_customerReferenceNo ||
                                row.trf_trfNo
                            }}
                        </a>
                    </template>
                    <template slot="report_reportNo" slot-scope="{ row }">
                        <el-tooltip
                            placement="top"
                            :content="row.report_reportNo"
                        >
                            <div class="sgs_smart_table_column_data">
                                <i
                                    class="el-icon-document"
                                    v-if="row.report_id_reportId"
                                    @click="
                                        downloadReportFile(
                                            row.report_id_reportId,
                                        )
                                    "
                                    style="
                                        color: #ff6600;
                                        font-size: 14px;
                                        cursor: pointer;
                                    "
                                ></i>
                                {{ row.report_reportNo }}
                            </div>
                        </el-tooltip>
                    </template>
                    <template slot="action" slot-scope="{ row }">
                        <el-tooltip
                            :content="$t('trfList.copyTrf')"
                            placement="top"
                        >
                            <i
                                class="el-icon-copy-document menu-icon"
                                v-if="
                                    permissionList.copyTrfBtn &&
                                    userInfo.userMgtId ==
                                        row.trf_header_createUserId &&
                                    row.trf_header_trfType != '20' &&
                                    row.trf_header_trfSourceType != 3
                                "
                                type="primary"
                                @click="copyTRF(row)"
                            ></i>
                        </el-tooltip>
                        <el-tooltip
                            :content="$t('operation.modify')"
                            placement="top"
                        >
                            <el-button
                                type="text"
                                size="medium"
                                v-if="
                                    permissionList.trfEditBtn &&
                                    userInfo.userMgtId ==
                                        row.trf_header_createUserId &&
                                    row.trf_header_trfSourceType != 3 &&
                                    row.trf_header_trfStatus - 0 <= 2
                                "
                                icon="el-icon-edit menu-icon"
                                @click="editTrf(row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip
                            :content="$t('trfList.viewEfilling')"
                            placement="top"
                        >
                            <i
                                class="el-icon-view menu-icon"
                                v-if="row.efilingIds && row.efilingIds.length"
                                @click="viewEfilling(row)"
                            ></i>
                        </el-tooltip>
                        <el-tooltip
                            :content="$t('trfList.createEfilling')"
                            placement="top"
                        >
                            <i
                                class="el-icon-files menu-icon"
                                v-if="!row.efilingIds || !row.efilingIds.length"
                                @click="handleEfilling(row)"
                            ></i>
                        </el-tooltip>
                    </template>
                    <template #operate-block-right>
                        <el-popover
                            v-if="permissionList.trfDownLoadBtn"
                            width="220"
                            trigger="hover"
                            placement="bottom"
                        >
                            <ul class="list-unstyled export-file">
                                <li>
                                    <div
                                        style="cursor: pointer"
                                        v-if="permissionList.trfDownLoadBtn"
                                        :loading="downLoading"
                                        @click="exportExcelClick"
                                    >
                                        {{ $t("operation.exportTRF") }}
                                    </div>
                                </li>
                                <li>
                                    <div
                                        style="cursor: pointer"
                                        v-if="permissionList.trfDownLoadBtn"
                                        :loading="downLoading"
                                        @click="exportReportExcelClick"
                                    >
                                        {{ $t("operation.exportReportFiles") }}
                                    </div>
                                </li>
                            </ul>
                            <el-button
                                size="medium"
                                icon="el-icon-download"
                                class="create-btn"
                                slot="reference"
                                :loading="downLoading"
                            ></el-button>
                        </el-popover>
                    </template>
                </es-table>
            </div>
            <el-table
                :data="excelData"
                stripe
                style="width: 0; height: 0"
                type="hidden"
                id="exportTable"
            >
                <el-table-column
                    v-for="(column, index) in exportCol.filter(
                        (col) => !col.hide,
                    )"
                    :prop="column.prop"
                    :label="column.label"
                ></el-table-column>
            </el-table>
            <el-dialog
                title="eFiling"
                :visible.sync="eFillingDialogVisible"
                class="efiling-dialog"
                width="55%"
                append-to-body
                v-if="eFillingDialogVisible"
            >
                <eFillingForm
                    ref="eFilling"
                    :formModel="formModel"
                    :productInfoList="productInfoList"
                ></eFillingForm>
                <template slot="footer">
                    <el-button type="primary" @click="handleSave(1)">
                        {{ $t("crud.saveBtn") }}
                    </el-button>
                    <el-button type="success" @click="handleSave(2)">
                        {{ $t("work.cpscTrfInfo.column.submit") }}
                    </el-button>
                    <el-button @click="eFillingDialogVisible = false">
                        {{ $t("crud.cancelBtn") }}
                    </el-button>
                </template>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
import {
    add,
    doMapping,
    listForSelect,
    tradeSelect,
} from "@/api/cpscTrfInfo/cpscTrfInfo"
import esTable from "../../components/es-table"
import {
    exportTrfReportFile,
    batchDownloadFile,
    downLoadFile,
    queryTrfDetail,
    submitRelation,
    getReportFileList,
} from "@/api/trf/trf"
import { mapGetters } from "vuex"
import XLSX from "xlsx"
import FileSaver from "file-saver"
export default {
    name: "",
    data() {
        return {
            eFillingDialogVisible: false,
            productInfoList: [],
            downLoading: false,
            batchDownloadHref: "",
            loadEsTable: false,
            excelData: [],
            exportCol: [],
            statusMap: {
                1: "Drafting",
                2: "Submitted",
                3: "Application Accepted",
                4: "Testing",
                5: "Report Issued",
                6: "Cancel",
                11: "Pending",
                12: "Pending",
                13: "Quotation to be confirmed",
                14: "Quotation confirmed",
            },
            queryModel: {
                //查询列表对象
                general: {
                    startDate: "",
                    endDate: "",
                    filterDate: [],
                    queryValue: "",
                },
                dynamicForm: {},
            },
            option: {
                action: true,
                size: "small",
                actionWidth: 170,
                filterable: true,
                filterId: "",
                lan: "",
                customerNumber: "",
                customerGroupCode: "",
                filterEvent: 0, //0代表非跳转 和 地址栏没有查询数据，1代表跳转或者地址栏有查询条件
            },
        }
    },
    methods: {
        getNewTrfNo() {
            const now = new Date()
            const year = now.getFullYear()
            const month = (now.getMonth() + 1).toString().padStart(2, "0") // 月份是从0开始的，所以+1
            const day = now.getDate().toString().padStart(2, "0")
            const hours = now.getHours().toString().padStart(2, "0")
            const minutes = now.getMinutes().toString().padStart(2, "0")
            const seconds = now.getSeconds().toString().padStart(2, "0")
            const endStr = Math.floor(Math.random() * 900) + 100

            // 连接成纯数字字符串
            const formattedTime = `${year}${month}${day}${hours}${minutes}${seconds}${endStr}`
            //const formattedTime = `${minutes}${seconds}`;
            return "eFiling" + formattedTime
        },
        checkProductInfo(productInfoList) {
            for (let productInfo of productInfoList) {
                if (
                    !productInfo ||
                    !productInfo.formList.length ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    )
                ) {
                    this.$message.error(
                        this.$t("work.cpscCustomerTrade.tip.primary"),
                    )
                    return {
                        flag: false,
                        msg: this.$t("work.cpscCustomerTrade.tip.primary"),
                    }
                }
                if (
                    !productInfo ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    ).productIdType ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    ).productId
                ) {
                    this.$message.error(
                        this.$t("work.cpscCustomerTrade.tip.productInfo"),
                    )
                    return {
                        flag: false,
                        msg: this.$t("work.cpscCustomerTrade.tip.productInfo"),
                    }
                }
                const { hasDuplicateIdType, hasDuplicateId } =
                    this.checkDuplicateProductInfo(productInfo.formList)
                if (hasDuplicateIdType)
                    return { flag: false, msg: "ProductIdType Repeat" }
                if (hasDuplicateId)
                    return { flag: false, msg: "ProductId Repeat" }
            }
            return { flag: true }
        },
        checkDuplicateProductInfo(data) {
            // 创建两个对象来分别存储 productIdType 和 productId 的计数
            const productIdTypeCount = {}
            const productIdCount = {}

            // 遍历数组中的每个实体
            for (const item of data) {
                const { productIdType, productId } = item

                // 检查 productIdType 是否重复
                productIdTypeCount[productIdType] =
                    (productIdTypeCount[productIdType] || 0) + 1
                if (productIdTypeCount[productIdType] > 1) {
                    return { hasDuplicateIdType: true, hasDuplicateId: false }
                }

                // 检查 productId 是否重复
                productIdCount[productId] = (productIdCount[productId] || 0) + 1
                if (productIdCount[productId] > 1) {
                    return { hasDuplicateIdType: false, hasDuplicateId: true }
                }
            }

            // 没有发现重复
            return { hasDuplicateIdType: false, hasDuplicateId: false }
        },
        async handleSave(trfStatus) {
            const fun = (trfStatus) => {
                let productInfoList = JSON.parse(
                    JSON.stringify(this.$refs.eFilling.productInfoList),
                )

                const { flag, msg } = this.checkProductInfo(productInfoList)
                let efilingFormData = ""
                if (flag) {
                    let formModel = this.$refs.eFilling.newFormModel
                    // if(!formModel.referenceNo){
                    //     formModel.referenceNo= this.getNewTrfNo()
                    // }
                    console.log("formModel", formModel)
                    efilingFormData = JSON.stringify({
                        formModel,
                        productInfoList,
                    })
                    //  trfObj.efilingList=efilingList
                } else {
                    return
                }
                productInfoList.forEach((productInfo) => {
                    let form = JSON.parse(
                        JSON.stringify({
                            ...productInfo,
                            ...this.$refs.eFilling.newFormModel,
                        }),
                    )
                    form.dataSource = 2
                    form.trfInfoStastus = trfStatus

                    //form.referenceNo = this.getNewTrfNo()
                    //获取 primay key
                    if (form.formList && form.formList.length) {
                        const primary = form.formList.find(
                            (item) => item.primaryId === "yes",
                        )
                        form.productId = primary.productId
                        form.productIdType = primary.productIdType
                    }
                    if (form.productModelName) {
                        form.productName = form.productModelName
                    }

                    form.productJson = JSON.stringify(productInfo)
                    // 点击save 和 submit时 新增数据，所以需要将 form中的id属性设置为null
                    add(form).then(
                        (res) => {
                            this.$message({
                                type: "success",
                                message: "Success!",
                            })
                        },
                        (error) => {
                            window.console.log(error)
                        },
                    )
                })
                submitRelation({
                    trfId: this.$refs.eFilling.newFormModel.trfId,
                    efilingFormData,
                }).then(async (res) => {
                    this.eFillingDialogVisible = false
                    await this.initOrderList()
                })
            }

            if (trfStatus == 1) {
                fun(trfStatus)
            } else {
                let formValidResult = await this.$refs.eFilling.formValidate()
                if (formValidResult) {
                    fun(trfStatus)
                }
            }
        },
        viewEfilling(row) {
            this.$router.push("/cpscTrfInfo/cpscTrfInfo?trfNo=" + row.trfNo)
        },
        async handleEfilling(row) {
            console.log(row)
            this.productInfoList = [{ referenceNo: this.getNewTrfNo() }]
            this.formModel = {
                trfStatus: 0,
                attachments: [],
            }
            this.eFillingDialogVisible = true
            const { data } = await queryTrfDetail({
                trfNo: row.trf_trfNo,
                trfId: row.trf_trfId,
                signature: row.signature,
            })
            console.log(data)
            let buyerName =
                data.data.trfCustomer.buyerCustomerNameEn ||
                data.data.trfCustomer.buyerCustomerGroupName
            const res = await listForSelect(buyerName)
            this.$refs.eFilling.getBuyerCustomerId(buyerName)
            const buyerCustomerList = res.data.data
            const buyer = buyerCustomerList.find(
                (item) => item.bossName == buyerName,
            )

            let type = this.language == "zh-CN" ? "CN" : "EN" //EN
            const formData = JSON.parse(data.data.dffFormData)[type]

            if (!formData || !formData.factoryName) {
                let param = {}
                param.trfId = row.trf_trfId
                param.buyerCustomerId = buyer ? buyer.id : undefined
                param.basicLabAddress = data.data.trfLab.labAddress
                param.basicServiceType = data.data.serviceType
                param.basicLabId = data.data.trfLab.labCode
                param.trfReferenceNo = row.trf_trfNo
                this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =
                    data.data.trfAttachments
                this.$refs.eFilling.newFormModel = Object.assign(
                    this.formModel,
                    param,
                )
                const mapping = await doMapping({
                    productLineCode: data.data.productLineCode,
                    productCategory: "",
                    customerCode: data.data.trfCustomer.buyerCustomerGroupCode,
                    formData: JSON.stringify(formData),
                    formList: JSON.stringify(
                        JSON.parse(data.data.dffGridData)[type],
                    ),
                })
                this.productInfoList = this.productInfoList.map((item) => {
                    return { ...item, ...mapping.data.data }
                })
            } else {
                const resTrade = await tradeSelect(
                    "Manufacture",
                    buyer ? buyer.customerId : "",
                    formData ? formData.factoryName : "",
                )
                const manufactureCustomerIds = resTrade.data.data
                const manufacture = manufactureCustomerIds.find((item) =>
                    item.tradeName == formData ? formData.factoryName : "",
                )
                const mapping = await doMapping({
                    productLineCode: data.data.productLineCode,
                    productCategory: "",
                    customerCode: data.data.trfCustomer.buyerCustomerGroupCode,
                    formData: JSON.stringify(formData),
                    formList: JSON.stringify(
                        JSON.parse(data.data.dffGridData)[type],
                    ),
                })
                let param = {}
                param.buyerCustomerId = buyer ? buyer.id : undefined
                param.trfId = row.trf_trfId
                param.basicLabAddress = data.data.trfLab.labAddress
                // param.basicLabContactName=data.data.trfLabContact.contactName
                param.basicServiceType = data.data.serviceType
                param.basicLabId = data.data.trfLab.labCode
                param.trfReferenceNo = row.trf_trfNo
                console.log(param)
                this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =
                    data.data.trfAttachments
                this.$refs.eFilling.newFormModel = Object.assign(
                    this.formModel,
                    param,
                )
                console.log("newFormModel", this.$refs.eFilling.newFormModel)
                manufacture &&
                    (await this.$refs.eFilling.setPOC(
                        "Manufacture",
                        buyer ? buyer.customerId : "",
                        formData ? formData.factoryName : "",
                    ))
                manufacture &&
                    this.$refs.eFilling.handleTradeChange(
                        manufacture.id,
                        manufacture,
                    )
                let manufactureSub = {}
                if (manufacture) {
                    manufactureSub.manufactureContactId =
                        manufacture.cpscCustomerTradeContactVO.id
                    manufactureSub.manufactureContactName =
                        manufacture.cpscCustomerTradeContactVO.contactName
                    manufactureSub.manufactureContactTelephone =
                        manufacture.cpscCustomerTradeContactVO.telphone
                    manufactureSub.manufactureCustomerAddress =
                        manufacture.tradeAddress
                    manufactureSub.manufactureCustomerAddressLocal =
                        manufacture.tradeAddress2
                    manufactureSub.manufactureCustomerCityId =
                        manufacture.tradeCity
                    manufactureSub.manufactureCustomerCityName =
                        manufacture.tradeCity
                    manufactureSub.manufactureCustomerCountryId =
                        manufacture.tradeCountryId
                    manufactureSub.manufactureCustomerCountryName =
                        manufacture.tradeCountryName
                    manufactureSub.manufactureCustomerId = manufacture.id
                    manufactureSub.manufactureCustomerName =
                        manufacture.tradeName
                    manufactureSub.manufactureCustomerNameLocal =
                        manufacture.tradeName
                    manufactureSub.manufactureContactEmaill =
                        manufacture.cpscCustomerTradeContactVitem
                }
                this.productInfoList = this.productInfoList.map((item) => {
                    return { ...item, ...mapping.data.data, ...manufactureSub }
                })
                buyer && this.$refs.eFilling.handleBuyerChange(buyer.id)
            }
        },
        downloadReportFile(reportId) {
            if (!reportId) {
                return
            }
            getReportFileList({ reportIds: [reportId] })
                .then((res) => {
                    if (
                        res.status == 200 &&
                        res.data &&
                        res.data.data &&
                        res.data.data.length > 0
                    ) {
                        res.data.data.forEach((da) => {
                            let { cloudId } = da
                            downLoadFile(cloudId).then(
                                (res) => {
                                    var pdfUrl = res.data.data
                                    window.open(pdfUrl, "_blank")
                                },
                                (error) => {
                                    this.$message.error(this.$t("api.error"))
                                },
                            )
                        })
                    } else {
                        this.$message.error(this.$t("api.error"))
                    }
                })
                .catch((err) => {
                    this.$message.error(this.$t("api.error"))
                })
        },
        trfDetailClick(row) {
            let hash = new Date().getTime() + ""
            if (row.trf_header_trfType == 30) {
                window.open(
                    "/#/afl/trf/trfDetail?id=" +
                        row.trf_trfId +
                        "&title=" +
                        row.trf_trfNo +
                        "&trfNo=" +
                        row.trf_trfNo +
                        "&hash=" +
                        hash +
                        "&actionType=detail" +
                        "&signature=" +
                        row.signature,
                    "_blank",
                )
            } else {
                window.open(
                    "/#/trf/trfDetail?id=" +
                        row.trf_trfId +
                        "&title=" +
                        row.trf_trfNo +
                        "&trfNo=" +
                        row.trf_trfNo +
                        "&hash=" +
                        hash +
                        "&actionType=detail" +
                        "&signature=" +
                        row.signature,
                    "_blank",
                )
            }
        },
        copyTRF(row) {
            this.actionTRF(row, "Copy TRF", "copy")
        },

        editTrf(row) {
            this.actionTRF(row, row.trf_trfNo, "edit")
        },
        actionTRF(row, title, action) {
            let url =
                row.trf_header_trfType == 30
                    ? "/afl/trf/trfDetail"
                    : "/trf/trfDetail"
            let flag = row.trf_header_trfType == 30 ? 3 : 1
            let params = {
                id: row.trf_trfId,
                title: title,
                trfNo: row.trf_trfNo,
                signature: row.signature,
                actionType: action,
                flag: flag,
            }
            let routePath = this.$router.resolve({
                path: url,
                query: params,
            })
            window.open(routePath.href, "_blank")
        },
        exportReportExcelClick() {
            this.batchDownloadHref = ""
            let tableObj = this.$refs.esTable
            let { page, tableData } = tableObj
            let { total } = page
            if (total <= 0) {
                this.$notify.error(this.$t("operation.exportReportTips"))
                return
            }
            this.handlerExportReportExcelClick()
        },
        chunkArray(array, size = 20) {
            const result = []
            if (!array || array.length == 0) {
                return result
            }
            for (let i = 0; i < array.length; i += size) {
                result.push(array.slice(i, i + size))
            }
            return result
        },
        sleep(ms = 500) {
            return new Promise((resolve) => {
                setTimeout(resolve, ms)
            })
        },
        handlerExportReportExcelClick() {
            let tableObj = this.$refs.esTable
            let { tableData } = tableObj
            let data = tableData
            let trfIds = data.map((da) => da.trf_trfId)
            this.downLoading = true
            exportTrfReportFile({ trfIds })
                .then(async (res) => {
                    try {
                        let resultData = res.data.data
                        let datas = this.chunkArray(resultData, 20)
                        for (const data of datas) {
                            await this.sleep(1000)
                            //生成一份说明文件：
                            const csvData = [["TRF No", "File Name"]]
                            ;(data || []).forEach((da) => {
                                let { trfNo, fileName } = da
                                let obj = []
                                obj.push(trfNo)
                                obj.push(fileName)
                                csvData.push(obj)
                            })
                            //report文件
                            let cloudIDs = (data || []).map((c) => c.cloudId)
                            let reportListParam = {
                                systemId: 2,
                                cloudIDs,
                            }
                            this.downloadZip(reportListParam, csvData)
                        }
                    } catch (e) {
                        this.downLoading = false
                    }
                })
                .catch((err) => {
                    this.downLoading = false
                })
        },
        downloadZip(reportListParam, data) {
            // 转换数据为CSV格式
            //下载批量文件
            const csvContent =
                "\ufeff" + data.map((row) => row.join(",")).join("\n")
            // 创建Blob对象
            const blob = new Blob([csvContent], {
                type: "text/csv;charset=utf-8;",
            })
            FileSaver.saveAs(blob, "Report list.csv")
            this.downLoading = false
            let { systemId, cloudIDs } = reportListParam
            let cs = []
            cloudIDs.forEach((c) => {
                c = encodeURIComponent(c)
                cs.push(`cloudIDs=${c}`)
            })
            let cloudIdsParam = cs.join("&")
            let param = `fileName=Report List.zip&systemId=${systemId}&${cloudIdsParam}`
            let downloadUrl =
                "/api/sgsapi/FrameWorkApi/file/batchDownloadByCloudIDForGet?"
            downloadUrl = `${downloadUrl}${param}`
            //this.batchDownloadHref = downloadUrl;
            let dom = document.createElement("a")
            dom.href = downloadUrl
            dom.download = "Report List.zip"
            dom.style.display = "none"
            document.body.appendChild(dom)
            dom.click()
            dom.parentNode.removeChild(dom)
            window.URL.revokeObjectURL(downloadUrl)
            this.downLoading = false
        },
        async exportExcelClick() {
            let tableObj = this.$refs.esTable
            let { page } = tableObj
            let { total } = page
            if (total > 3000) {
                this.$confirm(
                    this.$t("trf.confirmExportTrfList"),
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        this.exportExcel()
                    })
                    .catch(() => {
                        /* this.btnSubmitLoading=false;
                         this.$message.error(this.$t('api.error'));*/
                    })
            } else {
                this.exportExcel()
            }
        },
        async exportExcel() {
            let { tableOption } = this.$refs.esTable
            let { column } = tableOption
            this.exportCol = column || []
            //渲染隐藏的es table
            this.downLoading = true
            let excelData = await this.$refs.esTable.loadExportData()
            if (!excelData || excelData.length == 0) {
                this.downLoading = false
                this.$notify.warning("There is no data can be export")
                return
            }
            this.excelData = excelData
            //数据填充之后，开始做导出
            this.$nextTick(() => {
                var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
                var wb = XLSX.utils.table_to_book(
                    document.querySelector("#exportTable"),
                    xlsxParam,
                )
                var wbout = XLSX.write(wb, {
                    bookType: "xlsx",
                    bookSST: true,
                    type: "array",
                })
                try {
                    FileSaver.saveAs(
                        new Blob([wbout], { type: "application/octet-stream" }),
                        "TestRequestList.xlsx",
                    )
                } catch (e) {
                    this.downLoading = false
                    console.log(e, wbout)
                }
                this.downLoading = false
                return wbout
            })
        },
        updateQueryParam(value) {
            let uri = window.location.href
            let key = "filterEvent"
            let re = new RegExp("([?&])" + key + "=.*?(&|$)", "i")
            let separator = uri.indexOf("?") !== -1 ? "&" : "?"
            let newUrl = uri
            if (uri.match(re)) {
                newUrl = uri.replace(re, "$1" + key + "=" + value + "$2")
            } else {
                newUrl = uri + separator + key + "=" + value
            }
            //console.log("2222222222",value)
            window.location.href = newUrl
        },
        initQueryModelByRoute() {
            this.loadEsTable = false
            let {
                startDate,
                endDate,
                trfStatus,
                reportResult,
                queryValue,
                queryModel,
                modifyQueryDate,
                filterId,
            } = this.$route.query
            //console.log("NewList : initQueryModelByRoute 触发了initQueryModel")
            if (queryModel) {
                this.option.filterEvent = 1
                this.option.filterable = modifyQueryDate == "true"
                this.option.filterId = filterId
                try {
                    this.queryModel = JSON.parse(queryModel)
                    this.$nextTick(() => {
                        this.loadEsTable = true
                    })
                    return
                } catch (e) {
                    console.log("url param error")
                }
            }
            if (queryValue && queryValue.length > 0) {
                this.queryModel.general.queryValue = queryValue
                this.option.filterEvent = 1
            }

            if (startDate && endDate) {
                try {
                    //避免date不符合搜索引擎格式，严格限制
                    let st = new Date(startDate.replace(/-/g, "/"))
                    let end = new Date(endDate.replace(/-/g, "/"))
                    if (st != "Invalid Date" && end != "Invalid Date") {
                        let stYear = st.getFullYear()
                        let stMonth = st.getMonth() + 1
                        stMonth = stMonth < 10 ? "0" + stMonth : stMonth
                        let stDate = st.getDate()
                        stDate = stDate < 10 ? "0" + stDate : stDate
                        let edYear = end.getFullYear()
                        let edMonth = end.getMonth() + 1
                        edMonth = edMonth < 10 ? "0" + edMonth : edMonth
                        let edDate = end.getDate()
                        edDate = edDate < 10 ? "0" + edDate : edDate

                        this.queryModel.general.startDate = `${stYear}-${stMonth}-${stDate}`
                        this.queryModel.general.endDate = `${edYear}-${edMonth}-${edDate}`
                        this.queryModel.general.filterDate = [
                            `${stYear}-${stMonth}-${stDate}`,
                            `${edYear}-${edMonth}-${edDate}`,
                        ]
                        this.option.filterEvent = 1
                    }
                } catch (e) {
                    console.log("trans date error", e)
                }
            }
            if (trfStatus - 0 > -1) {
                let val = this.statusMap[trfStatus - 0]
                val = val ? [val] : null
                this.queryModel.dynamicForm["trf_header_trfStatusName"] = {
                    condition: "in",
                    conditionValue: val,
                }
                this.option.filterEvent = 1
            }
            if (reportResult && reportResult.length > 0) {
                let conclusionConditionValue =
                    reportResult == "all" ? null : [reportResult]
                conclusionConditionValue =
                    reportResult == "See Result"
                        ? ["Pass", "Fail"]
                        : conclusionConditionValue
                let conclusionCondition =
                    reportResult == "See Result" ? "notIn" : "in"
                this.queryModel.dynamicForm[
                    "report_conclusion_customerConclusion"
                ] = {
                    condition: conclusionCondition,
                    conditionValue: conclusionConditionValue,
                }
                this.option.filterEvent = 1
            }
            this.$nextTick(() => {
                this.loadEsTable = true
            })
        },
        handlePopState(event) {
            //console.log("New List: handlePopState 浏览器地址变动被触发")
            window.location.reload()
        },
    },
    mounted() {
        document.title = "TRF List"
        window.addEventListener("popstate", this.handlePopState)
    },
    created() {
        //console.log("New List 被加载了");
        this.option.customerNumber = this.userInfo.bossNo
        this.option.productLineCode = this.userInfo.productLineCode
        this.option.customerGroupCode = this.userInfo.customerGroupCode
        this.option.lan = this.language == "zh-CN" ? "cn" : "en"
        this.initQueryModelByRoute()
    },
    watch: {
        language: {
            handler(newV) {
                if (newV == "zh-CN") {
                    this.$refs.esTable.changeLan("cn")
                } else {
                    this.$refs.esTable.changeLan("en")
                }
                document.title = "TRF List"
            },
        },
    },
    computed: {
        ...mapGetters([
            "userInfo",
            "permission",
            "language",
            "menu",
            "dimensions",
        ]),
        permissionList() {
            return {
                trfAddBtn: this.vaildData(
                    this.permission["sgs:trfList:addTrf"],
                    false,
                ),
                trfDownLoadBtn: this.vaildData(
                    this.permission["sgs:trfList:downLoadTrf"],
                    false,
                ),
                trfPrintBtn: this.vaildData(
                    this.permission["sgs:trfList:printBtn"],
                    false,
                ),
                trfEditBtn: this.vaildData(
                    this.permission["sgs:trfList:editTrf"],
                    false,
                ),
                copyTrfBtn: this.vaildData(
                    this.permission["sgs:trfList:copyTrf"],
                    false,
                ),
            }
        },
        role() {
            return {
                isSGS:
                    this.haseRole("SGSUserRole", "SgsAdmin") ||
                    this.haseRole("SGSUserRole", "SgsLabUser"),
                isBuyer: this.haseRole("UserRole", "Buyer"),
            }
        },
        //监听接收到的消息
        socketTask() {
            return this.$store.getters.socketTask
        },
    },
    props: {},
    updated() {},
    beforeDestroy() {
        window.removeEventListener("popstate", this.handlePopState)
    },
    components: {
        TRFStatus: (resolve) =>
            require(["../../components/trf/TRFStatus"], resolve),
        AFLTRFStatus: (resolve) =>
            require(["../../components/trf/AFLTRFStatus"], resolve),
        esTable,
        eFillingForm: (resolve) =>
            require(["@/views/trf/form/eFillingForm.vue"], resolve),
    },
}
</script>

<style lang="scss">
.newList {
    .wrap {
        background: #fff;
        padding: 24px 32px;
    }
    .menu-icon {
        font-size: 20px;
        cursor: pointer;
        margin: 0px 10px;
        color: #ff6600;
    }
    .create-btn {
        margin-bottom: 18px;
    }
}
ul.list-unstyled {
    li {
        padding: 10px;
    }
    li:hover {
        color: #ff6600;
    }
}
</style>
