<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_common_navList" id="sgs_smart_common_navList">
            <ul class="nav-list" ref="nav" id="trf-left-nav">
                <template v-for="(item, index) in navList">
                    <li :class="{'is-sub': item.isSub}"
                        :style="{color: item.active ? '#f60' : '#000', borderColor: item.active ? '#f60' : '#D9D9D9'}"
                        :key="item.name"
                        class="nav-item"
                        @click="toTarget(index)">
                        <h5>
                            {{ $t(item.name) }}
                            <i 
                                :class="['el-icon-success', item.pass ? 'pass': 'nopass']"
                                v-if="item.isRequired"
                            ></i>
                        </h5>
                    </li>
                </template>
            </ul>
        </div>
    </basic-container>
</template>

<script>
    import {objectIsNull} from "@/util/validate";

    export default {
        name: "navList",
        data() {
            return {
                pageLoading: false,
                navList:[],
                element:{
                    nav:[],
                    content:[]
                }
            }
        },
        methods: {
            initPage() {
                this.initNavList();
            },
            initNavList(){
                const nav = document.getElementsByClassName('nav-item')
                const cont = document.getElementsByClassName('content-item')
                this.element.nav = nav
                this.element.content = cont
                //导航初始化样式（默认第一个）
                nav[0].classList.add('active')
                this.navList[0].active = true;
                window.addEventListener("scroll", this.toScroll)
            },
            toTarget(index){
                const { content, nav } = this.element;
                this.navList.forEach((na,ind)=>{
                    na.active = index==ind;
                })
                const scrollTop = document.documentElement.scrollTop|| document.body.scrollTop;
                switch (index) {
                    case 0:
                        document.documentElement.scrollTop = 0;
                        return;
                }
                document.documentElement.scrollTop =  ((document.documentElement.scrollTop===0?-100:document.documentElement.scrollTop)+ (content[index].getBoundingClientRect().top))-100;
            },
            toScroll() {
                let navEle = document.querySelector('.nav-list')
                if(objectIsNull(navEle)){
                    return;
                }
                navEle.style.width = navEle.clientWidth + 'px'
                var w;
                //获取滚动距离
                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                const { content, nav } = this.element
                this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                window.addEventListener("resize", () => {
                    this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                });
                // 侧边栏和评论栏固定
                if(scrollTop != undefined && scrollTop > 170) {
                    navEle.style.position = 'fixed'
                    navEle.style.top = '80px'
                } else {
                    navEle.style.position = 'initial'
                }
                // 侧边栏菜单添加当前高亮
                let len = content.length;
                let scrollIndex = 0;
                for (let i = 0;i < len; i++) {
                    //获取每块内容距离顶部距离
                    const offset = content[i].offsetTop;
                    //当划过第一块内容 改变左侧导航样式
                    if (scrollTop >= offset) {
                        scrollIndex = i;
                    }
                }
                for (let n = 0; n < len; n++) {
                    if(!nav[n]) {
                        return;
                    }
                    n == scrollIndex ? nav[n].classList.add('active') : nav[n].classList.remove('active')
                }

                if(scrollTop == 0) {
                    nav[0].classList.add('active')
                    nav[1].classList.remove('active')
                }
            },
        },
        mounted() {
            this.initPage();
        },
        created() {
            this.navList = this.navArray;
        },
        watch: {},
        computed: {},
        props: {
            navArray:{
                type:Array,
                default(){
                    //[{name,sub,active}]
                    return []
                }
            }
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";

    .sgs_smart_common_navList {
        font-family: 'Arial' !important;
        .nav-list {
            list-style: none;
            margin-top: $module-margin-vertical;
            padding-left: 0;
            li {
                cursor: pointer;
                border-left: 3px solid #D9D9D9;
                padding-left: 24px;
                font-size: 16px;
                font-weight: 400;
                color: #000000;
                &.is-sub {
                    padding-left: 40px;
                    font-size: 16px;
                    font-weight: 400;
                    color: #656565;
                    h5 {
                        font-weight: normal;
                    }
                }
                &.active {
                    color: #f60;
                    border-color: #f60;
                }
                h5 {
                    padding: 10px 0;
                    margin: 0;
                    .pass{
                        color: #f60;
                        margin-right: 10px;
                    }
                    .nopass{
                        color: #D9D9D9;
                        margin-right: 10px;
                    }
                }
            }
        }
    }
</style>