<template>
    <div id="language">
      <a @click="handleSetLanguage('zh-CN')" ref='zh_a'  style="cursor:pointer"><i class="icon-all iconzhongwen" :style="{color: isLogin?'':'white'}"></i></a>
      <a @click="handleSetLanguage('en-US')" ref='en_a' style="cursor:pointer"><i class="icon-all iconyingwen"  :style="{color: isLogin?'':'white'}"></i></a>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {updateUserLanguage, queryUserLanguage} from "@/api/customer/customer";
    import {validatenull} from "../../../util/validate";
    import { setPageTitle } from '@/util/util';

    export default {
        name: "top-lang",
        data() {
            return {
                data: {
                  isActive: false
                }
            };
        },
        created() {
            //从用户之前保存的数据中获取语言
            if(this.isLoadLanageByIp){
                 this.queryUserLanguage();
            }
        },
        mounted() {
        },
        computed: {
            ...mapGetters(["language", "tag", "menuMap"])
        },
        props: {
            isLogin:{
              type:Boolean,
              default:false
            },
            loadMenu: {
                type: Boolean,
                default: true
            },
            isLoadLanageByIp:{
                 type: Boolean,
                 default: true
            }
        },
        methods: {
            changeLanguage(language) {
              if(language=='zh-CN') {
                // this.$refs.zh_a.style.color="#f60";
                // this.$refs.en_a.style.color="#999999";
                this.$refs.zh_a.className = "lang-active";
                this.$refs.en_a.className = "";
              } else {
                this.$refs.zh_a.className = "";
                this.$refs.en_a.className = "lang-active";
              }
            },
            handleSetLanguage(lang) {
                this.changeLanguage(lang);
                this.$i18n.locale = lang;
                this.$store.commit("SET_LANGUAGE", lang);
                //将切换的语言保存至customer信息中
                this.updateUserLanguage(this.language);
                let tag = this.tag;
                let title = this.$router.$avueRouter.generateTitle(
                    tag.label,
                    (tag.meta || {}).i18n
                );
                //根据当前的标签也获取label的值动态设置浏览器标题
                this.$router.$avueRouter.setTitle(title);
                if (this.loadMenu) {
                    this.$store.dispatch("GetMenu", this.language).then(data => {
                        if (data.length === 0) return;
                        this.$router.$avueRouter.formatRoutes(data, true);
                        setPageTitle(this.$router, this.$store)
                    });
                }
            },
            updateUserLanguage(lang) {
                if(this.isLogin){
                    return;
                }
                updateUserLanguage(lang).then(res => {
                    console.log("切换语言保存结果-->" + res.data.data.msg);
                    console.log(res)

                });
            },
            //查询当前用户配置的语言信息
            queryUserLanguage() {
                queryUserLanguage().then(res => {
                    var language = res.data.data;
                    this.changeLanguage(language);
                    if (language == null || language == undefined || language == '') {
                        //判断页面请求是否带有language
                        if (!validatenull(this.$route.query.language)) {
                            language = this.$route.query.language;
                        } else {
                            language = 'en-US';
                        }
                        language = language.trim();
                        //用户未获取到Language的话
                        this.updateUserLanguage(language);
                        this.$i18n.locale = language;
                        this.$store.commit("SET_LANGUAGE", language);

                        let tag = this.tag;
                        let title = this.$router.$avueRouter.generateTitle(
                            tag.label,
                            (tag.meta || {}).i18n
                        );
                        //根据当前的标签也获取label的值动态设置浏览器标题
                        // this.$router.$avueRouter.setTitle(title);
                        if (this.loadMenu) {
                            this.$store.dispatch("GetMenu", this.language).then(data => {
                                if (data.length === 0) return;
                                this.$router.$avueRouter.formatRoutes(data, true);
                            });
                        }
                    } else {
                        //判断页面请求是否带有language
                        console.log(this.$route.query.language);
                        if (!validatenull(this.$route.query.language)) {
                            // if (this.$route.query.language === 'en-US'  this.$route.query.language === 'zh-CN') {
                            language = this.$route.query.language;
                            console.log(language);
                            // }
                        }
                        language = language.trim();
                        this.$i18n.locale = language;
                        this.$store.commit("SET_LANGUAGE", language);

                        let tag = this.tag;
                        let title = this.$router.$avueRouter.generateTitle(
                            tag.label,
                            (tag.meta || {}).i18n
                        );
                        //根据当前的标签也获取label的值动态设置浏览器标题
                        // this.$router.$avueRouter.setTitle(title);
                        if (this.loadMenu) {
                            this.$store.dispatch("GetMenu", this.language).then(data => {
                                if (data.length === 0) return;
                                this.$router.$avueRouter.formatRoutes(data, true);
                            });
                        }
                    }


                });
            }
        }
    };
</script>

<style lang="scss" scoped>

.lang-active {
  display: none;
  //background: #f60;
  //color: #fff !important;
 // border-radius: 3px;
}
.dis_block {
  display: block;
  }
.fr {
  float: right !important;
}
.c_title {
  color: #f60 !important;
}
/* a + a:before {
  content: '|';
  margin: 0 5px;
} */
</style>
