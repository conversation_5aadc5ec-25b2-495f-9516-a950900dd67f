<template>
  <div class="main" id="test-result">
    <div class="title" >
      <h3>{{ $t("testResult.title") }}</h3>
      <h3>
        <el-date-picker
            type="monthrange"
            align="right"
            unlink-panels
            range-separator="-"
            v-model="dateVal"
            value-format="yyyy-MM-dd"
            :start-placeholder="$t('datePicker.startTime')"
            :end-placeholder="$t('datePicker.endTime')"
            :picker-options="pickerOptions"
            @change="loadAllEChart(handleDateChange($event))">
        </el-date-picker>
      </h3>
    </div>
    <el-row>
      <el-col span="8">
        <el-row :gutter="0" class="text-center">
          <el-col span="8" v-for="item in reportStatusList" :key="item.name">
            <router-link tag="a"
                         :to="{ path: item.path, query: { startDate: encodeURIComponent(params.startDate), endDate: encodeURIComponent(params.endDate), hash: new Date().getTime() } }">
              <p class="tit"><span :class="item.class"></span>{{ $t(item.lang) }}</p>
              <h2>{{ reportStatusPie[item.value] }}</h2>
            </router-link>
          </el-col>
          <el-col span="24">
            <TestResultPieChart :orderSum="reportStatusPie"/>
          </el-col>
        </el-row>
      </el-col>
      <el-col span="16">
      <TestResultLineChart   :orderSum="reportStatusLine" :startDateY="params.startDate" :endDateY="params.endDate"  />
      </el-col>
    </el-row>
  </div>
</template>

<script>
    import {reportStatCount, reportStatCountByMonth} from "@/api/welIndex";
    import {dateTimeTool, dateFormatEnums, dateUnitEnums} from "@/util/dateTimeUtil";
    import {trfStatCount} from "@/api/welIndex";
    import {reportStatusEnums} from "@/commons/enums/ReportStatusEnums";
    import {roleEnums} from "@/commons/enums/RoleEnums";
    import {mapGetters} from "vuex";
    import {objectIsNull,validatenull} from "@/util/validate";
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
    import moment from "moment";
    import {deepClone} from '@/util/util'


    export default {
        name: "ReportStatusView",
        components: {
          TestResultPieChart: (resolve) => require(["@/components/chart/TestResultPieChart"], resolve),
          TestResultLineChart: (resolve) => require(["@/components/chart/TestResultLineChart"], resolve),
        },
        data() {
            return {
                dateVal: 'month',
                params: {
                  startDate: dateTimeTool.startDate,
                  endDate: dateTimeTool.endDate,
                },
                reportStatusList: reportStatusEnums.slice(0,3),
                roleList: roleEnums,
                reportStatusPie: {
                  passCount: 0,
                  failCount: 0,
                  otherCount: 0,
                  allCount: 0,
                },
                reportStatusLine:[]
            }
        },
        computed: {
          ...mapGetters(["userInfo", "permission"]),
          pickerOptions() {
            dateTimeTool.pickerOptions.shortcuts.forEach(item => {
              item.text = this.$t(item.code);
            });
            return dateTimeTool.pickerOptions;
          }
        },
        mounted() {
            this.dateVal = [this.params.startDate, this.params.endDate];
            this.loadAllEChart(this.handleDateChange(this.dateVal));
            console.log("加载组件 ===>", "ReportStatusView");
        },
      methods: {
        currentTz(value,format){
          if (!value) return ''
          if(!format){
            format='YYYY-MM-DD HH:mm:ss';
          }
          value = tzFormChina(value,format);
          value = value.toString();
          return value;
        },
        tzToChina(val){
          if (!val) return ''
          let value = tzToChina(val);
          return value;
        },
        handleDateChange(e) {
          if(e === null) return;
          this.params.startDate = e[0];
          this.params.endDate = e[1];
          return this.params;
        },
        loadAllEChart(val) {
          if (val === null || !dateTimeTool.dateSpanValid(val)) return;

          let params = deepClone(this.params)
          if(!objectIsNull(params.startDate)){
            params.startDate=this.tzToChina(moment(params.startDate).format("YYYY-MM-DD HH:mm:ss"))
          }
          if(!objectIsNull(params.endDate)){
            params.endDate=this.tzToChina(moment(params.endDate).format("YYYY-MM-DD HH:mm:ss"))
          }
          params.productLineCode=this.userInfo.productLineCode;
          Promise.all([reportStatCount(params), reportStatCountByMonth(params)]).then(res => {
            console.log("all请求", res);
            this.reportStatusPie = res[0].data.data;
            this.reportStatusLine = res[1].data.data;
          });
        }
      }
    }
</script>

<style scoped lang="scss">
  $green: #34A853;
  $red: #EA4336;
  $grey: #D9D9D9;
  $orange: #ff6600;

  .main {
    padding: 0 16px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    max-height: 450px;

    .title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      h3 {
        color: #1b1b1b;
        font-weight: bold;
        text-rendering: optimizeLegibility;
        text-transform: uppercase;
      }
    }

    .tit {
      font-size: 14px;
      font-weight: 400;
      color: #656565;
      line-height: 20px;
      span {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 6px;
      }
      .green {
        background-color: $green;
      }
      .red {
        background-color: $red;
      }
      .grey {
        background-color: $grey;
      }
    }

    h2 {
      font-size: 32px;
      font-family: UniversLT;
      color: #1B1B1B;
      line-height: 32px;
    }
  }

  .pie {
    display: flex;
    flex-direction: column;
  }

  @media screen and (max-width: 1024px) {
    .main {
      h2 {
        font-size: 14px;
        font-family: UniversLT;
        color: #1B1B1B;
        line-height: 24px;
      }
    }
  }
</style>
