<template>
    <div class="sgs-box">
        <div class="sgs-group">
            <h3>{{$t('trf.basicInfo')}}</h3>
        </div>
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item :label="$t('trf.isRetest')"  v-if="valiUsable('isRetestFlag')"  prop="isRetestFlag" :rules="{required:valiRequired('isRetestFlag'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-radio-group v-model="isRetestFlag" @change="changeRetest" clearable>
                        <el-radio label="1"
                                  :value="1">{{$t('common.no')}}
                        </el-radio>
                        <el-radio label="2"
                                  :value="2">{{$t('common.yes')}}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <el-col :span="12" v-if="isShowReportNo">
                <el-form-item :label="$t('trf.previousReportNo')" v-if="valiUsable('retestReportNo')" prop="retestReportNo" :rules="{required:valiRequired('retestReportNo'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                    <el-input maxlength="36" v-model="retestReportNo" autocomplete="off" clearable></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('service.serviceType')" v-if="valiUsable('serviceType')"  prop="serviceType" :rules="{required:valiRequired('serviceType'),message:$t('trf.validate.requiredBlur'),trigger:'change'}" >
                    <el-select v-model="serviceType" @change="serviceTypeChange"
                               style="width: 100%;" :placeholder="$t('service.serviceType')"
                               clearable>
                        <el-option v-for="(serviceType,index) in serviceTypeData"
                                   :label="serviceType.serviceTypeName"
                                   :value="serviceType.serviceTypeCode"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('lab.labName')" v-if="valiUsable('labCode')"  prop="trfLab.labCode"  :rules="{required:valiRequired('labCode'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-select v-model="trfLab.labCode" :placeholder="$t('lab.selLabName')"
                               @change="selectLabChange" filterable :filter-method="dataFilter"
                               style="width: 100%;" clearable>
                        <el-option v-for="(lab,index) in templateLabData" :label="lab.labName"
                                   :value="lab.labCode"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" v-if="labContactFlag">
                <el-form-item :label="$t('labContact.labContactName')"  v-if="valiUsable('labContactId')"  prop="trfLabContact.labContactId"  :rules="{required:valiRequired('labContactId'),message:$t('trf.validate.requiredBlur'),trigger:'change'}" >
                    <el-select v-model="trfLabContact.labContactId"
                               filterable @change="selectLabContactChange"
                               style="width: 100%;" clearable>
                        <el-option v-for="(labContact,index) in templateLabContactData"
                                   :label="labContact.labContactName"
                                   :value="labContact.labContactId"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('lab.labAddress')" v-if="valiUsable('labAddress')"  prop="trfLab.labAddress"  :rules="{required:valiRequired('labAddress'),message:$t('trf.validate.requiredBlur'),trigger:'change'}" >
                    <el-input maxlength="500" v-model="trfLab.labAddress" autocomplete="off" clearable></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import {validatenull} from "@/util/validate";
    import {
        getServiceType,
    } from "@/api/common/index";
    import {getTemplateLabList,getTemplateLabContactList} from "@/api/trf/trf";
    import {mapGetters} from "vuex";
    export default {
        name: "trfBasic",
        /*props: {
            trfId:String,
            bossNo:String,
            isSgs:{
                type:Boolean,
                default:false
            },
            templateObj:{
                type:Object,
                default:null
            },
        },*/
        props:['trfId','bossNo','isSgs','templateObj','lab','labContact','fieldSettingsData','serviceType','serviceTypeName'],
        data() {
            return {
                isShowReportNo: false,//是否为重新测试 填写repoetNo
                labContactFlag: true,
                trfLab:{},
                trfLabContact:{},
                retestReportNo:'',
                isRetestFlag: '1',
                serviceTypeData:[],
                templateLabData:[],
                templateLabContactData:[],
                serviceType:'',
                serviceTypeName:'',
                templateLabDataParam:{},
                templateLabContactDataParam: {},
                testPackageParam:{},
            }
        },
        computed: {
            templateWatch() {
                const { templateObj } = this
                return {
                    templateObj
                }
            },
            ...mapGetters(["userInfo", "language", "permission"]),
        },
        watch: {
            templateWatch: {
                handler: function(newval , oldval) {
                    if(!validatenull(newval)){
                        //查询服务类型
                        this.searchServiceType(newval.templateObj.productLineId);
                        //查询实验室
                        this.$set( this.templateLabDataParam,'productLineCode_equal',newval.templateObj.productLineCode);
                        this.$set( this.templateLabDataParam,'trfTemplateId_equal',newval.templateObj.id);
                        this.searchTemplateLabData();
                    }
                },
                deep: true
            },
            trfLab:{
                handler(){   //注意此处就是handler
                    console.log('lab',this.trfLab);
                    this.$emit('update:lab',this.trfLab);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            trfLabContact:{
                handler(){   //注意此处就是handler
                    console.log('labContact',this.trfLabContact);
                    this.$emit('update:labContact',this.trfLabContact);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
        },
        created() {
            var companyId = this.userInfo.companyId
        },
        methods: {
            //验证表单项是否展示方法
            valiUsable(code){
                var usableFlag=true;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        usableFlag=  this.fieldSettingsData[code].usable!=1?false:true
                    }
                }
                return usableFlag;
            },
            //验证必填项方法
            valiRequired(code){
                var requiredFlag=false;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        requiredFlag=  this.fieldSettingsData[code].required==1?true:false
                    }
                }
                return requiredFlag;
            },
            changeRetest(value) {
                this.isRetestFlag = value;
                if (value == 1) {
                    this.isShowReportNo = false;
                    this.retestReportNo = '';
                } else {
                    this.isShowReportNo = true;
                }
            },
            serviceTypeChange(val) {
                this.serviceTypeName = '';
                let obj = {};
                obj = this.serviceTypeData.find((item) => {
                    return item.serviceTypeCode === val;
                });
                if (obj != undefined && obj != null) {
                    this.serviceTypeName = obj.serviceTypeName;
                }
            },
            selectLabChange(val) {
                this.$set(this.trfLab,'labAddress','');
                this.$set(this.trfLab,'labName','');
                this.$set(this.trfLabContact,'labContactId','');
                this.templateLabContactData = [];
                let obj = {};
                obj = this.templateLabData.find((item) => {
                    return item.labCode === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.trfLab,'labName',obj.labName);
                    this.$set(this.trfLabContact,'labCode',obj.labName);
                    this.$set(this.trfLabContact,'labAddress',obj.labName);
                    this.$set(this.trfLabContact,'labName',obj.labName);
                    this.$set(this.templateLabContactDataParam,'labTypeFlag',obj.labTypeFlag);
                    this.$set(this.trfLab,'labAddress',obj.labAddress);
                }
                this.$set(this.templateLabContactDataParam,'labCode',val);
                this.$set(this.templateLabContactDataParam,'trfTemplateId',this.templateObj.id);
                //查询实验室联系人
                this.searchTemplateLabContactData();
            },
            selectLabContactChange(val) {
                let obj = {};
                obj = this.templateLabContactData.find((item) => {
                    return item.labContactId === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.trfLabContact,'contactName',obj.labContactName);
                    this.$set(this.trfLabContact,'contactEmail',obj.contactEmail);
                    this.$set(this.trfLabContact,'contactTel',obj.contactTel);
                }
            },
            //获取服务类型
            searchServiceType(BUID) {
                getServiceType(BUID).then(res => {
                    const data = res.data.data;
                    this.serviceTypeData = data;
                    //默认服务类型为Regular
                    if (this.serviceTypeData != null && this.serviceTypeData != undefined) {
                        this.serviceType = '1';
                        this.serviceTypeName = 'Regular';
                        //为Trf中的Service Type赋值
                        this.$emit('getServiceType', this.serviceType);
                        this.$emit('getServiceTypeName', this.serviceTypeName);
                    } else {
                        this.serviceType = '';
                        this.serviceTypeName = '';
                    }
                });
            },
            //查询模板实验室下拉数据
            searchTemplateLabData() {
                var params = {};
                getTemplateLabList(Object.assign(params, this.templateLabDataParam)).then(res => {
                    const data = res.data.data;
                    this.templateLabData = data;
                });
            },
            //查询模板实验室联系人下拉数据
            searchTemplateLabContactData() {
                var params = {};
                getTemplateLabContactList(Object.assign(params, this.templateLabContactDataParam)).then(res => {
                    const data = res.data.data;
                    this.templateLabContactData = data;
                    if (this.templateLabContactData == null || this.templateLabContactData == undefined || this.templateLabContactData.length == 0) {//无联系人 不需要选择
                        this.labContactFlag = false;
                        return false;
                    }
                    this.labContactFlag = true;
                    let obj = {};
                    obj = this.templateLabContactData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        this.$set( this.trfLabContact,'labContactId',obj.labContactId);
                        this.$set( this.trfLabContact,'contactName',obj.labContactName);
                        this.$set( this.trfLabContact,'contactEmail',obj.labContactName);
                        this.$set( this.trfLabContact,'contactTel',obj.contactTel);

                        //加载出实验室数据
                        this.$set( this.trfLab,'labCode',obj.labCode);
                        let labObj = {};
                        labObj = this.templateLabData.find((item) => {
                            return item.labCode === obj.labCode;
                        });
                        if (labObj != undefined && labObj != null) {
                            //赋值实验室地址
                            this.$set( this.trfLab,'labAddress',labObj.labAddress);
                            this.$set( this.trfLabContact,'labName',labObj.labName);
                            this.$set( this.trfLabContact,'labCode',labObj.labCode);
                            this.$set( this.trfLabContact,'labAddressb',labObj.labAddress);
                        }
                    }
                });
            },
        }
    }
</script>

<style scoped>

</style>
