<template>
    <basic-container>
        <h1 class="top-title" v-if="isEdit">{{$t('extendData.editTitle')}}</h1>
        <h1 class="top-title" v-else>{{$t('extendData.newTitle')}}</h1>
        <el-card shadow="never" class="box-card">

            <el-row class="sgs-group">
                <h3>{{$t('template.baseInfoTitle')}}</h3>
                <br>
            </el-row>
            <el-form :model="specificform" :rules="rules" ref="specificform" label-width="200px" size="medium"
                     label-position="top" class="sgs-form">
                <el-row :gutter="20">

                    <el-col :span="12">
                        <el-form-item :label="$t('template.productLine')" prop="productLineCode">
                            <el-select clearable filterable v-model="specificform.productLineCode" style="width:100%"
                                       @change="selecProductLineChange" :disabled="templateDisabled" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                                <el-option v-for="(productLine,index) in productLineData"
                                           :label="productLine.productLineName"
                                           :value="productLine.productLineCode"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item :label="$t('template.customerGroup')" prop="customerGroupCode" ref="customerGroupCode">
                            <el-select clearable filterable v-model="specificform.customerGroupCode" style="width:100%"
                                       @change="customerGroupCodeChange" :disabled="templateDisabled" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                                <el-option v-for="(customerGroup,index) in customerGroupData"
                                           :label="customerGroup.customerGroupName"
                                           :value="customerGroup.customerGroupCode"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('extendData.template')" prop="trfTemplateId" ref="trfTemplateId">
                        <el-select clearable filterable v-model="specificform.trfTemplateId" style="width:100%"
                                    :disabled="templateDisabled" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                          <el-option v-for="(template,index) in templateData"
                                     :label="template.templateName"
                                     :value="template.id"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                  <!--  扩展信息标签 只能字母数字 -->
                    <el-col :span="12">
                        <el-form-item :label="$t('extendData.specificCode')" prop="extendSpecificCode" ref="extendSpecificCode">
                          <el-input clearable maxlength="50" v-model="specificform.extendSpecificCode"
                                    autocomplete="off" onKeyUp="value=value.replace(/[^a-zA-Z0-9]/g,'')"></el-input>
                        </el-form-item>
                    </el-col>

                  <!--  标签英文名称 -->
                    <el-col :span="12">
                      <el-form-item :label="$t('extendData.specificNameEn')" prop="specificNameEn" ref="specificNameEn">
                        <el-input clearable maxlength="50" v-model="specificform.specificNameEn"
                                  autocomplete="off"></el-input>
                      </el-form-item>
                    </el-col>

                  <!--  标签中文名称 -->
                    <el-col :span="12">
                      <el-form-item :label="$t('extendData.specificNameCn')" prop="specificNameCn">
                        <el-input clearable maxlength="50" v-model="specificform.specificNameCn"
                                  autocomplete="off"></el-input>
                      </el-form-item>
                    </el-col>
                  <!--  标签中文名称 -->
                    <el-col :span="12">
                      <el-form-item :label="$t('extendData.isMenu')" prop="isMenu">
                        <el-checkbox v-model="isMenuCheckbox" @change="changeCheckbox"></el-checkbox>
                      </el-form-item>
                    </el-col>



                </el-row>

                <el-row :gutter="20">

                  <!-- 英文富文本 -->
                  <el-col :span="12">
                    <el-form-item :label="$t('extendData.contentEn')" prop="contentEn">
                      <Toolbar
                          style="border-bottom: 1px solid #ccc"
                          :editor="editor"
                          :defaultConfig="toolbarConfig"
                      />
                      <!-- 编辑器 -->
                      <Editor
                          style="height: 400px; overflow-y: hidden"
                          :defaultConfig="editorConfig"
                          v-model="specificform.contentEn"
                          @onChange="onChange"
                          @onCreated="onCreated"
                      />
                    </el-form-item>
                  </el-col>

                  <!-- 中文富文本 -->
                  <el-col :span="12">
                    <el-form-item :label="$t('extendData.contentCn')" prop="contentCn">
                      <Toolbar
                          style="border-bottom: 1px solid #ccc"
                          :editor="editor2"
                          :defaultConfig="toolbarConfig"
                      />
                      <!-- 编辑器 -->
                      <Editor
                          style="height: 400px; overflow-y: hidden"
                          :defaultConfig="editorConfig"
                          v-model="specificform.contentCn"
                          @onCreated="onCreated2"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>


                <el-row style="margin-top: 10px;text-align: center">
                    <el-button type="primary" @click="goBackTemplateList" v-if="permissionList.goBackTemplateListBtn" >{{$t('operation.goBack')}}</el-button>
                    <el-button type="primary" v-if="permissionList.submitTemplateBtn"  @click="submitSpecificform('specificform')" :disabled="isDisabled">
                        {{$t('operation.confirm')}}
                    </el-button>
                </el-row>

            </el-form>
        </el-card>



    </basic-container>
</template>

<script>

    import {
        submitSpecificform,
        detail,

    } from "@/api/specificextend/specificextend";
    import {
        getCustomerGroup,
        getProductLine,
    } from "@/api/common/index";
    import {getTemplateList} from "@/api/trf/trf";
    import {mapGetters} from "vuex";
    import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
    import '@wangeditor/editor/dist/css/style.css';
    import { i18nChangeLanguage } from '@wangeditor/editor'

    export default {
      components: { Editor, Toolbar },
        created() {
            this.queryproductLineData();
            this.queryCustomerGroupData();

            //获取路由参数
            this.getParams();
        },
        data() {
            var validContentEn = (rule, value, callback) => {
              value = value.replace(/<.*?>/g,'')
              if (!value) {
                callback(this.$t('extendData.contentEnBlur'));
              }else {
                callback();
              }
            }

            var validateSpecificNameEn = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('extendData.specificNameEnBlur')));
              } else if (value.toString().trim().length === 0) {
                callback(new Error(this.$t('extendData.specificNameEnBlur')));
              } else {
                callback();
              }
            }
            var validateExtendSpecificCode = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('extendData.specificCodeBlur')));
              } else if (value.toString().trim().length === 0) {
                callback(new Error(this.$t('extendData.specificCodeBlur')));
              } else {
                callback();
              }
            }
            var validateTemplateName = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('extendData.selTemplate')));
              } else if (value.toString().trim().length === 0) {
                callback(new Error(this.$t('extendData.selTemplate')));
              } else {
                callback();
              }
            }
            var validateProductLineCode = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('template.selProductLine')));
              } else if (value.toString().trim().length === 0) {
                callback(new Error(this.$t('template.selProductLine')));
              } else {
                callback();
              }
            }
          var validateCustomerGroupCode = (rule, value, callback) => {
            if (!value) {
              callback(new Error(this.$t('template.selCustomerGroup')));
            } else if (value.toString().trim().length === 0) {
              callback(new Error(this.$t('template.selCustomerGroup')));
            } else {
              callback();
            }
          }
          var validateTrfTemplateId = (rule, value, callback) => {
            if (!value) {
              callback(new Error(this.$t('extendData.selTemplate')));
            } else if (value.toString().trim().length === 0) {
              callback(new Error(this.$t('extendData.selTemplate')));
            } else {
              callback();
            }
          }


            return {
              editor: null,
              editor2: null,
              html: "<p>hello&nbsp;world</p>",
              toolbarConfig: {
                // toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
                // excludeKeys: [ /* 隐藏哪些菜单 */ ],
              },
              editorConfig: {
                placeholder: '请输入内容...',
                // autoFocus: false,

                // 所有的菜单配置，都要在 MENU_CONF 属性下
                MENU_CONF: {},
              },
                templateDisabled:false,
                productLineData: [],
                customerGroupData: [],
                templateData:[],
                isMenuCheckbox:true,
                isEdit:false,
                specificform: {
                  id: '',
                  trfTemplateId: '',
                  extendSpecificCode: '',
                  specificNameEn: '',
                  specificNameCn: '',
                  contentCn: '',
                  contentEn: '',
                  isMenu: 0,
                  customerGroupCode: null,
                  productLineId: '',
                  productLineCode: '',
                },
                templateDataParam: {
                  productLineCode: '',
                  isLoadGeneral: 1,
                  customerGroupCode: '',
                  trfStatus: '1',
                },
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                data: [],

                rules: {
                    templateName: [
                        {required: true,trigger:['blur','change'], validator:validateTemplateName},
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    'productLineCode': [
                        {required: true,trigger:['blur','change'], validator:validateProductLineCode}
                    ],
                    'customerGroupCode': [
                        {required: true,trigger:['blur','change'], validator:validateCustomerGroupCode}
                    ],
                    'trfTemplateId':[
                        {required: true,trigger:['blur','change'], validator:validateTrfTemplateId}
                    ],
                    'extendSpecificCode':[
                      {required: true,trigger:['blur','change'], validator:validateExtendSpecificCode}
                    ],
                    'specificNameEn':[
                      {required: true,trigger:['blur','change'], validator:validateSpecificNameEn}
                    ],
                    'contentEn':[
                      {required: true, trigger:['blur','change'],validator:validContentEn}
                    ]


                },
            };
        },
        watch: {
          /*'specificform.contentEn': function (){
            this.$refs.specificform.validateField('contentEn')
          },*/
          //监听语言变化
          language: function (newVal) {debugger;
            console.log("新语言:"+newVal)
            if(this.$refs['specificform']){
              this.$refs['specificform'].fields.forEach(item => {
                if(item.validateState === 'error'){
                  this.$refs['specificform'].validateField(item.labelFor)
                }
              })
            }

          }
        },
        computed: {
            ...mapGetters(["permission","language"]),
            permissionList() {
                return {
                    goBackTemplateListBtn: this.vaildData(this.permission['sgs:extend:goBack'], false),
                    submitTemplateBtn: this.vaildData(this.permission['sgs:extend:save'], false),
                };
            }
        },
      beforeDestroy() {
        const editor = this.editor
        if (editor == null) return
        editor.destroy() // 组件销毁时，及时销毁编辑器
      },

        methods: {
          onCreated(editor) {debugger;
            // 切换语言 - 'en' 或者 'zh-CN'
            //i18nChangeLanguage(this.language)
            this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
          },
          onCreated2(editor) {
            //i18nChangeLanguage(this.language)
            this.editor2 = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
          },
          onChange(editor) {
            //console.log("onChange", editor.getHtml()); // onChange 时获取编辑器最新内容
            this.$refs.specificform.validateField('contentEn')
          },
            changeCheckbox(val){debugger
              if(val){
                this.specificform.isMenu = 1;
              }else {
                this.specificform.isMenu = 0;
              }
            },
            selecProductLineChange(val){
              this.$set(this.templateDataParam, 'productLineCode', val);
              //清空模版
              this.specificform.trfTemplateId='';
              this.queryTemplateData();
            },
            customerGroupCodeChange(val){
              this.specificform.trfTemplateId='';
              this.$set(this.templateDataParam, 'customerGroupCode', val);
              this.queryTemplateData();
            },

            getParams() {
                // 取到路由带过来的参数
                const routerParams = this.$route.query.id
                // 将数据放在当前组件的数据内
                this.templateId = routerParams;
                if (routerParams != '' && routerParams != undefined) {
                    this.isEdit = true
                    //查询模板详情
                    this.queryDetail(this.templateId);
                }

            },
            queryDetail(templateId) {
                let _this = this
                detail(templateId).then(res => {
                    const data = res.data.data;
                    this.specificform = data;
                    this.$set(this.templateDataParam, 'productLineCode', this.specificform.productLineCode);
                    this.$set(this.templateDataParam, 'customerGroupCode', this.specificform.customerGroupCode);
                    this.queryTemplateData();
                    if(this.specificform.isMenu == 1){
                      _this.isMenuCheckbox = true
                    }else {
                      _this.isMenuCheckbox = false
                    }
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },




            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.customerGroupData = data;
                });
            },
            queryproductLineData() {
                getProductLine().then(res => {
                    const data = res.data.data;
                    this.productLineData = data;
                });
            },
            queryTemplateData(){
              let params = {}
              getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
                const data = res.data.data;
                this.templateData = data;
              })
            },



            goBackTemplateList(){
                this.$router.push({path: '/extend/list', query: {}});
            },
            submitSpecificform() {
                this.isDisabled = true;
                this.$refs['specificform'].validate((valid) => {
                    if (valid) {
                        const loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });

                        this.isDisabled = true;

                        if(this.isMenuCheckbox){
                          this.specificform.isMenu = 1;
                        }else {
                          this.specificform.isMenu = 0;
                        }

                        submitSpecificform(this.specificform).then(() => {
                            loading.close();
                            this.isDisabled = false;
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.$router.push({path: '/extend/list', query: {}});

                        }, error => {
                            loading.close();
                            this.isDisabled = false;
                            //this.$message.error(this.$t('api.error'));
                            console.log(error);
                        });
                        // }
                    } else {
                        this.isDisabled = false;
                        console.log('error submit!!');
                        return false;
                    }
                });
            },

        }
    };
</script>

<style scoped>
    .el-table__header thead .el-table-column--selection label {
        display: none;
    }

    .el-table__header thead .el-table-column--selection div {
        content: "全选";
    }
</style>
