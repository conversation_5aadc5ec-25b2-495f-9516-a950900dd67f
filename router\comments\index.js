const router = require("koa-router")();
const Api = require("./../../request");

/*/api/sgs-pbm/sample/web/v1/comments/list*/
router.post("/api/sgs-pbm/sample/web/v1/comments/list", async (ctx, next) => {
    const datas = await Api.post("sgs-pbm/sample/web/v1/comments/list", ctx);
    ctx.body = datas;
})
/*/api/sgs-pbm/sample/web/v1/notification*/
router.post("/api/sgs-pbm/sample/web/v1/notification", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/notification", ctx);
  ctx.body = datas;
})
/*/api/sgs-pbm/sample/web/v1/comments/add*/
router.post("/api/sgs-pbm/sample/web/v1/comments/add", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/comments/add", ctx);
  ctx.body = datas;
})
module.exports = router;
