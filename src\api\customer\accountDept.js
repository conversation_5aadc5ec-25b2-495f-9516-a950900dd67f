import request from '@/router/axios';


export const addAccountDept = (param) => {
    return request({
        url: '/api/sgs-mart/account-dept/add',
        method: 'post',
        data: {
            ...param
        }
    })
}

export const getAccountDeptIds = (accountId) => {
    return request({
        url: '/api/sgs-mart/account-dept/department-ids',
        method: 'get',
        params: {
            accountId,
        }
    })
}

