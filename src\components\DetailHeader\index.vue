<template>
  <div class="detail-header">
    <h2>{{ headInfo.headName }}</h2>
    <h3>{{ headInfo.subHeadName }}</h3>
    <div class="detail-sub-info">
      <div class="sub-info-item">
        <div
          class="label-group"
          v-for="item in headInfo.keys"
          :key="item.label"
        >
          <span class="label">{{ item.label }}:</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </div>
      <div class="sub-info-item">
        <div class="label-group">
          <span class="label">Create Time:</span>
          <span class="value">{{ createTime }}</span>
        </div>
        <div class="label-group">
          <span class="label">Update Time:</span>
          <span class="value">{{ updateTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  headInfo: {
    type: Object,
    required: true,
    default: () => ({
      headName: '',
      subHeadName: '',
      createTime: null,
      updateTime: null,
      keys: [],
    }),
  },
})
const nowDate = new Date()
  .toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  })
  .replace(/\//g, '-')

const createTime = computed(() => props.headInfo.createTime || nowDate)
const updateTime = computed(() => props.headInfo.updateTime || nowDate)
const getTruncatedText = (text, maxLength = 10) => {
  if (!text) return ''
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text
}
</script>

<style scoped lang="scss">
@use '@/assets/style/unit.module.scss' as *;
.detail-header {
  background: #fff;
  padding: $module-padding;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  h2 {
    font-size: 16px;
    color: $text-color-value;
  }

  h3 {
    font-size: 24px;
    padding-top: $inline-element-spacing;
    padding-bottom: $inline-element-spacing;
    word-wrap: break-word;
    word-break: break-all;
  }
}
.detail-sub-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .sub-info-item {
    display: flex;
  }
  .label-group {
    padding-right: $inline-element-spacing;
  }
  .label {
    color: $text-color-value;
  }

  .value {
    padding-top: $inline-element-spacing;
    padding-left: $inline-element-spacing;
  }
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #dcdfe6;
  margin-top: 16px;
}
</style>
