<template>
    <div>
        {{currentPage}} / {{pageCount}}
        <pdf
                src="http://localhost:8100/sgs-mart/file/download?cloudId=CI/2020/06/images_1592915977255.pdf"
                @num-pages="pageCount = $event"
                @page-loaded="currentPage = $event"
        ></pdf>
    </div>
</template>

<script>

    export default {
        components: {
            pdf: resolve => require(['vue-pdf'], resolve)
        },
        data() {
            return {
                currentPage: 0,
                pageCount: 0,
            }
        }
    }

</script>
