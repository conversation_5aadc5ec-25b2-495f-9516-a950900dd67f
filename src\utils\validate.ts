/**
 * 验证是否为有效的 URL 地址
 * @param s - 待验证的字符串
 * @returns 如果是有效的 URL 返回 true，否则返回 false
 */
export function isURL(s: string): boolean {
  return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 验证邮箱地址是否有效
 * @param email - 待验证的邮箱地址
 * @returns 如果是有效的邮箱地址返回 true，否则返回 false
 */
export function validateEmail(email: string): boolean {
  const re = /^[\w\.\-]+@([\w\-]+\.)+[\w\-]+$/
  return re.test(email)
}

/**
 * 判断手机号码是否正确
 * @param phone - 待验证的手机号码
 * @returns 一个数组，第一个元素表示验证结果（布尔值），第二个元素表示验证信息（字符串）
 */
export function isvalidatemobile(phone: string): [boolean, string] {
  let list: [boolean, string] = [true, '']
  let result = true
  let msg = ''
  const isPhone = /^0\d{2,3}-?\d{7,8}$/

  if (!validatenull(phone)) {
    if (phone.length === 11) {
      if (isPhone.test(phone)) {
        msg = '手机号码格式不正确'
      } else {
        result = false
      }
    } else {
      msg = '手机号码长度不为 11 位'
    }
  } else {
    msg = '手机号码不能为空'
  }

  list = [result, msg]
  return list
}

/**
 * 判断值是否为空
 * @param val - 待判断的值
 * @returns 如果为空返回 true，否则返回 false
 */
export function validatenull(val: unknown): boolean {
  if (typeof val === 'boolean') {
    return false
  }
  if (typeof val === 'number') {
    return false
  }
  if (Array.isArray(val)) {
    return val.length === 0
  }
  if (typeof val === 'object' && val !== null) {
    return JSON.stringify(val) === '{}'
  }
  return (
    val === 'null' ||
    val === null ||
    val === 'undefined' ||
    val === undefined ||
    val === ''
  )
}

/**
 * 判断对象是否为空
 * @param val - 待判断的对象
 * @returns 如果对象为空返回 true，否则返回 false
 */
export function objectIsNull(val: unknown): boolean {
  return (
    val === undefined ||
    val === null ||
    val === '' ||
    (Array.isArray(val) && val.length === 0)
  )
}
