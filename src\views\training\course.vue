<template>
  <basic-container>
    <!-- <el-breadcrumb class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('navbar.courseManager')}}</el-breadcrumb-item>
    </el-breadcrumb> -->
    <h1 class="top-title">{{$t('navbar.courseManager')}}</h1>
    <el-card shadow="never" class="sgs-box">
        <el-row>
            <el-form :inline="true" ref="formSelect" :model="formSelect" size="medium">
                <el-form-item prop="courseTitle">
                    <el-input clearable v-model="formSelect.courseTitle" :placeholder="$t('training.CourseTitle')"></el-input>
                </el-form-item>
                <el-form-item  prop="courseLevel">
                    <el-select  v-model="formSelect.courseLevel" multiple @change="selectRoleChange" :placeholder="$t('training.TrainingCourseLevel')" style="width: 100%">
                        <el-option v-for="item in courseLevelList" :key="item.id" :lable="item.id" :value="item.name"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button v-if="permissionList.searchCourseBtn" type="primary" @click="showTableButton">{{ $t('operation.search') }}</el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <el-row>
        <el-row align="right" style="margin: 5px 0px">
            <el-button v-if="permissionList.addCourseBtn" type="primary" size="medium" @click="openDialogOfAdd">{{ $t('operation.add') }}</el-button>
        </el-row>
        <el-table
                ref="courseTable"
                :data="showData"
                row-key="id"
                lazy
                @expand-change="courseContactChange"
                stripe style="width:100%"
                :expand-row-keys="expands"
                v-loading="listLoading"
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column type="index" label="#" width="50"> </el-table-column>
            <el-table-column type="expand"  >
            <template slot-scope="props">
                <el-table :data="tableData"  v-loading="contactloading" :element-loading-text="$t('loading')" style="width: 100%">
                <el-table-column
                        prop="moduleName"
                        :label="$t('training.CourseModule')">
                </el-table-column>
                <el-table-column
                        prop="customerGroupName"
                        :label="$t('training.CustomerGroup')"
                >
                </el-table-column>
                <!--<el-table-column
                        prop="courseTitle"
                        width="360"
                        :label="$t('training.CourseTitle')">
                </el-table-column>
                <el-table-column
                        prop="courseLevel"
                        width="360"
                        :label="$t('training.TrainingCourseLevel')">
                </el-table-column>-->
                <el-table-column :label="$t('operation.title')" width="260" align="center">
                    <template slot-scope="scope">
                    <el-button v-if="permissionList.editModuleBtn" type="text" @click="openModuleEditDialog(scope.row)">{{ $t('operation.edit') }}</el-button>
                    <el-button v-if="permissionList.deleteModuleBtn" type="text" @click="handleDelModule(scope.row)">{{ $t('operation.remove') }}</el-button>
                    <el-button v-if="permissionList.attachmentModuleBtn" type="text" @click="openFileDialog(scope.row)">{{ $t('training.Attachment') }}</el-button>
                    </template>
                </el-table-column>
                </el-table>
            </template>
            </el-table-column>
            <el-table-column prop="courseTitle" :label="$t('training.CourseTitle')" width="260px;" ></el-table-column>
            <el-table-column prop="courseLevel" :label="$t('training.TrainingCourseLevel')" width="160px" ></el-table-column>
            <!--<el-table-column prop="buyerCustomerGroupName" :label="$t('training.CustomerGroup')"></el-table-column>-->
            <el-table-column prop="courseIntroduction" :label="$t('training.CourseIntroduction')" :show-overflow-tooltip="true" ></el-table-column>
            <el-table-column :label="$t('training.Action')" align="center" width="240px;">
            <template scope="scope">
                <el-button v-if="permissionList.addModuleBtn" type="text" @click="openModuleAdd(scope.row)">{{ $t('training.AddModule') }}</el-button>
                <el-button v-if="permissionList.editCourseBtn" type="text" @click="opendialog(scope.row)">{{ $t('operation.edit') }}</el-button>
                <el-button v-if="permissionList.deleteCourseBtn" type="text" @click="handleDel(scope.row)">{{ $t('operation.remove') }}</el-button>
            </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        </el-row>
    </el-card>
    <el-drawer :title="addOrUpdCourse" :visible.sync="dialogFormVisibleOfAdd" size="60%">
      <el-form  ref="form" :model="form" label-width="200px"  class="sgs-form" label-position="left" size="medium" :rules="rules" >
        <el-form-item :label="$t('training.CourseTitle')"  prop="courseTitle">
          <el-input clearable v-model="form.courseTitle"></el-input>
        </el-form-item>
        <el-form-item :label="$t('training.TrainingCourseLevel')"  prop="courseLevel">
          <el-select v-model="form.courseLevel" @change="selectRoleChange" :placeholder="$t('training.TrainingCourseLevel')"  style="width: 100%">
            <el-option v-for="item in courseLevelList" :key="item.id" :lable="item.id" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <!--<el-form-item :label="$t('customerGroup.name')" prop="buyerCustomerGroupName" >
          <el-select v-model="form.buyerCustomerGroupName" @change="selectCustomerGroupChange" :placeholder="$t('training.CustomerGroup')" style="width:100%">
            <el-option v-for="item in pris" :key="item.id" :lable="item.id" :value="item.name"></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item :label="$t('training.CourseIntroduction')"  prop="courseIntroduction">
          <el-input clearable type="textarea" :maxlength="500" show-word-limit  :rows="4" v-model="form.courseIntroduction"></el-input>
        </el-form-item>
        <!--<el-form-item label="Upload Course Image"  prop="uploadCourseImage">-->
          <!--<el-upload-->
                  <!--class="avatar-uploader"-->
                  <!--action="https://apidev.sgsonline.com.cn/FrameWorkApi/file/doUpload"-->
                  <!--:show-file-list="false"-->
                  <!--:on-success="handleAvatarSuccessOfImg"-->
                  <!--:before-upload="beforeAvatarUpload">-->
            <!--<img  v-model="form.uploadCourseImage" v-if="imageUrl" :src="imageUrl" class="avatar">-->
            <!--<i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
          <!--</el-upload>-->
          <!--<el-input clearable type="hidden" v-model="form.uploadCourseImage">{{ imageUrl }}</el-input>-->
        <!--</el-form-item>-->
        <div class="sgs-bottom">
          <el-button type="primary" @click="onSubmit('0')" v-loading.fullscreen.lock="courseSave">{{ $t('operation.save') }}</el-button>
          <el-button @click="dialogFormVisibleOfAdd=false">{{ $t('operation.cancel') }}</el-button>
        </div>
      </el-form>
    </el-drawer>

    <el-drawer size="60%" :title="$t('training.TrainingMaterials')" :visible.sync="dialogFileVisible">
      <ui>
        <el-card v-if="fileDialogFileUrlList.length == 0">
          <div style="text-align: center;color: #909399">
            <h3>{{ $t('training.NoData') }}</h3>
          </div>
        </el-card>
        <li v-for="item in fileDialogFileUrlList" >
          <a class='download' :href='item.moduleMaterialFileUrl' download=""  title="download">{{ item.moduleMaterialFileUrlName }}</a>
          <el-divider></el-divider>
        </li>
      </ui>
    </el-drawer>
    <el-drawer size="70%" :title="addOrUpdModule" :visible.sync="dialogVisibleModuleAdd">
      <div class="sgs-group">
        <h3>{{$t('info.base')}}</h3>
      </div>
      <el-form ref="formModuleAdd" :model="formModuleAdd" label-width="200px"
               label-position="left" class="sgs-form" size="medium" :rules="rulesModule">
      <el-form-item :label="$t('training.CourseTitle')" v-show="show1">
        <!--<el-input clearable v-model="form.courseTitle"></el-input>-->
        <el-select v-model="formModuleAdd.courseTitle" @change="selectTitleChangeModule" :placeholder="$t('training.CourseTitle')" style="width: 100%" disabled>
          <el-option v-for="item in courseList" :key="item.id" :lable="item.id" :value="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('training.TrainingCourseLevel')" v-show="show1">
        <el-input clearable v-model="formModuleAdd.courseLevel" disabled  ></el-input>
      </el-form-item>
      <el-form-item :label="$t('training.CourseIntroduction')" v-show="show1">
        <el-input clearable type="textarea" :rows="6" :max="5" v-model="formModuleAdd.courseIntroduction" disabled></el-input>
      </el-form-item>
      <el-form-item :label="$t('customerGroup.name')" prop="customerGroupName" >
        <el-select filterable v-model="formModuleAdd.customerGroupName" @change="selectCustomerGroupChange" :placeholder="$t('training.CustomerGroup')" style="width:100%">
          <el-option v-for="item in pris" :key="item.id" :lable="item.id" :value="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('training.TrainingCourseModules')"  prop="moduleName" >
        <el-input clearable v-model="formModuleAdd.moduleName"></el-input>
      </el-form-item>
        <el-row class="sgs-group">
          <h3>{{$t('info.attachment')}}</h3>
          <div class="right" >
            <el-upload
                    class="upload-demo"
                    action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                    :on-change="uploadChange"
                    :on-success="handleAvatarSuccess"
                    :file-list="fileList"
                    :show-file-list="false">
              <el-button size="small" type="primary">{{ $t('training.Browse') }}</el-button>
              <!--<div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
            </el-upload>
          </div>
        </el-row>

        <el-table :data="formModuleAdd.attachmentList"
                  :element-loading-text="$t('uploadLoadingText')"
                  element-loading-spinner="el-icon-loading"
                  v-loading="uploadLoading"
                  width="100%">
          <el-table-column
                  type="index"
                  fixed
                  label="#"
                  width="50">
          </el-table-column>
          <el-table-column
                  prop="documentName"
                  :label="$t('attachment.name')">
          </el-table-column>
          <el-table-column
                  :label="$t('operation.title')"
                  width="180">
            <template slot-scope="scope">
              <el-button type="text" @click="downloadAttachmentRow(scope.row)" size="small" icon="el-icon-download">{{$t('operation.download')}}</el-button>
              <el-button @click="removeAttachmentRow(scope.$index)" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="sgs-bottom">
          <el-button type="primary" @click="addModule" v-loading.fullscreen.lock="moduleSave">{{ $t('operation.save') }}</el-button>
          <el-button type="" @click="resetForm('formModuleAdd')">{{ $t('operation.reset') }}</el-button>
          <el-button type="" @click="closeModuleEditDialog">{{ $t('operation.cancel') }}</el-button>
        </div>
      </el-form>
    </el-drawer>
  </basic-container>
</template>


<script>
    import {add, Get_CourseLevel, showTable2, handleDel2, selCourseById, updCourseById, getModulePage} from "@/api/training/course";
    import {selModuleMaterialFileUrl, selModuleById, updModuleById,sel_group, handleDelModule,addModule} from "@/api/training/module";
    import {uploadClassMaterials,selLocationList,selCourseModule,Get_Course} from "@/api/training/class";
    import {getCloudFileURL} from "@/api/common/index";
    import {mapGetters} from "vuex";
    export default {
        data() {
            return {
                uploadLoading:false,
                name: "course",
                show:true,
                show1:false,
                form: {},
                formDialogModuleEdit: {},
                imageUrl: '',
                uploadCourseImage: '',
                editForm: '',
                addForm: '',
                formSelect: {
                    courseLevel:[],
                    courseLevelArrayList:[],
                },//查询form
                pris: [],
                courseLevelList: [],
                courseLevelId2:'',
                courseLevel1: '',
                fileList:[],   //文件列表展示
                pickerOptions1: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                },
                dialogFormVisibleOfAdd:false,     //添加模态框是否显示
                dialogVisibleModuleAdd:false,     //模块 修改模态框是否显示
                dialogFileVisible:false,     //查看文件模态框是否显示
                fileDialogFileUrlList:'',    //文件展示列表，后台已转换，可直接下载
                showData:[],
                listLoading: false,
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                pageC: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                rules:{
                    courseTitle :[{required: true, message: this.$t('training.validate.courseTitle'), trigger: 'blur'}],
                    courseLevel :[{required: true, message: this.$t('training.validate.trainingCourseLevel'), trigger: 'blur'}],
                    /*buyerCustomerGroupName :[{required: true, message: 'Please enter', trigger: 'blur'}],*/
                    enrollmentStartDate :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    enrollmentExpiredDate :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    courseIntroduction :[{required: true, message: this.$t('training.validate.courseIntroduction'), trigger: 'blur'}],
                },
                rulesModule:{
                    //courseTitle :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    customerGroupName :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    moduleName :[{required: true, message: 'Please enter', trigger: 'blur'}],
                },
                tableData:[],
                expands:[],
                currentRow:{},
                formModuleAdd:{
                    attachmentList: [],
                },
                courseList:'',   //课程标题select使用
                courseId:'',   //课程标题select使用
                groupId:'',
                groupName:'',
                groupList: [],
                moduleSave: false,
                courseSave: false,
                addOrUpdModule:'',
                addOrUpdCourse:''
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    searchCourseBtn: this.vaildData(this.permission['sgs:training:course:search'],false),
                    addCourseBtn: this.vaildData(this.permission['sgs:training:course:add'],false),
                    editCourseBtn: this.vaildData(this.permission['sgs:training:course:edit'],false),
                    deleteCourseBtn: this.vaildData(this.permission['sgs:training:course:delete'],false),
                    addModuleBtn: this.vaildData(this.permission['sgs:training:module:add'],false),
                    editModuleBtn: this.vaildData(this.permission['sgs:training:module:edit'],false),
                    deleteModuleBtn: this.vaildData(this.permission['sgs:training:module:delete'],false),
                    attachmentModuleBtn: this.vaildData(this.permission['sgs:training:module:attachment'],false)
                };
            }
        },
        created: function () {
            this.selectRoleList();
            this.showTable();
        },
        methods: {
            selectRoleList() {//初始化下拉框动态数据
                let obj = [];
                Get_CourseLevel({

                }).then(res => {
                    res.data.forEach((item, index) => {//关键的是将后台返回的数据进行遍历，并封装好
                        obj.push({
                            id: item.id,//id必须品啊
                            name: item.levelName//name必须品啊
                        });
                    });
                });
                this.courseLevelList = obj;
                return obj;
            },
            remoteMethod(locationName) {
                this.list = [];
                selLocationList({
                    locationName,
                }).then(res => {
                    let aa = res.data.rows;
                    console.log(aa);
                    aa.map(item=>{
                        this.list.push({
                            label:item.locationName,
                            //value:item.locationCode
                            value:item.locationName
                        })
                    })
                });
            },
            showTableButton(){
              this.currentPage=1;
                this.showTable(this.currentPage,this.pageSize);
            },
            //初始化课程表格
            showTable(){
                this.listLoading = true;
                this.formSelect.courseLevelArrayList = []
                this.formSelect.courseLevel.forEach(item =>{
                    this.formSelect.courseLevelArrayList += item + ',';
                })
                var a = this.formSelect.courseLevel
                this.formSelect.courseLevel = "";
                console.log(this.formSelect);
                showTable2(this.page.currentPage,this.page.pageSize,this.formSelect).then(result => {
                    console.log(result);
                    this.listLoading = false;
                    this.showData = result.data.data.records;
                    this.page.total = Number(result.data.data.total)
                    this.formSelect.courseLevel = a;
                });
            },
            //课程，修改和提交
            onSubmit(val) {
                this.$refs['form'].validate((valid) => {
                    if (!valid) {
                        return false;
                    }else {
                        //this.form.uploadCourseImage = this.uploadCourseImage;
                        this.form.courseLevel = this.courseLevelId2;
                        this.form.courseLevelId = this.courseLevel1;
                        //this.form.buyerCustomerGroupCode = this.groupId;
                        this.form.isPublish = val;
                        this.courseSave = true;
                        if(this.form.id){
                            this.form.uploadCourseImage = this.uploadCourseImage;
                            add(this.form,this.fileList).then(res  => {
                                if (res.data.msg == '重复'){
                                    this.$message({
                                        type: "info",
                                        message: this.$t('training.RepeatToAdd')
                                    });
                                    this.isDisabled = false;
                                    this.courseSave = false;
                                    return;
                                }
                                this.isDisabled = false;
                                this.$message({
                                    type: "success",
                                    message: this.$t('training.success')
                                });
                                this.showTable("", "");
                                this.dialogFormVisibleOfAdd = false;
                                this.courseSave = false;
                            }, error => {

                                this.courseSave = false;
                            });
                        }else{
                            this.courseSave = true;
                            add(this.form,this.fileList).then(res  => {
                                if (res.data.msg == '重复'){
                                    this.$message({
                                        type: "info",
                                        message: this.$t('training.RepeatToAdd')
                                    });
                                    this.isDisabled = false;
                                    this.courseSave = false;
                                    return;
                                }
                                this.isDisabled = false;
                                this.$message({
                                    type: "success",
                                    message: this.$t('training.success')
                                });
                                this.showTable("","");
                                this.dialogFormVisibleOfAdd = false;
                                this.resetForm('form');
                                this.courseSave = false;
                            }, error => {
                                this.courseSave = false;
                                console.log(error);
                            });
                        }
                    }
                });
            },

            //图片上传成功后
            handleAvatarSuccessOfImg(res, file) {
                this.imageUrl = URL.createObjectURL(file.raw);//实时预览地址
                //this.uploadCourseImage = URL.createObjectURL(res.data[0].cloudID);
                this.uploadCourseImage = res.data[0].cloudID;
            },
           uploadChange(file, fileList) {
                if(file.status == 'ready'){
                    //开启loading效果
                    this.uploadLoading=true;
                }else{
                      this.uploadLoading=false;
                }
            },
           /* beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isJPG) {
                    this.$message.error('上传图片只能是 JPG 格式!');
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 2MB!');
                }
                return isJPG && isLt2M;
            },*/
            //课程等级选择后，回显
            selectRoleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.courseLevelList.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.courseLevel1 = obj.id;//提交数据使用，此处可忽略
                this.courseLevelId2 = obj.name;//用于回显名称
                this.page.currentPage = 1;
            },
            //上传成功以后，去后台算下载的地址和匹配文件信息，返回前端，循环展示
            handleAvatarSuccess(res, file) {
                const attachment = {
                    'cloudId':res.data[0].cloudID,
                    'fileUrl':res.data[0].path,
                    'documentName':file.name
                };
                this.formModuleAdd.attachmentList.push(attachment);
                //this.uploadFileList.push(res.data[0].cloudID);
                //this.uploadFileName.push(res.data[0].attachmentName);
              this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('uploadSuccess'),
                    type: 'warning'
                });
            },
            removeAttachmentRow(index){
                this.formModuleAdd.attachmentList.splice(index,1);
            },
            downloadAttachmentRow(row){
                getCloudFileURL(row.cloudId).then(res => {
                    window.open(res.data,"_blank");
                });
            },
            // 移除上传的文件列表
            delFileList(url) {
                for (let i = this.fileList.length - 1; i >= 0; i--) {
                    if (this.fileList[i].fileUrl == url) {
                        this.fileList.splice(i, 1);
                    }
                }
            },
            // 删除课程
            handleDel(row){
                this.$confirm(this.$t('operation.confirmDelete'), '提示', {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: 'warning'
                }).then(() => {
                    handleDel2(row.id).then(() => {
                        this.isDisabled = false;
                        this.$message({
                            type: "success",
                            message: this.$t('training.success')
                        });
                        this.showTable("","");
                    }, error => {
                        console.log(error);

                    });
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });*/
                });
            },
            //打开修改课程
            opendialog:function(id){
                //this.selectCustomerGroupList();//加载客户组
                this.addOrUpdCourse = this.$t('training.EditCourse')
                selCourseById(id).then(res => {
                    console.log(res);
                    this.form = res.data.course;
                    //this.imageUrl = res.data.fileUrl;
                    this.selectRoleChange(this.form.courseLevel);
                });
                this.dialogFormVisibleOfAdd = true
            },
            //打开模块修改
            openModuleEditDialog:function(id){
                this.addOrUpdModule = this.$t('training.EditModule')
                selModuleById(id).then(res => {
                    console.log(res);
                    this.formModuleAdd = res.data.trainingModule;
                    this.formModuleAdd.attachmentList = res.data.trainingMaterials;
                });
                this.selectCustomerGroupList();//加载客户组
                this.dialogVisibleModuleAdd = true
            },//关闭模块修改
            closeModuleEditDialog:function(id){
                this.resetForm('formModuleAdd');
                this.formModuleAdd.attachmentList = [];
                this.dialogVisibleModuleAdd = false
            },
            //添加模块信息
            addModule() {
                this.$refs['formModuleAdd'].validate((valid) => {
                    if (!valid) {
                        return false;
                    }else {
                      this.formModuleAdd.customerGroupId = this.groupId;
                        this.moduleSave = true;
                      addModule(this.formModuleAdd).then(() => {
                          this.isDisabled = false;
                          this.$message({
                              type: "success",
                              message: this.$t('training.success')
                          });
                          getModulePage(this.pageC.currentPage, this.pageC.pageSize, Object.assign({courseId: this.formModuleAdd.courseId}, this.query)).then(res => {
                              console.log(res);
                              this.listLoading = false;
                              this.tableData = res.data.data.records;
                          });
                          this.dialogVisibleModuleAdd = false;
                          this.resetForm('formModuleAdd');
                          this.formModuleAdd.attachmentList = [];
                          this.moduleSave = false;
                      }, error => {
                          this.moduleSave = false;
                          console.log(error);
                      });
                    }
                })
            },
            //删除模块
            handleDelModule(row){
              debugger;
                this.$confirm(this.$t('operation.confirmDelete'), '提示', {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: 'warning'
                }).then(() => {
                    handleDelModule(row.id).then(() => {
                        this.isDisabled = false;
                        this.$message({
                            type: "success",
                            message: this.$t('training.success')
                        });
                        this.searchCourseContactList(row.courseId);
                    }, error => {
                        console.log(error);

                    });
                }).catch(() => {
                    /*this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });*/
                });
            },
            //打开模块文件模态框
            openFileDialog:function(id){
                selModuleMaterialFileUrl(id).then(res => {
                    console.log(res);
                    this.fileDialogFileUrlList = res.data;
                    //this.formDialog.s
                });
                this.dialogFileVisible = true
            },
            //客户组变化，回显数据
            selectCustomerGroupChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.pris.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.groupId = obj.id;//提交数据使用，此处可忽略
                this.groupName = obj.name;//用于回显名称
            },
            //获取客户组
            selectCustomerGroupList() {//初始化下拉框动态数据
                let obj = [];
                sel_group({

                }).then(res => {
                    res.data.data.forEach((item, index) => {//关键的是将后台返回的数据进行遍历，并封装好
                        obj.push({
                            id: item.customerGroupCode,//id必须品啊
                            name: item.customerGroupName//name必须品啊
                        });
                    });
                });
                this.groupId = obj.id;//提交数据使用，此处可忽略
                this.groupName = obj.name;//用于回显名称
                this.pris = obj;
                return obj;
            },
            toClassList(){
                this.$router.push({path: '/training/classList'})
            },
            noFinash(){
                //alert("Coding....")
            },
            //打开模块添加页面
            openModuleAdd(courseId){
                this.addOrUpdModule = this.$t('training.AddModule')
                this.selectCourse(courseId);
                this.selectCourseList();
                this.selectCustomerGroupList();
                this.formModuleAdd.id = '';
              this.dialogVisibleModuleAdd = true;
            },
            //初始化课程下拉框动态数据
            selectCourseList() {
                let obj = [];
                Get_Course({

                }).then(res => {
                    console.log(res);
                    res.data.data.forEach((item, index) => {
                        obj.push({
                            id: item.id,
                            name: item.courseTitle,
                            courseLevel : item.courseLevel
                        });
                    });
                });
                this.courseList = obj;
                return obj;
            },

            openDialogOfAdd:function(){
                this.addOrUpdCourse = this.$t('training.AddCourse')
                this.form = {};
                this.selectRoleList();
                //this.selectCustomerGroupList();//加载客户组
                this.dialogFormVisibleOfAdd = true
            },
            resetForm(searchForm) {
                this.$nextTick(()=>{
                    this.$refs[searchForm].resetFields();
                    this.formModuleAdd.attachmentList = [];
                })
                //this.$refs['form'].resetFields()
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.showTable();
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.showTable();
            },
            //展开子表
            courseContactChange(row, expandedRows){
                this.tableData = [];
                this.$refs.courseTable.setCurrentRow();
                this.currentRow = row
                if (this.expands.join(',').indexOf(row.id) === -1) {
                    this.expands = [this.currentRow.id]
                    this.searchCourseContactList(row.id);
                } else {
                    this.expands.splice(0, this.expands.length)
                }
                /* if(vlaue2.length!=0){//展开
                     this.searchLabContactList(value1.id);
                 }*/
            },
            searchCourseContactList(id){
                getModulePage(this.pageC.currentPage, this.pageC.pageSize, Object.assign({courseId:id}, this.query)).then(res => {
                    this.listLoading = false;
                    this.tableData = res.data.data.records;
                });
            },

            //打开新增模块，回显数据
            selectCourse(id) {
                selCourseById(id).then(res => {
                    console.log(res);
                    this.formModuleAdd.courseId = res.data.course.id;
                    this.formModuleAdd.courseTitle = res.data.course.courseTitle;
                    this.formModuleAdd.courseLevel = res.data.course.courseLevel;//回填表单信息，展示用
                    this.formModuleAdd.courseLevelName = res.data.course.courseLevelName;//回填表单信息，展示用
                    this.formModuleAdd.courseIntroduction = res.data.course.courseIntroduction;
                });

                var objOfModule = [];

                this.courseModuleList = objOfModule;
                return objOfModule;
                //this.courseLevelId2 = obj.name;//用于回显名称*/
            },
        }
    }


</script>

<style lang="scss">
  .el-tooltip__popper {
    max-width: 400px;
    line-height: 180%;
  }
</style>
