// 全局变量
@import "./variables.scss";
// ele样式覆盖
@import "./element-ui.scss";
// 顶部右侧显示
@import "./top.scss";
// 导航标签
@import "./tags.scss";
// 工具类函数
@import "./mixin.scss";
// 动画
@import "./animate/vue-transition.scss";
//主题
@import "./theme/index.scss";
//适配
@import "./media.scss";
//通用配置
@import "./normalize.scss";
// 基础配置
@import "./unit.scss";

body {
  font-size: 14px;
}
a {
  color: #656565;
  transition: all 0.2s;
}
a:hover {
  color: #f60;
  text-decoration: none;
}
a:focus {
  color: #f60;
  text-decoration: none;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  // -webkit-text-fill-color: #ededed !important;
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  background-color: transparent !important;
  background-image: none !important;
  transition: background-color 50000s ease-in-out 0s !important; //背景色透明 生效时长 过渡效果 启用时延迟的时间
}
input {
  background-color: transparent;
}

* {
  outline: none;
}

table {
  font-size: 14px !important;
  font-weight: 400;
  color: #1b1b1b;
}


.plain-black {
  border-color: #1b1b1b;
}

.el-input__inner,
.el-textarea.is-disabled .el-textarea__inner {
  border: 0 !important;
  border-bottom: 1px solid #1b1b1b !important;
  border-radius: 0 !important;
  &:hover {
    border-color: #1b1b1b !important;
  }
}
.el-input__prefix,
.el-date-editor .el-range__icon {
  // left: -5px;
  color: #1b1b1b;
}
.el-date-editor i.el-range__icon {
  position: relative;
  left: -8px;
}

.line-btn {
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-bottom-color: #ff6600;
  padding-left: 0;
  padding-right: 0;
  font-size: 16px;
  font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
    "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  //font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff6600;
  background: transparent;
  &:hover {
    background-color: transparent;
  }
}

.black-btn {
  background: #000 !important;
  border-color: #000 !important;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  &:hover {
    color: #fff;
  }
}

.el-table {
  th {
    background-color: #f5f5f5 !important;
    font-size: 14px;
    font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
      "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    //font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 22px;
    height: 64px;
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 1) inset !important;
  -webkit-transition-delay: 99999s;
  -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-submenu__title {
  padding: 5px 10px;
  height: auto;
}
.el-menu--popup {
  padding: 0;
}
.el-input.is-disabled .el-input__inner,
.el-textarea.is-disabled .el-textarea__inner,
.form-control[disabled] {
  background-color: #fff;
  border-color: #ccc !important;
}
.el-button--primary.is-plain {
  background-color: transparent;
}

.top-title {
  font-size: 24px;
  font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
    "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  //font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 32px;
  margin: 0px 0 17px;
  padding-left: 24px;
}
.list-header-title {
  font-size: 24px;
  font-weight: 500;
}
.detail-header-title {
  margin: 0;
}

.el-icon-date {
  background: url("/img/icon/date.png") no-repeat center;
  &::before {
    content: "";
  }
}
.el-icon-search {
  // background: url("/img/icon/search.png") no-repeat center;
  // display: block;
  // width: 24px;
  // height: 24px;
  // position: relative;
  // top: 6px;
  // left: -5px;
  // &::before {
  //   content: "";
  // }
}
.el-input-group__append {
  padding: 0 9px;
  border-bottom: 1px solid #1b1b1b !important;
  border-radius: 0;
  border-width: 0;
  background: transparent;
}
.el-pagination .el-select .el-input .el-input__inner,
.el-pagination__editor.el-input .el-input__inner,
.el-pager li {
  border: 1px solid #e6e6e6 !important;
}
.el-pager li {
  margin: 0 4px;
  color: #656565;
  font-weight: 400;
  min-width: 30px;
  &.active {
    background-color: #f60;
    color: #fff;
    border-color: #f60 !important;
  }
}
.el-input-group__prepend {
  border: 0;
  border-bottom: 1px solid #1b1b1b;
  border-radius: 0;
  background-color: #fff;
}
.el-dialog__title {
  font-size: 20px;
  font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
    "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  //font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #1b1b1b;
  line-height: 28px;
}
.z-index-cover {
  z-index: 9999 !important;
}
.step-content {
  position: absolute;
  background: rgba(255, 255, 255, 1);
  bottom: -123px;
  padding: 24px;
  font-size: 20px;
  font-weight: 400;
  color: #000000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  h4 {
    margin: 0;
    white-space: nowrap;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span.tit {
      padding-right: 30px;
    }
    span.skip {
      font-size: 12px;
      font-weight: 400;
      color: #808080;
      cursor: pointer;
    }
  }
  .sub-txt {
    color: #999;
    font-size: 16px;
    margin-top: 16px;
  }
  .step-num {
    padding-top: 16px;
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap;
    color: #656565;
    display: flex;
    justify-content: space-between;
  }
  &::after {
    content: " ";
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-bottom-color: #fff;
    position: absolute;
    top: -20px;
  }
}

.modal-dialog {
  margin: 110px auto;
}

.el-date-editor.el-input input {
  padding-left: 30px !important;
}
.testingExclusions {
  max-width: 800px;
  min-width: 800px !important;
}
.labName {
  max-width: 350px;
  min-width: 350px !important;
}
@media print {
  .page-no-print {
    display: none;
  }
  @page {
    size: auto;
    margin: 0;
  }
}
// 统一表格高度
.table-header-filter{
  th{
    height: 68px!important;
  }
}
.table-header-no-filter{
  th{
    height: 50px!important;
  }
}

// 按钮样式类
.custom-primary-button {
  border-radius: 20px;
  border-color: $primary-color;
  color: $primary-color;
  background-color: #fff;
}
.custom-info-button{
  background-color: #fff;
  border-color: $button-info-color;
  color: $button-info-color;
  border-radius: 20px;
}
.button-default {
  border: 1px solid rgb(220, 223, 230); 
  font-size: 12px; 
}
.button-default:hover {
  color: rgb(255, 102, 0);
  background-color: rgb(255, 240, 230);
  border:1px solid rgb(255, 209, 179);
}
.button-default-focus  {
  font-size: 12px; 
  color: rgb(255, 102, 0);
  background-color: rgb(255, 240, 230);
  border:1px solid rgb(255, 209, 179);
}
.reviewConclusion .el-input .el-input__inner {
  border-bottom: 1px solid #C0C4CC!important; 
}
.el-date-editor .el-range-input{
  border: none!important;
}