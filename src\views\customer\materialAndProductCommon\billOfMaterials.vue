<template>
    <basic-container v-loading="pageLoading">
        <div
                class="sgs_smart_material_product_billOfMaterials"
                id="sgs_smart_material_product_billOfMaterials"
        >
            <el-row v-if="editRule">
                <el-col :span="12">
                    <section>
                        <h4>&nbsp;</h4>
                    </section>
                </el-col>
                <el-col :span="12" style="text-align: right">
                    <el-button type="text" @click="createTRF">
                        <i class="el-icon-document-add"></i>
                        Create TRF
                    </el-button>
                    <el-button type="text" @click="showMaterialTestLine">
                        <i class="el-icon-document"></i>
                        Detail
                    </el-button>
                    <el-button type="text" @click="addMaterials">
                        <i class="el-icon-plus"></i>
                        Add
                    </el-button>
                </el-col>
            </el-row>
            <section>
                <common-table
                        border
                        fit
                        style="width: 100%"
                        row-key="id"
                        ref="bom_material_table"
                        stripe
                        height="400"
                        max-height="400"
                        :size="tableOption.size"
                        :data="billOfMaterialList"
                        :option="tableOption"
                        :filters="tableOption.filters"
                        filter-by-local
                >
                    <template slot="sampleNo" slot-scope="{row}">
                        <a class="sgs_route_link"
                           :href="'/#/customer/newMaterial/detail?action=detail&id='+row.id"
                           target="_blank"
                        >
                            {{ row.sampleNo }}
                        </a>
                    </template>
                    <template slot="trfNo" slot-scope="{row}">
                        <span v-if="row.trfNo &&  row.trfNo.length==1"
                              @click="toTrfDetail(row.trfNo[0])"
                              style="color: #ff6600; font-size: 14px; cursor: pointer">
                            {{row.trfNo[0].relNo }}
                        </span>
                        <el-popover
                                v-if="row.trfNo &&  row.trfNo.length>1"
                                placement="top-start"
                                title="TRF No"
                                width="150"
                                trigger="hover"
                                content="">
                            <ul style="width: 100%; list-style: none;padding:0;margin:0;">
                                <li v-for="(rel,reIndex) in row.trfNo"
                                    :key="'rel_'+reIndex"
                                    style="padding: 5px"
                                >
                              <span @click="toTrfDetail(rel)"
                                    style="color: #ff6600; font-size: 14px; cursor: pointer">
                                  {{rel.relNo }}</span>
                                </li>
                            </ul>
                            <span slot="reference" style="cursor: pointer">
                                <span @click="toTrfDetail(row.trfNo[0])"
                                    style="color: #ff6600; font-size: 14px; cursor: pointer">
                                    {{row.trfNo[0].relNo}}
                                </span>
                          </span>
                        </el-popover>
                    </template>
                    <template slot="isTrf" slot-scope="{row}">
                            <span
                                    :style="{
                                    color:
                                        row.isTrf == 1 ? '#98e794' : '#ff6600',
                                }"
                            >
                                {{ row.isTrf == 1 ? "Yes" : "No" }}
                            </span>
                    </template>
                    <template slot="actionColumn" slot-scope="{row}">
                        <el-button
                                :disabled="false"
                                :icon="getCommentIcon(row)"
                                style="padding: 2px 5px"
                                type="text"
                                @click="openComment(row.bomRelationId)"
                        ></el-button>
                        <i
                                v-if="
                                    showApproved &&
                                    [0, 1].includes(row.approvedStatus)
                                "
                                :disabled="false"
                                class="el-icon-success approved_icon"
                                :class="
                                    getApprovedClass(
                                        row.approvedStatus,
                                        'Approved',
                                    )
                                "
                                style="color: #00d26a"
                                @click="
                                    approvedObj(
                                        row.id,
                                        1,
                                        row.bomRelationId,
                                        row.approvedStatus,
                                    )
                                "
                        ></i>
                        <i
                                v-if="
                                    showApproved &&
                                    [0, 2].includes(row.approvedStatus)
                                "
                                :disabled="false"
                                class="el-icon-error approved_icon"
                                :class="
                                    getApprovedClass(
                                        row.approvedStatus,
                                        'Reject',
                                    )
                                "
                                style="color: #ff6600"
                                @click="
                                    approvedObj(
                                        row.id,
                                        2,
                                        row.bomRelationId,
                                        row.approvedStatus,
                                    )
                                "
                        ></i>
                        <i
                                v-if="
                                    showApproved &&
                                    [1, 2].includes(row.approvedStatus)
                                "
                                :disabled="false"
                                class="el-icon-more approved_icon more_icon"
                                @click="
                                    approvedObj(
                                        row.id,
                                        0,
                                        row.bomRelationId,
                                        row.approvedStatus,
                                    )
                                "
                        ></i>

                        <el-popconfirm
                                v-if="editRule"
                                confirm-button-text="Delete"
                                cancel-button-text="Cancel"
                                icon="el-icon-info"
                                icon-color="red"
                                title="Delete the data?"
                                @confirm="delRelation(row)"
                        >
                            <i
                                    slot="reference"
                                    style="
                                        color: #ff6600;
                                        padding: 2px 5px;
                                        cursor: pointer;
                                    "
                                    class="el-icon-close"
                            ></i>
                        </el-popconfirm>
                    </template>
                </common-table>
            </section>
        </div>
        <el-dialog
                :visible.sync="showAddMaterial"
                width="70%"
                append-to-body
                title="Link Material"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
        >
            <link-material
                    v-if="showAddMaterial"
                    ref="linkMaterialComp"
                    :sample-id="sampleId"
                    :customer-obj="customerObj"
                    @cancelDia="cancelLinkMaterial"
            ></link-material>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showAddMaterial = false">Cancel</el-button>
                <el-button v-loading="saveBtnLoading" type="primary" @click="saveBillOfMaterial">
                    Select
                </el-button>
            </span>
        </el-dialog>
        <!-- testline detail-->
        <el-dialog
                title="Material TestLine Detail"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal="false"
                show-close
                v-dialog-drag
                :visible.sync="showTestLineDia"
                width="90%"
                append-to-body
        >
            <materials-testline
                    v-if="showTestLineDia"
                    :object-id="sampleId"
            ></materials-testline>
        </el-dialog>
        <reject-provided
                v-if="rejectShow"
                :object-id="materialRelId"
                :object-type="currentApprovedType"
                @cancelDia="rejectShow = false"
                @rejectSuccess="rejectSuccess"
        ></reject-provided>
        <chat-view
                v-if="showChat"
                :object-id="chat.objectId"
                :object-type="currentApprovedType"
                @cancelDia="showChat = false"
        ></chat-view>
    </basic-container>
</template>

<script>
import sampleApi from "@/api/newSamples"
import RejectProvided from "./rejectProvided"
import ChatView from "./chatView"
import MaterialsTestline from "./materialsTestline"
import LinkMaterial from "./linkMaterial";
import commonTable from "@/components/tableList/commonTable.vue";

export default {
    name: "billOfMaterials",
    data() {
        return {
            pageLoading: false,
            showAddMaterial: false,
            tableLoading: false,
            saveBtnLoading: false,
            rejectShow: false,
            showTestLineDia: false,
            showChat: false,
            currentApprovedType: "bomMaterial",
            materialRelId: "",
            materialId: "",
            searchForm: {
                sampleNo: "",
                sampleCode: "",
            },
            billOfMaterialList: [],
            dataList: [],
            allTestResultList: [],
            chat: {
                objectId: "",
            },
            tableOption: {
                size: "small",
                border: true,
                menuShow: false,
                filters: {},
                index: false,
                selection: true,
                selectionDis:this.createTRFpermissions,
                action: true,
                actionWidth: 80,
                actionName:'Action',
                column: [
                    {label: 'Material ID', prop: 'sampleNo', filter: true, slot: true, minWidth: 220, type: "Input"},
                    {
                        label: 'Material Name',
                        prop: 'sampleName',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'Material Type',
                        prop: 'sampleType',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'Composition',
                        prop: 'composition',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'Construction',
                        prop: 'construction',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {label: 'TRF No', prop: 'trfNo', searchProp:'relNo',filter: true, slot: true, minWidth: 220, type: "Input"},
                    {
                        label: 'Material Supplier',
                        prop: 'supplierName',
                        filter: true,
                        slot: false,
                        minWidth: 220,
                        type: "Input"
                    },
                    {
                        label: 'SGS Tested', prop: 'isTrf', filter: true, slot: true,
                        minWidth: 220,
                        type: "Select",
                        dicData: [{label: 'Yes', value: 1}, {label: 'No', value: 0}]
                    },
                ]
            },
        }
    },
    methods: {
        openComment(rowId) {
            this.chat.objectId = rowId
            this.showChat = true
        },
        createTRFpermissions(row){
            let {permissions} = row;
            return (permissions || []).map(p=>p.action).includes('toTrf');
        },
        handlerApproved(param, refreshPage = false) {
            sampleApi
                .approved(param)
                .then((res) => {
                    if (
                        res.status == 200 &&
                        res.data &&
                        res.data.status == 200
                    ) {
                        let {data} = res.data
                        if (data == "confirm") {
                            //需要弹窗确认，再次请求，然后刷新整个页面
                            this.$confirm(
                                'All info has been approved. Please confirm you would like to set the product status to "Approved"',
                                "Tips",
                                {
                                    confirmButtonText: "Confirm",
                                    cancelButtonText: "Cancel",
                                    type: "warning",
                                },
                            )
                                .then(() => {
                                    param["checkApproved"] = false
                                    this.handlerApproved(param, true)
                                })
                                .catch((err) => {
                                })
                            return
                        }
                        this.$notify.success("Success")
                        if (refreshPage) {
                            window.location.reload()
                            return
                        }
                        this.initBillList()
                    } else {
                        this.$notify.error("Fail")
                    }
                })
                .catch((err) => {
                    this.$notify.error("Fail")
                })
        },
        rejectSuccess(approvedType) {
            let param = {
                sampleId: this.sampleId,
                objectId: this.materialId,
                approvedType: this.currentApprovedType,
                approvedStatus: 2,
            }
            this.rejectShow = false
            this.handlerApproved(param)
        },
        toTrfDetail(trf){
            let {relId,relNo,signature} = trf;
            window.open('/#/trf/trfDetail?id=' + relId + '&title=' + relNo + '&trfNo=' + relNo + '&hash=' + new Date().getTime() + '' + '&actionType=detail' + '&signature=' + signature, '_blank');
        },
        getApprovedClass(approvedStatus, btnType) {
            if (!approvedStatus || approvedStatus == 0) {
                return "approve_gary"
            }
            if (approvedStatus == 1) {
                if (btnType == "Approved") {
                    return "approve_green"
                }
                if (btnType == "Reject") {
                    return "approve_gary"
                }
            }
            if (approvedStatus == 2) {
                if (btnType == "Approved") {
                    return "approve_gary"
                }
                if (btnType == "Reject") {
                    return "approve_red"
                }
            }
        },
        approvedObj(
            objectId,
            approvedStatus,
            bomRelationId,
            rowApprovedStatus,
        ) {
            if (approvedStatus == rowApprovedStatus) {
                return
            }
            let tips = ""
            if (approvedStatus == 0) {
                tips = "Return"
            }
            if (approvedStatus == 1) {
                tips = "Approve"
            }
            if (approvedStatus == 2) {
                tips = "Reject"
            }
            this.$confirm(tips + " the Material ?", "Tips", {
                confirmButtonText: "Confirm",
                cancelButtonText: "Cancel",
                type: "warning",
            })
                .then(() => {
                    if (approvedStatus == 2) {
                        this.materialId = objectId
                        this.materialRelId = bomRelationId
                        this.rejectShow = true
                        return
                    }
                    let param = {
                        sampleId: this.sampleId,
                        objectId,
                        approvedType: this.currentApprovedType,
                        approvedStatus,
                    }
                    this.handlerApproved(param)
                })
                .catch((err) => {
                })
        },
        createTRF() {
            let selection = this.$refs.bom_material_table.getSelection();
            if (!selection || selection.length != 1) {
                this.$notify.error("Please select at least one piece of data");
                return;
            }
            let objectIds = [...new Set(selection.map((row) => row.bomRelationId))];
            let action = 'individualSample';
            let param = {
                action,
                objectIds,
                sourceType:'bom'
            };
            sampleApi.checkToTrf(param).then((res) => {
                if (res.status == 200 && res.data && res.data.status == 200) {
                    let testId = res.data.data;
                    let customerGroupCode = selection[0].buyerCustomerGroupCode;
                    let bossNo = selection[0].buyerCustomerBossNo;
                    let customer = {
                        bossNo,
                        customerGroupCode,
                    };
                    customer = encodeURIComponent(JSON.stringify(customer));
                    let bu = selection[0].productLineCode;
                    let routeUrl = this.$router.resolve({
                        path: "/trf/trfForm",
                        query: {
                            actionType: action,
                            flag: 1,
                            customer,
                            bu,
                            sourceType:'bom',
                            testId
                        },
                    });
                    window.open(routeUrl.href, "_blank");
                }
            }) .catch((err) => {
                console.log("err", err);
                //this.$notify.warning(err);
            });
        },
        showMaterialTestLine() {
            this.showTestLineDia = true
        },
        addMaterials() {
            this.showAddMaterial = true;
            /*this.searchForm.sampleNo = ""
            this.searchForm.sampleCode = ""
            this.search()*/
        },
        search() {
            this.tableLoading = true
            this.dataList = []
            let param = Object.assign({}, {id: this.sampleId}, this.searchForm)
            sampleApi.billApi
                .unBomMaterialPage(param)
                .then((res) => {
                    //console.log("res",res)
                    if (
                        res.status == 200 &&
                        res.data &&
                        res.data.data &&
                        res.data.data.records
                    ) {
                        this.dataList = res.data.data.records
                    }
                    this.tableLoading = false
                })
                .catch((err) => {
                    console.log("err", err)
                    this.tableLoading = false
                })
        },
        cancelLinkMaterial(){
            this.initBillList();
            this.showAddMaterial = false;
        },
        saveBillOfMaterial() {
            let selection = this.$refs.linkMaterialComp.getSelection();
            if (!selection || selection.length == 0) {
                return
            }

            let objectBomIds = selection.map((s) => s.id)
            let saveParam = {
                objectId: this.sampleId,
                objectBomIds,
            }
            this.saveBtnLoading = true
            sampleApi.billApi
                .bindsBom(saveParam)
                .then((res) => {
                    //console.log("bind bom res",res)
                    if (res.status == 200) {
                        this.initBillList()
                    }
                    this.showAddMaterial = false
                    this.saveBtnLoading = false
                })
                .catch((err) => {
                    this.saveBtnLoading = false
                    console.log("bind bom err", err)
                })
        },
        initBillList() {
            this.pageLoading = true
            this.billOfMaterialList = []
            this.allTestResultList = []
            sampleApi.billApi
                .getBomMaterialList({id: this.sampleId})
                .then((res) => {
                    if (res.status == 200 && res.data && res.data.data) {
                        let dbData = res.data.data
                        this.billOfMaterialList = dbData
                    }
                    this.pageLoading = false
                })
                .catch((err) => {
                    this.pageLoading = false
                    console.log("query bill list err", err)
                })
        },
        delRelation(row) {
            let {id} = row
            let delParam = {
                objectBomId: id,
                objectId: this.sampleId,
            }
            sampleApi.billApi
                .deleteBomRelation(delParam)
                .then((res) => {
                    //console.log("deleteBomRelation",res);
                    if (res.status == 200) {
                        this.initBillList()
                    }
                })
                .catch((err) => {
                    console.log("deleteBomRelation err", err)
                })
        },
        getCommentIcon(rows) {
            let {commentsNum} = rows
            //没有对话信息
            if (!commentsNum || commentsNum.length == 0) {
                return "el-icon-chat-square"
            } else {
                return "el-icon-s-comment"
            }
        },
    },
    mounted() {
    },
    created() {
        this.initBillList()
    },
    watch: {},
    computed: {},
    props: {
        editRule: true,
        showApproved: {
            type: Boolean,
            default() {
                return false
            },
        },
        sampleId: {
            type: String,
            default() {
                return ""
            },
        },
        customerObj: {
            type: Object,
            default() {
                return {
                    productLineCode: '',
                    productLineName: '',
                    customerGroupCode: '',
                    customerGroupName: ''
                }
            }
        }
    },
    updated() {
    },
    beforeDestroy() {
    },
    destroyed() {
    },
    components: {LinkMaterial, MaterialsTestline, ChatView, RejectProvided, commonTable},
}
</script>

<style lang="scss">
.sgs_smart_material_product_billOfMaterials {
  font-family: "Arial" !important;
  background: #fff;

  .sgs_route_link {
    color: #ff6600;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
