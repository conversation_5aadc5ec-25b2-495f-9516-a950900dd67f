<template>
  <basic-container>
    <!-- <el-breadcrumb class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('navbar.personalitySetting')}}</el-breadcrumb-item>
    </el-breadcrumb> -->
    <h1 class="top-title">{{ $t("navbar.personalitySetting") }}</h1>
      <el-row>
        <el-tabs type="border-card">
          <el-tab-pane v-if="permissionList.wechatPushUnit" :label="$t('notifaction.wechat.title')">
            <wechat-push></wechat-push>
          </el-tab-pane>
            <el-tab-pane v-if="permissionList.emailNotificationUnit"  :label="$t('notifaction.email.title')" >
              <notifaction isUserConfig></notifaction>
          </el-tab-pane>
        </el-tabs>
      </el-row>
  </basic-container>
</template>

<script>


  import {mapGetters} from "vuex";
  export default {
      components: {
        WechatPush: resolve => require(['@/views/personality/wechatPush'], resolve),
        notifaction:resolve => require(['@/views/customer/notifaction'],resolve)
      },
      data() {
        return {

        }
      },
    computed: {
            ...mapGetters(["permission", "userInfo"]),
            permissionList() {
                return {
                    wechatPushUnit: this.vaildData(this.permission['sgs:main:wechatPush:setting'], false),
                    emailNotificationUnit: this.vaildData(this.permission['sgs:main:emailNotification:setting'], false),
                };
            },
        },
  };
</script>

<style>
</style>
