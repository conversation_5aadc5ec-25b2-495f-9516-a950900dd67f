import { objectIsNull } from '@/utils/validate'
import registerApi from '@/api/register'

type ServiceUnit = {
  languages?: Array<{
    languageCode: string
    serviceUnitName: string
  }>
  serviceUnitName?: string
}

type ServiceUnitTool = {
  queryServiceUnits: (language: string) => Promise<ServiceUnit[] | undefined>
}

// 假设这些常量和函数在其他地方已经定义
const SERVICE_UNIT_LANGUAGE_CHI = 'CHI'
const SERVICE_UNIT_LANGUAGE_EN = 'EN'

/**
 * 将输入的语言代码转换为服务单元使用的语言代码。
 * @param language - 输入的语言代码，如 'zh-CN'。
 * @returns 转换后的服务单元语言代码。
 */
function convertLanguage(language: string): string {
  let newLanguage: string = SERVICE_UNIT_LANGUAGE_EN // 默认英文
  switch (language) {
    case 'zh-CN':
      newLanguage = SERVICE_UNIT_LANGUAGE_CHI
      break
    default:
      newLanguage = SERVICE_UNIT_LANGUAGE_EN
      break
  }
  return newLanguage
}

// 定义 serviceUnitMap
const serviceUnitMap: Map<string, ServiceUnit[]> = new Map()

// 定义 serviceUnitTool 对象
const serviceUnitTool: ServiceUnitTool = {
  queryServiceUnits: async function (
    language: string,
  ): Promise<ServiceUnit[] | undefined> {
    const newLanguage = convertLanguage(language)
    try {
      const res = await registerApi.queryServiceUnit(language)
      if (!objectIsNull(res.data)) {
        if (newLanguage === SERVICE_UNIT_LANGUAGE_CHI) {
          // 中文取值逻辑不同
          for (const serviceUnit of res.data) {
            if (!objectIsNull(serviceUnit.languages)) {
              for (const languageObj of serviceUnit.languages) {
                if (languageObj.languageCode === newLanguage) {
                  serviceUnit.serviceUnitName = languageObj.serviceUnitName
                }
              }
            }
          }
        }
        serviceUnitMap.set('serviceUnits', res.data)
      }
    } catch (error) {
      console.error('查询服务单元时出错:', error)
    }

    return serviceUnitMap.get('serviceUnits')
  },
}

export default serviceUnitTool
