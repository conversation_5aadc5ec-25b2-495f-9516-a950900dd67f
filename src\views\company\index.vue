<template>
  <basic-container>
    <el-breadcrumb class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('navbar.authentication')}}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-row>
      <el-tabs tab-position="left">
        <el-tab-pane :label="$t('user.companyAuthentication')">
          <company-info></company-info>
        </el-tab-pane>
<!--        <el-tab-pane  :label="$t('route.info')" >-->
<!--          <upd-user-info></upd-user-info>-->
<!--        </el-tab-pane>-->
      </el-tabs>
    </el-row>
  </basic-container>
</template>

<script>



  export default {
      components: {
        CompanyInfo: resolve => require(['@/views/company/detail'], resolve)
      },
      data() {
        return {

        }
      },
  };
</script>

<style>
</style>
