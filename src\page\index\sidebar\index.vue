<template>
  <div class="sgs-header-sidebar">
    <div v-if="validatenull(menu)" style="padding-top: 33px;color: #999;">{{ $t('menuTip') }}</div>
    <!--:collapse="keyCollapse"-->
    <el-menu unique-opened class="el-menu-demo v-menu-vertical sgs-header-menu" :default-active="nowTagValue" mode="horizontal"
      :show-timeout="200" :collapse="keyCollapse" background-color="#3c515b" text-color="#fff" active-text-color="#F60">
      <div class="navigation-item-wrapper">
        <navigation-item 
          v-for="(menuItem, i) in menu" 
          :screen="screen" 
          :props="website.menu.props" 
          :key="i"
          :item="menuItem" 
          class="navigation-item scroll" />
      </div>
    </el-menu>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "sidebar",
  components: {
    sidebarItem: resolve => require(['./sidebarItem'], resolve),
    logo: resolve => require(['../logo'], resolve),
    navigationItem: resolve => require(['./v-menu-item'], resolve)
  },
  data() {
    return {

    };
  },
  created() {
    //debugger
    this.$store.dispatch("GetMenu", this.language).then(data => {
      if (data.length === 0) return;
      this.$router.$avueRouter.formatRoutes(data, true);
    });
  },
  computed: {
    ...mapGetters(["website", "menu", "tag", "keyCollapse", "screen", "language"]),
    nowTagValue: function () {
      return this.$router.$avueRouter.getValue(this.$route);
    }
  },
  mounted() {

  },
  methods: {
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
    },
    removeWrapper() {
      this.$refs['sidebarList'].forEach(component => {
        const parent = component.$el.parentNode;
        parent.append(component.$el.children);
        [component.$el.children].forEach(child => component.$el.removeChild(child));
      })
      console.log('sidebarList', this.$refs['sidebarList'])
    }
  }
};
</script>
<style lang="scss" scoped>
  .sgs-header-sidebar{
    .el-menu--horizontal{}
  }
.navigation-item-wrapper {
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-wrap: nowrap;
}

.navigation-item {
  height: 100%;
  overflow: auto;
}

#drap-line {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 90%;
  margin-top: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.75);
  cursor: e-resize;
  border-radius: 5px;
}

.scroll::-webkit-scrollbar {
  width: 0px;
  height: 10px;
  transition: all 0.5s ease;
}

.v-menu-vertical:not(.el-menu--collapse) {
  min-height: 400px;
}

.v-nav-control-btn-wrapper {
  position: absolute;
  left: 0;
  top: 0;
}

.v-nav-control-btn-wrapper,
.v-load-btn {
  width: 100%;
  padding: 2px;
}

.v-load-btn {
  position: absolute;
  bottom: 0;
  left: 0;
}

.v-load-btn span {
  font-size: 0.8rem;
}

.v-menu-vertical {
  width: 100%;
  height: 100%;
}
</style>

