<template>
    <div class="dynamicForms" id="dynamicForms">
        Vue页面预览 这个页面组件未来会做很多动态组件的渲染
        目前只有一个固定的在trfDetail.vue 页面
    </div>
</template>

<script>
    export default {
        name: "dynamicForms",
        data() {
            return {}
        },
        methods: {},
        mounted() {
        },
        created() {
        },
        watch: {},
        props: {},
        components: {}
    }
</script>

<style scoped>
    .dynamicForms {
    }
</style>