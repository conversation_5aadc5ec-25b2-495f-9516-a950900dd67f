@import "@/styles/unit.scss";

.sgs-left,
.sgs-header,
.sgs-top,
.sgs-logo,
.sgs-layout .login-logo,
.sgs-main {
  transition: all 0.3s;
}
.sgs-contail {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  // background: #f7f7f7 url('/img/workbench_bg.png') no-repeat right top 0px / 100%;
  // background-size: 100%;
  padding-top: 104px;
}

.sgs-left {
  position: fixed;
  left: 0;
  top: 0;
  width: 240px;
  height: 100%;
  z-index: 1025;
}

.sgs-header {
  top: 0;
  position: fixed;
  z-index: 1999;
  width: 100%;
  background-color: #3c515b;
  box-sizing: border-box;
}

.sgs-main {
  background-color: #f5f5f5;
  // background: #f7f7f7 url("/img/workbench_bg.png") no-repeat right top -180px / 100%;
  min-width: 1000px;
  // padding-top: 104px;
  // margin-bottom: 60px;
  //position: absolute;
  // padding-bottom: 20px;
  //width: calc(100% - 240px);
  //height: calc(100% - 70px);
  box-sizing: border-box;
  //overflow: hidden;
  min-height: calc(100% - 102px);
}

.sgs-bottom {
  text-align: center;
  margin: 5px;
}

.sgs-view {
  margin: 0px 30px 30px;
  // padding: 24px 32px;
  // background-color: #fff;
}

.sgs-footer {
  height: 70px;
  text-align: center;
  vertical-align: middle;
  line-height: 70px;
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  z-index: 1000;
  background-color: #fff;
  border-top: 1px solid #e6e6e6;
}

.sgs-shade {
  position: fixed;
  display: none;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1024;
  &--show {
    display: block;
  }
}

.sgs-box {
  margin-top: $module-margin-vertical;

  .sgs-title {
    font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
      "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    // font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif;
    font-weight: bold;
    text-rendering: optimizeLegibility;
    text-transform: uppercase;
    // border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }
}

.sgs-form {
  .el-form-item {
    // border: 1px solid #e4e4e4;
    // border-radius: 5px;
    margin-bottom: 16px;
    .el-input__inner,
    .el-textarea__inner {
      padding-left: 11px;
    }
    .el-icon-date {
      margin-top: -3px;
    }
  }

  .el-form-item__label {
    padding: 0;
  }

  label {
    margin-bottom: 0px;
  }
}

.line {
  text-align: center;
}

.sgs-group {
  position: relative;

  h2,
  h3,
  h4 {
    margin: 0;
    padding: 0;
    // border-bottom: 1px solid #ddd;
    color: #1b1b1b;
    font-weight: bold;
    text-rendering: optimizeLegibility;
    text-transform: uppercase;
  }
  h3 {
    // margin-bottom: 16px;
  }

  .right {
    position: absolute;
    top: 0px;
    margin: 3px 0px;
    right: 0px;
  }
}

.avue-group__header {
  h1,
  h2,
  h3,
  h4 {
    font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
      "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    // font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif;
    margin: 10px 0px;
    padding-bottom: 16px;
    color: #424242;
    font-weight: bold;
    text-rendering: optimizeLegibility;
    text-transform: uppercase;
    font-size: 24px;
  }
}

.pull-right {
  float: right !important;
}

@media screen and (max-width: 992px) {
  $width: 240px;
  // ele的自适应
  .el-dialog,
  .el-message-box {
    width: 98% !important;
  }
  //登录页面
  .login-left {
    display: none !important;
  }
  .login-logo {
    padding-top: 30px !important;
    margin-left: -30px;
  }
  .login-weaper {
    margin: 0 auto;
    width: 96% !important;
  }
  .login-border {
    border-radius: 5px;
    padding: 40px;
    margin: 0 auto;
    float: none !important;
    width: 100% !important;
  }
  .login-main {
    width: 100% !important;
  }
  //主框架
  .sgs-tags {
    display: none;
  }
  .sgs-logo {
    left: -$width;
  }
  .sgs-main {
    left: 0;
    width: 100%;
  }
  .sgs-header {
    margin-bottom: 15px;
    padding-left: 15px;
  }
  .top-bar__item {
    display: none;
  }
}
