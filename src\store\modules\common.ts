import { setStore, removeStore } from '@/utils/store'
import website from '@/config/website'

// 定义 state 的类型
interface CommonState {
  language: string
  isCollapse: boolean
  isFullScreen: boolean
  isShade: boolean
  screen: number
  isLock: boolean
  showTag: boolean
  showDebug: boolean
  showCollapse: boolean
  showSearch: boolean
  showLock: boolean
  showFullScreen: boolean
  showTheme: boolean
  showMenu: boolean
  showColor: boolean
  colorName: string
  themeName: string
  lockPasswd: string
  website: typeof website
}

const common = {
  state(): CommonState {
    return {
      language: 'en-US',
      isCollapse: false,
      isFullScreen: false, 
      isShade: false,
      screen: -1,
      isLock: false,
      showTag: true,
      showDebug: true,
      showCollapse: true,
      showSearch: true,
      showLock: true,
      showFullScreen: true, 
      showTheme: true,
      showMenu: true,
      showColor: true,
      colorName: '#FF6600',
      themeName: 'theme-default',
      lockPasswd: '',
      website: website,
    }
  },
  mutations: {
    SET_LANGUAGE: (state: CommonState, language: string) => {
      state.language = language
      setStore({
        name: 'language',
        content: state.language,
      })
    },
    SET_SHADE: (state: CommonState, active: boolean) => {
      state.isShade = active
    },
    SET_COLLAPSE: (state: CommonState) => {
      state.isCollapse = !state.isCollapse
    },
    SET_FULLSCREEN: (state: CommonState) => {
      state.isFullScreen = !state.isFullScreen
    },
    SET_LOCK: (state: CommonState) => {
      state.isLock = true
      setStore({
        name: 'isLock',
        content: state.isLock,
        type: 'session',
      })
    },
    SET_SCREEN: (state: CommonState, screen: number) => {
      state.screen = screen
    },
    SET_COLOR_NAME: (state: CommonState, colorName: string) => {
      state.colorName = colorName
      setStore({
        name: 'colorName',
        content: state.colorName,
      })
    },
    SET_THEME_NAME: (state: CommonState, themeName: string) => {
      state.themeName = themeName
      setStore({
        name: 'themeName',
        content: state.themeName,
      })
    },
    SET_LOCK_PASSWD: (state: CommonState, lockPasswd: string) => {
      state.lockPasswd = lockPasswd
      setStore({
        name: 'lockPasswd',
        content: state.lockPasswd,
        type: 'session',
      })
    },
    CLEAR_LOCK: (state: CommonState) => {
      state.isLock = false
      state.lockPasswd = ''
      removeStore({
        name: 'lockPasswd',
        type: 'session',
      })
      removeStore({
        name: 'isLock',
        type: 'session',
      })
    },
  },
}

export default common
