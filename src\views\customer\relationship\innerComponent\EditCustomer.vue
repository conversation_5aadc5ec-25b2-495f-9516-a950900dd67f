<template>
    <div class="smart_views_customer_relationship_component_EditCustomer" id="smart_views_customer_relationship_component_EditCustomer">
        <el-form :model="customerForm" label-width="160px" ref="formRef" label-position="top">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item :label="t('scm.company.en')" prop="companyNameEN">
                        <el-autocomplete v-model="customerForm.companyNameEN"
                                         :fetch-suggestions="remoteTianyanchaCustomer"
                                         :trigger-on-focus="false"
                                         maxlength="200"
                                         show-word-limit
                                         clearable
                                         @select="handleSelect"
                                         value-key="companyName">
                            <template #default="{item}">
                                {{item.companyNameFull}}
                            </template>
                        </el-autocomplete>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item :label="t('scm.company.cn')" prop="companyNameCN">
                        <el-input maxlength="200" show-word-limit v-model="customerForm.companyNameCN" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item :label="t('scm.company.addressEn')" prop="companyAddressEN">
                        <el-input clearable maxlength="300" show-word-limit v-model="customerForm.companyAddressEN" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item :label="t('scm.company.addressCn')" prop="companyAddressCN">
                        <el-input clearable maxlength="300" show-word-limit v-model="customerForm.companyAddressCN" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <p class="card-title">{{ t('scm.company.businessContacts') }}</p>
        <EditContact ref="contactFormRef" :contacts="customerForm.contacts"></EditContact>
        <el-row style="padding-top: 20px">
            <el-col style="text-align: center">
                <el-button type="primary" plain class="footer_btn" @click="submitForm">{{ t('scm.btnName.save') }}</el-button>
                <el-button plain class="footer_btn" @click="cancelForm">{{ t('scm.btnName.cancel') }}</el-button>
            </el-col>
        </el-row>
    </div>
</template>


<script setup>
import { ref, reactive } from 'vue';
import { ElNotification } from 'element-plus';
import api from '@/api/customerRelation'
import EditContact from "./EditContact.vue";
import {useI18n} from 'vue-i18n';
const { t } = useI18n();

defineOptions({
    name:'EditCustomer'
})

const contactFormRef = ref();
const emit = defineEmits(['cancelDia','saveSuccess']);

const customerForm = reactive({
    companyNameEN: '',
    companyNameCN: '',
    companyAddressEN: '',
    companyAddressCN: '',
    contacts: []
});

const companyInfo = ref({});

const formRef = ref(null);

const prop = defineProps({
    useParent:{
        type: Boolean,
        default: false,
    },
    roleType:{
        type:String,
        default:''
    },
    refParentId:{
        type:String,
        default:''
    },
    childCompanyId:{
        type:String,
        default:''
    }
})


const submitForm = () => {
    formRef.value.validate(async (valid) => {
        const contactsList = await contactFormRef.value.getContactSaveData()
        if (valid) {
            //组装数据
            let {companyNameEN,
                companyNameCN,
                companyAddressEN,
                companyAddressCN} = customerForm;
            let param = {
                smartCustomerId: "",
                nameList: [
                    {companyName: companyNameEN,languageId: "en-US"},
                    {companyName:companyNameCN,languageId: "zh-CN"}
                ],
                addressList: [
                    {companyAddress: companyAddressEN,languageId: "en-US"},
                    {companyAddress: companyAddressCN,languageId: "zh-CN"}
                ],
                contactsList,
                ...companyInfo.value
            }
            applyRelation(param);
        } else {
            ElNotification.error('Please fill in the required fields');
            return false;
        }
    });
};

const applyRelation = (params)=>{
    let param = {
        refRootId:'',
        refParentId: prop.refParentId,
        header: {
            child: {
                ...params,
                companyType:'company',
                roleType: prop.roleType
            }
        },
        domain: "CUSTOMER"
    }
    if(prop.useParent){
        param.header['parent'] =  param.header.child;
        delete param.header.child;
    }
    api.applyRelation(param).then(res=>{
        if(res.status==200){
            emit('saveSuccess')
            cancelForm();
        }
    })
}

const cancelForm = () => {
    emit('cancelDia')
};

const companyListForDetail = ref([]);
const remoteTianyanchaCustomer = (keyWord, cb) => {
    if(!keyWord){
        cb([])
        return;
    }
    let param = {
        companyName : keyWord,
    }
    api.queryCompanyList(param).then(res=>{
        if(res.status==200 && res.data){
            let data = res.data;
            data.forEach(da=>{
                let {nameList} = da;
                let {companyId,companyName,companyNameFull} = nameList[0];
                da['companyId'] = companyId;
                da['companyName'] = companyName;
                da['companyNameFull'] = companyNameFull;
            })
            companyListForDetail.value = data || [];
            cb(companyListForDetail.value);
        }else{
            cb([])
        }
    })
};

const handleSelect = (item) => {
    companyInfo.value = item;
};

</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_component_EditCustomer {
  padding: 20px;
  .el-table {
    margin-top: 10px;
  }
  .footer_btn {
    min-width: 80px;
  }
  p.card-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
    padding: 10px 0 5px 0;
  }
}
</style>

