import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path-browserify'
import config from './package.json'
import viteSentry from 'vite-plugin-sentry'
import vueI18n from '@intlify/unplugin-vue-i18n/vite'

// https://vitejs.dev/config/
export default defineConfig((mode) => {
    const currentEnv = mode.mode
    const sentryConfig = {
        configFile: './.sentryclirc',
        release: config.version, // 版本号
        deploy: {
            env: currentEnv,
        },
        skipEnvironmentCheck: true, // 可以跳过环境检查
        sourceMaps: {
            include: ['./dist/assets'],
            ignore: ['node_modules'],
            urlPrefix: '~/assets', // 注意这里设置正确，否则sourcemap上传不正确
        },
    } as any

    return {
        base: '/web/',
        plugins: [
            vue(),
            vueI18n({
                runtimeOnly: true,
                compositionOnly: true,
                include: [path.resolve(__dirname, './src/locales/**')],
            }),
            currentEnv === 'production' ? viteSentry(sentryConfig) : null,
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, '/src'),
                assets: path.resolve(__dirname, '/src/assets'),
                components: path.resolve(__dirname, '/src/components'),
                request: path.resolve(__dirname, '/src/request'),
                router: path.resolve(__dirname, '/src/router'),
                store: path.resolve(__dirname, '/src/store'),
                type: path.resolve(__dirname, '/src/type'),
                util: path.resolve(__dirname, '/src/utils'),
                views: path.resolve(__dirname, '/src/views'),
            },
        },
        css: {
            preprocessorOptions: {
                scss: {
                    api: 'modern',
                    //   additionalData: '@import "src/assets/style/mixin.scss";',
                    // additionalData: '@import "./src/assets/style/element-variarbles.scss";'
                },
            },
            modules: {
                // 开启 CSS 模块化
                localsConvention: 'camelCase',
            },
        },
        server: {
            host: '0.0.0.0',
            port: 9537, // 自定义端口号
            open: true, // 启动后是否直接打开浏览器
            proxy: {
                '/apirouter/api': {
                    target: 'https://uat.sgsmart-online.com/', // 代理的目标地址
                    changeOrigin: true,
                    secure: false,
                },
                '/api': {
                    target: 'https://test.sgsmart-online.com',
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api/, '/api')
                }
            },
        },
        build: {
            // 开启sourcemap
            sourcemap: true,
            minify: 'terser',
            // 删除console预计debugger
            terserOptions: {
                compress: {
                    drop_console: true,
                    drop_debugger: true,
                },
            },
        },
    }
})
