<template>
    <basic-container class="smart_document_auth_list" id="smart_document_auth_list">
        <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.authorization')}}</el-breadcrumb-item>
        </el-breadcrumb>
        <el-row>
            <el-row>
                <el-button type="primary" @click="addRow" size="medium">{{$t('operation.add')}}</el-button>
                <el-button type="primary" @click="downloadTemplate" size="medium">{{$t('form.exportTemplate')}}</el-button>
                <el-button type="primary" @click="importTemplate" size="medium">{{$t('operation.batchImportAuthorization')}}</el-button>
            </el-row>
            <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                    size="medium">
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
                <el-table-column
                        fixed
                        prop="documentTitle"
                        :label="$t('documentLibrary.nameFull')"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="customerNo"
                        width="150"
                        :label="$t('register.bossNo')">
                </el-table-column>
                <el-table-column
                        prop="customerNameZh"
                        :label="$t('customer.name')">
                </el-table-column>
                <el-table-column
                        prop="productLineName"
                        width="180"
                        :label="$t('productLine.name')">
                </el-table-column>
                <el-table-column
                        prop="effectiveDate"
                        :label="$t('common.effectiveDate')"
                        width="120">
                </el-table-column>
                <el-table-column
                        prop="invalidDate"
                        :label="$t('common.invalidDate')"
                        width="120">
                </el-table-column>
                <el-table-column
                        :label="$t('operation.title')"
                        width="150"
                        align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="detailRow(scope.row)" size="small" icon="el-icon-edit">
                            {{$t('operation.edit')}}
                        </el-button>
                        <el-button @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-delete">
                            {{$t('operation.remove')}}
                        </el-button>
                    </template>
                </el-table-column>

            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-row>
        <el-row style="margin-top: 10px;text-align: center">
            <!--<el-button :disabled="isDisabled">{{$t('operation.cancel')}}</el-button>-->
            <el-button type="primary" style="width: 100px" @click="goBack">{{$t('operation.goBack')}}</el-button>
        </el-row>
        <el-drawer :title="title" :before-close="closeDrawer" :visible.sync="dialogFormVisible" size="60%">
            <el-form ref="form" :model="form" label-width="200px" label-position="left" size="medium" class="sgs-form">
                <el-form-item :label="$t('productLine.name')" prop="productLineCode"
                              :rules="{ required: true, message: $t('trf.validate.requiredBlur'), trigger: 'blur' }">
                    <el-select v-model="form.productLineCode" filterable clearable style="width:100%"
                               @change="selecProductLineCodeChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                   :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('register.bossNo')" prop="customerNo"
                              :rules="{ required: false, message: $t('trf.validate.requiredBlur'), trigger: 'blur' }">
                    <el-input :placeholder="$t('register.bossNo')" maxlength="10" v-model="form.customerNo">
                        <template #append>
                            <el-button icon="el-icon-search" @click="searchCustomer"></el-button>
                        </template>
                    </el-input>
                </el-form-item>

                <el-form-item :label="$t('customer.name')" style="margin-bottom: 5px">
                    <el-input readonly="readonly" :disabled="true" v-model="form.customerName">
                    </el-input>

                    <!--<el-select v-model="form.customerId" clearable filterable style="width:100%">
                        <el-option v-for="(customer,index) in customerData" :label="customer.customerNameZh"
                                   :value="customer.id"></el-option>
                    </el-select>-->
                </el-form-item>
                <span style="color: red">* {{$t('authorization.validate.customer')}}</span>
                <el-form-item :label="$t('common.effectiveDate')" style="margin-bottom: 5px">
                    <el-col :span="11">
                        <el-date-picker type="date" :placeholder="$t('authorization.selected')"
                                        v-model="form.effectiveDate" style="width: 80%;"
                                        format="yyyy-MM-dd"
                                        :value="form.effectiveDate"
                                        @input="val => updateStartDate(val)"
                                        :picker-options="selectedTime">

                        </el-date-picker>
                    </el-col>
                    <el-col class="line" :span="2">---</el-col>
                    <el-col :span="11">
                        <el-date-picker :placeholder="$t('authorization.selected')" v-model="form.invalidDate"
                                        style="width: 80%;"
                                        format="yyyy-MM-dd"
                                        :value="form.invalidDate"
                                        @input="val => updateEndDate(val)"
                                        :picker-options="selectedTime"></el-date-picker>
                    </el-col>
                </el-form-item>
                <span style="color: red">* {{$t('authorization.validate.date')}}</span>
                <el-form-item :label="$t('common.status.title')">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">{{$t('common.status.enable')}}</el-radio>
                        <el-radio :label="0">{{$t('common.status.disable')}}</el-radio>
                        <el-radio :label="2">{{$t('authorization.visibleOnly')}}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <div class="sgs-bottom">
                    <el-button @click="closeDrawer">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm()" :loading="btnSubmit">{{$t('operation.submit')}}
                    </el-button>
                </div>
            </el-form>
        </el-drawer>
        <el-dialog
                :visible.sync="showUploadFile"
                v-dialog-drag
                width="70%"
                :title="$t('operation.batchImportAuthorization')"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                v-loading="loadingPage"
                :element-loading-text="fullPageLoadingText"
        >
            <div v-if="showUploadFile" >
                <input type="file" accept=".xlsx" @change="changeUploadFile" style="display: none" name="file" id="uploadFileInput"></input>
                <el-row>
                    <el-col :span="12">
                        <el-button size="medium" type="primary" @click="chooseFile">{{$t('documentLibrary.chooseFile')}}</el-button>
                        <span style="padding-left: 10px">{{uploadFileName}}</span>
                        <i v-if="uploadFileName" class="el-icon-circle-close" @click="removeFile" style="cursor: pointer; padding-left: 10px"></i>
                    </el-col>
                    <el-col :span="12" style="text-align: right">
                        <el-button size="medium" type="primary" @click="clearErrorData">{{$t('documentLibrary.clearErrorData')}}</el-button>
                    </el-col>
                </el-row>
                <el-table
                    :data="preUploadDataList"
                    size="mini"
                    border
                    fit
                    ref="upload_table"
                    class="upload_table"
                    :row-class-name="uploadFileTableRowClass"
                    max-height="450"
                >
                    <el-table-column show-overflow-tooltip min-width="250" prop="productLineName" :label="$t('productLine.name')"></el-table-column>
                    <el-table-column show-overflow-tooltip min-width="200" prop="customerNo" :label="$t('register.bossNo')"></el-table-column>
                    <el-table-column show-overflow-tooltip min-width="200" prop="customerName" :label="$t('customer.name')"></el-table-column>
                    <el-table-column show-overflow-tooltip min-width="200" prop="effectiveDate" :label="$t('common.effectiveDate')"></el-table-column>
                    <el-table-column show-overflow-tooltip min-width="200" prop="invalidDate" :label="$t('common.invalidDate')"></el-table-column>
                    <el-table-column show-overflow-tooltip min-width="200" prop="statusName" :label="$t('common.status.title')"></el-table-column>
                    <el-table-column show-overflow-tooltip min-width="280" prop="tips" :label="$t('tip')">
                        <template slot-scope="{row}">
                            <span style="color:#ff6600">{{row.tips}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="80" prop="pageId" fixed="right">
                        <template #header>
                            {{$t('training.Action')}}
                        </template>
                        <template slot-scope="{row}">
                            <el-button type='text' icon="el-icon-delete" @click="removeUploadData(row.pageId)"></el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div slot="footer">
                <el-button @click="cancelImport" type="primary">{{$t('operation.cancel')}}</el-button>
                <el-button @click="batchImport" v-if="showConfirmBtn" type="primary" v-loading="importLoading">{{$t('operation.confirm')}}</el-button>
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
    import {getPageList, add, detail, remove,batchAdd,checkDataBeforeSubmit} from "@/api/document/authorization";
    import {getCustomerGroup, getProductLine} from "@/api/common/index";
    import {getList, getCustomerNotPageList,getCustomerByBossNos} from "@/api/customer/customerRegister";
    import moment from 'moment'
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {validatenull} from "../../../util/validate";
    import {mapGetters} from "vuex";
    import ExcelJs from "exceljs";
    import XLSX from 'xlsx'
    export default {

        data() {
            return {
                loadingPage:false,
                fullPageLoadingText:'Loading...',
                LanguageEnums:LanguageEnums,
                loading: false,
                tableData:[],
                name: "list",
                title: '',
                form: {documentId: this.$route.query.documentId},
                query: {documentId: this.$route.query.documentId},
                documentId: this.$route.query.documentId,
                btnSubmit: false,
                dialogFormVisible: false,
                showUploadFile: false,
                importLoading: false,
                showConfirmBtn: true,
                customerGroupData: [],
                productLineData: [],
                customerData: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                selectedTime: {
                    disabledDate: (time) => {
                        return time.getTime() < this.form.effectiveDate;
                    },
                },
                preUploadDataList:[],
                uploadFileName:'',
            }
        },
        watch: {
          //监听语言变化
          language: function (newVal) {
            this.onLoad(this.page);
          },
        },
        methods: {
            goBack() {
                this.$router.push({path: '/document/library/list', query: {}});
            },
            async searchCustomer() {
                if (!validatenull(this.form.customerNo)) {
                    if (this.form.customerNo.trim().length < 4) {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('documentLibrary.validate.boosLengthErr'),
                            type: 'warning'
                        });
                        return false;
                    }
                    var params = {};
                    params.bossNo = this.form.customerNo;
                    let res = await getCustomerNotPageList(params);
                    const data = res.data.data;
                    if (validatenull(data)) {
                        this.$set(this.form, 'customerId', null);
                        this.$set(this.form, 'customerName', null);
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('documentLibrary.validate.noCustomerTip'),
                            type: 'warning'
                        });
                        return false;
                    } else {
                        var customerObj = data[0];
                        if (!validatenull(customerObj)) {
                            this.$set(this.form, 'customerId', customerObj.id);
                            this.$set(this.form, 'customerName', customerObj.customerNameZh);
                        }
                        return true;
                    }
                } else {
                    this.$set(this.form, 'customerId', null);
                    this.$set(this.form, 'customerName', null);
                }
                return true;
            },
            closeDrawer(done) {
                this.$refs['form'].resetFields();
                this.dialogFormVisible = false;
            },
            async updateStartDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.$set(this.form, 'effectiveDate', date);
            },
            async updateEndDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.$set(this.form, 'invalidDate', date);
            },
            onLoad(page, params = {}) {
                this.loading = true;
                //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                  languageId=LanguageEnums.EN.code;
                }else{
                  languageId=LanguageEnums.CN.code;
                }
                this.query.languageId=languageId;
                getPageList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.tableData = [];
                    this.loading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            async submitForm() {
                /*  if (validatenull(this.form.customerId) && ) {
                      this.$notify({
                          title: this.$t('tip'),
                          message: this.$t('documentLibrary.validate.noCustomerTip'),
                          type: 'warning'
                      });
                      return false;
                  }*/
                //如果录入了bossNo则做校验处理，未录入则不处理
                if (validatenull(this.form.bossNo)) {
                    let result = await this.searchCustomer();
                    debugger;
                    if (!result) {
                        return false;
                    }
                }

                this.btnSubmit = true;
                this.form.documentId = this.documentId;

                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        add(this.form).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.btnSubmit = false;
                            this.dialogFormVisible = false;
                            this.onLoad(this.page);
                        }).catch(() => {
                            this.btnSubmit = false;
                        });
                    } else {
                        this.btnSubmit = false;
                    }
                });

            },
            detailRow(row) {
                this.title = this.$t('authorization.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form = res.data.data;
                });
            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            selecProductLineCodeChange(val) {
                let obj = {};
                obj = this.productLineData.find((item) => {
                    return item.productLineCode === val;
                });
                this.form.productLineName = obj.productLineName;
            },
            customerGroupCodeChange(val) {
                let obj = {};
                obj = this.customerGroupData.find((item) => {
                    return item.customerGroupCode === val;
                });
                this.form.customerGroupName = obj.customerGroupName;
            },
            async downloadTemplate(){
                //XLSX 导出，title
                const wookbook = new ExcelJs.Workbook();
                const workSheet = wookbook.addWorksheet("Template");
                let props = ['productLineCode','customerNo','effectiveDate','invalidDate','status'];
                let titles = ['Product Line','Boss No.','Effective date','Invalid date','Status'];
                workSheet.getCell("A1").value="document_auth_template"
                //从C列开始，前两列隐藏
                for(let i = 0;i<titles.length;i++){
                    let propChart = String.fromCharCode(67+i) + "1";
                    let char = String.fromCharCode(67+i) + "2";
                    workSheet.getCell(propChart).value = props[i];
                    workSheet.getCell(char).value = titles[i];
                }
                //设置C列宽度为120
                workSheet.getColumn(3).width = 40;
                //设置D E F G列宽度为16
                workSheet.getColumn(4).width = 16;
                workSheet.getColumn(5).width = 16;
                workSheet.getColumn(6).width = 16;
                workSheet.getColumn(7).width = 16;
                //隐藏不可编辑区域
                workSheet.getRow(1).hidden = true;
                workSheet.getColumn(1).hidden =true;
                workSheet.getColumn(2).hidden =true;
                //第二个sheet 设置下拉框属性
                const productLineCodeList = this.productLineData.map(p=>p.productLineCode);
                const productLineNameList = this.productLineData.map(p=>p.productLineName);
                const statusList  = ['Enable','Disable','Visible only'];
                const statusCodeList  = [1,0,2];
                const ws2 = wookbook.addWorksheet('productsheet');
                const ws3 = wookbook.addWorksheet('statussheet');
                ws2.getColumn("A").values = productLineNameList;
                ws2.getColumn("B").values = productLineCodeList;
                ws3.getColumn("A").values = statusList;
                ws3.getColumn("B").values = statusCodeList;

                //将下拉列 绑定到sheet2
                const columnA = workSheet.getColumn("C");
                for(let i=3;i<=502;i++){
                    let cellC =  workSheet.getCell("C"+i);
                    let cellG =  workSheet.getCell("G"+i);
                    cellC.dataValidation = {
                        type:'list',
                        allowBlank:false,
                        formulae:[`=productsheet!$A:$A`]
                    }
                    cellG.dataValidation = {
                        type:'list',
                        allowBlank:false,
                        formulae:[`=statussheet!$A:$A`]
                    }
                }
                // 设置C列显示A列对应的名称
                const enumColA = workSheet.getColumn("A");
                enumColA.eachCell({includeEmpty:true},function(cell,rowNumber){
                    if(rowNumber>2){
                        cell.value = {
                            formula: `=VLOOKUP(C${rowNumber},productsheet!$A:$B,2,FALSE)`
                        };
                    }
                });
                // 设置G列显示B列对应的名称
                const enumColB = workSheet.getColumn("B");
                enumColB.eachCell({includeEmpty:true},function(cell,rowNumber){
                    if(rowNumber>2){
                        cell.value = {
                            formula: `=VLOOKUP(G${rowNumber},statussheet!$A:$B,2,FALSE)`
                        };
                    }
                });
                //锁定第一行，不允许编辑
                workSheet.getRow(1).eachCell({includeEmpty:true},function(cell,rowNumber){
                    cell.locked = true;
                });

                //设置所有列的单元格格式为文本
                workSheet.protect(Math.random()+"");
                ws2.protect(Math.random()+"");
                ws3.protect(Math.random()+"");
                //隐藏 ws2 和 ws3 两个sheet
                ws2.hidden = true;
                ws3.hidden = true;
                //第3行到502行 可以编辑
                for(let colIndex = 0;colIndex<7;colIndex++){
                    //ABCDEFG
                    let cellSymbol = String.fromCharCode(65+colIndex);
                    //A3,A4,A5...A502 500行
                    for(let rowIndex = 3;rowIndex<=502;rowIndex++){
                        let cell = cellSymbol + rowIndex;
                        let rowCell = workSheet.getCell(cell);
                        rowCell.protection = {locked:false}
                        //设置单元格格式为文本
                        rowCell.numFmt = '0';
                        //设置单元格格式为日期
                        if(colIndex==4 || colIndex==5){
                            rowCell.numFmt = 'yyyy-mm-dd';
                        }
                        //设置单元格左对齐
                        rowCell.alignment = {
                            horizontal:'left',
                            vertical:'middle'
                        }
                    }
                }
                let buffer = await wookbook.xlsx.writeBuffer();
                let blob = new Blob([buffer],{
                    type:'application/vnd.ms-excel'
                })

                let url = URL.createObjectURL(blob);
                let link = document.createElement("a");
                link.href=url;
                link.download="Authorization Template.xlsx";
                link.click();
                URL.revokeObjectURL(url);
                link.remove();
            },
            importTemplate(){
                this.showConfirmBtn = true;
                this.showUploadFile = true;
            },
            addRow() {
                this.title = this.$t('authorization.title.add');
                this.form = {};
                this.dialogFormVisible = true;
            },
            removeUploadData(pageId){
                //根据pageId ，findindex 找到下标，使用数组splice 删除preUploadDataList数据
                let index = this.preUploadDataList.findIndex(p=>p.pageId==pageId);
                if(index!=-1){
                    this.preUploadDataList.splice(index,1);
                }
            },
            async changeUploadFile(event){
                let {files} = event.target;
                if(!files || files.length==0){
                    return;
                }
                let file = files[0];
                const wb = new ExcelJs.Workbook();
                try{
                    await wb.xlsx.load(file);
                }catch (e){
                    this.$notify.error(this.$t("documentLibrary.validate.noTemplate"));
                    return;
                }
                const worksheet = wb.getWorksheet(1);
                //第一行prop 校验
                const props = ['productLineCode','customerNo','effectiveDate','invalidDate','status'];
                let notSame = false;
                //读取第一行 3-8列
                const row1 = worksheet.getRow(1);
                if('document_auth_template' != row1.getCell('A').value || row1.getCell('B').value){
                    notSame = true;
                }
                row1.eachCell((cell,colNumber)=>{
                    if(colNumber>2){
                        if(!notSame){
                            notSame = props[colNumber-3] != cell.value;
                        }
                    }
                });
                if(notSame){
                    this.$notify.error(this.$t("documentLibrary.validate.noTemplate"));
                    this.removeFile();
                    return;
                }
                //获取worksheet 数据的行数
                const rowCount = worksheet.rowCount;
                //总行数大于502行 ，提示“Only import 500 price data”
                if(rowCount>502){
                    this.$notify.error(this.$t("documentLibrary.validate.numberLength500"));
                    return;
                }
                //从第三行开始读取数据
                this.uploadFileName = file.name;
                let formDataList = []
                let bossNos = [];
                for(let i=3;i<=rowCount;i++){
                    const row = worksheet.getRow(i);
                    const productLineName = row.getCell(3).value || '';
                    const customerNo = row.getCell(4).value || '';
                    let effectiveDate = row.getCell(5).value || '';
                    let invalidDate = row.getCell(6).value || '';
                    const statusName = row.getCell(7).value || '';
                    if(!productLineName && !customerNo && !effectiveDate && !invalidDate && !statusName){
                        continue;
                    }
                    const productLine = this.productLineData.find(p=>p.productLineName==productLineName.trim()) || {};
                    let productLineCode = productLine.productLineCode || '';
                    let tips = '';
                    let hasTips = false;

                    if(!productLineCode){
                        tips = this.$t('documentLibrary.validate.productLineErr');
                    }
                    let status = 1;
                    if(statusName){
                        const statusMap  = {'Enable':1,'Disable':0,'Visible only':2};
                        status = statusMap[statusName];
                        status = ![0,1,2].includes(status)?1:status;
                    }

                    if(effectiveDate){
                        try {
                            effectiveDate = moment(effectiveDate).format('YYYY-MM-DD')
                        }catch (e){}
                    }
                    if(invalidDate){
                        try {
                            invalidDate = moment(invalidDate).format('YYYY-MM-DD')
                        }catch (e){}
                    }
                    if(customerNo && (customerNo+'').trim().length<4){
                        tips = tips? tips : this.$t('documentLibrary.validate.boosLengthErr');
                    }
                    if(customerNo && (customerNo+'').trim().length>=4){
                        bossNos.push(customerNo);
                    }
                    hasTips = !!tips;
                    let pageId = Math.random();
                    let documentId = this.documentId;
                    formDataList.push({documentId,send:0,pageId,tips,hasTips, productLineName,customerNo,effectiveDate,invalidDate,status,statusName,productLineCode});
                }
                if(formDataList.length==0){
                    return;
                }
                this.loadingPage = true;
                let bossNoCustomerMap = {};
                if(bossNos.length>0){
                    bossNos = [...new Set(bossNos)];
                    //获取对应的customer信息
                    let bossNoResult = await getCustomerByBossNos({bossNos}).catch((err) => {
                        //this.$notify.error(this.$t("documentLibrary.validate.interfaceError"));
                        this.loadingPage = false;
                        this.removeFile();
                        return;
                    });
                    //解析返回数据中的customerId customerName
                    if(bossNoResult.status==200 && bossNoResult.data && bossNoResult.data.data){
                        let bossDataList = bossNoResult.data.data || [];
                        bossDataList.forEach(item=>{
                            let {bossNo} = item;
                            bossNoCustomerMap[bossNo] = item;
                        })
                    }else{
                        this.$notify.error(this.$t("documentLibrary.validate.interfaceError"));
                        this.loadingPage = false;
                        this.removeFile();
                        return;
                    }
                }
                let repeatDataMap = {};
                formDataList.forEach(da=>{
                    let {customerNo,hasTips,pageId,productLineCode} = da;
                    if(hasTips){
                        return;
                    }
                    if(!customerNo){
                        let pageIds = repeatDataMap[productLineCode+customerNo];
                        if(pageIds && pageIds.length>0){
                            this.$set(da,'hasTips',true);
                            this.$set(da,'tips',this.$t('documentLibrary.validate.repeatDataTip'));
                            return;
                        }
                        repeatDataMap[productLineCode+customerNo] = [pageId];
                        this.$set(da,'send',1);
                        return
                    }
                    let customer = bossNoCustomerMap[customerNo];
                    if(!customer || !customer.id){
                        this.$set(da,'hasTips',true);
                        this.$set(da,'tips',this.$t('documentLibrary.validate.noCustomerTip'));
                        return;
                    }
                    let {id,customerNameZh} = customer;

                    let pageIds = repeatDataMap[productLineCode+id];
                    if(pageIds && pageIds.length>0){
                        this.$set(da,'hasTips',true);
                        this.$set(da,'tips',this.$t('documentLibrary.validate.repeatDataTip'));
                        return;
                    }
                    repeatDataMap[productLineCode+id] = [pageId];
                    this.$set(da, 'customerId', id);
                    this.$set(da, 'customerName', customerNameZh);
                    this.$set(da,'send',1);
                });
                //进行第二层校验，数据是否已经存在数据库中
                let checkedDataList = formDataList.filter(f=>f.send==1);
                if(checkedDataList && checkedDataList.length>0){
                    let checkResult = await checkDataBeforeSubmit(checkedDataList).catch(err=>{
                        this.loadingPage = false;
                        return;
                    });
                    if(checkResult.status==200 && checkResult.data && checkResult.data.data){
                        let errData = checkResult.data.data || [];
                        (errData || []).forEach(ed=>{
                            let key = Object.keys(ed)[0];
                            formDataList.forEach(da=>{
                                let {pageId} = da;
                                if(key==pageId){
                                    this.$set(da,'send',0);
                                    this.$set(da,'tips',ed[key]);
                                    this.$set(da,'hasTips',true);
                                }
                            })
                        });
                    }
                }
                this.preUploadDataList = formDataList;
                this.loadingPage = false;
            },
            cancelImport(){
                this.showUploadFile = false;
                this.loadingPage = false;
                this.uploadFileName = '';
                this.preUploadDataList = [];
                this.onLoad(this.page);
            },
            batchImport(){
                if(!this.preUploadDataList || this.preUploadDataList.length==0){
                    return;
                }
                let sendData = this.preUploadDataList.filter(p=>p.send==1);
                if(!sendData || sendData.length==0){
                    this.$notify.error(this.$t("documentLibrary.validate.hasInvalidData"));
                    return;
                }
                //如果preUploadDataList 存在hasTips=true的数据，给出提示，先删除错误数据
                if(this.preUploadDataList.some(p=>p.hasTips)){
                    this.$notify.error(this.$t("documentLibrary.validate.hasInvalidData"));
                    return;
                }
                this.importLoading = true;
                batchAdd(sendData).then(res=>{
                    this.importLoading = false;
                    if(res.status==200 && res.data){
                        this.$notify.success("Success");
                        let errData = res.data.data;
                        if(!errData || errData.length==0){
                            this.showUploadFile = false;
                            this.uploadFileName = '';
                            this.preUploadDataList=[];
                            this.onLoad({
                                currentPage: 1,
                                pageSize: this.page.pageSize,
                            });
                            return
                        };
                        (errData || []).forEach(ed=>{
                            let key = Object.keys(ed)[0];
                            this.preUploadDataList.forEach(da=>{
                                let {pageId} = da;
                                if(key==pageId){
                                    this.$set(da,'tips',ed[key]);
                                    this.$set(da,'hasTips',true);
                                }
                            })
                        });
                        this.preUploadDataList = this.preUploadDataList.filter(p=>p.hasTips);
                        this.onLoad({
                            currentPage: 1,
                            pageSize: this.page.pageSize,
                        });
                        this.showConfirmBtn = false;
                    }
                }).catch(err=>{
                    console.log("sendData err",err)
                    this.$notify.error(this.$t("documentLibrary.validate.interfaceError"));
                    this.importLoading = false;
                })

            },
            clearErrorData(){
                this.preUploadDataList = this.preUploadDataList.filter(p=>p.hasTips==false);
            },
            removeFile(){
                document.getElementById("uploadFileInput").value = '';
                this.uploadFileName = '';
                this.preUploadDataList = [];
            },
            chooseFile(){
                document.getElementById("uploadFileInput").click();
            },
        },
        created() {
            this.onLoad(this.page);
            getCustomerGroup().then(res => {
                const data = res.data.data;
                this.customerGroupData = data;
            });
            getProductLine().then(res => {
                const data = res.data.data;
                this.productLineData = data;
            });
            /* getCustomerNotPageList().then(res => {
                 debugger;
                 const data = res.data.data;
                 this.customerData = data;
             });*/
        },
      computed: {
        ...mapGetters([
          "userInfo",
          "language"
        ]),
        uploadFileTableRowClass(){
            return ({row,rowIndex})=>{
              let {hasTips} = row;
              if(hasTips){
                  return 'upload_table_row_tips';
              }
              return '';
          }
        },
      }
    }
</script>

<style lang="scss">
.smart_document_auth_list{
  .upload_table{
    tr.el-table__row.upload_table_row_tips{
      color:#ff6600 !important;
    }
  }
}
</style>