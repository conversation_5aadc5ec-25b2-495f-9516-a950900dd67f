import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/add',
        method: 'post',
        data: form
    })
}
export const update = (form) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/update',
        method: 'post',
        data: form
    })
}
export const approve = (form) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/approveById',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/list',
        method: 'post',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/page/by-user',
        method: 'post',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const reject = (form) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/reject',
        method: 'post',
        data:form
    })
}
