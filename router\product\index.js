const router = require("koa-router")();
const Api = require("./../../request");

// 商品线
router.get("/api/sgs-mart/sgs-api/product-lines", async (ctx, next) => {
    const datas = await Api.get("sgs-mart/sgs-api/product-lines", ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/sample/web/v1/template/buConfig', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/sample/web/v1/template/buConfig', ctx)
    ctx.body = datas
})

router.post('/api/sgs-trims/trimsApi/queryWarning', async (ctx, next) => {
    const datas = await Api.post('sgs-trims/trimsApi/queryWarning', ctx)
    ctx.body = datas
})

router.post('/api/sgs-trims/trimsApi/queryCareLabelRegionCountry', async (ctx, next) => {
    const datas = await Api.post('sgs-trims/trimsApi/queryCareLabelRegionCountry', ctx)
    ctx.body = datas
})

router.post('/api/sgs-mart/sgs-trimsClient/queryCareLabel', async (ctx, next) => {
    const datas = await Api.post('sgs-mart/sgs-trimsClient/queryCareLabel', ctx)
    ctx.body = datas
})
router.post('/api/sgs-mart/trfApi/uploadCareLabel', async (ctx, next) => {
    const datas = await Api.post('sgs-mart/trfApi/uploadCareLabel', ctx)
    ctx.body = datas
})

router.post('/api/sgs-mart/sgs-api/dowonload-file', async (ctx, next) => {
    ctx.requestType = 'formData';
    const datas = await Api.post('sgs-mart/sgs-api/dowonload-file', ctx)
    ctx.body = datas
})
router.post('/api/sgs-pbm/component/documents/library', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/library', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/list', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/list', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/approval', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/approval', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/link', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/link', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/unlink', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/unlink', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/delete', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/delete', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/history', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/history', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/checkin', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/checkin', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/checkout', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/checkout', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/add', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/add', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-mart/file/saveAttachment', async (ctx, next) => {
    const datas = await Api.post('sgs-mart/file/saveAttachment', ctx);
    ctx.body = datas;
});

router.post('/api/sgs-pbm/component/documents/linkdetail/uncheckout', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/linkdetail/uncheckout', ctx);
    ctx.body = datas;
});
/*
 * /api/sgs-pbm/component/documents/linkdetail
 */
router.post('/api/sgs-pbm/component/documents/linkdetail', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/linkdetail', ctx);
    ctx.body = datas;
})
/*/api/sgs-pbm/component/documents/approve/undo*/
router.post('/api/sgs-pbm/component/documents/approve/undo', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/approve/undo', ctx);
    ctx.body = datas;
})
/*
/api/sgs-pbm/component/documents/approve
* */
router.post('/api/sgs-pbm/component/documents/approve', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/component/documents/approve', ctx);
    ctx.body = datas;
})

/* 
    Description
    分页获取物料模板列表
    1，前端参数都为可选，默认数据参考轻轻参数说明
    2，前端如果没有传customerNo，customerGroupCode，后端根据当前登录用户使用customerNo，customerGroupCode组合查询条件远程获取到模板列表
*/
router.post('/api/sgs-pbm/sample/web/v2/templateList', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/sample/web/v1/templateList', ctx);
    ctx.body = datas;
})

/* 
    material详情接口
*/
router.post('/api/sgs-pbm/sample/web/v1/detailt', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/sample/web/v1/detailt', ctx);
    ctx.body = datas;
})

/* 
    material详情接口
*/
router.post('/api/sgs-pbm/sample/web/v1/detail', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/sample/web/v1/detail', ctx);
    ctx.body = datas;
})

/* 
   Description
    新增或更新material数据接口，区分新增还是更新，通过提交表里是否有ID值作为依据。
    用于前端物料数据表单提交使用
*/
router.post('/api/sgs-pbm/sample/web/v1/create', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/sample/web/v1/create', ctx);
    ctx.body = datas;
})


/* 
   查询 change assignee 列表
*/
router.post('/api/sgs-pbm/sample/web/v1/employee/list', async (ctx, next) => {
    const datas = await Api.post('sgs-pbm/sample/web/v1/employee/list', ctx);
    ctx.body = datas;
})

module.exports = router;
