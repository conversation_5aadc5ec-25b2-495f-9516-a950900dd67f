import request from '@/router/axios';

export const submitTemplate = (template) => {
    return request({
        url: '/api/sgs-mart/template/submit',
        method: 'post',
        data: template
    })
}

export const checkDefaultTemplate = (template) => {
    return request({
        url: '/api/sgs-mart/template/checkDefaultTemplate',
        method: 'post',
        data: template
    })
}
export const submitFieldSettings = (data) => {
    return request({
        url: '/api/sgs-mart/ccl/config/submit',
        method: 'post',
        data: data
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/template/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const getFieldSettings = ( form) => {
    return request({
        url: '/api/sgs-mart/ccl/config/list',
        method: 'post',
        data: form
    })
}

export const getFieldSettingsByTemplateId = (templateId) => {
    return request({
        url: '/api/sgs-mart/ccl/config/map',
        method: 'post',
        params: {
            templateId,
        }
    })
}
export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/template/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}
export const getTemplateById = (id) => {
    return request({
        url: '/api/sgs-mart/template/getTemplateById',
        method: 'post',
        params: {
            id,
        }
    })
}

export const updateTemplateStatus = (template) => {
    return request({
        url: '/api/sgs-mart/template/updateTemplate',
        method: 'post',
        data: template
    })
}
export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/template/detail',
        method: 'get',
        params: {
            id,
        }
    })
}
/*根据选择的产品线ID查询产品类别*/
//条件获取客户组
export const queryTrimsProductCategory = (params) => {
    return request({
       // url: '/api/sgs-mart/sgs-api/queryTrimsProductCategory',
        url:'/api/sgsapi/FrameWorkApi/trims/api/queryTrimsProductCategory?productLineId='+params,
        method: 'post'
    })
}

export const queryDigitalReportSetting = (params) => {
    return request({
        url: '/api/sgs-mart/template/getReportTemplateSetting',
        method: 'post',
        data: params
    })
}
export const getFormDynamicList = (current, size, params) => {
    return request({
        url: '/api/sgs-form/form/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

