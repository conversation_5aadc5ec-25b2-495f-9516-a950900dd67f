<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart__rejectProvided" id="sgs_smart__rejectProvided">
            <el-dialog
                title="Reject Provided information"
                class="sgs_smart_product_rejectProvided"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                v-dialog-drag
                append-to-body
                :lock-scroll="false"
                show-close
                :visible.sync="showDia"
                width="30%"
            >
                <el-row>
                    <el-col>Please add some comments.</el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <textarea class="reject_content" :disabled="false" :rows="5" v-model="content"></textarea>
                    </el-col>
                </el-row>
                <div slot="footer" class="dialog-footer">
                    <el-button  :disabled="false" @click="cancelDia">Cancel</el-button>
                    <el-button  :disabled="false" type="primary" @click="saveComment">Save</el-button>
                </div>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
    import api from "../../../api/newSamples";
    export default {
        name: "rejectProvided",
        inject:['getMainObjectId'],
        data() {
            return {
                pageLoading: false,
                showDia: false,
                content:''
            }
        },
        methods: {
            initPage() {
                this.showDia = true;
            },
            cancelDia(){
                this.$emit('cancelDia');
                this.showDia = false;
            },
            saveComment(){
                if(!this.content){
                    return
                }
                let param = {
                    objectId:this.objectId,
                    objectType:this.objectType,
                    content:this.content,
                    mainId:this.getMainObjectId()
                }
                api.addComment(param).then(res=>{
                    if(res.status==200){
                        this.$emit('rejectSuccess',this.objectType);
                        this.showDia = false;
                    }
                }).catch(err=>{
                    this.$notify.error("Error")
                })
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            objectId:'',
            objectType:''
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart__rejectProvided {
        font-family: 'Arial' !important;
        background: #fff;

    }
    .sgs_smart_product_rejectProvided{
        .reject_content{
            min-width: 100%;
            border: solid 1px #8c8c8c;
            border-radius: 5px;
        }
    }
</style>