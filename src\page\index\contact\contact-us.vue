<template>
    <div class="page-no-print">
        <div class="tool-contact">
            <div v-if="btnFlag" class="contact-us" @click="backTop" style="margin-bottom: 16px;">
                <img src="/img/icon/top.png" />
            </div>
            <!-- 小橙点 -->
            <!-- <span style="font-size:12px; color:red;">{{menuFlag}}-{{!role.sgsRole}}-{{showCircle}}-{{!role.isBuyer}}</span> -->
            <template v-if="menuFlag && !role.isSGS && !role.isBuyer">
                <div @click="showGuideList" class="incomplete" v-if="showCircle" :class="{'show': showCircle}">
                    <div class="feature-box">
                        <div class="pulse"></div>
                        <div class="pulse1"></div>
                        <div class="pulse2"></div>
                    </div>
                </div>
            </template>
            <el-tooltip class="item" effect="dark" :content="$t('feedback.title.default')" placement="right-start">
                <div class="contact-us" @click="openContactUS">
                    <i style="color: #656565; font-size: 22px;" class="el-icon-chat-dot-round"></i>
                </div>
            </el-tooltip>
        </div>
        <el-drawer
                :title="$t('feedback.title.default')"
                direction="rtl"
                :visible.sync="contactUsVisible"
                size="30%"
                v-loading="true"
                class="guestbook-box">
            <el-card class="box-card">
                <el-form label-position="left" ref="feedbackForm" :model="guestbook" size="small" :rules="rules">
                    <el-form-item :label="$t('contact.name')"
                                  :rules="{ required: true, message:  $t('feedback.validate.nameBlur'), trigger: 'blur' }"
                                  prop="contactName">
                        <el-input v-model="guestbook.contactName" :placeholder="$t('feedback.validate.nameBlur')"
                                  maxlength="100"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('contact.phone')"
                                  :rules="{ required: true, pattern: /^1[34578]\d{9}$/, message: $t('contact.validate.phonelBlur'), trigger: 'blur' }"
                                  prop="mobile">
                        <el-input v-model="guestbook.mobile" :placeholder="$t('feedback.validate.phoneBlur')"
                                  maxlength="20"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('contact.email')"
                                  :rules="{ required: true, pattern: /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/, message: $t('feedback.validate.emailWaring'), trigger: 'blur' }"
                                  prop="email">
                        <el-input v-model="guestbook.email" :placeholder="$t('feedback.validate.emailBlur')"
                                  maxlength="150"></el-input>
                    </el-form-item>
                    <el-form-item :label="$t('feedback.content')"
                                  :rules="{ required: true,  message: $t('feedback.validate.contentBlur'), trigger: 'blur' }"
                                  porp="content">
                        <el-input type="textarea" v-model="guestbook.content"
                                  maxlength="300"></el-input>
                    </el-form-item>
                    <el-form-item style="text-align: right;">
                        <el-button type="primary" icon="el-icon-check" :loading="btnGuestbookSubmit"
                                   @click="submitForm">{{$t('operation.submit')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </el-drawer>
    </div>
</template>

<script>
    import { mapGetters } from "vuex";
    import {add} from '@/api/operation/guestbook';
    import {validatenull} from "@/util/validate";

    export default {
        name: "contact-us",
        data() {
            return {
                taskStatus: [],
                showCircle: false,
                btnFlag: false,
                btnGuestbookSubmit: false,
                contactUsVisible: false,
                guestbook: {},
                rules: {
                    email: [
                        {required: true, message: this.$t('feedback.validate.emailBlur'), trigger: 'blur'},
                        {type: 'email', message: this.$t('feedback.validate.emailWaring'), trigger: ['blur', 'change']},
                    ],
                },
                menuFlag: null,
                sgsRole: null
            };
        },
        mounted() {
            //判断是否为SGS
            setTimeout(() => {
                this.menuFlag = this.validateTrfMenu();
                this.sgsRole = this.role.isSGS;
            }, 600)

            window.addEventListener('scroll', this.scrollToTop)

            this.taskStatus = JSON.parse(localStorage.getItem('guideTask')) || this.taskStatus
            let complete = this.taskStatus.every(item => item.val == true);
            if(complete) {
                this.showCircle = false
            } else {
                let off = JSON.parse(localStorage.getItem('AUTO_OFF'))
                if(off) this.showCircle = true
            }
        },
        watch: {
            '$store.state.user.userInfo.guide' (newVal, oldVal) {   // 完成所有任务关闭红点
                console.log('监听小红点1', newVal)
                if(newVal == false) this.showCircle = false
            },
            '$store.state.user.taskListDialog' (newVal, oldVal) {
                // this.menuFlag = this.validateTrfMenu();
                // this.sgsRole = this.role.isSGS;
                // let complete = this.taskStatus.every(item => item.val == true);
                let complete = JSON.parse(localStorage.getItem('guideTask')).every(item => item.val == true);
                // console.log('监听小红点2', newVal, complete, this.menuFlag, !this.role.isSGS, !this.role.isBuyer)
                if(!complete) this.showCircle = !newVal
            },
        },
        destroyed() {
            window.removeEventListener('scroll', this.scrollToTop)
        },
        computed: {
            ...mapGetters(["userInfo", "language", "menu", "dimensions"]),
            role() {
                return {
                    isSGS: this.haseRole('SGSUserRole', 'SgsAdmin') || this.haseRole("SGSUserRole", "SgsLabUser"),
                    isBuyer: this.haseRole('UserRole', 'Buyer'),
                };
            },
        },
        methods: {
            //判断是否存在创建TRF菜单
            validateTrfMenu(){
                let result = false;
                if(!validatenull(this.menu)){
                    let menuStr = JSON.stringify(this.menu);
                    if(!validatenull(menuStr)){
                    if(menuStr.indexOf("/ccl/trf/newTrf") != -1){
                        result = true;
                    }
                }
                }
                return result;
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            showGuideList() {
                if(this.$store.state.user.taskListDialog) this.$store.commit('SET_TASK_DIALOG', false)
                setTimeout(() => {
                    this.$store.commit('SET_TASK_DIALOG', true)
                }, 150)
                this.showCircle = false
                localStorage.setItem('AUTO_OFF', false)
            },
            // 点击图片回到顶部方法，加计时器是为了过渡顺滑
            backTop() {
                const that = this
                let timer = setInterval(() => {
                    let ispeed = Math.floor(-that.scrollTop / 5)
                    document.documentElement.scrollTop = document.body.scrollTop = that.scrollTop + ispeed
                    if (that.scrollTop === 0) {
                        clearInterval(timer)
                    }
                }, 16)
            },

            // 为了计算距离顶部的高度，当高度大于60显示回顶部图标，小于60则隐藏
            scrollToTop() {
                const that = this
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
                that.scrollTop = scrollTop
                if (that.scrollTop > 60) {
                    that.btnFlag = true
                } else {
                    that.btnFlag = false
                }
            },
            openContactUS() {
                //清空表单中的内容
                this.guestbook = {};
                this.contactUsVisible = true;
            },
            submitForm() {
                this.btnGuestbookSubmit = true;
                this.$refs['feedbackForm'].validate((valid) => {
                    if (valid) {
                        add(this.guestbook).then(() => {
                            this.btnGuestbookSubmit = false;
                            this.contactUsVisible = false;
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                        }, error => {
                            console.log(error);
                        });
                    } else {
                        this.btnGuestbookSubmit = false;
                        console.log('error submit!!');
                        return false;
                    }
                });
            }
        },
    }
</script>

<style lang="scss">
    .tool-contact {
        position: fixed;
        width: 50px;
        bottom: 50px;
        right: 24px;
        color: #FFFFFF;
        font-size: 28px;
        font-weight: bold;
        /* opacity: 0.7; */
        /* background-color: #6b6b6b; */
        z-index: 2003;
        text-align: center;

        .contact-us {
            /* border: 1px solid #FFFFFF;
            width: 100%;
            height: 50px; */
            line-height: 48px;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            background: #FFFFFF;
            box-shadow: 0px 8px 40px 0px rgba(0, 0, 0, 0.2);
            color: #000;
            cursor: pointer;
            img{
                vertical-align: middle;
            }
        }

    }

    .guestbook-box {
        top: auto !important;
        right: 5px !important;
        bottom: 5px !important;
        height: 500px;
    }

    .el-drawer {
        border-radius: 20px;
    }

    .incomplete {
    /* position: fixed; */
    right: 30%;
    bottom: 50vh;
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
    /* background: #f60; */
    transition: all .3s;
    z-index: -1;
    opacity: 0;
    /* animation: slider 2s linear infinite; */
    /* transform: translateX(7px); */
    font-size: 22px;
    color: #fff;
    text-align: center;
    line-height: 48px;
    cursor: pointer;
    &.show {
      bottom: 114px;
      right: 33px;
      opacity: 1;
      z-index: 10;
    }

    .feature-box {
      width: 48px;
      height: 48px;
		    position: relative;
		    margin: 0px auto;
		    display: flex;
		    align-items: center;
		    justify-content: center;
        }
		.pulse {
            width: 20px;
		    height: 20px;
		    background: #f60;
		    border-radius: 50%;
        }
        .pulse1, .pulse2 {
            position: absolute;
		    width: 48px;
		    height: 48px;
		    top: 0;
		    background: #f60;
		    border-radius: 50%;
		    opacity: 0;
		    z-index: -1;
		    box-shadow: 1px 1px 15px #f60;
        }
        .pulse1 {
            animation: warn1 1.5s linear;
            animation-iteration-count: infinite;
        }
        .pulse2 {
            animation: warn2 1.5s linear;
            animation-iteration-count: infinite;
        }

  }

  /* @keyframes slider {
    0%, 100% {
      transform: translateX(7);
    }
    50% {
      transform: translateX(-7px);
    }
  } */

  @keyframes warn1 {
    0% {
      transform: scale(1);
      opacity: 0.01;
    }

    25% {
      transform: scale(1.2);
      opacity: 0.1;
    }

    50% {
      transform: scale(1.4);
      opacity: 0.07;
    }

    75% {
      transform: scale(1.6);
      opacity: 0.03;
    }

    100% {
      transform: scale(1.8);
      opacity: 0.01;
    }
  }

  @keyframes warn2 {
    0% {
      transform: scale(0.8);
      opacity: 0.01;
    }

    25% {
      transform: scale(0.8);
      opacity: 0.13;
    }

    50% {
      transform: scale(1);
      opacity: 0.1;
    }

    75% {
      transform: scale(1.2);
      opacity: 0.07;
    }

    100% {
      transform: scale(1.4);
      opacity: 0.01;
    }
  }
</style>
