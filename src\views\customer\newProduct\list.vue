<template>
  <basic-container v-loading="tableLoading">
    <div class="list_header">
      <div class="list-header-title">Product List</div>
    </div> 
    <div class="sgs_smart_product_list" id="sgs_smart_product_list">
      <div class="serach_block">
        <div class="header_opreate">
          <el-dropdown>
            <el-button type="primary" size="medium">
              Create Product
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="showDownloadTemplate"
                >New Product</el-dropdown-item
              >
              <el-dropdown-item @click.native="showImportTemplate"
                >Import Product</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown>
            <el-button type="primary" size="medium">
              Create TRF
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="createTrf('individualSample')"
                >Individual Creation</el-dropdown-item
              >
              <el-dropdown-item @click.native="createTrf('groupSample')"
                >Group Creation</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="serach_block_group">
          <filter-view
            source-type="PRODUCT"
            @changeFilter="changeView"
            @resetFilter="resetForm"
          ></filter-view>
          <reset-button @click="resetForm"></reset-button>
          <el-button size="medium" icon="el-icon-download" v-loading="exportLoading" @click="exportProducts">Export</el-button>
        </div>
      </div>

      <div :style="{'height': tableMinHeight + 'px'}">
     
      <el-table
        ref="samplesTable"
        :data="dataList"
        :max-height="tableMaxHeight"
        size="small"
        class="sgs_table_product_table table-header-no-filter"
        fit
        border
        resizable
      >
        <el-table-column
          type="selection"
          fixed="left"
          :selectable="disabledRow"
        ></el-table-column>
        <el-table-column prop="sampleNo" label="Product ID" width="200px">
          <template slot="header" slot-scope="scope">
            <table-filter 
              title="Product ID">
              <el-input
                    placeholder="Product ID"
                    clearable
                    size="mini"
                    v-model="searchForm.sampleNo"
                >
                    <template slot="append">
                        <i class="el-icon-search filter-search-icon"></i>
                    </template>
                </el-input>
            </table-filter>
          </template>
          <template slot-scope="{ row }">
            <a
              @click="toDetail('detail', row)"
              style="color: #ff6600; font-size: 14px; cursor: pointer"
              >{{ row.sampleNo }}</a
            >
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="sampleCode"
          label="Product Code"
          width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Product Code">
              <el-input
                placeholder="Product Code"
                clearable
                size="mini"
                v-model="searchForm.sampleCode"
              >
                    <template slot="append">
                        <i class="el-icon-search filter-search-icon"></i>
                    </template>
                </el-input>
            </table-filter>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="productLineName"
          label="Product Line"
          width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Product Line">
              <el-select
                style="width: 100%"
                size="mini"
                clearable
                placeholder="Product Line"
                filterable
                v-model="searchForm.buCode"
              >
                <el-option
                  v-for="(pl, index) of filterList.productLineList"
                  :key="'pl_' + index"
                  :label="pl.productLineName"
                  :value="pl.productLineCode"
                ></el-option>
              </el-select>
            </table-filter>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="sampleCategory"
          label="Product Category"
          width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Product Category">
              <el-input
                placeholder="Product Category"
                clearable
                size="mini"
                v-model="searchForm.sampleCategory"
              >
                    <template slot="append">
                        <i class="el-icon-search filter-search-icon"></i>
                    </template>
                </el-input>
            </table-filter>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="sampleName"
          label="Product Name"
          min-width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Product Name">
              <el-input
                placeholder="Product Name"
                clearable
                size="mini"
                v-model="searchForm.sampleName"
              >
                <template slot="append">
                        <i class="el-icon-search filter-search-icon"></i>
                    </template>
                </el-input>
            </table-filter>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="buyerCustomerGroupName"
          label="Buyer"
          width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Buyer">
              <el-select
                style="width: 100%"
                size="mini"
                clearable
                placeholder="Buyer"
                filterable
                v-model="searchForm.buyerCustomerGroupCode"
              >
                <el-option
                  v-for="(cl, index) of filterList.customerList"
                  :key="'cl_' + index"
                  :label="cl.customerGroupName"
                  :value="cl.customerGroupCode"
                ></el-option>
                <el-option
                  key="cl_general"
                  label="General"
                  value="General"
                ></el-option>
              </el-select>
            </table-filter>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="supplierName"
          label="Supplier"
          width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Supplier">
              <el-input
                placeholder="Supplier"
                clearable
                size="mini"
                v-model="searchForm.supplierName"
              >
                <template slot="append">
                        <i class="el-icon-search filter-search-icon"></i>
                    </template>
                </el-input>
            </table-filter>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="sampleStatusName"
          label="Product Status"
          width="200px"
        >
          <template slot="header" slot-scope="scope">
            <table-filter
              title="Product Status">
              <el-select
                size="mini"
                style="width: 100%"
                placeholder="Product Status"
                multiple
                filterable
                clearable
                v-model="searchForm.sampleStatus"
              >
                <el-option key="PS1" label="In-Progress" value="1"></el-option>
                <el-option key="PS2" label="Submitted" value="2"></el-option>
                <el-option key="PS3" label="Approved" value="3"></el-option>
                <el-option key="PS4" label="Cancelled" value="4"></el-option>
                <el-option key="PS5" label="Not In Use" value="5"></el-option>
              </el-select>
            </table-filter>
          </template>
          <template slot-scope="{ row }">
            <tag :color="setStatusColor(row.sampleStatus)">
                {{
                  row.sampleStatusName
                }}
            </tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" prop="id" label="Action" width="150px">
          <template slot-scope="{ row }">
            <el-tooltip
              content="Edit"
              placement="top"
              v-if="btnRole(row, 'Edit')"
            >
              <i
                class="el-icon-edit menu-icon"
                @click="toDetail('detail', row)"
              ></i>
            </el-tooltip>
            <el-tooltip content="Copy" placement="top">
              <i
                class="el-icon-copy-document menu-icon"
                @click="toDetail('copy', row)"
              ></i>
            </el-tooltip>
            <el-tooltip content="Cancel" placement="top">
              <el-popconfirm
                v-if="btnRole(row, 'Cancel')"
                confirm-button-text="Confirm"
                cancel-button-text="Cancel"
                icon="el-icon-info"
                icon-color="red"
                title="Cancel the data?"
                @confirm="cancelSample(row)"
              >
                <i slot="reference" class="el-icon-delete delete-icon"></i>
              </el-popconfirm>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
      <el-pagination
        @size-change="sizeChange"
        @current-change="currentChange"
        :current-page.sync="page.page"
        :page-sizes="page.sizes"
        :page-size.sync="page.rows"
        :layout="page.layout"
        :total="page.total"
      >
      </el-pagination>
      <!-- download template-->
      <el-dialog
        :visible.sync="downloadTemplateDia"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        title="Product Template"
      >
        <download-template
          v-if="downloadTemplateDia"
          :supplier-list="filterList.supplierList"
          :template-show-download="templateShowDownload"
          :customer-list="filterList.customerList"
          :product-line-list="filterList.productLineList"
          form-purpose="Product"
          @cancelDia="closeDia('downloadTemplateDia')"
        >
          <template slot="downloadTemplateSlot" v-if="templateShowDownload">
            <el-button type="primary" size="small" @click="showImportSamples"
              >Import Samples</el-button
            >
          </template>
        </download-template>
      </el-dialog>

      <sgs-batch-upload
        v-if="isLoadUpload"
        title="Import Product"
        append-to-body
        :systemID="1"
        :limit="1"
        :handle-upload-success="uploadSuccess"
        :handle-upload-error="uploadError"
        ref="batchUpload"
        accept=".xls,.xlsx"
        upload-url="/api/sgs-pbm/sample/web/v1/upload?dataType=product"
        :attachment-type-options="[]"
        attachment-type-default-value=""
        :file-max-sizes="20"
      >
      </sgs-batch-upload>
    </div>
  </basic-container>
</template>

<script>
import { queryScmCustomer } from "@/api/customer/scmCustomer";
import { getProductLine } from "@/api/common/index";
import { validatenull } from "@/util/validate";
import { debounce } from "lodash";
import DownloadTemplate from "../materialAndProductCommon/downloadTemplate";
import filterView from "../materialAndProductCommon/filterView";
import api from "../../../api/newSamples";
import { mapGetters } from "vuex";
import tableFilter from "../../../components/tableFilter/tableFilter.vue";
import { getStatusColor } from '@/util/status';
import tag from "@/components/tag/tag.vue";
import resetButton from "@/components/resetButton/resetButton.vue";

let vm;
export default {
  name: "ProductList",
  provide() {
    return {
      loadSearchForm: () => {
        return this.searchForm;
      },
    };
  },
  data() {
    return {
      isLoadUpload: false,
      showDetailDia: false,
      dataList: [],
      tableLoading: false,
      templateShowDownload: false,
      downloadTemplateDia: false,
      exportLoading: false,
      viewValue: "",
      searchForm: {
        sampleNo: "",
        buCode: "",
        sampleCategory: "",
        sampleCode: "",
        sampleName: "",
        buyerCustomerGroupCode: "",
        supplierName: "",
        approvalStatus: [],
        sampleStatus: [],
      },
      filterList: {
        buyerList: [],
        supplierList: [],
        customerList: [],
        productLineList: [],
        materialType: [
          { label: "Textiles", value: "textiles" },
          { label: "Knit Wear", value: "knitWear" },
          { label: "Woven", value: "woven" },
        ],
        materialStatus: [
          { label: "In-Progress", value: "1" },
          { label: "Submitted", value: "2" },
          { label: "Approved", value: "3" },
          { label: "Cancelled", value: "4" },
          { label: "Not In Use", value: "5" },
        ],
      },
      page: {
        //分页对象
        show: true,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: "total, sizes, prev, pager, next,jumper",
        total: 0,
      },
      tableMaxHeight: 0, // 根据试图动态设置表格高度
      tableMinHeight: 500, // 表格最小高度
    };
  },
  methods: {
    changeView(filterForm) {
      let defaultSearchForm = {
        productId: "",
        productCode: "",
        productLine: "",
        sampleCategory: "",
        productName: "",
        buyer: "",
        supplier: "",
        approvalStatus: "",
        sampleStatus: [],
      };
      let newFilter = Object.assign({}, defaultSearchForm, filterForm);
      this.$set(this, "searchForm", newFilter);
    },
    deboundceSearch: debounce(function (val) {
      vm.initTable();
    }, 500),
    closeDia(flag) {
      this[flag] = false;
    },
    // 切换当前页
    currentChange(currentPage) {
      //this.page.page = currentPage;
      this.initTable();
    },
    // 调整每页显示行数
    sizeChange(pageSize) {
      //this.page.rows = pageSize;
      this.initTable();
    },
    disabledRow(row, index) {
      let { permissions } = row;
      return (permissions || []).map((p) => p.action).includes("toTrf");
    },
    resetForm() {
      this.searchForm = {
        productId: "",
        sampleCode: "",
        productLine: "",
        sampleCategory: "",
        sampleName: "",
        buyer: "",
        supplier: "",
        approvalStatus: "",
        sampleStatus: [],
      };
      //this.initTable();
    },
    async initTemplateCustomerListForSupplier() {
      //再查询所有的模板 + 自身关联的buyer - 自身的模板
      let buyerList = await this.initClientRelationCustomerList("buyer");
      let buyerGroupCode = (buyerList || [])
        .filter((b) => b.scmCustomerGroupCode)
        .map((b) => b.scmCustomerGroupCode);

      let currentUser = this.userInfo.userName;
      let param = {
        currentUser, //必传
        formPurpose: "Product", //必传
      };
      //查询自身的模板
      api.queryProductViewCustomerList(param).then((res) => {
        let supplierCustomerList = [];
        if (res.status == 200 && res.data && res.data.status == 200) {
          supplierCustomerList = res.data.data || [];
        }
        api
          .queryProductViewCustomerList({ formPurpose: "Product" })
          .then((res) => {
            if (res.status == 200 && res.data && res.data.status == 200) {
              let customerList = res.data.data || [];
              if (this.role.isSupplier) {
                customerList = customerList.filter((c) =>
                  buyerGroupCode.includes(c.customerGroupCode)
                );
              }
              customerList = [
                ...new Set([...customerList, ...supplierCustomerList]),
              ];
              this.resetCustomerList(customerList);
            }
          });
      });
    },
    initTemplateCustomerList() {
      let currentUser = this.role.isSGS ? "" : this.userInfo.userName;
      let param = {
        currentUser, //必传
        formPurpose: "Product", //必传
      };
      api.queryProductViewCustomerList(param).then((res) => {
        if (res.status == 200 && res.data && res.data.status == 200) {
          let customerList = res.data.data || [];
          this.resetCustomerList(customerList);
        }
      });
    },
    resetCustomerList(customerList) {
      let newList = [];
      let cgs = [];
      customerList
        .filter((c) => c.customerGroupCode)
        .forEach((c) => {
          let { customerGroupCode } = c;
          if (!cgs.includes(customerGroupCode)) {
            newList.push(c);
            cgs.push(customerGroupCode);
          }
        });
      this.filterList.customerList = newList;
    },
    async initClientRelationCustomerList(relationshipType) {
      return new Promise((resolve) => {
        const params = {};
        const list = [];
        const item = {};
        item.relationshipType = relationshipType;
        /*if(customerName != null){
                        item.customerName = customerName;
                    }*/
        params.page = 1;
        params.rows = 1000;
        item.buCode =
          this.userInfo.productLineCode === "all"
            ? this.userInfo.defaultProductLineCode
            : this.userInfo.productLineCode;
        item.customerNo = this.userInfo.bossNo;
        list.push(item);
        params.list = list;
        queryScmCustomer(params)
          .then(
            (res) => {
              if (res.status == 200 && res.data) {
                resolve(res.data.rows);
              } else {
                resolve(false);
              }
            },
            (error) => {
              resolve(false);
            }
          )
          .catch((err) => {
            resolve(false);
          });
      });
    },
    initProductLine() {
      getProductLine().then((res) => {
        if (res.status == 200 && res.data) {
          const data = res.data.data || [];
          let currentUserPL = (this.userInfo.productLineCodes || "").split(",");
          if (
            !this.userInfo.productLineCodes &&
            this.userInfo.productLineCode == "all"
          ) {
            this.filterList.productLineList = data;
            return;
          }
          this.filterList.productLineList = data.filter((da) =>
            currentUserPL.includes(da.productLineCode)
          );
        }
      });
    },
    initTable() {
      this.dataList = [];
      //this.page.total = 0;
      let queryForm = Object.assign({}, this.searchForm, {
        current: this.page.page,
        size: this.page.rows,
      });
      queryForm["dataType"] = "product";
      this.tableLoading = true;
      api
        .querySamplesListPage(queryForm)
        .then(
          (res) => {
            this.tableLoading = false;
            if (res.status == 200 && res.data && res.data.data) {
              let { records, total } = res.data.data;
              this.dataList = records || [];
              this.page.total = total;
            } else {
              this.page.total = 0;
            }
            this.calculateTableHeight();
          },
          (error) => {
            this.page.total = 0;
            this.tableLoading = false;
            this.calculateTableHeight();
          }
        )
        .catch((err) => {
          this.page.total = 0;
          this.tableLoading = false;
          this.calculateTableHeight();
        });
    },
    createTrf(action) {
      let { selection } = this.$refs.samplesTable;
      if (!selection || selection.length == 0) {
        this.$message.error("Please select at least one piece of data");
        return;
      }
      if (action == "individualSample" && selection.length > 1) {
        this.$message.error("Only support select one piece of data");
        return;
      }

      if (selection.length > 5) {
        this.$message.error("Supports up to 5 pieces of data");
        return;
      }
      let objectIds = [...new Set(selection.map((row) => row.id))];
      let param = {
        action, objectIds
      };
      api
        .checkToTrf(param)
        .then((res) => {
          if (res.status == 200 && res.data && res.data.status == 200) {
              let testId = res.data.data;
            let customerGroupCode = selection[0].buyerCustomerGroupCode;
            let bossNo = selection[0].buyerCustomerBossNo;
            let customer = {
              bossNo,
              customerGroupCode,
            };
            customer = encodeURIComponent(JSON.stringify(customer));
            let bu = selection[0].productLineCode;
            let routeUrl = this.$router.resolve({
              path: "/trf/trfForm",
              query: {
                actionType: action,
                  testId,
                flag: 1,
                customer,
                bu,
              },
            });
            window.open(routeUrl.href, "_blank");
          }
        })
        .catch((err) => {
          console.log("check trf err", err);
        });
    },
    cancelSample(row) {
      let { id } = row;
      api
        .actionSamples({ ids: [id], action: "Cancel" })
        .then((res) => {
          if (res.status == 200 && res.data) {
            this.$notify.success("Success");
            this.initTable();
          } else {
            this.$notify.error(res.message || "Delete Fail");
          }
        })
        .catch((err) => {});
    },
    btnRole({ permissions }, code) {
      return (permissions || []).map((p) => p.action).includes(code);
    },
    toDetail(action, obj) {
      let query = { action };
      if (obj) {
        let { id } = obj;
        query["id"] = id;
      }
      let routeUrl = this.$router.resolve({
        path: "/customer/newProduct/detail",
        query,
      });
      window.open(routeUrl.href, "_blank");
    },
    showDownloadTemplate() {
      this.templateShowDownload = false;
      this.downloadTemplateDia = true;
    },
    showImportTemplate() {
      this.templateShowDownload = true;
      this.downloadTemplateDia = true;
    },
    showImportSamples() {
      this.isLoadUpload = false;
      this.$nextTick(() => {
        this.isLoadUpload = true;
        this.$nextTick(() => {
          this.$refs.batchUpload.open();
        });
      });
    },
    exportProducts() {
      let queryForm = Object.assign({}, this.searchForm);
      this.exportLoading = true;
      queryForm["dataType"] = "product";
      api.exportSampleData(
        queryForm,
        () => {
          this.exportLoading = false;
        },
        (err) => {
          this.exportLoading = false;
          this.$notify.error(err || "Export fail");
        }
      );
    },
    uploadError() {},
    uploadSuccess(fileData) {
      this.isLoadUpload = false;
      this.downloadTemplateDia = false;
      this.initTable();
    },
    haseRole(type, role) {
      if (validatenull(type) || validatenull(role)) {
        return false;
      }
      if (validatenull(this.userInfo.dimensions)) {
        return false;
      } else {
        if (this.userInfo.dimensions.hasOwnProperty(type)) {
          if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      }
    },
    calculateTableHeight() {
      const headerHeight = document.querySelector('.list_header').offsetHeight;
      const searchBlockHeight = document.querySelector('.serach_block').offsetHeight;
      const paginationHeight = document.querySelector('.el-pagination').offsetHeight;
      const containerHeight = document.querySelector('.sgs_smart_product_list').offsetHeight;
      const vhHeight = window.innerHeight * 1 - 438;
      if(vhHeight< 500){
        this.tableMinHeight = 500
      }else{
        this.tableMinHeight = vhHeight
      }
      this.tableMaxHeight = containerHeight - headerHeight - searchBlockHeight - paginationHeight; // 40 is for padding/margin
      debugger
    },
    handleSort(column) {
    },
    setStatusColor(status) {
      return getStatusColor(status)
    },
  },
  mounted() {
    if (this.role.isSupplier) {
      this.initTemplateCustomerListForSupplier();
    } else {
      this.initTemplateCustomerList();
    }
    this.initProductLine();
    this.initTable();
  },
  created() {
    vm = this;
  },
  watch: {
    searchForm: {
      immediate: false,
      deep: true,
      handler(newV) {
        this.page.page = 1;
        this.deboundceSearch();
      },
    },
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    role() {
      return {
        isSGS: this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser"),
        isSupplier: this.haseRole("UserRole", "Supplier"),
        isThirdPartyLab: this.haseRole("UserRole", "ThirdPartyLab"),
      };
    },
  },
  props: {},
  updated() {},
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  destroyed() {},
  components: { DownloadTemplate, filterView, tableFilter, tag, resetButton },
};
</script>

<style lang="scss">
@import "@/styles/unit.scss";

.sgs_smart_product_list {
  font-family: "Arial" !important;
  background: #fff;
  padding: $module-padding;
  .icon-base {
    font-size: 20px;
    cursor: pointer;
    margin: 0 10px;
  }

  .menu-icon {
      @extend .icon-base;
      color: $primary-color;
  }

  .delete-icon {
      @extend .icon-base;
      color: $icon-color-default;
  }

  .el-icon-arrow-down {
    font-size: 12px;
  }

  .title_justify {
    text-align: justify;
    display: flex;
    justify-content: space-between;
    padding: 0;
    align-items: center;
  }
  .serach_block {
    display: flex;
    justify-content: space-between;
    padding-bottom: $module-padding-vertical;

    .serach_block_group {
      display: flex;
    }
  }

  .table-scroll {
    height: calc(100vh - 460px);
  }

  .sgs_table_material_table {
    .el-table--mini .el-table__cell {
      padding: 0 !important;
    }
  }

  .TableHeaderFilterSave {
    .el-col {
      margin-bottom: 0;
    }
  }

  .el-table__cell {
    padding: 8px 0;
  }
  .header_opreate {
    display: flex;

    .el-dropdown {
      margin-right:  $inline-element-spacing;
    }

    .export_btn {
      color: #ff6600;
      border-bottom: solid 1px #ff6600;
      padding-bottom: 5px;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .sgs_table_product_table{
    label {
      margin-bottom: 4px;
    }
  }
}
.list_header {
  padding-bottom: 11px;
  
}
.filter-search-icon{
  width: 18px;
  cursor: pointer;
  border: none;
  left:0;
  top:0;
}

</style>
