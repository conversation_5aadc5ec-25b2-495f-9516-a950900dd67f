import request from '@/router/axios';


export const add = (params,fileList) => {
    return request({
        url: '/api/sgs-training/course/submit',
        method: 'post',
        data : params
            /*"materialParam" : JSON.stringify(fileList)*/

    })
}
export const showTable2 = (current, size,params) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/sel-course-list",
        changeOrigin: true,
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const getModulePage = (current, size,params) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/sel-training-module-list",
        changeOrigin: true,
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const updCourseById = (form) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/upd-course-by-id",
        changeOrigin: true,
        data: form
    })
}
export const selCourseById = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/sel-course-by-id",
        changeOrigin: true,
        data: id
    })
}
export const handleDel2 = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/del-course-by-id",
         params: {
            id,
        }
    })
}

export const Get_CourseLevel = () => {
    return request({
        url: '/api/sgs-training/course/sel-course-level',
        method: 'post'
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const approve = (id,approveStatus) => {
    return request({
        url: '/api/sgs-mart/customer/approve',
        method: 'get',
        params: {
            id,
            approveStatus
        }
    })
}