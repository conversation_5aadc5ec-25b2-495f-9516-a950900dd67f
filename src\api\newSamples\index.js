import request from "@/router/axios";

const createImaArrayForCareLabelResponse = (carelabelResponse) => {
  let careLabelDetail = "";
  let data = carelabelResponse.data || carelabelResponse.data.data;
  if (data && data.length > 0) {
    careLabelDetail = data[0]["careLabelDetail"];
    if (careLabelDetail) {
      //按照sequenceNo 排序，同时增加check:false
      careLabelDetail.sort((v1, v2) => {
        let v1No = v1["sequenceNo"];
        let v2No = v2["sequenceNo"];
        return v1No - v2No;
      });
      return careLabelDetail;
    } else {
      data.sort((v1, v2) => {
        let v1No = v1["sequenceNo"];
        let v2No = v2["sequenceNo"];
        return v1No - v2No;
      });
      return data;
    }
  }
  return careLabelDetail;
};

const sendPost = (url, param) => {
  return request({
    url,
    method: "post",
    data: param,
  });
};
const sendGet = (url) => {
  return request({
    url,
    method: "get",
    params: {},
  });
};
const sendPostFormData = (url, params) => {
  return request({
    url,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    params,
  });
};

let api = {
  querySamplesListPage: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/page", param);
  },
  uploadMaterial: (param) => {
    return sendPost("/api/sgs-pbm/material/web/v1/upload", param);
  },
  exportSampleData: (param, callBack, errCallBack) => {
    return request({
      url: "/api/sgs-pbm/sample/web/v1/export",
      method: "post",
      responseType: "blob",
      data: param,
    })
      .then((res) => {
        const { data, headers } = res;
        const blob = new Blob([data], { type: headers["content-type"] });
        if ("application/json" == data.type) {
          let fileReader = new FileReader();
          fileReader.readAsText(blob, "utf-8");
          fileReader.onload = () => {
            let msg = JSON.parse(fileReader.result);
            if (errCallBack) {
              errCallBack(msg && msg.msg);
            }
          };
          return;
        }
        let patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
        let disposition = patt.exec(headers["content-disposition"]);
        let fileName =
          disposition && disposition[1] ? disposition[1] : fileName;
        let dom = document.createElement("a");
        let url = window.URL.createObjectURL(blob);
        dom.href = url;
        dom.download = decodeURIComponent(fileName);
        dom.style.display = "none";
        document.body.appendChild(dom);
        dom.click();
        dom.parentNode.removeChild(dom);
        window.URL.revokeObjectURL(url);
        callBack();
      })
      .catch((err) => {
        if (errCallBack) {
          errCallBack();
        }
      });
  },
  downloadMaterialTemplate: (param, callBack, errCallBack) => {
    return request({
      url: "/api/sgs-pbm/sample/web/v1/download",
      method: "post",
      responseType: "blob",
      data: param,
    })
      .then((res) => {
        const { data, headers } = res;
        const blob = new Blob([data], { type: headers["content-type"] });
        if ("application/json" == data.type) {
          let fileReader = new FileReader();
          fileReader.readAsText(blob, "utf-8");
          fileReader.onload = () => {
            let msg = JSON.parse(fileReader.result);
            if (errCallBack) {
              errCallBack(msg);
            }
          };
          return;
        }
        let patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
        let disposition = patt.exec(headers["content-disposition"]);
        let fileName =
          disposition && disposition[1] ? disposition[1] : fileName;
        let dom = document.createElement("a");
        let url = window.URL.createObjectURL(blob);
        dom.href = url;
        dom.download = decodeURIComponent(fileName);
        dom.style.display = "none";
        document.body.appendChild(dom);
        dom.click();
        dom.parentNode.removeChild(dom);
        window.URL.revokeObjectURL(url);
        callBack();
      })
      .catch((err) => {
        if (errCallBack) {
          errCallBack();
        }
      });
  },
  geSampleTemplateList: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/templateList", param);
  },
  getSampleNewTemplateList: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/newestTemplate", param);
  },
  actionSamples: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/action", param);
  },
  createMaterial: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/create", param);
  },
  querySampleDetail: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/detail", param);
  },
  checkToTrf: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/check", param);
  },
  copySample: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/copy", param);
  },
  queryEmployee: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/employee/list", param);
  },
  queryScmCustomerList: (param) => {
    return sendPost("/api/sgs-mart/customer/scm/query", param);
  },
  /**
   * 入参为：{id:Integer,materialStatus:String}
   * @param param
   */
  changeMaterialStatus: (param) => {
    return sendPost("/api/sgs-pbm/material/web/v1/changeMstatus", param);
  },
  queryProductViewCustomerList: (param) => {
    return sendPost(
      "/api/sgsapi/DFFV2Api/dff/queryProductViewCustomerList",
      param
    );
  },
  queryDffFormAttrByDffFormIDList: (param) => {
    return sendPost(
      "/api/sgsapi//DFFV2Api/dff/queryDffFormAttrByDffFormIDList",
      param
    );
  },
  queryProductViewForm: (param) => {
    return sendPost("/api/sgsapi/DFFV2Api/dff/queryProductViewForm", param);
  },
  queryStandardDff: () => {
    return sendPost("/api/sgsapi/DFFV2Api/dffStandardConfig/queryStandardFieldCodeList", {});
  },
  queryCustomerField:(param)=>{
    return sendPost('/api/sgsapi/DFFV2Api/dff/queryCustomerDffConfig',param)
  },
  changeDffFormStatus: (param) => {
    return sendPost("/api/sgsapi/DFFV2Api/dff/changeDffFormStatus", param);
  },
  getMaterialRelationShip: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/testRecode", param);
  },
  saveAttachment: (attachment) => {
    return sendPost("/api/sgs-mart/file/saveAttachment", attachment);
  },
  saveReportInfo: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/documents/create", param);
  },
  modifyReportData: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/documents/update", param);
  },
  deleteTestComplianceDocument: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/documents/delete", param);
  },
  downLoadFile: (cloudId) => {
    return request({
      url: "/api/sgs-mart/trf/downLoadFileByCloudId",
      method: "post",
      params: {
        cloudId: cloudId,
      },
    });
  },
  saveDFFFormData: (param) => {
    return sendPost("/api/sgsapi/DFFV2Api/dff/saveDffFormAndFieldData", param);
  },
  saveDffFieldData: (param) => {
    return sendPost("/api/sgsapi/DFFV2Api/dff/saveDffFieldData", param);
  },
  deleteDffFormAttr: (param) => {
    return sendPost("/api/sgsapi/DFFV2Api/dff/deleteDffFormAttr", param);
  },
  approved: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/approved", param);
  },
  addComment: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/comments/add", param);
  },
  queryCommentList: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/comments/list", param);
  },
  queryNotification: (sampleId) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/notification", {
      id: sampleId,
    });
  },
  loadTestLinePage: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/testLine/list", param);
  },
  queryCountry: (token) => {
    return sendGet(
      `api/sgsapi/FrameWorkApi/trims/api/v3/queryCountryAndRegion?1=1&sgsToken=${token}&languageCode=&allLanguage=yes`
    );
  },
  queryLog: (param) => {
    return sendPost("/api/sgs-log/auditLog/page", param);
  },
  filedList: (param) => {
    return sendPost("/api/sgs-pbm/sample/web/v1/filed/list", param);
  },
  importMaterial:(param)=>{//预览之后导入的数据对象，不是file对象
    return sendPost('/api/sgs-pbm/sample/web/v1/import',param);
  },
  queryCategoryType:(param)=>{
    return sendPost('/api/sgs-pbm/sample/web/v1/template/buConfig',param);
  },
  careLabelApi: {
    queryWarning: (param) => {
      return sendPost("/api/sgs-trims/trimsApi/queryWarning", param);
    },
    queryCareLabelCountry: () => {
      return sendPost(
        "/api/sgs-trims/trimsApi/queryCareLabelRegionCountry",
        {}
      );
    },
    queryCareLabel: (params, callback) => {
      const conditionTypeArray = [
        "Washing",
        "Bleaching",
        "Drying",
        "Ironing",
        "Drycleaning",
        "Others",
      ];
      const requestMethods = [];
      //去掉最后一项的careLableDetailsDescription
      for (let i = 0; i < conditionTypeArray.length; i++) {
        let para = JSON.parse(JSON.stringify(params));
        para["usageType"] = conditionTypeArray[i];
        const postMethod = function () {
          let url = "api/sgs-mart/sgs-trimsClient/queryCareLabel";
          return request.post(url, para);
        };
        requestMethods.push(postMethod());
      }
      request
        .all(requestMethods)
        .then(
          request.spread(
            (Washing, Bleaching, Drying, Ironing, Drycleaning, Others) => {
              //console.log(Washing, Bleaching, Drying, Ironing, Drycleaning, Others)
              let array = [
                {
                  conditionType: "Washing",
                  imgArray: createImaArrayForCareLabelResponse(Washing),
                },
                {
                  conditionType: "Bleaching",
                  imgArray: createImaArrayForCareLabelResponse(Bleaching),
                },
                {
                  conditionType: "Drying",
                  imgArray: createImaArrayForCareLabelResponse(Drying),
                },
                {
                  conditionType: "Ironing",
                  imgArray: createImaArrayForCareLabelResponse(Ironing),
                },
                {
                  conditionType: "Drycleaning",
                  imgArray: createImaArrayForCareLabelResponse(Drycleaning),
                },
                {
                  conditionType: "Others",
                  imgArray: createImaArrayForCareLabelResponse(Others),
                },
              ];
              callback(array);
            }
          )
        )
        .catch((err) => {
          callback([]);
        });
    },
    queryBuyerCareLabel: (param) => {
      return sendPost("/api/sgs-mart/sgs-trimsClient/queryCareLabel", param);
    },
    uploadCareLabel: (param) => {
      return sendPost("/api/sgs-mart/trfApi/uploadCareLabel", param);
    },
    downloadPathById: (params) => {
      return sendPostFormData("/api/sgs-mart/sgs-api/dowonload-file", params);
    },
  },
  billApi: {
    unBomMaterialPage: (params) => {
      return sendPost("/api/sgs-pbm/sample/web/v1/unBom/page", params);
    },
    bindsBom: (params) => {
      return sendPost("/api/sgs-pbm/sample/web/v1/bom/create", params);
    },
    getBomMaterialList: (params) => {
      return sendPost("/api/sgs-pbm/sample/web/v1/bom/list", params);
    },
    deleteBomRelation: (params) => {
      return sendPost("/api/sgs-pbm/sample/web/v1/bom/delete", params);
    },
  },
  fileApi: {
    downloadFile: (cloudId) => {
      return sendPostFormData("/api/sgs-mart/trf/downLoadFileByCloudId", {
        cloudId,
      });
    },
    queryAttr: (param) => {
      return sendPost("/api/sgs-mart/attr/queryAttr", param);
    },
    deleteAttrById: (id) => {
      return sendPost("/api/sgs-mart/attr/deleteAttrById", { id });
    },
  },
};
export default api;
