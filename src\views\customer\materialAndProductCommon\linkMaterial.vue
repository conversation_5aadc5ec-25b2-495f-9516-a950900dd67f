<template>
    <basic-container v-loading="pageLoading">
        <div class="smart_views_linkMaterial" id="smart_views_linkMaterial">
            <el-row style="padding-bottom: 10px">
                <el-col :span="12" :offset="12" style="text-align: right">
                    <el-button type="primary" @click="createMaterial">Create Material</el-button>
                    <el-button type="primary" @click="importMaterial">Import Material</el-button>
                </el-col>
            </el-row>
            <el-row>
                <common-table
                        v-loading="tableLoading"
                        :element-loading-text="$t('loading')"
                        border
                        fit
                        style="width: 100%"
                        row-key="id"
                        ref="link_material_table"
                        stripe
                        height="400"
                        :size="tableOption.size"
                        :data="dataList"
                        :option="tableOption"
                        :filters="tableOption.filters"
                        filter-by-local
                ></common-table>
            </el-row>
            <!-- download template-->
            <el-dialog
                    :visible.sync="downloadTemplateDia"
                    append-to-body
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    :show-close="false"
                    title="Material Template">
                <download-template
                        v-if="downloadTemplateDia"
                        :template-show-download="templateShowDownload"
                        :customer-list="filterList.customerList"
                        :product-line-list="filterList.productLineList"
                        :form-purpose="'Material'"
                        source-type="bom"
                        :sample-id="sampleId"
                        disabled-customer-info
                        @cancelDia="closeDia">
                    <template slot="downloadTemplateSlot" v-if="templateShowDownload">
                        <el-button type="primary" size="small" @click="showImportMaterials">Import Materials</el-button>
                    </template>
                </download-template>
            </el-dialog>
            <sgs-batch-upload
                    v-if="isLoadUpload"
                    title="Import Material"
                    append-to-body
                    :systemID="1"
                    :limit="1"
                    :handle-upload-success="uploadSuccess"
                    :handle-upload-error="uploadError"
                    ref="batchUpload"
                    accept=".xls,.xlsx"
                    :upload-url="'/api/sgs-pbm/sample/web/v1/upload?dataType=material&operation=preview&sampleId='+sampleId"
                    :attachment-type-options="[]"
                    attachment-type-default-value=""
                    :file-max-sizes="20">
            </sgs-batch-upload>
            <!--     Preview Data       -->
            <el-dialog
                    :visible.sync="showPreviewDialog"
                    append-to-body
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    :show-close="false"
                    width="70%"
                    title="Preview Material">
                <preview-material-data
                    v-if="showPreviewDialog"
                    :preview-data-list="previewDataList"
                    :preview-data-template="previewDataTemplate"
                    ref="material_preview_data"
                ></preview-material-data>
                <span slot="footer" class="dialog-footer">
                <el-button @click="showPreviewDialog = false">Cancel</el-button>
                <el-button v-loading="saveBtnLoading" type="primary" @click="saveBillOfMaterial" >
                    Import
                </el-button>
            </span>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
import {mapGetters} from 'vuex';
import downloadTemplate from "./downloadTemplate.vue";
import sampleApi from "@/api/newSamples";
import commonTable from "@/components/tableList/commonTable.vue";
import {queryScmCustomer} from "@/api/customer/scmCustomer";
import {getProductLine} from "@/api/common";
import {validatenull} from "@/util/validate";
import PreviewMaterialData from "@/views/customer/materialAndProductCommon/previewMaterialData";

export default {
    name: "linkMaterial",
    data() {
        return {
            pageLoading: false,
            tableLoading: false,
            downloadTemplateDia: false,
            templateShowDownload: false,
            isLoadUpload:false,
            showPreviewDialog:false,
            saveBtnLoading:false,
            filterList: {
                customerList: [],
                productLineList: []
            },
            previewDataList:[],
            previewDataTemplate:{},
            dataList: [],
            originalDataList: [],
            tableOption: {
                size: "small",
                border: true,
                menuShow: false,
                filters: {},
                showSortIcon: false,
                index: false,
                selection: true,
                action: false,
                actionWidth: 80,
                column: [
                    {label: 'Material ID', prop: 'sampleNo', filter: true, slot: false,minWidth:220,type:"Input"},
                    {label: 'Material Code', prop: 'sampleCode', filter: true, slot: false,minWidth:220,type:"Input"},
                    {label: 'Material Name', prop: 'sampleName', filter: true, slot: false,minWidth:220,type:"Input"},
                    {label: 'Material Type', prop: 'sampleType', filter: true, slot: false,minWidth:220,type:"Input"},
                    {label: 'Material Supplier', prop: 'supplierName', filter: true, slot: false,minWidth:220,type:"Input"},
                ]
            },
        }
    },
    methods: {
        saveBillOfMaterial(){
            let importDataList = this.$refs.material_preview_data.getImportData();
            //保存数据
            let param = {
                objectId:this.sampleId,
                importType:'bom',
                data:importDataList
            }
            sampleApi.importMaterial(param).then(res=>{
                if(res.status==200){
                    this.isLoadUpload = false;
                    this.showPreviewDialog = false;
                    this.$emit('cancelDia');
                }
            }).catch(err=>{

            })
        },
        createMaterial() {
            this.templateShowDownload=false;
            this.downloadTemplateDia = true;
        },
        importMaterial() {
            this.templateShowDownload=true;
            this.downloadTemplateDia = true;
        },
        showImportTemplate() {
            this.downloadTemplateDia = true;
        },
        showImportMaterials() {
            this.isLoadUpload = false;
            this.$nextTick(() => {
                this.isLoadUpload = true;
                this.$nextTick(() => {
                    this.$refs.batchUpload.open();
                });
            });
        },
        closeDia() {
            this.downloadTemplateDia = false;
        },
        getSelection(){
            return this.$refs.link_material_table.getSelection();
        },
        uploadSuccess(data) {
            let {list,template} = data.data;
            this.previewDataList = list;
            this.previewDataTemplate = template;
            this.isLoadUpload = false;
            this.showPreviewDialog = true;
        },
        uploadError() {
            console.log("upload error");
        },
        search() {
            this.tableLoading = true
            this.dataList = []
            let param = {id: this.sampleId,current:1,size:1000};
            sampleApi.billApi.unBomMaterialPage(param)
                .then((res) => {
                    if (res.status == 200 && res.data && res.data.data && res.data.data.records) {
                        this.dataList = res.data.data.records;
                        this.originalDataList = res.data.data.records;
                    }
                    this.tableLoading = false
                })
                .catch((err) => {
                    console.log("err", err)
                    this.tableLoading = false
                })
        },
        async initClientRelationCustomerList(relationshipType) {
            return new Promise((resolve) => {
                const params = {};
                const list = [];
                const item = {};
                item.relationshipType = relationshipType;
                /*if(customerName != null){
                                item.customerName = customerName;
                            }*/
                params.page = 1;
                params.rows = 1000;
                item.buCode =
                    this.userInfo.productLineCode === "all"
                        ? this.userInfo.defaultProductLineCode
                        : this.userInfo.productLineCode;
                item.customerNo = this.userInfo.bossNo;
                list.push(item);
                params.list = list;
                queryScmCustomer(params)
                    .then(
                        (res) => {
                            if (res.status == 200 && res.data) {
                                resolve(res.data.rows);
                            } else {
                                resolve(false);
                            }
                        },
                        (error) => {
                            resolve(false);
                        }
                    )
                    .catch((err) => {
                        resolve(false);
                    });
            });
        },
        async initTemplateCustomerListForSupplier() {
            //再查询所有的模板 + 自身关联的buyer - 自身的模板
            let buyerList = await this.initClientRelationCustomerList("buyer");
            let buyerGroupCode = (buyerList || [])
                .filter((b) => b.scmCustomerGroupCode)
                .map((b) => b.scmCustomerGroupCode);

            let currentUser = this.userInfo.userName;
            let param = {
                currentUser, //必传
                formPurpose: "Material", //必传
            };
            //查询自身的模板
            sampleApi.queryProductViewCustomerList(param).then((res) => {
                let supplierCustomerList = [];
                if (res.status == 200 && res.data && res.data.status == 200) {
                    supplierCustomerList = res.data.data || [];
                }
                sampleApi.queryProductViewCustomerList({ formPurpose: "Material" })
                    .then((res) => {
                        if (res.status == 200 && res.data && res.data.status == 200) {
                            let customerList = res.data.data || [];
                            if (this.role.isSupplier) {
                                customerList = customerList.filter((c) =>
                                    buyerGroupCode.includes(c.customerGroupCode)
                                );
                            }
                            customerList = [
                                ...new Set([...customerList, ...supplierCustomerList]),
                            ];
                            this.resetCustomerList(customerList);
                        }
                    });
            });
        },
        initTemplateCustomerList() {
            let currentUser = this.role.isSGS ? "" : this.userInfo.userName;
            let param = {
                currentUser, //必传
                formPurpose: "Material", //必传
            };
            sampleApi.queryProductViewCustomerList(param).then((res) => {
                if (res.status == 200 && res.data && res.data.status == 200) {
                    let customerList = res.data.data || [];
                    this.resetCustomerList(customerList);
                }
            });
        },
        resetCustomerList(customerList) {
            let newList = [];
            let cgs = [];
            customerList .filter((c) => c.customerGroupCode)
                .forEach((c) => {
                    let { customerGroupCode } = c;
                    if (!cgs.includes(customerGroupCode)) {
                        newList.push(c);
                        cgs.push(customerGroupCode);
                    }
                });
            this.filterList.customerList = newList;
        },
        initProductLine() {
            getProductLine().then((res) => {
                if (res.status == 200 && res.data) {
                    const data = res.data.data || [];
                    let currentUserPL = (this.userInfo.productLineCodes || "").split(",");
                    if (
                        !this.userInfo.productLineCodes &&
                        this.userInfo.productLineCode == "all"
                    ) {
                        this.filterList.productLineList = data;
                        return;
                    }
                    this.filterList.productLineList = data.filter((da) =>
                        currentUserPL.includes(da.productLineCode)
                    );
                }
            });
        },
        haseRole(type, role) {
            if (validatenull(type) || validatenull(role)) {
                return false;
            }
            if (validatenull(this.userInfo.dimensions)) {
                return false;
            } else {
                if (this.userInfo.dimensions.hasOwnProperty(type)) {
                    if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }
        },
    },
    created() {
        this.search();
    },
    mounted() {
        let { productLineCode,
            productLineName,
            customerGroupCode,
            customerGroupName} = this.customerObj;
        this.filterList.productLineList = [{productLineCode,productLineName}];
        this.filterList.customerList = [{customerGroupCode,customerGroupName}];
    },
    components:{PreviewMaterialData, commonTable,downloadTemplate},
    computed: {
        ...mapGetters(['permission', 'userInfo', 'language']),
        role() {
            return {
                isSGS: this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser"),
                isSupplier: this.haseRole("UserRole", "Supplier"),
                isThirdPartyLab: this.haseRole("UserRole", "ThirdPartyLab"),
            };
        },
    },
    props: {
        sampleId: {
            type: String,
            default() {
                return ''
            }
        },
        customerObj:{
            type:Object,
            default(){
                return{
                    productLineCode:'',
                    productLineName:'',
                    customerGroupCode:'',
                    customerGroupName:''
                }
            }
        }
    },
}
</script>

<style lang="scss" scoped>
.smart_views_linkMaterial {

}
</style>