<template>
  <div class="sgs-logo">
    <div class="sgs-logo_subtitle" key="0" @click="toPage()">
      <img src="/img/SGS_SMART_logo.png" width="80" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'logo',
  setup() {
    const toPage = () =>{
      window.location.href = '/#/' 
    }
    return {
      toPage
    }
  },
}
</script>

<style lang="scss" scoped>
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-active {
  transition: opacity 2.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.sgs-logo {
  top: 0;
  left: 0;
  width: 100px;
  height: 80px;
  line-height: 80px;
  font-size: 20px;
  overflow: hidden;
  z-index: 1024;
}
.sgs-logo_subtitle {
  height: 100%;
  margin-top: 12px;
  cursor: pointer;
}
</style>
