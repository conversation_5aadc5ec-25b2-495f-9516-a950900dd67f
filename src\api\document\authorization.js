import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/submit',
        method: 'post',
        data: form
    })
}

export const batchAdd = (params) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/batchSubmit',
        method: 'post',
        data: params
    })
}
export const checkDataBeforeSubmit = (params) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/checkDataBeforeSubmit',
        method: 'post',
        data: params
    })
}


export const getPrograms = () => {
    return request({
        url: '/api/sgs-mart/authoriaztion/getAuthProgram',
        method: 'get',
    })
}

export const getPageList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}


export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}