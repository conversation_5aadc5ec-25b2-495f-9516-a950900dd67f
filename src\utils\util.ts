/**
 * localStorage 存入和获取
 * set存入数据
 * ex: 存入字符串 local.set('name': 'epan') / 存入对象local.set('sex': { a: boy, b: gril })
 * get获取数据
 * ex: local.get('name')
 * clear清除全部数据
 * ex: local.clear()
 * remove删除数据
 * ex: local.remove('name')
 **/
const local = {
  set(key: string, val?: any) {
    typeof val === 'string'
      ? localStorage.setItem(key, val)
      : localStorage.setItem(key, JSON.stringify(val))
  },
  get(key: string) {
    const val = localStorage.getItem(key)
    if (val) {
      const flag = val.startsWith('{') || val.startsWith('[')
      if (!flag) {
        return val
      } else {
        return JSON.parse(val)
      }
    }
  },
  clear() {
    localStorage.clear()
  },
  remove(key: string) {
    localStorage.removeItem(key)
  },
}

// 重定向URL
const redirect = (systemId: string | number) => {
  window.location.href = `/#/login?extraWeb=1&systemId=${systemId}&redirect=${window.location.pathname}${encodeURIComponent(window.location.search)}`
}

let apiHost = ''
const currentEnv = import.meta.env.MODE
if (currentEnv === 'uat') {
  apiHost = '/api'
} else if (currentEnv === 'production') {
  apiHost = '/api'
} else {
  apiHost = '/api'
}

/**
 * 对象深拷贝
 * @param data - 要拷贝的对象
 * @returns 拷贝后的对象
 */
const deepClone = (data: any) => {
  const type = getObjType(data)
  let obj: any
  if (type === 'array') {
    obj = []
  } else if (type === 'object') {
    obj = {}
  } else {
    // 不再具有下一层次
    return data
  }
  if (type === 'array') {
    for (let i = 0, len = data.length; i < len; i++) {
      ;(obj as any[]).push(deepClone(data[i]))
    }
  } else if (type === 'object') {
    for (const key in data) {
      ;(obj as { [key: string]: any })[key] = deepClone(data[key])
    }
  }
  return obj
}

/**
 * 获取对象类型
 * @param obj - 要获取类型的对象
 * @returns 对象的类型字符串
 */
const getObjType = (obj: any) => {
  const toString = Object.prototype.toString
  const map: { [key: string]: string } = {
    '[object Boolean]': 'boolean',
    '[object Number]': 'number',
    '[object String]': 'string',
    '[object Function]': 'function',
    '[object Array]': 'array',
    '[object Date]': 'date',
    '[object RegExp]': 'regExp',
    '[object Undefined]': 'undefined',
    '[object Null]': 'null',
    '[object Object]': 'object',
  }
  if (obj instanceof Element) {
    return 'element'
  }
  return map[toString.call(obj)]
}

export { local, redirect, apiHost, deepClone, getObjType }
