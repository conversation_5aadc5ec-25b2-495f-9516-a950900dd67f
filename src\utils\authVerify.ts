import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { redirect } from './util'
import { getToken } from './auth'
import store from '../store'
import { validatenull } from '@/utils/validate'

export const authVerify = () => {
  const route = useRoute()
  const token = getToken() || ''
  const systemId = ref((route.query.systemId as string) || '')
  if (systemId.value) {
    store.commit('SET_SYSTEM_ID', systemId.value)
  }

  if (token) {
    store.commit('SET_TOKEN', token)
    store.dispatch('LoginByToken', token)
  } else {
    redirect(systemId.value)
  }
}

export function hasRoleCheck(type: any, role: any, userInfo: any) {
  if (validatenull(type) || validatenull(role)) {
    return false
  }
  if (validatenull(userInfo.dimensions)) {
    return false
  }
  if (userInfo.dimensions.hasOwnProperty(type)) {
    return userInfo.dimensions[type].indexOf(role) >= 0
  }
  return false
}

/**
 * 校验权限
 * @param type - 权限类型
 * @param role - 角色
 * @param userInfo - 用户信息
 * @returns 是否有权限
 */
export const hasRole = (type: string, role: string, userInfo: any) => {
  if (validatenull(type) || validatenull(role)) {
    return false;
  }
  if (validatenull(userInfo.dimensions)) {
    return false;
  } else {
    if (userInfo.dimensions.hasOwnProperty(type)) {
      if (userInfo.dimensions[type].indexOf(role) >= 0) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
}
