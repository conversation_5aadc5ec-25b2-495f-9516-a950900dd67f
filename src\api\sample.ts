import request from './request'

// 定义通用的请求参数类型
type RequestParams = Record<string, any>

/**
 * 查询商品列表
 * @param params - 查询参数，类型为包含任意键值对的对象
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const querySamplesListPage = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/page',
    method: 'post',
    data: {
      ...params,
    },
  })
}

export const getSampleTemplateList = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/templateList',
    method: 'post',
    data: {
      ...params,
    },
  })
}

export const downloadMaterialTemplate = (
  params: RequestParams,
): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/download',
    method: 'post',
    responseType: 'blob',
    data: {
      ...params,
    },
  })
}

export const exportSampleData = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/export',
    method: 'post',
    responseType: 'blob',
    data: {
      ...params,
    },
  })
}

/**
 * 查询ProductViewCustomer信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryProductViewCustomerList = (
  params: RequestParams,
): Promise<any> => {
  return request({
    url: '/api/sgsapi/DFFV2Api/dff/queryProductViewCustomerList',
    method: 'post',
    data: {
      ...params,
    },
  })
}

export const checkToTrf = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/check',
    method: 'post',
    data: {
      ...params,
    },
  })
}

export const actionSamples = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/action',
    method: 'post',
    data: {
      ...params,
    },
  })
}

// table动态表头
export const filedList = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-pbm/sample/web/v1/filed/list',
    method: 'post',
    data: {
      ...params,
    },
  })
}
