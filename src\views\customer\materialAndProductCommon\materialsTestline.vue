<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_common_materialsTestline" id="sgs_smart_common_materialsTestline">
            <el-table
                    v-if="!pageLoading"
                    fit
                    border
                    size="mini"
                    height="650"
                    :data="materialTestLineList"
            >
                <el-table-column
                        show-overflow-tooltip
                        v-for="(col,ind) in columns"
                        :minWidth="col.width || 250"
                        :key="'col_'+ind"
                        :prop="col.prop">
                    <template #header>
                        <div style="text-align: center">{{col.label}}</div>
                        <el-row>
                            <el-col :span="8">
                                <el-select style="width: 100%" size="mini" v-model="materialTestLineFilter[col.prop]['condition']">
                                    <el-option label="In" value="in"></el-option>
                                    <el-option label="Not In" value="notIn"></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="16">
                                <el-input v-if="col.prop!='customerConclusion'"
                                          clearable
                                          v-model="materialTestLineFilter[col.prop]['conditionValue']"
                                          size="mini" :placeholder="col.label"></el-input>
                                <el-select v-if="col.prop=='customerConclusion'"
                                           size="mini" clearable filter multiple
                                           collapse-tags
                                           v-model="materialTestLineFilter[col.prop]['conditionValue']"
                                           :placeholder="col.label">
                                    <el-option
                                            v-for="(tr,ind) in testResultSelectList"
                                            :key="'tr_'+ind"
                                            :label="tr.label"
                                            :value="tr.value"
                                    ></el-option>
                                </el-select>
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </basic-container>
</template>

<script>
    import sampleApi from '@/api/newSamples'
    export default {
        name: "materialsTestline",
        data() {
            return {
                pageLoading: true,
                materialTestLineList:[],
                materialTestLineListOriginal:[],
                materialTestLineFilter:{},
                columns:[
                    {label:'Material ID',prop:'sampleNo'},
                    {label:'Material Name',prop:'sampleName'},
                    {label:'Material Code',prop:'sampleCode'},
                    {label:'Material Description',prop:'sampleDescription'},
                    {label:'Test Name',prop:'evaluationAlias'},
                    {label:'Report Number',prop:'trfReportNo'},
                    {label:'Test Result',prop:'customerConclusion',width:300}
                ],
                testResultSelectList:[
                    {
                        "label": "Inconclusive",
                        "value": "INCONCLUSIVE"
                    },
                    {
                        "label": "NA",
                        "value": "NA"
                    },
                    {
                        "label": "Exempt",
                        "value": "EXEMPT"
                    },
                    {
                        "label": "Data Only",
                        "value": "DATA ONLY"
                    },
                    {
                        "label": "Fail",
                        "value": "FAIL"
                    },
                    {
                        "label": "Pass",
                        "value": "PASS"
                    },
                    {
                        "label": "See Result",
                        "value": "SEE RESULT"
                    }
                ],
            }
        },
        methods: {
            initPage() {
                this.columns.forEach(col=>{
                    let {prop} = col;
                    let conditionValue = '';
                    let condition = 'in';
                    this.$set(this.materialTestLineFilter,prop,{condition,conditionValue});
                })
                this.loadTestLine();
                this.pageLoading = false;
            },
            loadTestLine(){
                let param = {
                    id:this.objectId
                }
                this.materialTestLineList = [];
                this.materialTestLineListOriginal = [];
                sampleApi.loadTestLinePage(param).then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        let materialTestLineList = res.data.data || [];
                        materialTestLineList.forEach((mt,tseq)=>{
                            let {sampleNo,sampleName,sampleCode,sampleDescription,testLineList} = mt;
                            let materialId = mt.id;
                            if(testLineList && testLineList.length>0){
                                testLineList.forEach((tl,seq)=>{
                                    let tlSeq = tseq * 100 + seq;
                                    let {evaluationAlias,customerConclusion,conclusion,id,trfReportNo} = tl;
                                    this.materialTestLineList.push({
                                      tlSeq,evaluationAlias,customerConclusion,id,sampleNo,sampleName,sampleCode,sampleDescription,materialId,trfReportNo,conclusion
                                    })
                                    this.materialTestLineListOriginal.push({
                                      tlSeq,evaluationAlias,customerConclusion,id,sampleNo,sampleName,sampleCode,sampleDescription,materialId,trfReportNo,conclusion
                                    })
                                })
                            }else{
                                let tlSeq = (tseq+1) * 10000;
                                this.materialTestLineList.push({
                                  tlSeq,sampleNo,sampleName,sampleCode,sampleDescription,materialId
                                })
                                this.materialTestLineListOriginal.push({
                                  tlSeq,sampleNo,sampleName,sampleCode,sampleDescription,materialId
                                })
                            }
                        });
                        this.materialTestLineList.sort((a,b)=>{
                            return a.tlSeq - b.tlSeq;
                        })
                        this.materialTestLineListOriginal.sort((a,b)=>{
                            return a.tlSeq - b.tlSeq;
                        })
                    }else{
                        this.$notify.error("Query data fail");
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    console.log("search tl error")
                    this.pageLoading = false;
                })
            },
            pageSearch(){
                if(this.pageLoading){
                    return;
                }
                if(!this.materialTestLineListOriginal || this.materialTestLineListOriginal.length==0){
                    return;
                }
                let props = Object.keys(this.materialTestLineFilter);
                let afterFilterData = JSON.parse(JSON.stringify(this.materialTestLineListOriginal));
                props.forEach(prop=>{
                    let {condition,conditionValue} = this.materialTestLineFilter[prop];
                    if(conditionValue){
                        afterFilterData = afterFilterData.filter(d=> {
                            if(prop=='customerConclusion'){
                                if(!conditionValue || conditionValue.length==0){
                                    return true;
                                }
                                //select ，数组内容判断
                                if(prop=='customerConclusion'){
                                    if(!conditionValue || conditionValue.length==0){
                                        return true;
                                    }
                                    if(condition=='in'){
                                        return (conditionValue || []).includes((d['conclusion'] || '').toUpperCase());
                                    }
                                    return !(conditionValue || []).includes((d['conclusion'] || '').toUpperCase());
                                }
                                if(condition=='in'){
                                    return (conditionValue || []).includes((d[prop] || '').toUpperCase());
                                }
                                return !(conditionValue || []).includes((d[prop] || '').toUpperCase());
                            }
                            if(condition=='in'){
                                return (d[prop] || '').toUpperCase().indexOf(conditionValue.toUpperCase()) > -1;
                            }
                            return (d[prop] || '').toUpperCase().indexOf(conditionValue.toUpperCase()) == -1;
                        })
                    }
                })
                this.materialTestLineList = afterFilterData;
            },

        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {
            materialTestLineFilter:{
                immediate:false,
                deep:true,
                handler(newV,oldV){
                    this.pageSearch();
                }
            }
        },
        computed: {},
        props: {
            objectId:''
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_common_materialsTestline {
    }
</style>