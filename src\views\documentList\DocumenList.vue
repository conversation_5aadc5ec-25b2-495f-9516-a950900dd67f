<script setup lang="ts">
import { reactive, onMounted } from 'vue'

const state = reactive({
  title: 'Document List',
})
onMounted(() => {})

import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
const changeLanguage = (lang: string) => {
  locale.value = lang
}
</script>

<template>
  <div class="document-list">
    <h1>
      {{ state.title }}
    </h1>
    <h1>国际化：{{ $t('common.title') }}</h1>
    <el-button @click="changeLanguage('en-US')">English</el-button>
    <el-button @click="changeLanguage('zh-CN')">中文</el-button>
    <el-button>Home Page Request</el-button>
  </div>
</template>

<style lang="scss" scoped></style>
