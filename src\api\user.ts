import request from './request'

/**
 * 通过用户名登录
 * @param tenantId - 租户 ID，类型为字符串
 * @param account - 账号，类型为字符串
 * @param password - 密码，类型为字符串
 * @param type - 登录类型，类型为字符串
 * @param receiveMarketingCommunication - 是否接收营销通讯，默认为 0，类型为数字
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const loginByUsername = (
  tenantId: string,
  account: string,
  password?: string | null,
  type?: string,
  receiveMarketingCommunication?: number,
): Promise<any> => {
  return request({
    url: '/sgs-auth/token',
    method: 'post',
    params: {
      tenantId,
      account,
      password,
      type,
      receiveMarketingCommunication,
    },
  })
}

/**
 * 验证令牌
 * @param token - 令牌，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const validateToken = (token: string): Promise<any> => {
  return request({
    url: '/sgs-auth/token/validate',
    method: 'post',
    params: {
      token,
    },
  })
}

/**
 * 获取按钮列表
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const getButtons = (): Promise<any> => {
  return request({
    url: `/nkop-system/menu/buttons?hash=${new Date().getTime()}`,
    method: 'get',
  })
}

/**
 * 获取路由信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const routes = (): Promise<any> => {
  return request({
    url: `/sgs-auth/e-filing/routes?hash=${new Date().getTime()}`,
    method: 'get',
  })
}

/**
 * 设置引导信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const setGuide = (): Promise<any> => {
  return request({
    url: `/sgs-mart/userSetting/saveUserGuide?hash=${new Date().getTime()}`,
    method: 'get',
  })
}

/**
 * 获取用户信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const getUserInfo = (): Promise<any> => {
  return request({
    url: `/user/getUserInfo?hash=${new Date().getTime()}`,
    method: 'get',
  })
}

/**
 * 获取菜单信息
 * @param languageCode - 语言代码，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const getMenu = (languageCode: string): Promise<any> => {
  return request({
    url: `/sgs-auth/routes?hash=${new Date().getTime()}`,
    method: 'get',
    params: {
      languageCode,
    },
  })
}

/**
 * 获取顶部菜单信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const getTopMenu = (): Promise<any> => {
  return request({
    url: `/user/getTopMenu?hash=${new Date().getTime()}`,
    method: 'get',
  })
}

/**
 * 用户登出
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const logout = (): Promise<any> => {
  return request({
    url: `/user/logout?hash=${new Date().getTime()}`,
    method: 'get',
  })
}

/**
 * 电子申报登录
 * @param token - 令牌，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const loginEFiling = (token: string): Promise<any> => {
  return request({
    url: '/sgs-e-filling/sgs-auth/oauth/sgsToken/validate',
    method: 'post',
    params: { token },
  })
}
