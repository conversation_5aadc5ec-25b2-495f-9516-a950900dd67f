
$green: #34A853;
$red: #EA4336;
$grey: #D9D9D9;
$orange: #ff6600;

.dashboard-main {
  margin: 0px;
  padding: 0px 32px 30px;
  background-color: transparent;

  ::v-deep .el-card__body {
    height: 100%;
    padding: 26px 32px !important;
  }

  .first-line {
    height: 435px;
    min-height: 435px;
    display: flex;
  }

  .buyer-report {
    height: 100%;
    background: #fff;
    overflow: hidden;
  }

  .supplier-report {
    padding-left: 12px;
    padding-right: 12px;
    height: 100%;
    background: #fff;
    margin-right: 0px;
    overflow: hidden;
    .slider {
      width: 95%;
      margin: 0 auto;
      margin-top: 32px
    }
  }

  .afl {
    padding-left: 12px;
    padding-right: 12px;
    height: 100%;
    background: #fff;
    margin-right: 0px;
    overflow: hidden;
    border-radius: 5px;
    .slider {
      width: 95%;
      margin: 0 auto;
      margin-top: 32px
    }
  }

  .sgs-box {
    // padding: 6px 12px;
    border-radius: 0;
    box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.02);
    border: 0;
  }

  .report {
    padding-left: 12px;
    padding-right: 12px;
    height: 100%;
    background: #fff;
    overflow: hidden;
    margin-left: 20px;
  }

  .new-tag {
    color: #fff;
    background: #f60;
    font-size: 12px;
    font-weight: 400;
    height: auto;
    line-height: normal;
    padding: 0px 4px;
    margin-top: 15px;
  }

  .lib-a {
    font-size: 16px;
    font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    font-weight: 400;
    line-height: 48px;
    cursor: pointer;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .test-result {
    .pie-chart {
      margin-top: 24px;
    }
    .line-chart {
      position: relative;
    }
    .tit {
      font-size: 14px;
      font-weight: 400;
      color: #656565;
      line-height: 20px;
      span {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 6px;
      }
      .green {
        background-color: $green;
      }
      .red {
        background-color: $red;
      }
      .grey {
        background-color: $grey;
      }
    }
    h2 {
      font-size: 32px;
      font-family: UniversLT;
      color: #1B1B1B;
      line-height: 32px;
    }
  }

  .requisition-status {
    background: transparent;
    margin-top: 0;
    box-shadow: none;
    border: 0;
    ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin-top: 24px;
      margin-bottom: 0;
      li {
        flex: 1;
        flex-shrink: 0;
        padding-left: 30px;
        position: relative;
        a {
          color: #1B1B1B;
          .icon {
            width: 56px;
            height: 56px;
            float: left;
            margin: 12px 32px 0 0;
          }
          // &:hover {
          //   color: $orange;
          //   .icon {
          //     transform: translateX(10px);
          //   }
          // }
        }
        &:first-of-type {
          padding-left: 0;
        }
        &:last-of-type {
          &::after {
            width: 0;
          }
        }
        &::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 100%;
          right: 0;
          background: #E6E6E6;
          bottom: 0;
        }
        .status {
          font-size: 16px;
          font-weight: 400;
          color: #737373;
          line-height: 25px;
          margin-bottom: 10px;
        }
        .count {
          font-size: 56px;
          font-family: UniversLT;
          line-height: 56px;
        }
        svg {
          position: absolute;
          right: 30px;
          bottom: 0;
          bottom: 12px;
        }
      }
    }
    ::v-deep .time-select {
      // padding-left: 20px;
      // background: url('/img/icon_date.png') no-repeat 5px 7px;
      background: transparent;
      // border-bottom: 1px solid #1B1B1B;
      // position: absolute;
      // right: 0;
      // top: -15px;
      // > i {
      //   font-size: 18px;
      // }
      // input {
      //   background: transparent;
      //   border: 0;
      //   color: #1b1b1b;
      //   font-size: 16px;
      // }
      // .el-select__caret {
      //   color: #1b1b1b;
      //   font-size: 18px;
      //   font-weight: bold;
      // }
    }
  }
}
.el-card__body {
  .sgs-group {
      h3 {
        margin-bottom: 0;
      }
  }
}









.page-example3 {
  height: 250px;
  overflow: hidden;

  ul.list {
    margin-top: 20px;
    list-style: none;
    padding: 0;
  }

  .ul-scoll {
    li {
      margin: 6px;
      padding: 5px;
      background: rgba(198, 142, 226, 0.4);
    }
  }
}

.quick-access {
  width: 100px;
  height: 460px;
  background-color: #ffffff;
  border-radius: 8px;
  position: fixed;
  margin: auto;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1;

  a {
    .el-button--primary {
      color: #ffffff;
      background-color: #999;
      border-color: #999;
    }
  }

  a:hover {
    .el-button--primary {
      color: #ffffff;
      background-color: #ff6600;
      border-color: #ff6600;
    }
  }
}

#trf-status {
  margin-top: 20px;
  height: 210px;
  font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  // font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica,
  //   sans-serif;

  .el-card__body {
    padding: 0px !important;
  }

  .box {
    padding-top: 30px;
    height: 215px;
    position: relative;

    .count {
      width: 100%;
      font-size: 50px;
      font-weight: bold;
    }

    .status {
      font-size: 18px;
      font-weight: bold;
      line-height: 25px;
    }
  }

  a {
    text-decoration: none;

    i {
      position: absolute;
      right: 1%;
      top: 65%;
      font-size: 52px;
      float: right;
      color: #d9d9d9;
    }
  }

  a:hover {
    i {
      color: #ff6600;
    }
  }

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;

    li {
      display: inline;
      line-height: 40px;
      float: left;
      padding: 10px;
      width: 20%;
      background-color: #f5f5f5;
      border: 3px solid #ffffff;
      height: 220px;
      overflow: hidden;
    }
  }
}
#afl-trf-status {
  margin-top: 20px;
  height: 210px;
  font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif; 
  // font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica,
  //   sans-serif;

  .el-card__body {
    padding: 0px !important;
  }

  .box {
    padding-top: 30px;
    height: 215px;
    position: relative;

    .count {
      width: 100%;
      font-size: 50px;
      font-weight: bold;
    }

    .status {
      font-size: 18px;
      font-weight: bold;
      line-height: 25px;
    }
  }

  a {
    text-decoration: none;

    i {
      position: absolute;
      right: 1%;
      top: 65%;
      font-size: 52px;
      float: right;
      color: #d9d9d9;
    }
  }

  a:hover {
    i {
      color: #ff6600;
    }
  }

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;

    li {
      display: inline;
      line-height: 40px;
      float: left;
      padding: 10px;
      width: 14%;
      background-color: #f5f5f5;
      border: 3px solid #ffffff;
      height: 220px;
      overflow: hidden;
    }
  }
}
#status-count {
  height: 300px;

  // font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica,
  //   sans-serif;
  font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif; 

  .el-card__body {
    padding: 0px;
  }

  .box {
    margin: 0px;
    line-height: 65px;
    font-size: 30px;
    color: #ffffff;

    .icon {
      text-align: center;
    }

    .count {
      font-size: 18px;
      font-weight: bold;
      color: #ff6600;
    }
  }

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;

    li {
      height: 75px;
      line-height: 75px;
      float: left;
      padding: 0px;
      width: 100%;
      background-color: #f5f5f5;
      border: 5px solid #ffffff;
    }
  }
}

.document {
  ui {
    li {
      list-style: none;
      line-height: 48px;
    }
  }
}

@media screen and (max-width:1600px) {
  .dashboard-main {
    .requisition-status {
      ul li {
        padding: 0 20px;
        &.afl-status {
          img {
            float: none !important;
          }
          .status {
            margin: 10px 0;
          }
        }
        .en-status {
          font-size: 12px;
          height: 32px;
          word-break: break-word;
        }
        a .icon {
          margin-right: 20px;
        }
        .count {
          font-size: 24px;
          font-family: UniversLT;
          line-height: 32px;
        }
      }
    }
  }
}
