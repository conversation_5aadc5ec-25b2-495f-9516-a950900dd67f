import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/submit',
        method: 'post',
        data: form
    })
}
export const updPublish = (form) => {
    return request({
        url: '/api/sgs-training/class/upd-publish',
        method: 'post',
        data: form
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-training/class/class-manager/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const getPage = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const getTestPackage = (id) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/detail',
        method: 'get',
        params: {
            id,
        }
    })
}
export const completeClass = (id) => {
    return request({
        url: '/api/sgs-training/class/complete',
        method: 'get',
        params: {
            id,
        }
    })
}