<template>
  <el-dialog class="upload-dialog" v-model="dialogVisible" :title="$t('work.pageCreate.selectFiles')" width="35%">
    <div class="upload-dialog-content">
      <div class="file-selector">
        <input type="file" ref="fileInput" @change="handleFileChange" multiple style="display: none" :accept="props.limit.join(',')">
        <div style="display: flex;column-gap: 0px;width: 95%;align-items: center;">
          <span style="font-size: 14px;color: #909399;white-space: nowrap;margin-right: 15px;"> {{ $t('work.pageCreate.fileName') }}:</span>
          <el-input :placeholder="$t('work.pageCreate.fileName')" v-model="searchFileName">
          </el-input><el-button type="primary" @click="triggerFileSelect">{{ $t('work.pageCreate.selectFile') }}</el-button>
        </div>
        <div style="display: flex;column-gap: 0px;width: 70%;align-items: center;white-space: nowrap;margin-top:10px;font-size: 12px;color: #878787;">
          <span>{{ fileTypeLimit }}</span>
        </div>
      </div>
      <div v-if="!filteredFiles.length" class="no-files">
        {{ $t('work.pageCreate.noAttachments') }}
      </div>
      <div v-else v-for="(file, index) in filteredFiles.filter((item: FileItem) => item.delFlag != 1)" :key="index" style="width: 100%;">
        <div class="selected-file-item">
          <span class="file-name"><img :src="getFileIcon(file.fileName)" style="width: 16px;height: 16px;margin-right: 5px;">{{ decodeURIComponent(file.fileName)  }}</span>
          <el-icon class="delete-icon" @click="removeSelectedFile(getOriginalIndex(file))"><CircleCloseFilled /></el-icon>
          <el-switch v-model="file.authorization" size="small" :active-text="$t('work.pageCreate.authorization')" :active-value="1" :inactive-value="0" @change="handleAuthorizationChange(file)" />
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmUploadFiles">{{ $t('work.pageCreate.confirm') }}</el-button>
        <el-button @click="closeDialog">{{ $t('work.pageCreate.cancel') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, PropType } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { uploadFile } from '@/api/upload'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()
const fileTypeLimit = computed(() => {
  if(locale.value == 'zh-CN'){
    return `只能上传${props.limit.join('、')}格式文件`
  }else{
    return `Only ${props.limit.join('、')} files are allowed`
  }
})

// Define the FileItem interface for better type safety
interface FileItem {
  [any:string]:any
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  attachments: {
    type: Array as PropType<FileItem[]>,
    default: () => []
  },
  limit: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  videoForm: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'update:attachments', 'contentUpdated'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const fileInput = ref()
const searchFileName = ref('')
const selectedFiles = ref<FileItem[]>([])

// When dialog is opened, initialize with existing attachments
const openDialog = () => {
  selectedFiles.value = props.attachments.length 
    ? JSON.parse(JSON.stringify(props.attachments)) 
    : []
}

// Watch for modelValue changes to initialize selected files
import { watch } from 'vue'
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    openDialog()
  }
})

const closeDialog = () => {
  searchFileName.value = ''
  selectedFiles.value = []
  emit('update:modelValue', false)
}

const triggerFileSelect = () => {
  fileInput.value.click()
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  
  if (target.files) {
    for (let i = 0; i < target.files.length; i++) {
      const file = target.files[i]
      
      // Check if the file has an allowed extension
      const allowedExtensions = props.limit
      const fileName = file.name.toLowerCase()
      const isAllowedType = allowedExtensions.some(ext => fileName.endsWith(ext))
      
      if (!isAllowedType) {
        ElMessage.error(`${t('work.pageCreate.fileTypeError')} ${file.name}`)
        continue
      }

      if (beforeAttachmentUpload(file)) {
        selectedFiles.value.push({
          file: file,
          fileName: file.name,
          authorization: 1 // Default to authorized
        })
      }
    }
  }
  // Reset the file input so the same file can be selected again
  if (fileInput.value) fileInput.value.value = ''
}

const beforeAttachmentUpload = (file: File) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error(t('work.pageCreate.fileSizeLimit'))
    return false
  }
  return true
}

const removeSelectedFile = (index: number) => {
  selectedFiles.value[index].delFlag = 1
}

const contentArray = ref<{content: string, url: string, authorization: number}[]>([])

const getPreviewContent = (url: string, fileType: string, authorization: number) => {
  const fileExtension = fileType.toLowerCase()
  
  const authClass = authorization === 1 ? 'authorization' : 'unauthorization'
  
  if (fileExtension === 'mp4' || fileExtension === 'ogg' || fileExtension === 'webm') {
    // Determine the correct MIME type
    let mimeType = 'video/mp4'
    if (fileExtension === 'ogg') mimeType = 'video/ogg'
    if (fileExtension === 'webm') mimeType = 'video/webm'
    
    return {
      content: `<div class="preview-item ${authClass}">
          <video controls style="width: 100%; max-height: 100%;">
              <source src="${url}" type="${mimeType}">
              Your browser does not support the video tag.
          </video>
      </div>`,
      url,
      authorization
    }
  } else if (fileExtension === 'pdf') {
    return {
      content: `<div class="preview-item ${authClass}">
          <iframe src="/api/sgs-e-filling/sgs-knowledge/knowledge/file/preview?url=${url}" style="width: 100%; height: 600px; border: none;"></iframe>
      </div>`,
      url,
      authorization
    }
  } else if (['doc', 'docx'].includes(fileExtension)) {
    const encodedUrl = encodeURIComponent(url)
    return {
      content: `<div class="preview-item ${authClass}">
          <iframe src="https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}" 
              style="width: 100%; height: 600px; border: none;">
          </iframe>
      </div>`,
      url,
      authorization
    }
  } else if (['xls', 'xlsx'].includes(fileExtension)) {
    const encodedUrl = encodeURIComponent(url)
    return {
      content: `<div class="preview-item ${authClass}">
          <iframe src="https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}" 
              style="width: 100%; height: 600px; border: none;">
          </iframe>
      </div>`,
      url,
      authorization
    }
  }
  return {
    content: '',
    url,
    authorization
  }
}

// Handle authorization change for a file
const handleAuthorizationChange = (file: FileItem) => {
  // For files that have already been processed and have content in contentArray,
  // we need to update their authorization status
  if (file.fileUrl) {
    const contentIndex = contentArray.value.findIndex(item => 
      item.url === file.fileUrl || 
      file.fileUrl.includes(encodeURIComponent(item.url)) ||
      item.url.includes(encodeURIComponent(file.fileUrl))
    )
    
    if (contentIndex > -1) {
      // Update the authorization in contentArray
      contentArray.value[contentIndex].authorization = file.authorization
      
      // Also update the HTML content to reflect the new authorization status
      const authClass = file.authorization === 1 ? 'authorization' : 'unauthorization'
      const oldAuthClass = file.authorization === 1 ? 'unauthorization' : 'authorization'
      
      // Replace the class in the content HTML
      contentArray.value[contentIndex].content = contentArray.value[contentIndex].content.replace(
        oldAuthClass, 
        authClass
      )
    }
  }
}

const confirmUploadFiles = async () => {
  debugger
  try {
    const newAttachments = [] as any[]
    // Store existing content array values
    const existingContentArray = [...contentArray.value]
    // Clear only for new uploads
    contentArray.value = []
    
    // Process files with actual File objects (new uploads)
    for (const fileData of selectedFiles.value.filter(f => f.delFlag !== 1 && f.file)) {
      const formData = new FormData()
      formData.append('file', fileData.file)
      formData.append('systemID', '59')
      
      const res: any = await uploadFile(formData)
      
      if (res?.rows && res.rows.length > 0) {
        const fileUrl = res.rows[0]
        const fileExtension = fileData.file.name.split('.').pop()?.toLowerCase() || ''
        
        // Get the preview content with authorization status
        const previewItem = getPreviewContent(fileUrl, fileExtension, fileData.authorization)
        
        // Add to content array
        if (previewItem.content) {
          contentArray.value.push(previewItem)
        }
        
        // Add to form's video links
        if (!props.videoForm.videoFormLink) {
          props.videoForm.videoFormLink = []
        }
        props.videoForm.videoFormLink.push(fileUrl)
        
        // Add to attachments
        newAttachments.push({
          fileName: fileData.file.name,
          fileType: fileData.file.type,
          fileUrl: fileUrl,
          authorization: fileData.authorization,
          status: 1
        })
      }
    }
    
    // Process existing files (from previous uploads)
    for (const fileData of selectedFiles.value.filter(f => f.delFlag !== 1 && !f.file && f.fileUrl)) {
      // Get file extension from the filename or URL
      const fileExtension = fileData.fileName 
        ? fileData.fileName.split('.').pop()?.toLowerCase() 
        : fileData.fileUrl.split('.').pop()?.toLowerCase() || ''
      
      // Get the preview content with authorization status - use the fileData's authorization value
      const previewItem = getPreviewContent(fileData.fileUrl, fileExtension, fileData.authorization)
      
      // Add to content array if valid
      if (previewItem.content) {
        contentArray.value.push(previewItem)
      }
      
      // Add to attachments list
      newAttachments.push({
        fileName: fileData.fileName,
        fileType: fileData.fileType || fileExtension,
        fileUrl: fileData.fileUrl,
        authorization: fileData.authorization,
        status: 1
      })
    }
    
    // Combine existing content with new content
    const mergedContent = [...existingContentArray, ...contentArray.value]
    
    // Remove duplicates by URL
    const uniqueContent = mergedContent.filter((item, index, self) => 
      index === self.findIndex(t => t.url === item.url)
    )
    
    // Update video form content without replacing existing content
    if (uniqueContent.length > 0) {
      // Only use new content if we're starting fresh
      if (!props.videoForm.videoFormContent || props.videoForm.videoFormContent === '') {
        props.videoForm.videoFormContent = contentArray.value.map(item => item.content).join('')
      } else if (contentArray.value.length > 0) {
        // Otherwise append new content to existing content
        props.videoForm.videoFormContent = uniqueContent.map(item => item.content).join('')
      }
    }
    
    // Emit the updated attachments (both existing and new)
    emit('update:attachments', newAttachments)
    emit('contentUpdated', uniqueContent)
    
    // Close dialog
    closeDialog()
  } catch (error) {
    console.error('Upload error:', error)
    ElMessage.error(t('work.pageCreate.uploadFailed'))
  }
}

// Computed property to filter files based on search input
const filteredFiles = computed(() => {
  if (!searchFileName.value.trim()) {
    return selectedFiles.value
  }
  
  return selectedFiles.value.filter((file: FileItem) => 
    file.fileName.toLowerCase().includes(searchFileName.value.toLowerCase()) && file.delFlag != 1
  )
})

// Helper function to get the original index in selectedFiles array
const getOriginalIndex = (file: FileItem) => {
  return selectedFiles.value.findIndex(f => f === file)
}

// Function to get appropriate icon based on file type
const getFileIcon = (fileName: string) => {
  if (!fileName) return '/pageCreate/file-default.svg'
  
  const type = fileName.toLowerCase()
  if (type.includes('image') || ['.jpg', '.jpeg', '.png', '.gif', '.bmp'].some(ext => type.includes(ext))) {
    return '/pageCreate/imgIcon.svg'
  } else if (type.includes('pdf') || type.includes('.pdf')) {
    return '/pageCreate/pdfIcon.svg'
  } else if (type.includes('word') || type.includes('docx')) {
    return '/pageCreate/wordIcon.svg'
  } else if (type.includes('excel') || type.includes('sheet') || type.includes('xlsx')) {
    return '/pageCreate/excelIcon.svg'
  } else if(type.includes('mp4') || type.includes('ogg') || type.includes('webm') || 
     type.includes('mov') || type.includes('avi') || type.includes('mp3') || 
     type.includes('wav') || type.includes('m4a') || type.includes('m4v') || 
     type.includes('m4b') || type.includes('m4p')){
    return '/pageCreate/videoIcon.svg'
  }
  
  return '/pageCreate/file-default.svg'
}
</script>

<style lang="scss">
.upload-dialog {
  .el-dialog__header {
    padding: 15px;
    .el-dialog__title {
      font-size: 26px;
      font-weight: 700;
    }
  }
  
  border-radius: 25px;
  .dialog-footer {
    display: flex;
    justify-content: center;
    .el-button {
      width: 100px;
      height: 36px;
    }
  }
  // Styles for upload dialog content
.upload-dialog-content {
  max-height: 300px;
  overflow-y: auto;
  
  .no-files {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
    margin-bottom: 10px;
  }
  
  .selected-file-item {
    margin-left: 16%;
    width: 78%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    cursor: pointer;
    .delete-icon {
      display: none;
    }
    &:hover {
      background: #f5f7fa;
      .delete-icon {
        font-size: 20px;
        display: inline-block;
        cursor: pointer;
        color: rgb(179,179,179);
        margin: 0 10px;
        &:hover {
          opacity: 0.8;
        }
      }
    }
    .file-name {
      display: flex;
      align-items: center;
      flex: 1;
      margin-right: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .file-selector {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 40px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }
}
}
</style> 