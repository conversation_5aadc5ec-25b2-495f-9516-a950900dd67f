<template>
    <basic-container>
        <el-row>
            <br>
            <el-col :span="8">
                <el-form :model="form" size="small" :rules="rules" ref="ruleForm" label-width="150px"
                         >
                    <el-form-item>
                        <el-checkbox :label="1" v-model="isEmailPushFlag">{{$t('notifaction.email.isEmailPush')}}
                        </el-checkbox>

                    </el-form-item>
                    <el-form-item class="text-left">
                        <el-button type="primary" @click="updateEmailPush">{{$t('operation.save')}}</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </basic-container>
</template>

<script>

    import {queryUserNotification, updateEmailPush} from "@/api/system/user";
    import {mapGetters, mapState} from "vuex";
    import {validatenull} from "../../util/validate";

    export default {
        name: "emailPush",
        data() {
            return {
                //wechatPush
                isEmailPushFlag: false,
                emailNotification:0,
                form: {},
                rules: {}
            };
        },
        created() {
            //查询当前用户是否已开发微推送通知
            this.onLoad();
        },
        computed: {
            ...mapGetters([
                "userInfo"
            ])
        },
        watch: {
            isEmailPushFlag: function (newVal) {
                this.emailNotification=0;
                if (newVal) {
                    this.emailNotification=1;
                }
            },
        },
        methods: {
            onLoad() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading…',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                })
                queryUserNotification().then(res => {
                    loading.close();
                    const data = res.data.data;
                        if (data== 1 || data.wechatPush == '1') {
                            this.isEmailPushFlag = true;
                        } else {
                            this.isEmailPushFlag = false;
                        }
                      this.emailNotification = data;
                }).catch(() => {
                    loading.close();
                });
            },
            updateEmailPush() {
                updateEmailPush(this.emailNotification).then(res => {
                    if (res.data.success) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    } else {
                        this.$message({
                            type: "error",
                            message: res.data.msg
                        });
                    }
                })

            },
        }
    };
</script>

<style scoped>

</style>
