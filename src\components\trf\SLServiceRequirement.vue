<template>
  <div>
     <!--评定规则-->
     <el-row>
       <!-- 评论（需要评论请选择） -->
        <el-col :span="8" style="margin-bottom: -15px;">
            <el-form-item :label="$t('service.comment')">
                <el-radio-group v-model="trfServiceRequire.isComment"   @change="isCommentChange" :disabled="trfDisabled">
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>                 
                </el-radio-group>
            </el-form-item>
        </el-col>
      <el-col :span="24">
        <el-form-item :label="$t('service.judgmentRule.title')" style="margin-bottom: 2px;">
            <el-radio-group v-model="trfServiceRequire.judgmentRule" :disabled="trfDisabled || trfServiceRequire.isComment==0 || isNull(trfServiceRequire.isComment)">
                <el-radio :label="3">{{$t('service.judgmentRule.rule3')}}</el-radio>
                <el-radio :label="4">{{$t('service.judgmentRule.rule4')}}</el-radio>
                <el-radio :label="5">{{$t('service.judgmentRule.rule5')}}</el-radio>
                <el-radio :label="6">{{$t('service.judgmentRule.rule6')}}</el-radio>
            </el-radio-group>
        </el-form-item>
        <span>{{$t('service.judgmentRule.remark')}}</span>
      </el-col>
    </el-row>
   <!--资质要求-->
   <el-row>
      <el-col :span="24">
        <el-form-item :label="$t('service.reportSymbol')">
          <el-checkbox-group v-model="reportSymbolData" :disabled="trfDisabled"
                              @change="serviceItemChange($event, dictionaryEnums.ServiceRequirement.ReportSymbol.name)">
            <el-checkbox name="accreditationCheckbox"
                          v-for="(reportSymbol, index) in reportSymbolList"
                          :label="reportSymbol.sysKey" :key="reportSymbol.sysKey">
              {{reportSymbol.sysValue}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('service.returnSample')">
            <el-checkbox-group v-model="returnSampleArray" :disabled="trfDisabled"
                              @change="returnSampleChange">
              <el-checkbox name="returnSampleCheckbox"
                          v-for="(returnSample,index) in returnSampleList"
                          :label="returnSample.sysKey" :key="returnSample.sysKey">
                {{returnSample.sysValue}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
       
        <!-- 纸质报告 -->
        <el-col :span="12">
            <el-form-item :label="$t('service.hardCopy')">
                <el-radio-group v-model="trfServiceRequire.isCopy" :disabled="trfDisabled">
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
        </el-row>
        <el-row>
          <!-- 拍照（如需拍照请选择 -->
          <el-col :span="12">
              <el-form-item :label="$t('service.takePhoto')">
                  <el-radio-group v-model="trfServiceRequire.isPhoto" :disabled="trfDisabled">
                      <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                      <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                  </el-radio-group>
              </el-form-item>
          </el-col>
          <!-- 首页确认（是否需要首页确认） -->
          <el-col :span="12">
              <el-form-item :label="$t('service.coverPageConfirmation')">
                  <el-radio-group v-model="trfServiceRequire.isConfimCover" :disabled="trfDisabled">
                      <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                      <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                  </el-radio-group>
              </el-form-item>
          </el-col>
          <!-- 报价 -->
          <el-col :span="12">
              <el-form-item :label="$t('service.quotation')">
                  <el-radio-group v-model="trfServiceRequire.isQuotation" :disabled="trfDisabled">
                      <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                      <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                  </el-radio-group>
              </el-form-item>
          </el-col>
      </el-row>
  </div>
</template>

<script>
import serviceRequirement from "@/components/trf/js/serviceRequirement";
import { objectIsNull } from "@/util/validate";
import {getReturnSampleArryCN} from "@/api/trf/trf";
import {mapGetters} from "vuex";
import {DictionaryEnums} from "@/commons/enums/DictionaryEnums";


export default {
  name: "SLServiceRequirement",
  props: {
    trfServiceRequire: {
      type: Object,
      required: true,
      default: {},
      description: "服务需求对象"
    },
    trfDisabled: {
      type: Boolean,
      required: true,
      default: false,
      description: '是否可操作'
    }
  },
  watch: {
    language: function (newVal) {
      this.initResultSampleList();
    },
    returnSampleArray: {
      handler(newVal){
        if(!objectIsNull(newVal)) {
          let values = [...newVal];   //先赋值，再排序; 不赋值直接排序会重复执行监听，不排序则存入的顺序不一致
          values.sort();
          this.trfServiceRequire.returnSampleRequire = values.join(',');
          let choiceReturnSample = [];
          this.returnSampleList.forEach(item => {
            values.indexOf(item.sysKey) >= 0 ? choiceReturnSample.push(item.sysValue) : null;
          });
          this.trfServiceRequire.returnSampleName = choiceReturnSample.join(',');
        }
      }
    }
  },
  computed: {
    ...mapGetters(["language"]),
  },
  data() {
    return {
      reportSymbolList: [],                  //报告资质可选项
      reportSymbolData: [],                 //报告资质已选项
      returnSampleList: [],
      returnSampleArray: [],
      returnSampleMap: new Map([]),
      dictionaryEnums: DictionaryEnums
    }
  },

  mounted() {
    console.log("SLServiceRequirement", this.returnSampleData);
    this.initResultSampleList();
    this.returnSampleArray = objectIsNull(this.trfServiceRequire.returnSampleRequire) ? [] : this.trfServiceRequire.returnSampleRequire.split(',');
    this.initDictionary();
    this.reportSymbolData = objectIsNull(this.trfServiceRequire.accreditation) ? [] : this.trfServiceRequire.accreditation.split(',');

  },

  methods: {
    isNull(value) {
        return objectIsNull(value);
    },
    isCommentChange(value){
      if(value===0){
          this.trfServiceRequire.judgmentRule=null;
      }
      if(value==1){
        this.trfServiceRequire.judgmentRule=3;
      }
     
    },
    serviceItemChange(values, tag) {
      debugger;
      if(objectIsNull(values)) {
        switch (tag) {
          case DictionaryEnums.ServiceRequirement.ReportSymbol.name:
            this.trfServiceRequire.accreditation = null;
            break;
        }
      }else{
        switch (tag) {
          case DictionaryEnums.ServiceRequirement.ReportSymbol.name:
            this.trfServiceRequire.accreditation = objectIsNull(this.reportSymbolData) ? null :  this.reportSymbolData.join(',');
            break;
        }
      }
    },
    async initDictionary(){
      console.log("gary111",this.dictionaryEnums.ServiceRequirement.ReportSymbol.name)
      debugger;
      this.reportSymbolList = await serviceRequirement.queryDictionary(this.dictionaryEnums.ServiceRequirement.ReportSymbol.name, this.dictionaryEnums.ServiceRequirement.ReportSymbol, this.language)
      debugger;
    },
    returnSampleChange(values) {
      if(objectIsNull(values)) {
        this.returnSampleArray = [];
        this.trfServiceRequire.returnSampleName = null;
        this.trfServiceRequire.returnSampleRequire = null;
      }
      else {
        this.returnSampleArray = values;
      }
    },
    //初始化退样要求
    async initResultSampleList() {
      if(this.returnSampleMap.get(this.language) === undefined) {
        await getReturnSampleArryCN(this.language).then(res => {
          if(res.data && res.data.length > 0) {
            this.returnSampleMap.set(this.language, res.data);
          }
          else {
            this.returnSampleMap.set(this.language, []);
          }
        });
      }
      this.returnSampleList = this.returnSampleMap.get(this.language);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
}
</style>
