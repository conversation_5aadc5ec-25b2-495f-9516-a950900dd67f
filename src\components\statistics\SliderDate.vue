<template>
  <el-row>
    <el-col v-if="isShowSelect" :span="2">
    </el-col>
    <el-col :span="isShowSelect ? 18 : 24">
      <VueSlider :processStyle="processStyle"
                 :tooltipStyle="processStyle"
                 :value="value"
                 @change="inputHandle"
                 :min="1"
                 :max="max"
                 :labelStyle="{
                color: '#c0c0c0'
               }"
                 :data="dataValue"
                 :enable-cross="false" :tooltip="'none'" :marks="marks">
        <template slot="process" slot-scope="scope">
          <div class="vue-slider-process" :style="scope.style">
            <div :class="[
              'merge-tooltip',
              'vue-slider-dot-tooltip-inner',
              'vue-slider-dot-tooltip-inner-top',
            ]">
              {{ value[0] }} - {{ value[1] }}
            </div>
          </div>
        </template>
      </VueSlider>
    </el-col>
  </el-row>
</template>

<script>

  import moment from 'moment'
  import 'vue-slider-component/theme/default.css'
  import _ from 'lodash'

  const format = 'YYYY-MM'

  const formatMark = function (val) {
    return ({
      label: `${val}`,
      labelStyle: {
        opacity: val
      },
      labelActiveStyle: {
        color: '#20b426'
      },
      start:null,
      end: null,
      style: {}
    })
  }

  export default {
    name: "SliderDate",
    components: {
      VueSlider: resolve => require(['vue-slider-component'], resolve),
      DateRange: resolve => require(['./DateRange'], resolve)
    },
    model: {
      prop: 'valueData',
      event: 'input'
    },
    props: {
      isShowSelect: {
        type: Boolean,
        default: true
      },
      valueData: {
        required: true,
        type: Array,
        validator: (val) => val.length >=2
      },
      valueFormat: {
        type: String,
        defualt: 'YYYY-MM'
      }
    },
    data() {
      //设置月份 12个月
      const start_time = moment().subtract(13, 'months');
      //获取今天23时59分59秒
      const end_time = moment().startOf('month').subtract('month', -1)
      // moment().endOf("months");
      //获取开始时间和结束时间的时间差
      const oneYearDays = end_time.diff(start_time, 'months')
      console.log(oneYearDays);
      const dataValue = []
      for (let i = 0; i <= oneYearDays; i++) {
        let tempDate = moment().subtract(12, 'months').add(i, 'months').format(format)
        dataValue.push(tempDate)
      }
      console.log(dataValue);
      return {
        dataValue: dataValue,
        processStyle: {
          background: '#20b426'
        },
        max: oneYearDays,
        //value: [moment().subtract(1, 'M').format(format), moment().format(format)],
      }
    },
    computed: {
      value () {
        return [moment(this.valueData[0], this.valueFormat).format(format), moment(this.valueData[1], this.valueFormat).format(format)]
      }
    },
    methods: {

      formatTip(val) {
        return this.$lodash.get(this.marks, val)
      },
      marks(val) {
        return this.$lodash.reverse(this.$lodash.cloneDeep(this.dataValue)).indexOf(val)  ? formatMark(val) : false
      },
      inputHandle: _.debounce(function (val) {
        this.$emit('input', [val[0], val[1]])
      }, 500)
    }
  }
</script>

<style scoped lang="scss">
  /deep/ .merge-tooltip {
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translate(-50%, -8px);
    background-color: #20b426
  }
</style>
