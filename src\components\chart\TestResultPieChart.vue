<template>
  <div class="pie-chart-canvas" v-loading="pieLoading">
    <!-- <chart :options='options' id="result-pie" eleId="result-pie" /> -->
    <div id="result-pie" :style="style"></div>
    <div class="pie-total">
      <h3>{{ $t("wel1.total") }}</h3>
      <p>{{ orderSum.allCount }}</p>
    </div>
  </div>
</template>

<script>
import chart from './index.vue'

let color = ["#34A853", "#EA4336", "#D9D9D9"]

export default {
  name: 'TestResultPieChart',
  props: {
    orderSum: {
      type: Object
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '400px'
    },
  },
  components: {
    chart
  },
  data() {
    let that = this;
    return {
      pieLoading: true,
      buttonType: 'day',
      options: {
        title: {
          text: "",
        },
        // tooltip: {
        //   trigger: 'axis',
        //   backgroundColor:'rgba(255,255,255,0.9)',
        //   borderWidth:'1',
        //   borderColor:'gray'
        // },
        tooltip: {
          show: false,
          trigger: 'item',
          // formatter: "{a} <br/>{b}: {c} ({d}%)",
          // position: ['10%', '50%']
        },
        color: ["transparent", "#34A853", "#EA4336", "#D9D9D9"],
        legend: {
          show: false
        },
        grid: {
          top: '3%',
          left: '1%',
          right: '0%',
          bottom: '3%',
          containLabel: true
        },
        series: [
          {
            name: "",
            type: "pie",
            //起始刻度的角度，默认为 90 度，即圆心的正上方。0 度为圆心的正右方。
            startAngle: 0,
            hoverAnimation: false,
            tooltip: {
              show: false
            },
            radius: ["80%", "100%"],
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 0 // 圆间隔
            },
            data: []
          }
        ]
      }
    }
  },
  methods: {
    loadEchartsData() {
      let that = this;
       let data = [
            {
              value: that.orderSum.allCount === 0 ? 100 : that.orderSum.allCount, //值要为最后三项的总和，100为随意值，是为了填满图表
              itemStyle: {
                normal: { color: "transparent" }
              }
            },
            {
              value: that.orderSum.allCount === 0 ? 100 : that.orderSum.passCount,  // 100为随意值
              itemStyle: { color: that.orderSum.allCount === 0 ? "#f9f9f9" : "#34A853" },
            },
            {
              value: that.orderSum.failCount,
              itemStyle: {
                normal: { color: "#EA4336" }
              }
            },
            {
              value: that.orderSum.otherCount,
              itemStyle: {
                normal: { color: "#D9D9D9" }
              }
            }
          ]
          this.options.series[0].data = data
          this.myChart = echarts.init(document.querySelector('#result-pie'))
          this.myChart.setOption(this.options)

          this.myChart.on("click", function(param){
            console.log(param.data);
          });
       this.pieLoading = false;
    },
  },
  mounted() {
    let that = this
    this.$nextTick(() => {
      const timer = setInterval(() => {
        if (document.readyState === 'complete') {
          window.clearInterval(timer)
          that.loadEchartsData();
        }
      }, 300)
    })
  },
  computed: {
    style() {
      return {
        height: this.height,
        width: this.width
      }
    }
  },
  watch: {
    orderSum: {
      deep:true,
      handler:function (newVal,oldValue){
         this.loadEchartsData();
      }
    },
    deep:true
  }
}
</script>

<style lang="scss" scoped>
  .pie-chart-canvas {
    margin-top: 88px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: transparent;

    .pie-total {
      position: absolute; /* 子元素设置为绝对定位 */
      top: 50%; /* 使元素垂直居中 */
      transform: translateY(-50%); /* 根据元素高度向上平移一半的距离 */

      h3 {
        font-size: 16px;
        font-weight: 400;
        color: #656565;
      }
      p {
        font-size: 56px;
        font-family: UniversLT;
        color: #1B1B1B;
        line-height: 56px;
      }
    }
  }
  @media screen and (max-width: 1024px) {
    .pie-chart-canvas {
      .pie-total {
        h3 {
          font-size: 14px;
        }

        p {
          font-size: 32px;
          line-height: 32px;
        }
      }
    }
  }
</style>
