<template>
    <div>
        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
            <el-form-item>
                <el-input
                    @keyup.enter.native="onSearch"
                    @clear="onSearch"
                    v-model="query.beLongName"
                    :placeholder="$t('beLongTo.name')"
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <!-- <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button> -->
                <!-- <el-button v-if="permissionList.addBtn" type="primary" @click="addBeLongTo">{{$t('operation.add')}}
                </el-button> -->

                <el-button v-if="permissionList.addBtn" @click="addBeLongTo" class="line-btn">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
        <el-table ref=deptTable
                  :data="tableData"
                  :row-key="getRowKeys"
                  style="width: 100%"
                  size="medium"
        >
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="beLongName"
                    :label="$t('department.name')">
            </el-table-column>
            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')"
                    width="150"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="updateUser"
                    :label="$t('common.operator')"
                    width="120">
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="280"
                    align="center">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)">
                        {{$t('operation.edit')}}
                    </el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text">
                        {{$t('operation.remove')}}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-drawer :before-close="closeBeLongToDrawer" :title="beLongToTitle" :visible.sync="dialogBeLongFormVisible"
                   size="60%">
            <el-form :model="beLongForm" :rules="beLongRules" label-position="left" ref="beLongForm"
                     label-width="200px" class="sgs-form">
                <el-form-item :label="$t('beLongTo.name')" prop="beLongName"
                              :rules="{ required: true, message: this.$t('beLongTo.validate.name'), trigger: 'blur' }">
                    <el-input maxlength="200" v-model="beLongForm.beLongName" clearable
                              autocomplete="off"></el-input>
                </el-form-item>
                <div class="sgs-bottom">
                    <el-button @click="closeBeLongToDrawer" :disabled="isBeLongToDisabled">{{$t('operation.cancel')}}
                    </el-button>
                    <el-button type="primary" @click="submitForm('beLongForm')" :loading="submitLoading"
                               :disabled="isBeLongToDisabled">
                        {{$t('operation.confirm')}}
                    </el-button>
                </div>
            </el-form>

        </el-drawer>
    </div>
</template>

<script>
    import {getPageByUser, add, remove, detail, updateLongStatus} from "@/api/customer/beLong";
    import {mapGetters} from "vuex";
    import {deepClone} from '@/util/util'

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        data() {
            return {
                isBeLongToDisabled: false,
                beLongToTitle: this.$t('operation.add'),
                beLongData: [],
                getRowKeys(row) {
                    return row.id
                },
                beLongForm: {
                    beLongName: '',
                },
                expands: [],
                currentRow: {},
                name: "dept",
                title: '',
                dialogBeLongFormVisible: false,
                submitLoading: false,
                tableData: [],
                query: {},
                sort: {descs: 'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                beLongRules: {}
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:dept:add'], false),
                    editBtn: this.vaildData(this.permission['sgs:customer:dept:edit'], false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:dept:delete'], false),
                };
            }
        },
        methods: {
            closeBeLongToDrawer(done) {
                this.$refs['beLongForm'].resetFields();
                this.dialogBeLongFormVisible = false;
            },
            deptBeLongChange(row, expandedRows) {
                this.$refs.deptTable.setCurrentRow();
                this.currentRow = row
                if (this.expands.join(',').indexOf(row.id) === -1) {
                    this.expands = [this.currentRow.id]
                    this.searchBeLongList(row.id);
                } else {
                    this.expands.splice(0, this.expands.length)
                }
                /* if(vlaue2.length!=0){//展开
                     this.searchLabContactList(value1.id);
                 }*/
            },
            addBeLongTo(row) {
                this.beLongForm.beLongName='';
                this.dialogBeLongFormVisible = true;
            },
            searchBeLongList(deptId) {

            },
            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        // this.submitLoading = true;
                        add(this.beLongForm).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.submitLoading = false;
                            this.dialogBeLongFormVisible = false;
                            this.onLoad(this.page);
                        }, error => {
                            this.submitLoading = false;
                        });
                    } else {
                        return false;
                    }
                });

            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                    .then(() => {
                        remove(row.id).then(() => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.onLoad(this.page);
                        });
                    })
            },
            detailRow(row) {
                this.title = this.$t('department.title.edit');
                this.beLongForm = deepClone(row);
                this.dialogBeLongFormVisible = true;
                /* detail(row.id).then(res => {
                     //获取后台数据付给页面，并打开
                     this.form = res.data.data;
                 });*/

            },
            addRow() {
                this.form = {};
                this.title = this.$t('department.title.add');
                this.beLongForm.beLongName='';
                this.dialogBeLongFormVisible = true;
            },
            changeStatus(row) {
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                updateLongStatus(modifiedForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            }
        },
        created() {
            this.onLoad(this.page);
        },
    }
</script>

<style scoped>

</style>
