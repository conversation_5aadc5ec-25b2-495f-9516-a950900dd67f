import request from './request'

// 定义请求参数的类型
type RequestParams = Record<string, any>

/**
 * 查询业务单元设置
 * @param params - 查询参数，类型为包含任意键值对的对象
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryBuSetting = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/sgs-api/queryBuParamSetting',
    method: 'post',
    data: params,
  })
}

// 下载
export const downloadFile = (params: RequestParams): Promise<any> => {
  return request({
    url: `/sgs-scm/admin/common-file/download-file`,
    method: 'get',
    params,
  })
}

export const getFileUrlByCloudId = (cloudId: string): Promise<any> => {
  let formData = new FormData();
  formData.append('cloudID', cloudId);
  return request({
    url: '/api/sgs-mart/FrameWorkApi/file/downloadByCloudID?systemID=1&networkType=2',
    method: 'post',
    data:formData
  })
}

// 导入supplier
export const importSupplier = (params: RequestParams): Promise<any> => {
  return request({
    url: '/sgs-scm/excel/supplier/import',
    method: 'post',
    data: params,
  })
}
// 上传
export const doUpload = (formData: any, uploadUrl: string) => {
  return request({
    url: uploadUrl,
    method: 'post',
    data: formData,
  })
}

export const  queryTianYanCha = (keyWord:string)=>{
  return request({
    url: '/api/sgs-mart/common/tianyancha/search',
    method: 'get',
    headers:{
      loading:'false'
    },
    params:{keyWord}
  })
}

//获取字典项数据，可传入语言信息 EN/CHI
export const queryDictionaryByLanguage = (params:RequestParams): Promise<any> =>{
  return request({
    url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup='+params['sysKeyGroup']+"&systemID="+params['systemID']+"&languageCode="+params['languageCode'],
    method: 'get',
    data: params
  })
}

export const saveAttachment = (attachment: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/file/saveAttachment',
    method: 'post',
    data: attachment,
  })
}
