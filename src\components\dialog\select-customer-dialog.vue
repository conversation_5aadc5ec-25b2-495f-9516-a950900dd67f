<template>
    <basic-container>
        <el-drawer title="选择客户" :visible.sync="customerVisible" width="90%" append-to-body  @close="close">
            <el-button type="primary"  size="small" @click="selectedCustomers">添加</el-button>
        <el-table
                :data="tableData"
                style="width: 100%"
                size="medium"
                @select="handleSelectionChange">
            <el-table-column
                    type="selection"
                    prop="ID"
                    width="50">
            </el-table-column>
            <el-table-column
                    fixed
                    prop="customerNameZh"
                    :label="$t('customer.name.zh')">
            </el-table-column>
            <el-table-column
                    fixed
                    prop="customerNameEn"
                    :label="$t('customer.name.en')">
            </el-table-column>
        </el-table>

        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        </el-drawer>
    </basic-container>

</template>

<script>
    import {getList} from "@/api/customer/customerRegister";
    export default {
        props:['customerVisible'],
        data(){
            return{
                name: "select-customer-dialog",
                //选中客户数组
                customers: [],
                tableData: [],
                form: {},
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
            }
        },
        methods:{
            handleSelectionChange(val) {
                this.customers.push(val);
            },
            //选中需要授权的客户列表
            selectedCustomers(){
                this.$emit('handleSelectCustomer',this.customers);
               this.close();
               //返回授权页面
            },
            onLoad(page, params = {}) {
                getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            close(){
                this.customerVisible = false;
                this.$emit('update:customerVisible',this.customerVisible);
            },
            currentChange(currentPage) {
              this.page.currentPage = currentPage;
              this.onLoad(this.page);
            },
            sizeChange(pageSize) {
              this.page.pageSize = pageSize;
              this.onLoad(this.page);
            },


        },
        created() {
            this.onLoad(this.page);
        }
    }
</script>

<style scoped>

</style>
