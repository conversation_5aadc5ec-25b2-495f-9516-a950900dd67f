// import { template } from 'lodash'
import request from './request'
type RequestParams = Record<string, any>

const sendPost = (
    url: string,
    param: RequestParams,
    headers?: RequestParams,
): Promise<any> => {
    headers = headers ? headers : {}
    return request({
        url,
        method: 'post',
        headers: {
            ...headers,
        },
        data: param,
    })
}
const sendGet = (
    url: string,
    param: RequestParams,
    headers?: RequestParams,
): Promise<any> => {
    url = url + '?hash=' + new Date().getTime()
    return request({
        url,
        method: 'get',
        headers: {
            loading: 'false',
            ...headers,
        },
        params: param,
    })
}


const createImaArrayForCareLabelResponse = (carelabelResponse: any) => {
    console.log("carelabelResponse", carelabelResponse)
    if (!carelabelResponse) {
        return [];
    }
    let careLabelDetail: any[] = [];
    let data = carelabelResponse || carelabelResponse.data;
    if (data && data.length > 0) {
        careLabelDetail = data[0]["careLabelDetail"];
        if (careLabelDetail) {
            //按照sequenceNo 排序，同时增加check:false
            careLabelDetail.sort((v1: any, v2: any) => {
                let v1No = v1["sequenceNo"];
                let v2No = v2["sequenceNo"];
                return v1No - v2No;
            });
            return careLabelDetail;
        } else {
            data.sort((v1: any, v2: any) => {
                let v1No = v1["sequenceNo"];
                let v2No = v2["sequenceNo"];
                return v1No - v2No;
            });
            return data;
        }
    }
    return careLabelDetail;
};

/**
 * 查询product-lines信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */

const api = {
    getProductLine: () => {
        return sendGet('/api/sgs-mart/sgs-api/product-lines', {});
    },
    queryBuConfig: (param: RequestParams) => {
        return sendPost('/api/sgs-pbm/sample/web/v1/template/buConfig', param, { loading: 'false' })
    },
    workbookApi: {
        loadObjectRelationList: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/library', param)
        },
        loadTableData: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/list', param)
        },
        approve: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/approve', param)
        },
        approveReturn: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/approve/undo', param)
        },
        linkExistDoc: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/link', param)
        },
        linkDetail: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/linkdetail', param);
        },
        unLink: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/unlink', param)
        },
        deleteWorkbook: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/delete', param)
        },
        queryHistory: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/history', param)
        },
        checkIn: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/checkin', param)
        },
        checkOut: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/checkout', param)
        },
        unCheckout: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/linkdetail/uncheckout', param)
        },
        addDocument: (param: RequestParams) => {
            return sendPost('/api/sgs-pbm/component/documents/add', param)
        }
    },
    careLabelApi: {
        queryWarning: (param: RequestParams) => {
            return sendPost("/api/sgs-trims/trimsApi/queryWarning", param);
        },
        queryCareLabelCountry: () => {
            return sendPost("/api/sgs-trims/trimsApi/queryCareLabelRegionCountry", {}, { loading: 'false' });
        },
        queryCareLabel: (params: RequestParams, callback: Function) => {
            const conditionTypeArray = [
                "Washing",
                "Bleaching",
                "Drying",
                "Ironing",
                "Drycleaning",
                /*"Others",*/
            ];
            const requestMethods = conditionTypeArray.map(conditionType => {
                const para = JSON.parse(JSON.stringify(params));
                para["usageType"] = conditionType;

                const url = "/api/sgs-mart/sgs-trimsClient/queryCareLabel";
                return sendPost(url, para);
            });
            Promise.all(requestMethods)
                .then(responses => {
                    const array = responses.map((res, index) => ({
                        conditionType: conditionTypeArray[index],
                        imgArray: createImaArrayForCareLabelResponse(res),
                    }));
                    console.log("获取到的额array", array)
                    callback(array);
                })
                .catch((err) => {
                    console.log("查询异常", err)
                    callback([]);
                });
        },
        queryBuyerCareLabel: (param: RequestParams) => {
            return sendPost("/api/sgs-mart/sgs-trimsClient/queryCareLabel", param);
        },
        uploadCareLabel: (param: RequestParams) => {
            return sendPost("/api/sgs-mart/trfApi/uploadCareLabel", param);
        },
        downloadPathById: (params: RequestParams) => {
            return sendPost("/api/sgs-mart/sgs-api/dowonload-file", params);
        },
    },
    templateApi: {
        templateList: (params: RequestParams) => {
            return sendPost("/api/sgs-pbm/sample/web/v2/templateList", params);
        },
        detail: (params: RequestParams) => {
            return sendPost("/api/sgs-pbm/sample/web/v1/detail", params);
        },
        create: (params: RequestParams) => {
            return sendPost("/api/sgs-pbm/sample/web/v1/create", params);
        },
    },
    queryEmployee: (params: RequestParams) => {
        return sendPost("/api/sgs-pbm/sample/web/v1/employee/list", params);
    },
}

export default api;
