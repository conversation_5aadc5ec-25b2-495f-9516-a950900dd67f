import Layout from '@/page/index/'

export default [{
    path: '/wel',
    component: Layout,
    redirect: '/wel/index',
    children: [{
        path: 'index',
        name: '首页',
        meta: {
            i18n: 'dashboard'
        },
        component: resolve => require([ '@/views/wel' ], resolve)

    }]
},
{
    path: '/document',
    component: Layout,
    redirect: '/document/list',
    children: [{
        path: 'list',
        name: '授权展示页',
        meta: {
            i18n: 'documentList'
        },
        component: resolve => require([ '@/views/document/list' ], resolve)
    },
    {
        path: 'authorization',
        name: '授权展示页',
        meta: {
            i18n: 'documentAuthorization'
        },
        component: resolve => require([ '@/views/document/authorization/list' ], resolve)
    },
        {
            path:'more',
            name:'More',
            meta: {
                i18n: 'documentMore'
            },
            component:resolve => require(['@/views/document/more'],resolve)
        }
    ]
},{
    path: '/scmReceiver',
    component: Layout,
    redirect: '/scm/index',
    children: [{
        path: 'index',
        name: 'SCM',
        meta: {
            i18n: 'SCM'
        },
        component: resolve => require([ '@/views/scm/index' ], resolve)
    }]
},
    {
        path: '/scmProvider',
        component: Layout,
        redirect: '/scm/index',
        children: [{
            path: 'index',
            name: 'SCM',
            meta: {
                i18n: 'SCM'
            },
            component: resolve => require([ '@/views/scm/index' ], resolve)
        }]
    },{
    path: '/powerbi',
    component: Layout,
    redirect: '/powerbi/index',
    children: [{
        path: 'index',
        name: '报表',
        meta: {
            i18n: 'dashboard'
        },
        component: resolve => require([ '@/views/dashboard/powerbi' ], resolve)
    },{
        path: 'list',
        name: '报表',
        meta: {
            i18n: 'dashboard'
        },
        component: resolve => require([ '@/views/dashboard/powerbiList' ], resolve)
    }]
},{
    path: '/document',
    component: Layout,
    children: [{
      path: 'documentShareing',
      name: 'documentShareing',
      meta: {
        i18n: 'documentShareing'
        },
      component: resolve => require([ '@/views/document/documentShareing' ], resolve)
    }]
  },
    {
        path: '/document',
        component: Layout,
        children: [{
            path: 'documentShareing1',
            name: 'documentShareing1',
            meta: {
                i18n: 'documentShareing1'
            },
            component: resolve => require([ '@/views/document/documentShareing1' ], resolve)
        }]
    },
    {
    path: '/template',
    component: Layout,
    redirect: '/template/template-setting',
    children: [{
        path: 'template-setting',
        name: '模板设置',
        meta: {
            i18n: 'templateSetting'
        },
        component: resolve => require([ '@/views/template/template-setting' ], resolve)
    }]
},{
    path: '/customer/offlineRegister',
    component: Layout,
    redirect: '/customer/offlineRegister/offlineCustomer',
    children: [{
        path: 'offlineCustomer',
        name: '线下注册',
        meta: {
            i18n: 'offlineCustomer'
        },
        component: resolve => require([ '@/views/customer/offlineRegister/offlineCustomer' ], resolve)
    }]
},{
    path: '/customer/thirdLabRegister',
    component: Layout,
    redirect: '/customer/thirdLabRegister/thirdLabRegister',
    children: [{
        path: 'thirdLabRegister',
        name: '第三方实验室注册',
        meta: {
            i18n: 'thirdLabRegister'
        },
        component: resolve => require([ '@/views/customer/thirdLabRegister/thirdLabRegister' ], resolve)
    }]
}, {
        path: '/customer/authority',
        component: Layout,
        redirect: '/customer/authority',
        children: [{
            path: 'authority',
            name: 'Authority',
            meta: {
                i18n: 'Authority'
            },
            component: resolve => require([ '@/views/customer/authority/index' ], resolve)
        },]
    },{
        path: '/trf',
        component: Layout,
        redirect: '/trf/trfForm',
        children: [
            {
                path: 'cpscTrfInfo',
                name: 'new TRF List Demo',
                meta: {
                    i18n: 'trf'
                },
                component: resolve => require([ '@/views/cpscTrfInfo/cpscTrfInfo' ], resolve)
            },
            {
                path: 'newList',
                name: 'new TRF List Demo',
                meta: {
                    i18n: 'trf'
                },
                component: resolve => require([ '@/views/trf/newList' ], resolve)
            },{
            path: 'trfForm',
            name: 'TRF Detail',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/trf/trfForm' ], resolve)
        },{
            path: 'trfDetail',
            name: 'TRF Detail',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/trf/trfForm' ], resolve)
        },{
            path: 'temp',
            name: 'Template',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/trf/template' ], resolve)
        },{
            path: 'trfDetailTransfer',
            name: 'trfDetailTransfer',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/trf/trfDetailTransfer' ], resolve)
        }, {
            path: 'todoList',
            name: '代办列表',
            meta: {
                i18n: 'todoList'
            },
            component: resolve => require([ '@/views/trf/todoList' ], resolve)
        }, {
                path: 'todoListForLululemon',
                name: '代办列表',
                meta: {
                    i18n: 'todoList'
                },
                component: resolve => require([ '@/views/trf/todoListForLululemon' ], resolve)
            }]
    },
    // {
    //     path: '/thirdPartyLab',
    //     component: Layout,
    //     redirect: '/trf/trfForm',
    //     children: [{
    //         path: 'trfForm',
    //         name: 'new TRF',
    //         meta: {
    //             i18n: 'trf'
    //         },
    //         component:  resolve => require([ '@/views/thirdPartyLab/index' ], resolve)
    //     },{
    //         path: 'thirdTLabTrf',
    //         name: 'new TRF',
    //         meta: {
    //             i18n: 'trf'
    //         },
    //         component: resolve => require([ '@/views/thirdPartyLab/index' ], resolve)
    //     },{
    //         path: 'thirdTLabTrfDetail',
    //         name: 'new TRF',
    //         meta: {
    //             i18n: 'trf'
    //         },
    //         component:  resolve => require([ '@/views/thirdPartyLab/index' ], resolve)
    //     }]
    // },
    {
    path: '/info',
    component: Layout,
    redirect: '/info/index',
    children: [{
        path: 'index',
        name: '个人信息',
        meta: {
            i18n: 'personalInfomation'
        },
        component: resolve => require([ '@/views/user/info' ], resolve)
    }]
},{
    path: '/personality',
    component: Layout,
    redirect: '/personality/settings',
    children: [{
        path: 'settings',
        name: '个人设置',
        meta: {
            i18n: 'personalSettings'
        },
        component: resolve => require([ '@/views/personality/settings' ], resolve)
    }]
},{
    path: '/company',
    component: Layout,
    redirect: '/company/index',
    children: [{
        path: 'index',
        name: '公司管理',
        meta: {
            i18n: 'companyManagement'
        },
        component: resolve => require([ '@/views/company/index' ], resolve)
    },{
        path: 'info',
        name: '公司信息',
        meta: {
            i18n: 'companyInformation'
        },
        component: resolve => require([ '@/views/company/detailInfo' ], resolve)
    }]
},
    {
    path: '/todo',
    component: Layout,
    redirect: '/quotationAndInvoice/list',
  /*  children: [{
        path: '/toto',
        name: 'Quotation & Inovice',
        component: resolve => require([ '@/views/todo/quotationAndInvoice/list' ], resolve)
    }]*/
},

{
    path: '/training',
    component: Layout,
    children: [{
        path: 'class',
        name: 'New Class',
        meta: {
            i18n: 'newClass'
        },
        component: resolve => require([ '@/views/training/class' ], resolve)
    }]
}, {
    path: '/training',
    component: Layout,
    children: [{
        path: 'attendance',
        name: 'attendance',
        meta: {
            i18n: 'trainingAttendance'
        },
        component:  resolve => require([ '@/views/training/attendance' ], resolve)
    }]
},{
        path: '/training',
        component: Layout,
        children: [{
            path: 'performance',
            name: 'performance',
            meta: {
                i18n: 'trainingPerformance'
            },
            component: resolve => require([ '@/views/training/performance' ], resolve)
        }]
    },{
    path: '/trf',
    component: Layout,
    children: [{
      path: 'previewCA2',
      name: 'previewCA2',
      meta: {
        i18n: 'trfPreviewCA2'
        },
      component: resolve => require([ '@/views/trf/previewCA2' ], resolve)
    }]
  }, {
        path: '/statistics',
        component: Layout,
        children: [{
            path: 'executiveSummary',
            name: 'executiveSummary',
            meta: {
                i18n: 'statisticsExecutiveSummary'
            },
            component: resolve => require([ '@/views/statistics/executiveSummary' ], resolve)
        }]
    },/*{
        path: '/statistics',
        component: Layout,
        children: [{
            path: 'chemicalTestPerformance',
            name: 'chemicalTestPerformance',
            component: () =>
                import( /!* webpackChunkName: "views" *!/ '@/views/statistics/chemicalTestPerformance')
        }]
    },*/{
        path: '/statistics',
        component: Layout,
        children: [{
            path: 'physicalTestPerformance',
            name: 'physicalTestPerformance',
            meta: {
                i18n: 'statisticsPhysicalTestPerformance'
            },
            component:  resolve => require([ '@/views/statistics/physicalTestPerformance' ], resolve)
        }]
    },{
        path: '/statistics',
        component: Layout,
        children: [{
            path: 'supplierPerformanceOverall',
            name: 'supplierPerformanceOverall',
            meta: {
                i18n: 'statisticsSupplierPerformanceOverall'
            },
            component: resolve => require([ '@/views/statistics/supplierPerformanceOverall' ], resolve)
        }]
    },{
        path: '/statistics',
        component: Layout,
        children: [{
            path: 'supplierPerformanceChemical',
            name: 'supplierPerformanceChemical',
            meta: {
                i18n: 'statisticsSupplierPerformanceChemical'
            },
            component: resolve => require([ '@/views/statistics/supplierPerformanceChemical' ], resolve)
        }]
    },{
        path: '/statistics',
        component: Layout,
        children: [{
            path: 'supplierPerformancePhysical',
            name: 'supplierPerformancePhysical',
            meta: {
                i18n: 'statisticsSupplierPerformancePhysical'
            },
            component: resolve => require([ '@/views/statistics/supplierPerformancePhysical' ], resolve)
        }]
    },{
        path: '/form',
        component: Layout,
        redirect: '/form/design',
        children: [{
            path: 'list',
            name: '表单管理',
            meta: {
                i18n: 'formManagement'
            },
            component: resolve => require([ '@/views/form/list' ], resolve)
        },{
            path: 'detail',
            name: '表单管理',
            meta: {
                i18n: 'formDetail'
            },
            component: resolve => require([ '@/views/form/detail' ], resolve)
        },{
            path: ':formCode/list',
            name: '表单管理',
            meta: {
                i18n: 'formCodeManagement'
            },
            component:resolve => require([ '@/views/form/detail' ], resolve)
        }, {
            path: 'design',
            name: '表单设计',
            meta: {
                i18n: 'formDesign'
            },
            component: resolve => require([ '@/views/form/design' ], resolve)
        }]
    },{
        path: '/customer/material',
        component: Layout,
        children: [{
            path: 'config',
            name: 'config',
            meta: {
                i18n: 'customerMaterialConfig'
            },
            component: resolve => require([ '@/views/customer/material/config' ], resolve)
        }]
    },{
        path: '/customer/material',
        component: Layout,
        children: [{
            path: 'list',
            name: 'list',
            meta: {
                i18n: 'customerMaterialList'
            },
            component:  resolve => require([ '@/views/customer/material/list' ], resolve)
        }]
    },{
        path: '/customer/material',
        component: Layout,
        children: [{
            path: 'materialReport',
            name: 'report',
            meta: {
                i18n: 'materialReport'
            },
            component:resolve => require([ '@/views/customer/material/report' ], resolve)
        }]
    },
    {
        path: '/customer/newMaterial',
        component: Layout,
        children: [{
            path: 'list',
            name: 'Material-List',
            meta: {
                i18n: 'materialList'
            },
            component:resolve => require([ '@/views/customer/newMaterial/list' ], resolve)
        },{
            path: 'detail',
            name: 'Material-Detail',
            meta: {
                i18n: 'materialDetail'
            },
            component:resolve => require([ '@/views/customer/newMaterial/detail' ], resolve)
        }
        ]
    },
    {
        path: '/customer/newProduct',
        component: Layout,
        children: [{
            path: 'list',
            name: 'Product-List',
            meta: {
                i18n: 'productList'
            },
            component:resolve => require([ '@/views/customer/newProduct/list' ], resolve)
        },{
            path: 'detail',
            name: 'Product-Detail',
            meta: {
                i18n: 'productDetail'
            },
            component:resolve => require([ '@/views/customer/newProduct/detail' ], resolve)
        }
        ]
    },{
        path: '/customer/template',
        component: Layout,
        children: [{
            path: 'material',
            name: 'Material-Template-List',
            meta: {
                i18n: 'materialTemplateList'
            },
            component:resolve => require([ '@/views/customer/template/material' ], resolve)
        },{
            path: 'product',
            name: 'Product-Template-List',
            meta: {
                i18n: 'productTemplateList'
            },
            component:resolve => require([ '@/views/customer/template/product' ], resolve)
        },
        {
            path: 'templateDetail',
            name: 'Template Detail',
            meta: {
                i18n: 'templateDetail'
            },
            component:resolve => require([ '@/views/customer/template/newTemplate' ], resolve)
        },

        ]
    },
    {
        path: '/ccl',
        component: Layout,
        children: [{
            path: 'tableComponent',
            name: 'tableComponent',
            meta: {
                i18n: 'tableComponent'
            },
            component: resolve => require([ '@/views/ccl/tableComponent' ], resolve)
        }]
    },{
        path: '/ccl',
        component: Layout,
        children: [{
            path: 'formComponent',
            name: 'formComponent',
            meta: {
                i18n: 'formComponent'
            },
            component: resolve => require([ '@/views/ccl/formComponent' ], resolve)
        }]
    },
    {
        path: '/ccl/trf',
        component: Layout,
        redirect: '/ccl/trf/trfForm',
        children: [{
            path: 'trfForm',
            name: 'New CCL TRF',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/ccl/trf/trfForm' ], resolve)
        },{
            path: 'trfDetail',
            name: 'new CCL TRF',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/ccl/trf/trfForm' ], resolve)
        },{
            path: 'newTrf',
            name: 'TRF Detail',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/trf/trfForm' ], resolve)
        }]
    },
    {
        path: '/afl/trf',
        component: Layout,
        redirect: '/afl/trf/newTrf',
        children: [{
            path: 'newTrf',
            name: 'TRF Detail',
            meta: {
                i18n: 'trf'
            },
            component: resolve => require([ '@/views/trf/trfFormAfl' ], resolve)
        },{
            path: 'trfDetail',
            name: 'TRF Detail',
            meta: {
                i18n: 'trf'
            },
            component:  resolve => require([ '@/views/trf/trfFormAfl' ], resolve)
        }]
    },
    {
        path: '/todo/quotationAndInvoice',
        component: Layout,
        redirect: '/todo/quotationAndInvoice/list',
        children: [{
            path: 'quotationAndInvoice',
            name: '报价 & 对账',
            meta: {
                i18n: 'quotationAndInvoice'
            },
            component: resolve => require([ '@/views/todo/quotationAndInvoice/list' ], resolve)
        }]
    },
    {
        path: '/invoice/orderList',
        component: Layout,
        redirect: '/invoice/orderList',
        children: [{
            path: 'invoiceOrder',
            name: '在线支付',
            meta: {
                i18n: 'invoiceOrder'
            },
            component: resolve => require([ '@/views/invoice/orderList' ], resolve)
        }]
    },
    {
        path: '/customer',
        component: Layout,
        //redirect: '/customer/management',
        children: [{
            path: 'management',
            name: '管理',
            meta: {
                i18n: 'customerManagement'
            },
            component: resolve => require([ '@/views/customer/management' ], resolve)
        }]
    },
    {
        path: '/extend',
        component: Layout,
        redirect: '/extend/specificextend-setting',
        children: [{
            path: 'specificextend-setting',
            name: '扩展信息设置',
            meta: {
                i18n: 'extendSetting'
            },
            component: resolve => require([ '@/views/extend/specificextend-setting' ], resolve)
        }]
    },
    {
        path: '/accountPer',
        component: Layout,
        name: 'accountPer',
        // component: () =>
        //     import( /* webpackChunkName: "page" */ '@/views/accountPer/index'),
        children: [{
            path: 'index',
            name: '授权展示页',
            component: resolve => require([ '@/views/accountPer/index' ], resolve)
        },{
            path: 'authority',
            name: '授权管理',
            meta: {
                i18n: 'authorityManage'
            },
            component: resolve => require([ '@/views/accountPer/authority' ], resolve)
        }]
    },


    {
        path: '/settings',
        component: Layout,
        redirect: '/settings/components',
        children: []
    },
    {
        path: '/dataEntry',
        component: Layout,
        redirect: '/dataEntry/detail',
        children: [{
            path: 'inputResult',
            name: 'Input Result',
            meta: {
                i18n: 'inputResult'
            },
            component:  resolve => require([ '@/views/dataEntry/detail/index' ], resolve)
        }]
    },
    {
        path: '/tableDemo',
        component: Layout,
        children: [{
            path: 'demo',
            name: 'Table Demo',
            meta: {
                i18n: 'tableDemo'
            },
            component:  resolve => require([ '@/views/tableDemo/demo.vue' ], resolve)
        }]
    },
    {
        path: '/eFiling',
        component: Layout,
        redirect: '/eFiling/index',
        children: [{
            path: 'index',
            name: 'eFiling',
            meta: {
                i18n: 'eFiling'
            },
            component: resolve => require([ '@/views/cpscTrfInfo/add.vue' ], resolve)
        }]
    },
    {
        path: '/protocol',
        component: Layout,
        redirect: '/protocol/list',
        children: [{
            path: 'list',
            name: 'Protocol List',
            meta: {
                i18n: 'ProtocolList'
            },
            component: resolve => require([ '@/views/protocol/list.vue' ], resolve)
        },
            {
                path: 'detail',
                name: 'Protocol Detail',
                meta: {
                    i18n: 'ProtocolDetail'
                },
                component: resolve => require([ '@/views/protocol/detail.vue' ], resolve)
            }]
    }
]
