<template>
  <div
    class="sgs_smart_new_material_downloadTemplate"
    id="sgs_smart_new_material_downloadTemplate"
  >
    <el-form
      :model="templateForm"
      :rules="rules"
      ref="downloadTemplateForm"
      label-position="left"
      label-width="140px"
      size="small"
    >
      <el-form-item label="Product Line" prop="productLineCode">
        <el-select
          style="width: 100%"
          clearable
          filterable
          v-model="templateForm.productLineCode"
          @change="queryTemplateList"
        >
          <el-option
            v-for="(pl, index) of lists.productLineCodes"
            :key="'pl_' + index"
            :label="pl.productLineName"
            :value="pl.productLineCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Customer" prop="customerGroupCode">
        <el-select
          clearable
          filterable
          style="width: 100%"
          @change="queryTemplateList"
          v-model="templateForm.customerGroupCode"
        >
          <el-option
            v-for="(ms, index) of lists.customerList"
            :key="'ms_' + index"
            :label="ms.customerGroupName"
            :value="ms.customerGroupCode"
          >
              {{ms.customerGroupName}} ({{ms.customerGroupCode}})
          </el-option>
          <el-option label="General" value="General"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="formPurpose + ' Template'" prop="templateId">
        <el-select
          clearable
          filterable
          style="width: 100%"
          v-model="templateForm.templateId"
        >
          <el-option
            v-for="(te, index) of lists.templateList"
            :key="'te_' + index"
            :label="te.templateName"
            :value="te.templateId"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col style="text-align: right">
        <slot name="downloadTemplateSlot"></slot>
        <el-button
          type="primary"
          size="small"
          v-if="!templateShowDownload"
          @click="createNewMaterial('new')"
        >
          Create {{ formPurpose }}
        </el-button>
        <el-button
          type="primary"
          size="small"
          v-if="templateShowDownload"
          @click="downloadTemplate"
        >
          Download Template
        </el-button>
        <el-button type="info" size="small" @click="emit('cancelDia')">
          Cancel
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { downloadMaterialTemplate, getSampleTemplateList } from '@/api/sample'
import { ElNotification } from 'element-plus'
import { generateRouteUrl } from '@/utils/util'

const router = useRouter()
// 定义 props
const props = defineProps({
  templateShowDownload: {
    type: Boolean,
    default: false,
  },
  customerList: {
    type: Array,
    default: () => [],
  },
  productLineList: {
    type: Array,
    default: () => [],
  },
  formPurpose: {
    type: String,
    default: '',
  },
})

// 定义 emits
const emit = defineEmits(['cancelDia'])

// 使用 store
const store = useStore()
const permission = computed(() => store.state.user.permission)
const userInfo = computed(() => store.state.user.userInfo)

// 定义响应式数据
const lists = ref({
  productLineCodes: [],
  customerList: [],
  templateList: [],
})

const rules = {
  productLineCode: [
    { required: true, message: 'Please select', trigger: 'change' },
  ],
  customerGroupCode: [
    { required: true, message: 'Please select', trigger: 'change' },
  ],
  templateId: [{ required: true, message: 'Please select', trigger: 'change' }],
}

const templateForm = ref({
  productLineCode: '',
  customerGroupCode: '',
  templateId: '',
})

const downloadTemplateForm = ref(null)

// 定义方法
const createNewMaterial = () => {
  if (!downloadTemplateForm.value) return
  downloadTemplateForm.value.validate((valid) => {
    if (!valid) {
      return
    }
    const query = {
      action: 'new',
      templateId: templateForm.value.templateId,
    }
    const winUrl = generateRouteUrl(
      `/web/customer/newProduct/detail`,
      query,
    )
    window.open(winUrl, '_blank')
    emit('cancelDia')
  })
}

const downloadTemplate = () => {
  if (!downloadTemplateForm.value) return
  downloadTemplateForm.value.validate((valid) => {
    if (!valid) {
      return
    }
    const param = {
      templateId: templateForm.value.templateId,
      formPurpose: props.formPurpose,
    }
    downloadMaterialTemplate(param).then((res) => {
      const { data, headers } = res
      const blob = new Blob([data], { type: headers['content-type'] })
      if ('application/json' == data.type) {
        let fileReader = new FileReader()
        fileReader.readAsText(blob, 'utf-8')
        fileReader.onload = () => {
          let msg = JSON.parse(fileReader.result)
          ElNotification.error(msg || 'Export fail')
        }
        return
      }
      let patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
      let disposition = patt.exec(headers['content-disposition'])
      let fileName = disposition && disposition[1] ? disposition[1] : 'template'
      let dom = document.createElement('a')
      let url = window.URL.createObjectURL(blob)
      dom.href = url
      dom.download = decodeURIComponent(fileName)
      dom.style.display = 'none'
      document.body.appendChild(dom)
      dom.click()
      dom.parentNode.removeChild(dom)
      window.URL.revokeObjectURL(url)
    })
  })
}

const queryTemplateList = () => {
  lists.value.templateList = []
  templateForm.value.templateId = ''
  if (!templateForm.value.productLineCode) {
    return
  }
  const param = {
    buCode: templateForm.value.productLineCode,
    customerGroupCode: templateForm.value.customerGroupCode,
    queryAllVersion: 0,
    rows: 10,
    page: 1,
    formPurpose: props.formPurpose,
  }
  getSampleTemplateList(param).then((res) => {
    lists.value.templateList = res.data || []
  })
}

onMounted(() => {
  let customerList = props.customerList || []
  const currentUserInConfig = customerList.find(
    (c) => c.customerGroupCode === userInfo.value?.customerGroupCode,
  )
  if (!currentUserInConfig && userInfo.value) {
    const { customerGroupCode, customerGroupId, customerGroupName } =
      userInfo.value
    if (customerGroupCode) {
      customerList.push({
        customerGroupCode,
        customerGroupId,
        customerGroupName,
      })
    }
  }
  lists.value.customerList = customerList
  lists.value.productLineCodes = props.productLineList || []
})
</script>

<style scoped>
.sgs_smart_new_material_downloadTemplate {
}
</style>
