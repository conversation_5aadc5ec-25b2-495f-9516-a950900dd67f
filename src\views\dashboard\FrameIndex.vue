<template>
  <div class="frame_index_main">
    <grid-layout
        :layout.sync="layout"
        :col-num="12"
        :row-height="baseHeight"
        :is-draggable="false"
        :is-resizable="false"
        :is-mirrored="false"
        :vertical-compact="true"
        :margin="[10, 10]"
        :use-css-transforms="true">
      <grid-item v-for="(item, index) in layout"
                 :x="item.x"
                 :y="item.y"
                 :w="item.w"
                 :h="item.h"
                 :i="item.i"
                 :key="index" class="gridItem">
        <component :is="item.tag" />
      </grid-item>
    </grid-layout>
  </div>
</template>

<script>
  import {GridItem, GridLayout} from 'vue-grid-layout';
  import Vue from "vue";
  import {mapGetters} from "vuex";
  export default {
    name: "FrameIndex",
    components:{ GridLayout, GridItem },
    props: {
      layout: {
        type: Array,
        default: []
      }
    },
    data() {
      return {
        baseHeight: 30,
      }
    },

    computed: {
      ...mapGetters(["userInfo", "permission"]),
    },

    created() {
      this.layout.forEach((item, index) => {
        Vue.component(item.tag, this.loadComponents(item.path, item.name));
      });
    },

    methods: {
      loadComponents(path, name) {
        return () => import(`@/components/${path}/${name}.vue`);
      },
    }
  }
</script>

<style scoped lang="scss">
  .frame_index_main {
    width: 100%;
  }
</style>
