import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'element-plus/dist/index.css'
import ElementPlus from 'element-plus'
import './assets/style/common.scss'
import * as Sentry from '@sentry/vue'
import { Integrations } from '@sentry/tracing'
import config from './../package.json'
import i18n from './utils/i18n'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import directive from './directives' // 引入自定义指令

const app = createApp(App)
const userRouter = router()
const currentEnv = import.meta.env.MODE
currentEnv === 'production' &&
  Sentry.init({
    app,
    dsn: 'https://<EMAIL>/43',
    release: config.version,
    integrations: [
      new Integrations.BrowserTracing({
        routingInstrumentation: Sentry.vueRouterInstrumentation(userRouter),
        tracePropagationTargets: ['localhost'],
      }),
      new Sentry.Replay(),
    ],
    tracesSampleRate: 1.0,
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    ignoreErrors: [
      'ResizeObserver loop completed with undelivered notifications.',
      'ResizeObserver loop limit exceeded',
      'Non-Error promise rejection captured with value: cancel',
    ],
  })

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册自定义指令
directive(app)

app.use(i18n)
app.use(userRouter).use(store).use(ElementPlus).mount('#app')
