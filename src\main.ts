import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/style/reset.scss'
import './assets/style/icontfont.css'
import './assets/style/element.scss'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as Sentry from '@sentry/vue'
import { Integrations } from '@sentry/tracing'
import config from './../package.json'
import i18n from './utils/i18n'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import directive from './directives' // 引入自定义指令

const app = createApp(App)
const userRouter = router()
const currentEnv = import.meta.env.MODE
// currentEnv === 'production' &&
  Sentry.init({
    app,
    dsn: "https://<EMAIL>/44",
    release: config.version,
    integrations: [
      new Integrations.BrowserTracing({
        routingInstrumentation: Sentry.vueRouterInstrumentation(userRouter),
        tracePropagationTargets: ['localhost'],
      }),
      new Sentry.Replay(),
    ],
    tracesSampleRate: 1.0,
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    ignoreErrors: [],
  })

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册自定义指令
directive(app)

app.use(i18n)
app
  .use(userRouter)
  .use(store)
  .use(ElementPlus, {
    locale: zhCn,
  })
  .mount('#app')
