<template>
    <div id="TableHeaderCollapseFilter" class="TableHeaderCollapseFilter" v-loading="loadingFilter">
        <el-collapse v-model="activeNames" :size="size || 'mini'">
            <el-collapse-item
                    v-for="(col,title) of filters"
                    :title="title"
                    :name="title"
                    :key="title">
                <template slot="title">
                   <b>{{title}}</b>
                </template>
                <el-row :gutter="20">
                    <el-col :span="8"
                            v-for="(field,index) in col"
                            :key="index">
                        <el-form-item :label="field.displayName"
                                      :prop="field.fieldCode"
                                      :required="field.required=='true'"
                                      style="width: 100%"
                        >
                            <component
                                    :value.sync="formData[field.fieldCode]"
                                    :is="componentMap[field.fieldType]"
                                    :id="field.fieldCode"
                                    :form-obj="formData[field.fieldCode]"
                                    style="width: 100%"
                                    :key="field.fieldCode"
                                    :placeholder="field.displayName"
                                    :all-options="field"
                            ></component>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script>
    import InputText from "./template/InputText";
    import DatePic from "./template/DatePic";
    import SelectComp from "./template/SelectComp";
    export default {
        name: "TableHeaderCollapseFilter",
        inject:['size','searchSubmit','language'],
        data() {
            return {
                loadingFilter:false,
                componentMap: {
                    text: InputText,
                    date: DatePic,
                    select:SelectComp
                },
                activeNames:[],
                filters:{},
                firstSectionName:''
            }
        },
        methods: {
            Search(){
                this.searchSubmit.fn();
            },
            createFilterInput(){
                this.filterConfig.forEach((col,index)=>{
                    let {options} = col;
                    try{
                        options = JSON.parse(options);
                    }catch (e) {
                        options = {}
                    }
                    let {section} = options;
                    section = section || 'General';
                    if(!this.firstSectionName){
                        this.firstSectionName = section;
                    }
                    //全部默认展开
                    this.filters[section] = [];
                });
                //转成具体组件
                this.filterConfig.forEach((col,index)=>{
                    let {options} = col;
                    try{
                        options = JSON.parse(options);
                    }catch (e) {
                        options = {}
                    }
                    let {section,languageLab} = options;
                    section = section || 'General';
                    let filter = JSON.parse(JSON.stringify(col));
                    let currentLan = this.language();
                    if(currentLan){
                        filter.displayName = languageLab[currentLan] || filter.displayName;
                    }
                    this.filters[section].push(filter)
                })
                this.activeNames = [this.firstSectionName];
            },
            changeLabel(lan){
                Object.keys(this.filters).forEach(section=>{
                    this.filters[section].forEach(f=>{
                        let {options} = f;
                        try{
                            options = JSON.parse(options)
                            let {languageLab} = options ||{};
                            if(languageLab){
                                f.displayName = languageLab[lan] || f.displayName;
                            }
                        }catch (e) {

                        }
                    })
                })
                this.$forceUpdate();
            },
        },
        created() {
            this.createFilterInput();
        },
        mounted() {
        },
        computed:{
            getLanguage(){
                return this.language();
            }
        },
        watch:{
            getLanguage:{
                immediate:true,
                handler(newV){
                    this.changeLabel(newV);
                }
            },
            filterConfig:{
                immediate:true,
                deep:true,
                handler(newV){
                    this.createFilterInput()
                }
            },
        },
        props:{
            formData:{
                type:Object,
                default:()=>{
                    return {}
                }
            },
            tableLan:String,
            filterConfig:{
                type:Array,
                default:()=>{
                    return []
                }
            }
        },
        components: {
            InputText,DatePic,SelectComp
        }
    }
</script>

<style scoped>
    .TableHeaderCollapseFilter {
    }
</style>