<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.myOrder')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.myOrder')}}</h1>
        <el-card shadow="never" class="sgs-box" >
            <el-row>
                <el-form :inline="true" :model="query" size="medium">
                    <el-form-item>
                        <el-col :span="11">
                            <el-date-picker type="date" :placeholder="$t('invoice.fapiaoIssuedStartDate')"
                                            format="yyyy-MM-dd"
                                            :value="query.createStartDate"
                                            @input="val => updateStartDate(val)"
                                            :picker-options="pickerOptions"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                        <el-col class="line" :span="2">-</el-col>
                        <el-col :span="11">
                            <el-date-picker :placeholder="$t('invoice.fapiaoIssuedEndDate')" format="yyyy-MM-dd"
                                            :value="query.createEndDate"
                                            @input="val => updateEndDate(val)"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                        <span style="color: red"> *{{$t('dateValidate.startMaxError')}}</span>
                    </el-form-item>

                    <el-form-item>
                        <el-select clearable filterable :placeholder="$t('invoice.paymentStatus')"
                                v-model="query.status" style="width:100%">
                            <el-option label="全部"
                                    :value="0"></el-option>
                            <el-option label="待支付"
                                    :value="1"></el-option>
                            <el-option label="支付成功"
                                    :value="2"></el-option>
                            <el-option label="支付失败"
                                    :value="3"></el-option>
                            <el-option label="已退款"
                                    :value="4"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onReset">{{$t('operation.reset')}}</el-button>
                        <el-button type="primary" @click="onSearch" v-if="permissionList.searchBtn">
                            {{$t('operation.search')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <!--:data="tableData"-->
                <el-table
                        stripe="true"
                        :data="tableData"
                        v-loading="loading"
                        :element-loading-text="$t('loading')"
                        style="width: 100%"
                        size="medium"
                >

                    <el-table-column
                            fixed
                            width="180px"
                            prop="batchNo"
                            align="center"
                            :label="$t('invoice.order.orderNo')"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="totalAmount"
                            width="160px"
                            align="center"
                            :label="$t('invoice.order.orderPayable')">
                        <template slot-scope="scope">
                            <span>{{scope.row.totalAmount| rounding}}</span>
                        </template>
                    </el-table-column>
                    <!--<el-table-column
                            prop="payPayable"
                            width="160px"
                            align="center"
                            :label="$t('invoice.order.payPayable')">
                    </el-table-column>-->
                    <el-table-column
                            prop="paymentPlatform"
                            width="180px"
                            align="center"
                            :formatter="payTypeFormatter"
                            :label="$t('invoice.order.payType')">
                    </el-table-column>
                    <el-table-column
                            show-overflow-tooltip="true"
                            prop="status"
                            width="180px"
                            align="center"
                            :formatter="orderStatusFormatter"
                            :label="$t('invoice.order.orderStatus')">
                    </el-table-column>
                    <el-table-column
                            show-overflow-tooltip="true"
                            prop="createDate"
                            width="160px"
                            align="center"
                            :label="$t('user.createTime')">
                    </el-table-column>

                    <el-table-column
                            fixed="right"
                            :label="$t('operation.title')"
                            align="center">
                        <template slot-scope="scope">
                            <el-button type="text" v-if="scope.row.status==1"
                                    @click="goPayment(scope.row)">
                                {{$t('invoice.order.payment')}}
                            </el-button>

                            <el-button type="text" v-if="scope.row.status==1"
                                    @click="cancelOrder(scope.row)">
                                {{$t('invoice.order.cancelOrder')}}
                            </el-button>

                            <el-button type="text" v-if="scope.row.status!=1"
                                    @click="orderDetail(scope.row)">
                                {{$t('invoice.order.detail')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="query.pageNo"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="query.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>

                <el-dialog :title="$t('invoice.paymentInfo')" :visible.sync="dialogPayInvoiceInfo" width="70%" top="10vh"
                >
                    <div class="modal-body">
                        <el-table :data="selectedInvocieData">
                            <el-table-column property="invoiceNo" :label="$t('invoice.invoiceNo')" width="120px">
                            </el-table-column>

                            <el-table-column
                                    prop="fapiaoNo"
                                    width="120px"
                                    align="center"
                                    :label="$t('invoice.fapiaoNo')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.fapiaoNo}}</span>
                                </template>
                            </el-table-column>
                            <!--<el-table-column
                                    prop="awbNo"
                                    width="120px"
                                    align="center"
                                    :label="$t('invoice.awbNo')">
                                <template slot-scope="scope">
                                            <span>{{scope.row.invoiceInfo.awbNo}}</span>
                                </template>
                            </el-table-column>-->
                            <el-table-column
                                    prop="fapiaoIssuedDate"
                                    width="140px"
                                    align="center"
                                    :label="$t('invoice.fapiaoIssuedDate')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.fapiaoIssuedDate}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    show-overflow-tooltip="true"
                                    prop="reportNo"
                                    width="180px"
                                    align="center"
                                    :label="$t('invoice.reportNo')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.reportNo}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    show-overflow-tooltip="true"
                                    prop="clientReferenceNo"
                                    align="center"
                                    :label="$t('invoice.clientReferenceNo')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.clientReferenceNo}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    align="center"
                                    prop="currency"
                                    width="90px"
                                    :label="$t('invoice.currency')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.currency}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    align="center"
                                    show-overflow-tooltip="true"
                                    width="100px"
                                    prop="contactName"
                                    :label="$t('invoice.contactName')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.contactName}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column property="amount"
                                            width="120px"
                                            :label="$t('invoice.invoiceBalance')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.amount| rounding}}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="fc-price-info" style="text-align: right;margin-top: 10px; line-height: 25px">
                            <span class="price-tit">{{$t('invoice.totalPayable')}}：</span>
                            <span class="price-num" id="sumPayPriceId">￥{{totalAmout}}</span>
                            <p>{{$t('invoice.payTop1')}}<span style="color: #f16a6a ">{{$t('invoice.payTop2')}}</span>{{$t('invoice.payTop3')}}
                            </p>
                        </div>
                        <div class="bg_title_tint">
                            <h3 class="line_h_3em pl_3 c_title text_14em">{{$t('invoice.validate.selectPayType')}}</h3>
                        </div>
                        <div>
                            <el-radio v-model="payType" disabled="true" v-for="(item,index) in payTypes" size="medium"
                                    :label="item.code"
                                    :key="item.code">
                                <img v-if="item.code==100000" src="@/images/alipay.png" class="file_icon"
                                    style="width: 80px">
                                <img v-if="item.code==100001" src="@/images/wechat.png" class="file_icon"
                                    style="width: 80px">
                                <img v-if="item.code==100002" src="@/images/unionPay.png" class="file_icon"
                                    style="width: 80px">
                                {{item.name}}
                            </el-radio>
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer" style="text-align: right;top: 5px;">
                        <el-button type="primary" @click="submitPayment">{{$t('invoice.submitPay')}}</el-button>
                    </div>
                </el-dialog>

                <el-dialog :title="$t('invoice.order.detail')" :visible.sync="dialogPayOrderDetail" width="70%" top="10vh"
                >
                    <div class="modal-body">
                        <el-table :data="selectedInvocieData">
                            <el-table-column property="invoiceNo" :label="$t('invoice.invoiceNo')" width="120px">
                            </el-table-column>

                            <el-table-column
                                    prop="fapiaoNo"
                                    width="120px"
                                    align="center"
                                    :label="$t('invoice.fapiaoNo')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.fapiaoNo}}</span>
                                </template>
                            </el-table-column>
                            <!--<el-table-column
                                    prop="awbNo"
                                    width="120px"
                                    align="center"
                                    :label="$t('invoice.awbNo')">
                                <template slot-scope="scope">
                                            <span>{{scope.row.invoiceInfo.awbNo}}</span>
                                </template>
                            </el-table-column>-->
                            <el-table-column
                                    prop="fapiaoIssuedDate"
                                    width="140px"
                                    align="center"
                                    :label="$t('invoice.fapiaoIssuedDate')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.fapiaoIssuedDate}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    show-overflow-tooltip="true"
                                    prop="reportNo"
                                    width="180px"
                                    align="center"
                                    :label="$t('invoice.reportNo')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.reportNo}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    show-overflow-tooltip="true"
                                    prop="clientReferenceNo"
                                    align="center"
                                    :label="$t('invoice.clientReferenceNo')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.clientReferenceNo}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    align="center"
                                    prop="currency"
                                    width="90px"
                                    :label="$t('invoice.currency')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.currency}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    align="center"
                                    show-overflow-tooltip="true"
                                    width="100px"
                                    prop="contactName"
                                    :label="$t('invoice.contactName')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceInfo.contactName}}</span>
                                </template>
                            </el-table-column>


                            <el-table-column property="amount"
                                            width="120px"
                                            :label="$t('invoice.invoiceAmount')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.amount| rounding}}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="fc-price-info" style="text-align: right;margin-top: 10px; line-height: 25px">
                            <span class="price-tit">{{$t('invoice.order.orderPayable')}}：</span>
                            <span class="price-num" id="sumPayPriceId">￥{{totalAmout}}</span>
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer" style="text-align: right;top: 5px;">
                        <!-- <el-button type="primary" @click="submitPayment">{{$t('invoice.submitPay')}}</el-button>-->
                    </div>
                </el-dialog>


            </el-row>
        </el-card>

    </basic-container>
</template>

<script>
    import {getList, getPayType, createInvoiceOrder, cancel, goPayment, detail} from "@/api/invoice/paymentOrder";
    import {mapGetters} from "vuex";
    import moment from 'moment'
    import {validatenull} from "@/util/validate";
    import SgsDateRange from './../../components/date/date-range'

    export default {
        components: {
            SgsDateRange
        },
        data() {
            return {
                 pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < new Date('2022-01-01')-8.64e7;
                    },
                },
                selectOrderObj: {},
                payHtml: '',
                dialogPay: false,
                payTypes: [],
                payType: null,
                totalAmout: 0,//总金额
                dialogPayInvoiceInfo: false,
                dialogPayOrderDetail: false,
                selectedInvocieData: [],
                conditions: {
                    reportNo: '',
                    clientReferenceNo: '',
                    labName: ''
                },
                tableData: [],
                searchReport: '',
                search: '',
                loading: false,
                form: {},
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    createStartDate: null,
                    createStartDate: null,
                    paymentPlatform: '',
                    status: ''
                },
                submitForm: {
                    payType: '',
                    totalAmount: '',
                    frontUrl: '',
                    invoiceList: []
                },
                page: {
                    total: 0
                },

            }
        },
        filters: {
            rounding(value) {
                return value.toFixed(2)
            }
        },
        watch: {
            'conditions.reportNo': function (newVal) {
                //获取路由参数
            },
            //监听语言变化
            language: function (newVal) {
                 //this.onload();
            }


        },
        methods: {
            orderDetail(row) {
                this.selectedInvocieData = [];
                this.selectedInvocieData = row.invoiceList;
                this.totalAmout = row.totalAmount;
                this.payType = row.paymentPlatform;

                var cancelOrder = {};
                cancelOrder.batchNo = row.batchNo
                 //开启loading
                const loading = this.$loading({
                    lock: true,
                    text: '加载中…',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                })
                detail(cancelOrder).then(res => {
                    loading.close();
                    let data = res.data.data;
                    this.selectedInvocieData = data.invoiceList;
                    this.dialogPayOrderDetail = true;
                }).catch(() => {
                    loading.close();
                });
            },
            cancelOrder(row) {
                this.$confirm(this.$t('invoice.order.cancelOrderTip'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    var cancelOrder = {};
                    cancelOrder.batchNo = row.batchNo
                    //打开loading
                    this.loading=true;
                    cancel(cancelOrder).then(() => {
                         this.loading=false;
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.query.pageNo = 1;
                        this.query.createStartDate = moment().subtract('days', 30).format('YYYY-MM-DD');
                        this.query.createEndDate = moment().format('YYYY-MM-DD');
                        this.onLoad();
                    }).catch(() => {
                    this.loading=false;
                    });
                })
            },
            goPayment(row) {
                debugger;
                this.selectOrderObj = {};
                this.selectOrderObj = row;
                this.selectedInvocieData = [];
                this.totalAmout = row.totalAmount;
                this.payType = row.paymentPlatform;
                var cancelOrder = {};
                cancelOrder.batchNo = row.batchNo
                  //开启loading
                const loading = this.$loading({
                    lock: true,
                    text: '加载中…',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                })
                detail(cancelOrder).then(res => {
                    loading.close();
                    let data = res.data.data;
                    this.selectedInvocieData = data.invoiceList;
                    this.dialogPayInvoiceInfo = true;
                }).catch(() => {
                    loading.close();
                });
            },
            submitPayment() {

                var goPaymentObj = {};
                goPaymentObj.batchNo = this.selectOrderObj.batchNo

                //开启loading
                const loading = this.$loading({
                    lock: true,
                    text: '正在前往支付页面',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                })
                //提交
                goPayment(goPaymentObj).then(res => {
                    let result = res.data.data;
                    if (!validatenull(result)) {
                        if (result.errorCode != 0) {//错误 给出提示
                            loading.close();
                            this.$notify({
                                title: this.$t('tip'),
                                message: result.errorMessage,
                                type: 'warning'
                            });
                            return false;
                        }
                        loading.close();
                        var payHtml = result.result.payResult;
                        var doc = window.open().document;
                        if (this.selectOrderObj.paymentPlatform == '100000') {
                            doc.write("<html><head><title></title></head><body><br/>");
                            doc.write(payHtml);
                            doc.write("</body></html>");
                        } else if (this.selectOrderObj.paymentPlatform == '100002') {
                            doc.write(payHtml);
                        }
                    }
                }).catch(() => {
                    loading.close();
                });
                this.dialogPayInvoiceInfo = false;
                /*this.$router.push({
                    path: '/payError',
                })*/
            },

            queryPayType() {
                getPayType().then(res => {
                    let payTypesData = res.data.data;
                    if (!validatenull(payTypesData)) {
                        this.payTypes = res.data.data;
                    }
                }).catch(() => {

                });
            },
            async updateStartDate(val) {

                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.createStartDate = date;
            },
            async updateEndDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.createEndDate = date;
            },
            payTypeFormatter(row, column) {
                var payType = row[column.property];
                var payTypeName = "";
                switch (payType) {
                    case '100000' :
                        payTypeName = this.$t('pay.payType.alipay')
                        break
                    case '100002':
                        payTypeName = this.$t('pay.payType.unionPay')
                        break
                    case '100001':
                        payTypeName = this.$t('pay.payType.wechat')
                        break
                    default:
                        payTypeName = "";
                        break
                }
                return payTypeName;
            },
            orderStatusFormatter(row, column) {
                var orderStatus = row[column.property];
                var statusName = "";
                switch (orderStatus) {
                    case 1:
                        statusName = this.$t('pay.waitForPay')
                        break
                    case 2:
                        statusName =  this.$t('pay.paySuccess')
                        break
                    case 3:
                        statusName = this.$t('pay.Paymentfailed')
                        break
                    case 4:
                        statusName = this.$t('pay.Refunded')
                        break
                    default:
                        statusName = "";
                        break
                }
                return statusName;
            },
            checkTime() {
                var begintime = this.query.createStartDate;
                var endtime = this.query.createEndDate;
                if (validatenull(endtime)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.endDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }

                var time1 = new Date(begintime).getTime();
                var time2 = new Date(endtime).getTime();
                if (validatenull(begintime)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.startDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                if (validatenull(endtime == '')) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.endDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                if (time1 > time2) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.endDateErrorValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                //判断时间跨度是否大于6个月  修改为12个月
                var flag = this.checkDateSection(begintime, endtime);
                if (!flag) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.betweenDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                //验证当前时间最多向前一年
                endtime = moment().format('YYYY-MM-DD');
                flag = this.checkDateSection(begintime, endtime);
                if (!flag) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.betweenDateValidateOneYear'),
                        type: 'warning'
                    });
                    return false;
                }


                return true;
            },


            eFapiaoClick(row) {
                window.open(row.efapiaoFileUrl, '_blank')
            },
            async updateStartDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.createStartDate = date;
                //this.onLoad();
            },
            async updateEndDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.createEndDate = date;
                // await this.onLoad();

            },

            onReset() {
                this.query.pageNo = 1;
                this.query.createStartDate = moment().subtract('days', 30).format('YYYY-MM-DD');
                this.query.createEndDate = moment().format('YYYY-MM-DD');
                this.query.status = '';
                this.query.paymentPlatform = '';
                this.onLoad();
            },
            checkDateSection(begintime, endtime) {
                var flag = true;
                var arr1 = begintime.split('-');
                var arr2 = endtime.split('-');

                arr1[1] = parseInt(arr1[1]);
                arr1[2] = parseInt(arr1[2]);
                arr2[1] = parseInt(arr2[1]);
                arr2[2] = parseInt(arr2[2]);
                if (arr1[0] == arr2[0]) {//同年
                    if (arr2[1] - arr1[1] > 12) { //月间隔超过6个月
                        flag = false;
                    } else if (arr2[1] - arr1[1] == 12) { //月相隔3个月，比较日
                        if (arr2[2] > arr1[2]) { //结束日期的日大于开始日期的日
                            flag = false;
                        }
                    }
                } else { //不同年
                    if (arr2[0] - arr1[0] > 1) {
                        flag = false;
                    } else if (arr2[0] - arr1[0] == 1) {
                        if (arr1[1] < 1) { //开始年的月份小于1时，不需要跨年
                            console.log("arr1[1] < 7");
                            flag = false;
                        } else if (arr1[1] + 12 - arr2[1] < 12) { //月相隔大于12个月
                            console.log("arr1[1]+12-arr2[1] < 12");
                            flag = false;
                        } else if (arr1[1] + 12 - arr2[1] == 12) { //月相隔3个月，比较日
                            if (arr2[2] > arr1[2]) { //结束日期的日大于开始日期的日
                                console.log("截止日 arr2[2] > " + arr2[2]);
                                console.log("开始日 arr1[2] > " + arr1[2]);
                                flag = false;
                            }
                        }
                    }
                }
                return flag;
            },
            onSearch() {
                this.query.pageNo = 1;
                this.onLoad();
            },
            onLoad() {
                var loadFlag = true;
                if (validatenull(this.query.createStartDate) && validatenull(this.query.createEndDate)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.startDateAndEndDateValidate1'),
                        type: 'warning'
                    });
                    return false;
                }
                loadFlag = this.checkTime();
                if (!loadFlag) {
                    return false;
                }
                this.loading = true;
                getList(this.query).then(res => {
                    this.loading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(pageNo) {
                this.query.pageNo = pageNo;
                this.onLoad();
            },
            sizeChange(pageSize) {
                this.query.pageNo = 1;
                this.query.pageSize = pageSize;
                this.onLoad();
            },

            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                    .then(() => {
                        remove(row.id);
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad();
                    });

            }
        },
        created() {
            //设置默认查询时间最近一个月
            this.query.pageNo = 1;
            this.query.createStartDate = moment().subtract('days', 30).format('YYYY-MM-DD');
            this.query.createEndDate = moment().format('YYYY-MM-DD');
            this.onLoad();
            //查询当前用户的支付方式
            this.queryPayType();
        },
        computed: {
            ...mapGetters([
                "userInfo",
                "permission"
            ]),
            permissionList() {
                return {
                    searchBtn: this.vaildData(this.permission['sgs:invoice:searchBtn'], false),
                    billFileBtn: this.vaildData(this.permission['sgs:invoice:billFileBtn'], false),
                    invoiceFileBtn: this.vaildData(this.permission['sgs:invoice:invoiceFileBtn'], false),
                };
            },
        },

    }
</script>

<style scoped>
    .ml_05em {
        margin-left: 0.5em !important;
    }

    .mt_03em {
        margin-top: 0.3em !important;
    }

    .mr_3 {
        margin-right: 3%;
    }

    .fl {
        float: left !important;
    }

    .bg_title_tint {
        background-color: #eeeff6 !important;
    }

    .price-num {
        color: #e4393c;
        font-family: Verdana;
        font-weight: 700;
        font-size: 18px;
        min-width: 122px;
        _width: 122px;
        float: right;
        *float: none;
        text-align: right;
    }
</style>
