import { setToken, removeToken, encryptor } from '@/utils/auth'
import { setStore } from '@/utils/store'
import { isURL, validatenull } from '@/utils/validate'
import { deepClone } from '@/utils/util'
import website from '@/config/website'
import {
  loginByUsername,
  validateToken,
  getUserInfo,
  getMenu,
  getTopMenu,
  getButtons,
   routes,
   loginEFiling,
} from '@/api/user'

// Define the type for the state object
interface UserState {
  userInfo: any[]
  permission: { [key: string]: boolean }
  dimensions: any
  posts: any[]
  roles: any[]
  menu: any[]
  menuAll: any[]
  token: string
  taskListDialog: any
  taskType: string
  addBuyer: boolean
  addContact: boolean
  setAddress: boolean
  nextStep: number
  [key: string]: any // Add an index signature
}

function addPath(ele: any, first: boolean) {
  const menu = website.menu
  const propsConfig = menu.props
  const propsDefault = {
    label: propsConfig.label || 'name',
    path: propsConfig.path || 'path',
    icon: propsConfig.icon || 'icon',
    children: propsConfig.children || 'children',
  }
  const icon = ele[propsDefault.icon]
  ele[propsDefault.icon] = validatenull(icon) ? menu.iconDefault : icon
  const isChild =
    ele[propsDefault.children] && ele[propsDefault.children].length !== 0
  if (!isChild) ele[propsDefault.children] = []
  if (!isChild && first && !isURL(ele[propsDefault.path])) {
    ele[propsDefault.path] = ele[propsDefault.path] + '/index'
  } else {
    ele[propsDefault.children].forEach((child: any) => {
      addPath(child, false)
    })
  }
}

const user = {
  state(): UserState {
    return {
      userInfo: [],
      permission: {},
      dimensions: {},
      posts: [],
      roles: [],
      menu: [],
      menuAll: [],
      token: '',
      taskListDialog: null,
      taskType: '',
      addBuyer: false,
      addContact: false,
      setAddress: false,
      nextStep: -1,
    }
  },
  actions: {
    async LoginByUsername({ commit }: { commit: any }, userInfo: any) {
    
      try {
        let pass = encryptor(userInfo.password)
        let permission: string[] = []
        const res = await loginByUsername(
          userInfo.tenantId,
          userInfo.username,
          pass,
          userInfo.type,
          userInfo.receiveMarketingCommunication,
        )
        const data = res.data
        commit('SET_TASK_DIALOG', data.guide)
        commit('SET_TOKEN', data.accessToken)
        commit('SET_USERINFO', data)

        permission.push(...data.permissions.split(','))
        commit('SET_DIMENSIONS', data.dimensions)
        commit('SET_POSTS', data.posts)
        let { tcpp, userMgtId } = data
        commit('SET_PERMISSION', permission)

        const routesRes = await routes()
        let routesData = routesRes.data
        permission.push(...routesData.buttons.map((item: any) => item.code))
        commit('SET_PERMISSION', permission)

       await loginEFiling(data.accessToken)
        return { tcpp, userMgtId }
      } catch (error) {
        throw error
      }
    },
    async LoginByToken({ commit }: { commit: any }, token: string) {
      try {
        const res = await validateToken(token)
        const data = res.data
        commit('SET_TOKEN', data.accessToken)
        commit('SET_USERINFO', data)
        commit('SET_PERMISSION', data.permissions.split(','))
        commit('SET_DIMENSIONS', data.dimensions)
        commit('SET_POSTS', data.posts)
      } catch (error) {
        throw error
      }
    },
    async GetButtons({ commit }: { commit: any }) {
      try {
        const res = await getButtons()
        const data = res.data
        commit('SET_PERMISSION', data)
      } catch (error) {
        throw error
      }
    },
    async LoginByPhone({ commit }: { commit: any }, userInfo: any) {
      try {
        const res = await loginByUsername(userInfo.phone, userInfo.code)
        const data = res.data
        commit('SET_TOKEN', data)
      } catch (error) {
        throw error
      }
    },
    async GetUserInfo({ commit }: { commit: any }) {
      try {
        const res = await getUserInfo()
        const data = res.data
        commit('SET_ROLES', data.roles)
        return data
      } catch (error) {
        throw error
      }
    },

    async LogOut({ commit }: { commit: any }) {
      try {

        commit('SET_TOKEN', '')
        commit('SET_MENU', [])
        commit('SET_ROLES', [])
        removeToken()
      } catch (error) {
        throw error
      }
    },
    async FedLogOut({ commit }: { commit: any }) {
      commit('SET_TOKEN', '')
      commit('SET_MENU', [])
      commit('SET_ROLES', [])
      removeToken()
    },
    async GetTopMenu() {
      try {
        const res = await getTopMenu()
        const data = res.data || []
        return data
      } catch (error) {
        throw error
      }
    },
    async GetMenu({ commit }: { commit: any; dispatch: any }, parentId: any) {
      try {
        const res = await getMenu(parentId)
        const data = res.data
        let menu = deepClone(data)
        menu &&
          menu.forEach((ele: any) => {
            addPath(ele, true)
          })
        commit('SET_MENU', menu)
        return menu
      } catch (error) {
        throw error
      }
    },
  },
  mutations: {
    SET_TASK_DIALOG(state: UserState, bool: any) {
      state.taskListDialog = bool
    },
    SET_TASK_TYPE(state: UserState, type: string) {
      state.taskType = type
    },
    SET_GUIDE(state: UserState, obj: { name: string; val: any }) {
      state[obj.name] = obj.val
    },
    SET_NEXTSTEP(state: UserState, num: number) {
      state.nextStep = num
    },
    SET_TOKEN(state: UserState, token: string) {
      setToken(token)
      state.token = token
      setStore({ name: 'token', content: token })
    },
    SET_USERINFO(state: UserState, userInfo: any) {
      state.userInfo = userInfo
      setStore({ name: 'userInfo', content: state.userInfo })
    },
    SET_MENU(state: UserState, menu: any[]) {
      state.menu = menu
      setStore({ name: 'menu', content: state.menu, type: 'session' })
    },
    SET_MENU_ALL(state: UserState, menuAll: any[]) {
      state.menuAll = menuAll
    },
    SET_ROLES(state: UserState, roles: any[]) {
      state.roles = roles
    },
    SET_PERMISSION(state: UserState, permission: string[]) {
      state.permission = {}
      permission &&
        permission.forEach((ele) => {
          state.permission[ele] = true
        })
      setStore({
        name: 'permission',
        content: state.permission,
        type: 'session',
      })
    },
    SET_DIMENSIONS(state: UserState, dimensions: any) {
      state.dimensions = dimensions
      setStore({
        name: 'dimensions',
        content: state.dimensions,
        type: 'session',
      })
    },
    SET_POSTS(state: UserState, posts: any[]) {
      state.posts = posts
      setStore({ name: 'posts', content: state.posts, type: 'session' })
    },
  },
}

export default user
