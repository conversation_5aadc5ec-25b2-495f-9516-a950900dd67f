// 配置编译环境和线上环境之间的切换

let baseUrl = '';
let iconfontVersion = ['567566_pwc3oottzol', '1066523_v8rsbcusj5q'];
let iconfontUrl = `//at.alicdn.com/t/font_$key.css`;
let codeUrl = `${baseUrl}/code`
let changePasswordUrl='';
let testResultUrl='';
let inspectionUrl='';
let scmBaseUrl='';
let inputResultUrl='';
let slPortalUrlEn='';
let slPortalUrlCn='';
let currentEnv='uat';
const env = process.env
if (env.VUE_APP_ENV == 'development') {
    baseUrl = ``; // 开发环境地址
    currentEnv='uat';
    changePasswordUrl="https://testuserpassport.sgsmart-online.com/sgsExternalWeb/forgot_password.html";//暂时将开发环境配置为测试地址
    testResultUrl="http://127.0.0.1:8082/#/?token=";
    inspectionUrl="https://newinspection-uat.sgsonline.com.cn/";
    scmBaseUrl="http://127.0.0.1:8100/";
    inputResultUrl="http://127.0.0.1:8082/resultweb";
    slPortalUrlEn="10.168.128.25:81/sl-en.html";
    slPortalUrlCn="10.168.128.25:81/sl-cn.html";
} else if (env.VUE_APP_ENV == 'production') {
    baseUrl = ``; //生产环境地址
    currentEnv='prod';
    changePasswordUrl="https://userpassport.sgsmart-online.com/sgsExternalWeb/forgot_password.html";
    testResultUrl='https://plus.sgsmart-online.com/resultweb/#/?token=';
    inspectionUrl="https://newinspection.sgsonline.com.cn/";
    scmBaseUrl="https://plus.sgsmart-online.com/";
    inputResultUrl="https://plus.sgsmart-online.com/resultweb";
    slPortalUrlEn="portal-sl.sgsmart-online.com";
    slPortalUrlCn="portal-sl.sgsmart-online.com/sl-cn.html";
} else if (env.VUE_APP_ENV == 'uat') {
    baseUrl = ``; //测试环境地址
    currentEnv='uat';
    changePasswordUrl="https://testuserpassport.sgsmart-online.com/sgsExternalWeb/forgot_password.html";
    testResultUrl='https://uat.sgsmart-online.com/resultweb/#/?token=';
    inspectionUrl="https://newinspection-uat.sgsonline.com.cn/";
    scmBaseUrl="https://uat.sgsmart-online.com/"
    inputResultUrl="https://uat.sgsmart-online.com/resultweb";
    slPortalUrlEn="10.168.128.25:81/sl-en.html";
    slPortalUrlCn="10.168.128.25:81/sl-cn.html";
}else if(env.VUE_APP_ENV == 'test'){
     baseUrl = ``; //测试环境地址
    currentEnv='test';
    changePasswordUrl="https://testuserpassport.sgsmart-online.com/sgsExternalWeb/forgot_password.html";
    testResultUrl='https://test.sgsmart-online.com/resultweb/#/?token=';
    inspectionUrl="https://newinspection-uat.sgsonline.com.cn/";
    scmBaseUrl="https://test.sgsmart-online.com/"
    inputResultUrl='https://test.sgsmart-online.com/resultweb';
    slPortalUrlEn="10.168.128.25:81/sl-en.html";
    slPortalUrlCn="10.168.128.25:81/sl-cn.html";
}
export {
    baseUrl,
    changePasswordUrl,
    testResultUrl,
    inspectionUrl,
    inputResultUrl,
    scmBaseUrl,
    iconfontUrl,
    iconfontVersion,
    codeUrl,
    env,
    slPortalUrlEn,
    slPortalUrlCn,
    currentEnv
}
