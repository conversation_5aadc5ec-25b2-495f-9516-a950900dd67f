<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.testpackageManagement')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.testpackageManagement')}}</h1>
        <el-card shadow="never" class="box-card">
            <el-row>
                <el-form label-position="left" :inline="true" :model="form" size="medium">
                    <el-form-item>
                        <el-select clearable filterable v-model="query.productLineCode"
                                :placeholder="$t('productLine.name')">
                            <el-option v-for="item in productLineOptions" :key="item.id" :label="item.productLineName"
                                    :value="item.productLineCode"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select clearable filterable v-model="query.customerGroupCode"
                                :placeholder="$t('customerGroup.name')">
                            <el-option v-for="item in customerGroupOptions" :key="item.id" :label="item.customerGroupName"
                                    :value="item.customerGroupCode"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-input clearable v-model="query.testPackageName"
                                :placeholder="$t('testpackage.name')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchReset" v-loading.fullscreen.lock="fullscreenLoading">
                            {{$t('operation.search')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <el-row align="right" style="margin: 5px 0px">
                    <el-button type="primary" icon="el-icon-circle-plus-outline" size="medium" @click="handlerAdd">
                        {{$t('operation.add')}}
                    </el-button>
                </el-row>
                <el-table
                        ref="testPackageTable"
                        :data="tableData"
                        style="width: 100%"
                        v-loading="loading"
                        row-key="id"
                        :element-loading-text="$t('loading')" :empty-text="$t('NoData')">
                    <el-table-column label="#"
                                    type="index">
                    </el-table-column>
                    <el-table-column
                            prop="productLineName"
                            :label="$t('term.productLine')"
                            width="260">
                    </el-table-column>
                    <el-table-column
                            prop="customerGroupName"
                            :label="$t('term.customerGroup')"
                            width="260">
                    </el-table-column>
                  <el-table-column
                      prop="customerName"
                      :label="$t('term.customer')"
                      width="260">
                  </el-table-column>
                    <el-table-column
                            prop="testPackageName"
                            :label="$t('testpackage.name')">
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="200"
                            align="center">
                        <template slot-scope="scope">
                            <el-button type="text"
                                    @click="handleEdit(scope.row)">
                                {{$t('operation.edit')}}
                            </el-button>
                            <el-button type="text"
                                    @click="handleDelete(scope.row)">
                                {{$t('operation.remove')}}
                            </el-button>
                            <el-button  @click="copy(scope.row)" type="text">
                                {{$t('operation.copy')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page.sync="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
        <el-dialog :title="$t('testpackage.titleAdd')" :close-on-click-modal="false" :visible.sync="dialogFormVisible" width="80%" size="80%" v-if="dialogFormVisible">
            <el-card class="box-card">
                <el-form :model="form" label-position="left" ref="testPackageForm" :rules="testPackageRules"
                         label-width="200px" size="medium" class="sgs-form">
                    <el-form-item :label="$t('term.productLine')" prop="productLineCode">
                        <el-select filterable v-model="form.productLineCode" clearable style="width: 100%"
                                   @change="selectProductLine" :disabled="productLineSelect" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                            <el-option v-for="item in productLineOptions" :key="item.productLineId"
                                       :label="item.productLineName"
                                       :value="item.productLineCode"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('term.customerGroupOrCustomer')" prop="customerDataCode">
                      <el-select v-model="form.customerModel" @change="customerSelectModelChange" :disabled="customerGroupSelect" style="width:30%">
                        <el-option v-for="(model) in $t('customer.selectModel')"
                                   :label="model.label"
                                   :value="model.value"></el-option>
                      </el-select>
                        <el-select filterable v-model="form.customerDataCode" v-if="form.customerModel=='1'" clearable style="width: 100%"
                                   @change="selectCustomerGroup" :disabled="customerGroupSelect" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                            <el-option v-for="item in customerGroupOptions" :key="item.customerGroupId"
                                       :label="item.customerGroupName"
                                       :value="item.customerGroupCode"></el-option>
                        </el-select>
                        <el-select clearable filterable remote :remote-method="queryCustomerList" v-model="form.customerDataCode" v-if="form.customerModel=='2'" style="width:100%"
                                   @change="customerChange" :disabled="customerGroupSelect" :placeholder="$t('operation.pleaseEnterContent')" :no-data-text="$t('NoData')">
                          <el-option v-for="(customer) in customerData"
                                     :key = "customer.customerId"
                                     :label="customer.customerName"
                                     :value="customer.customerId">
                            <span style="float: left">{{ customer.customerNameEn }}</span>
                            <span style="float: right;">{{ customer.customerNameCn }}</span>
                          </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('testpackage.name')" prop="testPackageName">
                        <el-input v-model="form.testPackageName"
                                  clearable
                                  autocomplete="off"
                                  maxlength="200"></el-input>
                    </el-form-item>

                    <testpackage :treeData.sync="treeData" :deleteIds.sync="deleteIds"></testpackage>

                    <div class="sgs-bottom">
                        <el-button @click="dialogFormVisible = false" :disabled="isDisabled">
                            {{$t('operation.cancel')}}
                        </el-button>
                        <el-button type="primary" @click="submitForm()" :disabled="isDisabled">
                            {{$t('operation.confirm')}}
                        </el-button>
                    </div>
                </el-form>

            </el-card>
        </el-dialog>
        <test-package-dialog :visible.sync="selectParentDialogVisible"
                             :testpackage.sync="form"
                             @selection="handleSelectParentPackage">
        </test-package-dialog>
    </basic-container>
</template>

<script>
    import {add, getPage, remove, getTestPackage} from "@/api/lab/test-package";
    import {getProductLine, getCustomerGroup} from "@/api/common";
    import {searchCustomerAndGroup} from "@/api/customer/customer";

    export default {
        components: {
            Testpackage: resolve => require(['./testpackage'], resolve),
            TestPackageDialog: resolve => require(['../../../components/dialog/test-package-dialog'], resolve)
        },
        data() {
            return {
                name: "list",
                dialogFormVisible: false,
                selectParentDialogVisible: false,
                isDisabled: false,
                actionType:'',
                form: {
                    testPackageName: '',
                    productLineId: '',
                    parentId: '',
                    customerDataCode:''
                },
                query: {
                    testPackageName: '',
                },
                sort: {descs: 'update_time'},
                tableData: [],
                treeData: [],
                deleteIds: [],
                selectionList: [],
                selectRow: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                maps: new Map(),
                productLineOptions: [],
                customerGroupOptions: [],
                customerData:[],
                productLineSelect: false,
                customerGroupSelect: false,
                loading: true,
                fullscreenLoading: false,
                testPackageRules: {
                    productLineCode: [
                        {required: true, message: this.$t('template.selProductLine')},
                    ],
                    customerGroupCode: [
                        {required: true, message: this.$t('template.selCustomerGroup')},
                    ],
                    customerDataCode:[
                        {required: true, message: this.$t('template.selCustomerOrGroup')},
                    ],
                    testPackageName: [
                        {required: true, message: this.$t('testpackage.inputTestPackageName')}
                    ]
                },
            }
        },
        created() {
            getProductLine().then(res => {
                this.productLineOptions = res.data.data;
            });
            getCustomerGroup().then(res => {
                this.customerGroupOptions = res.data.data;
            });
            this.onLoad(this.page);
        },
        methods: {
            searchReset() {
                this.fullscreenLoading = true;
                this.onLoad(this.page);
            },
            submitForm() {
                this.$set(this.form,'actionType',this.actionType);
                this.fullscreenLoading = true;
                this.form.testPackageTrees = this.treeData;
                this.form.deleteIds = this.deleteIds;
                this.isDisabled = true;
                this.$refs['testPackageForm'].validate((valid) => {
                    if (valid) {
                        add(this.form).then(() => {
                            this.isDisabled = false;
                            this.dialogFormVisible = false;
                            this.fullscreenLoading = false;

                            this.onLoad(this.page);

                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                        }, error => {
                            this.isDisabled = false;
                            this.fullscreenLoading = false;
                            console.log(error);
                        });
                    } else {
                        this.isDisabled = false;
                        this.fullscreenLoading = false;
                    }
                });

            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            handlerAdd() {
                this.actionType='';
                this.form = {};
                this.$set(this.form,'customerModel','1');
                this.customerGroupSelect = false;
                this.productLineSelect = false;
                this.dialogFormVisible = true;
                this.treeData = [];
            },
            handleEdit(row) {
                this.actionType='';
                this.form = row;
                this.selectRow = row;
                this.customerGroupSelect = true;
                this.productLineSelect = true;
                getTestPackage(this.form.id).then(res => {
                  debugger
                    this.form = res.data.data;
                    this.form.customerBossNo? this.$set(this.form,'customerModel','2'): this.$set(this.form,'customerModel','1');
                    this.form.customerDataCode = this.form.customerBossNo?this.form.customerName:this.form.customerGroupName
                    this.treeData = res.data.data.testPackageTrees;
                    this.dialogFormVisible = true;
                });
            },
            copy(row){
                this.actionType="copy"
                this.form = row;
                this.selectRow = row;
                this.customerGroupSelect = false;
                this.productLineSelect = false;
                getTestPackage(this.form.id).then(res => {
                    this.form = res.data.data;
                    this.$set(this.form,'testPackageName','');
                    this.$set(this.form,'customerDataCode','');
                    this.$set(this.form,'customerGroupName','');
                    this.$set(this.form,'customerGroupCode','');
                    this.$set(this.form,'customerBossNo','');
                    this.$set(this.form,'customerName','');
                    this.$set(this.form,'customerModel','1')
                    this.treeData = res.data.data.testPackageTrees;
                    this.dialogFormVisible = true;
                });
            },
            handleDelete(row) {
                this.selectRow = row;
                this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: "warning"
                }).then(() => {
                    remove(row.id).then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        const {parentId} = this.selectRow;
                        const {tree, treeNode, resolve} = this.maps.get(parentId);
                        this.$set(this.$refs['testPackageTable'].store.states.lazyTreeNodeMap, parentId, []);
                        this.loadTree(tree, treeNode, resolve);
                    }, error => {
                        console.log(error);
                    });
                })
                    .then(() => {
                        this.onLoad(this.page);
                    });

            },
            onLoad(page, params = {}) {
                params = {parentId: -1}
                // if((JSON.stringify(this.query) == "{}")){
                //     params = {parentId : -1}
                // }
                getPage(page.currentPage, page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
                    this.loading = false;
                    this.fullscreenLoading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            selectProductLine(label) {
                console.log(label);
                let productLine = {};
                productLine = this.productLineOptions.find((item) => {//这里的userRoleList就是上面遍历的数据源
                    return item.productLineCode === label;//筛选出匹配数据
                });
                this.form.productLineName = productLine.productLineName;
            },
            queryCustomerList(customerName){
              if(customerName&&customerName.length<2){
                return false;
              }
              const param = {
                page:1,
                rows:200,
                displayType:'account',
                customerNameCn:customerName
              };
              console.log(param)
              searchCustomerAndGroup(param).then(res => {
                console.log(res.data.data);
                const data = res.data.data;
                this.customerData =data;
              });
            },
            customerChange(val){
              let obj = {};
              obj = this.customerData.find((item) => {
                return item.customerId === val;
              });
              this.$set(this.form,'customerBossNo',obj.number);
              this.$set(this.form,'customerName',obj.customerNameEn);
            },
            selectCustomerGroup(label) {
                let customerGroup = {};
                customerGroup = this.customerGroupOptions.find((item) => {//这里的userRoleList就是上面遍历的数据源
                    return item.customerGroupCode === label;//筛选出匹配数据
                });
                this.$set(this.form,'customerGroupCode',label);
                this.$set(this.form,'customerGroupName',customerGroup.customerGroupName);
            },
          customerSelectModelChange(){
            this.$set(this.form,'customerDataCode','');
            this.$set(this.form,'customerGroupName','');
            this.$set(this.form,'customerGroupCode','');
            this.$set(this.form,'customerBossNo','');
            this.$set(this.form,'customerName','');
          },
        }
    }
</script>

<style scoped>
</style>
