<template>
    <div id="DatePic" class="DatePic">
        <el-date-picker
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="dataValue"
                ref="date_range_diy"
                type="daterange"
                :clearable="getClearable"
                :range-separator="allOptions.rules"
                :unlink-panels="false"
                :picker-options="pickerOptions"
        >
        </el-date-picker>
    </div>
</template>

<script>
    import moment from "moment";

    export default {
        name: "DatePic",
        data() {
            return {
                dataValue: [],
                options:{
                    "maxRange":6
                },
                pickerOptions: {
                    shortcuts: [{
                        text: this.getPickerText('lastWeek'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.getPickerText('lastMonth'),
                        onClick(picker) {
                            const end = new Date();
                            const start = moment(new Date(new Date().setMonth(new Date().getMonth() - 1))).toDate();
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.getPickerText('lastHalfYear'),
                        onClick(picker) {
                            const end = new Date();
                            const start = moment(new Date(new Date().setMonth(new Date().getMonth() - 6))).toDate();
                            picker.$emit('pick', [start, end]);
                        }
                    },{
                        text: this.getPickerText('lastYear'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setFullYear(start.getFullYear() - 1);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                }
            }
        },
        methods: {
            initSearchDate(dateRange = 30){
                let currentDate = new Date();
                let m = currentDate.getMonth() + 1;
                let finish = currentDate.getFullYear() + "-" + (m < 10 ? ("0" + m) : m) + "-" + currentDate.getDate();

                let preDate = new Date(currentDate.setDate(currentDate.getDate() - dateRange));
                let mm = preDate.getMonth() + 1;
                let start = preDate.getFullYear() + "-" + (mm < 10 ? ("0" + mm) : mm) + "-" + preDate.getDate();
                return [start, finish]
            },
            getPickerText(val){
                if('lastWeek'==val){
                    return this.$t('datePicker.lastWeek')
                }
                if('lastMonth'==val){
                    return this.$t('datePicker.lastMonth')
                }
                if('lastHalfYear'==val){
                    return this.$t('datePicker.lastHalfYear')
                }
                if('lastYear'==val){
                    return this.$t('datePicker.lastYear')
                }
            },
        },
        created() {
            //console.log("1 初始化日期")
            //this.dataValue = this.initSearchDate(30);
            if(!this.allOptions.options){
                this.options = Object.assign({},this.allOptions,{options:{}});
            }else {
                this.options = Object.assign({},this.allOptions);
            }
        },
        mounted() {
        },
        components: {},
        watch:{
            /*dataValue: {
                deep: true,
                immediate: true,
                handler(newV, oldV) {
                    //console.log("date pic",newV)
                    this.$emit("update:value", newV ? newV.join(this.allOptions.rules) : null)
                }
            },*/
            dataValue: {
                deep: true,
                immediate: true,
                handler(newV, oldV) {
                    console.log("date newV",newV)
                    let obj = {
                        condition: 'in',
                        conditionValue:  newV
                    }
                    this.$emit("update:value", obj)
                }
            },
            formObj:{
                deep:true,
                handler(newV,oldV){
                    if(newV){
                        let {conditionValue} = newV;
                        this.$set(this,'dataValue',conditionValue)
                    }
                }
            }
        },
        computed:{
            getClearable:{
                get(){
                    let cl = false;
                    try{
                        let op = JSON.parse(this.options.options)
                        cl = op.clearable;
                    }catch (e) {
                        //console.log(e);
                    }
                    return cl;
                },
                set(){}
            }
        },
        props: {
            value: null,
            allOptions:{},
            formObj:{}
        },
        render(createElement, context) {

        }
    }
</script>

<style scoped>
    .DatePic {
    }
</style>