<template>
    <div style="width: 100%;display: flex;justify-content: center;">
        <div class="pageCreate">
            <el-row>
                <div style="width: 98.5%;" class="top-card">
                    <div>
                        <el-button link size="default" icon="ArrowLeftBold" @click="handleBack" style="width: 50px;">{{
                            $t('operation.back') }}</el-button>
                    </div>
                    <div><el-button type="primary" size="default" style="border-radius: 3px;" @click="handleSave"><img
                                src="/pageCreate/save.svg" style="width: 14px; height: 14px; margin-right: 5px;" />{{
                                    $t('work.pageCreate.save') }}</el-button>
                        <el-button type="primary" size="default" style="border-radius: 3px;" @click="handlePublish"><img
                                src="/pageCreate/publish.svg" style="width: 14px; height: 14px; margin-right: 5px;" />{{
                                    $t('work.pageCreate.publish') }}</el-button>
                        <el-button class="show-btn" size="default" style="border-radius: 3px;border: 1px solid #D2D2D2;"
                            @click="handlePreview"><img src="/pageCreate/show.svg"
                                style="width: 14px; height: 14px; margin-right: 5px;" />{{ $t('work.pageCreate.browse')
                                }}</el-button>
                    </div>


                    <!-- <div class="top-button" @click="handleSave"><el-icon><DocumentAdd /></el-icon>{{ $t('work.pageCreate.save') }}</div>
            <div class="top-button" @click="handlePublish"><el-icon><DocumentChecked /></el-icon>{{ $t('work.pageCreate.publish') }}</div>
            <div class="top-button">{{ $t('work.pageCreate.browse') }}</div> -->


                </div>

            </el-row>

            <el-row :gutter="24" style="width: 100%;">
                <el-col :span="17">
                    <LinkForm :form="formBase" v-show="formBase.contentType == 'url'" ref="linkFormRef" />
                    <ImageForm :form="formBase" v-show="formBase.contentType == 'image'" ref="imageFormRef" />
                    <VideoForm :form="formBase" v-show="formBase.contentType == 'file'" ref="videoFormRef" />
                    <ArticleForm :form="formBase" v-show="formBase.contentType == 'txt'" ref="articleFormRef" />
                </el-col>
                <el-col :span="7" class="right-con">
                    <el-card class="right-card">
                        <el-tabs v-model="activeName" class="demo-tabs" size="small">
                            <el-tab-pane :label="$t('work.pageCreate.baseProp')" name="base">
                                <el-form :model="formBase" label-position="top">
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.author') + ':'" style="width: 100%">
                                            <el-input disabled v-model="formBase.author"
                                                :placeholder="$t('work.pageCreate.enterAuthor')"></el-input>
                                        </el-form-item>
                                    </el-row>
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.editor') + ':'" style="width: 100%">
                                            <el-input disabled v-model="formBase.editor"
                                                :placeholder="$t('work.pageCreate.enterEditor')"></el-input>
                                        </el-form-item>
                                    </el-row>
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.category') + ':'" style="width: 100%">
                                            <el-tree-select v-model="formBase.categoryId"
                                                :placeholder="$t('work.pageCreate.enterCategory')"
                                                :data="navigationBarTreeList" :props="{
                                                    label: 'categoryName',
                                                    children: 'children',
                                                    value: 'id'
                                                }" @change="handleCategoryChange" check-strictly default-expand-all>
                                                <template #default="{ data }">
                                                    <span>{{ data.categoryName }}</span>
                                                </template>
                                            </el-tree-select>
                                        </el-form-item>
                                    </el-row>
                                    <!-- <el-row>
                <el-form-item label="所属内容:">
                    <el-input v-model="formBase.content" disabled></el-input>
                </el-form-item>
               </el-row> -->
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.coverImage')">
                                            <el-upload class="cover-uploader" :show-file-list="false"
                                                :on-success="handleCoverSuccess" :before-upload="beforeCoverUpload"
                                                :http-request="customUpload">
                                                <img v-if="formBase.coverPlan" :src="formBase.coverPlan"
                                                    class="cover-image" />
                                                <el-icon v-else class="cover-uploader-icon">
                                                    <Plus />
                                                </el-icon>
                                            </el-upload>
                                        </el-form-item>
                                    </el-row>
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.contentType') + ':'"
                                            style="width: 100%;">
                                            <el-radio-group v-model="formBase.contentType" style="width: 100%;">

                                                <el-radio label="txt">{{ $t('work.pageCreate.articleType') }}</el-radio>

                                                <el-radio label="image">{{ $t('work.pageCreate.imageType') }}</el-radio>

                                                <el-radio label="file">{{ $t('work.pageCreate.fileType') }}</el-radio>
                                                <el-radio label="url">{{ $t('work.pageCreate.linkType') }}</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-row>
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.tags') + '：'" style="width: 100%">
                                            <el-select v-model="selectedTags" multiple filterable clearable
                                                :placeholder="$t('work.pageCreate.selectTags')">
                                                <template #label="{ label, value }">

                                                    <span>{{ getTagName(value, label) }}</span>
                                                </template>
                                                <el-option v-for="item in tagList" :key="item.id"
                                                    :label="item.tagNameCn" :value="item.id">

                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-row>
                                    <el-row>
                                        <el-form-item :label="$t('work.pageCreate.summary') + '：'"
                                            style="width: 100%;margin-bottom: 0;">
                                            <el-input :placeholder="$t('work.pageCreate.enterSummary')" :rows="rows"
                                                v-model="formBase.description" type="textarea" maxlength="500"
                                                show-word-limit></el-input>
                                        </el-form-item>
                                    </el-row>

                                </el-form>
                            </el-tab-pane>
                            <el-tab-pane :label="$t('work.pageCreate.multiDimensionManage')" name="multi">
                                <el-form v-show="modelList.length > 0" style="overflow-x: auto;" :model="formBase"
                                    ref="multiFormRef" :rules="multiFormRules" label-position="top">
                                    <el-row v-for="model in modelList" :key="model.id">
                                        <!-- 文本输入框 -->
                                        <el-form-item style="width: 100%;" :label="model.modelNameCn"
                                            :prop="`knowledgeModelRelationDTOList[${modelList.indexOf(model)}].modelValue`"
                                            v-if="model.modelType === 'txt'">
                                            <el-input v-model="model.modelValue" :maxlength="model.modelLength"
                                                show-word-limit @input="validateField(model)"
                                                @blur="validateField(model)" />
                                        </el-form-item>

                                        <!-- 日期选择器 -->
                                        <el-form-item style="width: 100%;" :label="model.modelNameCn"
                                            :prop="`knowledgeModelRelationDTOList[${modelList.indexOf(model)}].modelValue`"
                                            v-if="model.modelType === 'date'">
                                            <el-date-picker style="width: 100%;" v-model="model.modelValue" type="date"
                                                format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                                @change="validateField(model)" />
                                        </el-form-item>

                                        <!-- 文件上传 -->
                                        <el-form-item :label="model.modelNameCn"
                                            :prop="`knowledgeModelRelationDTOList[${modelList.indexOf(model)}].modelValue`"
                                            v-if="model.modelType === 'file'" style="width: 100%">
                                            <!-- Display existing files if available -->
                                            <div v-if="model.modelValue" class="existing-files">
                                                <div v-for="(fileId, index) in model.modelValue.split(',')" :key="index"
                                                    class="file-item">
                                                    <span class="file-name" @click="openFile(fileId)">{{
                                                        getFileName(fileId) }}</span>
                                                    <!-- <el-button 
                   type="danger" 
                   size="small" 
                   @click="removeFile(model, fileId)"
                   class="remove-file-btn"
                 >
                   删除
                 </el-button> -->
                                                    <el-icon class="remove-file-btn" @click="removeFile(model, fileId)">
                                                        <CircleCloseFilled />
                                                    </el-icon>
                                                </div>
                                            </div>
                                            <el-upload :show-file-list="false"
                                                :on-success="(response: any) => handleFileUploadSuccess(response, model)"
                                                :before-upload="beforeFileUpload"
                                                :http-request="customUploadAttachment">
                                                <el-button style="border-radius: 0;" type="primary">{{
                                                    $t('work.pageCreate.clickUpload')
                                                }}</el-button>
                                            </el-upload>
                                        </el-form-item>

                                        <!-- 下拉选择 -->
                                        <el-form-item style="width: 100%" :label="model.modelNameCn"
                                            :prop="`knowledgeModelRelationDTOList[${modelList.indexOf(model)}].modelValue`"
                                            v-if="model.modelType === 'pullTochose'">
                                            <el-select v-model="model.modelValue" @change="validateField(model)">
                                                <el-option v-for="item in model.predefinedValList" :key="item"
                                                    :label="item" :value="item" />
                                            </el-select>
                                        </el-form-item>

                                        <!-- URL输入框 -->
                                        <el-form-item style="width: 100%;" :label="model.modelNameCn"
                                            :prop="`knowledgeModelRelationDTOList[${modelList.indexOf(model)}].modelValue`"
                                            v-if="model.modelType === 'url'">
                                            <el-input v-model="model.modelValue" @input="validateField(model)"
                                                @blur="validateField(model)">
                                                <template #append>
                                                    <el-button style="border-radius: 0;"
                                                        @click="openUrl(model.modelValue)">{{
                                                            $t('work.pageCreate.visit') }}</el-button>
                                                </template>
                                            </el-input>
                                        </el-form-item>
                                    </el-row>
                                </el-form>
                                <div v-show="modelList.length == 0"
                                    style="display: flex; justify-content: center; align-items: center; width: 100%;height: 600px;">
                                    <img src="/detailedArticle/no-data.svg" />
                                </div>

                            </el-tab-pane>

                        </el-tabs>
                    </el-card>

                </el-col>
            </el-row>

        </div>
    </div>

</template>
<script lang="ts" setup>
//import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const categoryId = route.query.categoryId
//const isDis=ref(categoryId?true:false)
let id = route.query.id
import { ref, watch, nextTick, computed } from 'vue'

import { ElMessage } from 'element-plus'
import { uploadFile, uploadFileAttachment } from '@/api/upload'
import { tagManagementList } from '@/api/tagManagement/tagManagement'
import LinkForm from './linkForm.vue'
import ImageForm from './imageForm.vue'
import VideoForm from './videoForm.vue'
import ArticleForm from './articleForm.vue'
import { knowledgeModelGetlist } from '@/api/knowledgeModel/knowledgeModel'
import { navigationBarTree } from '@/api/navigationBar/navigationBar'
import { pageCreateSave, pageCreatePublish, pageCreatePreview } from '@/api/pageCreate/pageCreate'
import { knowledgeDetailsGet } from '@/api/detailedArticle/detailedArticle'
import { getCloudFileURL } from '@/api/getCloudFileUrl.ts'
import { useI18n } from 'vue-i18n'
//import { tagManagementPageinfo } from '@/api/tagManagement/tagManagement'


const userInfo = ref(JSON.parse(localStorage.getItem('SGS-userInfo') || '{}'))

const { t, locale } = useI18n()
const rows = computed(() => {
    if (locale.value == 'zh-CN') {
        return 21
    } else if (locale.value == 'en-US') {
        return 21
    }
})


interface ModelItem {
    id: string,
    // modelId: string,
    modelNameCn: string
    modelType: string
    modelLength: number
    requiredFlag: number
    predefinedValList?: string[]
    modelValue?: string
    [key: string]: any
}
interface attachmentItem {
    id: number,
    createUser: string,
    createDept: number,
    createTime: string,
    updateUser: string,
    updateTime: string,
    status: number,
    isDeleted: number,
    tenantId: string,
    knowledgeId: number,
    fileName: string,
    fileId: string,
    cloudId: string,
    fileType: string,
    viewType: string,
    downloadCount: string
}
interface ExtItem {
    id: number,
    extTitle: string,
    extAbstract: string,
    extCover: string,
    sortKey: number,
    [key: string]: any
}
interface UploadResponse {
    data?: Array<{
        cloudID: string
        [key: string]: any
    }>
}

// interface FileInfo {
//     id: string;
//     name: string;
// }

const activeName = ref('base')
// const form =ref({
//     title:'',
//     link:'',
//     content:'',
//     imageList:[{
//         title:'',
//         summary:'',
//         src:'/img/logo.png'
//     },{
//         title:'',
//         summary:'',
//         src:'/img/logo.png'
//     }]
// })
const formBase = ref({
    title: '',
    link: '',
    linkFormLink: [],
    videoFormLink: [],
    author: userInfo.value.content.userName,
    editor: userInfo.value.content.userName,
    categoryId: categoryId,
    content: '',
    linkFormContent: '',
    videoFormContent: '',
    articleFormContent: '',
    contentType: 'txt',
    coverPlan: '',
    knowledgeTagRelationDTOList: [] as TagItem[],
    description: '',
    knowledgeModelRelationDTOList: [] as ModelItem[],
    knowledgeExtDTOList: [] as ExtItem[],
    knowledgeAttachmentDTOList: [] as attachmentItem[]
})

// Add a flag to control when the watcher should be active
const isInitialDataLoading = ref(true)

// Add a map to store file information
const fileInfoMap = ref<Map<string, string>>(new Map())

// Function to get file name from ID
const getFileName = (fileId: string): string => {
    return fileInfoMap.value.get(fileId) || fileId
}
const handleCategoryChange = (e: any) => {

    const getModelList = async () => {
        const res = await knowledgeModelGetlist({ categoryId: e })
        modelList.value = res.data.map((item: any) => ({
            ...item,
            modelValue: ''
        }))
        // 初始化表单验证规则
        initFormRules()
    }
    if (e) {
        getModelList()
    }

}
// Add watcher for contentType
// watch(() => formBase.value.contentType, (newType) => {
//    // debugger
//     // Skip the watcher logic during initial data loading
//     //if (isInitialDataLoading.value) return

//     // Clear content and link when content type changes
//    // formBase.value.content = ''
//     formBase.value.link = ''

//     // Clear specific content based on content type
//     switch(newType) {
//         case 'txt': // Article
//            // formBase.value.content = ''
//             break
//         case 'image': // Image
//            //formBase.value.knowledgeExtDTOList = []
//             break
//         case 'file': // Video
//            // formBase.value.content = ''
//             formBase.value.link = ''
//             break
//         case 'url': // Link
//            // formBase.value.content = ''
//             formBase.value.link = ''
//             break
//     }
// })
interface TagItem {
    id: number | string,
    tagId: number | string,
    tagNameCn: string
    [key: string]: any
}
const tagList = ref<TagItem[]>([])
const tagListAll = ref<TagItem[]>([])
const getTagList = async () => {
    const res = await tagManagementList({ current: 1, size: 10000 })
    tagList.value = res.data.records.filter((item: TagItem) => item.status == 1)
    tagListAll.value = res.data.records
}
getTagList()
const getTagName = (id: number, label: string) => {

    const tag = tagList.value.find((tag: TagItem) => tag.id === id)
    if (tag) {
        return tag.tagNameCn
    } else {
        const tagAll = tagListAll.value.find((tag: TagItem) => tag.id === id)
        if (tagAll) {
            return tagAll.tagNameCn
        } else {
            return label
        }
    }


}
if (id) {
    const getPageCreate = async () => {
        try {
            // Set flag to true before loading data
            isInitialDataLoading.value = false

            const res: any = await knowledgeDetailsGet({ id, categoryId })

            if (res.code !== 200 || !res.data?.knowledgeVO) {
                ElMessage.error(t('work.pageCreate.getPageDataFailed'))
                isInitialDataLoading.value = false
                return
            }

            const { knowledgeVO } = res.data

            // Extract nested lists while creating a clean copy of the main object
            const {
                knowledgeModelRelationList = [],
                knowledgeExtList = [],
                knowledgeAttachmentList = [],
                knowledgeTagRelationList = [],
                ...mainData
            } = knowledgeVO

            // Update form with all data
            formBase.value = {
                ...mainData,
                knowledgeModelRelationDTOList: knowledgeModelRelationList,
                knowledgeExtDTOList: knowledgeExtList,
                knowledgeAttachmentDTOList: knowledgeAttachmentList,
                knowledgeTagRelationDTOList: knowledgeTagRelationList,
                content: knowledgeVO.content ? decodeURIComponent(knowledgeVO.content) : '',


                author: knowledgeVO.author ? knowledgeVO.author : userInfo.value.content.userName,
                editor: userInfo.value.content.userName,
                categoryId: knowledgeVO.categoryId + ''
            }

            if (knowledgeVO.contentType == 'url') {
                formBase.value.linkFormContent = knowledgeVO.content ? decodeURIComponent(knowledgeVO.content) : ''
                formBase.value.videoFormContent = ''
                formBase.value.articleFormContent = ''
                formBase.value.linkFormLink = knowledgeVO.fileLink ? knowledgeVO.fileLink.split(',') : []
                formBase.value.videoFormLink = []
            }
            else if (knowledgeVO.contentType == 'file') {
                formBase.value.linkFormContent = ''
                formBase.value.videoFormContent = knowledgeVO.content ? decodeURIComponent(knowledgeVO.content) : ''
                formBase.value.articleFormContent = ''
                formBase.value.videoFormLink = knowledgeVO.fileLink ? knowledgeVO.fileLink.split(',') : []
                formBase.value.linkFormLink = []
            } else if (knowledgeVO.contentType == 'image') {
                formBase.value.linkFormContent = ''
                formBase.value.videoFormContent = ''
                formBase.value.articleFormContent = ''
                formBase.value.linkFormLink = []
                formBase.value.videoFormLink = []
            } else if (knowledgeVO.contentType == 'txt') {
                formBase.value.linkFormContent = ''
                formBase.value.videoFormContent = ''
                formBase.value.articleFormContent = knowledgeVO.content ? decodeURIComponent(knowledgeVO.content) : ''
                formBase.value.linkFormLink = []
                formBase.value.videoFormLink = []
            }
            modelList.value = knowledgeModelRelationList

            // Populate fileInfoMap with attachment information
            if (knowledgeAttachmentList && knowledgeAttachmentList.length > 0) {
                knowledgeAttachmentList.forEach((attachment: attachmentItem) => {
                    if (attachment.cloudId && attachment.fileName) {
                        fileInfoMap.value.set(attachment.cloudId, attachment.fileName)
                    }
                })
            }

            // Also check for file IDs in model values
            if (knowledgeModelRelationList && knowledgeModelRelationList.length > 0) {
                knowledgeModelRelationList.forEach((model: ModelItem) => {
                    if (model.modelType === 'file' && model.modelValue) {
                        // For each file ID that doesn't have a name in the map yet,
                        // set a default name

                        model.modelValue.split(',').forEach((fileId: string) => {
                            if (!fileInfoMap.value.has(fileId)) {
                                fileInfoMap.value.set(fileId, `${fileId.substring(fileId.lastIndexOf('/') + 1)}`)
                            }
                        })
                    }
                })
            }
            knowledgeVO.knowledgeTagEntityList.forEach((item: any) => {

                if (!tagListAll.value.find((tag: TagItem) => tag.id == item.id)) {
                    tagListAll.value.push(item)
                }
            })
            // Update selected tags based on loaded data
            selectedTags.value = knowledgeTagRelationList.map((tag: TagItem) => tag.tagId || tag.id)
            console.log(formBase.value)

            // Reset flag after data is loaded
            isInitialDataLoading.value = true
        } catch (error) {
            console.error('加载页面数据失败:', error)
            ElMessage.error(t('work.pageCreate.loadPageDataFailed'))
            isInitialDataLoading.value = false
        }
    }
    getPageCreate()
} else {
    isInitialDataLoading.value = true
}
const navigationBarTreeList = ref<any[]>([])
const getNavigationBarTreeList = async () => {
    const res: any = await navigationBarTree()
    navigationBarTreeList.value = res.data.records
    if (res && res.code === 200) {
        // 将结果传递给父组件进行更新


        // 兼容不同的返回数据结构
        if (res.data) {
            if (Array.isArray(res.data)) {
                navigationBarTreeList.value = res.data.filter((item: any) => item.categoryName != '法律法规')
                // total = res.data.length
            } else {
                navigationBarTreeList.value = res.data.records || res.data.list || []
                //total = res.data.total || list.length || 0
            }
        }

    } else {
        console.error('API响应错误:', res)
    }
}
getNavigationBarTreeList()

const selectedTags = ref<number[]>([])
const modelList = ref<ModelItem[]>([])
if (id) {

} else {

    const getModelList = async () => {
        const res = await knowledgeModelGetlist({ categoryId })
        modelList.value = res.data.map((item: any) => ({
            ...item,
            modelValue: ''
        }))
        // 初始化表单验证规则
        initFormRules()
    }
    if (categoryId) {
        getModelList()
    }

}

// Watch for changes in selectedTags and update knowledgeTagRelationDTOList
watch(selectedTags, (newVal) => {
    // Find the full tag objects that match the selected IDs
    formBase.value.knowledgeTagRelationDTOList = tagListAll.value.filter(tag =>
        newVal.includes(tag.id as number)
    ).map(tag => ({
        ...tag,
        tagId: tag.id,
        tagNameCn: tag.tagNameCn
    }))
    debugger
})
const openFile = (fileId: string) => {
    getCloudFileURL({ cloudID: fileId, systemID: 1, networkType: 2 }).then((res: any) => {
        window.open(res)
    })
}

const handleCoverSuccess = (response: any) => {
    if (response?.rows?.length > 0) {
        formBase.value.coverPlan = response.rows[0]
    }
}

const beforeCoverUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
        ElMessage.error(t('work.pageCreate.onlyImageAllowed'))
        return false
    }
    if (!isLt2M) {
        ElMessage.error(t('work.pageCreate.imageSizeLimit'))
        return false
    }
    return true
}
const customUploadAttachment = async (options: any) => {
    try {
        const formData = new FormData()
        formData.append('file', options.file)
        formData.append('systemID', '59')
        const res = await uploadFileAttachment(formData)

        // Store the original file name for later use
        if (res && res.data && res.data.length > 0) {
            (res as any).originalFileName = options.file.name
        }

        options.onSuccess(res)
    } catch (error) {
        options.onError(error)
        ElMessage.error(t('work.pageCreate.uploadFailed'))
    }
}
const customUpload = async (options: any) => {
    try {
        const formData = new FormData()
        formData.append('file', options.file)
        formData.append('systemID', '59')
        const res = await uploadFile(formData)

        options.onSuccess(res)
    } catch (error) {
        options.onError(error)
        ElMessage.error(t('work.pageCreate.uploadFailed'))
    }
}

// 处理文件上传成功
const handleFileSuccess = (response: UploadResponse, model: ModelItem) => {
    if (response?.data && response.data.length > 0 && response.data[0].cloudID) {
        const fileId = response.data[0].cloudID
        // Try to get the file name from various sources
        const fileName = response.data[0].fileName ||
            (response as any).originalFileName ||
            t('work.pageCreate.unnamedFile')

        // Store file information
        fileInfoMap.value.set(fileId, fileName)

        // Update model value
        model.modelValue = model.modelValue ? model.modelValue + ',' + fileId : fileId
    }
}

// 处理文件上传成功并验证字段
const handleFileUploadSuccess = (response: UploadResponse, model: ModelItem) => {
    handleFileSuccess(response, model)
    // 上传成功后验证字段
    validateField(model)
}

// 验证单个字段
const validateField = (model: ModelItem) => {
    if (!multiFormRef.value) return;

    // 确保数据结构正确
    prepareFormData();

    // 获取字段路径
    const index = modelList.value.indexOf(model);
    const fieldPath = `knowledgeModelRelationDTOList[${index}].modelValue`;

    // 如果字段有值且是必填字段，则清除验证错误
    if (model.modelValue && model.requiredFlag === 1) {
        // 使用nextTick确保DOM更新后再清除验证
        nextTick(() => {
            multiFormRef.value.clearValidate(fieldPath);
        });
    } else if (model.requiredFlag === 1) {
        // 如果是必填字段但没有值，重新验证
        nextTick(() => {
            multiFormRef.value.validateField(fieldPath);
        });
    }
}

// 打开URL
const openUrl = (url: string | undefined) => {
    if (url) {
        window.open(url)
    }
}

// 添加表单引用和规则
const multiFormRef = ref()
const multiFormRules = ref<Record<string, any[]>>({})

// 处理表单提交前的数据整理
const prepareFormData = () => {

    // 确保knowledgeModelRelationDTOList是一个数组
    if (!formBase.value.knowledgeModelRelationDTOList) {
        formBase.value.knowledgeModelRelationDTOList = [];
    }

    // 更新数组中的每个元素
    formBase.value.knowledgeModelRelationDTOList = modelList.value.map(model => ({

        ...model,
        modelId: model.id,
        id: '',
        modelValue: model.modelValue || ''
    }));

}

// 初始化表单验证规则
const initFormRules = () => {
    // 先清空规则
    multiFormRules.value = {};

    // 为每种类型的表单项设置适当的验证规则和触发事件
    modelList.value.forEach((model, index) => {
        if (model.requiredFlag === 1) {
            const fieldPath = `knowledgeModelRelationDTOList[${index}].modelValue`;
            let rule = {};

            switch (model.modelType) {
                case 'txt':
                    rule = { required: true, message: `${t('work.pageCreate.pleaseEnter')}${model.modelNameCn}`, trigger: 'blur' };
                    break;
                case 'date':
                    rule = { required: true, message: `${t('work.pageCreate.pleaseSelect')}${model.modelNameCn}`, trigger: 'change' };
                    break;
                case 'file':
                    rule = { required: true, message: `${t('work.pageCreate.pleaseUpload')}${model.modelNameCn}`, trigger: 'change' };
                    break;
                case 'pullTochose':
                    rule = { required: true, message: `${t('work.pageCreate.pleaseSelect')}${model.modelNameCn}`, trigger: 'change' };
                    break;
                case 'url':
                    rule = { required: true, message: `${t('work.pageCreate.pleaseEnter')}${model.modelNameCn}`, trigger: ['blur', 'change'] };
                    break;
                default:
                    rule = { required: true, message: `${t('work.pageCreate.pleaseComplete')}${model.modelNameCn}`, trigger: 'change' };
            }

            // 将规则添加到multiFormRules中
            multiFormRules.value[fieldPath] = [rule];
        }
    });

    // 初始化数据结构
    prepareFormData();
}

// Save button click handler
const handleSave = async () => {
    // First prepare data, ensuring knowledgeModelRelationDTOList is updated
    prepareFormData()
    if (!formBase.value.categoryId) {
        ElMessage.error(t('work.pageCreate.categoryMessage'))
        return false
    }

    // First validate the content form based on content type
    let contentFormValid = true
    try {
        if (formBase.value.contentType === 'txt' && articleFormRef.value) {
            await articleFormRef.value.validate()
        } else if (formBase.value.contentType === 'url' && linkFormRef.value) {
            await linkFormRef.value.validate()
        } else if (formBase.value.contentType === 'image' && imageFormRef.value) {
            await imageFormRef.value.validate()
        } else if (formBase.value.contentType === 'file' && videoFormRef.value) {
            await videoFormRef.value.validate()
        }
    } catch (error) {
        contentFormValid = false
        ElMessage.warning(t('work.pageCreate.completeRequiredFields'))
        return
    }

    // Then validate the multi-dimensional form if needed
    if (!contentFormValid) {
        return
    }

    // Use el-form's validate method for validation
    if (multiFormRef.value) {
        try {
            await multiFormRef.value.validate()
            // Validation passed, call save API
            let res: any
            let param = JSON.parse(JSON.stringify(formBase.value))
            if (param.contentType == 'txt') {
                param.content = encodeURIComponent(param.articleFormContent)
                param.articleFormContent = ''
                param.fileLink = ''
            } else if (param.contentType == 'url') {
                // For URL type, store the direct link in content (not encoded)
                param.content = param.linkFormContent
                param.fileLink = param.linkFormLink.join(',')
            } else if (param.contentType == 'file') {
                param.content = encodeURIComponent(param.videoFormContent)
                param.fileLink = param.videoFormLink.join(',')
            } else if (param.contentType == 'image') {
                param.content = ''
                param.fileLink = ''
            }

            if (id) {
                param.id = id
                res = await pageCreateSave(param)
            } else {
                param.id = ''
                res = await pageCreateSave(param)
            }
            if (res.code === 200) {
                ElMessage.success(t('work.pageCreate.saveSuccess'))
                id = res.data.id
            } else {
                //ElMessage.error(t('work.pageCreate.saveFailed'))
            }
        } catch (error) {
            // Validation failed, switch to multi-dimensional management tab
            activeName.value = 'multi'
            ElMessage.warning(t('work.pageCreate.completeRequiredFields'))
        }
    } else {
        let param = JSON.parse(JSON.stringify(formBase.value))
        if (param.contentType == 'txt') {
            param.content = encodeURIComponent(param.articleFormContent)
            param.articleFormContent = ''
            param.fileLink = ''
        } else if (param.contentType == 'url') {
            // For URL type, store the direct link in content (not encoded)
            param.content = param.linkFormContent
            param.fileLink = param.linkFormLink.join(',')
        } else if (param.contentType == 'file') {
            param.content = encodeURIComponent(param.videoFormContent)
            param.fileLink = param.videoFormLink.join(',')
        } else if (param.contentType == 'image') {
            param.content = ''
            param.fileLink = ''
        }
        // If form reference doesn't exist, directly call save API
        if (id) {
            param.id = id
        } else {
            param.id = ''
        }
        const res: any = await pageCreateSave(param)
        if (res.code === 200) {
            ElMessage.success(t('work.pageCreate.saveSuccess'))
            id = res.data.id
        } else {
            //ElMessage.error(t('work.pageCreate.saveFailed'))
        }
    }
}

// Publish button click handler
const handlePublish = async () => {
    prepareFormData()
    if (!formBase.value.categoryId) {
        ElMessage.error(t('work.pageCreate.categoryMessage'))
        return false
    }

    // First validate the content form based on content type
    let contentFormValid = true
    try {
        if (formBase.value.contentType === 'txt' && articleFormRef.value) {
            await articleFormRef.value.validate()
        } else if (formBase.value.contentType === 'url' && linkFormRef.value) {
            await linkFormRef.value.validate()
        } else if (formBase.value.contentType === 'image' && imageFormRef.value) {
            await imageFormRef.value.validate()
        } else if (formBase.value.contentType === 'file' && videoFormRef.value) {
            await videoFormRef.value.validate()
        }
    } catch (error) {
        contentFormValid = false
        ElMessage.warning(t('work.pageCreate.completeRequiredFields'))
        return
    }

    // Then validate the multi-dimensional form if needed
    if (!contentFormValid) {
        return
    }

    // Use el-form's validate method for validation
    if (multiFormRef.value) {
        try {
            await multiFormRef.value.validate()
            // Validation passed, call publish API
            let res: any
            let param = JSON.parse(JSON.stringify(formBase.value))
            if (param.contentType == 'txt') {
                param.content = encodeURIComponent(param.articleFormContent)
                param.articleFormContent = ''
                param.fileLink = ''
            } else if (param.contentType == 'url') {
                // For URL type, store the direct link in content (not encoded)
                param.content = param.linkFormContent
                param.fileLink = param.linkFormLink.join(',')
            } else if (param.contentType == 'file') {
                param.content = encodeURIComponent(param.videoFormContent)
                param.fileLink = param.videoFormLink.join(',')
            } else if (param.contentType == 'image') {
                param.content = ''
                param.fileLink = ''
            }
            param.publishFlag = 1
            if (id) {
                param.id = id
                res = await pageCreatePublish(param)
            } else {
                param.id = ''
                res = await pageCreatePublish(param)
            }
            if (res.code === 200) {
                ElMessage.success(t('work.pageCreate.publishSuccess'))
                id = res.data.id
                // Return to previous page after successful publication
                router.back()
            } else {
                ElMessage.error(t('work.pageCreate.publishFailed'))
            }
        } catch (error) {
            // Validation failed, switch to multi-dimensional management tab
            activeName.value = 'multi'
            ElMessage.warning(t('work.pageCreate.completeRequiredFields'))
        }
    } else {
        let param = JSON.parse(JSON.stringify(formBase.value))
        if (param.contentType == 'txt') {
            param.content = encodeURIComponent(param.articleFormContent)
            param.articleFormContent = ''
            param.fileLink = ''
        } else if (param.contentType == 'url') {
            // For URL type, store the direct link in content (not encoded)
            param.content = param.linkFormContent
            param.fileLink = param.linkFormLink.join(',')
        } else if (param.contentType == 'file') {
            param.content = encodeURIComponent(param.videoFormContent)
            param.fileLink = param.videoFormLink.join(',')
        } else if (param.contentType == 'image') {
            param.content = ''
            param.fileLink = ''
        }
        param.publishFlag = 1
        if (id) {
            param.id = id
        } else {
            param.id = ''
        }
        // If form reference doesn't exist, directly call publish API
        const res: any = await pageCreatePublish(param)
        if (res.code === 200) {
            ElMessage.success(t('work.pageCreate.publishSuccess'))
            id = res.data.id
            // Return to previous page after successful publication
            router.back()
        } else {
            ElMessage.error(t('work.pageCreate.publishFailed'))
        }
    }
}
const handlePreview = async () => {
    prepareFormData()
    if (!formBase.value.categoryId) {
        ElMessage.error(t('work.pageCreate.categoryMessage'))
        return
    }

    // First validate the content form based on content type
    let contentFormValid = true
    try {
        if (formBase.value.contentType === 'txt' && articleFormRef.value) {
            await articleFormRef.value.validate()
        } else if (formBase.value.contentType === 'url' && linkFormRef.value) {
            await linkFormRef.value.validate()
        } else if (formBase.value.contentType === 'image' && imageFormRef.value) {
            await imageFormRef.value.validate()
        } else if (formBase.value.contentType === 'file' && videoFormRef.value) {
            await videoFormRef.value.validate()
        }
    } catch (error) {
        contentFormValid = false
        ElMessage.warning(t('work.pageCreate.completeRequiredFields'))
        return
    }

    // If content form validation fails, return
    if (!contentFormValid) {
        return
    }

    // For URL type, directly open the link in a new tab
    if (formBase.value.contentType === 'url' && formBase.value.linkFormContent) {
        window.open(formBase.value.linkFormContent, '_blank')
        return
    }

    let param = JSON.parse(JSON.stringify(formBase.value))
    if (param.contentType == 'txt') {
        param.content = encodeURIComponent(param.articleFormContent)
        param.articleFormContent = ''
        param.fileLink = ''
    } else if (param.contentType == 'url') {
        // For URL type, store the direct link in content (not encoded)
        param.content = param.linkFormContent
        param.fileLink = param.linkFormLink.join(',')
    } else if (param.contentType == 'file') {
        param.content = encodeURIComponent(param.videoFormContent)
        param.fileLink = param.videoFormLink.join(',')
    } else if (param.contentType == 'image') {
        param.content = ''
        param.fileLink = ''
    }
    // param.publishFlag=1
    if (id) {
        param.id = id
    } else {
        param.id = ''
    }
    // If form reference doesn't exist, directly call publish API
    const res: any = await pageCreatePreview(param)
    if (res.code === 200) {
        // ElMessage.success(t('work.pageCreate.publishSuccess'))
        id = res.data.id
        window.open('/knowledge/detailedArticle/detailedArticle?id=' + id + '&categoryId=' + formBase.value.categoryId + '&previewFlag=1')
        // router.push({ name: 'detailedArticle', query: { id: id, categoryId: formBase.value.categoryId } });
        // Return to previous page after successful publication
        //router.back()
    } else {
        //ElMessage.error(t('work.pageCreate.publishFailed'))
    }
}
// 文件上传前检查
const beforeFileUpload = (file: File) => {
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
        ElMessage.error(t('work.pageCreate.fileSizeLimit'))
        return false
    }
    return true
}

// 移除文件
const removeFile = (model: ModelItem, fileId: string) => {
    if (model.modelValue) {
        const fileIds = model.modelValue.split(',')
        const updatedFileIds = fileIds.filter(id => id !== fileId)
        model.modelValue = updatedFileIds.join(',')

        // 如果移除后没有文件了，可能需要验证
        if (model.requiredFlag === 1 && !model.modelValue) {
            nextTick(() => {
                const index = modelList.value.indexOf(model)
                const fieldPath = `knowledgeModelRelationDTOList[${index}].modelValue`
                multiFormRef.value?.validateField(fieldPath)
            })
        }
    }
}

// Add near other function declarations
const handleBack = () => {
    router.back()
}

// Don't forget to add the refs at the top of the script section 
const articleFormRef = ref()
const linkFormRef = ref()
const imageFormRef = ref()
const videoFormRef = ref()
</script>
<style lang="scss">
.pageCreate {
    .el-form-item__label {
        color: #2C3E50;
        font-size: 14px;
        margin-right: 10px;
    }

    .el-tabs__nav-wrap::after {
        background-color: #f5f5f5;
    }

    .el-tabs__item {
        font-weight: 700;
        color: #b3b3b3;

        padding-left: 20px !important;

        &.is-active {
            color: #FF6600
        }
    }

    width: 85%;

    .top-card {
        display: flex;
        padding-top: 15px;
        padding-bottom: 15px;
        justify-content: space-between;

        .el-button {
            width: 100px;
            height: 36px;
        }

        .show-btn {

            &:hover {
                img {
                    opacity: 0.7;
                }

                color: rgb(144, 147, 153);
            }
        }
    }

    // .el-textarea__inner{
    //   border-radius: 0 !important;
    //   border: 1px solid  #1b1b1b !important;
    // }

    .el-input-group__append {
        border-radius: 0px;

        .el-button {

            border-left: 1px solid rgb(220, 223, 230);
        }
    }

    .el-card {
        padding: 5px 10px;
        box-shadow: none;
        border: none;

        .el-card__body {
            .el-tabs {
                width: 100%;
            }

            width: 100%;
            display: flex;
            padding:0px;

            .top-button {
                color: rgb(116, 140, 117);
                font-size: 16px;
                //font-weight: 400;
                box-shadow: 1px 1px 1px 1px rgb(229, 229, 229);
                margin-right: 10px;
                width: 70px;
                height: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease-in-out;

                &:hover {
                    background-color: #ccc;
                }
            }
        }
    }



    .right-card {
        height: calc(100vh + 107px);
        overflow-y: auto;

        .el-card__body {
            overflow-x: auto;

            .el-tabs__content {
                padding: 0px 16px;
            }

            //padding:15px;
        }
    }

    .existing-files {
        margin-bottom: 10px;

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0px 10px;
            margin-bottom: 5px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #f9f9f9;
            overflow-x: auto;

            .file-name {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: rebeccapurple;
                cursor: pointer;
            }

            .remove-file-btn {
                margin-left: 10px;
                cursor: pointer;
            }
        }
    }

    .cover-uploader {
        .cover-image {
            width: 70px;
            height: 70px;
            display: block;
            object-fit: cover;
        }

        .cover-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 70px;
            height: 70px;
            text-align: center;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                border-color: rgb(255, 102, 0);
            }
        }
    }

}
</style>