export default {
  /*菜单路由*/
  route: {},
  footerBar: {
    termsOfUse: 'SGS SMART Terms of Use',
    DPP: 'Data Privacy Policy',
    termsOfUsePdfPath: '/static/pdf/SGSSMARTTermsofUse.pdf',
    DPPPdfPath: '/static/pdf/SGSSMARTDataPrivacyNotice.pdf',
  },
  tip: 'TIP',
  reminder: 'Reminder',
  title: 'SGS SMART',
  logoutTip: 'Exit the system, do you want to continue?',
  submitText: 'Submit',
  cancelText: 'Cancel',
  select: 'Please select',
  search: 'Please input search content',
  searchTxt: 'Please input content',
  menuTip: 'None menu list',
  loading: 'Loading...',
  uploadType: 'Upload pictures in JPG format only!',
  uploadType_PDF: 'Upload pictures in PDF format only!',
  uploadSize: 'Upload image size cannot exceed 2MB!',
  uploadFileSize: 'Upload file size cannot exceed 10MB!!',
  uploadFileSizeError: 'Upload file size cannot exceed ',
  uploadSuccess: 'Upload Success!',
  uploadLoadingText: 'File uploading…',
  uploadExceed_1:
    'Support uploading one attachment at most, please delete it and upload it again',
  systemLanguage: 'System language',
  number: 'NO.',
  piece: 'Piece',
  success: 'Success!',
  NoData: 'NoData',
  ascendingOrder: 'Asc',
  descendingOrder: 'Desc',
  language: {
    add: 'Add Language',
    name: 'Language',
    curLanguage: 'Current language',
    default: 'Default language',
    zh: 'Chinese',
    en: 'English',
    repeatLanguageMsg:
      'The same language data already exists, please do not add it again',
    manage: 'Language',
    validate: {
      selLanguageBlur: 'Please select language',
      setDefaultLanguage: 'Please set the default language',
    },
  },
  form: {
    printBtn: 'Print',
    mockBtn: 'Mock',
    submitBtn: 'Submit',
    emptyBtn: 'Empty',
    exportTemplate: 'Download Template',
    importTip: 'Only .xls .xlsx files',
  },
  operation: {
    title: 'Action',
    add: 'Add',
    edit: 'Edit',
    modify: 'Modify',
    remove: 'Delete',
    chat: 'Communicate',
    view: 'View',
    search: 'Search',
    submit: 'Submit',
    confirm: 'Confirm',
    cancel: 'Cancel',
    reset: 'Reset',
    disable: 'Disable',
    approve: 'Approve',
    pass: 'Pass',
    reject: 'Reject',
    auth: 'Authorize',
    select: 'Select',
    upload: 'Upload',
    download: 'Download',
    detail: 'Detail',
    confirmDelete: 'Confirm to delete the selected data！',
    confirmStatus: 'Confirm to update the selected data！',
    confirmEnable: 'Confirm enable selection data！',
    confirmDisable: 'Confirm disable selection data！',
    confirmReject: ' Please enter rejection reason ',
    confirmDeleteRoot:
      'All contents under the item will be deleted simultaneously , continue?',
    save: 'Save',
    finalizeReview: 'Finalize review',
    complete: 'Complete',
    isEnable: 'Is enable',
    saveAsTemplate: 'Save as template',
    back: 'Back',
    goBack: 'Go back',
    backToList: 'Back to list',
    print: 'Review & print',
    preview: 'Preview',
    copy: 'Copy',
    downLoad: 'Download',
    export: 'Export',
    exportTRF: 'Export TRF List',
    exportReportFiles: 'Export report files',
    exportReportTips: 'Please filter out TRF list first.',
    exportReportMaxTips:
      'You can only export up to 20 report files, please adjust your filter accordingly.',
    selected: 'Select',
    remark: 'Remark',
    more: 'More',
    pleaseSelect: 'Please select',
    and: ' and ',
    pleaseEnterContent: 'Please enter content...',
  },
  notice: {
    maintenanceNotice: 'SGS SMART system maintenance notice',
    maintenanceMessage:
      "SGS SMART will be upgraded from 19:00 Mar.26th to 00:00 March.28th (GMT+8) , during the period, the system won't be available. Sorry for inconvenience caused.",
  },
  page: {
    prePage: 'Previous page',
    nextPage: 'Next page',
    clock: 'Clockwise',
    counterClock: 'Anti-clockwise',
  },
  datePicker: {
    startTime: 'Start time',
    endTime: 'End time',
    lastWeek: 'Last week',
    lastMonth: 'Last month',
    lastThreeMonths: 'Last three months',
    lastHalfYear: 'Last 6 months',
    lastYear: 'Last year',
    error: {
      timeSpanMsg: 'Only support for one year.',
    },
  },
  navbar: {
    login: 'Login',
    logOut: 'Log out',
    searchTrf: 'Report NO/Test Request NO',
    userinfo: 'User information',
    personalitySetting: 'Account setting',
    authentication: 'Company authentication',
    companyInfo: 'Company information',
    dashboard: 'Home',
  },
  pend: {
    name: 'Todo list',
    pendType: 'Type',
    pendDescribe: 'Task',
    isRead: 'Read',
    goHandle: 'Handle',
    processed: 'Completed',
    createTime: 'Create time',
  },
  common: {
    title: 'SGS SMART ONLINE',

    status: {
      title: 'Status',
      enable: 'Enable',
      disable: 'Disable',
      push: 'Push',
      notPush: 'Not Push',
      reject: 'reject', // 14new
    },
    all: 'ALL',
    register: 'Register',
    effectiveDate: 'Effective date',
    invalidDate: 'Invalid date',
    operator: 'Modified By',
    time: 'Date',
    operationTime: 'Date modified',
    other: 'Other',
    default: 'Set default',
    isDefault: 'Default',
    yes: 'Yes',
    no: 'No',
    enable: 'Enable',
    disable: 'Disable',
    approveStatus: 'Approve status',
    pass: 'Approve',
    reject: 'Reject',
    isAdmin: 'Is Admin',
    isChangeStatus: 'Change status？',
    inputFilter: 'Enter keywords to filter',
    opinion: 'Approve Opinion',
  },
  work:{
    detailedArticle:{
      returnToSearch: 'Return to Search',
      downloadAttachment: 'Download Attachment',
      myFavorites: 'My Favorites',
      contactUs: 'Contact Us',
      home: 'Home',
      source: 'Source',
      attachmentInfo: 'Attachment Information',
      extensionContent: 'Extension Content',
      packageDownload: 'Package Download',
      recommendedContent: 'Recommended Content',
      knowledgeRecommendation: 'Knowledge Recommendation',
      historyVersion: 'History Version',
      quickNavigation: 'Quick Navigation',
      contentInfo: 'Content Information',
      addToFavorites: 'Add to Favorites',
      backToTop: 'Back to Top',
      addFavoriteSuccess: 'Added to favorites successfully',
      cancelFavoriteSuccess: 'Removed from favorites successfully',
      alreadyFavorite: 'Already in favorites',
      noAttachment: 'No attachment',
      alreadyAdded: 'Already added to favorites',
      removeFromFavorites: 'Remove from Favorites',
      viewCount: 'View Count',
      validTime: 'Effective Date',
      author: 'Author',
      status: 'Status',
      createTime: 'Create Time',
      publishTime: 'Law Promulgation Date',  
      attachmentExtensionFree: 'Current user can see all content',
      attachmentExtensionVip: 'The following content is only visible to paid users, please contact us if you need it',
      avoidRapidClicks: 'Please avoid rapid clicks',
      frequentOperation: 'Please avoid frequent operations',
      pleaseDoNotOperateFreely: 'Please avoid frequent operations',
      publishTimeTrival: 'Publish Time',
      statusActive: 'Active',
      statusInactive: 'Inactive',
    },
    contentPage:{
      searchKeywords: 'Search Keywords',
      searchPlaceholder: 'Please enter search content',
      searchType: 'Search Type',
      title: 'Title',
      summary: 'Summary',
      attachment: 'Attachment',
      publishTime: 'Publish Time',
      lastWeek: 'Last 7 Days',
      lastMonth: 'Last Month',
      lastHalfYear: 'Last 6 Months',
      tagSelection: 'Tag Selection',
      expandCondition: 'Expand Conditions',
      collapseCondition: 'Collapse Conditions',
      publishTimeDesc: 'Publish Time Desc',
      publishTimeAsc: 'Publish Time Asc',
      relevance: 'Relevance',
      viewCount: 'View Count',
      defaultSort: 'Default Sort',
      noResultsFound: 'Sorry, no results found for "{query}"',
      noData: 'Sorry, no related data',
      active: 'Active',
      inactive: 'Inactive',
      unknownStatus: 'Unknown Status',
      articleIdEmpty: 'Article ID cannot be empty',
      cancelFavoriteFailed: 'Failed to cancel favorite',
      addFavoriteFailed: 'Failed to add favorite',
      operationFailed: 'Operation failed, please try again later',
      selectTags: 'Please select tags',
      noDescription: 'No description available',
      startDate: 'Start Date',
      endDate: 'End Date',
      publishTimee: 'Promulgation Date',
      validTime: 'Effective Date',
    },
    tagManagement:{
      title: 'Tag Management',
      tagName: 'Tag Name',
      tagDescription: 'Tag Description',
      status: 'Status',
      creator: 'Creator',
      createTime: 'Create Time',
      operation: 'Action',
      search: 'Search',
      reset: 'Reset',
      add: 'New Tag',
      edit: 'Edit',
      delete: 'Delete',
      enable: 'Enable',
      disable: 'Disable',
      editTag: 'Edit Tag',
      addTag: 'New Tag',
      cancel: 'Cancel',
      save: 'Save',
      confirmDelete: 'Confirm Delete',
      deleteConfirmTitle: 'Warning',
      deleteConfirmMessage: 'Are you sure to delete tag',
      deleteSuccess: 'Delete successful',
      deleteFailed: 'Delete failed',
      saveSuccess: 'Save successful',
      saveFailed: 'Save failed',
      updateSuccess: 'Update successful',
      updateFailed: 'Update failed',
      pleaseInput: 'Please input',
      pleaseInputTagName: 'Please input tag name',
      pleaseInputTagDescription: 'Please input tag description',
      tagNameRequired:'Tag name is required',
      noData: 'No data',
    },
    pageCreate:{
      width:'Width',
      height:'Height',
      unknownResolution:'Unknown Resolution',
      loading:'Loading...',
      size:'Size',
      specification:'Specification',
      categoryMessage:'Please select the category where the article is located',
      baseProp:'Basic properties',
      title: 'Title',
      titlePlaceholder: 'Please enter title',
      attachments: 'Attachments',
      noAttachments: 'No Attachments',
      addAttachment: 'Add Attachment',
      articleContent: 'Article Content',
      enterContent: 'Please enter content',
      noData: 'No data',
      summary: 'Summary',
      summaryPlaceholder: 'Please enter summary',
      addImage: 'Add image',
      imageSizeLimit: 'Image size cannot exceed 2MB!',
      linkContent: 'Link content',
      enterLinkAndConfirm: 'Please enter link address and press Enter to confirm',
      pleaseFillLink: 'Please fill in the link',
      previewLink: 'Preview Link',
      fileLink: 'Select File',
      pleaseSelectFile: 'Please select a file',
      selectFile: 'Select File',
      supportedFileFormats: 'Please upload supported file formats: MP4, PDF, Word, Excel!',
      
      save: 'Save',
      publish: 'Publish',
      browse: 'Browse',
      author: 'Author',
      enterAuthor: 'Please enter author',
      editor: 'Editor',
      enterEditor: 'Please enter editor',
      category: 'Category',
      enterCategory: 'Please select category',
      coverImage: 'Cover image',
      contentType: 'Content type',
      articleType: 'Article',
      imageType: 'Image',
      fileType: 'File',
      linkType: 'Link',
      tags: 'Tags',
      selectTags: 'Please select tags',
      enterSummary: 'Please enter summary',
      multiDimensionManage: 'More information',
      clickUpload: 'Click to upload',
      visit: 'Visit',
      
      // 新增的翻译
      moveUp: 'Move Up',
      moveDown: 'Move Down',
      viewOriginal: 'View Original',
      editImage: 'Edit Image',
      delete: 'Delete',
      fileName: 'File Name',
     
      fileTypeError: 'Invalid file format',
      enterTitle: 'Please enter title',
      enterLinkFirst: 'Please enter link address',
      authorization: 'Authorization',
      unAuthorization: 'UnAuthorization',
      
      // Rich text editor toolbar
      bold: 'Bold',
      italic: 'Italic',
      underline: 'Underline',
      strikeThrough: 'Strike Through',
      code: 'Code',
      insertLink: 'Insert Link',
      insertImage: 'Insert Image',
      uploadImage: 'Upload Image',
      insertVideo: 'Insert Video',
      uploadVideo: 'Upload Video',
      insertTable: 'Insert Table',
      headerStyle: 'Header Style',
      paragraph: 'Paragraph',
      fontSize: 'Font Size',
      fontFamily: 'Font Family',
      lineHeight: 'Line Height',
      bulletedList: 'Bulleted List',
      numberedList: 'Numbered List',
      justifyLeft: 'Align Left',
      justifyCenter: 'Align Center',
      justifyRight: 'Align Right',
      justifyJustify: 'Justify',
      increaseIndent: 'Increase Indent',
      decreaseIndent: 'Decrease Indent',
      textColor: 'Text Color',
      backgroundColor: 'Background Color',
      clearStyle: 'Clear Style',
      undo: 'Undo',
      redo: 'Redo',
      
      // Upload confirmation dialog
      selectFiles: 'Select Files',
      confirm: 'Confirm',
      cancel: 'Cancel',
      
      // Form validation prompts
      pleaseEnter: 'Please enter',
      pleaseSelect: 'Please select',
      pleaseUpload: 'Please upload',
      pleaseComplete: 'Please complete',
      
      // Error messages
      onlyImageAllowed: 'Only image files are allowed!',
      uploadFailed: 'Upload failed',
      unnamedFile: 'Unnamed file',
      saveSuccess: 'Save successful',
      saveFailed: 'Save failed',
      publishSuccess: 'Publish successful',
      publishFailed: 'Publish failed',
      completeRequiredFields: 'Please complete the required multi-dimension fields',
      fileSizeLimit: 'File size cannot exceed 10MB!',
      getPageDataFailed: 'Failed to get page data',
      loadPageDataFailed: 'Failed to load page data',
    },
    knowledgeBaseHome:{
      searchPlaceholder: 'Please enter keywords',
      search: 'Search',
      hotTags: 'Hot Search:',
      tagLoadFailed: 'Tag loading failed',
      knowledgeRecommend: 'Knowledge Recommendation',
      latestKnowledge: 'Latest Knowledge',
      hotMaterials: 'Hot Materials',
      refresh: 'Refresh',
      materialLoadFailed: 'Material loading failed',
      knowledgeLoadFailed: 'Knowledge loading failed',
    },
    tabsPage:{
      contentList: 'Content List',
      recyclebin: 'Recycle Bin',
      articleStatus: 'Status',
      all: 'All',
      published: 'Published',
      unpublished: 'Unpublished',
      searchKeywords: 'Keywords',
      pleaseEnter: 'Please Enter',
      create: 'Create',
      refresh: 'Refresh',
      publishTimeDesc: 'Publish Time Desc',
      publishTimeAsc: 'Publish Time Asc',
      relevance: 'Relevance',
      viewCount: 'View Count',
      defaultSort: 'Default Sort',
      createTime: 'Create Time',
      author: 'Author',
      viewCountText: 'Views',
      edit: 'Edit',
      publish: 'Publish',
      republish: 'Republish',
      delete: 'Delete',
      view: 'View',
      unpublish: 'Unpublish',
      batchRestore: 'Batch Restore',
      deleteTime: 'Delete Time',
      restore: 'Restore',
      publishSuccess: 'Published Successfully',
      unpublishSuccess: 'Unpublished Successfully',
      operationFailed: 'Operation Failed',
      deleteSuccess: 'Deleted Successfully',
      deleteFailed: 'Delete Failed',
      restoreSuccess: 'Restored Successfully',
      restoreFailed: 'Restore Failed',
      batchRestoreSuccess: 'Batch Restored Successfully',
      batchRestoreFailed: 'Batch Restore Failed',
      selectRestoreItems: 'Please Select Items to Restore',
      noAddArticle: 'Cannot Add Articles in This Category',
      search: 'Search',
      publishTime: 'Publish Time',
      noData: 'Sorry, no related data',
      noResultsFound: 'Sorry, no results found for "{query}"',
      noDescription: 'No description available',
    },
    favPage:{
      subscribe: 'Subscribe',
      subscribed: 'Subscribed',
      searchKeyword: 'Search Keyword',
      searchType: 'Search Type',
      publishTime: 'Publish Time',
      tagSelection: 'Tag Selection',
      pleaseSelectTags: 'Please Select Tags',
      search: 'Search',
      reset: 'Reset',
      expandCondition: 'Expand Conditions',
      collapseCondition: 'Collapse Conditions',
      subscribeSuccess: 'Subscribe Successfully',
      cancelSubscribeSuccess: 'Unsubscribe Successfully',
      operationFailed: 'Operation Failed',
      startDate: 'Start Date',
      endDate: 'End Date',
      publishTimeDesc: 'Publish Time Desc',
      publishTimeAsc: 'Publish Time Asc',
      relevance: 'Relevance',
      viewCount: 'View Count',
      defaultSort: 'Default Sort',
      cancelFavoriteSuccess: 'Cancel Favorite Success',
      noData: 'Sorry, no related data',
      noResultsFound: 'Sorry, no results found for "{query}"',
      searchPlaceholder: 'Please enter keywords',
      noDescription: 'No description available',
    },
    pagination: {
      total: 'Total',
      itemsPerPage: 'Items per page',
      prev: 'Previous',
      next: 'Next',
      jumper: 'Go to',
      pageSize: 'Page size',
      page: 'Page'
    },
    navigationBar: {
      searchPlaceholder: 'Enter keywords to filter',
      noSearchResult: 'No results found',
      paymentRequired: 'This content requires payment to view',
      adminRequired: 'This content requires administrator privileges'
    },
  }
}
