export default {
  /*菜单路由*/
  route: {},
  footerBar: {
    termsOfUse: 'SGS SMART Terms of Use',
    DPP: 'Data Privacy Policy',
    termsOfUsePdfPath: '/static/pdf/SGSSMARTTermsofUse.pdf',
    DPPPdfPath: '/static/pdf/SGSSMARTDataPrivacyNotice.pdf',
  },
  action:'Action',
  tip: 'TIP',
  reminder: 'Reminder',
  title: 'SGS SMART',
  logoutTip: 'Exit the system, do you want to continue?',
  submitText: 'Submit',
  cancelText: 'Cancel',
  select: 'Please select',
  search: 'Please input search content',
  searchTxt: 'Please input content',
  menuTip: 'None menu list',
  loading: 'Loading...',
  uploadType: 'Upload pictures in JPG format only!',
  uploadType_PDF: 'Upload pictures in PDF format only!',
  uploadSize: 'Upload image size cannot exceed 2MB!',
  uploadFileSize: 'Upload file size cannot exceed 10MB!!',
  uploadFileSizeError: 'Upload file size cannot exceed ',
  uploadSuccess: 'Upload Success!',
  uploadLoadingText: 'File uploading…',
  uploadExceed_1:
    'Support uploading one attachment at most, please delete it and upload it again',
  systemLanguage: 'System language',
  number: 'NO.',
  piece: 'Piece',
  success: 'Success!',
  NoData: 'NoData',
  ascendingOrder: 'Asc',
  descendingOrder: 'Desc',
  language: {
    add: 'Add Language',
    name: 'Language',
    curLanguage: 'Current language',
    default: 'Default language',
    zh: 'Chinese',
    en: 'English',
    repeatLanguageMsg:
      'The same language data already exists, please do not add it again',
    manage: 'Language',
    validate: {
      selLanguageBlur: 'Please select language',
      setDefaultLanguage: 'Please set the default language',
    },
  },
  form: {
    printBtn: 'Print',
    mockBtn: 'Mock',
    submitBtn: 'Submit',
    emptyBtn: 'Empty',
    exportTemplate: 'Download Template',
    importTip: 'Only .xls .xlsx files',
  },
  operation: {
    title: 'Action',
    add: 'Add',
    edit: 'Edit',
    modify: 'Modify',
    remove: 'Delete',
    chat: 'Communicate',
    view: 'View',
    search: 'Search',
    submit: 'Submit',
    confirm: 'Confirm',
    cancel: 'Cancel',
    reset: 'Reset',
    resetTip: 'Clear All Filters',
    disable: 'Disable',
    approve: 'Approve',
    pass: 'Pass',
    reject: 'Reject',
    auth: 'Authorize',
    select: 'Select',
    upload: 'Upload',
    download: 'Download',
    detail: 'Detail',
    confirmDelete: 'Confirm to delete the selected data!',
    confirmStatus: 'Confirm to update the selected data!',
    confirmEnable: 'Confirm enable selection data!',
    confirmDisable: 'Confirm disable selection data!',
    confirmReject: ' Please enter rejection reason ',
    confirmDeleteRoot:
      'All contents under the item will be deleted simultaneously , continue?',
    save: 'Save',
    finalizeReview: 'Finalize review',
    complete: 'Complete',
    isEnable: 'Is enable',
    saveAsTemplate: 'Save as template',
    back: 'Back',
    goBack: 'Go back',
    backToList: 'Back to list',
    print: 'Review & print',
    preview: 'Preview',
    copy: 'Copy',
    downLoad: 'Download',
    export: 'Export',
    exportTRF: 'Export TRF List',
    exportReportFiles: 'Export report files',
    exportReportTips: 'Please filter out TRF list first.',
    exportReportMaxTips:
      'You can only export up to 20 report files, please adjust your filter accordingly.',
    selected: 'Select',
    remark: 'Remark',
    more: 'More',
    pleaseSelect: 'Please select',
    and: ' and ',
    pleaseEnterContent: 'Please enter content...',
  },
  notice: {
    maintenanceNotice: 'SGS SMART system maintenance notice',
    maintenanceMessage:
      "SGS SMART will be upgraded from 19:00 Mar.26th to 00:00 March.28th (GMT+8) , during the period, the system won't be available. Sorry for inconvenience caused.",
  },
  page: {
    prePage: 'Previous page',
    nextPage: 'Next page',
    clock: 'Clockwise',
    counterClock: 'Anti-clockwise',
  },
  datePicker: {
    startTime: 'Start time',
    endTime: 'End time',
    lastWeek: 'Last week',
    lastMonth: 'Last month',
    lastThreeMonths: 'Last three months',
    lastHalfYear: 'Last 6 months',
    lastYear: 'Last year',
    error: {
      timeSpanMsg: 'Only support for one year.',
    },
  },
  navbar: {
    logOut: 'Log out',
    searchTrf: 'Report NO/Test Request NO',
    userinfo: 'User information',
    personalitySetting: 'Account setting',
    authentication: 'Company authentication',
    companyInfo: 'Company information',
    dashboard: 'Home',
  },
  pend: {
    name: 'Todo list',
    pendType: 'Type',
    pendDescribe: 'Task',
    isRead: 'Read',
    goHandle: 'Handle',
    processed: 'Completed',
    createTime: 'Create time',
  },
  common: {
    title: 'SGS SMART ONLINE',

    status: {
      title: 'Status',
      enable: 'Enable',
      disable: 'Disable',
      push: 'Push',
      notPush: 'Not Push',
      reject: 'reject', // 14new
    },
    all: 'ALL',
    register: 'Register',
    effectiveDate: 'Effective date',
    invalidDate: 'Invalid date',
    operator: 'Modified By',
    time: 'Date',
    operationTime: 'Date modified',
    other: 'Other',
    default: 'Set default',
    isDefault: 'Default',
    yes: 'Yes',
    no: 'No',
    enable: 'Enable',
    disable: 'Disable',
    approveStatus: 'Approve status',
    pass: 'Approve',
    reject: 'Reject',
    isAdmin: 'Is Admin',
    isChangeStatus: 'Change status？',
    inputFilter: 'Enter keywords to filter',
    opinion: 'Approve Opinion',
    closeTab:
      'Please make sure you have saved the information before you close the window',
  },
  formValidate: {
    validateError: 'Necessary info in application is not completed',
  },
  filter: {
    set: 'Set',
    default: 'Default',
    delete: 'Delete',
    saveFilter: 'Save View',
    replaceAnExisting: 'Replace an existing',
    saveAsNew: 'Save as New',
    save: 'Save',
    saveAsLocal: 'Save as Local',
    saveAsPublic: 'Save as Public',
    filterPlaceHolder: 'My Saved Views',
  },
  api: {
    success: 'Settings changed!',
    error: 'Operation failed!',
  },
  register: {
    error: 'Error',
    userName: 'User Name',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    mobilePhone: 'Mobile Phone',
    mobilePhoneTip: 'You can use mobile phone to login',
    getCode: 'Get Code',
    verificationCode: 'Verification Code',
    next: 'Next',
    companyName: 'Company Name',
    companyNameCN: 'Company Name(CN)',
    taxNo: 'Tax No',
    joinCompany: 'Join Your Company',
    fillCompanyInformation: 'Fill in Company Information',
    companyAddress: 'Company Address',
    companyAddressCN: 'Company Address(CN)',
    productCategory: 'Product Category',
    sgsContacts: 'One email for SGS contacts',
    sgsReportNo: 'One report number from SGS',
    approveTip: 'Provide more informartion to expedite the approval',
    success: 'Success',
    registrationSubmitedTo:'Your registration has been submitted to',
    registrationSuccess:'You will get an email when the registration has been approved.',
    thankYou:'Thank you for choosing SGS.',
    welcomeText:'Hi',
    welcomeToSGS:'welcome to SGS SMART.',
    adminContact:'SGS administrator',
    pleaseUserInfo:'Please fill your information.',
    pleaseChooseCompany:'Please choose your company.',
    pleaseFillCompanyInfo:'Please fill in your company information.',
    submit:'Submit',
    returnLoginPage:'Return to login page',
    companyExist:'Company information already exists, please join the company',
    validate:{
      emailExist:'The email address already exists, please modify it or log in directly',
      passwordFormat:'Password must contain uppercase, lowercase, numbers and special characters',
      errorRegister:'Invalid parameter, please register again',
      companyNameEN:'Please input company name',
      taxNo:'Please input tax no',
      companyAddress:'Please input company address',
      serviceUnits:'Please select service units',
      passwordRequired:'Please input password',
      passwordNotMatch:'Two passwords do not match',
      confirmPasswordRequired:'Please input confirm password',
      verificationCodeRequired:'Please input verification code',
      passwordLength:'Password length should be 8 to 20 characters',
      mobileRequired:'Please input mobile number',
      mobileFormat:'Currently, only mobile numbers in mainland China are supported',
      verificationCodeLength:'Verification code should be 6 digits',
      userNameLength:'Length should be 3 to 50 characters',
      emailFormat:'Please input correct email format',
      userNameRequired:'Please input userName',
      emailRequired:'Please input email', 
      sgsReportNo:'Please input SGS report no',
      sgsContacts:'Please input SGS contacts',
      companyNameCN:'Please input company name(CN)',
    }
  },
  companyInfo:{
    title:'COMPANY INFO',
    customerNo:'SGS Customer No',
    reportNo:'SGS Report No',
    email:'SGS Contact Email',
    businessLicense:'Business License'
  },
  scm:{
    iamSupplier:'I am a Supplier',
    iamBuyer:'I am a Buyer',
    MyBuyer:'My Buyer',
    MySupplier:'My Supplier',
    MyManufacture:'My Manufacturer',
    address:'Address',
    assignedCode:'Assigned Code',
    sgsCode:'SGS Customer No.',
    approveStatus: 'Approve Status',
    supplierName: 'Supplier Name',
    buyerName: 'Buyer Name',
    manufacturerName: 'Manufacturer Name',
    manufacturer:'Manufacturers',
    companyName: 'Company Name',
    searchSupplier:'Search Supplier',
    searchBuyer:'Search Buyer',
    searchManufacture:'Search Manufacturer',
    createSupplier:'Create Supplier',
    createBuyer:'Create Buyer',
    createManufacture:'Create Manufacturer',
    updateUser:'Update User',
    updateTime:'Update Time',
    sgsBoard:'SGS Smart on Board',
    company:{
      en:'Company Name(EN)',
      cn:'Company Name(CN)',
      addressEn:'Company Address(EN)',
      addressCn:'Company Address(CN)',
      businessContacts:'Business Contacts',
    },
    approveStatusEnumName:{
      approve:'Approved',
      inProgress:'In Progress',
      reject:'Reject',
      noRequired:'Not Required',
    },
    action:'Action',
    linkName:{
      linkSupplier:'Link Supplier',
      linkBuyer:'Link Buyer',
      linkManufacture:'Link Manufacturer'
    },
    contact:{
      title:'Contacts',
      name:'Contacts Name',
      email:'Email',
      mobile:'Phone Number',
      addContact:'Add Contact',
      editContact:'Edit Contact',
    },
    btnName:{
      add:'Add',
      cancel:'Cancel',
      save:'Save',
      edit:'Edit',
      delete:'Delete',
      disable:'Disable',
      enable:'Enable',
      approve:'Approve',
      reject:'Reject'
    },
    validate:{
      contactName:'Please input name',
      companyName:'Please select company name'
    }

  }
}
