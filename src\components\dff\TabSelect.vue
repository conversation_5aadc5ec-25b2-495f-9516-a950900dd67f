<template>
    <el-popover
            width="600"
            placement="bottom"
            v-model="isShowTabSelect"
            trigger="click">
        <el-tabs tab-position="left" :value="tabValue" @tab-click="tabClickHandle">
            <el-checkbox-group :value="checkList" @input="changeHandle">
                <draggable ref="draggable" :value="dffData" @input="val => dffData = val" @start="drag=true" ghost-class="ghost"
                           @end="drag=false" :before-close="handleClose">
                    <el-checkbox style="display: block; margin-left: 30px;"
                                 v-for="(dItem, dIndex) in dffData"
                                 :label="dItem.fieldCode"
                                 :key="dIndex+dItem.fieldCode">{{$t(dItem.displayName)}}
                    </el-checkbox>
                </draggable>
            </el-checkbox-group>
            <el-button type="primary" style="float: right" @click="saveSortSetting">{{$t('operation.confirm')}}
            </el-button>
        </el-tabs>
        <el-button slot="reference" icon="el-icon-setting" class="setting-btn"></el-button>
    </el-popover>
</template>

<script>
    import {listSetting, saveSetting,queryDffRows} from '@/api/trf/trfListSetting'
    import {validatenull} from "@/util/validate";
    import {mapGetters} from "vuex";
    export default {
        name: "TabSelect",
        components: {
            draggable: resolve => require(['vuedraggable'], resolve)
        },
        computed: {
             ...mapGetters(["permission", "language"]),
        },
        data() {
            return {
                newProductLineCode:'',
                resultColumns:[],
                isShowTabSelect:false,
                tabValue: null,
                drag: true,
                dffData: [
                    /* {displayName: this.$t('trfList.trfNo'), fieldCode: 'trfNo'},*/
                    {displayName: 'trfList.serviceTypeName', fieldCode: 'serviceTypeName'},
                    {displayName: 'trfList.extendOrderNo', fieldCode: 'extendOrderNo'},
                    {displayName: 'trfList.buyer', fieldCode: 'buyerCustomerGroupName'},
                    {displayName: 'trfList.templateName', fieldCode: 'templateName'},
                    {displayName: 'trfList.applyCustomer', fieldCode: 'applyNameEn'},
                    {displayName: 'trfList.submitDate', fieldCode: 'trfSubmissionDate'},
                    {displayName: 'trfList.sampleDate', fieldCode: 'sampleReceiveDate'},
                    {displayName: 'trfList.dueDate', fieldCode: 'dueDate'},
                    {displayName: 'trfList.applyContact', fieldCode: 'applyContact'},
                    {displayName: 'trfList.labName', fieldCode: 'labName'},
                    {displayName: 'trfList.productDescription', fieldCode: 'productDescription'},
                    {displayName: 'trfList.styleNo', fieldCode: 'styleNo'},
                    {displayName: 'trfList.itemNo', fieldCode: 'itemNo'},
                    {displayName: 'trfList.poNo', fieldCode: 'poNo'},
                    {displayName: 'trfList.articleNo', fieldCode: 'refCode5'},
                    {displayName: 'trfList.reportNo', fieldCode: 'reportNo'},
                    {displayName: 'trfList.specialProduct', fieldCode: 'specialProductAttribute10'},
                    {displayName: 'trfList.reportApprovedDate', fieldCode: 'reportApprovedDate'},
                    {displayName: 'trfList.conclusion', fieldCode: 'reportConclusion'},
                   /* {displayName: 'trfList.pendingReason', fieldCode: 'pendingReason'},*/
                     {displayName: 'trfList.buyerReviewConclusion', fieldCode: 'buyerReviewConclusion'},
                ],
                dffRowsData:[],
                trfListSettings: [],
                checkList: [],
                defaultTemplate: [
                    /* {displayName: this.$t('trfList.trfNo'), fieldCode: 'trfNo'},*/
                    {displayName: 'trfList.serviceTypeName', fieldCode: 'serviceTypeName'},
                    {displayName: 'trfList.extendOrderNo', fieldCode: 'extendOrderNo'},
                    {displayName: 'trfList.buyer', fieldCode: 'buyerCustomerGroupName'},
                    {displayName: 'trfList.templateName', fieldCode: 'templateName'},
                    {displayName: 'trfList.applyCustomer', fieldCode: 'applyNameEn'},
                    {displayName: 'trfList.submitDate', fieldCode: 'trfSubmissionDate'},
                    {displayName: 'trfList.sampleDate', fieldCode: 'sampleReceiveDate'},
                    {displayName: 'trfList.applyContact', fieldCode: 'applyContact'},
                    {displayName: 'trfList.labName', fieldCode: 'labName'},
                    {displayName: 'trfList.productDescription', fieldCode: 'productDescription'},
                    {displayName: 'trfList.styleNo', fieldCode: 'styleNo'},
                    {displayName: 'trfList.poNo', fieldCode: 'poNo'},
                    {displayName: 'trfList.articleNo', fieldCode: 'refCode5'},
                    {displayName: 'trfList.reportNo', fieldCode: 'reportNo'},
                    {displayName: 'trfList.reportApprovedDate', fieldCode: 'reportApprovedDate'},
                    {displayName: 'trfList.conclusion', fieldCode: 'reportConclusion'},
                   /* {displayName: 'trfList.pendingReason', fieldCode: 'pendingReason'},*/
                     {displayName: 'trfList.buyerReviewConclusion', fieldCode: 'buyerReviewConclusion'},
                ],
                defaultAflTemplate: [
                    /* {displayName: this.$t('trfList.trfNo'), fieldCode: 'trfNo'},*/
                    // {displayName: 'trfList.buyer', fieldCode: 'buyerCustomerGroupName'},
                    // {displayName: 'trfList.templateName', fieldCode: 'templateName'},
                    {displayName: 'trfList.applyCustomer', fieldCode: 'customerNameEn'},
                    {displayName: 'trfList.submitDate', fieldCode: 'trfSubmissionDate'},
                    // {displayName: 'trfList.sampleDate', fieldCode: 'sampleReceiveDate'},
                      {displayName: 'trfList.applyContact', fieldCode: 'applyContact'},
                    // {displayName: 'trfList.labName', fieldCode: 'labName'},
                    // {displayName: 'trfList.productDescription', fieldCode: 'productDescription'},
                    // {displayName: 'trfList.styleNo', fieldCode: 'styleNo'},
                    // {displayName: 'trfList.poNo', fieldCode: 'poNo'},
                    // {displayName: 'trfList.articleNo', fieldCode: 'articleNo'},
                    {displayName: 'trfList.reportNo', fieldCode: 'reportNo'},
                    {displayName: 'trfList.reportApprovedDate', fieldCode: 'reportApprovedDate'},
                    //  {displayName: 'trfList.conclusion', fieldCode: 'reportConclusion'},
                    // {displayName: 'trfList.pendingReason', fieldCode: 'pendingReason'},
                    //  {displayName: 'trfList.reviewConclusion', fieldCode: 'reviewConclusion'},
                ]

            }
        },
        props: {
            userInfo:{
               type: Object,
                default: () => {
                    return null
                }
            },
            trfTemplate: {
                type: Array,
                default: () => {
                    return []
                }
            }
        },
        mounted() {
              this.newProductLineCode=this.userInfo.productLineCode;
              if(this.newProductLineCode==='all'){
                this.newProductLineCode=this.userInfo.defaultProductLineCode;
              }
              if(this.newProductLineCode=='AFL'){
                    this.dffData = this.defaultAflTemplate;
                }
        },
        created() {
            //查询DFF
            /* let res = await queryDffRows();
                if(!validatenull(res)){
                    let dffFieldDatas = res.data.data;
                    if(!validatenull(dffFieldDatas)){
                       dffFieldDatas.forEach(function (obj, key, dataList) {
                           let dffObj ={};
                            dffObj.displayName= obj.fieldName;
                           if(that.language=='zh-CN'){
                                dffObj.displayName= obj.fieldNameCN;
                           }
                            dffObj.fieldCode = obj.fieldCode;
                            that.dffRowsData.push(dffObj);
                        });
                    }
                 }
              this.dffData= this.getTemplateMethod();*/
        },
        methods: {
           async getTemplateMethod(){
                if(this.newProductLineCode=='AFL'){
                    return this.getAflDefaultTemplate();
                }else{
                    return this.getDefaultTemplate();
                }
            },
            handleClose(done) {
                done();
            },
            async init(tabVal) {
                await this.tabClickHandle(tabVal)
                /*  if (tabVal === 'all') {
                    this.checkList = this.getDefaultTemplate(this.companyType).map(item => item.fieldCode)
                    // 获取要获取默认的 选中的
                  }*/
                //this.$emit('updateVal', this.getValue(this.checkList))
            },
            async tabClickHandle(tab) {
                if(this.newProductLineCode!='AFL' && !validatenull(this.userInfo.customerGroupCode)) {
                    await this.queryDffRowsData();
                }
                // 初始化自己设置的 排序规则
                await this.initTRFListSetting();
                this.sortDffData();
                /* this.checkList= []
                 // 设置当前 选中的 tab 值, dff template ID
                 if (tab === 'all') {
                     this.dffData = this.getDefaultTemplate(this.companyType)
                     this.tabValue = tab
                 } else {
                   let id = null
                   if (this.$lodash.isString(tab)) {
                     id = tab
                   } else {
                     id = tab.name
                   }
                   this.tabValue = id
                    this.sortDffData();*/
                /* const dffData = await getDff({id: id})
                 if (dffData.status === 200 && !this.$lodash.isEmpty(dffData.data.result))  {
                   let dffDataMap = this.$lodash.get(dffData, 'data.result', []).filter(item => item.displayInSystem.indexOf('2') >= 0).map(item => {
                     return {
                       displayName: item.dispalyName,
                       fieldCode: 'productInstance.'+this.$lodash.lowerFirst(item.fieldCode)
                     }
                   })

                   getDefaultTemplate(this.companyType).forEach(item => {
                     if (this.$lodash.findIndex(dffDataMap, {fieldCode: item.fieldCode}) === -1) {
                       dffDataMap.unshift(item)
                     }
                   })
                   this.dffData = dffDataMap
                   console.log(id)
                   // 初始化自己设置的 排序规则
                   await this.initTRFListSetting()
                   this.sortDffData()
                 }*/
                //}
            },
            changeHandle(selectArray) {
                this.checkList = selectArray
            },
            getValue(selectArrayStr = []) {
                let currentListSortArray = [], sortIndex = 1
                this.dffData.forEach((n) => {
                    if (selectArrayStr.indexOf(n.fieldCode) !== -1) {
                        currentListSortArray.push({
                            fieldCode: n.fieldCode,
                            displayName: n.displayName,
                            templateId: this.tabValue,
                            sortIndex: sortIndex
                        })
                        sortIndex++
                    }
                })
                return currentListSortArray
            },
            getSearchValue(selectArrayStr = []) {
               this.resultColumns=[];
                let currentListSortArray = [], sortIndex = 1;
                let lastIndex = selectArrayStr.length;
                // this.dffData = this.dffData.filter(item => selectArrayStr.includes(item.fieldCode));
                this.dffData.forEach((n, index) => {
                    // for(var i=0; i<=selectArrayStr.length; i++) {
                    lastIndex++;
                    var codeIndex = selectArrayStr.indexOf(n.fieldCode);
                    if (codeIndex != -1) {
                        currentListSortArray.push({
                            fieldCode: n.fieldCode,
                            displayName: n.displayName,
                            templateId: this.tabValue,
                            sortIndex: codeIndex
                        })
                         this.resultColumns.push({
                            fieldCode: n.fieldCode,
                            displayName: n.displayName,
                            templateId: this.tabValue,
                            sortIndex: lastIndex
                        });
                        //sortIndex++
                    } else {
                        currentListSortArray.push({
                            fieldCode: n.fieldCode,
                            displayName: n.displayName,
                            templateId: this.tabValue,
                            sortIndex: lastIndex
                        })
                        sortIndex++
                    }
                    // }
                })
                return currentListSortArray
            },


            getDefaultTemplate() {
                const defaultTabTemplate = [
                    /* {displayName: this.$t('trfList.trfNo'), fieldCode: 'trfNo'},*/
                    {displayName: 'trfList.serviceTypeName', fieldCode: 'serviceTypeName'},
                    {displayName: 'trfList.extendOrderNo', fieldCode: 'extendOrderNo'},
                    {displayName: 'trfList.buyer', fieldCode: 'buyerCustomerGroupName'},
                    {displayName: 'trfList.templateName', fieldCode: 'templateName'},
                    {displayName: 'trfList.applyCustomer', fieldCode: 'applyNameEn'},
                    {displayName: 'trfList.submitDate', fieldCode: 'trfSubmissionDate'},
                    {displayName: 'trfList.sampleDate', fieldCode: 'sampleReceiveDate'},
                    {displayName: 'trfList.dueDate', fieldCode: 'dueDate'},
                    {displayName: 'trfList.applyContact', fieldCode: 'applyContact'},
                    {displayName: 'trfList.labName', fieldCode: 'labName'},
                    {displayName: 'trfList.productDescription', fieldCode: 'productDescription'},
                    {displayName: 'trfList.styleNo', fieldCode: 'styleNo'},
                    {displayName: 'trfList.poNo', fieldCode: 'poNo'},
                    {displayName: 'trfList.articleNo', fieldCode: 'refCode5'},
                    {displayName: 'trfList.reportNo', fieldCode: 'reportNo'},
                    {displayName: 'trfList.reportApprovedDate', fieldCode: 'reportApprovedDate'},
                    {displayName: 'trfList.conclusion', fieldCode: 'reportConclusion'},
                   /* {displayName: 'trfList.pendingReason', fieldCode: 'pendingReason'},*/
                     {displayName: 'trfList.buyerReviewConclusion', fieldCode: 'buyerReviewConclusion'},
                ]
                return defaultTabTemplate
            },
            getAflDefaultTemplate() {
                const defaultTabTemplate = [
                    /* {displayName: this.$t('trfList.trfNo'), fieldCode: 'trfNo'},*/
                    // {displayName: 'trfList.buyer', fieldCode: 'buyerCustomerGroupName'},
                    // {displayName: 'trfList.templateName', fieldCode: 'templateName'},
                    {displayName: 'trfList.applyCustomer', fieldCode: 'applyNameEn'},
                    // {displayName: 'trfList.applicaton', fieldCode: 'customerNameEn'},
                    {displayName: 'trfList.submitDate', fieldCode: 'trfSubmissionDate'},
                    // {displayName: 'trfList.sampleDate', fieldCode: 'sampleReceiveDate'},
                      {displayName: 'trfList.applyConcat', fieldCode: 'applyConcat'},
                    // {displayName: 'trfList.labName', fieldCode: 'labName'},
                    // {displayName: 'trfList.productDescription', fieldCode: 'productDescription'},
                    // {displayName: 'trfList.styleNo', fieldCode: 'styleNo'},
                    // {displayName: 'trfList.poNo', fieldCode: 'poNo'},
                    // {displayName: 'trfList.articleNo', fieldCode: 'articleNo'},
                    {displayName: 'trfList.reportNo', fieldCode: 'reportNo'},
                    {displayName: 'trfList.reportApprovedDate', fieldCode: 'reportApprovedDate'},
                    //  {displayName: 'trfList.conclusion', fieldCode: 'reportConclusion'},
                    // {displayName: 'trfList.pendingReason', fieldCode: 'pendingReason'},
                    //  {displayName: 'trfList.reviewConclusion', fieldCode: 'reviewConclusion'},
                ]
                return defaultTabTemplate
            },

            async queryDffRowsData(){
                const loading = this.$loading({
                  lock: true,
                  text: 'Loading',
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.7)',
                })
               this.defaultTemplate  = this.getDefaultTemplate();
                this.dffRowsData=[];
                let that =this;
                //获取ProductLineCode
                 let res = await queryDffRows(this.newProductLineCode);
                if(!validatenull(res)){
                    let dffFieldDatas = res.data.data;
                    if(!validatenull(dffFieldDatas)){
                       dffFieldDatas.forEach(function (obj, key, dataList) {
                           let dffObj ={};
                           if(that.language=='zh-CN'){
                                dffObj.displayName= obj.fieldNameCN;
                           }else{
                                dffObj.displayName= obj.fieldNameEN;
                           }
                            dffObj.fieldCode = obj.fieldCode;
                             that.defaultTemplate.push(dffObj);
                            //that.dffRowsData.push(dffObj);
                        });
                    }
                 }
                loading.close();
                let fieldMap = new Map();
                this.dffData.forEach(item => {
                    fieldMap.set(item.fieldCode, item);
                });

                that.defaultTemplate.forEach(item => {
                    if(!fieldMap.has(item.fieldCode)) {
                        fieldMap.set(item.fieldCode, item);
                    }
                });
                this.dffData = Array.from(fieldMap.values());
            },
            async initTRFListSetting() {
                // 初始化配置信息
                let res = await listSetting();

                if (res.data.code !== 200) {
                    return
                }
                let result = this.$lodash.get(res, 'data.data', []);
                if (result.length === 0) {//如果未从DB中获取到用户配置的表头数据，则获取默认数据
                    if(this.newProductLineCode=='AFL'){
                          this.checkList = this.defaultAflTemplate.map(item => item.fieldCode)
                    }else{
                         this.checkList = this.defaultTemplate.map(item => item.fieldCode)
                    }

                } else {
                    this.checkList = this.$lodash.orderBy(result, ['sortIndex'], ['asc']).map(item => item.fieldCode) || []
                    console.log(this.checkList);
                }
                this.dffData = this.getSearchValue(this.checkList);
                this.sortDffData();
                this.$emit('updateVal', this.resultColumns)
            },
            sortDffData() {
                this.dffData = this.$lodash.orderBy(this.dffData.map(item => {
                    if (this.checkList.includes(item.fieldCode)) {
                        item['sortIndex'] = this.checkList.indexOf(item.fieldCode) + 1
                    }
                    return item
                }), ['sortIndex'], ['asc']);
                this.resultColumns = this.$lodash.orderBy(this.resultColumns.map(item => {
                    if (this.checkList.includes(item.fieldCode)) {
                        item['sortIndex'] = this.checkList.indexOf(item.fieldCode) + 1
                    }
                    return item
                }), ['sortIndex'], ['asc'])

            },
            saveSortSetting() {
                if (this.checkList.length === 0) {
                    return false
                }
                let currentListSortArray = this.getValue(this.checkList)
                //saveSetting(currentListSortArray)
                saveSetting(currentListSortArray).then(res => {
                    console.log("TRF setting 保存结果=>" + res.data.code)
                });
                this.isShowTabSelect=false;
                this.$emit('updateVal', currentListSortArray)
            }
        }
    }
</script>

<style lang="scss" scoped>
.setting-btn {
  padding: 0;
  border: 0;
  background: transparent;
  color: #666;
  font-size: 23px;
}
</style>
