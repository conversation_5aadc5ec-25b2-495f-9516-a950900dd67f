
@charset "UTF-8";
/* CSS Document */


a {
	color: #999;
}
a:hover {
	color: #f60;
	text-decoration: none;
}
a:focus {
	color: #f60;
	text-decoration: none;
}
th, label {
	font-weight: normal;
}
button, input, select, textarea {
	margin: -1px 1px;
	border-radius: 0;
}
input:focus, input[type=text]:focus, textarea:focus {
	background: #fff!important;
	/* border: 1px solid #fff; */
	/* outline: none;
	box-shadow: none; */
}
.el-input__inner {
	border-bottom: 1px solid #1b1b1b!important;	
}
.el-select__input {
	border: 1px solid #fff!important;	
}
.container {
	width: 1220px;
}
.navbar-inverse .navbar-brand {
	color: #fff;
}
.navbar-inverse .navbar-nav>li>a {
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	color: #fff;
}
.breadcrumb {
	border-radius: 2px;
}
.table>tbody>tr.active>td, .table>tbody>tr.active>th, .table>tbody>tr>td.active, .table>tbody>tr>th.active, .table>tfoot>tr.active>td, .table>tfoot>tr.active>th, .table>tfoot>tr>td.active, .table>tfoot>tr>th.active, .table>thead>tr.active>td, .table>thead>tr.active>th, .table>thead>tr>td.active, .table>thead>tr>th.active {
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
	border-bottom: 1px solid #ddd;
}
.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
	border-top: 1px solid #ddd;
}
.input-group-btn:last-child>.btn, .input-group-btn:last-child>.btn-group {
	margin: -1px;
}
.panel {
	border: none;
	border-radius: 2px;
}
.panel-default>.panel-heading {
	/*position: relative;*/
	padding: 20px 0;
	background-color: white;
	border-radius: 2px;
	color: #333;
}
.panel-default>.panel-heading .sgs_anchor {
	/*position: absolute;
	top: -100px;*/
	padding-top: 120px;
	margin-top: -120px;
}
.panel-body {
	padding: 20px 0;
}
.panel-title {
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif;
	 */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	font-size: 3rem;
	font-weight: bold;
	text-rendering: optimizeLegibility;
	text-transform: uppercase;
}
.form-control {
	background: #f2f2f2;
	border: 1px solid #ccc;
	border-radius: 0;
	box-shadow: none;
}
.form-horizontal .form-group {
	margin-left: 0;
	margin-right: 0;
	margin-bottom: 8px;
	border: 1px solid #e4e4e4;
}
.form-horizontal .form-group .col-sm-3, .form-horizontal .form-group .col-sm-4, .form-horizontal .form-group .col-sm-5, .form-horizontal .form-group .col-sm-6, .form-horizontal .form-group .col-sm-7, .form-horizontal .form-group .col-sm-8, .form-horizontal .form-group .col-sm-9 {
	padding-right: 0;
}
.form-horizontal .control-label {
	text-align: left;
}
.pagination>li>a, .pagination>li>span {
	color: #999;
}
.pagination>li>a:hover, .pagination>li>span:hover {
	color: #f60;
}
.logo{
	padding: 20px 0;
}
.login{
	height: 600px;
	padding-top: 8%;
	background: url(../images/bg_sgs.png) center no-repeat;
	background-size: cover;
}
.classification{
	padding-top: 30px;
	background: #e4e4e4;
}
.card{
	height: 420px;
	background: #fff;
	margin-bottom: 30px;
}
.card figure{
	display: block;
	position: relative;
	width: 100%;
	height: 0;
	padding-bottom: 56.25% !important;
	background: #000;
}
.card figure img{
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
}
.card .card-content{
	padding: 0 24px 12px;
	padding: 0 1.5rem 0.75rem;
}
.card .card-content h3{
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
}
.card .card-content p{
	line-height: 1.8em;
}
.featured-numbers{
	height: 360px;
	background: url(../images/homepage-mountains.jpg) center bottom no-repeat;
	background-size: cover;
}
.featured-numbers .featured-number{
	margin-top: 35%;
	text-align: center;
}
.featured-numbers .featured-number dt{
	font-weight: normal;
}
.featured-numbers .featured-number dd{
	font-size: 4.8em;
}
.services{
	padding: 48px;
	padding: 3rem;
}
.services h2{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	font-weight: bold;
	text-align: center;
}
.services .service{
	text-align: center;
}
.services .service h3{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
}
.about{
	padding: 48px;
	padding: 3rem;
	background: url(../images/subscription-teaser-background.jpg) center no-repeat;
	background-size: cover;
}
.about .about-content{
	padding: 48px;
	padding: 3rem;
	border: 1px solid #fff;
	text-align: center;
	color: #fff;
}
.about .about-content h2{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
}
.news h2{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	font-weight: bold;
	text-align: center;
}
.news .new{
	display: table;
}
.news .new h3{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
}
.news .new .date-wrapper{
	display: table-header-group;
    width: 100% !important;
    color: #000;
}
.news .new .date-wrapper .date{
	position: relative;
}
.news .new .date-wrapper .date .date-inner{
	margin-left: 53px !important;
    display: table;
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
}
.news .new .date-wrapper .date .date-inner .date-day-month-year{
	display: table-cell;
    width: 100%;
    height: 68px!important;
    height: 4.25rem!important;
    vertical-align: bottom;
    padding-left: 13px;
    padding-left: 0.8125rem;
    border-left: 1px solid #ccc;
}
.news .new .date-wrapper .date .date-inner .date-day-month-year .date-day{
	position: absolute;
    bottom: 0;
    left: 0;
    width: 53px;
	/* font-family: "Univers Condensed","Helvetica Neue",Arial,Helvetica,sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    font-size: 52px!important;
    font-size: 3.25rem!important;
    line-height: 0.69231em !important;
}
.sgs_body_index {
	padding-top: 12%;
	background: url(../images/bg_sgs.png) no-repeat;
	background-size: cover;
}
.sgs_slogan {
	color: #fff;
}
.sgs_slogan h1 {
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	font-size: 64px;
	font-weight: bold;
	text-shadow: 0 0 20px rgba(0,0,0,0.25);
}
.sgs_slogan p {
	font-size: 18px;
}
.sgs_login {
	width: 100%;
	margin-top: 30px;
	padding: 30px;
	background: #fff;
}
.sgs_body_dashboard{
	position: relative;
	background: #e4e4e4;
	/*background: #f6f6f6 url(../images/bg_sgs.png);*/
	background-size: 100% auto;
	background-attachment: fixed;
	padding-top: 50px;
	/*font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, "sans-serif";*/
	font-family: 100%/1.5 "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	color: #666;
}
.sgs_body {
	position: relative;
	background: #e4e4e4;
	/*background: #f6f6f6 url(../images/bg_sgs.png);*/
	background-size: 100% auto;
	background-attachment: fixed;
	padding-top: 100px;
	/*font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, "sans-serif";*/
	/* font-family: 100%/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: 100%/1.5 "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	color: #666;
}
.sgs_navbar {
	background-color: #fff;
	border: none;
	border-radius: 0;
	-moz-box-shadow: 0 0 4px #aaa;
	-webkit-box-shadow: 0 0 4px #aaa;
	box-shadow: 0 0 4px #aaa;
	-ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#aaa')";
 filter: progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#aaa');
}
.sgs_logo {
	background: url(../images/<EMAIL>) no-repeat 15px 7px;
	background-size: auto 36px;
}
.sgs_logo img {
	visibility: hidden;
}
.dropdown-menu {
	border-width: 0;
}
.dropdown-menu>li>a {
	padding: 10px 20px;
	color: #666;
}
.dropdown-menu .divider {
	margin: 0;
}
.sgs_menu {
	background-repeat: no-repeat;
	background-size: auto 40px;
	background-position: center 5px;
	padding-left: 30px;
	padding-right: 30px;
	background-size: 0;
}
.sgs_menu .sgs_avatar {
	margin-top: -7px;
	margin-bottom: -7px;
	width: 32px;
	height: 32px;
	border-radius: 2px;
}
.sgs_submenu {
	background: #424242;
	padding-left: 30px;
	padding-right: 30px;
}
.sgs_submenu .navbar-header {
	margin-right: 30px;
	color: #666;
	font-weight: 300;
}
.sgs_submenu h3 {
	margin-top: 10px;
}
.sgs_menu .glyphicon {
	margin-right: 6px;
	color: #999;
}
.sgs_subnav>.active>a, .sgs_subnav>.active>a:focus, .sgs_subnav>.active>a:hover {
	background-color: #595959;
	color: #f60;
	border-bottom: 2px solid #f60;
}
.sgs_subnav {
}
.sgs_subnav>li>a {
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	padding-top: 15px;
	padding-bottom: 15px;
	line-height: 20px;
	border-radius: 0;
	color: #fff;
	border-bottom: 2px solid #424242;
	font-size: 16px;
}
.sgs_subnav>li>a:focus, .sgs_subnav>li>a:hover {
	background-color: #595959;
	color: #f60;
}
.navbar-inverse .sgs_subnav>.active>a, .navbar-inverse .sgs_subnav>.active>a:focus, .navbar-inverse .sgs_subnav>.active>a:hover {
	background-color: #fff;
	color: #f60;
	border-bottom: 2px solid #f60;
}
.sgs_subnav {
}
.navbar-inverse .sgs_subnav>li>a {
	padding-top: 15px;
	padding-bottom: 15px;
	line-height: 20px;
	border-radius: 0;
	color: #242424;
}
.navbar-inverse .sgs_subnav>li>a:focus, .navbar-inverse .sgs_subnav>li>a:hover {
	background-color: #fff;
	color: #f60;
}
.sgs_pagetitle {
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	margin-top: 15px;
	margin-bottom: 15px;
	line-height: 20px;
	color: #fff;
}
.sgs_pagetitle a{
	color: #fff;
	text-decoration: none;
}
.sgs_wrap {
}
.sgs_wrap .container-fluid {
	background: #fff;
	border-radius: 2px;
	margin: 30px;
	padding: 30px;
}
.sgs_subwrap {
}
.sgs_subwrap .container {
	background: #fff;
	border-radius: 2px;
	margin: 30px auto;
	padding: 30px;
}
.sgs_search_form {
	margin-bottom: 20px;
}
.sgs_btn {
	background: #f60;
	border: 1px solid #f96f3a;
	border-radius: 2px;
	color: #fff;
}
.sgs_btn:focus, .sgs_btn:hover, .sgs_btn:active {
	background: #ff8533;
	border: 1px solid #f60;
	color: #fff;
}
.sgs_btn_danger {
	border: 1px solid #ff5c3d;
	border-radius: 2px;
	color: #ff5c3d;
}
.sgs_btn_danger:focus, .sgs_btn_danger:hover, .sgs_btn_danger:active {
	background: #ff5c3d;
	color: #fff;
}
.sgs_btn_black {
	background: #242424;
	border: 1px solid #242424;
	border-radius: 2px;
	color: #fff;
}
.sgs_btn_black:focus, .sgs_btn_black:hover, .sgs_btn_black:active {
	background: #242424;
	color: #fff;
}
.sgs_process>li {
	text-align: center;
}
.sgs_process>li.active .glyphicon {
}
.sgs_process .glyphicon {
	margin-bottom: 10px;
	padding: 10px;
	border-radius: 32px;
}
.sgs_process p.time {
}
.sgs_process>li.active {
}
.sgs_process>li.done {
}
.sgs_process>li.done .glyphicon {
	background: #66bc6a;
	border-color: #66bc6a;
	color: #fff;
}
.sgs_portlet .form-control {
	border-top: none !important;
	border-left: none !important;
	border-right: none !important;
	border-bottom-color: #1b1b1b !important;
	border-radius: 0;
	box-shadow: none !important;
}
.sgs_portlet .title {
}
.sgs_portlet .title .caption {
	/* font-family: "Univers Light", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	float: left;
	font-weight: normal;
	color: #424242;
}
.sgs_portlet .title .tools {
	float: right;
}
.sgs_table {
}
.sgs_table>tbody>tr>td, .sgs_table>tbody>tr>th, .sgs_table>tfoot>tr>td, .sgs_table>tfoot>tr>th, .sgs_table>thead>tr>td, .sgs_table>thead>tr>th {
	vertical-align: middle;
	text-align: center;
}
.sgs_table_settlement {
}
.sgs_table_settlement>tbody>tr>td, .sgs_table_settlement>tbody>tr>th, .sgs_table_settlement>tfoot>tr>td, .sgs_table_settlement>tfoot>tr>th, .sgs_table_settlement>thead>tr>td, .sgs_table_settlement>thead>tr>th {
	vertical-align: middle;
}
.sgs_pagination {
	float: right;
	margin-top: 0;
}
.sgs_footer {
	margin-top: -30px;
	margin-bottom: 50px;
}
.sgs_copyright {
	height: 40px;
	line-height: 40px;
	text-align: center;
}
.sgs_bottom {
	background-color: #fff;
}
.sgs_glyphicon_approval {
	width: 16px;
	height: 16px;
	background: url(../images/Approval-success.png);
	background-size: 16px;
}
.sgs_glyphicon_reject {
	width: 16px;
	height: 16px;
	background: url(../images/Approval-fail.png);
	background-size: 16px;
}
.sgs_glyphicon_commercial_approval {
	width: 16px;
	height: 16px;
	background: url(../images/Approval.png);
	background-size: 16px;
}
.sgs_radio_group {
	padding-bottom: 7px;
}
.sgs_checkbox_group {
	padding-top: 7px;
	padding-bottom: 7px;
}
.sgs_modal_material {
	position: fixed;
	bottom: 0;
	width: 100%;
	margin: 0;
}
.sgs_modal_material .modal-content {
	border: none;
	border-radius: 0;
}
.sgs_modal_material .modal-content .modal-footer {
	text-align: center;
}
.fix-modal-open {
	overflow: initial;
}
.sgs_media {
	margin-top: 0;
	padding-top: 15px;
	border-bottom: 1px dashed #ddd;
}
.sgs_text_required {
	margin-left: 5px;
	color: #f60;
}
.select2-container--default .select2-selection--multiple {
	background: #f2f2f2;
	margin: -1px -1px -1px 1px;
	border: 1px solid #ccc;
	border-radius: 0;
}
.select2-container--default.select2-container--focus .select2-selection--multiple {
	background: #e4e4e4;
	border: 1px solid #ccc;
}
.file-preview {
	border-radius: 0;
}
/*下拉框*/
.select, .selectNext {
	cursor: pointer;
	position: relative;
	width: 180px;
	height: 40px;
	border: 1px solid #ddd;
	border-radius: 2px;
	line-height: 36px;
	padding-left: 12px;
	text-align: left;
}
.xl-icon, .xl-iconNext {
	position: absolute;
	width: 20px;
	height: 20px;
	background-image: url("../images/xl-icon_03.png");
	background-position: 4px 0px;
	background-repeat: no-repeat;
	right: 6px;
	top: 10px;
}
.select:hover, .selectNext:hover {
	background-color: #f2f2f2;
}
.sq-icon {
	background-position: 4px -33px;
}
/*--下拉选择列表样式--*/
.selectList, .selectListNext {
	z-index: 999;
	display: none;
	width: auto;
	border: 1px solid #ddd;
	border-radius: 2px;
	position: absolute;
	top: 45px;
	left: 0;
}
.listA, .listANext {
	width: 100%;
	height: 40px;
	line-height: 40px;
	font-size: 14px;
	color: #545454;
	text-align: left;
	display: block;
	background-color: #fff;
	padding-left: 12px;
	padding-right: 12px;
}
.listA:hover, .listANext:hover {
	background-color: #f5f5f5;
	color: #666;
}
.listA:first-child, .listANext:first-child {
	border-radius: 2px 2px 0 0;
}
.listA:last-child, .listANext:last-child {
	border-radius: 0 0 2px 2px;
}
/*-------------------------------------------------------------------------------------------------*/
.frameTriangle {
	width: 10px;
	height: 8px;
	background-image: url(../images/xl-icon_03.png);
	background-position: 0 -38px;
	background-repeat: no-repeat;
	display: block;
	position: absolute;
	top: -8px;
	left: 85px;
	background-color: #fff;
}
.dropDown {
	position: relative;
}
.selectListNext {
	top: 56px;
}
.sgs_trf_list .panel-title{
	font-size: inherit;
	font-weight: normal;
}
.sgs_trf_list .table{
	margin-bottom: 0;
}
.sgs_trf_list a{
	text-decoration: none;
}
ul.sgs_email_list{
	margin-bottom: 0;
	padding-top: 7px;
	padding-bottom: 7px;
}
a.sgs_text_link{
	color: #f60;
	text-decoration: none;
}
a.sgs_text_link:hover{
	color: #ff8533;
}
.sgs_form{

}
.sgs_form .left, .sgs_form .right{
	float: left;
	padding-left: 15px;
}
.sgs_form .left{
	width: 234.58px;
}
.sgs_textarea {
	width: 100%;
	background: #f2f2f2;
	border:1px solid #ccc;
	border-radius: 0;
	padding: 6px 12px;
}
.sgs_textarea:focus {
	background: #e4e4e4;
	border: 1px solid #ccc;
	outline: none;
	box-shadow: none;
}
.sgs_trf_ongoing{
	position: relative;
	height: 200px;
}
.sgs_trf_ongoing .bg{
	width: 100%;
	margin: 0;
	padding: 0;
	padding-top: 80px;
}
.sgs_trf_ongoing .bg li{
	float: left;
	display: inline-block;
	list-style: none;
	width: 10%;
	height: 3px;
	border-top: 2px solid #ddd;
}
.sgs_trf_ongoing .bg li.first, .sgs_trf_ongoing .bg li.last{
	border-top: none;
}
.sgs_trf_ongoing .step{
	position: absolute;
	top: 50px;
	width: 100%;
	margin: 0;
	padding: 0;
}
.sgs_trf_ongoing .step li{
	float: left;
	display: inline-block;
	list-style: none;
	width: 20%;
	text-align: center;
}
.sgs_trf_ongoing .step li .glyphicon{
	background: #fff;
	margin-bottom: 10px;
	padding: 16px;
	border: 2px solid #f60;
	border-radius: 50%;
	font-size: 24px;
	color: #f60;
}
.sgs_trf_ongoing .step li:hover .glyphicon{
	background: #f60;
	color: #fff;
}
.sgs_trf_ongoing .step li p{
	margin: 0;
}
.sgs_data{
	font-size: 28px;
	color: #424242;
}
.ui-rangeSlider-arrow, .ui-rangeSlider-container{
	height: 10px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
}
.ui-rangeSlider-noArrow .ui-rangeSlider-container{
	-moz-border-radius: 2px;
	border-radius: 2px;
	border-left: 1px solid #ddd;
	border-right: 1px solid #ddd;
}
.ui-rangeSlider-arrow, .ui-rangeSlider-noArrow .ui-rangeSlider-container, .ui-rangeSlider-withArrows .ui-rangeSlider-container{
	-webkit-box-shadow: inset 0 4px 6px -2px RGBA(0,0,0,.2);
	-moz-box-shadow: inset 0 4px 6px -2px RGBA(0,0,0,.2);
	box-shadow: inset 0 4px 6px -2px RGBA(0,0,0,.2);
}
.ui-rangeSlider-bar{
	height: 9px;
	background: #bbb;
	border-radius: 2px;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}
.ui-rangeSlider-arrow, .ui-rangeSlider-container, .ui-rangeSlider-label{
	background: #eee;
}
.ui-rangeSlider-leftArrow{
	border-left: 1px solid #ddd;
}
.ui-rangeSlider-label{
	color: #999;
	-webkit-box-shadow: 0 1px 0 #ddd;
	-moz-box-shadow: 0 1px 0 #ddd;
	box-shadow: 0 1px 0 #ddd;
}
.ui-rangeSlider-label-inner{
	border-top: 10px solid #eee;
}
ul.sgs_test_result{
	margin: 0;
	padding: 0;
}
ul.sgs_test_result li{
	list-style: none;
	margin-bottom: 10px;
	padding: 20px;
	border: 1px solid #ddd;
}
ul.sgs_test_result li .glyphicon{
	background: #424242;
	padding: 16px;
	font-size: 24px;
	color: #fff;
}
ul.sgs_test_result li p{
	margin-bottom: 0;
}
ul.sgs_test_result li.passed .glyphicon{
	background: #20b426;
}
ul.sgs_test_result li.passed p.sgs_data{
	color: #20b426;
}
ul.sgs_test_result li.failed .glyphicon{
	background: #d72926;
}
ul.sgs_test_result li.failed p.sgs_data{
	color: #d72926;
}

ul.sgs_test_result li.seeresult .glyphicon{
  background: #adadad;
}
ul.sgs_test_result li.seeresult p.sgs_data{
  color: #adadad;
}

ul.sgs_test_result li.total p.sgs_data{
	color: #424242;
}
.box h2{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    margin-bottom: 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #ddd;
	color: #424242;
}
ul.sgs_to_do_list{
	margin: 0;
	padding: 0;
  padding-top: 8px;
}
ul.sgs_to_do_list li{
	list-style: none;
	line-height: 2.4em;
    border-bottom: 1px dashed #ddd;
}
ul.sgs_to_do_list li .date{
	color: #ccc;
}
.form-control.sgs_search_input{
	width: 300px;
}
.sgs_prompt_wrap{
	position: relative;
}
.sgs_prompt{
	position: absolute;
	top: 36px;
	left: 0;
	z-index: 999;
	width: 500px;
	background: #fff url(../images/icon_point_bg.png) center 55px no-repeat;
	border: 1px solid #ddd;
	border-radius: 2px;
	margin: 0;
	padding: 0;
	-moz-box-shadow: 0 0 4px #ddd;
    box-shadow: 0 0 4px #ddd;
	display: none;
}
.sgs_prompt_wrap:hover .sgs_prompt{
	display: block;
}
.sgs_prompt li{
	width: 19%;
	height: 65px;
	padding-left: 0;
	padding-right: 0;
	padding-top: 10px;
	text-align: center;
}
.sgs_table_header{
	/* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
	font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
	font-size: 16px;
	font-weight: lighter;
}
.sgs_search_all{
}
.sgs_search_all .glyphicon{
	margin-right: 0;
	color: #fff;
}
.sgs_help_text{
    margin: 10px auto;
	padding: 10px 20px;
}
.sgs_trf_templates{
	height: 112px;
	overflow-y: auto;
	margin-bottom: 10px;
}
.sgs_trf_templates table{
	margin-bottom: 0;
}
.sgs_table_fixed{
	width: 100%;
	overflow-x: auto;
	overflow-y: hidden;
}
.sgs_table_fixed .sgs_table{
	width: auto;
}
.sgs_table_fixed .sgs_table th .form-control{
	min-width: 150px;
}
.sgs_th_custom .dropdown-menu{
	min-width: 180px;
}
.sgs_th_custom label{
	display: block;
	margin: 10px;
}
.sgs_th_custom label input[type=checkbox]{
	margin-right: 6px;
}
.has-error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  left: 0;
}
/*test package 样式*/
.c-index-hot, .c-index-hot1 {
  background-color: #FF6600;
}

.c-index {
  display: inline-block;
  padding: 1px 0;
  color: #fff;
  width: 14px;
  line-height: 100%;
  font-size: 12px;
  text-align: center;
  background-color: #FF6600;
}
.c-gap-icon-right-small {
  margin-right: 5px;
}
