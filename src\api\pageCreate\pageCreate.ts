import request from '../request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const pageCreateSave = (data: Object) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/save',
      method: 'post',
      data
    })
}
export const pageCreatePublish = (data: Object) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledge/publish',
    method: 'post',
    data
  })
}
export const pageCreatePreview = (data: Object) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledge/preview',
    method: 'post',
    data
  })
}
export const pageCreateUpdate = (data: Object) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/update',
      method: 'post',
      data
    })
}
