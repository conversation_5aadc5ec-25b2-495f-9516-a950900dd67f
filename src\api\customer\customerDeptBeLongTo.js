import request from '@/router/axios';


export const addDeptBeLongTos = (form) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/adds',
        method: 'post',
        data: form
    })
}
export const updateDeptBeLongToStatus = (form) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/updateDeptBeLongToStatus',
        method: 'post',
        data: form
    })
}
export const getDeptBeLongToList = (params) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/list-noPage',
        method: 'get',
        params: {
            ...params,
        }
    })
}



export const beLongTodetail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const removeBeLongTo = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const getDeptBeLongTos = (deptmentId) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/department-id',
        method: 'get',
        params: {
            deptmentId,
        }
    })
}
export const getDeptBeLongTosByUser = () => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/beLongs-user',
        method: 'get',
        params: {

        }
    })
}
export const getDeptBeLongTosByTemplateId= (templateId) => {
    return request({
        url: '/api/sgs-mart/customer/deptBeLong/beLongs-templateId',
        method: 'get',
        params: {
            templateId
        }
    })
}