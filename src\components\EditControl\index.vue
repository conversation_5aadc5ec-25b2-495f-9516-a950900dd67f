<template>
  <el-icon :size="18" class="icon-primary" v-if="!isEditing">
    <Edit @click.stop="handleEdit" />
  </el-icon>
  <el-icon :size="18" class="icon-primary" v-if="isEditing">
    <Check @click.stop="handleSave" />
  </el-icon>
  <el-icon :size="18" class="icon-close" v-if="isEditing">
    <Close v-if="isEditing" @click.stop="handleCancel" />
  </el-icon>
</template>

<script setup>
import { Edit, Check, Close } from '@element-plus/icons-vue'

const props = defineProps({
  isEditing: Boolean,
})

const emit = defineEmits(['edit', 'save', 'cancel'])

const handleEdit = () => emit('edit')
const handleSave = () => emit('save')
const handleCancel = () => emit('cancel')
</script>

<style scoped>
.icon-primary {
  color: var(--el-color-primary);
  cursor: pointer;
}
.icon-close {
  cursor: pointer;
}
</style>
