<template>
  <div class="chart">
    <div :id="eleId" :style="style"></div>
  </div>
</template>

<script>
// import echarts from 'echarts'

export default {
  name: 'Chart',
  props: {
    eleId: {
      type: String,
      default: new Date().getTime().toString()
    },
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '400px'
    },
    options: {
      type: Object,
      default: {
        series: [],
      }
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  computed: {
    style() {
      return {
        height: this.height,
        width: this.width
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(document.querySelector('#'+this.eleId))
      this.myChart.setOption(this.options, true)

      this.myChart.on("click", function(param){
        console.log(param.data);
      });

      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
    })
  },
  watch: {
    options: {
      deep: true,
      handler: function() {
        console.log('watch::', this.options)
        this.myChart.setOption(this.options, true)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {

}
</style>