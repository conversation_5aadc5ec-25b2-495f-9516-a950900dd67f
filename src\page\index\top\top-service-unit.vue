<template>
   <div id="1" class="sgs-service-unit">
     <el-dropdown class="question" v-if="showServiceUnitFlag" trigger="hover" >
        <span class="el-dropdown-link top-icon">
            <i :class="iconVal" style="color: #ffffff;font-size: 25px;"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
         <el-dropdown-item
                 v-for="(serviceUnit, index) in serviceUnits"
                 :key="'service_unit_dropdown_'+index"
                 @click.native="handleCommand(serviceUnit.productLineCode,serviceUnit.serviceDomain,serviceUnit)"
                :class="selServiceUnit==serviceUnit.serviceUnitCode?'default_serviceUnit_class':'normal_class'"
                :command="serviceUnit.productLineCode" >
             {{serviceUnit.serviceUnitName}}
             <el-button round
                    v-if="defaultServiceUnit!=serviceUnit.serviceUnitCode"
                    @click.stop="setDefault(serviceUnit)"
                    size="mini" style="margin-left: 12px;z-index: 99999">
                {{$t('common.default')}}
            </el-button>
         </el-dropdown-item>
         <el-dropdown-item  command="all"
                            @click.native="handleCommand('all')"
                            :class="!selServiceUnit?'default_serviceUnit_class':'normal_class'"
         >
             {{$t('common.all')}}
             <el-button round
                        v-if="defaultServiceUnit!='all'"
                        @click.stop="setDefault('all')"
                        size="mini" style="margin-left: 12px;z-index: 99999">
                 {{$t('common.default')}}
             </el-button>
         </el-dropdown-item>
       </el-dropdown-menu>
     </el-dropdown>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {inspectionUrl} from '@/config/env';
    import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";
    import {updateDefaultServiceUnit} from "@/api/customer/account";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {getToken} from "@/util/auth"
    export default {
        name: "top-service-unit",
      data() {
        return {
          iconVal:'icon-all icongary_ALL',
          buIconList:[
            {
              productLineCode:'SL',
              icon:'icon-all iconxiebao1'
            },
            {
              productLineCode:'HL',
              icon:'icon-all iconshuibei'
            },
              {
                  productLineCode:'EE',
                  icon:'el-icon-watch'
              },
              {
                  productLineCode:'CPCH',
                  icon:'el-icon-table-lamp'
              },
            {
              productLineCode:'AFL',
              icon:'icon-all iconpingguo'
            },{
              productLineCode:'all',
              icon:'icon-all icongary_ALL'
            }
          ],
          serviceUnits:[],
          serviceUnitDataItems:[],
          serviceUnitName:'测试项目',
          showServiceUnitFlag:false,
          defaultServiceUnit:'',
          selServiceUnit:''
        }
      },
        created() {
         let isSgs=this.haseRole("SGSUserRole", "SgsAdmin");
          if(isSgs){
            return false;
          }
          if(objectIsNull(this.userInfo.selServiceDomain)){
            this.userInfo.selServiceDomain=this.userInfo.defaultServiceDomain;
          }
          this.defaultServiceUnit=this.userInfo.defaultServiceUnit || 'all';
          if(objectIsNull(this.userInfo.selServiceUnitCode)){
            this.selServiceUnit=this.userInfo.defaultServiceUnit;
          }else{
            this.selServiceUnit=this.userInfo.selServiceUnitCode;
          }
          this.initData();
        },
        mounted() {
        },
        computed: {
            ...mapGetters(["language","userInfo","dimensions"]),
            role() {
              return {
              isSGS: this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser")
            }
          }
        },
        watch: {
          'language': function () {
            this.onloadServiceUnit();
          }
        },
        methods: {
          haseRole(type, role) {
              if (validatenull(type) || validatenull(role)) {
                  return false;
              }
              if (validatenull(this.dimensions)) {
                  return false;
              } else {
                  if (this.dimensions.hasOwnProperty(type)) {
                      if (this.dimensions[type].indexOf(role) >= 0) {
                          return true;
                      } else {
                          return false;
                      }
                  } else {
                      return false;
                  }
              }
          },
          async setDefault(serviceUnit){
              console.log("setDefault",serviceUnit)
              if(serviceUnit=='all'){
                  serviceUnit = {
                      serviceUnitCode:'',
                      productLineCode:'all',
                      serviceDomain:'',
                  }
              }
            //调用接口，设置默认
            await  updateDefaultServiceUnit(serviceUnit.serviceUnitCode).then(res => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: this.$t('api.success')
                });
                this.userInfo.productLineCode=serviceUnit.productLineCode;
                this.userInfo.defaultProductLineCode=serviceUnit.productLineCode;
                this.userInfo.defaultServiceDomain=serviceUnit.serviceDomain;
                this.userInfo.selServiceDomain=serviceUnit.serviceDomain;
                this.userInfo.defaultServiceUnit=serviceUnit.serviceUnitCode;
                this.userInfo.selServiceUnitCode=serviceUnit.serviceUnitCode;
                this.$store.commit('SET_USERIFNO', this.userInfo)
                this.defaultServiceUnit=serviceUnit.serviceUnitCode;
                debugger;
                this.selServiceUnit=serviceUnit.serviceUnitCode;
                //判断选择的是否为inspection
                if(serviceUnit.serviceDomain==='Inspection'){
                  //跳转至inspection界面
                  window.open(inspectionUrl+"#/sso?redirect=/booking/Booking&token="+getToken());
                  return false;
                }
                this.reload();
              } else {
                this.$message({
                  type: "error",
                  message: res.data.msg
                });
              }
            });

          },
         async initData(){
            //查询所有的
            await  this.queryServiceType();
            //查询当前用户的Service unit
            await this.queryUserServiceUnit();
          },
          async onloadServiceUnit(){
            await  this.initData();
            //重新加载选择的ServiceUnit名称
            await this.showServiceUnitName(this.userInfo.productLineCode || 'all');
          },
          handleCommand(bu,serviceDomain,serviceUnitObj){
            debugger;
            this.userInfo.selServiceDomain=serviceDomain;
            if(!objectIsNull(serviceUnitObj)){
              this.userInfo.selServiceUnitCode=serviceUnitObj.serviceUnitCode;
              this.selServiceUnit=serviceUnitObj.serviceUnitCode;
            }else{
              this.selServiceUnit='all';
            }

            //TODO 240526不上prod
            //判断选择的是否为inspection
            if(serviceDomain==='Inspection'){
              //跳转至inspection界面
              window.open(inspectionUrl+"#/sso?redirect=/booking/Booking&token="+getToken());
              return false;
            }
            this.showServiceUnitName(bu);
            //将选择的productLineCode更新至Session中
            this.userInfo.productLineCode=bu;
            this.$store.commit('SET_USERIFNO', this.userInfo);
            this.reload();
          },
          reload(){
            //重新加载页面
            location.reload()
          },
          async showServiceUnitName(value){
            this.iconVal = this.getBuImgUrl(value);
            if(value=='all'){
              this.serviceUnitName=this.$t('common.all');
            }else{
              let serviceUnitObj = this.serviceUnits.find(serviceUnit => {return serviceUnit.productLineCode == value})
              //获取选择的名称
              if(!objectIsNull(serviceUnitObj)){
                this.serviceUnitName=serviceUnitObj.serviceUnitName;
              }
            }
          },
          getBuImgUrl(bu){
            debugger;
            let buIconObj=  this.buIconList.find(buIcon => {return buIcon.productLineCode == bu});
            if(objectIsNull(buIconObj)) return '';
            return buIconObj.icon;
          },
          async queryUserServiceUnit(language){//从session中获取
            debugger;
            //将user中的serviceUnits转成SGSMart格式
            if(!objectIsNull(this.userInfo.serviceUnits)){

              //let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits, this.userInfo.serviceUnits);
              this.serviceUnitDataItems = this.userInfo.serviceUnits.split(',');
              if(!objectIsNull(this.serviceUnitDataItems)){
                let newServiceUnits=[];
                for (let serviceUnitObj of this.serviceUnits) {
                  if( this.serviceUnitDataItems.includes(serviceUnitObj.serviceUnitCode)){
                    newServiceUnits.push(serviceUnitObj)
                  }
                }
                //去除other的Serviceunit
                newServiceUnits = newServiceUnits.filter(item => item.productLineCode != 'other');
                this.serviceUnits=newServiceUnits;
                if(newServiceUnits.length>0){
                  this.showServiceUnitFlag=true;
                }
              }
            }

            let productLineCode =  this.userInfo.productLineCode || 'all';
            this.showServiceUnitName(productLineCode);
          },
          async queryServiceType() {
            let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
            this.serviceUnits=serviceUnitsDatas;
            console.log("首页",this.serviceUnits);
          },

        }
    };
</script>

<style lang="scss" scoped>
  .default_serviceUnit_class{
      font-weight: bold !important;
    }
.sgs-service-unit{
    .top-icon {
      font-weight: 400;
      color: #ffffff;
    }
    .task {
      margin-bottom: 0;
      li {
        padding: 10px;
        cursor: pointer;
        &:hover {
          background: #f2f2f2;
        }
      }
    }
    .question {
      margin-top:6px;
      /*margin-left: 5px;*/
      margin-bottom:5px;
      color: #f60;
      img {
        display: block;
      }
    }
    .top-bar__img {
      margin: 0 8px 0 0;
    }
}
</style>
