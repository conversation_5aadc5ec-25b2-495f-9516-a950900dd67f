/**
 * vue-router v3.0.1
 * (c) 2017 Evan You
 * @license MIT
 */
! function(t, e) { "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : t.VueRouter = e() }(this, function() {
    "use strict";

    function t(t, e) {}

    function e(t) { return Object.prototype.toString.call(t).indexOf("Error") > -1 }

    function r(t, e) {
        switch (typeof e) {
            case "undefined":
                return;
            case "object":
                return e;
            case "function":
                return e(t);
            case "boolean":
                return e ? t.params : void 0
        }
    }

    function n(t, e) { for (var r in e) t[r] = e[r]; return t }

    function o(t, e, r) { void 0 === e && (e = {}); var n, o = r || i; try { n = o(t || "") } catch (t) { n = {} } for (var a in e) n[a] = e[a]; return n }

    function i(t) {
        var e = {};
        return (t = t.trim().replace(/^(\?|#|&)/, "")) ? (t.split("&").forEach(function(t) {
            var r = t.replace(/\+/g, " ").split("="),
                n = Ut(r.shift()),
                o = r.length > 0 ? Ut(r.join("=")) : null;
            void 0 === e[n] ? e[n] = o : Array.isArray(e[n]) ? e[n].push(o) : e[n] = [e[n], o]
        }), e) : e
    }

    function a(t) { var e = t ? Object.keys(t).map(function(e) { var r = t[e]; if (void 0 === r) return ""; if (null === r) return Pt(e); if (Array.isArray(r)) { var n = []; return r.forEach(function(t) { void 0 !== t && (null === t ? n.push(Pt(e)) : n.push(Pt(e) + "=" + Pt(t))) }), n.join("&") } return Pt(e) + "=" + Pt(r) }).filter(function(t) { return t.length > 0 }).join("&") : null; return e ? "?" + e : "" }

    function u(t, e, r, n) {
        var o = n && n.options.stringifyQuery,
            i = e.query || {};
        try { i = c(i) } catch (t) {}
        var a = { name: e.name || t && t.name, meta: t && t.meta || {}, path: e.path || "/", hash: e.hash || "", query: i, params: e.params || {}, fullPath: p(e, o), matched: t ? s(t) : [] };
        return r && (a.redirectedFrom = p(r, o)), Object.freeze(a)
    }

    function c(t) { if (Array.isArray(t)) return t.map(c); if (t && "object" == typeof t) { var e = {}; for (var r in t) e[r] = c(t[r]); return e } return t }

    function s(t) { for (var e = []; t;) e.unshift(t), t = t.parent; return e }

    function p(t, e) {
        var r = t.path,
            n = t.query;
        void 0 === n && (n = {});
        var o = t.hash;
        void 0 === o && (o = "");
        var i = e || a;
        return (r || "/") + i(n) + o
    }

    function f(t, e) { return e === Ht ? t === e : !!e && (t.path && e.path ? t.path.replace(Mt, "") === e.path.replace(Mt, "") && t.hash === e.hash && h(t.query, e.query) : !(!t.name || !e.name) && (t.name === e.name && t.hash === e.hash && h(t.query, e.query) && h(t.params, e.params))) }

    function h(t, e) {
        if (void 0 === t && (t = {}), void 0 === e && (e = {}), !t || !e) return t === e;
        var r = Object.keys(t),
            n = Object.keys(e);
        return r.length === n.length && r.every(function(r) {
            var n = t[r],
                o = e[r];
            return "object" == typeof n && "object" == typeof o ? h(n, o) : String(n) === String(o)
        })
    }

    function l(t, e) { return 0 === t.path.replace(Mt, "/").indexOf(e.path.replace(Mt, "/")) && (!e.hash || t.hash === e.hash) && d(t.query, e.query) }

    function d(t, e) {
        for (var r in e)
            if (!(r in t)) return !1;
        return !0
    }

    function y(t) { if (!(t.metaKey || t.altKey || t.ctrlKey || t.shiftKey || t.defaultPrevented || void 0 !== t.button && 0 !== t.button)) { if (t.currentTarget && t.currentTarget.getAttribute) { var e = t.currentTarget.getAttribute("target"); if (/\b_blank\b/i.test(e)) return } return t.preventDefault && t.preventDefault(), !0 } }

    function v(t) {
        if (t)
            for (var e, r = 0; r < t.length; r++) { if ("a" === (e = t[r]).tag) return e; if (e.children && (e = v(e.children))) return e }
    }

    function m(t) {
        if (!m.installed || Tt !== t) {
            m.installed = !0, Tt = t;
            var e = function(t) { return void 0 !== t },
                r = function(t, r) {
                    var n = t.$options._parentVnode;
                    e(n) && e(n = n.data) && e(n = n.registerRouteInstance) && n(t, r)
                };
            t.mixin({ beforeCreate: function() { e(this.$options.router) ? (this._routerRoot = this, this._router = this.$options.router, this._router.init(this), t.util.defineReactive(this, "_route", this._router.history.current)) : this._routerRoot = this.$parent && this.$parent._routerRoot || this, r(this, this) }, destroyed: function() { r(this) } }), Object.defineProperty(t.prototype, "$router", { get: function() { return this._routerRoot._router } }), Object.defineProperty(t.prototype, "$route", { get: function() { return this._routerRoot._route } }), t.component("router-view", St), t.component("router-link", zt);
            var n = t.config.optionMergeStrategies;
            n.beforeRouteEnter = n.beforeRouteLeave = n.beforeRouteUpdate = n.created
        }
    }

    function g(t, e, r) {
        var n = t.charAt(0);
        if ("/" === n) return t;
        if ("?" === n || "#" === n) return e + t;
        var o = e.split("/");
        r && o[o.length - 1] || o.pop();
        for (var i = t.replace(/^\//, "").split("/"), a = 0; a < i.length; a++) { var u = i[a]; ".." === u ? o.pop() : "." !== u && o.push(u) }
        return "" !== o[0] && o.unshift(""), o.join("/")
    }

    function b(t) {
        var e = "",
            r = "",
            n = t.indexOf("#");
        n >= 0 && (e = t.slice(n), t = t.slice(0, n));
        var o = t.indexOf("?");
        return o >= 0 && (r = t.slice(o + 1), t = t.slice(0, o)), { path: t, query: r, hash: e }
    }

    function w(t) { return t.replace(/\/\//g, "/") }

    function x(t, e) {
        for (var r, n = [], o = 0, i = 0, a = "", u = e && e.delimiter || "/"; null != (r = Qt.exec(t));) {
            var c = r[0],
                s = r[1],
                p = r.index;
            if (a += t.slice(i, p), i = p + c.length, s) a += s[1];
            else {
                var f = t[i],
                    h = r[2],
                    l = r[3],
                    d = r[4],
                    y = r[5],
                    v = r[6],
                    m = r[7];
                a && (n.push(a), a = "");
                var g = null != h && null != f && f !== h,
                    b = "+" === v || "*" === v,
                    w = "?" === v || "*" === v,
                    x = r[2] || u,
                    k = d || y;
                n.push({ name: l || o++, prefix: h || "", delimiter: x, optional: w, repeat: b, partial: g, asterisk: !!m, pattern: k ? C(k) : m ? ".*" : "[^" + O(x) + "]+?" })
            }
        }
        return i < t.length && (a += t.substr(i)), a && n.push(a), n
    }

    function k(t) { return encodeURI(t).replace(/[\/?#]/g, function(t) { return "%" + t.charCodeAt(0).toString(16).toUpperCase() }) }

    function R(t) { return encodeURI(t).replace(/[?#]/g, function(t) { return "%" + t.charCodeAt(0).toString(16).toUpperCase() }) }

    function E(t) {
        for (var e = new Array(t.length), r = 0; r < t.length; r++) "object" == typeof t[r] && (e[r] = new RegExp("^(?:" + t[r].pattern + ")$"));
        return function(r, n) {
            for (var o = "", i = r || {}, a = (n || {}).pretty ? k : encodeURIComponent, u = 0; u < t.length; u++) {
                var c = t[u];
                if ("string" != typeof c) {
                    var s, p = i[c.name];
                    if (null == p) { if (c.optional) { c.partial && (o += c.prefix); continue } throw new TypeError('Expected "' + c.name + '" to be defined') }
                    if (Ft(p)) {
                        if (!c.repeat) throw new TypeError('Expected "' + c.name + '" to not repeat, but received `' + JSON.stringify(p) + "`");
                        if (0 === p.length) { if (c.optional) continue; throw new TypeError('Expected "' + c.name + '" to not be empty') }
                        for (var f = 0; f < p.length; f++) {
                            if (s = a(p[f]), !e[u].test(s)) throw new TypeError('Expected all "' + c.name + '" to match "' + c.pattern + '", but received `' + JSON.stringify(s) + "`");
                            o += (0 === f ? c.prefix : c.delimiter) + s
                        }
                    } else {
                        if (s = c.asterisk ? R(p) : a(p), !e[u].test(s)) throw new TypeError('Expected "' + c.name + '" to match "' + c.pattern + '", but received "' + s + '"');
                        o += c.prefix + s
                    }
                } else o += c
            }
            return o
        }
    }

    function O(t) { return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g, "\\$1") }

    function C(t) { return t.replace(/([=!:$\/()])/g, "\\$1") }

    function j(t, e) { return t.keys = e, t }

    function A(t) { return t.sensitive ? "" : "i" }

    function _(t, e) {
        var r = t.source.match(/\((?!\?)/g);
        if (r)
            for (var n = 0; n < r.length; n++) e.push({ name: n, prefix: null, delimiter: null, optional: !1, repeat: !1, partial: !1, asterisk: !1, pattern: null });
        return j(t, e)
    }

    function T(t, e, r) { for (var n = [], o = 0; o < t.length; o++) n.push(q(t[o], e, r).source); return j(new RegExp("(?:" + n.join("|") + ")", A(r)), e) }

    function S(t, e, r) { return $(x(t, r), e, r) }

    function $(t, e, r) {
        Ft(e) || (r = e || r, e = []);
        for (var n = (r = r || {}).strict, o = !1 !== r.end, i = "", a = 0; a < t.length; a++) {
            var u = t[a];
            if ("string" == typeof u) i += O(u);
            else {
                var c = O(u.prefix),
                    s = "(?:" + u.pattern + ")";
                e.push(u), u.repeat && (s += "(?:" + c + s + ")*"), i += s = u.optional ? u.partial ? c + "(" + s + ")?" : "(?:" + c + "(" + s + "))?" : c + "(" + s + ")"
            }
        }
        var p = O(r.delimiter || "/"),
            f = i.slice(-p.length) === p;
        return n || (i = (f ? i.slice(0, -p.length) : i) + "(?:" + p + "(?=$))?"), i += o ? "$" : n && f ? "" : "(?=" + p + "|$)", j(new RegExp("^" + i, A(r)), e)
    }

    function q(t, e, r) { return Ft(e) || (r = e || r, e = []), r = r || {}, t instanceof RegExp ? _(t, e) : Ft(t) ? T(t, e, r) : S(t, e, r) }

    function L(t, e, r) { try { return (Xt[t] || (Xt[t] = Dt.compile(t)))(e || {}, { pretty: !0 }) } catch (t) { return "" } }

    function P(t, e, r, n) {
        var o = e || [],
            i = r || Object.create(null),
            a = n || Object.create(null);
        t.forEach(function(t) { U(o, i, a, t) });
        for (var u = 0, c = o.length; u < c; u++) "*" === o[u] && (o.push(o.splice(u, 1)[0]), c--, u--);
        return { pathList: o, pathMap: i, nameMap: a }
    }

    function U(t, e, r, n, o, i) {
        var a = n.path,
            u = n.name,
            c = n.pathToRegexpOptions || {},
            s = H(a, o, c.strict);
        "boolean" == typeof n.caseSensitive && (c.sensitive = n.caseSensitive);
        var p = { path: s, regex: M(s, c), components: n.components || { default: n.component }, instances: {}, name: u, parent: o, matchAs: i, redirect: n.redirect, beforeEnter: n.beforeEnter, meta: n.meta || {}, props: null == n.props ? {} : n.components ? n.props : { default: n.props } };
        n.children && n.children.forEach(function(n) {
            var o = i ? w(i + "/" + n.path) : void 0;
            U(t, e, r, n, p, o)
        }), void 0 !== n.alias && (Array.isArray(n.alias) ? n.alias : [n.alias]).forEach(function(i) {
            var a = { path: i, children: n.children };
            U(t, e, r, a, o, p.path || "/")
        }), e[p.path] || (t.push(p.path), e[p.path] = p), u && (r[u] || (r[u] = p))
    }

    function M(t, e) { return Dt(t, [], e) }

    function H(t, e, r) { return r || (t = t.replace(/\/$/, "")), "/" === t[0] ? t : null == e ? t : w(e.path + "/" + t) }

    function I(t, e, r, n) {
        var i = "string" == typeof t ? { path: t } : t;
        if (i.name || i._normalized) return i;
        if (!i.path && i.params && e) {
            (i = V({}, i))._normalized = !0;
            var a = V(V({}, e.params), i.params);
            if (e.name) i.name = e.name, i.params = a;
            else if (e.matched.length) {
                var u = e.matched[e.matched.length - 1].path;
                i.path = L(u, a, "path " + e.path)
            }
            return i
        }
        var c = b(i.path || ""),
            s = e && e.path || "/",
            p = c.path ? g(c.path, s, r || i.append) : s,
            f = o(c.query, i.query, n && n.options.parseQuery),
            h = i.hash || c.hash;
        return h && "#" !== h.charAt(0) && (h = "#" + h), { _normalized: !0, path: p, query: f, hash: h }
    }

    function V(t, e) { for (var r in e) t[r] = e[r]; return t }

    function z(t, e) {
        function r(t, r, n) {
            var o = I(t, r, !1, e),
                a = o.name;
            if (a) {
                var u = p[a];
                if (!u) return i(null, o);
                var f = u.regex.keys.filter(function(t) { return !t.optional }).map(function(t) { return t.name });
                if ("object" != typeof o.params && (o.params = {}), r && "object" == typeof r.params)
                    for (var h in r.params) !(h in o.params) && f.indexOf(h) > -1 && (o.params[h] = r.params[h]);
                if (u) return o.path = L(u.path, o.params, 'named route "' + a + '"'), i(u, o, n)
            } else if (o.path) {
                o.params = {};
                for (var l = 0; l < c.length; l++) {
                    var d = c[l],
                        y = s[d];
                    if (B(y.regex, o.path, o.params)) return i(y, o, n)
                }
            }
            return i(null, o)
        }

        function n(t, n) {
            var o = t.redirect,
                a = "function" == typeof o ? o(u(t, n, null, e)) : o;
            if ("string" == typeof a && (a = { path: a }), !a || "object" != typeof a) return i(null, n);
            var c = a,
                s = c.name,
                p = c.path,
                f = n.query,
                h = n.hash,
                l = n.params;
            if (f = c.hasOwnProperty("query") ? c.query : f, h = c.hasOwnProperty("hash") ? c.hash : h, l = c.hasOwnProperty("params") ? c.params : l, s) return r({ _normalized: !0, name: s, query: f, hash: h, params: l }, void 0, n);
            if (p) { var d = F(p, t); return r({ _normalized: !0, path: L(d, l, 'redirect route with path "' + d + '"'), query: f, hash: h }, void 0, n) }
            return i(null, n)
        }

        function o(t, e, n) {
            var o = r({ _normalized: !0, path: L(n, e.params, 'aliased route with path "' + n + '"') });
            if (o) {
                var a = o.matched,
                    u = a[a.length - 1];
                return e.params = o.params, i(u, e)
            }
            return i(null, e)
        }

        function i(t, r, i) { return t && t.redirect ? n(t, i || r) : t && t.matchAs ? o(t, r, t.matchAs) : u(t, r, i, e) }
        var a = P(t),
            c = a.pathList,
            s = a.pathMap,
            p = a.nameMap;
        return { match: r, addRoutes: function(t) { P(t, c, s, p) } }
    }

    function B(t, e, r) {
        var n = e.match(t);
        if (!n) return !1;
        if (!r) return !0;
        for (var o = 1, i = n.length; o < i; ++o) {
            var a = t.keys[o - 1],
                u = "string" == typeof n[o] ? decodeURIComponent(n[o]) : n[o];
            a && (r[a.name] = u)
        }
        return !0
    }

    function F(t, e) { return g(t, e.parent ? e.parent.path : "/", !0) }

    function D() { window.history.replaceState({ key: et() }, ""), window.addEventListener("popstate", function(t) { J(), t.state && t.state.key && rt(t.state.key) }) }

    function K(t, e, r, n) {
        if (t.app) {
            var o = t.options.scrollBehavior;
            o && t.app.$nextTick(function() {
                var t = N(),
                    i = o(e, r, n ? t : null);
                i && ("function" == typeof i.then ? i.then(function(e) { Z(e, t) }).catch(function(t) {}) : Z(i, t))
            })
        }
    }

    function J() {
        var t = et();
        t && (Yt[t] = { x: window.pageXOffset, y: window.pageYOffset })
    }

    function N() { var t = et(); if (t) return Yt[t] }

    function Q(t, e) {
        var r = document.documentElement.getBoundingClientRect(),
            n = t.getBoundingClientRect();
        return { x: n.left - r.left - e.x, y: n.top - r.top - e.y }
    }

    function X(t) { return G(t.x) || G(t.y) }

    function Y(t) { return { x: G(t.x) ? t.x : window.pageXOffset, y: G(t.y) ? t.y : window.pageYOffset } }

    function W(t) { return { x: G(t.x) ? t.x : 0, y: G(t.y) ? t.y : 0 } }

    function G(t) { return "number" == typeof t }

    function Z(t, e) {
        var r = "object" == typeof t;
        if (r && "string" == typeof t.selector) {
            var n = document.querySelector(t.selector);
            if (n) {
                var o = t.offset && "object" == typeof t.offset ? t.offset : {};
                e = Q(n, o = W(o))
            } else X(t) && (e = Y(t))
        } else r && X(t) && (e = Y(t));
        e && window.scrollTo(e.x, e.y)
    }

    function tt() { return Gt.now().toFixed(3) }

    function et() { return Zt }

    function rt(t) { Zt = t }

    function nt(t, e) { J(); var r = window.history; try { e ? r.replaceState({ key: Zt }, "", t) : (Zt = tt(), r.pushState({ key: Zt }, "", t)) } catch (r) { window.location[e ? "replace" : "assign"](t) } }

    function ot(t) { nt(t, !0) }

    function it(t, e, r) {
        var n = function(o) { o >= t.length ? r() : t[o] ? e(t[o], function() { n(o + 1) }) : n(o + 1) };
        n(0)
    }

    function at(t) {
        return function(r, n, o) {
            var i = !1,
                a = 0,
                u = null;
            ut(t, function(t, r, n, c) {
                if ("function" == typeof t && void 0 === t.cid) {
                    i = !0, a++;
                    var s, p = pt(function(e) { st(e) && (e = e.default), t.resolved = "function" == typeof e ? e : Tt.extend(e), n.components[c] = e, --a <= 0 && o() }),
                        f = pt(function(t) {
                            var r = "Failed to resolve async component " + c + ": " + t;
                            u || (u = e(t) ? t : new Error(r), o(u))
                        });
                    try { s = t(p, f) } catch (t) { f(t) }
                    if (s)
                        if ("function" == typeof s.then) s.then(p, f);
                        else {
                            var h = s.component;
                            h && "function" == typeof h.then && h.then(p, f)
                        }
                }
            }), i || o()
        }
    }

    function ut(t, e) { return ct(t.map(function(t) { return Object.keys(t.components).map(function(r) { return e(t.components[r], t.instances[r], t, r) }) })) }

    function ct(t) { return Array.prototype.concat.apply([], t) }

    function st(t) { return t.__esModule || te && "Module" === t[Symbol.toStringTag] }

    function pt(t) { var e = !1; return function() { for (var r = [], n = arguments.length; n--;) r[n] = arguments[n]; if (!e) return e = !0, t.apply(this, r) } }

    function ft(t) {
        if (!t)
            if (Bt) {
                var e = document.querySelector("base");
                t = (t = e && e.getAttribute("href") || "/").replace(/^https?:\/\/[^\/]+/, "")
            } else t = "/";
        return "/" !== t.charAt(0) && (t = "/" + t), t.replace(/\/$/, "")
    }

    function ht(t, e) { var r, n = Math.max(t.length, e.length); for (r = 0; r < n && t[r] === e[r]; r++); return { updated: e.slice(0, r), activated: e.slice(r), deactivated: t.slice(r) } }

    function lt(t, e, r, n) { var o = ut(t, function(t, n, o, i) { var a = dt(t, e); if (a) return Array.isArray(a) ? a.map(function(t) { return r(t, n, o, i) }) : r(a, n, o, i) }); return ct(n ? o.reverse() : o) }

    function dt(t, e) { return "function" != typeof t && (t = Tt.extend(t)), t.options[e] }

    function yt(t) { return lt(t, "beforeRouteLeave", mt, !0) }

    function vt(t) { return lt(t, "beforeRouteUpdate", mt) }

    function mt(t, e) { if (e) return function() { return t.apply(e, arguments) } }

    function gt(t, e, r) { return lt(t, "beforeRouteEnter", function(t, n, o, i) { return bt(t, o, i, e, r) }) }

    function bt(t, e, r, n, o) { return function(i, a, u) { return t(i, a, function(t) { u(t), "function" == typeof t && n.push(function() { wt(t, e.instances, r, o) }) }) } }

    function wt(t, e, r, n) { e[r] ? t(e[r]) : n() && setTimeout(function() { wt(t, e, r, n) }, 16) }

    function xt(t) { var e = window.location.pathname; return t && 0 === e.indexOf(t) && (e = e.slice(t.length)), (e || "/") + window.location.search + window.location.hash }

    function kt(t) { var e = xt(t); if (!/^\/#/.test(e)) return window.location.replace(w(t + "/#" + e)), !0 }

    function Rt() { var t = Et(); return "/" === t.charAt(0) || (jt("/" + t), !1) }

    function Et() {
        var t = window.location.href,
            e = t.indexOf("#");
        return -1 === e ? "" : t.slice(e + 1)
    }

    function Ot(t) {
        var e = window.location.href,
            r = e.indexOf("#");
        return (r >= 0 ? e.slice(0, r) : e) + "#" + t
    }

    function Ct(t) { Wt ? nt(Ot(t)) : window.location.hash = t }

    function jt(t) { Wt ? ot(Ot(t)) : window.location.replace(Ot(t)) }

    function At(t, e) {
        return t.push(e),
            function() {
                var r = t.indexOf(e);
                r > -1 && t.splice(r, 1)
            }
    }

    function _t(t, e, r) { var n = "hash" === r ? "#" + e : e; return t ? w(t + "/" + n) : n }
    var Tt, St = {
            name: "router-view",
            functional: !0,
            props: { name: { type: String, default: "default" } },
            render: function(t, e) {
                var o = e.props,
                    i = e.children,
                    a = e.parent,
                    u = e.data;
                u.routerView = !0;
                for (var c = a.$createElement, s = o.name, p = a.$route, f = a._routerViewCache || (a._routerViewCache = {}), h = 0, l = !1; a && a._routerRoot !== a;) a.$vnode && a.$vnode.data.routerView && h++, a._inactive && (l = !0), a = a.$parent;
                if (u.routerViewDepth = h, l) return c(f[s], u, i);
                var d = p.matched[h];
                if (!d) return f[s] = null, c();
                var y = f[s] = d.components[s];
                u.registerRouteInstance = function(t, e) {
                    var r = d.instances[s];
                    (e && r !== t || !e && r === t) && (d.instances[s] = e)
                }, (u.hook || (u.hook = {})).prepatch = function(t, e) { d.instances[s] = e.componentInstance };
                var v = u.props = r(p, d.props && d.props[s]);
                if (v) { v = u.props = n({}, v); var m = u.attrs = u.attrs || {}; for (var g in v) y.props && g in y.props || (m[g] = v[g], delete v[g]) }
                return c(y, u, i)
            }
        },
        $t = /[!'()*]/g,
        qt = function(t) { return "%" + t.charCodeAt(0).toString(16) },
        Lt = /%2C/g,
        Pt = function(t) { return encodeURIComponent(t).replace($t, qt).replace(Lt, ",") },
        Ut = decodeURIComponent,
        Mt = /\/?$/,
        Ht = u(null, { path: "/" }),
        It = [String, Object],
        Vt = [String, Array],
        zt = {
            name: "router-link",
            props: { to: { type: It, required: !0 }, tag: { type: String, default: "a" }, exact: Boolean, append: Boolean, replace: Boolean, activeClass: String, exactActiveClass: String, event: { type: Vt, default: "click" } },
            render: function(t) {
                var e = this,
                    r = this.$router,
                    n = this.$route,
                    o = r.resolve(this.to, n, this.append),
                    i = o.location,
                    a = o.route,
                    c = o.href,
                    s = {},
                    p = r.options.linkActiveClass,
                    h = r.options.linkExactActiveClass,
                    d = null == p ? "router-link-active" : p,
                    m = null == h ? "router-link-exact-active" : h,
                    g = null == this.activeClass ? d : this.activeClass,
                    b = null == this.exactActiveClass ? m : this.exactActiveClass,
                    w = i.path ? u(null, i, null, r) : a;
                s[b] = f(n, w), s[g] = this.exact ? s[b] : l(n, w);
                var x = function(t) { y(t) && (e.replace ? r.replace(i) : r.push(i)) },
                    k = { click: y };
                Array.isArray(this.event) ? this.event.forEach(function(t) { k[t] = x }) : k[this.event] = x;
                var R = { class: s };
                if ("a" === this.tag) R.on = k, R.attrs = { href: c };
                else {
                    var E = v(this.$slots.default);
                    if (E) {
                        E.isStatic = !1;
                        var O = Tt.util.extend;
                        (E.data = O({}, E.data)).on = k, (E.data.attrs = O({}, E.data.attrs)).href = c
                    } else R.on = k
                }
                return t(this.tag, R, this.$slots.default)
            }
        },
        Bt = "undefined" != typeof window,
        Ft = Array.isArray || function(t) { return "[object Array]" == Object.prototype.toString.call(t) },
        Dt = q,
        Kt = x,
        Jt = E,
        Nt = $,
        Qt = new RegExp(["(\\\\.)", "([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"), "g");
    Dt.parse = Kt, Dt.compile = function(t, e) { return E(x(t, e)) }, Dt.tokensToFunction = Jt, Dt.tokensToRegExp = Nt;
    var Xt = Object.create(null),
        Yt = Object.create(null),
        Wt = Bt && function() { var t = window.navigator.userAgent; return (-1 === t.indexOf("Android 2.") && -1 === t.indexOf("Android 4.0") || -1 === t.indexOf("Mobile Safari") || -1 !== t.indexOf("Chrome") || -1 !== t.indexOf("Windows Phone")) && (window.history && "pushState" in window.history) }(),
        Gt = Bt && window.performance && window.performance.now ? window.performance : Date,
        Zt = tt(),
        te = "function" == typeof Symbol && "symbol" == typeof Symbol.toStringTag,
        ee = function(t, e) { this.router = t, this.base = ft(e), this.current = Ht, this.pending = null, this.ready = !1, this.readyCbs = [], this.readyErrorCbs = [], this.errorCbs = [] };
    ee.prototype.listen = function(t) { this.cb = t }, ee.prototype.onReady = function(t, e) { this.ready ? t() : (this.readyCbs.push(t), e && this.readyErrorCbs.push(e)) }, ee.prototype.onError = function(t) { this.errorCbs.push(t) }, ee.prototype.transitionTo = function(t, e, r) {
        var n = this,
            o = this.router.match(t, this.current);
        this.confirmTransition(o, function() { n.updateRoute(o), e && e(o), n.ensureURL(), n.ready || (n.ready = !0, n.readyCbs.forEach(function(t) { t(o) })) }, function(t) { r && r(t), t && !n.ready && (n.ready = !0, n.readyErrorCbs.forEach(function(e) { e(t) })) })
    }, ee.prototype.confirmTransition = function(r, n, o) {
        var i = this,
            a = this.current,
            u = function(r) { e(r) && (i.errorCbs.length ? i.errorCbs.forEach(function(t) { t(r) }) : (t(!1, "uncaught error during route navigation:"), console.error(r))), o && o(r) };
        if (f(r, a) && r.matched.length === a.matched.length) return this.ensureURL(), u();
        var c = ht(this.current.matched, r.matched),
            s = c.updated,
            p = c.deactivated,
            h = c.activated,
            l = [].concat(yt(p), this.router.beforeHooks, vt(s), h.map(function(t) { return t.beforeEnter }), at(h));
        this.pending = r;
        var d = function(t, n) { if (i.pending !== r) return u(); try { t(r, a, function(t) {!1 === t || e(t) ? (i.ensureURL(!0), u(t)) : "string" == typeof t || "object" == typeof t && ("string" == typeof t.path || "string" == typeof t.name) ? (u(), "object" == typeof t && t.replace ? i.replace(t) : i.push(t)) : n(t) }) } catch (t) { u(t) } };
        it(l, d, function() {
            var t = [];
            it(gt(h, t, function() { return i.current === r }).concat(i.router.resolveHooks), d, function() {
                if (i.pending !== r) return u();
                i.pending = null, n(r), i.router.app && i.router.app.$nextTick(function() { t.forEach(function(t) { t() }) })
            })
        })
    }, ee.prototype.updateRoute = function(t) {
        var e = this.current;
        this.current = t, this.cb && this.cb(t), this.router.afterHooks.forEach(function(r) { r && r(t, e) })
    };
    var re = function(t) {
            function e(e, r) {
                var n = this;
                t.call(this, e, r);
                var o = e.options.scrollBehavior;
                o && D();
                var i = xt(this.base);
                window.addEventListener("popstate", function(t) {
                    var r = n.current,
                        a = xt(n.base);
                    n.current === Ht && a === i || n.transitionTo(a, function(t) { o && K(e, t, r, !0) })
                })
            }
            return t && (e.__proto__ = t), e.prototype = Object.create(t && t.prototype), e.prototype.constructor = e, e.prototype.go = function(t) { window.history.go(t) }, e.prototype.push = function(t, e, r) {
                var n = this,
                    o = this.current;
                this.transitionTo(t, function(t) { nt(w(n.base + t.fullPath)), K(n.router, t, o, !1), e && e(t) }, r)
            }, e.prototype.replace = function(t, e, r) {
                var n = this,
                    o = this.current;
                this.transitionTo(t, function(t) { ot(w(n.base + t.fullPath)), K(n.router, t, o, !1), e && e(t) }, r)
            }, e.prototype.ensureURL = function(t) {
                if (xt(this.base) !== this.current.fullPath) {
                    var e = w(this.base + this.current.fullPath);
                    t ? nt(e) : ot(e)
                }
            }, e.prototype.getCurrentLocation = function() { return xt(this.base) }, e
        }(ee),
        ne = function(t) {
            function e(e, r, n) { t.call(this, e, r), n && kt(this.base) || Rt() }
            return t && (e.__proto__ = t), e.prototype = Object.create(t && t.prototype), e.prototype.constructor = e, e.prototype.setupListeners = function() {
                var t = this,
                    e = this.router.options.scrollBehavior,
                    r = Wt && e;
                r && D(), window.addEventListener(Wt ? "popstate" : "hashchange", function() {
                    var e = t.current;
                    Rt() && t.transitionTo(Et(), function(n) { r && K(t.router, n, e, !0), Wt || jt(n.fullPath) })
                })
            }, e.prototype.push = function(t, e, r) {
                var n = this,
                    o = this.current;
                this.transitionTo(t, function(t) { Ct(t.fullPath), K(n.router, t, o, !1), e && e(t) }, r)
            }, e.prototype.replace = function(t, e, r) {
                var n = this,
                    o = this.current;
                this.transitionTo(t, function(t) { jt(t.fullPath), K(n.router, t, o, !1), e && e(t) }, r)
            }, e.prototype.go = function(t) { window.history.go(t) }, e.prototype.ensureURL = function(t) {
                var e = this.current.fullPath;
                Et() !== e && (t ? Ct(e) : jt(e))
            }, e.prototype.getCurrentLocation = function() { return Et() }, e
        }(ee),
        oe = function(t) {
            function e(e, r) { t.call(this, e, r), this.stack = [], this.index = -1 }
            return t && (e.__proto__ = t), e.prototype = Object.create(t && t.prototype), e.prototype.constructor = e, e.prototype.push = function(t, e, r) {
                var n = this;
                this.transitionTo(t, function(t) { n.stack = n.stack.slice(0, n.index + 1).concat(t), n.index++, e && e(t) }, r)
            }, e.prototype.replace = function(t, e, r) {
                var n = this;
                this.transitionTo(t, function(t) { n.stack = n.stack.slice(0, n.index).concat(t), e && e(t) }, r)
            }, e.prototype.go = function(t) {
                var e = this,
                    r = this.index + t;
                if (!(r < 0 || r >= this.stack.length)) {
                    var n = this.stack[r];
                    this.confirmTransition(n, function() { e.index = r, e.updateRoute(n) })
                }
            }, e.prototype.getCurrentLocation = function() { var t = this.stack[this.stack.length - 1]; return t ? t.fullPath : "/" }, e.prototype.ensureURL = function() {}, e
        }(ee),
        ie = function(t) {
            void 0 === t && (t = {}), this.app = null, this.apps = [], this.options = t, this.beforeHooks = [], this.resolveHooks = [], this.afterHooks = [], this.matcher = z(t.routes || [], this);
            var e = t.mode || "hash";
            switch (this.fallback = "history" === e && !Wt && !1 !== t.fallback, this.fallback && (e = "hash"), Bt || (e = "abstract"), this.mode = e, e) {
                case "history":
                    this.history = new re(this, t.base);
                    break;
                case "hash":
                    this.history = new ne(this, t.base, this.fallback);
                    break;
                case "abstract":
                    this.history = new oe(this, t.base)
            }
        },
        ae = { currentRoute: { configurable: !0 } };
    return ie.prototype.match = function(t, e, r) { return this.matcher.match(t, e, r) }, ae.currentRoute.get = function() { return this.history && this.history.current }, ie.prototype.init = function(t) {
        var e = this;
        if (this.apps.push(t), !this.app) {
            this.app = t;
            var r = this.history;
            if (r instanceof re) r.transitionTo(r.getCurrentLocation());
            else if (r instanceof ne) {
                var n = function() { r.setupListeners() };
                r.transitionTo(r.getCurrentLocation(), n, n)
            }
            r.listen(function(t) { e.apps.forEach(function(e) { e._route = t }) })
        }
    }, ie.prototype.beforeEach = function(t) { return At(this.beforeHooks, t) }, ie.prototype.beforeResolve = function(t) { return At(this.resolveHooks, t) }, ie.prototype.afterEach = function(t) { return At(this.afterHooks, t) }, ie.prototype.onReady = function(t, e) { this.history.onReady(t, e) }, ie.prototype.onError = function(t) { this.history.onError(t) }, ie.prototype.push = function(t, e, r) { this.history.push(t, e, r) }, ie.prototype.replace = function(t, e, r) { this.history.replace(t, e, r) }, ie.prototype.go = function(t) { this.history.go(t) }, ie.prototype.back = function() { this.go(-1) }, ie.prototype.forward = function() { this.go(1) }, ie.prototype.getMatchedComponents = function(t) { var e = t ? t.matched ? t : this.resolve(t).route : this.currentRoute; return e ? [].concat.apply([], e.matched.map(function(t) { return Object.keys(t.components).map(function(e) { return t.components[e] }) })) : [] }, ie.prototype.resolve = function(t, e, r) {
        var n = I(t, e || this.history.current, r, this),
            o = this.match(n, e),
            i = o.redirectedFrom || o.fullPath;
        return { location: n, route: o, href: _t(this.history.base, i, this.mode), normalizedTo: n, resolved: o }
    }, ie.prototype.addRoutes = function(t) { this.matcher.addRoutes(t), this.history.current !== Ht && this.history.transitionTo(this.history.getCurrentLocation()) }, Object.defineProperties(ie.prototype, ae), ie.install = m, ie.version = "3.0.1", Bt && window.Vue && window.Vue.use(ie), ie
});