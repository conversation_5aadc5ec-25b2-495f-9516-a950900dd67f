<template>
    <div class="videoForm">
        <el-form :model="props.form" @submit.native.prevent :label-width="labelWidth" label-position="right" :rules="rules" ref="formRef">
            <el-row>
                <el-card class="video-card">
                    <el-form-item :label="$t('work.pageCreate.title') + ':'" style="width: 100%;" prop="title" required>
                        <el-input :placeholder="$t('work.pageCreate.enterTitle')" type="textarea" :rows="2" v-model="props.form.title" maxlength="500" show-word-limit></el-input>
                    </el-form-item>
                   
                    <el-form-item :label="$t('work.pageCreate.fileLink') + ':'" style="width: 100%;margin-top: 20px;">
                        <div class="video-upload">
                            <el-input-tag readonly v-model="displayFileNames" style="width: 87.5%;" :placeholder="$t('work.pageCreate.pleaseSelectFile')" ></el-input-tag>
                            <el-button 
                              class="upload-btn"
                              style="border-radius: 3px;" 
                              type="primary"
                              @click="openUploadDialog">
                              <img src="/pageCreate/upload.svg" style="width: 14px; height: 14px; margin-right: 5px;" />
                              {{ $t('work.pageCreate.selectFile') }}
                            </el-button>
                        </div>
                    </el-form-item>
                  
                   
                        <VideoContent 
                        style="width: 100%;margin-top: 20px;"
                          v-model="props.form.videoFormContent"
                          :video-form-links="props.form.videoFormLink"
                          :items="contentItems"
                          @items-updated="updateContentItems"
                          @update:video-form-links="updateVideoFormLinks" 
                        />
              
                  
                </el-card>
            </el-row>
        </el-form>
       
        <VideoUploadDialog
          v-model="uploadDialogVisible"
          :attachments="currentAttachments"
          :limit="['.mp4','.pdf','.docx','.xlsx','.ogg','.webm']"
          :video-form="props.form"
          @content-updated="updateContentItems"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import VideoUploadDialog from './components/VideoUploadDialog.vue'
import VideoContent from './components/VideoContent.vue'

const { t, locale } = useI18n()
const labelWidth = computed(() => {
    if(locale.value == 'zh-CN'){
        return '80px'
    }else if(locale.value == 'en-US'){
        return '90px'
    }
})

const uploadDialogVisible = ref(false)

// Form validation related
const formRef = ref()
const rules = computed(() => {
  return {
    title: [
      { required: true, message: t('work.pageCreate.pleaseEnter') + t('work.pageCreate.title'), trigger: 'blur' }
    ]
  }
})

// Make validate method available for parent component
defineExpose({
  validate: () => {
    if (formRef.value) {
      return formRef.value.validate()
    }
    return Promise.resolve(true)
  }
})

const props = defineProps({
    form: {
        type: Object,
        default: () => ({
            title: '',
            videoFormLink: [],
            videoFormContent: '',
            knowledgeAttachmentDTOList: []
        })
    }
})

// 计算属性，用于显示文件名而不是URL
const displayFileNames = computed(() => {
    return props.form.videoFormLink.map((url: string) => {
        // 提取文件名并解码
        const fileName = decodeURIComponent(url.split('/').pop() || 'file')
        return fileName
    })
})

const emit = defineEmits(['itemsUpdated'])

// Initialize knowledgeAttachmentDTOList if it doesn't exist
if (!props.form.knowledgeAttachmentDTOList) {
    props.form.knowledgeAttachmentDTOList = []
}

// Content items array to track uploaded files
const contentItems = ref<{ content: string, url: string, authorization: number, deleted?: boolean }[]>([])

// Store current attachments for the upload dialog
const currentAttachments = ref<any[]>([])

// Keep track of deleted URLs to prevent them from reappearing
const deletedUrls = ref<string[]>([])

// Initialize contentItems from existing form data when component mounts
const initializeContentItems = () => {
    if (props.form.videoFormContent && props.form.videoFormLink && props.form.videoFormLink.length > 0) {
        // Use regex to extract individual item content blocks from the combined HTML content
        const previewItemRegex = /<div class="preview-item (authorization|unauthorization)">([\s\S]*?)<\/div>/g
        const content = props.form.videoFormContent
        let match
        const extractedItems: { content: string, url: string, authorization: number }[] = []
        
        while ((match = previewItemRegex.exec(content)) !== null) {
            const authClass = match[1]
            const authorization = authClass === 'authorization' ? 1 : 0
            const itemContent = match[0]
            
            // Extract URL from content
            const urlMatch = itemContent.match(/src="([^"]+)"/)
            if (urlMatch && urlMatch[1]) {
                let url = urlMatch[1]
                
                // If it's an Office document with a view URL, extract the actual document URL
                if (url.includes('https://view.officeapps.live.com/op/embed.aspx?src=')) {
                    const encodedUrl = url.split('src=')[1]
                    url = decodeURIComponent(encodedUrl)
                }
                
                // Check if this URL exists in videoFormLink
                const matchingLink = props.form.videoFormLink.find((link: string) => 
                    link === url || 
                    url.includes(encodeURIComponent(link)) || 
                    link.includes(encodeURIComponent(url))
                )
                
                if (matchingLink) {
                    extractedItems.push({
                        content: itemContent,
                        url: matchingLink,
                        authorization
                    })
                }
            }
        }
        
        // If we couldn't extract items or there was a mismatch, create items from videoFormLink directly
        if (extractedItems.length === 0 || extractedItems.length !== props.form.videoFormLink.length) {
            props.form.videoFormLink.forEach((url: string) => {
                // Get file extension from URL
                const fileExtension = url.split('.').pop()?.toLowerCase() || ''
                const authorization = 1 // Default to authorized
                
                // Create preview content based on file type
                const previewItem = getPreviewContent(url, fileExtension, authorization)
                if (previewItem.content) {
                    extractedItems.push(previewItem)
                }
            })
        }
        
        // Set contentItems
        contentItems.value = extractedItems
    }
}

// Helper function to generate preview content based on file type
const getPreviewContent = (url: string, fileType: string, authorization: number) => {
    const fileExtension = fileType.toLowerCase()
    const authClass = authorization === 1 ? 'authorization' : 'unauthorization'
    
    if (fileExtension === 'mp4' || fileExtension === 'ogg' || fileExtension === 'webm') {
        // Determine the correct MIME type
        let mimeType = 'video/mp4'
        if (fileExtension === 'ogg') mimeType = 'video/ogg'
        if (fileExtension === 'webm') mimeType = 'video/webm'
        
        return {
            content: `<div class="preview-item ${authClass}">
                <video controls style="width: 100%; max-height: 100%;">
                    <source src="${url}" type="${mimeType}">
                    Your browser does not support the video tag.
                </video>
            </div>`,
            url,
            authorization
        }
    } else if (fileExtension === 'pdf') {
        return {
            content: `<div class="preview-item ${authClass}">
                <iframe src="/api/sgs-e-filling/sgs-knowledge/knowledge/file/preview?url=${url}" style="width: 100%; height: 600px; border: none;"></iframe>
            </div>`,
            url,
            authorization
        }
    } else if (['doc', 'docx'].includes(fileExtension)) {
        const encodedUrl = encodeURIComponent(url)
        return {
            content: `<div class="preview-item ${authClass}">
                <iframe src="https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}" 
                    style="width: 100%; height: 600px; border: none;">
                </iframe>
            </div>`,
            url,
            authorization
        }
    } else if (['xls', 'xlsx'].includes(fileExtension)) {
        const encodedUrl = encodeURIComponent(url)
        return {
            content: `<div class="preview-item ${authClass}">
                <iframe src="https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}" 
                    style="width: 100%; height: 600px; border: none;">
                </iframe>
            </div>`,
            url,
            authorization
        }
    }
    
    return {
        content: '',
        url,
        authorization
    }
}

// Call the initialization function when component is mounted
onMounted(() => {
    initializeContentItems()
})

// Watch for changes in form.videoFormLink to update contentItems
watch(() => props.form.videoFormLink, (newLinks) => {
    if (newLinks && newLinks.length > 0 && contentItems.value.length === 0) {
        initializeContentItems()
    }
}, { deep: true })

// Create attachments from existing links and content when component initializes
const initializeAttachmentsFromLinks = () => {
   
        // Create attachments from existing links, filtering out deleted ones
        currentAttachments.value = props.form.videoFormLink
            .filter((url: string) => !deletedUrls.value.includes(url))
            .map((url: string) => {
              debugger
                // Extract file name from URL
                const fileName = decodeURIComponent(url.split('/').pop() || 'file')
                // Find if there's a matching content item with authorization info
                const matchingItem = contentItems.value.find(item => 
                    item.url === url || 
                    item.url.includes(encodeURIComponent(url)) || 
                    item.content.includes(url)
                )
                
                return {
                    fileName,
                    fileUrl: url,
                    authorization: matchingItem?.authorization,
                    status: 1
                }
            })
    
}

// Watch for changes in contentItems
watch(contentItems, (newItems) => {
    // Update currentAttachments when contentItems changes
    if (newItems.length > 0) {
        initializeAttachmentsFromLinks()
    }
}, { deep: true })

// Open the upload dialog
const openUploadDialog = () => {
    // Initialize attachments from existing links before opening dialog
    initializeAttachmentsFromLinks()
    uploadDialogVisible.value = true
}

// Update content items when receiving from dialog
const updateContentItems = (items: { content: string, url: string, authorization: number, deleted?: boolean }[]) => {
    // Check for deleted items and add them to deletedUrls
    const newDeletedItems = items.filter(item => item.deleted).map(item => item.url)
    if (newDeletedItems.length > 0) {
        deletedUrls.value = [...deletedUrls.value, ...newDeletedItems]
    }
    
    // Merge new items with existing items instead of replacing, excluding deleted items
    const filteredItems = items.filter(item => !item.deleted && !deletedUrls.value.includes(item.url))
    const existingUrls = contentItems.value.map(item => item.url)
    const newItems = filteredItems.filter(item => !existingUrls.includes(item.url))
    
    if (newItems.length > 0) {
        contentItems.value = [...contentItems.value, ...newItems]
        
        // Update the form's content by combining all items
        props.form.videoFormContent = contentItems.value.map(item => item.content).join('')
    } else if (filteredItems.length > 0 && contentItems.value.length === 0) {
        // If we don't have any existing items but received new ones
        contentItems.value = filteredItems
    } else if (filteredItems.length < contentItems.value.length) {
        // Handle case where items were deleted
        contentItems.value = filteredItems
    } else {
        // Update existing items (e.g., authorization changes)
        contentItems.value = filteredItems
        
        // Also update authorization in currentAttachments to keep them in sync
        for (const item of filteredItems) {
            const attachmentIndex = currentAttachments.value.findIndex(attachment => 
                attachment.fileUrl === item.url || 
                item.url.includes(encodeURIComponent(attachment.fileUrl)) ||
                attachment.fileUrl.includes(encodeURIComponent(item.url))
            )
            
            if (attachmentIndex > -1) {
                currentAttachments.value[attachmentIndex].authorization = item.authorization
            }
        }
    }
}

// Update video form links when changed in content component
const updateVideoFormLinks = (links: string[]) => {
 
    props.form.videoFormLink = links
}

// Handle tag removal
// const handleTagRemove = (tag: string) => {
//     // Find the index in the videoFormLink array
//     const linkIndex = props.form.videoFormLink.indexOf(tag)
    
//     if (linkIndex > -1) {
//         // Add to deleted URLs list
//         deletedUrls.value.push(tag)
        
//         // Find the matching item in contentItems
//         const contentIndex = contentItems.value.findIndex(item => item.url === tag || 
//         item.url.includes(encodeURIComponent(tag)) || 
//         item.content.includes(tag))
        
//         if (contentIndex > -1) {
//             // Remove the item from contentItems
//             contentItems.value.splice(contentIndex, 1)
            
//             // Update the form content
//             props.form.videoFormContent = contentItems.value.map(item => item.content).join('')
            
//             // Remove from currentAttachments as well
//             const attachmentIndex = currentAttachments.value.findIndex(item => 
//                 item.fileUrl === tag || 
//                 (item.fileUrl && tag.includes(item.fileUrl))
//             )
//             if (attachmentIndex > -1) {
//                 currentAttachments.value.splice(attachmentIndex, 1)
//             }
            
//             // Emit the updated items
//             emit('itemsUpdated', contentItems.value)
//         }
//     }
// }
</script>

<style lang="scss">
.videoForm {
    height: 100%;
    overflow-y: auto;
    .video-card{
        padding-top: 30px !important;
        padding-left:30px !important;
        .el-form-item{
            margin: 5px 0px;
        }
    }
    .el-card {
        .attachment-form-item{
            .el-form-item__label{
                height: 80%;
                align-items: center;
            }
            .el-form-item__content{
                display: flex;
                align-items: baseline;
                gap: 10px;
            }
        }
        width: 100%;
        .el-card__body {
            .no-data{
                width: 95%;
                height: calc(100vh - 57px);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: #909399;
            }
            .content-container {
                width: 95%;
                height: calc(100vh - 57px);
                overflow-y: auto;
                padding: 10px;
                .preview-item {
                    width: 100%;
                    height: 600px;
                    border: 1px solid #eee;
                    border-radius: 4px;
                    overflow: hidden;
                }
            }
            width: 100%;
            padding: 10px 10px;
            .el-form-item {
                margin-bottom: 0px;
            }
            .content {
                height: 700px;
                .el-form-item__content {
                    display: inline;
                    div {
                        height: 98%;
                        display: flex;
                        flex-direction: column;
                        gap: 20px;
                        padding: 10px;
                        
                        .preview-item {
                            width: 100%;
                            height: 800px;
                            border: 1px solid #eee;
                            border-radius: 4px;
                            overflow: hidden;
                        }
                    }
                }
            }
            flex-wrap: wrap;
        }
    }

    .video-upload {
        display: flex;
        gap: 10px;
        width: 100%;
        align-items: center;
        
        .el-input {
            flex: 1;
        }
        
        .upload-btn {
            flex-shrink: 0;
        }
    }
}
</style>