<template>
    <div id="commonTable" class="commonTable" v-loading="!showTableFlag">
       <!-- <table-header-filter
            v-if="showTableFlag"
            :filter-config="filterConfig"
            :query-model="queryModel"
        ></table-header-filter>-->
        <el-form
                v-if="showTableFlag"
                label-width="120px"
                ref="sgs_form_search_form"
                inline
                :model="queryModel.general"
                :size="tableOption.size || 'medium'">
            <table-header-general-filter
                    ref="table_header_general_filter"
                    :form-data="queryModel.general"
                    :sort-by="queryModel.sortBy"
                    :condition-from-view="!!filterContentObj.currentId || option.filterable"
                    :dynamic-form="tabsDynamicForm"
                    :order-by="queryModel.sortOrder"
                    :columns="tableOption.column"
                    :showTableFlag="showTableFlag"
            >
                <template #operate-block-right>
                    <slot name="operate-block-right"></slot>
                </template>
            </table-header-general-filter>
        </el-form>
        <el-table
                v-if="showTableFlag && showTableListFlag"
                v-loading="tableDataLoading"
                v-bind="$attrs"
                :key="reload"
                :data="tableData"
                style="width: 100%"
                :size="tableOption.size || 'medium'"
                fit
                border
                resizable
                :max-height="tableMaxHeight"
                v-on="$listeners"
                ref="table"
                class="sgs_smart_common_table"
                :header-cell-class-name="getTableColClass"
                :cell-class-name="getTableColClass"
        >
            <!-- 复选框 -->
            <el-table-column
                    v-if="tableOption.selection"
                    :selectable="tableOption.selectionDis"
                    type="selection"
                    width="35"
                    align="center"
                    fixed
            >
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
                    v-if="tableOption.index"
                    type="index"
                    label="#"
                    align="center"
                    width="80px"
                    fixed
            >
            </el-table-column>
            <!-- 显示列 -->
            <el-table-column
                    v-for="(column, index) in getTableOptionColumn"
                    :prop="column.prop"
                    :label="column.label"
                    :width="column.width"
                    :min-width="column.minWidth || 220"
                    :fixed="column.fixed"
                    :key="'sgs-table-col-'+column.prop"
                    :align="column.align"
            >
                    <template slot="header" slot-scope="scope">
                        <slot
                                v-if="column.headerslot"
                                v-bind="scope"
                                :prop="column.prop"
                                :label="column.label"
                                :name="'header-' + column.prop"
                        ></slot>
                        <template v-if="!column.headerslot">
                            <el-row>
                                <el-col :span="20" >
                                    <div style="overflow-wrap:break-word;width: 90%;text-align:left">
                                        <el-tooltip class="item" effect="dark" :content="column.label" placement="top">
                                            <div class="sgs_smart_table_header_label">
                                                {{column.label}}
                                            </div>
                                        </el-tooltip>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <el-popover
                                        placement="bottom"
                                        width="300"
                                        :ref="'sort_popover_'+(column.prop=='trf_header_trfStatusName'?'trf_header_trfStatus':column.prop)"
                                        trigger="click">
                                    <!-- @hide="queryData(column.prop)"-->
                                    <el-row>
                                        <el-col style="text-align: center">
                                            <el-radio-group
                                                    class="es-table-header-sort"
                                                    :size="tableOption.size || 'small'"
                                                    v-model="fieldSortObj[column.prop=='trf_header_trfStatusName'?'trf_header_trfStatus':column.prop]"
                                                    @input="sortByColumn(column.prop=='trf_header_trfStatusName'?'trf_header_trfStatus':column.prop)">
                                                <el-radio-button :label="true">ASC<i class="el-icon-sort-up"></i> </el-radio-button>
                                                <el-radio-button :label="false">DESC<i class="el-icon-sort-down"></i></el-radio-button>
                                            </el-radio-group>

                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="6" v-if="'date'!=column.fieldType">
                                            <el-select
                                                    v-model="dynamicForm[column.prop]['condition']"
                                                    :size="tableOption.size || 'small'"
                                                    style="width: 100%"
                                                    @change="queryData(column.prop)"
                                                    filterable
                                            >
                                                <el-option
                                                        v-for="(op,ind) in getSelectList(column.fieldType)"
                                                        :key="'col_'+ind"
                                                        :label="op.label"
                                                        :value="op.value"
                                                ></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="'date'==column.fieldType?24:18">
                                            <!--@keyup.enter.native="queryData"-->
                                            <el-input
                                                    v-if="'text'==column.fieldType"
                                                    :disabled="conditionInputDisabled(column.prop)"
                                                    style="width: 100%"
                                                    :size="tableOption.size || 'small'"
                                                    :placeholder="column.label"
                                                    v-model="dynamicForm[column.prop]['conditionValue']"
                                                    :key="column.prop"
                                                    @clear="queryData(column.prop)"
                                                    @keyup.enter.native="queryData(column.prop)"
                                                    clearable
                                            >
                                                <template slot="append">
                                                    <i style="width: 16px;color: #ff6600;cursor: pointer;border: none;left:0;top:0;"
                                                       class="el-icon-search"
                                                       @click="queryData(column.prop)"
                                                    ></i>
                                                </template>
                                            </el-input>
                                            <!--@change="queryData"-->
                                            <el-select
                                                    v-if="'select'==column.fieldType"
                                                    style="width: 100%"
                                                    :size="tableOption.size || 'small'"
                                                    :multiple="column.multiple"
                                                    :placeholder="column.label"
                                                    filterable
                                                    :no-data-text="(column['noData'] || {})[tableLan]"
                                                    @change="queryDataAndCaseSelect(column)"
                                                    v-model="dynamicForm[column.prop]['conditionValue']"
                                                    :key="'header-select-'+column.prop"
                                                    clearable
                                            >
                                                <!--<el-option v-for="(op,ind) in computeDataList(column)"-->
                                                <el-option v-for="(op,ind) in tableHeaderSelectList[column.prop]"
                                                           :label="$t(op.label)"
                                                           :value="op.value"
                                                           :key="'select_'+ind"
                                                ></el-option>
                                            </el-select>

                                            <!--@change="queryData"-->
                                            <el-date-picker
                                                    v-if="'date'==column.fieldType"
                                                    style="width: 100%"
                                                    :size="tableOption.size || 'small'"
                                                    format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd"
                                                    v-model="dynamicForm[column.prop]['conditionValue']"
                                                    type="daterange"
                                                    clearable
                                                    range-separator="-"
                                                    @change="queryData(column.prop)"
                                                    :unlink-panels="false"
                                                    :picker-options="pickerOptions"
                                            >
                                            </el-date-picker>
                                        </el-col>
                                    </el-row>
                                    <div style="cursor: pointer;" slot="reference">
                                        <img v-if="dynamicForm[column.prop]['conditionValue'] && dynamicForm[column.prop]['conditionValue'].length>0" src="./icon/hasCondition.png" style="width: 16px"/>
                                        <img v-if="!dynamicForm[column.prop]['conditionValue'] || dynamicForm[column.prop]['conditionValue'].length==0" src="./icon/noCondition.png" style="width: 16px"/>
                                    </div>
                                </el-popover>
                            </el-col>
                            </el-row>
                        </template>
                    </template>
                    <!-- 内容部分的slot -->
                    <template slot-scope="scope">
                        <el-tooltip v-if="column.showTooltip && scope.row[column.prop] && scope.row[column.prop].length > 0"
                                    placement="top">
                            <div slot="content" style="max-width: 600px;">
                                <slot
                                        v-if="column.slot"
                                        v-bind="scope"
                                        :prop="column.prop"
                                        :label="column.label"
                                        :name="column.prop"
                                ></slot>
                                <span v-else v-html="scope.row[column.prop]"></span>
                            </div>
                            <i class="el-icon-info"></i> &nbsp;
                        </el-tooltip>
                        <slot
                                v-if="column.slot"
                                v-bind="scope"
                                :prop="column.prop"
                                :label="column.label"
                                :name="column.prop"
                        ></slot>
                        <span v-else-if="column.dicData != null && column.dicData.length > 0">
                            {{ getDicLabel(column.dicData, scope.row[column.prop]) }}
                      </span>
                        <span v-else>
                            <el-tooltip placement="top">
                                <div slot="content">
                                  <div v-html="getColumnData(scope.row,column)"></div>
                                </div>
                                <div class="sgs_smart_table_column_data"
                                     v-html="getColumnData(scope.row,column)">
                                </div>
                            </el-tooltip>
                          </span>
                    </template>
            </el-table-column>
                <!-- 操作列 -->
            <el-table-column
                    v-if="tableOption.action"
                    label="Action"
                    fixed="right"
                    :width="tableOption.actionWidth || '200px'"
            >
                <template slot="header" slot-scope="scope">
                    <div style="line-height: 35px;">
                        <span style="float:left">
                            {{$t('operation.title')}}
                        </span>
                        <i class="el-icon-setting" style="cursor: pointer;font-size: 24px;float:right;margin-top:4px" @click="openDraw"></i>
                    </div>
                </template>
                <template slot-scope="scope">
                    <slot v-bind="scope" name="action"></slot>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page.sync="page.page"
                :page-sizes="page.sizes"
                :page-size.sync="page.rows"
                :layout="page.layout"
                :total="page.total"
        >
        </el-pagination>
        <!-- 右侧抽屉-->
        <table-column-checked
            v-if="configColumnShow"
            :column-option="drawColumns"
            :checked-val="checkedColumn"
            :role-filter="queryModel.general.roleFilter"
            @closed="configColumnShow=false"
            @updateColShow="updateColShow"
        ></table-column-checked>

        <div ref="exportExcel" style="display: none;"></div>
    </div>
</template>
<script>
    import Sortable from 'sortablejs';
    import TableHeaderFilter from "./TableHeaderFilter";
    import TableColumnChecked from "./TableColumnChecked";
    import TableHeaderCollapseFilter from "./TableHeaderCollapseFilter";
    import TableHeaderGeneralFilter from './TableHeaderGeneralFilter'
    import TableHeaderTabs from "./TableHeaderTabs";
    import api from "./api";
    import commonTableApi from './commonTableApi'
    import moment from 'moment';
    import _debounce from 'lodash/debounce'
    let vm;

    export default {
        name: "commonTable",
        components: {TableHeaderCollapseFilter, TableColumnChecked, TableHeaderFilter,TableHeaderTabs,TableHeaderGeneralFilter},
        provide(){
            return{
                resetQueryForm:this.resetQueryModelForm,
                language:()=>{return this.tableLan},
                getConditionList:()=>{return this.selectList},
                size: this.tableOption.size,
                loadFilterSuccess: this.loadSuccess,
                searchSubmit:{
                    fn:this.generalFilter,
                    sortByDefault:this.sortByDefaultColumn,
                    changeDateRange:()=>{this.modifyQueryDate = true},
                },
                tableObj:{
                    getQueryModal:()=>{ return this.queryModel},
                    getColumns:()=>{ return this.tableOption.column}
                },
                filterConfigObj:{
                    getFilterContentObj:()=>{return this.filterContentObj},
                    changeFilter:this.changeFilter,
                    addFilterConfig:this.addFilterConfig,
                    delFilterConfig:this.delFilterConfig,
                    updFilterConfig:this.updFilterConfig,
                    updateFilterDefault:this.updateFilterDefault,
                },
                removeSignFilter:this.cleanSignFilter,
                removeSortFilter:this.clearSort,
            }
        },
        data() {
            return {
                reload:Math.random(),
                tableLan:'',
                modifyQueryDate:false,
                languageCode:'',
                drawColumns:[],//抽屉col对象
                tableOption: {//表格option
                    maxHeight:0,
                    action:true,
                    size:'medium',
                    actionWidth:180,
                    filterable:true,
                    pageShow:true,
                    column:[]
                },
                checkedColumn: [],
                allColumn: [],
                showTableFlag: false,
                showTableListFlag:false,
                tableDataLoading:false,
                tableData: [],
                tableMaxHeight: 1000,
                configColumnShow: false,
                page:{//分页对象
                    show: true,
                    page: 1,
                    rows: 20,
                    small: true,
                    sizes: [10, 20, 50, 100],
                    layout: 'total, sizes, prev, pager, next,jumper',
                    total: 0
                },
                filterConfig:[],//动态filter
                configData:{},//所有config数据
                tableFilter:{},
                dynamicForm:{},
                tabsDynamicForm:{},//查询条件展示区域
                tableHeaderSelectList:{},
                queryModel:{//查询列表对象
                    general:{
                        startDate:'',
                        endDate:'',
                        filterDate:[],
                        queryValue:'',
                        isSubmissionTimeout:0,
                        currentDate:-1,
                        roleFilter:{'applicant':1,'buyer':1,'agent':1,'supplier':1,'manufacturer':1},
                    },
                    dynamicForm:{},
                    sortBy:'',
                    sortOrder:''
                },
                filterContentObj:{//从DB获取的filter保存
                    filterList:[],//所有的filter 用来后续切换filter
                    currentContent:{},//当前filter内容
                    currentConfig:{}, //是config ，仅仅是当前的页面UI的配置，不是完整的content
                    currentId:''
                 },
                fieldSortObj:{},
                selectList:[
                    {label:"In", i18n:'SEComment.in',value:'in'},
                    {label:"Not In",i18n:'SEComment.notIn',value:'notIn'},
                    {label:"Is Blank", i18n:'SEComment.isBlank',value:'blank'},
                    {label:"Is Not Blank", i18n:'SEComment.isNotBlank',value:'notBlank'},
                    {label:"＝", i18n:'SEComment.eq',value:'eq'},
                    {label:"≠",i18n:'SEComment.notEq',value:'notEq'},
                ],
                pickerOptions: {
                    shortcuts: [{
                        text: this.getPickerText('lastWeek'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.getPickerText('lastMonth'),
                        onClick(picker) {
                            const end = new Date();
                            const start = moment(new Date(new Date().setMonth(new Date().getMonth() - 1))).toDate();
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: this.getPickerText('lastHalfYear'),
                        onClick(picker) {
                            const end = new Date();
                            const start = moment(new Date(new Date().setMonth(new Date().getMonth() - 6))).toDate();
                            picker.$emit('pick', [start, end]);
                        }
                    },{
                        text: this.getPickerText('lastYear'),
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setFullYear(start.getFullYear() - 1);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                }
            }
        },
        methods: {
            getTableHeaderLabel(column){
                let newLabel = column.label || '';
                try{
                    newLabel =  newLabel.length > 20 ? (newLabel.substr(0,18)+'...') : newLabel;
                }catch (e) {
                    console.log(e);
                }
                return newLabel;
            },
            changeLan(lan){
                this.tableLan = lan;
                this.tableOption.lan = lan;
                this.tableOption.column.forEach(col=>{
                    let {languageLab} = col;
                    col.label = languageLab[lan] || col.label;
                })
            },
            //初始化入口
            async init(){
                await this.initTableFilterConfig();
                await this.initColumnSelectOption();
                if(this.filterContentObj.currentId){
                   await this.changeFilter(this.filterContentObj.currentId);
                }else {
                    await this.initQueryModel();
                }
                this.doResize();
                //一定要config 都加载完成之后，再将table显示出来
                this.showTableFlag = true;
            },
            async initTableFilterConfig(){
                //加载filter的保存结果
                let hideColumn = [];
                let filterColumns = {};
                await api.filterQuery().then(res=>{
                    let {data} = res.data;
                    if(!data || data.length==0){
                        return;
                    }
                    this.filterContentObj.filterList = data;
                    let defaultData = data.filter(da=>da.defaultFlag-0==1);
                    if((!defaultData || defaultData.length==0 ) && this.option.filterEvent-0!=1){
                        return;
                    }

                    let {filterId} = this.option;
                    if(this.option.filterEvent-0==1 && (!filterId || filterId==null || filterId==undefined || filterId=='undefined' || filterId=='null' )){
                        return;
                    }
                    if(filterId){
                        defaultData = data.filter(da=>da.id == filterId);
                    }
                    if(!defaultData || defaultData.length==0){
                        return;
                    }
                    defaultData = defaultData[0];
                    let {content,id} = defaultData;
                    let filterContent = JSON.parse(content);

                    if(this.option.filterEvent-0==1){
                        this.filterContentObj.currentId = filterId;
                    }else{
                        //处理默认filter
                        this.filterContentObj.currentConfig = defaultData;
                        this.filterContentObj.currentContent = filterContent;
                        this.filterContentObj.currentId = id;
                    }
                    let {columns} = filterContent;
                    columns.forEach(col=>{
                        let {prop,seq} = col;
                        filterColumns[prop] = seq;
                    })
                    hideColumn = (columns || []).filter(col=>col.hide).map(col=>col.prop);
                }).catch(err=>{
                    console.log("filterQUery err",err)
                })

                //渲染页面的filter、table的column配置
                let param = {
                    productLineCode:this.option.productLineCode || 'all'
                }
                await api.dimensionQuery(param).then(async (res)=>{
                    let {data} = res.data;
                    this.configData = data;
                    if(!data){
                        return;
                    }
                    let {dffList,filterList,listList} = data;
                    dffList = dffList || [];
                    dffList.forEach((dff,dffIndex)=>{
                        let {fieldType,options,fieldSeq} = dff;
                        if(!fieldType){
                            dff.fieldType = 'text'
                        }
                        if(fieldSeq=='null' || !fieldSeq || fieldSeq=='undefined'){
                            fieldSeq = 999;
                        }
                        let seq = fieldSeq-0+100;
                        let baseOP = {
                            clearable:true,
                            minWidth:220,
                            section:'DFF',
                            languageLab:{
                                en:'',
                                cn:''
                            },
                            seq
                        }
                        if(!options){
                           options = baseOP;
                        }else{
                            options = Object.assign({},baseOP, JSON.parse(options))
                        }
                        this.tempFnFormTargetDffFieldSelect(dff,options);
                        dff.options = JSON.stringify(options);
                    })
                    this.filterConfig = [...filterList,...dffList] || [];
                    //组装动态表格column
                    let col = [];
                    let fieldSortObj = {};
                    ([...listList,...dffList] || []).forEach(li=>{
                        let {displayName,fieldCode,fieldType,fieldSeq,options} = li;
                        try {
                            options = JSON.parse(options)
                            fieldSeq = options.seq || fieldSeq;
                        }catch (e) {
                            options = {}
                        }
                        //根据db 保存的结果，进行页面的col渲染
                        let hide = (hideColumn ||[]).includes(fieldCode)
                        let seq = filterColumns[fieldCode]-0>-1 ? (filterColumns[fieldCode]-0) : (fieldSeq-0);
                        //处理多语言展示label
                        let displayNameLan = displayName;
                        if(this.tableOption.lan=='cn'){
                            let {languageLab} = options;
                            if(languageLab && languageLab.cn){
                                displayNameLan = languageLab.cn;
                            }
                        }
                        let baseCol = Object.assign({}, {
                            "prop": fieldCode,
                            "label": displayNameLan,
                            fieldType
                        },options,{hide,seq});
                        col.push(baseCol);
                        fieldSortObj[fieldCode] = null;
                        //查询下拉框
                        if("select"==fieldType){
                            if(baseCol.dicData && baseCol.dicData.length>0 ){
                                let firstObj = baseCol.dicData[0];
                                if(firstObj.l && firstObj.v){
                                    let {dicPre} = baseCol;
                                    baseCol.dicData.forEach(obj=>{
                                        obj['value'] = obj.v;
                                        obj['label'] = dicPre? (dicPre+'.'+obj.l):obj.l;
                                    })
                                }
                            }
                            this.tableHeaderSelectList[fieldCode] = baseCol.dicData || [];
                        }
                        this.$set(this.queryModel.dynamicForm,fieldCode,{
                            condition:'in',
                            conditionValue:''
                        }) ;
                        this.$set(this.dynamicForm,fieldCode,{
                            condition:'in',
                            conditionValue:''
                        })
                    })
                    col.sort((c1,c2)=>c1.seq-c2.seq);
                    this.tableOption.column = col;
                    //额外补充的配置，对应trfStatusName，原因是需要用status进行排序，不用name排序
                    fieldSortObj['trf_header_trfStatus']=null;
                    this.fieldSortObj = fieldSortObj;
                }).catch(err=>{
                    console.log("dimensionQuery err",err)
                })

            },
            tempFnFormTargetDffFieldSelect(dff,options){
                if(this.option.customerGroupCode !="CG0000219"){
                    return
                }
                let fieldForSelect = ['dff_producttype','dff_productionstage','dff_countryoforigin','dff_countryofdestination'];
                let {fieldCode} = dff;
                fieldCode = (fieldCode||'').toLowerCase()
                if(!fieldForSelect.includes(fieldCode)){
                    return;
                }
                let productTypeDicData =[
                    {label:"Non Retail",value:"Non Retail"},
                    {label:"Retest",value:"Retest"},
                    {label:"Store Order",value:"Store Order"},
                    {label:"Target.com",value:"Target.com"},
                    {label:"Test Order",value:"Test Order"},
                    {label:"Compare to Program",value:"Compare to Program"},
                    {label:"Program Quantity-5000 units",value:"Program Quantity-5000 units"},
                    {label:"Special Request",value:"Special Request"},
                    {label:"Quotation Prior to Initiation",value:"Quotation Prior to Initiation"},
                    {label:"Component Testing (Flame Retardant/Flammability)",value:"Component Testing (Flame Retardant/Flammability)"},
                    {label:"DVS-Mass Production",value:"DVS-Mass Production"},
                    {label:"DVS-Made to Order",value:"DVS-Made to Order"},
                    {label:"Initial",value:"Initial"},
                    {label:"Recertification (Existing Target Style)",value:"Recertification (Existing Target Style)"}
                ]
                let productionStageDicData = [
                    {label:"ECR Full",value:"ECR Full"},
                    {label:"ECR Partial",value:"ECR Partial"},
                    {label:"PPT",value:"PPT"},
                    {label:"TOP",value:"TOP"},
                    {label:"TOP Partial",value:"TOP Partial"},
                    {label:"ORT 3",value:"ORT 3"},
                    {label:"ORT 6",value:"ORT 6"},
                    {label:"ORT 12",value:"ORT 12"},
                    {label:"ORT Partial",value:"ORT Partial"},
                    {label:"CST",value:"CST"},
                    {label:"CUT",value:"CUT"},
                    {label:"Special Request",value:"Special Request"},
                    {label:"Document Review",value:"Document Review"}];

                dff.fieldType = 'select';
                options['multiple'] = true;
                /*if(fieldCode=='dff_producttype'){
                    options['dicData']=  productTypeDicData;
                }
                if(fieldCode=='dff_productionstage'){
                    options['dicData']=  productionStageDicData;
                }*/
                //处理api调用的
                if(fieldCode=='dff_countryoforigin' || fieldCode=='dff_countryofdestination'){
                    let op ={
                        "remote":false,
                        "remoteLabelCode":"code",
                        "remoteValueCode":"value",
                        "remoteUrl":"/api/sgsapi/FrameWorkApi/trims/api/v3/queryCountryAndRegion?1=1&specialItem=- ",
                        "remoteReqType":"get",
                        "resultCodes":"parentData"
                    }
                    options = Object.assign(options,op);
                }
            },
            async initColumnSelectOption(){
                for (const column of this.tableOption.column) {
                    let {fieldType,remoteUrl,remoteReqType,remoteParams,
                        resultCodes,remoteLabelCode,remoteValueCode,prop} = column;
                    //如果option中含有查询接口，需要调用接口获取远程数据
                    if('select'!=fieldType){
                        continue;
                    }
                    let url = remoteUrl;
                    if(!url){
                        continue;
                    }
                    let reqType =  remoteReqType ;
                    if(!reqType){
                        continue;
                    }
                    let params  = remoteParams || {};
                    let {pageParam} = params;
                    if(pageParam){
                        let value = this[pageParam] || this.tableOption[pageParam];
                        let extParam = {};
                        extParam[pageParam] = value;
                        delete params['pageParam'];
                        params = Object.assign({},params,extParam);
                    }
                    let resultParseCodes = resultCodes || ['list'];
                    let remoteRes = null;
                    switch (reqType) {
                        case 'get':
                            await commonTableApi.doRemoteGet(remoteUrl,params).then(res=>{
                                remoteRes = res;
                            }).catch(err=>{})
                            break;
                        case 'post':
                            await commonTableApi.doRemotePost(remoteUrl,params).then(res=>{
                                remoteRes = res;
                            }).catch(err=>{})
                            break;
                        case 'postData':
                            await commonTableApi.doRemoteFormData(remoteUrl,params).then(res=>{
                                remoteRes = res;
                            }).catch(err=>{
                                console.log("post data err",err)
                            })
                            break;
                    }
                    if(remoteRes){
                        if(remoteRes.status==200 && remoteRes.data){
                            let resultData = remoteRes.data;
                            if(resultParseCodes!='parentData'){
                                resultParseCodes.forEach(code=>{
                                    resultData = resultData[code];
                                })
                            }
                            let dataList = [];
                            (resultData || []).forEach(re=>{
                                let label =  re[remoteLabelCode || 'label'];
                                let value =  re[remoteValueCode || 'value'];
                                dataList.push({label,value})
                            })
                            column.dataList = dataList;
                            this.tableHeaderSelectList[prop] = dataList;
                        }
                    }
                }
            },
            //初始化查询条件对象，方便后面使用的地方可以直接双向绑定
            async initQueryModel(useConfigFilter=false){
                //如果DB中已经存在queryModel 则直接使用Db的
                let {filter} = this.filterContentObj.currentContent;
                if(filter && useConfigFilter){
                    this.createdDynamicCondition(filter);
                    return
                }
                //db不存在，则初始化一个
                let {page} = this.pageQueryParam;
                if(page && page.rows){
                    this.page.rows = page.rows;
                }
                let {dffList,filterList,listList} = this.configData || {};
                ([...listList,...dffList] || []).forEach(l=>{
                    let {fieldCode} = l;
                    //目前页面传值，特殊处理一下
                    let {condition,conditionValue} = this.handlerPageParam(fieldCode);
                    this.$set(this.queryModel.dynamicForm,fieldCode,{
                        condition,
                        conditionValue
                    });
                    this.$set(this.dynamicForm,fieldCode,{
                        condition,
                        conditionValue
                    });
                })
                this.$set(this.queryModel.general,'showCollapseFilter',false);
                this.queryModel.general = Object.assign({},this.queryModel.general,this.pageQueryParam.general);
                this.tabsDynamicForm = JSON.parse(JSON.stringify(this.dynamicForm));
                if(this.pageQueryParam.sortBy){
                    let {sortBy,sortOrder} = this.pageQueryParam;
                    this.queryModel.sortBy = sortBy+'';
                    this.queryModel.sortOrder = sortOrder+'';
                    this.fieldSortObj[sortBy] = sortOrder =='Asc';
                }
                this.$forceUpdate();
            },
            createdDynamicCondition(filterConfig){
                let {dynamicForm,general, sortBy,sortOrder} = filterConfig;
                //如果filter里面不存在配置的字段，那么就新增一个结构
                let {startDate,endDate,
                    filterDate,
                    queryValue,
                    roleFilter,
                    isSubmissionTimeout,
                    currentDate} = general;
                this.queryModel.general = {
                    startDate,endDate,
                    filterDate,
                    queryValue,
                    roleFilter,
                    isSubmissionTimeout,
                    currentDate
                };
                this.queryModel.sortBy = sortBy || '';
                this.queryModel.sortOrder = sortOrder || '';
                if(sortBy){
                    this.fieldSortObj[sortBy] = sortOrder =='Asc';
                }
                //开始处理dynamicForm 两个对象都要处理
                let filterPropKeys = Object.keys(dynamicForm);
                let columnProps = this.tableOption.column.map(c=>c.prop);
                //找出column有，但是filter不存在的数据
                let differenceProps = (columnProps || []).filter(prop=>!filterPropKeys.includes(prop));
                this.$set(this,'dynamicForm',dynamicForm);
                this.$set(this,'tabsDynamicForm',JSON.parse(JSON.stringify(dynamicForm)));
                this.$set(this.queryModel,'dynamicForm',JSON.parse(JSON.stringify(dynamicForm)));
                differenceProps.forEach(diffProp=>{
                    let obj = {
                        condition:'in',
                        conditionValue:''
                    }
                    this.$set(this.dynamicForm,diffProp,obj);
                    this.$set(this.queryModel.dynamicForm,diffProp,{
                        condition:'in',
                        conditionValue:''
                    });
                })
            },
            handlerPageParam(fieldCode){
                let copyPageQueryForm = JSON.parse(JSON.stringify(this.pageQueryParam.dynamicForm));
                let {condition,conditionValue} = copyPageQueryForm[fieldCode] || {};
                if(fieldCode == 'report_conclusion_customerConclusion'){
                    let {report_conclusion_customerConclusion} = this.pageQueryParam.dynamicForm;
                    if(report_conclusion_customerConclusion && report_conclusion_customerConclusion.condition && report_conclusion_customerConclusion.conditionValue){
                        condition = report_conclusion_customerConclusion.condition;
                        conditionValue = report_conclusion_customerConclusion.conditionValue;
                    }
                }
                if(fieldCode == 'trf_header_trfStatusName'){
                    let {trf_header_trfStatusName} = this.pageQueryParam.dynamicForm;
                    if(trf_header_trfStatusName && trf_header_trfStatusName.condition && trf_header_trfStatusName.conditionValue){
                        conditionValue = trf_header_trfStatusName.conditionValue;
                    }
                }
                condition=condition?condition:'in';
                conditionValue=conditionValue?conditionValue:'';
                return {condition,conditionValue}
            },
            queryDataAndCaseSelect(col){
                let {prop,cascade} = col;
                //如果存在级联，就触发级联查询
                this.queryData(prop);
                if(!cascade){
                    return;
                }
                let {field,url,paramName,label,code,result,reqType} = cascade;
                try{
                    //是否存在对应的级联表头
                    let cascadeProp =  this.tableOption.column.filter(c=>c.prop==field);
                    if(!cascadeProp || cascadeProp.length==0){
                        return;
                    }
                    // 获取查询的值
                    let {conditionValue} = this.dynamicForm[prop];
                    //将对应的级联下拉框置为[] 对应的对象也清空
                    this.tableHeaderSelectList[field].length = 0;
                    this.$set(this.dynamicForm[field],'conditionValue','');
                    this.$set(this.queryModel.dynamicForm[field],'conditionValue','');
                    this.tableOption.column.forEach(col=>{
                        if(col.prop==field){
                            this.$set(col,'dataList',[])
                        }
                    })
                    if(!conditionValue || conditionValue.length==0){
                        return;
                    }
                    //构建查询参数
                    if(!url){
                        return;
                    }
                    if(!reqType){
                        return;
                    }
                    let param = {}
                    param[paramName] = conditionValue;
                    let resultParseCodes = result || ['list'];
                    switch (reqType) {
                        case 'get':
                            url = encodeURI(url + `?${paramName}=${conditionValue}`);
                            commonTableApi.doRemoteGet(url,param).then(res=>{
                                this.handlerRemoteRes(res,resultParseCodes,label,code,field);
                            }).catch(err=>{})
                            break;
                        case 'post':
                            commonTableApi.doRemotePost(url,param).then(res=>{
                                this.handlerRemoteRes(res,resultParseCodes,label,code,field);
                            }).catch(err=>{})
                            break;
                    }
                }catch (e) {
                    //级联数据可以查询不出，但是不能影响当前的table查询
                    console.log("case query err",e);
                }
            },
            handlerRemoteRes(remoteRes,resultParseCodes,remoteLabel,remoteCode,field){
                if(remoteRes.status==200 && remoteRes.data){
                    let resultData = remoteRes.data;
                    if(resultParseCodes!='parentData'){
                        resultParseCodes.forEach(code=>{
                            resultData = resultData[code];
                        })
                    }
                    let dataList = [];
                    (resultData || []).forEach(re=>{
                        let label =  re[remoteLabel || 'label'];
                        let value =  re[remoteCode || 'value'];
                        dataList.push({label,value})
                    })
                    let scrollLeft =  this.$refs.table.bodyWrapper.scrollLeft;
                    this.showTableListFlag = false;
                    this.$nextTick(()=>{
                        this.showTableListFlag = true;
                        this.tableOption.column.forEach(col=>{
                            if(col.prop == field){
                                this.$set(col,'dataList',dataList)
                                dataList.forEach(da=>{
                                    this.tableHeaderSelectList[field].push(da);
                                })
                            }
                        })
                        this.tableDataLoading = true;
                        setTimeout(()=>{
                            this.$refs.table.bodyWrapper.scrollLeft = scrollLeft;
                        },200)
                        setTimeout(()=>{
                            this.tableDataLoading = false;
                        },500)
                    })
                }
            },
            queryData(prop){

                let {conditionValue,condition} = this.dynamicForm[prop];
                if(['notBlank','blank'].includes(condition)){
                    conditionValue = '';
                    this.$set(this.dynamicForm[prop],'conditionValue','');
                }
                this.queryModel.dynamicForm = JSON.parse(JSON.stringify(this.dynamicForm));
                this.page.page=1;
                this.tabsDynamicForm = JSON.parse(JSON.stringify(this.dynamicForm));
                this.initTable();
            },
            async loadExportData(){
                return new Promise((resolve, reject) => {
                    //这里是查询数据了，
                    let pageNo = 1;
                    let pageSize = 3000;
                    let {dynamicForm,general,sortBy,sortOrder} = this.translateParameterForDate();
                    let param = Object.assign({},{dynamicForm,general,sortBy,sortOrder},{pageSize,pageNo})
                    let exportTableData = [];
                    api.query(param).then(res=>{
                        if(res.status==200){
                            let {data} = res.data;
                            let {content,totalElements} = data;
                            exportTableData = content;
                        }
                        resolve(exportTableData);
                    },error=>{
                        resolve([]);
                        this.$notify.error(error)
                    }).catch(err=>{
                        resolve([]);
                    })
                })
            },
            loadSuccess(){
                this.showTableListFlag = true;
                this.initTable();
            },
            generalFilter(){
                this.page.page=1;
                this.initTable();
            },
            /*本方法不修改原本的queryModel，不影响filter，仅在query之前做一些特殊处理
             * */
            translateParameterForDate(){
                let copyQueryModle = JSON.parse(JSON.stringify(this.queryModel))
                let {dynamicForm,general} = copyQueryModle;
                let { startDate,endDate} = general;
                general.productLineCode =  this.option.productLineCode || 'all';
                if(startDate && endDate){
                    general.startDate = startDate+' 00:00:00';
                    general.endDate = endDate+' 23:59:59';
                }
                let keys = Object.keys(dynamicForm);
                (keys || []).forEach(p=>{
                    let col = this.tableOption.column.find(c=>c.prop==p);
                    //目的是兼容filter老版本
                    if(!col){
                        return;
                    }
                    if(col.fieldType=='date'){
                        let {conditionValue} = dynamicForm[p];
                        if(conditionValue && conditionValue.length==2){
                            let startDate = conditionValue[0]+' 00:00:00';
                            let endDate = conditionValue[1]+' 23:59:59';
                            dynamicForm[p].conditionValue = [startDate,endDate];
                        }
                    }
                    let {searchField} = col;
                    //console.log("存在searchField",searchField)
                    //修改为目标查询字段
                    if(searchField){
                        dynamicForm[searchField] = JSON.parse(JSON.stringify(dynamicForm[p]));
                        dynamicForm[p].condition = "in";
                        dynamicForm[p].conditionValue = "";
                    }
                })
                return copyQueryModle;
            },
            initTable() {
                //这里是查询数据了，
                let pageNo = this.page.page;
                let pageSize = this.page.rows;
                //为了方便后端时区处理，所有的date类型的查询条件都加上时分秒
                let {dynamicForm,general,sortBy,sortOrder} = this.translateParameterForDate();
                let param = Object.assign({},{dynamicForm,general, sortBy,sortOrder},{pageSize,pageNo})
                this.tableDataLoading = true;
                this.tableData = [];
                api.query(param).then(res=>{
                    this.tableDataLoading = false;
                    if(res.status==200){
                        let {data} = res.data;
                        let {content,totalElements} = data;
                        this.tableData = content;
                        this.page.total = totalElements;
                    }
                },error=>{
                    this.tableDataLoading = false;
                    console.log("查询返回结果 error：",error);
                    //this.$notify.error(error)
                }).catch(err=>{
                    this.tableDataLoading = false;
                    console.log("查询返回结果 err：",err);
                })
            },
            getTableColClass({row,column,rowIndex,columnIndex}){
                return 'es_table_col'+column.property;
            },
            sortByDefaultColumn(prop,sortOrder){
                this.queryModel.sortBy = prop;
                this.queryModel.sortOrder = sortOrder;
                this.page.page = 1;
                this.initTable();
            },
            sortByColumn(prop){
                for(let k in this.fieldSortObj){
                    if(prop==k){
                        continue;
                    }
                    this.$set(this.fieldSortObj,k,null);
                }
                this.queryModel.sortBy = prop;
                this.queryModel.sortOrder = this.fieldSortObj[prop]?'Asc':'Desc';
                this.$refs['sort_popover_'+prop].forEach(ref=>{
                    try{
                        ref.handleBlur();
                    }catch (e){
                        console.log("e",e)
                    }
                })
                this.initTable();
            },
            clearSort(){
                this.$set(this.fieldSortObj,this.queryModel.sortBy,null);
                this.$set(this.queryModel,'sortBy','');
                this.$set(this.queryModel,'sortOrder','');
                this.initTable();
            },
            cleanSignFilter(prop){
                this.$set(this.queryModel.dynamicForm[prop],'conditionValue','');
                this.$set(this.queryModel.dynamicForm[prop],'condition','in');
                this.$set(this.dynamicForm[prop],'conditionValue','');
                this.$set(this.dynamicForm[prop],'condition','in');
                this.queryData(prop);
            },
            delFilterConfig(id){
                this.filterContentObj.filterList = this.filterContentObj.filterList.filter(f=>f.id!=id);
                //console.log("删除的id和当前的id",id,this.filterContentObj.currentId);
                if(this.filterContentObj.currentId == id){
                    this.filterContentObj.currentConfig = {};
                    this.filterContentObj.currentId = '';
                    this.resetQueryModelForm();
                    this.handlerUrlParam();
                    window.location.reload();
                }
            },
            updateFilterDefault(id){
                this.filterContentObj.filterList.forEach(f=>{
                    f.defaultFlag = f.id == id?1:0;
                })
            },
            updFilterConfig(config){
                let ind = -1;
                this.filterContentObj.filterList.forEach((f,index)=>{
                    if(f.id == config.id){
                        ind = index;
                    }
                })
                if(ind>-1){
                    this.filterContentObj.filterList.splice(ind,1,config);
                    this.filterContentObj.currentConfig = config;
                }
            },
            addFilterConfig(config){
                this.filterContentObj.filterList.push(config);
                this.filterContentObj.currentConfig = config;
                this.filterContentObj.currentId = config.id;
                this.handlerUrlParam();
            },
            async changeFilter(rowId){
                let changeFilter = this.filterContentObj.filterList.find(f=>f.id==rowId);
                let {content,id} = changeFilter;

                this.filterContentObj.currentConfig = changeFilter;
                this.filterContentObj.currentContent = content ? JSON.parse(content) : {};
                this.filterContentObj.currentId = id ;

                //开始改变UI
                this.showTableFlag = false;
                let {columns} = this.filterContentObj.currentContent;
                let hideColumn = (columns || []).filter(col=>col.hide).map(col=>col.prop);
                let columnSeq = {};
                (columns || []).forEach(col=>{
                    let {prop,seq} = col;
                    columnSeq[prop] = seq;
                })

                this.tableOption.column.forEach((col,index)=>{
                    let {prop} = col;
                    col.hide = hideColumn.includes(prop);
                    col.seq = columnSeq[prop] || index;
                })
                this.tableOption.column.sort((c1,c2)=>c1.seq-c2.seq);
                await this.initQueryModel(true);
                this.$nextTick(()=>{
                    this.handlerUrlParam();
                    //这里会触发数据重新加载
                    this.showTableFlag = true;
                })
            },
            getColumnData(row,column){
                let {prop,fieldType} = column;
                let val = row[prop];
                if(fieldType=='date' && val){
                    try{
                        val = moment(val).format("YYYY-MM-DD");
                    }catch (e) {}
                }
                //console.log("columnData,row",row,prop,fieldType);
                return val;
            },
            getDicLabel(dicData, value) {
                let dic = dicData.filter((dic) => {
                    return dic.value == value;
                });
                if (dic.length > 0) {
                    return this.$t(dic[0].label);
                } else {
                    return value;
                }
            },
            // 切换当前页
            currentChange(currentPage) {
                //this.page.page = currentPage;
                this.initTable();
            },
            // 调整每页显示行数
            sizeChange(pageSize) {
                //this.page.rows = pageSize;
                this.initTable();
            },
            /* 抽屉控制字段显示与否开始 */
            openDraw(){
                this.drawColumns = JSON.parse(JSON.stringify(this.tableOption.column || []));
                this.checkedColumn = (this.tableOption.column || []).filter(col=>!col.hide).map(col=>col.prop);
                this.configColumnShow = true;
            },
            /* 不用save抽屉时进行保存了，这段代码先留作参考 */
            handleCheckAllChange(val) {
                this.showTableFlag = false;
               //TODO
                this.showTableFlag = true;
                this.$nextTick(() => {
                    //在数据加载完，重新渲染表格
                    this.$refs['table'].doLayout();
                });
            },
            resetQueryModelForm(){
                //general的固定字段，手动清空，Dynamic的需要动态清空
                this.$set(this.queryModel.general,'startDate','');
                this.$set(this.queryModel.general,'endDate','');
                this.$set(this.queryModel.general,'filterDate',[]);
                this.$set(this.queryModel.general,'queryValue','');
                this.$set(this.queryModel.general,'isSubmissionTimeout',0);
                this.$set(this.queryModel.general,'currentDate',-1);
                let roleFilter = {'applicant':1,'buyer':1,'agent':1,'supplier':1,'manufacturer':1};
                this.$set(this.queryModel.general,'roleFilter',roleFilter);
                this.$set(this.queryModel,'sortBy','');
                this.$set(this.queryModel,'sortOrder','');
                this.$set(this,'modifyQueryDate',false);
                let dkeys = Object.keys(this.dynamicForm)
                dkeys.forEach(key=>{
                    let condition='in';
                    let conditionValue='';
                    this.$set(this.dynamicForm,key, {condition,conditionValue})
                    this.$set(this.tabsDynamicForm,key, {condition,conditionValue})
                })

                let keys = Object.keys(this.queryModel.dynamicForm)
                keys.forEach(key=>{
                    let condition='in';
                    let conditionValue='';
                    this.$set(this.queryModel.dynamicForm,key, {condition,conditionValue})
                })
                //清空filter列表
                this.$set(this.filterContentObj,'currentContent',{});
                this.$set(this.filterContentObj,'currentConfig',{});
                this.$set(this.filterContentObj,'currentId','');
                //还原column的显示
                this.tableOption.column.forEach((col,index)=>{
                    col.hide = false;
                })
                //清空默认View
                this.$set(this.option,'filterId','');
                this.$refs.table_header_general_filter.clearShortCutClass();
                this.initTable();
            },
            /*
            * 字段显示与否，通过修改column的hide 来控制显示不显示，
            * 在没有save filter之前，只是临时的，但是需要联动filter里面对应的查询条件
            *  */
            updateColShow(datas,checkedColumn,roleFilter){
                this.$set(this.tableOption,'column',datas);
                this.tableOption.column.forEach(col=>{
                    let {prop} = col;
                    col.hide = !(checkedColumn ||[]).includes(prop);
                })
                this.configColumnShow = false;
                this.showTableListFlag = false;
                this.$nextTick(() => {
                    //在数据加载完，重新渲染表格拖动
                    //this.Drop_create();
                    this.$set(this.queryModel.general,'roleFilter',roleFilter);
                    this.page.page = 1;
                    this.handlerUrlParam();
                    this.showTableListFlag = true;
                    //触发查询
                    this.initTable();
                });
            },
            /* 抽屉控制字段显示与否结束 */
            handlerResize:_debounce(function(){vm.doResize()},500),
            doResize() {
                let clientHeight = document.documentElement.clientHeight;
                clientHeight = clientHeight<700?700:clientHeight;
                this.tableMaxHeight = clientHeight - 430;
            },
            /* 获取pick的i18n文本*/
            getPickerText(val){
                if('lastWeek'==val){
                    return this.$t('datePicker.lastWeek')
                }
                if('lastMonth'==val){
                    return this.$t('datePicker.lastMonth')
                }
                if('lastHalfYear'==val){
                    return this.$t('datePicker.lastHalfYear')
                }
                if('lastYear'==val){
                    return this.$t('datePicker.lastYear')
                }
            },

            /* 拖动功能开始  */
           handlerDrop(oldIndex,newIndex){
                //2. 表头拖动得到时候，联动tr下的td变换位置
                let column = this.tableOption.column;
                let targetCol = column.splice(oldIndex, 1)[0]
                column.splice(newIndex, 0, targetCol);
                column.forEach((col,index)=>{
                   col.seq = index;
                })
                this.tableOption.column = column;
                if(oldIndex!=newIndex){
                    this.handlerDropTrTd(oldIndex,newIndex);
                }
            },
            handlerDropTrTd(oldIndex,newIndex){
                /*TIPS:手动替换html dom ，不使用avue源码中的修改el-table的key，
                 直接修改key会造成页面闪烁，
                 同时整个页面的滚动条会自动回滚到上面
                 （因为key变化，table要重新渲染，会先销毁，然后重建，整个dom块的高度就变成了0再变成完全数据高度）
                */
                //1. 获取排序之后的header的col
                let eleTableHeaderTrTh = 'div.el-table__header-wrapper table thead tr th';
                let colClassList = [];
                let headerThs = this.$refs.table.$el.querySelectorAll(eleTableHeaderTrTh);
                (headerThs || []).forEach(th=>{
                    let classList = th.classList;
                    classList.forEach(cl=>{
                        if(cl.indexOf('es_table_col')>-1){
                            //按顺序放入array中
                            colClassList.push(cl);
                        }
                    })
                })

                //数据层面的tr td
                let eleTableTrClass = 'div.el-table__body-wrapper table.el-table__body tr';
                let trs = this.$refs.table.$el.querySelectorAll(eleTableTrClass);
                let trsTds = [];
                (trs || []).forEach(tr=>{
                    let tds = tr.querySelectorAll('td');
                    let tdObj = {};
                    //remove 一行数据
                    tds.forEach(td=>{
                        let classList = td.classList;
                        classList.forEach(cl=>{
                            if(cl.indexOf('es_table_col')>-1){
                                tdObj[cl] = td;
                            }
                        })

                        tr.removeChild(td);
                    })
                    //再回填一行数据
                    colClassList.forEach(col=>{
                        tr.appendChild(tdObj[col]);
                    })
                });
            },
            Drop_create(){
                let interVal =  setInterval(()=>{
                    let el =  this.getDropColumnEl();
                    if(!el){
                        return;
                    }
                    clearInterval(interVal);
                    this.Drop_EventInit();
                },100)
            },
            Drop_EventInit() {
                let el =  this.getDropColumnEl();
                let noIndexCount = 0;
                ['selection', 'index', 'expand'].forEach(ele => {
                    if (this.tableOption[ele]) { noIndexCount += 1 }
                })
                //注册拖动事件
                this.Drop_registry('column',el,evt=>{
                    let oldIndex = evt.oldIndex - noIndexCount;
                    let newIndex = evt.newIndex - noIndexCount;
                    //拖动事件触发
                    this.handlerDrop(oldIndex,newIndex);
                })
            },
            Drop_registry (type, el, callback) {
                //1. 注册拖动事件
                if (!Sortable) {
                    return
                }
                Sortable.create(el, {
                    animation: 500,
                    delay: 0,
                    onEnd: evt => callback(evt)
                })
            },
            getDropColumnEl(){
                let dropColClass = '.el-table__header-wrapper tr';
                if(!this.$refs.table){
                    return null;
                }
                let el =  this.$refs.table.$el.querySelector(dropColClass);
                return el;
            },
            /* 拖动功能结束  */
            conditionInputDisabled(columnProp){
                let {condition} = this.dynamicForm[columnProp];
                return ['notBlank','blank'].includes(condition);
            },
            getSelectList(fieldType){
                if(fieldType=='select'){
                    return [
                        {label:"In", i18n:'SEComment.in',value:'in'},
                        {label:"Not In",i18n:'SEComment.notIn',value:'notIn'},
                        {label:"Is Blank", i18n:'SEComment.isBlank',value:'blank'},
                        {label:"Is Not Blank", i18n:'SEComment.isNotBlank',value:'notBlank'}
                    ]
                }
                return this.selectList;
            },
            /* 处理查询条件 url*/
            handlerUrlParam(){
                let copyQueryModel = JSON.parse(JSON.stringify(this.queryModel));
                let {general,sortBy,sortOrder,dynamicForm,page} = copyQueryModel;
                let urlQueryModel = {
                    dynamicForm:{},
                    general:{},
                    page:{
                        rows:this.page.rows
                    }
                }
                let hasConditionValue = false;
                if(sortBy){
                    urlQueryModel['sortBy'] = sortBy;
                    urlQueryModel['sortOrder'] = sortOrder;
                }
                let dynaKeys = Object.keys(dynamicForm);
                dynaKeys.forEach((key,ind)=>{
                    let {condition,conditionValue} = dynamicForm[key];
                    if(!['blank','notBlank'].includes(condition) && (!conditionValue || conditionValue.length==0)){
                        hasConditionValue ? '' : (hasConditionValue = false);
                        return;
                    }
                    hasConditionValue = true;
                    urlQueryModel.dynamicForm[key] =  dynamicForm[key];
                });
                let generalKeys = Object.keys(general);
                generalKeys.forEach((key,ind)=>{
                    let value = general[key];
                    if(!value || value.length==0){
                        return;
                    }
                    //如果为true 则代表时间被修改过，是第二次加载 false代表第一次加载
                    if(!this.option.filterable && ['queryValue','isSubmissionTimeout','currentDate'].includes(key)){
                        //只要queryValue,isSubmissionTime,currentDate 是默认值，则代表没有被修改过
                        hasConditionValue = general[key] != this.queryModel.general[key];
                    }
                    urlQueryModel.general[key] =  general[key];
                });
                //没有修改过查询条件，不执行地址替换
                //console.log("Commontable 是否修改过查询条件---", hasConditionValue,modifyQueryDate)
                let modifyQueryDate = this.modifyQueryDate || this.option.filterable;
                if(!hasConditionValue && !modifyQueryDate){
                    return;
                }
                const url = new URL(window.location.href);
                const params = new URLSearchParams();
                // 如果参数已存在，则更新其值；否则，添加新参数
                params.set('queryModel', JSON.stringify(urlQueryModel));
                params.set('modifyQueryDate', modifyQueryDate);
                params.set('filterId',  this.filterContentObj.currentId || this.option.filterId);
                //console.log("CommonTable 更新url",url,params)
                // 使用 pushState() 更新 URL，避免页面刷新
                let preUrl = url.origin+"/#/trf/newList";
                const newUrl = `${preUrl}?${params.toString()}`;
                window.history.pushState({ path: newUrl }, '', newUrl);
                //console.log("CommonTable : 修改了URL地址",modifyQueryDate)
            },
        },
        beforeDestroy() {
            window.removeEventListener('resize',this.handlerResize)
        },
        created() {
            vm = this;
            this.tableLan = this.option.lan || 'en';
            this.languageCode = this.option.lan == 'cn' ? 'CHI' : 'EN';
            this.tableOption = Object.assign({},this.tableOption,this.option);
            //组装外部默认查询参数
        },
        mounted() {
            this.init();
            window.addEventListener('resize',this.handlerResize);
        },
        watch: {
            queryModel:{
                immediate:false,
                deep:true,
                handler(newV){
                    //console.log("CommonTable 监听queryModel变动",newV)
                    this.handlerUrlParam();
                }
            },
            'page.rows':{
              immediate:false,
              handler(newV){
                  //console.log("CommonTable 监听page变动",newV)
                  this.handlerUrlParam();
              }
            },
            showTableFlag: {
                immediate:true,
                handler(newVal) {
                    if(newVal){
                        // 设置Table最大高度
                        //this.doResize();
                        //注册拖拽功能
                        //this.Drop_create();
                    }
                },
                deep: true
            },
        },
        computed: {
            computeDataList(){
              return (column)=>{
                  let {prop} = column;
                  return this.tableHeaderSelectList[prop] || column.dicData || column.dataList;
              }
            },
            getTableOptionColumn(){
                let columns = this.tableOption.column.filter(col=>!col.hide);
                columns.sort((a,b)=>a.seq-b.seq);
                return columns;
            }
        },
        props: {
            /*
            * 含有
            * selection：是否开启前端复选框
            * index: 索引序号,
            * fixHeight: 自动高度,
            * column：列对象
            *
            * action：最后的action 操作列 Boolean
            * actionWidth：最后一列的宽度
            * layout: 分页的layout布局
            * bossNumber: 用来查询数据
            * filterEvent: 0正常加载， 1优先加载地址栏查询
            * filterId: ''
            * */
            option: {
                type: Object,
            },
            //加载中
            onLoad: Function,
            pageQueryParam:{
                general:{
                    startDate:'',
                    endDate:'',
                    filterDate:[],
                    queryValue:''
                },
                dynamicForm:{},
                page:{}
            }
        }

    };
</script>

<style lang="scss">
@import "@/styles/unit.scss";

    .es-table-header-sort span.el-radio-button__inner{
        width:100px !important;
    }
    .es-table-header-sort .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        color: #FFFFFF;
        background-color: $primary-color;
        border-color: $primary-color;
        -webkit-box-shadow: -1px 0 0 0 $primary-color;
        box-shadow: -1px 0 0 0 $primary-color;
    }
    .commonTable{
        .sgs_smart_table_column_data,.sgs_smart_table_header_label{
            overflow: hidden !important;
            white-space: nowrap !important;
        }
        .es_table_coltrf_trfNo{
            font-family: 'Arial' !important;
        }

        .el-table.sgs_smart_common_table{
            th{
                height: 50px !important;
                padding: 0 !important;
            }
        }
        .el-col{
            margin-bottom: 0 !important;
        }
        .trf-header-sort-icon{
            &.isSort{
                transform: rotate(180deg);
            }
        }

        .top-title {
            font-size: 24px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #000000;
            line-height: 32px;
            margin: 0px 0 17px;
        }

        .el-icon-arrow-right:before {
            font-weight: 700;
        }

        .trf-o-btn {
            margin-top: -14px;
        }

        .f-sort a {
            float: left;
            padding: 0 9px;
            height: 23px;
            border: 1px solid #CCC;
            line-height: 23px;
            margin-right: -1px;
            background: #FFF;
            color: #333;
        }

        a.curr {
            border-color: #919191;
            background: #919191;
            color: #FFF;
        }

       

        .el-table__fixed-right {
            height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
            bottom: 17px; // 固定列默认设置了定位，    position: absolute;top: 0;left: 0;只需要再设置一下bottom的值，让固定列和父元素的底部出现距离即可
            background: #fff;
        }

        .otherActiveClass {
            color: #FFF;
            background-color: #ebeef5;
        }

        .row-expand-cover {
            .el-table__expand-icon {
                visibility: hidden;
            }
        }

        .otherActiveClass:hover {
            background: #ebeef5;
            color: #FFF;
        }

        .newIcon {
            position: absolute;
            right: 0;
            top: 0;
        }

        .wrap {
            background: #fff;
            padding: 24px 32px;
        }

        .date-group {
            .date-group-wrap{
                button {
                    border: 0;
                    border-bottom: 1px solid transparent;
                    border-radius: 0;
                    padding: 10px 0;
                    margin: 0 10px;
                    color: black;
    
                    &:hover, &:focus {
                        background-color: initial;
                        border-color: transparent;
                        color: #ff6600;
                    }
    
                    &.active {
                        border-bottom-color: #f60;
                        color: #f60;
                    }
                }
            }

            .bg_orange {
                color: #EA4336;
                margin-right: 20px;

                img {
                    margin-top: -7px;
                }
            }
        }

        .btn-sort {
            .active {
                color: #FF6600;
                border-color: #ffd1b3;
            }
        }

        .es-table-common-table {
            .cell {
                white-space: nowrap !important;
            }

            th {
                padding-top: 16px;

                &.is-left {
                    padding-top: 22px;
                }

                .el-input__inner {
                    background: transparent;
                    border-color: #D8D8D8;
                    padding-left: 0;
                    /* color: #000; */
                    &::-webkit-input-placeholder {
                        /* color: transparent; */
                    }
                }

                .date-input {
                    .el-input__inner {
                        padding-left: 24px;
                    }
                }

                .cell {
                    > div {
                        &:last-of-type {
                            margin-top: 8px;
                        }
                    }

                    .el-select .el-input__inner:focus {
                        border-color: #D8D8D8;
                    }

                    .el-select .el-input .el-select__caret {
                        color: #D8D8D8;
                    }

                    .el-input__prefix {
                        left: -5px;
                    }
                }

                &:last-of-type {
                    .cell {
                        margin-top: -49px;
                    }

                    .operatop > span {
                        float: right;
                    }
                }
            }
        }

        #trf-list-filter {
            float: left;
            width: fit-content;
            /* margin-bottom: 24px; */
            .el-form-item {
                margin-bottom: 0;
            }
        }

        .sort-btn {
            float: left;

            button {
                border-color: #ccc;
            }
        }

        #add-trf-btn {
            width: 248px;
            text-align: left;
            margin-left: 24px;
        }

        .add-menu {
            li {
                width: 100%;
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                font-weight: 400;
                color: #000000;
                display: inline-block;
                padding-left: 10px;
                transition: all .2s;

                a {
                    display: block;
                }

                &:hover {
                    background: rgba(255, 102, 0, 0.1);
                    color: #FF6600;
                }
            }
        }

        .plain-black {
            height: 40px;
            padding-top: 0;
            padding-bottom: 0;
        }

        .trf-sort-icon {
            /* transition: .2s all; */
            &.isSort {
                transform: rotate(180deg)
            }
        }

        .tools {
            /* display: flex;
            justify-content: space-between; */
            /*margin-bottom: 24px;*/
        }

        .filter-date {
            width: 260px !important;
            padding-right: 0 !important;
        }

        .check-time-out-2 {
            display: none;
        }

        @media screen and (max-width: 1900px) {
            #trf-list-filter {
                float: left;
            }
            .en-trf-list-filter {
                margin-bottom: 15px;
            }
            .en-sort-btn {
                width: 100%;
            }
        }

        @media screen and (max-width: 1600px) {
            .cn-trf-list-filter {
                margin-bottom: 15px;
            }
            .cn-sort-btn {
                float: left;
            }
        }

        @media screen and (max-width: 1366px) {
            #trf-list-filter .el-input__inner, .filter-date {
                width: 260px !important;
            }
            .en-trf-list-filter .check-time-out-1 {
                /* display: none; */
            }
            .en-sort-btn {
                /* text-align: right; */
                .check-time-out-2 {
                    /* display: inline-block;
                    height: 40px;
                    line-height: 40px; */
                }

                button {
                    /* margin-left: 16px !important; */
                }
            }
            .cn-sort-btn {
                .check-time-out-2 {
                    /* display: none; */
                }
            }
        }
    }
</style>
