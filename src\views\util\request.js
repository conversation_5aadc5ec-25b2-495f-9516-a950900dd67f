import axios from 'axios'
import store from '@/store'
import { getToken } from '@/util/auth'
import NProgress from 'nprogress' // progress bar
import _ from 'lodash'
import '@/assets/nprogress.css'// progress bar style
import Qs from 'qs'
import toastr from '@/util/toastr'

axios.defaults.withCredentials = true

const service = axios.create({
  withCredentials: true,
  baseURL: process.env.VUE_APP_BASE_API, // api的base_url
  timeout: 5000000, // request timeout
  headers: {
    'content-type': 'application/json'
  },
  paramsSerializer: function(params) {
    return Qs.stringify(params, {encodeValuesOnly: true})
  }
})

service.interceptors.request.use(config => {
  NProgress.start()
  if (store.getters.token) {
    config.headers['sgsToken'] = getToken()
    config.headers['sgsLanguage'] = store.getters.language
  }
  // IE 会缓存请求, 加上hash 不会有缓存
 // config.url = config.url + '?hash=' + new Date().getTime()
  return config
}, error => {
  return Promise.reject(error)
})

service.interceptors.response.use(
  response => {
    NProgress.done()
    return response
  },
  /**
  * 下面的注释为通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
  * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
  */
  //  const res = response.data;
  //     if (res.code !== 20000) {
  //       Message({
  //         message: res.message,
  //         type: 'error',
  //         duration: 5 * 1000
  //       });
  //       // 50008:非法的token; 50012:其他客户端登录了;  50014:Token 过期了;
  //       if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
  //         MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
  //           confirmButtonText: '重新登录',
  //           cancelButtonText: '取消',
  //           type: 'warning'
  //         }).then(() => {
  //           store.dispatch('FedLogOut').then(() => {
  //             location.reload();// 为了重新实例化vue-router对象 避免bug
  //           });
  //         })
  //       }
  //       return Promise.reject('error');
  //     } else {
  //       return response.data;
  //     }
  error => {
    NProgress.done()
    let message = error.message
    let status = _.result(error, 'response.status')
    switch (status) {
      case 401:
        message='User is not Login, Please Login'
        break
      default:
        message=_.result(error, 'response.data.message', 'System Error!')
        break
    }
    toastr.error(message)
    return Promise.reject(error)
  })
export default service
