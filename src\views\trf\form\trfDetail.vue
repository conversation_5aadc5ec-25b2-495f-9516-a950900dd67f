<template>
    <div class="trf-detail sgs_smart_trf_trf_detail">
        <div
            v-loading.fullscreen.lock="fullPageLoading"
            :element-loading-text="fullPageLoadingText"
        ></div>
        <!-- <div class="top" style="background: #f60; height: 4px; position: fixed; top: 0; left: 0;"><div class="colors"></div></div> -->
        <el-row :gutter="24">
            <el-col :span="3" class="page-no-print">
                <ul class="nav-list" ref="nav" id="trf-left-nav">
                    <template v-for="(item, index) in navList">
                        <template
                            v-if="item.level == '3-1' && !isCareLabelLoadFlag"
                        ></template>
                        <li
                            :class="{'is-sub': item.isSub}"
                            v-show="item.showStep <= showStep && !item.hide"
                            :key="item.name"
                            class="nav-item"
                            @click="toTarget(index)"
                        >
                            <h5>
                                {{
                                    $t(item.alias)
                                        ? $t(item.alias)
                                        : language == "zh-CN"
                                        ? item.specificNameCn
                                        : item.specificNameEn
                                }}
                            </h5>
                        </li>
                    </template>
                </ul>
            </el-col>
            <el-col :span="17">
                <el-form
                    label-position="top"
                    :model="trf"
                    :rules="trfRules"
                    ref="trf"
                    @submit.native.prevent
                    label-width="260px"
                    size="medium"
                    class="demo-form-inline sgs-form"
                >
                    <!-- 选择模板弹窗 -->
                    <el-dialog
                        :title="$t('trf.changeTemplate')"
                        :modal="false"
                        :visible.sync="templateDialogVisible"
                        width="1200px"
                        :close-on-click-modal="false"
                        :close-on-press-escape="false"
                        @close="handleCloseTemplate"
                    >
                        <el-row :gutter="20" class="template-lable">
                            <el-col :span="12">
                                <el-form-item
                                    :label="$t('buyer.label')"
                                    prop="buyerNo"
                                >
                                    <el-select
                                        v-model="trf.buyerNo"
                                        clearable
                                        filterable
                                        :placeholder="$t('customerGroup.name')"
                                        :disabled="buyerDisabled"
                                        @clear="handleBlur"
                                        @change="selectBuyerCustomerGroupChange"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="(
                                                customerGroup, index
                                            ) in buyerCustomerGroupData"
                                            :key="index"
                                            :label="
                                                customerGroup.customerGroupName
                                            "
                                            :value="
                                                customerGroup.customerGroupCode
                                            "
                                        >
                                            <span
                                                style="float: left"
                                                v-if="!customerGroup.customerGroupCode"
                                            >
                                                <i
                                                    class="icon-all icongongsi"
                                                    style="
                                                        font-size: 20px !important;
                                                    "
                                                ></i>
                                            </span>
                                            <span
                                                style="float: left"
                                                v-if="customerGroup.customerGroupCode"
                                            >
                                                <i
                                                    class="icon-all iconjituangongsi"
                                                    style="
                                                        font-size: 20px !important;
                                                    "
                                                ></i>
                                            </span>
                                            <span style="float: left">
                                                {{
                                                    customerGroup.customerGroupName
                                                }}
                                            </span>
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item :label="$t('agent.label')">
                                    <el-select
                                        v-model="trf.agentCustomerName"
                                        filterable
                                        clearable
                                        :placeholder="
                                            $t('agentCustomerGroup.name')
                                        "
                                        reserve-keyword
                                        remote
                                        allow-create
                                        :remote-method="
                                            searchAgentCustomerGroup
                                        "
                                        @change="selectAgentCustomerGroupChange"
                                        :disabled="trfDisabled"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="(
                                                customerGroup, index
                                            ) in agentCustomerGroupData"
                                            :key="index"
                                            :label="
                                                customerGroup.customerNameEN
                                            "
                                            :value="
                                                customerGroup.customerNameEN
                                            "
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12">
                                <el-form-item
                                    :label="$t('trf.trfTemplate')"
                                    prop="trfTemplateId"
                                >
                                    <el-select
                                        v-model="trf.trfTemplateId"
                                        clearable
                                        filterable
                                        :disabled="templateDisabled"
                                        @change="selectTemplateChange"
                                        @clear="handleBlur"
                                        v-loading="templateLoading"
                                        style="width: 100%"
                                        :placeholder="
                                            $t('trf.placeholder.template')
                                        "
                                    >
                                        <el-option
                                            v-for="(
                                                template, index
                                            ) in templateData.filter(
                                                (item) => item.formType != 2,
                                            )"
                                            :key="'dff_' + index"
                                            :label="template.templateName"
                                            :value="template.id"
                                        ></el-option>
                                        <el-option
                                            v-for="(
                                                template, index
                                            ) in templateData.filter(
                                                (item) => item.formType == 2,
                                            )"
                                            :key="'dynamic_' + index"
                                            :label="template.templateName"
                                            :value="template.id"
                                        ></el-option>
                                        <!--<el-option-group :label="$t('template.groupDff')">
                                        </el-option-group>
                                        <el-option-group :label="$t('template.groupForm')">
                                        </el-option-group>-->
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    :label="$t('service.reportLanguage')"
                                    v-if="valiUsable('reportLanguage')"
                                    prop="servicRequire.reportLanguage"
                                    :rules="{
                                        required: true,
                                        message: $t(
                                            'trf.validate.requiredBlur',
                                        ),
                                        trigger: 'change',
                                    }"
                                >
                                    <el-select
                                        v-model="
                                            trf.servicRequire.reportLanguage
                                        "
                                        @change="reportChange"
                                        :disabled="trfDisabled"
                                        clearable
                                        @clear="handleBlur"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="(
                                                reportLanguage, index
                                            ) in reportLanguageData"
                                            :key="index"
                                            :label="reportLanguage.sysValue"
                                            :value="reportLanguage.sysKey"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" v-if="agentTipShowFlag">
                                <span style="color: red">
                                    {{ $t("trf.agentTip") }}
                                </span>
                            </el-col>
                            <el-col :span="12" v-if="isShowBelongFlag">
                                <el-form-item
                                    :label="$t('beLongTo.title')"
                                    prop="beLong"
                                >
                                    <el-select
                                        v-model="trf.beLongId"
                                        clearable
                                        filterable
                                        :placeholder="$t('beLongTo.title')"
                                        :disabled="trfDisabled"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="(
                                                beLong, index
                                            ) in beLongData"
                                            :label="beLong.beLongName"
                                            :value="beLong.beLongId"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    :label="$t('trf.selfReference')"
                                    prop="isSelfRefrence"
                                    style="height: 35px; text-align: left"
                                >
                                    <el-checkbox-group
                                        v-model="isSelfRefrenceFlag"
                                        style="padding: 4px"
                                        :disabled="trfDisabled"
                                    >
                                        <el-checkbox
                                            name="type"
                                            @change="tipsReference"
                                        ></el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                            <el-col
                                :span="12"
                                v-if="
                                    templateSpecificCode.indexOf('eFiling') !=
                                    -1
                                "
                            >
                                <el-form-item
                                    :label="$t('trf.isEfillingFlag')"
                                    prop="isEfillingFlag"
                                    style="height: 35px; text-align: left"
                                >
                                    <el-checkbox-group
                                        v-model="isEfillingFlag"
                                        style="padding: 4px"
                                    >
                                        <el-checkbox name="true"></el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                            <!--                            <el-col :span="24">
                                <div class="dialog-footer">
                                    <el-button size="small" type="primary" :loading="btnSbuTrfTemplateLoading"
                                            @click="submitTemplate">{{$t('operation.confirm')}}
                                    </el-button>
                                </div>
                            </el-col>-->
                        </el-row>

                        <div slot="footer" class="dialog-footer">
                            <el-button
                                size="small"
                                type="primary"
                                :loading="btnSbuTrfTemplateLoading"
                                @click="submitTemplate"
                            >
                                {{ $t("operation.confirm") }}
                            </el-button>
                        </div>
                    </el-dialog>

                    <!-- 客户信息 -->
                    <el-card class="sgs-box content-item" id="customer_card">
                        <div class="sgs-group">
                            <h3>{{ $t("trf.customerInfo") }}</h3>
                        </div>

                        <TRFCustomer
                            v-if="loadCustomerFlag"
                            ref="applicantRef"
                            :templateSpecificCode="templateSpecificCode"
                            :templateDataObj="templateDataObj"
                            :trf="trf"
                            :trfStatus="trf.trfStatus-0"
                            :trfDisabled="trfDisabled"
                            :emailGroupData="emailGroupData"
                            :trfCustomer="trf.trfCustomer"
                            :trfCustomers="trf.customerList"
                            :productLineCode="trf.productLineCode"
                            :needSupplier="needSupplier"
                            :needManufacture="needManufacture"
                            :languageNumber="languageNumber"
                            :showCustomerNameFlag="showCustomerNameFlag"
                            :supplierSameAsApplicant="supplierSameAsApplicantFlag"
                            @updateBuyerInfo="updateBuyerInfo"
                            @updateBuyerGroupInfo="updateBuyerGroupInfo"
                            @enterApplicant="updateTrfApplicant"
                            @clear="handleBlur"
                        />
                    </el-card>
                    <!-- 基本信息 -->
                    <el-card
                        v-show="trf.trfStatus > 1 || showStep >= 1"
                        :class="[
                            'sgs-box',
                            showStep >= 1 ? 'content-item' : '',
                        ]"
                        id="basic_card"
                        v-loading="basicSaveLoading"
                    >
                        <div class="sgs-group">
                            <h3>{{ $t("trf.basicInfo") }}</h3>
                        </div>
                        <el-row>
                            <el-col style="text-align: right">
                                <el-button
                                    v-if="
                                        basicModifyDisabled &&
                                        showModifyBtn('basic')
                                    "
                                    type="primary"
                                    size="medium"
                                    @click="basicModifyDisabled = false"
                                >
                                    {{ $t("operation.modify") }}
                                </el-button>
                                <el-button
                                    v-if="
                                        !basicModifyDisabled &&
                                        showModifyBtn('basic')
                                    "
                                    type="primary"
                                    size="medium"
                                    @click="updateModifyBasic('basic')"
                                >
                                    {{ $t("operation.save") }}
                                </el-button>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <div class="item-warp clearfix">
                                <el-col :span="8">
                                    <!--非动态校验-->
                                    <el-form-item
                                        ref="serviceType"
                                        :label="$t('service.serviceType')"
                                        prop="serviceType"
                                        :rules="[
                                            {
                                                required: true,
                                                message:
                                                    $t(
                                                        'operation.pleaseSelect',
                                                    ) +
                                                    ' ' +
                                                    $t('service.serviceType'),
                                                trigger: ['blur', 'change'],
                                            },
                                        ]"
                                    >
                                        <el-select
                                            v-model="trf.serviceType"
                                            @change="serviceTypeChange"
                                            style="width: 100%"
                                            :placeholder="
                                                $t('service.serviceType')
                                            "
                                            :disabled="
                                                disabledComonent('basic')
                                            "
                                            clearable
                                        >
                                            <el-option
                                                v-for="(
                                                    serviceType, index
                                                ) in serviceTypeData"
                                                :key="index"
                                                :label="
                                                    serviceType.serviceTypeName
                                                "
                                                :value="
                                                    serviceType.serviceTypeCode
                                                "
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8" v-if="labContactFlag">
                                    <el-form-item
                                        :label="$t('labContact.labContactName')"
                                        v-if="valiUsable('labContactId')"
                                        prop="trfLabContact.contactName"
                                        :rules="{
                                            required: false,
                                            message: $t(
                                                'trf.validate.requiredBlur',
                                            ),
                                            trigger: 'change',
                                        }"
                                    >
                                        <el-select
                                            v-model="
                                                trf.trfLabContact.contactName
                                            "
                                            :disabled="
                                                disabledComonent('basic')
                                            "
                                            :placeholder="
                                                $t('operation.pleaseSelect')
                                            "
                                            filterable
                                            @change="selectLabContactChange"
                                            style="width: 100%"
                                            clearable
                                        >
                                            <el-option
                                                v-for="(
                                                    labContact, index
                                                ) in templateLabContactData"
                                                :key="index"
                                                :label="
                                                    labContact.labContactName
                                                "
                                                :value="
                                                    labContact.labContactName
                                                "
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </div>
                            <div class="item-warp clearfix">
                                <el-col :span="8">
                                    <!--非动态校验-->
                                    <el-form-item
                                        ref="trfLab.labCode"
                                        :label="$t('lab.labName')"
                                        prop="trfLab.labCode"
                                    >
                                        <el-select
                                            v-model="trf.trfLab.labCode"
                                            :placeholder="$t('lab.selLabName')"
                                            @change="selectLabChange"
                                            filterable
                                            @clear="handleBlur"
                                            :disabled="
                                                disabledComonent('basic')
                                            "
                                            style="width: 100%"
                                            clearable
                                        >
                                            <el-option
                                                v-for="(
                                                    lab, index
                                                ) in templateLabData"
                                                :label="
                                                    language === 'zh-CN'
                                                        ? checkObjectIsNull(
                                                              lab.labNameCn,
                                                          )
                                                            ? lab.labName
                                                            : lab.labNameCn
                                                        : checkObjectIsNull(
                                                              lab.labName,
                                                          )
                                                        ? lab.labNameCn
                                                        : lab.labName
                                                "
                                                :key="index"
                                                :disabled="lab.disabled"
                                                :value="lab.labCode"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item
                                        :label="$t('lab.labAddress')"
                                        v-if="valiUsable('labAddress')"
                                        prop="trfLab.labAddress"
                                        :rules="{
                                            required: false,
                                            message: $t(
                                                'trf.validate.requiredBlur',
                                            ),
                                            trigger: 'change',
                                        }"
                                    >
                                        <el-input
                                            maxlength="500"
                                            v-model="trf.trfLab.labAddress"
                                            autocomplete="off"
                                            :disabled="true"
                                            clearable
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </div>
                        </el-row>
                        <el-row
                            v-if="
                                ['HK SL', 'HK HL'].includes(
                                    trf.trfLab.labCode,
                                ) && [1, 2].includes(trf.trfStatus)
                            "
                        >
                            <el-col :span="24">
                                <label>{{ $t("trf.HKCMS") }}</label>
                                &nbsp;&nbsp;
                                <a
                                    href="javascript:void(0)"
                                    @click="openHKCMS"
                                    style="color: #ff6600"
                                >
                                    <u>
                                        Go to SGSHK Online Sample Pickup
                                        Platform
                                    </u>
                                </a>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 动态信息 -->
                    <el-card
                        v-if="showDynamicForm"
                        v-show="trf.trfStatus > 1 || showStep >= 2"
                        :class="[
                            'sgs-box',
                            showStep >= 2 ? 'content-item' : '',
                        ]"
                        id="dynamic_form_card"
                    >
                        <div class="sgs-group">
                            <h3>{{ $t("trf.dynamicForm") }}</h3>
                        </div>
                        <div :class="[showStep >= 2 ? 'content-item1' : '']">
                            <sgs-form-design-render
                                v-if="showDynamicForm"
                                id="VFormDesignrender"
                                ref="VFormDesignrender"
                                :form-obj="dynamicFormObj"
                                :disabled="trfDisabled"
                                :form-id="queryDynamicFormId"
                                :form-group-id="queryDynamicFormDesignGroupId"
                                :language="languageNumber"
                                :only-display-always="
                                    dynamicFormOnlyDisplayAlways
                                "
                                @value-change="formChangeValue"
                            ></sgs-form-design-render>
                        </div>
                    </el-card>
                    <!-- 样品信息 -->
                    <el-card
                        v-if="!showDynamicForm"
                        v-show="trf.trfStatus > 1 || showStep >= 2"
                        :class="[
                            'sgs-box',
                            showStep >= 2 ? 'content-item' : '',
                        ]"
                        id="product_card"
                    >
                        <div class="sgs-group">
                            <h3>{{ $t("trf.productAndSample") }}</h3>
                        </div>
                        <!--DFF Form 样品内容-->
                        <div :class="[showStep >= 2 ? 'content-item1' : '']">
                            <el-scrollbar>
                                <el-row
                                    id="dff-form"
                                    name="DFF Form"
                                    v-if="isDffFormLoadFlag"
                                >
                                    <productSample
                                        ref="frm"
                                        :trf-id="trf.id"
                                        :env="env == 'local' ? 'uat' : env"
                                        :bu-code="trf.productLineCode"
                                        :location-code="'SZ'"
                                        :customer-group-code="
                                            trf.trfCustomer
                                                .buyerCustomerGroupCode
                                        "
                                        :disabled-flag="trfDisabled"
                                        :dff-form-data-object="dffFormObjNew"
                                        :is-care-label-load-flag="
                                            isCareLabelLoadFlag
                                        "
                                        :language-select-show="
                                            trf.servicRequire.reportLanguage ==
                                                3 ||
                                            trf.servicRequire.reportLanguage ==
                                                6
                                                ? true
                                                : false
                                        "
                                        @computedRequired="handleBlur"
                                    ></productSample>
                                </el-row>
                            </el-scrollbar>
                        </div>
                    </el-card>

                    <!-- 测试项目 -->
                    <el-card
                        v-show="trf.trfStatus > 1 || showStep >= 3"
                        :class="[
                            'sgs-box',
                            showStep >= 3 ? 'content-item' : '',
                        ]"
                        id="testpackate_card"
                    >
                        <div class="sgs-group">
                            <h3>
                                {{ $t("trf.testPackage") }}
                                <el-button
                                    v-if="!trfDisabled"
                                    @click="testPackageAddVisible = true"
                                    class="line-btn pull-right"
                                    style="margin-top: -15px"
                                >
                                    <i class="el-icon-circle-plus-outline"></i>
                                    {{ $t("operation.add") }}
                                </el-button>
                            </h3>
                        </div>
                        <el-row>
                            <el-col :span="24" class="tree">
                                <el-form-item label="">
                                    <el-table
                                        row-key="id"
                                        default-expand-all
                                        :data="newTreeData"
                                        style="width: 100%"
                                        class="trf-form-table"
                                        :tree-props="{
                                            children: 'children',
                                            hasChildren: 'hasChildren',
                                        }"
                                    >
                                        <!-- <el-table-column :label="$t('number')" type="index"></el-table-column> -->
                                        <el-table-column
                                            prop="testPackageName"
                                            :label="$t('trf.testPackage')"
                                            width="60%"
                                        >
                                            <template slot-scope="scope">
                                                <!-- {{ scope.row.selected }} - {{ scope.row.parentId }} node.checked -  -->
                                                <!--                                                <el-tooltip v-if="scope.row.testPackageName.length > 58" effect="dark" :content="scope.row.testPackageName" placement="top">
                                                    <span :class="{'hide-line': scope.row.selected != true, 'show-line': scope.row.selected, 'tree-root': scope.row.parentId == -1}">
                                                        {{ scope.row.testPackageName.length > 58 ? scope.row.testPackageName.slice(0,58)+'...' : scope.row.testPackageName }}
                                                    </span>
                                                </el-tooltip>-->
                                                <span
                                                    :class="{
                                                        'hide-line':
                                                            scope.row
                                                                .selected !=
                                                            true,
                                                        'show-line':
                                                            scope.row.selected,
                                                        'tree-root':
                                                            scope.row
                                                                .parentId == -1,
                                                    }"
                                                >
                                                    <div
                                                        style="
                                                            padding: 0 0 0 64px;
                                                            margin-top: -25px;
                                                            word-wrap: break-word;
                                                            word-break: break-word;
                                                        "
                                                    >
                                                        {{
                                                            scope.row
                                                                .testPackageName
                                                        }}
                                                    </div>
                                                </span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            prop="remark"
                                            :label="$t('operation.remark')"
                                            width="30%"
                                        >
                                            <template slot-scope="scope">
                                                <el-input
                                                    v-if="
                                                        scope.row.parentId !=
                                                            -1 &&
                                                        scope.row.children
                                                            .length <= 0
                                                    "
                                                    :class="
                                                        scope.row.remark ==
                                                        'Remark'
                                                            ? 'placeholder-remark'
                                                            : ''
                                                    "
                                                    maxlength="250"
                                                    v-model="scope.row.remark"
                                                    autocomplete="off"
                                                    :disabled="trfDisabled"
                                                    @clear="
                                                        handleRemarkBlur(
                                                            scope.$index,
                                                            scope.row,
                                                        )
                                                    "
                                                    @focus="
                                                        handleRemarkFoucs(
                                                            scope.$index,
                                                            scope.row,
                                                        )
                                                    "
                                                    @blur.capture.native="
                                                        handleRemarkBlur(
                                                            scope.$index,
                                                            scope.row,
                                                        )
                                                    "
                                                    clearable
                                                ></el-input>
                                            </template>
                                        </el-table-column>
                                        <el-table-column
                                            :label="$t('operation.title')"
                                            width="10%"
                                        >
                                            <template slot-scope="scope">
                                                <el-button
                                                    v-if="
                                                        scope.row.parentId !=
                                                            -1 &&
                                                        scope.row.children
                                                            .length <= 0 &&
                                                        !trfDisabled
                                                    "
                                                    @click.native.prevent="
                                                        handleTestPackageDel(
                                                            scope.$index,
                                                            scope.row.id,
                                                        )
                                                    "
                                                    type="text"
                                                    size="small"
                                                >
                                                    {{ $t("operation.remove") }}
                                                </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item :label="$t('trf.remark')">
                                    <el-input
                                        maxlength="1000"
                                        type="textarea"
                                        v-model="trf.remark"
                                        :rows="4"
                                        style="border-radius: 0"
                                        :disabled="trfDisabled"
                                        autocomplete="off"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-card>
                    <!-- 树弹窗 -->
                    <el-dialog
                        :title="
                            languageNumber == 1
                                ? 'Add'
                                : '添加' + $t('trf.testPackage')
                        "
                        :visible.sync="testPackageAddVisible"
                        @open="handleOpen"
                        @close="handleClose"
                        class="tree-dialog"
                        width="1200px"
                    >
                        <el-row slot="footer" :gutter="24">
                            <el-col :span="12">
                                <h5 class="num-top text-left">
                                    {{ $t("trf.testPackage") }}
                                </h5>
                                <div class="tree-left">
                                    <el-input
                                        :disabled="trfDisabled"
                                        :placeholder="$t('common.inputFilter')"
                                        v-model="filterText"
                                        clearable
                                    ></el-input>
                                    <div style="height: 400px; overflow: auto">
                                        <el-tree
                                            style="margin-top: 1px"
                                            :key="commponentKey"
                                            :disabled="trfDisabled"
                                            class="filter-tree"
                                            :data="testPackageTreedata"
                                            :props="defaultProps"
                                            default-expand-all
                                            :show-checkbox="!testPackageExpand"
                                            node-key="id"
                                            v-loading="treeLoading"
                                            :filter-node-method="filterNode"
                                            :expand-on-click-node="false"
                                            ref="selTree"
                                            @check-change="treeCheckChange"
                                        >
                                            <template
                                                #default="{
                                                    node,
                                                    testPackageTreedata,
                                                }"
                                            >
                                                <span class="custom-tree-node">
                                                    <span style="width: 70%">
                                                        <el-tooltip
                                                            effect="dark"
                                                            :content="
                                                                node.label
                                                            "
                                                            placement="top"
                                                            v-if="
                                                                node.label
                                                                    .length > 40
                                                            "
                                                        >
                                                            <span>
                                                                {{
                                                                    node.label
                                                                        .length >
                                                                    40
                                                                        ? node.label.slice(
                                                                              0,
                                                                              40,
                                                                          ) +
                                                                          "..."
                                                                        : node.label
                                                                }}
                                                            </span>
                                                        </el-tooltip>
                                                        <span v-else>
                                                            {{ node.label }}
                                                        </span>
                                                        <el-button
                                                            type="text"
                                                            v-if="
                                                                node.label &&
                                                                node.data
                                                                    .parentId ==
                                                                    -1 &&
                                                                !trfDisabled &&
                                                                !node.expanded &&
                                                                node.data
                                                                    .children
                                                            "
                                                            style="
                                                                margin-left: 10px;
                                                                color: #409eff;
                                                            "
                                                            @click="
                                                                showMore(
                                                                    node,
                                                                    testPackageTreedata,
                                                                )
                                                            "
                                                        >
                                                            {{
                                                                $t(
                                                                    "operation.more",
                                                                )
                                                            }}...
                                                        </el-button>
                                                    </span>
                                                </span>
                                            </template>
                                        </el-tree>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <h5 class="num-top text-left clearfix">
                                    {{ $t("operation.selected") }}({{
                                        testPackageCheckedList.length
                                    }})
                                    <span
                                        class="pull-right clear-all-tree"
                                        @click="resetChecked"
                                    >
                                        <img src="/img/icon/clear-tree.png" />
                                        {{ $t("training.Clear") }}
                                    </span>
                                </h5>
                                <div class="tree-right">
                                    <el-input
                                        :placeholder="$t('common.inputFilter')"
                                        v-model="treeSearch"
                                    ></el-input>
                                    <!-- :data="newTreeData.filter(data => !treeSearch || data.testPackageName.toLowerCase().includes(treeSearch.toLowerCase()))" -->
                                    <div style="height: 400px; overflow: auto">
                                        <el-table
                                            size="mini"
                                            row-key="id"
                                            default-expand-all
                                            :data="newTreeData"
                                            style="width: 100%"
                                            class="trf-form-table-dialog"
                                            :tree-props="{
                                                children: 'children',
                                                hasChildren: 'hasChildren',
                                            }"
                                        >
                                            <el-table-column
                                                prop="testPackageName"
                                                label=""
                                            >
                                                <template slot-scope="scope">
                                                    <el-tooltip
                                                        v-if="
                                                            scope.row
                                                                .testPackageName
                                                                .length > 40
                                                        "
                                                        effect="dark"
                                                        :content="
                                                            scope.row
                                                                .testPackageName
                                                        "
                                                        placement="top"
                                                    >
                                                        <span
                                                            :class="{
                                                                'hide-line-dialog':
                                                                    scope.row
                                                                        .selected !=
                                                                    true,
                                                                'show-line-dialog':
                                                                    scope.row
                                                                        .selected,
                                                                'tree-root-dialog':
                                                                    scope.row
                                                                        .parentId ==
                                                                    -1,
                                                            }"
                                                        >
                                                            {{
                                                                scope.row
                                                                    .testPackageName
                                                                    .length > 40
                                                                    ? scope.row.testPackageName.slice(
                                                                          0,
                                                                          40,
                                                                      ) + "..."
                                                                    : scope.row
                                                                          .testPackageName
                                                            }}
                                                        </span>
                                                    </el-tooltip>
                                                    <span
                                                        v-else
                                                        :class="{
                                                            'hide-line-dialog':
                                                                scope.row
                                                                    .selected !=
                                                                true,
                                                            'show-line-dialog':
                                                                scope.row
                                                                    .selected,
                                                            'tree-root-dialog':
                                                                scope.row
                                                                    .parentId ==
                                                                -1,
                                                        }"
                                                    >
                                                        {{
                                                            scope.row
                                                                .testPackageName
                                                        }}
                                                    </span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column
                                                label=""
                                                width="60"
                                            >
                                                <template slot-scope="scope">
                                                    <el-button
                                                        v-if="
                                                            scope.row
                                                                .parentId !=
                                                                -1 &&
                                                            scope.row.children
                                                                .length <= 0 &&
                                                            !trfDisabled
                                                        "
                                                        @click.native.prevent="
                                                            handleTestPackageDel(
                                                                scope.$index,
                                                                scope.row.id,
                                                            )
                                                        "
                                                        type="text"
                                                        size="small"
                                                    >
                                                        <i
                                                            class="el-icon-close"
                                                        ></i>
                                                    </el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <div
                                    class="text-right"
                                    style="margin-top: 32px"
                                >
                                    <el-button
                                        @click="testPackageAddVisible = false"
                                    >
                                        {{ $t("operation.cancel") }}
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="confirmTestPackage"
                                    >
                                        {{ $t("operation.confirm") }}
                                    </el-button>
                                </div>
                            </el-col>
                        </el-row>
                    </el-dialog>

                    <el-dialog
                        :title="$t('operation.remark')"
                        :visible.sync="testPackageVisible"
                    >
                        <el-form-item
                            :label="$t('operation.remark')"
                            label-width="160px"
                        >
                            <el-input
                                maxlength="250"
                                v-model="remark"
                                autocomplete="off"
                                :disabled="trfDisabled"
                            ></el-input>
                        </el-form-item>
                        <div slot="footer" class="dialog-footer">
                            <el-button
                                size="small"
                                @click="testPackageVisible = false"
                            >
                                {{ $t("operation.cancel") }}
                            </el-button>
                            <el-button
                                size="small"
                                type="primary"
                                :disabled="trfDisabled"
                                @click="addTestPackageRemark()"
                            >
                                {{ $t("operation.confirm") }}
                            </el-button>
                        </div>
                    </el-dialog>

                    <!-- 服务需求 -->
                    <BaseServiceRequirement
                        v-show="trf.trfStatus > 1 || showStep >= 4"
                        :productLineCode="trf.productLineCode"
                        :trfServiceRequire="trf.servicRequire"
                        :trfDataLoadStatus="trfDataLoadStatus"
                        :emailGroupData="emailGroupData"
                        :reportDeliveredTo="trf.reportDeliveredTo"
                        :failedReportDeliveredTo="trf.failedReportDeliveredTo"
                        :trfDisabled="trfDisabled"
                        :isGeneralRequest="trf.isGeneralRequest"
                        :isCpRequest="trf.isCpRequest"
                        ref="serviceRequireRef"
                        @onChangeDeliveredTo="onChangeDeliveredTo"
                        @onChangeFailDeliveredTo="onChangeFailDeliveredTo"
                        @showMenus="updateMenus"
                        @serviceItemChange="serviceItemChange"
                        :class="[
                            'sgs-box',
                            showStep >= 4 ? 'content-item' : '',
                        ]"
                        id="service_card"
                        style="padding: 0 0"
                    />

                    <!-- 附件 -->
                    <TrfAttachment
                        ref="trfFile"
                        v-if="showStep >= 5"
                        :trfAttachments="trf.trfAttachments"
                        :trf="trf"
                        :showStep="showStep"
                        :file-max-sizes="20"
                        :trfDisabled="trfDisabled"
                    ></TrfAttachment>
                    <el-card
                        :class="[
                            'sgs-box',
                            showStep >= 6 ? 'content-item' : '',
                        ]"
                        v-if="
                            (trf.trfStatus > 1 || showStep >= 6) &&
                            isEfillingFlag
                        "
                    >
                        <eFillingForm
                            ref="eFilling"
                            :formModel="formModel"
                            :productInfoList="productInfoList"
                        >
                            <template slot="header">
                                <h3 style="font-weight: bold">eFiling</h3>
                                <el-button type="primary" @click="mapping()">
                                    Mapping
                                </el-button>
                            </template>
                        </eFillingForm>
                    </el-card>

                    <TRFSpecificExtend
                        v-if="
                            (trf.trfStatus > 1 || showStep >= 5) &&
                            trf.trfTemplateId
                        "
                        :trf-id="trf.id"
                        :template-id="trf.trfTemplateId"
                        @showMenus="updateMenus"
                        :class="[
                            'sgs-box',
                            showStep >= 5 ? 'content-item' : '',
                        ]"
                        id="specific_extend_card"
                        style="padding: 0 0"
                    />
                    <el-button
                        v-show="isEfillingFlag ? showStep < 6 : showStep < 5"
                        @click="showStep++"
                        id="create-trf-contunue"
                        type="primary"
                        style="margin-top: 24px; margin-right: 24px"
                    >
                        {{ $t("guide.continue") }}
                    </el-button>

                    <!-- 通用条款 -->
                    <el-row class="page-no-print">
                        <span
                            style="
                                font-size: 14px;
                                margin-top: 20px;
                                display: block;
                                color: #999;
                            "
                        >
                            {{ $t("trf.serviceClause") }}
                            <a
                                target="_blank"
                                style="color: #ff6600"
                                href="https://www.sgs.com/en/terms-and-conditions"
                            >
                                https://www.sgs.com/en/terms-and-conditions
                            </a>
                        </span>
                    </el-row>

                    <el-row
                        class="sgs-footer page-no-print"
                        style="position: fixed"
                    >
                        <el-button
                            class="custom-primary-button"
                            type="primary"
                            v-if="removeTrfBtnIsShow && trf.trfStatus == 1"
                            plain
                            :loading="btnSubmitLoading"
                            @click="delTrf()"
                        >
                            {{ $t("operation.remove") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            v-if="
                                permissionList.returnTrfBtn &&
                                trf.trfStatus == 2 &&
                                (userInfo.userMgtId == trf.createUserId ||
                                    (trf.trfSourceType == 'CUSTOMER_TRF' &&
                                        (role.isLabUser || role.isSGS)))
                            "
                            type="primary"
                            :loading="btnSubmitLoading"
                            @click="returnTrfStatusClick()"
                        >
                            {{ $t("trf.returnTrfSatus") }}
                        </el-button>

                        <el-button
                            class="custom-primary-button"
                            type="primary"
                            v-if="cancelTrfBtnIsShow"
                            plain
                            :loading="btnSubmitLoading"
                            @click="cancelTrf('6')"
                        >
                            {{ $t("operation.cancel") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            type="primary"
                            v-if="saveTrfBtnIsShow"
                            :loading="btnSubmitLoading"
                            @click="onSubmit('1')"
                        >
                            {{ $t("operation.save") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            type="primary"
                            v-if="submitTrfBtnIsShow"
                            :loading="btnSubmitLoading"
                            @click="onSubmit('2')"
                        >
                            {{ $t("operation.submit") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            type="primary"
                            @click="saveAsTrfTemplate"
                            v-if="saveAsTemplateBtnIsShow"
                            plain
                            :loading="btnSubmitLoading"
                        >
                            {{ $t("operation.saveAsTemplate") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            type="primary"
                            @click="toTrfList('0')"
                            :loading="btnSubmitLoading"
                        >
                            {{ $t("operation.backToList") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            v-if="
                                permissionList.confirmSampleBtn &&
                                trf.trfStatus == 2
                            "
                            type="primary"
                            :loading="btnSubmitLoading"
                            @click="confirmSampleClick()"
                        >
                            {{ $t("trf.confirmSample") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            v-if="
                                permissionList.privewTemplateBtn &&
                                trf.trfStatus > 1 &&
                                trf.trfStatus != 6
                            "
                            type="primary"
                            @click="toPrivewTemplate('0')"
                            :loading="btnSubmitLoading"
                        >
                            {{ $t("trf.print") }}
                        </el-button>
                        <el-button
                            class="custom-primary-button"
                            v-if="
                                permissionList.trfHistoryBtn &&
                                trf.id != '' &&
                                optionType != 'copy' &&
                                optionType != 'trfTemplate'
                            "
                            type="primary"
                            @click="trfHistoryClick"
                            :loading="btnSubmitLoading"
                        >
                            {{ $t("trf.history.title") }}
                        </el-button>
                    </el-row>
                </el-form>
            </el-col>
            <el-col :span="4" class="page-no-print">
                <div v-if="trf.trfStatus > 1" class="scroll-box clearfix">
                    <h5 class="add-comment-title">
                        {{ $t("communicationLog.title") }}
                    </h5>
                    <div class="add-comment">
                        <communication
                            v-if="trf.trfStatus > 1"
                            @closeDialog="getCommentData"
                            :showWithBtn="true"
                            :customerContactData="customerContactData"
                            :communicationTableData="communicationTableData"
                            :trfNo="trf.trfNo"
                            :trfStatus="trf.trfStatus - 0"
                            :trfEmail="trf.trfCustomerContact.applyContactEmail"
                            :trfId="trf.id"
                            :uploadFileMaxSize="20"
                            :product-line-code="trf.productLineCode"
                            :trfType="10"
                        ></communication>
                    </div>
                    <div
                        class="inner-scroll clearfix"
                        v-if="communicationTableData.length > 0"
                    >
                        <dl class="comment-list pull-right">
                            <!-- <dd class="add-comment">
                                <communication v-if="trf.trfStatus>1"
                                    @closeDialog="getCommentData"
                                    :showWithBtn="true"
                                    :customerContactData="customerContactData"
                                    :communicationTableData="communicationTableData"
                                    :trfNo="trf.trfNo"
                                    :trfStatus="trf.trfStatus"
                                    :trfEmail="trf.trfCustomerContact.applyContactEmail"
                                    :trfId="trf.id"
                                    :trfType="10">
                                </communication>
                            </dd> -->
                            <dd
                                v-for="(item, index) in communicationTableData"
                                :key="index"
                            >
                                <el-card shadow="always">
                                    <div class="comment">
                                        {{ item.comment }}
                                    </div>
                                    <div class="c-info">
                                        <p>
                                            <i class="el-icon-user"></i>
                                            {{ item.createUser }}
                                        </p>
                                        <p v-if="item.contactEmail != ''">
                                            <i class="el-icon-message"></i>
                                            {{ item.contactEmail }}
                                        </p>
                                        <p v-if="item.fileUrl != ''">
                                            <i class="el-icon-link"></i>
                                            &nbsp;
                                            <el-link
                                                type="primary"
                                                @click="
                                                    downAttachment(item.fileUrl)
                                                "
                                            >
                                                {{ $t("attachment.title") }}
                                            </el-link>
                                        </p>
                                        <p>
                                            <i class="el-icon-date"></i>
                                            {{
                                                currentTz_YMD(
                                                    item.newCreateTime,
                                                )
                                            }}
                                        </p>
                                    </div>
                                </el-card>
                            </dd>
                        </dl>
                    </div>
                    <div
                        v-if="communicationTableData.length <= 0"
                        class="no-data text-center"
                    >
                        <img src="/img/icon/empty-data.png" />
                        <p>{{ $t("training.NoData") }}</p>
                    </div>
                </div>
                <div v-else class="scroll-box">
                    <h5 class="add-comment-title">
                        {{ $t("communicationLog.title") }}
                    </h5>
                    <div class="no-data text-center">
                        <img src="/img/icon/empty-data.png" />
                        <p>{{ $t("training.NoData") }}</p>
                    </div>
                </div>
            </el-col>
        </el-row>

        <el-dialog
            :title="$t('operation.saveAsTemplate')"
            :visible.sync="trfTemplateDialogFormVisible"
        >
            <el-form
                :model="trfUserTemplate"
                ref="trfUserTemplateForm"
                rules="trfUserTemplateRules"
            >
                <el-form-item
                    :label="$t('trf.trfTemplateName')"
                    label-width="160px"
                    prop="trfTemplateName"
                >
                    <el-input
                        maxlength="200"
                        v-model="trfUserTemplate.trfTemplateName"
                        autocomplete="off"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button
                    size="small"
                    @click="trfTemplateDialogFormVisible = false"
                >
                    {{ $t("operation.cancel") }}
                </el-button>
                <el-button
                    size="small"
                    type="primary"
                    :loading="btnSbuTrfTemplateLoading"
                    @click="saveTrfUserTemplate('trfUserTemplate')"
                >
                    {{ $t("operation.confirm") }}
                </el-button>
            </div>
        </el-dialog>

        <trf-history
            :visible.sync="historyVisible"
            :trfId="this.trf.id"
        ></trf-history>
        <el-dialog
            :title="$t('trf.confirmSample')"
            :visible.sync="trfConfirmSampleDialogFormVisible"
        >
            <el-form
                :model="confirmSampleForm"
                ref="confirmSampleForm"
                :rules="confirmSampleFormRules"
            >
                <el-row>
                    <el-form-item
                        :label="$t('trf.sampleReceivedDate')"
                        required
                        :rules="[
                            {
                                required: true,
                                message: $t('trf.validate.requiredBlur'),
                                trigger: 'change',
                            },
                        ]"
                        prop="sampleReceiveDate"
                    >
                        <el-date-picker
                            v-model="confirmSampleForm.sampleReceiveDate"
                            type="datetime"
                            :placeholder="$t('trf.sampleReceivedDate')"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            :picker-options="pickerOptions0"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>

                    <el-form-item :label="$t('trf.dueDate')" prop="dueDate">
                        <el-date-picker
                            clearable
                            v-model="confirmSampleForm.dueDate"
                            type="datetime"
                            :placeholder="$t('trf.dueDate')"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            :picker-options="pickerOptions1"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer page-no-print">
                <el-button
                    size="small"
                    @click="trfConfirmSampleDialogFormVisible = false"
                >
                    {{ $t("operation.cancel") }}
                </el-button>
                <el-button
                    size="small"
                    type="primary"
                    :loading="btnConfirmSampleLoading"
                    @click="confirmSampleDateSubmit('confirmSampleForm')"
                >
                    {{ $t("operation.confirm") }}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
//import {bizComponent} from 'biz-component'
import {
    add,
    doMapping,
    listForSelect,
    tradeSelect,
} from "@/api/cpscTrfInfo/cpscTrfInfo"

import {mapGetters} from "vuex"
import {currentEnv} from "@/config/env"
import {deepClone} from "@/util/util"
import {getDeptBeLongTosByTemplateId} from "@/api/customer/customerDeptBeLongTo"
import {queryCustomerByBuCodeAndCustomerId} from "@/api/customer/customer"
import {getFieldSettingsByTemplateId} from "@/api/template/template"
import {queryTrfComments} from "@/api/trf/communication"
import {sampleToTrf, sampleConvertDff} from "@/api/trf/newMaterial"
import {
    checkDffFormIdSmapleGroup,
    getAgentCustomerGroupByParms,
    getCloudFileURL,
    getCustomerGroup,
    getCustomerGroupByParms,
    getDffFormComfirmIdBatchByIds,
    getServiceType,
} from "@/api/common/index"
import {
    queryCustomerForId,
    searchCustomer,
    validateAuth,
} from "@/api/customer/customer"
import {
    buyerPkAgent,
    cancelTrfV2,
    confirmSampleDateV2,
    getBuyerInfoPage,
    getCustomerAddressList,
    getCustomerContactList,
    getReportLanguageArry,
    getTemplateLabContactList,
    getTemplateLabList,
    getTemplateList,
    getTemplateTestPackageList,
    queryMaterial,
    queryTrfDetail,
    removeTrfV2,
    returnTrfV2,
    saveTrfV2,
    saveTrfUserTemplate,
    trfTemplateDetail,
    queryScmCustomerList,
    convertDff,
    convertDesign,
    detailForCopy,
    queryScmList, updateBasicData
} from "@/api/trf/trf"
import moment from "moment"
import {objectIsNull, validateEmail, validatenull} from "@/util/validate"
import {getMailGroupAndContactEmail} from "@/api/customer/customerEmailGroup"
import {ProductLineEnums} from "@/commons/enums/ProductLineEnums"
import {CustomerUsageEnums} from "@/commons/enums/CustomerUsageEnums"
import {TrfTypeEnums, TrfSourceEnums} from "@/commons/enums/TrfEnums"
import {LanguageEnums} from "@/commons/enums/LanguageEnums"
import {tzFormChina, tzToChina} from "@/util/datetimeUtils"

export default {
    name: "trfDetail",
    inject: ["reload"],
    components: {
        TRFCustomer: (resolve) =>
            require(["@/components/trf/TRFCustomer.vue"], resolve),
        TestResult: (resolve) =>
            require(["@/views/trf/form/testResult"], resolve),
        Communication: (resolve) =>
            require(["../../../components/dialog/Communication"], resolve),
        TrfHistory: (resolve) =>
            require(["../../../components/dialog/trf-history-dialog"], resolve),
        TRFSpecificExtend: (resolve) =>
            require(["@/components/trf/TRFSpecificExtend"], resolve),
        BaseServiceRequirement: (resolve) =>
            require(["@/components/trf/BaseServiceRequirement.vue"], resolve),
        productSample: (resolve) => require(["./productSample"], resolve),
        TrfAttachment: (resolve) =>
            require(["@/components/trf/TrfAttachment"], resolve),
        eFillingForm: (resolve) => require(["./eFillingForm.vue"], resolve),
    },
    props: {
        trfNo: {
            type: String,
            default: null,
        },
        trfStatus: {
            type: String,
            default: null,
        },
    },
    created() {},
    data() {
        var validateContactNameLength = (rule, value, callback) => {
            if (value) {
                if (!validatenull(value.trim())) {
                    if (value.length > 50) {
                        return callback(
                            new Error(
                                this.$t(
                                    "trf.validate.contactNameLengthErrorInfo",
                                ),
                            ),
                        )
                    } else {
                        callback()
                    }
                }
            } else {
                return callback(new Error(this.$t("trf.validate.requiredBlur")))
            }
        }
        var validateTemplate = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.templateSel")))
            } else {
                callback()
            }
        }
        var validateBuyerCustomer = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.buyerSel")))
            } else {
                callback()
            }
        }

        var validateCustomerName = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validateCustomerAddress = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validateSampleReceiveDate = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
            }
        }
        var validateApplyContactTel = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validateApplyContactEmail = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length > 200) {
                callback(new Error(this.$t("trf.validate.emailLengthError")))
            } else {
                callback()
            }
        }

        var validatePayCustomerNameEn = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validatePayCustomerAddressEn = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validatePayContactName = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validatePayContactTel = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else {
                callback()
            }
        }
        var validatePayContactEmail = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length === 0) {
                callback(new Error(this.$t("trf.validate.requiredBlur")))
            } else if (value.trim().length > 200) {
                callback(new Error(this.$t("register.emailRigthBlur")))
            } else {
                callback()
            }
        }

        var validateLab = (rule, value, callback) => {
            if (value) {
                var valuestr = value.trim()
                if (!valuestr) {
                    callback(new Error(this.$t("lab.selLab")))
                } else {
                    callback()
                }
            } else {
                callback(new Error(this.$t("lab.selLab")))
            }
        }

        return {
            templateLoading: false,
            trfDataLoadStatus: false,
            filledInCount: 0,
            totalCount: 0,
            productInfoList: [
                {
                    referenceNo: this.getNewTrfNo(),
                },
            ],
            templateSpecificCode: "",
            templateDataObj: {},
            loadCustomerFlag: true,
            LanguageEnums: LanguageEnums,
            CustomerUsageEnums: CustomerUsageEnums,
            firstLoad: true,
            treeFilterText: "",
            myDefaultProps: {
                children: "children",
                label: "label",
            },
            newTreeData: [],
            openTempTreeData: [],
            showMoreContent: false,
            showStep: 0,
            formModel: {
                trfStatus: 0,
                cpscTrfAttachList: [],
            },
            navList: [
                {
                    name: "客户信息",
                    alias: "trf.customerInfo",
                    level: "1",
                    isSub: false,
                    showStep: 0,
                    hide: false,
                },
                {
                    name: "基本信息",
                    alias: "trf.basicInfo",
                    level: "2",
                    isSub: false,
                    showStep: 1,
                    hide: false,
                },
                {
                    name: this.showDynamicForm ? "表单信息" : "样品信息",
                    alias: this.showDynamicForm
                        ? "trf.dynamicForm"
                        : "trf.productAndSample",
                    level: "3",
                    isSub: false,
                    showStep: 2,
                    hide: false,
                },
                // { name: '表单信息', alias:'trf.dynamicForm', level: '3', isSub: false, showStep: 2,hide:true},
                // { name: '样品信息', alias:'trf.productAndSample', level: '3', isSub: false, showStep: 2,hide:false},
                {
                    name: "测试项目",
                    alias: "trf.testPackage",
                    level: "4",
                    isSub: false,
                    showStep: 3,
                    hide: false,
                },
                {
                    name: "服务需求",
                    alias: "service.servicRequire",
                    level: "5",
                    isSub: false,
                    showStep: 4,
                    hide: false,
                },
                {
                    name: "附件",
                    alias: "attachment.title",
                    level: "6",
                    isSub: false,
                    showStep: 5,
                    hide: false,
                },
                {
                    name: "eFiling",
                    alias: "eFiling",
                    level: "7",
                    isSub: false,
                    showStep: 6,
                    hide: true,
                },
            ],
            dynamicNavList: [
                {
                    name: "客户信息",
                    alias: "trf.customerInfo",
                    level: "1",
                    isSub: false,
                    showStep: 0,
                    hide: false,
                },
                {
                    name: "基本信息",
                    alias: "trf.basicInfo",
                    level: "2",
                    isSub: false,
                    showStep: 1,
                    hide: false,
                },
                {
                    name: "表单信息",
                    alias: "trf.dynamicForm",
                    level: "3",
                    isSub: false,
                    showStep: 2,
                    hide: false,
                },
                // { name: '表单信息', alias:'trf.dynamicForm', level: '3', isSub: false, showStep: 2,hide:true},
                // { name: '样品信息', alias:'trf.productAndSample', level: '3', isSub: false, showStep: 2,hide:false},
                {
                    name: "测试项目",
                    alias: "trf.testPackage",
                    level: "4",
                    isSub: false,
                    showStep: 3,
                    hide: false,
                },
                {
                    name: "服务需求",
                    alias: "service.servicRequire",
                    level: "5",
                    isSub: false,
                    showStep: 4,
                    hide: false,
                },
                {
                    name: "附件",
                    alias: "attachment.title",
                    level: "6",
                    isSub: false,
                    showStep: 5,
                },
                {
                    name: "eFiling",
                    alias: "eFiling",
                    level: "7",
                    isSub: false,
                    showStep: 6,
                    hide: true,
                },
            ],
            element: {
                nav: [],
                content: [],
            },
            confirmFlag: false,
            more: false,
            materialCustomer: {},
            trfTemplateId: "",
            trfTemplateName: "",
            languageNumber: 1,
            historyVisible: false,
            productLineCode: "",
            templateChangeObj: {},
            showDynamicForm: false,
            queryDynamicFormId: "",
            queryDynamicFormDesignGroupId: "",
            queryDynamicFormCode: "",

            dynamicFormObj: {},
            dynamicFormOnlyDisplayAlways: true,
            agentTipShowFlag: false,
            generalRequestFlag: false,
            node: {},
            remark: "",
            testPackageAddVisible: false,
            testPackageVisible: false,
            commponentKey: 0,
            testPackageExpand: false,
            isShowBelongFlag: false,
            //showProductCategoryFlag: false,
            // productCategoryData: [],
            templateDialogVisible: false,
            beLongData: [],
            trfDisabled: false,
            templateDisabled: false,
            buyerDisabled: false,
            isSupplier: false,
            treeLoading: false,
            uploadLoading: false,
            trfFlag: 1,
            cclValidateTable: true,
            cclValidate: true,
            fieldSettingsData: [],
            removeTrfBtnIsShow: false,
            saveTrfBtnIsShow: true,
            cancelTrfBtnIsShow: false,
            submitTrfBtnIsShow: true,
            saveAsTemplateBtnIsShow: true,
            emailArray: [],
            emailGroupData: [],
            hash: "",
            trfConfirmSampleDialogFormVisible: false,
            trfTemplateDialogFormVisible: false,
            labContactFlag: true,
            showCustmerName: "",
            showCustomerNameFlag: true,
            supplierSameAsApplicantFlag: false,
            btnSubmitLoading: false,
            fullPageLoading: false,
            fullPageLoadingText: "Loading data...",
            btnSbuTrfTemplateLoading: false,
            btnConfirmSampleLoading: false,
            optionType: "",
            isShowSubmitBtn: true,
            isShowRemark: false,
            trfShowData: [],
            customerParams: {
                customerNumber: "",
            },
            isShow: false, //是否为回显数据
            templateDataLoad: false, //模板数据是否加载完成
            fileList: [],
            isDffFormLoadFlag: false,
            isDffGridLoadFlag: false,
            isCareLabelLoadFlag: false,
            //窗口选中的testPackage数据
            drawerSelTestPackageList: [],
            filterText: "",
            filterSelectedText: "",
            defaultProps: {
                label: "testPackageName",
                children: "children",
                value: "id",
            },
            selTestPackageTreedata: [],
            testPackageTreedata: [],
            trfId: "",
            dialogSelTestPackageFormVisible: false,
            filesData: [],
            dffFormId: "03a1f3b2-713b-413e-8d3d-ad5cbed44981",
            customerId: "", //查询客户条件
            props: {
                label: "testPackageName",
                children: "parentId",
                isLeaf: "leaf",
                value: "id",
                remark: "remark",
            },
            onloadTreeFlag: false,
            templateData: [],
            templateLabData: [],
            templateLabContactData: [],
            clientHeight: "",
            templateCustomerId: "",
            needSupplier: false,
            needManufacture: false,
            buyerCustomerGroupData: [],
            agentCustomerGroupData: [],
            customerAddressData: [],
            customerContactData: [],
            returnSampleData: [], //退样要求
            serviceTypeData: [],
            reportLanguageData: [],
            // invoiceData: [],
            communicationTableData: [],
            identicalFlag: true, //默认与申请人一样
            isSelfRefrenceFlag: false,
            isEfillingFlag: false,
            isCommentFlag: false,
            isCopyFlag: false,
            isPhotoFlag: false,
            isConfimCoverFlag: false,
            testPackageIds: [],
            testId: "",
            sourceType: "",
            /*表单参数*/
            trf: {
                beLongId: null,
                buyerNo: "",
                agentCustomerName: "",
                isGeneralRequest: 0,
                isCpRequest: 0,
                trfSourceType: TrfSourceEnums.ONLINE_TRF.NAME,
                trfSourceId: null,
                testPackageList: [],
                isSelfRefrence: 0,
                isSame: 0,
                trfType: TrfTypeEnums.DEFAULT_TEMPLATE.CODE, //默然买家模版建单
                materialIds: "",
                applicantUser: "",
                id: "",
                actionType: "add",
                remark: "",
                retestReportNo: "", //重新测试原来的reportNo需录入
                dffFormId: "",
                dffGridId: "",
                dffGridData: null,
                dffFormData: null,
                careLabelData: null,
                trfStatus: "0",
                trfTemplateId: "",
                templateName: "",
                productLineId: "",
                productLineCode: "",
                productLineName: "",
                retest_report_no: "", //上次重测编号
                serviceType: "",
                serviceTypeName: "",
                reportDeliveredTo: "",
                failedReportDeliveredTo: "",
                sampleReceiveDate: "",
                testPackageAllIds: "",
                customerList: [],
                trfCustomer: {
                    buyerCustomerGroupId: "",
                    buyerCustomerGroupCode: "",
                    // 只有选择的是buyer客户的时候才有值
                    buyerCustomerNo: "",
                    buyerCustomerGroupName: "",
                    agentCustomerGroupId: "",
                    agentCustomerGroupCode: "",
                    agentCustomerGroupName: "",
                    sgsCustomerId: "",
                    sgsAccountId: "",
                    customerId: "",
                    customerAddressId: "",
                    customerAddressEn: "",
                    customerNameZh: "",
                    customerNameEn: "",
                    payCustomerNameZh: "",
                    payCustomerAddressZh: "",
                    payCustomerNameEn: "",
                    payCustomerAddressEn: "",
                    isSame: "1",
                },
                // invoice: {},
                trfCustomerContact: {},
                trfLab: {},
                trfLabContact: {},
                servicRequire: {
                    vatType: null,
                    reportLanguage: "", //报告语言
                    reportLanguageName: "",
                    judgmentRule: "",
                },
                trfAttachments: [],
            },
            trfUserTemplate: {
                trfType: TrfTypeEnums.USER_TEMPLATE.CODE,
                productLineCode: "",
                trfTemplateName: "",
                trfTemplateData: "",
                trf_name: "",
                trfTemplateId: null,
                trfBuyerCustomerGroupName: "",
                trfBuyerCustomerGroupCode: "",
            },
            confirmSampleForm: {
                id: "",
                dueDate: null,
                sampleReceiveDate: new Date(),
            },
            queryDffFormGeneralDataParam: {
                customerGroupId: "General",
                buCode: "SL",
                type: "FORM",
                systemCode: "PreOrder",
                //moduleCode: "TRF-Product"
            },

            buyerCustomerGroupParam: {
                customerNumber: "",
                customerGroupCode: "",
            },
            scmCustomerReqParam: {
                page: 1,
                rows: 9999,
                list: [],
            },
            page: {
                currentPage: 1,
                pageSize: 1000,
            },
            customerContactParam: {
                customerId_equal: "",
                status: "1",
            },
            testPackageParam: {
                templateId: "",
                trfStatus: "1",
            },
            testPackageCheckedList: [],
            customerParam: {
                number: "",
                taxNo: "",
                customerName: "",
                rows: 20,
            },
            customerGroupParam: {
                rows: 5,
                groupName: "",
                groupCode: "",
            },
            agentCustomerParam: {},
            templateDataParam: {
                productLineCode: "",
                isLoadGeneral: 1,
                customerGroupCode: "",
                customerBossNo: "",
                trfStatus: "1",
            },
            templateLabDataParam: {
                trfTemplateId: "",
                productLineCode: "",
                trfStatus: "1",
            },
            templateLabContactDataParam: {
                trfTemplateId: "",
                labCode: "",
                labTypeFlag: "",
                trfStatus: "1",
            },
            buyerPkAgentParam: {
                agentGroupCode: "",
                buyerGroupCode: "",
            },
            sgsLabParam: {
                buCode: "",
                labCode: "",
            },
            communicationParam: {},
            pickerOptions0: {
                disabledDate: this.disabledConfirmDate,
                selectableRange: "00:00:00 - 23:59:59",
            },
            pickerOptions1: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7
                },
            },
            sampleHasCarelabel: false,
            //****dff Form 组件参数
            dffFormObjNew: {
                dffFormId: "",
                dffGridId: "",
                dffFormData: "",
                dffGridData: "",
                languageId: 1,
            },
            //================================
            newMatrilaObj: {
                serviceType: "",
                productLineCode: "",
                labName: "",
                labCode: "",
                labContact: "",
            },
            //==============================
            languageID: 1,
            dffLanguageObj: {
                CN: {},
                EN: {},
            },
            dffLanguageDefaultObj: {
                CN: {},
                EN: {},
            },
            dffHeaderConfig: [],
            disabledFlag: false,
            //dff Grid 组件参数
            dataList: {
                /*'EN': [{
                        productItemNo: 'Sample_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }],
                    'CN': [{
                        productItemNo: 'Sample_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }]*/
            },
            defaultDataList_new: {
                /*'EN': [{
                        productItemNo: 'Sample_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }],
                    'CN': [{
                        productItemNo: 'Sample_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }]*/
            },
            //***********careLabel组件参数结束
            trfUserTemplateRules: {
                trfTemplateName: [
                    {
                        required: true,
                        message: this.$t("trf.placeholder.trfTemplateName"),
                        trigger: "blur",
                    },
                    /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                ],
            },
            confirmSampleFormRules: {
                /*'sampleReceiveDate': [
                    {required: true, validator: validateSampleReceiveDate, trigger: 'change'},
                  ],*/
            },
            trfRules: {
                trfTemplateId: [
                    {
                        required: true,
                        validator: validateTemplate,
                        trigger: "change",
                    },
                ],
                // 'trfCustomer.buyerCustomerGroupCode': [
                //     {required: true, validator: validateBuyerCustomer, trigger: 'change'},
                // ],
                buyerNo: [
                    {
                        required: true,
                        validator: validateBuyerCustomer,
                        trigger: "change",
                    },
                ],
                "applicationCustomer.customerNameEn": [
                    {
                        required: true,
                        validator: validateCustomerName,
                        trigger: "blur",
                    },
                ],

                "trfCustomer.customerNameEn": [
                    {
                        required: true,
                        validator: validateCustomerName,
                        trigger: "blur",
                    },
                ],

                "trfCustomer.customerNameCh": [
                    {
                        required: true,
                        validator: validateCustomerName,
                        trigger: "blur",
                    },
                ],
                "trfCustomer.customerAddressEn": [
                    {
                        required: true,
                        validator: validateCustomerAddress,
                        trigger: "change",
                    },
                ],
                "trfLab.labCode": [
                    {required: true, validator: validateLab, trigger: "change"},
                ],
                "trfCustomerContact.applyContactName": [
                    {
                        required: true,
                        validator: validateContactNameLength,
                        trigger: "change",
                    },
                ],
                "trfCustomerContact.applyContactTel": [
                    {
                        required: true,
                        validator: validateApplyContactTel,
                        trigger: "blur",
                    },
                ],
                "trfCustomerContact.applyContactEmail": [
                    {
                        required: true,
                        validator: validateApplyContactEmail,
                        trigger: "blur",
                    },
                ],

                "trfCustomer.payCustomerNameEn": [
                    {
                        required: true,
                        validator: validatePayCustomerNameEn,
                        trigger: "blur",
                    },
                ],
                "trfCustomer.payCustomerAddressEn": [
                    {
                        required: true,
                        validator: validatePayCustomerAddressEn,
                        trigger: "blur",
                    },
                ],
                "trfCustomerContact.payContactName": [
                    {
                        required: true,
                        validator: validatePayContactName,
                        trigger: "blur",
                    },
                ],
                "trfCustomerContact.payContactTel": [
                    {
                        required: true,
                        validator: validatePayContactTel,
                        trigger: "blur",
                    },
                ],
                "trfCustomerContact.payContactEmail": [
                    {
                        required: true,
                        validator: validatePayContactEmail,
                        trigger: "blur",
                    },
                ],
            },
            testPackageTableData: [],
            formProgress: 0,
            minScreen: false,
            treeSelectedIds: [],
            treeSearch: "",
            tempCheckList: [],
            env: "uat",
            onloadDFFFlag: false,
            basicModifyDisabled: true,
            basicSaveLoading: false,
        }
    },
    computed: {
        ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
        permissionList() {
            return {
                removeBtn: this.vaildData(
                    this.permission["sgs:trf:removeTrf"],
                    false,
                ),
                saveAsTemplateBtn: this.vaildData(
                    this.permission["sgs:trf:saveAsTemplateTrf"],
                    false,
                ),
                returnTrfBtn: this.vaildData(
                    this.permission["sgs:trf:returnTrf"],
                    false,
                ),
                trfSaveBtn: this.vaildData(
                    this.permission["sgs:trf:saveTrf"],
                    false,
                ),
                trfSubmitBtn: this.vaildData(
                    this.permission["sgs:trf:submitTrf"],
                    false,
                ),
                confirmSampleBtn: this.vaildData(
                    this.permission["sgs:trf:confirmSample"],
                    false,
                ),
                cancelBtn: this.vaildData(
                    this.permission["sgs:trf:cancel"],
                    false,
                ),
                privewTemplateBtn: this.vaildData(
                    this.permission["sgs:trf:privewTemplate"],
                    false,
                ),
                trfHistoryBtn: this.vaildData(
                    this.permission["sgs:trf:history"],
                    false,
                ),
                basicModifyBtn: this.vaildData(
                    this.permission["sgs:trf:basic:modify:btn"],
                    true,
                ),
            }
        },
        role() {
            return {
                isSGS:
                    this.haseRole("SGSUserRole", "SgsAdmin") ||
                    this.haseRole("SGSUserRole", "SgsLabUser"),
                isThirdPartyLab: this.haseRole("UserRole", "ThirdPartyLab"),
                isBuyer: this.haseRole("UserRole", "Buyer"),
                isLabUser: this.haseRole("SGSUserRole", "SgsLabUser"),
            }
        },
        applyEmailValue: {
            get() {
                if (this.trf.trfCustomerContact.applyContactEmail) {
                    return this.$lodash.split(
                        this.trf.trfCustomerContact.applyContactEmail,
                        ",",
                    )
                }
                return []
            },
            set(val) {
                this.$set(
                    this.trf.trfCustomerContact,
                    "applyContactEmail",
                    this.$lodash.join(val, ","),
                )
            },
        },
        payEmailValue: {
            get() {
                if (this.trf.trfCustomerContact.payContactEmail) {
                    return this.$lodash.split(
                        this.trf.trfCustomerContact.payContactEmail,
                        ",",
                    )
                }
                return []
            },
            set(val) {
                this.$set(
                    this.trf.trfCustomerContact,
                    "payContactEmail",
                    this.$lodash.join(val, ","),
                )
            },
        },
    },
    mounted() {
        this.env = currentEnv
        let that = this
        if (this.language == "zh-CN") {
            this.languageNumber = 2
        } else {
            this.languageNumber = 1
        }
        //只有非admin，非labUser 需要控制alwasys
        this.dynamicFormOnlyDisplayAlways = !(
            this.role.isSGS || this.role.isThirdPartyLab
        )
        this.initTrf()

        const nav = document.getElementsByClassName("nav-item")
        const cont = document.getElementsByClassName("content-item")
        this.element.nav = nav
        this.element.content = cont
        //导航初始化样式（默认第一个）
        nav[0].classList.add("active")
        //事件监听
        window.addEventListener("scroll", this.toScroll)
    },
    watch: {
        templateSpecificCode(newV, oldV) {
            //模板只要发生改变，就重置efiling选项为false
            this.isEfillingFlag =
                false ||
                (!!this.trf.efilingList && this.trf.efilingList.length > 0)
        },
        isEfillingFlag(newValue, oldValue) {
            const ef = this.navList.find((item) => item.alias === "eFiling")
            ef.hide = !newValue
        },
        "$store.state.user.taskType"(newVal, oldVal) {
            if (newVal == "") this.templateDialogVisible = true // 引导完打开弹窗
        },
        hash: function (newVal) {
            //获取路由参数
            this.getParams()
        },
        applyEmailValue(val, old) {
            if (!this.firstLoad)
                setTimeout(() => {
                    this.computedProgress()
                }, 100)
        },
        "trf.trfCustomerContact.applyContactName"() {
            if (!this.firstLoad)
                setTimeout(() => {
                    this.computedProgress()
                }, 100)
        },
        "trf.trfTemplateId"() {
            this.clearExtendMenus()
        },
        "trf.trfLab.labCode"() {
            if (!this.firstLoad)
                setTimeout(() => {
                    this.computedProgress()
                }, 100)
        },

        filterText(val) {
            if (!validatenull(val)) {
                val = val.toLowerCase()
            }
            this.$refs.selTree.filter(val)
        },
        treeFilterText(val) {
            this.$refs.myTree.filter(val)
        },
        filterSelectedText: {
            //首次绑定是否执行handler
            immediate: true,
            //一般情况下，数据发生变化handler才会执行
            handler(val) {
                //过滤数据赋值给新数组
                if (this.recoveryList) {
                    this.filterList = this.recoveryList.filter((item) => {
                        //判断是否在数组中存在
                        if (item.replyContent == null) item.replyContent = ""
                        return item.replyContent.indexOf(val) !== -1
                    })
                }
            },
        },

        //监听语言变化
        language: function (newVal) {
            if (newVal == "zh-CN") {
                this.languageNumber = 2
            } else {
                this.languageNumber = 1
            }
            if (this.showDynamicForm) {
                this.$refs.VFormDesignrender.changeLanguage(this.languageNumber)
            }
            //this.queryCustomer();
            this.queryReportLanguage()
            //重新触发查询communication log
            if (this.trf.trfStatus >= 2) {
                this.queryCommunicationLog()
            }
            this.searchServiceType(this.trf.productLineId)
            if (!validatenull(this.trf.trfTemplateId)) {
                this.$set(
                    this.templateLabDataParam,
                    "productLineCode",
                    this.trf.productLineCode,
                )
                this.$set(
                    this.templateLabDataParam,
                    "trfTemplateId",
                    this.trf.trfTemplateId,
                )
                this.searchTemplateLabData()
            }
        },
        //监听报告语言变化
        "$i18n.locale": function () {
            //this.$refs['trf'].clearValidate();

            if (this.$refs["trf"] != undefined) {
                this.$refs["trf"].fields.forEach((item) => {
                    if (item.validateState === "error") {
                        this.$refs["trf"].validateField(item.labelFor)
                    }
                })
            }
        },

        // 监听`clientHeight`发生改变
        clientHeight: function () {
            this.changeFixed(this.clientHeight)
        },
        /* identicalFlag: function (newVal) {
                this.trf.trfCustomer.isSame = 0;
                this.$set(this.trf.trfCustomerContact, 'isSame', 0);
                if (newVal) {
                    this.trf.trfCustomer.isSame = 1;
                    this.$set(this.trf.trfCustomerContact, 'isSame', 1);
                }
            },*/
        isSelfRefrenceFlag: function (newVal) {
            this.$set(this.trf, "isSelfRefrence", 0)
            if (newVal) {
                this.$set(this.trf, "isSelfRefrence", 1)
            }
        },
        showDynamicForm: {
            immediate: true,
            handler(newV) {
                this.showDynamicNavList(newV)
                if (this.showDynamicForm) {
                    this.navList = this.dynamicNavList
                }
            },
        },
        "confirmSampleForm.sampleReceiveDate": {
            immediate: false,
            deep: true,
            handler(newV) {
                let selectDate = new Date(newV)
                let beginDate = new Date(
                    this.currentTzDate(this.trf.trfSubmissionDate),
                )
                var y = selectDate.getFullYear()
                var m = selectDate.getMonth()
                var d = selectDate.getDate()
                var by = beginDate.getFullYear()
                var bm = beginDate.getMonth()
                var bd = beginDate.getDate()
                var hours = beginDate.getHours()
                var minutes = beginDate.getMinutes()
                var seconds = beginDate.getSeconds()
                hours = hours < 10 ? "0" + hours : hours
                minutes = minutes < 10 ? "0" + minutes : minutes
                seconds = seconds < 10 ? "0" + seconds : seconds
                this.pickerOptions0.selectableRange = "00:00:00 - 23:59:59"
                if (y == by && m == bm && d == bd) {
                    this.pickerOptions0.selectableRange = `${hours}:${minutes}:${seconds} - 23:59:59`
                }
            },
        },
        fullPageLoading: {
            immediate: true,
            handler(newV) {
                if (newV) {
                    setTimeout(() => {
                        this.fullPageLoadingText =
                            "Data loading is slow, please refresh the page"
                    }, 10000)
                } else {
                    this.fullPageLoadingText = "Loading data..."
                }
            },
        },
    },
    filters: {
        remarkFilter: function (value) {
            value = value.toString()
            if (value.length > 20) {
                value = value.slice(0, 20) + "..."
            }
            return value
        },
    },

    methods: {
        //copy、Usertemplate建单 重新获取最新CustomerGroup Name信息
        async reloadCustomerGroupInfo() {
            if (validatenull(this.trf)) {
                return
            }
            let buyerCustomer = this.getBuyerCustomerObj()
            if (validatenull(buyerCustomer)) {
                return
            }
            let buyerCustomerGroupCode = buyerCustomer.customerGroupCode
            if (
                buyerCustomerGroupCode != "General" &&
                !validatenull(buyerCustomerGroupCode)
            ) {
                //查询buyer数据后再重新赋值
                var customerParams = {}
                customerParams.groupCode = buyerCustomerGroupCode
                let customerGroups = []
                customerGroups = await getCustomerGroupByParms(
                    customerParams,
                ).then((res) => {
                    return res.data.data
                })
                if (!validatenull(customerGroups)) {
                    let newGroup = customerGroups[0]
                    let groupName = newGroup.customerGroupName
                    if (!validatenull(groupName)) {
                        if (!objectIsNull(this.trf.customerList)) {
                            for (let customerObj of this.trf.customerList) {
                                if (
                                    customerObj.customerUsage ===
                                    this.CustomerUsageEnums.BUYER.code
                                ) {
                                    customerObj.customerGroupName = groupName
                                    this.trf.trfCustomer.buyerCustomerGroupName =
                                        groupName
                                }
                            }
                        }
                    }
                }
            }
            this.$emit("trf", this.trf)
        },
        tipsReference() {
            console.log("tipsReference", this.isSelfRefrenceFlag)
            if (!this.isSelfRefrenceFlag) {
                return
            }
            this.$confirm(this.$t("trf.tipReference"), this.$t("tip"), {
                confirmButtonText: this.$t("trf.tipsConfirm"),
                cancelButtonText: this.$t("trf.tipsCancel"),
                type: "warning",
            })
                .then(() => {})
                .catch(() => {
                    this.isSelfRefrenceFlag = false
                })
        },
        //从CustomerList中 获取买家对象
        getBuyerCustomerObj() {
            let buyerCustomerObj = null
            if (!validatenull(this.trf.customerList)) {
                buyerCustomerObj = this.trf.customerList.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.BUYER.code) {
                        return item
                    }
                })
            }
            return buyerCustomerObj
        },
        updateBuyerInfo(buyerInfo) {
            if (objectIsNull(buyerInfo)) {
                this.$set(this.trf, "buyerNo", null)
                return false
            }
            let buyerNo = buyerInfo.customerGroupCode
            if (validatenull(buyerNo)) {
                buyerNo = buyerInfo.bossNo
            }
            this.$set(this.trf, "buyerNo", buyerNo + "")
        },
        //SMART-3838 更新重新选择的Buyer Group信息
        async updateBuyerGroupInfo(buyergroupInfo) {
            this.trf.trfCustomer.buyerCustomerGroupCode =
                buyergroupInfo.customerGroupCode
            this.trf.trfCustomer.buyerCustomerGroupName =
                buyergroupInfo.customerGroupName
            this.trf.trfCustomer.buyerCustomerGroupId =
                buyergroupInfo.customerGroupId
            this.trf.buyerNo = buyergroupInfo.customerGroupCode
            //重新加载模板数据
            //查询模板数据
            this.$set(this.templateDataParam, "trfStatus", "1")
            this.$set(
                this.templateDataParam,
                "customerGroupCode",
                buyergroupInfo.customerGroupCode,
            )
            this.$set(
                this.templateDataParam,
                "productLineCode",
                this.productLineCode,
            )
            if (validatenull(this.productLineCode)) {
                this.$set(
                    this.templateDataParam,
                    "productLineCode",
                    this.trf.productLineCode,
                )
            }
            var params = {}
            let res = await getTemplateList(
                Object.assign(params, this.templateDataParam),
            )
            const data = res.data.data
            let templateobj = null
            if (!validatenull(data)) {
                templateobj = data.find((item) => {
                    return (
                        item.customerGroupCode ===
                        buyergroupInfo.customerGroupCode
                    )
                })
            }
            if (!validatenull(templateobj)) {
                //存在客户组模板 给出错误提示
                this.$confirm(
                    this.$t("trf.customerTemplateExistError"),
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        //清空原有选择模板，并打开模板选择界面
                        this.trf.trfTemplateId = ""
                        this.trf.templateName = ""
                        this.templateData = data
                        this.templateDialogVisible = true
                    })
                    .catch(() => {})
            }
            this.$emit("trfDetail", this.trf)
        },
        async mapping() {
            let dffInfo = this.$refs.frm.getDatas()
            const res = await listForSelect(
                this.trf.trfCustomer.buyerCustomerGroupName,
            )
            this.$refs.eFilling.getBuyerCustomerId(
                this.trf.trfCustomer.buyerCustomerGroupName,
            )
            const buyerCustomerList = res.data.data
            const buyer = buyerCustomerList.find(
                (item) =>
                    item.bossName ==
                    this.trf.trfCustomer.buyerCustomerGroupName,
            )
            let type = (this.languageID = 1 ? "EN" : "CN") //EN
            const formData = this.transSaveData(dffInfo.formValues)[type]
            const resTrade = await tradeSelect(
                "Manufacture",
                buyer ? buyer.customerId : "",
                formData.factoryName,
            )
            const manufactureCustomerIds = resTrade.data.data
            const manufacture = manufactureCustomerIds.find(
                (item) => item.tradeName == formData.factoryName,
            )
            console.log(this.trf)
            const mapping = await doMapping({
                productLineCode: this.trf.productLineCode,
                customerCode: this.trf.trfCustomer.buyerCustomerGroupCode,
                formData: JSON.stringify(formData),
                formList: JSON.stringify(dffInfo.gridValues[type]),
            })
            let param = {}
            param.buyerCustomerId = buyer ? buyer.id : undefined

            param.basicLabAddress = this.trf.trfLab.labAddress
            //  param.basicLabContactName=this.trf.trfLabContact.contactName
            param.basicServiceType = this.trf.serviceType
            param.basicLabId = this.trf.trfLab.labCode
            this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =
                this.trf.trfAttachments
            this.$refs.eFilling.newFormModel = Object.assign(
                this.formModel,
                param,
            )

            manufacture &&
                (await this.$refs.eFilling.setPOC(
                    "Manufacture",
                    buyer ? buyer.customerId : "",
                    formData.factoryName,
                ))

            manufacture &&
                this.$refs.eFilling.handleTradeChange(
                    manufacture.id,
                    manufacture,
                )
            let manufactureSub = {}
            if (manufacture) {
                manufactureSub.manufactureContactId =
                    manufacture.cpscCustomerTradeContactVO.id
                manufactureSub.manufactureContactName =
                    manufacture.cpscCustomerTradeContactVO.contactName
                manufactureSub.manufactureContactTelephone =
                    manufacture.cpscCustomerTradeContactVO.telphone
                manufactureSub.manufactureCustomerAddress =
                    manufacture.tradeAddress
                manufactureSub.manufactureCustomerAddressLocal =
                    manufacture.tradeAddress2
                manufactureSub.manufactureCustomerCityId = manufacture.tradeCity
                manufactureSub.manufactureCustomerCityName =
                    manufacture.tradeCity
                manufactureSub.manufactureCustomerCountryId =
                    manufacture.tradeCountryId
                manufactureSub.manufactureCustomerCountryName =
                    manufacture.tradeCountryName
                manufactureSub.manufactureCustomerId = manufacture.id
                manufactureSub.manufactureCustomerName = manufacture.tradeName
                manufactureSub.manufactureCustomerNameLocal =
                    manufacture.tradeName
                manufactureSub.manufactureContactEmaill =
                    manufacture.cpscCustomerTradeContactVitem
            }
            this.productInfoList = this.productInfoList.map((item) => {
                // item.formList[0].productId = mapping.data.data.productId
                return {...item, ...mapping.data.data, ...manufactureSub}
            })
            buyer && this.$refs.eFilling.handleBuyerChange(buyer.id)
        },
        async downAttachment(cloudId) {
            getCloudFileURL(cloudId).then((res) => {
                window.open(res.data, "_blank")
            })
        },
        disabledConfirmDate(time) {
            let beginDate =
                new Date(
                    this.currentTzDate(this.trf.trfSubmissionDate),
                ).getTime() - 8.64e7
            let picDate = time.getTime()
            let date = new Date()
            return picDate - beginDate <= 0 || picDate > date.getTime()
        },
        currentTzDate(val) {
            if (!val) return ""
            let value = tzFormChina(val)
            return value
        },

        tzToChina(val) {
            if (!val) return ""
            let value = tzToChina(val)
            return value
        },
        checkObjectIsNull(val) {
            return objectIsNull(val)
        },
        currentTz_YMD(val) {
            if (!val) return ""
            let value = tzFormChina(val, "YYYY-MM-DD HH:mm:ss")
            return moment(value).format("YYYY-MM-DD")
        },
        handleBlur(e) {
            setTimeout(() => {
                this.computedProgress()
            }, 200)
        },
        handleFocus(e) {
            setTimeout(() => {
                this.addSelectListen()
            }, 200)
        },
        // 添加监听
        addSelectListen() {
            let that = this
            let nodes = document.querySelectorAll(".el-select-dropdown__item")
            nodes.forEach((item) => {
                item.addEventListener("click", function (e) {
                    that.computedProgress()
                })
            })
        },
        // 进度计算
        computedProgress() {
            let that = this
            let gridRequireInput = []
            let gridRequireTextarea = []
            let require = document.querySelectorAll(".is-required")
            // let elements = [...document.querySelectorAll('.is-required')].filter(el => !el.closest('.el-table__fixed-body-wrapper'));
            var ipts = document.querySelectorAll(
                ".el-table__body-wrapper input",
            )
            ipts.forEach((item) => {
                if (
                    item.getAttribute("id") &&
                    item.getAttribute("id").indexOf("validation") != -1
                )
                    gridRequireInput.push(item)
            })

            let textAreas = document.querySelectorAll(
                ".el-table__body-wrapper  textarea",
            )
            textAreas.forEach((item) => {
                if (
                    item.getAttribute("id") &&
                    item.getAttribute("id").indexOf("validation") != -1
                )
                    gridRequireTextarea.push(item)
            })

            let grid = gridRequireInput.concat(gridRequireTextarea)
            grid.forEach((item) => {
                item.addEventListener("input", cback, true)
            })
            var inputs = [].concat(grid)
            if (require) {
                require.forEach((item, index) => {
                    let tag = item.querySelector(".el-select__tags") // 下拉
                    if (tag) {
                        let tagsArr = tag.querySelectorAll(".el-tag")
                        if (tagsArr) inputs.push(tagsArr)
                    } else {
                        var els = item.querySelectorAll("input, textarea")
                        for (var j = els.length; j--; ) {
                            if (
                                els[j].type != "button" &&
                                els[j].type != "submit" &&
                                els[j].type != "checkbox" &&
                                els[j].type != "radio"
                            ) {
                                inputs.push(els[j])
                                els[j].addEventListener("input", cback, true)
                            }
                        }
                    }
                    // 处理 checkbox group
                    handleCheckboxGroup(item, cback, inputs)
                    // 单独处理 radio 元素
                    handleRadioElements(item, cback, inputs)
                })
            }
            cback()

            function cback(e) {
                var arr = []
                // 去重 id 相同的元素
                const uniqueInputs = []
                const seenIds = {}
                for (let i = 0; i < inputs.length; i++) {
                    const input = inputs[i]
                    // 检查节点是否为空节点，若为空则跳过
                    if (input.length === 0) {
                        continue
                    }
                    const id = input.id
                    if (id && !seenIds[id]) {
                        seenIds[id] = true
                        uniqueInputs.push(input)
                    } else if (!id) {
                        // 如果元素没有 id，直接添加
                        uniqueInputs.push(input)
                    }
                }
                for (var i = uniqueInputs.length; i--; ) {
                    if (
                        uniqueInputs[i].classList &&
                        uniqueInputs[i].classList.contains("el-checkbox-group")
                    ) {
                        const hasElSelect =
                            uniqueInputs[i].querySelector(".el-select") !== null
                        const hasElTag =
                            uniqueInputs[i].querySelector(".el-tag") !== null
                        const checkedCheckboxes = uniqueInputs[
                            i
                        ].querySelectorAll('input[type="checkbox"]:checked')
                        if (checkedCheckboxes.length > 0) {
                            if (hasElSelect && hasElTag) {
                                arr.push(uniqueInputs[i])
                            }
                            if (!hasElSelect) {
                                arr.push(uniqueInputs[i])
                            }
                        }
                    }
                    if (
                        uniqueInputs[i].classList &&
                        uniqueInputs[i].classList.contains("el-radio-group")
                    ) {
                        const checkedCheckboxes = uniqueInputs[
                            i
                        ].querySelectorAll('input[type="radio"]:checked')
                        if (checkedCheckboxes.length > 0) {
                            arr.push(uniqueInputs[i])
                        }
                    }
                    if (
                        (uniqueInputs[i].value != void 0 &&
                            uniqueInputs[i].value.length > 0) ||
                        uniqueInputs[i].length > 0
                    ) {
                        arr.push(uniqueInputs[i])
                    }
                }

                var r = arr.length // 已填值长度
                var i = uniqueInputs.length // 所有要填写input长度
                that.filledInCount = r
                that.totalCount = i
                //console.log("全局数量缓存：---", r, i)
                that.formProgress = Math.floor((r / i) * 100)
                that.$emit("formProgress", that.formProgress)
            }

            // 处理 checkbox group
            function handleCheckboxGroup(item, cback, inputs) {
                const checkboxGroups =
                    item.querySelectorAll(".el-checkbox-group")
                checkboxGroups.forEach((group) => {
                    inputs.push(group)
                    // 为整个 group 绑定 change 事件
                    group.addEventListener("change", cback, true)
                })
            }
            // 处理 radio 元素
            function handleRadioElements(item, cback, inputs) {
                const radioGroups = item.querySelectorAll(".el-radio-group")
                radioGroups.forEach((group) => {
                    inputs.push(group)
                    // 为整个 group 绑定 change 事件
                    group.addEventListener("change", cback, true)
                })
            }
        },
        serviceItemChange(count) {
            let filledInCount = this.filledInCount
            if (count > 0) {
                filledInCount = this.filledInCount - count
            }
            this.formProgress = Math.floor(
                (filledInCount / this.totalCount) * 100,
            )
            this.$emit("formProgress", this.formProgress)
        },
        //点击导航
        toTarget(index) {
            const {content, nav} = this.element
            for (let n = 0; n < nav.length; n++) {
                n == index
                    ? nav[n].classList.add("active")
                    : nav[n].classList.remove("active")
            }
            const scrollTop =
                document.documentElement.scrollTop || document.body.scrollTop
            switch (index) {
                case 0:
                    document.documentElement.scrollTop = 0
                    return
            }
            document.documentElement.scrollTop =
                (document.documentElement.scrollTop === 0
                    ? -150
                    : document.documentElement.scrollTop) +
                content[index].getBoundingClientRect().top -
                100
        },
        //屏幕滚动
        toScroll() {
            if (this.showStep < 1) return
            let navEle = document.querySelector(".nav-list")
            if (objectIsNull(navEle)) {
                return
            }
            let trfBase = document.querySelector(".trf-base")
            navEle.style.width = navEle.clientWidth + "px"
            var w
            var commentEl // 评论列表
            if (document.querySelector(".scroll-box")) {
                commentEl = document.querySelector(".scroll-box")
                w = commentEl.clientWidth
                commentEl.style.width = w + "px"
            }
            //获取滚动距离
            const scrollTop =
                document.documentElement.scrollTop || document.body.scrollTop
            const {content, nav} = this.element
            this.minScreen = document.body.offsetWidth <= 1366 ? true : false
            window.addEventListener("resize", () => {
                this.minScreen =
                    document.body.offsetWidth <= 1366 ? true : false
            })
            // 侧边栏和评论栏固定
            if (scrollTop != undefined && scrollTop > 170) {
                navEle.style.position = "fixed"
                navEle.style.top = "80px"
                if (commentEl) commentEl.style.position = "fixed"
                if (commentEl) commentEl.style.top = "80px"
                if (trfBase) {
                    trfBase.style.position = "fixed"
                    trfBase.style.width = "calc(100% - 64px)"
                    trfBase.style.top = this.minScreen ? "-69px" : "-91px"
                    trfBase.style.left = "32px"
                }
            } else {
                navEle.style.position = "initial"
                if (commentEl) commentEl.style.position = "initial"
                if (trfBase) {
                    trfBase.style.position = "relative"
                    trfBase.style.width = "100%"
                    trfBase.style.top = "0"
                    trfBase.style.left = "0"
                }
            }
            // 侧边栏菜单添加当前高亮
            for (let i = 0, len = content.length; i < len; i++) {
                //获取每块内容距离顶部距离
                const offset = content[i].offsetTop - 100
                //当划过第一块内容 改变左侧导航样式
                if (scrollTop >= offset) {
                    for (let n = 0; n < len; n++) {
                        if (!nav[n]) return
                        n == i
                            ? nav[n].classList.add("active")
                            : nav[n].classList.remove("active")
                    }
                }
            }
            if (scrollTop == 0) {
                nav[0].classList.add("active")
                nav[1].classList.remove("active")
            }
            let scrollHeight =
                document.documentElement.scrollHeight ||
                document.body.scrollHeight
            let clientHeight =
                document.documentElement.clientHeight || window.innerHeight
            if (
                this.showStep === 6 &&
                scrollTop + clientHeight >= scrollHeight
            ) {
                this.toTarget(this.navList.length - 1)
            }
        },
        resetChecked() {
            this.$refs.selTree.setCheckedKeys([])
        },
        trfHistoryClick() {
            this.historyVisible = true
        },
        addTestPackageRemark() {
            this.node.data.remark = this.remark
            this.testPackageVisible = false
        },
        showMore(node, testPackageTreedata) {
            node.expanded = true
        },
        handleRemarkFoucs(index, rows) {
            if (rows.remark == "Remark") {
                this.$set(rows, "remark", "")
                // this.$set(this.testPackageTableData[index], 'remark', '')
            }
        },
        handleRemarkBlur(index, rows) {
            if (rows.remark == "") {
                this.$set(rows, "remark", "Remark")
                this.setNodeRemarkWithId(
                    this.testPackageTreedata,
                    rows.id,
                    "Remark",
                )
            } else {
                this.setNodeRemarkWithId(
                    this.testPackageTreedata,
                    rows.id,
                    rows.remark,
                )
            }
        },
        setNodeRemarkWithId(arr, nodeId, remark) {
            arr.forEach((item) => {
                if (item.id == nodeId) item.remark = remark
                if (item.children)
                    this.setNodeRemarkWithId(item.children, nodeId, remark)
            })
        },
        addRemark(node, testPackageTreedata) {
            //打开录入remark弹框
            this.testPackageVisible = true
            this.remark = ""
            if (node.data.remark != "Remark" && node.data.remark != "remark") {
                this.remark = node.data.remark
            }
            this.node = node
        },
        async initTrf() {
            //判断当前登录用户是否为SGS
            let isSgs = false
            if (
                !validatenull(this.userInfo.dimensions) &&
                !validatenull(this.userInfo.dimensions.SGSUserRole)
            ) {
                let role = this.userInfo.dimensions.SGSUserRole
                role.forEach((item) => {
                    if (item === "SgsAdmin" || item === "SgsLabUser") {
                        isSgs = true
                    }
                })
            }
            this.isSupplier = this.haseRole("UserRole", "Supplier")
            if (!objectIsNull(this.userInfo.companyId)) {
                this.trf.trfCustomer.customerId = this.userInfo.companyId
            }
            if (isSgs) {
                this.trf.trfType = TrfTypeEnums.DEFAULT_TEMPLATE.CODE
                //SGS查询所有客户组
                await this.queryCustomerGroupData()
            } else {
                //查询当前客户所属BU
                //this.queryCustomerProductLineCode();
                // this.queryBuyerCustomerGroupData(this.userInfo.bossNo);
                await this.queryNewScmCustomerList();
                //await this.queryScmCustomerList(this.userInfo.bossNo)
            }
            //获取路由参数
            this.getParams()
            //查询客户组邮件和联系人邮件数据
            this.searchEmailGroup()

            this.fieldSettingsData = null
        },
        async submitTemplate() {
            //校验是否存在Template
            if (validatenull(this.trf.trfTemplateId)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("trf.placeholder.template"),
                    type: "warning",
                })
                return false
            }
            if (["individualSample", "groupSample"].includes(this.optionType)) {
                //单独处理新的material数据
                let result = await this.loadNewMaterialData()
                if (!result) {
                    /*this.$notify({
                         title: this.$t('tip'),
                         message: 'Load material error!',
                         type: 'warning'
                     });*/
                    return
                }
            }

            this.templateDialogVisible = false
            //开启loading效果
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            })
            // 不存在form不显示
            let obj = this.templateChangeObj
            //渲染动态表单
            if (!validatenull(obj)) {
                this.showDynamicForm = false
                this.queryDynamicFormId = ""
                this.queryDynamicFormDesignGroupId = obj.formDesignGroupId

                this.$nextTick(() => {
                    this.showDynamicForm = obj.formType - 0 == 2
                })
            }

            let newDffGridId = null
            let newDffFormId = null
            if (!validatenull(obj.dffFormId) && obj.formType - 0 != 2) {
                let dffFormIds = []
                dffFormIds.push(obj.dffFormId)
                //调用接口 查询最新dffFormID
                let comfirmData = await this.getDffComfirmIdBatchByIds(
                    dffFormIds,
                )
                newDffFormId = obj.dffFormId
                if (!this.checkObjectIsNull(comfirmData)) {
                    newDffFormId = comfirmData[0].comfirmDffFormId
                }
                //TODO 新版DFF
                this.$set(this.dffFormObjNew, "dffFormId", newDffFormId)
                //判断当前语言获取对应的dffForm
                //默认查询英文
                this.$set(this.dffFormObjNew, "languageId", 1)
                if (this.trf.servicRequire.reportLanguage == 3) {
                    this.languageID = this.languageNumber
                } else if (this.trf.servicRequire.reportLanguage == 2) {
                    this.languageID = 2
                    //TODO 新版DFF
                    this.$set(this.dffFormObjNew, "languageId", 2)
                } else {
                    this.dffLanguageObj.CN = {}
                    this.languageID = 1
                    //TODO 新版DFF
                    this.$set(this.dffFormObjNew, "languageId", 1)
                }
                loading.close()
                //    if(newDffFormId!=this.trf.dffFormId ) {//重新渲染DFF
                this.onloadDFFFlag = true
                //    }

                this.$set(this.trf, "dffFormId", newDffFormId)
                // 不存在grid不显示
                this.$set(this.dffFormObjNew, "dffGridId", "")
                if (!validatenull(obj.dffGridId)) {
                    //调用接口 查询最新dffGridID
                    let dffGridIds = []
                    dffGridIds.push(obj.dffGridId)
                    let comfirmData = await this.getDffComfirmIdBatchByIds(
                        dffGridIds,
                    )
                    newDffGridId = obj.dffGridId
                    if (!this.checkObjectIsNull(comfirmData)) {
                        newDffGridId = comfirmData[0].comfirmDffFormId
                    }
                    //TODO 新版DFF
                    this.$set(this.dffFormObjNew, "dffGridId", newDffGridId)

                    this.$set(this.trf, "dffGridId", newDffGridId)
                    /*if (this.trf.servicRequire.reportLanguage == 2 || this.trf.servicRequire.reportLanguage == 3) {
                     this.getLanguageGridObjList(2);
                 } else {
                     this.getLanguageGridObjList(1);
                 }*/
                    if (this.onloadDFFFlag) {
                        this.$set(
                            this.dffFormObjNew,
                            "dffGridData",
                            this.dataList,
                        )
                        this.$nextTick(() => {
                            this.isDffGridLoadFlag = true
                        })
                    }

                    this.$set(this.trf, "carelabelInstances", [])
                    //sample开单，存在洗唛的话，就不能清空
                    if (!this.sampleHasCarelabel) {
                        this.$set(
                            this.dffFormObjNew,
                            "careLabelData",
                            this.trf.carelabelInstances,
                        )
                    }
                    if (!validatenull(obj.specificCode)) {
                        if (obj.specificCode.indexOf("care_Label") != -1) {
                            this.isCareLabelLoadFlag = true
                        }
                    }
                    loading.close()
                } else {
                    loading.close()
                }
            } else {
                loading.close()
            }
            if (this.onloadDFFFlag) {
                //重新渲染DFF
                // this.$set(this.dffFormObjNew, 'dffFormData', '');
                const actionType = this.$route.query.actionType
                if (
                    !["individualSample", "groupSample", "material"].includes(
                        actionType,
                    ) &&
                    newDffFormId != this.trf.dffFormId
                ) {
                    this.$set(this.dffFormObjNew, "dffFormData", "")
                }
                if (
                    !["individualSample", "groupSample", "material"].includes(
                        actionType,
                    ) &&
                    newDffGridId != this.trf.dffGridId
                ) {
                    this.$set(this.dffFormObjNew, "dffGridData", [])
                }
                this.isDffFormLoadFlag = false
                this.$nextTick(() => {
                    this.isDffFormLoadFlag = true
                })
            }

            setTimeout(() => {
                this.addSelectListen()
                this.computedProgress()
                this.firstLoad = false
            }, 600)
            this.onloadDFFFlag = false
            this.$emit("trfDetail", this.trf)
        },
        // queryCustomerProductLineCode() {
        //     queryCustomerForId(this.userInfo.companyId).then(res => {
        //         const data = res.data.data;
        //         this.trf.productLineCode1 = data.productLineCode;
        //     });
        // },
        editTemplate() {
            if (validatenull(this.trf.trfTemplateId)) {
                this.templateDialogVisible = true
            } else {
                //给出提示
                this.$confirm(
                    this.$t("trf.changeTemplateMsg"),
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        this.templateDialogVisible = true
                    })
                    .catch(() => {})
            }
        },

        queryCurrentDeptBeLong(templateId) {
            getDeptBeLongTosByTemplateId(templateId).then((res) => {
                const data = res.data.data
                this.beLongData = data
                if (validatenull(this.beLongData)) {
                    this.isShowBelongFlag = false
                    this.$set(this.trf, "beLongId", null)
                } else {
                    this.isShowBelongFlag = true
                }
            })
        },
        haseRole(type, role) {
            if (validatenull(type) || validatenull(role)) {
                return false
            }
            if (validatenull(this.userInfo.dimensions)) {
                return false
            } else {
                if (this.userInfo.dimensions.hasOwnProperty(type)) {
                    if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                        return true
                    } else {
                        return false
                    }
                } else {
                    return false
                }
            }
        },
        //验证表单项是否展示方法
        valiUsable(code) {
            var usableFlag = true
            if (!objectIsNull(this.fieldSettingsData)) {
                if (
                    this.fieldSettingsData[code] != null &&
                    this.fieldSettingsData[code] != undefined
                ) {
                    usableFlag =
                        this.fieldSettingsData[code].usable != 1 ? false : true
                }
            }
            return usableFlag
        },
        //验证必填项方法
        valiRequired(code) {
            var requiredFlag = false
            if (
                this.fieldSettingsData != null &&
                this.fieldSettingsData != undefined
            ) {
                if (
                    this.fieldSettingsData[code] != null &&
                    this.fieldSettingsData[code] != undefined
                ) {
                    requiredFlag =
                        this.fieldSettingsData[code].required == 1
                            ? true
                            : false
                }
            }
            return requiredFlag
        },
        async queryCustomerGroupData() {
            await getCustomerGroup().then((res) => {
                this.buyerCustomerGroupData = res.data.data
                // 默认添加general数据
                let generalObj = {}
                generalObj.customerGroupId = ""
                generalObj.customerGroupCode = "General"
                generalObj.customerGroupName = "General"
                generalObj.customerType = "group"
                this.buyerCustomerGroupData.push(generalObj)
                // 默认选择General
                if (!this.trf.trfCustomer.buyerCustomerGroupCode) {
                    console.log(
                        ">>>>>没有配置buyerCustomerGroupCode,进行默认值复制",
                    )
                    this.$set(this.trf, "buyerNo", "General")
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupCode",
                        "General",
                    )
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupName",
                        "General",
                    )
                }
            })
        },
        searchEmailGroup() {
            getMailGroupAndContactEmail().then((res) => {
                this.emailGroupData = res.data.data
            })
        },

        applyEmailValueChange(emailList) {
            //调用通用方法
            let obj = {}
            let pasteEmailArr = []
            emailList.find((item1) => {
                obj = this.emailGroupData.find((item) => {
                    if (item1 == item.emailGroupName) {
                        //判断当前邮件是否和
                        return item
                    }
                })
                if (validatenull(obj)) {
                    //手动输入邮箱验证
                    let validateRes = validateEmail(item1)
                    if (validateRes) {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1)
                        }
                    }
                } else {
                    if (obj.type != 2) {
                        //邮件组数据
                        var contactEmail = obj.contacts
                        contactEmail.find((item2) => {
                            if (
                                pasteEmailArr.indexOf(item2.contactEmail) == -1
                            ) {
                                pasteEmailArr.push(item2.contactEmail)
                            }
                        })
                    } else {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1)
                        }
                    }
                }
            })
            this.applyEmailValue = pasteEmailArr
            if (this.applyEmailValue.length != 0) {
                this.trf.trfCustomerContact.applyContactEmail =
                    this.applyEmailValue.join(",")
            } else {
                this.trf.trfCustomerContact.applyContactEmail = ""
            }
        },
        payEmailValueChange(emailList) {
            //调用通用方法
            let obj = {}
            let pasteEmailArr = []
            emailList.find((item1) => {
                obj = this.emailGroupData.find((item) => {
                    if (item1 == item.emailGroupName) {
                        //判断当前邮件是否和
                        return item
                    }
                })
                if (validatenull(obj)) {
                    //手动输入邮箱验证
                    let validateRes = validateEmail(item1)
                    if (validateRes) {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1)
                        }
                    }
                } else {
                    if (obj.type != 2) {
                        //邮件组数据
                        var contactEmail = obj.contacts
                        contactEmail.find((item2) => {
                            if (
                                pasteEmailArr.indexOf(item2.contactEmail) == -1
                            ) {
                                pasteEmailArr.push(item2.contactEmail)
                            }
                        })
                    } else {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1)
                        }
                    }
                }
            })
            this.payEmailValue = pasteEmailArr
            if (this.payEmailValue.length != 0) {
                this.trf.trfCustomerContact.payContactEmail =
                    this.payEmailValue.join(",")
            } else {
                this.trf.trfCustomerContact.payContactEmail = ""
            }
        },

        serviceTypeChange(val) {
            this.trf.serviceTypeName = ""
            let obj = {}
            obj = this.serviceTypeData.find((item) => {
                return item.serviceTypeCode === val
            })

            if (!objectIsNull(obj)) {
                this.trf.serviceTypeName = obj.serviceTypeName
            }
        },
        async reportChange(val) {
            if (val != this.languageID) {
                this.onloadDFFFlag = true
            }
            /*this.isCareLabelLoadFlag = false;
              this.dataList=[];*/
            let obj = {}
            obj = this.reportLanguageData.find((item) => {
                return item.sysKey === val
            })

            if (!objectIsNull(obj)) {
                this.trf.servicRequire.reportLanguageName = obj.sysValue

                if (val == 1) {
                    this.languageID = 1
                } else if (val == 2) {
                    this.languageID = 2
                } else {
                    this.languageID = 1
                }
            }

            let templateObj = {}
            templateObj = this.templateData.find((item) => {
                return item.id === val
            })
            //加载dff form组件
            if (!objectIsNull(templateObj)) {
                if (this.trfFlag == 2) {
                    let fieldData = await getFieldSettingsByTemplateId(
                        templateObj.id,
                    )
                    if (fieldData.data.code == 200) {
                        this.fieldSettingsData = fieldData.data.data
                    }
                }

                // 不存在form不显示
                if (!validatenull(templateObj.dffFormId)) {
                    let dffFormIds = []
                    dffFormIds.push(templateObj.dffFormId)
                    //调用接口 查询最新dffFormID
                    let comfirmData = await this.getDffComfirmIdBatchByIds(
                        dffFormIds,
                    )
                    var newDffFormId = templateObj.dffFormId
                    if (!this.checkObjectIsNull(comfirmData)) {
                        newDffFormId = comfirmData[0].comfirmDffFormId
                    }
                    this.$set(this.trf, "dffFormId", newDffFormId)
                    //判断当前语言获取对应的dffForm
                    //默认查询英文
                    if (this.trf.servicRequire.reportLanguage == 2) {
                        this.languageID = 2
                    } else {
                        this.languageID = 1
                    }

                    // 不存在grid不显示
                    this.$set(this.trf, "dffGridId", "")
                    if (!validatenull(templateObj.dffGridId)) {
                        //调用接口 查询最新dffGridID
                        let dffGridIds = []
                        dffGridIds.push(templateObj.dffGridId)
                        let comfirmData = await this.getDffComfirmIdBatchByIds(
                            dffGridIds,
                        )
                        var newDffGridId = templateObj.dffGridId
                        if (!this.checkObjectIsNull(comfirmData)) {
                            newDffGridId = comfirmData[0].comfirmDffFormId
                        }
                        this.$set(this.trf, "dffGridId", newDffGridId)
                        this.$set(
                            this.dffFormObjNew,
                            "dffGridData",
                            this.dataList,
                        )
                        this.isDffGridLoadFlag = true
                        //清空CareLabel数据
                        this.$set(this.trf, "carelabelInstances", [])
                        this.$set(
                            this.dffFormObjNew,
                            "careLabelData",
                            this.trf.carelabelInstances,
                        )
                        if (!validatenull(obj.specificCode)) {
                            if (obj.specificCode.indexOf("care_Label") != -1) {
                                this.isCareLabelLoadFlag = true
                            }
                        }
                    }
                    this.isDffFormLoadFlag = true
                }
            }
            const actionType = this.$route.query.actionType
            const materialSelIds = this.$route.query.ids
            const materialConfigId = this.$route.query.cid
            if (actionType == "material") {
                await this.onLoadMaterialData(materialSelIds, materialConfigId)
            }

            //重新出发模板change事件
            // if (!validatenull(this.trf.trfTemplateId)) {
            //     this.selectTemplateChange(this.trf.trfTemplateId)
            // }
        },

        getCommentData(v) {
            this.queryCommunicationLog()
        },
        queryCommunicationLog() {
            let communicationParams = {}
            this.communicationParam.trfNo = this.$route.query.trfNo
            this.communicationParam.signature = this.$route.query.signature
            queryTrfComments(
                Object.assign(communicationParams, this.communicationParam),
            ).then((res) => {
                this.communicationTableData = res.data.data
            })
        },
        toPrivewTemplate() {
            //迁移数据暂不支持预览导入
            let prefix = "SR"
            if (this.$route.query.trfNo.startsWith(prefix)) {
                this.$alert(this.$t("trf.printMsg"))
            } else {
                //this.$router.push({path: '/trf/previewCA2?id=' + this.$route.query.id + '&trfNo=' + this.$route.query.trfNo + '&signature=' + this.$route.query.signature})
                window.open(
                    "/#/trf/previewCA2?id=" +
                        this.$route.query.id +
                        "&trfNo=" +
                        this.$route.query.trfNo +
                        "&signature=" +
                        this.$route.query.signature,
                    "_blank",
                )
            }
        },
        loading() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            })
            setTimeout(() => {
                loading.close()
            }, 2500)
        },
        queryReportLanguage() {
            getReportLanguageArry(this.language, this.trf.productLineId).then(
                (res) => {
                    const data = res.data
                    if (data.length > 0) {
                        this.reportLanguageData = data
                        this.$emit(
                            "reportLanguageData",
                            this.reportLanguageData,
                        )
                        //默认设置为英文报告语言
                        if (
                            validatenull(this.trf.servicRequire.reportLanguage)
                        ) {
                            this.$set(
                                this.trf.servicRequire,
                                "reportLanguage",
                                "1",
                            )
                            let obj = {}
                            obj = this.reportLanguageData.find((item) => {
                                return item.sysKey === 1 || item.sysKey === "1"
                            })
                            if (!objectIsNull(obj)) {
                                this.$set(
                                    this.trf.servicRequire,
                                    "reportLanguageName",
                                    obj.sysValue,
                                )
                            }
                        }
                    }
                },
            )
        },
        queryCustomer(bossNo) {
            this.$set(this.customerParam, "number", bossNo)
            var params = {}
            searchCustomer(Object.assign(params, this.customerParam)).then(
                (res) => {
                    const data = res.data.data
                    if (data.length > 0) {
                        if (
                            this.trf.servicRequire.reportHeader == "" ||
                            this.trf.servicRequire.reportHeader == null
                        ) {
                            this.$set(
                                this.trf.servicRequire,
                                "reportHeader",
                                data[0].nameCN,
                            )
                        }
                        if (
                            this.trf.servicRequire.reportHeaderEn == "" ||
                            this.trf.servicRequire.reportHeaderEn == null
                        ) {
                            this.$set(
                                this.trf.servicRequire,
                                "reportHeaderEn",
                                data[0].nameEN,
                            )
                        }
                        if (this.language == "zh-CN") {
                            if (
                                this.trf.servicRequire.reportHeader == "" ||
                                this.trf.servicRequire.reportHeader == null
                            ) {
                                this.$set(
                                    this.trf.servicRequire,
                                    "reportHeader",
                                    data[0].nameCN,
                                )
                            }
                        }
                    }
                },
            )
        },

        //返回列表
        toTrfList() {
            this.$router.push({path: "/trf/newList", query: {}})
        },
        //****************dff Form method Start***************
        /* getLanguageObjList(language) {
                var languageStr = 'EN';
                if (language == 2) {
                    languageStr = 'CN';
                }
                this.$set(this.dffFormObjNew, 'dffFormData', this.dffLanguageObj);

            },*/
        //****************dff Form method End***************

        //****************dff Grid method Start***************
        /*getLanguageGridObjList(language) {
                var languageStr = 'EN';
                var data = this.dataList[languageStr];
                if (language == 2) {
                    languageStr = 'CN';
                    data = this.dataList[languageStr];
                }
                if (validatenull(data)) {
                    data = this.defaultDataList_new[languageStr];
                }
              this.$set(this.dffFormObjNew, 'dffGridData', this.dataList);
            },*/
        //**********dff Grid method End***************
        treeCheckChange(data, checked, indeterminate) {
            let list = this.$refs.selTree.getCheckedNodes()
            let halfNodes = this.$refs.selTree.getHalfCheckedNodes()

            if (data.children.length <= 0) data.selected = checked

            this.newTreeData = JSON.parse(
                JSON.stringify(this.testPackageTreedata),
            )
            for (let i = 0; i < this.newTreeData.length; i++) {
                this.newTreeData[i].selected = false
                for (let j = 0; j < halfNodes.length; j++) {
                    if (halfNodes[j].id == this.newTreeData[i].id)
                        this.newTreeData[i].selected = true
                }
                for (let k = 0; k < list.length; k++) {
                    if (list[k].id == this.newTreeData[i].id)
                        this.newTreeData[i].selected = true
                }
                if (
                    this.newTreeData[i].children &&
                    this.newTreeData[i].children.length > 0
                ) {
                    this.deepEach(this.newTreeData[i].children)
                }
            }
            if (data.children.length <= 0)
                this.setParentSelected(this.newTreeData, data.parentId, checked)
            if (list.length <= 0) this.newTreeData = []
            setTimeout(() => {
                this.setTestPackageTreeStyleDialog()
            }, 50)
        },
        deepEach(arr) {
            arr.forEach((item) => {
                item.selected = false
                let list = this.$refs.selTree.getCheckedNodes()
                let nodes = this.$refs.selTree.getHalfCheckedNodes()

                nodes.forEach((subItem) => {
                    if (subItem.id == item.id) item.selected = true
                })
                list.forEach((checkItem) => {
                    if (checkItem.id == item.id) item.selected = true
                })

                if (item.children && item.children.length > 0) {
                    item.selected = false
                    nodes.forEach((subItem) => {
                        if (subItem.id == item.id) item.selected = true
                    })
                    list.forEach((checkItem) => {
                        if (checkItem.id == item.id) item.selected = true
                    })
                    this.deepEach(item.children)
                }
            })
        },
        setTestPackageTreeStyleDialog() {
            let treeNode = document.querySelectorAll(".hide-line-dialog")
            let treeNodeShow = document.querySelectorAll(".show-line-dialog")
            treeNode.forEach((item) => {
                item.parentNode.parentNode.parentNode.style.visibility =
                    "collapse"
            })
            treeNodeShow.forEach((item) => {
                item.parentNode.parentNode.parentNode.style.visibility =
                    "visible"
            })
            let root = document.querySelector(".tree-root-dialog")
            root.parentNode.parentNode.parentNode.style.visibility = "visible"
        },
        handleSelectTree(list, selectNodes) {
            let that = this
            let subArr = []
            const hasChildrenAttr = function (itemObj) {
                return itemObj.children !== undefined
            }
            for (let i = 0; i < list.length; i++) {
                if (list[i].children.length > 0) {
                    list[i].children = []
                    selectNodes.forEach((item) => {
                        if (item.parentId == list[i].id) {
                            list[i].children.push(item)
                            subArr.push(list[i])
                        }
                    })
                    let subArr2 = this.uniqueFunc(subArr, "id") // 去重
                    that.newTreeData[0].children = subArr2
                }
                if (hasChildrenAttr(list[i])) {
                    that.handleSelectTree(list[i].children)
                }
            }
        },
        // 对象数组去重
        uniqueFunc(arr, uniId) {
            const res = new Map()
            return arr.filter(
                (item) => !res.has(item[uniId]) && res.set(item[uniId], 1),
            )
        },
        setParentSelected(lists, parentId, checked) {
            let that = this
            const hasChildrenAttr = function (obj) {
                return obj.children !== undefined
            }
            for (let i = 0; i < lists.length; i++) {
                if (lists[i].children) {
                    let every = lists[i].children.every(
                        (item) => item.selected == undefined || !item.selected,
                    )
                    if (lists[i].selected == undefined) {
                        if (every) {
                            lists[i].selected = false
                        } else {
                            lists[i].selected = true
                        }
                    }
                }
                if (hasChildrenAttr(lists[i])) {
                    that.setParentSelected(lists[i].children, parentId, checked)
                }
            }
        },
        handleTestPackageDel(index, id) {
            this.$confirm(this.$t("operation.confirmDelete"), this.$t("tip"), {
                confirmButtonText: this.$t("operation.confirm"),
                cancelButtonText: this.$t("operation.cancel"),
                type: "warning",
            }).then(() => {
                let treeSelectedIds = this.$refs.selTree.getCheckedKeys(true)
                let index = treeSelectedIds.findIndex((item) => item == id)
                treeSelectedIds.splice(index, 1)
                this.$refs.selTree.setCheckedKeys(treeSelectedIds)
                if (treeSelectedIds.length <= 0) {
                    this.$refs.selTree.setCheckedKeys([])
                }
                setTimeout(() => {
                    this.setTestPackageTreeStyle()
                }, 200)
            })
        },
        confirmTestPackage() {
            this.confirmFlag = true
            this.testPackageAddVisible = false
            this.treeSelectedIds = this.$refs.selTree.getCheckedKeys(true)
            this.setTestPackageTreeStyle()
            setTimeout(() => {
                this.confirmFlag = false
            }, 500)
        },
        handleOpen() {
            this.openTempTreeData = this.$refs.selTree.getCheckedKeys(true)
        },
        handleClose(e) {
            if (!this.confirmFlag) {
                // this.testPackageCheckedList = this.testPackageTableData
                this.$refs.selTree.setCheckedKeys(this.openTempTreeData)
                setTimeout(() => {
                    this.setTestPackageTreeStyle()
                }, 100)
            }
        },
        setTestPackageTreeStyle() {
            let treeNode = document.querySelectorAll(".hide-line")
            let treeNodeShow = document.querySelectorAll(".show-line")
            treeNode.forEach((item) => {
                item.parentNode.parentNode.parentNode.style.visibility =
                    "collapse"
            })
            treeNodeShow.forEach((item) => {
                item.parentNode.parentNode.parentNode.style.visibility =
                    "visible"
            })
            let root = document.querySelector(".tree-root")
            root.parentNode.parentNode.parentNode.style.visibility = "visible"
        },
        filterNode(value, data) {
            if (!value) return true
            return data.testPackageName.toLowerCase().indexOf(value) !== -1
        },

        //初始化页面需加载的数据
        initMethod(customerId) {
            //查询申请人数据
            this.customerId = customerId
            this.trf.trfCustomer.customerId = customerId
            this.customerContactParam.customerId_equal = customerId

            //查询发票信息
            // this.searchInvoice();
            //查询公司地址
            this.searchCustomerAddressData()
            //查询联系人
            this.searchCustomerContactData()
            //接口获取当前登录用户查询客户信息
            this.queryCustomer(this.userInfo.bossNo)
        },
        /*获取$route中的参数*/
        async getParams() {
            // 取到路由带过来的参数
            const trfFlag = this.$route.query.flag
            let routerParams = {
                trfId: this.$route.query.id,
                trfNo: this.$route.query.trfNo,
                signature: this.$route.query.signature,
            }
            routerParams = this.$route.query.id ? routerParams : undefined
            const actionType = this.$route.query.actionType
            const buCode = this.$route.query.bu
            this.productLineCode = buCode
            if (!this.checkObjectIsNull(this.productLineCode)) {
                this.$emit("productLineCode", this.productLineCode)
                this.$set(this.trf, "productLineCode", this.productLineCode)
            }
            this.trfFlag = trfFlag
            this.optionType = actionType
            //判断是否存在bossNo
            if (validatenull(this.userInfo.bossNo)) {
                //申请方可手动输入
                this.showCustomerNameFlag = false
            }
            if (actionType == "trfTemplate") {
                this.queryTrfTemplate(this.$route.query.trfTemplateId)
                this.showStep = 5
                return false
            } else if (actionType == "material") {
                //马丁鞋
                this.templateDisabled = true
                this.buyerDisabled = true
                //获取选择中的马丁鞋ids查询数据
                const materialSelIds = this.$route.query.ids
                const materialConfigId = this.$route.query.cid
                this.showStep = 5
                if (!validatenull(this.$route.query.customer)) {
                    this.materialCustomer = JSON.parse(
                        decodeURIComponent(this.$route.query.customer),
                    )
                }
                this.trfTemplateId = this.$route.query.trfTemplateId
                this.trfTemplateName = this.$route.query.trfTemplateName
                this.trf.trfType = TrfTypeEnums.MATERIAL.CODE

                if (!objectIsNull(materialSelIds)) {
                    this.trf.materialIds = materialSelIds
                    await this.onLoadMaterialData(
                        materialSelIds,
                        materialConfigId,
                    )
                    this.$emit("trfDetail", this.trf)
                }
            } else if (
                ["individualSample", "groupSample"].includes(actionType)
            ) {
                //console.log("新的material")
                //最新的material
                //获取选择中的马丁鞋ids查询数据
                const testId = this.$route.query.testId;
                const sourceType = this.$route.query.sourceType;
                this.buyerDisabled = true
                this.showStep = 5
                this.loadCustomerFlag = true
                if (!validatenull(this.$route.query.customer)) {
                    this.materialCustomer = JSON.parse(
                        decodeURIComponent(this.$route.query.customer),
                    )
                }
                if (!objectIsNull(testId)) {
                    let {bossNo, customerGroupCode} = this.materialCustomer
                    if (!bossNo) {
                        bossNo = this.userInfo.bossNo
                    }
                    console.log(">>>>>新的material 赋值buyerNo",customerGroupCode || "general")
                    this.$set(
                        this.trf,
                        "buyerNo",
                        customerGroupCode || "general",
                    )
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupCode",
                        customerGroupCode || "General",
                    )
                    this.$set(
                        this.templateDataParam,
                        "customerGroupCode",
                        customerGroupCode || "General",
                    )
                    //触发buyer change事件
                    this.selectBuyerCustomerGroupChange(
                        customerGroupCode || "general",
                    )
                    this.testId = testId;
                    this.sourceType = sourceType;
                    //弹窗 选择模板信息
                }
            }
            var title = this.$t("route.trf")
            if (!objectIsNull(this.$route.query.title)) {
                title = this.$route.query.title
            }
            //document.title = title;
            if (
                actionType == "add" ||
                actionType == "" ||
                actionType == undefined
            ) {
                //触发buery客户 Change事件
                if (!validatenull(this.userInfo.companyId)) {
                    this.initMethod(this.userInfo.companyId)
                }
                this.selectBuyerCustomerGroupChange("General")
            }
            if (!objectIsNull(actionType)) {
                this.trf.actionType = actionType
            } else {
                this.trf.actionType = "add"
            }
            if (!this.permissionList.trfSaveBtn) {
                this.saveTrfBtnIsShow = false
            }
            if (!this.permissionList.trfSubmitBtn) {
                this.submitTrfBtnIsShow = false
            }
            if (!this.permissionList.saveAsTemplateBtn) {
                this.saveAsTemplateBtnIsShow = false
            }
            if (!this.permissionList.cancelBtn) {
                this.cancelTrfBtnIsShow = false
            }
            if (!this.permissionList.removeBtn) {
                this.removeTrfBtnIsShow = false
            }

            // 将数据放在当前组件的数据内
            this.trfId = routerParams ? routerParams.trfId : routerParams
            if (routerParams) {
                this.queryTrf(routerParams)
            } else {
                //新建
                if (this.$store.state.user.taskType != "") {
                    this.templateDialogVisible = false
                } else {
                    let menuFlag = this.validateTrfMenu()
                    let sgsRole = this.role.isSGS
                    // 用户首次进入相关页面显示功能引导
                    let list = JSON.parse(localStorage.getItem("loginUserList"))
                    let user = list.find(
                        (item) => item.userMgtId == this.userInfo.userMgtId,
                    )
                    let finish = user.fnList[2]["finish_createTrf"]

                    if (this.role.isBuyer) {
                        // buyer直接打开功能引导
                        if (!finish)
                            this.$store.commit("SET_TASK_TYPE", "createTrf")
                    } else {
                        let autoClose = JSON.parse(
                            localStorage.getItem("AUTO_OFF"),
                        )
                        if (
                            (!this.userInfo.guide || autoClose) &&
                            !finish &&
                            !sgsRole
                        ) {
                            this.$store.commit("SET_TASK_TYPE", "createTrf")
                        } else {
                            this.templateDialogVisible = true
                        }
                    }
                    if (finish) this.templateDialogVisible = true
                }
            }
        },
        //判断是否存在创建TRF菜单
        validateTrfMenu() {
            let result = false
            if (!validatenull(this.menu)) {
                let menuStr = JSON.stringify(this.menu)
                if (!validatenull(menuStr)) {
                    if (menuStr.indexOf("/ccl/trf/newTrf") != -1) {
                        result = true
                    }
                }
            }
            return result
        },
        async loadNewMaterialData() {
            return new Promise((resolve, reject) => {
                let type = "material"
                let templateId = this.trf.trfTemplateId

                let bossNo = ""
                if (!validatenull(this.materialCustomer)) {
                    //存在materialCustomer 标识为SGS建单，反之为客户建单
                    bossNo = this.materialCustomer.bossNo
                } else {
                    bossNo = this.userInfo.bossNo
                }
                if (!validatenull(bossNo)) {
                    //存在bossNo
                    //将公司名称置为禁止手动输入，走原来逻辑
                    this.queryCustomer(bossNo)
                    //SGS管理员也需要带出申请方联系人、地址等信息
                    if (!objectIsNull(this.userInfo.companyId)) {
                        this.initMethod(this.userInfo.companyId)
                    }
                } else {
                    //申请方可手动输入
                    this.showCustomerNameFlag = false
                }

                //查询接口获取数据
                let param = {
                    type,
                    testId:this.testId,
                    sourceType:this.sourceType
                }
                //第一个接口，获取material本身数据
                sampleToTrf(param)
                    .then((response) => {
                        if (
                            response.status == 200 &&
                            response.data &&
                            response.data.data
                        ) {
                            this.$set(this.trf, "sampleNos", [param.testId]);
                            let secondParam = {
                                templateId,
                                data: response.data.data,
                                action: "merge",
                            }
                            sampleConvertDff(secondParam)
                                .then((res) => {
                                    let succ =
                                        res.status == 200 &&
                                        res.data.code == 200 &&
                                        res.data.data &&
                                        res.data.data.length > 0
                                    if (succ) {
                                        let {
                                            dff,
                                            dffFormData,
                                            dffGridData,
                                            careLabel,
                                        } = res.data.data[0]
                                        this.loadCustomerFlag = true
                                        let obj = {}
                                        let objCN = {}
                                        console.log("dffFormData",dffFormData)
                                        Object.keys(dffFormData.eN).forEach(
                                            (key) => {
                                                let k = _.lowerFirst(key)
                                                let val = dffFormData.eN[key];
                                                try{
                                                    val = JSON.parse(dffFormData.eN[key]).join(",");
                                                }catch(e){

                                                }
                                                obj[k] = val;
                                            },
                                        )
                                        Object.keys(dffFormData.cN).forEach(
                                            (key) => {
                                                let k = _.lowerFirst(key)
                                                let val = dffFormData.eN[key];
                                                try{
                                                    val = JSON.parse(dffFormData.eN[key]).join(",");
                                                }catch(e){

                                                }
                                                objCN[k] = val;
                                            },
                                        )
                                        this.dffLanguageObj.EN = obj
                                        this.dffLanguageObj.CN = objCN //dffFormData.eN;
                                        this.dataList.CN = dffGridData.cN
                                        this.dataList.EN = dffGridData.eN
                                        //基础信息填充 supplier manufacter lab
                                        let {
                                            serviceType,
                                            materialStatus,
                                            productLineCode,
                                            materialNo,
                                            manufacturerName,
                                            manufacturerContact,
                                            manufacturerAddress,
                                            labName,
                                            labCode,
                                            isBom,
                                            supplierCode,
                                            templateId,
                                            uniqueSignature,
                                            customerBossNo,
                                            isDeleted,
                                            id,
                                            customerGroupCode,
                                            labContact,
                                            supplierName,
                                            supplierAddress,
                                            manufacturerTel,
                                            supplierEmail,
                                            materialStatusName,
                                            isTrf,
                                            manufacturerEmail,
                                            updateUser,
                                            updateTime,
                                            materialCode,
                                            manufacturerCode,
                                            templateGroupId,
                                            materialCategory,
                                            materialDescription,
                                            materialName,
                                            templateName,
                                            createTime,
                                            createUser,
                                            supplierTel,
                                            supplierContact,
                                            status,
                                        } = dff

                                        this.newMatrilaObj = {
                                            serviceType,
                                            productLineCode,
                                            labName,
                                            labCode,
                                            labContact,
                                        }

                                        let trfTemplateLabCode = (
                                            this.templateLabData || []
                                        ).find((l) => l.labCode == labCode)
                                        if (
                                            labCode &&
                                            (!trfTemplateLabCode ||
                                                !trfTemplateLabCode.labCode)
                                        ) {
                                            resolve(false)
                                            this.$notify({
                                                title: this.$t("tip"),
                                                message:
                                                    "Trf template labcode not match material template labcode!",
                                                type: "warning",
                                            })
                                            return
                                        }
                                        if (labCode) {
                                            this.trf.trfLab.labCode = labCode
                                            this.selectLabChange(labCode)
                                        }

                                        let serviceData = (
                                            this.serviceTypeData || []
                                        ).find(
                                            (s) =>
                                                s.serviceTypeCode ==
                                                serviceType,
                                        )
                                        if (
                                            serviceData &&
                                            serviceData.serviceTypeCode
                                        ) {
                                            this.trf.serviceType = serviceType
                                            this.serviceTypeChange(serviceType)
                                        }
                                        this.$refs.applicantRef.appendSupplierAndManufactureCustomer(
                                            dff || {},
                                        )

                                        //dff data 赋值
                                        this.$set(
                                            this.dffFormObjNew,
                                            "dffFormData",
                                            this.dffLanguageObj,
                                        )
                                        this.$set(
                                            this.dffFormObjNew,
                                            "dffGridData",
                                            this.dataList,
                                        )
                                        console.log(
                                            "samples   carelabel",
                                            careLabel,
                                        )
                                        if (careLabel && careLabel.length > 0) {
                                            this.sampleHasCarelabel = true
                                            this.$set(
                                                this.dffFormObjNew,
                                                "careLabelData",
                                                careLabel,
                                            )
                                        }
                                    }
                                    resolve(succ)
                                })
                                .catch((errr) => {
                                    console.log(">>>>>接口异常11111", errr)
                                    resolve(false)
                                })
                        } else {
                            resolve(false)
                        }
                    })
                    .catch((err) => {
                        console.log(">>>>>接口异常", err)
                        resolve(false)
                    })
            })
        },
        //查询马丁鞋物料数据Method
        async onLoadMaterialData(ids, cid) {
            this.loadCustomerFlag = false
            queryMaterial(ids, cid).then(
                (res) => {
                    var dffData = res.data.data.dff
                    this.loadCustomerFlag = true
                    if (!objectIsNull(dffData)) {
                        //先默认赋值
                        this.$set(
                            this.trf,
                            "buyerNo",
                            dffData.customerGroupCode,
                        )
                        this.$set(
                            this.trf.trfCustomer,
                            "buyerCustomerGroupCode",
                            dffData.customerGroupCode,
                        )
                        this.$set(
                            this.trf.trfCustomer,
                            "buyerCustomerGroupName",
                            dffData.customerGroupName,
                        )
                        //查询buyer数据后再重新赋值
                        var agentParams = {}
                        this.customerGroupParam.groupCode =
                            dffData.customerGroupCode
                        getCustomerGroupByParms(
                            Object.assign(agentParams, this.customerGroupParam),
                        ).then((res) => {
                            const data = res.data.data
                            this.buyerCustomerGroupData = data
                            let obj = {}
                            obj = this.buyerCustomerGroupData.find((item) => {
                                return (
                                    item.customerGroupCode ===
                                    dffData.customerGroupCode
                                )
                            })
                            if (!objectIsNull(obj)) {
                                this.$set(
                                    this.trf.trfCustomer,
                                    "buyerCustomerGroupId",
                                    obj.customerGroupId,
                                )
                                this.$set(
                                    this.trf.trfCustomer,
                                    "buyerCustomerGroupName",
                                    obj.customerGroupName,
                                )
                            }
                        })
                        this.$set(
                            this.trf.trfCustomer,
                            "buyerCustomerGroupCode",
                            dffData.customerGroupCode,
                        )
                        //查询模板数据
                        this.$set(
                            this.templateDataParam,
                            "customerGroupCode",
                            dffData.customerGroupCode,
                        )
                        //初始化查询默认设置为1，只有历史申请单才查询被删除的template
                        this.templateDataParam.trfStatus = "1"
                        this.$nextTick(() => {
                            this.$refs.applicantRef.searchBuyerCustomerInfo(
                                null,
                                0,
                            )
                        })
                    }
                    var dffFormData = res.data.data.dffFormData
                    var dffGridData = res.data.data.dffGridData
                    var bossNo = ""
                    var customerName = ""
                    if (!validatenull(this.materialCustomer)) {
                        //存在materialCustomer 标识为SGS建单，反之为客户建单
                        bossNo = this.materialCustomer.bossNo
                        customerName = this.materialCustomer.customerName
                    } else {
                        bossNo = this.userInfo.bossNo
                    }
                    // var bossNo = res.data.data.bossNo;
                    var templateId = this.trfTemplateId
                    this.$set(this.templateDataParam, "id", templateId)
                    this.$set(this.trf, "trfTemplateId", templateId)
                    this.$set(this.templateDataParam, "trfStatus", 2) //material只查询一个
                    this.$set(this.templateDataParam, "isLoadGeneral", 0) //不加载General

                    if (!validatenull(bossNo)) {
                        //存在bossNo
                        //将公司名称置为禁止手动输入，走原来逻辑
                        this.queryCustomer(bossNo)
                        //SGS管理员也需要带出申请方联系人、地址等信息
                        if (!objectIsNull(this.userInfo.companyId)) {
                            this.initMethod(this.userInfo.companyId)
                        }
                    } else {
                        //申请方可手动输入
                        this.showCustomerNameFlag = false
                        if (!validatenull(customerName)) {
                            this.$set(
                                this.trf.trfCustomer,
                                "customerNameEn",
                                customerName,
                            )
                        }
                    }
                    this.dffLanguageObj.EN = dffFormData.eN
                    this.dffLanguageObj.CN = dffFormData.eN
                    this.dataList.CN = dffGridData.eN
                    this.dataList.EN = dffGridData.eN
                    // this.getLanguageObjList(1);
                    this.$set(
                        this.dffFormObjNew,
                        "dffFormData",
                        this.dffLanguageObj,
                    )
                    this.$set(this.dffFormObjNew, "dffGridData", this.dataList)
                    this.searchTemplateData()
                },
                (error) => {
                    this.$message.error(this.$t("api.error"))
                },
            )
        },
        //查询TRF模板
        queryTrfTemplate(trfTemplateId) {
            this.loadCustomerFlag = false
            trfTemplateDetail(trfTemplateId).then(
                (res) => {
                    var data = res.data.data
                    if (!objectIsNull(data)) {
                        if (!objectIsNull(data.trfTemplateData)) {
                            this.trf = JSON.parse(data.trfTemplateData)
                            if (
                                this.trf.efilingList &&
                                this.trf.efilingList.length
                            ) {
                                this.navList.find(
                                    (item) => item.name == "eFiling",
                                ).hide = false
                                const efilingFormDataJSON =
                                    this.trf.efilingList[0].efilingFormData
                                this.isEfillingFlag = true
                                const {formModel, productInfoList} =
                                    JSON.parse(efilingFormDataJSON)

                                this.formModel = formModel

                                this.formModel.id = null

                                productInfoList.forEach((item) => {
                                    item.referenceNo = this.getNewTrfNo()
                                })
                                this.productInfoList = productInfoList
                                console.log("商朝", this.productInfoList)
                                this.showStep = 6
                            }
                            //SMART-4037 如果选择的Group则赋值新的GroupName
                            this.supplierSameAsApplicant(this.trf.customerList);
                            this.reloadCustomerGroupInfo()
                            this.trf.trfType = TrfTypeEnums.USER_TEMPLATE.CODE;
                            this.trf.trfSourceType =
                                TrfSourceEnums.ONLINE_TRF.NAME;
                            this.trf.trfSourceId = trfTemplateId
                            this.$set(this.trf, "trfStatus", 0)
                            this.$set(this.trf, "actionType", "add")
                            this.loadCustomerFlag = true
                            this.$emit("trfId", this.trf.id)
                            if (this.$refs.trfFile) {
                                this.$refs.trfFile.initData(
                                    this.trf.trfAttachments,
                                )
                            }
                            this.displayTrf()
                            //回显附件
                            setTimeout(() => {
                                this.addSelectListen()
                                this.computedProgress()
                                this.firstLoad = false
                            }, 800)
                        }
                    }
                },
                (error) => {
                    this.$message.error(this.$t("api.error"))
                },
            )
        },
        queryTrf(params) {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            })
            this.loadCustomerFlag = false
            const actionType = this.$route.query.actionType
            let ajaxSendRes = null
            if (actionType == "copy") {
                ajaxSendRes = detailForCopy(params)
            } else {
                ajaxSendRes = queryTrfDetail(params)
            }
            ajaxSendRes.then(
                (res) => {
                    loading.close()
                    this.isShow = true
                    const data = res.data.data
                    this.trf = data;
                    this.trfDataLoadStatus = true
                    //回显动态表单 以formId为准
                    let {formType, formDesignId,customerList} = data
                    this.supplierSameAsApplicant(customerList);
                    this.loadCustomerFlag = true; //重新加载Customer
                    //判断customerList中 applicant 和 supplier是否一致
                    this.queryDynamicFormId = formDesignId
                    //查询report Language
                    this.queryReportLanguage()
                    //修改navlist的显示
                    this.showDynamicNavList(formType - 2 == 0)

                    this.$emit("productLineCode", this.trf.productLineCode)
                    if (
                        this.checkObjectIsNull(
                            this.trf.trfCustomer.customerNameEn,
                        )
                    ) {
                        this.$set(
                            this.trf.trfCustomer,
                            "customerNameEn",
                            this.trf.trfCustomer.customerNameZh,
                        )
                    }
                    if (
                        this.checkObjectIsNull(
                            this.trf.trfCustomer.customerNameZh,
                        )
                    ) {
                        this.$set(
                            this.trf.trfCustomer,
                            "customerNameZh",
                            this.trf.trfCustomer.customerNameEn,
                        )
                    }
                    if (this.trf.efilingList && this.trf.efilingList.length) {
                        this.navList.find(
                            (item) => item.name == "eFiling",
                        ).hide = false
                        const efilingFormDataJSON =
                            this.trf.efilingList[0].efilingFormData
                        this.isEfillingFlag = true
                        const {formModel, productInfoList} =
                            JSON.parse(efilingFormDataJSON)

                        this.formModel = formModel
                        if (
                            actionType == "copy" ||
                            actionType == "trfTemplate"
                        ) {
                            this.formModel.id = null
                            productInfoList.forEach((item) => {
                                item.referenceNo = this.getNewTrfNo()
                            })
                        } else {
                            this.formModel.id = this.trf.efilingList[0].id
                        }
                        this.productInfoList = productInfoList

                        console.log("shangchao")
                        console.log(this.formModel)
                        console.log(this.productInfoList)
                    }
                    this.saveTrfBtnIsShow = true
                    this.submitTrfBtnIsShow = true
                    this.saveAsTemplateBtnIsShow = true
                    this.cancelTrfBtnIsShow = false
                    this.removeTrfBtnIsShow = false
                    if (this.trf.trfStatus > 0) this.showStep = 6
                    if (this.optionType == "copy") {
                        this.trf.extendOrderNo = ""
                        this.$set(this.trf, "trfStatus", 1)
                    }

                    if (
                        !this.permissionList.trfSaveBtn ||
                        this.trf.trfStatus >= 2
                    ) {
                        this.saveTrfBtnIsShow = false
                    }
                    if (
                        !this.permissionList.trfSubmitBtn ||
                        this.trf.trfStatus >= 2
                    ) {
                        this.submitTrfBtnIsShow = false
                    }
                    if (!this.permissionList.saveAsTemplateBtn) {
                        this.saveAsTemplateBtnIsShow = false
                    }

                    //取消按钮控制  1、有该权限，提交状态下当前用户可以操作自己的 2、已提交 < 进行中 sgs可操作 >2 && <5
                    //permissionList.cancelBtn && trf.trfStatus<=4 && trf.trfStatus>1 && userInfo.userMgtId == trf.applicantUser
                    if (this.permissionList.cancelBtn) {
                        if (this.trf.trfStatus == 2) {
                            if (
                                this.trf.createUserId == this.userInfo.userMgtId
                            ) {
                                this.cancelTrfBtnIsShow = true
                            }
                        }
                    }
                    //删除按钮控制
                    if (this.permissionList.removeBtn) {
                        if (
                            data.trfStatus == 1 &&
                            !validatenull(data.id) &&
                            this.optionType != "copy"
                        ) {
                            if (
                                this.trf.createUserId == this.userInfo.userMgtId
                            ) {
                                this.removeTrfBtnIsShow = true
                            }
                        }
                    }
                    if (this.optionType != "add") {
                        if (
                            this.trf.createUserId != this.userInfo.userMgtId ||
                            this.trf.trfSourceType === "ORDER_TO_TRF" ||
                            this.trf.trfSourceType === "CUSTOMER_TRF"
                        ) {
                            this.saveTrfBtnIsShow = false
                            this.submitTrfBtnIsShow = false
                            this.saveAsTemplateBtnIsShow = false
                        }
                    }
                    // 回传父类
                    this.trfNo = this.trf.trfNo
                    this.trfStatus = this.trf.trfStatus
                    this.$emit("update:trfNo", this.trfNo)
                    this.$emit("update:trfStatus", this.trfStatus)

                    this.trf.actionType = this.optionType
                    if (this.optionType == "copy" || this.optionType == "add") {
                        this.trf.trfStatus = 1
                        if (this.optionType == "copy") {
                            this.trf.trfSourceType =
                                TrfSourceEnums.ONLINE_TRF.NAME
                            this.trf.trfType = TrfTypeEnums.COPY.CODE
                            this.trf.trfSourceId = this.trfId
                        }
                    }
                    if (this.trf.trfStatus <= 1) {
                        //重新加载最新GroupName
                        this.reloadCustomerGroupInfo()
                        //copy 重新获取公司数据
                        this.queryCustomer(this.userInfo.bossNo)
                    }
                    //将trfId放入页面传值
                    this.$emit("trfId", this.trf.id)
                    this.$emit("trf", this.trf)
                    //查询communicationnLog
                    if (this.trf.trfStatus > 1) {
                        this.communicationParam.trfId = this.trf.id
                        this.communicationParam.trfType =
                            TrfTypeEnums.DEFAULT_TEMPLATE.CODE
                        this.queryCommunicationLog()
                    }
                    this.btnSubmitLoading = true
                    this.fullPageLoading = true
                    this.displayTrf()
                    this.$nextTick(() => {
                        //查询Buyer客户
                        if (
                            this.$refs.applicantRef &&
                            this.trf.trfStatus <= 1
                        ) {
                            this.$refs.applicantRef.searchBuyerCustomerInfo(
                                this.trf.trfCustomer.buyerCustomerNo,
                                1,
                            )
                        }
                    })
                    setTimeout(() => {
                        this.btnSubmitLoading = false
                        this.fullPageLoading = false
                    }, 2000)
                    // 进度计算
                    setTimeout(() => {
                        this.addSelectListen()
                        this.computedProgress()
                        this.firstLoad = false
                    }, 1500)
                },
                (error) => {
                    loading.close()
                    this.$message.error(error || this.$t("api.error"))
                },
            )
        },
        supplierSameAsApplicant(customerList){
            let applicantCustomer = customerList.find(c=>c.customerUsage==1);
            let supplierCustomer = customerList.find(c=>c.customerUsage==5);
            if(!applicantCustomer || !supplierCustomer){
                this.supplierSameAsApplicantFlag = false;
                return;
            }
            let applicantColumnStr = this.calcCustomerStr(applicantCustomer);
            let supplierCustomerStr = this.calcCustomerStr(supplierCustomer);
            this.supplierSameAsApplicantFlag = applicantColumnStr==supplierCustomerStr;
        },
        calcCustomerStr(customer){
            let { customerUsage,  customerId,  bossNo, customerReferenceNo,
                customerGroupCode,  customerGroupId,  customerGroupName, customerName,
                 customerAddress,  customerAccountId,  marketSegmentName,
                marketSegmentCode,  languageList,  customerContactList
            } = customer;
            let {contactEmail,contactName,contactTelephone} = customerContactList[0];

            let nameAndAddressEN = languageList.find(lan=>lan.languageId==1) || {};
            let nameAndAddressCN = languageList.find(lan=>lan.languageId==2) || {};
            let customerNameEN = nameAndAddressEN.customerName || '';
            let customerAddressEN = nameAndAddressEN.customerAddress || '';
            let customerNameCN = nameAndAddressCN.customerName || '';
            let customerAddressCN = nameAndAddressCN.customerAddress || '';

            let str =(customerId || '') + (bossNo || '')+customerNameEN+customerNameCN
                +customerAddressEN+customerAddressCN+(customerReferenceNo||'')
                +(contactEmail || '')+(contactName|| '')+(contactTelephone|| '');
            return str;

        },
        displayTrf() {
            let isSgs = false
            if (
                !validatenull(this.userInfo.dimensions) &&
                !validatenull(this.userInfo.dimensions.SGSUserRole)
            ) {
                let role = this.userInfo.dimensions.SGSUserRole
                role.forEach((item) => {
                    if (item === "SgsAdmin" || item === "SgsLabUser") {
                        isSgs = true
                    }
                })
            }
            // if (!isSgs) {//SGS管理员才可展示取消按钮
            //     this.queryCustomerProductLineCode();
            // }

            if (this.trf.trfStatus > 1) {
                //是否展示general request
                if (this.trf.isGeneralRequest == 1) {
                    this.generalRequestFlag = true
                } else {
                    this.generalRequestFlag = false
                }
                this.showCustomerNameFlag = true
                this.trfDisabled = true
                this.templateDisabled = true
                this.buyerDisabled = true
                const inputs = this.$refs["trf"].$el.querySelectorAll("input")
                ;[...inputs].forEach((elm) => {
                    elm.classList.remove("activeTrf")
                    elm.classList.add("disabledTrf")
                })
                const textAreas =
                    this.$refs["trf"].$el.querySelectorAll("textarea")
                ;[...textAreas].forEach((elm) => {
                    elm.classList.remove("activeTrf")
                    elm.classList.add("disabledTrf")
                })
            }

            if (this.trf.servicRequire.reportLanguage == 1) {
                this.languageID = 1
            } else if (this.trf.servicRequire.reportLanguage == 2) {
                this.languageID = 2
            } else {
                this.languageID = 1
            }

            //重新加载客户名称
            this.$set(
                this.trf,
                "buyerNo",
                this.trf.trfCustomer.agentCustomerNameEn,
            )
            if (this.trf.trfStatus <= 1) {
                //SMART-3496 判断当前客户组信息中是否保存Group信息
                let customerList = this.trf.customerList
                if (!objectIsNull(customerList)) {
                    let buyerCustomerData = customerList.find((value) => {
                        return (
                            value.customerUsage ==
                            this.CustomerUsageEnums.BUYER.code
                        )
                    })
                    if (
                        !objectIsNull(buyerCustomerData) &&
                        !objectIsNull(buyerCustomerData.customerGroupCode)
                    ) {
                        //判断现有buyerGrup数据中是否存在对应Group
                        let isBuyerGroupCustomerData =
                            this.buyerCustomerGroupData.find((value) => {
                                return (
                                    value.customerGroupCode ==
                                    buyerCustomerData.customerGroupCode
                                )
                            })
                        if (objectIsNull(isBuyerGroupCustomerData)) {
                            //不存在 则新增一条
                            let groupObj = {}
                            groupObj.customerType = "group"
                            groupObj.customerGroupId =
                                buyerCustomerData.customerGroupId
                            groupObj.customerGroupCode =
                                buyerCustomerData.customerGroupCode
                            groupObj.customerGroupName =
                                buyerCustomerData.customerGroupName
                            this.buyerCustomerGroupData.push(groupObj)
                        }
                    }
                }

                if (!validatenull(this.userInfo.bossNo)) {
                    this.queryCustomer(this.userInfo.bossNo)
                }
            }
            if (this.trf.trfStatus >= 1 && this.trf.actionType != "copy") {
                this.isShowSubmitBtn = false
                this.trfShowData = []
                var trfTable = {}
                trfTable.trfNo = this.trf.trfNo
                trfTable.createTime = this.trf.createTime
                trfTable.trfSubmissionDate = this.trf.trfSubmissionDate
                trfTable.trfStatus = this.trf.trfStatus
                if (this.trf.trfStatus == 11 || this.trf.trfStatus == 12) {
                    this.isShowRemark = true
                    trfTable.pendingReason =
                        "Pending Reason: " + this.trf.pendingReason
                }
                this.trfShowData.push(trfTable)
            }
            if (
                this.trf.trfCustomer.isSame == 1 ||
                this.trf.servicRequire.isSame == "1"
            ) {
                this.identicalFlag = true
            } else {
                this.identicalFlag = false
            }
            if (
                this.trf.isSelfRefrence == 1 ||
                this.trf.isSelfRefrence == "1"
            ) {
                this.isSelfRefrenceFlag = true
            } else {
                this.isSelfRefrenceFlag = false
            }
            //回显地址
            if (!validatenull(this.trf.trfCustomer.customerId)) {
                var customerAddressParams = {}
                this.customerContactParam.customerId_equal =
                    this.trf.trfCustomer.customerId
                if (this.trf.trfCustomer.customerId == -1) {
                    this.trf.trfCustomer.customerId = ""
                }
                getCustomerAddressList(
                    Object.assign(
                        customerAddressParams,
                        this.customerContactParam,
                    ),
                ).then((res) => {
                    const data = res.data.data
                    this.customerAddressData = data
                    //this.processCustomerAddress();
                })
                //回显customerContact
                this.customerContactData.customerId =
                    this.trf.trfCustomer.customerId
                if (
                    this.trf.trfCustomer.customerId == -1 ||
                    this.trf.trfCustomer.customerId == undefined
                ) {
                    this.customerContactData.customerId = ""
                }
                var customerContactparams = {}
                getCustomerContactList(
                    Object.assign(
                        customerContactparams,
                        this.customerContactParam,
                    ),
                ).then((res) => {
                    const data = res.data.data
                    this.customerContactData = data
                    //this.processCustomerContact();
                })
            } //回显联系人数据
            else if (!validatenull(this.userInfo.companyId)) {
                this.customerContactParam.customerId_equal =
                    this.userInfo.companyId
                var addresParams = {}
                getCustomerAddressList(
                    Object.assign(addresParams, this.customerContactParam),
                ).then((res) => {
                    const data = res.data.data
                    this.customerAddressData = data
                    //this.processCustomerAddress();
                })
                var contactParams = {}
                getCustomerContactList(
                    Object.assign(contactParams, this.customerContactParam),
                ).then((res) => {
                    const data = res.data.data
                    this.customerContactData = data
                    //this.processCustomerContact();
                })
            }
            //回显agentGroup
            var agentParams = {}
            this.$set(
                this.customerGroupParam,
                "groupName",
                this.trf.trfCustomer.agentCustomerGroupName,
            )
            var agentParams = {}
            this.$set(
                this.agentCustomerParam,
                "customerName",
                this.trf.trfCustomer.agentCustomerNameEn,
            )
            this.$set(this.agentCustomerParam, "rows", 5)
            getAgentCustomerGroupByParms(
                Object.assign(agentParams, this.agentCustomerParam),
            ).then((res) => {
                const data = res.data.data
                this.agentCustomerGroupData = data
                this.$set(
                    this.trf,
                    "agentCustomerName",
                    this.trf.trfCustomer.agentCustomerNameEn,
                )
            })

            //回显template下拉数据
            // this.templateDataParam.customerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
            //draft状态只允许获取未删除，且状态正常的模板列表；传入模板ID；
            if (this.trf.actionType == "copy" || this.trf.actionType == "add") {
                this.templateDataParam.trfStatus = 1
                this.templateLabDataParam.trfStatus = 1
                this.templateLabContactDataParam.trfStatus = 1
            }
            if (this.trf.trfType == TrfTypeEnums.MATERIAL.CODE) {
                this.templateDisabled = true
                this.buyerDisabled = true
            }
            //2021-09-02修改，申请单回显或编辑可以去除BU限制
            this.$set(
                this.templateDataParam,
                "productLineCode",
                this.trf.productLineCode,
            )
            this.$set(
                this.templateDataParam,
                "trfStatus",
                this.trf.trfStatus == 0 ? 1 : this.trf.trfStatus,
            )
            this.$set(
                this.templateDataParam,
                "productLineCode",
                this.trf.productLineCode,
            )

            this.$set(this.templateDataParam, "id", this.trf.trfTemplateId)

            this.$set(
                this.templateLabDataParam,
                "trfStatus",
                this.trf.trfStatus,
            )
            this.$set(
                this.templateLabContactDataParam,
                "trfStatus",
                this.trf.trfStatus,
            )
            this.$set(
                this.templateLabDataParam,
                "labCode",
                this.trf.trfLab.labCode,
            )
            //验证已保存的TRF中的模板是否正常状态
            if (validatenull(this.trf.trfTemplateId)) {
                if (this.trf.trfStatus <= 1 && this.trf.actionType == "copy") {
                    this.$notify({
                        title: this.$t("tip"),
                        message: this.$t("trf.templateErrorInfo"),
                        type: "warning",
                    })
                    this.$set(this.trf, "trfLab", {})
                    this.$set(this.trf, "trfLabContact", {})
                    this.$set(this.trf, "trfTemplateId", "")
                    this.$set(this.trf, "templateName", "")
                    this.$set(this.trf, "serviceType", "")
                    return false
                }
            }
            this.$set(
                this.trfUserTemplate,
                "trfBuyerCustomerGroupCode",
                this.trf.trfCustomer.buyerCustomerGroupCode,
            )
            this.$set(
                this.trfUserTemplate,
                "trfBuyerCustomerGroupName",
                this.trf.trfCustomer.buyerCustomerGroupName,
            )
            // 2021-09-30新增逻辑->如果该draft状态申请单选择了buyer和agent，则需要重新PK并加载PK后的Template数据，反之无agent数据则加载buyer的Template数据
            if (this.templateDataParam.trfStatus <= 1) {
                let buyerCustomerGroupCode =
                    this.trf.trfCustomer.buyerCustomerGroupCode
                let buyerBossNo = this.trf.trfCustomer.buyerCustomerNo
                let buyerCustomer = this.getBuyerCustomerObj()
                if (!validatenull(buyerCustomer)) {
                    buyerCustomerGroupCode = buyerCustomer.customerGroupCode
                    buyerBossNo = buyerCustomer.bossNo
                }
                if (
                    !validatenull(buyerCustomerGroupCode) &&
                    !validatenull(
                        this.trf.trfCustomer.agentCustomerGroupCode,
                    ) &&
                    validatenull(this.trf.trfCustomer.buyerCustomerNo)
                ) {
                    this.$set(
                        this.buyerPkAgentParam,
                        "agentGroupCode",
                        this.trf.trfCustomer.agentCustomerGroupCode,
                    )
                    this.$set(
                        this.buyerPkAgentParam,
                        "buyerGroupCode",
                        buyerCustomerGroupCode,
                    )
                    var params = {}
                    buyerPkAgent(
                        Object.assign(params, this.buyerPkAgentParam),
                    ).then((res) => {
                        const data = res.data.data
                        //查询模板数据
                        this.$set(
                            this.templateDataParam,
                            "customerGroupCode",
                            data,
                        )
                        this.$set(this.templateDataParam, "trfStatus", "1")
                        this.displayTrfTemplate()
                    })
                } else {
                    this.$set(
                        this.templateDataParam,
                        "customerGroupCode",
                        buyerCustomerGroupCode,
                    )
                    if (validatenull(buyerCustomerGroupCode)) {
                        this.$set(
                            this.templateDataParam,
                            "customerBossNo",
                            buyerBossNo,
                        )
                        //this.$set(this.templateDataParam, 'customerGroupCode', this.trf.trfCustomer.buyerCustomerNo);
                    }
                    this.displayTrfTemplate()
                }
            } else {
                this.displayTrfTemplate()
            }

            //回显测试包数据
            if (this.trf.trfStatus < 2) {
                this.testPackageParam.templateId = this.trf.trfTemplateId
                this.testPackageParam.trfStatus = this.trf.trfStatus
                this.testPackageParam.trfId = this.trf.id
                this.treeLoading = true
                getTemplateTestPackageList(
                    Object.assign({parentId: -1}, this.testPackageParam),
                ).then((res) => {
                    this.treeLoading = false
                    this.testPackageTreedata = res.data.data
                    //模板数据需要单独处理
                    if (!validatenull(this.testPackageTreedata)) {
                        //判断testPackageList是否有remark；
                        if (!validatenull(this.trf.testPackageList)) {
                            this.trf.testPackageList =
                                this.trf.testPackageList.filter(function (
                                    item,
                                ) {
                                    return !validatenull(item.remark)
                                })
                            if (!validatenull(this.trf.testPackageList)) {
                                this.testPackageTreedata = this.setRemark(
                                    this.testPackageTreedata,
                                )
                            }
                        }
                    }
                    var ids = this.trf.testPackageIds
                    if (!validatenull(ids)) {
                        // this.$refs.selTree.setCheckedKeys(ids);
                        this.testPackShow(
                            this.testPackageTreedata[0].children,
                            ids,
                        )
                        // this.testPackageCheckedList = this.testPackageTableData
                        // 回显测试包
                        this.newTreeData = JSON.parse(
                            JSON.stringify(this.testPackageTreedata),
                        )

                        this.$nextTick(() => {
                            this.$refs.selTree.setCheckedKeys(ids)
                            setTimeout(() => {
                                this.setTestPackageTreeStyle()
                            }, 500)
                        })
                    }
                })
            } else {
                this.commponentKey += 1
                this.testPackageExpand = true
                this.testPackageTreedata = this.trf.testPackages
                this.newTreeData = JSON.parse(
                    JSON.stringify(this.trf.testPackages),
                )
            }

            this.trf.servicRequire.reportLanguage =
                this.trf.servicRequire.reportLanguage + ""
            if (this.trf.trfStatus == 2) {
                //自动触发撤销TRF操作
                if (this.$route.query.actionType == "edit") {
                    this.returnTrfStatusClick()
                }
            }
        },
        processCustomerContact() {
            if (this.optionType != "copy" && this.optionType != "trfTemplate") {
                return
            }
            if (!this.customerContactData) {
                this.applyContactSetNull()
            } else {
                let contactData = this.customerContactData.find((value) => {
                    return (
                        value.contactName ==
                        this.trf.trfCustomerContact.applyContactName
                    )
                })
                if (!contactData) {
                    this.applyContactSetNull()
                } else {
                    if (
                        contactData.contactTel !=
                        this.trf.trfCustomerContact.applyContactTel
                    ) {
                        this.trf.trfCustomerContact.applyContactTel = ""
                    }
                    if (
                        contactData.contactEmail !=
                        this.trf.trfCustomerContact.applyContactEmail
                    ) {
                        this.trf.trfCustomerContact.applyContactEmail = ""
                    }
                }
            }
        },
        processCustomerAddress() {
            if (this.optionType != "copy" && this.optionType != "trfTemplate") {
                return
            }
            let setAddressNull = false
            if (!this.customerAddressData) {
                setAddressNull = true
            } else {
                let addressData = this.customerAddressData.find((value) => {
                    return (
                        value.addressDetail ==
                            this.trf.trfCustomer.customerAddressEn ||
                        value.addressDetail ==
                            this.trf.trfCustomer.customerAddressCn
                    )
                })
                if (!addressData) {
                    setAddressNull = true
                }
            }
            if (setAddressNull) {
                this.trf.trfCustomer.customerAddressEn = ""
                this.trf.trfCustomer.customerAddressZh = ""
                this.trf.trfCustomer.customerAddressId = ""
            }
        },
        processTemplateLab() {
            if (this.trf.trfStatus != 1) {
                return
            }
            let setLabNull = false
            if (!this.templateLabData) {
                setLabNull = true
            } else {
                let templateLab = this.templateLabData.find((value) => {
                    return value.labCode == this.trf.trfLab.labCode
                })
                if (
                    !templateLab ||
                    (templateLab.labAddress != this.trf.trfLab.labAddress &&
                        templateLab.labAddressCn != this.trf.trfLab.labAddress)
                ) {
                    setLabNull = true
                }
            }
            if (setLabNull) {
                if (this.trf.trfLab && this.trf.trfLab.labCode) {
                    this.$notify({
                        title: this.$t("tip"),
                        message: this.$t("lab.labChanged"),
                        type: "warning",
                    })
                }
                this.trf.trfLab = {}
                this.trf.trfLabContact = {}
            }
        },

        processTemplateLabContact() {
            if (
                (!this.templateLabContactData ||
                    this.templateLabContactData.length == 0) &&
                !this.trf.trfLabContact.contactName
            ) {
                this.labContactFlag = false
                return false
            } else {
                this.templateLabContactData.forEach((item, index, array) => {
                    var contactEmail = item.contactEmail
                    if (!validatenull(contactEmail)) {
                        item.labContactName =
                            item.labContactName + " (" + contactEmail + ")"
                    }
                })
            }
            var labContactObj = {}
            labContactObj = this.templateLabContactData.find((item) => {
                return (
                    item.labContactName === this.trf.trfLabContact.contactName
                )
            })
            if (
                validatenull(labContactObj) &&
                this.trf.trfLabContact.contactName
            ) {
                if (this.trf.trfStatus == 1) {
                    this.$notify({
                        title: this.$t("tip"),
                        message: this.$t("lab.labChanged"),
                        type: "warning",
                    })
                    this.$set(this.trf, "trfLabContact", {})
                    this.$set(this.trf, "trfLab", {})
                    if (
                        !this.templateLabContactData ||
                        this.templateLabContactData.length == 0
                    ) {
                        this.labContactFlag = false
                    }
                    return
                } /*else if(this.trf && this.trf.trfLabContact && this.trf.trfLabContact.contactName ){
                  if(!this.trf.trfLabContact.labContactId){
                    this.trf.trfLabContact.labContactId = this.trf.trfLabContact.contactName
                  }
                  this.templateLabContactData.push({"labContactId":this.trf.trfLabContact.labContactId,"labContactName":this.trf.trfLabContact.contactName})
                }*/
            }
        },
        applyContactSetNull() {
            this.trf.trfCustomerContact.applyContactEmail = ""
            this.trf.trfCustomerContact.applyContactFax = ""
            this.trf.trfCustomerContact.applyContactName = ""
            this.trf.trfCustomerContact.applyContactTel = ""
        },
        // 回显测试包
        testPackShow(tree, ids) {
            for (let i = 0; i < tree.length; i++) {
                if (tree[i].children.length > 0) {
                    this.testPackShow(tree[i].children, ids)
                }
                ids.forEach((itemId) => {
                    if (itemId == tree[i].id) {
                        this.testPackageTableData.push(tree[i])
                    }
                })
                // break
            }
        },
        async displayTrfTemplate() {
            // 如果选择的buyer是当前登陆的客户，需要用客户的BossNo去查询模板
            if (
                this.userInfo.customerGroupCode ===
                this.trf.trfCustomer.buyerCustomerGroupCode
            ) {
                this.$set(
                    this.templateDataParam,
                    "customerBossNo",
                    this.userInfo.bossNo,
                )
            }
            //非Draft状态的可以获取已删除或状态置为禁用的模板列表；
            var params = {}
            await getTemplateList(
                Object.assign(params, this.templateDataParam),
            ).then(async (res) => {
                this.templateDataLoad = true
                const data = res.data.data
                this.templateData = data
                this.isDffFormLoadFlag = false
                this.isDffGridLoadFlag = false
                this.isCareLabelLoadFlag = false
                if (
                    this.trf.trfTemplateId == null ||
                    this.trf.trfTemplateId == -1
                ) {
                    this.trf.trfTemplateId = ""
                }
                var obj = {}
                obj = data.find((item) => {
                    return item.id === this.trf.trfTemplateId
                })
                this.templateChangeObj = obj
                console.log("displayTrfTemplate 赋值", this.templateChangeObj)
                this.$refs.applicantRef.initTemplateCustomerFieldConfig(obj)
                if (!objectIsNull(obj)) {
                    this.templateSpecificCode = obj.specificCode
                }

                let {formType, formDesignId, formDesignGroupId} = obj || {}
                if (formType == 2) {
                    //进行转换
                    let resObj = this.trf // await this.convertCode(2);
                    let {formDesignData} = resObj
                    this.dynamicFormObj = formDesignData
                    this.queryDynamicFormDesignGroupId = formDesignGroupId
                    this.showDynamicForm = true
                }
                //回显服务类型
                this.queryCurrentDeptBeLong(this.trf.trfTemplateId)
                if (this.trf.trfStatus <= 1 || this.trf.actionType == "copy") {
                    if (
                        obj == undefined ||
                        obj.isDeleted == 1 ||
                        obj.status != 1
                    ) {
                        this.templateDialogVisible = true
                        this.$notify({
                            title: this.$t("tip"),
                            message: this.$t("trf.templateErrorInfo"),
                            type: "warning",
                        })
                        this.$set(this.trf, "trfLab", {})
                        this.$set(this.trf, "trfLabContact", {})
                        this.$set(this.trf, "trfTemplateId", "")
                        this.$set(this.trf, "templateName", "")
                        this.$set(this.trf, "serviceType", "")
                        this.$set(this.trfUserTemplate, "trfName", "")
                        this.$set(this.trfUserTemplate, "trfTemplateId", null)
                        return false
                    }
                    if (!validatenull(obj)) {
                        this.$set(this.trf, "templateName", obj.templateName)
                    }

                    if (
                        obj &&
                        obj.specificCode.indexOf("general_additional") != -1
                    ) {
                        this.generalRequestFlag = true
                        this.trf.isGeneralRequest = 1
                    } else {
                        this.generalRequestFlag = false
                        this.trf.isGeneralRequest = 0
                    }
                    if (
                        obj &&
                        obj.specificCode.indexOf("additional_for_cp") != -1
                    ) {
                        this.trf.isCpRequest = 1
                    } else {
                        this.trf.isCpRequest = 0
                    }
                }
                //回显必填校验
                let productLineId = this.trf.productLineId
                if (obj) {
                    this.$set(this.trfUserTemplate, "trfName", obj.templateName)
                    this.$set(this.trfUserTemplate, "trfTemplateId", obj.id)
                    getFieldSettingsByTemplateId(obj.id).then((res) => {
                        if (res.data.code == 200) {
                            this.fieldSettingsData = res.data.data
                        }
                    })
                    this.$set(
                        this.templateLabDataParam,
                        "productLineCode",
                        obj.productLineCode,
                    )
                    productLineId = obj.productLineId
                }
                getServiceType(productLineId, this.language).then((res) => {
                    const data = res.data.data
                    this.serviceTypeData = data
                })

                //回显实验室以及实验室联系人
                var labParams = {}
                this.templateLabDataParam.trfTemplateId = this.trf.trfTemplateId

                this.$set(this.templateLabDataParam, "language", this.language)
                getTemplateLabList(
                    Object.assign(labParams, this.templateLabDataParam),
                ).then((res) => {
                    const data = res.data.data
                    this.templateLabData = data
                    this.processTemplateLab()
                    //SMART-2014 无论任何TRF 回显时均显示Lab中的lab Address
                    let templateLab = this.templateLabData.find((value) => {
                        return value.labCode == this.trf.trfLab.labCode
                    })
                    if (!objectIsNull(templateLab)) {
                        var labAddress = templateLab.labAddress
                        if (this.language == "zh-CN") {
                            if (!validatenull(templateLab.labAddressCn)) {
                                labAddress = templateLab.labAddressCn
                            }
                        }
                        this.$set(this.trf.trfLab, "labAddress", labAddress)
                    }
                })
                //查询实验室联系人
                /*this.templateLabContactDataParam.trfTemplateId = this.trf.trfTemplateId;
                    this.templateLabContactDataParam.labCode = '';*/
                this.$set(
                    this.templateLabContactDataParam,
                    "labCode",
                    this.trf.trfLab.labCode,
                )
                this.$set(
                    this.templateLabContactDataParam,
                    "trfTemplateId",
                    this.trf.trfTemplateId,
                )
                var labContactParams = {}
                let _this = this
                getTemplateLabContactList(
                    Object.assign(
                        labContactParams,
                        this.templateLabContactDataParam,
                    ),
                ).then((res) => {
                    const data = res.data.data
                    _this.templateLabContactData = data
                    _this.processTemplateLabContact()
                })
                //动态表单
                if (formType == 2) {
                    return
                }
                /* TIPS:
                       下面是DFF相关代码，所以在上面formType=2的时候，直接用了return
                     如果以后逻辑需要写在这两段中间，需要添加上if(xxx) 来区分formType=1/2的逻辑
                     */
                //处理加载语言问题
                this.setDffLanguageValue()
                //加载dff form组件  //20-05-09 DFF组件获取最新ID逻辑修改
                //copy和TRF 模板需要获取最新的dff Form Id
                if (
                    this.optionType == "copy" ||
                    this.optionType == "trfTemplate"
                ) {
                    if (
                        obj &&
                        obj.specificCode.indexOf("general_additional") != -1
                    ) {
                        this.generalRequestFlag = true
                        this.trf.isGeneralRequest = 1
                    } else {
                        this.generalRequestFlag = false
                        this.trf.isGeneralRequest = 0
                    }
                    if (
                        obj &&
                        obj.specificCode.indexOf("additional_for_cp") != -1
                    ) {
                        this.trf.isCpRequest = 1
                    } else {
                        this.trf.isCpRequest = 0
                    }
                    //copy & Trf Template DFF Form
                    if (!validatenull(this.trf.dffFormId)) {
                        //验证TRF的 DFF FormID和Template 的DFF Form Id 是否属于同一Group
                        var dffIDList = []
                        dffIDList.push(this.trf.dffFormId)
                        dffIDList.push(obj.dffFormId)
                        var checkDffGroupObj = {}
                        checkDffGroupObj["dffIDList"] = dffIDList
                        checkDffFormIdSmapleGroup(checkDffGroupObj).then(
                            (res) => {
                                const data = res.data.data
                                if (!data.sameFormGroup) {
                                    //不属于同一Group，清空Template 重新选择
                                    this.$notify({
                                        title: this.$t("tip"),
                                        message: this.$t(
                                            "trf.templateErrorInfo",
                                        ),
                                        type: "warning",
                                    })
                                    this.$set(this.trf, "trfLab", {})
                                    this.$set(this.trf, "trfLabContact", {})
                                    this.$set(this.trf, "trfTemplateId", "")
                                    this.$set(this.trf, "templateName", "")
                                    this.$set(this.trf, "serviceType", "")
                                    this.$set(
                                        this.trfUserTemplate,
                                        "trfName",
                                        "",
                                    )
                                    this.$set(
                                        this.trfUserTemplate,
                                        "trfTemplateId",
                                        null,
                                    )
                                    return false
                                }
                            },
                        )

                        var dffFormIds = []
                        dffFormIds.push(this.trf.dffFormId)
                        let comfirmData = await this.getDffComfirmIdBatchByIds(
                            dffFormIds,
                        )
                        let newDffFormId = this.trf.dffFormId
                        if (!this.checkObjectIsNull(comfirmData)) {
                            newDffFormId = comfirmData[0].comfirmDffFormId
                        }
                        this.$set(this.trf, "dffFormId", newDffFormId) //重新赋值dffFormId;
                        // TODO新版DFF
                        this.$set(this.dffFormObjNew, "dffFormId", newDffFormId)

                        if (!objectIsNull(this.trf.dffFormData)) {
                            this.dffLanguageObj = JSON.parse(
                                this.trf.dffFormData,
                            )
                            if (validatenull(this.dffLanguageObj.EN)) {
                                this.dffLanguageObj.EN = {}
                            }
                            if (validatenull(this.dffLanguageObj.CN)) {
                                this.dffLanguageObj.CN = {}
                            }
                            this.$set(
                                this.dffFormObjNew,
                                "dffFormData",
                                this.dffLanguageObj,
                            )
                        }
                    }
                    //copy & Trf Template DFF Grid
                    this.$set(this.dffFormObjNew, "dffGridId", "")
                    if (!validatenull(this.trf.dffGridId)) {
                        let dffGridIds = []
                        dffGridIds.push(this.trf.dffGridId)
                        let comfirmData = await this.getDffComfirmIdBatchByIds(
                            dffGridIds,
                        )
                        let newDffGridId = this.trf.dffGridId
                        if (!this.checkObjectIsNull(comfirmData)) {
                            newDffGridId = comfirmData[0].comfirmDffFormId
                        }
                        this.$set(this.trf, "dffGridId", newDffGridId) //重新赋值dffGridId;
                        // TODO新版DFF
                        this.$set(this.dffFormObjNew, "dffGridId", newDffGridId)
                        if (!objectIsNull(this.trf.dffGridData)) {
                            this.dataList = JSON.parse(
                                this.trf.dffGridData.toString(),
                            )
                            if (validatenull(this.dataList.EN)) {
                                this.dataList.EN = this.defaultDataList_new.EN
                            }
                            if (validatenull(this.dataList.CN)) {
                                this.dataList.CN = this.defaultDataList_new.CN
                            }
                            /*if (this.trf.servicRequire.reportLanguage == 2) {
                                this.getLanguageGridObjList(2);
                            } else {
                                this.getLanguageGridObjList(1);
                            }*/
                            this.$set(
                                this.dffFormObjNew,
                                "dffGridData",
                                this.dataList,
                            )
                        }
                        this.isDffGridLoadFlag = true
                        /*//TODO 重新加载DFF Grid
                          if (this.isDffFormLoadFlag) {
                            this.$nextTick(() => {
                              this.$refs.frm.dffFormLoad();
                            });
                          }*/
                        //copy和模板进来以模板配置为准
                        if (!validatenull(obj.specificCode)) {
                            if (obj.specificCode.indexOf("care_Label") != -1) {
                                if (
                                    !objectIsNull(this.trf.carelabelInstances)
                                ) {
                                    //TODO 新版DFF
                                    this.$set(
                                        this.dffFormObjNew,
                                        "careLabelData",
                                        this.trf.carelabelInstances,
                                    )
                                }
                                this.isCareLabelLoadFlag = true
                            }
                        }
                    }
                    this.$nextTick(() => {
                        this.isDffFormLoadFlag = true
                    })
                } else {
                    if (!validatenull(this.trf.dffFormId)) {
                        // TODO新版DFF
                        this.$set(
                            this.dffFormObjNew,
                            "dffFormId",
                            this.trf.dffFormId,
                        )
                        if (!objectIsNull(this.trf.dffFormData)) {
                            this.dffLanguageObj = JSON.parse(
                                this.trf.dffFormData,
                            )
                            if (validatenull(this.dffLanguageObj.EN)) {
                                this.dffLanguageObj.EN = {}
                            }
                            if (validatenull(this.dffLanguageObj.CN)) {
                                this.dffLanguageObj.CN = {}
                            }
                            //判断报告语言 是否加载中文
                            this.$set(
                                this.dffFormObjNew,
                                "languageId",
                                this.dffLanguageObj,
                            )
                            this.$set(
                                this.dffFormObjNew,
                                "dffFormData",
                                this.dffLanguageObj,
                            )
                        }
                    }
                    // 不存在grid不显示
                    this.$set(this.dffFormObjNew, "dffGridId", "")
                    if (!validatenull(this.trf.dffGridId)) {
                        // TODO新版DFF
                        this.$set(
                            this.dffFormObjNew,
                            "dffGridId",
                            this.trf.dffGridId,
                        )
                        if (!objectIsNull(this.trf.dffGridData)) {
                            this.dataList = JSON.parse(this.trf.dffGridData)
                            if (validatenull(this.dataList.EN)) {
                                this.dataList.EN = this.defaultDataList_new.EN
                            }
                            if (validatenull(this.dataList.CN)) {
                                this.dataList.CN = this.defaultDataList_new.CN
                            }
                            this.$set(
                                this.dffFormObjNew,
                                "dffGridData",
                                this.dataList,
                            )
                        }
                        if (this.trf.trfStatus > 1) {
                            if (!validatenull(this.trf.carelabelInstances)) {
                                if (
                                    !objectIsNull(this.trf.carelabelInstances)
                                ) {
                                    //TODO 新版DFF
                                    this.$set(
                                        this.dffFormObjNew,
                                        "careLabelData",
                                        this.trf.carelabelInstances,
                                    )
                                }
                                this.$nextTick(() => {
                                    this.isCareLabelLoadFlag = true
                                })
                            } else {
                                if (!validatenull(obj.specificCode)) {
                                    if (
                                        obj.specificCode.indexOf(
                                            "care_Label",
                                        ) != -1
                                    ) {
                                        if (
                                            !objectIsNull(
                                                this.trf.carelabelInstances,
                                            )
                                        ) {
                                            //TODO 新版DFF
                                            this.$set(
                                                this.dffFormObjNew,
                                                "careLabelData",
                                                this.trf.carelabelInstances,
                                            )
                                        }
                                        this.$nextTick(() => {
                                            this.isCareLabelLoadFlag = true
                                        })
                                    }
                                }
                            }
                        } else {
                            if (!validatenull(obj.specificCode)) {
                                if (
                                    obj.specificCode.indexOf("care_Label") != -1
                                ) {
                                    if (
                                        !objectIsNull(
                                            this.trf.carelabelInstances,
                                        )
                                    ) {
                                        //TODO 新版DFF
                                        this.$set(
                                            this.dffFormObjNew,
                                            "careLabelData",
                                            this.trf.carelabelInstances,
                                        )
                                    }
                                    this.$nextTick(() => {
                                        this.isCareLabelLoadFlag = true
                                    })
                                }
                            }
                        }
                    }
                    this.setDffLanguageValue()
                    this.$nextTick(() => {
                        this.isDffFormLoadFlag = true
                    })
                }
            })
            this.$emit("trfDetail", this.trf)
        },
        setDffLanguageValue() {
            //处理加载语言问题
            if (this.trf.servicRequire.reportLanguage == 3) {
                this.$set(this.dffFormObjNew, "languageId", 1)
                this.languageID = this.languageNumber
            } else if (this.trf.servicRequire.reportLanguage == 2) {
                this.languageID = 2
                //TODO 新版DFF
                this.$set(this.dffFormObjNew, "languageId", 2)
            } else {
                this.dffLanguageObj.CN = {}
                this.languageID = 1
                //TODO 新版DFF
                this.$set(this.dffFormObjNew, "languageId", 1)
            }
        },
        setRemark(datas) {
            //遍历树  获取id数组
            var testPackageList = this.trf.testPackageList
            for (var i in datas) {
                for (var z in testPackageList) {
                    if (testPackageList[z].testPackageId == datas[i].id) {
                        if (!validatenull(testPackageList[z].remark)) {
                            datas[i].remark = testPackageList[z].remark
                        }
                    }
                    if (datas[i].children) {
                        this.setRemark(datas[i].children)
                    }
                }
            }
            return datas
        },
        async getDffComfirmIdBatchByIds(ids) {
            return await getDffFormComfirmIdBatchByIds(ids).then((res) => {
                return res.data.data
            })
        },
        getReturnSampleArry() {
            getReturnSampleArryCN(this.language).then((res) => {
                const data = res.data
                if (data.length > 0) {
                    this.returnSampleData = data
                }
            })
        },
        //获取服务类型
        searchServiceType: function (BUID) {
            getServiceType(BUID, this.language).then((res) => {
                const data = res.data.data
                this.serviceTypeData = data
                //默认服务类型为Regular
                if (!objectIsNull(this.serviceTypeData)) {
                    this.trf.serviceType = "1"
                    this.trf.serviceTypeName = "Regular"
                } else {
                    this.trf.serviceType = ""
                    this.trf.serviceTypeName = ""
                }
            })
        },
        handleNodeClick(data) {
            this.trf.testPackage.testPackageId = data.id
        },
        //另存为TRF模板保存方法
        async saveTrfUserTemplate() {
            if (
                this.trfUserTemplate.trfTemplateName == "" ||
                this.trfUserTemplate.trfTemplateName == null
            ) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("trf.placeholder.trfTemplateName"),
                    type: "warning",
                })
                return false
            }
            this.btnSbuTrfTemplateLoading = true
            //SMART-4137 保存模板时也需要初始化ServiceReq信息
            // if(this.trf.productLineCode === ProductLineEnums.HL.code && this.trf.isGeneralRequest === 1) {
            this.trf.servicRequire = this.$refs.serviceRequireRef
                ? this.$refs.serviceRequireRef.packagingServiceRequireData()
                : this.trf.servicRequire
            // }
            //保存dff组件数据
            this.saveTrfTestPackageAndDffData("userTemplate")
            //保存客户Customer List数据
            this.trf.customerList = this.$refs.applicantRef.mergeCustomerInfo(
                this.trf.customerList,
            );
            if (!objectIsNull(this.trf.customerList)) {
                for (let customerObj of this.trf.customerList) {
                    if (
                        customerObj.customerUsage ===
                        this.CustomerUsageEnums.BUYER.code
                    ) {
                        if (
                            this.trf.trfCustomer.buyerCustomerGroupCode ==
                            "General"
                        ) {
                            customerObj.customerGroupCode = "General"
                            customerObj.customerGroupName = "General"
                        } else {
                            customerObj.customerGroupCode =
                                this.trf.trfCustomer.buyerCustomerGroupCode
                            customerObj.customerGroupName =
                                this.trf.trfCustomer.buyerCustomerGroupName
                        }
                    }
                }
            }

            let cloneTemplateData = await this.getAndMergeDynamicFormData()
            cloneTemplateData = deepClone(cloneTemplateData)
            cloneTemplateData.id = null
            cloneTemplateData.trfNo = null
            cloneTemplateData.extendOrderNo = null
            cloneTemplateData.trfStatus = 1
            this.$set(
                this.trfUserTemplate,
                "trfType",
                TrfTypeEnums.USER_TEMPLATE.CODE,
            )
            //先默认从TRF中获取BU
            let templateProductLineCode = this.trf.productLineCode
            //如果不存在的话，再从浏览器中获取
            if (objectIsNull(templateProductLineCode)) {
                if (!objectIsNull(this.$route.query.bu)) {
                    templateProductLineCode = this.$route.query.bu
                }
            }
            this.$set(
                this.trfUserTemplate,
                "productLineCode",
                templateProductLineCode,
            )
            this.$set(
                this.trfUserTemplate,
                "trfTemplateData",
                JSON.stringify(cloneTemplateData),
            )
            this.$set(
                this.trfUserTemplate,
                "trfTemplateId",
                this.trf.trfTemplateId,
            )
            this.$set(this.trfUserTemplate, "trfName", this.trf.templateName)
            this.$set(
                this.trfUserTemplate,
                "trfBuyerCustomerGroupCode",
                this.trf.trfCustomer.buyerCustomerGroupCode,
            )
            this.$set(
                this.trfUserTemplate,
                "trfBuyerCustomerGroupName",
                this.trf.trfCustomer.buyerCustomerGroupName,
            )
            saveTrfUserTemplate(this.trfUserTemplate).then(
                (res) => {
                    this.trfTemplateDialogFormVisible = false
                    this.btnSbuTrfTemplateLoading = false
                    this.$message({
                        type: "success",
                        message: this.$t("api.success"),
                    })
                },
                (error) => {
                    this.btnSbuTrfTemplateLoading = false
                    this.$message.error(this.$t("api.error"))
                },
            )
        },
        //另存为TRF模板
        saveAsTrfTemplate() {
            //校验是否选择模板
            if (validatenull(this.trf.trfTemplateId)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("trf.validate.noTemplateMsg"),
                    type: "warning",
                })
                return false
            }
            this.trfUserTemplate.trfTemplateName = ""
            this.trfTemplateDialogFormVisible = true
        },
        //return 状态回退点击事件
        returnTrfStatusClick() {
            this.$confirm(this.$t("trf.confirmReturnTrf"), this.$t("tip"), {
                confirmButtonText: this.$t("submitText"),
                cancelButtonText: this.$t("cancelText"),
                type: "warning",
                closeOnClickModal: false,
            })
                .then(() => {
                    this.btnSubmitLoading = true
                    returnTrfV2(this.trf.id, this.$route.query.signature).then(
                        (res) => {
                            this.$message({
                                type: "success",
                                message: this.$t("api.success"),
                            })
                            let hashVal = new Date().getTime()
                            this.$router.push({
                                path: "/trf/trfDetail",
                                query: {
                                    id: this.trf.id,
                                    flag: this.trfFlag,
                                    actionType: "detail",
                                    hash: hashVal,
                                    signature: res.data.data,
                                    trfNo: this.trf.trfNo,
                                },
                            })

                            this.btnSubmitLoading = false
                            this.commponentKey += 1
                            this.trfDisabled = false
                            this.showDynamicForm = false
                            this.testPackageExpand = false
                            this.showCustomerNameFlag = false
                            this.templateDisabled = false
                            this.buyerDisabled = false
                            this.getParams()
                            //this.hash = hashVal;
                        },
                        (error) => {
                            this.btnSubmitLoading = false
                            this.$message.error(
                                res.data.message || this.$t("api.error"),
                            )
                        },
                    )
                })
                .catch(() => {
                    /* this.btnSubmitLoading=false;
                     this.$message.error(this.$t('api.error'));*/
                })
        },
        //确认收样点击事件
        confirmSampleClick() {
            this.confirmSampleForm.id = this.trf.id
            if (
                this.confirmSampleForm.sampleReceiveDate == "" ||
                this.confirmSampleForm.sampleReceiveDate == null
            ) {
                this.confirmSampleForm.sampleReceiveDate = new Date()
            }
            this.trfConfirmSampleDialogFormVisible = true
        },
        //确认收样表单提交
        confirmSampleDateSubmit(form) {
            this.$refs[form].validate(async (valid, error) => {
                if (valid) {
                    //check
                    if (
                        !objectIsNull(this.confirmSampleForm.dueDate) &&
                        !objectIsNull(this.confirmSampleForm.sampleReceiveDate)
                    ) {
                        var time1 = new Date(
                            this.confirmSampleForm.dueDate,
                        ).getTime()
                        var time2 = new Date(
                            this.confirmSampleForm.sampleReceiveDate,
                        ).getTime()
                        if (time1 < time2) {
                            this.$notify({
                                title: this.$t("tip"),
                                message: this.$t(
                                    "dateValidate.dueDataAndSmapleDataValiMsg",
                                ),
                                type: "warning",
                            })
                            return false
                        }
                    }
                    this.btnConfirmSampleLoading = true
                    this.confirmSampleForm.sampleReceiveDate = moment(
                        this.confirmSampleForm.sampleReceiveDate,
                    ).format("YYYY-MM-DD HH:mm:ss")
                    if (!objectIsNull(this.confirmSampleForm.dueDate)) {
                        this.confirmSampleForm.dueDate = moment(
                            this.confirmSampleForm.dueDate,
                        ).format("YYYY-MM-DD HH:mm:ss")
                    }
                    this.confirmSampleForm.trfId = this.trf.id
                    this.confirmSampleForm.trfNo = this.trf.trfNo
                    this.confirmSampleForm.signature =
                        this.$route.query.signature
                    //将录入的sample revice Data 转成东8区时间
                    let newConfirmSampleForm = deepClone(this.confirmSampleForm)
                    newConfirmSampleForm.sampleReceiveDate = this.tzToChina(
                        newConfirmSampleForm.sampleReceiveDate,
                    )
                    newConfirmSampleForm.dueDate = this.tzToChina(
                        newConfirmSampleForm.dueDate,
                    )
                    confirmSampleDateV2(newConfirmSampleForm).then(
                        (res) => {
                            this.$message({
                                type: "success",
                                message: this.$t("api.success"),
                            })
                            this.btnConfirmSampleLoading = false
                            this.trfConfirmSampleDialogFormVisible = false
                            let hashVal = new Date().getTime()
                            this.$router.push({
                                path: "/trf/trfDetail",
                                query: {
                                    id: this.trf.id,
                                    flag: this.trfFlag,
                                    actionType: "detail",
                                    hash: hashVal,
                                    signature: res.data.data,
                                    trfNo: this.trf.trfNo,
                                },
                            })
                            this.hash = hashVal
                        },
                        (error) => {
                            this.btnConfirmSampleLoading = false
                            this.$message.error(this.$t("api.error"))
                        },
                    )
                } else {
                    return false
                }
            })
        },
        //取消申请单
        cancelTrf(status) {
            this.$confirm(this.$t("trf.confirmCancelTrf"), this.$t("tip"), {
                confirmButtonText: this.$t("submitText"),
                cancelButtonText: this.$t("cancelText"),
                type: "warning",
            })
                .then(() => {
                    this.btnSubmitLoading = true
                    cancelTrfV2(this.trf.id, this.$route.query.signature).then(
                        (res) => {
                            this.$message({
                                type: "success",
                                message: this.$t("api.success"),
                            })
                            this.btnSubmitLoading = false
                            let hashVal = new Date().getTime()
                            this.$router.push({
                                path: "/trf/trfDetail",
                                query: {
                                    id: this.trf.id,
                                    flag: this.trfFlag,
                                    actionType: "edit",
                                    hash: hashVal,
                                    signature: res.data.data,
                                    trfNo: this.trf.trfNo,
                                },
                            })
                            this.hash = hashVal
                            //this.$router.push({path: '/trf/list', query: {}});
                        },
                        (error) => {
                            this.btnSubmitLoading = false
                            this.$message.error(
                                res.data.message || this.$t("api.error"),
                            )
                        },
                    )
                })
                .catch(() => {})
        },
        //删除申请单
        delTrf() {
            this.$confirm(this.$t("trf.confirmRemoveTrf"), this.$t("tip"), {
                confirmButtonText: this.$t("operation.remove"),
                cancelButtonText: this.$t("operation.back"),
                type: "warning",
            })
                .then(() => {
                    this.btnSubmitLoading = true
                    removeTrfV2(
                        this.trf.id,
                        this.trf.trfNo,
                        this.$route.query.signature,
                    ).then(
                        (res) => {
                            this.$message({
                                type: "success",
                                message: this.$t("api.success"),
                            })
                            this.btnSubmitLoading = false
                            //刷新TRF List
                            if (window.opener && window.opener !== window) {
                                //跳转TRF List
                                var channel = new BroadcastChannel("notice")
                                channel.postMessage(
                                    "发出消息，赶紧去更新数据吧",
                                )
                                window.close()
                            } else {
                                this.toTrfList()
                            }
                        },
                        (error) => {
                            this.btnSubmitLoading = false
                            this.$message.error(
                                res.data.message || this.$t("api.error"),
                            )
                        },
                    )
                })
                .catch(() => {
                    this.btnSubmitLoading = false
                    /* this.$message.error(this.$t('api.error'));*/
                })
        },
        //滚动到校验不通过的dom节点
        scrollToErrEl(refName, errDomSelector, errMsg) {
            if (!refName) {
                return
            }
            let dom = this.$refs[refName]
            if (!dom) {
                return
            }
            let errorDom = dom.$el
            if (errDomSelector) {
                console.log("errDomSelector", errDomSelector)
                let errDom = document.querySelector(errDomSelector)
                if (errDom) {
                    errorDom = errDom
                }
            }
            errorDom.scrollIntoView({
                block: "center",
                behavior: "smooth",
            })
            this.$notify({
                title: this.$t("tip"),
                message: errMsg || this.$t("trf.trfValidateError"),
                type: "warning",
            })
        },
        // 单个元素滚动方法
        validateAndScroll(refName) {
            const invalidElement = this.$refs[refName].validateForm()
            if (invalidElement) {
                invalidElement.scrollIntoView({
                    block: "center",
                    behavior: "smooth",
                })
            }
            this.$notify({
                title: this.$t("tip"),
                message: this.$t("trf.trfValidateError"),
                type: "warning",
            })
        },
        getNewTrfNo() {
            const now = new Date()
            const year = now.getFullYear()
            const month = (now.getMonth() + 1).toString().padStart(2, "0") // 月份是从0开始的，所以+1
            const day = now.getDate().toString().padStart(2, "0")
            const hours = now.getHours().toString().padStart(2, "0")
            const minutes = now.getMinutes().toString().padStart(2, "0")
            const seconds = now.getSeconds().toString().padStart(2, "0")
            const endStr = Math.floor(Math.random() * 900) + 100

            // 连接成纯数字字符串
            const formattedTime = `${year}${month}${day}${hours}${minutes}${seconds}${endStr}`
            //const formattedTime = `${minutes}${seconds}`;
            return "eFiling" + formattedTime
        },
        checkDuplicateProductInfo(data) {
            // 创建两个对象来分别存储 productIdType 和 productId 的计数
            const productIdTypeCount = {}
            const productIdCount = {}

            // 遍历数组中的每个实体
            for (const item of data) {
                const {productIdType, productId} = item

                // 检查 productIdType 是否重复
                productIdTypeCount[productIdType] =
                    (productIdTypeCount[productIdType] || 0) + 1
                if (productIdTypeCount[productIdType] > 1) {
                    return {hasDuplicateIdType: true, hasDuplicateId: false}
                }

                // 检查 productId 是否重复
                productIdCount[productId] = (productIdCount[productId] || 0) + 1
                if (productIdCount[productId] > 1) {
                    return {hasDuplicateIdType: false, hasDuplicateId: true}
                }
            }

            // 没有发现重复
            return {hasDuplicateIdType: false, hasDuplicateId: false}
        },
        checkProductInfo(productInfoList) {
            for (let productInfo of productInfoList) {
                if (
                    !productInfo ||
                    !productInfo.formList.length ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    )
                ) {
                    this.$message.error(
                        this.$t("work.cpscCustomerTrade.tip.primary"),
                    )
                    return {
                        flag: false,
                        msg: this.$t("work.cpscCustomerTrade.tip.primary"),
                    }
                }
                if (
                    !productInfo ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    ).productIdType ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    ).productId
                ) {
                    this.$message.error(
                        this.$t("work.cpscCustomerTrade.tip.productInfo"),
                    )
                    return {
                        flag: false,
                        msg: this.$t("work.cpscCustomerTrade.tip.productInfo"),
                    }
                }
                const {hasDuplicateIdType, hasDuplicateId} =
                    this.checkDuplicateProductInfo(productInfo.formList)
                if (hasDuplicateIdType)
                    return {flag: false, msg: "ProductIdType Repeat"}
                if (hasDuplicateId)
                    return {flag: false, msg: "ProductId Repeat"}
            }
            return {flag: true}
        },
        handleSave(trfReferenceNo, status) {
            let productInfoList = JSON.parse(
                JSON.stringify(this.productInfoList),
            )
            //const fun =()=>{
            const {flag, msg} = this.checkProductInfo(productInfoList)
            console.log("1")
            if (!flag) {
                console.log(msg)
                this.$message.error(msg)
                this.btnSubmitLoading = false
                return
            }
            for (let productInfo of productInfoList) {
                //校验

                let formModel = this.$refs.eFilling.newFormModel
                let form = JSON.parse(
                    JSON.stringify({...formModel, ...productInfo}),
                )
                form.dataSource = 2
                form.trfInfoStastus = status

                //获取 primay key
                if (form.formList && form.formList.length) {
                    const primary = form.formList.find(
                        (item) => item.primaryId === "yes",
                    )

                    form.productId = primary.productId
                    form.productIdType = primary.productIdType
                }
                if (form.productModelName) {
                    form.productName = form.productModelName
                }

                form.productJson = JSON.stringify(productInfo)

                // 点击save 和 submit时 新增数据，所以需要将 form中的id属性设置为null
                add({...form, trfReferenceNo}).then(
                    () => {
                        this.$message({
                            type: "success",
                            message: "Success!",
                        })
                    },
                    (error) => {
                        window.console.log(error)
                    },
                )
            }
            // }

            //fun()
        },
        openPanel() {
            let allPanel = document.querySelectorAll(
                ".sgs_smart_trf_trf_detail div.el-collapse-item__wrap",
            )
            if (allPanel) {
                try {
                    allPanel.forEach((panel) => {
                        console.log("panel", panel)
                        if (!panel || !panel.style) {
                            return
                        }
                        let display = panel.style.display
                        if (display == "none") {
                            panel.style.display = ""
                        }
                    })
                } catch (e) {}
            }
        },

        async onSubmit(submitFlag) {
            //校验是否选择模板
            if (validatenull(this.trf.trfTemplateId)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("trf.validate.noTemplateMsg"),
                    type: "warning",
                })
                return false
            }
            //submit的时候 需要打开panel 进行dingwei
            submitFlag == 2 ? this.openPanel() : ""
            console.log("submitFlag========", submitFlag)
            this.more = true
            this.trf.customerList = this.$refs.applicantRef.mergeCustomerInfo(
                this.trf.customerList,
            )
            let applicantCustomer = {}
            if (!objectIsNull(this.trf.customerList)) {
                for (let customerObj of this.trf.customerList) {
                    if (
                        customerObj.customerUsage ===
                        this.CustomerUsageEnums.BUYER.code
                    ) {
                        if (
                            this.trf.trfCustomer.buyerCustomerGroupCode ==
                            "General"
                        ) {
                            customerObj.customerGroupCode = "General"
                            customerObj.customerGroupName = "General"
                        } else {
                            customerObj.customerGroupCode =
                                this.trf.trfCustomer.buyerCustomerGroupCode
                            customerObj.customerGroupName =
                                this.trf.trfCustomer.buyerCustomerGroupName
                        }
                    } else if (
                        customerObj.customerUsage ===
                        this.CustomerUsageEnums.APPLICATION.code
                    ) {
                        applicantCustomer.customerId = customerObj.customerId
                    }
                }
            }
            this.trf.trfCustomer =
                this.$refs.applicantRef.packagingCustomerData(
                    this.trf.trfCustomer,
                )
            this.trf.isSame = this.trf.trfCustomer.isSame
            this.trf.trfCustomerContact =
                this.$refs.applicantRef.packagingCustomerData(
                    this.trf.trfCustomerContact,
                )
            // if(this.trf.productLineCode === ProductLineEnums.HL.code && this.trf.isGeneralRequest === 1) {
            if (this.$refs.serviceRequireRef) {
                this.trf.servicRequire =
                    this.$refs.serviceRequireRef.packagingServiceRequireData()
            } else {
                this.trf.servicRequire.vatType = null
                this.trf.servicRequire = this.trf.servicRequire
            }

            // }
            //验证 只有提交的时候做验证
            if (submitFlag == "2" || submitFlag == 2) {
                //校验动态该表单必填项
                if (this.showDynamicForm) {
                    let formValidResult =
                        await this.$refs.VFormDesignrender.formValidate()
                    console.log("check--formValidResult:", formValidResult)
                    if (!formValidResult.valid) {
                        let errMsg = formValidResult.msg
                        let errDiv = null
                        let message = null
                        try {
                            if (errMsg && Object.keys(errMsg).length > 0) {
                                errDiv = Object.keys(errMsg)[0]
                                let errMsgObjKeys = Object.keys(errMsg[errDiv])
                                if (errMsgObjKeys) {
                                    let errObj =
                                        errMsg[errDiv][errMsgObjKeys[0]]
                                    if (errObj && !errObj.message) {
                                        errObj = errObj[0]
                                    }
                                    message = errObj.message
                                }
                            }
                        } catch (e) {}
                        this.scrollToErrEl(
                            "VFormDesignrender",
                            `label[for='${errDiv}']`,
                            message,
                        )
                        return false
                    }
                }
                if (this.isEfillingFlag) {
                    let formValidResult =
                        await this.$refs.eFilling.formValidate()
                    console.log("动态表单的check--eFilling:", formValidResult)
                    if (!formValidResult) {
                        this.scrollToErrEl("eFilling")
                        return false
                    }
                }
                //校验客户必填
                let customerValidateFlag =
                    await this.$refs.applicantRef.validataCustomerForm()
                if (!customerValidateFlag) {
                    return false
                }
                //验证申请单数据
                this.$refs["trf"].validate(async (valid, error) => {
                    if (valid) {
                        if (this.isDffFormLoadFlag) {
                            let valid =
                                this.$refs.frm.checkFormAndGridValidate()
                            if (!valid) {
                                this.scrollToErrEl("frm")
                                return false
                            }
                        }
                        let that = this
                        if (
                            this.trf.productLineCode ===
                                ProductLineEnums.HL.code &&
                            this.trf.isGeneralRequest === 1
                        ) {
                            if (!this.trf.servicRequire.isValid) {
                                /* this.$notify({
                            title: this.$t('tip'),
                            message: this.checkObjectIsNull(this.trf.servicRequire.validMsg) ? this.$t('trf.trfValidateError') : this.trf.servicRequire.validMsg,
                            type: 'warning'
                        });*/
                                this.validateAndScroll("serviceRequireRef")
                                return false
                            }
                        }
                        if (objectIsNull(applicantCustomer.customerId)) {
                            applicantCustomer.customerId =
                                this.trf.trfCustomer.customerId
                        }
                        if (
                            !objectIsNull(applicantCustomer.customerId) &&
                            applicantCustomer.customerId !=
                                this.userInfo.companyId &&
                            !objectIsNull(this.userInfo.companyId)
                        ) {
                            let valid =
                                await this.$refs.applicantRef.validateCurrentAuth()
                            if (!valid) {
                                this.$notify({
                                    title: this.$t("tip"),
                                    message: this.$t(
                                        "authorization.authorValid",
                                    ),
                                    type: "warning",
                                })
                                return false
                            }
                        }
                        //验证CCL 自定义表单
                        this.saveTrfSummit(submitFlag)
                    } else {
                        // 跳转未填写的必填项，el-form-item 需要增加 ref 属性方便定位，值和 prop 相同即可
                        let str = []
                        let applicationError = ["customerNameEn"]
                        for (let key in error) {
                            error[key].map((item) => {
                                str.push(item.message)
                            })

                            let dom = this.$refs[Object.keys(error)[0]]

                            if (objectIsNull(dom)) {
                                if (
                                    applicationError.indexOf(
                                        Object.keys(error)[0],
                                    ) != -1
                                ) {
                                    dom =
                                        this.$refs["applicantRef"].$refs[
                                            Object.keys(error)[0]
                                        ]
                                }
                            }
                            if (
                                Object.prototype.toString.call(dom) !==
                                "[object Object]"
                            ) {
                                dom = dom[0]
                                break
                            }
                            dom.$el.scrollIntoView({
                                block: "center",
                                behavior: "smooth",
                            })
                        }

                        this.$notify({
                            title: this.$t("tip"),
                            message: this.$t("trf.trfValidateError"),
                            type: "warning",
                        })
                        return false
                    }
                })
            } else {
                this.saveTrfSummit(submitFlag)
            }
        },
        async getAndMergeDynamicFormData() {
            if (!this.showDynamicForm) {
                return this.trf
            }
            let formData = this.$refs.VFormDesignrender.getFormData()
            let formObj = this.$refs.VFormDesignrender.getFormAndGroupId()
            //假设当前的属性放到formData中
            //console.log("转换数据的obj:",this.templateChangeObj)
            let {formCode, formType, formDesignId, formDesignGroupId} =
                this.templateChangeObj
            let {formId, groupId} = formObj
            this.$set(this.trf, "formCode", formCode)
            this.$set(this.trf, "formType", formType)
            //使用当前动态表单本身的id和groupId
            this.$set(this.trf, "formDesignId", formId)
            this.$set(this.trf, "formDesignGroupId", groupId)
            this.trf.formDesignData = formData
            //console.log("转换之qian的数据", this.trf);
            let trfObj = await this.convertCode(1)
            //console.log("转换之后的数据", trfObj);
            return trfObj
        },
        async convertCode(type) {
            if (![1, 2].includes(type)) {
                return
            }
            return new Promise((resolve, reject) => {
                let response = null
                if (type == 1) {
                    //console.log("保存时进行转换",this.trf)
                    response = convertDff(this.trf)
                }
                if (type == 2) {
                    //console.log("回显时进行转换",this.trf)
                    response = convertDesign(this.trf)
                }
                response
                    .then((res) => {
                        console.log("convertDff res", res)
                        let {data} = res.data
                        resolve(data)
                    })
                    .catch((err) => {
                        reject()
                    })
            })
        },
        async saveTrfSummit(submitFlag) {
            //07-13添加factory回写 函数调用；
            this.trf.signature = this.$route.query.signature
            this.saveTrfTestPackageAndDffData()

            this.trf.trfStatus = submitFlag

            if (
                this.trf.trfLab &&
                this.trf.trfLab.labCode &&
                this.templateData
            ) {
                this.templateLabData.forEach((item) => {
                    if (item.labCode === this.trf.trfLab.labCode) {
                        this.trf.trfLab.labName = item.labName
                        this.trf.trfLab.labNameCn = item.labNameCn
                    }
                })
            }

            let trfObj = await this.getAndMergeDynamicFormData()
            //提交表单
            this.btnSubmitLoading = true
            //
            if (this.isEfillingFlag && this.$refs.eFilling) {
                let productInfoList = this.$refs.eFilling.productInfoList
                const {flag, msg} = this.checkProductInfo(productInfoList)
                if (flag) {
                    let formModel = this.$refs.eFilling.newFormModel
                    // if(!formModel.referenceNo){
                    //     formModel.referenceNo= this.getNewTrfNo()
                    // }

                    const efilingList = [
                        {
                            efilingFormData: JSON.stringify({
                                formModel,
                                productInfoList,
                            }),
                        },
                    ]
                    trfObj.efilingList = efilingList
                } else {
                    this.btnSubmitLoading = false
                    return
                }
            }
            trfObj.language = this.language
            let res = await saveTrfV2(trfObj)
                .finally(() => {
                    this.btnSubmitLoading = false
                })
                .catch(() => {
                    this.trf.trfStatus = 1 //进入异常的话 状态初始化
                    this.btnSubmitLoading = false
                    return false
                })
            if (res && res.data && res.data.data && res.data.data.code == 200) {
                this.btnSubmitLoading = false
                //this.careInstructions = res.data.data.data.carelabelInstances;
                this.$message({
                    type: "success",
                    message: this.$t("api.success"),
                })
                var actionType = "detail"
                if (submitFlag == "1") {
                    actionType = "edit"
                    let hashVal = new Date().getTime()
                    if (this.isEfillingFlag) {
                        this.handleSave(res.data.data.data.trfNo, submitFlag)
                    }
                    const routeData = this.$router.resolve({
                        path: "/trf/trfDetail",
                        query: {
                            id: res.data.data.data.id,
                            actionType: actionType,
                            flag: 1,
                            hash: hashVal,
                            signature: res.data.data.data.signature,
                            trfNo: res.data.data.data.trfNo,
                        },
                    })

                    this.hash = hashVal
                    window.location.href = routeData.href
                    window.location.reload()
                }
                if (submitFlag == "2" || submitFlag == 2) {
                    this.$alert(this.$t("trf.printTrfTip"), this.$t("tip"), {
                        confirmButtonText: this.$t("operation.confirm"),
                        callback: (action) => {
                            let hashVal = new Date().getTime()

                            if (this.isEfillingFlag) {
                                this.handleSave(
                                    res.data.data.data.trfNo,
                                    submitFlag,
                                )
                            }
                            const routeData = this.$router.resolve({
                                path: "/trf/trfDetail",
                                query: {
                                    id: res.data.data.data.id,
                                    actionType: actionType,
                                    flag: 1,
                                    hash: hashVal,
                                    signature: res.data.data.data.signature,
                                    trfNo: res.data.data.data.trfNo,
                                },
                            })
                            this.hash = hashVal
                            window.location.href = routeData.href
                            window.location.reload()
                        },
                    })
                }
            } else {
                this.trf.trfStatus = 1 //进入异常的话 状态初始化
                this.btnSubmitLoading = false
                this.$message.error(res.data.data.msg)
            }
        },
        transSaveData(dffFormData) {
            let saveDataItem = JSON.parse(JSON.stringify(dffFormData))
            let otherN = ["OTHER", "其他"]
            for (let key in saveDataItem) {
                if (key == "EN") {
                    //saveDataItem[key].forEach(dff => {
                    for (let dffKey in saveDataItem["EN"]) {
                        if (saveDataItem["EN"][dffKey + "_other"]) {
                            let allSelect = saveDataItem["EN"][dffKey]
                            let otherData =
                                saveDataItem["EN"][dffKey + "_other"]
                            let allData = allSelect.split(",")
                            if (allData.length > 1) {
                                let shouldSave = allData.filter(
                                    (da) => !otherN.includes(da),
                                )
                                otherData = [...shouldSave, otherData].join(",")
                            }
                            saveDataItem["EN"][dffKey] = otherData
                        }
                    }
                }
                if (key == "CN") {
                    for (let dffKey in saveDataItem["CN"]) {
                        if (saveDataItem["CN"][dffKey + "_other"]) {
                            let allSelect = saveDataItem["CN"][dffKey]
                            let otherData =
                                saveDataItem["CN"][dffKey + "_other"]
                            let allData = allSelect.split(",")
                            if (allData.length > 1) {
                                let shouldSave = allData.filter(
                                    (da) => !otherN.includes(da),
                                )
                                otherData = [...shouldSave, otherData].join(",")
                            }
                            saveDataItem["CN"][dffKey] = otherData
                        }
                    }
                }
            }
            return saveDataItem
        },
        saveTrfTestPackageAndDffData(action) {
            //获取选中的testPackage
            var ids
            if (this.$refs.selTree) {
                ids = this.$refs.selTree.getCheckedKeys(true)
            } else {
                ids = this.trf.testPackageIds
            }
            if (ids != undefined && ids.length > 0) {
                this.trf.testPackageIds = ids
            } else {
                this.trf.testPackageIds = []
            }

            var selectTestPackageIds
            selectTestPackageIds = this.$refs.selTree.getCheckedNodes(
                false,
                true,
            )

            if (this.trf.trfStatus > 1 && action == "userTemplate") {
                // 已提交状的
                this.trf.testPackageIds = []
                this.tempCheckList = []
                this.mapTree(this.trf.testPackages)
                selectTestPackageIds = this.tempCheckList
            }

            var selectIdsStr = ""
            if (this.trf.trfStatus <= 1) this.trf.testPackages = []
            this.trf.testPackageList = []

            if (selectTestPackageIds) {
                selectTestPackageIds.find((item) => {
                    var testPackage = {}
                    if (item.remark != "remark" && item.remark != "Remark") {
                        testPackage.remark = item.remark
                    }
                    if (this.trf.trfStatus > 1) {
                        // 已提交状态的
                        testPackage.testPackageId = item.testPackageId
                        this.trf.testPackageList.push(testPackage)
                        selectIdsStr += item.testPackageId + ","
                    } else {
                        testPackage.testPackageId = item.id
                        this.trf.testPackageList.push(testPackage)
                        selectIdsStr += item.id + ","
                    }
                })
            }
            selectIdsStr =
                selectIdsStr.substring(selectIdsStr.length - 1) == ","
                    ? selectIdsStr.substring(0, selectIdsStr.length - 1)
                    : selectIdsStr
            this.trf.testPackageAllIds = selectIdsStr
            delete this.trf.dffGridData
            delete this.trf.careLabelData
            delete this.trf.dffFormData
            //动态表单和dff 只能2选1
            if (this.showDynamicForm) {
                return
            }
            //TODO 获取新DFF Form和Grid数据
            var dffInfo = this.$refs.frm.getDatas()
            this.trf.dffFormData = JSON.stringify(
                this.transSaveData(dffInfo.formValues),
            )
            this.trf.dffGridData = JSON.stringify(dffInfo.gridValues)
            //待更新为获取最新CareLabel
            if (this.isCareLabelLoadFlag) {
                let careLabelData = this.$refs.frm.getCareLabelDatas()
                if (!objectIsNull(careLabelData)) {
                    for (let careLabel of careLabelData) {
                        delete careLabel.imgArray
                    }
                }
                this.trf.careLabelData = JSON.stringify(careLabelData)
                this.trf.carelabelInstances = careLabelData
            }
        },
        mapTree(data) {
            data.forEach((items) => {
                //遍历树 拼入相应的disabled
                if (items.children.length <= 0) {
                    this.trf.testPackageIds.push(items.testPackageId)
                }
                this.tempCheckList.push(items)
                if (items.children) {
                    this.mapTree(items.children)
                }
            })
        },
        iter(level) {
            let that = this
            return function (node) {
                ;(node.children || []).forEach(that.iter(level + 1))
            }
        },
        contactrNameChange(val) {
            let obj = {}
            obj = this.customerContactData.find((item) => {
                return item.contactName === val
            })
            if (!objectIsNull(obj)) {
                //获取联系人详情
                this.$set(
                    this.trf.trfCustomerContact,
                    "applyContactTel",
                    obj.contactTel,
                )
                this.$set(
                    this.trf.trfCustomerContact,
                    "applyContactEmail",
                    obj.contactEmail,
                )
            }
        },
        customerNameChange(val) {
            this.$set(this.trf.servicRequire, "reportHeader", val)
            this.$set(this.trf.servicRequire, "reportHeaderEn", val)
        },
        customerAddressChange(val) {
            let obj = {}
            obj = this.customerAddressData.find((item) => {
                return item.addressDetail === val
            })
            if (!objectIsNull(obj)) {
                if (
                    this.trf.servicRequire.reportAddress == "" ||
                    this.trf.servicRequire.reportAddress == null
                ) {
                    this.$set(
                        this.trf.servicRequire,
                        "reportAddress",
                        obj.addressDetail,
                    )
                }
                if (
                    this.trf.servicRequire.reportAddressEn == "" ||
                    this.trf.servicRequire.reportAddressEn == null
                ) {
                    this.$set(
                        this.trf.servicRequire,
                        "reportAddressEn",
                        obj.addressDetail,
                    )
                }
            } else {
                this.$set(this.trf.servicRequire, "reportAddress", val)
                this.$set(this.trf.servicRequire, "reportAddressEn", val)
            }
        },
        selectLabChange(val) {
            console.log("change lab", val)
            this.$set(this.trf.trfLab, "labAddress", "")
            this.$set(this.trf.trfLab, "labName", "")
            this.$set(this.trf.trfLab, "labNameCn", "")
            //if(validatenull(val)){
            if (
                this.trf.trfStatus <= 2 ||
                (this.showModifyBtn("basic") && !this.disabledComonent("basic"))
            ) {
                this.$set(this.trf.trfLabContact, "contactName", "")
                this.$set(this.trf, "trfLabContact", {})
            }

            //}
            this.templateLabContactData = []
            if (validatenull(val)) {
                return
            }
            let obj = {}
            obj = this.templateLabData.find((item) => {
                return item.labCode === val
            })
            if (!objectIsNull(obj)) {
                this.$set(this.trf.trfLab, "labCode", obj.labCode)
                this.$set(this.trf.trfLab, "labName", obj.labName)
                this.$set(this.trf.trfLab, "labNameCn", obj.labNameCn)
                this.$set(this.trf.trfLabContact, "labCode", obj.labCode)
                this.$set(this.trf.trfLabContact, "labAddress", obj.labAddress)
                this.$set(this.trf.trfLabContact, "labName", obj.labName)
                this.$set(
                    this.templateLabContactDataParam,
                    "labTypeFlag",
                    obj.labTypeFlag,
                )
                var labAddress = obj.labAddress
                if (this.language == "zh-CN") {
                    if (!validatenull(obj.labAddressCn)) {
                        labAddress = obj.labAddressCn
                    }
                }
                this.$set(this.trf.trfLab, "labAddress", labAddress)
            }
            this.$set(this.templateLabContactDataParam, "labCode", val)
            this.$set(
                this.templateLabContactDataParam,
                "trfTemplateId",
                this.trf.trfTemplateId,
            )
            //查询实验室联系人
            this.searchTemplateLabContactData()
        },
        selectLabContactChange(val) {
            let obj = {}
            obj = this.templateLabContactData.find((item) => {
                return item.labContactName === val
            })
            if (!objectIsNull(obj)) {
                this.$set(
                    this.trf.trfLabContact,
                    "labContactId",
                    obj.labContactId,
                )
                this.$set(this.trf.trfLabContact, "contactName", val)
                this.$set(
                    this.trf.trfLabContact,
                    "contactEmail",
                    obj.contactEmail,
                )
                this.$set(this.trf.trfLabContact, "contactTel", obj.contactTel)
            }
        },
        //查询模板实验室下拉数据
        searchTemplateLabContactData() {
            var params = {}
            let _this = this
            getTemplateLabContactList(
                Object.assign(params, this.templateLabContactDataParam),
            ).then((res) => {
                const data = res.data.data
                this.templateLabContactData = data
                if (data == null || data == undefined || data.length == 0) {
                    //无联系人 不需要选择 && !_this.trf.trfLabContact.contactName
                    this.labContactFlag = false
                    this.$set(this.trf, "trfLabContact", {})
                    return false
                } else {
                    this.templateLabContactData.forEach(
                        (item, index, array) => {
                            var contactEmail = item.contactEmail
                            if (!validatenull(contactEmail)) {
                                item.labContactName =
                                    item.labContactName +
                                    " (" +
                                    contactEmail +
                                    ")"
                            }
                        },
                    )
                }
                this.labContactFlag = true
                let obj = {}
                if (!validatenull(this.trf.trfLabContact.id)) {
                    obj = this.templateLabContactData.find((item) => {
                        return (
                            item.labContactName ===
                            this.trf.trfLabContact.contactName
                        )
                    })
                } else if (this.newMatrilaObj.labContact) {
                    obj = this.templateLabContactData.find((item) => {
                        return (
                            item.labContactName ===
                            this.newMatrilaObj.labContact
                        )
                    })
                    if (objectIsNull(obj)) {
                        obj = this.templateLabContactData.find((item) => {
                            return item.isDefault === 1
                        })
                    }
                } else {
                    obj = this.templateLabContactData.find((item) => {
                        return item.isDefault === 1
                    })
                }

                if (!objectIsNull(obj)) {
                    this.$set(
                        this.trf.trfLabContact,
                        "labContactId",
                        obj.labContactId,
                    )
                    this.$set(
                        this.trf.trfLabContact,
                        "contactName",
                        obj.labContactName,
                    )
                    this.$set(
                        this.trf.trfLabContact,
                        "contactEmail",
                        obj.contactEmail,
                    )
                    this.$set(
                        this.trf.trfLabContact,
                        "contactTel",
                        obj.contactTel,
                    )

                    //加载出实验室数据
                    this.$set(this.trf.trfLab, "labCode", obj.labCode)
                    let labObj = {}
                    labObj = this.templateLabData.find((item) => {
                        return item.labCode === obj.labCode
                    })
                    if (!objectIsNull(labObj)) {
                        //赋值实验室地址
                        //this.$set(this.trf.trfLab, 'labAddress', labObj.labAddress);
                        this.$set(
                            this.trf.trfLabContact,
                            "labName",
                            labObj.labName,
                        )
                        this.$set(
                            this.trf.trfLabContact,
                            "labCode",
                            labObj.labCode,
                        )
                        this.$set(
                            this.trf.trfLabContact,
                            "labAddress",
                            labObj.labAddress,
                        )
                    }
                }
            })
        },
        async selectTemplateChange(val) {
            this.trf.trfLab = {}
            this.trf.trfLabContact = {}
            this.trf.serviceType = ""
            this.trf.serviceTypeName = ""
            this.isDffFormLoadFlag = false
            this.isDffGridLoadFlag = false
            this.isCareLabelLoadFlag = false
            this.trf.dffFormId = ""
            this.trf.dffGridId = ""
            this.dataList = this.defaultDataList_new
            this.beLongData = []
            this.generalRequestFlag = false
            this.trf.isGeneralRequest = 0
            this.trf.isCpRequest = 0
            this.$set(this.trf, "templateName", "")
            this.$set(this.trf.servicRequire, "returnSampleName", "")
            this.$set(this.trf.servicRequire, "returnSampleRequire", "")
            this.$set(this.trf.servicRequire, "vatType", null)
            this.$set(this.trf.servicRequire, "isComment", 0)
            this.$set(this.trf.servicRequire, "isCopy", 0)
            this.$set(this.trf.servicRequire, "isPhoto", 0)
            this.$set(this.trf.servicRequire, "isConfimCover", 0)
            this.$set(this.trf.servicRequire, "isQuotation", 0)
            this.$set(this.trf.servicRequire, "contactList", [])
            //选择模版后，清空模版名称
            this.$set(this.trfUserTemplate, "trfName", "")
            this.$set(this.trfUserTemplate, "trfTemplateId", null)

            if (validatenull(val)) {
                return
            }

            //查询Belong
            this.queryCurrentDeptBeLong(val)
            /*if (!this.optionType == 'material') {

                }*/
            let obj = {}
            obj = this.templateData.find((item) => {
                return item.id === val
            })
            //SMART-3853将加载动态表单迁移至提交Template选择弹框后
            //console.log("加载tempalte----------",obj)
            // this.queryDynamicFormDesignGroupId = obj.formDesignGroupId;
            // this.showDynamicForm = false;
            // this.$nextTick(()=>{
            //     this.showDynamicForm = (obj.formType-0==2);
            // })
            //console.log("showDynamicForm",this.showDynamicForm,obj.formType,this.queryDynamicFormDesignGroupId)

            this.$set(this.trf, "printTemplateId", obj.printTemplateId)
            this.templateChangeObj = obj
            //加载dff form组件
            if (!objectIsNull(obj)) {
                if (
                    obj.productLineCode == "HL" &&
                    obj.specificCode.indexOf("general_additional") != -1
                ) {
                    this.$set(this.trf.servicRequire, "vatType", 2)
                }
                //SMART-3450 增加模板校验，选择的非General模板是否存在customer信息
                if (obj.customerGroupCode != "General") {
                    if (objectIsNull(obj.customerBossNo)) {
                        this.$notify({
                            title: this.$t("tip"),
                            message: this.$t("trf.templateCustomerError"),
                            type: "warning",
                        })
                        //清空选择模板
                        this.$set(this.trf, "trfTemplateId", null)
                        this.$set(this.trf, "templateName", "")
                        return false
                    }
                }
                if (this.trfFlag == 2) {
                    let fieldData = await getFieldSettingsByTemplateId(obj.id)
                    if (fieldData.data.code == 200) {
                        this.fieldSettingsData = fieldData.data.data
                    }
                }
                this.templateSpecificCode = obj.specificCode
                this.templateDataObj = obj
                //查询Buyer客户
                if (this.$refs.applicantRef) {
                    this.$refs.applicantRef.searchBuyerCustomerInfo(
                        this.trf.trfCustomer.buyerCustomerNo,
                        0,
                    )
                }
                if (obj.specificCode.indexOf("general_additional") != -1) {
                    this.generalRequestFlag = true
                    this.trf.isGeneralRequest = 1
                } else {
                    this.generalRequestFlag = false
                    this.trf.isGeneralRequest = 0
                }
                if (
                    obj &&
                    obj.specificCode.indexOf("additional_for_cp") != -1
                ) {
                    this.trf.isCpRequest = 1
                } else {
                    this.trf.isCpRequest = 0
                }
                this.$set(this.trf, "formType", obj.formType)
                this.$set(this.trf, "templateName", obj.templateName)
                this.$set(this.trfUserTemplate, "trfName", obj.templateName)
                this.$set(this.trfUserTemplate, "trfTemplateId", obj.id)
                this.$set(this.trf, "productLineId", obj.productLineId)
                this.$set(this.trf, "productLineCode", obj.productLineCode)
                this.$set(
                    this.templateLabDataParam,
                    "productLineCode",
                    obj.productLineCode,
                )
                this.$set(this.trf, "productLineName", obj.productLineName)
                //查询服务类型sel
                this.searchServiceType(obj.productLineId)
                //选择模板后 接口获取报告语言
                this.queryReportLanguage()
            }
            //查询该模板下的实验室及联系人
            this.$set(
                this.templateLabDataParam,
                "trfTemplateId",
                this.trf.trfTemplateId,
            )
            this.$set(
                this.testPackageParam,
                "templateId",
                this.trf.trfTemplateId,
            )
            //查询模板关联的测试包
            this.searchTemplateTestPackage()
            this.searchTemplateLabData()
        },
        searchTemplateTestPackage() {
            this.treeLoading = true
            getTemplateTestPackageList(
                Object.assign({parentId: -1}, this.testPackageParam),
            ).then((res) => {
                this.treeLoading = false
                this.testPackageTreedata = res.data.data
            })
        },
        //查询模板实验室下拉数据
        searchTemplateLabData() {
            let params = {}
            this.$set(this.templateLabDataParam, "language", this.language)
            getTemplateLabList(
                Object.assign(params, this.templateLabDataParam),
            ).then((res) => {
                const data = res.data.data
                this.templateLabData = data
                if (data.length == 1) {
                    this.selectLabChange(data[0].labCode)
                }
                /*console.log(this.trf.trfLab.labCode);
                    if(!validatenull(this.trf.trfLab.labCode)){
                          this.selectLabChange(this.trf.trfLab.labCode);
                    }*/
            })
        },
        async searchTemplateData(isAgentTemplateSearch = false) {
            this.$set(
                this.templateDataParam,
                "productLineCode",
                this.productLineCode,
            )
            // 如果选择的buyer是当前登陆的客户，需要用客户的BossNo去查询模板
            if (
                this.userInfo.customerGroupCode ===
                this.trf.trfCustomer.buyerCustomerGroupCode
            ) {
                this.$set(
                    this.templateDataParam,
                    "customerBossNo",
                    this.userInfo.bossNo,
                )
            }

            var params = {}
            let res = await getTemplateList(
                Object.assign(params, this.templateDataParam),
            )
            //getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
            this.templateDataLoad = true
            const data = res.data.data
            this.templateData = data
            //20-6-19新增逻辑 如果buyer选择general的话 则只带出general的模板，如果只有一个模板的话 则自动带入，无需选择；
            if (this.templateData.length == 1) {
                this.trf.trfLab = {}
                this.trf.trfLabContact = {}
                this.trf.serviceType = ""
                this.isDffFormLoadFlag = false
                this.isDffGridLoadFlag = false
                this.isCareLabelLoadFlag = false
                this.$set(this.trf, "dffFormId", "")
                this.$set(this.trf, "dffGridId", "")
                this.trf.dffFormId = ""
                this.trf.dffGridId = ""
                var obj = this.templateData[0]
                this.templateChangeObj = obj
                this.queryDynamicFormDesignGroupId = obj.formDesignGroupId
                this.showDynamicForm = obj.formType - 0 == 2
                this.$set(this.trf, "trfTemplateId", obj.id)
                //加载dff form 或者动态form组件
                if (!objectIsNull(obj)) {
                    //SMART-3450 增加模板校验，选择的非General模板是否存在customer信息
                    if (obj.customerGroupCode != "General") {
                        if (objectIsNull(obj.customerBossNo)) {
                            this.$notify({
                                title: this.$t("tip"),
                                message: this.$t("trf.templateCustomerError"),
                                type: "warning",
                            })
                            //清空选择模板
                            this.$set(this.trf, "trfTemplateId", null)
                            this.$set(this.trf, "templateName", "")
                            //关闭模板loading效果
                            this.templateLoading = false
                            return false
                        }
                    }
                    this.templateDataObj = obj
                    this.templateSpecificCode = obj.specificCode
                    this.$nextTick(() => {
                        this.$refs.applicantRef.searchBuyerCustomerInfo(
                            isAgentTemplateSearch
                                ? ""
                                : this.templateDataObj.customerBossNo,
                            0,
                        )
                    })
                    this.$set(this.trf, "templateName", obj.templateName)
                    this.$set(this.trf, "productLineId", obj.productLineId)
                    //加载reportLanguage
                    this.queryReportLanguage()
                    this.$set(this.trf, "productLineCode", obj.productLineCode)
                    this.$set(
                        this.templateLabDataParam,
                        "productLineCode",
                        obj.productLineCode,
                    )
                    this.$set(this.trf, "productLineName", obj.productLineName)
                    //查询服务类型sel
                    this.searchServiceType(obj.productLineId)
                    if (obj.specificCode.indexOf("general_additional") != -1) {
                        this.trf.isGeneralRequest = 1
                    } else {
                        this.trf.isGeneralRequest = 0
                    }
                    if (
                        obj &&
                        obj.specificCode.indexOf("additional_for_cp") != -1
                    ) {
                        this.trf.isCpRequest = 1
                    } else {
                        this.trf.isCpRequest = 0
                    }
                    // 不存在form不显示
                    if (!validatenull(obj.dffFormId) && obj.formType - 0 != 2) {
                        let dffFormIds = []
                        dffFormIds.push(obj.dffFormId)
                        //调用接口 查询最新dffFormID
                        let comfirmData = await this.getDffComfirmIdBatchByIds(
                            dffFormIds,
                        )
                        let newDffFormId = obj.dffFormId
                        if (!this.checkObjectIsNull(comfirmData)) {
                            newDffFormId = comfirmData[0].comfirmDffFormId
                        }
                        this.$set(this.trf, "dffFormId", newDffFormId)
                        this.$set(this.dffFormObjNew, "dffFormId", newDffFormId)
                        //判断当前语言获取对应的dffForm
                        //默认查询英文
                        this.languageID = 1

                        // 不存在grid不显示
                        if (!validatenull(obj.dffGridId)) {
                            //调用接口 查询最新dffGridID
                            let dffGridIds = []
                            dffGridIds.push(obj.dffGridId)
                            let comfirmData =
                                await this.getDffComfirmIdBatchByIds(dffGridIds)
                            let newDffGridId = obj.dffGridId
                            if (!this.checkObjectIsNull(comfirmData)) {
                                newDffGridId = comfirmData[0].comfirmDffFormId
                            }
                            this.$set(this.trf, "dffGridId", newDffGridId)
                            //TODO 新版DFF
                            this.$set(
                                this.dffFormObjNew,
                                "dffGridId",
                                newDffGridId,
                            )
                            // this.getLanguageGridObjList(1);
                            this.$set(
                                this.dffFormObjNew,
                                "dffGridData",
                                this.dataList,
                            )
                            //TODO  重新加载DFF Grid
                            if (this.isDffFormLoadFlag) {
                                this.$nextTick(() => {
                                    this.$refs.frm.dffFormLoad()
                                })
                            }
                            if (!validatenull(obj.specificCode)) {
                                if (
                                    obj.specificCode.indexOf("care_Label") != -1
                                ) {
                                    this.$nextTick(() => {
                                        this.isCareLabelLoadFlag = true
                                    })
                                }
                            }
                        } else {
                            this.$set(this.dffFormObjNew, "dffGridId", "")
                        }

                        //TODO 加载最新DFF
                        this.$nextTick(() => {
                            this.isDffFormLoadFlag = true
                        })
                    }
                }
                //查询该模板下的实验室及联系人
                this.$set(
                    this.templateLabDataParam,
                    "trfTemplateId",
                    this.trf.trfTemplateId,
                )
                this.$set(
                    this.testPackageParam,
                    "templateId",
                    this.trf.trfTemplateId,
                )
                //查询模板关联的测试包
                this.searchTemplateTestPackage()
                this.searchTemplateLabData()
                //查询模板实验室联系人
                this.$set(
                    this.templateLabContactDataParam,
                    "trfTemplateId",
                    this.trf.trfTemplateId,
                )
                //模板初始化查询实验室时去掉labCode条件
                /*this.$set(this.templateLabContactDataParam, 'labCode', '');
                    this.searchTemplateLabContactData();*/
            }
            //关闭模板loading效果
            this.templateLoading = false
            // });
        },

        selectParentPackage() {
            this.dialogSelTestPackageFormVisible = true
        },
        /**
         * 切换买家Change事件方法
         * */
        selectBuyerCustomerGroupChange(val) {
            console.log(">>>>> 触发change事件", val)
            //清空关联数据
            this.trf.trfTemplateId = ""
            this.trf.templateName = ""
            this.templateData = []
            this.trf.trfLab = {}
            this.templateLabData = []
            this.$set(this.trf.trfLabContact, "labContactId", "")
            this.$set(this.trf.trfLabContact, "contactName", "")
            this.$set(this.templateDataParam, "customerBossNo", "")
            this.$set(this.templateDataParam, "customerGroupCode", "")
            this.$set(this.trf.trfCustomer, "buyerCustomerNo", "")
            this.$set(this.trf.trfCustomer, "buyerCustomerGroupName", null)
            this.$set(this.trf.trfCustomer, "buyerCustomerGroupCode", null)
            this.templateLabContactData = []
            this.selTestPackageTreedata = []
            //去除dff模板
            this.isDffFormLoadFlag = false
            this.isDffGridLoadFlag = false
            this.isCareLabelLoadFlag = false
            //清空测试包
            this.testPackageTreedata = []
            let obj = {}
            obj = this.buyerCustomerGroupData.find((item) => {
                return item.customerGroupCode === val
            })
            console.log(">>>>>buyer change事件,没有获取到obj", obj)
            this.templateCustomerId = obj ? obj.customerId : null
            //this.initCustomerExt();
            if (!objectIsNull(obj)) {
                if (obj.customerType === "customer") {
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerNo",
                        obj.bossNo,
                    )
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerName",
                        obj.customerName,
                    )
                    this.$set(
                        this.templateDataParam,
                        "customerBossNo",
                        obj.bossNo,
                    )
                } else {
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupId",
                        obj.customerGroupId,
                    )
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupCode",
                        obj.customerGroupCode,
                    )
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupName",
                        obj.customerGroupName,
                    )
                    this.$set(
                        this.templateDataParam,
                        "customerGroupCode",
                        obj.customerGroupCode,
                    )
                }
            }
            if (val == "General") {
                console.log(">>>>> general????")
                this.$set(this.trf, "buyerNo", "General")
                this.$set(this.trf.trfCustomer, "buyerCustomerGroupId", "")
                this.$set(
                    this.trf.trfCustomer,
                    "buyerCustomerGroupName",
                    "General",
                )
                this.$set(this.trf.trfCustomer, "buyerCustomerGroupCode", val)
                this.$set(this.templateDataParam, "customerGroupCode", val)
            }

            this.$set(this.buyerPkAgentParam, "buyerGroupCode", val)
            // this.$set(this.templateDataParam, 'customerGroupCode', val);
            this.$set(
                this.templateDataParam,
                "productLineCode",
                this.productLineCode,
            )
            this.$set(
                this.trfUserTemplate,
                "trfBuyerCustomerGroupCode",
                this.trf.trfCustomer.buyerCustomerGroupCode,
            )
            this.$set(
                this.trfUserTemplate,
                "trfBuyerCustomerGroupName",
                this.trf.trfCustomer.buyerCustomerGroupName,
            )
            if (
                !validatenull(this.trf.trfCustomer.agentCustomerGroupCode) &&
                !validatenull(val) &&
                validatenull(this.trf.trfCustomer.buyerCustomerNo)
            ) {
                this.$set(
                    this.buyerPkAgentParam,
                    "agentGroupCode",
                    this.trf.trfCustomer.agentCustomerGroupCode,
                )
                this.buyerPkAgent()
            } else {
                if (validatenull(val)) {
                    //无选择buyer
                    //判断是否选择agent
                    if (
                        !validatenull(
                            this.trf.trfCustomer.agentCustomerGroupCode,
                        )
                    ) {
                        this.$set(
                            this.templateDataParam,
                            "customerGroupCode",
                            this.trf.trfCustomer.agentCustomerGroupCode,
                        )
                    } else {
                        this.$set(
                            this.templateDataParam,
                            "customerGroupCode",
                            "General",
                        )
                    }
                }
                // 选择的是buyer客户
                if (!validatenull(this.trf.trfCustomer.buyerCustomerNo)) {
                    this.$set(
                        this.templateDataParam,
                        "customerBossNo",
                        this.trf.trfCustomer.buyerCustomerNo,
                    )
                }
            }
            //查询模板
            this.$set(this.templateDataParam, "trfStatus", 1)
            //开启模板loading效果
            this.templateLoading = true
            this.searchTemplateData()
        },
        initCustomerExt() {
            this.needSupplier = false
            this.needManufacture = false
            if (!this.templateCustomerId || !this.productLineCode) {
                return
            }
            let param = {
                buCode: this.productLineCode,
                customerId: this.templateCustomerId,
            }
            queryCustomerByBuCodeAndCustomerId(param)
                .then(
                    (res) => {
                        if (res.data && res.data.customerId) {
                            let {customerExtNewList} = res.data
                            let cust = customerExtNewList.find(
                                (c) => c.customerId == param.customerId,
                            )
                            if (!cust) {
                                return
                            }
                            let {needSupplier, needManufacture} = cust

                            this.needSupplier = needSupplier == 1
                            this.needManufacture = needManufacture == 1
                        }
                    },
                    (error) => {
                        this.specDisabled = []
                    },
                )
                .catch((err) => {
                    this.specDisabled = []
                })
        },
        /**
         * 切换agent Change事件方法
         * */
        selectAgentCustomerGroupChange(val) {
            //只有选择agent 存在code后才清空模板
            this.$set(this.trf.trfCustomer, "agentCustomerId", "")
            this.$set(this.trf.trfCustomer, "agentAccountId", "")
            this.$set(this.trf.trfCustomer, "agentCustomerGroupId", "")
            this.$set(this.trf.trfCustomer, "agentCustomerGroupName", "")
            this.$set(this.trf.trfCustomer, "agentCustomerGroupCode", "")
            this.$set(this.trf.trfCustomer, "agentCustomerNameEn", val)
            this.$set(this.trf.trfCustomer, "agentCustomerNameCn", "")
            this.$set(
                this.templateDataParam,
                "productLineCode",
                this.trf.productLineCode,
            )
            this.agentTipShowFlag = false
            let obj = {}
            obj = this.agentCustomerGroupData.find((item) => {
                return item.customerNameEN === val
            })
            if (!objectIsNull(obj)) {
                this.agentTipShowFlag = true
                this.$set(
                    this.trf.trfCustomer,
                    "agentCustomerId",
                    obj.customerId,
                )
                this.$set(this.trf.trfCustomer, "agentAccountId", obj.accountID)
                this.$set(
                    this.trf.trfCustomer,
                    "agentCustomerNameCn",
                    obj.customerNameCN,
                )
                this.$set(
                    this.trf.trfCustomer,
                    "agentCustomerNameEn",
                    obj.customerNameEN,
                )
                this.$set(
                    this.trf.trfCustomer,
                    "agentCustomerGroupId",
                    obj.customerGroupId,
                )
                this.$set(
                    this.trf.trfCustomer,
                    "agentCustomerGroupName",
                    obj.customerGroupName,
                )
                this.$set(
                    this.trf.trfCustomer,
                    "agentCustomerGroupCode",
                    obj.customerGroupCode,
                )
                // 2021-07-29只有选择agent 存在code后才清空模板
                this.$set(this.templateDataParam, "trfStatus", 1)
                if (
                    !validatenull(obj) &&
                    !validatenull(obj.customerGroupCode)
                ) {
                    this.$set(
                        this.buyerPkAgentParam,
                        "agentGroupCode",
                        obj.customerGroupCode,
                    )
                    if (
                        !validatenull(
                            this.trf.trfCustomer.buyerCustomerGroupCode,
                        ) &&
                        !validatenull(obj.customerGroupCode)
                    ) {
                        this.$set(
                            this.buyerPkAgentParam,
                            "buyerGroupCode",
                            this.trf.trfCustomer.buyerCustomerGroupCode,
                        )
                        this.$set(this.trf, "trfTemplateId", "")
                        this.buyerPkAgent()
                    }
                } else {
                    if (
                        !validatenull(
                            this.trf.trfCustomer.buyerCustomerGroupCode,
                        )
                    ) {
                        //清空选择的模板信息
                        this.$set(this.trf, "trfTemplateId", "")
                        //查询模板
                        this.$set(
                            this.templateDataParam,
                            "customerGroupCode",
                            this.trf.trfCustomer.buyerCustomerGroupCode,
                        )

                        this.searchTemplateData()
                    }
                }
            } else {
                if (
                    !validatenull(this.trf.trfCustomer.buyerCustomerGroupCode)
                ) {
                    //清空选择的模板信息
                    this.$set(this.trf, "trfTemplateId", "")
                    //查询模板
                    this.$set(
                        this.templateDataParam,
                        "customerGroupCode",
                        this.trf.trfCustomer.buyerCustomerGroupCode,
                    )

                    this.searchTemplateData()
                }
            }
            //选择Agent后加载agent的客户
            this.$refs.applicantRef.searchAgentCustomerInfo(
                obj,
                this.trf.trfCustomer.agentCustomerGroupCode,
            )
        },
        /**
         * 买家 & Agent PK，用于判断获取那个客户的DFF
         * */
        buyerPkAgent() {
            var params = {}
            buyerPkAgent(Object.assign(params, this.buyerPkAgentParam)).then(
                (res) => {
                    const data = res.data.data
                    //查询模板数据
                    this.$set(this.templateDataParam, "customerGroupCode", data)
                    // this.templateDataParam.customerGroupCode = data;
                    this.$set(this.templateDataParam, "trfStatus", "1")
                    this.searchTemplateData(
                        this.buyerPkAgentParam.agentGroupCode == data,
                    )
                },
            )
        },
        /**
         * 查询客户联系人
         * */
        searchCustomerContactData() {
            var params = {}
            getCustomerContactList(
                Object.assign(params, this.customerContactParam),
            ).then((res) => {
                const data = res.data.data
                this.customerContactData = data
                let obj = {}
                obj = this.customerContactData.find((item) => {
                    return item.isDefault === 1
                })
                if (!objectIsNull(obj)) {
                    //this.$set(this.trf.trfCustomerContact,'customerContactId',obj.id)
                    this.$set(
                        this.trf.trfCustomerContact,
                        "applyContactName",
                        obj.contactName,
                    )
                    this.contactrNameChange(obj.contactName)
                }
            })
        },
        /**
         * 查询客户地址信息
         * */
        searchCustomerAddressData() {
            var params = {}
            getCustomerAddressList(
                Object.assign(params, this.customerContactParam),
            ).then((res) => {
                const data = res.data.data
                this.customerAddressData = data
                let obj = {}
                obj = this.customerAddressData.find((item) => {
                    return item.isDefault === 1
                })
                if (!objectIsNull(obj)) {
                    // this.$set(this.trf.trfCustomer,'customerAddressId',obj.id);
                    this.$set(
                        this.trf.trfCustomer,
                        "customerAddressEn",
                        obj.addressDetail,
                    )
                    if (
                        this.trf.servicRequire.reportAddress == "" ||
                        this.trf.servicRequire.reportAddress == null
                    ) {
                        this.$set(
                            this.trf.servicRequire,
                            "reportAddress",
                            obj.addressDetail,
                        )
                    }
                    if (
                        this.trf.servicRequire.reportAddressEn == "" ||
                        this.trf.servicRequire.reportAddressEn == null
                    ) {
                        this.$set(
                            this.trf.servicRequire,
                            "reportAddressEn",
                            obj.addressDetail,
                        )
                    }
                }
            })
        },
        changeFixed(clientHeight) {
            //动态修改样式
            this.$refs.homePage.$el.style.height = clientHeight - 20 + "px"
        },
        //Buyer买方客户组 后期修改为查询关联登录用户相关的客户组
        queryBuyerCustomerGroupData: function (bossNo) {
            var params = {}
            this.buyerCustomerGroupParam.customerNumber = bossNo
            if (validatenull(bossNo)) {
                this.buyerCustomerGroupParam.customerGroupCode = "General"
            }
            getBuyerInfoPage(
                this.page.currentPage,
                this.page.pageSize,
                Object.assign(params, this.buyerCustomerGroupParam),
            ).then((res) => {
                var customerGroupData = res.data.rows
                if (objectIsNull(customerGroupData)) {
                    customerGroupData = []
                }
                customerGroupData.forEach((item, index, array) => {
                    if (
                        item.customerGroupCode !=
                        this.userInfo.customerGroupCode
                    ) {
                        this.buyerCustomerGroupData.push(item)
                    }
                })
                //添加公司的group信息
                var groupObj = {}
                groupObj.customerGroupId = this.userInfo.customerGroupId
                groupObj.customerGroupCode = this.userInfo.customerGroupCode
                groupObj.customerGroupName = this.userInfo.customerGroupName
                groupObj.customerType = "group"
                if (
                    groupObj.customerGroupCode != "" &&
                    groupObj.customerGroupName != ""
                ) {
                    this.buyerCustomerGroupData.push(groupObj)
                }
                var generalObj = {}
                generalObj.customerGroupId = ""
                generalObj.customerGroupCode = "General"
                generalObj.customerGroupName = "General"
                generalObj.customerType = "group"
                this.buyerCustomerGroupData.push(generalObj)
                this.page.total = res.data.records
            })
        },
        async queryNewScmCustomerList(){
            let buyerBossNo = [this.userInfo.bossNo];
            //console.log("bossNo",buyerBossNo)
            let param = {
                buyerBossNo,
                roleType: "BUYER",
                "supplierBossNo": "",
                "companyName":""
            }
            await queryScmList(param).then(res=>{
                let customerData = res.data.data;
                if (objectIsNull(customerData)) {
                    customerData=[];
                }
                customerData.forEach((item) => {
                    let {scmCustomerNameCN,scmCustomerNameEN,buyerCustomerCode,
                        customerGroupName,customerGroupCode,
                        customerNo,companyAddressCN,companyAddressEN,customerId} = item;
                    let customer = {}
                    customer.customerType="group";
                    let customerName = scmCustomerNameEN || scmCustomerNameCN;
                    if(validatenull(customerGroupCode)){//客户
                        customer.customerType="customer";
                        if(this.language==this.LanguageEnums.CN.name){
                            customerName=scmCustomerNameCN;
                        }
                        if(validatenull(customerName)){
                            customerName=scmCustomerNameEN;
                        }
                    }
                    customer.customerId = customerId;
                    customer.customerGroupCode = customerGroupCode?customerGroupCode:customerNo;
                    customer.customerGroupName = customerGroupName?customerGroupName:customerName;
                    customer.bossNo = customerNo;
                    customer.customerName = customerName
                    this.buyerCustomerGroupData.push(customer);
                })
                //默认添加自己为Buyer
                //SMART-2641 增加逻辑，如果当前用户非buyer或agent则无需追加自己
                if(!objectIsNull(this.userInfo.customerGroupCode)){
                    let groupObj = {};
                    groupObj.customerType="group";
                    if(validatenull(this.userInfo.customerGroupCode)){
                        groupObj.customerType="customer";
                    }
                    groupObj.customerGroupId = this.userInfo.customerGroupId;
                    groupObj.customerGroupCode = this.userInfo.customerGroupCode?this.userInfo.customerGroupCode:this.userInfo.bossNo;
                    groupObj.customerGroupName = this.userInfo.customerGroupName?this.userInfo.customerGroupName:this.userInfo.customerName;
                    groupObj.bossNo = this.userInfo.customerGroupCode?"":this.userInfo.bossNo;
                    groupObj.customerName = this.userInfo.customerName
                    let obj = this.buyerCustomerGroupData.find((item) => {
                        return item.customerGroupCode === groupObj.customerGroupCode;
                    });
                    if(objectIsNull(obj)){
                        this.buyerCustomerGroupData.push(groupObj);
                    }
                }

                // 默认添加general数据
                let generalObj = {};
                generalObj.customerGroupId = "";
                generalObj.customerGroupCode = "General";
                generalObj.customerGroupName = "General";
                generalObj.customerType="group";
                this.buyerCustomerGroupData.push(generalObj);
                // 默认选择General
                if(!this.trf.trfCustomer.buyerCustomerGroupCode){
                    console.log(">>>>>query scm ,赋值了general");
                    this.$set(this.trf, 'buyerNo', "General");
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupCode', "General");
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupName', "General");
                }
            })
        },
        async queryScmCustomerList(bossNo) {
            let scmCustomerReqItem = {}
            scmCustomerReqItem.buCode = this.trf.productLineCode
            scmCustomerReqItem.relationshipType = "buyer"
            scmCustomerReqItem.customerNo = bossNo
            this.scmCustomerReqParam.list.push(scmCustomerReqItem)
            await queryScmCustomerList(this.scmCustomerReqParam).then((res) => {
                let customerData = res.data.rows
                if (objectIsNull(customerData)) {
                    customerData = []
                }
                customerData.forEach((item) => {
                    let customer = {}
                    customer.customerType = "group"
                    let customerName = item.scmCustomerGroupName
                    if (validatenull(item.scmCustomerGroupCode)) {
                        //客户
                        customer.customerType = "customer"
                        if (this.language == this.LanguageEnums.CN.name) {
                            customerName = item.scmCustomerNameCN
                        }
                        if (validatenull(customerName)) {
                            customerName = item.scmCustomerNameEN
                        }
                    }
                    customer.customerId = item.customerId
                    customer.customerGroupCode = item.scmCustomerGroupCode
                        ? item.scmCustomerGroupCode
                        : item.scmCustomerNo
                    customer.customerGroupName = item.scmCustomerGroupName
                        ? item.scmCustomerGroupName
                        : customerName
                    customer.bossNo = item.scmCustomerNo
                    customer.customerName = customerName
                    this.buyerCustomerGroupData.push(customer)
                })
                //默认添加自己为Buyer
                //SMART-2641 增加逻辑，如果当前用户非buyer或agent则无需追加自己
                if (!objectIsNull(this.userInfo.customerGroupCode)) {
                    let groupObj = {}
                    groupObj.customerType = "group"
                    if (validatenull(this.userInfo.customerGroupCode)) {
                        groupObj.customerType = "customer"
                    }
                    groupObj.customerGroupId = this.userInfo.customerGroupId
                    groupObj.customerGroupCode = this.userInfo.customerGroupCode
                        ? this.userInfo.customerGroupCode
                        : this.userInfo.bossNo
                    groupObj.customerGroupName = this.userInfo.customerGroupName
                        ? this.userInfo.customerGroupName
                        : this.userInfo.customerName
                    groupObj.bossNo = this.userInfo.customerGroupCode
                        ? ""
                        : this.userInfo.bossNo
                    groupObj.customerName = this.userInfo.customerName
                    let obj = this.buyerCustomerGroupData.find((item) => {
                        return (
                            item.customerGroupCode ===
                            groupObj.customerGroupCode
                        )
                    })
                    if (objectIsNull(obj)) {
                        this.buyerCustomerGroupData.push(groupObj)
                    }
                }

                // 默认添加general数据
                let generalObj = {}
                generalObj.customerGroupId = ""
                generalObj.customerGroupCode = "General"
                generalObj.customerGroupName = "General"
                generalObj.customerType = "group"
                this.buyerCustomerGroupData.push(generalObj)
                // 默认选择General
                if (!this.trf.trfCustomer.buyerCustomerGroupCode) {
                    console.log(">>>>>query scm ,赋值了general")
                    this.$set(this.trf, "buyerNo", "General")
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupCode",
                        "General",
                    )
                    this.$set(
                        this.trf.trfCustomer,
                        "buyerCustomerGroupName",
                        "General",
                    )
                }
            })
        },
        //代理商客户组查询 输入3个字符后再执行查询匹配 最大查询五条数据，防止全部客户组暴露
        searchAgentCustomerGroup(val) {
            //this.customerGroupParam.groupName = val;

            this.$set(this.agentCustomerParam, "customerName", val)
            this.$set(this.agentCustomerParam, "rows", 5)
            if (val.length >= 3) {
                //输入满足三个字符后查询客户组数据
                this.queryAgentCustomerGroupData()
            }
        },
        /**
         * 查询Agent客户数据
         * */
        queryAgentCustomerGroupData() {
            var params = {}
            getAgentCustomerGroupByParms(
                Object.assign(params, this.agentCustomerParam),
            ).then((res) => {
                const data = res.data.data
                this.agentCustomerGroupData = data
            })
        },
        getTreeIds: function (arr, ids = []) {
            arr.forEach(({id, children}) => {
                if (id) {
                    ids.push(id)
                }
                if (children) {
                    this.getTreeIds(children, ids)
                }
            })
            return ids
        },

        handleCloseTemplate() {
            this.$nextTick(() => {
                setTimeout(() => {
                    this.addSelectListen()
                    this.computedProgress()
                    this.firstLoad = false
                }, 600)
            })
        },
        onChangeDeliveredTo(emails) {
            this.trf.reportDeliveredTo = objectIsNull(emails)
                ? ""
                : emails.join(",")
        },

        onChangeFailDeliveredTo(emails) {
            this.trf.failedReportDeliveredTo = objectIsNull(emails)
                ? ""
                : emails.join(",")
        },

        updateMenus(menus) {
            if (!menus || menus.length <= 0) return
            let source = this.navList[this.navList.length - 1]
            let level = parseInt(source.level)
            menus.forEach((item) => {
                let nav = JSON.stringify(source)
                nav = JSON.parse(nav)
                nav = Object.assign(Object.assign(nav, item), {
                    level: ++level + "",
                })
                this.navList.push(nav)
            })
        },
        clearExtendMenus() {
            this.navList = this.navList.filter((item) => !item.isExtend)
        },

        updateTrfApplicant(customer) {
            this.trf.applicantUser = customer.authUserId
            this.identicalFlag = false
        },
        formChangeValue({value, column, item, dic}) {
            //console.log("form 表单值改变，触发change事件",value,column,item,dic);
            let {required} = column
            if (required) {
                setTimeout((value, item, dic) => {
                    this.computedProgress()
                }, 600)
            }
        },
        showDynamicNavList(flag) {
            this.navList.forEach((nav) => {
                if ("trf.dynamicForm" == nav.alias) {
                    nav.hide = !flag
                }
                if ("trf.productAndSample" == nav.alias) {
                    nav.hide = flag
                }
            })
        },
        updateModifyBasic(name) {
            //修改basic informartion
            let {labCode} = this.trf.trfLab
            if (!labCode) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("trf.trfValidateError"),
                    type: "warning",
                })
                return
            }
            let param = {
                id: this.trf.id,
                serviceType: this.trf.serviceType,
                trfLab: this.trf.trfLab,
                trfLabContact: this.trf.trfLabContact,
            }
            this.$set(this, name + "SaveLoading", true)
            updateBasicData(param)
                .then((res) => {
                    this.$set(this, name + "ModifyDisabled", true)
                    this.$set(this, name + "SaveLoading", false)
                    if (res.status == 200 && res.data.status == 200) {
                        this.$notify.success(this.$t("api.success"))
                    } else {
                        this.$notify.success(
                            res.data.msg || this.$t("api.error"),
                        )
                    }
                })
                .catch((err) => {
                    this.$notify.success(this.$t("api.error"))
                })
        },
        showModifyBtn(btnAuthName) {
            let statusVlid = [3, 4].includes(this.trf.trfStatus - 0)
            if (!statusVlid) {
                return false
            }
            let customerField = btnAuthName + "ModifyBtn"
            return this.permissionList[customerField]
        },
        disabledComonent(btnAuthName) {
            if (this.permissionList[btnAuthName + "ModifyBtn"]) {
                return this.trfDisabled && this[btnAuthName + "ModifyDisabled"]
            }
            return this.trfDisabled
        },
        openHKCMS() {
            let lan = 0
            let customer =
                this.$refs.applicantRef.customerForm.applicationCustomer
            let companyName =
                customer.customerNameEn || customer.customerNameZh || ""
            let templateLab =
                this.templateLabData.find(
                    (value) => value.labCode == this.trf.trfLab.labCode,
                ) || {}
            let lab = templateLab.labName || templateLab.labNameCn || ""
            let address =
                templateLab.labAddress || templateLab.labAddressCn || ""
            let customerAddress = customer.customerAddressEn || customer.customerAddress || ""
            if (this.language == "zh-CN") {
                //给到CMS的 英文是0 中文是1
                lan = 1
                companyName =
                    customer.customerNameZh || customer.customerNameEn || ""
                lab = templateLab.labNameCn || templateLab.labName || ""
                address =
                    templateLab.labAddressCn || templateLab.labAddress || ""
                customerAddress = customer.customerAddress || customer.customerAddressEn || ""
            }
            let url = `https://cms-uat.sgsonline.com.cn/CMS_UAT/WebPage/PickUp?companyName=${companyName}&address=${customerAddress}&lab=${lab}&lan=${lan}`
            if (this.env == "prod") {
                url = `http://cms.sgsonline.com.cn/CMS/WebPage/PickUp?companyName=${companyName}&address=${customerAddress}&lab=${lab}&lan=${lan}`
            }
            window.open(url, "_blank")
        },
    },
}
</script>
<style>
.activeTrf::placeholder {
    color: #ffffff !important;
}

.disabledTrf::placeholder {
    color: transparent !important;
}
</style>
<style lang="scss">
.test-title {
    height: 40px;
    line-height: 40px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    button {
        margin-top: -5px;
    }
}
.tree {
    table,
    .el-table__empty-block {
        width: 100% !important;
    }
    .el-tree-node {
        white-space: normal;

        .el-tree-node__content {
            height: 100%;
            align-items: start;
        }
    }
}

.el-steps--simple {
    height: 50px;
    background: #424242;

    .el-step {
        cursor: pointer;

        i {
            color: #ffffff;
        }

        .el-step__title {
            color: #ffffff;
            font-size: 12px;
        }
    }
}

.modal {
    z-index: 2000;
}

.contact {
    table {
        th {
            padding: 5px;
            text-align: center;
        }

        td {
            padding: 5px 20px;
        }
    }
}

#dff-grid {
    .tools {
        .sgs_btn {
            border-radius: 0;
        }
        > span {
            padding-top: 7px;
        }
    }

    .grid_table {
        thead {
            tr {
                white-space: nowrap;
            }
        }
    }
}

#productSampleHeaderForm {
    label {
        font-weight: normal !important;
        /* white-space: nowrap; */
        line-height: 1.5;
    }
    #productSampleHeadNew .el-form-item__label {
        width: 100% !important;
        &:before {
            margin-right: 0 !important;
        }
    }

    .el-form-item {
        margin-bottom: 8px;
    }

    .el-form-item__label {
        width: 260px !important;
    }

    #productSampleHeadNew .el-form-item__error {
        top: 96% !important;
    }

    .el-form-item__content {
        margin-left: 0px !important;
    }

    .form-horizontal {
        padding-left: 10px;
        padding-right: 10px;
    }

    .textareaHead textarea {
        height: 36px;
        margin-top: 0px !important;
        border: none;
        border-bottom: 1px solid #1b1b1b;
        border-radius: 0;
    }

    .el-date-editor.el-input {
        .el-input__inner {
            padding-left: 30px;
        }
    }

    .row {
        margin-right: -24px;
        margin-left: -24px;
    }
}

.grid_validation {
    color: #f56c6c !important;
    text-align: left !important;
    font-size: 12px !important;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    text-align: left;
}

.sgs-group {
    h3 {
        /* border-bottom: 1px solid #dfdfdf; */
        /* padding-bottom: 20px; */
        margin-bottom: 10px;
    }
}

.show-more {
    height: 190px;
    overflow: hidden;
}
.show-more-btn {
    color: #f60;
    padding-bottom: 3px;
    border-bottom: 1px solid #f60;
    font-weight: bold;
    cursor: pointer;
}
.nav-list {
    list-style: none;
    margin-top: 24px;
    padding-left: 0;
    li {
        cursor: pointer;
        border-left: 3px solid #d9d9d9;
        padding-left: 24px;
        font-size: 16px;
        font-weight: 400;
        color: #000000;
        &.is-sub {
            padding-left: 40px;
            font-size: 16px;
            font-weight: 400;
            color: #656565;
            h5 {
                font-weight: normal;
            }
        }
        &.active {
            color: #f60;
            border-color: #f60;
        }
        h5 {
            padding: 10px 0;
            margin: 0;
        }
    }
}
.add-comment-title {
    margin: 26px 0 30px;
    font-size: 16px;
}
.scroll-box {
    /* height: 60vh; */
    .inner-scroll {
        padding-left: 3px;
        height: 60vh;
        overflow: auto;
        &::-webkit-scrollbar {
            width: 0 !important;
        }
    }
}
.add-comment {
    padding-left: 36px;
    position: relative;
    &::after {
        content: " ";
        width: 20px;
        height: 20px;
        border-radius: 50%;
        top: 1px;
        background: #f60;
        position: absolute;
        left: 3px;
    }
}
.comment-list {
    width: calc(100% - 9px);
    border-left: 3px solid #ccc;
    padding-left: 20px;
    padding-top: 30px;
    dt {
    }
    dd {
        position: relative;
        margin-bottom: 20px;
        box-shadow: 0 15px 30px rgba(204, 204, 204, 0.2);
        .comment {
            margin-bottom: 20px;
            font-weight: 600;
            word-break: break-word;
        }
        &::after {
            content: " ";
            position: absolute;
            left: -28px;
            top: 19px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #f60;
            background: #fff;
        }
        &:last-of-type {
            margin-bottom: 0;
        }
        .c-info {
            p {
                word-break: break-all;
            }
        }
    }
}
.trf-detail .el-table th {
    background: #f5f5f5 !important;
    font-size: 14px;
    font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
        "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    // font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000 !important;
}
.el-textarea__inner {
    border-radius: 0 !important;
    border-color: #999 !important;
    min-height: 36px !important;
}
.upload-btn {
    width: 100%;
    border: 1px dashed;
    height: 64px;
    margin-top: 8px;
}
.sgs_smart_trf_trf_detail {
    .el-card__body {
        padding: 26px 32px 32px !important;
        .sgs-group {
            h3 {
                margin-bottom: 32px;
            }
        }
        .el-form-item__label {
            padding-left: 12px;
            font-size: 12px;
            color: #999;
        }
    }
}
.form-progress {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 22px;
    em {
        font-style: normal;
        color: #f60;
    }
}
.placeholder-remark {
    .el-input__inner {
        text-indent: -999px;
    }
}
.no-data {
    padding-top: 80px;
    p {
        margin-top: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #656565;
        line-height: 22px;
    }
}
.tree-left,
.tree-right {
    border: 1px solid #d9d9d9;
    padding: 10px;
}
.tree-right {
    .trf-form-table-dialog {
        &::before {
            height: 0;
        }
        .el-table__header-wrapper {
            display: none;
        }
        td {
            border: 0;
            padding: 0;
        }
    }
}
.num-top {
    height: 40px;
    padding: 0 16px;
    background: #f5f5f5;
    font-size: 14px;
    // font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #000000;
    line-height: 40px;
    margin: 0;
}
.clear-all-tree {
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    color: #656565;
    img {
        vertical-align: sub;
    }
}
#grid_table_,
#productSampleHeaderForm .el-form-item__label {
    table {
        th {
            font-size: 12px !important;
            color: #999 !important;
        }
    }
}
.template-lable {
    .el-form-item {
        .el-form-item__label {
            width: 100% !important;
        }
    }
}
.tree-dialog {
    .el-dialog__body {
        display: none;
    }
}
.max-line {
    display: inline-block;
    max-width: 380px;
    overflow: hidden;
    text-overflow: ellipsis;
}
.el-tooltip__popper {
    max-width: 400px;
}
.el-notification {
    z-index: 3000 !important;
}
.sgs_portlet {
    .el-button--danger {
        color: #ffffff;
        background-color: #fd6602;
        border-color: #fd6602;
    }
}
.productSample_DFFGrid .el-table th {
    background: #ffffff !important;
    .cell {
        text-transform: capitalize !important;
        font-size: 12px;
        color: #999;
        font-family: Arial, "localArial", "\601D\6E90\9ED1\4F53",
            "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC",
            "WenQuanYi Micro Hei", sans-serif !important;
    }
}
.form-horizontal .control-label {
    color: #999;
    text-align: left !important;
    padding-top: 0px !important;
    line-height: 35px;
}

#VFormDesignrender {
    .avue-dynamic
        table.el-table__body
        .el-table__row
        td
        .el-form-item.is-required
        label.el-form-item__label {
        z-index: 9 !important;
    }
    .avue-dynamic table.el-table__body .el-table__row td .el-checkbox-group,
    .avue-dynamic table.el-table__body .el-table__row td .el-radio-group {
        display: flex !important;
        flex-wrap: wrap !important;
        label {
            word-break: break-all !important;
            white-space: break-spaces !important;
            span.el-checkbox__label {
                display: inline !important;
            }
        }
    }
}
</style>
