<template>
  <div>
    <h1 class="top-title">{{ $t('navbar.accountPer') }}</h1>
    <el-card shadow="never" class="box-card">
    <el-row>
        <el-form :inline="true" :model="query" size="medium">
            <el-form-item :label="$t('customer.name')">
                <el-input v-model="query.name" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
            </el-form-item>
        </el-form>
    </el-row>
     <el-row>
        <el-table
            :data="customerDatas"
            v-loading="tableLoading"
            style="width: 100%" >
            <el-table-column
                    type="index"
                    fixed
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    fixed
                    :show-overflow-tooltip='true'
                    prop="name"
                    :label="$t('customer.name')">
            </el-table-column>          
            <el-table-column
                      :label="$t('operation.title')"
                      width="150"
                      align="center">
                  <template slot-scope="scope">
                      <el-button  @click="accountAuth(scope.row)" type="text">{{$t('accountPer.title')}}</el-button>
                  </template>
              </el-table-column>
          </el-table>
          <el-pagination
                  @size-change="sizeChange"
                  @current-change="currentChange"
                  :current-page="page.currentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="page.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="page.total">
          </el-pagination>
      </el-row>
      <!-- <el-dialog
                title="账号权限管理"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :visible.sync="accountPerDialogVisible"
                width="800"
                append-to-body>
          <el-form :model="accountPerForm" label-width="200px" label-position="left"  size="medium" class="sgs-form" :rules="languageRules">
            <el-form-item label="账号选择" prop="account" >
              <el-select filterable clearable v-model="accountPerForm.userAccountId" style="width:100%"
                        :placeholder="$t('operation.pleaseSelect')"
                        :no-data-text="$t('NoData')" clearable>
                <el-option v-for="(account,index) in accountDatas" :label="account.name"
                          :value="account.code"></el-option>
                      
              </el-select>
            </el-form-item>

           
            <el-form-item label="客户组" prop="customerGroupCode" >
              <el-select filterable clearable v-model="accountPerForm.customerGroupCode" style="width:100%"
                        :placeholder="$t('operation.pleaseSelect')"
                        :no-data-text="$t('NoData')" clearable>
                <el-option v-for="(customerGroup,index) in customerGroupData" :label="customerGroup.customerGroupName"
                          :value="customerGroup.customerGroupCode"></el-option>
                      
              </el-select>
            </el-form-item>

            <el-form-item label="客户" prop="customerGroupCode" >
              <el-select filterable clearable v-model="accountPerForm.customerNo" style="width:100%"
                        :placeholder="$t('operation.pleaseSelect')"
                        :no-data-text="$t('NoData')" clearable>
                <el-option v-for="(account,index) in accountDatas" :label="account.name"
                          :value="account.code"></el-option>
                      
              </el-select>
            </el-form-item>



          </el-form>

      </el-dialog>       -->


    </el-card>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import {queryCompanyListByPage} from "@/api/user/accountPer";
import {validatenull,objectIsNull} from "@/util/validate";
import {
  getCustomerGroup,
  getCustomerGroupByParms
} from "@/api/common/index";
export default {
  name: "accountPer",
  components: {
  },
  sizeChange(){
    this.onLoad();
  },
  currentChange(){
    debugger;
    this.onLoad();
  },
  created() {
    this.onSearch();
    //查询客户组数据
    //this.queryCustomerGroupData();
  },
  data() {
    return {
      customerGroupData:[],
      accountPerDialogVisible:false,
      accountDatas:[],
      accountQuery:{},
      accountPerForm:{
        userAccountId:'',
        userAccountName:'',
        customerGroupCode:'',
        customerGroupName:'',
        userCompanyId:'',
        userCompanyName:'',
        smartCustomerId:'',
        bossNo:''
      },
      tableLoading:false,
      customerDatas:[],//列表数据
      page: {
            pageSize: 10,
            currentPage: 1,
            total: 0
        },
        query: {
          page:1,
          rows:10,
          customerNo:'',
          name:'',
          executeSystemCustomerNo:'',
          companyId:''
        }
    }
  },
  computed: {
    ...mapGetters(["userInfo","permission"]),
    permissionList() {
      return {
  
      };
    }
  },
  watch: {},
  methods: {
    //account授权
    // accountAuth(row){
    //   debugger;
    //     if(!objectIsNull(row)){
    //         //查询account
    //         this.queryAccountByCompanyId(row.companyId);
           
    //         this.accountPerDialogVisible = true;
    //     }
    // },
    accountAuth(row) {
      this.selectRow = row;
      // this.$router.push("{ path: /document/authorization,query: { documentId: row.id }}");
      this.$router.push({path: "/accountPer/authority", query: {"companyId": row.id}});
    },
    // queryAccountByCompanyId(companyId){
    //   if(!objectIsNull(companyId)){
    //     this.accountQuery.page = 1;
    //     this.accountQuery.rows = 500;
    //     this.accountQuery.companyId = companyId;
    //     getUsers(this.accountQuery).then(res => {
    //         this.accountDatas = res.data.rows;
    //     });
    //   }
    // },
    // queryCustomerGroupData(){
    //   getCustomerGroup().then(res => {
    //       this.customerGroupData = res.data.data;
    //   });
    // },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
      this.onLoad();
    },
    currentChange(pageCurrent){
        this.page.currentPage = pageCurrent;
        this.onLoad();
    },
    onSearch() {
        this.page.currentPage=1;
        this.onLoad(this.page);
    },
    onLoad(){
      this.tableLoading=true;
      debugger;
      this.query.page=this.page.currentPage;
      this.query.rows=this.page.pageSize;
      queryCompanyListByPage(this.query).then(res => {
        this.tableLoading=false;
        debugger;
        let tableData = res.data.data.rows;
        this.page.total= res.data.data.records;
        this.customerDatas =tableData;
      }).catch(error=>{
        this.tableLoading=false
      });
    },


  },
};
</script>
