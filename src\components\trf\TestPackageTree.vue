<template>
  <!--<table width="100%" border="1">
    <tbody v-for="item in arrays">
    <tr v-for="(zItem,zIndex) in $lodash.chunk(item,2)">
      <td width="50%" v-for="(children,cIndex) in zItem">
        <span style="margin: 5px" class="c-index  c-index-hot1 c-gap-icon-right-small">{{item.index++}}</span>{{children.testPackageName}}
      </td>
    </tr>
    <tr>
      <td colspan="2">&nbsp;</td>
    </tr>
    </tbody>
  </table>-->

  <ul style="list-style-type: none;">
    <li v-for="item in arrays">
      {{item.testPackageName}}
      <test-package-tree :arrays='item.children'></test-package-tree>
    </li>
  </ul>
</template>

<script>
    export default {
      name:'testPackageTree',
      props:['arrays'],
      data(){
        return {

        }
      }
    }
</script>

<style scoped>

</style>
