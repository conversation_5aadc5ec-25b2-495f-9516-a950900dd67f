<template>
  <basic-container>
  <h3 class="top-title">{{$t('authority.authorityManage')}}</h3>
    <el-card shadow="always">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="wrap-tab">
        <!-- 我的授权-->
        <el-tab-pane name="myAuthority"  :label="$t('authority.myAuthority')">
            <my-authority v-if="tabRefresh.myAuthority"></my-authority>
        </el-tab-pane>
      <!-- 我的申请-->
        <el-tab-pane name="myApplication"  :label="$t('authority.myApplication')" :lazy="true">
          <my-application  v-if="tabRefresh.myApplication"></my-application>
        </el-tab-pane>
    </el-tabs>
    </el-card>
  </basic-container>
</template>
<script>
import {mapGetters} from "vuex";
import {objectIsNull} from "@/util/validate";

export default {
  components: {
    myAuthority: resolve => require(['./myAuthority'], resolve),
    myApplication: resolve => require(['./myApplication'], resolve)
  },
  data() {
    return {
      name: "authority Manage",
      activeName: "myAuthority",
      tabRefresh: {
        myAuthority: true,
        myApplication: false,
      },
    }
  },
  created() {
    debugger;
    this.activeName = "myAuthority";
    //获取请求url的flag标识，判断是否为申请页面
    const flag = this.$route.query.flag;
    if(!objectIsNull(flag)){
      if(flag==='1'){
        this.activeName = "myApplication";
        this.tabRefresh.myApplication=true;
      }
    }

  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
  },
  methods: {
    handleClick(tab) {
      Object.keys(this.tabRefresh).forEach(item => {
        this.tabRefresh[item] = false;
      })
      this.tabRefresh[tab.name] = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.wrap-tab {
    ::v-deep .el-tabs__nav-wrap {
        &::after {
            position: inherit;
        }
    }
    ::v-deep .el-tabs__content {
        margin-top: -58px;
    }
}
@import '../tab.scss';
</style>