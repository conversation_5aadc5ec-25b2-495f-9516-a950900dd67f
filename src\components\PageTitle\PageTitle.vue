<template>
  <div class="list-header">
    <div class="list-header-title">{{ title }}</div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'

defineProps<{
  title: string
}>()
</script>

<style scoped lang="scss">
@use '@/assets/style/unit.module.scss' as *;

.list-header-title {
  font-size: 20px;
  font-weight: 500;
  padding-bottom: $module-padding-vertical;
}
</style>
