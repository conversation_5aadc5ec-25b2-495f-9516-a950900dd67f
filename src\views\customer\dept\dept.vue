<template>
    <div>
        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
            <el-form-item>
                <el-input
                    @input="onSearch"
                    @keyup.enter.native="onSearch"
                    @clear="onSearch"
                    v-model="query.departmentName"
                    :placeholder="$t('department.nameFull')"
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <!-- <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button> -->
                <!-- <el-button v-if="permissionList.addBtn" type="primary" @click="addRow">{{$t('operation.add')}}
                </el-button> -->

                <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
        <el-table ref=deptTable
                  :data="tableData"
                  :row-key="getRowKeys"
                  style="width: 100%"
                  size="medium"
        >
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <!--<el-table-column type="expand">
                <template slot-scope="props">
                    <el-table :data="beLongTableData" v-loading="beLongloading" :element-loading-text="$t('loading')"
                              style="width: 100%">
                        <el-table-column
                                prop="beLongName"
                                :label="$t('beLongTo.title')"
                                width="200"
                        >
                        </el-table-column>

                    </el-table>
                </template>
            </el-table-column>-->


            <el-table-column
                    prop="departmentName"
                    :label="$t('department.name')">
            </el-table-column>

            <el-table-column
                    prop="beLongs"
                    :label="$t('beLongTo.title')">
                <template slot-scope="scope">
                    <el-tag size="small"
                            v-for="beLong in scope.row.beLongs"
                            >{{beLong.beLongName}}
                    </el-tag>
                </template>
            </el-table-column>


            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')"
                    width="160"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="updateUser"
                    :label="$t('common.operator')"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="status"
                    :label="$t('common.status.title')"
                    width="80"
                    align="center">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                placement="top">
                        <el-switch
                                v-model="scope.row.status"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9FF"
                                :active-value="1"
                                :inactive-value="0"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="280"
                    align="center">
                <template slot-scope="scope">
                   <!-- <el-button type="text" @click="addBeLongTo(scope.row)" size="small"
                               icon="el-icon-add">{{$t('beLongTo.add')}}
                    </el-button>-->
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)">
                        {{$t('operation.edit')}}
                    </el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text">
                        {{$t('operation.remove')}}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog :title="title" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
            <el-form ref="form" :model="form" label-width="200px" label-position="left" class="sgs-form" size="medium">
                <el-form-item :label="$t('department.name')"
                              :rules="{ required: true, message: this.$t('department.nameBlur'), trigger: 'blur' }"
                              prop="departmentName">
                    <el-input
                            maxlength="150"
                            v-model="form.departmentName"></el-input>
                </el-form-item>
                <el-form-item :label="$t('beLongTo.title')">
                    <el-select v-model="beLongIds" :placeholder="$t('beLongTo.title')" multiple
                               @change="selectBeLongChange" reserve-keyword filterable style="width: 100%;" clearable>
                        <el-option v-for="(beLang,index) in beLongData" :label="beLang.beLongName"
                                   :value="beLang.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="bottom clearfix " style="text-align: center;margin-top:100px">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="submitForm('form')" :loading="submitLoading">
                    {{$t('operation.submit')}}
                </el-button>
            </div>
        </el-dialog>
        <el-dialog :before-close="closeBeLongToDrawer" :title="beLongToTitle" :visible.sync="dialogBeLongToFormVisible" :close-on-click-modal="false"
                   size="60%">
            <el-form :model="beLongForm" :rules="contactRules" label-position="left" ref="beLongToForm"
                     label-width="200px" class="sgs-form">
                <el-form-item :label="$t('beLongTo.title')">
                    <el-select v-model="beLongIds" :placeholder="$t('beLongTo.title')" multiple
                               @change="selectBeLongChange" reserve-keyword filterable style="width: 100%;" clearable>
                        <el-option v-for="(beLang,index) in beLongData" :label="beLang.beLongName"
                                   :value="beLang.id"></el-option>
                    </el-select>
                </el-form-item>
                <div class="sgs-bottom">
                    <el-button @click="closeBeLongToDrawer" :disabled="isBeLongToDisabled">{{$t('operation.cancel')}}
                    </el-button>
                    <el-button type="primary" @click="submitBeLongToForm('beLongToForm')"
                               :disabled="isBeLongToDisabled">
                        {{$t('operation.confirm')}}
                    </el-button>
                </div>
            </el-form>

        </el-dialog>
    </div>
</template>

<script>
    import {getPageByUser, add, remove, detail, updateDeptStatus} from "@/api/customer/customerDept";
    import {addDeptBeLongTos, getDeptBeLongTos} from "@/api/customer/customerDeptBeLongTo";
    import {getBeLongs} from "@/api/customer/beLong";
    import {mapGetters} from "vuex";
    import {validatenull} from "../../../util/validate";

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        data() {
            return {
                isBeLongToDisabled: false,
                beLongToTitle: this.$t('operation.add'),
                dialogBeLongToFormVisible: false,
                beLongloading: false,
                beLongData: [],
                beLongTableData: [],
                beLongIds: [],
                getRowKeys(row) {
                    return row.id
                },
                expands: [],
                currentRow: {},
                name: "dept",
                title: '',
                dialogFormVisible: false,
                submitLoading: false,
                tableData: [],
                form: {departmentName: '', beLongs: [],},
                beLongForm: {
                    deptId: '',
                    beLongs: [],
                },
                query: {},
                sort: {descs: 'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                contactRules: {}
            }
        },
        computed: {
            ...mapGetters(["permission", "userInfo"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:dept:add'], false),
                    editBtn: this.vaildData(this.permission['sgs:customer:dept:edit'], false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:dept:delete'], false),
                };
            }
        },
        methods: {
            selectBeLongChange(val) {
                this.beLongForm.beLongs = [];
                this.form.beLongs = [];
                //为所选的部门添加beLong
                val.find((selBeLong) => {
                    let newBeLong = {};
                    this.beLongData.find((beLong) => {
                        if (selBeLong == beLong.id) {
                            newBeLong.beLongName = beLong.beLongName;
                            newBeLong.beLongCode = beLong.beLongCode;
                            newBeLong.beLongId = beLong.id;
                        }
                    });
                    this.beLongForm.beLongs.push(newBeLong);
                    this.form.beLongs.push(newBeLong);
                });
            },
            closeBeLongToDrawer(done) {
                this.$refs['beLongToForm'].resetFields();
                this.dialogBeLongToFormVisible = false;
            },
            deptBeLongChange(row, expandedRows) {
                this.$refs.deptTable.setCurrentRow();
                this.currentRow = row
                if (this.expands.join(',').indexOf(row.id) === -1) {
                    this.expands = [this.currentRow.id]
                    this.searchBeLongList(row.id);
                } else {
                    this.expands.splice(0, this.expands.length)
                }
                /* if(vlaue2.length!=0){//展开
                     this.searchLabContactList(value1.id);
                 }*/
            },
            addBeLongTo(row) {
                this.beLongForm.deptId = row.id;
                this.dialogBeLongToFormVisible = true;
            },
            submitBeLongToForm() {
                this.isBeLongToDisabled = true;
                //校验
                this.$refs['beLongToForm'].validate((valid) => {
                    if (valid) {
                        addDeptBeLongTos(this.beLongForm.beLongs).then(() => {
                            this.dialogBeLongToFormVisible = false;
                            this.isBeLongToDisabled = false;
                            //this.onLoad(this.page);
                            //this.searchLabContactList(this.labContactForm.labId);
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                        }, error => {
                            this.isBeLongToDisabled = false;
                            this.$message.error(this.$t('api.error'));
                            console.log(error);
                        });
                    } else {
                        this.isBeLongToDisabled = false;
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            searchBeLongList(deptId) {
                //根据部门ID查询BeLong信息
                getDeptBeLongTos(deptId).then(res => {
                    const data = res.data.data;
                    this.beLongTableData = data;
                });
            },
            onSearch() {
                 this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.submitLoading = true;
                        add(this.form).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.submitLoading = false;
                            this.dialogFormVisible = false;
                            this.onLoad(this.page);
                        }, error => {
                            this.submitLoading = false;
                        });
                    } else {
                        return false;
                    }
                });

            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                    .then(() => {
                        remove(row.id).then(() => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.onLoad(this.page);
                        });
                    })
            },
            detailRow(row) {
                this.title = this.$t('department.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form = res.data.data;
                    //加载belong回显
                    this.beLongIds = [];
                    var beLongs = this.form.beLongs;
                    if (!validatenull(beLongs)) {
                        beLongs.find((beLong) => {
                            this.beLongIds.push(beLong.beLongId);
                        });
                        console.log(this.beLongIds);
                    }
                });

            },
            addRow() {
                this.form = {};
                this.beLongIds = [];
                this.title = this.$t('department.title.add');
                this.dialogFormVisible = true;
            },
            changeStatus(row) {
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                updateDeptStatus(modifiedForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            loadBeLongs() {
                const customerId = this.userInfo.companyId;
                getBeLongs(customerId).then(res => {
                    const data = res.data.data;
                    this.beLongData = data;
                });
            },
        },
        created() {
            this.onLoad(this.page);
            this.loadBeLongs();
        },
    }
</script>

<style scoped>

</style>
