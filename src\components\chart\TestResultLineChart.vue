<template>
  <div id="main" style="width: 100%; min-height: 400px" v-loading="lineLoading"></div>
</template>
<script>
import chart from './index.vue'
import i18n from "@/lang";
import moment from "moment";
import _ from "lodash";
import {reportLineOptions} from '@/commons/echart/trf/reportLine';

let color = ["#34A853", "#EA4336", "#D9D9D9"]

export default {
  name: 'TestResultLineChart',
  props: {
    orderSum: {
      type: Array,
      default: []
    },
    startDateY: {
      type: String,
      default: null
    },
    endDateY: {
      type: String,
      default: null
    }
  },
  components: {
    chart
  },
  data() {
    return {
      lineLoading: true,
      chartOptions: reportLineOptions
    }
  },
  mounted() {
    let that = this;
    this.$nextTick(() => {
      const timer = setInterval(() => {
          window.clearInterval(timer)
          that.loadEchartsData();
      }, 300)
    })
  },
  watch: {
    orderSum: {
      deep:true,
      handler:function (newVal,oldValue){
        this.loadEchartsData();
      }
    },
    "$i18n.locale": function () {
      this.loadEchartsData();
    },
    deep:true
  },
  methods: {
    loadEchartsData() {
      console.log('line pic data ==>', this.orderSum)
      let myChart = echarts.init(document.getElementById("main"));
      this.$once("hook:beforeDestroy", function () {
        myChart.clear();
      });

      let monthData = [];
      for(let i = 0; i <= 12; i++) {
        monthData.push(moment(this.startDateY).add(i, "M").format("YYYY/MM"));
      }

      this.chartOptions.legend.data = [];
      this.chartOptions.legend.languageKey.forEach(key => {
        this.chartOptions.legend.data.push(i18n.t(key));
      });

      this.chartOptions.yAxis.name = `(${i18n.t(this.chartOptions.yAxis.languageKey)})`;
      this.chartOptions.xAxis.data = monthData;
      this.chartOptions.xAxis.minInterval = 1;
      this.chartOptions.series.forEach(ser => {
        ser.data = [];
        monthData.map((item) => {
          let filterArray = _.filter(this.orderSum, { month: item });
          let val = _.result(_.findLast(filterArray, (find) => find.overallResult === ser.code), "countVal", 0);
          ser.name = i18n.t(ser.languageKey);
          ser.data.push(val);
        });
      });
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(this.chartOptions);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
      this.lineLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.test-result-chart {
  position: relative;
  top: 95px;
  .el-radio-group {
    position: absolute;
    right: 0;
    top: -41px;
  }
}
</style>
