<template>
  <basic-container>
    <avue-crud :option="option"
               :data="data"
               ref="crud"
               v-model="form"
               :page="page"
               :permission="permissionList"
               @row-del="rowDel"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="scope" slot="menuLeft">
        <!--<el-button type="danger"
                   icon="el-icon-plus"
                   size="small"
                   plain
                   @click.stop="showFormDesign()">新 增</el-button>-->
        <el-button type="danger"
                   icon="el-icon-plus"
                   size="small"
                   plain
                   @click.stop="showFormDesignGlobal()">新增</el-button>
       <!-- <el-button type="danger"
                   icon="el-icon-plus"
                   size="small"
                   plain
                   @click.stop="previewFormDesignGlobal">预览</el-button>-->
      </template>
      <template slot-scope="scope" slot="menu">
       <!-- <el-button type="text"
                   size="small"
                   icon="el-icon-edit"
                   v-if="permissionList.designBtn"
                   @click="showFormDesign(scope.row)">设 计
        </el-button>-->
        <el-button type="text"
                   size="small"
                   icon="el-icon-edit"
                   v-if="permissionList.designBtn"
                   @click="showFormDesignGlobal(scope.row)">设计
        </el-button>
        <el-button type="text"
                   size="small"
                   icon="el-icon-setting"
                   v-if="permissionList.manageBtn"
                   @click="toDetail(scope.row)">管 理
        </el-button>
      </template>
    </avue-crud>
    <el-dialog
            title="表单设计"
            :visible.sync="designDialogVisible"
            width="90%"
            top="10px"
            :before-close="handleClose">
      老的设计器暂时不可用
      <!--<nkop-form-design :options="formOptions"
                        ref="formdesign"
                        @submit="handleSave"></nkop-form-design>-->
    </el-dialog>
    <el-dialog
            title="表单设计"
            :visible.sync="globalDesignDialogVisible"
            fullscreen
            top="10px"
            :before-close="handleClose">
      <sgs-form-design
                v-if="globalDesignDialogVisible"
                :options="globalFormOptions"
                 :base-req-domain-url="formRender.baseReqDomainUrl"
                  ref="globalformdesign"
                  :need-save="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                @submit="handleSave"></sgs-form-design>
    </el-dialog>
    <el-dialog
            title="预览表单设计"
            :visible.sync="globalPreviewDesignDialogVisible"
            width="90%"
            top="10px"
            :before-close="handleClose">
      <el-form v-model="formRender" label-width="180px" size="small">
        <el-row>
          <el-col :span="4">
            <el-button @click="changeLan(1)">切换EN</el-button>
            <el-button @click="changeLan(2)">切换CN</el-button>
          </el-col>
          <el-col :span="12">
            <el-form-item label="表单code">
              <el-input type="text" v-model="formRender.formCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="showForm">点我加载Form</el-button>
          </el-col>
        </el-row>
      </el-form>
      <sgs-form-design-render
              v-if="formRender.loadRender"
              :base-req-domain-url="formRender.baseReqDomainUrl"
              ref="VFormDesignrender"
              :env="formRender.env"
              :form-obj="formRender.formObj"
              :form-code="formRender.formCode"
              :language="formRender.language"
      ></sgs-form-design-render>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, remove, update, add} from "@/api/form/form";
  import {mapGetters} from "vuex";


  export default {
    data() {
      return {
        formRender:{
          loadRender:false,
          env:'test',
          baseReqDomainUrl:"",
          formCode:'',
          formObj:{},
          language:'cn'
        },
        form: {},
        designDialogVisible: false,
        globalDesignDialogVisible: false,
        globalPreviewDesignDialogVisible: false,
        jsonData:{},
        selectionRow:{},
        selectionList: [],
        query: {},
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        formOptions: {},
        globalFormOptions:{},
        option: {
          tip: false,
          border: true,
          index: true,
          selection: true,
          viewBtn: true,
          emptyBtnIcon: 'el-icon-refresh',
          emptyBtnText: this.$t('reset'),
          column: [
            {
              label: "表单名称",
              prop: "name",
              search: true,
              rules: [{
                required: true,
                message: "请输入表单名称",
                trigger: "blur"
              }]
            },
            {
              label: "表单编码",
              prop: "code",
              search: true,
              rules: [{
                required: true,
                message: "请输入表单编码",
                trigger: "blur"
              }]
            },
            {
              label: "表单内容",
              prop: "content",
              hide: true,
              rules: [{
                required: true,
                message: "请输入表单内容",
                trigger: "blur"
              }]
            },
            {
              label: "版本",
              prop: "versionId",
              rules: []
            },
            {
                label: "备注",
                prop: "remark",
                rules: []
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          viewBtn:false,
          addBtn: false,
          delBtn: this.vaildData(this.permission['nkop:form:delete'], true),
          editBtn: false,
          designBtn: this.vaildData(this.permission['nkop:form:design'], true),
          manageBtn: this.vaildData(this.permission['nkop:form:manage'], true),
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      changeLan(lan){
        this.$refs.VFormDesignrender.changeLanguage(lan);
      },
      showForm(){
        this.formRender.loadRender = false;
        this.$nextTick(()=>{
          this.formRender.loadRender = true;
        })
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      toDetail(row){
          this.$router.push({ path: '/form/'+row.code+'/list',query:{'name':row.name} });
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params,done) {
        this.query = params;
        this.page.currentPage=1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      previewFormDesignGlobal(){
        this.globalPreviewDesignDialogVisible = true;

      },
      showFormDesignGlobal(row){
        this.selectionRow = row || {content:'{}'};
        this.globalDesignDialogVisible = true;
        this.globalFormOptions = JSON.parse(this.selectionRow.content);
      },
      showFormDesign(row){
        this.selectionRow = row || {content:'{}'};
        this.designDialogVisible = true;
        this.formOptions = JSON.parse(this.selectionRow.content);
      },
      handleSave(data){
        const params = {
            id: this.selectionRow.id||'',
            code: data.code,
            name: data.name,
            content: JSON.stringify(data),
            remark: data.remark,
            productLineCode:data.productLineCode || '',
            dffGridId:data.dffGridId,
            dffFormId:data.dffFormId,
            customerGroupCode:data.customerGroupCode
        };
        add(params).then(() => {
            this.onLoad(this.page);
            this.$message({
                type: "success",
                message: "操作成功!"
            });
        }, error => {
            console.log(error);
        });
      },
      handleClose(done) {
          this.$confirm('确认关闭？')
              .then(_ => {
                  done();
              })
              .catch(_ => {});
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
        });
      }
    }
  };
</script>

<style>
</style>
