<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.quotationAndInvoice')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <el-row>
            <el-col :span="16">
                <h1 class="top-title">
                    {{ $t("navbar.quotationAndInvoice") }}
                </h1>
            </el-col>
            <el-col :span="8">
                <div class="trf-o-btn text-right">
                    <el-button
                        class="line-btn"
                        v-if="
                            permissionList.quotationExportBtn ||
                            permissionList.invoiceExportBtn
                        "
                        :loading="downLoading"
                        @click="exportExcelClick"
                    >
                        <img
                            style="vertical-align: text-bottom"
                            src="/img/icon/export.png"
                        />
                        {{ $t("operation.export") }}
                    </el-button>
                </div>
            </el-col>
        </el-row>
        <div class="wrap">
            <el-row>

                <el-form :inline="true" :model="query" size="medium">
                    <div class="tools clearfix">
                        <div
                            id="trf-list-filter"
                            :class="
                                language == 'en-US'
                                    ? 'en-trf-list-filter'
                                    : 'cn-trf-list-filter'
                            "
                        >
                            <el-form-item>
                                <el-input
                                    :placeholder="$t('trfList.query.searchFor')"
                                    v-model.trim="
                                        query.order.properties.queryValue
                                    "
                                    @keyup.enter.native="handelSubmit"
                                    clearable
                                    @clear="handelSubmit"
                                >
                                    <i
                                        slot="prefix"
                                        class="el-input__icon el-icon-search"
                                        style="left: -10px; position: relative"
                                    ></i>
                                </el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-date-picker
                                    class="filter-date"
                                    :value="[
                                        query.order.properties.startDate,
                                        query.order.properties.endDate,
                                    ]"
                                    type="daterange"
                                    format="yyyy-MM-dd"
                                    range-separator="-"
                                    :unlink-panels="true"
                                    :clearable="true"
                                    @input="(val) => updateDate(val)"
                                    :start-placeholder="
                                        $t('trfList.query.selStartDate')
                                    "
                                    :end-placeholder="
                                        $t('trfList.query.selEndDate')
                                    "
                                ></el-date-picker>

                                <!-- <el-col :span="11">
                                    <el-date-picker type="date" :placeholder="$t('trfList.query.selStartDate')"
                                        format="yyyy-MM-dd"
                                        :value="query.order.properties.startDate"
                                        @input="val => updateStartDate(val)"
                                        style="width: 100%;"></el-date-picker>
                                </el-col>
                                <el-col class="line" :span="2">-</el-col>
                                <el-col :span="11">
                                    <el-date-picker :placeholder="$t('trfList.query.selEndDate')" format="yyyy-MM-dd"
                                        :value="query.order.properties.endDate"
                                        @input="val => updateEndDate(val)"
                                        style="width: 100%;"></el-date-picker>
                                </el-col> -->
                            </el-form-item>
                            <el-form-item class="date-group">
                                <el-button
                                    @click="selectedDate(1, 'w', 0)"
                                    :class="{ active: currentDate == 0 }"
                                >
                                    {{ $t("trfList.query.oneWeek") }}
                                </el-button>
                                <el-button
                                    @click="selectedDate(1, 'M', 1)"
                                    :class="{ active: currentDate == 1 }"
                                >
                                    {{ $t("trfList.query.oneMonth") }}
                                </el-button>
                                <el-button
                                    @click="selectedDate(6, 'M', 2)"
                                    :class="{ active: currentDate == 2 }"
                                >
                                    {{ $t("trfList.query.sixMonth") }}
                                </el-button>
                                <el-button
                                    @click="selectedDate(12, 'M', 3)"
                                    :class="{ active: currentDate == 3 }"
                                >
                                    {{ $t("trfList.query.oneYear") }}
                                </el-button>
                                <!--  <el-button type="primary" @click="selectedDate(1, 'y')"> {{$t('trfList.query.oneYear')}}</el-button>-->
                            </el-form-item>
                            <reset-button @click="clear"></reset-button>
                        </div>
                    </div>
                </el-form>
            </el-row>
            <el-row>
                <el-tabs
                    type="border-card"
                    v-model="activeName"
                    @tab-click="handleClick"
                >
                    <el-tab-pane
                        v-if="permissionList.quotationTab"
                        :label="$t('quotation.title')"
                        name="quotation"
                        lazy="true"
                    >
                        <el-table
                            ref="quotationTable"
                            class="quotationInvoiceTable"
                            :row-key="getRowKeys"
                            :row-class-name="getRowClassName"
                            v-loading="loading"
                            fixed
                            :element-loading-text="$t('loading')"
                            :data="quotationData"
                            style="width: 100%"
                        >
                            <el-table-column
                                    align="left"
                                    prop="quotationStatus"
                                    :show-overflow-tooltip="true"
                                    :formatter="quotationStatusFormtter"
                                    :label="$t('quotation.status.title')"
                                    min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("quotation.status.title") }}
                                    </div>
                                    <el-select
                                            size="small"
                                            v-model="
                                            query.order.properties
                                                .quotationStatus
                                        "
                                            @change="trfStatusChange"
                                            @clear="handelSubmit"
                                            :placeholder="$t('select')"
                                            clearable
                                    >
                                        <el-option
                                                :value="null"
                                                :label="$t('trfStatus.all')"
                                        ></el-option>
                                        <el-option
                                                :value="0"
                                                :label="
                                                $t(
                                                    'quotation.status.toBeConfirm',
                                                )
                                            "
                                        ></el-option>
                                        <el-option
                                                :value="1"
                                                :label="
                                                $t('quotation.status.confirmed')
                                            "
                                        ></el-option>
                                        <el-option
                                                :value="2"
                                                :label="
                                                $t('quotation.status.reject')
                                            "
                                        ></el-option>
                                        <el-option
                                                :value="3"
                                                :label="
                                                $t('quotation.status.cancel')
                                            "
                                        ></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="quotationNo"
                                :label="$t('quotation.quotationNo')"
                                min-width="220"
                                align="left"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("quotation.quotationNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.quotationNo
                                        "
                                        :placeholder="
                                            $t('quotation.quotationNo')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.quotationNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="trfNo"
                                align="left"
                                label="TRF No"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.trfNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.trfNo"
                                        :placeholder="$t('trfList.trfNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.trfNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <i
                                        v-show="
                                            scope.row.trfStatus === 2 &&
                                            scope.row.isSubmissionTimeout === 1
                                        "
                                        class="el-icon-alarm-clock"
                                        title="Submission Timeout"
                                        style="cursor: pointer; color: red"
                                    ></i>
                                    <a
                                        @click="trfDetailClick(scope.row)"
                                        style="
                                            color: #ff6600;
                                            font-size: 14px;
                                            cursor: pointer;
                                        "
                                    >
                                        {{ scope.row.trfNo }}
                                    </a>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="templateName"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.templateName')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.templateName") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.templateName
                                        "
                                        :placeholder="
                                            $t('trfList.templateName')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.templateName =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="newReportNo"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.reportNo')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.reportNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.reportNo"
                                        :placeholder="$t('trfList.reportNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.reportNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="articleNo"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.articleNo')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.articleNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.articleNo
                                        "
                                        :placeholder="$t('trfList.articleNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.articleNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="styleNo"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.styleNo')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.styleNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.styleNo"
                                        :placeholder="$t('trfList.styleNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.styleNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="testItemsAmount"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.totalTestItems')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("quotation.totalTestItems") }}
                                    </div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties
                                                .testItemsAmount
                                        "
                                        :placeholder="
                                            $t('quotation.totalTestItems')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.testItemsAmount =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <span>
                                        {{
                                            (isNaN(
                                                parseFloat(
                                                    scope.row.testItemsAmount,
                                                ),
                                            )
                                                ? 0.0
                                                : parseFloat(
                                                      scope.row.testItemsAmount,
                                                  )
                                            ).toFixed(2)
                                        }}
                                    </span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="estimatedTax"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.estimatedTax')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("quotation.estimatedTax") }}
                                    </div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.estimatedTax
                                        "
                                        :placeholder="
                                            $t('quotation.estimatedTax')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.estimatedTax =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <span>
                                        {{
                                            (isNaN(
                                                parseFloat(
                                                    scope.row.estimatedTax,
                                                ),
                                            )
                                                ? 0.0
                                                : parseFloat(
                                                      scope.row.estimatedTax,
                                                  )
                                            ).toFixed(2)
                                        }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="estimatedTax"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.totalAmount')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("quotation.totalAmount") }}</div>
                                    <!--<div style="height:32px;line-height: 32px;"> </div>-->
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.estimatedTax
                                        "
                                        :placeholder="
                                            $t('quotation.totalAmount')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        disabled="true"
                                        @input="
                                            (val) =>
                                                (query.order.properties.estimatedTax =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <span>
                                        {{
                                            (
                                                (isNaN(
                                                    parseFloat(
                                                        scope.row
                                                            .testItemsAmount,
                                                    ),
                                                )
                                                    ? 0.0
                                                    : parseFloat(
                                                          scope.row
                                                              .testItemsAmount,
                                                      )) +
                                                (isNaN(
                                                    parseFloat(
                                                        scope.row.estimatedTax,
                                                    ),
                                                )
                                                    ? 0.0
                                                    : parseFloat(
                                                          scope.row
                                                              .estimatedTax,
                                                      ))
                                            ).toFixed(2)
                                        }}
                                    </span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="commentTime"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.commentTime')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("quotation.commentTime") }}</div>
                                    <el-date-picker
                                        size="small"
                                        style="width: 100%; padding-left: 0px"
                                        type="date"
                                        format="yyyy-MM-dd"
                                        :placeholder="
                                            $t('quotation.commentTime')
                                        "
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .commentTimeStr
                                        "
                                        @input="
                                            (val) =>
                                                changeCommentDateDateQuery(val)
                                        "
                                    ></el-date-picker>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="commentBy"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.commentBy')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("quotation.commentBy") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.commentBy
                                        "
                                        :placeholder="$t('quotation.commentBy')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.commentBy =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="labName"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.labName')"
                                min-width="260"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.labName") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.labName"
                                        :placeholder="$t('trfList.labName')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.labName =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                fixed="right"
                                :label="$t('operation.title')"
                                width="100"
                                align="left"
                            >
                                <template slot-scope="scope">
                                    <el-tooltip effect="dark" placement="top" :content="btn.label" v-for="(btn, index) in getQuotationButtons(scope.row)">
                                        <el-button
                                            v-if="btn.visible "
                                            @click="btn.click"
                                            type="text"
                                            size="small"
                                            :icon="btn.icon"
                                        >
                                        </el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane
                        v-if="permissionList.invoiceTab"
                        :label="$t('invoice.title')"
                        name="invoice"
                        lazy="true"
                    >
                        <el-table
                            ref="invoiceTable"
                            class="quotationInvoiceTable"
                            :row-key="getRowKeys"
                            :row-class-name="getRowClassName"
                            v-loading="loading"
                            fixed
                            :element-loading-text="$t('loading')"
                            :data="invoiceData"
                            style="width: 100%"
                        >
                            <el-table-column
                                    align="left"
                                    prop="invoiceStatus"
                                    :show-overflow-tooltip="true"
                                    :formatter="invoiceStatusFormtter"
                                    :label="$t('invoiceTodo.status.title')"
                                    min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("invoiceTodo.status.title") }}
                                    </div>
                                    <el-select
                                            size="small"
                                            v-model="
                                            query.order.properties.invoiceStatus
                                        "
                                            @change="trfStatusChange"
                                            @clear="handelSubmit"
                                            :placeholder="$t('select')"
                                            clearable
                                    >
                                        <el-option
                                                :value="null"
                                                :label="$t('trfStatus.all')"
                                        ></el-option>
                                        <el-option
                                                :value="0"
                                                :label="
                                                $t(
                                                    'invoiceTodo.status.toBeConfirm',
                                                )
                                            "
                                        ></el-option>
                                        <el-option
                                                :value="1"
                                                :label="
                                                $t(
                                                    'invoiceTodo.status.confirmed',
                                                )
                                            "
                                        ></el-option>
                                        <el-option
                                                :value="2"
                                                :label="
                                                $t('invoiceTodo.status.reject')
                                            "
                                        ></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="invoiceNo"
                                :label="$t('quotation.invoiceNo')"
                                min-width="220"
                                align="left"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("quotation.invoiceNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.invoiceNo
                                        "
                                        :placeholder="$t('quotation.invoiceNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.invoiceNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="trfNo"
                                align="left"
                                label="TRF No"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.trfNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.trfNo"
                                        :placeholder="$t('trfList.trfNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.trfNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <i
                                        v-show="
                                            scope.row.trfStatus === 2 &&
                                            scope.row.isSubmissionTimeout === 1
                                        "
                                        class="el-icon-alarm-clock"
                                        title="Submission Timeout"
                                        style="cursor: pointer; color: red"
                                    ></i>
                                    <a
                                        @click="trfDetailClick(scope.row)"
                                        style="
                                            color: #ff6600;
                                            font-size: 14px;
                                            cursor: pointer;
                                        "
                                    >
                                        {{ scope.row.trfNo }}
                                    </a>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="templateName"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.templateName')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.templateName") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.templateName
                                        "
                                        :placeholder="
                                            $t('trfList.templateName')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.templateName =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="newReportNo"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.reportNo')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.reportNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.reportNo"
                                        :placeholder="$t('trfList.reportNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.reportNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="articleNo"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.articleNo')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.articleNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.articleNo
                                        "
                                        :placeholder="$t('trfList.articleNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.articleNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="styleNo"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.styleNo')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.styleNo") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.styleNo"
                                        :placeholder="$t('trfList.styleNo')"
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.styleNo =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="testItemsAmount"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.totalTestItems')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("quotation.totalTestItems") }}
                                    </div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties
                                                .testItemsAmount
                                        "
                                        :placeholder="
                                            $t('quotation.totalTestItems')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.testItemsAmount =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <span>
                                        {{
                                            (isNaN(
                                                parseFloat(
                                                    scope.row.testItemsAmount,
                                                ),
                                            )
                                                ? 0.0
                                                : parseFloat(
                                                      scope.row.testItemsAmount,
                                                  )
                                            ).toFixed(2)
                                        }}
                                    </span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="estimatedTax"
                                :show-overflow-tooltip="true"
                                :label="$t('quotation.estimatedTax')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("quotation.estimatedTax") }}
                                    </div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.estimatedTax
                                        "
                                        :placeholder="
                                            $t('quotation.estimatedTax')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.estimatedTax =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <span>
                                        {{
                                            (isNaN(
                                                parseFloat(
                                                    scope.row.estimatedTax,
                                                ),
                                            )
                                                ? 0.0
                                                : parseFloat(
                                                      scope.row.estimatedTax,
                                                  )
                                            ).toFixed(2)
                                        }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="estimatedTax"
                                :label="$t('quotation.totalAmount')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("quotation.totalAmount") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.estimatedTax
                                        "
                                        :placeholder="
                                            $t('quotation.totalAmount')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        disabled="true"
                                        @input="
                                            (val) =>
                                                (query.order.properties.estimatedTax =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                                <template slot-scope="scope">
                                    <span>
                                        {{
                                            (
                                                (isNaN(
                                                    parseFloat(
                                                        scope.row
                                                            .testItemsAmount,
                                                    ),
                                                )
                                                    ? 0.0
                                                    : parseFloat(
                                                          scope.row
                                                              .testItemsAmount,
                                                      )) +
                                                (isNaN(
                                                    parseFloat(
                                                        scope.row.estimatedTax,
                                                    ),
                                                )
                                                    ? 0.0
                                                    : parseFloat(
                                                          scope.row
                                                              .estimatedTax,
                                                      ))
                                            ).toFixed(2)
                                        }}
                                    </span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="commentTime"
                                :show-overflow-tooltip="true"
                                :label="$t('invoiceTodo.commentTime')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>
                                        {{ $t("invoiceTodo.commentTime") }}
                                    </div>
                                    <el-date-picker
                                        size="small"
                                        style="width: 100%; padding-left: 0px"
                                        type="date"
                                        format="yyyy-MM-dd"
                                        :placeholder="
                                            $t('invoiceTodo.commentTime')
                                        "
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .commentTimeStr
                                        "
                                        @input="
                                            (val) =>
                                                changeCommentDateDateQuery(val)
                                        "
                                    ></el-date-picker>
                                </template>
                            </el-table-column>

                            <el-table-column
                                align="left"
                                prop="commentBy"
                                :show-overflow-tooltip="true"
                                :label="$t('invoiceTodo.commentBy')"
                                min-width="220"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("invoiceTodo.commentBy") }}</div>
                                    <el-input
                                        size="small"
                                        :value="
                                            query.order.properties.commentBy
                                        "
                                        :placeholder="
                                            $t('invoiceTodo.commentBy')
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @clear="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.commentBy =
                                                    val)
                                        "
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                align="left"
                                prop="labName"
                                :show-overflow-tooltip="true"
                                :label="$t('trfList.labName')"
                                min-width="260"
                            >
                                <template slot="header" slot-scope="scope">
                                    <div>{{ $t("trfList.labName") }}</div>
                                    <el-input
                                        size="small"
                                        :value="query.order.properties.labName"
                                        :placeholder="$t('trfList.labName')"
                                        @keyup.enter.native="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.labName =
                                                    val)
                                        "
                                        @clear="handelSubmit"
                                        clearable
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                fixed="right"
                                :label="$t('operation.title')"
                                width="100"
                                align="left"
                            >
                                <template slot-scope="scope">
                                    <el-tooltip effect="dark" placement="top" :content="btn.label" v-for="(btn, index) in getInvoiceButtons(scope.row)" >
                                        <el-button
                                            v-if="btn.visible && index === 0"
                                            @click="btn.click"
                                            type="text"
                                            size="small"
                                            :icon="btn.icon"
                                        >
                                        </el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                </el-tabs>

                <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="query.pageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="query.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="query.total"
                ></el-pagination>
            </el-row>

            <el-table
                :data="excelQuotationData"
                stripe
                style="width: 0px; height: 0px"
                type="hidden"
                id="exportQuotationTable"
            >
                <el-table-column
                    prop="quotationNo"
                    :label="$t('quotation.quotationNo')"
                    width="200px"
                ></el-table-column>
                <el-table-column
                    :label="$t('trfList.trfNo')"
                    prop="trfNo"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="templateName"
                    :label="$t('trfList.templateName')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="newReportNo"
                    :label="$t('trfList.reportNo')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="articleNo"
                    :label="$t('trfList.articleNo')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="styleNo"
                    :label="$t('trfList.styleNo')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="testItemsAmount"
                    :label="$t('quotation.totalTestItems')"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                (isNaN(parseFloat(scope.row.testItemsAmount))
                                    ? 0.0
                                    : parseFloat(scope.row.testItemsAmount)
                                ).toFixed(2)
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="estimatedTax"
                    :label="$t('quotation.estimatedTax')"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                (isNaN(parseFloat(scope.row.estimatedTax))
                                    ? 0.0
                                    : parseFloat(scope.row.estimatedTax)
                                ).toFixed(2)
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    align="left"
                    prop="estimatedTax"
                    :label="$t('quotation.totalAmount')"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                (
                                    (isNaN(
                                        parseFloat(scope.row.testItemsAmount),
                                    )
                                        ? 0.0
                                        : parseFloat(
                                              scope.row.testItemsAmount,
                                          )) +
                                    (isNaN(parseFloat(scope.row.estimatedTax))
                                        ? 0.0
                                        : parseFloat(scope.row.estimatedTax))
                                ).toFixed(2)
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="commentTime"
                    :label="$t('quotation.commentTime')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="commentBy"
                    :label="$t('quotation.commentBy')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="labName"
                    :label="$t('trfList.labName')"
                    width="260"
                ></el-table-column>
                <el-table-column
                    prop="quotationStatus"
                    :formatter="quotationStatusFormtter"
                    :label="$t('quotation.status.title')"
                    width="160"
                ></el-table-column>
            </el-table>
            <el-table
                :data="excelInvoiceData"
                stripe
                style="width: 0px; height: 0px"
                type="hidden"
                id="exportInvoiceTable"
            >
                <el-table-column
                    prop="invoiceNo"
                    :label="$t('quotation.invoiceNo')"
                    width="200px"
                ></el-table-column>
                <el-table-column
                    :label="$t('trfList.trfNo')"
                    prop="trfNo"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="templateName"
                    :label="$t('trfList.templateName')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="newReportNo"
                    :label="$t('trfList.reportNo')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="articleNo"
                    :label="$t('trfList.articleNo')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="styleNo"
                    :label="$t('trfList.styleNo')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="testItemsAmount"
                    :label="$t('quotation.totalTestItems')"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                (isNaN(parseFloat(scope.row.testItemsAmount))
                                    ? 0.0
                                    : parseFloat(scope.row.testItemsAmount)
                                ).toFixed(2)
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="estimatedTax"
                    :label="$t('quotation.estimatedTax')"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                (isNaN(parseFloat(scope.row.estimatedTax))
                                    ? 0.0
                                    : parseFloat(scope.row.estimatedTax)
                                ).toFixed(2)
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    align="left"
                    prop="estimatedTax"
                    :label="$t('quotation.totalAmount')"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                (
                                    (isNaN(
                                        parseFloat(scope.row.testItemsAmount),
                                    )
                                        ? 0.0
                                        : parseFloat(
                                              scope.row.testItemsAmount,
                                          )) +
                                    (isNaN(parseFloat(scope.row.estimatedTax))
                                        ? 0.0
                                        : parseFloat(scope.row.estimatedTax))
                                ).toFixed(2)
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="commentTime"
                    :label="$t('invoiceTodo.commentTime')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="commentBy"
                    :label="$t('invoiceTodo.commentBy')"
                    width="160"
                ></el-table-column>
                <el-table-column
                    prop="labName"
                    :label="$t('trfList.labName')"
                    width="260"
                ></el-table-column>
                <el-table-column
                    prop="invoiceStatus"
                    :formatter="invoiceStatusFormtter"
                    :label="$t('quotation.status.title')"
                    width="160"
                ></el-table-column>
            </el-table>
        </div>
        <pdf-dialog ref="pdf"></pdf-dialog>

        <el-dialog
            :title="title"
            :visible.sync="dialogFormVisible"
            size="60%"
            :close-on-click-modal="false"
            :append-to-body="true"
        >
            <el-form
                ref="form"
                :model="form"
                label-width="160px"
                label-position="left"
                size="medium"
            >
                <el-form-item
                    id="input-buyer-name"
                    :label="$t('quotation.rejectReason')"
                    :rules="{
                        required: true,
                        message: $t('quotation.validate.rejectReason'),
                        trigger: 'blur',
                    }"
                    prop="rejectReason"
                >
                    <el-input v-model="form.rejectReason"></el-input>
                </el-form-item>
            </el-form>
            <div class="bottom clearfix" style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">
                    {{ $t("operation.cancel") }}
                </el-button>
                <el-button
                    size="small"
                    type="primary"
                    @click="confirmRejectQuotation('form')"
                    :loading="submitLoading"
                    id="add-buyer-confirm"
                >
                    {{ $t("operation.submit") }}
                </el-button>
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
import {
    downLoadFile,
    getInvoiceList,
    exportQuotations,
    exportInvoices,
    confirmQuotation,
    rejectQuotation,
    cancelQuotation,
    confirmInvoice,
    rejectInvoice,
    cancelInvoice,
    v2GetQuotationList,
} from "@/api/trf/trf"
import moment from "moment"
import { deepClone } from "@/util/util"
import { mapGetters } from "vuex"
import XLSX from "xlsx"
import FileSaver from "file-saver"
import { validatenull } from "@/util/validate"
import { ProductLineEnums } from "@/commons/enums/BuEnums"
import resetButton from "@/components/resetButton/resetButton.vue"

export default {
    name: "todoList",
    components: {
        PdfDialog: (resolve) =>
            require(["../../../components/dialog/pdf/PdfDialog"], resolve),
        resetButton,
    },
    created() {
        debugger

        //如果没有时间传递 首次默认查询近30天的数据
        if (validatenull(this.query.order.properties.startDate)) {
            this.query.order.properties.startDate = moment()
                .subtract("days", 30)
                .format("YYYY-MM-DD")
            this.query.defaultProperties.startDate = moment()
                .subtract("days", 30)
                .format("YYYY-MM-DD")
        }
        if (validatenull(this.query.order.properties.endDate)) {
            this.query.order.properties.endDate = moment().format("YYYY-MM-DD")
            this.query.defaultProperties.endDate = moment().format("YYYY-MM-DD")
        }
        this.onSubmit()
    },

    data() {
        return {
            activeName: "quotation",
            downLoading: false,
            excelQuotationData: [],
            excelInvoiceData: [],
            loading: false,
            quotationData: [],
            invoiceData: [],
            dffTemplateData: [],
            checkedTimeOutTrf: false,
            getRowKeys(row) {
                return row.id
            },
            tabRefresh: {
                quotation: true,
                invoice: false,
            },
            query: {
                isQuotationTab: true,
                isShowReviewConclusion: false,
                pageNo: 1,
                pageSize: 10,
                total: 0,
                order: {
                    properties: {
                        invoiceStatus: "",
                        quotationStatus: "",
                        commentBy: "",
                        commentTimeStr: "",
                        quotationNo: "",
                        invoiceNo: "",
                        testItemsAmount: "",
                        estimatedTax: "",
                        templateName: "",
                        buyerCustomerGroupName: "",
                        applicationNameEn: "",
                        trfStatus: null,
                        startDate: null,
                        endDate: null,
                        trfNo: "",
                        reportNo: "",
                        productDescription: "",
                        styleNo: "",
                        poNo: "",
                        articleNo: "",
                        productColor: "",
                        refCode3: "",
                        previousReportNo: "",
                        fiberComposition: "",
                        collection: "",
                        season: "",
                        buyerOrgannization1: "",
                        productionStage: "",
                        firstTimeApplicationFlag: "",
                        buyerSourcingOffice: "",
                        productCategory1: "",
                        pendingReason: "",
                        reportConclusion: "",
                        reviewConclusion: "",
                        submitDateStr: null,
                        isSubmissionTimeout: null,
                        sampleReceiveDateStr: null,
                        reportDateStr: null,
                        labName: "",
                        contactName: "",
                        language: "",
                        sortColumn: "", //排序字段
                        sort: "desc", //排序方式
                    },
                },
                defaultProperties: {
                    invoiceStatus: "",
                    quotationStatus: "",
                    commentBy: "",
                    commentTimeStr: "",
                    quotationNo: "",
                    invoiceNo: "",
                    testItemsAmount: "",
                    estimatedTax: "",
                    templateName: "",
                    buyerCustomerGroupName: "",
                    applicationNameEn: "",
                    trfStatus: null,
                    startDate: null,
                    endDate: null,
                    trfNo: "",
                    reportNo: "",
                    productDescription: "",
                    styleNo: "",
                    poNo: "",
                    articleNo: "",
                    productColor: "",
                    refCode3: "",
                    previousReportNo: "",
                    fiberComposition: "",
                    collection: "",
                    season: "",
                    buyerOrgannization1: "",
                    productionStage: "",
                    firstTimeApplicationFlag: "",
                    buyerSourcingOffice: "",
                    productCategory1: "",
                    pendingReason: "",
                    reportConclusion: "",
                    reviewConclusion: "",
                    submitDateStr: null,
                    isSubmissionTimeout: null,
                    sampleReceiveDateStr: null,
                    reportDateStr: null,
                    labName: "",
                    contactName: "",
                    language: "",
                    sortColumn: "", //排序字段
                    sort: "desc", //排序方式
                },
            },
            data: [],
            reports: [],
            trfParamForm: {
                startDate: "",
                endDate: "",
            },
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            pickerOptions1: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7
                },
            },
            chatItemIndex: "",
            dialogFormVisible: false,
            title: "",
            submitLoading: false,
            form: {},
        }
    },
    computed: {
        ...mapGetters(["userInfo", "permission", "language", "dimensions"]),
        permissionList() {
            return {
                quotationTab: this.vaildData(
                    this.permission["sgs:trf:quotationTab"],
                    false,
                ),
                quotationConfirmBtn: this.vaildData(
                    this.permission["sgs:trf:quotation:confirm"],
                    false,
                ),
                quotationRejectBtn: this.vaildData(
                    this.permission["sgs:trf:quotation:reject"],
                    false,
                ),
                quotationCancelBtn: this.vaildData(
                    this.permission["sgs:trf:quotation:cancel"],
                    false,
                ),
                quotationPreviewBtn: this.vaildData(
                    this.permission["sgs:trf:quotation:preview"],
                    false,
                ),
                quotationExportBtn: this.vaildData(
                    this.permission["sgs:trf:quotation:export"],
                    false,
                ),
                invoiceTab: this.vaildData(
                    this.permission["sgs:trf:invoice:tab"],
                    false,
                ),
                invoiceConfirmBtn: this.vaildData(
                    this.permission["sgs:trf:invoice:confirm"],
                    false,
                ),
                invoiceRejectBtn: this.vaildData(
                    this.permission["sgs:trf:invoice:reject"],
                    false,
                ),
                invoiceCancelBtn: this.vaildData(
                    this.permission["sgs:trf:invoice:cancel"],
                    false,
                ),
                invoicePreviewBtn: this.vaildData(
                    this.permission["sgs:trf:invoice:preview"],
                    false,
                ),
                invoiceExportBtn: this.vaildData(
                    this.permission["sgs:trf:invoice:export"],
                    false,
                ),
            }
        },
        role() {
            return {
                isSGS:
                    this.haseRole("SGSUserRole", "SgsAdmin") ||
                    this.haseRole("SGSUserRole", "SgsLabUser"),
            }
        },
        //监听接收到的消息QuotationConfirmed
        socketTask() {
            return this.$store.getters.socketTask
        },
    },
    watch: {
        socketTask() {
            if (this.socketTask) {
                if (this.chatItemIndex && this.quotationData.length > 0) {
                    this.quotationData[this.chatItemIndex].imHasUnread = false
                }
                this.$refs.chatDialog.show()
            }
        },
        //监听语言变化
        language: function (newVal) {
            this.initOrderList()
        },
    },
    filters: {
        formtterDate: function (value) {
            if (!value) return ""
            value = value.toString()
            return moment(value).format("YYYY-MM-DD")
        },
    },
    methods: {
        async downLoad(fileId) {
            debugger
            if (!validatenull(fileId)) {
                await this.$refs.pdf.open(fileId)
            }
        },
        async changeCommentDateDateQuery(val) {
            let commentDate = null
            if (val) {
                commentDate = moment(val).format("YYYY-MM-DD")
            }
            this.query.order.properties.commentTimeStr = commentDate
            this.query.pageNo = 1
            await this.initOrderList()
        },
        handleClick(tab, event) {
            Object.keys(this.tabRefresh).forEach((item) => {
                this.tabRefresh[item] = false
            })
            debugger
            this.tabRefresh[tab.name] = true
            this.query.order.properties = deepClone(
                this.query.defaultProperties,
            )
            this.onSubmit()
        },
        confirmQuotation(quotationId) {
            if (!validatenull(quotationId)) {
                this.$confirm(
                    "Do you want to confirm this invoice?",
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        confirmQuotation(quotationId).then(
                            (res) => {
                                this.$message({
                                    type: "success",
                                    message: this.$t("api.success"),
                                })
                                //重新加载invoice数据
                                this.onSubmit()
                            },
                            (error) => {
                                this.$message.error(this.$t("api.error"))
                            },
                        )
                    })
                    .catch(() => {})
            }
        },
        rejectQuotation(quotationId) {
            /*if(!validatenull(quotationId)){
                     this.$confirm('Whether to reject the Quotation?', this.$t('tip'), {
                        confirmButtonText: this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        rejectQuotation(quotationId).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        //重新加载invoice数据
                       this.onSubmit();
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }).catch(() => {
                    });
                }*/
            this.form = { quotationId: quotationId }
            this.dialogFormVisible = true
            this.title = this.$t("quotation.rejectReason")
        },
        confirmRejectQuotation() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    debugger
                    this.submitLoading = true
                    rejectQuotation(this.form)
                        .then(
                            (res) => {
                                this.$message({
                                    type: "success",
                                    message: this.$t("api.success"),
                                })
                                this.submitLoading = false
                                this.dialogFormVisible = false
                                this.initInvoiceTotal(this.trfId)
                                this.init()
                            },
                            (error) => {
                                this.$message.error(this.$t("api.error"))
                            },
                        )
                        .catch(() => {
                            this.submitLoading = false
                        })
                } else {
                    return false
                }
            })
        },
        cancelQuotation(quotationId) {
            if (!validatenull(quotationId)) {
                this.$confirm(
                    "Whether to cancel the invoice?",
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        cancelQuotation(quotationId).then(
                            (res) => {
                                this.$message({
                                    type: "success",
                                    message: this.$t("api.success"),
                                })
                                //重新加载invoice数据
                                this.initInvoiceTotal(this.trfId)
                            },
                            (error) => {
                                this.$message.error(this.$t("api.error"))
                            },
                        )
                    })
                    .catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    })
            }
        },
        confirmInvoice(invoiceId) {
            if (!validatenull(invoiceId)) {
                this.$confirm(
                    "Do you want to confirm this invoice?",
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        confirmInvoice(invoiceId).then(
                            (res) => {
                                this.$message({
                                    type: "success",
                                    message: this.$t("api.success"),
                                })
                                //重新加载invoice数据
                                this.onSubmit()
                            },
                            (error) => {
                                this.$message.error(this.$t("api.error"))
                            },
                        )
                    })
                    .catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    })
            }
        },
        rejectInvoice(invoiceId) {
            if (!validatenull(invoiceId)) {
                this.$confirm(
                    "Whether to reject the invoice?",
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        rejectInvoice(invoiceId).then(
                            (res) => {
                                this.$message({
                                    type: "success",
                                    message: this.$t("api.success"),
                                })
                                //重新加载invoice数据
                                this.onSubmit()
                            },
                            (error) => {
                                this.$message.error(this.$t("api.error"))
                            },
                        )
                    })
                    .catch(() => {})
            }
        },
        cancelInvoice(invoiceId) {
            if (!validatenull(invoiceId)) {
                this.$confirm(
                    "Whether to cancel the invoice?",
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        cancelInvoice(invoiceId).then(
                            (res) => {
                                this.$message({
                                    type: "success",
                                    message: this.$t("api.success"),
                                })
                                //重新加载invoice数据
                                this.initInvoiceTotal(this.trfId)
                            },
                            (error) => {
                                this.$message.error(this.$t("api.error"))
                            },
                        )
                    })
                    .catch(() => {
                        /*this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });*/
                    })
            }
        },

        haseRole(type, role) {
            if (validatenull(type) || validatenull(role)) {
                return false
            }
            if (validatenull(this.dimensions)) {
                return false
            } else {
                if (this.dimensions.hasOwnProperty(type)) {
                    if (this.dimensions[type].indexOf(role) >= 0) {
                        return true
                    } else {
                        return false
                    }
                } else {
                    return false
                }
            }
        },
        async sortChange(val) {
            console.log(val)
            if (val == "1" || val == 1) {
                this.query.order.properties.sortColumn =
                    " t.trf_submission_date "
            } else if (val == "2" || val == 2) {
                this.query.order.properties.sortColumn =
                    " t.buyer_review_conclusion "
            } else {
                this.query.order.properties.sortColumn = ""
            }
            await this.initOrderList()
        },
        // 打开聊天弹框
        openChat(item, index) {
            let trfNo = item.trfNo
            this.chatItemIndex = index
            console.log(this.chatItemIndex)
            let userInfo = window.localStorage.getItem("SGS-userInfo")
            userInfo = userInfo ? JSON.parse(userInfo) : ""
            if (userInfo) {
                let userId = userInfo.content.userMgtId
                this.$store.dispatch("webSocketInit", {
                    trfNo: trfNo,
                    userId: userId,
                })
            }
        },
        // 监听沟通弹框隐藏
        async hideChatDialog() {
            this.chatItemIndex = ""
            await this.initOrderList()
        },
        trfDetailClick(row) {
            let hash = new Date().getTime() + ""
            console.log(row)
            if (row.productLineCode == ProductLineEnums.AFL.CODE) {
                window.open(
                    "/#/afl/trf/trfDetail?id=" +
                        row.trfId +
                        "&trfNo=" +
                        row.trfNo +
                        "&title=" +
                        row.trfNo +
                        "&signature=" +
                        row.signature +
                        "&hash=" +
                        hash +
                        "&actionType=detail",
                    "_blank",
                )
            } else {
                window.open(
                    "/#/trf/trfDetail?id=" +
                        row.trfId +
                        "&trfNo=" +
                        row.trfNo +
                        "&title=" +
                        row.trfNo +
                        "&signature=" +
                        row.signature +
                        "&hash=" +
                        hash +
                        "&actionType=detail",
                    "_blank",
                )
            }
        },
        quotationStatusFormtter(row, column) {
            var status = row[column.property]
            if (status == 0 || status == "0") {
                return this.$t("quotation.status.toBeConfirm")
            } else if (status == 1 || status == "1") {
                return this.$t("quotation.status.confirmed")
            } else if (status == 2 || status == "2") {
                return this.$t("quotation.status.reject")
            } else if (status == 3 || status == "3") {
                return this.$t("quotation.status.cancel")
            } else {
                return ""
            }
        },
        invoiceStatusFormtter(row, column) {
            var status = row[column.property]
            if (status == 0 || status == "0") {
                return this.$t("invoiceTodo.status.toBeConfirm")
            } else if (status == 1 || status == "1") {
                return this.$t("invoiceTodo.status.confirmed")
            } else if (status == 2 || status == "2") {
                return this.$t("invoiceTodo.status.reject")
            } else if (status == 3 || status == "3") {
                return this.$t("invoiceTodo.status.cancel")
            } else {
                return ""
            }
        },

        async handelSubmit() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async trfStatusChange() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async reportConclusionChange() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async currentChange(pageCurrent) {
            this.query.pageNo = pageCurrent
            await this.initOrderList()
        },
        getRowClassName({ row, rowIndex }) {
            if (row.trfStatus != 5) {
                return "row-expand-cover"
            }
        },
        downloadReport(cloudId) {
            if (cloudId != "" && cloudId != null) {
                downLoadFile(cloudId).then(
                    (res) => {
                        var pdfUrl = res.data.data
                        this.downLoadReportOts(pdfUrl)
                        /*this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });*/
                    },
                    (error) => {
                        this.$message.error(this.$t("api.error"))
                    },
                )
            }
            //window.open(pdfUrl, '_blank')
        },
        downLoadReportOts(pdfUrl) {
            window.open(pdfUrl, "_blank")
        },
        getReportColor(reportConclusion) {
            if (reportConclusion === "Pass") {
                return "green"
            } else if (reportConclusion === "Fail") {
                return "red"
            } else {
                return null
            }
        },

        retestFormtter(row, column) {
            var reTest = row[column.property]
            var reTestStr = this.$t("common.yes")
            if (reTest == 1) {
                reTestStr = this.$t("common.no")
            }
            return reTestStr
        },
        trfStatusFormatter(row, column) {
            var trfStatus = row[column.property]
            var statusName = "Drafting"
            switch (trfStatus) {
                case 2:
                    statusName = "Submitted"
                    break
                case 3:
                    statusName = "Application Accepted"
                    break
                case 4:
                    statusName = "Testing"
                    break
                case 5:
                    statusName = "Report Issued"
                    break
                case 6:
                    statusName = "Cancel"
                    break
                case 11:
                    statusName = "Pending"
                    break
                case 12:
                    statusName = "Pending"
                    break
                default:
                    statusName = "Drafting"
                    break
            }

            return statusName
        },
        async clear() {
            this.query.order.properties.startDate = ""
            this.query.order.properties.endDate = ""
            this.query.order.properties.queryValue = ""
            this.query.order.properties.quotationNo = ""
            this.query.order.properties.trfNo = ""
            this.query.order.properties.templateName = ""
            this.query.order.properties.reportNo = ""
            this.query.order.properties.articleNo = ""
            this.query.order.properties.styleNo = ""
            this.query.order.properties.testItemsAmount = ""
            this.query.order.properties.estimatedTax = ""
            this.query.order.properties.commentTimeStr = ""
            this.query.order.properties.commentBy = ""
            this.query.order.properties.labName = ""
            this.query.order.properties.quotationStatus = ""
            this.query.order.properties.invoiceNo = ""
            this.query.order.properties.invoiceStatus = ""
            await this.initOrderList()
        },
        async exportExcel() {
            debugger
            var params = {}
            this.downLoading = true
            if (this.activeName == "quotation") {
                let res = await exportQuotations(
                    Object.assign({
                        trfQuery: this.query.order.properties,
                        pageNo: -1,
                        pageSize: 500,
                    }),
                )
                this.downLoading = false
                const data = res.data.data
                this.excelQuotationData = data.records
                this.$nextTick(function () {
                    var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
                    var wb = XLSX.utils.table_to_book(
                        document.querySelector("#exportQuotationTable"),
                        xlsxParam,
                    )
                    var wbout = XLSX.write(wb, {
                        bookType: "xlsx",
                        bookSST: true,
                        type: "array",
                    })
                    try {
                        FileSaver.saveAs(
                            new Blob([wbout], {
                                type: "application/octet-stream",
                            }),
                            "Quotation.xlsx",
                        )
                    } catch (e) {
                        if (typeof console !== "undefined") {
                            console.log(e, wbout)
                        }
                    }
                    return wbout
                })
            } else {
                // Invoice
                debugger
                let res = await exportInvoices(
                    Object.assign({
                        trfQuery: this.query.order.properties,
                        pageNo: -1,
                        pageSize: 500,
                    }),
                )
                this.downLoading = false
                const data = res.data.data
                this.excelInvoiceData = data.records
                this.$nextTick(function () {
                    var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
                    var wb = XLSX.utils.table_to_book(
                        document.querySelector("#exportInvoiceTable"),
                        xlsxParam,
                    )
                    var wbout = XLSX.write(wb, {
                        bookType: "xlsx",
                        bookSST: true,
                        type: "array",
                    })
                    try {
                        FileSaver.saveAs(
                            new Blob([wbout], {
                                type: "application/octet-stream",
                            }),
                            "Invoice.xlsx",
                        )
                    } catch (e) {
                        if (typeof console !== "undefined") {
                            console.log(e, wbout)
                        }
                    }
                    return wbout
                })
            }
        },

        async exportExcelClick() {
            //判断当前导出条数是否>3000  >3000给出确认提示
            if (this.query.total > 500) {
                this.$confirm(
                    this.$t("quotation.confirmExportQuotationList"),
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        this.exportExcel()
                    })
                    .catch(() => {
                        /* this.btnSubmitLoading=false;
                         this.$message.error(this.$t('api.error'));*/
                    })
            } else {
                this.exportExcel()
            }
        },
        async selectedDate(num, dateName) {
            let endDate = moment().format("YYYY-MM-DD")
            var startDate
            if (dateName != "w") {
                startDate = moment(
                    new Date(new Date().setMonth(new Date().getMonth() - num)),
                ).format("YYYY-MM-DD")
            } else {
                startDate = moment()
                    .subtract(num, dateName)
                    .format("YYYY-MM-DD")
            }
            this.query.order.properties.startDate = startDate
            this.query.order.properties.endDate = endDate
            this.query.defaultProperties.startDate = startDate
            this.query.defaultProperties.endDate = endDate
            await this.initOrderList()
        },
        async updateStartDate(val) {
            let date = null
            if (val) {
                date = moment(val).format("YYYY-MM-DD")
            }
            this.query.order.properties.startDate = date
            this.query.defaultProperties.startDate = date
            await this.initOrderList()
        },
        async updateEndDate(val) {
            let date = null
            if (val) {
                date = moment(val).format("YYYY-MM-DD")
            }
            this.query.order.properties.endDate = date
            await this.initOrderList()
        },
        checkTime() {
            var begintime = this.query.order.properties.startDate
            var endtime = this.query.order.properties.endDate
            if (validatenull(endtime)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.endDateValidate"),
                    type: "warning",
                })
                return false
            }

            var time1 = new Date(begintime).getTime()
            var time2 = new Date(endtime).getTime()
            if (validatenull(begintime)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.startDateValidate"),
                    type: "warning",
                })
                return false
            }
            if (validatenull(endtime == "")) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.endDateValidate"),
                    type: "warning",
                })
                return false
            }
            if (time1 > time2) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.endDateErrorValidate"),
                    type: "warning",
                })
                return false
            }

            //判断时间跨度是否大于6个月  修改为12个月
            var arr1 = begintime.split("-")
            var arr2 = endtime.split("-")
            arr1[1] = parseInt(arr1[1])
            arr1[2] = parseInt(arr1[2])
            arr2[1] = parseInt(arr2[1])
            arr2[2] = parseInt(arr2[2])
            var flag = true
            if (arr1[0] == arr2[0]) {
                //同年
                if (arr2[1] - arr1[1] > 12) {
                    //月间隔超过6个月
                    flag = false
                } else if (arr2[1] - arr1[1] == 12) {
                    //月相隔3个月，比较日
                    if (arr2[2] > arr1[2]) {
                        //结束日期的日大于开始日期的日
                        flag = false
                    }
                }
            } else {
                //不同年
                if (arr2[0] - arr1[0] > 1) {
                    flag = false
                } else if (arr2[0] - arr1[0] == 1) {
                    if (arr1[1] < 1) {
                        //开始年的月份小于1时，不需要跨年
                        console.log("arr1[1] < 7")
                        flag = false
                    } else if (arr1[1] + 12 - arr2[1] < 12) {
                        //月相隔大于12个月
                        console.log("arr1[1]+12-arr2[1] < 12")
                        flag = false
                    } else if (arr1[1] + 12 - arr2[1] == 12) {
                        //月相隔3个月，比较日
                        if (arr2[2] > arr1[2]) {
                            //结束日期的日大于开始日期的日
                            console.log("截止日 arr2[2] > " + arr2[2])
                            console.log("开始日 arr1[2] > " + arr1[2])
                            flag = false
                        }
                    }
                }
            }
            if (!flag) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.betweenDateValidate"),
                    type: "warning",
                })
                return false
            }
            return true
        },
        beforeRouteEnter(to, from, next) {
            next((vm) => vm.init())
        },
        async init() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.3)",
            })
            try {
                await this.initOrderList()
            } catch (e) {
                this.$error({ message: e.message || "Loading Data Is Fail!" })
            }
            loading.close()
        },
        async onSubmit() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async initOrderList() {
            debugger
            var submitFlag = true
            if (
                validatenull(this.query.order.properties.startDate) &&
                validatenull(this.query.order.properties.endDate)
            ) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t(
                        "dateValidate.startDateAndEndDateValidate",
                    ),
                    type: "warning",
                })
                return false
            }
            submitFlag = this.checkTime()
            if (!submitFlag) {
                return false
            }

            this.query.order.properties.language = this.language
            var params = {}
            this.loading = true
            if (this.activeName == "quotation") {
                let res = await v2GetQuotationList(
                    Object.assign({
                        trfQuery: this.query.order.properties,
                        pageNo: this.query.pageNo,
                        pageSize: this.query.pageSize,
                    }),
                )
                debugger
                this.loading = false
                const data = res.data.data
                this.query.total = data.total
                this.quotationData = data.records
                console.log("quotationData", this.quotationData)
            } else if (this.activeName == "invoice") {
                let res = await getInvoiceList(
                    Object.assign({
                        trfQuery: this.query.order.properties,
                        pageNo: this.query.pageNo,
                        pageSize: this.query.pageSize,
                    }),
                )
                debugger
                this.loading = false
                const data = res.data.data
                this.query.total = data.total
                this.invoiceData = data.records
                console.log("quotationData", this.invoiceData)
            }
        },
        //分页查询
        async sizeChange(pageSize) {
            this.query.pageSize = pageSize
            await this.initOrderList()
        },
        async updateDate(val) {
            console.log("DATE:::", val)
            let date = []
            if (val) {
                date[0] = moment(val[0]).format("YYYY-MM-DD")
                date[1] = moment(val[1]).format("YYYY-MM-DD")
            }
            this.query.order.properties.startDate = date[0]
            this.query.order.properties.endDate = date[1]
            await this.initOrderList()
        },
         // 获取报价单操作按钮配置
         getQuotationButtons(row) {
            return [
                {
                    visible: this.permissionList.quotationConfirmBtn && row.quotationStatus === 0,
                    click: () => this.confirmQuotation(row.id),
                    label: this.$t("operation.confirm"),
                    icon: "el-icon-check"
                },
                {
                    visible: this.permissionList.quotationRejectBtn && row.quotationStatus === 0,
                    click: () => this.rejectQuotation(row.id),
                    label: this.$t("invoiceTodo.status.reject"),
                    icon: "el-icon-close"
                },
                {
                    visible: this.permissionList.quotationCancelBtn && row.quotationStatus === 2,
                    click: () => this.cancelQuotation(row.id),
                    label: this.$t("invoiceTodo.status.cancel"),
                    icon: "el-icon-close"
                },
                {
                    visible: this.permissionList.quotationPreviewBtn && row.attachmentId,
                    click: () => this.downLoad(row.attachmentId),
                    label: this.$t("operation.preview"),
                    icon: "el-icon-document"
                }
            ];
        },
        // 计算满足显示条件的按钮数量
        getVisibleButtonCount(row) {
            return this.getQuotationButtons(row).filter(btn => btn.visible).length;
        },
        // 获取发票操作按钮配置
        getInvoiceButtons(row) {
            return [
                {
                    // visible: this.permissionList.invoiceConfirmBtn && row.invoiceStatus === 0,
                    visible: true,
                    click: () => this.confirmInvoice(row.id),
                    label: this.$t("invoiceTodo.paid"),
                    icon: "el-icon-check"
                },
                {
                    // visible: this.permissionList.invoiceRejectBtn && row.invoiceStatus === 0,
                    visible: true,
                    click: () => this.rejectInvoice(row.id),
                    label: this.$t("invoiceTodo.status.reject"),
                    icon: "el-icon-close"
                },
                {
                    // visible: this.permissionList.invoiceCancelBtn && row.invoiceStatus === 2,
                    visible: true,
                    click: () => this.cancelInvoice(row.id),
                    label: this.$t("invoiceTodo.status.cancel"),
                    icon: "el-icon-close"
                },
                {
                    // visible: this.permissionList.invoicePreviewBtn && row.attachmentId,
                    visible: true,
                    click: () => this.downLoad(row.attachmentId),
                    label: this.$t("operation.preview"),
                    icon: "el-icon-document"
                }
            ];
        },
        // 计算满足显示条件的发票操作按钮数量
        getVisibleInvoiceButtonCount(row) {
            return this.getInvoiceButtons(row).filter(btn => btn.visible).length;
        },
    },
}
</script>

<style lang="scss">
.top-title {
    font-size: 24px;
    font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
        "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    // font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 32px;
    margin: 0px 0 17px;
}
.trf-o-btn {
    margin-top: -14px;
}

.f-sort a {
    float: left;
    padding: 0 9px;
    height: 23px;
    border: 1px solid #ccc;
    line-height: 23px;
    margin-right: -1px;
    background: #fff;
    color: #333;
}

a.curr {
    border-color: #919191;
    background: #919191;
    color: #fff;
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #ffffff;
    background-color: #919191;
    border-color: #919191;
    -webkit-box-shadow: -1px 0 0 0 #919191;
    box-shadow: -1px 0 0 0 #919191;
}

.el-table__fixed-right {
    height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
    bottom: 17px; // 固定列默认设置了定位，    position: absolute;top: 0;left: 0;只需要再设置一下bottom的值，让固定列和父元素的底部出现距离即可
    background: #fff;
}

.otherActiveClass {
    color: #fff;
    background-color: #ebeef5;
}

.row-expand-cover {
    .el-table__expand-icon {
        visibility: hidden;
    }
}

.otherActiveClass:hover {
    background: #ebeef5;
    color: #fff;
}

.newIcon {
    position: absolute;
    right: 0;
    top: 0;
}

.wrap {
    background: #fff;
    padding: 24px 32px;
}
.date-group {
    button {
        border: 0;
        border-bottom: 1px solid transparent;
        border-radius: 0;
        padding: 10px 0;
        margin: 0 10px;
        &:hover,
        &:focus {
            background-color: initial;
            border-color: transparent;
        }
        &.active {
            border-bottom-color: #f60;
            color: #f60;
        }
    }
    .bg_orange {
        color: #ea4336;
        margin-right: 20px;
        img {
            margin-top: -7px;
        }
    }
}
.btn-sort {
    .active {
        color: #ff6600;
        border-color: #ffd1b3;
        background-color: #fff0e6;
    }
}
.quotationInvoiceTable {
    .cell {
        white-space: nowrap !important;
    }
    th {
        padding-top: 16px;

        &.is-left {
            padding-top: 22px;
        }
        .el-input.is-disabled .el-input__inner {
            background: transparent;
        }
        .el-input__inner {
            background: transparent;
            border-color: #d8d8d8;
            padding-left: 0;
            /* color: #000; */
            &::-webkit-input-placeholder {
                /* color: transparent; */
            }
        }
        .date-input {
            .el-input__inner {
                padding-left: 24px;
            }
        }
        .el-date-editor--date {
            .el-input__inner {
                padding-left: 20px;
                padding-right: 0;
            }
        }
        .cell {
            > div {
                &:last-of-type {
                    margin-top: 8px;
                }
            }
            .el-select .el-input__inner:focus {
                border-color: #d8d8d8;
            }
            .el-select .el-input .el-select__caret {
                color: #d8d8d8;
            }
            .el-input__prefix {
                left: -5px;
            }
        }
        &:last-of-type {
            .cell {
                margin-top: -49px;
            }
            .operatop > span {
                float: right;
            }
        }
    }
}
#trf-list-filter {
    float: left;
    width: fit-content;
    /* margin-bottom: 24px; */
    .el-form-item {
        margin-bottom: 0;
    }
}
.sort-btn {
    float: right;
}
#add-trf-btn {
    width: 248px;
    text-align: left;
    margin-left: 24px;
}
.add-menu {
    li {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        display: inline-block;
        padding-left: 10px;
        transition: all 0.2s;
        a {
            display: block;
        }
        &:hover {
            background: rgba(255, 102, 0, 0.1);
            color: #ff6600;
        }
    }
}
.plain-black {
    height: 40px;
    padding-top: 0;
    padding-bottom: 0;
}
.trf-sort-icon {
    /* transition: .2s all; */
    &.isSort {
        transform: rotate(180deg);
    }
}
.tools {
    /* display: flex;
        justify-content: space-between; */
    margin-bottom: 24px;
}
.filter-date {
    width: 260px !important;
}
.drop-btn{
    color: #ff6600;
}
.icon-more{
    transform: translateY(2px);
}
@media screen and (max-width: 1900px) {
    #trf-list-filter {
        float: left;
    }
    .en-trf-list-filter {
        margin-bottom: 15px;
    }
    .en-sort-btn {
        width: 100%;
    }
}
@media screen and (max-width: 1600px) {
    .cn-trf-list-filter {
        margin-bottom: 15px;
    }
    .cn-sort-btn {
        float: left;
    }
}
@media screen and (max-width: 1280px) {
    #trf-list-filter .el-input__inner,
    .filter-date {
        width: 260px !important;
    }
}
</style>
