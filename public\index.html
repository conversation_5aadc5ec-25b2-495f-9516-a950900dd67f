<!DOCTYPE html>
<html lang="en" xml:lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="chrome=1"/>
   <!-- <script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-KN2BCW56"></script> -->
   <!-- Google Tag Manager -->
    <script>
    (function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-KN2BCW56');
    </script>
<!-- End Google Tag Manager -->
    <!-- <link href="http://**************:81/biz-component/5.0.38/css/biz-component.css" rel="stylesheet"/>-->
    <!--<link rel="stylesheet" href="<%= BASE_URL %>cdn/sgs/styles/biz-component.css" rel="stylesheet"/>-->
    <link rel="stylesheet" href="//at.alicdn.com/t/c/font_1650859_4ohhxfaolm8.css">

    <link rel="stylesheet" href="<%= BASE_URL %>cdn/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/sgs/styles/sgs-style.css" rel="stylesheet"/>
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/animate/3.5.2/animate.css">
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/iconfont/1.0.0/index.css">
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/select2/css/select2.min.css">
    <link rel="stylesheet" href="<%= BASE_URL %>cdn/bootstrap/css/bootstrap.css">
    <link rel="icon" href="<%= BASE_URL %>favicon.png">
    <title>SGS SMART</title>
    <style>
        html,
        body,
        #app {
            height: 100%;
            background: #f7f7f7;
            margin: 0;
            padding: 0;
        }

        .avue-home {
            background-color: #303133;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .avue-home__main {
            user-select: none;
            width: 100%;
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .avue-home__footer {
            width: 100%;
            flex-grow: 0;
            text-align: center;
            padding: 1em 0;
        }

        .avue-home__footer > a {
            font-size: 12px;
            color: #ABABAB;
            text-decoration: none;
        }

        .avue-home__loading {
            height: 32px;
            width: 32px;
            margin-bottom: 20px;
        }

        .avue-home__title {
            color: #FFF;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .avue-home__sub-title {
            color: #ABABAB;
            font-size: 12px;
        }
    </style>
</head>

<body>
<noscript>
    <strong>
        Sorry, SGS SMART won't work without JavaScript support. Please enable JavaScript for your browser before
        continuing.
    </strong>
</noscript>
<!-- Google Tag Manager (noscript)  -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KN2BCW56" height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
<div id="app">
    <div class="avue-home">
        <div class="avue-home__main">
            <img class="avue-home__loading" src="./svg/loading-spin.svg" alt="loading">
            <div class="avue-home__title">
                Loading resources
            </div>
            <div class="avue-home__sub-title d">
                It may take more time to load resources for the first web time. Please wait patiently
            </div>
        </div>
        <div class="avue-home__footer">
            <a href="https://plus.sgsmart-online.com/" target="_blank">
                https://plus.sgsmart-online.com</a>
        </div>
    </div>
</div>
<!-- built files will be auto injected -->
<!--<script src="<%= BASE_URL %>util/aes.js" charset="utf-8"></script>-->
<!--<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js" charset="utf-8"></script>-->
<script src="<%= BASE_URL %>cdn/vue/vue.min.js" charset="utf-8"></script>
<script src="<%= BASE_URL %>cdn/jsPDF/1.5.3/jspdf.min.js" charset="utf-8"></script>
<script src="<%= BASE_URL %>cdn/vuex/2.4.1/vuex.min.js" charset="utf-8"></script>
<script src="<%= BASE_URL %>cdn/vue-router/3.0.1/vue-router.min.js" charset="utf-8"></script>
<script src="<%= BASE_URL %>cdn/axios/1.0.0/axios.min.js" charset="utf-8"></script>
<script src="<%= BASE_URL %>cdn/echarts/5.1.0/echarts.min.js" charset="utf-8"></script>

<!--<script src="http://**************:81/biz-component/5.0.38/js/biz-component.js" ></script>-->
<!--<script src="<%= BASE_URL %>cdn/element-ui/index.js" charset="utf-8"></script>-->
<!--<script src="<%= BASE_URL %>cdn/sgs/sgs-plugin-dataEntry.min.js" charset="utf-8"></script>-->
<!--<script src="<%= BASE_URL %>cdn/sgs/biz-component.js"></script>-->
<script src="<%= BASE_URL %>cdn/powerbi.min.js"></script>

<!--<script type="text/javascript"-->
<!--src='https://webapi.amap.com/maps?v=1.4.11&key=a40c6d547150ac9c43cc17cc0a95f324&plugin=AMap.PlaceSearch'></script>-->
<!--<script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>-->
<!--<script src="https://cdn.bootcss.com/Sortable/1.10.0-rc2/Sortable.min.js"></script>-->
<!--<script type="text/javascript" src="https://digitreport-backend.sgsonline.com.cn/spreadjs/gc.spread.sheets.all.14.0.2.min.js"></script>-->
<div id="pt-message" class="pt-message">
    <p id="msg"></p>
</div>
</body>
<script>

    function isIE() { //ie?
        if (!!window.ActiveXObject || "ActiveXObject" in window)
            return true;
        else
            return false;
    }

    let isIe = isIE();
    if (isIe) { //判断是否是IE6,  值可以改为相应的版本号，如果判定所有的IE，可以自己做一个判定是否存在msie这个词。
        let i = confirm("您的浏览器版本过低，访问页面将会出现不可预料的错误，为了更好的体验,请更换Chrome、Edge浏览器！（If your browser version is too low, unexpected errors will appear when accessing the page. For better experience, please replace chrome, edge browsers!）")//弹出提示信息   我写的比较夸张...
        if (i) {
            window.opener = null
            window.open("", "_self") //点击确认后打开一个新的空白页  "_blank"改为"_self"点击确认后直接关闭浏览器
            window.close();
        }
        if (!i) {
            window.opener = null
            window.open("", "_self") //点击取消后打开一个新的空白页  "_blank"改为"_self"点击确认后直接关闭浏览器
            window.close();
        }
    }

    if(window.safari) {
        let agent = window.navigator.userAgent;
        let arrays = agent.split(" ");
        let version = null;
        if(arrays[11]) {
            version = arrays[11].split("/")[1];
            version = parseFloat(version);

            if(isNaN(version) || version < 16.6) {
                let result = confirm("抱歉，请升级Safari浏览器到16.6版本或以上。(Sorry, please upgrade your Safari to version 16.6 or higher. )");
                if(result) {
                    window.location.href = "https://support.apple.com/zh-cn/HT204416";
                }
            }
        }
    }
</script>
</html>
