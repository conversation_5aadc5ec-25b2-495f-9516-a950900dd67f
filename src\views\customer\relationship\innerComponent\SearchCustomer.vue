<template>
    <div class="smart_views_customer_relationship_SearchCustomer" id="smart_views_customer_relationship_SearchCustomer">
        <el-dialog
                v-model="linkedBuyerDia"
                :title="title"
                width="50%"
                append-to-body
                :show-close="false"
                :close-on-press-escape="false"
                :close-on-click-modal="false"
                class="searchCustomerDia"
                draggable
        >
            <el-form label-position="top"
                     style="padding-top: 20px"
                     :model="companyForm"
                     ref="companyFormRef">
                <el-form-item v-if="!queryRelation" :label="t('scm.companyName')"
                              prop="companyName"
                              :rules="[{validator: (rule, value, callback) => {
                                  if(!companyForm.companyId && !companyForm.bossNo && !companyForm.groupCode){
                                      callback(new Error(t('scm.validate.companyName')));
                                  }else {
                                      callback();
                                  }
                                }},
                                {required:true, message: t('scm.validate.companyName'), trigger: 'blur' }]">
                    <el-autocomplete v-model="companyForm.companyName"
                                     :fetch-suggestions="queryRelationCustomer"
                                     :trigger-on-focus="false"
                                     maxlength="200"
                                     show-word-limit
                                     clearable
                                     @select="handleCompanyChange"
                                     @clear="handleCompanyClear"
                                     value-key="companyName">
                        <template #default="{item}">
                            {{item.companyNameFull}}
                        </template>
                    </el-autocomplete>
                </el-form-item>
                <el-form-item
                        v-if="queryRelation" :label="t('scm.companyName')"
                        prop="companyId"
                        :rules="[{required:true, message: t('scm.validate.companyName'), trigger: 'change' }]">
                    <el-select
                        v-model="companyForm.companyId"
                        style="width: 100%"
                        clearable
                        filterable 
                        @clear="handleCompanyClear"
                    >
                        <el-option
                            v-for="(comp,index) in innerRelationDataList"
                            :label="comp.companyName"
                            :value="comp.companyId"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div style="text-align: center;padding-top: 20px">
                    <el-button type="primary" plain @click="handlerSaveCustomer">{{t('scm.btnName.add')}}</el-button>
                    <el-button @click="cancelDia">{{t('scm.btnName.cancel')}}</el-button>
                    <el-button type="primary" plain v-if="showCreatedBtn" @click="handlerCreateCustomer" icon="Plus">{{createdBtnName}}</el-button>
                </div>
            </template>
        </el-dialog>

        <el-dialog
            v-model="createCustomerDia"
            :title="createdBtnName"
            width="75%"
            append-to-body
            lock-scroll
            :show-close="false"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            draggable
    >
        <EditCustomer
                v-if="createCustomerDia"
                :roleType="roleType"
                :refParentId="refParentId"
                :useParent="useParent"
                :systemId="systemId"
                @cancelDia="cancelDia"
                @saveSuccess="saveCustomerSuccess"
        ></EditCustomer>
    </el-dialog>
    </div>
</template>

<script setup>
import {mapGetters, useStore} from 'vuex';
import {ref, watch, onBeforeMount, onMounted, reactive, computed} from 'vue';
const linkedBuyerDia = ref(false);
import EditCustomer from './EditCustomer.vue'
import customerRelationApi from '@/api/customerRelation'
import {useI18n} from 'vue-i18n';
import {ElNotification} from "element-plus";
const { t } = useI18n();
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)


defineOptions({
    name:'SearchCustomer'
})

const emit = defineEmits(['saveSuccess','cancelDia'])


const props = defineProps({
    useParent:{
        type: Boolean,
        default: false,
    },
    roleType: {
        type: String,
        default: '',
    },
    refParentId:{
      type:String,
      default:''
    },
    childCompanyId:{
        type:String,
        default:''
    },
    title: {
        type: String,
        default: '',
    },
    createdBtnName: {
        type: String,
        default: 'Create',
    },
    showCreatedBtn: {
        type:Boolean,
        default:false
    },
    systemId:{
        type:String,
        default:'UM'
    },
    queryRelation:{
        type:Boolean,
        default:false
    },
    createIfNotExist:{
        type:Boolean,
        default:false
    },
    useInternalDataList:{//服务T2级别，用于查询自身关系链数据
        type:Boolean,
        default:false
    }
})
onBeforeMount(()=>{
    if(props.queryRelation){
        handlerQueryInnerRelation();
    }
})
onMounted(()=>{
    linkedBuyerDia.value = true;
})

const companyForm = reactive({
    companyName:'',
    companyId:'',
    bossNo:'',
    groupCode:''
})
const companyFormRef = ref();
const handleCompanyChange = (item)=>{
    companyForm.companyId = item.companyId;
    companyForm.bossNo = item.bossNo;
    companyForm.groupCode = item.groupCode;
}
const handleCompanyClear = ()=>{
    companyForm.companyId = '';
    companyForm.bossNo = '';
    companyForm.groupCode = '';
}


const handlerSaveCustomer = async ()=>{
    try{
        await companyFormRef.value.validate();
    }catch(e){
        return
    }
    let {companyName} = companyForm;
    let param = {
        refRootId:'',
        refParentId: props.refParentId,
        header: {
            child: {
                roleType: props.roleType,
                buCode: userInfo.value.productLineCode,
                companyType: (props.createIfNotExist ? 'boss':'company'),
                nameList: [
                    {companyName,languageId: "en-US"},
                    {companyName,languageId: "zh-CN"}
                ],
                ...companyForm
            }
        },
        domain: "CUSTOMER"
    }
    if(props.useParent){
        param.header['parent'] =  param.header.child;
        delete param.header.child;
    }
    customerRelationApi.applyRelation(param).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            emit('saveSuccess');
            cancelDia();
        }
    })
}
const cancelDia = ()=>{
    emit('cancelDia')
    createCustomerDia.value=false;
    linkedBuyerDia.value = false;
}
const saveCustomerSuccess = ()=>{
    ElNotification.success({ message: t('success'), duration: 2000});
    emit('saveSuccess')
}

const companyListForDetail = ref([]);
const queryRelationCustomer = (keyWord, cb) => {
    handleCompanyClear();
    if(!keyWord){
        cb([])
        return;
    }
    let param = {
        companyName : keyWord,
        system: props.systemId,
        relationshipType:props.roleType,
        buCode: userInfo.value.productLineCode
    }
    customerRelationApi.queryCustomerInfo(param).then(res=>{
        if(res.status==200 && res.data){
            let data = res.data;
            data.forEach(da=>{
                let {nameList} = da;
                let {companyId,companyName,companyNameFull} = nameList[0];
                da['companyId'] = companyId;
                da['companyName'] = companyName;
                da['companyNameFull'] = companyNameFull;
            })
            companyListForDetail.value = data || [];
            cb(companyListForDetail.value);
        }else{
            cb([])
        }
    })
};

const createCustomerDia = ref(false)
const handlerCreateCustomer = ()=>{
    linkedBuyerDia.value = false;
    createCustomerDia.value = true;
}


//查询内部关系
const innerRelationDataList = ref([])
const handlerQueryInnerRelation = (keyWord, cb) => {
    let param = {
        id: '',
        "currentRole": 'SUPPLIER',
        "targetRole": props.roleType,
        "tier": 'T1',
        current:1,
        size:1000
    }
    customerRelationApi.querySCM(param,{loading:'false'}).then(res=>{
        //tableOption.loading=false;
        if(res.status==200 && res.data){
            let {records} = res.data;
            let result = records.map(item=>{
                return {
                    companyId:item.childCompanyId,
                    companyName:item.companyName,
                    bossNo:item.bossNo
                }
            })
            innerRelationDataList.value = result;
        }
    }).catch(err=>{
    })
};

</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_SearchCustomer {
}
.searchCustomerDia {
    button {
      min-width: 80px;
    }
}
</style>