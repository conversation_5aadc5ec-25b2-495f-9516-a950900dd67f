import Vue from 'vue'
import VueI18n from 'vue-i18n'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang
import enLocale from './en'
import zhLocale from './zh'
import { getStore } from '@/util/store'
Vue.use(VueI18n)
const messages = {
  'en-US': {
    ...elementEnLocale,
    ...enLocale,

  },
  'zh-CN': {
    ...elementZhLocale,
    ...zhLocale,

  }
}

const i18n = new VueI18n({
  silentTranslationWarn :true,
  locale: getStore({ name: 'language' }) || 'zh-CN',
  messages
})

export default i18n
