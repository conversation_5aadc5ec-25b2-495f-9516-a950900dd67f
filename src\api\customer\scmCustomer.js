import request from '@/router/axios';

export const saveBuyerScmCustomer = (form) => {
    return request({
        url: '/api/sgs-mart/customer/scm/save',
        method: 'post',
        data: form
    })
}

export const saveScmCustomer = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/scm/save',
        method: 'post',
        data: form
    })
}

export const queryScmCustomer = (form) => {
    return request({
        url: '/api/sgs-mart/customer/scm/query',
        method: 'post',
        data: form
    })
}

export const deleteScmCustomer = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/scm/delete',
        method: 'post',
        data: form
    })
}

export const customersOrGroup = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/customersOrGroup',
        method: 'post',
        data: form
    })
}
