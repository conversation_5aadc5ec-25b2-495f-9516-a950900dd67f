<template>
    <div class="sgs_mart_trf_form_auditTrail" id="sgs_mart_trf_form_auditTrail" v-loading="!showTable">
        <el-row v-if="showTable">
            <audit-log-table
                v-for="(log,index) in logTypeList"
                :key="'log_'+index"
                :object-no="log.objectNo"
                :object-type="log.objectType"
                :object-date="objectDate"
                :root-object-id="trfId"
                :ref-system-id="log.systemId"
                :log-from="log.logFrom"
                :title="log.label"
            ></audit-log-table>
        </el-row>
        <el-card v-if="logTypeList.length==0 && showTable">
            <el-row >
                <el-col style="text-align: center">
                    No Data
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
    import auditLogApi from '@/api/auditlog'
    import AuditLogTable from "./auditLogTable";

    export default {
        name: "auditTrail",
        props: {
            trfId: String,
            trfNo: String,
            objectDate:String
        },
        data() {
            return {
                showTable:false,
                logTypeList: [],
                labelEnums:{
                    trf:'TRF',
                    report :'Report',
                    result :'Result',
                    conclusion:'conclusion',
                    trfProduct:'TRF Product',
                    trfReportProduct:'Trf Report Product'
                },
            }
        },
        methods: {
            initLogData() {
                if (!this.trfId) {
                    return ;
                }
                this.showTable = false;
                this.logTypeList = [];
                auditLogApi.queryTrfAndReportByTrfId({trfId:this.trfId}).then(res=>{
                    this.showTable = true;
                    if(res.status==200 && res.data){
                        let logTypeList = res.data.data;
                        (logTypeList || []).forEach(l=>{
                            l['label'] = this.labelEnums[l.objectType] || '';
                            if(l.logFrom-0==1){
                                l['label'] = 'TRF Data Process';
                            }
                            this.logTypeList.push(l);
                        })
                    }
                }).catch(err=>{
                    this.showTable = true;
                })

            }
        },
        mounted() {
           this.initLogData();
        },
        created() {
        },
        watch: {},
        computed: {},
        updated() {
        },
        components: {AuditLogTable}
    }
</script>

<style lang="scss">
    .sgs_mart_trf_form_auditTrail {
        background: white !important;
    }
</style>