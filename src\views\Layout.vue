<template>
  <div class="container">
    <Header class="header"></Header>
    <div class="main">
      <router-view></router-view>
    </div>
    <Footer class="footer"></Footer>
  </div>
</template>

<script lang="ts" setup>
import Header from '@/components/Header/index.vue'
import Footer from '@/components/Footer/index.vue'
import { authVerify } from '@/utils/authVerify'
import store from '@/store'
import { getStore } from '@/utils/store'

authVerify()
const language = getStore({ name: 'language'})
store.commit('SET_LANGUAGE', language)
</script>

<style lang="scss" scoped>
@use '@/assets/style/unit.module.scss' as *;
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 84px;
}

.header {
  background-color: #3c515b;
  padding: 0 10px;
  height: 80px;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 1999;
}

.main {
  flex: 1;
  overflow: auto;
  min-height: calc(100vh - 186px);
  background: $background-color;
}
</style>
