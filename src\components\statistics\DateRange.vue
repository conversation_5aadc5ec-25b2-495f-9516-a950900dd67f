<template>
  <el-date-picker
    style="width: 80%"
    class="my-date-picker"
    :value="value"
    @input="changeHandle"
    type="daterange"
    :picker-options="pickerOptions"
    range-separator="-"
    start-placeholder="Start Date"
    end-placeholder="End Date"
    value-format="yyyy-MM-dd"
    size="mini"
    :editable="true"
    align="right">
  </el-date-picker>
</template>

<script>
  export default {
    name: "DateRange",
    data() {
      return {
        pickerOptions: {
          shortcuts: [{
            text: 'Last week',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: 'Last month',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: 'Last 3 months',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
      }
    },
    props: {
      value: Array
    },
    methods: {
      changeHandle(val) {
        this.$emit('change', val)
      }
    }
  }
</script>

<style scoped>
  /deep/ .el-picker-panel [slot=sidebar], /deep/ .el-picker-panel__sidebar {
    width: 115px !important;
  }
</style>
