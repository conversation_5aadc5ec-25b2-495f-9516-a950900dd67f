import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/submit',
    method: 'post',
    data: row
  })
}

export const submitEntry = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/submitEntry',
    method: 'post',
    data: row
  })
}
export const listForSelect = (name) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/listForSelect?name='+name,
    method: 'get',

  })
}

export const doEfillingImport = (id) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/doEfillingImport',
    method: 'get',
    params: {
      id
    }
  })
}

export const tradeSelect = (tradeType,cpscCustomerId,name) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomerTrade/listForSelect',
    method: 'get',
    params: {
      name,
      tradeType,
      cpscCustomerId
    }
  })
}

export const citationsSelect = (certType) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCitation/listForSelect',
    method: 'get',
    params: {
      certType
    }
  })
}

export const copy = (id) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/copy',
    method: 'get',
    params: {
      id
    }
  })
}
export const getCollections = (id) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscTrfInfo/getCollections?customerId='+id,
    method: 'get',
  })
}
// export const dictionaryCertType = () => {
//   return request({
//     url: '/api/sgs-e-filling/sgs-system/dict/dictionary?code=certType',
//     method: 'get',
//   })
// }
export const getBasicLabList = (productLineCodes,certType) =>{
  return request({
    url: '/api/sgs-mart/labApi/get-lab-list',
    method: 'get',
    params: {
      productLineCodes,
      certType
    }
  })
}
export const doMapping = (data) =>{
  return request({
    url: '/api/sgs-e-filling/sgs-work/trfMapping/doMapping',
    method: 'post',
    data
  })
}