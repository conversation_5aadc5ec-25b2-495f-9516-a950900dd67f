<template>
    <div class="sgs_smart_customer_management_notifaction" v-loading="loading">
        <div>
            <div>
                <h3>{{ $t('notifaction.general') }}</h3>
                <h4>{{ $t('notifaction.tickedUpdated') }}</h4>
                <el-checkbox :indeterminate="isIndeterminate"
                             v-model="checkAll"
                             @change="handleCheckAllChange">
                    {{$t('notifaction.selectAll')}}
                </el-checkbox>
            </div>
            <div id="chioce-trf_change"
                 v-if="showCheckBoxDom('trfStatusChange')"
                 :style="{'z-index': $store.state.user.taskType == 'setNotice' ? 9996 : 1}">
                <el-checkbox @change="trfStatusChange" v-model="form.trfStatusChange">
                    {{$t('notifaction.trfStatusName')}}
                </el-checkbox>
            </div>
            <div style="margin-left: 20px" v-if="form.trfStatusChange">
                <ul>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('trfStatusApplicant')" @change="guideChange" v-model="form.trfStatusApplicant">{{$t('trfStatus.applicationAccepted')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('trfStatusTesting')" @change="guideChange" v-model="form.trfStatusTesting">{{$t('trfStatus.testing')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('trfStatusCancel')" @change="guideChange" v-model="form.trfStatusCancel">{{$t('trfStatus.cancel')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('trfStatusPending')" @change="guideChange" v-model="form.trfStatusPending">{{$t('trfStatus.pending')}}</el-checkbox>
                    </li>
                </ul>
            </div>
            <div id="chioce-report"
                 v-if="showCheckBoxDom('finalReportAttached')"
                 :style="{'z-index': $store.state.user.taskType == 'setNotice' ? 9996 : 1}">
                <el-checkbox style="padding-bottom: 10px" @change="reportIssuedChange" v-model="form.finalReportAttached">
                    {{$t('notifaction.finalReportAttached')}}
                </el-checkbox>
                <h5>{{$t('notifaction.reportCompletedTips')}}</h5>
            </div>
            <div id="choice-pass"
                 v-if="form.finalReportAttached"
                 :style="{'margin-left':'20px','z-index': $store.state.user.taskType == 'setNotice' ? 9997 : 1, 'position': 'relative'}">
                <h4 style="color: #1b1b1b; font-size: 16px;">{{ $t('notifaction.overAllResult') }}</h4>
                <ul>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('overAllPass')" @change="guideChange" v-model="form.overAllPass">{{$t('notifaction.pass')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('overAllFail')" @change="guideChange" v-model="form.overAllFail">{{$t('notifaction.fail')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('overAllSeeResult')" @change="guideChange" v-model="form.overAllSeeResult">{{$t('notifaction.seeResult')}}</el-checkbox>
                    </li>
                </ul>
            </div>
            <div id="chioce-review_conclusion"
                 v-if="showCheckBoxDom('reviewConclusionStatus')"
                 :style="{'z-index': $store.state.user.taskType == 'setNotice' ? 9996 : 1}">
                <el-checkbox
                        @change="reviewConclusionChange"
                        v-model="form.reviewConclusionStatus">
                    {{$t('report.reviewConclusion')}}
                </el-checkbox>
            </div>
            <div id="choice-review_conclusion_result"
                 v-if="form.reviewConclusionStatus"
                 :style="{'margin-left':'20px','z-index': $store.state.user.taskType == 'setNotice' ? 9997 : 1, 'position': 'relative'}">
                <ul>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('reviewConclusionFinalized')" @change="guideChange" v-model="form.reviewConclusionFinalized">{{$t('report.reviewConclusionStatus_3')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('reviewConclusionReviewd')" @change="guideChange" v-model="form.reviewConclusionReviewd">{{$t('report.reviewConclusionStatus_1')}}</el-checkbox>
                    </li>
                    <li>
                        <el-checkbox v-if="showCheckBoxDom('reviewConclusionSubmitted')" @change="guideChange" v-model="form.reviewConclusionSubmitted">{{$t('report.reviewConclusionStatus_2')}}</el-checkbox>
                    </li>
                </ul>
            </div>
            <div>
                <el-checkbox v-if="showCheckBoxDom('allCommunicationLog')" @change="messageReceivedChange" v-model="form.allCommunicationLog">
                    {{$t('notifaction.allCommunicationLog')}}
                </el-checkbox>
            </div>
            <div>
                <el-checkbox v-if="showCheckBoxDom('quotation')"  v-model="form.quotation">
                    {{$t('notifaction.quotation')}}
                </el-checkbox>
            </div>
        </div>

        <el-card v-if="isUserConfig && is219">
            <el-row>
                <el-col>
                    <h3>Filter settings</h3>
                    <h4>Set your notification filter below</h4>
                </el-col>
                <el-col>
                    Receive emails based on below criteria.
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="18">
                    <el-tooltip class="item" effect="dark" content="Add Filter" placement="top">
                        <el-button
                                v-if="!dffFilter.filters || dffFilter.filters.length<2"
                                type="primary"
                                size="mini"
                                icon="el-icon-plus"
                                @click="addDffConfig"></el-button>
                    </el-tooltip>
                    <div style="display: inline" v-if="dffFilter.filters && dffFilter.filters.length>0">
                        <el-select
                                style="width: 120px;padding-left: 20px;"
                                size="mini"
                                v-model="dffFilter.condition">
                            <el-option value="1" label="And" ></el-option>
                            <el-option value="2" label="Or" ></el-option>
                        </el-select>
                        <h4>And / Or means the relationship between two filters.</h4>
                    </div>
                </el-col>
            </el-row>
            <el-row  v-for="(filterObj,find) in dffFilter.filters" :key="'filter_row_'+find" style="padding-bottom: 20px">
                <el-col :span="6">
                    <div>Filter by</div>
                    <div>
                        <el-select
                            v-model="filterObj.filterCode"
                            placeholder="Select filter"
                            style="width:100%"
                            filterable
                            clearable
                            @change="filterObj.filterValue=''"
                        >
                            <el-option
                                    v-for="(field,index) in dffConfigList"
                                    :key="'config_filter_'+index"
                                    :label="field.fieldLabel"
                                    :value="field.fieldCode"
                            ></el-option>
                        </el-select>
                    </div>
                </el-col>
                <el-col :offset="1" :span="6">
                    <div>Condition</div>
                    <div style="line-height: 40px;text-align: center;width: 100%;border-bottom: solid 1px black;">
                        contains
                    </div>
                </el-col>
                <el-col :offset="1" :span="6">
                    <div>Filter Value(s)</div>
                    <div>
                        <el-input v-model="filterObj.filterValue"
                                  placeholder="Separate multiple values by comma."
                                  type="primary"
                                  size="100%"></el-input>
                    </div>
                </el-col>
                <el-col :span="2">
                    <el-popconfirm
                            confirm-button-text='confirm'
                            cancel-button-text='cancel'
                            icon="el-icon-info"
                            icon-color="red"
                            title="delete the data?"
                            @confirm="deleteDffConfig(filterObj)"
                    >
                        <i class="el-icon-delete" slot="reference" style="font-size: 20px;line-height: 60px;color:#ff6600"></i>
                    </el-popconfirm>
                </el-col>
            </el-row>
        </el-card>
        <div class="bottom clearfix " style="text-align: center">
            <el-button size="small" v-if="isAdmin && permissionList.submitBtn && !isUserConfig" type="primary" @click="submitForm"
                       :loading="btnGuestbookSubmit">{{$t('operation.submit')}}
            </el-button>
            <el-button size="small" v-if="!isAdmin || isUserConfig" type="primary" @click="submitUserForm"
                       :loading="btnGuestbookSubmit">{{$t('operation.submit')}}
            </el-button>
        </div>
    </div>
</template>

<script>
    import {add, detail,addUserNotification,queryUserNotification} from "@/api/customer/customerNotification";
    import {queryCustomerDffConfig} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import {validatenull} from "../../util/validate";

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            },
            isUserConfig:Boolean
        },
        name: "notifaction",

        data() {
            return {
                showUnSaveTips:false,
                is219:false,
                loading:false,
                checkAll: false,
                isIndeterminate: false,
                isAdmin:false,
                query: {},
                showCheckBox:[],
                adminList:['customer:supplier:admin','customer:agent:admin','customer:buyer:admin'],
                allDffFilterCondition:'and',
                dffConfigList:[
                    {fieldLabel:'Department',fieldCode:'BuyerOrgannizationCode3'},
                    {fieldLabel:'Sample Production Stage',fieldCode:'ProductionStage'},
                ],
                dffFilter:{
                    condition:'1',
                    filters:[],
                },
                form: {
                    trfStatusChange: false,

                    trfStatusApplicant: false,
                    trfStatusTesting: false,
                    trfStatusCancel: false,
                    trfStatusPending: false,

                    finalReportAttached: false,
                    overAllPass: false,
                    overAllFail: false,
                    overAllSeeResult: false,

                    reviewConclusionStatus: false,
                    reviewConclusionFinalized: false,
                    reviewConclusionReviewd: false,
                    reviewConclusionSubmitted: false,

                    allCommunicationLog: false,
                    quotation: false,
                },
                btnGuestbookSubmit: false,
                adminKeys : ['trfStatusChange',
                    'trfStatusApplicant',
                    'trfStatusTesting',
                    'trfStatusCancel',
                    'trfStatusPending',
                    'finalReportAttached',
                    'overAllPass',
                    'overAllFail',
                    'overAllSeeResult',
                    'reviewConclusionStatus',
                    'reviewConclusionFinalized',
                    'reviewConclusionReviewd',
                    'reviewConclusionSubmitted',
                    'allCommunicationLog',
                    'quotation']
            }
        },
        computed: {
            ...mapGetters(["permission", "userInfo","dimensions"]),
            permissionList() {
                return {
                    submitBtn: this.vaildData(this.permission['sgs:customer:notification:submit'], false),
                };
            },
        },
        watch: {
            form:{
              deep:true,
              handler(newV,oldV){
                  if(this.isAdmin && !this.isUserConfig){
                      this.checkAdminSelectAll();
                  }else{
                      this.checkUserSelectAll();
                  }
              }
            },
            '$store.state.user.taskType': {
                immediate: true,
                handler: function (newVal, oldVal) {
                    if (newVal == 'setNotice') {
                        setTimeout(() => {
                            this.$set(this.form, 'finalReportAttached', false);
                        }, 500)
                    }
                },
                deep: true
            },
        },
        methods: {
            handleCheckAllChange() {
                if(!this.isAdmin){
                    this.showCheckBox.forEach(key=>{
                        this.$set(this.form,key,this.checkAll);
                    })
                }else{
                    this.$set(this.form, 'trfStatusChange', this.checkAll);
                    this.$set(this.form, 'trfStatusApplicant', this.checkAll);
                    this.$set(this.form, 'trfStatusTesting', this.checkAll);
                    this.$set(this.form, 'trfStatusCancel', this.checkAll);
                    this.$set(this.form, 'trfStatusPending', this.checkAll);
                    this.$set(this.form, 'finalReportAttached', this.checkAll);
                    this.$set(this.form, 'overAllPass', this.checkAll);
                    this.$set(this.form, 'overAllFail', this.checkAll);
                    this.$set(this.form, 'overAllSeeResult', this.checkAll);
                    this.$set(this.form, 'reviewConclusionStatus', this.checkAll);
                    this.$set(this.form, 'reviewConclusionFinalized', this.checkAll);
                    this.$set(this.form, 'reviewConclusionReviewd', this.checkAll);
                    this.$set(this.form, 'reviewConclusionSubmitted', this.checkAll);
                    this.$set(this.form, 'allCommunicationLog', this.checkAll);
                    this.$set(this.form, 'quotation', this.checkAll);
                }
            },
            trfStatusChange() {
                if (!this.form.trfStatusChange) {
                    this.form.trfStatusApplicant = false;
                    this.form.trfStatusTesting = false;
                    this.form.trfStatusCancel = false;
                    this.form.trfStatusPending = false;
                }
            },
            reviewConclusionChange(){
                if(!this.form.reviewConclusionStatus){
                    this.form.reviewConclusionFinalized =false;
                    this.form.reviewConclusionReviewd = false;
                    this.form.reviewConclusionSubmitted = false;
                }
            },
            //报告完成change事件
            reportIssuedChange(val) {
                if (this.$store.state.user.taskType == 'setNotice') {
                    this.$store.commit('SET_NEXTSTEP', Math.random())
                }
                if (!val) {
                    this.$set(this.form, 'overAllPass', false);
                    this.$set(this.form, 'overAllFail', false);
                    this.$set(this.form, 'overAllSeeResult', false);
                    //this.$set(this.form, 'trfStatusChange', false);
                }
            },
            guideChange() {
                if (this.$store.state.user.taskType == 'setNotice') {
                    console.log('任务引导状态')
                    let task = JSON.parse(localStorage.getItem('guideTask'))
                    let index = task.findIndex(item => item.type == 'setNotice')
                    if (!task[index].val) {
                        console.log('任务还没完成')
                        task[index].val = true  // 设置当前任务为完成状态
                        this.$notify({
                            title: this.$t('success'),
                            message: this.$t('guide.notificationSuccess'),
                            type: 'success',
                            duration: 1500
                        });
                    }
                    this.$store.commit('SET_TASK_TYPE', '')

                    localStorage.setItem('guideTask', JSON.stringify(task))
                    let complete = task.every(item => item.val == true);
                    if (!complete) {
                        console.log('任务未完成的打开弹窗')
                        setTimeout(() => {
                            this.$store.commit('SET_TASK_DIALOG', true) // 2s后打开任务列表
                        }, 2000)
                    }
                }
            },
            messageReceivedChange(val) {
                if (!val) {
                    //this.$set(this.form, 'trfStatusChange', false);
                }
            },
            validCheckBox(){
                //check report trfStatus
                if (this.form.finalReportAttached && !(this.form.overAllPass || this.form.overAllFail || this.form.overAllSeeResult)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: 'Select at least one report conclusion',
                        type: 'warning'
                    });
                    return false;
                }
                if (this.form.trfStatusChange &&
                    !(this.form.trfStatusApplicant ||
                        this.form.trfStatusApplicant ||
                        this.form.trfStatusTesting ||
                        this.form.trfStatusCancel ||
                        this.form.trfStatusPending)
                ) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: 'Select at least one trf status',
                        type: 'warning'
                    });
                    return false;
                }
                if(this.dffFilter && this.dffFilter.filters.length>0){
                    let emptyConditon = this.dffFilter.filters.filter(c=>!c.filterCode || !c.filterValue);
                    if(emptyConditon && emptyConditon.length>0){
                        this.$notify({
                            title: this.$t('tip'),
                            message: 'Please input filter',
                            type: 'warning'
                        });
                        return false;
                    }
                    let ccs = this.dffFilter.filters.map(c=>c.filterCode);
                    let ccsSet = [...new Set(ccs)];
                    if(ccsSet.length < ccs.length){
                        this.$notify({
                            title: this.$t('tip'),
                            message: "Filter can't repeat",
                            type: 'warning'
                        });
                        return false;
                    }
                }
                return true;
            },
            submitUserForm(){
               let valid = this.validCheckBox();
               if(!valid){
                   return;
               }
                this.btnGuestbookSubmit = true;
               let param = Object.assign({},this.form,{dffFilter:this.dffFilter});
                addUserNotification(param).then(res => {
                    this.onLoad();
                    //this.form=res.data.data;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.btnGuestbookSubmit = false;
                },error=>{
                    this.btnGuestbookSubmit = false;
                });
            },
            submitForm() {
                let valid = this.validCheckBox();
                if(!valid){
                    return;
                }
                this.btnGuestbookSubmit = true;
                add(this.form).then(res => {
                    this.onLoad();
                    //this.form=res.data.data;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.btnGuestbookSubmit = false;
                },error=>{
                    this.btnGuestbookSubmit = false;
                });

            },
            checkRole(){
                let {posts,customerGroupCode} = this.userInfo;
                this.isAdmin = this.haseRole('UserRole',"CompanyAdmin") ;
                this.is219 = (customerGroupCode=='CG0000219');
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            showCheckBoxDom(prop){
                return this.isAdmin || (!this.isAdmin && this.showCheckBox.includes(prop));
            },
            handlerShowCheckBox(data){
                this.adminKeys.forEach(k=>{
                    if(data[k] == true){
                        this.showCheckBox.push(k);
                    }
                })
            },
            addDffConfig(){
                let obj = {
                    filterCode:"",
                    filterValue:''
                };
                this.dffFilter.filters.push(obj)
            },
            deleteDffConfig(fieldObj){
                let {fieldCode} = fieldObj;
                let index = this.dffFilter.filters.findIndex(c=>c.fieldCode == fieldCode);
                this.dffFilter.filters.splice(index,1);
            },
            loadRoleAndConfig(){
                this.checkRole();
                this.initCustomerDffConfig();
            },
            initCustomerDffConfig(){
                this.dffConfigList = [];
                let {customerGroupCode} = this.userInfo;
                if(!customerGroupCode){
                    return;
                }
                queryCustomerDffConfig({customerGroupCode}).then(res=>{
                    //console.log("queryCustomerDffConfig res",res);
                    if(res.data && res.data.data){
                        let {data} = res.data;
                        this.dffConfigList = data;
                    }
                },error=>{

                }).catch(err=>{

                })
            },
            onLoad() {
                this.showUnSaveTips = false;
                this.loading = true;
                detail(1).then(res => {
                    if(this.isAdmin && !this.isUserConfig){
                        this.form = res.data.data;
                        this.loading = false;
                        return;
                    }
                    this.handlerShowCheckBox(res.data.data);
                    //获取用户配置数据
                    queryUserNotification().then(res=>{
                        //console.log("queryUserNotification",res);
                        this.loading = false;
                        let userConfig = res.data.data;
                        if(!userConfig || Object.keys(userConfig).length==0){
                            return;
                        }
                        let {emailSubscribeIsNull,emailNotification} = userConfig;
                        this.form = Object.assign({},this.form,userConfig);
                        if(this.form.dffFilter && this.form.dffFilter.filters){
                            this.dffFilter = this.form.dffFilter;
                            return
                        }

                        //user 没有配置过dffFilter ，且emailNotification = true 则默认选中所有
                        if(emailSubscribeIsNull && emailNotification==1){
                            this.checkAll = true;
                            this.showUnSaveTips = true;
                            this.handleCheckAllChange();
                        }
                    },error=>{
                        this.loading = false;
                    })

                },err=>{
                    this.loading = false;
                });
            },
            checkAdminSelectAll(){
                let selectAll = true;
                this.adminKeys.forEach(key=>{
                    selectAll = selectAll && this.form[key]
                })
                this.checkAll = selectAll;
            },
            checkUserSelectAll(){
                let selectAll = true;
                this.showCheckBox.forEach(key=>{
                    selectAll = selectAll && this.form[key]
                })
                this.checkAll = selectAll;
            }
        },
        created() {
            this.loadRoleAndConfig();
            this.onLoad();
        }
    }
</script>

<style lang="scss">
    .sgs_smart_customer_management_notifaction {
        h3 {
            font-size: 20px;
            font-family: "Regular", Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
            font-weight: 500;
            color: #000000;
        }

        h4 {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            margin: 8px 0 24px;
        }

        h5 {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            margin: -15px 0 24px 24px;
        }
        ul{
            list-style: none;
            padding: 5px;
            margin: 0;
        }

        label.el-checkbox {
            font-size: 14px;
            font-weight: 400;
            color: #000;
            margin-bottom: 10px;
        }

        #chioce-report {
            position: relative
        }
    }
</style>
