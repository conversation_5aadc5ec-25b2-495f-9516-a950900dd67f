import request from '../request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const tagManagementList = (params: Object) => {
  return request({
    url: `/sgs-e-filling/sgs-knowledge/knowledgeTag/page?hash=${new Date().getTime()}`,
    method: 'get',
    params
  })
}
export const tagManagementPageinfo = (params: Object) => {
  return request({
    url: `/sgs-e-filling/sgs-knowledge/knowledgeTag/pageinfo?hash=${new Date().getTime()}`,
    method: 'get',
    params
  })
}
export const tagManagementSave = (data: Object) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeTag/save',
    method: 'post',
    data
  })
}
  export const tagManagementUpdate = (data: Object) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledgeTag/update',
      method: 'post',
      data,
      loading: false
    })
} 
export const tagManagementRemove = (params: Object) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeTag/remove',
    method: 'post',
    params
  })
} 
export const tagManagementStatus = (params: Object) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledgeTag/status',
    method: 'post',
    params
  })
} 

