const router = require("koa-router")();
const Api = require("./../../request");

//基础查询
router.post("/api/sgs-scm/customer/scm/query", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/query", ctx);
    ctx.body = datas;
});
//供应商查看当前生产商关联的买家
router.post("/api/sgs-scm/customer/scm/manufacture", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/manufacture", ctx);
    ctx.body = datas;
});
//新增company
router.post("/api/sgs-scm/customer/company/apply", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/company/apply", ctx);
    ctx.body = datas;
});
//修改status {id,status:0/1}
router.post("/api/sgs-scm/customer/scm/status", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/status", ctx);
    ctx.body = datas;
});
//修改assignCode,入参 {id,childAssignCode}
router.post("/api/sgs-scm/customer/scm/changAssignCode", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/changAssignCode", ctx);
    ctx.body = datas;
});
//查询联系人，入参 companyId
router.post("/api/sgs-scm/customer/company/contacts", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/company/contacts", ctx);
    ctx.body = datas;
});
//添加客户关系
router.post("/api/sgs-scm/customer/scm/applyRelationShip", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/applyRelationShip", ctx);
    ctx.body = datas;
});
router.post("/api/sgs-scm/customer/company/query", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/company/query", ctx);
    ctx.body = datas;
});
router.post("/api/sgs-scm/customer/scm/edit", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/edit", ctx);
    ctx.body = datas;
});
router.post("/api/sgs-scm/customer/scm/queryContacts", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/queryContacts", ctx);
    ctx.body = datas;
});
router.post("/api/sgs-scm/customer/scm/approve", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/approve", ctx);
    ctx.body = datas;
});
router.post("/api/sgs-scm/customer/company/addBuyer", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/company/addBuyer", ctx);
    ctx.body = datas;
});
router.post("/api/sgs-scm/customer/company/get", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/company/get", ctx);
    ctx.body = datas;
})

/* supplier manufactures T2 */
router.post("/api/sgs-scm/customer/scm/buyer/queryManufacturer", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/buyer/queryManufacturer", ctx);
    ctx.body = datas;
})

/* query Manufacturer Type enum */
router.post("/api/sgs-scm/customer/scm/queryManufacturerType", async (ctx, next) => {
    const datas = await Api.post("sgs-scm/customer/scm/queryManufacturerType", ctx);
    ctx.body = datas;
})

module.exports = router;
