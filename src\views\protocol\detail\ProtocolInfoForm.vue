<template>
  <el-form
    :model="formData"
    ref="protocolFormRef"
    label-position="top"
    label-width="150px"
  >
    <el-row
      :gutter="20"
      v-for="(fl, readOnlyIndex) in [
        readOnly<PERSON>ieldList,
        generalFieldList,
        customerFieldList,
      ]"
      :key="'readonlyField_' + readOnlyIndex"
    >
      <el-col :span="12" v-for="(f, index) in fl" :key="'gen_cus_' + index">
        <FormField
          :formData="formData"
          :fieldCode="f.fieldCode"
          :fieldType="f.fieldType"
          :isRequired="f.isRequired"
          :isReadOnly="f.isReadOnly"
          :fieldLabel="f.fieldLabel"
          :length="f.length"
          :sourceValue="f.sourceValue"
        />
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import FormField from '@/components/FormField/index.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  readOnlyFieldList: {
    type: Array,
    default: () => [],
  },
  generalFieldList: {
    type: Array,
    default: () => [],
  },
  customerFieldList: {
    type: Array,
    default: () => [],
  },
})

const protocolFormRef = ref(null)
// 校验表单
const validateForm = () => {
  return new Promise((resolve, reject) => {
    protocolFormRef.value.validate((valid) => {
      resolve(valid)
    })
  })
}

defineExpose({
  validateForm,
})
</script>

<style scoped></style>
