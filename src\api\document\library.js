import request from '@/router/axios';


export const changePin = (param)=>{
    return request({
        url: '/api/sgs-mart/library/updatePin',
        method: 'post',
        data: param
    })
}

export const add = (form) => {
    return request({
        url: '/api/sgs-mart/library/submit',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/library/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/library/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/library/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

/*
export const getPrograms = () => {
    return request({
        url: '/api/nkop-system/dict/dictionary?code=productLine',
        method: 'get',
    })
}
*/

export const getLibrarys = (id) => {
    return request({
        url: '/api/sgs-mart/library/getLibrarys',
        method: 'get',
        params: {
            id,
        }
    })
}
export const pbiLibraryDictionary = ()=>{
    return request({
        url:'/api/sgs-mart/library/dictionary',
        method:'get'
    })
}