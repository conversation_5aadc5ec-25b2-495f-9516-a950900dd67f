import request from './request'

// 定义通用的请求参数类型
type RequestParams = Record<string, any>

/**
 * 查询待处理列表
 * @param current - 当前页码，类型为数字
 * @param size - 每页显示数量，类型为数字
 * @param params - 其他查询参数，类型为包含任意键值对的对象
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryPendList = (
  current: number,
  size: number,
  params: RequestParams,
): Promise<any> => {
  return request({
    url: '/api/sgs-mart/pend/list',
    method: 'post',
    params: {
      ...params,
      current,
      size,
    },
  })
}

/**
 * 查询待处理数量
 * @param params - 查询参数，类型为包含任意键值对的对象
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryPendCount = (params: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/pend/count',
    method: 'post',
    params: {
      ...params,
    },
  })
}

/**
 * 更新待处理项的阅读状态
 * @param form - 表单数据，类型为包含任意键值对的对象
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const updatePendReadStatus = (form: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/pend/updatePendReadStatus',
    method: 'post',
    data: form,
  })
}
