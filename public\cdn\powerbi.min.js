/*! powerbi-client v2.9.0 | (c) 2016 Microsoft Corporation MIT */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports["powerbi-client"]=e():t["powerbi-client"]=e()}(this,function(){return function(t){function e(a){if(r[a])return r[a].exports;var i=r[a]={exports:{},id:a,loaded:!1};return t[a].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var r={};return e.m=t,e.c=r,e.p="",e(0)}([function(t,e,r){var a=r(1);e.service=a;var i=r(17);e.factories=i;var o=r(5);e.models=o;var n=r(6);e.Report=n.Report;var l=r(13);e.Dashboard=l.Dashboard;var s=r(14);e.Tile=s.Tile;var d=r(2);e.Embed=d.Embed;var u=r(8);e.Page=u.Page;var p=r(15);e.Qna=p.Qna;var c=r(16);e.Visual=c.Visual;var f=r(9);e.VisualDescriptor=f.VisualDescriptor;var h=new a.Service(i.hpmFactory,i.wpmpFactory,i.routerFactory);window.powerbi=h},function(t,e,r){var a=r(2),i=r(6),o=r(12),n=r(13),l=r(14),s=r(8),d=r(15),u=r(16),p=r(3),c=function(){function t(e,r,a,i){var o=this;void 0===i&&(i={}),this.wpmp=r(i.wpmpName,i.logMessages),this.hpm=e(this.wpmp,null,i.version,i.type),this.router=a(this.wpmp),this.uniqueSessionId=p.generateUUID(),this.router.post("/reports/:uniqueId/events/:eventName",function(t,e){var r={type:"report",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)}),this.router.post("/reports/:uniqueId/pages/:pageName/events/:eventName",function(t,e){var r={type:"report",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)}),this.router.post("/reports/:uniqueId/pages/:pageName/visuals/:visualName/events/:eventName",function(t,e){var r={type:"report",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)}),this.router.post("/dashboards/:uniqueId/events/:eventName",function(t,e){var r={type:"dashboard",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)}),this.router.post("/tile/:uniqueId/events/:eventName",function(t,e){var r={type:"tile",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)}),this.router.post("/qna/:uniqueId/events/:eventName",function(t,e){var r={type:"qna",id:t.params.uniqueId,name:t.params.eventName,value:t.body};o.handleEvent(r)}),this.router.post("/ready/:uniqueId",function(t,e){var r={type:"report",id:t.params.uniqueId,name:"ready",value:t.body};o.handleEvent(r)}),this.embeds=[],this.config=p.assign({},t.defaultConfig,i),this.config.autoEmbedOnContentLoaded&&this.enableAutoEmbed()}return t.prototype.createReport=function(t,e){e.type="create";var r=t,a=new o.Create(this,r,e);return r.powerBiEmbed=a,this.addOrOverwriteEmbed(a,t),a},t.prototype.init=function(t,e){var r=this;void 0===e&&(e=void 0),t=t&&t instanceof HTMLElement?t:document.body;var i=Array.prototype.slice.call(t.querySelectorAll("["+a.Embed.embedUrlAttribute+"]"));return i.map(function(t){return r.embed(t,e)})},t.prototype.embed=function(t,e){return void 0===e&&(e={}),this.embedInternal(t,e)},t.prototype.load=function(t,e){return void 0===e&&(e={}),this.embedInternal(t,e,!0,!1)},t.prototype.bootstrap=function(t,e){return this.embedInternal(t,e,!1,!0)},t.prototype.embedInternal=function(t,e,r,a){void 0===e&&(e={});var i,o=t;if(o.powerBiEmbed){if(a)throw new Error("Attempted to bootstrap element "+t.outerHTML+", but the element is already a powerbi element.");i=this.embedExisting(o,e,r)}else i=this.embedNew(o,e,r,a);return i},t.prototype.getNumberOfComponents=function(){return this.embeds?this.embeds.length:0},t.prototype.getSdkSessionId=function(){return this.uniqueSessionId},t.prototype.embedNew=function(e,r,o,n){var l=r.type||e.getAttribute(a.Embed.typeAttribute);if(!l)throw new Error("Attempted to embed using config "+JSON.stringify(r)+" on element "+e.outerHTML+", but could not determine what type of component to embed. You must specify a type in the configuration or as an attribute such as '"+a.Embed.typeAttribute+'="'+i.Report.type.toLowerCase()+"\"'.");r.type=l;var s=p.find(function(t){return l===t.type.toLowerCase()},t.components);if(!s)throw new Error("Attempted to embed component of type: "+l+" but did not find any matching component.  Please verify the type you specified is intended.");var d=new s(this,e,r,o,n);return e.powerBiEmbed=d,this.addOrOverwriteEmbed(d,e),d},t.prototype.embedExisting=function(t,e,r){var a=p.find(function(e){return e.element===t},this.embeds);if(!a)throw new Error("Attempted to embed using config "+JSON.stringify(e)+" on element "+t.outerHTML+" which already has embedded comopnent associated, but could not find the existing comopnent in the list of active components. This could indicate the embeds list is out of sync with the DOM, or the component is referencing the incorrect HTML element.");if(e.type&&"qna"===e.type.toLowerCase())return this.embedNew(t,e);if("string"==typeof e.type&&e.type!==a.config.type){if("report"===e.type&&"create"===a.config.type){var o=new i.Report(this,t,e,(!1),(!1),t.powerBiEmbed.iframe);return o.load(e),t.powerBiEmbed=o,this.addOrOverwriteEmbed(a,t),o}throw new Error("Embedding on an existing element with a different type than the previous embed object is not supported.  Attempted to embed using config "+JSON.stringify(e)+" on element "+t.outerHTML+", but the existing element contains an embed of type: "+this.config.type+" which does not match the new type: "+e.type)}return a.populateConfig(e,!1),a.load(a.config,r),a},t.prototype.enableAutoEmbed=function(){var t=this;window.addEventListener("DOMContentLoaded",function(e){return t.init(document.body)},!1)},t.prototype.get=function(t){var e=t;if(!e.powerBiEmbed)throw new Error("You attempted to get an instance of powerbi component associated with element: "+t.outerHTML+" but there was no associated instance.");return e.powerBiEmbed},t.prototype.find=function(t){return p.find(function(e){return e.config.uniqueId===t},this.embeds)},t.prototype.addOrOverwriteEmbed=function(t,e){this.embeds=this.embeds.filter(function(t){return t.element.id!==e.id}),this.embeds.push(t)},t.prototype.reset=function(t){var e=t;if(e.powerBiEmbed){var r=e.powerBiEmbed;r.frontLoadHandler&&r.element.removeEventListener("ready",r.frontLoadHandler,!1),p.remove(function(t){return t===e.powerBiEmbed},this.embeds),delete e.powerBiEmbed;var a=t.querySelector("iframe");a&&(void 0!==a.remove?a.remove():a.parentElement.removeChild(a))}},t.prototype.handleTileEvents=function(t){"tile"===t.type&&this.handleEvent(t)},t.prototype.handleEvent=function(t){var e=p.find(function(e){return e.config.uniqueId===t.id},this.embeds);if(e){var r=t.value;if("pageChanged"===t.name){var a="newPage",i=r[a];if(!i)throw new Error("Page model not found at 'event.value."+a+"'.");r[a]=new s.Page(e,i.name,i.displayName,(!0))}p.raiseCustomEvent(e.element,t.name,r)}},t.prototype.preload=function(t,e){var r=document.createElement("iframe");r.setAttribute("style","display:none;"),r.setAttribute("src",t.embedUrl),r.setAttribute("scrolling","no"),r.setAttribute("allowfullscreen","false");var a=e;return a||(a=document.getElementsByTagName("body")[0]),a.appendChild(r),r.onload=function(){p.raiseCustomEvent(r,"preloaded",{})},r},t.components=[l.Tile,i.Report,n.Dashboard,d.Qna,u.Visual],t.defaultConfig={autoEmbedOnContentLoaded:!1,onError:function(){for(var t=[],e=0;e<arguments.length;e++)t[e-0]=arguments[e];return console.log(t[0],t.slice(1))}},t}();e.Service=c},function(t,e,r){var a=r(3),i=r(4),o=r(5),n=function(){function t(e,r,a,i,o,n){this.allowedEvents=[],Array.prototype.push.apply(this.allowedEvents,t.allowedEvents),this.eventHandlers=[],this.service=e,this.element=r,this.iframe=i,this.embeType=a.type.toLowerCase(),this.populateConfig(a,n),"create"===this.embeType?this.setIframe(!1,o,n):this.setIframe(!0,o,n)}return t.prototype.createReport=function(t){var e=o.validateCreateReport(t);if(e)throw e;return this.service.hpm.post("/report/create",t,{uid:this.config.uniqueId,sdkSessionId:this.service.getSdkSessionId()},this.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.save=function(){return this.service.hpm.post("/report/save",null,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},t.prototype.saveAs=function(t){return this.service.hpm.post("/report/saveAs",t,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},t.prototype.load=function(t,e){var r=this;if(t.accessToken){var o=e&&"report"===t.type?this.phasedLoadPath:this.loadPath,n={uid:this.config.uniqueId,sdkSessionId:this.service.getSdkSessionId(),bootstrapped:this.config.bootstrapped,sdkVersion:i["default"].version};return this.service.hpm.post(o,t,n,this.iframe.contentWindow).then(function(e){return a.assign(r.config,t),e.body},function(t){throw t.body})}},t.prototype.off=function(t,e){var r=this,i={name:t,type:null,id:null,value:null};if(e)a.remove(function(t){return t.test(i)&&t.handle===e},this.eventHandlers),this.element.removeEventListener(t,e);else{var o=this.eventHandlers.filter(function(t){return t.test(i)});o.forEach(function(e){a.remove(function(t){return t===e},r.eventHandlers),r.element.removeEventListener(t,e.handle)})}},t.prototype.on=function(t,e){if(this.allowedEvents.indexOf(t)===-1)throw new Error("eventName is must be one of "+this.allowedEvents+". You passed: "+t);this.eventHandlers.push({test:function(e){return e.name===t},handle:e}),this.element.addEventListener(t,e)},t.prototype.reload=function(){return this.load(this.config)},t.prototype.setAccessToken=function(e){var r=this,a=this.config.type;return a="create"===a||"visual"===a||"qna"===a?"report":a,this.service.hpm.post("/"+a+"/token",e,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(a){return r.config.accessToken=e,r.element.setAttribute(t.accessTokenAttribute,e),r.service.accessToken=e,a.body})["catch"](function(t){throw t.body})},t.prototype.getAccessToken=function(e){var r=this.config.accessToken||this.element.getAttribute(t.accessTokenAttribute)||e;if(!r)throw new Error("No access token was found for element. You must specify an access token directly on the element using attribute '"+t.accessTokenAttribute+"' or specify a global token at: powerbi.accessToken.");return r},t.prototype.populateConfig=function(t,e){this.bootstrapConfig?(this.config=a.assign({},this.bootstrapConfig,t),this.bootstrapConfig=null):this.config=a.assign({},t),this.config.embedUrl=this.getEmbedUrl(e),this.config.groupId=this.getGroupId(),this.addLocaleToEmbedUrl(t),this.config.uniqueId=this.getUniqueId(),e?(this.bootstrapConfig=this.config,this.bootstrapConfig.bootstrapped=!0):this.config.accessToken=this.getAccessToken(this.service.accessToken),this.configChanged(e)},t.prototype.addLocaleToEmbedUrl=function(t){if(t.settings){var e=t.settings.localeSettings;e&&e.language&&(this.config.embedUrl=a.addParamToUrl(this.config.embedUrl,"language",e.language)),e&&e.formatLocale&&(this.config.embedUrl=a.addParamToUrl(this.config.embedUrl,"formatLocale",e.formatLocale))}},t.prototype.getEmbedUrl=function(e){var r=this.config.embedUrl||this.element.getAttribute(t.embedUrlAttribute);if(e&&!r&&(r=this.getDefaultEmbedUrl(this.config.hostname)),"string"!=typeof r||0===r.length)throw new Error("Embed Url is required, but it was not found. You must provide an embed url either as part of embed configuration or as attribute '"+t.embedUrlAttribute+"'.");return r},t.prototype.getDefaultEmbedUrl=function(e){e||(e=t.defaultEmbedHostName);var r=this.getDefaultEmbedUrlEndpoint();if(e=e.toLowerCase().trim(),0===e.indexOf("http://"))throw new Error("HTTP is not allowed. HTTPS is required");return 0===e.indexOf("https://")?e+"/"+r:"https://"+e+"/"+r},t.prototype.getUniqueId=function(){return this.config.uniqueId||this.element.getAttribute(t.nameAttribute)||a.createRandomString()},t.prototype.getGroupId=function(){return this.config.groupId||t.findGroupIdFromEmbedUrl(this.config.embedUrl)},t.prototype.fullscreen=function(){var t=this.iframe.requestFullscreen||this.iframe.msRequestFullscreen||this.iframe.mozRequestFullScreen||this.iframe.webkitRequestFullscreen;t.call(this.iframe)},t.prototype.exitFullscreen=function(){if(this.isFullscreen(this.iframe)){var t=document.exitFullscreen||document.mozCancelFullScreen||document.webkitExitFullscreen||document.msExitFullscreen;t.call(document)}},t.prototype.isFullscreen=function(t){var e=["fullscreenElement","webkitFullscreenElement","mozFullscreenScreenElement","msFullscreenElement"];return e.some(function(e){return document[e]===t})},t.prototype.setIframe=function(e,r,i){var o=this;if(!this.iframe){var n=document.createElement("iframe"),l=this.config.uniqueId?a.addParamToUrl(this.config.embedUrl,"uid",this.config.uniqueId):this.config.embedUrl;n.style.width="100%",n.style.height="100%",n.setAttribute("src",l),n.setAttribute("scrolling","no"),n.setAttribute("allowfullscreen","true");for(var s=this.element;s.firstChild;)s.removeChild(s.firstChild);s.appendChild(n),this.iframe=s.firstChild}if(e){if(!i){var d=this.validate(this.config);if(d)throw d}this.iframe.addEventListener("load",function(){return o.load(o.config,r)},!1),this.service.getNumberOfComponents()<=t.maxFrontLoadTimes&&(this.frontLoadHandler=function(){return o.frontLoadSendConfig(o.config)},this.element.addEventListener("ready",this.frontLoadHandler,!1))}else this.iframe.addEventListener("load",function(){return o.createReport(o.createConfig)},!1)},t.prototype.setComponentTitle=function(t){this.iframe&&(null==t?this.iframe.removeAttribute("title"):this.iframe.setAttribute("title",t))},t.prototype.setComponentTabIndex=function(t){this.element&&this.element.setAttribute("tabindex",null==t?"0":t.toString())},t.prototype.removeComponentTabIndex=function(t){this.element&&this.element.removeAttribute("tabindex")},t.findGroupIdFromEmbedUrl=function(t){var e,r=/groupId="?([^&]+)"?/,a=t.match(r);return a&&(e=a[1]),e},t.prototype.frontLoadSendConfig=function(t){if(t.accessToken){var e=this.validate(t);if(e)throw e;if(null!=this.iframe.contentWindow)return this.service.hpm.post("/frontload/config",t,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})}},t.allowedEvents=["loaded","saved","rendered","saveAsTriggered","error","dataSelected","buttonClicked"],t.accessTokenAttribute="powerbi-access-token",t.embedUrlAttribute="powerbi-embed-url",t.nameAttribute="powerbi-name",t.typeAttribute="powerbi-type",t.defaultEmbedHostName="https://app.powerbi.com",t.maxFrontLoadTimes=2,t}();e.Embed=n},function(t,e){function r(t,e,r){var a;"function"==typeof CustomEvent?a=new CustomEvent(e,{detail:r,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent"),a.initCustomEvent(e,!0,!0,r)),t.dispatchEvent(a)}function a(t,e){if(!Array.isArray(e))throw new Error("You attempted to call find with second parameter that was not an array. You passed: "+e);var r;return e.some(function(e,a){if(t(e))return r=a,!0}),r}function i(t,e){var r=a(t,e);return e[r]}function o(t,e){var r=a(t,e);e.splice(r,1)}function n(){for(var t=[],e=0;e<arguments.length;e++)t[e-0]=arguments[e];var r=t[0];if(void 0===r||null===r)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(r),i=1;i<arguments.length;i++){var o=arguments[i];if(void 0!==o&&null!==o)for(var n in o)o.hasOwnProperty(n)&&(a[n]=o[n])}return a}function l(){return(Math.random()+1).toString(36).substring(7)}function s(){var t=(new Date).getTime();return"undefined"!=typeof performance&&"function"==typeof performance.now&&(t+=performance.now()),"xxxxxxxxxxxxxxxxxxxx".replace(/[xy]/g,function(e){var r=t+16*Math.random()&15|0;return t>>=4,r.toString(16)})}function d(t,e,r){var a=t.indexOf("?")>0?"&":"?";return t+=a+e+"="+r}function u(t,e,r){return t.get("/report/hasUnsavedChanges",{uid:e},r).then(function(t){return!t.body},function(t){throw t.body})}function p(t){return t.toLowerCase().indexOf("/rdlembed?")>=0}e.raiseCustomEvent=r,e.findIndex=a,e.find=i,e.remove=o,e.assign=n,e.createRandomString=l,e.generateUUID=s,e.addParamToUrl=d,e.isSavedInternal=u,e.isRDLEmbed=p},function(t,e){var r={version:"2.9.0",type:"js"};Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=r},function(t,e,r){/*! powerbi-models v1.2.1 | (c) 2016 Microsoft Corporation MIT */
!function(e,r){t.exports=r()}(this,function(){return function(t){function e(a){if(r[a])return r[a].exports;var i=r[a]={exports:{},id:a,loaded:!1};return t[a].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var r={};return e.m=t,e.c=r,e.p="",e(0)}([function(t,e,r){function a(t){return l(t)&&!!t.keys}function i(t){return o(t)===G.Basic&&!!t.keyValues}function o(t){if(t.filterType)return t.filterType;var e=t,r=t;return"string"==typeof e.operator&&Array.isArray(e.values)?G.Basic:"string"==typeof r.logicalOperator&&Array.isArray(r.conditions)?G.Advanced:G.Unknown}function n(t){return void 0!==t.table&&void 0!==t.measure}function l(t){return!(!t.table||!t.column||t.aggregationFunction)}function s(t){return!(!(t.table&&t.hierarchy&&t.hierarchyLevel)||t.aggregationFunction)}function d(t){return!!(t.table&&t.hierarchy&&t.hierarchyLevel&&t.aggregationFunction)}function u(t){return!!(t.table&&t.column&&t.aggregationFunction)}function p(t){var e=t.message;return e||(e=t.path+" is invalid. Not meeting "+t.keyword+" constraint"),{message:e}}function c(t){var r=e.Validators.visualSelectorValidator.validate(t);return r?r.map(p):void 0}function f(t){var r=e.Validators.slicerValidator.validate(t);return r?r.map(p):void 0}function h(t){var r=e.Validators.slicerStateValidator.validate(t);return r?r.map(p):void 0}function v(t){var r=e.Validators.playBookmarkRequestValidator.validate(t);return r?r.map(p):void 0}function y(t){var r=e.Validators.addBookmarkRequestValidator.validate(t);return r?r.map(p):void 0}function m(t){var r=e.Validators.applyBookmarkByNameRequestValidator.validate(t);return r?r.map(p):void 0}function g(t){var r=e.Validators.applyBookmarkStateRequestValidator.validate(t);return r?r.map(p):void 0}function V(t){var r=e.Validators.settingsValidator.validate(t);return r?r.map(p):void 0}function w(t){var r=e.Validators.customPageSizeValidator.validate(t);return r?r.map(p):void 0}function b(t){var r=e.Validators.extensionValidator.validate(t);return r?r.map(p):void 0}function E(t){var r=e.Validators.reportLoadValidator.validate(t);return r?r.map(p):void 0}function S(t){var r=e.Validators.reportCreateValidator.validate(t);return r?r.map(p):void 0}function T(t){var r=e.Validators.dashboardLoadValidator.validate(t);return r?r.map(p):void 0}function P(t){var r=e.Validators.tileLoadValidator.validate(t);return r?r.map(p):void 0}function O(t){var r=e.Validators.pageValidator.validate(t);return r?r.map(p):void 0}function _(t){var r=e.Validators.filtersValidator.validate(t);return r?r.map(p):void 0}function R(t){var r=e.Validators.saveAsParametersValidator.validate(t);return r?r.map(p):void 0}function A(t){var r=e.Validators.loadQnaValidator.validate(t);return r?r.map(p):void 0}function F(t){var r=e.Validators.qnaInterpretInputDataValidator.validate(t);return r?r.map(p):void 0}function x(t){var r=e.Validators.exportDataRequestValidator.validate(t);return r?r.map(p):void 0}function k(t){var r=e.Validators.visualHeaderValidator.validate(t);return r?r.map(p):void 0}function C(t){var r=e.Validators.visualSettingsValidator.validate(t);return r?r.map(p):void 0}function I(t){var r=e.Validators.commandsSettingsValidator.validate(t);return r?r.map(p):void 0}function M(t){var r=e.Validators.customThemeValidator.validate(t);return r?r.map(p):void 0}var q=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0}),e.Validators=r(1).Validators;var L;!function(t){t[t.Information=0]="Information",t[t.Verbose=1]="Verbose",t[t.Warning=2]="Warning",t[t.Error=3]="Error",t[t.ExpectedError=4]="ExpectedError",t[t.UnexpectedError=5]="UnexpectedError",t[t.Fatal=6]="Fatal"}(L=e.TraceType||(e.TraceType={}));var j;!function(t){t[t.Widescreen=0]="Widescreen",t[t.Standard=1]="Standard",t[t.Cortana=2]="Cortana",t[t.Letter=3]="Letter",t[t.Custom=4]="Custom"}(j=e.PageSizeType||(e.PageSizeType={}));var N;!function(t){t[t.FitToPage=0]="FitToPage",t[t.FitToWidth=1]="FitToWidth",t[t.ActualSize=2]="ActualSize"}(N=e.DisplayOption||(e.DisplayOption={}));var D;!function(t){t[t.Default=0]="Default",t[t.Transparent=1]="Transparent"}(D=e.BackgroundType||(e.BackgroundType={}));var U;!function(t){t[t.Visible=0]="Visible",t[t.Hidden=1]="Hidden"}(U=e.VisualContainerDisplayMode||(e.VisualContainerDisplayMode={}));var W;!function(t){t[t.Master=0]="Master",t[t.Custom=1]="Custom",t[t.MobilePortrait=2]="MobilePortrait",t[t.MobileLandscape=3]="MobileLandscape"}(W=e.LayoutType||(e.LayoutType={}));var B;!function(t){t[t.AlwaysVisible=0]="AlwaysVisible",t[t.HiddenInViewMode=1]="HiddenInViewMode"}(B=e.SectionVisibility||(e.SectionVisibility={}));var H;!function(t){t[t.Read=0]="Read",t[t.ReadWrite=1]="ReadWrite",t[t.Copy=2]="Copy",t[t.Create=4]="Create",t[t.All=7]="All"}(H=e.Permissions||(e.Permissions={}));var z;!function(t){t[t.View=0]="View",t[t.Edit=1]="Edit"}(z=e.ViewMode||(e.ViewMode={}));var J;!function(t){t[t.Aad=0]="Aad",t[t.Embed=1]="Embed"}(J=e.TokenType||(e.TokenType={}));var Y;!function(t){t[t.Bottom=0]="Bottom",t[t.Top=1]="Top"}(Y=e.MenuLocation||(e.MenuLocation={}));var Q;!function(t){t[t.Report=0]="Report",t[t.Page=1]="Page",t[t.Visual=2]="Visual"}(Q=e.FiltersLevel||(e.FiltersLevel={}));var G;!function(t){t[t.Advanced=0]="Advanced",t[t.Basic=1]="Basic",t[t.Unknown=2]="Unknown",t[t.IncludeExclude=3]="IncludeExclude",t[t.RelativeDate=4]="RelativeDate",t[t.TopN=5]="TopN",t[t.Tuple=6]="Tuple"}(G=e.FilterType||(e.FilterType={}));var $;!function(t){t[t.Days=0]="Days",t[t.Weeks=1]="Weeks",t[t.CalendarWeeks=2]="CalendarWeeks",t[t.Months=3]="Months",t[t.CalendarMonths=4]="CalendarMonths",t[t.Years=5]="Years",t[t.CalendarYears=6]="CalendarYears"}($=e.RelativeDateFilterTimeUnit||(e.RelativeDateFilterTimeUnit={}));var K;!function(t){t[t.InLast=0]="InLast",t[t.InThis=1]="InThis",t[t.InNext=2]="InNext"}(K=e.RelativeDateOperators||(e.RelativeDateOperators={}));var X=function(){function t(t,e){this.target=t,this.filterType=e}return t.prototype.toJSON=function(){var t={$schema:this.schemaUrl,target:this.target,filterType:this.filterType};return void 0!==this.displaySettings&&(t.displaySettings=this.displaySettings),t},t}();e.Filter=X;var Z=function(t){function e(r,a,i){var o=t.call(this,r,G.Unknown)||this;return o.message=a,o.notSupportedTypeName=i,o.schemaUrl=e.schemaUrl,o}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.message=this.message,e.notSupportedTypeName=this.notSupportedTypeName,e},e.schemaUrl="http://powerbi.com/product/schema#notSupported",e}(X);e.NotSupportedFilter=Z;var tt=function(t){function e(r,a,i){var o=t.call(this,r,G.IncludeExclude)||this;return o.values=i,o.isExclude=a,o.schemaUrl=e.schemaUrl,o}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.isExclude=this.isExclude,e.values=this.values,e},e.schemaUrl="http://powerbi.com/product/schema#includeExclude",e}(X);e.IncludeExcludeFilter=tt;var et=function(t){function e(r,a,i,o){var n=t.call(this,r,G.TopN)||this;return n.operator=a,n.itemCount=i,n.schemaUrl=e.schemaUrl,n.orderBy=o,n}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.itemCount=this.itemCount,e.orderBy=this.orderBy,e},e.schemaUrl="http://powerbi.com/product/schema#topN",e}(X);e.TopNFilter=et;var rt=function(t){function e(r,a,i,o,n){var l=t.call(this,r,G.RelativeDate)||this;return l.operator=a,l.timeUnitsCount=i,l.timeUnitType=o,l.includeToday=n,l.schemaUrl=e.schemaUrl,l}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.timeUnitsCount=this.timeUnitsCount,e.timeUnitType=this.timeUnitType,e.includeToday=this.includeToday,e},e.schemaUrl="http://powerbi.com/product/schema#relativeDate",e}(X);e.RelativeDateFilter=rt;var at=function(t){function e(r,a){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];var n=t.call(this,r,G.Basic)||this;if(n.operator=a,n.schemaUrl=e.schemaUrl,0===i.length&&"All"!==a)throw new Error('values must be a non-empty array unless your operator is "All".');return Array.isArray(i[0])?n.values=i[0]:n.values=i,n}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.values=this.values,e},e.schemaUrl="http://powerbi.com/product/schema#basic",e}(X);e.BasicFilter=at;var it=function(t){function e(e,r,a,i){var o=t.call(this,e,r,a)||this;o.keyValues=i,o.target=e;var n=e.keys?e.keys.length:0;if(n>0&&!i)throw new Error("You should pass the values to be filtered for each key. You passed: no values and "+n+" keys");if(0===n&&i&&i.length>0)throw new Error("You passed key values but your target object doesn't contain the keys to be filtered");for(var l=0;l<o.keyValues.length;l++)if(o.keyValues[l]){var s=o.keyValues[l].length;if(s!==n)throw new Error("Each tuple of key values should contain a value for each of the keys. You passed: "+s+" values and "+n+" keys")}return o}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.keyValues=this.keyValues,e},e}(at);e.BasicFilterWithKeys=it;var ot=function(t){function e(r,a,i){var o=t.call(this,r,G.Tuple)||this;return o.operator=a,o.schemaUrl=e.schemaUrl,o.values=i,o}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.operator=this.operator,e.values=this.values,e.target=this.target,e},e.schemaUrl="http://powerbi.com/product/schema#tuple",e}(X);e.TupleFilter=ot;var nt=function(t){function e(r,a){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];var n=t.call(this,r,G.Advanced)||this;if(n.schemaUrl=e.schemaUrl,"string"!=typeof a||0===a.length)throw new Error("logicalOperator must be a valid operator, You passed: "+a);n.logicalOperator=a;var l;if(l=Array.isArray(i[0])?i[0]:i,0===l.length)throw new Error("conditions must be a non-empty array. You passed: "+i);if(l.length>2)throw new Error("AdvancedFilters may not have more than two conditions. You passed: "+i.length);if(1===l.length&&"And"!==a)throw new Error('Logical Operator must be "And" when there is only one condition provided');return n.conditions=l,n}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.logicalOperator=this.logicalOperator,e.conditions=this.conditions,e},e.schemaUrl="http://powerbi.com/product/schema#advanced",e}(X);e.AdvancedFilter=nt,e.isFilterKeyColumnsTarget=a,e.isBasicFilterWithKeys=i,e.getFilterType=o,e.isMeasure=n,e.isColumn=l,e.isHierarchyLevel=s,e.isHierarchyLevelAggr=d,e.isColumnAggr=u;var lt;!function(t){t[t.Interactive=0]="Interactive",t[t.ResultOnly=1]="ResultOnly"}(lt=e.QnaMode||(e.QnaMode={}));var st;!function(t){t[t.Summarized=0]="Summarized",t[t.Underlying=1]="Underlying"}(st=e.ExportDataType||(e.ExportDataType={}));var dt;!function(t){t[t.Off=0]="Off",t[t.Presentation=1]="Presentation"}(dt=e.BookmarksPlayMode||(e.BookmarksPlayMode={})),e.CommonErrorCodes={TokenExpired:"TokenExpired",NotFound:"PowerBIEntityNotFound",InvalidParameters:"Invalid parameters",LoadReportFailed:"LoadReportFailed",NotAuthorized:"PowerBINotAuthorizedException",FailedToLoadModel:"ExplorationContainer_FailedToLoadModel_DefaultDetails"},e.TextAlignment={Left:"left",Center:"center",Right:"right"},e.LegendPosition={Top:"Top",Bottom:"Bottom",Right:"Right",Left:"Left",TopCenter:"TopCenter",BottomCenter:"BottomCenter",RightCenter:"RightCenter",LeftCenter:"LeftCenter"};var ut;!function(t){t[t.Ascending=1]="Ascending",t[t.Descending=2]="Descending"}(ut=e.SortDirection||(e.SortDirection={}));var pt=function(){function t(t){this.$schema=t}return t.prototype.toJSON=function(){return{$schema:this.$schema}},t}();e.Selector=pt;var ct=function(t){function e(r){var a=t.call(this,e.schemaUrl)||this;return a.pageName=r,a}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.pageName=this.pageName,e},e.schemaUrl="http://powerbi.com/product/schema#pageSelector",e}(pt);e.PageSelector=ct;var ft=function(t){function e(r){var a=t.call(this,e.schemaUrl)||this;return a.visualName=r,a}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.visualName=this.visualName,e},e.schemaUrl="http://powerbi.com/product/schema#visualSelector",e}(pt);e.VisualSelector=ft;var ht=function(t){function e(e){var r=t.call(this,ft.schemaUrl)||this;return r.visualType=e,r}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.visualType=this.visualType,e},e.schemaUrl="http://powerbi.com/product/schema#visualTypeSelector",e}(pt);e.VisualTypeSelector=ht;var vt=function(t){function e(e){var r=t.call(this,ft.schemaUrl)||this;return r.target=e,r}return q(e,t),e.prototype.toJSON=function(){var e=t.prototype.toJSON.call(this);return e.target=this.target,e},e.schemaUrl="http://powerbi.com/product/schema#slicerTargetSelector",e}(pt);e.SlicerTargetSelector=vt;var yt;!function(t){t[t.Enabled=0]="Enabled",t[t.Disabled=1]="Disabled",t[t.Hidden=2]="Hidden"}(yt=e.CommandDisplayOption||(e.CommandDisplayOption={}));var mt;!function(t){t[t.Grouping=0]="Grouping",t[t.Measure=1]="Measure",t[t.GroupingOrMeasure=2]="GroupingOrMeasure"}(mt=e.VisualDataRoleKind||(e.VisualDataRoleKind={}));var gt;!function(t){t[t.Measure=0]="Measure",t[t.Grouping=1]="Grouping"}(gt=e.VisualDataRoleKindPreference||(e.VisualDataRoleKindPreference={})),e.validateVisualSelector=c,e.validateSlicer=f,e.validateSlicerState=h,e.validatePlayBookmarkRequest=v,e.validateAddBookmarkRequest=y,e.validateApplyBookmarkByNameRequest=m,e.validateApplyBookmarkStateRequest=g,e.validateSettings=V,e.validateCustomPageSize=w,e.validateExtension=b,e.validateReportLoad=E,e.validateCreateReport=S,e.validateDashboardLoad=T,e.validateTileLoad=P,e.validatePage=O,e.validateFilter=_,e.validateSaveAsParameters=R,e.validateLoadQnaConfiguration=A,e.validateQnaInterpretInputData=F,e.validateExportDataRequest=x,e.validateVisualHeader=k,e.validateVisualSettings=C,e.validateCommandsSettings=I,e.validateCustomTheme=M},function(t,e,r){Object.defineProperty(e,"__esModule",{value:!0});var a=r(2),i=r(3),o=r(5),n=r(6),l=r(7),s=r(8),d=r(9),u=r(10),p=r(11),c=r(12),f=r(13),h=r(14),v=r(15),y=r(16),m=r(17),g=r(18),V=r(19),w=r(20),b=r(21),E=r(22),S=r(23),T=r(24);e.Validators={addBookmarkRequestValidator:new n.AddBookmarkRequestValidator,advancedFilterTypeValidator:new a.EnumValidator([0]),advancedFilterValidator:new l.AdvancedFilterValidator,anyArrayValidator:new a.ArrayValidator([new d.AnyOfValidator([new a.StringValidator,new a.NumberValidator,new a.BooleanValidator])]),anyFilterValidator:new d.AnyOfValidator([new l.BasicFilterValidator,new l.AdvancedFilterValidator,new l.IncludeExcludeFilterValidator,new l.NotSupportedFilterValidator,new l.RelativeDateFilterValidator,new l.TopNFilterValidator]),anyValueValidator:new d.AnyOfValidator([new a.StringValidator,new a.NumberValidator,new a.BooleanValidator]),applyBookmarkByNameRequestValidator:new n.ApplyBookmarkByNameRequestValidator,applyBookmarkStateRequestValidator:new n.ApplyBookmarkStateRequestValidator,applyBookmarkValidator:new d.AnyOfValidator([new n.ApplyBookmarkByNameRequestValidator,new n.ApplyBookmarkStateRequestValidator]),backgroundValidator:new a.EnumValidator([0,1]),basicFilterTypeValidator:new a.EnumValidator([1]),basicFilterValidator:new l.BasicFilterValidator,booleanArrayValidator:new a.BooleanArrayValidator,booleanValidator:new a.BooleanValidator,commandDisplayOptionValidator:new a.EnumValidator([0,1,2]),commandExtensionSelectorValidator:new d.AnyOfValidator([new w.VisualSelectorValidator,new w.VisualTypeSelectorValidator]),commandExtensionValidator:new i.CommandExtensionValidator,commandsSettingsArrayValidator:new a.ArrayValidator([new S.CommandsSettingsValidator]),commandsSettingsValidator:new S.CommandsSettingsValidator,conditionItemValidator:new l.ConditionItemValidator,customLayoutDisplayOptionValidator:new a.EnumValidator([0,1,2]),customLayoutValidator:new g.CustomLayoutValidator,customPageSizeValidator:new h.CustomPageSizeValidator,customThemeValidator:new T.CustomThemeValidator,dashboardLoadValidator:new c.DashboardLoadValidator,displayStateModeValidator:new a.EnumValidator([0,1]),displayStateValidator:new g.DisplayStateValidator,exportDataRequestValidator:new V.ExportDataRequestValidator,extensionArrayValidator:new a.ArrayValidator([new i.ExtensionValidator]),extensionPointsValidator:new i.ExtensionPointsValidator,extensionValidator:new i.ExtensionValidator,fieldRequiredValidator:new s.FieldRequiredValidator,filterColumnTargetValidator:new l.FilterColumnTargetValidator,filterConditionsValidator:new a.ArrayValidator([new l.ConditionItemValidator]),filterHierarchyTargetValidator:new l.FilterHierarchyTargetValidator,filterMeasureTargetValidator:new l.FilterMeasureTargetValidator,filterTargetValidator:new d.AnyOfValidator([new l.FilterColumnTargetValidator,new l.FilterHierarchyTargetValidator,new l.FilterMeasureTargetValidator]),filtersArrayValidator:new a.ArrayValidator([new d.AnyOfValidator([new l.BasicFilterValidator,new l.AdvancedFilterValidator,new l.RelativeDateFilterValidator])]),filtersValidator:new l.FilterValidator,includeExcludeFilterValidator:new l.IncludeExcludeFilterValidator,includeExludeFilterTypeValidator:new a.EnumValidator([3]),layoutTypeValidator:new a.EnumValidator([0,1,2,3]),loadQnaValidator:new v.LoadQnaValidator,menuExtensionValidator:new i.MenuExtensionValidator,menuLocationValidator:new a.EnumValidator([0,1]),notSupportedFilterTypeValidator:new a.EnumValidator([2]),notSupportedFilterValidator:new l.NotSupportedFilterValidator,numberArrayValidator:new a.NumberArrayValidator,numberValidator:new a.NumberValidator,pageLayoutValidator:new m.MapValidator([new a.StringValidator],[new g.VisualLayoutValidator]),pageSizeTypeValidator:new a.EnumValidator([0,1,2,3,4,5]),pageSizeValidator:new h.PageSizeValidator,pageValidator:new h.PageValidator,pageViewFieldValidator:new h.PageViewFieldValidator,pagesLayoutValidator:new m.MapValidator([new a.StringValidator],[new g.PageLayoutValidator]),permissionsValidator:new a.EnumValidator([0,1,2,4,7]),playBookmarkRequestValidator:new n.PlayBookmarkRequestValidator,qnaInterpretInputDataValidator:new v.QnaInterpretInputDataValidator,qnaSettingValidator:new v.QnaSettingsValidator,relativeDateFilterOperatorValidator:new a.EnumValidator([0,1,2]),relativeDateFilterTimeUnitTypeValidator:new a.EnumValidator([0,1,2,3,4,5,6]),relativeDateFilterTypeValidator:new a.EnumValidator([4]),relativeDateFilterValidator:new l.RelativeDateFilterValidator,reportCreateValidator:new p.ReportCreateValidator,reportLoadValidator:new u.ReportLoadValidator,saveAsParametersValidator:new y.SaveAsParametersValidator,settingsValidator:new o.SettingsValidator,singleCommandSettingsValidator:new S.SingleCommandSettingsValidator,slicerSelectorValidator:new d.AnyOfValidator([new w.VisualSelectorValidator,new w.SlicerTargetSelectorValidator]),slicerStateValidator:new b.SlicerStateValidator,slicerTargetValidator:new d.AnyOfValidator([new l.FilterColumnTargetValidator,new l.FilterHierarchyTargetValidator,new l.FilterMeasureTargetValidator,new l.FilterKeyColumnsTargetValidator,new l.FilterKeyHierarchyTargetValidator]),slicerValidator:new b.SlicerValidator,stringArrayValidator:new a.StringArrayValidator,stringValidator:new a.StringValidator,tileLoadValidator:new f.TileLoadValidator,tokenTypeValidator:new a.EnumValidator([0,1]),topNFilterTypeValidator:new a.EnumValidator([5]),topNFilterValidator:new l.TopNFilterValidator,viewModeValidator:new a.EnumValidator([0,1]),visualCommandSelectorValidator:new d.AnyOfValidator([new w.VisualSelectorValidator,new w.VisualTypeSelectorValidator]),visualHeaderSelectorValidator:new d.AnyOfValidator([new w.VisualSelectorValidator,new w.VisualTypeSelectorValidator]),visualHeaderSettingsValidator:new E.VisualHeaderSettingsValidator,visualHeaderValidator:new E.VisualHeaderValidator,visualHeadersValidator:new a.ArrayValidator([new E.VisualHeaderValidator]),visualLayoutValidator:new g.VisualLayoutValidator,visualSelectorValidator:new w.VisualSelectorValidator,visualSettingsValidator:new E.VisualSettingsValidator,visualTypeSelectorValidator:new w.VisualTypeSelectorValidator}},function(t,e){var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var a=function(){function t(){}return t.prototype.validate=function(t,e,r){return null==t?null:"object"!=typeof t||Array.isArray(t)?[{message:void 0!==r?r+" must be an object":"input must be an object",path:e,keyword:"type"}]:null},t}();e.ObjectValidator=a;var i=function(){function t(t){this.itemValidators=t}return t.prototype.validate=function(t,e,r){if(null==t)return null;if(!Array.isArray(t))return[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"type"}];for(var a=0;a<t.length;a++)for(var i=(e?e+".":"")+r+"."+a,o=0,n=this.itemValidators;o<n.length;o++){var l=n[o],s=l.validate(t[a],i,r);if(s)return[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"type"}]}return null},t}();e.ArrayValidator=i;var o=function(){function t(t){this.expectedType=t}return t.prototype.validate=function(t,e,r){return null==t?null:typeof t!==this.expectedType?[{message:r+" must be a "+this.expectedType,path:(e?e+".":"")+r,keyword:"type"}]:null},t}();e.TypeValidator=o;var n=function(t){function e(){return t.call(this,"string")||this}return r(e,t),e}(o);e.StringValidator=n;var l=function(t){function e(){return t.call(this,"boolean")||this}return r(e,t),e}(o);e.BooleanValidator=l;var s=function(t){function e(){return t.call(this,"number")||this}return r(e,t),e}(o);e.NumberValidator=s;var d=function(){function t(t){this.possibleValues=t}return t.prototype.validate=function(t,e,r){return null==t?null:this.possibleValues.indexOf(t)<0?[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"invalid"}]:null},t}();e.ValueValidator=d;var u=function(t){function e(e){var r=t.call(this,[e])||this;return r.schemaValue=e,r}return r(e,t),e.prototype.validate=function(e,r,a){return t.prototype.validate.call(this,e,r,a)},e}(d);e.SchemaValidator=u;var p=function(t){function e(e){var r=t.call(this)||this;return r.possibleValues=e,r}return r(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var i=t.prototype.validate.call(this,e,r,a);if(i)return i;var o=new d(this.possibleValues);return o.validate(e,r,a)},e}(s);e.EnumValidator=p;var c=function(t){function e(){return t.call(this,[new n])||this}return r(e,t),e.prototype.validate=function(e,r,a){var i=t.prototype.validate.call(this,e,r,a);return i?[{message:a+" must be an array of strings",path:(r?r+".":"")+a,keyword:"type"}]:null},e}(i);e.StringArrayValidator=c;var f=function(t){function e(){return t.call(this,[new l])||this}return r(e,t),e.prototype.validate=function(e,r,a){var i=t.prototype.validate.call(this,e,r,a);return i?[{message:a+" must be an array of booleans",path:(r?r+".":"")+a,keyword:"type"}]:null},e}(i);e.BooleanArrayValidator=f;var h=function(t){function e(){return t.call(this,[new s])||this}return r(e,t),e.prototype.validate=function(e,r,a){var i=t.prototype.validate.call(this,e,r,a);return i?[{message:a+" must be an array of numbers",path:(r?r+".":"")+a,keyword:"type"}]:null},e}(i);e.NumberArrayValidator=h},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"title",validators:[i.Validators.stringValidator]},{field:"icon",validators:[i.Validators.stringValidator]},{field:"menuLocation",validators:[i.Validators.menuLocationValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.MenuExtensionValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"visualContextMenu",validators:[i.Validators.menuExtensionValidator]},{field:"visualOptionsMenu",validators:[i.Validators.menuExtensionValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ExtensionPointsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"name",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"extend",validators:[i.Validators.fieldRequiredValidator,i.Validators.extensionPointsValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ExtensionItemValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"title",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"icon",validators:[i.Validators.stringValidator]},{field:"selector",validators:[i.Validators.commandExtensionSelectorValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(d);e.CommandExtensionValidator=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"command",validators:[i.Validators.commandExtensionValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ExtensionValidator=p},function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t){this.fieldValidatorsPairs=t}return t.prototype.validate=function(t,e,r){if(!this.fieldValidatorsPairs)return null;for(var a=e?e+"."+r:r,i=0,o=this.fieldValidatorsPairs;i<o.length;i++)for(var n=o[i],l=0,s=n.validators;l<s.length;l++){var d=s[l],u=d.validate(t[n.field],a,n.field);if(u)return u}return null},t}();e.MultipleFieldsValidator=r},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"filterPaneEnabled",validators:[i.Validators.booleanValidator]},{field:"navContentPaneEnabled",validators:[i.Validators.booleanValidator]},{field:"bookmarksPaneEnabled",validators:[i.Validators.booleanValidator]},{field:"useCustomSaveAsDialog",validators:[i.Validators.booleanValidator]},{field:"extensions",validators:[i.Validators.extensionArrayValidator]},{field:"layoutType",validators:[i.Validators.layoutTypeValidator]},{field:"customLayout",validators:[i.Validators.customLayoutValidator]},{field:"background",validators:[i.Validators.backgroundValidator]},{field:"visualSettings",validators:[i.Validators.visualSettingsValidator]},{field:"hideErrors",validators:[i.Validators.booleanValidator]},{field:"commands",validators:[i.Validators.commandsSettingsArrayValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.SettingsValidator=l},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var l=t.prototype.validate.call(this,e,r,a);if(l)return l;var s=[{field:"playMode",validators:[i.Validators.fieldRequiredValidator,new n.EnumValidator([0,1])]}],d=new o.MultipleFieldsValidator(s);return d.validate(e,r,a)},e}(n.ObjectValidator);e.PlayBookmarkRequestValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"state",validators:[i.Validators.stringValidator]},{field:"displayName",validators:[i.Validators.stringValidator]},{field:"apply",validators:[i.Validators.booleanValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.AddBookmarkRequestValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"name",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ApplyBookmarkByNameRequestValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"state",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ApplyBookmarkStateRequestValidator=u},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"table",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"column",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.FilterColumnTargetValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"keys",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringArrayValidator]
}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(l);e.FilterKeyColumnsTargetValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"table",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"hierarchy",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"hierarchyLevel",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.FilterHierarchyTargetValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"keys",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringArrayValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(d);e.FilterKeyHierarchyTargetValidator=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"table",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"measure",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.FilterMeasureTargetValidator=p;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"target",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterTargetValidator]},{field:"operator",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"values",validators:[i.Validators.fieldRequiredValidator,i.Validators.anyArrayValidator]},{field:"filterType",validators:[i.Validators.basicFilterTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.BasicFilterValidator=c;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"target",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterTargetValidator]},{field:"logicalOperator",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"conditions",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterConditionsValidator]},{field:"filterType",validators:[i.Validators.advancedFilterTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.AdvancedFilterValidator=f;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"target",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterTargetValidator]},{field:"operator",validators:[i.Validators.fieldRequiredValidator,i.Validators.relativeDateFilterOperatorValidator]},{field:"timeUnitsCount",validators:[i.Validators.fieldRequiredValidator,i.Validators.numberValidator]},{field:"timeUnitType",validators:[i.Validators.fieldRequiredValidator,i.Validators.relativeDateFilterTimeUnitTypeValidator]},{field:"includeToday",validators:[i.Validators.fieldRequiredValidator,i.Validators.booleanValidator]},{field:"filterType",validators:[i.Validators.relativeDateFilterTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.RelativeDateFilterValidator=h;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"target",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterTargetValidator]},{field:"operator",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"itemCount",validators:[i.Validators.fieldRequiredValidator,i.Validators.numberValidator]},{field:"filterType",validators:[i.Validators.topNFilterTypeValidator]},{field:"orderBy",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterTargetValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.TopNFilterValidator=v;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"target",validators:[i.Validators.filterTargetValidator]},{field:"message",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"notSupportedTypeName",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"filterType",validators:[i.Validators.notSupportedFilterTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.NotSupportedFilterValidator=y;var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"target",validators:[i.Validators.fieldRequiredValidator,i.Validators.filterTargetValidator]},{field:"isExclude",validators:[i.Validators.fieldRequiredValidator,i.Validators.booleanValidator]},{field:"values",validators:[i.Validators.fieldRequiredValidator,i.Validators.anyArrayValidator]},{field:"filterType",validators:[i.Validators.includeExludeFilterTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.IncludeExcludeFilterValidator=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(t,e,r){return null==t?null:i.Validators.anyFilterValidator.validate(t,e,r)},e}(n.ObjectValidator);e.FilterValidator=g;var V=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"value",validators:[i.Validators.anyValueValidator]},{field:"operator",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ConditionItemValidator=V},function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(){}return t.prototype.validate=function(t,e,r){return null==t?[{message:r+" is required",path:(e?e+".":"")+r,keyword:"required"}]:null},t}();e.FieldRequiredValidator=r},function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t){this.validators=t}return t.prototype.validate=function(t,e,r){if(null==t)return null;for(var a=!1,i=0,o=this.validators;i<o.length;i++){var n=o[i],l=n.validate(t,e,r);if(!l){a=!0;break}}return a?null:[{message:r+" property is invalid",path:(e?e+".":"")+r,keyword:"invalid"}]},t}();e.AnyOfValidator=r},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"accessToken",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"id",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"groupId",validators:[i.Validators.stringValidator]},{field:"settings",validators:[i.Validators.settingsValidator]},{field:"pageName",validators:[i.Validators.stringValidator]},{field:"filters",validators:[i.Validators.filtersArrayValidator]},{field:"permissions",validators:[i.Validators.permissionsValidator]},{field:"viewMode",validators:[i.Validators.viewModeValidator]},{field:"tokenType",validators:[i.Validators.tokenTypeValidator]},{field:"bookmark",validators:[i.Validators.applyBookmarkValidator]},{field:"theme",validators:[i.Validators.customThemeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ReportLoadValidator=l},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"accessToken",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"datasetId",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"groupId",validators:[i.Validators.stringValidator]},{field:"tokenType",validators:[i.Validators.tokenTypeValidator]},{field:"theme",validators:[i.Validators.customThemeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.ReportCreateValidator=l},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"accessToken",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"id",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"groupId",validators:[i.Validators.stringValidator]},{field:"pageView",validators:[i.Validators.pageViewFieldValidator]},{field:"tokenType",validators:[i.Validators.tokenTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.DashboardLoadValidator=l},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"accessToken",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"id",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"dashboardId",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"groupId",validators:[i.Validators.stringValidator]},{field:"pageView",validators:[i.Validators.stringValidator]},{field:"tokenType",validators:[i.Validators.tokenTypeValidator]},{field:"width",validators:[i.Validators.numberValidator]},{field:"height",validators:[i.Validators.numberValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.TileLoadValidator=l},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"type",validators:[i.Validators.fieldRequiredValidator,i.Validators.pageSizeTypeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.PageSizeValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"width",validators:[i.Validators.numberValidator]},{field:"height",validators:[i.Validators.numberValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(l);e.CustomPageSizeValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"name",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.PageValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var i=t.prototype.validate.call(this,e,r,a);if(i)return i;var o=["actualSize","fitToWidth","oneColumn"];return o.indexOf(e)<0?[{message:'pageView must be a string with one of the following values: "actualSize", "fitToWidth", "oneColumn"'}]:null},e}(n.StringValidator);e.PageViewFieldValidator=u},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"accessToken",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]},{field:"datasetIds",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringArrayValidator]},{field:"question",validators:[i.Validators.stringValidator]},{field:"viewMode",validators:[i.Validators.viewModeValidator]},{field:"settings",validators:[i.Validators.qnaSettingValidator]},{field:"tokenType",validators:[i.Validators.tokenTypeValidator]},{field:"groupId",validators:[i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.LoadQnaValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"filterPaneEnabled",validators:[i.Validators.booleanValidator]},{field:"hideErrors",validators:[i.Validators.booleanValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.QnaSettingsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"datasetIds",validators:[i.Validators.stringArrayValidator]},{field:"question",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.QnaInterpretInputDataValidator=d},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"name",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.SaveAsParametersValidator=l},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(2),o=function(t){function e(e,r){var a=t.call(this)||this;return a.keyValidators=e,a.valueValidators=r,a}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var i=t.prototype.validate.call(this,e,r,a);if(i)return i;for(var o in e)if(e.hasOwnProperty(o)){for(var n=(r?r+".":"")+a+"."+o,l=0,s=this.keyValidators;l<s.length;l++){var d=s[l];if(i=d.validate(o,n,a))return i}for(var u=0,p=this.valueValidators;u<p.length;u++){var c=p[u];if(i=c.validate(e[o],n,a))return i}}return null},e}(i.ObjectValidator);e.MapValidator=o},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"pageSize",validators:[i.Validators.pageSizeValidator]},{field:"displayOption",validators:[i.Validators.customLayoutDisplayOptionValidator]},{field:"pagesLayout",validators:[i.Validators.pagesLayoutValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.CustomLayoutValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"x",validators:[i.Validators.numberValidator]},{field:"y",validators:[i.Validators.numberValidator]},{field:"z",validators:[i.Validators.numberValidator]},{field:"width",validators:[i.Validators.numberValidator]},{field:"height",validators:[i.Validators.numberValidator]},{field:"displayState",validators:[i.Validators.displayStateValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.VisualLayoutValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"mode",validators:[i.Validators.displayStateModeValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.DisplayStateValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"visualsLayout",validators:[i.Validators.fieldRequiredValidator,i.Validators.pageLayoutValidator]},{field:"defaultLayout",validators:[i.Validators.visualLayoutValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.PageLayoutValidator=u},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(4),o=r(2),n=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"rows",validators:[new o.NumberValidator]},{field:"exportDataType",validators:[new o.EnumValidator([0,1])]}],s=new i.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(o.ObjectValidator);e.ExportDataRequestValidator=n},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=r(2),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var s=[{field:"$schema",validators:[i.Validators.stringValidator,new l.SchemaValidator("http://powerbi.com/product/schema#visualSelector")]},{field:"visualName",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],d=new o.MultipleFieldsValidator(s);return d.validate(e,r,a)},e}(n.ObjectValidator);e.VisualSelectorValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var s=[{field:"$schema",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator,new l.SchemaValidator("http://powerbi.com/product/schema#visualTypeSelector")]},{field:"visualType",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator]}],d=new o.MultipleFieldsValidator(s);return d.validate(e,r,a)},e}(n.ObjectValidator);e.VisualTypeSelectorValidator=d;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var s=[{field:"$schema",validators:[i.Validators.fieldRequiredValidator,i.Validators.stringValidator,new l.SchemaValidator("http://powerbi.com/product/schema#slicerTargetSelector")]},{field:"target",validators:[i.Validators.fieldRequiredValidator,i.Validators.slicerTargetValidator]}],d=new o.MultipleFieldsValidator(s);return d.validate(e,r,a)},e}(n.ObjectValidator);e.SlicerTargetSelectorValidator=u},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"selector",validators:[i.Validators.fieldRequiredValidator,i.Validators.slicerSelectorValidator]},{field:"state",validators:[i.Validators.fieldRequiredValidator,i.Validators.slicerStateValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.SlicerValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"filters",validators:[i.Validators.filtersArrayValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.SlicerStateValidator=s},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"visualHeaders",validators:[i.Validators.visualHeadersValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.VisualSettingsValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"visible",validators:[i.Validators.booleanValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.VisualHeaderSettingsValidator=s;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"settings",validators:[i.Validators.fieldRequiredValidator,i.Validators.visualHeaderSettingsValidator]},{field:"selector",validators:[i.Validators.visualHeaderSelectorValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.VisualHeaderValidator=d},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(1),o=r(4),n=r(2),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"copy",validators:[i.Validators.singleCommandSettingsValidator]},{field:"drill",validators:[i.Validators.singleCommandSettingsValidator]},{field:"drillthrough",validators:[i.Validators.singleCommandSettingsValidator]},{field:"expandCollapse",validators:[i.Validators.singleCommandSettingsValidator]},{field:"exportData",validators:[i.Validators.singleCommandSettingsValidator]},{field:"includeExclude",validators:[i.Validators.singleCommandSettingsValidator]},{field:"removeVisual",validators:[i.Validators.singleCommandSettingsValidator]},{field:"search",validators:[i.Validators.singleCommandSettingsValidator]},{field:"seeData",validators:[i.Validators.singleCommandSettingsValidator]},{field:"sort",validators:[i.Validators.singleCommandSettingsValidator]},{field:"spotlight",validators:[i.Validators.singleCommandSettingsValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.CommandsSettingsValidator=l;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"displayOption",validators:[i.Validators.fieldRequiredValidator,i.Validators.commandDisplayOptionValidator]},{field:"selector",validators:[i.Validators.visualCommandSelectorValidator]}],s=new o.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(n.ObjectValidator);e.SingleCommandSettingsValidator=s},function(t,e,r){var a=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(e,r){function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}}();Object.defineProperty(e,"__esModule",{value:!0});var i=r(4),o=r(2),n=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.validate=function(e,r,a){if(null==e)return null;var n=t.prototype.validate.call(this,e,r,a);if(n)return n;var l=[{field:"themeJson",validators:[new o.ObjectValidator]}],s=new i.MultipleFieldsValidator(l);return s.validate(e,r,a)},e}(o.ObjectValidator);e.CustomThemeValidator=n}])})},function(t,e,r){var a=this&&this.__extends||function(t,e){function r(){this.constructor=t}for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=r(2),o=r(5),n=r(3),l=r(7),s=r(8),d=r(10),u=r(11),p=function(t){function e(r,a,i,o,n,l){var s=i;t.call(this,r,a,s,l,o,n),this.loadPath="/report/load",this.phasedLoadPath="/report/prepare",Array.prototype.push.apply(this.allowedEvents,e.allowedEvents),this.bookmarksManager=new u.BookmarksManager(r,s,this.iframe)}return a(e,t),e.findIdFromEmbedUrl=function(t){var e,r=/reportId="?([^&]+)"?/,a=t.match(r);return a&&(e=a[1]),e},e.prototype.render=function(t){return this.service.hpm.post("/report/render",t,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},e.prototype.getFilters=function(){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.service.hpm.get("/report/filters",{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},e.prototype.getId=function(){var t=this.config,r=t.id||this.element.getAttribute(e.reportIdAttribute)||e.findIdFromEmbedUrl(t.embedUrl);if("string"!=typeof r||0===r.length)throw new Error("Report id is required, but it was not found. You must provide an id either as part of embed configuration or as attribute '"+e.reportIdAttribute+"'.");return r},e.prototype.getPages=function(){var t=this;return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.service.hpm.get("/report/pages",{
uid:this.config.uniqueId},this.iframe.contentWindow).then(function(e){return e.body.map(function(e){return new s.Page(t,e.name,e.displayName,e.isActive,e.visibility,e.defaultSize,e.defaultDisplayOption)})},function(t){throw t.body})},e.prototype.page=function(t,e,r,a){return new s.Page(this,t,e,r,a)},e.prototype.print=function(){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.service.hpm.post("/report/print",null,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},e.prototype.removeFilters=function(){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.setFilters([])},e.prototype.setPage=function(t){if(n.isRDLEmbed(this.config.embedUrl))return Promise.reject(l.APINotSupportedForRDLError);var e={name:t,displayName:null,isActive:!0};return this.service.hpm.put("/report/pages/active",e,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},e.prototype.setFilters=function(t){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.service.hpm.put("/report/filters",t,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},e.prototype.updateSettings=function(t){return n.isRDLEmbed(this.config.embedUrl)&&null!=t.customLayout?Promise.reject(l.APINotSupportedForRDLError):this.service.hpm.patch("/report/settings",t,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},e.prototype.validate=function(t){return o.validateReportLoad(t)},e.prototype.configChanged=function(t){var r=this.config;this.isMobileSettings(r.settings)&&(r.embedUrl=n.addParamToUrl(r.embedUrl,"isMobile","true"));var a=this.element.getAttribute(e.filterPaneEnabledAttribute),i=this.element.getAttribute(e.navContentPaneEnabledAttribute),o={filterPaneEnabled:null==a?d.Defaults.defaultSettings.filterPaneEnabled:"false"!==a,navContentPaneEnabled:null==i?d.Defaults.defaultSettings.navContentPaneEnabled:"false"!==i};this.config.settings=n.assign({},o,r.settings),t||(r.id=this.getId())},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"reportEmbed"},e.prototype.switchMode=function(t){var e;e="string"==typeof t?t:this.viewModeToString(t);var r="/report/switchMode/"+e;return this.service.hpm.post(r,null,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},e.prototype.refresh=function(){return this.service.hpm.post("/report/refresh",null,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},e.prototype.isSaved=function(){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):n.isSavedInternal(this.service.hpm,this.config.uniqueId,this.iframe.contentWindow)},e.prototype.applyTheme=function(t){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.applyThemeInternal(t)},e.prototype.resetTheme=function(){return n.isRDLEmbed(this.config.embedUrl)?Promise.reject(l.APINotSupportedForRDLError):this.applyThemeInternal({})},e.prototype.applyThemeInternal=function(t){return this.service.hpm.put("/report/theme",t,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body})["catch"](function(t){throw t.body})},e.prototype.viewModeToString=function(t){var e;switch(t){case o.ViewMode.Edit:e="edit";break;case o.ViewMode.View:e="view"}return e},e.prototype.isMobileSettings=function(t){return t&&(t.layoutType===o.LayoutType.MobileLandscape||t.layoutType===o.LayoutType.MobilePortrait)},e.allowedEvents=["filtersApplied","pageChanged","commandTriggered","swipeStart","swipeEnd","bookmarkApplied"],e.reportIdAttribute="powerbi-report-id",e.filterPaneEnabledAttribute="powerbi-settings-filter-pane-enabled",e.navContentPaneEnabledAttribute="powerbi-settings-nav-content-pane-enabled",e.typeAttribute="powerbi-type",e.type="Report",e}(i.Embed);e.Report=p},function(t,e){e.APINotSupportedForRDLError="This API is currently not supported for RDL reports"},function(t,e,r){var a=r(9),i=r(5),o=r(3),n=r(7),l=function(){function t(t,e,r,a,i,o,n){this.report=t,this.name=e,this.displayName=r,this.isActive=a,this.visibility=i,this.defaultSize=o,this.defaultDisplayOption=n}return t.prototype.getFilters=function(){return this.report.service.hpm.get("/report/pages/"+this.name+"/filters",{uid:this.report.config.uniqueId},this.report.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.removeFilters=function(){return this.setFilters([])},t.prototype.setActive=function(){var t={name:this.name,displayName:null,isActive:!0};return this.report.service.hpm.put("/report/pages/active",t,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)["catch"](function(t){throw t.body})},t.prototype.setFilters=function(t){return this.report.service.hpm.put("/report/pages/"+this.name+"/filters",t,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow)["catch"](function(t){throw t.body})},t.prototype.getVisuals=function(){var t=this;return o.isRDLEmbed(this.report.config.embedUrl)?Promise.reject(n.APINotSupportedForRDLError):this.report.service.hpm.get("/report/pages/"+this.name+"/visuals",{uid:this.report.config.uniqueId},this.report.iframe.contentWindow).then(function(e){return e.body.map(function(e){return new a.VisualDescriptor(t,e.name,e.title,e.type,e.layout)})},function(t){throw t.body})},t.prototype.hasLayout=function(t){if(o.isRDLEmbed(this.report.config.embedUrl))return Promise.reject(n.APINotSupportedForRDLError);var e=i.LayoutType[t];return this.report.service.hpm.get("/report/pages/"+this.name+"/layoutTypes/"+e,{uid:this.report.config.uniqueId},this.report.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t}();e.Page=l},function(t,e){var r=function(){function t(t,e,r,a,i){this.name=e,this.title=r,this.type=a,this.layout=i,this.page=t}return t.prototype.getFilters=function(){return this.page.report.service.hpm.get("/report/pages/"+this.page.name+"/visuals/"+this.name+"/filters",{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.removeFilters=function(){return this.setFilters([])},t.prototype.setFilters=function(t){return this.page.report.service.hpm.put("/report/pages/"+this.page.name+"/visuals/"+this.name+"/filters",t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)["catch"](function(t){throw t.body})},t.prototype.exportData=function(t,e){var r={rows:e,exportDataType:t};return this.page.report.service.hpm.post("/report/pages/"+this.page.name+"/visuals/"+this.name+"/exportData",r,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.setSlicerState=function(t){return this.page.report.service.hpm.put("/report/pages/"+this.page.name+"/visuals/"+this.name+"/slicer",t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)["catch"](function(t){throw t.body})},t.prototype.getSlicerState=function(){return this.page.report.service.hpm.get("/report/pages/"+this.page.name+"/visuals/"+this.name+"/slicer",{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.clone=function(t){return void 0===t&&(t={}),this.page.report.service.hpm.post("/report/pages/"+this.page.name+"/visuals/"+this.name+"/clone",t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.sortBy=function(t){return this.page.report.service.hpm.put("/report/pages/"+this.page.name+"/visuals/"+this.name+"/sortBy",t,{uid:this.page.report.config.uniqueId},this.page.report.iframe.contentWindow)["catch"](function(t){throw t.body})},t}();e.VisualDescriptor=r},function(t,e){var r=function(){function t(){}return t.defaultSettings={filterPaneEnabled:!0},t.defaultQnaSettings={filterPaneEnabled:!1},t}();e.Defaults=r},function(t,e,r){var a=r(3),i=r(7),o=function(){function t(t,e,r){this.service=t,this.config=e,this.iframe=r}return t.prototype.getBookmarks=function(){return a.isRDLEmbed(this.config.embedUrl)?Promise.reject(i.APINotSupportedForRDLError):this.service.hpm.get("/report/bookmarks",{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.apply=function(t){if(a.isRDLEmbed(this.config.embedUrl))return Promise.reject(i.APINotSupportedForRDLError);var e={name:t};return this.service.hpm.post("/report/bookmarks/applyByName",e,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},t.prototype.play=function(t){if(a.isRDLEmbed(this.config.embedUrl))return Promise.reject(i.APINotSupportedForRDLError);var e={playMode:t};return this.service.hpm.post("/report/bookmarks/play",e,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},t.prototype.capture=function(){return a.isRDLEmbed(this.config.embedUrl)?Promise.reject(i.APINotSupportedForRDLError):this.service.hpm.post("/report/bookmarks/capture",null,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},t.prototype.applyState=function(t){if(a.isRDLEmbed(this.config.embedUrl))return Promise.reject(i.APINotSupportedForRDLError);var e={state:t};return this.service.hpm.post("/report/bookmarks/applyState",e,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},t}();e.BookmarksManager=o},function(t,e,r){var a=this&&this.__extends||function(t,e){function r(){this.constructor=t}for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=r(5),o=r(2),n=r(3),l=function(t){function e(e,r,a,i,o){t.call(this,e,r,a,void 0,i,o)}return a(e,t),e.prototype.getId=function(){var t=this.createConfig&&this.createConfig.datasetId?this.createConfig.datasetId:e.findIdFromEmbedUrl(this.config.embedUrl);if("string"!=typeof t||0===t.length)throw new Error("Dataset id is required, but it was not found. You must provide an id either as part of embed configuration.");return t},e.prototype.validate=function(t){return i.validateCreateReport(t)},e.prototype.configChanged=function(t){if(!t){var e=this.config;this.createConfig={accessToken:e.accessToken,datasetId:e.datasetId||this.getId(),groupId:e.groupId,settings:e.settings,tokenType:e.tokenType,theme:e.theme}}},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"reportEmbed"},e.prototype.isSaved=function(){return n.isSavedInternal(this.service.hpm,this.config.uniqueId,this.iframe.contentWindow)},e.findIdFromEmbedUrl=function(t){var e,r=/datasetId="?([^&]+)"?/,a=t.match(r);return a&&(e=a[1]),e},e}(o.Embed);e.Create=l},function(t,e,r){var a=this&&this.__extends||function(t,e){function r(){this.constructor=t}for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=r(2),o=r(5),n=function(t){function e(r,a,i,o,n){t.call(this,r,a,i,void 0,o,n),this.loadPath="/dashboard/load",this.phasedLoadPath="/dashboard/prepare",Array.prototype.push.apply(this.allowedEvents,e.allowedEvents)}return a(e,t),e.findIdFromEmbedUrl=function(t){var e,r=/dashboardId="?([^&]+)"?/,a=t.match(r);return a&&(e=a[1]),e},e.prototype.getId=function(){var t=this.config,r=t.id||this.element.getAttribute(e.dashboardIdAttribute)||e.findIdFromEmbedUrl(t.embedUrl);if("string"!=typeof r||0===r.length)throw new Error("Dashboard id is required, but it was not found. You must provide an id either as part of embed configuration or as attribute '"+e.dashboardIdAttribute+"'.");return r},e.prototype.validate=function(t){var e=t,r=o.validateDashboardLoad(e);return r?r:this.ValidatePageView(e.pageView)},e.prototype.configChanged=function(t){t||(this.config.id=this.getId())},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"dashboardEmbed"},e.prototype.ValidatePageView=function(t){if(t&&"fitToWidth"!==t&&"oneColumn"!==t&&"actualSize"!==t)return[{message:"pageView must be one of the followings: fitToWidth, oneColumn, actualSize"}]},e.allowedEvents=["tileClicked","error"],e.dashboardIdAttribute="powerbi-dashboard-id",e.typeAttribute="powerbi-type",e.type="Dashboard",e}(i.Embed);e.Dashboard=n},function(t,e,r){var a=this&&this.__extends||function(t,e){function r(){this.constructor=t}for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=r(5),o=r(2),n=function(t){function e(r,a,i,o,n){var l=i;t.call(this,r,a,l,void 0,o,n),this.loadPath="/tile/load",Array.prototype.push.apply(this.allowedEvents,e.allowedEvents)}return a(e,t),e.prototype.getId=function(){var t=this.config,r=t.id||e.findIdFromEmbedUrl(this.config.embedUrl);if("string"!=typeof r||0===r.length)throw new Error("Tile id is required, but it was not found. You must provide an id either as part of embed configuration.");return r},e.prototype.validate=function(t){var e=t;return i.validateTileLoad(e)},e.prototype.configChanged=function(t){t||(this.config.id=this.getId())},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"tileEmbed"},e.findIdFromEmbedUrl=function(t){var e,r=/tileId="?([^&]+)"?/,a=t.match(r);return a&&(e=a[1]),e},e.type="Tile",e.allowedEvents=["tileClicked","tileLoaded"],e}(o.Embed);e.Tile=n},function(t,e,r){var a=this&&this.__extends||function(t,e){function r(){this.constructor=t}for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=r(5),o=r(2),n=function(t){function e(r,a,i,o,n){t.call(this,r,a,i,void 0,o,n),this.loadPath="/qna/load",this.phasedLoadPath="/qna/prepare",Array.prototype.push.apply(this.allowedEvents,e.allowedEvents)}return a(e,t),e.prototype.getId=function(){return null},e.prototype.setQuestion=function(t){var e={question:t};return this.service.hpm.post("/qna/interpret",e,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},e.prototype.configChanged=function(t){},e.prototype.getDefaultEmbedUrlEndpoint=function(){return"qnaEmbed"},e.prototype.validate=function(t){return i.validateLoadQnaConfiguration(t)},e.type="Qna",e.allowedEvents=["loaded","visualRendered"],e}(o.Embed);e.Qna=n},function(t,e,r){var a=this&&this.__extends||function(t,e){function r(){this.constructor=t}for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)},i=r(5),o=r(6),n=function(t){function e(e,r,a,i,o,n){t.call(this,e,r,a,i,o,n)}return a(e,t),e.prototype.load=function(e,r){var a=e;if(a.accessToken){if("string"!=typeof a.pageName||0===a.pageName.length)throw new Error("Page name is required when embedding a visual.");if("string"!=typeof a.visualName||0===a.visualName.length)throw new Error("Visual name is required, but it was not found. You must provide a visual name as part of embed configuration.");var o=a.width?a.width:this.iframe.offsetWidth,n=a.height?a.height:this.iframe.offsetHeight,l={type:i.PageSizeType.Custom,width:o,height:n},s={};return s[a.pageName]={defaultLayout:{displayState:{mode:i.VisualContainerDisplayMode.Hidden}},visualsLayout:{}},s[a.pageName].visualsLayout[a.visualName]={displayState:{mode:i.VisualContainerDisplayMode.Visible},x:1,y:1,z:1,width:l.width,height:l.height},a.settings=a.settings||{},a.settings.filterPaneEnabled=!1,a.settings.navContentPaneEnabled=!1,a.settings.layoutType=i.LayoutType.Custom,a.settings.customLayout={displayOption:i.DisplayOption.FitToPage,pageSize:l,pagesLayout:s},t.prototype.load.call(this,a,r)}},e.prototype.getPages=function(){throw e.GetPagesNotSupportedError},e.prototype.setPage=function(t){throw e.SetPageNotSupportedError},e.prototype.getFilters=function(t){var e=this.getFiltersLevelUrl(t);return this.service.hpm.get(e,{uid:this.config.uniqueId},this.iframe.contentWindow).then(function(t){return t.body},function(t){throw t.body})},e.prototype.setFilters=function(t,e){var r=this.getFiltersLevelUrl(e);return this.service.hpm.put(r,t,{uid:this.config.uniqueId},this.iframe.contentWindow)["catch"](function(t){throw t.body})},e.prototype.removeFilters=function(t){return this.setFilters([],t)},e.prototype.getFiltersLevelUrl=function(t){var e=this.config;switch(t){case i.FiltersLevel.Report:return"/report/filters";case i.FiltersLevel.Page:return"/report/pages/"+e.pageName+"/filters";default:return"/report/pages/"+e.pageName+"/visuals/"+e.visualName+"/filters"}},e.type="visual",e.GetPagesNotSupportedError="Get pages is not supported while embedding a visual.",e.SetPageNotSupportedError="Set page is not supported while embedding a visual.",e}(o.Report);e.Visual=n},function(t,e,r){var a=r(4),i=r(18),o=r(19),n=r(20);e.hpmFactory=function(t,e,r,i){return void 0===r&&(r=a["default"].version),void 0===i&&(i=a["default"].type),new o.HttpPostMessage(t,{"x-sdk-type":i,"x-sdk-version":r},e)},e.wpmpFactory=function(t,e,r){return new i.WindowPostMessageProxy({processTrackingProperties:{addTrackingProperties:o.HttpPostMessage.addTrackingProperties,getTrackingProperties:o.HttpPostMessage.getTrackingProperties},isErrorMessage:o.HttpPostMessage.isErrorMessage,suppressWarnings:!0,name:t,logMessages:e,eventSourceOverrideWindow:r})},e.routerFactory=function(t){return new n.Router(t)}},function(t,e,r){/*! window-post-message-proxy v0.2.5 | (c) 2016 Microsoft Corporation MIT */
!function(e,r){t.exports=r()}(this,function(){return function(t){function e(a){if(r[a])return r[a].exports;var i=r[a]={exports:{},id:a,loaded:!1};return t[a].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var r={};return e.m=t,e.c=r,e.p="",e(0)}([function(t,e){"use strict";var r=function(){function t(e){var r=this;void 0===e&&(e={processTrackingProperties:{addTrackingProperties:t.defaultAddTrackingProperties,getTrackingProperties:t.defaultGetTrackingProperties},isErrorMessage:t.defaultIsErrorMessage,receiveWindow:window,name:t.createRandomString()}),this.pendingRequestPromises={},this.addTrackingProperties=e.processTrackingProperties&&e.processTrackingProperties.addTrackingProperties||t.defaultAddTrackingProperties,this.getTrackingProperties=e.processTrackingProperties&&e.processTrackingProperties.getTrackingProperties||t.defaultGetTrackingProperties,this.isErrorMessage=e.isErrorMessage||t.defaultIsErrorMessage,this.receiveWindow=e.receiveWindow||window,this.name=e.name||t.createRandomString(),this.logMessages=e.logMessages||!1,this.eventSourceOverrideWindow=e.eventSourceOverrideWindow,this.suppressWarnings=e.suppressWarnings||!1,this.logMessages&&console.log("new WindowPostMessageProxy created with name: "+this.name+" receiving on window: "+this.receiveWindow.document.title),this.handlers=[],this.windowMessageHandler=function(t){return r.onMessageReceived(t)},this.start()}return t.defaultAddTrackingProperties=function(e,r){return e[t.messagePropertyName]=r,e},t.defaultGetTrackingProperties=function(e){return e[t.messagePropertyName]},t.defaultIsErrorMessage=function(t){return!!t.error},t.createDeferred=function(){var t={resolve:null,reject:null,promise:null},e=new Promise(function(e,r){t.resolve=e,t.reject=r});return t.promise=e,t},t.createRandomString=function(){return(Math.random()+1).toString(36).substring(7)},t.prototype.addHandler=function(t){this.handlers.push(t)},t.prototype.removeHandler=function(t){var e=this.handlers.indexOf(t);if(e===-1)throw new Error("You attempted to remove a handler but no matching handler was found.");this.handlers.splice(e,1)},t.prototype.start=function(){this.receiveWindow.addEventListener("message",this.windowMessageHandler)},t.prototype.stop=function(){this.receiveWindow.removeEventListener("message",this.windowMessageHandler)},t.prototype.postMessage=function(e,r){var a={id:t.createRandomString()};this.addTrackingProperties(r,a),this.logMessages&&(console.log(this.name+" Posting message:"),console.log(JSON.stringify(r,null,"  "))),e.postMessage(r,"*");var i=t.createDeferred();return this.pendingRequestPromises[a.id]=i,i.promise},t.prototype.sendResponse=function(t,e,r){this.addTrackingProperties(e,r),this.logMessages&&(console.log(this.name+" Sending response:"),console.log(JSON.stringify(e,null,"  "))),t.postMessage(e,"*")},t.prototype.onMessageReceived=function(t){var e=this;this.logMessages&&(console.log(this.name+" Received message:"),console.log("type: "+t.type),console.log(JSON.stringify(t.data,null,"  ")));var r=this.eventSourceOverrideWindow||t.source,a=t.data;if("object"!=typeof a)return void(this.suppressWarnings||console.warn("Proxy("+this.name+"): Received message that was not an object. Discarding message"));var i;try{i=this.getTrackingProperties(a)}catch(o){this.suppressWarnings||console.warn("Proxy("+this.name+"): Error occurred when attempting to get tracking properties from incoming message:",JSON.stringify(a,null,"  "),"Error: ",o)}var n;if(i&&(n=this.pendingRequestPromises[i.id]),n){var l=!0;try{l=this.isErrorMessage(a)}catch(o){console.warn("Proxy("+this.name+") Error occurred when trying to determine if message is consider an error response. Message: ",JSON.stringify(a,null,""),"Error: ",o)}l?n.reject(a):n.resolve(a),delete this.pendingRequestPromises[i.id]}else{var s=this.handlers.some(function(t){var o=!1;try{o=t.test(a)}catch(n){e.suppressWarnings||console.warn("Proxy("+e.name+"): Error occurred when handler was testing incoming message:",JSON.stringify(a,null,"  "),"Error: ",n)}if(o){var l=void 0;try{l=Promise.resolve(t.handle(a))}catch(n){e.suppressWarnings||console.warn("Proxy("+e.name+"): Error occurred when handler was processing incoming message:",JSON.stringify(a,null,"  "),"Error: ",n),l=Promise.resolve()}return l.then(function(t){if(!t){var o="Handler for message: "+JSON.stringify(a,null,"  ")+" did not return a response message. The default response message will be returned instead.";e.suppressWarnings||console.warn("Proxy("+e.name+"): "+o),t={warning:o}}e.sendResponse(r,t,i)}),!0}});s||this.suppressWarnings||console.warn("Proxy("+this.name+") did not handle message. Handlers: "+this.handlers.length+"  Message: "+JSON.stringify(a,null,"")+".")}},t.messagePropertyName="windowPostMessageProxy",t}();e.WindowPostMessageProxy=r}])})},function(t,e,r){/*! http-post-message v0.2.3 | (c) 2016 Microsoft Corporation MIT */
!function(e,r){t.exports=r()}(this,function(){return function(t){function e(a){if(r[a])return r[a].exports;var i=r[a]={exports:{},id:a,loaded:!1};return t[a].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var r={};return e.m=t,e.c=r,e.p="",e(0)}([function(t,e){"use strict";var r=function(){function t(t,e,r){void 0===e&&(e={}),this.defaultHeaders=e,this.defaultTargetWindow=r,this.windowPostMessageProxy=t}return t.addTrackingProperties=function(t,e){return t.headers=t.headers||{},e&&e.id&&(t.headers.id=e.id),t},t.getTrackingProperties=function(t){return{id:t.headers&&t.headers.id}},t.isErrorMessage=function(t){return"number"==typeof(t&&t.statusCode)&&!(200<=t.statusCode&&t.statusCode<300)},t.prototype.get=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=this.defaultTargetWindow),this.send({method:"GET",url:t,headers:e},r)},t.prototype.post=function(t,e,r,a){return void 0===r&&(r={}),void 0===a&&(a=this.defaultTargetWindow),this.send({method:"POST",url:t,headers:r,body:e},a)},t.prototype.put=function(t,e,r,a){return void 0===r&&(r={}),void 0===a&&(a=this.defaultTargetWindow),this.send({method:"PUT",url:t,headers:r,body:e},a)},t.prototype.patch=function(t,e,r,a){return void 0===r&&(r={}),void 0===a&&(a=this.defaultTargetWindow),this.send({method:"PATCH",url:t,headers:r,body:e},a)},t.prototype["delete"]=function(t,e,r,a){return void 0===e&&(e=null),void 0===r&&(r={}),void 0===a&&(a=this.defaultTargetWindow),this.send({method:"DELETE",url:t,headers:r,body:e},a)},t.prototype.send=function(t,e){if(void 0===e&&(e=this.defaultTargetWindow),t.headers=this.assign({},this.defaultHeaders,t.headers),!e)throw new Error("target window is not provided.  You must either provide the target window explicitly as argument to request, or specify default target window when constructing instance of this class.");return this.windowPostMessageProxy.postMessage(e,t)},t.prototype.assign=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(void 0===t||null===t)throw new TypeError("Cannot convert undefined or null to object");var a=Object(t);return e.forEach(function(t){if(void 0!==t&&null!==t)for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(a[e]=t[e])}),a},t}();e.HttpPostMessage=r}])})},function(t,e,r){/*! powerbi-router v0.1.5 | (c) 2016 Microsoft Corporation MIT */
!function(e,r){t.exports=r()}(this,function(){return function(t){function e(a){if(r[a])return r[a].exports;var i=r[a]={exports:{},id:a,loaded:!1};return t[a].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var r={};return e.m=t,e.c=r,e.p="",e(0)}([function(t,e,r){"use strict";var a=r(1),i=function(){function t(t){this.handlers=t,this.getRouteRecognizer=new a,this.patchRouteRecognizer=new a,this.postRouteRecognizer=new a,this.putRouteRecognizer=new a,this.deleteRouteRecognizer=new a}return t.prototype.get=function(t,e){return this.registerHandler(this.getRouteRecognizer,"GET",t,e),this},t.prototype.patch=function(t,e){return this.registerHandler(this.patchRouteRecognizer,"PATCH",t,e),this},t.prototype.post=function(t,e){return this.registerHandler(this.postRouteRecognizer,"POST",t,e),this},t.prototype.put=function(t,e){return this.registerHandler(this.putRouteRecognizer,"PUT",t,e),this},t.prototype["delete"]=function(t,e){return this.registerHandler(this.deleteRouteRecognizer,"DELETE",t,e),this},t.prototype.registerHandler=function(t,e,r,a){var i=function(t){var e=new o;return Promise.resolve(a(t,e)).then(function(t){return e})};t.add([{path:r,handler:i}]);var n={test:function(r){if(r.method!==e)return!1;var a=t.recognize(r.url);if(void 0===a)return!1;var i=a[0];return r.params=i.params,r.queryParams=a.queryParams,r.handler=i.handler,!0},handle:function(t){return t.handler(t)}};this.handlers.addHandler(n)},t}();e.Router=i;var o=function(){function t(){this.statusCode=200,this.headers={},this.body=null}return t.prototype.send=function(t,e){this.statusCode=t,this.body=e},t}();e.Response=o},function(t,e,r){var a;(function(t){(function(){"use strict";function i(t,e,r){this.path=t,this.matcher=e,this.delegate=r}function o(t){this.routes={},this.children={},this.target=t}function n(t,e,r){return function(a,o){var l=t+a;return o?void o(n(l,e,r)):new i(t+a,e,r)}}function l(t,e,r){for(var a=0,i=0;i<t.length;i++)a+=t[i].path.length;e=e.substr(a);var o={path:e,handler:r};t.push(o)}function s(t,e,r,a){var i=e.routes;for(var o in i)if(i.hasOwnProperty(o)){var n=t.slice();l(n,o,i[o]),e.children[o]?s(n,e.children[o],r,a):r.call(a,n)}}function d(t){return"[object Array]"===Object.prototype.toString.call(t)}function u(t){this.string=t}function p(t){this.name=t}function c(t){this.name=t}function f(){}function h(t,e,r){"/"===t.charAt(0)&&(t=t.substr(1));var a=t.split("/"),i=new Array(a.length);r.val="";for(var o=0;o<a.length;o++){var n,l=a[o];(n=l.match(/^:([^\/]+)$/))?(i[o]=new p(n[1]),e.push(n[1]),r.val+="3"):(n=l.match(/^\*([^\/]+)$/))?(i[o]=new c(n[1]),r.val+="1",e.push(n[1])):""===l?(i[o]=new f,r.val+="2"):(i[o]=new u(l),r.val+="4")}return r.val=+r.val,i}function v(t){this.charSpec=t,this.nextStates=[],this.charSpecs={},this.regex=void 0,this.handlers=void 0,this.specificity=void 0}function y(t){return t.sort(function(t,e){return e.specificity.val-t.specificity.val})}function m(t,e){for(var r=[],a=0,i=t.length;a<i;a++){var o=t[a];r=r.concat(o.match(e))}return r}function g(t){this.queryParams=t||{}}function V(t,e,r){var a=t.handlers,i=t.regex,o=e.match(i),n=1,l=new g(r);l.length=a.length;for(var s=0;s<a.length;s++){for(var d=a[s],u=d.names,p={},c=0;c<u.length;c++)p[u[c]]=o[n++];l[s]={handler:d.handler,params:p,isDynamic:!!u.length}}return l}function w(t){t=t.replace(/\+/gm,"%20");var e;try{e=decodeURIComponent(t)}catch(r){e=""}return e}i.prototype={to:function(t,e){var r=this.delegate;if(r&&r.willAddRoute&&(t=r.willAddRoute(this.matcher.target,t)),this.matcher.add(this.path,t),e){if(0===e.length)throw new Error("You must have an argument in the function passed to `to`");this.matcher.addChild(this.path,t,e,this.delegate)}return this}},o.prototype={add:function(t,e){this.routes[t]=e},addChild:function(t,e,r,a){var i=new o(e);this.children[t]=i;var l=n(t,i,a);a&&a.contextEntered&&a.contextEntered(e,l),r(l)}};var b=function(t,e){var r=new o;t(n("",r,this.delegate)),s([],r,function(t){e?e(this,t):this.add(t)},this)},E=["/",".","*","+","?","|","(",")","[","]","{","}","\\"],S=new RegExp("(\\"+E.join("|\\")+")","g");u.prototype={eachChar:function(t){for(var e,r=this.string,a=0;a<r.length;a++)e=r.charAt(a),t=t.put({invalidChars:void 0,repeat:!1,validChars:e});return t},regex:function(){return this.string.replace(S,"\\$1")},generate:function(){return this.string}},p.prototype={eachChar:function(t){return t.put({invalidChars:"/",repeat:!0,validChars:void 0})},regex:function(){return"([^/]+)"},generate:function(t){return t[this.name]}},c.prototype={eachChar:function(t){return t.put({invalidChars:"",repeat:!0,validChars:void 0})},regex:function(){return"(.+)"},generate:function(t){return t[this.name]}},f.prototype={eachChar:function(t){return t},regex:function(){return""},generate:function(){return""}},v.prototype={get:function(t){if(this.charSpecs[t.validChars])return this.charSpecs[t.validChars];for(var e=this.nextStates,r=0;r<e.length;r++){var a=e[r],i=a.charSpec.validChars===t.validChars;if(i=i&&a.charSpec.invalidChars===t.invalidChars)return this.charSpecs[t.validChars]=a,a}},put:function(t){var e;return(e=this.get(t))?e:(e=new v(t),this.nextStates.push(e),t.repeat&&e.nextStates.push(e),e)},match:function(t){for(var e,r,a,i=this.nextStates,o=[],n=0;n<i.length;n++)e=i[n],r=e.charSpec,"undefined"!=typeof(a=r.validChars)?a.indexOf(t)!==-1&&o.push(e):"undefined"!=typeof(a=r.invalidChars)&&a.indexOf(t)===-1&&o.push(e);return o}};var T=Object.create||function(t){function e(){}return e.prototype=t,new e};g.prototype=T({splice:Array.prototype.splice,slice:Array.prototype.slice,push:Array.prototype.push,length:0,queryParams:null});var P=function(){this.rootState=new v,this.names={}};P.prototype={add:function(t,e){for(var r,a=this.rootState,i="^",o={},n=new Array(t.length),l=[],s=!0,d=0;d<t.length;d++){var u=t[d],p=[],c=h(u.path,p,o);l=l.concat(c);for(var v=0;v<c.length;v++){var y=c[v];y instanceof f||(s=!1,a=a.put({invalidChars:void 0,repeat:!1,validChars:"/"}),i+="/",a=y.eachChar(a),i+=y.regex())}var m={handler:u.handler,names:p};n[d]=m}s&&(a=a.put({invalidChars:void 0,repeat:!1,validChars:"/"}),i+="/"),a.handlers=n,a.regex=new RegExp(i+"$"),a.specificity=o,(r=e&&e.as)&&(this.names[r]={segments:l,handlers:n})},handlersFor:function(t){var e=this.names[t];if(!e)throw new Error("There is no route named "+t);for(var r=new Array(e.handlers.length),a=0;a<e.handlers.length;a++)r[a]=e.handlers[a];return r},hasRoute:function(t){return!!this.names[t]},generate:function(t,e){var r=this.names[t],a="";if(!r)throw new Error("There is no route named "+t);for(var i=r.segments,o=0;o<i.length;o++){var n=i[o];n instanceof f||(a+="/",a+=n.generate(e))}return"/"!==a.charAt(0)&&(a="/"+a),e&&e.queryParams&&(a+=this.generateQueryString(e.queryParams,r.handlers)),a},generateQueryString:function(t,e){var r=[],a=[];for(var i in t)t.hasOwnProperty(i)&&a.push(i);a.sort();for(var o=0;o<a.length;o++){i=a[o];var n=t[i];if(null!=n){var l=encodeURIComponent(i);if(d(n))for(var s=0;s<n.length;s++){var u=i+"[]="+encodeURIComponent(n[s]);r.push(u)}else l+="="+encodeURIComponent(n),r.push(l)}}return 0===r.length?"":"?"+r.join("&")},parseQueryString:function(t){for(var e=t.split("&"),r={},a=0;a<e.length;a++){var i,o=e[a].split("="),n=w(o[0]),l=n.length,s=!1;1===o.length?i="true":(l>2&&"[]"===n.slice(l-2)&&(s=!0,n=n.slice(0,l-2),r[n]||(r[n]=[])),i=o[1]?w(o[1]):""),s?r[n].push(i):r[n]=i}return r},recognize:function(t){var e,r,a,i=[this.rootState],o={},n=!1;if(a=t.indexOf("?"),a!==-1){var l=t.substr(a+1,t.length);t=t.substr(0,a),o=this.parseQueryString(l)}for(t=decodeURI(t),"/"!==t.charAt(0)&&(t="/"+t),e=t.length,e>1&&"/"===t.charAt(e-1)&&(t=t.substr(0,e-1),n=!0),r=0;r<t.length&&(i=m(i,t.charAt(r)),i.length);r++);var s=[];for(r=0;r<i.length;r++)i[r].handlers&&s.push(i[r]);i=y(s);var d=s[0];if(d&&d.handlers)return n&&"(.+)$"===d.regex.source.slice(-5)&&(t+="/"),V(d,t,o)}},P.prototype.map=b,P.VERSION="0.1.11";var O=P;r(3).amd?(a=function(){return O}.call(e,r,e,t),!(void 0!==a&&(t.exports=a))):"undefined"!=typeof t&&t.exports?t.exports=O:"undefined"!=typeof this&&(this.RouteRecognizer=O)}).call(this)}).call(e,r(2)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children=[],t.webpackPolyfill=1),t}},function(t,e){t.exports=function(){throw new Error("define cannot be used indirect")}}])})}])});