<template><!-- 纺织服装/家居生活 -->
    <div class="trf-detail sgs_smart_trf_trf_detail_afl">
        <el-row :gutter="24">
            <el-col :span="4">
                <ul class="nav-list" ref="nav" id="trf-left-nav">
                    <li @click="toTarget(index)" :class="{'is-sub': item.isSub}" v-for="(item, index) in navList" v-show="item.showStep <= showStep" :key="item.name" class="nav-item">
                        <h5>{{ $t(item.alias) }}</h5>
                    </li>
                </ul>
            </el-col>
            <el-col :span="16">
                <!-- 新版调到顶部了 -->
                <!-- <el-table :data="trfShowData" style="width: 100%">
                    <el-table-column
                            prop="trfNo"
                            :label="$t('trfList.trfNo')" style="width:20%">
                    </el-table-column>
                    <el-table-column
                            prop="createTime"
                            :label="$t('trfList.createDate')" style="width:20%">
                    </el-table-column>
                    <el-table-column
                            prop="trfSubmissionDate" :formatter="trfTableDateFormtter"
                            :label="$t('trfList.submitDate')" style="width:20%">
                    </el-table-column>
                    <el-table-column
                            prop="trfStatus"
                            :label="$t('trfList.jobStatus')" :formatter="trfTableStatusFormtter" style="width:20%">
                    </el-table-column>
                    <el-table-column v-if="isShowRemark"
                                    prop="pendingReason"
                                    :label="$t('operation.remark')" style="width:30%">
                    </el-table-column>
                </el-table> -->

                <el-form label-position="top" :model="trf" :rules="trfRules" ref="trf" @submit.native.prevent
                        label-width="260px" size="medium"
                        class="demo-form-inline sgs-form">

                    <!-- 客户信息 -->
                    <el-card class="sgs-box content-item" id="customer_card">
                        <div class="sgs-group">
                            <h3>{{$t('trf.customerInfo')}}</h3>
                        </div>
                        <!-- 申请方信息 -->
                        <div class="content-item">
                            <el-row>
                                <h4 class="sgs-title">{{$t('trf.applicantInfo')}}</h4>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col span="12">
                                    <!--非动态校验-->
                                    <el-form-item ref="trfCustomer.customerNameZh" :label="$t('customer.name')" prop="trfCustomer.customerNameZh"
                                                :rules="{required: true,message:$t('trf.validate.requiredBlur'),trigger:'blur',pattern: '[^ \x22]+' }">
                                        <el-input maxlength="200" v-model="trf.trfCustomer.customerNameZh" @clear="handleBlur"
                                                :disabled="showCustomerNameFlag || trfCustomerDisabled"></el-input>
                                    </el-form-item>
                                </el-col>
                                <!--非动态校验-->
                                <el-col span="12">
                                    <el-form-item ref="trfCustomer.customerAddressZh" :label="$t('customer.address')" prop="trfCustomer.customerAddressZh"
                                                :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                                        <el-select v-model="trf.trfCustomer.customerAddressZh" clearable
                                                   allow-create
                                                   default-first-option
                                                   :disabled="trfDisabled"
                                                   @clear="handleBlur"
                                                   @change="customerAddressChange"
                                                   filterable
                                                   style="width: 100%;">
                                            <el-option v-for="(address,index) in customerAddressData"
                                                    :label="address.addressDetail" :value="address.addressDetail"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20" class="contact">
                                <!--非动态校验-->
                                <el-col span="12">
                                    <el-form-item ref="trfCustomerContact.applyContactName" :label="$t('contact.title.default')" prop="trfCustomerContact.applyContactName"
                                                :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                                        <el-select v-model="trf.trfCustomerContact.applyContactName"
                                                   @change="contactrNameChange"
                                                   @clear="handleBlur"
                                                   default-first-option
                                                   clearable
                                                   allow-create
                                                   filterable
                                                   :disabled="trfDisabled"
                                                   style="width: 100%;">
                                            <el-option v-for="(contact,index) in customerContactData"
                                                    :label="contact.contactName"
                                                    :value="contact.contactName"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item ref="trfCustomerContact.applyContactTel" :label="$t('contact.phone')" prop="trfCustomerContact.applyContactTel"
                                                :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                        <el-input maxlength="128" v-model="trf.trfCustomerContact.applyContactTel" clearable
                                                allow-create
                                                @clear="handleBlur"
                                                :disabled="trfDisabled"
                                                autocomplete="off"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col span="12">
                                    <el-form-item ref="trfCustomerContact.applyContactEmail" :label="$t('contact.email')" prop="trfCustomerContact.applyContactEmail"
                                                :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                        <el-input maxlength="100" v-model="trf.trfCustomerContact.applyContactEmail"
                                                  allow-create
                                                  default-first-option
                                                  :disabled="trfDisabled"
                                                  autocomplete="off"
                                                  @clear="handleBlur"
                                                  @blur="concatEmailBlur"
                                                  clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 付款方信息 -->
                        <div class="content-item">
                            <el-row style="width:100%;display:flex;align-items:center;border-bottom:1px solid #eee">
                                <h4 class="sgs-title" style="border:none;padding:0">{{$t('trf.payInfo')}}</h4>
                                <div style="margin:10px;">
                                    <el-checkbox-group v-model="identicalFlag" :disabled="trfDisabled">
                                        <el-checkbox :label="$t('trf.sameApplicant')" name="type"></el-checkbox>
                                    </el-checkbox-group>
                                </div>
                            </el-row>
                            <el-row :gutter="20" v-if="!identicalFlag" style="margin-top:10px">
                                <!--非动态校验-->
                                <el-col span="12">
                                    <el-form-item :label="$t('customer.payNameNew1')" prop="trfCustomer.payCustomerNameZh"
                                                :rules="{required: true,message:$t('trf.validate.requiredBlur'),trigger:'blur',pattern: '[^ \x22]+' }">
                                        <el-input maxlength="100" v-model="trf.trfCustomer.payCustomerNameZh"
                                                :disabled="trfDisabled"
                                                @clear="handleBlur"
                                                autocomplete="off"
                                                clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>

                    <!-- 基础服务信息 -->
                    <el-card v-show="showStep >= 1" id='basic_card' :class="['sgs-box', showStep >= 1 ? 'content-item':'']">
                        <div class="sgs-group">
                            <h3>{{$t('trf.basicInfoAfl')}}</h3>
                        </div>
                        <el-row :gutter="20">
                            <el-col span="12">
                                <!--非动态校验-->
                                <el-form-item :label="$t('service.serviceType')" prop="serviceType">
                                    <el-select v-model="trf.serviceType" @change="serviceTypeChange"
                                            style="width: 100%;" :placeholder="$t('service.serviceType')"
                                            :disabled="trfDisabled"
                                            clearable>
                                        <el-option v-for="(serviceType,index) in serviceTypeData"
                                                :label="serviceType.serviceTypeName"
                                                :value="serviceType.serviceTypeCode"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <!--非动态校验-->
                            <el-col span="12">
                                <el-form-item ref="trfLabContact.labContactId" :label="$t('labContact.labContactNameAfl')" v-if="valiUsable('labContactId')"
                                            prop="trfLabContact.labContactId"
                                            :rules="{required:true,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                                    <el-select v-model="trf.trfLabContact.labContactId"
                                            :disabled="trfDisabled"
                                            clearable
                                            @blur.capture.native="handleBlur"
                                            @focus="handleFocus"
                                            @clear="handleBlur"
                                            filterable @change="selectAflUserChange"
                                            style="width: 100%;">
                                        <el-option v-for="(labContact,index) in alflHostListForCurrentUserList"
                                                :label="labContact.trueName"
                                                :value="labContact.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 样品信息 -->
                    <el-card :class="['sgs-box', showStep >= 2 ? 'content-item':'']" v-show="showStep >= 2" id="product_card">
                        <div class="sgs-group">
                            <h3>{{$t('trf.productAndSample')}}</h3>
                        </div>
                        <!--DFF Form-->
                        <el-scrollbar>
                            <el-row id="dff-form" name="DFF Form" v-if="isDffFormLoadFlag">
                                <div v-for="item in dffFormList" :key='item.id' :class="['sgs-box', showStep >= 2 ? 'content-item':'']">
                                    <p class="form_item_header"><i v-if="item.mandatoryFlag==1" class="mandatoryFlag">*</i>{{item.dispalyName}}
                                    </p>
                                    <div class="form_item_box" v-if="item.fieldType == 'Select'">
                                        <el-radio-group v-model="dffObjNew[item.fieldCode]" @change="dffFormChange(item,$event)"
                                                        :disabled="trfDisabled">
                                            <el-radio :label="item1.value" v-for="(item1,index) in item.attrValue"
                                                    class="checkbox_box" :checked='index==0?true:false'>
                                                {{item1.valueCode}}
                                            </el-radio>
                                        </el-radio-group>
                                    </div>
                                    <div class="form_item_box" v-else-if="item.fieldType == 'Select2'">
                                        <el-checkbox-group v-model="dffObjNew[item.fieldCode]"
                                                        @change="dffFormChange(item,$event)" :disabled="trfDisabled">
                                            <el-checkbox
                                                    :label="item1.value"
                                                    v-for="item1 in item.attrValue"
                                                    class="checkbox_box"
                                                    :disabled="(!dffFormDanger&&item.dispalyName=='危险性')?true:false">
                                                {{item1.valueCode}}
                                            </el-checkbox>
                                        </el-checkbox-group>
                                        <p style="position:absolute;left:0;bottom:-40px;color:#F56C6C"
                                        v-if="dffObjNew[item.fieldCode].length==0 && item.trfDisabledDff">必填项不能为空</p>
                                    </div>
                                </div>
                            </el-row>
                        </el-scrollbar>
                        <!--DFF Grid-->
                        <el-row v-if="dffFormTableList.length>0" :class="['sgs-box', showStep >= 2 ? 'content-item':'']">
                            <!-- 请填写或批量导入样品信息 -->
                            <h4 class="sgs-title">{{$t('trf.dffGridTitle')}}</h4>
                        </el-row>
                        <div class="dffGird_wrap" v-if="dffFormTableList.length>0">
                            <div class="addGrid_btn">
                                <el-input size="small" class="dffDridIpt" style="text-align: center;" min="1" placeholder="请输入数量" v-model="dffGridIpt"
                                        :disabled="trfDisabled"></el-input>
                                <el-button type="primary" size="small" @click="addDffGrid" :disabled="trfDisabled">
                                    {{$t('trf.trfAddBtn')}}
                                </el-button>
                                <el-button type="primary" size="small" @click="handleExport" :disabled="trfDisabled">
                                    {{$t('trf.trfDownBtn')}}
                                </el-button>
                                <el-upload
                                        class="upload-demo"
                                        ref="upload1"
                                        accept=".xls,.xlsx"
                                        :disabled="trfDisabled"
                                        :show-file-list='false'
                                        :http-request='handleImport'>
                                    <el-button size="small" class="uploadResume" :disabled="trfDisabled">
                                        {{$t('trf.trfUploadBtn')}}
                                    </el-button>
                                </el-upload>
                            </div>
                            <div class="dffGrid_list">
                                <div class="dffGirg_item" v-for='item in dffFormTableList[0]'><i v-if="item.mandatoryFlag==1"
                                                                                                class="mandatoryFlag">*</i>{{item.dispalyName}}
                                </div>
                                <div class="dffGirg_del"></div>
                            </div>
                            <div class="dffGrid_list" v-for='(item,index) in dffFormTableList'>
                                <div class="dffGirg_item" v-for="item1 in item">
                                    <el-tooltip :content="item1[item1.fieldCode]" placement="top" effect="light">
                                        <div class="test-input" :class="{'is-required': item1.dispalyName == '样品名称'}">
                                            <input type="text" class="ipt2"
                                            v-if="!item1.fieldCode.endsWith('Date') && item1.fieldType=='Input'"
                                            v-model="item1[item1.fieldCode]" :disabled="trfDisabled">
                                        </div>
                                        <el-input maxlength="300" class="dff_grid_textarea" type="textarea"
                                                v-model="item1[item1.fieldCode]"
                                                v-if="!item1.fieldCode.endsWith('Date') && item1.fieldType=='TextArea'"
                                                :disabled="trfDisabled"
                                                autocomplete="off"></el-input>
                                        <el-date-picker
                                                v-if="item1.fieldCode.endsWith('Date')"
                                                v-model="item1[item1.fieldCode]"
                                                value-format="yyyy-MM-dd"
                                                type="date"
                                                :disabled="trfDisabled"
                                                placeholder="选择日期"
                                        ></el-date-picker>
                                    </el-tooltip>
                                </div>
                                <div class="dffGirg_del">
                                    <span class="glyphicon glyphicon-trash" @click="dffGridDelOne(index)"
                                        :class="trfDisabled?'background':''"></span>
                                    <span class="glyphicon glyphicon-copy" @click="dffGridCopyOne(index)"
                                        :class="trfDisabled?'background':''"></span>
                                </div>
                                <p style="position:absolute;left:0;bottom:-40px;color:#F56C6C" v-if="trfDisabledDffGrid">
                                    必填项不能为空</p>
                            </div>
                        </div>
                        <!-- CARE LABEL 水洗标签 -->
<!--                        <el-scrollbar>
                            <doc-section id="care-label" name="Care Label" v-if="isCareLabelLoadFlag">
                                <care-label
                                        :care-instructions="careInstructions"
                                        :product-item-nos-list="productItemNosList"
                                        :buyer-customer-group-code="buyerCustomerGroupCode"
                                        :buyer-or-agent-name="buyerOrAgentName"
                                        :interfaces-object="interfacesObject"
                                        :disabled-flag="trfDisabled"
                                        @saveCheck="saveCheck"
                                />
                            </doc-section>
                        </el-scrollbar>-->
                    </el-card>

                    <!-- 报告需求 -->
                    <el-card v-show="showStep >= 3" id="service_card" :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                        <div class="sgs-group">
                            <h3>{{$t('service.reportRequire')}}</h3>
                        </div>
                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header">{{$t('service.reportLanguage')}}</p>
                            <div class="form_item_box">
                                <el-checkbox-group v-model="trf.servicRequire.reportLanguage" @change="reportLanguageChange"
                                                :disabled="trfDisabled">
                                    <el-checkbox label="2" class="checkbox_box1">{{$t('service.chineseReport')}}</el-checkbox>
                                    <el-checkbox label="1" class="checkbox_box1">{{$t('service.enReport')}}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>

                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header">{{$t('service.reportConsistent')}}</p>
                            <div class="form_item_box">
                                <el-radio-group v-model="trf.servicRequire.isIdentical" :disabled="trfDisabled"
                                                @change="isIdenticalChange">
                                    <el-radio :label="1" class="checkbox_box1">{{$t('common.yes')}}</el-radio>
                                    <el-radio :label="2" class="checkbox_box1">{{$t('common.no')}}</el-radio>
                                </el-radio-group>
                            </div>
                            <el-row :gutter="20">
                                <el-col span="24" v-if="reportLanguageVerify('zh')">
                                    <el-form-item ref="servicRequire.reportHeader" :label="$t('service.reportHeaderZh')" v-if="valiUsable('reportHeader')"
                                                prop="servicRequire.reportHeader"
                                                :rules="{required:trf.servicRequire.reportLanguage.includes('2')?true:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                        <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportHeader"
                                                :disabled="trfDisabled || isIdenticalDisabled"
                                                autocomplete="off"
                                                clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col span="24" v-if="reportLanguageVerify('zh')">
                                    <el-form-item ref="servicRequire.reportAddress" :label="$t('service.reportAddressZh')" v-if="valiUsable('reportAddress')"
                                                prop="servicRequire.reportAddress"
                                                :rules="{required:trf.servicRequire.reportLanguage.includes('2')?true:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                        <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportAddress"
                                                :disabled="trfDisabled || isIdenticalDisabled"
                                                autocomplete="off"
                                                clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col span="24" v-if="reportLanguageVerify('en')">
                                    <el-form-item ref="servicRequire.reportHeaderEn" :label="$t('service.reportHeaderEn')" v-if="valiUsable('reportHeaderEn')"
                                                prop="servicRequire.reportHeaderEn"
                                                :rules="{required:trf.servicRequire.reportLanguage.includes('1')?true:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                        <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportHeaderEn"
                                                :disabled="trfDisabled"
                                                autocomplete="off"
                                                clearable></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col span="24" v-if="reportLanguageVerify('en')">
                                    <el-form-item ref="servicRequire.reportAddressEn" :label="$t('service.reportAddressEn')" v-if="valiUsable('reportAddressEn')"
                                                prop="servicRequire.reportAddressEn"
                                                :rules="{required:trf.servicRequire.reportLanguage.includes('1')?true:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                        <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.reportAddressEn"
                                                :disabled="trfDisabled"
                                                autocomplete="off"
                                                clearable></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header">{{$t('service.reportMethod')}}</p>
                            <div class="form_item_box" style="flex-wrap: wrap;">
                                <el-radio-group v-model="trf.servicRequire.reportMethod" :disabled="trfDisabled">
                                    <el-radio label="1" class="checkbox_box1">{{$t('service.oneSample')}}</el-radio>
                                    <el-radio label="2" class="checkbox_box1">{{$t('service.multipleSample')}}</el-radio>
                                </el-radio-group>
                                <el-checkbox style="margin-left:30px" true-label='1' false-label='0' class="checkbox_box1"
                                            :disabled="trfDisabled" v-model="otherReportMethodCheckBox"
                                            @change='otherReportMethodCheckBoxChange'>
                                    {{$t('service.other')}}
                                    <input type="text" class="ipt" v-model="trf.servicRequire.otherReportMethod"
                                        :disabled="trfDisabled" v-if="otherReportMethodCheckBox==1">
                                </el-checkbox>
                                <div>
                                    <el-checkbox :true-label='1' :false-label='0' class="checkbox_box1" :disabled="trfDisabled"
                                            v-model="trf.servicRequire.needProductStandards">
                                        {{$t('service.productStandards')}}
                                    </el-checkbox>
                                </div>
                            </div>
                        </div>

                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header" >{{$t('service.reportType')}}</p>
                            <div class="form_item_box">
                                <el-checkbox-group v-model="trf.servicRequire.reportType" :disabled="trfDisabled">
                                    <el-checkbox label="1" class="checkbox_box1">{{$t('service.electronic')}}</el-checkbox>
                                    <el-checkbox label="2" class="checkbox_box1">{{$t('service.paper')}}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>

                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header" >{{$t('service.qualificationMark')}}</p>
                            <div class="form_item_box cma-cnas">
                                <el-radio-group v-model="trf.servicRequire.qualificationMark" :disabled="trfDisabled">
                                    <el-radio label="1" class="checkbox_box1">{{$t('service.noAdd')}}</el-radio>
                                    <el-radio label="2" class="checkbox_box1">{{$t('service.addCNAS')}}</el-radio>
                                    <el-radio label="3" class="checkbox_box1">{{$t('service.addCMA')}}</el-radio>
                                    <el-radio label="4" class="checkbox_box1">{{$t('service.addCNASAndCMA')}}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>

                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header" >{{$t('service.isReportReflectCustomerInformation')}}</p>
                            <div class="form_item_box">
                                <el-radio-group v-model="trf.servicRequire.isReportReflectCustomerInformation"
                                                :disabled="trfDisabled">
                                    <el-radio :label="1" class="checkbox_box1">{{$t('common.yes')}}</el-radio>
                                    <el-radio :label="0" class="checkbox_box1">{{$t('common.no')}}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>

                        <div :class="['sgs-box', showStep >= 3 ? 'content-item':'']">
                            <p class="form_item_header">{{$t('service.resultJudging')}}</p>
                            <div class="form_item_box">
                                <el-radio-group v-model="trf.servicRequire.resultJudging" :disabled="trfDisabled">
                                    <el-radio :label="1" class="checkbox_box1">{{$t('common.yes')}}</el-radio>
                                    <el-radio :label="0" class="checkbox_box1">{{$t('common.no')}}</el-radio>
                                </el-radio-group>
                            </div>
                            <el-form-item :label="$t('service.judgementPrinciple')" v-if="valiUsable('judgementPrinciple')"
                                        prop="servicRequire.judgementPrinciple"
                                        :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                                <el-input type="text" maxlength="200" v-model="trf.servicRequire.judgementPrinciple" clearable
                                        :disabled="trfDisabled"
                                        autocomplete="off"></el-input>
                            </el-form-item>

                            <el-row>
                                <el-col span="24">
                                    <el-form-item :label="$t('service.reportReceivingEmail')"
                                                prop="servicRequire.reportReceivingEmail">
                                        <el-input type="text" maxlength="200" v-model="trf.servicRequire.reportReceivingEmail"
                                                clearable
                                                :disabled="trfDisabled"
                                                autocomplete="off"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col span="24">
                                    <el-form-item :label="$t('service.otherRequest')" v-if="valiUsable('otherRequire')"
                                                prop="servicRequire.otherRequire"
                                                :rules="{required:false,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                                        <el-input type="textarea" maxlength="200" v-model="trf.servicRequire.otherRequire"
                                                :placeholder="$t('service.otherTipAfl')" clearable
                                                :disabled="trfDisabled"
                                                autocomplete="off"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>

                    <!-- 附件 -->
                    <el-card v-show="showStep >= 4" id="attachment_card" :class="['sgs-box', showStep >= 4 ? 'content-item':'']" >
                        <div class="sgs-group">
                            <h3 style="margin-bottom: 20px;">{{$t('trf.attachment')}}</h3>
                            <div class="right">
<!--                                <el-upload
                                        :disabled="trfDisabled"
                                        class="upload-demo"
                                        action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                        :on-success="uploadSuccess"
                                        :on-change="uploadChange"
                                        :file-list="fileList"
                                        :show-file-list="false">
                                    <el-button size="small" v-if="trf.trfStatus < 2" type="primary" :disabled="trfDisabled">
                                        {{$t('operation.upload')}}
                                    </el-button>
                                </el-upload>-->
                                <el-button size="small"  v-if="trf.trfStatus < 2" type="primary" :disabled="trfDisabled" @click="openUpload">
                                  {{$t('operation.upload')}}
                                </el-button>
                                <sgs-batch-upload :systemID="1" :limit="5" :handle-upload-success="uploadSuccess" :handle-upload-error="handleUploadError"  ref="batchUpload" :upload-url="uploadUrl" :file-max-sizes="5"></sgs-batch-upload>
                            </div>
                        </div>
                        <el-table :data="trf.trfAttachments" width="100%"
                                :element-loading-text="$t('uploadLoadingText')"
                                element-loading-spinner="el-icon-loading"
                                v-loading="uploadLoading">
                            <el-table-column
                                    type="index"
                                    fixed
                                    label="#"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="fileName"
                                    :label="$t('attachment.name')">
                            </el-table-column>
                            <el-table-column
                                    :label="$t('operation.title')"
                                    width="180">
                                <template slot-scope="scope">
                                    <el-button type="text" @click="downloadAttachmentRow(scope.row)"
                                            size="small" icon="el-icon-download">{{$t('operation.download')}}
                                    </el-button>
                                    <el-button @click="removeAttachmentRow(scope.$index)" type="text" :disabled='trfDisabled'
                                            size="small" icon="el-icon-delete">{{$t('operation.remove')}}
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                    <!-- <communication v-if="trf.trfStatus>1" :customerContactData="customerContactData"
                                :communicationTableData="communicationTableData" :trfNo="trf.trfNo"
                                :trfStatus="trf.trfStatus" :trfEmail="trf.trfCustomerContact.applyContactEmail"
                                :trfId="trf.id" :trfType="30">
                    </communication> -->
                    <!--添加-->
                    <!--<el-row>
                        <span style="margin-left: 18px"><b>如果报告不加盖 CMA 的标识，此类检测数据、结果仅用于客户科研、教学、内部质量控制、产品研发等目的，仅供内部参考。</b></span>
                    </el-row>
                    <el-row></el-row>-->

                    <el-button v-show="showStep < 4" @click="showStep++" id="create-trf-contunue" type="primary" style="margin-top: 24px; margin-right: 24px;">
                        {{ $t('guide.continue') }}
                    </el-button>

                    <el-row>
                        <span style="font-size: 14px;
                                    margin-top: 20px;
                                    display: block;
                                    color: #999;">{{$t('trf.serviceClause')}} <a target="_blank" style="color: #ff6600"
                                                                                    href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a></span>
                    </el-row>
                    <el-row class="sgs-footer">
                        <el-button
                                v-if="permissionList.returnTrfBtn && trf.trfStatus==2 && userInfo.userMgtId == trf.applicantUser "
                                type="primary"
                                :loading="btnSubmitLoading"
                                @click="returnTrfStatusClick()">{{$t('trf.returnTrfSatus')}}
                        </el-button>
                        <el-button v-if="permissionList.confirmSampleBtn && trf.trfStatus==2&&optionType!='detail'"
                                type="primary" :loading="btnSubmitLoading"
                                @click="confirmSampleClick()">{{$t('trf.confirmSample')}}
                        </el-button>
                        <el-button type="primary" v-if="cancelTrfBtnIsShow"
                                :loading="btnSubmitLoading" @click="cancelTrf('6')">{{$t('operation.cancel')}}
                        </el-button>
                        <el-button type="primary" v-if="saveTrfBtnIsShow"
                                :loading="btnSubmitLoading"
                                @click="onSubmit('1')">
                            {{$t('operation.save')}}
                        </el-button>
                        <el-button type="primary" v-if="submitTrfBtnIsShow"
                                :loading="btnSubmitLoading"
                                @click="onSubmit('2')">
                            {{$t('operation.submit')}}
                        </el-button>
                        <el-button type="primary" @click="saveAsTrfTemplate"
                                v-if="saveAsTemplateBtnIsShow&&!$route.query.applicationUser"
                                :loading="btnSubmitLoading">{{$t('operation.saveAsTemplate')}}
                        </el-button>
                        <!-- 从设置-AFl客户管理列表进来saveAsTemplate -->
                        <el-button type="primary" @click="saveAsTrfTemplate" v-if="$route.query.applicationUser"
                                :loading="btnSubmitLoading">{{$t('operation.saveAsTemplate')}}
                        </el-button>
                        <el-button type="primary" @click="toTrfList('0')" :loading="btnSubmitLoading">
                            {{$t('operation.backToList')}}
                        </el-button>
                        <el-button
                                v-if="permissionList.privewTemplateBtn && trf.trfStatus >1  && trf.trfStatus!=6 "
                                type="primary" @click="toPrivewTemplate('0')"
                                :loading="btnSubmitLoading">
                            {{$t('trf.print')}}
                        </el-button>
                        <el-button
                                v-if="permissionList.trfHistoryBtn && trf.id!='' && optionType!='copy' && optionType !='trfTemplate'"
                                type="primary" @click="trfHistoryClick"
                                :loading="btnSubmitLoading">
                            {{$t('trf.history.title')}}
                        </el-button>

                    </el-row>
                    <el-dialog :title="$t('operation.remark')" :visible.sync="testPackageVisible">
                        <el-form-item :label="$t('operation.remark')" label-width="160px">
                            <el-input maxlength="250" v-model="remark" autocomplete="off" :disabled="trfDisabled"></el-input>
                        </el-form-item>
                        <div slot="footer" class="dialog-footer">
                            <el-button size="small" @click="testPackageVisible = false">{{$t('operation.cancel')}}
                            </el-button>
                            <el-button size="small" type="primary" :disabled="trfDisabled"
                                    @click="addTestPackageRemark()">{{$t('operation.confirm')}}
                            </el-button>
                        </div>
                    </el-dialog>
                </el-form>
            </el-col>

            <el-col :span="4" v-if="trf.trfStatus>1">
                <div class="scroll-box clearfix">
                    <h5 class="add-comment-title">{{ $t('communicationLog.title') }}</h5>
                    <div class="add-comment">
                        <communication v-if="trf.trfStatus>1"
                            @closeDialog="getCommentData"
                            :showWithBtn="true"
                            :customerContactData="customerContactData"
                            :communicationTableData="communicationTableData"
                            :trfNo="trf.trfNo"
                            :trfStatus="trf.trfStatus"
                            :trfEmail="trf.trfCustomerContact.applyContactEmail"
                            :trfId="trf.id"
                            :product-line-code="productLineCode"
                            >
                        </communication>
                    </div>
                    <div class="inner-scroll clearfix">
                        <dl class="comment-list pull-right">
                            <dd v-for="(item, index) in communicationTableData" :key="index">
                                <el-card shadow="always">
                                    <div class="comment">{{ item.comment }}</div>
                                    <div class="c-info">
                                        <p><i class="el-icon-user"></i> {{ item.createUser }}</p>
                                        <p v-if="item.contactEmail != ''"><i class="el-icon-message"></i> {{ item.contactEmail }}</p>
                                        <p v-if="item.fileUrl != ''">
                                            <i class="el-icon-link"></i>&nbsp;
                                            <el-link type="primary" @click="downAttachment(item.fileUrl)">{{ $t('attachment.title') }}</el-link>
                                        </p>
                                        <p><i class="el-icon-date"></i> {{ item.createTime }}</p>
                                    </div>
                                </el-card>
                            </dd>
                        </dl>
                    </div>
                </div>
            </el-col>
        </el-row>

        <trf-history :visible.sync="historyVisible" :trfId="this.trf.id"
        ></trf-history>

        <el-dialog :title="$t('operation.saveAsTemplate')" :visible.sync="trfTemplateDialogFormVisible">
            <el-form :model="trfUserTemplate" ref="trfUserTemplateForm" rules="trfUserTemplateRules">
                <el-form-item :label="$t('trf.trfTemplateName')" label-width="160px" prop="trfTemplateName">
                    <el-input maxlength="200" v-model="trfUserTemplate.trfTemplateName" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="trfTemplateDialogFormVisible = false">{{$t('operation.cancel')}}
                </el-button>
                <el-button size="small" type="primary" :loading="btnSbuTrfTemplateLoading"
                           @click="saveTrfUserTemplate('trfUserTemplate')">{{$t('operation.confirm')}}
                </el-button>
            </div>
        </el-dialog>


        <el-dialog :title="$t('trf.confirmSample')" :visible.sync="trfConfirmSampleDialogFormVisible">
            <el-form :model="confirmSampleForm" ref="confirmSampleForm" rules="confirmSampleFormRules">
                <el-row>
                    <el-form-item :label="$t('trf.sampleReceivedDate')" prop="sampleReceiveDate">
                        <el-date-picker clearable
                                        v-model="confirmSampleForm.sampleReceiveDate"
                                        type="date"
                                        :placeholder="$t('trf.sampleReceivedDate')"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :picker-options="pickerOptions1"
                                        style="width: 100%;">
                        </el-date-picker>
                    </el-form-item>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="trfConfirmSampleDialogFormVisible = false">{{$t('operation.cancel')}}
                </el-button>
                <el-button size="small" type="primary" :loading="btnConfirmSampleLoading"
                           @click="confirmSampleDateSubmit('trfUserTemplate')">{{$t('operation.confirm')}}
                </el-button>
            </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="printTrfTipDialogVisible" width="530px">
            <div class="printTrfTip_info">
                <span class="orange_text">{{$t('trf.printTrfTipAFL')}}</span>
                <p>TRF编号:{{trf.trfNo}}</p>
                <span>如有疑问，请随时联系您的专属客户经理：</span>
                <p>
                    <span class="orange_text">{{trf.trfLabContact.contactName}}</span>
                </p>
                <p>{{trf.trfLabContact.contactTel}}</p>
                <!-- <p>收件地址:{{trf.trfLab.labAddress}}</p> -->
            </div>
            <span slot="footer" class="dialog-footer">
                <!-- <el-button @click="printTrfTipDialogVisible = false">取 消</el-button> -->
                <el-button type="primary" @click="printTrfTipSubmit">确 定</el-button>
            </span>
        </el-dialog>
        <!-- <p class="chat_btn" v-if="trfStatus!=1&&optionType!='add'" @click="openChat" :class="!socketTask?'gray':''">沟通</p> -->
    </div>
</template>
<script>
    /*import {bizComponent} from 'biz-component'*/
    import {mapGetters} from "vuex";
    import {deepClone} from '@/util/util'
    import {getDeptBeLongTosByTemplateId} from "@/api/customer/customerDeptBeLongTo";
    import {getFieldSettingsByTemplateId, detail} from "@/api/template/template";
    import {
        getCommunicationLogList
    } from "@/api/trf/communication";
    import {
        getCustomerGroupByParms,
        getServiceType,
        getCloudFileURL,
        getDffFormComfirmIdBatchByIds,
        checkDffFormIdSmapleGroup,
        getAgentCustomerGroupByParms,
        getCustomerGroup, serviceTypeList
    } from "@/api/common/index";
    import {searchCustomer, queryCustomerByBossNo, queryCustomerForId} from "@/api/customer/customer";
    import {

      getBuyerInfoPage,
      trfTemplateDetail,
      getTemplateList,
      getCustomerAddressList,
      cancelTrf,
      returnTrf,
      saveAflTrfUserTemplate,
      getTemplateTestPackageList,
      buyerPkAgent,
      selectAflHostListForCurrentUser,
      queryDffFromData,
      getLablist,
      customerDetail,
      getContactsListbyUser,
      getContactsListForAfl,
      getAflTrfTemplateId, aflTrfDetail, saveAflTrf, confirmAflSampleDate,
      uploadAflGridExcelData
    } from "@/api/trf/trf";
    import moment from 'moment'
    import {ProductLineEnums} from "@/commons/enums/BuEnums";
    import {validatenull, validateEmail} from "@/util/validate";
    import {export2Excel} from '@/util/excel'
    import {TrfSourceEnums, TrfTypeEnums} from "@/commons/enums/TrfEnums";

    export default {
        name: 'trfDetail',
        inject: ['reload'],
        components: {
            TestResult: resolve => require(['@/views/trf/form/testResult'], resolve),
            Communication: resolve => require(['../../../components/dialog/Communication'], resolve),
            TrfHistory: resolve => require(['../../../components/dialog/trf-history-dialog'], resolve)
        },
        props: {
            trfNo: {
                type: String,
                default: null,
            },
            trfStatus: {
                type: String,
                default: null,
            },
        },
        created() {
        },
        data() {
            var validateContactNameLength = (rule, value, callback) => {
                if (!validatenull(value.trim())) {
                    if (value.length > 50) {
                        return callback(new Error(this.$t('trf.validate.contactNameLengthErrorInfo')));
                    } else {
                        callback();
                    }
                } else {
                    return callback(new Error(this.$t('trf.validate.requiredBlur')));
                }
            };
            return {
                uploadUrl: '/api/sgsapi/FrameWorkApi/file/doUpload',
                showStep: 10,   // 改需求了，全部展开，大于4就行
                navList: [
                    { name: '客户信息', alias:'trf.customerInfo', level: '1', isSub: false, showStep: 0},
                    { name: '申请方信息', alias:'trf.applicantInfo', level: '1-1', isSub: true, showStep: 0 },
                    { name: '付款方信息', alias:'trf.payInfo', level: '1-2', isSub: true, showStep: 0 },

                    { name: '基础服务信息', alias:'trf.basicInfoAfl', level: '2', isSub: false, showStep: 1 },

                    { name: '样品信息', alias:'trf.productAndSample', level: '3', isSub: false, showStep: 2},
                    { name: '存储要求', alias:'trf.requirements', level: '3-1', isSub: true, showStep: 2},
                    { name: '样品保留期限', alias:'trf.sampleRetentionPeriod', level: '3-2', isSub: true, showStep: 2},
                    { name: '样品主要成分', alias:'trf.mainComponentsSample', level: '3-3', isSub: true, showStep: 2},
                    { name: '危险性', alias:'trf.danger', level: '3-4', isSub: true, showStep: 2},
                    { name: '请填写或批量导入样品信息 ', alias:'trf.dffGridTitle', level: '3-5', isSub: true, showStep: 2},

                    { name: '报告需求', alias:'service.reportRequire', level: '4', isSub: false, showStep: 3 },
                    { name: '报告语言', alias:'service.reportLanguage', level: '4-1', isSub: true, showStep: 3},
                    { name: '报告抬头是否与申请方一致', alias:'service.reportConsistent', level: '4-2', isSub: true, showStep: 3},
                    { name: '报告方式', alias:'service.reportMethod', level: '4-3', isSub: true, showStep: 3},
                    { name: '报告类型', alias:'service.reportType', level: '4-4', isSub: true, showStep: 3},
                    { name: '资质标志CMA/CNAS标志', alias:'service.qualificationMark', level: '4-5', isSub: true, showStep: 3},
                    { name: '报告是否体现目的国、买家、供应商及代理商信息', alias:'service.isReportReflectCustomerInformation', level: '4-6', isSub: true, showStep: 3},
                    { name: '结果评判', alias:'service.resultJudging', level: '4-7', isSub: true, showStep: 3},

                    { name: '附件', alias:'attachment.title', level: '5', isSub: false, showStep: 4 }
                ],
                productLineCode:'',
                element: {
                    nav: [],
                    content: []
                },
                historyVisible: false,
                node: {},
                remark: '',
                testPackageVisible: false,
                commponentKey: 0,
                testPackageExpand: false,
                isShowBelongFlag: false,
                showProductCategoryFlag: false,
                // productCategoryData: [],
                templateDialogVisible: false,
                beLongData: [],
                trfDisabled: false,
                templateDisabled: false,
                buyerDisabled: false,
                isShowDffLanguage: false,
                isSupplier: false,
                treeLoading: false,
                uploadLoading: false,
                trfFlag: 3,
                cclValidateTable: true,
                cclValidate: true,
                fieldSettingsData: [],
                saveTrfBtnIsShow: true,
                cancelTrfBtnIsShow: false,
                submitTrfBtnIsShow: true,
                saveAsTemplateBtnIsShow: true,
                emailArray: [],
                // emailGroupData: [],
                hash: '',
                trfConfirmSampleDialogFormVisible: false,
                trfTemplateDialogFormVisible: false,
                labContactFlag: true,
                showCustmerName: '',
                showCustomerNameFlag: true,
                btnSubmitLoading: false,
                btnSbuTrfTemplateLoading: false,
                btnConfirmSampleLoading: false,
                optionType: '',
                isShowSubmitBtn: true,
                isShowRemark: false,
                trfShowData: [],
                customerParams: {
                    customerNumber: '',
                },
                isShow: false,//是否为回显数据
                templateDataLoad: false,//模板数据是否加载完成
                fileList: [],
                isDffFormLoadFlag: false,
                isDffGridLoadFlag: true,
                isCareLabelLoadFlag: false,
                //窗口选中的testPackage数据
                drawerSelTestPackageList: [],
                // filterText: '',
                defaultProps: {
                    label: 'testPackageName',
                    children: 'children',
                    value: 'id'
                },
                selTestPackageTreedata: [],
                testPackageTreedata: [],
                trfId: '',
                dialogSelTestPackageFormVisible: false,
                filesData: [],
                dffFormId: '03a1f3b2-713b-413e-8d3d-ad5cbed44981',
                customerId: '',//查询客户条件
                props: {
                    label: 'testPackageName',
                    children: 'parentId',
                    isLeaf: 'leaf',
                    value: 'id',
                    remark: 'remark',
                },
                onloadTreeFlag: false,
                templateData: [],
                templateLabData: [],
                templateLabContactData: [],
                clientHeight: '',
                buyerCustomerGroupData: [],
                agentCustomerGroupData: [],
                customerAddressData: [],
                customerContactData: [],
                // returnSampleData: [],//退样要求
                serviceTypeData: [],
                reportLanguageData: [],
                // invoiceData: [],
                returnSampleArry: [],
                communicationTableData: [],
                identicalFlag: true,//默认与申请人一样
                isSelfRefrenceFlag: false,
                /*  isCommentFlag: false,
                  isCopyFlag: false,
                  isPhotoFlag: false,
                  isConfimCoverFlag: false,*/
                testPackageIds: [],
                /*表单参数*/
                trf: {
                    trfSourceType: null,
                    trfSourceId: null,
                    testPackageList: [],
                    isSelfRefrence: 0,
                    trfType: 30,
                    materialIds: '',
                    applicantUser: '',
                    id: '',
                    actionType: 'add',
                    remark: '',
                    retestReportNo: '',//重新测试原来的reportNo需录入
                    dffFormId: '',
                    isSame: 1,
                    dffGridId: '',
                    dffGridData: null,
                    dffFormData: null,
                    careLabelData: null,
                    trfStatus: '0',
                    trfTemplateId: '',
                    templateName: '',
                    productLineId: '',
                    productLineCode: 'AFL',
                    productLineName: '',
                    retest_report_no: '',//上次重测编号
                    // buyerCustomerGroupId: '',
                    // buyerCustomerGroupCode: 'General',
                    // buyerCustomerGroupName: '',
                    // agentCustomerGroupId: '',
                    // agentCustomerGroupCode: '',
                    // agentCustomerGroupName: '',
                    serviceType: '',
                    serviceTypeName: '',
                    reportDeliveredTo: '',
                    failedReportDeliveredTo: '',
                    sampleReceiveDate: '',
                    testPackageAllIds: '',

                    trfCustomer: {
                        buyerCustomerGroupId: '',
                        buyerCustomerGroupCode: 'General',
                        buyerCustomerGroupName: '',
                        agentCustomerGroupId: '',
                        agentCustomerGroupCode: '',
                        agentCustomerGroupName: '',
                        sgsCustomerId: '',
                        sgsAccountId: '',
                        customerId: '',
                        customerAddressId: '',
                        customerAddressEn: '',
                        customerAddressZh: '',
                        customerNameZh: '',
                        customerNameEn: '',
                        payCustomerNameZh: '',
                        payCustomerAddressZh: '',
                        payCustomerNameEn: '',
                        payCustomerAddressEn: '',
                        isSame: '1',
                    },
                    // invoice: {},
                    trfCustomerContact: {},
                    trfLab: {},
                    trfLabContact: {},
                    servicRequire: {
                        isIdentical: 1,// 报告抬头是否与申请方一致
                        reportLanguage: ['2'],//报告语言
                        reportLanguageName: 'Chinese Report',
                        reportMethod: '1',// 报告方式
                        reportMethod_other: '',
                        reportType: ['1'],// 报告类型
                        needProductStandards: '',
                        otherReportMethod: '',
                        qualificationMark: '1',//资质标志CMA/CNAS标志
                        reportHeader: '',
                        reportAddress: '',
                        reportHeaderEn: '',
                        reportAddressEn: '',
                        isReportReflectCustomerInformation: 0,
                        resultJudging: 0,
                        judgementPrinciple: '',
                        reportReceivingEmail: ''
                    },
                    trfAttachments: [],
                },
                otherReportMethodCheckBox: 0,// 报告方式其他
                trfUserTemplate: {
                    trfType: 30,
                    trfTemplateName: '',
                    trfTemplateData: '',
                    trf_name:'',
                    trfTemplateId:null,
                    trfBuyerCustomerGroupName:'',
                    trfBuyerCustomerGroupCode:''
                },
                confirmSampleForm: {
                    id: '',
                    sampleReceiveDate: new Date(),
                },
                queryDffFormGeneralDataParam: {
                    customerGroupId: 'General',
                    buCode: 'SL',
                    type: 'FORM',
                    systemCode: "PreOrder",
                    //moduleCode: "TRF-Product"
                },
                buyerCustomerGroupParam: {
                    customerNumber: '',
                    customerGroupCode: '',
                },
                page: {
                    currentPage: 1,
                    pageSize: 1000,
                },
                customerContactParam: {
                    customerId_equal: '',
                    status: '1',
                },
                testPackageParam: {
                    templateId: '',
                    trfStatus: '1'
                },
                customerParam: {
                    number: '',
                    taxNo: '',
                    customerName: '',
                    rows: 20
                },
                customerGroupParam: {
                    rows: 5,
                    groupName: '',
                    groupCode: '',
                },
                agentCustomerParam: {},
                templateDataParam: {
                    productLineCode: 'AFL',
                    isLoadGeneral: 1,
                    customerGroupCode: '',
                    trfStatus: '1',
                },
                templateLabDataParam: {
                    trfTemplateId: '',
                    productLineCode: '',
                    trfStatus: '1',
                },
                templateLabContactDataParam: {
                    trfTemplateId: '',
                    labCode: '',
                    labTypeFlag: '',
                    trfStatus: '1',
                },
                buyerPkAgentParam: {
                    agentGroupCode: '',
                    buyerGroupCode: '',
                },
                sgsLabParam: {
                    buCode: '',
                    labCode: ''
                },
                communicationParam: {},
                emailGroupParam: {
                    status: 1,
                },
                pickerOptions1: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                },
                //****dff Form 组件参数
                languageID: 2,
                languageList: [
                    {value: 1, languageCode: '', name: 'English'}, {value: 2, languageCode: 'CHI', name: 'Chinese'}],
                reqDffFormObj: {
                    url: 'api/sgsapi/DFFV2Api/',
                    id: '',
                    name: '',
                    outQueryCountryUrl: '/api/sgsapi/FrameWorkApi/trims/api/v3/queryCountryAndRegion',
                },
                dffFormParams: {
                    id: '',
                    languageID: 2,
                    languageCode: 'CHI'
                },//样品信息数据参数
                dffFormList: [], // 样品信息数据
                dffObjNew: {}, // 样品信息表单绑定值
                trfDisabledDff: false, // 样品信息提交是否为空
                trfDisabledDffGrid: false,
                dffFormDanger: true, // 样品主要成分是否选择的是非危险性
                dffFormTableParams: {
                    id: '',
                    languageID: 2,
                    languageCode: 'CHI'
                },
                dffFormTableList: [], // 样品信息表格数据
                dffGridTableTemplate: [], // 标品信息表格增加的模板数据
                dffTableObjNew: {}, // 样品信息表格表单绑定值
                dffGridIpt: 1,// 样品表格表单添加数量值
                laboratoryParams: {
                    locationCode: '',
                    productLineCode: 'AFL'
                },
                dffObj: {},
                dffLanguageObj: {
                    "CN": {},
                    "EN": {}
                },
                dffLanguageDefaultObj: {
                    "CN": {},
                    "EN": {}
                },
                customerDetailInfo: '',// 报告抬头数据
                isIdenticalDisabled: false,
                dffTemplates: [],
                dffHeaderConfig: [],
                disabledFlag: false,
                //dff Grid 组件参数
                reqDffGridObj: {
                    url: '/api/sgsapi/DFFV2Api/',
                    id: '',
                    name: '',
                },
                objList: [],
                objListNew: [],
                dffGridTemplates: [],
                dataList: {
                    'EN': [{
                        productItemNo: 'SupplierCode_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }],
                    'CN': [{
                        productItemNo: 'SupplierCode_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }]
                },
                defaultDataList_new: {
                    'EN': [{
                        productItemNo: 'SupplierCode_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }],
                    'CN': [{
                        productItemNo: 'SupplierCode_1',
                        dFFFormID: '',
                        sampleID: 'A'
                    }]
                },
                //***care Label 组件参数
                interfacesObject: {
                    queryFileUrl: '/api/sgs-mart/sgs-api/dowonload-file',
                    deleteFileUrl: '/api/sgs-trims/FrameWorkApi/file/delete',
                    queryCareLabelUrl: '/api/sgs-mart/sgs-trimsClient/queryCareLabel ',
                    queryCareLabelRegionCountryUrl: '/api/sgs-trims/trimsApi/queryCareLabelRegionCountry',
                    queryWarningUrl: '/api/sgs-trims/trimsApi/queryWarning',
                    uploadFileUrl: '/api/sgs-mart/trfApi/uploadCareLabel'
                },
                buyerCustomerGroupCode: '',
                buyerOrAgentName: '',
                careInstructions: [
                    {
                        "careLabelSeq": 1, "productItemNo": ["SupplierCode_1"],
                        "imgArray": [],
                        "radioType": "",
                        "selectCountry": "",
                        "selectImgIds": "",//"623,638,641,652,656",
                        "careInstruction": "",
                        "imgPathYun": []
                    },
                ],
                dafaultCareInstructions: [
                    {
                        "careLabelSeq": 1, "productItemNo": ["SupplierCode_1"],
                        "imgArray": [],
                        "radioType": "",
                        "selectCountry": "",
                        "selectImgIds": "",//"623,638,641,652,656",
                        "careInstruction": "",
                        "imgPathYun": []
                    },
                ],
                productItemNosList: ['SupplierCode_1'],
                //***********careLabel组件参数结束
                trfUserTemplateRules: {
                    trfTemplateName: [
                        {required: true, message: this.$t('trf.placeholder.trfTemplateName'), trigger: 'blur'},
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                },
                trfRules: {
                    trfTemplateId: [
                        {required: true, message: this.$t('trf.templateSel'), trigger: 'change'},
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    'trfLab.labCode': [
                        {required: true, message: this.$t('lab.selLab'), trigger: 'change'}
                    ]
                },
                alflHostListForCurrentUserList: [],
                pdfSrc: '',
                vHtml: '',
                applicationUser: '',
                printTrfTipDialogVisible: false, // 打印申请单dialog
                id: '', // 打印申请单提交后的id
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission", "dimensions", "socketTask", "webSocketReconnectCount","language"]),
            permissionList() {
                return {
                    saveAsTemplateBtn: this.vaildData(this.permission['sgs:trf:saveAsTemplateTrf'], false),
                    returnTrfBtn: this.vaildData(this.permission['sgs:trf:returnTrf'], false),
                    trfSaveBtn: this.vaildData(this.permission['sgs:trf:saveTrf'], false),
                    trfSubmitBtn: this.vaildData(this.permission['sgs:trf:submitTrf'], false),
                    confirmSampleBtn: this.vaildData(this.permission['sgs:trf:confirmSample'], false),
                    cancelBtn: this.vaildData(this.permission['sgs:trf:cancel'], false),
                    privewTemplateBtn: this.vaildData(this.permission['sgs:trf:privewTemplate'], false),
                    trfHistoryBtn: this.vaildData(this.permission['sgs:trf:history'], false),
                };
            },
            emailAddressesValue: {
                get() {
                    if (this.trf.reportDeliveredTo) {
                        return this.$lodash.split(this.trf.reportDeliveredTo, ',')
                    }
                    return []
                },
                set(val) {
                    this.$set(this.trf, 'reportDeliveredTo', this.$lodash.join(val, ','))
                }
            },
            failEmailAddressesValue: {
                get() {
                    if (this.trf.failedReportDeliveredTo) {
                        return this.$lodash.split(this.trf.failedReportDeliveredTo, ',')
                    }
                    return []
                },
                set(val) {
                    this.$set(this.trf, 'failedReportDeliveredTo', this.$lodash.join(val, ','))
                }
            },
        },
        watch: {
            'hash': function (newVal) {
                //获取路由参数
                this.getParams();
            },
            // filterText(val) {
            //     this.$refs.selTree.filter(val);
            // },
            //监听语言变化
            language: function (newVal) {
                if (newVal == 'en-US') {
                    this.dffFormParams.languageID = 1
                    this.dffFormParams.languageCode = ''
                    this.dffFormTableParams.languageID = 1
                    this.dffFormTableParams.languageCode = ''
                } else {
                    this.dffFormParams.languageID = 2
                    this.dffFormParams.languageCode = 'CHI'
                    this.dffFormTableParams.languageID = 2
                    this.dffFormTableParams.languageCode = 'CHI'
                }
                this.getqueryDffFormData()
                this.getqueryDffFormTableData()
                //触发查询
                // this.getReturnSampleArry();
                //this.queryCustomer();
                // this.queryReportLanguage();
                //重新触发查询communication log
                if (this.trf.trfStatus >= 2) {
                    this.queryCommunicationLog();
                }
                //查询BU
                // this.queryProductCategory();
            },
            // 监听`clientHeight`发生改变
            clientHeight: function () {
                this.changeFixed(this.clientHeight)
            },
            identicalFlag: function (newVal) {
                debugger;
                this.trf.trfCustomer.isSame = 0;
                this.$set(this.trf,'isSame',0);
                this.$set(this.trf.trfCustomerContact, 'isSame', 0);
                if (newVal) {
                    this.trf.isSame == 1;
                    this.trf.trfCustomer.isSame = 1;
                    this.$set(this.trf.trfCustomerContact, 'isSame', 1);
                }
            },
            isSelfRefrenceFlag: function (newVal) {
                this.$set(this.trf, 'isSelfRefrence', 0);
                if (newVal) {
                    this.$set(this.trf, 'isSelfRefrence', 1);
                }
            },
        },
        filters: {
            remarkFilter: function (value) {
                value = value.toString();
                if (value.length > 20) {
                    value = value.slice(0, 20) + '...'
                }
                return value
            }
        },
        mounted() {
            console.log('访问组件===>', 'trfDetail_3');
            let that = this
            that.productLineCode=ProductLineEnums.AFL.CODE;
            if (this.$store.getters.language == 'zh-CN') {
                this.dffFormParams.languageID = 2
                this.dffFormParams.languageCode = 'CHI'
                this.dffFormTableParams.languageID = 2
                this.dffFormTableParams.languageCode = 'CHI'
            } else {
                this.dffFormParams.languageID = 1
                this.dffFormParams.languageCode = ''
                this.dffFormTableParams.languageID = 1
                this.dffFormTableParams.languageCode = ''
            }
            this.initTrf();
            // document.getElementById('document').addEventListener('change', this.uploading, false)

            const nav = document.getElementsByClassName('nav-item')
            const cont = document.getElementsByClassName('content-item')
            this.element.nav = nav
            this.element.content = cont
            //导航初始化样式（默认第一个）
            nav[0].classList.add('active')
            this.$nextTick(() => {
                window.addEventListener("scroll", this.toScroll)
                setTimeout(_=>{
                    console.log('页面加载完成时添加监听')
                    that.addSelectListen()
                    that.computedProgress()
                }, 1200)
            })
        },
        methods: {
            openUpload(){
              let _self = this;
              _self.$nextTick(()=>{
                this.$refs.batchUpload.open();
              });
            },
            handleUploadError(){},
            handleBlur(e) {
                console.log('失去焦点', e)
                setTimeout(() => {
                    this.computedProgress()
                }, 200)
            },
            handleFocus(e) {
                console.log('得到焦点', e)
                setTimeout(()=>{
                    this.addSelectListen()
                }, 200)
            },
            // 添加监听
            addSelectListen() {
                let that = this
                let nodes = document.querySelectorAll('.el-select-dropdown__item')
                nodes.forEach(item => {
                    item.addEventListener('click', function(e){
                        console.log('下拉监听：', e)
                        that.computedProgress()
                    })
                })
            },
            // 进度计算
            computedProgress() {
                let that = this
                let gridRequireInput = []
                let gridRequireTextarea = []
                let require = document.querySelectorAll(".is-required")

                var ipts = document.querySelectorAll(".grid_table input")
                ipts.forEach(item => {
                    if(item.getAttribute('id') && item.getAttribute('id').indexOf('validation') != -1) gridRequireInput.push(item)
                })
                let textAreas = document.querySelectorAll(".grid_table textarea")
                textAreas.forEach(item => {
                    if(item.getAttribute('id') && item.getAttribute('id').indexOf('validation') != -1) gridRequireTextarea.push(item)
                })

                let grid = gridRequireInput.concat(gridRequireTextarea)
                grid.forEach(item => {
                    item.addEventListener("input", cback, true);
                })

                var inputs = [].concat(grid)
              if(require){
                require.forEach(item => {
                  let tag = item.querySelector('.el-select__tags') // 下拉
                  if(tag) {
                    let tagsArr = tag.querySelectorAll('.el-tag')
                    if(tagsArr) inputs.push(tagsArr);
                  } else {
                    var els = item.querySelectorAll("input, textarea");
                    for (var j = els.length; j--;) {
                      if (els[j].type != "button" && els[j].type != "submit") {
                        inputs.push(els[j]);
                        els[j].addEventListener("input", cback, true);
                      }
                    }
                  }
                })
              }

                console.log('All Input::', inputs)
                cback()

                function cback(e) {
                    // console.log('执行cback计算')
                    var arr = [];
                    for (var i = inputs.length; i--;) {
                        // console.log(inputs[i].length, inputs[i].value)
                        if ((inputs[i].value != void 0 && inputs[i].value.length > 0) || inputs[i].length > 0) {
                            arr.push(inputs[i]);
                        }
                    }
                    var r = arr.length; // 已填值长度
                    var i = inputs.length;  // 所有要填写input长度
                    that.formProgress = Math.floor(r / i * 100)
                    that.$emit('formProgress', that.formProgress)
                    // var s = document.querySelector(".top");
                    // s.style.width = Math.floor(r / i * 100) + "%"
                    // for (var o = s.length; o--;) {
                        console.log('进度计算：', r / i * 100)
                        // s[o].style.width = 100 - r / i * 100 + "%";
                    // }
                }
            },

            //点击导航
            toTarget(index) {
                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                const { content, nav } = this.element
                // const offset_top = content[index].offsetTop
                // document.documentElement.scrollTop = offset_top
                document.documentElement.scrollTop = content[index].getBoundingClientRect().top + document.documentElement.scrollTop - 100
            },
            //屏幕滚动
            toScroll() {
                if(this.showStep < 1) return
                let navEle = document.querySelector('.nav-list')
                let trfBase = document.querySelector('.trf-base')
                navEle.style.width = navEle.clientWidth + 'px'
                // console.log(navEle.clientWidth)
                var w;
                var commentEl; // 评论列表
                if(document.querySelector('.scroll-box')) {
                    commentEl = document.querySelector('.scroll-box')
                    w = commentEl.clientWidth
                    commentEl.style.width = w + 'px'
                }
                //获取滚动距离
                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                const { content, nav } = this.element
                this.minScreen = document.body.offsetWidth <= 1519 ? true : false
                window.addEventListener("resize", () => {
                    this.minScreen = document.body.offsetWidth <= 1519 ? true : false
                });
                // 侧边栏和评论栏固定
                if(scrollTop != undefined && scrollTop > 170) {
                    navEle.style.position = 'fixed'
                    navEle.style.top = this.minScreen ? 0:'80px';//'80px'
                    if(commentEl) commentEl.style.position = 'fixed'
                    if(commentEl) commentEl.style.top = this.minScreen ? 0:'80px';
                    if(trfBase) {
                        trfBase.style.position = 'fixed'
                        trfBase.style.width = 'calc(100% - 64px)'
                        trfBase.style.top = this.minScreen ? '-96px' : '-96px'
                        trfBase.style.left = '32px'
                    }
                } else {
                    navEle.style.position = 'initial'
                    if(commentEl) commentEl.style.position = 'initial'
                    if(trfBase) {
                        trfBase.style.position = 'relative'
                        trfBase.style.width = '100%'
                        trfBase.style.top = '0'
                        trfBase.style.left = '0'
                    }
                }
                // 侧边栏菜单添加当前高亮
                for (let i = 0, len = content.length; i < len; i++) {
                    //获取每块内容距离顶部距离
                    // const offset = content[i].offsetTop
                    const offset = content[i].getBoundingClientRect().top + document.documentElement.scrollTop - 100
                    //当划过第一块内容 改变左侧导航样式
                    if (scrollTop+5 >= offset) {
                        for (let n = 0; n < len; n++) {
                            n == i ? nav[n].classList.add('active') : nav[n].classList.remove('active')
                        }
                    }
                }
                if(scrollTop == 0) {
                    for (let n = 0; n < content.length; n++) {
                        nav[n].classList.remove('active')
                    }
                    nav[0].classList.add('active')
                }
            },
            trfHistoryClick() {
                this.historyVisible = true;
            },
            initTrf() {
                console.log("initTrf");
                //判断当前登录用户是否为SGS
                let isSgs = false;
                if (!validatenull(this.userInfo.dimensions) && !validatenull(this.userInfo.dimensions.SGSUserRole)) {
                    0
                    let role = this.userInfo.dimensions.SGSUserRole;
                    role.forEach(item => {
                        if (item === 'SgsAdmin'|| item==='SgsLabUser') {
                            isSgs = true;
                        }
                    });
                }
                this.isSupplier = this.haseRole('UserRole', 'Supplier');
                if (isSgs) {
                    this.trf.trfType = 30;
                    //SGS查询所有客户组
                    this.queryCustomerGroupData();
                    this.showProductCategoryFlag = false;
                } else {
                    //买方客户组 后期关联登录用户查询
                    this.showProductCategoryFlag = true;
                    //查询当前客户所属BU
                    // this.queryCustomerProductLineCode();
                    this.queryBuyerCustomerGroupData(this.userInfo.bossNo);
                }
                //获取路由参数
                this.getParams();
                //查询客户组邮件和联系人邮件数据
                // this.searchEmailGroup();
                //获取退货要求
                // this.getReturnSampleArry();
                //接口获取报告语言
                // this.queryReportLanguage();
                if (this.customerId) {
                    this.getContactsListForAfl()
                } else {
                    this.getContactsListbyUser()
                }
                this.fieldSettingsData = null;
            },
            async getParams() {
                // 取到路由带过来的参数
                let templateId = ''
                debugger;
                if (this.$route.path == '/afl/trf/newTrf') {
                    let tem = await getAflTrfTemplateId();
                    const temData = tem.data;
                    if (temData && temData.code == 200) {
                        templateId = temData.data
                    }
                }
                const trfFlag = this.$route.query.flag;
                const routerParams = this.$route.query.id;
                const actionType = this.$route.query.actionType;
                // const templateId = this.$route.query.templateId;
                this.applicationUser = this.$route.query.applicationUser
                this.customerId = this.$route.query.customerId
                if (trfFlag) {
                    this.trfFlag = trfFlag;
                }
                this.optionType = actionType || 'add';

                //判断是否存在bossNo
                if (validatenull(this.userInfo.bossNo)) {
                    //申请方可手动输入
                    this.showCustomerNameFlag = false;
                }
                // let res = await serviceTypeList(this.language);
                // const data = res.data.data;
                // this.productCategoryData = [];
                // if (!validatenull(data)) {
                //     data.find((item) => {
                //         if (item.productLineCode != 'other') {
                //             this.productCategoryData.push(item);
                //         }
                //     });
                // }

                if (this.$route.query.customerId) {
                    this.customerDetail(this.$route.query.customerId)
                } else if (this.optionType != 'trfTemplate') {
                    let userInfo = window.localStorage.getItem('SGS-userInfo')
                    userInfo = userInfo ? JSON.parse(userInfo) : ''
                    if (userInfo) {
                        this.customerDetail(userInfo.content.companyId)
                    }
                }

                if (actionType == 'trfTemplate') {
                    this.queryTrfTemplate(this.$route.query.trfTemplateId);
                    this.showStep = 10  // 改需求了，全部展开，大于4就行
                    return false;
                }  else if (templateId) {
                    if (this.applicationUser) {
                        this.saveTrfBtnIsShow = false;
                        this.submitTrfBtnIsShow = false;
                    }
                    detail(templateId).then(res => {
                        this.selectBuyerCustomerGroupChange(res.data.data.customerGroupCode)
                        this.dffFormParams.id = res.data.data.dffFormId
                        this.dffFormTableParams.id = res.data.data.dffGridId
                        this.getqueryDffFormData()
                        this.getqueryDffFormTableData()
                    }, error => {
                        this.$message.error(this.$t('api.error'));
                    });
                    return
                }
                var title = this.$t('route.trf');
                if (this.$route.query.title != null && this.$route.query.title != undefined) {
                    title = this.$route.query.title;
                }
                //document.title = title;
                // if (!validatenull(this.userInfo.companyId)) {
                //     let res = await serviceTypeList(this.language);
                //     const data = res.data.data;
                //     this.productCategoryData = [];
                //     if (!validatenull(data)) {
                //         data.find((item) => {
                //             if (item.productLineCode != 'other') {
                //                 this.productCategoryData.push(item);
                //             }
                //         });
                //     }
                // }
                debugger;
                if (actionType == 'add' || actionType == '' || actionType == undefined) {
                    //触发buery客户 Change事件
                    if (!validatenull(this.userInfo.companyId)) {
                        this.initMethod(this.userInfo.companyId);
                    }
                    // this.selectBuyerCustomerGroupChange("General");
                }
                if (actionType != '' && actionType != null) {
                    this.trf.actionType = actionType;
                } else {
                    this.trf.actionType = 'add';
                }

                if (!this.permissionList.trfSaveBtn || this.applicationUser) {
                    this.saveTrfBtnIsShow = false;
                }
                if (!this.permissionList.trfSubmitBtn || this.applicationUser) {
                    this.submitTrfBtnIsShow = false;
                }
                if (!this.permissionList.saveAsTemplateBtn) {
                    this.saveAsTemplateBtnIsShow = false;
                }
                if (!this.permissionList.cancelBtn) {
                    this.cancelTrfBtnIsShow = false;
                }

                // 将数据放在当前组件的数据内
                this.trfId = routerParams;
                if (routerParams != '' && routerParams != undefined) {
                    this.queryTrf(this.trfId);
                    // this.loading();
                    //this.trf.id=routerParams;
                    // this.selectBuyerCustomerGroupChange()
                } else {//新建
                    // this.templateDialogVisible = true;
                }

            },
            customerDetail(id) {
                let param = {
                    id: id
                }
                customerDetail(param).then(res => {
                    if (res.data.code == 200) {
                        let data = res.data.data
                        this.customerDetailInfo = data
                        if (data && data.customer) {
                            debugger;
                            this.trf.trfCustomer.customerNameZh = data.customer.customerNameZh
                            this.trf.trfCustomer.customerNameEn = data.customer.customerNameEn
                            //this.trf.trfCustomer.customerAddressZh = data.customer.customerAddressZh
                            this.trf.trfCustomer.customerAddressEn = data.customer.customerAddressEn
                            this.trf.trfCustomer.customerId = data.customer.id
                            this.trfCustomerDisabled = true
                            this.initMethod(id);
                        }
                        this.isIdenticalChange()
                    }
                })
            },
            yulan() {
                // http://test.chinartn.com/h5
                // let pdfSrc = '/aaaa.docx'
                let pdfSrc = '/aaa.pdf'
                if (/\.(pdf|PDF)$/.test(pdfSrc)) {
                    this.pdfSrc = pdf.createLoadingTask(pdfSrc)
                } else {
                    axios({
                        method: 'get',
                        responseType: 'blob', // 设置响应文件格式
                        url: pdfSrc,
                    }).then(({data}) => {
                        // this.$refs.file.innerHTML = data
                        docx.renderAsync(data, this.$refs.file) // 渲染到页面预览
                    })
                    // const that = this
                    // const xhr = new XMLHttpRequest()
                    // xhr.open('get',pdfSrc,true)
                    // xhr.responseType = 'arraybuffer'
                    // xhr.onload = ()=>{
                    //     if (xhr.status == 200) {
                    //         mammoth.convertToHtml({arrayBuffer:new Uint8Array(xhr.response)}).then((result) => {
                    //             this.$nextTick(()=>{
                    //                 this.vHtml = result.value
                    //             })
                    //         })
                    //     }
                    // }
                    // xhr.send()
                    // var reader = new FileReader()
                    // reader.readAsArrayBuffer(pdfSrc)
                    // reader.onload = function(e) {
                    //     const buffer = e.target.result // 此时是arraybuffer类型
                    //     mammoth.convertToHtml({ arrayBuffer: buffer }).then((result) => {
                    //     console.log(result)
                    //     that.vHtml = result.value
                    //     }).done()
                    // }
                }
            },
            addTestPackageRemark() {
                this.node.data.remark = this.remark;
                this.testPackageVisible = false;
            },

            reportLanguageChange(val) {
                let customer = this.customerDetailInfo.customer
                if (this.trf.servicRequire.reportLanguage.length == 2) {
                    if (this.trf.servicRequire.isIdentical == 1) {//和申请方一致
                        this.trf.servicRequire.reportHeader = customer ? customer.customerNameZh : ''
                        this.trf.servicRequire.reportAddress = this.trf.trfCustomer.customerAddressZh;
                        if (this.trf.servicRequire.reportHeaderEn == '') {
                            this.trf.servicRequire.reportHeaderEn = customer ? customer.customerNameEn : ''
                        }
                        if (this.trf.servicRequire.reportAddressEn == '') {
                            this.trf.servicRequire.reportAddressEn = customer ? customer.customerAddressEn : ''
                        }
                        this.trf.servicRequire.reportLanguageName = 'Chinese&English Report'
                    }
                } else if (this.trf.servicRequire.reportLanguage.includes('2')) {// 2 有选择中文报告
                     if (this.trf.servicRequire.isIdentical == 1) {//和申请方一致
                         this.trf.servicRequire.reportHeader = customer ? customer.customerNameZh : ''
                         this.trf.servicRequire.reportAddress = this.trf.trfCustomer.customerAddressZh;
                         this.trf.servicRequire.reportHeaderEn = ''
                         this.trf.servicRequire.reportAddressEn = ''
                         this.trf.servicRequire.reportLanguageName = 'Chinese Report'
                     }
                } else if (this.trf.servicRequire.reportLanguage.includes('1')) {
                    if (this.trf.servicRequire.isIdentical == 1) {//和申请方一致
                        this.trf.servicRequire.reportHeaderEn = customer ? customer.customerNameEn : ''
                        this.trf.servicRequire.reportAddressEn =  customer ? customer.customerAddressEn : ''
                        this.trf.servicRequire.reportHeader = ''
                        this.trf.servicRequire.reportAddress = ''
                        this.trf.servicRequire.reportLanguageName = 'English Report'
                    }
                }
            },
            // 报告语言和报告抬头是否与申请方一致变化
            isIdenticalChange() {
                this.isIdenticalDisabled = false //可输入
                let customer = this.customerDetailInfo.customer
                if (this.trf.servicRequire.isIdentical == 1) {//标识和申请方一致
                    //判断报告语言
                    if (this.trf.servicRequire.reportLanguage.length == 2) {// 中英文报告
                        this.trf.servicRequire.reportHeader = customer ? customer.customerNameZh : ''
                        this.trf.servicRequire.reportAddress = this.trf.trfCustomer.customerAddressZh;
                        if (this.trf.servicRequire.reportHeaderEn == '') {
                            this.trf.servicRequire.reportHeaderEn = customer ? customer.customerNameEn : ''//TODO 需确认
                        }
                        if (this.trf.servicRequire.reportAddressEn == '') {
                            this.trf.servicRequire.reportAddressEn = customer ? customer.customerAddressEn : '' //TODO 需确认
                        }
                        this.trf.servicRequire.reportLanguageName = 'Chinese&English Report'
                    } else if (this.trf.servicRequire.reportLanguage.includes('2')) {// 2 ：有选择中文报告
                        this.trf.servicRequire.reportHeader = customer ? customer.customerNameZh : ''
                        this.trf.servicRequire.reportAddress = this.trf.trfCustomer.customerAddressZh;
                        this.trf.servicRequire.reportHeaderEn = ''
                        this.trf.servicRequire.reportAddressEn = ''
                        this.trf.servicRequire.reportLanguageName = 'Chinese Report'
                    } else if (this.trf.servicRequire.reportLanguage.includes('1')) {// 1：英文报告
                        this.trf.servicRequire.reportHeaderEn = customer ? customer.customerNameEn : ''
                        this.trf.servicRequire.reportAddressEn = this.trf.trfCustomer.customerAddressZh;// TODO 问题点 需确认，如果用户选择的报告为英文报告，那应该带入哪个公司地址？ 目前暂时为获取录入的地址
                        this.trf.servicRequire.reportHeader = ''
                        this.trf.servicRequire.reportAddress = ''
                        this.trf.servicRequire.reportLanguageName = 'English Report'
                    }
                    this.isIdenticalDisabled = true //禁止输入
                } else {//和申请方不一致
                    this.isIdenticalDisabled = false
                }
            },
            // 获取联系人列表
            getContactsListbyUser() {
                getContactsListbyUser().then(res => {
                    const data = res.data.data.records;
                    this.customerContactData = data;
                    let obj = {};
                    obj = this.customerContactData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        //this.$set(this.trf.trfCustomerContact,'customerContactId',obj.id)
                        this.$set(this.trf.trfCustomerContact, 'applyContactName', obj.contactName)
                        this.$set(this.trf.trfCustomerContact, 'applyContactTel', obj.contactTel);
                        this.$set(this.trf.trfCustomerContact, 'applyContactEmail', obj.contactEmail);
                        this.$set(this.trf.trfCustomerContact, 'customerContactId', obj.id);
                        if (this.optionType == 'add') {
                            this.$set(this.trf.servicRequire, 'reportReceivingEmail', obj.contactEmail);
                        }
                    }
                })
            },
            getContactsListForAfl() {
                let param = {
                    isDeleted: 0,
                    customerId: this.customerId
                }
                getContactsListForAfl(param).then(res => {
                    const data = res.data.data.records;
                    this.customerContactData = data;
                    let obj = {};
                    obj = this.customerContactData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        this.$set(this.trf.trfCustomerContact, 'applyContactName', obj.contactName)
                        this.$set(this.trf.trfCustomerContact, 'applyContactTel', obj.contactTel);
                        this.$set(this.trf.trfCustomerContact, 'applyContactEmail', obj.contactEmail);
                        this.$set(this.trf.trfCustomerContact, 'customerContactId', obj.id);
                        if (this.optionType == 'add') {
                            this.$set(this.trf.servicRequire, 'reportReceivingEmail', obj.contactEmail);
                        }
                    }
                })
            },
            submitTemplate() {
                this.templateDialogVisible = false;

                this.$emit('trfDetail', this.trf)
                //判断所选的BU是否为CCL
                /* if (this.trf.productLineCode == 'CChemLab') {
                     //跳转至CCL建单页面
                     this.$router.push({
                         path: '/ccl/trf/trfForm',
                         query: {
                             templateId: this.trf.trfTemplateId,
                         }
                     })
                 }*/
            },
            queryCurrentDeptBeLong(templateId) {
                getDeptBeLongTosByTemplateId(templateId).then(res => {
                    const data = res.data.data;
                    this.beLongData = data;

                    if (validatenull(this.beLongData)) {
                        this.isShowBelongFlag = false;
                        this.$set(this.trf.trfCustomer, 'beLongId', null);
                    } else {
                        this.isShowBelongFlag = true;
                    }
                });
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.userInfo.dimensions)) {
                    return false;
                } else {
                    if (this.userInfo.dimensions.hasOwnProperty(type)) {
                        if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            //验证表单项是否展示方法
            valiUsable(code) {
                var usableFlag = true;
                if (this.fieldSettingsData != null && this.fieldSettingsData != undefined) {
                    if (this.fieldSettingsData[code] != null && this.fieldSettingsData[code] != undefined) {
                        usableFlag = this.fieldSettingsData[code].usable != 1 ? false : true
                    }
                }
                return usableFlag;
            },
            //验证必填项方法
            valiRequired(code) {
                var requiredFlag = false;
                if (this.fieldSettingsData != null && this.fieldSettingsData != undefined) {
                    if (this.fieldSettingsData[code] != null && this.fieldSettingsData[code] != undefined) {
                        requiredFlag = this.fieldSettingsData[code].required == 1 ? true : false
                    }
                }
                return requiredFlag;
            },
            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.buyerCustomerGroupData = data;
                });
            },

            serviceTypeChange(val) {
                this.trf.serviceTypeName = '';
                let obj = {};
                obj = this.serviceTypeData.find((item) => {
                    return item.serviceTypeCode === val;
                });
                if (obj != undefined && obj != null) {
                    this.trf.serviceTypeName = obj.serviceTypeName;
                }
            },
            // async reportChange(val) {
            //     let obj = {};
            //     obj = this.reportLanguageData.find((item) => {
            //         return item.sysKey === val;
            //     });
            //     if (obj != undefined && obj != null) {
            //         this.trf.servicRequire.reportLanguageName = obj.sysValue;
            //         if (val == 1) {
            //             this.isShowDffLanguage = false;
            //             // this.languageID = 1;
            //         } else if (val == 2) {
            //             this.isShowDffLanguage = false;
            //             // this.languageID = 2;
            //         } else {
            //             this.isShowDffLanguage = true;
            //             // this.languageID = 1;
            //         }
            //     }
            //     var careLabList = ['SupplierCode_1'];
            //     this.productItemNosList = careLabList;
            //     this.careInstructions = this.dafaultCareInstructions;
            //     let templateObj = {};
            //     templateObj = this.templateData.find((item) => {
            //         return item.id === val;
            //     });
            //     //加载dff form组件
            //     if (templateObj != undefined && templateObj != null) {
            //         if (this.trfFlag == 2) {
            //             let fieldData = await getFieldSettingsByTemplateId(templateObj.id);
            //             if (fieldData.data.code == 200) {
            //                 this.fieldSettingsData = fieldData.data.data;
            //             }
            //         }

            //         // 不存在form不显示
            //         if (!validatenull(templateObj.dffFormId)) {
            //             let dffFormIds = [];
            //             dffFormIds.push(templateObj.dffFormId);
            //             //调用接口 查询最新dffFormID
            //             getDffFormComfirmIdBatchByIds(dffFormIds).then(res => {
            //                 var newDffFormId = templateObj.dffFormId;
            //                 const data = res.data.data;
            //                 if (data != null && data.length > 0 && data != undefined) {
            //                     newDffFormId = data[0].comfirmDffFormId;
            //                 }
            //                 this.$set(this.trf, 'dffFormId', newDffFormId);
            //                 //判断当前语言获取对应的dffForm
            //                 //默认查询英文
            //                 this.languageID = 2;
            //                 // if (this.trf.servicRequire.reportLanguage == 2) {
            //                 //     this.languageID = 2;
            //                 //     //this.dffObj = this.dffLanguageObj.CN;
            //                 // } else {
            //                 //     //this.dffLanguageObj.CN = {};
            //                 //     this.languageID = 1;
            //                 //     //this.dffObj = this.dffLanguageObj.EN;
            //                 // }
            //                 this.$set(this.reqDffFormObj, 'id', newDffFormId);
            //                 this.$set(this.reqDffFormObj, 'name', templateObj.dffName);
            //                 this.isDffFormLoadFlag = true;
            //                 // this.$nextTick(() => {
            //                 //     var that = this;
            //                 //     console.log(that.$refs.dffForm1);
            //                 //     if (that.$refs.dffForm1.headerConfig.length > 0 && this.$refs.gridRef.dffTableConfigData.length > 0) {

            //                 //     }
            //                 // });

            //                 return false;
            //             });
            //             // 不存在grid不显示
            //             this.$set(this.reqDffGridObj, 'name', templateObj.dffName);
            //             if (!validatenull(templateObj.dffGridId)) {
            //                 //调用接口 查询最新dffGridID
            //                 let dffGridIds = [];
            //                 dffGridIds.push(templateObj.dffGridId);
            //                 getDffFormComfirmIdBatchByIds(dffGridIds).then(res => {
            //                     var newDffGridId = templateObj.dffGridId;
            //                     const data = res.data.data;
            //                     if (data != null && data.length > 0 && data != undefined) {
            //                         newDffGridId = data[0].comfirmDffFormId;
            //                     }
            //                     this.$set(this.trf, 'dffGridId', newDffGridId);
            //                     this.$set(this.reqDffGridObj, 'id', newDffGridId);
            //                     if (this.trf.servicRequire.reportLanguage.includes('2')) {
            //                         // this.objList = this.dataList.CN;
            //                         this.getLanguageGridObjList(2);
            //                     } else {
            //                         this.getLanguageGridObjList(1);
            //                         //this.objList = this.dataList.EN;
            //                     }
            //                     this.isDffGridLoadFlag = true;
            //                     if (!validatenull(obj.specificCode)) {
            //                         if (obj.specificCode.indexOf("care_Label") != -1) {
            //                             this.buyerCustomerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
            //                             this.buyerOrAgentName = this.trf.trfCustomer.buyerCustomerGroupName;
            //                             this.isCareLabelLoadFlag = true;
            //                         }
            //                     }

            //                 });
            //             }
            //         }
            //     }

            //     //重新出发模板change事件
            //     // if (!validatenull(this.trf.trfTemplateId)) {
            //     //     this.selectTemplateChange(this.trf.trfTemplateId)
            //     // }
            // },
            getCommentData(v) {
              this.queryCommunicationLog()
            },
            queryCommunicationLog() {
              debugger;
                var communicationParams = {};
                getCommunicationLogList(Object.assign(communicationParams, this.communicationParam)).then(res => {
                    const data = res.data.data;
                    this.communicationTableData = data;
                });
            },
            toPrivewTemplate() {
                this.$router.push({path: '/trf/previewCA2?id=' + this.$route.query.id+ '&trfNo=' + this.trf.trfNo + '&signature=' + this.$route.query.signature})
            },
            loading() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                setTimeout(() => {
                    loading.close();
                }, 2500);
            },
            // queryReportLanguage() {
            //     getReportLanguageArry(this.language).then(res => {
            //         const data = res.data;
            //         if (data.length > 0) {
            //             this.reportLanguageData = data;
            //             //默认设置为英文报告语言
            //             if (validatenull(this.trf.servicRequire.reportLanguage)) {
            //                 this.$set(this.trf.servicRequire, 'reportLanguage', [2])

            //                 let obj = {};
            //                 obj = this.reportLanguageData.find((item) => {
            //                     return item.sysKey === '2';
            //                 });
            //                 if (obj != undefined && obj != null) {
            //                     this.$set(this.trf.servicRequire, 'reportLanguageName', obj.sysValue)
            //                     this.reportChange(obj.sysKey)
            //                 }
            //             }
            //         }
            //     });
            // },
            queryCustomer(bossNo) {
                this.$set(this.customerParam, 'number', bossNo)
                var params = {};
                searchCustomer(Object.assign(params, this.customerParam)).then(res => {
                    const data = res.data.data;
                    if (data.length > 0) {
                        this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                        if (this.trf.servicRequire.reportHeader == '' || this.trf.servicRequire.reportHeader == null) {
                            this.$set(this.trf.servicRequire, 'reportHeader', data[0].nameEN)
                        }
                        this.$set(this.trf.trfCustomer, 'sgsCustomerId', data[0].customerId);
                        this.$set(this.trf.trfCustomer, 'sgsAccountId', data[0].accountId);
                        this.$set(this.trf.trfCustomer, 'customerNameZh', data[0].nameCN);
                        this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                        if (this.language == 'zh-CN') {
                            this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                            if (this.trf.servicRequire.reportHeader == '' || this.trf.servicRequire.reportHeader == null) {
                                this.$set(this.trf.servicRequire, 'reportHeader', data[0].nameCN)
                            }
                        }
                    }
                });
            },
            trfTableDateFormtter(row, column) {
                var date = row[column.property];
                if (date == undefined || date == '') {
                    return this.$t('trf.notSubmit');
                }
                ;
                return moment(date).format("YYYY-MM-DD")
            },
            trfTableStatusFormtter(row, column) {
                var status = row[column.property];
                if (status == 1 || status == '1') {
                    return this.$t('trfStatus.draft');
                } else if (status == 2 || status == '2') {
                    return this.$t('trfStatus.submitted');
                } else if (status == 3 || status == '3') {
                    return this.$t('trfStatus.preparation');
                } else if (status == 4 || status == '4') {
                    return this.$t('trfStatus.testing');
                } else if (status == 5 || status == '5') {
                    return this.$t('trfStatus.completed');
                } else if (status == 6 || status == '6') {
                    return this.$t('trfStatus.cancel');
                } else if (status == 11 || status == '11' || status == 12 || status == '12') {
                    return this.$t('trfStatus.pending');
                } else if (status == 13 || status == '13') {
                    return this.$t('trfStatus.confirmingQuotation');
                } else if (status == 14 || status == '14') {
                    return this.$t('trfStatus.confirmQuotation');
                } else {
                    return '';
                }
            },
            //返回列表
            toTrfList() {
                if (this.applicationUser) {
                    this.$router.push({path: '/afl/template/management', query: {}});
                } else {
                    this.$router.push({path: '/trf/newList', query: {}});
                }
            },
            //****************dff Form method Start***************
            getLanguageObjList(language) {
                var languageStr = 'CN';
                // if (language == 2) {
                //     languageStr = 'CN';
                // }
                this.dffObjNew = this.dffLanguageObj[languageStr];
                this.dffFormParams.id = this.trf.dffFormId
                this.getqueryDffFormData()
            },


            //****************dff Grid method Start***************
            getLanguageGridObjList(language) {
                var languageStr = 'CN';
                var data = this.dataList[languageStr];
                if (validatenull(data)) {
                    data = this.defaultDataList_new[languageStr];
                }
                this.objListNew = data;
                this.dffFormTableParams.id = this.trf.dffGridId
                this.getqueryDffFormTableData()
            },

            //初始化页面需加载的数据
            initMethod(customerId) {
                debugger;
                //查询申请人数据
                this.customerId = customerId;
                this.trf.trfCustomer.customerId = customerId;
                this.customerContactParam.customerId_equal = customerId;

                //查询公司地址
                 this.searchCustomerAddressData();
            },


            //查询TRF模板
            queryTrfTemplate(trfTemplateId) {
                trfTemplateDetail(trfTemplateId).then(res => {
                    var data = res.data.data;
                    if (data != null && data != undefined) {
                        if (data.trfTemplateData != null && data.trfTemplateData != '') {
                            this.trf = JSON.parse(data.trfTemplateData);
                            console.log('this.trf:', this.trf)
                            this.trf.trfSourceType = TrfSourceEnums.ONLINE_TRF.NAME;
                            this.trf.trfSourceId = trfTemplateId;
                            this.dffObjNew = this.trf.dffFormData ? JSON.parse(this.trf.dffFormData).CN : {}
                            this.objListNew = this.trf.dffGridData ? JSON.parse(this.trf.dffGridData).CN : []
                            this.dffFormParams.id = this.trf.dffFormId
                            this.dffFormTableParams.id = this.trf.dffGridId
                            this.getqueryDffFormData()
                            this.getqueryDffFormTableData()
                            this.$set(this.trf, "trfStatus", 1);
                            this.$set(this.trf, "actionType", 'add');
                            this.displayTrf();
                        }
                    }
                }, error => {
                    this.$message.error(this.$t('api.error'));
                });
            },
            queryTrf(trfId) {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                aflTrfDetail(trfId).then(res => {
                    loading.close();
                    this.isShow = true;
                    const data = res.data.data;
                    this.trf = {...data};


                    if (this.trf.dffFormData) {
                        this.dffObjNew = JSON.parse(this.trf.dffFormData).CN
                    }
                    this.saveTrfBtnIsShow = true;
                    this.submitTrfBtnIsShow = true;
                    this.saveAsTemplateBtnIsShow = true;
                    this.cancelTrfBtnIsShow = false;
                    if (this.optionType == 'copy') {
                        this.trf.extendOrderNo = '';
                        this.$set(this.trf, "trfStatus", 1);
                    }
                    if (!this.permissionList.trfSaveBtn || this.trf.trfStatus >= 2 || this.applicationUser) {
                        this.saveTrfBtnIsShow = false;
                    }
                    if (!this.permissionList.trfSubmitBtn || this.trf.trfStatus >= 2 || this.applicationUser) {
                        this.submitTrfBtnIsShow = false;
                    }
                    if (!this.permissionList.saveAsTemplateBtn) {
                        this.saveAsTemplateBtnIsShow = false;
                    }

                    //取消按钮控制  1、有该权限，提交状态下当前用户可以操作自己的 2、已提交 < 进行中 sgs可操作 >2 && <5
                    //permissionList.cancelBtn && trf.trfStatus<=4 && trf.trfStatus>1 && userInfo.userMgtId == trf.applicantUser
                    if (this.permissionList.cancelBtn) {
                        if (this.trf.trfStatus == 2) {
                            if (this.trf.applicantUser == this.userInfo.userMgtId) {
                                this.cancelTrfBtnIsShow = true;
                            }
                        } else if (this.trf.trfStatus > 2 && this.trf.trfStatus < 5) {//需配置SGS管理员 userManage权限
                            let isSgs = false;
                            if (!validatenull(this.userInfo.dimensions) && !validatenull(this.userInfo.dimensions.SGSUserRole)) {
                                let role = this.userInfo.dimensions.SGSUserRole;
                                role.forEach(item => {
                                    if (item === 'SgsAdmin') {
                                        isSgs = true;
                                    }
                                });
                            }
                            if (isSgs) {//SGS管理员才可展示取消按钮
                                this.cancelTrfBtnIsShow = true;
                            }
                        }
                    }
                    if (this.optionType != 'add') {
                        if (this.trf.applicantUser != this.userInfo.userMgtId) {
                            this.saveTrfBtnIsShow = false;
                            this.submitTrfBtnIsShow = false;
                            this.saveAsTemplateBtnIsShow = false;
                        }
                    }
                    // 回传父类
                    this.trfNo = this.trf.trfNo;
                    this.trfStatus = this.trf.trfStatus;
                    this.$emit('update:trfNo', this.trfNo);
                    this.$emit('update:trfStatus', this.trfStatus);

                    this.trf.actionType = this.optionType;
                    if (this.optionType == 'copy' || this.optionType == 'add') {
                        this.trf.trfStatus = 1;
                        if (this.optionType == 'copy') {
                            this.trf.trfSourceType = TrfSourceEnums.ONLINE_TRF.NAME;
                            //无论COPY还是其他建单，trfType均为30 均属于AFL PO的建单
                            this.trf.trfType = TrfTypeEnums.AFL_PREORDER.CODE;
                            this.trf.trfSourceId = trfId;
                        }
                    }
                    //将trfId放入页面传值
                    this.$emit('trfId', this.trf.id);
                    this.$emit('trf', this.trf)
                    //查询communicationnLog
                    if (this.trf.trfStatus > 1) {
                        this.communicationParam.trfId = this.trf.id;
                        //this.communicationParam.trfType = 30;
                        this.queryCommunicationLog();
                    }
                    this.displayTrf();
                    this.$route.query.signature = this.trf.signature
                    /*let path = this.$router.history.current.path
                    this.$router.push({ path, query: {signature:this.trf.signature}})*/
                }, error => {
                    loading.close();
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
            displayTrf() {
                let isSgs = false;
                if (!validatenull(this.userInfo.dimensions) && !validatenull(this.userInfo.dimensions.SGSUserRole)) {
                    let role = this.userInfo.dimensions.SGSUserRole;
                    role.forEach(item => {
                        if (item === 'SgsAdmin' || item==='SgsLabUser') {
                            isSgs = true;
                        }
                    });
                }
                // if (!isSgs) {//SGS管理员才可展示取消按钮
                //     this.queryCustomerProductLineCode();
                // }
                if (this.trf.trfStatus > 1) {
                    this.showCustomerNameFlag = true;
                    this.trfDisabled = true;
                    this.templateDisabled = true;
                    this.buyerDisabled = true;
                    this.isShowDffLanguage = true;
                    const inputs = this.$refs['trf'].$el.querySelectorAll('input');
                    [...inputs].forEach(elm => {
                        elm.classList.remove('activeTrf');
                        elm.classList.add('disabledTrf');
                    })
                    const textAreas = this.$refs['trf'].$el.querySelectorAll('textarea');
                    [...textAreas].forEach(elm => {
                        elm.classList.remove('activeTrf');
                        elm.classList.add('disabledTrf');
                    })
                }

                if (this.trf.servicRequire.reportLanguage.includes('1')) {
                    this.isShowDffLanguage = false;
                    this.languageID = 1;
                } else if (this.trf.servicRequire.reportLanguage.includes('2')) {
                    this.isShowDffLanguage = false;
                    this.languageID = 2;
                } else {
                    this.isShowDffLanguage = true;
                    this.languageID = 2;
                }
                if (this.trf.trfStatus >= 1 && this.trf.actionType != 'copy') {
                    this.isShowSubmitBtn = false;
                    this.trfShowData = [];
                    var trfTable = {};
                    trfTable.trfNo = this.trf.trfNo;
                    trfTable.createTime = this.trf.createTime;
                    trfTable.trfSubmissionDate = this.trf.trfSubmissionDate;
                    trfTable.trfStatus = this.trf.trfStatus;
                    if (this.trf.trfStatus == 11 || this.trf.trfStatus == 12) {
                        this.isShowRemark = true;
                        trfTable.pendingReason = 'Pending Reason: ' + this.trf.pendingReason;
                    }
                    this.trfShowData.push(trfTable);
                }
                if (  this.trf.isSame == 1 ||this.trf.trfCustomer.isSame == 1 || this.trf.servicRequire.isSame == '1') {
                    this.identicalFlag = true;
                } else {
                    this.identicalFlag = false;
                }
                if (this.trf.isSelfRefrence == 1 || this.trf.isSelfRefrence == '1') {
                    this.isSelfRefrenceFlag = true;
                } else {
                    this.isSelfRefrenceFlag = false;
                }
                if (this.trf.servicRequire) {
                    let reportLanguage = this.trf.servicRequire.reportLanguage
                    if (reportLanguage) {
                        if (typeof reportLanguage == 'string') {
                            reportLanguage = reportLanguage.split(',')
                        }
                        this.$set(this.trf.servicRequire, 'reportLanguage', reportLanguage)
                    }
                    let reportType = this.trf.servicRequire.reportType
                    if (reportType) {
                        if (typeof reportType == 'string') {
                            reportType = reportType.split(',')
                        }
                        this.trf.servicRequire.reportType = reportType
                    }
                    if (this.trf.servicRequire.otherReportMethod) {
                        this.otherReportMethodCheckBox = '1'
                    }
                }
                if (this.trf.servicRequire.isIdentical == 1) {
                    this.isIdenticalDisabled = true
                } else {
                    this.isIdenticalDisabled = false
                }
                //回显地址
                if (!validatenull(this.trf.trfCustomer.customerId)) {
                     var customerAddressParams = {};
                     this.customerContactParam.customerId_equal = this.trf.trfCustomer.customerId;
                    if (this.trf.trfCustomer.customerId == -1) {
                        this.trf.trfCustomer.customerId = '';
                    }
                    getCustomerAddressList(Object.assign(customerAddressParams, this.customerContactParam)).then(res => {
                        const data = res.data.data;
                        this.customerAddressData = data;
                    });
                    // //回显customerContact
                    // this.customerContactData.customerId = this.trf.trfCustomer.customerId;
                    // if (this.trf.trfCustomer.customerId == -1 || this.trf.trfCustomer.customerId == undefined) {
                    //     this.customerContactData.customerId = '';
                    // }
                    // var customerContactparams = {};
                    // getCustomerContactList(Object.assign(customerContactparams, this.customerContactParam)).then(res => {
                    //     const data = res.data.data;
                    //     this.customerContactData = data;
                    // });
                }


                //回显联系人数据
                // if (!validatenull(this.userInfo.companyId)) {
                //     this.customerContactParam.customerId_equal = this.userInfo.companyId;
                //     var addresParams = {};
                //     getCustomerAddressList(Object.assign(addresParams, this.customerContactParam)).then(res => {
                //         const data = res.data.data;
                //         this.customerAddressData = data;
                //     });
                //     var contactParams = {};
                //     getCustomerContactList(Object.assign(contactParams, this.customerContactParam)).then(res => {
                //         const data = res.data.data;
                //         this.customerContactData = data;
                //     });
                // }
                //回显agentGroup
                var agentParams = {};
                this.$set(this.customerGroupParam, 'groupName', this.trf.trfCustomer.agentCustomerGroupName);
                var agentParams = {};
                /* this.$set(this.agentCustomerParam, 'customerName', this.trf.trfCustomer.agentCustomerNameEn);
                 this.$set(this.agentCustomerParam, 'rows', 5);
                 getAgentCustomerGroupByParms(Object.assign(agentParams, this.agentCustomerParam)).then(res => {
                     const data = res.data.data;
                     this.agentCustomerGroupData = data;
                     this.trf.agentCustomerName = this.trf.trfCustomer.agentCustomerNameEn;
                 });*/
                //回显template下拉数据
                this.templateDataParam.customerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
                //draft状态只允许获取未删除，且状态正常的模板列表；传入模板ID
                if (this.trf.actionType == 'copy') {
                    this.templateDataParam.trfStatus = 1;
                    // this.templateLabDataParam.trfStatus = 1;
                    // this.templateLabContactDataParam.trfStatus = 1;
                }
                if (this.trf.trfType == 11) {
                    this.templateDisabled = true;
                    this.buyerDisabled = true;
                }
                this.$set(this.templateDataParam, 'productLineCode', this.trf.productLineCode);
                this.$set(this.templateDataParam, 'trfStatus', this.trf.trfStatus);
                this.$set(this.templateDataParam, 'id', this.trf.trfTemplateId);

                // this.$set(this.templateLabDataParam, 'trfStatus', this.trf.trfStatus);
                // this.$set(this.templateLabContactDataParam, 'trfStatus', this.trf.trfStatus);
                // this.$set(this.templateLabDataParam, 'labCode', this.trf.trfLab.labCode);
                //验证已保存的TRF中的模板是否正常状态
                if (validatenull(this.trf.trfTemplateId)) {
                    if (this.trf.trfStatus == 1 && this.trf.actionType == 'copy') {
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.templateErrorInfo'),
                            type: 'warning'
                        });
                        this.$set(this.trf, 'trfLab', {});
                        this.$set(this.trf, 'trfLabContact', {});
                        this.$set(this.trf, 'trfTemplateId', '');
                        this.$set(this.trf, 'templateName', '');
                        this.$set(this.trf, 'serviceType', '');
                        return false;
                    }
                }
                //非Draft状态的可以获取已删除或状态置为禁用的模板列表；
                var params = {};
                getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
                    this.templateDataLoad = true;
                    const data = res.data.data;
                    this.templateData = data;
                    this.isDffFormLoadFlag = false;
                    this.isDffGridLoadFlag = false;
                    this.isCareLabelLoadFlag = false;
                    if (this.trf.trfTemplateId == null || this.trf.trfTemplateId == -1) {
                        this.trf.trfTemplateId = '';
                    }
                    var obj = {};
                    obj = data.find((item) => {
                        return item.id === this.trf.trfTemplateId;
                    });
                    console.log(obj)
                    //回显服务类型
                    if (obj != null && obj != undefined) {
                        //回显belong
                        this.queryCurrentDeptBeLong(this.trf.trfTemplateId);
                        if (this.trf.trfStatus == 1 || this.trf.actionType == 'copy') {
                            if (obj.isDeleted == 1 || obj.status != 1) {
                                this.$notify({
                                    title: this.$t('tip'),
                                    message: this.$t('trf.templateErrorInfo'),
                                    type: 'warning'
                                });
                                this.$set(this.trf, 'trfLab', {});
                                this.$set(this.trf, 'trfLabContact', {});
                                this.$set(this.trf, 'trfTemplateId', '');
                                this.$set(this.trf, 'templateName', '');
                                this.$set(this.trf, 'serviceType', '');
                                return false;
                            }
                        }
                        //回显必填校验
                        getFieldSettingsByTemplateId(obj.id).then(res => {
                            if (res.data.code == 200) {
                                this.fieldSettingsData = res.data.data;
                            }
                        });
                        getServiceType(obj.productLineId).then(res => {
                            const data = res.data.data;
                            this.serviceTypeData = data;
                        });
                        //回显实验室以及实验室联系人
                        // var labParams = {};
                        // this.templateLabDataParam.trfTemplateId = this.trf.trfTemplateId;
                        // this.$set(this.templateLabDataParam, 'productLineCode', obj.productLineCode);
                        // getTemplateLabList(Object.assign(labParams, this.templateLabDataParam)).then(res => {
                        //     const data = res.data.data;
                        //     this.templateLabData = data;
                        //     var labObj = {};
                        //     labObj = this.templateLabData.find((item) => {
                        //         return item.labCode === this.trf.trfLab.labCode;
                        //     });
                        //     if (validatenull(labObj)) {
                        //         this.$set(this.trf.trfLab, 'labCode', '');
                        //         this.$set(this.trf.trfLab, 'labName', '');
                        //         this.$set(this.trf.trfLab, 'labAddress', '');
                        //     }
                        // });
                        //查询实验室联系人
                        if (this.trf.trfLabContact) {
                            if(!validatenull(this.trf.trfLabContact.labContactId)){
                                this.trf.trfLabContact.labContactId = parseInt(this.trf.trfLabContact.labContactId)
                            }
                        }
                        this.selectAflHostListForCurrentUser()
                        /*this.templateLabContactDataParam.trfTemplateId = this.trf.trfTemplateId;
                        this.templateLabContactDataParam.labCode = '';*/
                        // this.$set(this.templateLabContactDataParam, 'labCode', this.trf.trfLab.labCode);
                        // this.$set(this.templateLabContactDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                        // var labContactParams = {};
                        // getTemplateLabContactList(Object.assign(labContactParams, this.templateLabContactDataParam)).then(res => {
                        //     const data = res.data.data;
                        //     this.templateLabContactData = data;
                        //     if (this.templateLabContactData == null || this.templateLabContactData == undefined || this.templateLabContactData.length == 0) {//无联系人 不需要选择
                        //         this.labContactFlag = false;
                        //         return false;
                        //     } else {
                        //         this.templateLabContactData.forEach((item, index, array) => {
                        //             var contactEmail = item.contactEmail;
                        //             if (!validatenull(contactEmail)) {
                        //                 item.labContactName = item.labContactName + " (" + contactEmail + ")";
                        //             }
                        //         })
                        //         var labContactObj = {};
                        //         labContactObj = this.templateLabContactData.find((item) => {
                        //             return item.labContactId === this.trf.trfLabContact.labContactId;
                        //         });
                        //         if (validatenull(labContactObj)) {
                        //             this.$set(this.trf, 'trfLabContact', {});
                        //         }
                        //     }
                        // });
                        //加载dff form组件  //20-05-09 DFF组件获取最新ID逻辑修改
                        //copy和TRF 模板需要获取最新的dff Form Id
                        console.log(this.optionType)
                        if (this.optionType == 'copy' || this.optionType == 'trfTemplate') {
                            this.$nextTick(() => {
                                //copy & Trf Template DFF Form
                                if (!validatenull(this.trf.dffFormId)) {
                                    //验证TRF的 DFF FormID和Template 的DFF Form Id 是否属于同一Group
                                    var dffIDList = [];
                                    dffIDList.push(this.trf.dffFormId);
                                    dffIDList.push(obj.dffFormId);
                                    var checkDffGroupObj = {};
                                    checkDffGroupObj['dffIDList'] = dffIDList;
                                    checkDffFormIdSmapleGroup(checkDffGroupObj).then(res => {
                                        const data = res.data.data;
                                        if (!data.sameFormGroup) {//不属于同一Group，清空Template 重新选择
                                            this.$notify({
                                                title: this.$t('tip'),
                                                message: this.$t('trf.templateErrorInfo'),
                                                type: 'warning'
                                            });
                                            this.$set(this.trf, 'trfLab', {});
                                            this.$set(this.trf, 'trfLabContact', {});
                                            this.$set(this.trf, 'trfTemplateId', '');
                                            this.$set(this.trf, 'templateName', '');
                                            this.$set(this.trf, 'serviceType', '');
                                            return false;
                                        }
                                    })

                                    var dffFormIds = [];
                                    dffFormIds.push(this.trf.dffFormId);
                                    getDffFormComfirmIdBatchByIds(dffFormIds).then(res => {
                                        var newDffFormId = this.trf.dffFormId;
                                        const data = res.data.data;
                                        if (data != null && data.length > 0 && data != undefined) {
                                            newDffFormId = data[0].comfirmDffFormId;
                                        }
                                        this.$set(this.trf, 'dffFormId', newDffFormId);//重新赋值dffFormId;
                                        this.reqDffFormObj.id = newDffFormId;//'01a39781-2b0a-4c06-b552-d8c2e0f002ac';
                                        this.reqDffFormObj.name = this.trf.dffName;//'C&A Footwear';
                                        if (this.trf.dffFormData != null && this.trf.dffFormData != undefined) {
                                            console.log(this.trf.dffFormData);
                                            try {
                                                this.dffLanguageObj = JSON.parse(this.trf.dffFormData);
                                            } catch (e) {
                                                this.dffLanguageObj = this.dffLanguageDefaultObj;
                                            }
                                            if (validatenull(this.dffLanguageObj.EN)) {
                                                this.dffLanguageObj.EN = {};
                                            }
                                            if (validatenull(this.dffLanguageObj.CN)) {
                                                this.dffLanguageObj.CN = {};
                                            }
                                            if (this.trf.servicRequire.reportLanguage.includes('2')) {
                                                this.getLanguageObjList(2);
                                            } else {
                                                this.getLanguageObjList(1);
                                            }
                                        }
                                        this.isDffFormLoadFlag = true;

                                    });
                                }
                                //copy & Trf Template DFF Grid
                                if (!validatenull(this.trf.dffGridId)) {
                                    var dffGridIds = [];
                                    dffGridIds.push(this.trf.dffGridId);
                                    getDffFormComfirmIdBatchByIds(dffGridIds).then(res => {
                                        var newDffGridId = this.trf.dffGridId;
                                        const data = res.data.data;
                                        if (data != null && data.length > 0 && data != undefined) {
                                            newDffGridId = data[0].comfirmDffFormId;
                                        }
                                        this.$set(this.trf, 'dffGridId', newDffGridId);//重新赋值dffGridId;
                                        this.reqDffGridObj.id = newDffGridId;//'10e66b71-a136-4e45-bb6d-22927803563d';//obj.dffGridId;
                                        if (this.trf.dffGridData != null && this.trf.dffGridData != undefined) {
                                            this.dataList = JSON.parse(this.trf.dffGridData.toString());
                                            if (validatenull(this.dataList.EN)) {
                                                this.dataList.EN = this.defaultDataList_new.EN;
                                            }
                                            if (validatenull(this.dataList.CN)) {
                                                this.dataList.CN = this.defaultDataList_new.CN;
                                            }
                                            if (this.trf.servicRequire.reportLanguage.includes('2')) {
                                                this.getLanguageGridObjList(2);
                                            } else {
                                                this.getLanguageGridObjList(1);
                                            }

                                            var productItemNos = new Array();
                                            this.objList.forEach((item, index, array) => {
                                                productItemNos.push(item.productItemNo);
                                            })
                                            this.productItemNosList = productItemNos;
                                        } else {
                                            this.dataList = this.defaultDataList_new;
                                            this.getLanguageGridObjList(1);
                                            var productItemNos = new Array();
                                            this.objList.forEach((item, index, array) => {
                                                productItemNos.push(item.productItemNo);
                                            })
                                            this.productItemNosList = productItemNos;
                                        }
                                        this.isDffGridLoadFlag = true;
                                        //copy和模板进来以模板配置为准
                                        if (!validatenull(obj.specificCode)) {
                                            if (obj.specificCode.indexOf("care_Label") != -1) {
                                                this.buyerCustomerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
                                                this.buyerOrAgentName = this.trf.trfCustomer.buyerCustomerGroupName;
                                                /* if (this.trf.careLabelData != null && this.trf.careLabelData != undefined && this.trf.careLabelData.length != 0) {
                                                     this.careInstructions = JSON.parse(this.trf.careLabelData);
                                                 }*/
                                                if (this.trf.carelabelInstances != null && this.trf.carelabelInstances != undefined && this.trf.carelabelInstances.length != 0) {
                                                    this.careInstructions = this.trf.carelabelInstances;
                                                } else {
                                                    this.careInstructions = this.dafaultCareInstructions;
                                                }
                                                this.isCareLabelLoadFlag = true;
                                            }
                                        }
                                    });
                                }
                            });
                        } else {
                            if (obj != undefined && obj != null) {
                                this.$nextTick(() => {
                                    if (!validatenull(this.trf.dffFormId)) {
                                        // this.reqDffFormObj.id = this.trf.dffFormId;//'01a39781-2b0a-4c06-b552-d8c2e0f002ac';
                                        // this.reqDffFormObj.name = this.trf.dffName;//'C&A Footwear';
                                        if (this.trf.dffFormData != null && this.trf.dffFormData != undefined) {
                                            this.dffLanguageObj = JSON.parse(this.trf.dffFormData);
                                            if (validatenull(this.dffLanguageObj.EN)) {
                                                this.dffLanguageObj.EN = {};
                                            }
                                            if (validatenull(this.dffLanguageObj.CN)) {
                                                this.dffLanguageObj.CN = {};
                                            }
                                            if (this.trf.servicRequire.reportLanguage.includes('2')) {
                                                this.getLanguageObjList(2);
                                            } else {
                                                this.getLanguageObjList(1);
                                            }

                                        }
                                        this.isDffFormLoadFlag = true;

                                    }
                                    // 不存在grid不显示
                                    // this.reqDffGridObj.name = this.trf.dffName;
                                    if (!validatenull(this.trf.dffGridId)) {
                                        // this.reqDffGridObj.id = this.trf.dffGridId;//'10e66b71-a136-4e45-bb6d-22927803563d';//obj.dffGridId;
                                        if (this.trf.dffGridData != null && this.trf.dffGridData != undefined) {
                                            this.dataList = JSON.parse(this.trf.dffGridData);
                                            if (validatenull(this.dataList.EN)) {
                                                this.dataList.EN = this.defaultDataList_new.EN;
                                            }
                                            if (validatenull(this.dataList.CN)) {
                                                this.dataList.CN = this.defaultDataList_new.CN;
                                            }
                                            if (this.trf.servicRequire.reportLanguage.includes('2')) {
                                                this.getLanguageGridObjList(2);
                                            } else {
                                                this.getLanguageGridObjList(1);
                                            }


                                            var productItemNos = new Array();
                                            this.objList.forEach((item, index, array) => {
                                                productItemNos.push(item.productItemNo);
                                            })
                                            this.productItemNosList = productItemNos;
                                        } else {
                                            this.dataList = this.defaultDataList_new;
                                            this.getLanguageGridObjList(1);
                                            var productItemNos = new Array();
                                            this.objList.forEach((item, index, array) => {
                                                productItemNos.push(item.productItemNo);
                                            })
                                            this.productItemNosList = productItemNos;
                                        }
                                        this.isDffGridLoadFlag = true;
                                        if (this.trf.trfStatus > 1) {
                                            if (!validatenull(this.trf.carelabelInstances)) {
                                                this.buyerCustomerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
                                                this.buyerOrAgentName = this.trf.trfCustomer.buyerCustomerGroupName;
                                                /*if (this.trf.careLabelData != null && this.trf.careLabelData != undefined && this.trf.careLabelData.length != 0) {
                                                    this.careInstructions = JSON.parse(this.trf.careLabelData);
                                                }*/
                                                if (this.trf.carelabelInstances != null && this.trf.carelabelInstances != undefined && this.trf.carelabelInstances.length != 0) {
                                                    this.careInstructions = this.trf.carelabelInstances;
                                                } else {
                                                    this.careInstructions = this.dafaultCareInstructions;
                                                }
                                                this.isCareLabelLoadFlag = true;
                                            }
                                        } else {
                                            if (!validatenull(obj.specificCode)) {
                                                if (obj.specificCode.indexOf("care_Label") != -1) {
                                                    this.buyerCustomerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
                                                    this.buyerOrAgentName = this.trf.trfCustomer.buyerCustomerGroupName;
                                                    /*if (this.trf.careLabelData != null && this.trf.careLabelData != undefined && this.trf.careLabelData.length != 0) {
                                                        this.careInstructions = JSON.parse(this.trf.careLabelData);
                                                    }*/
                                                    if (this.trf.carelabelInstances != null && this.trf.carelabelInstances != undefined && this.trf.carelabelInstances.length != 0) {
                                                        this.careInstructions = this.trf.carelabelInstances;
                                                    } else {
                                                        this.careInstructions = this.dafaultCareInstructions;
                                                    }
                                                    this.isCareLabelLoadFlag = true;
                                                }
                                            }
                                        }


                                    }
                                });
                            }
                        }

                    } else {

                        if (this.trf.trfStatus == 1 || this.trf.actionType == 'copy') {
                            this.$notify({
                                title: this.$t('tip'),
                                message: this.$t('trf.templateErrorInfo'),
                                type: 'warning'
                            });
                            this.$set(this.trf, 'trfLab', {});
                            this.$set(this.trf, 'trfLabContact', {});
                            this.$set(this.trf, 'trfTemplateId', '');
                            this.$set(this.trf, 'templateName', '');
                            this.$set(this.trf, 'serviceType', '');
                            return false;
                        }
                    }
                });
                //回显测试包数据
                if (this.trf.trfStatus < 2) {
                    this.testPackageParam.templateId = this.trf.trfTemplateId;
                    this.testPackageParam.trfStatus = this.trf.trfStatus;
                    this.testPackageParam.trfId = this.trf.id;
                    this.treeLoading = true;
                    getTemplateTestPackageList(Object.assign({parentId: -1}, this.testPackageParam)).then(res => {
                        this.treeLoading = false;
                        this.testPackageTreedata = res.data.data;
                        //模板数据需要单独处理
                        if (!validatenull(this.testPackageTreedata)) {
                            //判断testPackageList是否有remark；
                            if (!validatenull(this.trf.testPackageList)) {
                                this.trf.testPackageList = this.trf.testPackageList.filter(function (item) {
                                    return !validatenull(item.remark);
                                });
                                console.log(this.trf.testPackageList);
                                if (!validatenull(this.trf.testPackageList)) {
                                    this.testPackageTreedata = this.setRemark(this.testPackageTreedata);
                                }
                            }
                        }
                        var ids = this.trf.testPackageIds;
                        if (!validatenull(ids)) {
                            this.$refs.selTree.setCheckedKeys(ids);
                        }
                    });
                } else {
                    this.commponentKey += 1;
                    this.testPackageExpand = true;
                    this.testPackageTreedata = this.trf.testPackages;

                }


                var returnSampleRequireStr = this.trf.servicRequire.returnSampleRequire;
                if (returnSampleRequireStr != null && returnSampleRequireStr != undefined) {
                    this.returnSampleArry = returnSampleRequireStr.split(',');
                }

                // this.trf.servicRequire.reportLanguage = this.trf.servicRequire.reportLanguage + '';
                if (this.trf.trfStatus == 2) {//自动触发撤销TRF操作
                    if (this.$route.query.actionType == 'edit') {
                        //alert("//自动触发撤销TRF操作");
                        this.returnTrfStatusClick();
                    }
                }
            },
            setRemark(datas) { //遍历树  获取id数组
                var testPackageList = this.trf.testPackageList
                for (var i in datas) {
                    for (var z in testPackageList) {
                        if (testPackageList[z].testPackageId == datas[i].id) {
                            if (!validatenull(testPackageList[z].remark)) {
                                datas[i].remark = testPackageList[z].remark;
                            }
                        }
                        if (datas[i].children) {
                            this.setRemark(datas[i].children);
                        }
                    }

                }
                return datas;
            },
            // getReturnSampleArry() {
            //     getReturnSampleArryCN(this.language).then(res => {
            //         const data = res.data;
            //         if (data.length > 0) {
            //             this.returnSampleData = data;
            //         }
            //     });
            // },
            //获取服务类型
            searchServiceType: function (BUID) {
                getServiceType(BUID).then(res => {
                    const data = res.data.data;
                    this.serviceTypeData = data;
                    //默认服务类型为Regular
                    if (this.serviceTypeData != null && this.serviceTypeData != undefined && this.serviceTypeData.length > 0) {
                        this.trf.serviceType = '1';
                        this.trf.serviceTypeName = 'Regular';
                    } else {
                        this.trf.serviceType = '';
                        this.trf.serviceTypeName = '';
                    }
                });
            },
            handleNodeClick(data) {
                this.trf.testPackage.testPackageId = data.id;
            },
            //另存为TRF模板保存方法
            saveTrfUserTemplate() {
                if (this.trfUserTemplate.trfTemplateName == '' || this.trfUserTemplate.trfTemplateName == null) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('trf.placeholder.trfTemplateName'),
                        type: 'warning'
                    });
                    return false;
                }
                this.btnSbuTrfTemplateLoading = true;
                //保存dff组件数据
                this.saveTrfTestPackageAndDffData('userTemplate');
                let cloneTemplateData = deepClone(this.trf);
                cloneTemplateData.id = null;
                cloneTemplateData.trfNo = null;
                cloneTemplateData.extendOrderNo = null;
                cloneTemplateData.trfStatus = 1;
                this.$set(this.trfUserTemplate, 'trfType', 30);
              this.$set(this.trfUserTemplate, 'productLineCode', ProductLineEnums.AFL.CODE);
                this.$set(this.trfUserTemplate, 'trfTemplateData', JSON.stringify(cloneTemplateData));
                this.$set(this.trfUserTemplate, 'trfName', this.trf.templateName);
                this.$set(this.trfUserTemplate, 'trfTemplateId', this.trf.trfTemplateId);
                this.$set(this.trfUserTemplate, 'trfBuyerCustomerGroupCode',this.trf.trfCustomer.buyerCustomerGroupCode);
                this.$set(this.trfUserTemplate, 'trfBuyerCustomerGroupName', this.trf.trfCustomer.buyerCustomerGroupName);

                if (this.applicationUser) {
                    this.$set(this.trfUserTemplate, 'applicantUser', this.applicationUser);
                }
                saveAflTrfUserTemplate(this.trfUserTemplate).then(res => {
                    this.trfTemplateDialogFormVisible = false;
                    this.btnSbuTrfTemplateLoading = false;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                }, error => {
                    this.btnSbuTrfTemplateLoading = false;
                    this.$message.error(this.$t('api.error'));
                });
            },
            //另存为TRF模板
            saveAsTrfTemplate() {
                this.trfUserTemplate.trfTemplateName = '';
                this.trfTemplateDialogFormVisible = true;
            },
            //return 状态回退点击事件
            returnTrfStatusClick() {
                this.$confirm(this.$t('trf.confirmReturnTrf'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    this.btnSubmitLoading = true;
                    console.log("回退Flag：")
                    console.log(this.trfFlag)
                    returnTrf(this.trf.id, this.trfFlag).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        let hashVal = new Date().getTime();
                        this.$router.push({
                            path: '/afl/trf/trfDetail',
                            query: {id: this.trf.id, flag: this.trfFlag, actionType: 'detail', hash: hashVal,trfNo:this.trf.trfNo,signature:this.$route.query.signature}
                        })

                        this.btnSubmitLoading = false;
                        this.commponentKey += 1;
                        this.trfDisabled = false;
                        this.testPackageExpand = false;
                        this.showCustomerNameFlag = false;
                        this.templateDisabled = false;
                        this.buyerDisabled = false;
                        this.isShowDffLanguage = false;
                        this.getParams();
                        //this.hash = hashVal;
                    }, error => {
                        this.btnSubmitLoading = false;
                        this.$message.error(this.$t('api.error'));
                    });
                }).catch(() => {
                    /* this.btnSubmitLoading=false;
                     this.$message.error(this.$t('api.error'));*/
                });
            },
            //确认收样点击事件
            confirmSampleClick() {
                this.confirmSampleForm.id = this.trf.id;
                if (this.confirmSampleForm.sampleReceiveDate == '' || this.confirmSampleForm.sampleReceiveDate == null) {
                    this.confirmSampleForm.sampleReceiveDate = dateVal;
                }
                this.trfConfirmSampleDialogFormVisible = true;
            },
            //确认收样表单提交
            confirmSampleDateSubmit() {
                this.btnConfirmSampleLoading = true;
                this.confirmSampleForm.sampleReceiveDate = moment(this.confirmSampleForm.sampleReceiveDate).format("YYYY-MM-DD HH:mm:ss");
                confirmAflSampleDate(this.confirmSampleForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.btnConfirmSampleLoading = false;
                    this.trfConfirmSampleDialogFormVisible = false;
                    let hashVal = new Date().getTime()
                    this.$router.push({
                        path: '/afl/trf/trfDetail',
                        query: {id: this.trf.id, flag: this.trfFlag, actionType: 'detail', hash: hashVal,trfNo:this.trf.trfNo,signature:this.$route.query.signature}
                    })
                    this.hash = hashVal;
                }, error => {
                    this.btnConfirmSampleLoading = false;
                    this.$message.error(this.$t('api.error'));
                });
            },
            //取消申请单
            cancelTrf(status) {
                this.$confirm(this.$t('trf.confirmCancelTrf'), this.$t('tip'), {
                    confirmButtonText: this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    this.btnSubmitLoading = true;
                    cancelTrf(this.trf.id).then(res => {
                        this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });
                        this.btnSubmitLoading = false;
                        let hashVal = new Date().getTime()
                        this.$router.push({
                            path: '/afl/trf/trfDetail',
                            query: {id: this.trf.id, flag: this.trfFlag, actionType: 'edit', hash: hashVal,trfNo:this.trf.trfNo,signature:this.$route.query.signature}
                        })
                        this.hash = hashVal;
                        //this.$router.push({path: '/trf/list', query: {}});
                    }, error => {
                        this.btnSubmitLoading = false;
                        this.$message.error(this.$t('api.error'));
                    });
                }).catch(() => {
                    /* this.btnSubmitLoading=false;
                     this.$message.error(this.$t('api.error'));*/
                });
            },
            // 获取样品信息表单数据
            getqueryDffFormData() {
                queryDffFromData(this.dffFormParams).then(res => {
                    this.dffFormList = res.data
                    console.log(this.dffObjNew)
                    this.dffFormList.forEach(ele => {
                        //首字母转小写
                        ele.fieldCode = ele.fieldCode.replace(ele.fieldCode[0], ele.fieldCode[0].toLowerCase());
                        if (ele.fieldType == 'Select2') {
                            this.$set(this.dffObjNew, ele.fieldCode, this.dffObjNew[ele.fieldCode] ? this.dffObjNew[ele.fieldCode] : [])
                        } else {
                            this.$set(this.dffObjNew, ele.fieldCode, this.dffObjNew[ele.fieldCode] ? this.dffObjNew[ele.fieldCode] : '')
                        }
                        let idx = ele.dffFormAttrEvents.findIndex(ele1 => {
                            return ele1.eventType == 'init'
                        })
                        if (idx > -1) {
                            let arr = ele.dffFormAttrEvents[idx].dffFormEventEffects[0].dffAttrValues
                            if (this.optionType == 'add' || this.optionType == 'edit' || this.trf.trfStatus == 1) {
                                if (ele.fieldType == 'Select') {
                                    if (this.optionType == 'add') {
                                        this.dffObjNew[ele.fieldCode] = arr[0].value
                                    }
                                    if (ele.dispalyName == '样品主要成分' && this.dffObjNew[ele.fieldCode] == '非危险样品') {
                                        this.dffFormDanger = false
                                    } else {
                                        this.dffFormDanger = true
                                    }
                                } else if (ele.fieldType == 'Select2' && ele.dispalyName != '危险性' && this.optionType == 'add') {
                                    this.dffObjNew[ele.fieldCode].push(arr[0].value)
                                }
                            }
                            ele.attrValue = arr
                        }
                    })
                })
            },
            // 获取样品信息表格表单数据
            getqueryDffFormTableData() {
                this.dffFormTableList = []
                queryDffFromData(this.dffFormTableParams).then(res => {
                    let arr = []
                    res.data.forEach(ele => {
                        //首字母转小写
                        ele.fieldCode = ele.fieldCode.replace(ele.fieldCode[0], ele.fieldCode[0].toLowerCase());
                        arr.push({...ele})
                    })
                    this.dffGridTableTemplate = arr
                    if (this.objListNew.length > 0) {
                        this.objListNew.forEach(ele => {
                            if (ele.dFFFormID) {
                                let list = this.returnDffGridData(ele)
                                if (list.length > 0) {
                                    this.dffFormTableList.push(list)
                                }
                            }
                        })
                    }
                    if ((this.optionType == 'add' || this.optionType == 'edit' || this.trf.trfStatus == 1) && this.dffFormTableList.length == 0) {
                        this.dffFormTableList.push(res.data)
                    }
                })
            },
            returnDffGridData(ele) {
                let list = []
                this.dffGridTableTemplate.forEach(ele => {
                    list.push({...ele})
                })
                let idx = 0
                list.forEach(ele1 => {
                    if ((ele[ele1.fieldCode] && ele1.mandatoryFlag == 1) || (ele1.mandatoryFlag == 0)) {
                        ele1[ele1.fieldCode] = ele[ele1.fieldCode]
                        idx++
                    }
                })
                if (idx == list.length) {
                    return list
                } else {
                    return []
                }
            },
            otherReportMethodCheckBoxChange() {
                if (this.otherReportMethodCheckBox == 0) {
                    this.trf.servicRequire.otherReportMethod = ''
                }
            },
            // 添加表格表单
            addDffGrid() {
                let num = this.dffGridIpt || 1
                for (let i = 0; i < num; i++) {
                    let arr = []
                    this.dffGridTableTemplate.forEach(ele => {
                        arr.push({...ele})
                    })
                    this.dffFormTableList.push(arr)
                }
            },
            // 下载模板
            handleExport() {
                let columns = []
                this.dffGridTableTemplate.forEach(ele => {
                    columns.push({
                        title: ele.dispalyName,
                        key: ele.fieldCode
                    })
                })
               debugger;

               let languageID = 1;
                if(this.language==='zh-CN'){
                    languageID=2;
                }

                window.open("/api/sgs-mart/afl/trf/download-excel?dffGridId=" + this.dffFormTableParams.id+"&languageId="+languageID+"&fileName=样品数据", '_blank');
                //export2Excel(columns, [], '样品数据')
            },
            // 导入数据
            handleImport(f) {
                debugger;
                this.$confirm(this.$t('trf.trfConfirmTit'), this.$t('tip'), {
                    confirmButtonText: this.$t('common.yes'),
                    cancelButtonText: this.$t('common.no'),
                    type: 'warning'
                }).then(() => {
                    this.dffFormTableList = []
                    this.pushImportData(f)
                }).catch(() => {
                    this.pushImportData(f)
                });
            },
            pushImportData(f) {
                debugger;
                var formData = new FormData();
                formData.append("file", f.file);
                //formData.append("param", JSON.stringify( this.dffGridTableTemplate));
                 //修改为后端上传
                 uploadAflGridExcelData(formData).then(res => {
                    debugger;
                    console.log('后端 excel Data',res)
                   let data = res.data.data;
                   let outdata = data;//XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]]);
                    let arrs = []
                    outdata.forEach(ele => {
                        let arr = []
                        this.dffGridTableTemplate.forEach(ele => {
                            arr.push({...ele})
                        })
                        arr.forEach(ele1 => {
                            if (ele[ele1.dispalyName]) {
                                // if (ele1.fieldCode.endsWith("Attribute5")) {
                                //     if (typeof ele[ele1.dispalyName] == 'number' && ele1.dispalyName.includes('生产日期')) {
                                //         const time = new Date((ele[ele1.dispalyName] - 1) * 24 * 3600000 - 1)
                                //         time.setYear(time.getFullYear() - 70)
                                //         time.setDate(time.getDate())
                                //         const year = time.getFullYear()
                                //         const month = time.getMonth() + 1
                                //         const date = time.getDate()
                                //         console.log(year + "-" + (month < 10 ? '0' + month : month) + "-" + (date < 10 ? '0' + date : date))
                                //         ele1[ele1.fieldCode] = year + "-" + (month < 10 ? '0' + month : month) + "-" + (date < 10 ? '0' + date : date)
                                //     } else {
                                //         ele1[ele1.fieldCode] = ele[ele1.dispalyName]
                                //     }
                                // } else {
                                    ele1[ele1.fieldCode] = ele[ele1.dispalyName]
                               // }
                            }
                        })
                        arrs.push(arr)
                    })
                    debugger;
                    this.dffFormTableList = arrs.concat(this.dffFormTableList)

                }).catch(error => {
                    console.log('upload Error',error.message);
                })


                 return false;
                let list = this.dffGridTableTemplate
                let _this = this
                var XLSX = require("xlsx");
                var reader = new FileReader();
                reader.onload = function (e) {
                    var data = e.target.result;
                    var workbook = XLSX.read(data, {type: 'binary'});
                    let outdata = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]]);
                    console.log('excel Data',outdata)
                    let arrs = []
                    outdata.forEach(ele => {
                        let arr = []
                        _this.dffGridTableTemplate.forEach(ele => {
                            arr.push({...ele})
                        })
                        arr.forEach(ele1 => {
                            if (ele[ele1.dispalyName]) {
                                if (ele1.fieldCode.endsWith("Attribute5")) {
                                    if (typeof ele[ele1.dispalyName] == 'number' && ele1.dispalyName.includes('生产日期')) {
                                        const time = new Date((ele[ele1.dispalyName] - 1) * 24 * 3600000 - 1)
                                        time.setYear(time.getFullYear() - 70)
                                        time.setDate(time.getDate())
                                        const year = time.getFullYear()
                                        const month = time.getMonth() + 1
                                        const date = time.getDate()
                                        console.log(year + "-" + (month < 10 ? '0' + month : month) + "-" + (date < 10 ? '0' + date : date))
                                        ele1[ele1.fieldCode] = year + "-" + (month < 10 ? '0' + month : month) + "-" + (date < 10 ? '0' + date : date)
                                    } else {
                                        ele1[ele1.fieldCode] = ele[ele1.dispalyName]
                                    }
                                } else {
                                    ele1[ele1.fieldCode] = ele[ele1.dispalyName]
                                }
                            }
                        })
                        arrs.push(arr)
                    })
                    debugger;
                    _this.dffFormTableList = arrs.concat(_this.dffFormTableList)
                };
                reader.readAsBinaryString(f.file);
            },
            // 删除表格表单
            dffGridDelOne(index) {
                if (this.trfDisabled) return
                if (this.dffFormTableList.length == 1) {
                    this.$notify({
                        type: 'error',
                        title: 'Error',
                        message: '至少需要一个产品样本',
                        duration: 4500
                    });
                    return;
                }
                this.dffFormTableList.splice(index, 1)
                // this.$confirm('至少有一个产品样本是否确实要删除产品样本！此操作无法撤消!!', '提示', {
                //     confirmButtonText: '确定',
                //     cancelButtonText: '取消',
                //     type: 'warning'
                // }).then(() => {
                //     this.dffFormTableList.splice(index,1)
                // }).catch(() => {
                //     this.$message({
                //         type: 'info',
                //         message: '已取消删除'
                //     });
                // });
            },
            // 复制
            dffGridCopyOne(index) {
                if (this.trfDisabled) return
                let arr = []
                this.dffFormTableList[index].forEach(ele => {
                    arr.push({...ele})
                })
                this.dffFormTableList.push(arr)
            },
            // 是否选择了非危险品
            dffFormChange(item, e) {
                if (item.dispalyName == '样品主要成分') {
                    if (e == '非危险样品') {
                        this.dffObjNew.specialProductAttribute5 = []
                        this.dffFormDanger = false
                    } else {
                        this.dffFormDanger = true
                    }
                }
                item.trfDisabledDff = false
            },
            // 是否选择了其他
            isChoose(label, name) {
                let value = this.dffObjNew[label]
                if (value.length == 0) return false
                let idx = value.findIndex(ele => {
                    return ele == name
                })
                if (idx > -1) {
                    return true
                } else {
                    return false
                }
            },
            // 选择其他时input 监听
            dffIptChange(item) {
                console.log(item)
            },
            // 判断报告语言选择的是中文还是英文
            reportLanguageVerify(type) {
                let reportLanguage = this.trf.servicRequire.reportLanguage
                if (type == 'en' && reportLanguage.includes('1')) {
                    return true
                } else if (type == 'zh' && reportLanguage.includes('2')) {
                    return true
                } else {
                    return false
                }
            },
            getDffFormData(value) {
                console.log(value)
            },
            getDffGridData(value) {
            },
            onSubmit(submitFlag) {
                //验证 只有提交的时候做验证
                if (submitFlag == '2' || submitFlag == 2) {
                    console.log(this.dffObjNew)
                    console.log(this.dffFormTableList)
                    console.log(this.dffFormList)
                    //验证申请单数据
                    this.$refs['trf'].validate((valid, error) => {
                        if (valid) {
                            console.log('dffFormLoadFlag->' + this.isDffFormLoadFlag);
                            if (this.isDffFormLoadFlag) {
                                for (let key in this.dffObjNew) {
                                    let obj = this.dffFormList.find(ele => {
                                        return ele.fieldCode == key
                                    })
                                    if ((obj && obj.mandatoryFlag == 1 && key == 'specialProductAttribute5' && this.dffObjNew[key].length == 0 && this.dffFormDanger) || (obj && obj.mandatoryFlag == 1 && this.dffObjNew[key].length == 0 && key != 'specialProductAttribute5')) {
                                        this.$notify({
                                            title: this.$t('tip'),
                                            message: this.$t('trf.trfValidateError'),
                                            type: 'warning'
                                        });
                                        let obj = this.dffFormList.find(ele => {
                                            return ele.fieldCode == key
                                        })
                                        if (obj) {
                                            this.$set(obj, 'trfDisabledDff', true)
                                        }
                                        return false;
                                    }
                                }
                                // let valid = this.$refs.dffForm1.checkForm();
                                // if (!valid) {
                                //     this.$notify({
                                //         title: this.$t('tip'),
                                //         message: this.$t('trf.trfValidateError'),
                                //         type: 'warning'
                                //     });
                                //     return false;
                                // }
                            }/* else {
                                this.$notify({
                                    title: this.$t('tip'),
                                    message: this.$t('trf.trfValidateError'),
                                    type: 'warning'
                                });
                                return false;
                            }*/

                            let valid = false
                            this.dffFormTableList.forEach(ele => {
                                let n = 0
                                ele.forEach(ele1 => {
                                    if ((ele1[ele1.fieldCode] && ele1.mandatoryFlag == 1) || ele1.mandatoryFlag == 0) {
                                        n++
                                    }
                                })
                                if (n == ele.length) {
                                    valid = true
                                    return
                                }
                            })
                            if (!valid) {
                                this.$notify({
                                    title: this.$t('tip'),
                                    message: this.$t('trf.trfValidateError'),
                                    type: 'warning'
                                });
                                this.trfDisabledDffGrid = true
                                return false;
                            } else {
                                this.trfDisabledDffGrid = false
                                // let arr = []
                                // this.dffFormTableList.forEach((ele,index)=>{
                                //   let json = {
                                //     dFFFormID:'',
                                //     productItemNo:'SupplierCode_'+(index+1),
                                //   }
                                //   ele.forEach(ele1=>{
                                //     json.dFFFormID = ele1.dFFFormID
                                //     json[ele1.fieldCode] = ele1[ele1.fieldCode]
                                //   })
                                //   arr.push(json)
                                // })
                                // this.objListNew = arr
                            }

                            // if (this.isDffGridLoadFlag) {

                            // let gridValid = this.$refs.gridRef.checkGrid(true);
                            // if (!gridValid) {
                            //     this.$notify({
                            //         title: this.$t('tip'),
                            //         message: this.$t('trf.trfValidateError'),
                            //         type: 'warning'
                            //     });
                            //     return false;
                            // }
                            // }

                            //验证CCL 自定义表单
                            this.saveTrfSummit(submitFlag);
                            //验证发票
                        } else {
                            // 跳转未填写的必填项，el-form-item 需要增加 ref 属性方便定位，值和 prop 相同即可
                            let str = []
                            for (let key in error) {
                                error[key].map(item => {
                                    str.push(item.message)
                                })
                                let dom = this.$refs[Object.keys(error)[0]]
                                if (Object.prototype.toString.call(dom) !== '[object Object]') {
                                    dom = dom[0]
                                    break
                                }
                                dom.$el.scrollIntoView({
                                    block: 'center',
                                    behavior: 'smooth'
                                });
                            }

                            this.$notify({
                                title: this.$t('tip'),
                                message: this.$t('trf.trfValidateError'),
                                type: 'warning'
                            });
                            return false;
                        }
                    });
                } else {
                    this.saveTrfSummit(submitFlag);
                }
            },
            async saveTrfSummit(submitFlag) {
                //07-13添加factory回写 函数调用；
                // if (this.isDffFormLoadFlag) {
                //     this.$refs.dffForm1.callbackApiFnc();
                // }

                //生成dffGrid数据
                this.saveDffGridDataList()

                this.trf.trfStatus = submitFlag;
                this.saveTrfTestPackageAndDffData();
                //提交表单
                this.btnSubmitLoading = true;

                console.log("提交数据");
                var submitTrf = JSON.parse(JSON.stringify(this.trf));
                if (Array.isArray(submitTrf.servicRequire.reportLanguage) && submitTrf.servicRequire.reportLanguage) {
                    submitTrf.servicRequire.reportLanguage = submitTrf.servicRequire.reportLanguage.join(",");
                } else {
                    submitTrf.servicRequire.reportLanguage = ''
                }
                if (Array.isArray(submitTrf.servicRequire.reportType) && submitTrf.servicRequire.reportType) {
                    submitTrf.servicRequire.reportType = submitTrf.servicRequire.reportType.join(",");
                } else {
                    submitTrf.servicRequire.reportType = ''
                }
                console.log("提交Flag：")
                console.log(this.trfFlag)
                let res = await saveAflTrf(submitTrf);
                // let res = {data:{data:{code:500}}}
                if (res.data.data.code == 200) {
                    this.btnSubmitLoading = false;
                    this.careInstructions = res.data.data.data.carelabelInstances;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    var actionType = 'detail';
                    if (submitFlag == '1') {
                        actionType = 'edit';
                        let hashVal = new Date().getTime()
                        this.$router.push({
                            path: '/afl/trf/trfDetail',
                            query: {id: res.data.data.data.id, actionType: actionType, flag: 3, hash: hashVal,trfNo:res.data.data.data.trfNo,signature:this.$route.query.signature}
                        })
                        this.hash = hashVal;
                        this.reload();
                    }
                    if (submitFlag == '2' || submitFlag == 2) {
                        this.id = res.data.data.data.id
                        this.$set(this.trf, 'trfNo', res.data.data.data.trfNo)
                        this.printTrfTipDialogVisible = true
                        // this.$alert(this.$t('trf.printTrfTip'), this.$t('tip'), {
                        //     confirmButtonText: this.$t('operation.confirm'),
                        //     callback: action => {

                        //     }
                        // });
                    }
                } else {
                    this.trf.trfStatus = 1;//进入异常的话 状态初始化
                    this.btnSubmitLoading = false;
                    this.$message.error(this.$t('api.error'));
                }


                /*saveCclTrf(this.trf, this.trfFlag).then(res => {
                    this.btnSubmitLoading = false;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.trf.carelabelInstances=null;
                    this.careInstructions=[];
                    var actionType = 'detail';
                    if (submitFlag == '1') {
                        actionType = 'edit';
                        let hashVal = new Date().getTime()
                        this.$router.push({
                            path: '/afl/trf/trfDetail',
                            query: {id: res.data.data.data.id, actionType: actionType, flag: 1, hash: hashVal}
                        })
                        this.hash = hashVal;
                    }
                    if (submitFlag == '2' || submitFlag == 2) {

                        this.$alert(this.$t('trf.printTrfTip'), this.$t('tip'), {
                            confirmButtonText: this.$t('operation.confirm'),
                            callback: action => {
                                let hashVal = new Date().getTime()
                                this.$router.push({
                                    path: '/afl/trf/trfDetail',
                                    query: {id: res.data.data.data.id, actionType: actionType, flag: 1, hash: hashVal}
                                })
                                this.hash = hashVal;
                            }
                        });
                    }
                }, error => {
                    this.trf.trfStatus = 1;//进入异常的话 状态初始化
                    this.btnSubmitLoading = false;
                    this.$message.error(this.$t('api.error'));
                });*/
            },
            printTrfTipSubmit() {
                let hashVal = new Date().getTime()
                this.$router.push({
                    path: '/afl/trf/trfDetail',
                    query: {id: this.id, actionType: 'detail', flag: 3, hash: hashVal,trfNo:this.trf.trfNo,signature:this.$route.query.signature}
                })
                this.hash = hashVal;
                this.reload();
            },
            //生成dffGrid数据
            saveDffGridDataList() {
                let arr = []
                this.dffFormTableList.forEach((ele, index) => {
                    let json = {
                        dFFFormID: '',
                        productItemNo: 'SupplierCode_' + (index + 1),
                    }
                    ele.forEach(ele1 => {
                        json.dFFFormID = ele1.dFFFormID
                        json[ele1.fieldCode] = ele1[ele1.fieldCode]
                    })
                    arr.push(json)
                })
                this.objListNew = arr
            },
            transSaveData(dffFormData) {
                let saveDataItem = JSON.parse(JSON.stringify(dffFormData));
                console.log(saveDataItem)
                let otherN = ['OTHER', '其他'];
                for (let key in saveDataItem) {
                    if (key == 'EN') {
                        //saveDataItem[key].forEach(dff => {
                        for (let dffKey in saveDataItem['EN']) {
                            if (saveDataItem['EN'][dffKey + '_other']) {
                                let allSelect = saveDataItem['EN'][dffKey];
                                let otherData = saveDataItem['EN'][dffKey + '_other'];
                                // let allData = allSelect.split(",");
                                let allData = allSelect
                                if (allData.length > 1) {
                                    let shouldSave = allData.filter(da => !otherN.includes(da));
                                    otherData = [...shouldSave, otherData].join(",");
                                }
                                saveDataItem['EN'][dffKey] = otherData;
                            }
                        }
                    }
                    if (key == 'CN') {
                        for (let dffKey in saveDataItem['CN']) {
                            if (saveDataItem['CN'][dffKey + '_other']) {
                                let allSelect = saveDataItem['CN'][dffKey];
                                let otherData = saveDataItem['CN'][dffKey + '_other']
                                // let allData = allSelect.split(",");
                                let allData = allSelect
                                console.log(allData)
                                if (allData.length > 1) {
                                    let shouldSave = allData.filter(da => !otherN.includes(da));
                                    otherData = [...shouldSave, otherData].join(",");
                                }
                                saveDataItem['CN'][dffKey] = otherData;
                            }
                        }
                    }
                }
                return saveDataItem;
            },
            saveTrfTestPackageAndDffData(action) {
                //获取选中的testPackage
                //if (action != 'userTemplate') {
                // var ids = this.$refs.selTree.getCheckedKeys();
                // if (ids != undefined && ids.length > 0) {
                //     this.trf.testPackageIds = ids;
                // } else {
                //     this.trf.testPackageIds = [];
                // }
                // }
                //生成dffGrid数据
                this.saveDffGridDataList()
                // var selectTestPackageIds = this.$refs.selTree.getCheckedNodes(false, true);
                var selectIdsStr = '';
                this.trf.testPackages = [];
                this.trf.testPackageList = [];

                // if (selectTestPackageIds) {
                //     selectTestPackageIds.find((item) => {
                //         var testPackage = {};
                //         if (item.remark != 'remark' && item.remark != 'Remark') {
                //             testPackage.remark = item.remark;
                //         }
                //         testPackage.testPackageId = item.id;
                //         this.trf.testPackageList.push(testPackage);
                //         selectIdsStr += item.id + ',';
                //     });
                // }

                selectIdsStr = (selectIdsStr.substring(selectIdsStr.length - 1) == ',') ? selectIdsStr.substring(0, selectIdsStr.length - 1) : selectIdsStr;
                this.trf.testPackageAllIds = selectIdsStr;
                delete this.trf.dffGridData;
                delete this.trf.careLabelData;
                delete this.trf.dffFormData;
                //获取当前语言
                // if (this.languageID == 1) {
                //     this.dffLanguageObj.EN = this.dffObjNew;
                //     this.dataList.EN = this.objListNew;
                // } else {
                //     this.dffLanguageObj.CN = this.dffObjNew;
                //     this.dataList.CN = this.objListNew;
                // }

                this.dffLanguageObj.CN = this.dffObjNew;
                this.dataList.CN = this.objListNew;
                console.log(this.dffObjNew)
                console.log(this.objListNew)
                this.trf.dffFormData = JSON.stringify(this.transSaveData(this.dffLanguageObj));
                this.trf.dffGridData = JSON.stringify(this.dataList);
                console.log(this.trf)
                if (this.isCareLabelLoadFlag) {
                    console.log(JSON.stringify(this.careInstructions));
                    //this.trf.careLabelData = JSON.stringify(this.careInstructions);
                    this.trf.carelabelInstances = this.careInstructions;
                }
            },
            contactrNameChange(val) {
                let obj = {};
                obj = this.customerContactData.find((item) => {
                    return item.contactName === val;
                });
                if (obj != undefined && obj != null) {
                    //获取联系人详情
                    this.$set(this.trf.trfCustomerContact, 'applyContactTel', obj.contactTel);
                    this.$set(this.trf.trfCustomerContact, 'applyContactEmail', obj.contactEmail);
                    if (!this.trf.servicRequire.reportReceivingEmail && obj.contactEmail) {
                        this.trf.servicRequire.reportReceivingEmail = obj.contactEmail
                    }
                }
            },
            // 申请方信息邮箱失去焦点
            concatEmailBlur() {
                if (!this.trf.servicRequire.reportReceivingEmail && this.trf.trfCustomerContact.applyContactEmail) {
                    this.trf.servicRequire.reportReceivingEmail = this.trf.trfCustomerContact.applyContactEmail
                }
            },
            customerAddressChange(val) {
                let obj = {};
                obj = this.customerAddressData.find((item) => {
                    return item.addressDetail === val;
                });
                if (obj != undefined && obj != null) {
                    if (this.trf.servicRequire.reportAddress == '' || this.trf.servicRequire.reportAddress == null) {
                        this.$set(this.trf.servicRequire, 'reportAddress', obj.addressDetail);
                    }
                } else {
                    this.$set(this.trf.servicRequire, 'reportAddress', val);
                }
            },
            selectLabChange(val) {
                // this.$set(this.trf.trfLab, 'labAddress', '');
                // this.$set(this.trf.trfLab, 'labName', '');
                // this.$set(this.trf, 'trfLabContact', {});
                // this.templateLabContactData = [];
                let obj = {};
                obj = this.templateLabData.find((item) => {
                    return item.labCode === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.trf.trfLab, 'labCode', obj.labCode);
                    this.$set(this.trf.trfLab, 'labName', obj.labName);
                    this.$set(this.trf.trfLab, 'labAddress', obj.labAddress);
                    this.$set(this.trf.trfLabContact, 'labCode', obj.labName);
                    this.$set(this.trf.trfLabContact, 'labAddress', obj.labAddress);
                    this.$set(this.trf.trfLabContact, 'labName', obj.labName);
                    // this.$set(this.templateLabContactDataParam, 'labTypeFlag', obj.labTypeFlag);
                }
                // this.$set(this.templateLabContactDataParam, 'labCode', val);
                // this.$set(this.templateLabContactDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                //查询实验室联系人
                // this.searchTemplateLabContactData();
            },
            // selectLabContactChange(val) {
            //     let obj = {};
            //     obj = this.templateLabContactData.find((item) => {
            //         return item.labContactId === val;
            //     });
            //     if (obj != undefined && obj != null) {
            //         this.$set(this.trf.trfLabContact, 'contactName', obj.labContactName);
            //         this.$set(this.trf.trfLabContact, 'contactEmail', obj.contactEmail);
            //         this.$set(this.trf.trfLabContact, 'contactTel', obj.contactTel);
            //     }
            // },
            //查询模板实验室下拉数据
            // searchTemplateLabContactData() {
            //     var params = {};
            //     getTemplateLabContactList(Object.assign(params, this.templateLabContactDataParam)).then(res => {
            //         const data = res.data.data;
            //         this.templateLabContactData = data;
            //         if (this.templateLabContactData == null || this.templateLabContactData == undefined || this.templateLabContactData.length == 0) {//无联系人 不需要选择
            //             this.labContactFlag = false;
            //             return false;
            //         } else {
            //             this.templateLabContactData.forEach((item, index, array) => {
            //                 var contactEmail = item.contactEmail;
            //                 if (!validatenull(contactEmail)) {
            //                     item.labContactName = item.labContactName + " (" + contactEmail + ")";
            //                 }
            //             })
            //         }
            //         this.labContactFlag = true;
            //         let obj = {};
            //         obj = this.templateLabContactData.find((item) => {
            //             return item.isDefault === 1;
            //         });
            //         if (obj != null && obj != undefined) {
            //             this.$set(this.trf.trfLabContact, 'labContactId', obj.labContactId);
            //             this.$set(this.trf.trfLabContact, 'contactName', obj.labContactName);
            //             this.$set(this.trf.trfLabContact, 'contactEmail', obj.contactEmail);
            //             this.$set(this.trf.trfLabContact, 'contactTel', obj.contactTel);

            //             加载出实验室数据
            //             this.$set(this.trf.trfLab, 'labCode', obj.labCode);
            //             let labObj = {};
            //             labObj = this.templateLabData.find((item) => {
            //                 return item.labCode === obj.labCode;
            //             });
            //             if (labObj != undefined && labObj != null) {
            //                 //赋值实验室地址
            //                 this.$set(this.trf.trfLab, 'labAddress', labObj.labAddress);
            //                 this.$set(this.trf.trfLabContact, 'labName', labObj.labName);
            //                 this.$set(this.trf.trfLabContact, 'labCode', labObj.labCode);
            //                 this.$set(this.trf.trfLabContact, 'labAddressb', labObj.labAddress);
            //             }
            //         }
            //     });
            // },
            async selectTemplateChange(val) {
                this.trf.trfLab = {};
                this.trf.trfLabContact = {};
                this.trf.serviceType = '';
                this.trf.serviceTypeName = '';
                this.isDffFormLoadFlag = false;
                this.isDffGridLoadFlag = false;
                this.isCareLabelLoadFlag = false;
                this.trf.dffFormId = '';
                this.trf.dffGridId = '';
                this.dffObj = {};
                this.dataList = this.defaultDataList_new;
                this.beLongData = [];
                this.$set(this.trf, 'templateName', '');
                //查询Belong
                this.queryCurrentDeptBeLong(val);
                /*if (!this.optionType == 'material') {

                }*/
                var careLabList = ['SupplierCode_1'];
                this.productItemNosList = careLabList;
                this.careInstructions = this.dafaultCareInstructions;
                let obj = {};
                obj = this.templateData.find((item) => {
                    return item.id === val;
                });
                //加载dff form组件
                if (obj != undefined && obj != null) {
                    if (this.trfFlag == 2) {
                        let fieldData = await getFieldSettingsByTemplateId(obj.id);
                        if (fieldData.data.code == 200) {
                            this.fieldSettingsData = fieldData.data.data;
                        }
                    }

                    // 不存在form不显示
                    if (!validatenull(obj.dffFormId)) {
                        let dffFormIds = [];
                        dffFormIds.push(obj.dffFormId);
                        //调用接口 查询最新dffFormID
                        getDffFormComfirmIdBatchByIds(dffFormIds).then(res => {
                            var newDffFormId = obj.dffFormId;
                            const data = res.data.data;
                            if (data != null && data.length > 0 && data != undefined) {
                                newDffFormId = data[0].comfirmDffFormId;
                            }
                            this.$set(this.trf, 'dffFormId', newDffFormId);
                            //判断当前语言获取对应的dffForm
                            //默认查询英文
                            if (this.trf.servicRequire.reportLanguage.includes('2')) {
                                this.languageID = 2;
                                //this.dffObj = this.dffLanguageObj.CN;
                            } else {
                                this.dffLanguageObj.CN = {};
                                this.languageID = 1;
                                //this.dffObj = this.dffLanguageObj.EN;
                            }
                            this.$set(this.reqDffFormObj, 'id', newDffFormId);
                            this.$set(this.reqDffFormObj, 'name', obj.dffName);
                            this.isDffFormLoadFlag = true;
                            // this.$nextTick(() => {
                            //     var that = this;
                            //     console.log(that.$refs.dffForm1);
                            //     if (that.$refs.dffForm1.headerConfig.length > 0 && this.$refs.gridRef.dffTableConfigData.length > 0) {

                            //     }
                            // });

                            return false;
                        });
                        // 不存在grid不显示
                        this.$set(this.reqDffGridObj, 'name', obj.dffName);
                        if (!validatenull(obj.dffGridId)) {
                            //调用接口 查询最新dffGridID
                            let dffGridIds = [];
                            dffGridIds.push(obj.dffGridId);
                            getDffFormComfirmIdBatchByIds(dffGridIds).then(res => {
                                var newDffGridId = obj.dffGridId;
                                const data = res.data.data;
                                if (data != null && data.length > 0 && data != undefined) {
                                    newDffGridId = data[0].comfirmDffFormId;
                                }
                                this.$set(this.trf, 'dffGridId', newDffGridId);
                                this.$set(this.reqDffGridObj, 'id', newDffGridId);
                                if (this.trf.servicRequire.reportLanguage.includes('2')) {
                                    // this.objList = this.dataList.CN;
                                    this.getLanguageGridObjList(2);
                                } else {
                                    this.getLanguageGridObjList(1);
                                    //this.objList = this.dataList.EN;
                                }
                                this.isDffGridLoadFlag = true;
                                if (!validatenull(obj.specificCode)) {
                                    if (obj.specificCode.indexOf("care_Label") != -1) {
                                        this.buyerCustomerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
                                        this.buyerOrAgentName = this.trf.trfCustomer.buyerCustomerGroupName;
                                        this.isCareLabelLoadFlag = true;
                                    }
                                }

                            });
                        }
                    }
                    this.$set(this.trf, 'templateName', obj.templateName);
                    this.$set(this.trf, 'productLineId', obj.productLineId);
                    this.$set(this.trf, 'productLineCode', obj.productLineCode);
                    // this.$set(this.templateLabDataParam, 'productLineCode', obj.productLineCode);
                    this.$set(this.trf, 'productLineName', obj.productLineName);
                    //查询服务类型sel
                    this.searchServiceType(obj.productLineId);
                }
                //查询该模板下的实验室及联系人
                // this.$set(this.templateLabDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                this.$set(this.testPackageParam, 'templateId', this.trf.trfTemplateId);
                //查询模板关联的测试包
                this.searchTemplateTestPackage();
                // this.searchTemplateLabData();
            },
            searchTemplateTestPackage() {
                this.treeLoading = true;
                getTemplateTestPackageList(Object.assign({parentId: -1}, this.testPackageParam)).then(res => {
                    this.treeLoading = false;
                    this.testPackageTreedata = res.data.data;
                });
            },
            //查询模板实验室下拉数据
            // searchTemplateLabData() {
            //     var params = {};
            //     getTemplateLabList(Object.assign(params, this.templateLabDataParam)).then(res => {
            //         const data = res.data.data;
            //         this.templateLabData = data;
            //         if (data.length == 1) {
            //             this.selectLabChange(data[0].labCode);
            //         }
            //     });
            // },
            async searchTemplateData() {
                this.$set(this.templateDataParam, 'productLineCode', this.trf.productLineCode);
                var params = {};
                let res = await getTemplateList(Object.assign(params, this.templateDataParam));
                //getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
                this.templateDataLoad = true;
                const data = res.data.data;
                this.templateData = data;
                //20-6-19新增逻辑 如果buyer选择general的话 则只带出general的模板，如果只有一个模板的话 则自动带入，无需选择；
                if (this.templateData.length == 1) {
                    this.trf.trfLab = {};
                    this.trf.trfLabContact = {};
                    this.trf.serviceType = '';
                    this.isDffFormLoadFlag = false;
                    // this.isDffGridLoadFlag = false;
                    this.isCareLabelLoadFlag = false;
                    this.$set(this.trf, 'dffFormId', '');
                    this.$set(this.trf, 'dffGridId', '');
                    this.trf.dffFormId = '';
                    this.trf.dffGridId = '';
                    var obj = this.templateData[0];
                    this.$set(this.trf, 'trfTemplateId', obj.id);
                    //加载dff form组件
                    if (obj != undefined && obj != null) {

                        this.$nextTick(() => {
                            // 不存在form不显示
                            if (!validatenull(obj.dffFormId)) {
                                let dffFormIds = [];
                                dffFormIds.push(obj.dffFormId);
                                //调用接口 查询最新dffFormID
                                getDffFormComfirmIdBatchByIds(dffFormIds).then(res => {
                                    var newDffFormId = obj.dffFormId;
                                    const data = res.data.data;
                                    if (data != null && data.length > 0 && data != undefined) {
                                        newDffFormId = data[0].comfirmDffFormId;
                                    }
                                    this.$set(this.trf, 'dffFormId', newDffFormId);
                                    //判断当前语言获取对应的dffForm
                                    //默认查询中文
                                    this.languageID = 2;
                                    this.$set(this.reqDffFormObj, 'id', newDffFormId);
                                    this.$set(this.reqDffFormObj, 'name', obj.dffName);
                                    this.isDffFormLoadFlag = true;
                                });
                                // 不存在grid不显示
                                this.$set(this.reqDffGridObj, 'name', obj.dffName);
                                // this.reqDffGridObj.name = obj.dffName;
                                if (!validatenull(obj.dffGridId)) {
                                    //调用接口 查询最新dffGridID
                                    let dffGridIds = [];
                                    dffGridIds.push(obj.dffGridId);
                                    getDffFormComfirmIdBatchByIds(dffGridIds).then(res => {
                                        var newDffGridId = obj.dffGridId;
                                        const data = res.data.data;
                                        if (data != null && data.length > 0 && data != undefined) {
                                            newDffGridId = data[0].comfirmDffFormId;
                                        }
                                        this.$set(this.trf, 'dffGridId', newDffGridId);
                                        this.$set(this.reqDffGridObj, 'id', newDffGridId);
                                        this.getLanguageGridObjList(1);
                                        this.isDffGridLoadFlag = true;
                                        if (!validatenull(obj.specificCode)) {
                                            if (obj.specificCode.indexOf("care_Label") != -1) {
                                                this.buyerCustomerGroupCode = this.trf.trfCustomer.buyerCustomerGroupCode;
                                                this.buyerOrAgentName = this.trf.trfCustomer.buyerCustomerGroupName;
                                                this.isCareLabelLoadFlag = true;
                                            }
                                        }
                                    });
                                }
                            }
                        });
                        this.$set(this.trf, 'templateName', obj.templateName);
                        this.$set(this.trf, 'productLineId', obj.productLineId);
                        this.$set(this.trf, 'productLineCode', obj.productLineCode);
                        // this.$set(this.templateLabDataParam, 'productLineCode', obj.productLineCode);
                        this.$set(this.trf, 'productLineName', obj.productLineName);
                        //查询服务类型sel
                        this.searchServiceType(obj.productLineId);
                        // 接口获取实验室联系人
                        this.selectAflHostListForCurrentUser()
                    }
                    //查询该模板下的实验室及联系人
                    // this.$set(this.templateLabDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                    this.$set(this.testPackageParam, 'templateId', this.trf.trfTemplateId);
                    //查询模板关联的测试包
                    this.searchTemplateTestPackage();
                    // this.searchTemplateLabData();
                    //查询模板实验室联系人
                    // this.$set(this.templateLabContactDataParam, 'trfTemplateId', this.trf.trfTemplateId);
                    //模板初始化查询实验室时去掉labCode条件
                    // this.$set(this.templateLabContactDataParam, 'labCode', '');
                    // this.searchTemplateLabContactData();
                }
                // });
            },
            uploadSuccess(data) {
              if(data){
                data.data.forEach(item =>{
                  const attachment = {
                    'attachmentId': item.cloudID,
                    'fileUrl': item.path,
                    'fileName': item.attachmentName+"."+item.suffixes,
                    'size': item.size
                  }
                  this.trf.trfAttachments.push(attachment);
                });
              }
              this.$refs.batchUpload.close();
            },
            uploadChange(file, fileList) {
                if (file.status == 'ready') {
                    //开启loading效果
                    this.uploadLoading = true;
                } else {
                    this.uploadLoading = false;
                }
            },
            downloadAttachmentRow(row) {
                getCloudFileURL(row.attachmentId).then(res => {
                    window.open(res.data, "_blank");
                });
            },
            removeAttachmentRow(index) {
                this.trf.trfAttachments.splice(index, 1);
            },
            selectParentPackage() {
                this.dialogSelTestPackageFormVisible = true;
            },
            selectBuyerCustomerGroupChange(val) {
                //清空关联数据
                this.trf.trfTemplateId = '';
                this.trf.templateName = '';
                this.templateData = [];
                this.trf.trfLab = {};
                this.templateLabData = [];
                this.$set(this.trf.trfLabContact, 'labContactId', '');
                this.templateLabContactData = [];
                this.selTestPackageTreedata = [];
                //去除dff模板
                this.isDffFormLoadFlag = false;
                // this.isDffGridLoadFlag = false;
                this.isCareLabelLoadFlag = false;
                //清空测试包
                this.testPackageTreedata = [];
                let obj = {};
                obj = this.buyerCustomerGroupData.find((item) => {
                    return item.customerGroupCode === val;
                });
                if (obj != undefined && obj != null) {
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupId', obj.customerGroupId);
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupName', obj.customerGroupName);
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupCode', obj.customerGroupCode);
                }
                if (val == 'General') {
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupId', '');
                    this.$set(this.trf.trfCustomer, 'buyerCustomerGroupName', 'General');
                }
                this.$set(this.trf.trfCustomer, 'buyerCustomerGroupCode', val);
                this.$set(this.buyerPkAgentParam, 'buyerGroupCode', val);
                this.$set(this.templateDataParam, 'customerGroupCode', val);
                // this.$set(this.templateDataParam, 'productLineCode', this.productLineCode);
                if (!validatenull(this.trf.trfCustomer.agentCustomerGroupCode) && !validatenull(val)) {
                    this.$set(this.buyerPkAgentParam, 'agentGroupCode', this.trf.trfCustomer.agentCustomerGroupCode);
                    this.buyerPkAgent();
                } else {
                    if (validatenull(val)) {//无选择buyer
                        //判断是否选择agent
                        if (!validatenull(this.trf.trfCustomer.agentCustomerGroupCode)) {
                            this.$set(this.templateDataParam, 'customerGroupCode', this.trf.trfCustomer.agentCustomerGroupCode);
                        } else {
                            this.$set(this.templateDataParam, 'customerGroupCode', "General");
                        }
                    }
                }
                //查询模板
                this.$set(this.templateDataParam, 'trfStatus', 1);
                this.searchTemplateData();
            },

            buyerPkAgent() {
                var params = {};
                buyerPkAgent(Object.assign(params, this.buyerPkAgentParam)).then(res => {
                    const data = res.data.data;
                    //查询模板数据
                    this.$set(this.templateDataParam, 'customerGroupCode', data);
                    // this.templateDataParam.customerGroupCode = data;
                    this.$set(this.templateDataParam, 'trfStatus', '1');
                    this.searchTemplateData();
                });
            },
            // 查询实验室联系人
            selectAflHostListForCurrentUser() {
                console.log("selectAflHostListForCurrentUser")
                let labContactId = ''
                if (this.$route.query.applicationUser) {
                    labContactId = this.$route.query.applicationUser
                } else if (this.optionType == 'detail') {
                    labContactId = this.trf.applicantUser
                } else {
                    labContactId = ''
                    // labContactId = this.trf.trfLabContact.labContactId
                }
                let param = {
                    accountId: labContactId
                }
                selectAflHostListForCurrentUser(param).then(res => {
                    const data = res.data.data;
                    this.alflHostListForCurrentUserList = data.hostList || []
                    let id
                    if (this.optionType == 'add') {
                        id = data.hostMainId
                    } else {
                        id = this.trf.trfLabContact.labContactId
                    }
                    this.$set(this.trf.trfLabContact, 'labContactId', id);
                    let obj = this.alflHostListForCurrentUserList.find(ele => {
                        return id == ele.id
                    })
                    if (obj && obj.id) {
                        let location = obj.location
                        this.laboratoryParams.locationCode = location
                        this.getLablist()
                        this.$set(this.trf.trfLab, 'locationCode', location);
                        this.$set(this.trf.trfLabContact, 'contactName', obj.trueName);
                        this.$set(this.trf.trfLabContact, 'contactEmail', obj.email);
                        this.$set(this.trf.trfLabContact, 'contactTel', obj.mobile);
                    }
                });
            },
            // 获取实验室数据
            getLablist() {
                getLablist(this.laboratoryParams).then(res => {
                    let data = res.data.data
                    if (data.length > 0) {
                        // this.trf.trfLab.labCode = data[0].labCode
                        // this.trf.trfLab.labAddress = data[0].labAddress
                        this.templateLabData = data
                    }
                })
            },
            selectAflUserChange(e) {
                this.$set(this.trf.trfLab, 'locationCode', "");
                console.log(e)
                let idx = this.alflHostListForCurrentUserList.findIndex(ele => {
                    return ele.id == e
                })
                console.log(idx, this.alflHostListForCurrentUserList)
                if (idx > -1) {
                    let location = this.alflHostListForCurrentUserList[idx].location
                    this.$set(this.trf.trfLab, 'locationCode', location);
                    this.laboratoryParams.locationCode = location
                    this.getLablist()
                }

                this.$set(this.trf.trfLabContact, 'labContactId', e);
                this.$set(this.trf.trfLabContact, 'contactName', this.alflHostListForCurrentUserList[idx].trueName);
                this.$set(this.trf.trfLabContact, 'contactEmail', this.alflHostListForCurrentUserList[idx].email);
                this.$set(this.trf.trfLabContact, 'contactTel', this.alflHostListForCurrentUserList[idx].mobile);
            },
            // searchCustomerContactData() {
            //     var params = {};
            //     getCustomerContactList(Object.assign(params, this.customerContactParam)).then(res => {
            //         const data = res.data.data;
            //         this.customerContactData = data;
            //         let obj = {};
            //         obj = this.customerContactData.find((item) => {
            //             return item.isDefault === 1;
            //         });
            //         if (obj != null && obj != undefined) {
            //             this.$set(this.trf.trfCustomerContact, 'applyContactName', obj.contactName)
            //             this.contactrNameChange(obj.contactName);
            //         }
            //     });
            // },
            searchCustomerAddressData() {
                var params = {};
                getCustomerAddressList(Object.assign(params, this.customerContactParam)).then(res => {
                    debugger;
                    const data = res.data.data;
                    this.customerAddressData = data;
                    let obj = {};
                    obj = this.customerAddressData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if (obj != null && obj != undefined) {
                        // this.$set(this.trf.trfCustomer,'customerAddressId',obj.id);
                        this.$set(this.trf.trfCustomer, 'customerAddressZh', obj.addressDetail);
                        if (this.trf.servicRequire.reportAddress == '' || this.trf.servicRequire.reportAddress == null) {
                            this.$set(this.trf.servicRequire, 'reportAddress', obj.addressDetail);
                        }
                    }
                });
            },
            changeFixed(clientHeight) { //动态修改样式
                this.$refs.homePage.$el.style.height = clientHeight - 20 + 'px';
            },
            //Buyer买方客户组 后期修改为查询关联登录用户相关的客户组
            queryBuyerCustomerGroupData: function (bossNo) {
                var params = {}
                this.buyerCustomerGroupParam.customerNumber = bossNo;
                if (validatenull(bossNo)) {
                    this.buyerCustomerGroupParam.customerGroupCode = 'General';
                }
                getBuyerInfoPage(this.page.currentPage, this.page.pageSize, Object.assign(params, this.buyerCustomerGroupParam)).then(res => {
                    var customerGroupData = res.data.rows;
                    if (customerGroupData != null && customerGroupData != undefined) {
                        customerGroupData.forEach((item, index, array) => {
                            if (item.customerGroupCode != this.userInfo.customerGroupCode) {
                                this.buyerCustomerGroupData.push(item);
                            }
                        })
                        //添加公司的group信息
                        var groupObj = {};
                        groupObj.customerGroupId = this.userInfo.customerGroupId;
                        groupObj.customerGroupCode = this.userInfo.customerGroupCode;
                        groupObj.customerGroupName = this.userInfo.customerGroupName;
                        if (groupObj.customerGroupCode != '' && groupObj.customerGroupName != '') {
                            this.buyerCustomerGroupData.push(groupObj);
                        }
                        var generalObj = {};
                        generalObj.customerGroupId = "";
                        generalObj.customerGroupCode = "General";
                        generalObj.customerGroupName = "General";
                        this.buyerCustomerGroupData.push(generalObj);
                        this.page.total = res.data.records;
                    }
                });
            },
            //代理商客户组查询 输入3个字符后再执行查询匹配 最大查询五条数据，防止全部客户组暴露
            searchAgentCustomerGroup(val) {
                //this.customerGroupParam.groupName = val;
                this.$set(this.agentCustomerParam, 'customerName', val);
                this.$set(this.agentCustomerParam, 'rows', 5);
                if (val.length >= 3) {//输入满足三个字符后查询客户组数据
                    this.queryAgentCustomerGroupData();
                }
            },
            queryAgentCustomerGroupData() {
                var params = {};
                getAgentCustomerGroupByParms(Object.assign(params, this.agentCustomerParam)).then(res => {
                    const data = res.data.data;
                    this.agentCustomerGroupData = data;
                });
            },
            getTreeIds: function (arr, ids = []) {
                arr.forEach(({id, children}) => {
                    if (id) {
                        ids.push(id);
                    }
                    if (children) {
                        this.getTreeIds(children, ids);
                    }
                });
                return ids;
            },
            // showMore(node, testPackageTreedata) {
            //     node.expanded = true;
            // },
            // addRemark(node, testPackageTreedata) {
            //     //打开录入remark弹框
            //     this.testPackageVisible = true;
            //     this.remark = '';
            //     if (node.data.remark != 'Remark' && node.data.remark != 'remark') {
            //         this.remark = node.data.remark;
            //     }
            //     this.node = node;
            //     /* this.$prompt(this.$t('trf.remark'), this.$t('tip'), {
            //          confirmButtonText: this.$t('submitText'),
            //          cancelButtonText: this.$t('cancelText'),
            //          value:node.data.remark
            //      }).then(({value}) => {
            //          //将value值放入对应树形结构中
            //          node.data.remark=value;
            //      }).catch(() => {

            //      });*/
            // },
            // queryCustomerProductLineCode() {
            // queryCustomerForId(this.userInfo.companyId).then(res => {
            //     const data = res.data.data;
            //     this.trf.productLineCode1 = data.productLineCode;
            // });
            // },
            // editTemplate() {
            //     if (validatenull(this.trf.trfTemplateId)) {
            //         this.templateDialogVisible = true;
            //     } else {
            //         //给出提示
            //         this.$confirm(this.$t('trf.changeTemplateMsg'), this.$t('tip'), {
            //             confirmButtonText: this.$t('submitText'),
            //             cancelButtonText: this.$t('cancelText'),
            //             type: 'warning'
            //         }).then(() => {
            //             this.templateDialogVisible = true;
            //         }).catch(() => {
            //             /*this.$message({
            //                 type: 'info',
            //                 message: '已取消删除'
            //             });*/
            //         });
            //     }

            // },
            // selectProductCategoryChange(val) {
            //     //按照所属BU查询template
            //     this.trf.trfTemplateId = '';
            //     this.trf.templateName = '';
            //     this.templateData = [];
            //     this.trf.trfLab = {};
            //     this.templateLabData = [];
            //     this.$set(this.trf.trfLabContact, 'labContactId', '');
            //     this.templateLabContactData = [];
            //     this.selTestPackageTreedata = [];
            //     //去除dff模板
            //     this.isDffFormLoadFlag = false;
            //     // this.isDffGridLoadFlag = false;
            //     this.isCareLabelLoadFlag = false;
            //     //清空测试包
            //     this.testPackageTreedata = [];
            //     //查询模板
            //     this.$set(this.templateDataParam, 'productLineCode', val);
            //     this.searchTemplateData();
            // },
            // async queryProductCategory() {
            //     let res = await serviceTypeList(this.language);
            //     const data = res.data.data;
            //     this.productCategoryData = [];
            //     if (!validatenull(data)) {
            //         data.find((item) => {
            //             if (item.productLineCode != 'other') {
            //                 this.productCategoryData.push(item);
            //             }
            //         });
            //     }
            //     /*serviceTypeList(this.language).then(res => {
            //           const data = res.data.data;
            //           this.productCategoryData = data;
            //       });*/
            // },
            // searchEmailGroup() {
            //     var params = {};
            //     getMailGroupAndContactEmail(Object.assign(params, this.emailGroupParam)).then(res => {
            //         this.emailGroupData = res.data.data;
            //     });
            // },
            // reportDeliveredToChange(emailList) {
            //     let obj = {};
            //     let pasteEmailArr = [];
            //     emailList.find((item1) => {
            //         obj = this.emailGroupData.find((item) => {
            //             if (item1 == item.emailGroupName) {//判断当前邮件是否和
            //                 return item;
            //             }
            //         });
            //         if (validatenull(obj)) {//手动输入邮箱验证
            //             let validateRes = validateEmail(item1);
            //             if (validateRes) {
            //                 if (pasteEmailArr.indexOf(item1) == -1) {
            //                     pasteEmailArr.push(item1);
            //                 }
            //             }
            //         } else {
            //             if (obj.type != 2) {//邮件组数据
            //                 var contactEmail = obj.contacts;
            //                 contactEmail.find((item2) => {
            //                     if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
            //                         pasteEmailArr.push(item2.contactEmail);
            //                     }
            //                 });
            //             } else {
            //                 if (pasteEmailArr.indexOf(item1) == -1) {
            //                     pasteEmailArr.push(item1);
            //                 }
            //             }
            //         }
            //     });
            //     this.emailAddressesValue = pasteEmailArr;
            //     if (this.emailAddressesValue.length != 0) {
            //         this.trf.reportDeliveredTo = this.emailAddressesValue.join(',')
            //     } else {
            //         this.trf.reportDeliveredTo = '';
            //     }
            // },
            // failReportDeliveredToChange(emailList) {
            //     let obj = {};
            //     let pasteEmailArr = []
            //     emailList.find((item1) => {
            //         obj = this.emailGroupData.find((item) => {
            //             if (item1 == item.emailGroupName) {//判断当前邮件是否和
            //                 return item;
            //             }
            //         });
            //         if (validatenull(obj)) {//手动输入邮箱验证
            //             let validateRes = validateEmail(item1);
            //             if (validateRes) {
            //                 if (pasteEmailArr.indexOf(item1) == -1) {
            //                     pasteEmailArr.push(item1);
            //                 }
            //             }
            //         } else {
            //             if (obj.type != 2) {//邮件组数据
            //                 var contactEmail = obj.contacts;
            //                 contactEmail.find((item2) => {
            //                     if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
            //                         pasteEmailArr.push(item2.contactEmail);
            //                     }
            //                 });
            //             } else {
            //                 if (pasteEmailArr.indexOf(item1) == -1) {
            //                     pasteEmailArr.push(item1);
            //                 }
            //             }
            //         }
            //     });
            //     this.failemailAddressesValue = pasteEmailArr;
            //     if (this.failemailAddressesValue.length != 0) {
            //         this.trf.failedReportDeliveredTo = this.failemailAddressesValue.join(',')
            //     } else {
            //         this.trf.failedReportDeliveredTo = '';
            //     }
            // },
            // addProductSample() {
            //     var productItemNos = new Array();
            //     this.objList.forEach((item, index, array) => {
            //         productItemNos.push(item.productItemNo);
            //     })
            //     this.productItemNosList = productItemNos;
            // },
            // addProductSampleByQty() {
            // },
            // changeDffGridTemplate(dffGridId) {
            //     let currentObj = this.dataList[this.languageCode];
            //     if (dffGridId && currentObj[0].dFFFormID != dffGridId) {
            //         this.objList = [{productItemNo: 'SupplierCode_1', dFFFormID: dffGridId}]
            //     }

            // },
            // deleteProductSample(instanceItem, instanceIndex) {
            /*var productItemNos = new Array();
            this.objList.splice(instanceIndex,1);
            this.objList.forEach((item, index, array) => {
                productItemNos.push(item.productItemNo);
            })
            this.productItemNosList = productItemNos;*/
            //判断洗唛是否有用到删除的Grid数据
            /*var removeIndex = [];
            this.careInstructions.forEach((item, index, array) => {
                var productItemNoIndex = item.productItemNo.indexOf(instanceItem.productItemNo);
                if (item.productItemNo.length == 1 && productItemNoIndex > -1) {
                    item.productItemNo.splice(productItemNoIndex, 1);
                    removeIndex.push(index);
                } else if (productItemNoIndex != -1) {
                    item.productItemNo.splice(productItemNoIndex, 1);
                }

            });
            removeIndex.forEach((item, index, array) => {
                if (this.careInstructions.length > 1) {
                    this.careInstructions.splice(item, 1);
                }
            })*/
            // },
            // copySample(instanceItem, instanceIndex) {
            //     var productItemNos = new Array();
            //     this.objList.forEach((item, index, array) => {
            //         productItemNos.push(item.productItemNo);
            //     })
            //     this.productItemNosList = productItemNos;
            // },
            //**********dff Grid method End***************
            //********care Label Start****
            saveCheck() {
            },
            //********care Label End****
            // treeCheckChange(data, checked, indeterminate) {
            // },
            // filterNode(value, data) {
            //     if (!value) return true;
            //     return data.testPackageName.toLowerCase().indexOf(value) !== -1;
            // },
            // returnSampleChange(value) {
            //     //returnSampleData;
            //     var returnSampleName = '';
            //     var returnSample = this.returnSampleArry.join(',');
            //     var returnSampleArray1 = returnSample.split(",");
            //     if (returnSampleArray1 != '' && returnSampleArray1 != undefined) {
            //         returnSampleArray1.find((item) => {
            //             this.returnSampleData.find((item1) => {
            //                 if (item == item1.sysKey) {
            //                     returnSampleName += item1.sysValue + ',';
            //                 }
            //             });
            //         });
            //     }
            //     if (returnSampleName.length > 0) {
            //         returnSampleName = returnSampleName.substr(0, returnSampleName.length - 1);
            //     }
            //     this.trf.servicRequire.returnSampleName = returnSampleName;
            //     this.trf.servicRequire.returnSampleRequire = returnSample; // 记录所有被选中项的下标
            // },
        },
    };
</script>
<style>
    .activeTrf::placeholder {
        color: #ffffff !important;
    }

    .disabledTrf::placeholder {
        color: transparent !important;
    }
</style>
<style lang="scss">
    .tree {
        .el-tree-node {
            white-space: normal;

            .el-tree-node__content {
                height: 100%;
                align-items: start;
            }
        }
    }

    .el-steps--simple {
        height: 50px;
        background: #424242;

        .el-step {
            cursor: pointer;

            i {
                color: #FFFFFF;
            }

            .el-step__title {
                color: #FFFFFF;
                font-size: 12px;
            }
        }
    }

    .modal {
        z-index: 2010;
    }

    .contact {
        table {
            th {
                padding: 5px;
                text-align: center;
            }

            td {
                padding: 5px 20px;
            }
        }
    }

    #productSampleHeaderForm {
        label {
            font-weight: normal !important;
        }

        .el-form-item {
            margin-bottom: 8px;
        }

        .el-form-item__label {
            width: 260px !important;
        }

        #productSampleHeadNew .el-form-item__error {
            top: 96% !important;
        }

        .el-form-item__content {
            margin-left: 0px !important;
        }

        .form-horizontal {
            padding-left: 10px;
            padding-right: 10px;
        }

        .textareaHead textarea {
            height: 36px;
            margin-top: 0px !important;
        }

        .row {
            margin-right: -24px;
            margin-left: -24px;
        }
    }

    .grid_validation {
        color: #F56C6C !important;
        text-align: left !important;
        font-size: 12px !important;
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
    }

    .form_item_header {
        font-size: 18px;
        color: #525252;
        display: flex;
        align-items: flex-start;
        font-weight: bold;
        line-height: 36px;
        margin: 0;
    }

    .form_item_box {
        min-height: 56px;
        line-height: 32px;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;
        position: relative;
        display: flex;
        align-items: center;
    }

    .ipt {
        width: 180px;
        height: 30px;
        border: 1px solid #dcdfe5;
        margin-left: 10px;
        font-size: 14px;
        color: #333;
    }

    .el-radio-group {
        padding: 0;
        font-size: 0;
        vertical-align: unset;
    }

    .checkbox_box {
        width: 200px;
    }

    .checkbox_box1 {
        width: 220px;
    }
    .cma-cnas .checkbox_box1 {
        width: 160px;
    }

    .mandatoryFlag {
        color: #F56C6C;
        margin-right: 4px;
    }

    .orange_text {
        color: #FF6600;
        font-weight: normal;
    }

    .dff_grid_textarea textarea {
        height: 40px;
    }

    .dffGird_wrap {
        padding: 10px 0;

        .addGrid_btn {
            text-align: right;
            padding-bottom: 10px;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .el-button {
                margin: 0 10px;
            }

            .num {
                color: red;
            }

            .dffDridIpt {
                width: 100px;
                .el-input__inner {
                    text-align: center;
                }
            }
        }

        .dffGrid_list {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #eee;
            padding: 10px 0;
            position: relative;

            .dffGirg_item {
                word-break: break-all;
                flex: 1;
                min-width: 0;
                padding: 0 10px;
                box-sizing: border-box;

                .ipt2 {
                    width: 100%;
                    height: 40px;
                    border: 1px solid #dcdfe5;
                    font-size: 14px;
                }
            }

            .dffGirg_del {
                width: 100px;
                text-align: center;

                .glyphicon {
                    padding: 10px;
                    border: 1px solid #ccc;
                }

                .background {
                    background: #fafafa;
                }

                .glyphicon:first-child {
                    border-radius: 4px 0 0 4px;
                }

                .glyphicon:last-child {
                    border-radius: 0 4px 4px 0;
                    margin-left: -1px;
                }
            }
        }
    }

    .el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 100%;
    }

    .printTrfTip_info span {
        font-size: 14px;
        line-height: 26px;
        color: #666;
    }

    .printTrfTip_info p {
        font-size: 14px;
        line-height: 26px;
        color: #333;
        font-weight: bold;
    }

    .printTrfTip_info .orange_text {
        color: #FF6600;
    }

    .chat_btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        text-align: center;
        line-height: 50px;
        font-size: 14px;
        border: 1px solid #eee;
        position: fixed;
        right: 100px;
        bottom: 100px;
        background: #ff6600;
        color: #fff;
        z-index: 999;
        cursor: pointer;
    }

    .chat_btn.gray {
        background: #d2d2d2;
        color: #666;
    }
    .nav-list {
        list-style: none;
        margin-top: 24px;
        padding-left: 0;
        li {
            cursor: pointer;
            border-left: 3px solid #D9D9D9;
            padding-left: 24px;
            font-size: 16px;
            font-weight: 400;
            color: #000000;
            &.is-sub {
                padding-left: 40px;
                font-size: 16px;
                font-weight: 400;
                color: #656565;
                h5 {
                    font-weight: normal;
                }
            }
            &.active {
                color: #f60;
                border-color: #f60;
            }
            h5 {
                padding: 8px 0;
                margin: 0;
            }
        }
    }
    .add-comment-title {
        margin: 26px 0 30px;
        font-size: 16px;
    }
    .scroll-box {
        /* height: 60vh; */
        .inner-scroll {
            padding-left: 3px;
            height: 60vh;
            overflow: auto;
            &::-webkit-scrollbar { width: 0 !important }
        }
    }
    .add-comment {
        padding-left: 36px;
        position: relative;
        &::after {
            content: ' ';
            width: 20px;
            height: 20px;
            border-radius: 50%;
            top: 1px;
            background: #f60;
            position: absolute;
            left: 3px;
        }
        /* &::before {
            content: ' ';
            width: 3px;
            height: 50px;
            bottom: -30px;
            background: #ccc;
            position: absolute;
            left: 11px;
        } */
    }
    .sgs_smart_trf_trf_detail_afl{
        .el-card__body {
            padding: 26px 32px 32px !important;
            .sgs-group {
                h3 {
                    margin-bottom: 12px;
                }
            }
            .el-form-item__label {
                padding-left: 12px;
                font-size: 12px;
                color: #999;
            }
        }
    }
    .comment-list {
        width: calc(100% - 9px);
        border-left: 3px solid #ccc;
        padding-left: 20px;
        padding-top: 30px;
        dt {
        }
        dd {
            position: relative;
            margin-bottom: 20px;
            box-shadow: 0 15px 30px rgba(204, 204, 204, .2);
            .comment {
                margin-bottom: 20px;
                font-weight: 600;
            }
            &::after {
                content: ' ';
                position: absolute;
                left:  -28px;
                top: 19px;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 2px solid #f60;
                background: #fff;
            }
            &:last-of-type {
                margin-bottom: 0;
            }
            .c-info {
                p {
                    word-break: break-all;
                }
            }
        }
    }
</style>
