<template>


    <div >
      <el-form :inline="true" :model="authorityQuery" @submit.native.prevent size="medium" class="text-right">
        <el-form-item>
          <el-input
              @keyup.enter.native="onSearch"
              @clear="onSearch"
              v-model="authorityQuery.corpName"
              :placeholder="$t('dateValidate.companyNameValidate')"
              clearable>
            <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
        </el-form-item>
      </el-form>
        <el-table class="scrollbar" stripe   :data="myAuthorityList" v-loading="loading"
                  :element-loading-text="$t('loading')" style="width: 100%" height="500">
          <el-table-column prop="applyUserName"  :show-overflow-tooltip="true" :label="$t('account.userName')"  width="auto"  min-width="80">
          </el-table-column>
          <el-table-column prop="applyAccount"   :show-overflow-tooltip="true" :label="$t('account.title.default')"    min-width="100">
          </el-table-column>
          <el-table-column prop="customerNameZh"  :show-overflow-tooltip="true"  :label="$t('customer.companyNameCn')"  width="auto"  min-width="120">
          </el-table-column>
          <el-table-column prop="customerNameEn" :show-overflow-tooltip="true" :label="$t('customer.companyNameEn')"   min-width="120">
          </el-table-column>
          <el-table-column prop="authStatus" :label="$t('common.status.title')"   min-width="60">
            <template slot-scope="scope">
              <span v-if="scope.row.authStatus==4">{{$t('authority.status.toBeAudited')}}</span>
              <span v-if="scope.row.authStatus==1">{{$t('authority.status.audited')}}</span>
              <span v-if="scope.row.authStatus==2">{{$t('authority.status.rejected')}}</span>
              <span v-if="scope.row.authStatus==3">{{$t('authority.status.cancelled')}}</span>
            </template>
          </el-table-column>

          <el-table-column prop="updateTime" :label="$t('user.updateTime')"   min-width="80">
          </el-table-column>
          <el-table-column :label="$t('operation.title')" width="240" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button @click="agreeAuthority(scope.row)" v-if=" permissionList.approveBtn && scope.row.authStatus===4" type="text">{{
                  $t("authority.agree")
                }}
              </el-button>

              <el-button @click="rejectAuthority(scope.row)" v-if="permissionList.rejectBtn && scope.row.authStatus===4" type="text">{{
                  $t("common.reject")
              }}</el-button>
              <el-button @click="cancelAuthority(scope.row)" v-if="permissionList.cancelBtn && scope.row.authStatus===1" type="text">{{
                  $t("authority.stopAuthority")
                }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="sizeChange" @current-change="currentChange" :current-page="page.currentPage"
          :page-sizes="[10, 20, 50, 100]" :page-size="page.pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="page.total">
        </el-pagination>
   </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  querMyAuthorityList,changeAuthStatus
} from "@/api/customer/authority";

export default {
  data() {
    return {
      loading:false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      sort: { descs: "update_time" },
      myAuthorityList: [],
      authorityQuery: {
        corpName:'',
        bossNo:'',
        pageNo: "",
        pageSize:''
      },
    }
  },
  created() {
    this.initMyAuthorityData()
  },
  computed: {
    ...mapGetters(["permission", "userInfo","language"]),
    permissionList() {
      return {
        approveBtn: this.vaildData(this.permission['sgs:authority:approve'], false),
        rejectBtn: this.vaildData(this.permission['sgs:authority:reject'], false),
        cancelBtn: this.vaildData(this.permission['sgs:authority:cancel'], false),
      };
    }
  },
  methods: {
    onSearch() {
      this.page.currentPage=1;
      this.initMyAuthorityData();
    },
    lengthFilter: function (value,len) {
      value = value.toString();
      if (value.length > len) {
        value = value.slice(0, len) + '...'
      }
      return value
    },
    async handelSubmit() {
      this.page.currentPage = 1;
      await this.initMyAuthorityData();
    },
    initMyAuthorityData() {
      this.authorityQuery.pageNo = this.page.currentPage;
      this.authorityQuery.pageSize = this.page.pageSize;
      this.loading = true;
      this.$set(this.authorityQuery, "flag", 1);//我的授权
      querMyAuthorityList(this.authorityQuery).then((res) => {
        this.loading = false;
        this.myAuthorityList = res.data.data.records;
        this.page.total = res.data.data.total;
      }).catch(() => {
        this.loading = false;
      });
    },

    //分页查询
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.initMyAuthorityData();
    },
    currentChange(pageCurrent) {
      this.page.currentPage = pageCurrent;
      this.initMyAuthorityData();
    },

    search() {
      this.page.currentPage = 1;
      this.initMyAuthorityData();
    },
    //取消授权
    cancelAuthority(row) {
      this.$confirm(this.$t("authority.stopAuthorityConfirm"), this.$t("tip"), {
        confirmButtonText: this.$t("operation.confirm"),
        cancelButtonText: this.$t("operation.cancel"),
        type: "warning",
      }).then(() => {
        //取消授权
        let authObj = {};
        authObj.id = row.id;
        authObj.authStatus = 3;
        authObj.language=this.language;
        changeAuthStatus(authObj).then((res) => {
          this.$message({
            type: "success",
            message: this.$t('api.success')
          });
          this.onSearch();
        });
      })
          .catch(() => {
            /* this.$message({
              type: 'info',
              message: '已取消删除'
            });*/
          });
    },
    //拒绝
    rejectAuthority(row) {
      this.$confirm(this.$t("authority.rejectAuthority"), this.$t("tip"), {
        confirmButtonText: this.$t("operation.confirm"),
        cancelButtonText: this.$t("operation.cancel"),
        type: "warning",
      }).then(() => {
        //拒绝
        let authObj = {};
        authObj.id = row.id;
        authObj.authStatus = 2;
        authObj.language=this.language;
        changeAuthStatus(authObj).then((res) => {
          this.$message({
            type: "success",
            message: this.$t('api.success')
          });
          this.onSearch();
        });
      })
          .catch(() => {
            /* this.$message({
              type: 'info',
              message: '已取消删除'
            });*/
          });
    },
    //同意
    agreeAuthority(row) {
      this.$confirm(this.$t("authority.approveAuthorityConfirm"), this.$t("tip"), {
        confirmButtonText: this.$t("operation.confirm"),
        cancelButtonText: this.$t("operation.cancel"),
        type: "warning",
      }).then(() => {
        let authObj = {};
        authObj.id = row.id;
        authObj.authStatus = 1;
        authObj.language=this.language;
        changeAuthStatus(authObj).then((res) => {
          this.$message({
            type: "success",
            message: this.$t('api.success')
          });
          this.onSearch();
        });
      }).catch(() => {
        /* this.$message({
          type: 'info',
          message: '已取消删除'
        });*/
      });


    },
  }
}
</script>
<style lang="scss">

</style>

