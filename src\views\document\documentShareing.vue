<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.documentSharing')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{ $t('navbar.documentSharing') }}</h1>
        <el-card class="sgs-box" v-for="item in programList">
            <div>
                <div class="sgs-group">
                    <h3>
                        {{ item.programName }}
                    </h3>
                </div>
                <el-row :gutter="20">
                    <el-col :span="8" v-for="itemMap in item.programValue" v-if="item.programLayout == 10">
                        <el-card style="height: 260px;overflow: hidden;" shadow="hover"
                                 :body-style="{ padding: '0px' }">
                            <el-row>
                                <el-col :span="14">
                                    <div style="padding: 14px;height: 260px; position: relative;">
                                        <h4>
                                            <a class="text-fit" target="_blank"
                                               v-on:click="documentClick(itemMap)">{{ itemMap.documentTitle }}</a>
                                        </h4>
                                        <p class="desc text-fit">{{ itemMap.documentDec }}</p>
                                        <div v-if="itemMap.attachmentList.length >0" class="bottom clearfix">
                                            <el-popover placement="left" width="350" trigger="click">
                                                <el-row :gutter="10">
                                                    <el-col :span="8" v-for="itemFile in itemMap.attachmentList">
                                                        <a style="cursor:pointer" class='download'
                                                           @click="openPDFDialog(itemMap,itemFile)" title="下载">
                                                            <el-card shadow="hover"
                                                                     style="height: 100px;width: 100px;overflow: hidden;">
                                                                {{ itemFile.fileName }}
                                                            </el-card>
                                                        </a>
                                                    </el-col>
                                                </el-row>
                                                <el-button style="position: absolute;bottom: 5px; right: 5px;"
                                                           type="text" slot="reference" icon="el-icon-paperclip">
                                                    {{ itemMap.attachmentList.length }}
                                                    {{ $t('documentSharing.attachments') }}
                                                </el-button>
                                            </el-popover>
                                        </div>
                                        <div v-else>
                                            <el-button type="text" icon="el-icon-paperclip-transparent"></el-button>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="10">
                                    <a target="_blank" v-if="itemMap.imageCloudId" v-on:click="documentClick(itemMap)">
                                        <img :src="itemMap.imageCloudId" height="100%" style="max-height: 130px;margin-top: 40%;">
                                    </a>
                                    <a v-else>
                                        <div style="height: 130px;"></div>
                                    </a>
                                </el-col>
                            </el-row>
                        </el-card>
                    </el-col>

                    <el-col :span="8" v-for="itemMap in item.programValue" v-if="item.programLayout == 20">
                        <el-card style="overflow: hidden;" shadow="hover" :body-style="{ padding: '0px' }">
                            <a target="_blank" v-if="itemMap.imageCloudId" v-on:click="documentClick(itemMap)">
                                <img :src="itemMap.imageCloudId" class="document-cover">
                            </a>
                            <a v-else>
                                <div class="document-cover-default"></div>
                            </a>
                            <div style="padding: 14px;">
                                <h4>
                                    <a class="text-fit" target="_blank" v-on:click="documentClick(itemMap)">
                                        {{ itemMap.documentTitle }}
                                    </a>
                                </h4>
                                <p class="desc text-fit">{{ itemMap.documentDec }}</p>
                                <div v-if="itemMap.attachmentList.length > 0" class="bottom" style="text-align: right;">
                                    <el-popover placement="left" width="350" trigger="click">
                                        <el-row :gutter="10">
                                            <el-col :span="8" v-for="itemFile in itemMap.attachmentList">
                                                <a style="cursor:pointer" class='download'
                                                   @click="openPDFDialog(itemMap,itemFile)" title="下载">
                                                    <el-card shadow="hover"
                                                             style="height: 100px;width: 100px;overflow: hidden;">
                                                        {{ itemFile.fileName }}
                                                    </el-card>
                                                </a>
                                            </el-col>
                                        </el-row>
                                        <el-button type="text" slot="reference" icon="el-icon-paperclip">
                                            {{ itemMap.attachmentList.length }} {{ $t('documentSharing.attachments') }}
                                        </el-button>
                                    </el-popover>
                                </div>
                                <div v-else>
                                    <el-button type="text" icon="el-icon-paperclip-transparent"></el-button>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <div v-if="item.programLayout == 30">
                        <div class="group-main" v-if="item1.programName == item.programName" v-for="(item1, index) in programYearList" :key="index">
                            <span class="group-flex" v-for="itemMap1 in item1.programValue">
                                <span class="group-item" v-for="item2 in itemMap1">
                                    <span class="title">{{ item2.createYear }}</span>
                                    <a class='download' @click="openPDFDialog(item1,itemFile)" title="下载" v-for="itemFile in item2.attachmentList" :key="itemFile.id">
                                        <span style="color: white;">{{ itemFile.fileName }}</span>
                                    </a>
                                </span>
                            </span>
                        </div>
                    </div>
                </el-row>
            </div>
        </el-card>
        <pdf-dialog ref="pdf"></pdf-dialog>
    </basic-container>
</template>


<script>
    import {vipProgramList} from "@/api/document/supplyDocument";
    import {getCloudFileURL, addPowerBiLog} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";

    export default {
        components: {
            PdfDialog: resolve => require(['../../components/dialog/pdf/PdfDialog'], resolve),
        },
        data() {
            return {
                sort: {ascs: 'sort'},
                showData: [],
                programList: [],
                programYearList: [],
                queryForm: {}
            }
        },
        props: {
            menu: {
                type: String
            }
        },//['classId']

        created: function () {
            this.vipProgramList();
        },
      computed: {
        ...mapGetters([
          "userInfo",
          "language"
        ])
      },
      watch: {
        //监听语言变化
        language: function (newVal) {
          this.vipProgramList();
        },
      },
        methods: {
            documentClick(documentObj) {
                debugger;
                if (!documentObj.documentUrl) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('authorization.visibleOnlyTip'),
                        type: 'warning'
                    });
                    return;
                }
                let url = documentObj.documentUrl;
                let preHttps = url.substr(0,5);
                if(preHttps.indexOf("http")==-1 || preHttps.indexOf("https")=="-1"){
                    url = "https://"+url;
                }
                //追加pbiType
                let pbiType=0;
                if(documentObj.pbiType){
                    pbiType = documentObj.pbiType;   
                }
                if(url.indexOf('?') !== -1){//存在？
                    url= url+"&pbiType="+pbiType;
                }else{
                    url= url+"?pbiType="+pbiType;
                }               
                window.open(url, '_blank')
                const powerBiObj = {};
                powerBiObj.documentId = documentObj.id;
                powerBiObj.documentName = documentObj.documentTitle;
                powerBiObj.documentType = 'image';
                powerBiObj.documentUrl = documentObj.documentUrl;
                powerBiObj.imageCloudId = documentObj.imageCloudId;
                this.addPowerBiLog(powerBiObj);
            },
            addPowerBiLog(powerBiObj) {
                addPowerBiLog(powerBiObj).then(res => {
                });
            },
            async openPDFDialog(documentObj, fileObj) {
                debugger;
                const powerBiObj = {};
                powerBiObj.documentType = 'PDF';
                powerBiObj.documentId = documentObj.id;
                powerBiObj.documentName = documentObj.documentTitle;
                //powerBiObj.documentUrl = documentObj.documentUrl;
                powerBiObj.imageCloudId = fileObj.imageCloudId;
                powerBiObj.attachmentCloudId = fileObj.fileUrl;
                this.addPowerBiLog(powerBiObj);
                await this.$refs.pdf.open(fileObj.attachmentId);
            },
            vipProgramList() {
                debugger;
                console.log(this.$route.query)
                this.queryForm.documentType = this.$route.query['documentType']
                this.queryForm.showOnWhichMenu = this.$route.query['menu']
                this.queryForm.productLineCode=this.userInfo.productLineCode
                //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                  languageId=LanguageEnums.EN.code;
                }else{
                  languageId=LanguageEnums.CN.code;
                }
                this.queryForm.languageId=languageId;
                vipProgramList(this.queryForm).then(res => {
                    console.log(res);
                    this.programList = res.data.groupByProgram;
                    this.programYearList = res.data.groupByProgramAndYear;
                }, error => {
                    console.log(error);
                });
            },
            downFile(url) {
                getCloudFileURL(url).then(res => {
                    window.open(res.data, "_blank");
                });
            },
            selectAttachment(id) {

            }
        },
    }


</script>

<style scoped lang="less">
  .desc {
    height: 40px;
  }

  .text-fit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .document-cover {
    width: 100%;
    max-height: 200px;
  }

  .document-cover-default {
    height: 200px;
  }

  .group-main {
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;

    .group-flex {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .group-item {
    display: flex;
    flex-direction: column;
    margin: 0 10px 10px 0;
    width: 230px;
    background-color: #FA6601;
    color: #fff;

    .title {
      font-size: 30px;
      color: #fff;
    }

    a {
      color: #fff;
      float: right;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      justify-content: flex-end;
      margin: 0 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  @media screen and (max-width:1600px) {
    .document-cover {
      max-height: 140px;
    }

    .document-cover-default {
      height: 140px;
    }
  }
</style>
