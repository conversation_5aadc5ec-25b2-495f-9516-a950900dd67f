<template>
    <basic-container>
        <el-dialog :title="$t('trf.history.title')" :visible.sync="visible"  width="60%"
                   append-to-body @close="close">
            <el-table :data="records" v-loading="historyLoading" style="width: 100%">
                <el-table-column
                        prop="title"
                        :label="$t('trf.history.status')" style="width:20%">
                </el-table-column>
                <el-table-column
                        prop="createTime"
                        :label="$t('trf.history.operationTime')" style="width:20%">
                  <template slot-scope="scope">
                     <span>
                        {{   currentTzDate($lodash.get(scope.row, 'createTime')) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                        prop="createBy"
                        :label="$t('trf.history.createBy')" style="width:20%">
                </el-table-column>
            </el-table>
          <el-pagination
              @size-change="sizeChange"
              :current-page="query.pageNo"
              @current-change="currentChange"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="query.pageSize"
              :total="query.total"
              layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-dialog>
    </basic-container>


</template>

<script>
import { queryTrfLogs } from "@/api/trf/trf";
    import {validatenull} from "../../util/validate";
    import {mapGetters} from "vuex";
import {tzFormChina} from '@/util/datetimeUtils'


export default {
        data() {
            return {
                historyLoading: false,
                name: "trf-history-dialog",
                //选中客户数组
                customers: [],
                records: [],
                form: {},
                query: {
                  pageNo: 1,
                  pageSize: 10,
                  total: 0
                },
            }
        },
        props: {
            visible: {
                type: Boolean,
                default: false
            },
            trfId: {
                type: String,
                default: ''
            },
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
        },
        watch: {
            'trfId': {
                handler() {   //注意此处就是handler
                    //this.searchTrfHistory();
                },
                //deep: true,
                //immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            visible: function (newVal) {
                debugger;
                if (newVal) {
                    this.searchTrfHistory();
                }
            },

        },
        methods: {
          currentTzDate(val){
            if (!val) return ''
            let value = tzFormChina(val,'YYYY-MM-DD HH:mm');
            return value;
          },
            close() {
                this.visible = false;
                this.$emit('update:visible', this.visible);
            },
            searchTrfHistory() {
                if (!validatenull(this.trfId)) {
                    this.historyLoading = true;
                    let params = {
                        pageNo: this.query.pageNo,
                        pageSize: this.query.pageSize,
                        objectId: this.trfId,
                        language: this.language
                    };

                    queryTrfLogs(params).then(res => {
                        this.historyLoading = false;
                        if (res.data && res.data.data) {
                            this.records = res.data.data.records;
                            this.query.total = res.data.data.total;
                        }
                    }).catch(err => {
                        this.historyLoading = false;
                    })
                }
            },
            async sizeChange(pageSize) {
              this.query.pageSize = pageSize;
              await this.searchTrfHistory();
            },

            async currentChange(pageCurrent) {
              this.query.pageNo = pageCurrent;
              await this.searchTrfHistory();
            },
        },
    }
</script>

<style scoped>

</style>
