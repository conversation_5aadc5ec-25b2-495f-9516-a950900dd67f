<template>
    <div class="formComponent">

        <basic-container>
            <el-row>
                <h4 class="sgs-title">Basic product information 产品基本资料</h4>
            </el-row>
            <avue-form v-model="formDatas[form.code]"  v-for="(form,index) in forms" v-if="form.code=='product_basic_data'" :option="form.validateOption" :ref="'form-'+form.code" :key="'form-'+form.code"  :selfUpdate="true">
            </avue-form>
            <el-collapse v-model="activeNames" accordion @change="handleChange">
                <el-collapse-item :title="form.name" v-for="(form,index) in forms"  v-if="form.code!='product_basic_data'"  :name="form.name" >
                    <avue-form :option="form.validateOption" :ref="'form-'+form.code" :key="'form-'+form.code"  v-model="formDatas[form.code]">
                    </avue-form>
                </el-collapse-item>
            </el-collapse>
        </basic-container>
    </div>
</template>

<script>
    import {getFormDetail,getFormList} from "@/api/form/form";
    import {mapGetters} from "vuex";
    import { deepClone } from '@/util/util'

    export default {
        name: "formComponent",
        data() {
            return {
                activeNames: [],
                forms:[],
                /* checkCclForm(){
                   this.$refs['cclForm'].validate((valid) =>{
                     return valid;
                   });
                 },*/
                productForm:{},
                form: {},
                designDialogVisible: false,
                jsonData: {},
                selectionRow: {},
                selectionList: [],
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                formOptions: {},
                option: {
                },
                validateOption:{},
                data: [],
                validateCclForm:true
            };
        },
        props: {
            reportLanage:{
                type: String,
                default: () => ''
            },
            formCodes:{
                type: String,
                default: () => ''
            },
            cclCodes: {
                type: Object,
                default: () => []
            },
            formDatas:{
                type: null
            },
           },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    viewBtn: false,
                    addBtn: false,
                    delBtn: this.vaildData(this.permission['nkop:form:delete'], true),
                    editBtn: false,
                    designBtn: this.vaildData(this.permission['nkop:form:design'], true),
                    manageBtn: this.vaildData(this.permission['nkop:form:manage'], true),
                };
            },
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            }
        },
        watch: {
        },
        methods: {
            handleChange(val) {
                console.log(val);
            },
            checkCclForm: function checkCclForm() {
                this.$refs['form-product_basic_data'][0].validate((valid) => {
                    this.$emit("validateCclForm", valid);
                    return valid;
                });
            },
            initOption:function initOption(){
                this.validateOption=deepClone(this.option);
            },
            //根据报告语言校验表单
            checkCclFormByReportLanguage: function checkCclFormByReportLanguage(reportLanguage) {
                var language = ''
                if (reportLanguage == '1') {
                    language = 'Zh'
                }
                if(reportLanguage=='2'){
                    language = 'En'
                }
                if(reportLanguage=='3'){
                    language = 'EnZn'
                }
                if(language!=null && language!=''){
                    this.forms.find((form) => {
                        form.validateOption.column.find((item) => {
                            if (item.prop.indexOf(language) != -1) {//包含
                                //去除中文的校验
                                item.display = false;
                                //设置展示英文隐藏
                                /*  if (item.rules != null && item.rules != undefined) {
                                      item.rules.find((prop) => {
                                          prop.required = false;
                                      });
                                  }*/
                            }else{
                                item.display = true;
                            }
                        });
                        //
                         //处理group里面的
                         if( form.validateOption.group!=null &&  form.validateOption.group!=undefined){
                             form.validateOption.group.find((group) => {
                                group.column.find((item) => {
                                        if (item.prop.indexOf(language) != -1) {//包含
                                                 //去除中文的校验
                                             item.display = false;
                                        }else{
                                             item.display = true;
                                        }
                                });
                             });
                         }
                    });
                }
            },
            getDetail() {
                console.log(this.formCodes);
                getFormList(this.formCodes).then(res => {
                    this.forms =res.data.data;
                    this.forms.find((form) => {
                        form['option'] =  JSON.parse(form.content);
                        form['validateOption'] =  JSON.parse(form.content);
                    });
                    this.checkCclFormByReportLanguage(this.reportLanage);
                })
            },
        },
        created() {
            this.getDetail();
        }
    };
</script>

<style lang="scss">
    .formComponent{
        .el-collapse-item__header{
         /* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
        font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif ;
        text-rendering: optimizeLegibility;
        text-transform: uppercase;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        height: 48px;
        line-height: 48px;
        background-color: #FFFFFF;
        color: #303133;
        cursor: pointer;
        border-bottom: 1px solid #EBEEF5;
        font-size: 16px;
        font-weight: bold;
        -webkit-transition: border-bottom-color .3s;
        transition: border-bottom-color .3s;
        outline: none;
        }

        .avue-group__header h1 {
            /* font-family: "Univers Condensed", "Helvetica Neue", Arial, Helvetica, sans-serif; */
            font-family:  "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
            margin: 10px 0px;
            padding-bottom: 16px;
            color: #424242;
            font-weight: bold;
            text-rendering: optimizeLegibility;
            text-transform: uppercase;
            font-size: 15px;
        }
    }

</style>
