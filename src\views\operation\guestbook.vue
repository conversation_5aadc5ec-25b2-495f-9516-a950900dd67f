<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.feedback')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.feedback')}}</h1>
        <el-card shadow="never" class="sgs-box">
            <el-row>
                <el-form :inline="true" :model="query" size="medium">
                    <el-form-item>
                        <el-input clearable v-model="query.contactName"
                                :placeholder="$t('contact.nameFull')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input clearable v-model="query.mobile"
                                :placeholder="$t('contact.phone')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input clearable v-model="query.email"
                                :placeholder="$t('contact.email')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input clearable v-model="query.content_like"
                                :placeholder="$t('contact.content')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchChange">{{$t('operation.search')}}</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <el-table
                        :data="tableData"
                        style="width: 100%"
                        size="medium">
                    <el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            fixed
                            prop="contactName"
                            :label="$t('contact.nameFull')"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            fixed
                            prop="mobile"
                            :label="$t('contact.phone')"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="email"
                            :label="$t('contact.email')"
                            :show-overflow-tooltip="true"
                            width="200">
                    </el-table-column>
                    <el-table-column
                            prop="content"
                            :label="$t('feedback.content')"
                            :show-overflow-tooltip="true">
                    </el-table-column>
                    <!--<el-table-column-->
                            <!--label="操作"-->
                            <!--width="180">-->
                        <!--<template slot-scope="scope">-->
                            <!--<el-button type="text" @click="detailRow(scope.row)" size="small" icon="el-icon-delete">编辑</el-button>-->
                            <!--<el-button @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-edit">删除</el-button>-->
                        <!--</template>-->
                    <!--</el-table-column>-->
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
    </basic-container>
</template>

<script>
    import {getList} from "@/api/operation/guestbook";
    export default {
        data(){
            return{
                name: "list",
                tableData: [],
                form: {},
                query:{},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
            }
        },
        methods:{
            searchReset() {
                this.query = {};
                this.onLoad(this.page);
            },
            searchChange() {
                this.page.currentPage=1;
                this.onLoad(this.page, {});
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page, {});
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page, {});
            },
            onLoad(page, params = {}) {
                getList(page.currentPage, page.pageSize, Object.assign(params, this.query,this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            }
        },
        created() {
            this.onLoad(this.page);
        }

    }
</script>

<style lang="scss">
    .el-tooltip__popper {
        max-width: 400px;
        line-height: 180%;
    }
</style>
