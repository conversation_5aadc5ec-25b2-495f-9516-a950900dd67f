<template>
    <div id="TableColumnChecked" class="TableColumnChecked">
        <el-drawer
                :visible.sync="show"
                :direction="'rtl'"
                :append-to-body="true"
                :close-on-press-escape="false"
                :wrapperClosable="false"
                :modal-append-to-body="false"
                custom-class="trf_common_column_setting"
                @close="close"
        >
            <template slot="title">
                <h4>{{$t('SEComment.columnTitle')}}</h4>
            </template>
            <div class="trf_drawer_content">
                <el-collapse v-model="activeName">
                    <el-collapse-item :title="$t('SEComment.manageColumn')" name="columnSetting">
                        <template slot="title">
                            <h4 class="sgs-title">
                                {{$t('SEComment.manageColumn')}}
                            </h4>
                        </template>
                        <el-row :gutter="20" style="padding-bottom: 20px">
                            <el-col :span="11">
                                <el-input clearable :placeholder="$t('crud.searchBtn')" v-model="leftSearch">
                                    <template #prefix>
                                        <i class="el-icon-search"></i>
                                    </template>
                                </el-input>
                            </el-col>
                            <el-col :span="11" :offset="1">
                                <div style="
                                    height: 40px;
                                    display: flex;
                                    align-items: flex-end;
                                    align-content: flex-end;
                                ">{{rightShowColumns.length + 2}} {{$t('SEComment.columnSelected')}}</div>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="11" >
                                <el-card shadow="always" body-style="overflow: auto;padding:5px">
                                    <el-checkbox-group
                                            v-model="leftColumn"
                                            @change="handleCheckedColumnChange"
                                    >
                                        <el-checkbox
                                                v-for="column in (!leftSearch? leftHideColumns :(leftHideColumns.filter(c=>c.label.toUpperCase().indexOf((leftSearch+'').toUpperCase())>-1)))"
                                                :label="column"
                                                :key="column.prop"
                                                style="width: 100%;margin-right: 0;"
                                        >{{ column.label }}
                                        </el-checkbox>
                                    </el-checkbox-group>
                                </el-card>
                            </el-col>
                            <el-col :span="11" :offset="1">
                                <el-card class="common_table_column_checked" shadow="always" body-style="overflow: auto;padding:5px">
                                    <div
                                            class="drag_show_div"
                                            v-for="column in fixedColumns"
                                            :key="column.prop"
                                            style="width: 100%;margin-right: 0;"
                                    >
                                        <div class="left_move"></div>
                                        <div class="center_data">{{column.label}}</div>
                                        <div class="right_del"></div>
                                    </div>
                                    <draggable ref="draggable"
                                               v-model="rightShowColumns"
                                               :move="onMove"
                                               @start="drag=true"
                                               ghost-class="ghost"
                                               animation="166"
                                               @end="drag=false"
                                               @change="changeDrag"
                                    >
                                        <div
                                            class="drag_show_div"
                                            v-for="(column,rind) in rightShowColumns"
                                            :key="column.prop"
                                            style="width: 100%;margin-right: 0;"
                                        >
                                            <div class="left_move">
                                                <i class="el-icon-rank"></i>
                                            </div>
                                            <div class="center_data">{{column.label}}</div>
                                            <div class="right_del">
                                                <i class="el-icon-delete" @click="delShowColumn(column,rind)"></i>
                                            </div>
                                        </div>
                                    </draggable>
                                </el-card>
                            </el-col>
                        </el-row>
                    </el-collapse-item>
                    <el-collapse-item name="filterRule">
                        <template slot="title">
                            <h4 class="sgs-title">
                                {{$t('SEComment.filterRule')}}
                            </h4>
                        </template>
                        <div>
                           {{$t('SEComment.filterRuleTip')}}
                        </div>
                        <el-row :gutter="20">
                            <el-checkbox-group v-model="checkList" @change="changeCheckRole">
                               <el-col :span="8"
                                    v-for="(usageType,index) in customerRoles"
                                   v-show="usageType.show"
                                   :index="index"
                               >
                                   <el-checkbox :label="usageType.value">{{$t(usageType.label)}}</el-checkbox>
                               </el-col>
                            </el-checkbox-group>
                        </el-row>
                    </el-collapse-item>
                </el-collapse>
            </div>
            <el-row>
                <el-col style="text-align: right">
                    <el-button class="footer_save_btn" type="primary"  size="small" @click="saveSetting">{{$t('trfList.filter.save')}}</el-button>
                </el-col>
            </el-row>
        </el-drawer>
    </div>
</template>

<script>
    import {validatenull} from "@/util/validate";
    import {mapGetters} from "vuex";

    export default {
        name: "TableColumnChecked",
        data() {
            return {
                activeName:['columnSetting'],
                isIndeterminate:false,
                checkAll:false,
                checkedColumn:[],
                leftColumn:[],
                leftSearch:'',
                drag:true,
                show:true,
                datas:[],
                fixedColumns:[],
                leftHideColumns:[],
                rightShowColumns:[],
                customerRoles:[
                    {label:'trf.applicant',value:'applicant',show:true},
                    {label:'customer.new.buyer',value:'buyer',show:true},
                    {label:'customer.new.agent',value:'agent',show:true},
                    {label:'customer.new.supplier',value:'supplier',show:true},
                    {label:'customer.new.manufacturer',value:'manufacturer',show:true},
                ],
                checkList:['applicant','buyer','agent','supplier','manufacturer']
            }
        },
        methods: {
            handleCheckAllChange(val){
                this.checkedColumn = val? this.datas.map(p=>p.prop) : []
                this.isIndeterminate = false;
            },
            handleCheckedColumnChange(){
                this.leftColumn.forEach(col=>{
                    let index = this.leftHideColumns.findIndex(da=>da.prop == col.prop);
                    let copyColumn = JSON.parse(JSON.stringify(col));
                    copyColumn.hide = false;
                    this.leftHideColumns.splice(index,1);
                    this.rightShowColumns.push(copyColumn);
                })
                this.rightShowColumns.sort((a,b)=>a.seq-b.seq);
                this.$set(this,'leftColumn',[]);
            },
            saveSetting(){
                let roleFilter = {
                    'applicant':0,'buyer':0,'agent':0,'supplier':0,'manufacturer':0
                };
                this.checkList.forEach(c=>{
                    roleFilter[c] = 1;
                });
                if(this.userInfo.isInternalUser!=1 && !this.role.isBuyerOrAgent){
                    roleFilter['buyer'] = 1;
                    roleFilter['agent'] = 1;
                }
                let columns = [...this.fixedColumns,...this.rightShowColumns,...this.leftHideColumns];
                let checkedColumn = [...this.fixedColumns,...this.rightShowColumns].map(c=>{return c.prop});
                this.$emit('updateColShow',columns,checkedColumn,roleFilter);
            },
            changeCheckRole(){
                //console.log('changeCheckRole',this.checkList)
            },
            delShowColumn(column,index){
                this.rightShowColumns.splice(index,1);
                let copyColumn = JSON.parse(JSON.stringify(column));
                copyColumn.hide = true;
                this.leftHideColumns.push(copyColumn);
                this.leftHideColumns.sort((a,b)=>a.seq-b.seq);
            },
            onMove(event){
                let {draggedContext,relatedContext} = event;
                let dragFixed = draggedContext.element.fixed;
                let relatedFixed = relatedContext.element.fixed;
                //存在fixed 禁止拖动
                if(dragFixed || relatedFixed){
                    return false;
                }
            },
            changeDrag(event){
                //console.log("changeDrag",event);
                this.rightShowColumns.forEach((da,index)=>{
                    let {label,seq} = da;
                    //console.log(label,seq);
                    da.seq = index + this.fixedColumns.length;
                })
            },
            close(){
                this.$emit('closed')
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
        },
        created() {
            this.datas = this.columnOption;
            //分出已经选择的，还没有选择的
            this.fixedColumns = this.datas.filter(da=>['trf_header_trfStatusName','trf_trfNo'].includes(da.prop));
            this.fixedColumns.forEach(da=>{
                let {prop} = da;
                da.seq = prop == 'trf_header_trfStatusName'?0:1;
            })
            this.dragColumns = this.datas.filter(da=>!['trf_header_trfStatusName','trf_trfNo'].includes(da.prop));
            this.leftHideColumns = this.dragColumns.filter(da=>da.hide);
            this.rightShowColumns = this.dragColumns.filter(da=>!da.hide);
            if(Object.keys(this.roleFilter).length!=0){
                this.checkList = [];
            }
            let buyerAndAgent = [];
            for(let k of Object.keys(this.roleFilter)){
                if(this.roleFilter[k]==1){
                    this.checkList.push(k);
                }
                if(['buyer','agent'].includes(k)){
                    buyerAndAgent.push(k);
                }
            }
            if(this.userInfo.isInternalUser!=1){
                this.customerRoles.forEach((item)=>{
                    if(['buyer','agent'].includes(item.value)){
                        item.show = this.role.isBuyerOrAgent;
                    }
                })
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "menu", "dimensions"]),
            role() {
                return {
                    isBuyerOrAgent: this.haseRole('UserRole', 'Buyer') ||  this.haseRole('UserRole', 'Agent'),
                };
            },
        },
        mounted() {
        },
        watch:{
        },
        props:{
            //是完整的字段，就是table的columns
            columnOption:{
                type:Array,
                default:[]
            },
            //上一次选中的
            checkedVal:{
                type:Array,
                default(){
                    return []
                }
            },
            roleFilter:{
                type:Object,
                default(){
                    return {}
                }
            }
        },
        components: {}
    }
</script>

<style lang="scss">
@media (max-width: 768px) {
  .trf_common_column_setting{
    width: 50% !important;
  }
}
@media (max-width: 1024px) {
  .trf_common_column_setting{
    width: 50% !important;
  }
}
@media (max-width: 1366px) {
  .trf_common_column_setting{
    width: 50% !important;
  }
}
@media (min-width: 1367px) {
  .trf_common_column_setting{
    width: 35% !important;
  }
}

@media (max-height: 768px) {
  .trf_common_column_setting{
    .el-card__body {
      height: 300px !important;
    }
  }
}
@media (max-height: 1024px) {
  .trf_common_column_setting{
    .el-card__body {
      height: 400px !important;
    }
  }
}
@media (min-height: 1025px) {
  .trf_common_column_setting{
    .el-card__body {
      height: 500px !important;
    }
  }
}

.trf_common_column_setting{
    div.trf_drawer_content{
      height: calc(100% - 60px) !important;
    }

    .TableColumnChecked {
        .footer_save_btn{
        }
    }
  .common_table_column_checked .drag_show_div{
    display: flex;
    width: 100%;
    height: 36px;
    margin-bottom: 2px;
    line-height: 36px;
    &:nth-child(even){
        background: rgba(203, 201, 201, 0.13);
    }
    &:nth-child(odd){
        background: rgba(199, 197, 197, 0.13);
      }
    .left_move{
      width: 40px;
      cursor: move;
      text-align: center;
    }
    .center_data{
      width: calc(100% - 80px);
      overflow: hidden;
      white-space: nowrap;
      word-break: break-all;
      text-overflow: inherit;
    }
    .right_del{
      width: 40px;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>