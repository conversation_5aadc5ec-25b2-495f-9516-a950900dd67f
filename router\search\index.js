const router = require("koa-router")();
const Api = require("./../../request");

// 保存搜索过滤器
router.post("/api/sgs-mart/search/filter/save", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/search/filter/save", ctx);
  ctx.body = datas;
});

// 查询搜索过滤器
router.post("/api/sgs-mart/search/filter/query", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/search/filter/query", ctx);
    ctx.body = datas;
});

// 删除搜索过滤器
router.post("/api/sgs-mart/search/filter/delete", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/search/filter/delete", ctx);
    ctx.body = datas;
});

// 设置搜索过滤器为默认
router.post("/api/sgs-mart/search/filter/default", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/search/filter/default", ctx);
    ctx.body = datas;
});

// 执行搜索查询
router.post("/api/sgs-mart/search/dimension/list", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/search/dimension/list", ctx);
    ctx.body = datas;
});

// 设置搜索过滤器为默认
router.post("/api/sgs-mart/search/list", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/search/list", ctx);
    ctx.body = datas;
});

module.exports = router;