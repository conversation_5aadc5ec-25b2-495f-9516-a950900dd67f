<template>
  <div class="smart_views_CommonTable" id="smart_views_CommonTable">
    <el-row v-if="title">
      <el-col>
        <p class="card-title">
          {{ title }}
        </p>
      </el-col>
    </el-row>
    <el-row class="table_bar" v-if="menuShow">
      <el-col :span="18">
        <slot name="menuLeft"></slot>
      </el-col>
      <el-col :span="6" style="text-align: right">
        <slot name="menuRight"></slot>
      </el-col>
    </el-row>
    <el-table
      v-bind="$attrs"
      :data="tableDataList"
      ref="tableRef"
      :max-height="height || tableHeight"
      style="width: 100%"
      :size="tableOption.size || 'default'"
      :row-class-name="rowClassFN"
    >
      <!-- 必选框 -->
      <el-table-column
        v-if="option.selection"
        :selectable="option.selectionDis"
        type="selection"
        width="35"
        align="center"
        fixed
      >
      </el-table-column>
      <!-- 序号 -->
      <el-table-column
        v-if="option.index"
        type="index"
        label="#"
        align="center"
        width="45px"
        :fixed="option.indexFixed"
      >
        <template slot="header" slot-scope="scope">
          <div class="header_slot">
            <div class="header_slot_span">
              <span>#</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 显示列 -->
      <el-table-column
        v-for="(column, index) in columnOption.filter((item) => {
          return item.hide != true;
        })"
        :prop="column.prop"
        :label="column.i18n? $t(column.label): column.label"
        :width="column.width"
        :min-width="column.minWidth || 220"
        :fixed="column.fixed"
        :align="column.align || 'left'"
        show-overflow-tooltip
        :key="index"
      >
        <template slot="header" slot-scope="scope">
          <slot
            v-if="column.headerslot"
            v-bind="scope"
            :prop="column.prop"
            :label="column.label"
            :name="'header-' + column.prop"
          >
          </slot>
          <div class="header_slot">
            <el-select
              style="width: 100%; margin-top: 1px; padding-bottom: 1px"
              :placeholder="column.label ? $t(column.label) : column.label"
              v-if="
                columnHeaderShow[column.prop] &&
                column.filter &&
                column.type == 'Select'
              "
              v-model="filters[column.prop]"
              :multiple="column.multiple"
              filterable
              size="small"
              clearable
              @clear="changeHeader(column, false)"
            >
              <el-option
                v-for="(dic, index) in column.dicData"
                :value="dic.value"
                :label="column.i18n ? $t(dic.label) : dic.label"
                :key="index"
              >
              </el-option>
            </el-select>
            <!-- 不能换成v-show 因为v-focus 绑定的是insert驱动 ，如果换成update驱动，会导致不能失去焦点-->
            <el-input
              v-if="
                columnHeaderShow[column.prop] &&
                column.filter &&
                column.type == 'Input'
              "
              style="margin-top: 1px; padding-bottom: 1px"
              size="small"
              :placeholder="column.label ? $t(column.label) : column.label"
              v-model="filters[column.prop]"
              v-focus
              :key="column.prop"
              clearable
              @clear="changeHeader(column, false)"
              @blur="headerColumnBlur(column)"
            ></el-input>
            <el-date-picker
                v-if="columnHeaderShow[column.prop] && column.filter && column.type === 'DateRange'"
                v-model="filters[column.prop]"
                type="daterange"
                range-separator="To"
                start-placeholder="Start date"
                end-placeholder="End date"
                @clear="changeHeader(column,false)"
            />
            <div
              class="header_slot_span"
              v-show="!columnHeaderShow[column.prop]"
              @click="changeHeader(column, true)"
            >
              <span>
                {{ column.label ? $t(column.label) : column.label }}
                <!-- 待优化 -->
                <!-- <span v-show="column.filter" style="margin-left: 5px">
                  <i class="el-icon-caret-bottom"></i>
                </span> -->
              </span>
            </div>
            <div
              class="header_slot_sort"
              v-if="tableOption.showSortIcon && !column.notSort"
            >
              <i
                v-if="
                  !sortObj[column.prop] ||
                  !sortObj[column.prop].sort ||
                  [0].includes(sortObj[column.prop].sort)
                "
                @click.stop="sortColumn(column, 'Asc')"
                class="el-icon-d-caret"
              >
                <el-tooltip
                  placement="top"
                  content="To sort in ascending order"
                >
                </el-tooltip>
              </i>
              <i
                v-if="[1].includes(sortObj[column.prop].sort)"
                @click.stop="sortColumn(column, 'Desc')"
                class="el-icon-caret-top"
              >
                <el-tooltip
                  placement="top"
                  content="To sort in descending order"
                >
                </el-tooltip>
              </i>
              <i
                v-if="[-1].includes(sortObj[column.prop].sort)"
                @click.stop="sortColumn(column, '')"
                class="el-icon-caret-bottom"
              >
                <el-tooltip placement="top" content="To clear sorting">
                </el-tooltip>
              </i>
            </div>
          </div>
        </template>
        <template slot-scope="scope">
          <slot
            v-if="column.slot"
            v-bind="scope"
            :prop="column.prop"
            :label="column.label"
            :name="column.prop"
          ></slot>
          <span v-else-if="column.dicData != null && column.dicData.length > 0">
            <el-tag
              size="small"
              v-if="getDicLabel(column.dicData, scope.row[column.prop]).tag"
              :type="getDicLabel(column.dicData, scope.row[column.prop]).type"
            >
              <span
                style="display: inline-block;min-width: 74px;text-align: center;"
                :class="isLineThrough(scope.row, column) ? 'strikethrough' : ''"
              >
                {{
                  $t(getDicLabel(column.dicData, scope.row[column.prop]).label)
                }}
              </span>
            </el-tag>
            <el-tag
              size="small"
              v-else-if="getDicLabel2(column.dicData, scope.row[column.prop]).tag2"
              :type="getDicLabel2(column.dicData, scope.row[column.prop]).type"
            >
              <span
                style="display: inline-block;min-width: 74px;text-align: center;"
                :class="isLineThrough(scope.row, column) ? 'strikethrough' : ''"
              >
                {{
                  $t(getDicLabel2(column.dicData, scope.row[column.prop]).value)
                }}
              </span>
            </el-tag>
            <span
              v-else
              :class="isLineThrough(scope.row, column) ? 'strikethrough' : ''"
            >
              {{
                $t(getDicLabel(column.dicData, scope.row[column.prop]).label)
              }}
            </span>
          </span>
          <span
            v-else
            :class="isLineThrough(scope.row, column) ? 'strikethrough' : ''"
            >{{ scope.row[column.prop] }}</span
          >
        </template>
      </el-table-column>
      <!-- 操作列 -->
      <el-table-column
        v-if="option.action"
        fixed="right"
        :width="option.actionWidth || '200px'"
        align="center"
      >
        <template slot="header">
          <div class="header_slot">
            <div class="header_slot_span">
              <span>
                {{option.actionName? option.actionName: $t("operation.title") }}
              </span>
            </div>
          </div>
        </template>
        <template slot-scope="scope">
          <slot v-bind="scope" name="actionColumn"></slot>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="page && page.show"
      class="commontable-pagination"
      v-model:current-page="page.page"
      v-model:page-size="page.rows"
      :page-sizes="[10, 20, 50, 100]"
      size="small"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="page.total"
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </div>
</template>

<script>
import Tag from "./tag.vue";
import moment from "moment";

export default {
  name: "CommonTable",
  components: {
    Tag,
  },
  directives: {
    focus: {
      inserted: (el) => {
        const inputElement = el.querySelector('input');
        if (inputElement) {
          inputElement.focus();
        }
      },
      
    },
  },
  props: {
    menuShow: {
      type: null,
      default: false,
    },
    title: {
      type: null,
      default: "",
    },
    option: {
      type: Object,
      default: function () {
        return {
          hideRowColor: false, // 隐藏row的背景色是否透明
          showSortIcon: false, // 是否显示排序图标
          selection: false, // 是否显示勾选框
          selectionDis: () => {}, // 某行dis、或者不disabled,入参 function
          index: false, // 是否显示序号
          action: false, // 是否显示最后action操作列
          actionName:'',//action列的表头名字，不传的话 ，默认中英文Action
          sortSelectionFiledName: "", // 按照数据顺序返回勾选的数据，需要传递能唯一标识数据值的filedName
          column: [], // 列 ，示例：{prop: '属性名', label: '展示的名称', hide: false, filter: true, slot: false,width:180 ,fixed:'right/left/null'},
          disableOption: {
            // 解释说明 当 disableNeedStrikethrough=true 且 prop = disableValue 时，整行内容文本添加删除线
            disableNeedStrikethrough: false,
            disableValue: 0,
            disableProp: "",
          },
        };
      },
    },
    filterByLocal:{//是否开启前端过滤不请求后端，当为true时，请不要再在使用时监听filter
        type:Boolean,
        default:false,
    },
    filters: {
      type: Object,
    },
    sort: {
      type: Object,
    },
    page: {
      type: Object,
      default: function () {
        return {
          show: false,
          page: 1,
          rows: 10,
          total: 0,
          small: false,
          layout: "total, sizes, prev, pager, next,jumper",
          pagerCount: 7,
          size: 1,
          sizes: [10, 20, 50, 100],
        };
      },
    },
    height: {
      type: null,
    },
    onLoad: {
      type: Function,
    },
    data: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      sortObj: {},
      layout: "total, sizes, prev, pager, next",
      tableOption: {},
      firstLoad: true,
      defaultColumn: { column: { hide: false } },
      columnHeaderShow: {},
      tableRef: null,
      tableHeight: 0,
      resizeTimer: null,
        tableDataList:[],
    };
  },
  computed: {
    columnOption() {
      return this.tableOption.column || [];
    },
    showColumn() {
      return (
        this.columnOption.filter((item) => {
          return !item.hide;
        }) || []
      );
    },
  },
  methods: {
    handleResize() {
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.tableHeight = this.calculateHeight();
      }, 200);
    },
    isLineThrough(row) {
      let { disableOption } = this.tableOption;
      if (!disableOption) {
        return false;
      }
      let { disableNeedStrikethrough, disableValue, disableProp } =
        disableOption;
      if (!disableNeedStrikethrough) {
        return false;
      }
      let cellData = row[disableProp];
      return cellData === disableValue;
    },
    calculateHeight() {
      const baseHeight = window.innerHeight;
      const headerHeight = 300; // 根据实际标题栏高度调整
      const paginationHeight = this.page && this.page.show ? 50 : 0;
      let height = baseHeight - headerHeight - paginationHeight - 100; // 100为预留空间
      return height <= 400 ? 400 : height;
    },
    changeHeader(column, flag) {
      if (!column.filter) {
        return;
      }
      let { prop } = column;
      this.columnHeaderShow[prop] = flag;
      // this.$forceUpdate()
    },
    headerColumnBlur(column) {
      const filterValue = this.filters[column.prop];
      if (
        filterValue == null ||
        filterValue === "undefined" ||
        filterValue === undefined ||
        filterValue === "" ||
        filterValue.length === 0
      ) {
        this.changeHeader(column, false);
      }
    },
    getSelection() {
      if (!this.option.selection) {
        return [];
      }
      let data = this.$refs.tableRef.data;
      if (!data || data.length === 0) {
        return [];
      }
      if (!this.option.sortSelectionFiledName) {
        return this.$refs.tableRef.selection;
      }
      let filedName = this.option.sortSelectionFiledName;
      let currentSelect = this.$refs.tableRef.selection;
      let sortSelection = [];
      let selectFiledValue = currentSelect.map((cs) => cs[filedName]);
      if (!selectFiledValue) {
        return this.$refs.tableRef.selection;
      }
      data.forEach((da, ind) => {
        let dataFiledValue = da[filedName];
        if (selectFiledValue.includes(dataFiledValue)) {
          sortSelection.push(da);
        }
      });
      return sortSelection;
    },
    clearAllSelection() {
      this.$refs.tableRef.clearSelection();
    },
    rowClassFN(row) {
      if (this.option.hideRowColor) {
        return "hide-row-color";
      }
      return "normal-row-color";
    },
    getDicLabel(dicData, value) {
      let dic = dicData.filter((dic) => {
        return dic.value === value;
      });
      return dic && dic.length > 0 ? dic[0] : { label: "" };
    },
    getDicLabel2(dicData, value) {
      let dic = dicData.filter((dic) => {
        return dic.value == value;
      });
      return dic && dic.length > 0 ? dic[0] : value ? { label: value, value:value, tag2: true, type: "info" }:{label:''};
    },
    columnChange(val) {
      if (this.firstLoad) {
        return true;
      }
      this.tableOption.column.forEach((item) => {
        const t = val.filter((v) => {
          return v.prop === item.prop;
        });
        item.hide = t.length <= 0;
      });
    },
    currentChange(currentPage) {
      this.page.page = currentPage;
      this.$emit("changePage", this.page);
    },
    sizeChange(pageSize) {
      this.page.rows = pageSize;
      this.$emit("changePage", this.page);
    },
    sortColumn(column, order) {
      let { prop } = column;
      let keys = Object.keys(this.sortObj);
      keys.forEach((key) => {
        this.sortObj[key].sort = 0;
      });
      if (order) {
        this.sortObj[prop].sort = order === "Asc" ? 1 : -1;
      }
      this.$emit("sortBy", {
        prop: order ? prop : "",
        orderBy: order,
      });
    },
      filterLocalList(newV){
          if(!newV || Object.keys(newV).length==0){
              this.tableDataList = this.data;
              return;
          }
          this.tableDataList = this.data.filter(da=>{
              let keys = Object.keys(newV);
              let result = true;
              for(let key of keys){
                  let searchV = newV[key];
                  if(searchV==='' || searchV==null || searchV== undefined || searchV.length==0){
                     continue;
                  }
                  let col = this.tableOption.column.find(co=>co.prop==key);
                  let type = col.type.toLowerCase();
                  let {searchProp,multiple} = col;
                  if(type=='input'){
                    if(Array.isArray(da[key])){
                        if(searchProp){
                            result = result && (da[key] || []).map(d=>d[searchProp]).join("").indexOf(searchV)>-1;
                        }else{
                            result = result && (da[key] || []).join("").indexOf(searchV)>-1;
                        }
                    }else{
                        result = result && (da[key] || '').indexOf(searchV)>-1;
                    }
                  }
                  if(type=='select'){
                      if(multiple){
                          result = result && (searchV || []).includes(da[key]);
                      }else{
                        result = result && (da[key] || '') == searchV ;
                      }
                  }
                  if(type=='date' &&  searchV.length==2){
                      try{
                          let startDate = searchV[0];
                          let endDate = searchV[1];
                          startDate = moment(startDate);
                          endDate = moment(endDate);
                          let daDate = moment(da[key]);
                          result = result && daDate.isBetween(startDate,endDate,null,'[]');
                      }catch (e){
                          result = result && false;
                      }
                  }
              }
              return result;
          })
      }
  },
  beforeMount() {
    // 计算当前屏幕分辨率，用来展示表格高度
    this.option.column.forEach((item) => {
      if (!item.hide) {
        item["hide"] = this.defaultColumn.column.hide;
      }
    });
    this.tableOption = this.option;
    this.tableOption.column.forEach((item) => {
      this.$set(this.columnHeaderShow, item.prop, false);
      this.$set(this.sortObj, item.prop, {
        orderBy: item.prop,
        sort: 0,
      });
    });
  },
    created(){
    },
  mounted() {
    window.addEventListener("resize", this.handleResize);
    this.tableHeight = this.calculateHeight();
  },
    watch:{
      data:{
          immediate:true,
          handler(newV){
              this.tableDataList = newV;
          }
      },
        filters:{
            immediate:false,
            deep:true,
            handler(newV,oldV){
                if(!this.filterByLocal){
                    return;
                }
                this.filterLocalList(newV);
            }
        }
    },
  beforeUnmount() {
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style lang="scss" scoped>
.smart_views_CommonTable {
  p.card-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
    padding: 10px 0 5px 0;
  }
  .table_bar {
    padding: 5px 0;
  }

  .header_slot {
    display: flex;
    justify-content: flex-start;

    .header_slot_span {
      height: 32px;
      text-align: center;
      line-height: 32px;
      padding: 0;
      margin: 0;
      font-weight: 100;
    }

    .header_slot_sort {
      line-height: 34px;
      cursor: pointer;
    }

    div.el-select__placeholder span {
      font-weight: normal !important;
    }
  }
}
</style>
