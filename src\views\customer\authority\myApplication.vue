<template>
  <div>
    <el-form :inline="true" :model="authorityQuery" @submit.native.prevent size="medium" class="text-right">
      <el-form-item>
        <el-input
            @keyup.enter.native="onSearch"
            @clear="onSearch"
            v-model="authorityQuery.corpName"
            :placeholder="$t('dateValidate.companyNameValidate')"
            clearable>
          <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn">
          <i class="el-icon-circle-plus-outline"></i>
          {{ $t('operation.add') }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-table class="scrollbar" stripe  :data="myApplicationList" v-loading="loading"
              :element-loading-text="$t('loading')" style="width: 100%" height="500">
      <el-table-column prop="authUserName" :show-overflow-tooltip="true" :label="$t('account.userName')" width="auto"
                       min-width="80">
      </el-table-column>
      <el-table-column prop="authAccount" :show-overflow-tooltip="true" :label="$t('account.title.default')"
                       min-width="100">
      </el-table-column>
      <el-table-column prop="customerNameZh" :show-overflow-tooltip="true" :label="$t('customer.companyNameCn')"
                       width="auto" min-width="120">
      </el-table-column>
      <el-table-column prop="customerNameEn" :show-overflow-tooltip="true" :label="$t('customer.companyNameEn')"
                       min-width="120">
      </el-table-column>
      <el-table-column prop="authStatus" :label="$t('common.status.title')" min-width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.authStatus==4">{{ $t('authority.status.toBeAudited') }}</span>
          <span v-if="scope.row.authStatus==1">{{ $t('authority.status.audited') }}</span>
          <span v-if="scope.row.authStatus==2">{{ $t('authority.status.rejected') }}</span>
          <span v-if="scope.row.authStatus==3">{{ $t('authority.status.cancelled') }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="updateTime" :label="$t('user.updateTime')" min-width="100">
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChange" @current-change="currentChange" :current-page="page.currentPage"
                   :page-sizes="[10, 20, 50, 100]" :page-size="page.pageSize"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="page.total">
    </el-pagination>
    <el-drawer :before-close="closeApplicationDrawer" :title="$t('operation.add')"
               :visible.sync="dialogApplicationFormVisible"
               size="60%">
      <el-form :model="applicationForm" :rules="applicationRules" label-position="left" ref="applicationForm"
               label-width="200px" class="sgs-form">
        <el-form-item :label="$t('authority.bossNo')" prop="bossNo"
                      :rules="{ required: true, message: this.$t('authority.validate.bossNoVali'), trigger: 'blur' }">
          <el-input maxlength="50" v-model="applicationForm.bossNo" clearable
                    autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('authority.account')" prop="authAccount"
                      :rules="{ required: true, message: this.$t('authority.validate.accountValidate'), trigger: 'blur' }">
          <el-input maxlength="100" v-model="applicationForm.authAccount" clearable
                    autocomplete="off"></el-input>
        </el-form-item>
        <div class="sgs-bottom">
          <el-button @click="closeApplicationDrawer">{{ $t('operation.cancel') }}
          </el-button>
          <el-button type="primary" @click="submitForm()" :loading="submitLoading">
            {{ $t('operation.confirm') }}
          </el-button>
        </div>
      </el-form>

    </el-drawer>
  </div>
</template>

<script>
import {mapGetters} from "vuex";
import {objectIsNull} from "@/util/validate";
import {
  querMyAuthorityList, validateCustomer, saveAuth
} from "@/api/customer/authority";

export default {
  data() {
    return {
      loading: false,
      submitLoading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      sort: {descs: "update_time"},
      dialogApplicationFormVisible: false,
      myApplicationList: [],
      authorityQuery: {
        corpName: '',
        bossNo: '',
        pageNo: "",
        pageSize: ''
      },
      applicationRules: {},
      applicationForm: {
        bossNo:'',
        authAccount:''
      }
    }
  },
  created() {
    this.initMyAuthorityData()
  },
  computed: {
    ...mapGetters(["permission", "userInfo", "language"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission['sgs:authority:add'], false)
      };
    }
  },
  methods: {
    submitForm() {
      this.$set(this.applicationForm, "language", this.language);
      //校验
      this.$refs['applicationForm'].validate((valid, error) => {
        if (valid) {//校验通过
          this.submitLoading = true;
          //验证用户以及公司数据
          validateCustomer(this.applicationForm).then((res) => {
            let data = res.data.data;
            if (!objectIsNull(data)) {
              //新增授权
              let authObj = {};
              authObj.authUserId = data.id;
              authObj.authAccount = data.code
              authObj.authUserName = data.name;
              authObj.companyId = data.companyID;
              authObj.language = this.language;
              saveAuth(authObj).then((res) => {
                this.submitLoading = false;
                this.closeApplicationDrawer();
                this.$message({
                  type: "success",
                  message: this.$t('api.success')
                });
                this.onSearch();
              }).catch((e) => {
                this.submitLoading = false;
                //this.$message.error(e.message);
              });
            }
          }).catch((e) => {
            this.submitLoading = false;
            //this.$message.error(e.message);
          });

        } else {//校验未通过
          this.$notify({
            title: this.$t('tip'),
            message: this.$t('trf.trfValidateError'),
            type: 'warning'
          });
          return false;
        }
      });
    },
    closeApplicationDrawer(done) {
      this.submitLoading = false;
      this.$refs['applicationForm'].resetFields();
      this.dialogApplicationFormVisible = false;
    },
    onSearch() {
      this.page.currentPage = 1;
      this.initMyAuthorityData();
    },
    lengthFilter: function (value, len) {
      value = value.toString();
      if (value.length > len) {
        value = value.slice(0, len) + '...'
      }
      return value
    },
    async handelSubmit() {
      this.page.currentPage = 1;
      await this.initMyAuthorityData();
    },
    initMyAuthorityData() {
      this.authorityQuery.pageNo = this.page.currentPage;
      this.authorityQuery.pageSize = this.page.pageSize;
      this.loading = true;
      this.$set(this.authorityQuery, "flag", 0);//我的申请
      querMyAuthorityList(this.authorityQuery).then((res) => {
        this.loading = false;
        this.myApplicationList = res.data.data.records;
        this.page.total = res.data.data.total;
      }).catch(() => {
        this.loading = false;
      });
    },

    //分页查询
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.initMyAuthorityData();
    },
    currentChange(pageCurrent) {
      this.page.currentPage = pageCurrent;
      this.initMyAuthorityData();
    },

    search() {
      this.page.currentPage = 1;
      this.initMyAuthorityData();
    },
    addRow() {
      this.dialogApplicationFormVisible = true;
    },

    deleteTrfTemplate(row) {
      this.$confirm(this.$t("operation.confirmDelete"), this.$t("tip"), {
        confirmButtonText: this.$t("submitText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning",
      })
          .then(() => {
            removeTrfTemplate(row.id).then(
                () => {
                  this.$message({
                    type: "success",
                    message: "操作成功!",
                  });
                  this.initMyAuthorityData();
                },
                (error) => {
                  console.log(error);
                }
            );
          })
          .catch(() => {
            /* this.$message({
              type: 'info',
              message: '已取消删除'
            });*/
          });
    },
  }
}
</script>
<style lang="scss">

</style>

