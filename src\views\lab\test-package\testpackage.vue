<template>
    <el-form-item :label="$t('testpackage.testItem')">
        <el-row>
            <el-col :span="5">
                <el-button type="text" icon="el-icon-circle-plus-outline" size="small" @click="addTestItem">{{$t('operation.add')}} Item</el-button>
            </el-col>
            <el-col :offset="8" :span="10">
                <el-input size="small" clearable
                          :placeholder="$t('common.inputFilter')"
                          v-model="filterText">
                </el-input>
            </el-col>
        </el-row>
        <el-row class="question_info_lists">
            <el-tree ref="tree" :key="tree_key"
                     :data="treeData"
                     node-key="id"
                     :expand-on-click-node="false"
                     default-expand-all
                     draggable
                     :filter-node-method="filterNode" :empty-text="$t('NoData')">
                  <span class="custom-tree-node" slot-scope="{ node, data }">
                    <!--<span v-if="data.isEdit">-->
                        <!--<i class="el-icon-rank"></i> {{ data.testPackageName }}-->
                    <!--</span>-->
                    <span>
                        <span style="display: none;">{{data['_id']=(validatenull(data.id)?data['_id']:data.id)}}</span>
                        <i class="el-icon-rank" style="padding: 0px 10px;"></i>
                        <el-input clearable
                                  style="width: 360px;"
                                  :placeholder="$t('testpackage.testItem')"
                                  v-model="data.testPackageName"
                                  maxlength="200"
                        />
                    </span>
                    <span>
                        <!--<el-button type="primary" size="small" class="el-icon-edit" @click="(ev) => nodeEdit(ev, store, data)"  circle></el-button>-->
                        <el-button type="warning" size="small" class="el-icon-delete" @click="() => remove(node, data)"  circle></el-button>
                        <el-button type="success" size="small" class="el-icon-plus" @click="() => append(data)"  circle></el-button>
                    </span>
                  </span>
            </el-tree>
        </el-row>
    </el-form-item>
</template>

<script>
    import {validatenull} from "@/util/validate";
    let id = 1000;
    export default {
        name: "testpackage",
        props:['treeData','deleteIds'],
        data() {
            return {
                filterText: '',
                tree_key: 0,
                form: {id: '', testPackageName: '增加节点', isEdit: true, children: []}
            }
        },
        watch: {
            filterText(val) {
                this.$refs.tree.filter(val);
            },
            treeData(val) {
                this.filterText = '';
                this.$emit('update:treeData',this.treeData);
            },
            deleteIds(val) {
                this.$emit('update:deleteIds',this.deleteIds);
            }
        },
        created() {
        },
        methods: {
            filterNode(value, data) {
                if (!value) return true;
                return data.testPackageName.indexOf(value) !== -1;
            },
            addTestItem() {   //确定
                const testItem = {_id: id++, id: '', testPackageName: '', isEdit: false, children: []}
                this.treeData.push(testItem)
                this.add_question_flag = false
            },
            // 增加
            append(data) {
                // var maxid = '20'
                //新增数据
                const testItem = {_id: id++, id: '', testPackageName: '', isEdit: true, children: []}
                data.children.push(testItem)
                if (!node.expanded) {
                    node.expanded = true
                }
            },
            // 节点删除
            remove(node, data) {
                if(!validatenull(data.children)){
                    this.$confirm(this.$t('operation.confirmDeleteRoot'), this.$t('tip'), {
                        confirmButtonText:  this.$t('submitText'),
                        cancelButtonText: this.$t('cancelText'),
                        type: 'warning'
                    }).then(() => {
                        this.removeNode(node,data)
                    }).catch(() => {
                        return;
                    });
                }else{
                    if(!validatenull(data.id)){
                        this.deleteIds.push(data.id);
                    }
                    this.removeNode(node,data)
                }
            },
            removeNode(node, data) {
                const parent = node.parent
                const children = parent.data.children || parent.data
                const index = children.findIndex(d => d._id === data._id)
                children.splice(index, 1)
            }
        }
    }
</script>

<style >
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
    }
    .el-tree-node__content {
        height: 34px!important;
    }
</style>
