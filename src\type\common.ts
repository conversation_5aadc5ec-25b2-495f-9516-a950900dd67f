/* 枚举值参数 */
export interface Ienum {
  colName: string
  tableName: string
}

/* 分页参数 */
export interface IPage {
  pageNum: number
  pageRow: number
  pageTotal?: number
}

/* 请求接口 */
export interface IRequest extends IPage {
  name: string
}
/**
 * 请求方式
 */
export type RequestMethod = 'get' | 'post' | 'put' | 'delete'
/**
 * 请求配置
 */
export interface RequestOptions {
  url: string
  method: RequestMethod
  params?: Record<string, any>
  data?: Record<string, any>
  headers?: Record<string, string>
  [key: string]: any
  
}
/**
 * 公共请求响应
 */
export interface CommonResponse {
  code: number
  status: number
  msgCode: string
  msgArgs: any[]
  success: boolean
  data: string
  msg: string
}
