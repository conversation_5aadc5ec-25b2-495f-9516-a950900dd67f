[33mcommit 7834de01ba034b29e18033e79c4eecdfa27be67a[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32muat[m[33m, [m[1;31morigin/uat[m[33m)[m
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Fri May 23 16:52:47 2025 +0800

    激活sentry

[33mcommit 71cf1e6bbd53a7848555baf84f1eb826de5e269e[m
Merge: 7cc28c1 7576416
Author: igniting <<EMAIL>>
Date:   Wed May 21 15:22:01 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 75764163916f10c916029eb51c10680dff45c9ea[m
Merge: 1275cb0 bbb21cb
Author: 商朝 <<EMAIL>>
Date:   Wed May 21 15:20:47 2025 +0800

    Accept Merge Request #237: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/237?initial=true

[33mcommit bbb21cbe3b64eac9bae9071f2c454ef2c711d723[m
Merge: 14f77d2 a4558d0
Author: yuzihan <<EMAIL>>
Date:   Wed May 21 15:02:08 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 14f77d29476b1cb66bdbe745d441f08cb987fcc4[m
Author: yuzihan <<EMAIL>>
Date:   Wed May 21 15:02:00 2025 +0800

    fix：样式调整

[33mcommit 1275cb069d7ffe6eaf11e200e810a682c6ef83e3[m
Merge: bd9587d a4558d0
Author: 商朝 <<EMAIL>>
Date:   Wed May 21 14:56:05 2025 +0800

    Accept Merge Request #236: (dev -> dev-build)
    
    Merge Request: fix: Adjust sidebar positioning in detailedArticle.vue based on toolbar scroll position
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/236?initial=true

[33mcommit a4558d01b0624408f03f3c62dba782c31f91a3db[m
Author: shangchao <<EMAIL>>
Date:   Wed May 21 14:49:40 2025 +0800

    fix: Adjust sidebar positioning in detailedArticle.vue based on toolbar scroll position

[33mcommit 7cc28c144bd2eac04222566a29dc1ecb8fddd837[m
Merge: bd99c57 bd9587d
Author: igniting <<EMAIL>>
Date:   Wed May 21 14:29:38 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit bd9587d376eff1a2d8d0e2fd6dc426ed42742abe[m
Merge: 6bd72d8 6356280
Author: shangchao <<EMAIL>>
Date:   Wed May 21 14:29:02 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 6bd72d8259573bab077d46b432860846bbcda4d5[m
Author: shangchao <<EMAIL>>
Date:   Wed May 21 14:28:59 2025 +0800

    refactor: Update logo component to use router-link for navigation and clean up unused setup function

[33mcommit 6356280b6d90d88d4a012e9fc5e74531e3c787c7[m
Merge: 4fae8b5 cfcccc4
Author: 商朝 <<EMAIL>>
Date:   Wed May 21 14:20:38 2025 +0800

    Accept Merge Request #235: (dev -> dev-build)
    
    Merge Request: refactor: Comment out unused CSS styles in reset.scss and adjust margin in Layout.vue for improved layout
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/235

[33mcommit cfcccc43c727d8bca37b573bc77e0b1a999889b8[m
Merge: 7403e78 4fae8b5
Author: 商朝 <<EMAIL>>
Date:   Wed May 21 14:20:34 2025 +0800

    Merge branch refs/heads/dev-build into refs/heads/dev

[33mcommit 7403e78c5a8386e709e6c478b1f848293a467792[m
Author: shangchao <<EMAIL>>
Date:   Wed May 21 14:18:41 2025 +0800

    refactor: Comment out unused CSS styles in reset.scss and adjust margin in Layout.vue for improved layout

[33mcommit 4fae8b50304f881643a3fdab36786de1aa46e971[m
Author: shangchao <<EMAIL>>
Date:   Mon May 19 16:28:42 2025 +0800

    style: Restore font family styles in element.scss for consistent typography

[33mcommit 14c3a1b95666022924d808b9ab8b705a06a28c06[m
Author: shangchao <<EMAIL>>
Date:   Mon May 19 16:11:18 2025 +0800

    chore: Remove unused font files and comment out font family styles in element.scss

[33mcommit 0ff5d901114a5da5c1ce2db8b33da1415522dc50[m
Author: shangchao <<EMAIL>>
Date:   Mon May 19 16:10:56 2025 +0800

    style: Add commented-out font family styles in element.scss for future use

[33mcommit 941faac7ed45bc3e89a97acc9b627c6d0453e883[m
Author: shangchao <<EMAIL>>
Date:   Mon May 19 15:48:58 2025 +0800

    feat: Add new font files and update logo component for improved styling and functionality

[33mcommit bd99c5783626be649f170481299348caf1641f61[m
Merge: 87ce274 6181462
Author: igniting <<EMAIL>>
Date:   Mon May 19 15:22:39 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 6181462068614be73caa79820b015846ce770285[m
Author: shangchao <<EMAIL>>
Date:   Mon May 19 15:19:19 2025 +0800

    refactor: Update API endpoints in register.ts for consistency and remove unused functions in Header.vue and user.ts; add service unit tool for querying service units

[33mcommit 3ed7eb20d060abf754de56eeaf0740e09994c2c3[m
Merge: 456f1a2 79dffe8
Author: igniting <<EMAIL>>
Date:   Mon May 19 15:04:46 2025 +0800

    Merge branch 'dev' into dev-build

[33mcommit 79dffe8d3cce72a99403ed7c4f4b230bf4574036[m
Author: shangchao <<EMAIL>>
Date:   Mon May 19 14:51:31 2025 +0800

    feat: Add API service for registration and enhance UI styles with new service unit dropdown

[33mcommit 87ce274c462910bc59237f44da1bb661b9c7b7ce[m
Merge: f7bcbff 456f1a2
Author: igniting <<EMAIL>>
Date:   Fri May 16 16:22:56 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 456f1a26d3d318e375e6f4001b6f3ff0ac1d9a94[m
Author: shangchao <<EMAIL>>
Date:   Fri May 16 16:20:39 2025 +0800

    refactor: Simplify layout initialization by removing unused login logic and integrating language settings

[33mcommit 8273911bd421cae5647989d2e10b972b94da2a0d[m
Merge: d0abf57 5be5a31
Author: 商朝 <<EMAIL>>
Date:   Fri May 16 16:14:48 2025 +0800

    Accept Merge Request #234: (dev -> dev-build)
    
    Merge Request: Merge branch refs/heads/dev-build into refs/heads/dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/234?initial=true

[33mcommit 5be5a3106b1a159045aae20f0ee7efa983b1a245[m
Merge: 924a496 d0abf57
Author: 商朝 <<EMAIL>>
Date:   Fri May 16 16:11:05 2025 +0800

    Merge branch refs/heads/dev-build into refs/heads/dev

[33mcommit 924a4967c8c4d7ce7d32bdcc6569d8faede1dd68[m
Author: shangchao <<EMAIL>>
Date:   Fri May 16 15:58:10 2025 +0800

    fix: Remove unused external link indicator in navigationBar.vue

[33mcommit d0abf5706957966ccfe52b3d70fa77628436f874[m
Author: liuzeyu <<EMAIL>>
Date:   Fri May 16 15:20:46 2025 +0800

    fix: Add spacing before container styles in contentPage.vue and favPage.vue for improved layout

[33mcommit b233d9c3375f73aa9a943a48649ff9749ad77476[m
Merge: 81e2785 d571495
Author: liuzeyu <<EMAIL>>
Date:   Fri May 16 15:08:54 2025 +0800

    Merge branch 'dev' into dev-build

[33mcommit 81e2785e26aef6fe4afe55c642eef4b5fb1c7429[m
Merge: 2ebdd6c 197e680
Author: liuzeyu <<EMAIL>>
Date:   Fri May 16 14:14:38 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit d57149592d1e29775e3a3c9fab0145add56f8a58[m
Merge: 6c4a3fe 6afac95
Author: yuzihan <<EMAIL>>
Date:   Fri May 16 13:05:57 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 6c4a3fe11193c9dee487da15ee58d488ee8d9bfc[m
Author: yuzihan <<EMAIL>>
Date:   Fri May 16 13:05:50 2025 +0800

    fix:样式调整

[33mcommit f7bcbff904aff34899fed6f0b78dc73c6e3bf0a3[m
Author: igniting <<EMAIL>>
Date:   Thu May 15 18:27:29 2025 +0800

    发版

[33mcommit ac2e6f8619c98843cdf3218b58ebd7044ad64ce1[m
Merge: 5c4a34d 197e680
Author: igniting <<EMAIL>>
Date:   Thu May 15 18:15:11 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 197e680c854d374c40ce307f4d4cc48a3e1f16cf[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 18:13:35 2025 +0800

    fix: Comment out external link indicator in navigationBar.vue for clarity

[33mcommit d9d2782f1e57b5e636b3301624042ad1e321d7dc[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 18:12:54 2025 +0800

    fix: Update icon paths in navigationBar.vue to reflect new directory structure

[33mcommit 6afac95ef240b9143a0434a233656b1f674409cb[m
Author: liuzeyu <<EMAIL>>
Date:   Thu May 15 17:51:23 2025 +0800

    fix: 重构 Layout.vue 和 detailedArticle.vue 以提高代码一致性和可读性

[33mcommit 5c4a34db082373e43d1d29844f43494e17e1cd6a[m
Merge: bcbd6da b026f15
Author: igniting <<EMAIL>>
Date:   Thu May 15 17:35:57 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit b026f15e4d863b2a19bb80beb77ffdf367c8b480[m
Merge: 2df44a6 856600e
Author: igniting <<EMAIL>>
Date:   Thu May 15 17:35:34 2025 +0800

    Merge branch 'dev' into dev-build

[33mcommit 856600efaa4bcc96c99b9e5d5237b971483c1265[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 17:33:40 2025 +0800

    fix: Update navigationBar.vue to use loose equality for isOpen checks and clean up debugger statements

[33mcommit f409c745add5f4604c2c724ebe899ae472a571cf[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 17:14:15 2025 +0800

    fix: Update navigationBar.vue to correct icon labels and comment out unused external link indicator

[33mcommit afbd182a7d67fd728cf220602451281cc472f38e[m
Merge: 9df91f4 ff2e156
Author: yuzihan <<EMAIL>>
Date:   Thu May 15 17:03:10 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 9df91f45db9ca4e03ba8cc1a73eeda6c18a623cd[m
Merge: 7cba8dc 09254ca
Author: yuzihan <<EMAIL>>
Date:   Thu May 15 17:03:02 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit bcbd6dab6d4bcd70bb39e7f5fa5950dec4007748[m
Merge: 93bbdd7 2df44a6
Author: igniting <<EMAIL>>
Date:   Thu May 15 16:57:57 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 2df44a66f12066789eeb91bb2f8b1949e9b9880c[m
Merge: d494fab ff2e156
Author: 商朝 <<EMAIL>>
Date:   Thu May 15 16:56:37 2025 +0800

    Accept Merge Request #230: (dev -> dev-build)
    
    Merge Request: fix: Clean up debugger statements and comment out unused CSS styles in navigationBar.vue
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/230?initial=true

[33mcommit ff2e1568b5a353a3418babf7d85b6dda8e64608a[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 16:56:01 2025 +0800

    fix: Clean up debugger statements and comment out unused CSS styles in navigationBar.vue

[33mcommit d494fab6dfb5ff915ab4c5d382943c02e4e1d674[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 16:42:40 2025 +0800

    fix: Update navigation URLs in contentPage, favPage, and pageCreate to use the correct knowledge path

[33mcommit 7cba8dca9673cc3007010af616713c25d1ae9ad5[m
Author: yuzihan <<EMAIL>>
Date:   Thu May 15 16:41:18 2025 +0800

    fix：样式调整

[33mcommit 93bbdd705164e7ab186197b7f1badefd79df49bc[m
Merge: e44b376 5ab2d35
Author: igniting <<EMAIL>>
Date:   Thu May 15 16:27:49 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 5ab2d350e3733af68993b2e18a1c66e1a93b169f[m
Merge: 8e6896c 09254ca
Author: igniting <<EMAIL>>
Date:   Thu May 15 16:27:26 2025 +0800

    Merge branch 'dev' into dev-build

[33mcommit e44b376fe5fa64809765fd11a7d9a7260c2e43be[m
Merge: 60427dc 8e6896c
Author: igniting <<EMAIL>>
Date:   Thu May 15 16:17:09 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 8e6896c6b57c5e74af75f7464334b6257d615c22[m
Merge: e7a3ab2 c3d6b8d
Author: igniting <<EMAIL>>
Date:   Thu May 15 16:14:36 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 60427dcd11d971df8948f2f04e67d15d03882daa[m
Merge: 83601a5 e7a3ab2
Author: igniting <<EMAIL>>
Date:   Thu May 15 15:58:54 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit e7a3ab2603733ddc963f895ddd405d27c2f028b5[m
Merge: dd20365 f74b826
Author: igniting <<EMAIL>>
Date:   Thu May 15 15:57:54 2025 +0800

    Merge branch 'dev' into dev-build

[33mcommit 09254cac64120015cf5395fddfa38f0a2a033385[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 15:54:48 2025 +0800

    fix: Add debugger statement in handleMenuSelect for paymentFlag logic troubleshooting

[33mcommit f74b826eb8ff2042365d7832313bcb43dfc798af[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 15:47:23 2025 +0800

    feat: Enhance navigation and content handling with external link support and improved UI elements

[33mcommit c3d6b8d6680406b57c2dfef97043e426fb516626[m
Author: shangchao <<EMAIL>>
Date:   Thu May 15 14:11:52 2025 +0800

    fix: update image paths in contentPage, favPage, and tabsPage to use the correct directory structure

[33mcommit 1bc778317ea613f5d4f76a191b32be6eb9c56fe3[m
Author: yuzihan <<EMAIL>>
Date:   Thu May 15 13:01:22 2025 +0800

    fix:样式调整

[33mcommit 83601a503e071e1ac24d0b2c6802b8b23e931948[m
Merge: bcde415 dd20365
Author: igniting <<EMAIL>>
Date:   Thu May 15 11:24:50 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit dd2036525ba2ef81999e2322949382bcef57007a[m
Merge: 89ea925 817b985
Author: igniting <<EMAIL>>
Date:   Thu May 15 11:21:25 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 817b9859d84232bccd7b240c209b1b32c736891a[m
Merge: 9416dae bb72c0d
Author: 商朝 <<EMAIL>>
Date:   Thu May 15 11:20:48 2025 +0800

    Accept Merge Request #228: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/228

[33mcommit bb72c0d10d537360f58498b3effcae3dec09e98d[m
Merge: e0f4e6e 9416dae
Author: 商朝 <<EMAIL>>
Date:   Thu May 15 11:20:43 2025 +0800

    Merge branch refs/heads/dev-build into refs/heads/dev

[33mcommit e0f4e6e46ce783150cb3ca5d387fb59f44fb3aaa[m
Merge: b632b75 59d86e8
Author: yuzihan <<EMAIL>>
Date:   Thu May 15 11:02:07 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit b632b750c9be6904efd43304c34be84d577c0758[m
Author: yuzihan <<EMAIL>>
Date:   Thu May 15 11:01:46 2025 +0800

    fix:样式调整

[33mcommit 59d86e8bb41ced30cd3b9639d8949e0df38a5a2e[m
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 18:54:50 2025 +0800

    fix: 增强 detailedArticle.vue 以改进内容解码，添加额外的数据字段，并更新收藏状态处理。

[33mcommit bcde41504feb8ab6870caaab84085f61d84da66b[m
Merge: 6590c76 89ea925
Author: igniting <<EMAIL>>
Date:   Wed May 14 18:28:42 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 89ea925bff7f0e31bda8b44c62203ec8ea48eef0[m
Merge: cfadd5c 9416dae
Author: igniting <<EMAIL>>
Date:   Wed May 14 18:28:35 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 9416dae5259adcfa95056c4b22e00914a6f28ef3[m
Merge: 07b6e61 738e45c
Author: 刘泽宇- <<EMAIL>>
Date:   Wed May 14 18:28:14 2025 +0800

    Accept Merge Request #227: (dev -> dev-build)
    
    Merge Request: fix：样式调整
    
    Created By: @刘泽宇-
    Accepted By: @刘泽宇-
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/227?initial=true

[33mcommit 738e45cbccf9324c4d1dac43d507715c3f60d44b[m
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 18:22:24 2025 +0800

    fix：样式调整

[33mcommit 9947c19b4b6583285602153f7e1e11892382a1f5[m
Merge: 34a8ea1 3c9e0f8
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 18:21:44 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 34a8ea193652d2d75f3dcc77fe6693de266e97f1[m
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 18:21:38 2025 +0800

    fix：样式调整

[33mcommit 3c9e0f85c98a9abf0f69ff173b00bf924c06da0b[m
Merge: 525cc95 dcb5c21
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 18:11:27 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 525cc95cabebe48cc9ce48f60edd34f81fca7c32[m
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 18:10:59 2025 +0800

    fix: 优化 detailedArticle.vue 中附件显示逻辑，确保仅在有有效链接时显示下载文本。

[33mcommit dcb5c21c64fbea83f7d15ec9aaec9bfbace34b3e[m
Merge: 461eca5 61aca45
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 18:06:13 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 461eca5f2bd8af2c6f60843f43afa8854a5eb0d5[m
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 18:06:05 2025 +0800

    fix：样式调整

[33mcommit 61aca456a3b6b60dd0cfdf3f9b4545d529e9ea3d[m
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 17:52:57 2025 +0800

    fix: 更新 detailedArticle.vue 以修正标题绑定并改善格式。

[33mcommit 6590c76ffb06c341f03b11190267dc38e5b3a409[m
Merge: e1d6e3a cfadd5c
Author: igniting <<EMAIL>>
Date:   Wed May 14 17:36:30 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit cfadd5cb381bcf5adfff31116baae3ff99f7f4f9[m
Merge: 2509ac2 07b6e61
Author: igniting <<EMAIL>>
Date:   Wed May 14 17:36:21 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 07b6e613e6a3562a5e6f59e024ced81c8bd3599b[m
Merge: eea7660 1f3e2dd
Author: 商朝 <<EMAIL>>
Date:   Wed May 14 17:22:38 2025 +0800

    Accept Merge Request #226: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/226?initial=true

[33mcommit 1f3e2dd15e6ba9b8ebf8e7c5b59e5481a8ffef07[m
Merge: 0d89d37 5834e8d
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 16:35:42 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 0d89d37f2888a2227cd7b77e4b0847d40dc966b9[m
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 16:35:40 2025 +0800

    fix: 更新 en-US 和 zh-CN 地区的生效时间和发布时间的本地化设置。

[33mcommit 5834e8df0ee6485829458e070e85a4ed06f7a4b1[m
Merge: 02433b6 7a9ddcc
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 16:22:54 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 02433b6e331329efe7bd6751f8eef33c3eb2f679[m
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 16:22:47 2025 +0800

    fix：样式调整

[33mcommit eea7660dd3c9d16416c11e54898c3d218cdcf3bb[m
Merge: 35128d4 7a9ddcc
Author: 商朝 <<EMAIL>>
Date:   Wed May 14 16:10:14 2025 +0800

    Accept Merge Request #225: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/225?initial=true

[33mcommit 7a9ddccb79cf3be745443b778c1db7588527228e[m
Merge: 4ddfb9d a74d01a
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 16:07:51 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 4ddfb9d7ce13348e4fb7d5fe799ac4802cd73032[m
Author: liuzeyu <<EMAIL>>
Date:   Wed May 14 16:07:49 2025 +0800

    fix:更新 en-US 和 zh-CN 地区的生效时间和发布时间的本地化设置

[33mcommit a74d01a41819fa1efacc269f77dd30ff11177d8c[m
Merge: 4db814e 0bbc15d
Author: yuzihan <<EMAIL>>
Date:   Wed May 14 13:48:25 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 4db814e6b6f1698e0359f28fd73d2c3efd4639a8[m
Author: yuzihan <<EMAIL>>
Date:   Tue May 13 16:52:08 2025 +0800

    fix：样式调整

[33mcommit 0bbc15d0c2b7612f5ae8c27f2e2d2b0e205039c0[m
Merge: ef972ff c6f8d97
Author: liuzeyu <<EMAIL>>
Date:   Tue May 13 12:02:44 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ef972ffba4b2ab792a4339d29fde01f39f895f66[m
Author: liuzeyu <<EMAIL>>
Date:   Tue May 13 12:02:00 2025 +0800

    refactor: 增强状态处理并在detailedArticle.vue中更新关键字modelCode

[33mcommit e1d6e3a14e1d905d087d827715eccf74274a2d76[m
Merge: c97c441 2509ac2
Author: igniting <<EMAIL>>
Date:   Mon May 12 18:41:36 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 2509ac22a475dac8c18ef7154a4b478ff064e60c[m
Merge: e946ce0 35128d4
Author: igniting <<EMAIL>>
Date:   Mon May 12 18:41:30 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 35128d445f222b6ea96c90d49b9a91d799185a7e[m
Merge: 449aafb c6f8d97
Author: 商朝 <<EMAIL>>
Date:   Mon May 12 18:40:38 2025 +0800

    Accept Merge Request #220: (dev -> dev-build)
    
    Merge Request: fix: update status translation handling in detailedArticle.vue to ensure proper localization
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/220?initial=true

[33mcommit c6f8d9726894a6b33a7dd19a41d0ca2f2578b6d1[m
Author: shangchao <<EMAIL>>
Date:   Mon May 12 18:39:32 2025 +0800

    fix: update status translation handling in detailedArticle.vue to ensure proper localization

[33mcommit c97c441ac18cc74be18908e73113cc9cd05da8e7[m
Merge: c2fa97d e946ce0
Author: igniting <<EMAIL>>
Date:   Mon May 12 18:30:21 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit e946ce048fd6f4d2ba8668ff3817b09253422d2c[m
Merge: c244943 449aafb
Author: igniting <<EMAIL>>
Date:   Mon May 12 18:30:14 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 449aafb6cbf2ae028e410792950f04e77948d647[m
Merge: 712146b 4f8a7e2
Author: 商朝 <<EMAIL>>
Date:   Mon May 12 18:28:44 2025 +0800

    Accept Merge Request #219: (dev -> dev-build)
    
    Merge Request: fix: handle decoding errors in detailedArticle.vue and clean up whitespace in knowledgeBaseHome.vue
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/219?initial=true

[33mcommit 4f8a7e24e7e88870cd83dc3163f2c027f76b9db4[m
Author: shangchao <<EMAIL>>
Date:   Mon May 12 18:28:08 2025 +0800

    fix: handle decoding errors in detailedArticle.vue and clean up whitespace in knowledgeBaseHome.vue

[33mcommit 712146bc23979f0470d6a607ff0e4bae478f9cf4[m
Merge: dc62e55 bc354e5
Author: 商朝 <<EMAIL>>
Date:   Mon May 12 17:28:38 2025 +0800

    Accept Merge Request #218: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/218?initial=true

[33mcommit bc354e53d2e1d4b84ad8ef9a57151b4f0830a59b[m
Merge: 48598f0 9a592c7
Author: liuzeyu <<EMAIL>>
Date:   Mon May 12 17:06:18 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit c2fa97d1bbb51266eca8fd92927864ea6872c21d[m
Merge: ab35ce9 c244943
Author: igniting <<EMAIL>>
Date:   Mon May 12 17:02:49 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 48598f030d8b12f978a2f5598ac6bacfd97d40de[m
Author: liuzeyu <<EMAIL>>
Date:   Mon May 12 17:02:23 2025 +0800

    refactor:  文章页面属性名称多语言适配

[33mcommit c2449434bc0a45abe157b85e1698135330e45fd3[m
Merge: 64ae8a6 dc62e55
Author: igniting <<EMAIL>>
Date:   Mon May 12 17:02:09 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit dc62e556d7a0d05f1664f79cfb8ee0d62cacf9d2[m
Merge: 9c417e2 c90a889
Author: 商朝 <<EMAIL>>
Date:   Mon May 12 17:01:27 2025 +0800

    Accept Merge Request #217: (dev -> dev-build)
    
    Merge Request: fix: update status translation handling in detailedArticle.vue
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/217

[33mcommit c90a8894ed91a934720961407e9c513100916891[m
Merge: 9a592c7 9c417e2
Author: 商朝 <<EMAIL>>
Date:   Mon May 12 17:01:22 2025 +0800

    Merge branch refs/heads/dev-build into refs/heads/dev

[33mcommit 9a592c74dd75861ca3abe9556ce7280cf0b4e019[m
Author: shangchao <<EMAIL>>
Date:   Mon May 12 16:58:59 2025 +0800

    fix: update status translation handling in detailedArticle.vue

[33mcommit 24710402b1d48a87f8bf56a2461073273a5e265a[m
Merge: c4f8880 ee7ef84
Author: yuzihan <<EMAIL>>
Date:   Mon May 12 16:26:39 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit c4f88805dc03d591f4112ddebbe645b520d7d045[m
Author: yuzihan <<EMAIL>>
Date:   Mon May 12 16:26:33 2025 +0800

    fix：样式调整

[33mcommit ee7ef8413c8f3886f38d612e53404695c8c8e3f0[m
Author: shangchao <<EMAIL>>
Date:   Mon May 12 15:47:39 2025 +0800

    style: adjust layout and styling in knowledgeBaseHome.vue

[33mcommit 5fa7c0bf6e8e1e6253aefb372a8119837d3cfafa[m
Author: liuzeyu <<EMAIL>>
Date:   Fri May 9 16:35:50 2025 +0800

    style: improve layout and styling in detailedArticle.vue and pageCreate.vue

[33mcommit c6438409876bc21bb36b7c94fb861fe0d3aba7b1[m
Author: liuzeyu <<EMAIL>>
Date:   Fri May 9 16:04:26 2025 +0800

    refactor: clean up articleForm.vue template and improve code readability

[33mcommit ab35ce9c2f7207d87eed95e19c3d8f8e3360b111[m
Merge: f0cd82d 64ae8a6
Author: igniting <<EMAIL>>
Date:   Fri May 9 15:50:06 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 64ae8a6a0517264dfbbcfde79edccbafd9f66e37[m
Merge: d91399b 9c417e2
Author: igniting <<EMAIL>>
Date:   Fri May 9 15:48:39 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 9c417e2cc15e87b1be684c4ed58a7cdf4e6c3234[m
Author: shangchao <<EMAIL>>
Date:   Fri May 9 15:39:32 2025 +0800

    feat: import redirect utility in Header component

[33mcommit f0cd82d54aa3b7926f3eae10365796e26461d3a1[m
Merge: 70ce634 d91399b
Author: igniting <<EMAIL>>
Date:   Fri May 9 15:04:46 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit d91399b120942b1d532fed9d0e097a7384557142[m
Merge: b72bc36 ff525c3
Author: igniting <<EMAIL>>
Date:   Fri May 9 15:04:40 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit ff525c39e8c9e81c40f4357af7e4d52d2c4c35b4[m
Merge: 777f2f5 b91a1ae
Author: 商朝 <<EMAIL>>
Date:   Fri May 9 15:04:14 2025 +0800

    Accept Merge Request #208: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/208?initial=true

[33mcommit b91a1aee493842d0e41c550187124be7a015aa58[m
Merge: 388062b 663db7d
Author: liuzeyu <<EMAIL>>
Date:   Fri May 9 14:58:54 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 388062b4336ae0dac8d60beaaeb721a28b218964[m
Author: liuzeyu <<EMAIL>>
Date:   Fri May 9 14:58:52 2025 +0800

    feature: 标签hover效果

[33mcommit 663db7d4e52cda067b8e0877381939d8681d971c[m
Merge: ba79f34 953bc92
Author: yuzihan <<EMAIL>>
Date:   Fri May 9 14:51:42 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ba79f3423c9de067ebe6e130014ad95fcc104594[m
Author: yuzihan <<EMAIL>>
Date:   Fri May 9 14:51:36 2025 +0800

    fix：样式调整

[33mcommit 70ce634d8dccd002950aa486cb344ccab8e46b28[m
Merge: 3b026f5 b72bc36
Author: igniting <<EMAIL>>
Date:   Fri May 9 12:54:42 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit b72bc360d63263fb35a13ce9a089c8a1e444a0fd[m
Merge: c9e6071 777f2f5
Author: igniting <<EMAIL>>
Date:   Fri May 9 12:54:35 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 777f2f5f74bbec6452f4e7ef314b4258780bb95e[m
Author: shangchao <<EMAIL>>
Date:   Fri May 9 11:47:38 2025 +0800

    fix: update API endpoint URLs to remove redundant 'sgsmart' prefix for consistency

[33mcommit 3b026f5b66821008a4c35f8e762216f7c3bdf201[m
Merge: c34e92e c9e6071
Author: igniting <<EMAIL>>
Date:   Thu May 8 17:37:25 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit c9e6071b6ea0f6f93e17ce3feb4a03c5545d1e65[m
Merge: 71e4878 7134508
Author: igniting <<EMAIL>>
Date:   Thu May 8 17:36:53 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 7134508f6a399bda4834be835eaf5f812f2ace2e[m
Author: shangchao <<EMAIL>>
Date:   Thu May 8 17:36:01 2025 +0800

    fix: update PDF iframe source URLs to use relative paths for improved consistency

[33mcommit fcf519d893f47d605178f4cc6bebfe49cd3f903d[m
Author: shangchao <<EMAIL>>
Date:   Thu May 8 17:33:35 2025 +0800

    fix: update API endpoints to include a cache-busting hash parameter for all requests

[33mcommit c34e92e8e6fd632ec36399939355cc7576cef098[m
Merge: bc38fd2 71e4878
Author: igniting <<EMAIL>>
Date:   Thu May 8 16:20:58 2025 +0800

    Merge branch 'dev-build' into uat

[33mcommit 71e4878ce85f55f9a9fae25bba32e45dfed2471a[m
Merge: 463f862 fb07c84
Author: igniting <<EMAIL>>
Date:   Thu May 8 16:20:42 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 953bc92dfb4872154be6a187384fa9b62c3cb340[m
Author: liuzeyu <<EMAIL>>
Date:   Thu May 8 16:16:52 2025 +0800

    feature:标签样式微调

[33mcommit fb07c842d95621e054f9fe798f9f0e17501ca734[m
Merge: 8c12ead fd98745
Author: 商朝 <<EMAIL>>
Date:   Thu May 8 16:15:00 2025 +0800

    Accept Merge Request #207: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/207?initial=true

[33mcommit fd9874544abe931f8b65b179d7093bfbee01f72d[m
Merge: 8d054a1 08da474
Author: liuzeyu <<EMAIL>>
Date:   Thu May 8 15:58:47 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 8d054a1a5e7ca4efe032987f2580af4d896bef88[m
Author: liuzeyu <<EMAIL>>
Date:   Thu May 8 15:58:43 2025 +0800

    feature:样式调整

[33mcommit 08da4741d77e9c231b0c2733671a6fe8f376987f[m
Author: yuzihan <<EMAIL>>
Date:   Thu May 8 15:46:37 2025 +0800

    fix: 底部信息行字段调整

[33mcommit bc38fd2de67ffbbbd9b89ae08b9c76e1d35489ab[m
Merge: 1d2f520 463f862
Author: igniting <<EMAIL>>
Date:   Thu May 8 10:56:18 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit 463f862a68686d5b7e60e556fa96d82d28359782[m
Merge: aa43a3e 8c12ead
Author: igniting <<EMAIL>>
Date:   Thu May 8 10:56:00 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit 8c12eaddc69f4b79816c25cb6e3d3c6ebe0acdb3[m
Author: shangchao <<EMAIL>>
Date:   Thu May 8 10:54:14 2025 +0800

    fix: update file upload response handling in articleForm.vue to correctly insert uploaded media URLs

[33mcommit 1d2f520392bbe9e9e44c9d73a59db43dea0c2688[m
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 16:32:36 2025 +0800

    1

[33mcommit cb8d45c369c0b749c57c39cee36d17d6ba734661[m
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 16:11:28 2025 +0800

    1

[33mcommit 9559d55c0568676e8c7f27ba01062e0052ffea80[m
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 16:01:23 2025 +0800

    1

[33mcommit 8e6c562a647ae33a585a2429cbb01e810d3de119[m
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:52:16 2025 +0800

    1

[33mcommit 8260c263f603914839f97a06daa2665e001def41[m
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:39:03 2025 +0800

    1

[33mcommit f75443f64c19321a9adcefed2634eccde3ddc9cf[m
Merge: df562da aa43a3e
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:36:46 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit aa43a3e3c2e10b8395238a034528deebdbcf3559[m
Merge: ebde812 f9dbdc9
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:36:37 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build

[33mcommit f9dbdc920aca469da0eb8ce91c47eb52f6ab91f3[m
Merge: e81b702 55bbfd8
Author: 商朝 <<EMAIL>>
Date:   Mon Apr 28 15:35:48 2025 +0800

    Accept Merge Request #206: (dev -> dev-build)
    
    Merge Request: fix：导航栏权限设置
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/206?initial=true

[33mcommit df562dae134b1ee5ce11d33671d3f50a1787cf9e[m
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:31:12 2025 +0800

    1

[33mcommit 55bbfd86e9036e2ca9286de111d860e516eaaf41[m
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 28 15:30:27 2025 +0800

    fix：导航栏权限设置

[33mcommit bbf1bab8c5a6e2f05603eaa599b8942e804a97ff[m
Merge: 12135ef ebde812
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:26:30 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit ebde8122da87b938746c74aaa4184fb122ed687c[m
Merge: b19944a e81b702
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:26:11 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build
    
    # Conflicts:
    #       src/store/modules/user.ts

[33mcommit e81b702740cdcd47fac0bd10b3698b3c11eba598[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 28 15:24:54 2025 +0800

    refactor: remove commented-out logout function from user module for cleaner code

[33mcommit 12135ef24c6fe0388863381d6d12a64ca69b0949[m
Merge: 8025b01 b19944a
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:21:55 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit b19944a5b757f88dba0ae9b9f60e977353d19ef9[m
Merge: 1a8f5ee b89f322
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 15:21:31 2025 +0800

    Merge branch 'dev-build' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev-build
    
    # Conflicts:
    #       src/components/Header/index.vue
    #       src/components/Sidebar/MenuNode.vue
    #       src/store/modules/user.ts

[33mcommit b89f32215d56e130d0168c521abd31c3fdc8596a[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 28 15:19:28 2025 +0800

    refactor: update API endpoints to remove 'cs-api' prefix for consistency across file handling functions

[33mcommit 242559cdd4f586ded8acae1e86bb7bf745b00802[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 28 15:02:26 2025 +0800

    feature: 增加用户信息计算属性并完善菜单权限判断逻辑

[33mcommit 8025b01be9059eda84f99e4dffbb8c22c449f671[m
Merge: 21bfa41 1a8f5ee
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 14:16:17 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit 1a8f5ee7ac528b695741446f83dd2e8b7095fc0a[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 28 14:14:39 2025 +0800

    refactor: update API endpoints to remove 'cs-api' prefix for consistency and add new register API module

[33mcommit 21bfa416490cdb02bc8e99face7c832d71342a05[m
Merge: 57e8536 1dd9462
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 12:23:24 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit 1dd9462dd65ec647b8b357a162e2813fdb055bf3[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 28 12:22:30 2025 +0800

    fix: correct user menu options in Header component based on updated SGS status logic

[33mcommit 57e8536dbf0fee6c5a75b22b4971970479b64bc4[m
Merge: 24685b0 55c4a9c
Author: igniting <<EMAIL>>
Date:   Mon Apr 28 11:19:55 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit 55c4a9cb42a9228736c27167d472d005c503b678[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 28 11:17:25 2025 +0800

    feat: enhance Header component with TopServiceUnit and improve user menu options based on SGS status

[33mcommit 24685b0a63d7a85825f1244d92f4bd83c2a4e382[m
Merge: da79954 44cd949
Author: igniting <<EMAIL>>
Date:   Sun Apr 27 17:45:51 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit 44cd949204ca347d1f7bdb16c515ba3fe71fc236[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 17:45:19 2025 +0800

    fix: update window.open URLs to remove hash and ensure correct navigation for detailedArticle

[33mcommit da7995421bd25eaa6df1c43ddfc6078821483085[m
Merge: d5cf75e b7af9f3
Author: igniting <<EMAIL>>
Date:   Sun Apr 27 17:36:06 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit b7af9f30e9efa6ff46b71ef858301e53377e48c9[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 17:32:57 2025 +0800

    refactor: update routing in tabsPage.vue to use named routes and query parameters for navigation

[33mcommit 2faeb4070e955dd5fca09486706dc432e66c00d4[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 17:22:22 2025 +0800

    fix: update routing to use hash history and adjust URLs for detailedArticle navigation

[33mcommit 60d74a9270f74ddf60d891fb5e2b8a6930f8773a[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 17:12:02 2025 +0800

    fix: update URL in handlePreview function to point to the correct detailedArticle path

[33mcommit 8a72ab04de28276e68a4a11b2136ff89b4e0c0cf[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 17:08:20 2025 +0800

    refactor: enhance Sidebar component structure and improve role validation logic

[33mcommit d5cf75ec5e12172cd3822756d7db473284834ab8[m
Merge: 1145008 ca60396
Author: igniting <<EMAIL>>
Date:   Sun Apr 27 16:56:24 2025 +0800

    Merge branch 'dev-build' into test

[33mcommit ca60396db9fc9c2edfc60824e34c4937451d7c30[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 16:55:25 2025 +0800

    refactor: update API endpoints to remove 'sgsmart' prefix and adjust URLs for consistency across the application

[33mcommit 0bea3679726fc93beee123e1f73dbeaa24b61504[m
Merge: 8a8f343 ff5d16d
Author: 商朝 <<EMAIL>>
Date:   Sun Apr 27 16:42:26 2025 +0800

    Accept Merge Request #205: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/205?initial=true

[33mcommit ff5d16dca8338781b742613f6186f3d8bfcf0d5c[m
Merge: 87a1bf1 44c6f1c
Author: liuzeyu <<EMAIL>>
Date:   Sun Apr 27 16:41:55 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 87a1bf19c485ca90c0ef638b6af9a62240a41fe9[m
Author: liuzeyu <<EMAIL>>
Date:   Sun Apr 27 16:41:28 2025 +0800

    feature: 修改文章详情页的权限判断

[33mcommit 44c6f1c718a65469fc31ceba04dffd40547c2f33[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 14:39:30 2025 +0800

    feature: 添加用户VIP和内部用户标识到请求头

[33mcommit 8a8f343651639a2b0e86b243e1452cbdab45468e[m
Merge: c413572 f6154cc
Author: 商朝 <<EMAIL>>
Date:   Sun Apr 27 14:15:35 2025 +0800

    Accept Merge Request #204: (dev -> dev-build)
    
    Merge Request: feature：完善用户鉴权逻辑
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/204?initial=true

[33mcommit f6154ccff5b699845a0d2a4328c82b5d7a27b7a6[m
Author: liuzeyu <<EMAIL>>
Date:   Sun Apr 27 13:00:52 2025 +0800

    feature：完善用户鉴权逻辑

[33mcommit 2ebdd6c49963168ef61a90b07375c04b82bb9ce7[m
Author: shangchao <<EMAIL>>
Date:   Sun Apr 27 11:34:52 2025 +0800

    feat: add user information handling in detailedArticle component

[33mcommit c413572b1e9a04f6e9feee3dc76581819e8a7361[m
Merge: 425b787 726096f
Author: 商朝 <<EMAIL>>
Date:   Fri Apr 25 17:14:39 2025 +0800

    Accept Merge Request #203: (dev -> dev-build)
    
    Merge Request: fix:样式更改
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/203

[33mcommit 726096f6c91194b68795540365393a3918c82bed[m
Merge: 3842a18 425b787
Author: 商朝 <<EMAIL>>
Date:   Fri Apr 25 17:14:32 2025 +0800

    Merge branch refs/heads/dev-build into refs/heads/dev

[33mcommit 3842a18a10b2fed66f3a17755706c16f1b3ef34b[m
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 25 16:55:16 2025 +0800

    fix:样式更改

[33mcommit 6622a640d90cda703dbe8ea5561b739f392429ed[m
Merge: e220610 b61f3d4
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 25 16:44:37 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit e220610fc4cf73ab90315739b352017f7f2d4cf3[m
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 25 16:44:29 2025 +0800

    fix:样式调整

[33mcommit b61f3d48120f447ba42f79512fd24f33137c8db7[m
Author: liuzeyu <<EMAIL>>
Date:   Fri Apr 25 16:36:40 2025 +0800

    feature: 补充静态配置

[33mcommit aed51c7d3a361a4bad9389f0f01257b469f148ab[m
Author: liuzeyu <<EMAIL>>
Date:   Fri Apr 25 16:19:18 2025 +0800

    feature:修改匹配静态资源路径

[33mcommit 1b64c824ac9df915711bd08e8b777ab42982a8a5[m
Author: shangchao <<EMAIL>>
Date:   Fri Apr 25 16:02:40 2025 +0800

    feat: update routing paths and set base URL for knowledge section

[33mcommit 11450089767603ff5d88b10e2cb6bab2d71622d4[m
Merge: 425b787 649979f
Author: igniting <<EMAIL>>
Date:   Fri Apr 25 16:00:41 2025 +0800

    Merge branch 'test' of http://cndb139.apac.global.sgs.com/smart/smart-knowledge into test

[33mcommit 649979f020373d0b9c8858ce7bd99667f0879041[m
Author: Johnson <<EMAIL>>
Date:   Fri Apr 25 07:52:33 2025 +0000

    Update README.md

[33mcommit 425b78759f59752661d0d1d2b2c1a89ccdfdfbc4[m
Author: shangchao <<EMAIL>>
Date:   Fri Apr 25 15:36:19 2025 +0800

    fix: update API endpoint for token validation

[33mcommit b8d3a53717e548bb94c9e216c811432268045057[m
Merge: c044ea6 62cdc04
Author: 商朝 <<EMAIL>>
Date:   Fri Apr 25 15:10:14 2025 +0800

    Accept Merge Request #202: (dev -> dev-build)
    
    Merge Request: Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/202?initial=true

[33mcommit c044ea620774c3c42242019cb6d341f26cd83827[m
Author: shangchao <<EMAIL>>
Date:   Fri Apr 25 15:08:47 2025 +0800

    fix: update base URL in vite.config.ts and adjust router paths for knowledge section

[33mcommit 1b339b1af9977095f61aede07a7be66c85783697[m
Author: shangchao <<EMAIL>>
Date:   Fri Apr 25 15:02:43 2025 +0800

    fix: comment out base URL in vite.config.ts for deployment adjustments

[33mcommit 62cdc047d91d7b59c8ab95b5cca7ec8cdc63d0b5[m
Merge: a93aa94 555fe33
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 24 17:20:43 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit a93aa94e1d24d70a69892a71c03b72f7b5217d66[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 24 17:20:38 2025 +0800

    fix: 删除articlepage跳转冗余参数

[33mcommit c5ef6453c23368acca93f90cd76c6faee8cf2164[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 23 18:01:53 2025 +0800

    fix: simplify test script in package.json by removing unnecessary vue-tsc build step

[33mcommit a786903052bdf4394ce13811a2d47918d8b706cd[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 23 18:01:13 2025 +0800

    fix: update redirect function to accept an empty string

[33mcommit 86eec93a8931e39e3275becf24f886f7e0e6109e[m
Merge: c9df9ee 555fe33
Author: 商朝 <<EMAIL>>
Date:   Wed Apr 23 17:54:37 2025 +0800

    Accept Merge Request #201: (dev -> dev-build)
    
    Merge Request: style:page边距问题
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/201?initial=true

[33mcommit 555fe33aa57159670c6ed68f5fa6f0f3304240de[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 23 13:52:15 2025 +0800

    style:page边距问题

[33mcommit f70b0d3ac822a47dc647f0338a8118a2f4b2de6b[m
Merge: 08c3e4b b229a3d
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 22 14:57:08 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 08c3e4bebf63e790ec04d661b0ad8c584ba7ad73[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 22 14:56:59 2025 +0800

    fix：样式调整

[33mcommit c9df9eec76104f1f2ccc317536422df770453634[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 22 12:23:38 2025 +0800

    feature: add base URL for deployment, update user token validation URL, and introduce SCSS styles for consistent theming

[33mcommit b229a3d1229b625c471e02cfa0a7de83d30442dd[m
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 22 11:49:39 2025 +0800

    feature: 根据ui问题汇总调整样式

[33mcommit 4cc21348124880b41cfd19f6a48ab429ec0667f7[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 21 17:50:12 2025 +0800

    feature: 首页样式微调

[33mcommit ff27a3f70d08bc4ab0de638f8f2971821802a7c3[m
Merge: c8b0027 0935872
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 21 16:00:06 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit c8b0027a2b304c74de90d931309100fbd7457940[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 21 15:59:57 2025 +0800

    feature:样式微调

[33mcommit 0935872155cc9a0b6eefab3f26dd49c63200d576[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 21 12:45:57 2025 +0800

    fix: remove unnecessary 'embedded=true' parameter from PDF preview URLs

[33mcommit 03754f1875a1467404bbb6250b83f5b230e88830[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 21 11:35:55 2025 +0800

    refactor: update image and video preview components for improved layout and functionality

[33mcommit 5fef1a0eba7fab2b2031e7dffff8ce45f562aa5d[m
Author: liuzeyu <<EMAIL>>
Date:   Sun Apr 20 13:59:53 2025 +0800

    feature: 样式微调

[33mcommit 913798fd903cd48421d0544017f5d74d206c4725[m
Merge: 5c522f5 69f27bf
Author: liuzeyu <<EMAIL>>
Date:   Sun Apr 20 00:10:26 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 5c522f5bece8a88e3c23813c83330a9ab7ee8a0a[m
Author: liuzeyu <<EMAIL>>
Date:   Sun Apr 20 00:10:23 2025 +0800

    feature: hover显示效果

[33mcommit 69f27bfed86d22dc38188a20ad0136861a349512[m
Merge: 8a3b945 27bdbb2
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 18 18:03:20 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 8a3b945e58602451411dfd92d4a56e1eb19e10ba[m
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 18 18:03:12 2025 +0800

    fix:样式更改

[33mcommit 27bdbb23bb167addc8f733fd62a243e683d72c82[m
Author: liuzeyu <<EMAIL>>
Date:   Fri Apr 18 15:03:04 2025 +0800

    feature: tooltip组件初始化

[33mcommit 103f1145db317aa8723d73c99b522c2b600e9eaf[m
Author: shangchao <<EMAIL>>
Date:   Fri Apr 18 14:31:44 2025 +0800

    fix:标签bug修复

[33mcommit 53a17e35092d5493d660e35da73c6c7729a7de8f[m
Merge: ee06f3f aaff9a1
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:53:50 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ee06f3f010e026fe77735dd70b1c13876570f4e8[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:53:43 2025 +0800

    fix：样式更改

[33mcommit aaff9a1e94e9360f7316e7261af78aad1c5e921b[m
Merge: e19e948 2b72608
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 18:50:03 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit e19e9482b15c863330eb93130a791fcd63a0dca0[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 18:50:00 2025 +0800

    feat: initialize content items and enhance video preview handling in videoForm component

[33mcommit 2b72608695f3e4d68a8a311e01bfd3080164d4ca[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:49:48 2025 +0800

    fix:样式更改

[33mcommit f398584c67a5c45685282d42bc01544385a67f98[m
Merge: 8467fff 2ebf76d
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:41:48 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 8467fff1641d9bc893ea7e5ab321bb1807565f6b[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:41:41 2025 +0800

    fix:样式更改

[33mcommit 2ebf76d5362ee58c12dcb43fd2aee5eb60c6b208[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 18:19:05 2025 +0800

    fix: update styles for article and image forms

[33mcommit 5469c2760c92015030a4c2eb0d06036cf4b95da9[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:14:04 2025 +0800

    fix:样式更改

[33mcommit 9531ecd18d6133fbd3849062169596073760f9ba[m
Merge: 53cb3a4 bc35333
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:13:16 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 53cb3a42075a85093c30ab297f6e523af6f05d59[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 18:12:57 2025 +0800

    fix:样式更改

[33mcommit bc35333835544d407e40506c7db3386de24eb93a[m
Merge: 53920a2 62cc88d
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 18:11:16 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 53920a2be3337cb49a8e3da6b3578aa26c9d1112[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 18:11:14 2025 +0800

    fix:解决tag回显

[33mcommit 62cc88d71d3235e44c77d460182571f68134daa4[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 17:55:27 2025 +0800

    feature:补充添加hover提示

[33mcommit d48c8ccc7b14978846e3fe0f8af72b7f036a22ed[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 17:42:11 2025 +0800

    fix：修复model filter

[33mcommit 4558cb294d42ef96db557afe6d03792b84f13753[m
Merge: 3dad922 e7c2649
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 17:36:32 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 3dad922003b12cea1080bf2ffb2dd4490f15f2b1[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 17:36:27 2025 +0800

    feature: #197 #195 #193

[33mcommit e7c264951b7f2a1c58f6bdb495e895c5b2062f68[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 17:25:32 2025 +0800

    fix:bug修复

[33mcommit 2469a3dec14b38c746ee1dc2746be703aa335b16[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 16:23:31 2025 +0800

    exlint

[33mcommit b42132dfd3fe254404438e10d0ee7259d8a997c0[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 16:22:56 2025 +0800

    eslint:修复

[33mcommit 68cbc6e46f93c8356a2be8cc8a739a842e907515[m
Merge: b35cce5 fa88407
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 16:20:34 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit b35cce59120f902701edcb77a9a397843d109f5c[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 16:20:27 2025 +0800

    fix:样式更改

[33mcommit fa88407746ed152d5d8e7acb6034567a2b6b0564[m
Merge: d2d9567 142cd06
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 16:10:21 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit d2d956787d1762214cc3942994f996158f03e507[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 16:10:18 2025 +0800

    feature:文件授权

[33mcommit 142cd063097891fcdb618943c27792098dc73f47[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 15:21:54 2025 +0800

    fix:样式更改

[33mcommit fe381303ad7a98b2b528ff9da65cefecdeda58e7[m
Merge: 95735ec 2087aea
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 15:15:38 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 95735ec4b2ae9f2a94cdc7201efdc4fafdc05bf2[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 17 15:15:29 2025 +0800

    fix:样式更改

[33mcommit 2087aea54909e0c5964e5ce2a72d8a4d6b196d30[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 14:17:24 2025 +0800

    fix: update preview URL in pageCreate and remove debugger in App.vue

[33mcommit 38952a8ff86e5d06cea9f672d314cdb2a1ba11df[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 17 13:11:11 2025 +0800

    fix:elementPlus国际化

[33mcommit 0aa0bc49e6f55389a2cded9c7e02cad795359b97[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 11:22:05 2025 +0800

    fix:轮播图鉴权逻辑完善

[33mcommit 4a20488da4de878c56f3872b0f09af75d8847b23[m
Merge: f02d614 d624450
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 10:41:23 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit f02d6147547598c684ef504af3a749c5c81f469d[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 17 10:41:17 2025 +0800

    feature: 轮播图页面 图片权限管理

[33mcommit d62445065de116b5ca9d73a8d9a1e371ed168ca8[m
Merge: 0ab8445 8116568
Author: shangchao <<EMAIL>>
Date:   Wed Apr 16 18:07:32 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 0ab84456f9a101f9db61f0399ebcf4ec1bd8ea28[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 16 18:07:29 2025 +0800

    fix:tag置灰

[33mcommit 8116568a4e76479e91df22768df2dc967bc67f7e[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 16 18:01:02 2025 +0800

    fix:样式更改

[33mcommit 471afcece14de6c8207b57cf9ad4a020f7fcb642[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 16 17:57:41 2025 +0800

    fix:样式更改

[33mcommit f5e8ddd3f53dc6ec7fcc2a19a517ef818081f6b5[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 16 17:45:56 2025 +0800

    fix:样式更改

[33mcommit d3914d5fce0f42733b36b5797622a874c801af03[m
Merge: ff493c8 dfec667
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 16 17:25:58 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ff493c8a1e494ee435f240afac39c9fa11feba2e[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 16 17:25:49 2025 +0800

    fix:样式更改

[33mcommit dfec6677acfc7b4033e6b749e2bb2d61403837e0[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 16 16:34:16 2025 +0800

    fix:tag失活回显

[33mcommit 4587eafc6333c14b1c51b2b0376a6bda505796c4[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 16 16:10:20 2025 +0800

    feature:图片类型文章增加授权

[33mcommit 061a11f373a4efe121e7872b77c511b39cdc42c5[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 16 11:17:52 2025 +0800

    feat: add login text to navbar and implement automatic login initialization

[33mcommit 007362e844940ea400470feaff06b74e1fd5e2d7[m
Merge: 96428d8 e4edd9d
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 15 17:58:55 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 96428d8deee06ee64885341bb6e263a6256f969f[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 15 17:58:46 2025 +0800

    fix:样式更改

[33mcommit e4edd9da0f889b8cfc1783d422f69e4bd1cce25b[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 15 17:30:07 2025 +0800

    style:样式更改

[33mcommit dcad750fcc3ed07ffb9b5c4ba941e7bc9167f4a0[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 15 17:14:36 2025 +0800

    eslint:修复

[33mcommit 86b68944203439f233e4c4d9d24cbf379c621b12[m
Merge: dca7dee 0f5e1c5
Author: shangchao <<EMAIL>>
Date:   Tue Apr 15 17:11:26 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit dca7dee74138b76c964a43827a444cd8cfb12ea6[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 15 17:11:23 2025 +0800

    fix:bug修复

[33mcommit 0f5e1c5b11fd19a5c70b6396f2f4edbd8d90c08e[m
Merge: c235d0f 332d157
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 15 16:11:36 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit c235d0f07c12ff35164b1a0439af30141caab077[m
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 15 16:11:31 2025 +0800

    feature:调整轮播图样式

[33mcommit 332d157920ae22d7c7b74d2c74992915f3e7f3e0[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 18:10:09 2025 +0800

    fix:eslint

[33mcommit 1c0c3a428ad911345625ff79262500da2593d6e1[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 18:09:23 2025 +0800

    fix:文件变为链接异常

[33mcommit 756fe4e21219c77ea5d380b67e92cb9ca7c48f14[m
Merge: ca53a40 b99de69
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 14 17:54:00 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ca53a40016c9126d5f88a0ad8da47826a15cc40d[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 14 17:53:55 2025 +0800

    feature：article页面publishTime验空

[33mcommit b99de6931b893c90b85b116d7408f5d19b0be059[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 17:53:45 2025 +0800

    fix:作者编辑等回显问题解决

[33mcommit 5ff628bd03474c62a14db7107d5ac0064efe9e70[m
Merge: 5718fd0 9737778
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 14 17:32:20 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 5718fd0528b13a268f14a884b1378bd4b3bf6d88[m
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 14 17:32:11 2025 +0800

    feature:新增订阅功能在收藏页面。fix:样式调整

[33mcommit 9737778f4458d4dcb5cf40a4dc219622c7719686[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 17:28:44 2025 +0800

    fix: update user API endpoint and correct user info mutation naming

[33mcommit 5fc8293cef9dbd26dddf374cf30124d2100bba86[m
Merge: 0280e9f 333013e
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 14 17:04:55 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 0280e9f69772722d546a5ab7f953930ca7f9ddf3[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 14 17:04:53 2025 +0800

    feature:#157 添加发布时间属性

[33mcommit 333013e762bf62f92b5fff9248ba85f598ce96ba[m
Merge: 0ba8a96 9d2e0d0
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 17:03:35 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 0ba8a965d8d4f15009a48c77371578ee22819dc9[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 17:03:32 2025 +0800

    fix: update initialization logic and adjust video form height

[33mcommit 9d2e0d0d317b6384f8080bfbd0ec2b589665eeb9[m
Merge: 3af3765 5c96ecd
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 14 16:34:16 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 3af3765eab3e57e41d3d57ebb3f2f78275833f83[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 14 16:34:11 2025 +0800

    feature：#146 历史版本样式对齐问题

[33mcommit 5c96ecd2fa979c0b3e37dc47873d3cfb594e9bbd[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 15:40:16 2025 +0800

    feature:编辑链接标签保证回显

[33mcommit ccc1f784a1e9260cb8dd1d1e16e04fe3305934a4[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 14:43:27 2025 +0800

    style:调整padding-right

[33mcommit 840648d32d2874beea9a1a12e08cad797bf6628c[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 14:41:58 2025 +0800

    style:labelWidth调整

[33mcommit 2b2ba74c5e729914aa5a925a4f81b7016486a2fe[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 14:39:14 2025 +0800

    fix:国际化处理

[33mcommit 8079382100a1fbcc1fb74bb1ad1cfe1ef0b57538[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 14:36:22 2025 +0800

    fix:图片增加蒙版显示详细信息

[33mcommit 0e9e7b39ced9824bb6bb6170025ff45ee9dbea56[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 14 13:02:33 2025 +0800

    style:pageCreate样式改完

[33mcommit 3cbadc60423783087d2d319cb507a7b24a4c30bc[m
Author: yuzihan <<EMAIL>>
Date:   Sat Apr 12 15:56:12 2025 +0800

    fix:样式更改

[33mcommit 0662f8aa3c515b77b7ec7e3389bcaaeb3df76aa7[m
Merge: 59b4696 89be581
Author: yuzihan <<EMAIL>>
Date:   Sat Apr 12 15:54:23 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 59b4696914e8cee7a25522b9da8ab27e4e668bb6[m
Author: yuzihan <<EMAIL>>
Date:   Sat Apr 12 15:54:12 2025 +0800

    fix:样式更改

[33mcommit 89be581bc353425283e29454ee514775fd56e07c[m
Merge: e3bf0c2 174084a
Author: shangchao <<EMAIL>>
Date:   Fri Apr 11 18:45:17 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit e3bf0c246e036aa551406e56746353f6d19c8582[m
Author: shangchao <<EMAIL>>
Date:   Fri Apr 11 18:45:14 2025 +0800

    feat: add new SVG icons for page creation and update form styles

[33mcommit 174084a1678ada35d36889fcb20c8b9c674b3bbf[m
Author: liuzeyu <<EMAIL>>
Date:   Fri Apr 11 16:20:59 2025 +0800

    feature: 删除detailedArticle冗余方法

[33mcommit 73acd49d407aa5b3bfa039b97856f04d0d129363[m
Merge: 89f1dd5 503b073
Author: liuzeyu <<EMAIL>>
Date:   Fri Apr 11 16:19:35 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 89f1dd5a1add95b684f66626807e9febbd28151a[m
Author: liuzeyu <<EMAIL>>
Date:   Fri Apr 11 16:19:33 2025 +0800

    feature: 更改文章详情页样式

[33mcommit 503b073ceb70db9756c59060bc36eb1f651ebfa2[m
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 11 15:56:37 2025 +0800

    fix:样式更改

[33mcommit 48ff3cfa96103c0efdbece82ddc0c4d273df0072[m
Author: yuzihan <<EMAIL>>
Date:   Fri Apr 11 15:55:17 2025 +0800

    fix:国际化contentpage以及样式更改

[33mcommit effb327c7da89d8ada2e22a8faeb889a160d440e[m
Merge: ba85598 6df9750
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 10 18:03:39 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ba85598799b6e91a2e1be28d86cdd7639a062498[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 10 18:03:29 2025 +0800

    fix:样式更改

[33mcommit 6df975052a81b6f618afbd50a31736f990d3f4e6[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 10 16:19:54 2025 +0800

    fix:栏目回显问题修复

[33mcommit 9798f516feb3d19fe4a59613592067e72b157b90[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 10 15:46:52 2025 +0800

    fix: filter tag list to include only active items

[33mcommit b3b8bdd87df52507a81430faa139056cadc063f4[m
Merge: 13607f1 02997b7
Author: shangchao <<EMAIL>>
Date:   Thu Apr 10 15:34:07 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 13607f12c38efde3115b4b4dc2af5898e7f23257[m
Author: shangchao <<EMAIL>>
Date:   Thu Apr 10 15:34:04 2025 +0800

    style:新建页面表单样式更改

[33mcommit 02997b7a31006c7b88b8948d04478d65b3ed7893[m
Merge: 0629fc1 5a1aa1c
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 10 15:33:50 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 0629fc1b2d79afcdc0804f3ea96f0db0310a34f4[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 10 15:33:48 2025 +0800

    feature:增加收藏按钮防抖

[33mcommit 5a1aa1c650710ba037ed5dc1a4f8f82ec8cd27f3[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 10 14:19:32 2025 +0800

    fix:样式更改

[33mcommit 2ce115722a9366303df9fb1de03e6047abee76d7[m
Merge: 053d9c1 1a2ee6f
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 10 14:17:35 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 053d9c1be528ecca66da820ea549ce1ea6a2535d[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 10 14:17:27 2025 +0800

    fix: 样式更改

[33mcommit 1a2ee6fdab04de3786ce21ced887c6c04bde18b6[m
Merge: c9d8b0b 6d9b190
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 10 14:06:24 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit c9d8b0b76b8f1b1c860e9aa8c77dffbe048ae61f[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 10 14:06:18 2025 +0800

    feature：更新页面样式

[33mcommit 6d9b190078f47254aa42bcd656077a6acb5b6ea2[m
Merge: 2fe103c 09f277d
Author: shangchao <<EMAIL>>
Date:   Wed Apr 9 15:00:07 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 2fe103c8e1597f7174c8e481981548208eb9c1c8[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 9 15:00:02 2025 +0800

    style:表单框的样式回滚

[33mcommit 09f277da574f902d741219e28cf8d8c0246943d6[m
Merge: 73fbedc 3f63d2e
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 9 14:32:55 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 73fbedc770aec4027603b55b884bed2940d2e651[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 9 14:32:45 2025 +0800

    fix:收藏页面以及搜索页面折叠面板更改

[33mcommit 3f63d2eb27f8ae797c2d60d63afad9d5e0248cae[m
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 8 10:24:35 2025 +0800

    fix: #117 #118补充文章跳转 此条推荐样式

[33mcommit d3fd20e2aefa28ffc990f2e9628f7e3e500b77a7[m
Merge: acf83f2 a3d2f6f
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 7 18:12:15 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit acf83f28d983f380ee400119a4bb2e493b4eb148[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 7 18:11:56 2025 +0800

    feature:推荐文章跳转

[33mcommit a3d2f6f9ab21a6b74546dcbf8739c8889c636efa[m
Merge: 8b91469 b52c45d
Author: shangchao <<EMAIL>>
Date:   Mon Apr 7 15:45:25 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 8b914690d3693b144af9466abe05ff431308fcd9[m
Author: shangchao <<EMAIL>>
Date:   Mon Apr 7 15:45:22 2025 +0800

    fix:带入登录名值作者中

[33mcommit b52c45d5f9d1abda2ffc018f0b189768c2584cd9[m
Merge: 7f7a70c 5ced107
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 7 15:35:19 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 7f7a70cc88de38ceb2f4d6b4b684e6c2bebf989f[m
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 7 15:35:10 2025 +0800

    fix:跳转pagecreate页面时routerpush，切换查询条件时，分页重置

[33mcommit 5ced107e9953b165cecacbd9cb1ec78b31669ec4[m
Merge: f23f6f1 aa17803
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 7 14:56:21 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit f23f6f1f36884daa5155034550419bef2851b925[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Apr 7 14:56:16 2025 +0800

    feature：增加无附件时无法打包下载提示

[33mcommit aa1780306df6bb894b5ecd6a38e3058095f33502[m
Merge: 8a5ef4a 2b7c5aa
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 7 13:58:10 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 8a5ef4a6a72cdd34a67f7306b8104447a4cea5bb[m
Author: yuzihan <<EMAIL>>
Date:   Mon Apr 7 13:57:01 2025 +0800

    fix:标签启用状态改变，查询时也能查出来了

[33mcommit 2b7c5aa1d0f57fa91938abd39414e85cb0cc863e[m
Merge: 30c279d b314f88
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 3 17:53:33 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 30c279d8c40a626d01b3b9db63986cd3e70a22db[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 3 17:45:32 2025 +0800

    feature:下载量增加函数参数修正

[33mcommit b314f88f25a53b58194569c68728bf72a48d5eca[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 3 17:18:26 2025 +0800

    fix:样式修改

[33mcommit 9b0f5297bec1448167a6edd2b19b94fb8828ca83[m
Merge: 5b6d66c 5efb18f
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 3 16:08:11 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 5b6d66ce7c09f7635cefd8c76e70071497fe8f4a[m
Author: yuzihan <<EMAIL>>
Date:   Thu Apr 3 16:08:01 2025 +0800

    fix:样式更改

[33mcommit 5efb18f256e12ef3724885c2f560fc758500c080[m
Merge: 4d4a49e a31df68
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 3 15:21:59 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 4d4a49e5b766031d85deead5245b17496cead10a[m
Author: liuzeyu <<EMAIL>>
Date:   Thu Apr 3 15:21:56 2025 +0800

    feature:样式调整

[33mcommit a31df68e1c85af4268cc89f91e3c4ea963da3222[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 2 16:28:30 2025 +0800

    feature：添加搜索功能

[33mcommit 281f71261b06a512db4f1b6a771a6f36c021e2ae[m
Merge: 93664b0 5911244
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 2 15:17:36 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 93664b0e08cb5e4f0ca910c84e7a436e150a6a06[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 2 15:17:26 2025 +0800

    fix: 样式修改

[33mcommit 59112447fde5df7357f7599ecc69c5300e6ccd74[m
Merge: a31586a 42209f1
Author: liuzeyu <<EMAIL>>
Date:   Wed Apr 2 15:07:53 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit a31586af53efda529f9abc5c46ef6bb0a87c9495[m
Author: liuzeyu <<EMAIL>>
Date:   Wed Apr 2 15:07:48 2025 +0800

    feature:匹配api调整

[33mcommit 42209f133086001cf7642bd09e8f76716009b988[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 2 14:38:19 2025 +0800

    fix:basehomepage修复

[33mcommit c717181064879d6b3ebd85b59b80ec4f6c07aa89[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 2 13:46:08 2025 +0800

    fix:bug修复

[33mcommit 89a7841a35c493808a7e6310ea8414096228c0be[m
Merge: 3936dd1 7626f63
Author: shangchao <<EMAIL>>
Date:   Wed Apr 2 13:04:43 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 3936dd1df818a0375889581d64f781783b8fe240[m
Author: shangchao <<EMAIL>>
Date:   Wed Apr 2 13:04:40 2025 +0800

    refactcor:鉴权优化

[33mcommit 7626f636c1080f9e1f5fdd4a55d4392f4897b6ee[m
Author: yuzihan <<EMAIL>>
Date:   Wed Apr 2 11:45:33 2025 +0800

    fix:样式修改

[33mcommit f9603ff11fec0245e3e81c35e1276b0b2d3f7276[m
Merge: 147120a 3180f2d
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 17:04:51 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 147120ab143ca97995a0df587deee71c530035ba[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 17:04:39 2025 +0800

    fix:搜索栏加下拉历史

[33mcommit 3180f2d7abe4eeae5c2d8f03e95354528bde70c5[m
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 1 15:42:16 2025 +0800

    feature: 总览页面初始化

[33mcommit ddd84cff1996fc01e88bb3ae630ba43a21d3c78d[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 15:06:21 2025 +0800

    feature:下载count+1

[33mcommit dd66c5568b1fd17058b321bc61ca034945f423b1[m
Merge: aada97f 579e9c3
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 14:43:04 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit aada97ff314b34ad0c70dfc9f1168c5f430248ad[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 14:42:55 2025 +0800

    fix:样式修改

[33mcommit 579e9c3d495ed743962ca361a3e394bcb0199807[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 1 14:33:33 2025 +0800

    style:列表高度调整

[33mcommit 40dd281100baf42a48060cf37709806e44e9890e[m
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 1 14:29:49 2025 +0800

    feature: 完善文件下载计数功能

[33mcommit b22777b91df52c5df9d3b17e3d2751f04f3d773f[m
Merge: 3a01814 d0a42b0
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 1 12:57:59 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 3a01814b27916fcb198b3f76e7106ec6e1296412[m
Author: liuzeyu <<EMAIL>>
Date:   Tue Apr 1 12:57:54 2025 +0800

    fix:文件下载问题修复

[33mcommit d0a42b0ad000fa820b91a246d6df275bc1162cad[m
Merge: d4dd386 dd80eff
Author: shangchao <<EMAIL>>
Date:   Tue Apr 1 12:55:36 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit d4dd386bef16abaeed4797232f3f7cfe4764a0ab[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 1 12:55:33 2025 +0800

    fix:bug修复

[33mcommit dd80eff9ea9108a522943e901bcf7a8ad917d8c8[m
Merge: 22df105 eb19698
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 12:54:45 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit eb196980413f02bc7be5db2c1851667cb17fbb5c[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 1 12:27:46 2025 +0800

    fix:eslint修复

[33mcommit 0ce97d506c033b91a7c09dd4d38d7f4f9ce906bd[m
Merge: 160bbf9 c0213f8
Author: shangchao <<EMAIL>>
Date:   Tue Apr 1 12:26:53 2025 +0800

    fix:开放浏览功能

[33mcommit 22df105d3c8dd97546f862bfae2a1e38acdcc087[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 12:05:55 2025 +0800

    fix:样式更改 栏目部分

[33mcommit 160bbf95b713b747ada98722f56d62e8d16e767a[m
Author: shangchao <<EMAIL>>
Date:   Tue Apr 1 11:59:17 2025 +0800

    fix:bug修复

[33mcommit c0213f85180dff840ded996b9c2fcfbceb8d8247[m
Author: yuzihan <<EMAIL>>
Date:   Tue Apr 1 11:25:42 2025 +0800

    fix:无改动

[33mcommit 4b7d7732882bc0b74047112ea2871bedaf32534f[m
Merge: 917fd75 045bae3
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 21:00:03 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 917fd75da9e75d2cec294b728a05b03109a2c32a[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 21:00:00 2025 +0800

    fix:bug修复

[33mcommit 045bae3626c8614fc545d80811bbe2b82d17217e[m
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 20:59:33 2025 +0800

    fix:样式更改

[33mcommit 075e5d6ea7dffcdedb0977f39e8b1fa52942afaf[m
Merge: 6378590 677382b
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 20:20:46 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 63785903801ffd8716c8ed9194861566a0f92a35[m
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 20:20:22 2025 +0800

    fix:样式更改

[33mcommit 677382b40e5788a226372ddc4e4dc646060e1421[m
Merge: e19c7e3 1562e16
Author: liuzeyu <<EMAIL>>
Date:   Mon Mar 31 20:10:13 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit e19c7e32ac07088878ebdd37285d6116a5b59ac2[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Mar 31 20:08:45 2025 +0800

    fix:解决搜索栏空选项问题

[33mcommit 1562e16511e62e800e17294783e9fce2c5208032[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 19:49:23 2025 +0800

    fix:栏目回显

[33mcommit 53c622e29a7596354f2ff7eb10773a66f1ecb0b5[m
Merge: 254e451 d5eae2e
Author: liuzeyu <<EMAIL>>
Date:   Mon Mar 31 19:31:32 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 254e4517a577f7aac227a4724b3a2bd4fae3c741[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Mar 31 19:27:59 2025 +0800

    fix:样式修正

[33mcommit d5eae2e328f364cdf588cd9de32367cab2f27322[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 18:58:32 2025 +0800

    fix:bug修复

[33mcommit e6a53755b87cb77b885629619dfbc7b7c68a0a93[m
Merge: 3e9d485 79d7356
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 18:18:34 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 3e9d4852c544cc8f1233081c091a5aca030d6d1a[m
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 18:15:44 2025 +0800

    fix:样式修改

[33mcommit 79d7356d6efb59d68911ebc8c7942c12878dd0dd[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 17:22:25 2025 +0800

    fix:多维度管理修复

[33mcommit b64c943260f2f6667a06a904817e62c91687cc95[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 17:04:48 2025 +0800

    fix:bug修复

[33mcommit 08860ef657dcbf708291bdd6a8ed8e63c3f9fb90[m
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 16:34:05 2025 +0800

    fix:收藏页面间隙调整

[33mcommit f1facf812ef3297976b6ae4b7fc4fe9d7ee2c5ee[m
Merge: 33eb65c edf7c82
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 16:26:12 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 33eb65cac9468907dbd6cb8b04c621dd9b4de524[m
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 31 16:25:58 2025 +0800

    fix:内容列表，收藏页面，回收站页面样式更改，排序添加

[33mcommit edf7c82def1f7435b555349f60aa9660bd495ce2[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Mar 31 16:04:39 2025 +0800

    feature:页面样式调整和文章详情页面国际化

[33mcommit 36a6e1925aa91119758763f112e1b99139626f50[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 14:24:58 2025 +0800

    style:按钮样式调整

[33mcommit 653058dc38648f152a8bcc5be17b6c4837c9063f[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 14:20:40 2025 +0800

    style:page新建样式优化

[33mcommit 43b4506822dc087e9e59cc3c67dafda9136a7c26[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 13:17:37 2025 +0800

    style:基本属性滚动条控制

[33mcommit e812b997fb9ac7914468ae0b24ac31f1afd19c86[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 13:11:32 2025 +0800

    fix:附件上传弹框原型同步

[33mcommit 7fa2ed973668c14f85409685e3fce55ef02184c4[m
Merge: 74d28a9 00cbe11
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 12:47:39 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 74d28a93adf96fd69b295ebaa2e860aebb04538f[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 31 12:47:36 2025 +0800

    fix:bug修复

[33mcommit 00cbe11a81b608b0539f17aff5839ccdd3542f34[m
Author: liuzeyu <<EMAIL>>
Date:   Mon Mar 31 12:09:14 2025 +0800

    fix:#40 #35 样式修改

[33mcommit bef81f704f15e7708a0117733781a1f0bf7ccdf5[m
Author: liuzeyu <<EMAIL>>
Date:   Sun Mar 30 22:52:42 2025 +0800

    fix: 样式调整

[33mcommit ae33dd85a47c3c862dfee288abcecda328a0654f[m
Merge: f14c250 7cd12ee
Author: yuzihan <<EMAIL>>
Date:   Fri Mar 28 18:13:13 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit f14c25070ebcff19e34517dd26211928573d89e4[m
Author: yuzihan <<EMAIL>>
Date:   Fri Mar 28 18:13:04 2025 +0800

    fix:导航栏调相应页面的分页接口

[33mcommit 7cd12ee10b8cecf5296ce5434e335f1df7a4c70b[m
Author: shangchao <<EMAIL>>
Date:   Fri Mar 28 18:11:43 2025 +0800

    fix:tagsPage页面美化

[33mcommit cac8fd986c75d91589c15ebaf54623118021097b[m
Merge: 925b4e6 d23c84a
Author: parsifal486 <<EMAIL>>
Date:   Fri Mar 28 15:51:59 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit d23c84a284fc62a40553bb9cb92881324d409708[m
Author: yuzihan <<EMAIL>>
Date:   Fri Mar 28 15:31:57 2025 +0800

    fix:内容列表样式修改 回收站页面标题可点击实现

[33mcommit 925b4e6bd5a5c0377767a76a562ad0de2cb8a3e8[m
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 27 18:21:42 2025 +0800

    feature: 完善页面间跳转

[33mcommit e0d65a8838d271e612b41f471c55ea53194528d3[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 27 17:15:39 2025 +0800

    fix:收藏接口调整

[33mcommit 7c5cfc88ddfb1fdea11fa0350fee337727efeec3[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 27 14:11:17 2025 +0800

    fix:导航栏样式修改

[33mcommit b0b4a4224eba52f91f94db8847babfdd4c3cf42f[m
Merge: 79662c8 5f7398b
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 27 14:09:33 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 79662c8bf52deb92961cf4b9688b725f1baeb626[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 27 14:04:20 2025 +0800

    fix:回收站样式修改，导航栏样式修改以及搜索框接口调用。 feature:收藏页面初始化

[33mcommit 5f7398be086cb72301bdb47fc498ef6a62be3b85[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 27 12:03:21 2025 +0800

    fix:修复代码审查问题

[33mcommit f228a7eb55aa37b8d77054e8012c8ed99f14107d[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 27 12:01:40 2025 +0800

    feature:文件上传添加授权功能

[33mcommit ecc2173267c1f5571c4fac5872d2391584703343[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 27 11:07:32 2025 +0800

    fix:代码审查处理

[33mcommit 79e7e5b8970ed28341d08b59e3167d2d39ef6a4c[m
Author: parsifal486 <<EMAIL>>
Date:   Wed Mar 26 18:24:12 2025 +0800

    style: 删除部分调试代码

[33mcommit 4e1cfd721a8a977fb96b2f1226b7e970c7193794[m
Merge: 5344dea 218710b
Author: parsifal486 <<EMAIL>>
Date:   Wed Mar 26 18:16:50 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 5344dea8ed3c6cfcef6602064dba64f723adc3cb[m
Author: parsifal486 <<EMAIL>>
Date:   Wed Mar 26 18:16:43 2025 +0800

    style: 优化页面样式

[33mcommit 218710b2a3ea2b744d0d1af4e51956917f6beb1f[m
Author: shangchao <<EMAIL>>
Date:   Wed Mar 26 15:44:40 2025 +0800

    fix:创建使用url编码处理富文本

[33mcommit b316e156d0d90e2be54ac5c31ca75d8033bce5b3[m
Author: shangchao <<EMAIL>>
Date:   Tue Mar 25 17:14:29 2025 +0800

    style:优化新建样式

[33mcommit e9a28a394f1e25314817f6c67408e0a1521b01ea[m
Author: shangchao <<EMAIL>>
Date:   Tue Mar 25 14:16:30 2025 +0800

    fix:解决发版构建异常

[33mcommit 082cb4cb024602892df915898eae8f0fea3c4c06[m
Author: shangchao <<EMAIL>>
Date:   Tue Mar 25 13:28:03 2025 +0800

    style:列表页面样式调整

[33mcommit 0aff69380505b93626a2dd40e60251c3dd2a343b[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 24 11:50:02 2025 +0800

    fix:富文本多语言配置

[33mcommit d80a6a8979d81010bf149bc7d392eeaa881e2e48[m
Merge: 95ebab8 2eca5d5
Author: yuzihan <<EMAIL>>
Date:   Fri Mar 21 16:43:56 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 95ebab830c6a661fd278d195bacb9263871c4a6e[m
Author: yuzihan <<EMAIL>>
Date:   Fri Mar 21 16:43:34 2025 +0800

    style: 搜索列表页面排版修复

[33mcommit 39b3aef47722de6ffde663518974fd8181b7eb30[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 20 18:06:09 2025 +0800

    feature:添加必选项

[33mcommit 2eca5d549501c24e1986d439c30605cbeb7ec536[m
Merge: 9da326c 9f34f34
Author: shangchao <<EMAIL>>
Date:   Thu Mar 20 16:47:56 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 9da326cb637257e2c4b3b8ea2922512680db4be7[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 20 16:47:53 2025 +0800

    feature:按钮添加toolTip

[33mcommit 9f34f34c878bb5f085bd9f8bfc38373036ca9729[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 20 16:35:52 2025 +0800

    fix:status接口添加

[33mcommit a9437af7675ff437a653ed93235be7e2fd1e0b06[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 20 16:16:39 2025 +0800

    fix:eslint冲突

[33mcommit 9283630a505e999468e6688be8086b0dbd95295a[m
Merge: 3237525 ae7f394
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 20 16:05:03 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 3237525c8e723adbdfad18fc250fc29c8c08fa34[m
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 20 16:04:57 2025 +0800

    feature: 搜索栏历史记录展示 事项#20 #21 #26 #27 #28样式更正

[33mcommit ae7f39409eca130f16728d845bc5acc8acc91256[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 20 16:04:56 2025 +0800

    fix:搜索的placeholder调整

[33mcommit bec562e7722dd25e3f1bb2957b7fcf354d4a49f8[m
Merge: bffdadc 525524f
Author: shangchao <<EMAIL>>
Date:   Thu Mar 20 16:02:26 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit bffdadc575c68176c59d5ef6e84a02b8dd49a98a[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 20 16:02:22 2025 +0800

    style:标签管理样式优化

[33mcommit 525524f0e36459ad0809deefa1ecb21bfe3d55b7[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 20 14:59:32 2025 +0800

    feature:关键字搜索跳转功能实现

[33mcommit 16acbc08c9613cefb0d5a4ea3dc6877ab6a0fd9c[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 20 14:05:31 2025 +0800

    style:标签管理页面样式改动

[33mcommit bd5e3c832c018624abf5e86a00497eb46994ea85[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 20 11:50:19 2025 +0800

    style:回收站页面样式更改

[33mcommit 7bd9b42083292abf694911b0c008b045cc5f7b58[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 20 11:41:01 2025 +0800

    feature:回收站页面测试需求更改

[33mcommit 20992ff27d7ce02443757f182f647bb0a145ecb3[m
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 20 11:09:24 2025 +0800

    fix:修复knowledgebasehone

[33mcommit 72b35a3eeee9a661c7228e1df3960a17f51b0da3[m
Author: parsifal486 <<EMAIL>>
Date:   Wed Mar 19 19:23:47 2025 +0800

    feature: 知识库主页搜索推荐跳转

[33mcommit d1cc1c49bee5adc5744eb875d45a27b3e2446832[m
Merge: c6f25d6 3f624b3
Author: yuzihan <<EMAIL>>
Date:   Wed Mar 19 14:05:03 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit c6f25d64e6220be99b06b5e2aa8249572994306e[m
Author: yuzihan <<EMAIL>>
Date:   Wed Mar 19 14:04:21 2025 +0800

    feature:排版布局按测试要求更改

[33mcommit 3f624b3af34576f0463ef565ba701b936172725a[m
Author: parsifal486 <<EMAIL>>
Date:   Tue Mar 18 15:52:21 2025 +0800

    style:#20 #21 首页样式优化

[33mcommit ef8428b495eb257124764baa6dd91e60332c2a7d[m
Author: yuzihan <<EMAIL>>
Date:   Tue Mar 18 11:51:49 2025 +0800

    feature:回收站以及内容列表分页组件修改

[33mcommit 1798b0c6fa566d119201d38e2e8c8c5a53065af4[m
Merge: 6a58755 4519d35
Author: yuzihan <<EMAIL>>
Date:   Tue Mar 18 11:28:16 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 6a58755b5306c27256209a413a904608a85f6033[m
Author: yuzihan <<EMAIL>>
Date:   Tue Mar 18 11:27:16 2025 +0800

    feature:回收站接口初始化 导航栏接口调整

[33mcommit 4519d35a573693ec02aad5805bc69245d1242d67[m
Author: shangchao <<EMAIL>>
Date:   Tue Mar 18 11:24:51 2025 +0800

    refactor:密码重置

[33mcommit 05ae79aafe5d98384059aace4a9a2192c41e6dce[m
Author: shangchao <<EMAIL>>
Date:   Tue Mar 18 10:54:23 2025 +0800

    fix:修复代码审查问题

[33mcommit 8dd54e3a9bbeb71ef179d60f2b795f329448a88a[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 17 17:19:42 2025 +0800

    style:pageCreate国际化处理

[33mcommit ada0b57e07473aeeb67cee4a127e3d9ca5efcd47[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 17 16:22:58 2025 +0800

    feature:编辑和发布完毕

[33mcommit b1d4fea00922317fc04abcd2ea444831ce9ae712[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 17 16:13:04 2025 +0800

    feature:详情跳转

[33mcommit 4b20ea20ddcde87469b23970b3e427082bbe9df6[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 17 15:17:07 2025 +0800

    feature:文章附件完善

[33mcommit d5b9dbf23ec88769acf391a5db15033f52721950[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 17 14:46:08 2025 +0800

    feature:文章新建完毕

[33mcommit 07d41540103af3060be73308b99ff89d1b51295f[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 17 11:28:58 2025 +0800

    fix：导航栏问题处理

[33mcommit b4113dc102a90c068b232a83f0731c334e4cd8ba[m
Author: yuzihan <<EMAIL>>
Date:   Mon Mar 17 11:22:11 2025 +0800

    feature:跳转新建页面初始

[33mcommit dc4013f24e54c0f921f08ae3ac61f34dd3f5a474[m
Merge: 0407104 435985b
Author: parsifal486 <<EMAIL>>
Date:   Fri Mar 14 18:59:00 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 0407104363861db8ee53a423a60196aef37cf18e[m
Author: parsifal486 <<EMAIL>>
Date:   Fri Mar 14 18:58:51 2025 +0800

    feature: 知识详情页面开发

[33mcommit 435985b842abd0bc52d41d362ae78c8b87cba86f[m
Author: shangchao <<EMAIL>>
Date:   Fri Mar 14 18:12:38 2025 +0800

    feature：文章新建完善

[33mcommit 7c5ea77a0d02b246747192c1d8bb20a07c338cb2[m
Merge: 676d5d0 662e1fe
Author: shangchao <<EMAIL>>
Date:   Fri Mar 14 16:49:15 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 676d5d0d54c73fc04fd847797faf981e0528d38c[m
Author: shangchao <<EMAIL>>
Date:   Fri Mar 14 16:49:12 2025 +0800

    feature:文章新建对接

[33mcommit 662e1fe7b110306c1f76e4875026946ef8638e74[m
Merge: edad7bf 6be4465
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 13 15:54:13 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit edad7bfc8604da459af02f46d040a31f39e0ceda[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 13 15:53:56 2025 +0800

    feature:跳转传参新增categoryId

[33mcommit 6be4465b4838b910ce3bf5d602b550f105059beb[m
Merge: 9096772 e898033
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 13 14:13:57 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 909677236ad943b8908b86b72386ff8cd39d75b5[m
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 13 14:12:44 2025 +0800

    feature: 同步仓库

[33mcommit e8980337340536c86451fb8684104e332ccb846e[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 13 14:07:13 2025 +0800

    feature: integrate wangeditor for article creation and file upload functionality

[33mcommit b89b52da65eb30206472dfa035a0097ac97c3abc[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 13 11:33:10 2025 +0800

    feature:搜索页面的接口新增

[33mcommit f9b3dd2c86fffa434ed164231e503cf6adec3878[m
Author: yuzihan <<EMAIL>>
Date:   Wed Mar 12 11:40:15 2025 +0800

    feature:路由跳转示例

[33mcommit de3e465733b84fba67a89ba788c867d1a0f091dd[m
Merge: 2cc085f d19063d
Author: yuzihan <<EMAIL>>
Date:   Wed Mar 12 11:21:22 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 2cc085f182b9e41b4d68f03b89162b68eb8b6504[m
Author: yuzihan <<EMAIL>>
Date:   Wed Mar 12 11:21:15 2025 +0800

    feature:列表跳转开始

[33mcommit d19063dfb35c29864d8149184a7baa8d493c304f[m
Merge: 660a02c 47e87c8
Author: parsifal486 <<EMAIL>>
Date:   Wed Mar 12 11:19:56 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 660a02c7d36e873a866a961a560446608262ccdc[m
Author: parsifal486 <<EMAIL>>
Date:   Wed Mar 12 11:19:50 2025 +0800

    feature: 文章页面初步开发

[33mcommit 47e87c82ecbdaa4b810e8a46f8cbae3afed4e6af[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 6 17:27:41 2025 +0800

    fix:代码冲突

[33mcommit 93e8f3b112136ef18e091413afea34b7fc75b205[m
Merge: 278c8e1 da2a5f1
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 6 15:51:40 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit da2a5f103275650c08513cfc626a5f0857362a92[m
Merge: f770a68 46bcb79
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 6 15:46:37 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit f770a68de154b5e6d1e563f2de69813c5589168e[m
Author: yuzihan <<EMAIL>>
Date:   Thu Mar 6 15:41:20 2025 +0800

    feature:回收站页面开发以及走马灯开发

[33mcommit 278c8e191ce87af32c3e7d1221d9ac1f92de29cf[m
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 6 15:31:18 2025 +0800

    feature:联调演示

[33mcommit 46bcb791f1624d55df85ac596d5a2dbb81c0b012[m
Merge: ad4c017 c55cd23
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 6 14:40:43 2025 +0800

    Merge branch 'dev' of e.coding.net:g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit ad4c0176f91eded910f45bd2eebd38043d213d3d[m
Author: parsifal486 <<EMAIL>>
Date:   Thu Mar 6 14:40:02 2025 +0800

    feature:文章页面开发

[33mcommit c55cd23aa3f7473c72011611cfc40bdc7b5dddef[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 6 14:38:58 2025 +0800

    feature:tag接口新增

[33mcommit b2ab6c102410493c4fc398d5d735fe3824b3c19f[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 6 14:27:03 2025 +0800

    Refactor:添加请求头

[33mcommit 05e556e07cd34f836853c47f503f851f7160272e[m
Author: shangchao <<EMAIL>>
Date:   Thu Mar 6 11:29:21 2025 +0800

    refactor:法规api框架改造

[33mcommit f8d554d3bc2b9a4c176661aebcd9e8cfbb8a9c85[m
Merge: d0ec301 6c3d950
Author: 商朝 <<EMAIL>>
Date:   Thu Mar 6 10:59:03 2025 +0800

    Accept Merge Request #19: (dev-auth -> dev)
    
    Merge Request: refactor:添加auth
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/19?initial=true

[33mcommit d0ec3010a7c4a59054881765e45811aed9417ee1[m
Author: shangchao <<EMAIL>>
Date:   Wed Mar 5 12:11:44 2025 +0800

    Feature:文章创建完善

[33mcommit 89f083769dac96eeecac03d9db6c4dda1ed5835f[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 3 12:35:52 2025 +0800

    feature:文章发布Link页面完毕

[33mcommit 6c3d95081b1ea8dc44945ab81ee1ffebb4d81230[m
Author: shangchao <<EMAIL>>
Date:   Mon Mar 3 10:55:44 2025 +0800

    refactor:添加auth

[33mcommit fcc807c935701ca683091ec95f0199f6244d842a[m
Author: yuzihan <<EMAIL>>
Date:   Fri Feb 28 15:04:46 2025 +0800

    Feature:搜索列表集成左侧导航栏，细节调整

[33mcommit 7af43e17f0e095d3cc5cf455de0fe65b7a9911b6[m
Merge: 864d067 adfbefd
Author: yuzihan <<EMAIL>>
Date:   Fri Feb 28 14:53:23 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 864d067b865039d26e6f0d43c3365055fb55ef60[m
Author: yuzihan <<EMAIL>>
Date:   Fri Feb 28 14:53:14 2025 +0800

    feature:列表右侧初始化

[33mcommit adfbefd5081b7c0b1f21d92a5a1ccc73e0f9bd4a[m
Author: parsifal486 <<EMAIL>>
Date:   Fri Feb 28 11:51:21 2025 +0800

    feature:知识库首页集成侧边栏

[33mcommit ec309ed4b5449ff5c010900c028cac4cc4ed14f2[m
Merge: 1dc8b5e bff9b49
Author: yuzihan <<EMAIL>>
Date:   Thu Feb 27 11:56:26 2025 +0800

    Merge branch 'dev' of https://e.coding.net/g-nevy6703/sgs-regulation/smart-web3 into dev

[33mcommit 1dc8b5e76e90ac8e0a17ea571ab9687754c5e82c[m
Author: yuzihan <<EMAIL>>
Date:   Thu Feb 27 11:55:38 2025 +0800

    feature:列表一半功能提交

[33mcommit bff9b49e9ca873d941276c1319dd84fc78708d7b[m
Author: parsifal486 <<EMAIL>>
Date:   Wed Feb 26 18:09:42 2025 +0800

    Feature:知识库首页开发

[33mcommit 2eef1792648ce8248e6e111805d5f9364d527001[m
Author: yuzihan <<EMAIL>>
Date:   Tue Feb 25 17:42:57 2025 +0800

    Feature:设计页面左侧导航栏组件，以及路由的配置

[33mcommit a8030d9a3a20d8d13cf34d8385af0c8f0663ea75[m
Author: yuzihan <<EMAIL>>
Date:   Mon Feb 24 18:22:34 2025 +0800

    标签管理页面

[33mcommit 79beea6c14f0468030fb04d1f4e6b2d5eeb351c8[m
Author: yuzihan <<EMAIL>>
Date:   Sun Feb 23 12:57:15 2025 +0800

    tagMangement页面设计

[33mcommit ff0a296f1e8020d219123c62f7924de535159c96[m
Author: shangchao <<EMAIL>>
Date:   Fri Feb 21 17:52:28 2025 +0800

    Refactor:暂时取消鉴权

[33mcommit cbcc9eedb2056a7b8bdca576c720aa4ccc4f050a[m
Author: yuzihan <<EMAIL>>
Date:   Fri Feb 21 16:21:59 2025 +0800

    撤销测试

[33mcommit 154a25682132c611bc846336240225baab44413b[m
Author: yuzihan <<EMAIL>>
Date:   Fri Feb 21 16:20:20 2025 +0800

    测试

[33mcommit 3b56042968ffb86dcbe8a8aea14a44dcd6bd324c[m
Merge: 33a9f5e 0225e85
Author: 商朝 <<EMAIL>>
Date:   Fri Feb 21 15:55:51 2025 +0800

    Accept Merge Request #1: (master -> dev)
    
    Merge Request: refactor:template
    
    Created By: @商朝
    Accepted By: @商朝
    URL: https://g-nevy6703.coding.net/p/sgs-regulation/d/smart-web3/git/merge/1?initial=true

[33mcommit 0225e856485e6b7fb17e212c4a125bad47f8882a[m
Author: shangchao <<EMAIL>>
Date:   Fri Feb 21 15:50:40 2025 +0800

    refactor:template

[33mcommit 18426b85efdff5475bc92a8831fb3572f92911c6[m
Author: shangchao <<EMAIL>>
Date:   Fri Feb 21 15:49:49 2025 +0800

    refactor:重构分支为template

[33mcommit 7609ec05cb3daf6c8366b46094f3701ada00bd8f[m
Author: shangchao <<EMAIL>>
Date:   Fri Feb 21 14:50:24 2025 +0800

    Refactor:更改sass语法

[33mcommit 33a9f5e78c956db14d34a4e89b5dc7d5e3bd51e4[m
Author: shangchao <<EMAIL>>
Date:   Fri Feb 21 14:44:52 2025 +0800

    Refactor:init

[33mcommit 4100a13867f84a303996f317a58f880c139e1447[m
Author: 商朝 <<EMAIL>>
Date:   Fri Feb 21 14:43:37 2025 +0800

    Initial Commit
