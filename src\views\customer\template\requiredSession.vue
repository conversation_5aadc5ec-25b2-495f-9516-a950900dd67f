<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_template_requiredSession" id="sgs_smart_customer_template_requiredSession">
            <draggable v-model="generalFieldConfig" group="config" :move="onMove" animation="300">
                <transition-group>
                    <div v-for="(prop,index) in generalFieldConfig" :name="'section_ind'+prop.sectionName" :key="'section_ind'+index" class="move_config_div">
                        <div style="width: 100%;height: 20px;cursor: move;margin-top: -20px"></div>
                        <el-row :gutter="20">
                            <el-col :span="1" class="section" style="line-height: 40px">
                                <el-checkbox v-model="prop.display" :true-label="1" :false-label="0"></el-checkbox>
                            </el-col>
                            <el-col :span="4" style="text-align: left;line-height: 40px">
                                {{prop.sectionName}}
                            </el-col>

                            <el-col :span="16" style="text-align: left;line-height: 40px">
                                Customized Section Name: <el-input v-model="prop.sectionLabel" :disabled="disabledInput(prop)" size="mini" style="width:calc(100% - 180px)"></el-input>
                            </el-col>
                            <el-col :span="3" style="line-height: 40px">
                                <el-checkbox
                                        v-if="['Service'].includes(prop.sectionCode)"
                                        :disabled="disabledInput(prop)"
                                        v-model="prop.mandatory"
                                        @change="changeRequired(index)"
                                        :true-label="1"
                                        :false-label="0">Mandatory</el-checkbox>
                            </el-col>
                        </el-row>
                        <template v-if="prop.fieldConfig">
                            <div style="width: 100%;height: 1px;border-top: solid 1px #c8c8c8;padding: 2px"></div>
                            <el-row :gutter="20">
                                <el-col :span="5"> </el-col>
                                <el-col :span="prop.useMapping?5:8">Customized Label Name</el-col>
                                <el-col :span="prop.useMapping?5:8">Default Value</el-col>
                                <el-col :span="prop.useMapping?6:0">{{prop.useMapping?'Customer Field Mapping':''}} </el-col>
                                <el-col :span="3"> </el-col>
                            </el-row>
                            <el-row :gutter="20" v-for="(fl,inde) in prop.fieldList">
                                <template v-if="['supplierCode','supplierName','manufacturerCode','manufacturerName',
                                    'sampleCode','sampleName','sampleType','sampleDescription'].includes(fl.fieldCode)">
                                    <el-col :span="5" style="line-height: 40px">{{fileCodeName[fl.fieldCode]}}</el-col>
                                    <el-col :span="prop.useMapping?5:8">
                                        <el-input :disabled="disabledInput(prop)" v-model="fl.fieldLabel"></el-input>
                                    </el-col>
                                    <el-col :span="prop.useMapping?5:8">
                                        <el-input :disabled="disabledInput(prop) || fl.onlyShow" v-model="fl.defaultValue"></el-input>
                                    </el-col>
                                    <el-col :span="prop.useMapping?6:0">
                                        <el-select
                                                v-model="fl.customerFieldCode"
                                                style="width: 100%"
                                                clearable
                                                filterable
                                                :disabled="disabledInput(prop)">
                                            <el-option
                                                    v-for="(cf,cfIndex) in customerDffList"
                                                    :key="fl.fieldCode+cf.code+'_mapping_'+cfIndex"
                                                    :label="cf.name"
                                                    :value="cf.code"
                                            >
                                            </el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="3" style="line-height: 40px">
                                        <el-checkbox :disabled="disabledInput(prop)" v-model="fl.isRequired" :true-label="1" :false-label="0">Mandatory</el-checkbox>
                                    </el-col>
                                </template>
                            </el-row>
                        </template>
                    </div>
                </transition-group>
            </draggable>
        </div>
    </basic-container>
</template>

<script>
    import templateConfig from './templateConfig'
    import _ from "lodash";
    export default {
        name: "requiredSession",
        data() {
            return {
                pageLoading: false,
                fileCodeName:{
                    supplierCode:'Supplier Code',
                    supplierName:'Supplier Name',
                    manufacturerCode:'Manufacturer Code',
                    manufacturerName:'Manufacturer Name',
                    sampleCode:'Material Code',
                    sampleName:'Material Name',
                    sampleType:'Material Type',
                    sampleDescription:'Material Description'
                },
                defaultFieldConfig:[],
                generalFieldConfig:[],
                customerDffList:[]
            }
        },
        methods: {
            disabledInput(prop){
                return !prop.display || prop.display-0==0;
            },
            handlerDefaultConfig(){
                if(!this.formPurpose || !['Material','Product'].includes(this.formPurpose)){
                    return;
                }
                let config = templateConfig.materialConfig;
                if(['Product'].includes(this.formPurpose)){
                    config = templateConfig.productTemplateConfig;
                }
                this.defaultFieldConfig = config;
            },
            parseDffSelect(){
                this.customerDffList = this.standardDffList.map(dff=>{
                    let {customerFieldCode,customerLabelNameCN,customerLabelNameEN} = dff;
                    let code = _.lowerFirst(customerFieldCode);
                    let name = (customerLabelNameEN || customerLabelNameCN)+'('+code+')';
                    return {name,code}
                })
            },
            initPage() {
                if(!this.fieldConfig || this.fieldConfig.length==0){
                    this.generalFieldConfig = JSON.parse(JSON.stringify(this.defaultFieldConfig));
                    return
                }
                let pageFieldConfig = JSON.parse(JSON.stringify(this.fieldConfig));
                this.pageLoading = true;
                this.generalFieldConfig = [];
                let defaultSections = this.defaultFieldConfig.map(s=>s.sectionCode);
                this.defaultFieldConfig.forEach(dfc=>{
                    let {sectionCode} = JSON.parse(JSON.stringify(dfc));
                    let config = pageFieldConfig.find(f=>f.sectionCode == sectionCode);
                    if(config){
                        this.generalFieldConfig.push(Object.assign({},dfc, config));
                    }else{
                        this.generalFieldConfig.push(dfc);
                    }
                })
                let pageConfig = pageFieldConfig.filter(f=>!defaultSections.includes(f.sectionCode));
                if(pageConfig && pageConfig.length>0){
                    pageConfig.forEach(c=>{
                        //c['sectionName'] = c.section;
                        this.generalFieldConfig.push(c);
                    })
                }
                this.generalFieldConfig.sort((a,b)=>a.seq-b.seq);
                this.pageLoading = false;
            },
            onMove(e,originalEvent){
                let {draggedContext,relatedContext} = e;
                let oldIndex = draggedContext.index;
                let newIndex = relatedContext.index;
                let oldGc = this.generalFieldConfig[oldIndex];
                let newGc = this.generalFieldConfig[newIndex];
                let oldSeq = oldGc.seq;
                let newSeq = newGc.seq;
                this.generalFieldConfig.forEach((gc,index)=>{
                    if(index==oldIndex){
                        this.$set(gc,'seq',newSeq);
                    }
                    if(index==newIndex){
                        this.$set(gc,'seq',oldSeq);
                    }
                })
            },
            changeRequired(configIndex){
                this.generalFieldConfig.forEach((gen,index)=>{
                    if(index==configIndex){
                        let mandatory = gen.mandatory;
                        gen.fieldList.forEach(fl=>{
                            fl.isRequired = mandatory;
                        })
                    }
                })
            },
            getSaveData(){
                let materialInfoSection = this.generalFieldConfig.find(f=>f.sectionCode=='Material Info');
                let {fieldList} = materialInfoSection;
                let mappingCodes = fieldList.filter(f=>f.customerFieldCode);
                if(mappingCodes.length<=0){
                    return this.generalFieldConfig;
                }

                let codes = mappingCodes.map(f=>f.customerFieldCode);
                if(codes.length != new Set(codes).size){
                    //找到具体重复的code
                    let repeatCodes = codes.filter((item, index) => codes.indexOf(item) != index);
                    let names = [];
                    repeatCodes.forEach(rc=>{
                        let repeatFieldLabels = mappingCodes.filter(f=>f.customerFieldCode==rc).map(f=>f.fieldLabel);
                        names.push(...repeatFieldLabels);
                    })
                    this.$notify.error("Customer Field Mapping cannot be duplicated: "+names.join(' , '));
                    return false;
                }

                return this.generalFieldConfig;
            }
        },
        mounted() {
            this.parseDffSelect();
            this.initPage();
        },
        created() {
            this.handlerDefaultConfig();
        },
        watch: {},
        computed: {
        },
        props: {
            fieldConfig:[],
            formPurpose:'',
            standardDffList:[]
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_customer_template_requiredSession {
        font-family: 'Arial' !important;
        background: #fff;
        max-height: 800px;
        overflow: auto;
        table.requiredTable{
            width: 100%;
            border:solid 1px #c8c8c8;
        }
        .move_config_div{
            border: solid 1px #e8e8e8;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 10px;
            box-shadow: 2px 2px 4px #e7e7e7;
        }
    }
</style>