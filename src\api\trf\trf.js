import request from '@/router/axios';

export const getBuyerInfoPage = (page, rows, params) => {
    return request({
        url: '/api/sgsapi/CustomerApi/preOrderCustomer/detail/getCustomerGroupAndSupplyInfo',
        method: 'post',
        params: {
            ...params,
            page,
            rows,
        }
    })
}

export const queryScmCustomerList = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/scm/query',
        method: 'post',
        data: params
    })
}


export const trfDetail = (id) => {
    return request({
        url: '/api/sgs-mart/trf/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const aflTrfDetail = (id) => {
    return request({
        url: '/api/sgs-mart/trf/aflTrfDetail',
        method: 'get',
        params: {
            id,
        }
    })
}




//针对（邮件）推送的 trf detail 链接创建签名信息
export const createSignature = (params) => {
    return request({
        url: '/api/sgs-mart/v2/trf/getSignature',
        method: 'post',
        data: params
    });
}

export const getCustomerTrfListTitleList = () => {
    return request({
        url: '/api/sgs-mart/trf/trfListTitle',
        method: 'get',
    })
}

export const thirdPartyTrfDetail = (id) => {
    var url = '/api/sgs-mart/trf/detail';
    return request({
        url: url,
        method: 'get',
        params: {
            id,
        }
    })
}

export const queryTrfDetail = (params) => {
    let url = '/api/sgs-mart/v2/trf/detail';
    return request({
        url: url,
        method: 'post',
        data: params
    })
}

export const queryCclTrfDetail = (params) => {
    let url = '/api/sgs-mart/v2/ccl/trf/detail';
    return request({
        url: url,
        method: 'post',
        data: params
    })
}

export const printCclTrf = (id) => {
    return request({
        url: '/api/sgs-mart/ccl/trf/printCclTrf',
        method: 'get',
        params: {
            id,
        }
    })
}

export const trfTemplateDetail = (id) => {
    return request({
        url: '/api/sgs-mart/trf/trfTemplatedetail',
        method: 'get',
        params: {
            id,
        }
    })
}

//@Deprecated 可废弃
export const downTrfPDF = (id) => {
    return request({
        url: '/api/sgs-mart/statistics/downTrfPDF',
        method: 'post',
        responseType: 'blob',
        params: {
            id,
        }
    })
}
export const downTrfBarcode1 = (params) => {
    return request({
        url: '/api/sgs-mart/statistics/downTrfBarcode',
        method: 'get',
        responseType: 'blob',
        params: {
            ...params,
        }
    })
}

//@Deprecated 可废弃
export const viewPDF = (params) => {
    return request({
        url: '/api/sgs-mart/statistics/viewPDF',
        method: 'post',
        params: {...params}
    })
}

//@Deprecated 可废弃
export const downTrfBarcode2 = (params) => {
    return request({
        url: '/api/sgs-mart/statistics/viewPDF2',
        method: 'post',
        params: {...params}
    })
}

export const detailForPrint = (id) => {
    return request({
        url: '/api/sgs-mart/trf/detailForPrint',
        method: 'get',
        params: {
            id,
        }
    })
}

export const queryMaterial = (ids, cid) => {
    return request({
        url: '/api/sgs-mart/customer/materials/trfMaterial',
        method: 'get',
        params: {
            ids: ids,
            cid: cid
        }
    })
}

export const getContactList = (params) => {
    return request({
        url: '/api/sgs-mart/customerContact/listByParam',
        method: 'get',
        params: {
            ...params,
        }
    })
}
export const getTemplateList = (template) => {
    return request({
        url: '/api/sgs-mart/template/listByParamNotPage',
        method: 'post',
        data: template
    })
}
export const getTemplateListNoPage = (params) => {
    return request({
        url: '/api/sgs-mart/template/templateList-noPage',
        method: 'get',
        params: {
            ...params,
        }
    })
}
export const getTemplateLabList = (templateLab) => {
    return request({
        url: '/api/sgs-mart/template/templateLablist',
        method: 'post',
        data: templateLab
    })
}
export const returnTrf = (id,signature, flag) => { //只有afl调用
    let url = '/api/sgs-mart/afl/trf/returnTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            signature: signature
        }
    })
}

export const returnTrfV2 = (id,signature) => {
    var url = '/api/sgs-mart/v2/trf/returnTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            signature: signature
        }
    })
}

export const returnCclTrfV2 = (id,signature) => {
    var url = '/api/sgs-mart/v2/ccl/trf/returnCclTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            signature: signature
        }
    })
}



//@Deprecated 可废弃
export const print = (id) => {
    var url = '/api/sgs-mart/trf/print';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
        }
    })
}

export const cancelTrf = (id,signature) => {
    var url = '/api/sgs-mart/afl/trf/cancelTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            signature: signature
        }
    })
}

export const cancelTrfV2 = (id,signature) => {
    var url = '/api/sgs-mart/v2/trf/cancelTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            signature: signature
        }
    })
}

export const cancelCclTrfV2 = (id,signature) => {
    var url = '/api/sgs-mart/v2/ccl/trf/cancelCclTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            signature: signature
        }
    })
}
export const downLoadFile = (cloudId) => {
    return request({
        url: '/api/sgs-mart/trf/downLoadFileByCloudId',
        method: 'post',
        params: {
            cloudId: cloudId,
        }
    })
}
/**
 * 该方法没有返回对象，直接下载文件，cs那边给的就是文件流
 * @param params
 * @param fileName
 */
export const batchDownloadFile = (params,fileName,errorCallback,onlyNeedBlob = false,callBack)=>{
    request({
        url: '/api/sgsapi/FrameWorkApi/file/batchDownloadByCloudID',
        method: 'post',
        responseType:'blob',
        data:params
    }).then(res=>{
        const {data,headers} = res;
        const blob = new Blob([data],{type:headers['content-type']});
        if("application/json" == data.type){
            let fileReader = new FileReader();
            fileReader.readAsText(blob,"utf-8");
            fileReader.onload = ()=>{
                let msg = JSON.parse(fileReader.result);
                if(callBack){
                    callBack(msg);
                }
            }
            return;
        }
        if(onlyNeedBlob){
            callBack(blob);
            return;
        }
        let patt = new RegExp("fileName=([^;]+\\.[^\\.;]+);*");
        let disposition = patt.exec(headers['content-disposition']);
        fileName = disposition && disposition[1] ? disposition[1] : fileName;
        if(fileName.indexOf('.zip')==-1){
            fileName += ".zip";
        }
        let dom = document.createElement('a');
        let url = window.URL.createObjectURL(blob);
        dom.href = url;
        dom.download = decodeURI(fileName);
        dom.style.display = "none";
        document.body.appendChild(dom);
        dom.click();
        dom.parentNode.removeChild(dom);
        window.URL.revokeObjectURL(url);
    }).catch(err=>{
        errorCallback()
    })
}

//查询sgs实验室
export const getSgsLabList = (params) => {
    return request({
        url: '/api/sgs-mart/template/getSgsLablist',
        method: 'get',
        params: {
            ...params,
        }
    })
}
export const getTemplateLabContactList = (templateLabContact) => {
    return request({
        url: '/api/sgs-mart/template/templateLabContactlist',
        method: 'post',
        data: templateLabContact
    })
}
export const getCustomerAddressList = (params) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/listByParamNotPage',
        method: 'get',
        params: {
            ...params,
        }
    })
}
export const getCustomerInvoiceList = (params) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/listByParamNotPage',
        method: 'get',
        params: {
            ...params,
        }
    })
}
export const getCustomerContactList = (params) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/listByParam',
        method: 'get',
        params: {
            ...params,
        }
    })
}

export const getContactDeatil = (id) => {
    return request({
        url: '/api/sgs-mart/customer/contacts/detail',
        method: 'get',
        params: {
            id
        }
    })
}
//查询模板关联的测试包
export const getTemplateTestPackageList = (params) => {
    return request({
        url: '/api/sgs-mart/template/templateTestPackagelist',
        method: 'post',
        data: params
    })
}
//Buyer PK Agent
export const buyerPkAgent = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/buyerPkAgent',
        method: 'get',
        params: {
            ...params
        }
    })
}
export const queryAgentCustomerByParam = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/customers',
        method: 'get',
        params: {
            ...params
        }
    })
}
//新增保存测试申请单
export const saveTrf = (trf) => {
    return request({
        url: '/api/sgs-mart/trf/submit',
        method: 'post',
        data: trf
    })
}

//新增保存CCL测试申请单
export const saveTrfV2 = (trf) => {
    var url = '/api/sgs-mart/v2/trf/submit';///api/sgs-mart/trf/submit
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}

export const saveCclTrfV2 = (trf) => {
    var url = '/api/sgs-mart/v2/ccl/trf/submit';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}

//新增保存CCL测试申请单
export const saveThirdPartyTrf = (trf, flag) => {
    var url = '/api/sgs-mart/trf/submit';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}
export const saveAflTrf = (trf) => {
    var url = '/api/sgs-mart/afl/trf/submit';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}
//确认收样
export const confirmSampleDateV2 = (trf) => {
    var url = '/api/sgs-mart/v2/trf/confirmSampleDate';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}
//确认收样
export const confirmSampleThirdPartyDate = (trf) => {
    var url = '/api/sgs-mart/trf/confirmSampleDate';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}
export const confirmCclSampleDate = (trf) => {// thirdPartyLabDetail
    var url = '/api/sgs-mart/ccl/trf/confirmCclSampleDate';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}
export const confirmAflSampleDate = (trf) => {// thirdPartyLabDetail
    var url = '/api/sgs-mart/trf/confirmSampleDate';
    return request({
        url: url,
        method: 'post',
        data: trf
    })
}
//新增测试申请单模板
export const saveTrfUserTemplate = (trfUserTemplate) => {
    return request({
        url: '/api/sgs-mart/trf/saveTrfTemplate',
        method: 'post',
        data: trfUserTemplate
    })
}

//新增测试申请单模板
export const saveAflTrfUserTemplate = (trfUserTemplate) => {
    return request({
        url: '/api/sgs-mart/afl/trf/saveTrfTemplate',
        method: 'post',
        data: trfUserTemplate
    })
}

export const getReturnSampleArryCN = (language) => {
    let keyGroup = 'ReturnSample';
    if (language == 'zh-CN') {
        keyGroup = 'ReturnSampleCN';
    }
    return request({
        url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=0&SystemID=0&sysKeyGroup=' + keyGroup,
        method: 'get',
    })
}

//传入DictionaryEnums
export const getDictionarySettings = (dicSetting, language) => {
    return request({
        url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary',
        params: {
            bUID: dicSetting.buId,
            SystemID: dicSetting.systemId,
            sysKeyGroup: language === 'zh-CN' ? dicSetting.cnKey : dicSetting.enKey
        },
        method: 'get'
    });
}

export const getReportLanguageArry = (language,buId) => {
    var keyGroup = 'ReportLanguage';
    var url = '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID='+buId+'&SystemID=1&sysKeyGroup=' + keyGroup;
    if (language == 'zh-CN') {
        url = url + "&languageCode=CHI";
    }
    return request({
        url: url,
        method: 'get',
    })
}
export const getReturnSampleArryEN = () => {
    return request({
        url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=0&SystemID=0&sysKeyGroup=ReturnSampleEN',
        method: 'get',
    })
}
//查询申请单列表
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/trf/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

//查询申请单最新Top5
export const getTrf_top = (data) => {
    return request({
        url: '/api/sgs-mart/v2/trf/trf_top',
        method: 'post',
        data: data
    })
}

//查询申请单列表 已废弃，接口已删除
/*export const getList_new = (data) => {
    return request({
        url: '/api/sgs-mart/v2/trf/page_new',
        method: 'post',
        data: data
    })
}*/
//导出申请单列表
export const exportTrfs = (data) => {
    return request({
        url: '/api/sgs-mart/trf/exportTrfs',
        method: 'post',
        data: data
    })
}

export const exportTrfs_new = (data) => {
    return request({
        url: '/api/sgs-mart/v2/trf/exportTrfs',
        method: 'post',
        data: data
    })
}

//查询CCL申请单列表
export const getCclList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/ccl/trf/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getCclListV2 = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/v2/ccl/trf/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

//导出CCL申请单列表
export const exportCclList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/ccl/trf/exportCclList',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}


//查询申请单报告
export const getReport = (trfNo) => {
    return request({
        url: '/api/sgs-mart/trf/report/detail',
        method: 'get',
        params: {
            trfNo
        }
    })
}
//查询TRF 联系人
export const queryTrfCustomerContact = (trfNo) => {
    return request({
        url: '/api/sgs-mart/trf/trfCustomer/contact',
        method: 'get',
        params: {
            trfNo
        }
    })
}


//保存申请单报告
export const saveReport = (form) => {
    return request({
        url: '/api/sgs-mart/trf/report/submit',
        method: 'post',
        data: form
    })
}
export const saveReviewConclusionReport = (form) => {
    return request({
        url: '/api/sgs-mart/trf/report/reviewConclusion/submit',
        method: 'post',
        data: form
    })
}
//保存申请单报告
export const finalizeReport = (form) => {
    return request({
        url: '/api/sgs-mart/trf/report/finalize',
        method: 'post',
        data: form
    })
}
//保存账单数据
export const saveQuotation = (form) => {
    return request({
        url: '/api/sgs-mart/trf/quotation/save',
        method: 'post',
        data: form
    })
}

//保存发票数据
export const saveInvoice = (form) => {
    return request({
        url: '/api/sgs-mart/trf/invoice/save',
        method: 'post',
        data: form
    })
}

export const saveUploadFile = (form) => {
    return request({
        url: '/api/sgs-mart/trf/quotation/saveUploadFile',
        method: 'post',
        data: form
    })
}

/**
 * 查询quotation
 * @param data
 */
export function getQuotation(trfId) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/trf/quotation/details',
        params: {
            trfId
        }
    })
}

/**
 * 查询invoice
 * @param data
 */
export function getInvoice(trfId) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/trf/invoice/details',
        params: {
            trfId
        }
    })
}

export const remove = (id) => {
    return request({
        url: '/api/sgs-mart/trf/quotation/remove',
        method: 'post',
        params: {
            id,
        }
    })
}


export const removeInvoice = (id) => {
    return request({
        url: '/api/sgs-mart/trf/invoice/remove',
        method: 'post',
        params: {
            id,
        }
    })
}

//查询上传的文件
export function queryFile(param) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/trf/quotation/queryFile',
        params: {
            ...param
        }
    })
}

export function selectAflHostListForCurrentUser(param) {
    return request({
        method: 'post',
        url: '/api/sgs-mart/trf/selectAflHostListForCurrentUser',
        params: param
    })
}

export function queryDffFromData(param) {
    return request({
        method: 'post',
        url: '/api/sgsapi/DFFV2Api/dff/queryDff',
        params: param
    })
}

export function getLablist(param) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/lab/listLab',
        params: param
    })
}

export function customerDetail(param) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/customer/registerDetail',
        params: param
    })
}

export function getContactsListbyUser(param) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/customer/contacts/page/by-user?isDeleted=0',
        params: param
    })
}

export function getContactsListForAfl(param) {
    return request({
        method: 'get',
        url: '/api/sgs-mart/customer/contacts/listForAfl',
        params: param
    })
}

export function selectQuotationInfoByTrfId(param) {
    return request({
        method: 'post',
        url: '/api/sgs-mart/afl/trf/selectQuotationInfo',
        data: param
    })
}

export function quotationConfirmed(param) {
    return request({
        method: 'post',
        url: '/api/sgs-mart/afl/trf/quotationConfirmed',
        data: param
    })
}

export function downloadByCloudID(param) {
    return request({
        method: 'get',
        url: '/api/sgsapi/FrameWorkApi/file/downloadByCloudID',
        params: param
    })
}

export function getReportList(param) {
    return request({
        method: 'post',
        url: '/api/sgs-mart/afl/report/page',
        data: param
    })
}

export function getAflTrfTemplateId() {
    return request({
        method: 'post',
        url: '/api/sgs-mart/trf/getAflTrfTemplateId',
    })
}

export const queryTrfReviewConclusion = (trfNo) => {
    var url = '/api/sgs-mart/trf/queryTrfReviewConclusion';
    return request({
        url: url,
        method: 'post',
        params: {
            trfNo: trfNo,
        }
    })
}

export const queryTrfLogs = (params) => {
    return request({
        url: '/api/sgs-mart/log/trfLogs',
        method: 'post',
        data: params
    });
}


export const addComment = (form) => {
    return request({
        url: '/api/sgs-mart/comment/submit',
        method: 'post',
        data: form
    })
}


export const confirmQuotation = (id) => {
    var url = '/api/sgs-mart/trf/quotation/confirmQuotation';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
        }
    })
}
export const rejectQuotation = (param) => {
    var url = '/api/sgs-mart/trf/quotation/rejectQuotation';
    return request({
        url: url,
        method: 'post',
        data: param
        /*params: {
            id:id,
        }*/
    })
}

export const cancelQuotation = (id) => {
    var url = '/api/sgs-mart/trf/quotation/cancelQuotation';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
        }
    })
}

export const confirmInvoice = (id) => {
    var url = '/api/sgs-mart/trf/invoice/confirmInvoice';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
        }
    })
}
export const rejectInvoice = (id) => {
    var url = '/api/sgs-mart/trf/invoice/rejectInvoice';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
        }
    })
}
export const cancelInvoice = (id) => {
    var url = '/api/sgs-mart/trf/invoice/cancelInvoice';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
        }
    })
}
//查询申请单Quotation列表
export const v2GetQuotationList = (data) => {
    return request({
        url: '/api/sgs-mart/v2/trf/quotation/page_quotation',
        method: 'post',
        data: data
    })
}

//查询申请单Invoice列表
export const getInvoiceList = (data) => {
    return request({
        url: '/api/sgs-mart/trf/invoice/page_invoice',
        method: 'post',
        data: data
    })
}

export const exportQuotations = (data) => {
    return request({
        url: '/api/sgs-mart/trf/quotation/exportQuotations',
        method: 'post',
        data: data
    })
}
export const exportInvoices = (data) => {
    return request({
        url: '/api/sgs-mart/trf/invoice/exportInvoices',
        method: 'post',
        data: data
    })
}

//TRF 预览/打印 替换 print 方法
export const v2TrfPrint = (params) => {
    let url = '/api/sgs-mart/v2/trf/print';
    return request({
        url: url,
        method: 'post',
        data: params
    })
}

// 替换 viewPDF 方法
export const previewTrfPDF = (params) => {
    return request({
        url: '/api/sgs-mart/statistics/previewTrfPdf',
        method: 'post',
        params: {...params}
    })
}

// 替换 downTrfBarcode2 方法
export const exportTrfBarCode = (params) => {
    return request({
        url: '/api/sgs-mart/statistics/exportBarCode',
        method: 'post',
        params: {...params}
    })
}

//替换 downTrfPDF 方法
export const downLoadTrfPDF = (params) => {
    return request({
        url: '/api/sgs-mart/statistics/downLoadTrfPdf',
        method: 'post',
        responseType: 'blob',
        params: {...params}
    })
}
export function trfBaseInfo(param) {
    return request({
        method: 'post',
        url: `/trf/detail`,
        data: param
    });
}
//删除TRF
export const removeTrfV2 = (id,trfNo,signature) => {
    var url = '/api/sgs-mart/v2/trf/removeTrf';
    return request({
        url: url,
        method: 'post',
        params: {
            id: id,
            trfNo:trfNo,
            signature: signature
        }
    })
}
//AFL DFF Grid上传

export const uploadAflGridExcelData = (dffGridData) => {
    return request({
      headers:{'content-type': 'application/x-www-form-urlencoded'},
      url: '/api/sgs-mart/afl/trf/uploadAflGridExcelData',
      method: 'post',
      data: dffGridData
    })
  }

export const updateCommentReadStatus = (param)=>{
    return request({
        url: '/api/sgs-mart/comment/read',
        method: 'post',
        data: param
    })
}
//转换动态表单数据到标准结构
export const convertDff = (params) => {
    var url = '/api/sgs-form/form/convert/dff';
    return request({
        url: url,
        method: 'post',
        data:params
    })
}
//解析标准接口，转换为动态表单属性
export const convertDesign = (params) => {
    var url = '/api/sgs-form/form/convert/design';
    return request({
        url: url,
        method: 'post',
        data:params
    })
}
export const batchUploadReportAttachment = (params)=>{
    return request({
        url: '/api/sgs-mart/trf/report/batchUploadDoc',
        method: 'post',
        data: params
    })
}

export const detailForCopy = (params) => {
    let url = '/api/sgs-mart/v2/trf/detailForCopy';
    return request({
        url: url,
        method: 'post',
        data: params
    })
}
export const exportTrfReportFile = (params)=>{
    let url = '/api/sgs-mart/trf/report/batchDownloadReportFileByTrfId'
    return request({
        url,
        method:'post',
        data:params
    })
}
export const submitRelation = (params)=>{
    return request({
        url: '/api/sgs-mart/trfEfilingRelation/submitRelation',
        method: 'post',
        data: params
    })
}

export const updateTrfCustomer = (params)=>{
    return request({
        url: '/api/sgs-mart/v2/trf/updateTrfCustomer',
        method: 'post',
        data: params
    })
}
export const updateBasicData = (params)=>{
    return request({
        url: '/api/sgs-mart/v2/trf/updateServiceTypeAndLab',
        method: 'post',
        data: params
    })
}
export const getReportFileList = (params)=>{
    return request({
        url:'/api/sgs-mart/search/reportFiles',
        method:'post',
        data:params
    })
}
export const trfListDimensionQuery = (params)=>{
    let url ='/api/sgs-mart/search/dimension/list'
    return axios({
        url,
        method:'POST',
        data:params
    })
}
export const trfListTop5Query = (params)=>{
    let url ='/api/sgs-mart/search/list'
    let timezone = (0 - new Date().getTimezoneOffset())/60;
    return axios({
        url,
        method:'POST',
        data:params,
        headers:{
            timezone
        }
    })
}
export const getReportMatrixMode = (trfId,reportNos)=>{
    let url = '/api/sgs-mart/trf/report/getReportMatrixMode';
    return axios({
        url,
        method:'post',
        data:{
            trfId,reportNos
        }
    })
}

export const syncTrfReportConfirm = (data)=>{
    let url = '/api/sgs-mart/trf/report/syncTrfReportConfirm';
    return axios({
        url,
        method:'post',
        data
    })
}

export const isNewScm = (data)=>{
    let url = '/api/sgs-mart/v2/trf/isNewScm';
    return axios({
        url,
        method:'post',
        data
    })
}

export const queryScmList = (data)=>{
    let url = '/api/sgs-mart/v2/trf/scmList';
    return axios({
        url,
        method:'post',
        data
    })
}
export const queryTestResult = (data,headers)=>{
    headers['systemId'] = 1;
    headers['requestId'] = new Date().getTime();
    let url = '/api/sgsapi/customerbiz/api/v2/trf/previewSyncReport';
    return request({
        url,
        method:'post',
        headers,
        data
    })
}
