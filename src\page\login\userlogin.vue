<template>
    <div>
        <el-form class="login-form" status-icon   ref="loginForm" :model="loginForm" size="medium" label-width="0">
            <el-form-item v-if="tenantMode" prop="tenantId">
                <el-input size="large" @keyup.enter.native="handleLogin" v-model="loginForm.tenantId" auto-complete="off"
                    :placeholder="$t('login.tenantId')">
                    <i slot="prefix" class="icon-quanxian"></i>
                </el-input>
            </el-form-item>
            <el-form-item prop="username"  :rules="{required:true ,message:$t('login.username'),trigger:'blur'}">
                <el-input size="large" @keyup.enter.native="handleLogin" v-model="loginForm.username" auto-complete="off"
                    :placeholder="$t('login.username')">
                    <i slot="prefix"><img src="/img/icon/mobile.png" style="margin-top: 7px;" /></i>
                </el-input>
            </el-form-item>
            <el-form-item prop="password" :rules="{required:true ,message:$t('login.password'),trigger:'blur'}">
                <el-input size="large" @keyup.enter.native="handleLogin" type="password" v-model="loginForm.password"
                    auto-complete="off" :placeholder="$t('login.password')" show-password>
                    <i slot="prefix"><img src="/img/icon/password.png" style="margin-top: 6px;" /></i>
                </el-input>
            </el-form-item>
            <div class="login-menu text-right" style="margin-top: -10px;">
                <!-- <router-link type="text" :to="{ path: '/simpleRegister' }">{{ $t('login.toRegister') }}</router-link> -->
                <el-link @click="openChangePassword" type="primary" :underline="false" style="border-bottom: 1px solid #f60">{{ $t('login.forgetPassword') }}</el-link>
                <!-- <a  style="cursor: pointer;color: #aeacbb;">{{ $t('login.forgetPassword') }}</a> -->
            </div>
            <el-form-item>
                <el-button type="primary" @click.native.prevent="handleLogin" class="login-submit"
                    style="width: 100%;">
                    {{ $t('login.submit') }}
                </el-button>
            </el-form-item>
            <div class="checkbox">
                <el-checkbox v-model="agreeMarketing"
                             style="padding-left: 0;color: #555555">
                </el-checkbox>
                <span style="padding-left: 5px;color:#555555;">
                    {{ $t('login.readMarketing') }}
                        <a @click="openPolicy(2,true)" style="cursor: pointer;color: #FF6600;border-bottom: 1px solid #FF6600 ">
                    {{ $t('agreement.privacyPolicy') }}
                  </a>
                </span>
                <span>
                  {{ $t('login.readMarketingAfter') }}
                </span>

            </div>

        </el-form>

        <el-dialog
                :visible.sync="policyDia"
                width="70%"
                top="5vh"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                append-to-body
                :show-close="justOpenPolicy"
                @close="closePolicy"
        >
            <policy
                    v-if="policyDia"
                    :tabs-index="tabsIndex"
                    :just-open-policy="justOpenPolicy"
                    @agree="agreePolicy"
            ></policy>
        </el-dialog>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import website from '@/config/website';
import policy from "./policy";
import { saveLoginLog ,updatePolicy,queryBuSetting} from "@/api/common/index";
import {getToken } from '@/util/auth'

export default {
    name: "userlogin",
    inject: ['reload'],
    data() {
        return {
            policyDia:false,
            agreeMarketing: false,
            clauseVisible: false,
            myErrors: {},
            tenantMode: website.tenantMode,
            justOpenPolicy:false,
            tabsIndex:1,
            loginForm: {
                tenantId: "000000",
                username: "",
                password: "",
                type: "account"
            },
            userMgtId:'',
            extraWeb: this.$route.query.extraWeb,
            redirect: this.$route.query.redirect,

        };
    },
    created() {
        //this.reload();
    },
    mounted() {
    },
    components:{
        policy
    },
    computed: {
        ...mapGetters(["tagWel", "language"]),
    },
    props: [],
    methods: {
        closePolicy(){
            if(this.justOpenPolicy){
               return
            }
            this.$store.dispatch("LogOut");
        },
        agreePolicy(flag){
            if(!flag || this.justOpenPolicy){
                this.policyDia = false;
                return;
            }
            this.continueLogin();
        },
        openPolicy(tabsIndex,justOpenPolicy){
            this.justOpenPolicy = justOpenPolicy;
            this.tabsIndex = tabsIndex;
            this.policyDia = true;
        },
        openChangePassword() {
            window.open(this.changePasswordUrl);
        },
        openPrivacy() {
            if (this.language == 'en-US') {
                window.open("https://www.sgsgroup.com.cn/en-cn/privacy-at-sgs", "_blank");
            } else {
                window.open("https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs", "_blank");
            }

        },
        //通用服务条款
        openClauseVisible() {
            if (this.language == 'en-US') {
                window.open("https://www.sgs.com/-/media/sgscorp/documents/corporate/technical-documents/legal-documents/conditions-of-services/sgs-general-conditions-of-service-for-china-en.cdn.en.pdf", "_blank");
            } else {
                window.open("https://www.sgs.com/-/media/sgscorp/documents/corporate/technical-documents/legal-documents/conditions-of-services/sgs-legal-general-conditions-of-services-cn.cdn.en.pdf", "_blank");
            }
        },
        continueLogin(){
            //重新刷新路由
            let param = {
                account:this.loginForm.username
            }
            this.reload();
            const loading = this.$loading({
                lock: true,
                text: 'Login...',
                spinner: "el-icon-loading"
            });
            //保存Log
            saveLoginLog().then(res => {})
            //更新policy
            updatePolicy({userMgtId:this.userMgtId}).then(res=>{}).catch(err=>{})
            console.log('this.tagWel::::', this.tagWel)
            console.log("参数====",this.$route.query.redirectedFrom)
            console.log(this.$route.query)
            let skipUrl = this.tagWel.value;
            let systemId = this.$route.query.systemId;
            if(this.$route.query.redirectedFrom){
                skipUrl = this.$route.query.redirectedFrom;
            }
            //获取systemId 如果存在，则去cs获取配置，如果不存在，则是smart内部跳转
            if(systemId){
                let params ={
                    systemId:15,
                    groupCode:'SGSSmartRedirectedUrl',
                    productLineCode:'SGS',
                    paramCode:systemId
                }
                queryBuSetting(params).then(res=>{
                    loading.close();
                    if(res.data && res.data.data && res.data.data.length>0){
                        let configObj = res.data.data[0];
                        let {paramValue} = configObj;
                        //没有配置地址，走默认跳转逻辑
                        if(!paramValue){
                            this.$router.push({ path: skipUrl,query:this.$route.query });
                            return;
                        }
                        //有配置地址，按照配置地址跳转
                        let token = getToken();
                        let newSkipUrl = encodeURIComponent(skipUrl)+"&token="+token+"&systemId="+systemId;
                        let rootUrl = paramValue;
                        let redirectUrl = rootUrl+newSkipUrl;
                        window.location.href = redirectUrl;
                    }else{
                        this.$router.push({ path: skipUrl,query:this.$route.query });
                        loading.close();
                    }
                },error=>{
                    this.$router.push({ path: skipUrl,query:this.$route.query });
                    loading.close();
                }).catch(e=>{
                    this.$router.push({ path: skipUrl,query:this.$route.query });
                    loading.close();
                })
            }else{
                if (this.extraWeb) {
                    window.location.href = this.redirect;
                } else {
                    this.$router.push({ path: skipUrl,query:this.$route.query });
                }
                loading.close();
            }
            //this.$router.push({ path: this.tagWel.value });
        },
        handleLogin() {
            //验证是否同意条款 负责不予登录
            this.$refs.loginForm.validate(valid => {
                if (valid) {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Login...',
                        spinner: "el-icon-loading"
                    });
                    console.log("登录校验结果--》" + valid);
                    let param = Object.assign({},this.loginForm,{receiveMarketingCommunication:this.agreeMarketing?1:0});
                    this.$store.dispatch("LoginByUsername", param).then(({tcpp,userMgtId}) => {
                        console.log("登录校验结果--》" , valid);
                        console.log("登录跳转地址==》" , this.tagWel.value);
                        console.log("路由信息==》" , this.$router);
                        console.log(this.$router);
                        this.userMgtId = userMgtId;
                        if(tcpp-0!=1){
                            loading.close()
                            this.openPolicy(1,false)
                            return
                        }
                        this.continueLogin();
                    }).catch(() => {
                        loading.close()
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.second p {
    text-indent: 2em;
}

.modal-body {
    overflow: auto;
    height: 600px;
}

.login-submit {
    height: 48px;
    margin-top: 10px;
}

.login-form {
    /deep/ .el-input__inner {
        height: 40px;
        padding-left: 36px !important;
    }
}

.el-input__prefix {
    line-height: 45px;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #555555;
}
</style>
