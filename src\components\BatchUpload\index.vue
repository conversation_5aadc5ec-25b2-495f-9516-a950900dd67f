<template>
  <div>
    <el-dialog v-model="dialogVisible">
      <el-form >
        <el-row v-if="ifAttachmentType">
          <el-form-item label="Attachment Type" class="control-label">
            <el-col :span="20">
              <el-select
                filterable
                style="width: 100%"
                v-model="attachmentType">
                <el-option
                  v-for="item in attachmentTypeOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row v-if="showDesc">
          <el-col>
              <el-form-item label="Description">
                <el-input type="textarea" v-model="description" :row="3"></el-input>
              </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="file-preview">
        <div class="file-drop-zone">
          <el-row>
            <el-upload
              ref="uploadRef"
              list-type="picture-card"
              :drag="true"
              :limit="limit"
              :http-request="httpRequest"
              :on-exceed="handleExceed"
              :before-upload="beforeUpload"
              :file-list="fileList"
              multiple
            >
              <template #default>
                <div class="upload-plus-icon">
                  <el-icon><Plus /></el-icon>
                </div>
              </template>
              <template #file="{ file }">
                <span class="upload-list__item" style="text-align: center">
                  <span class="el-icon-document">
                    <el-icon><Document /></el-icon>
                  </span>
                  <span>
                    <p class="file-name">{{ file.name }}</p>
                  </span>
                  <span>
                    <p class="file-name" style="margin-top: -10px">
                      ({{ (file.size / 1024).toFixed(2) }} KB)
                    </p>
                  </span>
                </span>
                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                  >
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </template>
            </el-upload>
          </el-row>
          <el-row style="margin-top: 15px">
            <el-input v-model="fileCaptionName" readonly>
              <template #suffix>
                <el-button
                  type="warning"
                  :icon="Delete"
                  style="margin-right: -8px"
                  @click="removeAll"
                >
                  Remove
                </el-button>
                &nbsp;
                <el-button
                  type="warning"
                  :icon="Upload"
                  @click="upload"
                  :loading="uploadLoading"
                >
                  Upload
                </el-button>
              </template>
            </el-input>
          </el-row>
          <el-row>
            <p style="color: #bbb; font-size: 10px; margin-top: 15px">
              {{ remind }}
            </p>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { doUpload } from '@/api/common'
import { ElNotification } from 'element-plus'
import { Delete, Upload, Plus, Document } from '@element-plus/icons-vue'

const props = defineProps({
  accept: {
    type: null,
    default: '*',
  },
  limit: {
    type: Number,
    default: 100000,
  },
  systemID: {
    type: Number,
    default: '',
  },
  uploadUrl: {
    type: null,
    default: '',
  },
  handleUploadSuccess: {
    type: Function,
    default: null,
  },
  handleUploadError: {
    type: Function,
    default: null,
  },
  fileMaxSizes: {
    type: Number,
    default: 100,
  },
  attachmentTypeOptions: {
    type: null,
    default: () => [],
  },
  attachmentTypeDefaultValue: {
    type: null,
    default: '',
  },
  showDesc:{
    type:Boolean,
    default:false
  }
})

const emits = defineEmits(['update:editorContent'])
const description = ref('');
const ifAttachmentType = ref(false)
const attachmentType = ref('')
const fileMaxSize = ref(100)
const dialogVisible = ref(false)
const file = ref('')
const fileCaptionName = ref('')
const fileList = ref([])
const fileNameArray = ref([])
const remind = ref('')
const uploadLoading = ref(false)
const uploadRef = ref(null)

const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

const handleRemove = (file) => {
  const index = fileList.value.findIndex((item) => item.name === file.name)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
  const nameIndex = fileNameArray.value.indexOf(file.name)
  if (nameIndex > -1) {
    fileNameArray.value.splice(nameIndex, 1)
  }
  if (fileList.value.length === 0) {
    fileCaptionName.value = file.name
  } else {
    fileCaptionName.value = `${fileList.value.length} files selected`
  }
  if (fileList.value.length === 1) {
    fileCaptionName.value = fileNameArray.value[0]
  } else if (fileList.value.length === 0) {
    fileNameArray.value = []
    fileCaptionName.value = ''
  }
  uploadRef.value.handleRemove(file)
}

const removeAll = () => {
  uploadRef.value.clearFiles()
  fileList.value = []
  fileNameArray.value = []
  fileCaptionName.value = ''
}

const httpRequest = (option) => {
  fileList.value.push(option.file)
}

const beforeUpload = (file) => {
  if (fileNameArray.value.indexOf(file.name) > -1) {
      ElNotification.warning('The file has been uploaded')
    return false
  }
  const fileSize = file.size
  const maxSize = fileMaxSize.value * 1024 * 1024
  if (fileSize > maxSize) {
      ElNotification.warning(`file size should be less than ${fileMaxSize.value}MB`)
    return false
  }
  let fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toUpperCase()
  let accept = props.accept.toUpperCase()
  if (props.accept !== '*' && accept.indexOf(fileSuffix) < 0) {
      ElNotification.warning(`The file type must be ${props.accept}`)
    return false
  }
  if (fileList.value.length === 0) {
    fileCaptionName.value = file.name
  } else {
    fileCaptionName.value = `${fileList.value.length + 1} files selected`
  }
  fileNameArray.value.push(file.name)
}

const handleExceed = () => {
    ElNotification.warning(`You can upload ${props.limit} files at most`)
}

const upload = () => {
  if (!fileList.value || fileList.value.length === 0) {
      ElNotification.warning('Please select the file to upload')
    return false
  }
  uploadLoading.value = true
  const formData = new FormData()
  for (const file of fileList.value) {
    formData.append('file', file)
  }
  formData.append('systemID', props.systemID)
  const uploadUrl = props.uploadUrl
    ? props.uploadUrl
    : '/api/sgsapi/FrameWorkApi/file/doUpload'
  doUpload(formData, props.uploadUrl)
    .then((res) => {
      uploadLoading.value = false
      if(res.code === 200){
          ElNotification.success('Upload Success')
        removeAll()
        const param = {
          data: res.data.data,
          attachmentType: attachmentType.value,
        }
        props.handleUploadSuccess(param)
      }
    })
    .catch((error) => {
      uploadLoading.value = false
    })
}

onMounted(() => {
  if (props.fileMaxSizes) {
    fileMaxSize.value = props.fileMaxSizes
  }
  if (props.accept === '*') {
    remind.value = `Document size should be less than ${fileMaxSize.value}M.`
  } else {
    remind.value = `The file suffix can be ${props.accept},Document size should be less than ${fileMaxSize.value}M.`
  }
  if (props.attachmentTypeOptions && props.attachmentTypeOptions.length > 0) {
    attachmentType.value = props.attachmentTypeDefaultValue
    ifAttachmentType.value = true
  }
})

defineExpose({
  open,
  close,
})
</script>
<style scoped>
.el-upload__input {
  display: none !important;
}

.file-preview {
  border-radius: 5px;
  border: 1px solid #ddd;
  padding: 5px;
  width: 100%;
  margin-bottom: 5px;
}

.file-drop-zone {
  border: 1px dashed #aaa;
  border-radius: 4px;
  height: 100%;
  margin: 12px 15px 12px 12px;
  padding: 5px;
}

.file-drop-zone .el-icon-document {
  font-size: 80px;
  margin-top: 15px;
  width: 146px;
  height: 90px;
}

.file-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  padding-top: 4px;
  font-size: 11px;
  color: #777;
  margin: 5px auto;
}

:deep(.upload_btn):focus,
.upload_btn:hover,
.upload_btn:active {
  background: #ff8533 !important;
  border: 1px solid #f60 !important;
  color: #fff !important;
}

.el-upload-dragger {
  width: 146px !important;
  height: 146px !important;
}

.set-other-btn {
  color: #fff;
  background-color: #fa4eab;
  border-color: #fa4eab;
}
.set-other-btn:hover,
.set-other-btn:focus {
  background-color: #fe83c6;
  border-color: #fe83c6;
}

.file-drop-zone .el-button--warning {
  background-color: #ff6600;
  border-color: #ff6600;
}

.rt-input :deep(.el-input__inner) {
  color: rgb(92, 112, 234) !important;
  cursor: pointer;
}
.upload-list__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-plus-icon {
  width: 130px;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #aaaaaa;
}
</style>
