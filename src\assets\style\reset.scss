@use './unit.module.scss' as *;

:root {
  --el-color-white: #ffffff;
  --el-color-black: #000000;
  --el-color-primary: #ff6600;
  --el-color-primary-light-3: #ff8800;
  --el-color-primary-light-5: #ff6600;
  --el-color-primary-light-7: #ff8800;
  --el-color-primary-light-8: #fdf6ec;
  --el-color-primary-light-9: #fdf6ec;
  --el-color-primary-dark-2: #ff6600;

  --el-color-success: #67c23a;
  --el-color-success-light-3: #95d475;
  --el-color-success-light-5: #b3e19d;
  --el-color-success-light-7: #d1edc4;
  --el-color-success-light-8: #e1f3d8;
  --el-color-success-light-9: #f0f9eb;
  --el-color-success-dark-2: #529b2e;

  --el-color-warning: #ca4300;
  --el-color-warning-light-3: #eebe77;
  --el-color-warning-light-5: #f3d19e;
  --el-color-warning-light-7: #f8e3c5;
  --el-color-warning-light-8: #faecd8;
  --el-color-warning-light-9: #fdf6ec;
  --el-color-warning-dark-2: #ff5500;

  --el-color-danger: #f00;
  --el-color-danger-light-3: #f89898;
  --el-color-danger-light-5: #fab6b6;
  --el-color-danger-light-7: #fcd3d3;
  --el-color-danger-light-8: #fde2e2;
  --el-color-danger-light-9: #fef0f0;
  --el-color-danger-dark-2: #f00;

  --el-color-error: #f56c6c;
  --el-color-error-light-3: #f89898;
  --el-color-error-light-5: #fab6b6;
  --el-color-error-light-7: #fcd3d3;
  --el-color-error-light-8: #fde2e2;
  --el-color-error-light-9: #fef0f0;
  --el-color-error-dark-2: #c45656;

  --el-color-info: #909399;
  --el-color-info-light-3: #b1b3b8;
  --el-color-info-light-5: #c8c9cc;
  --el-color-info-light-7: #dedfe0;
  --el-color-info-light-8: #e9e9eb;
  --el-color-info-light-9: #f4f4f5;
  --el-color-info-dark-2: #73767a;

  --el-bg-color: #ffffff;
  --el-bg-color-page: #f2f3f5;
  --el-bg-color-overlay: #ffffff;
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #a8abb2;
  --el-text-color-disabled: #c0c4cc;
  --el-border-color: #dcdfe6;
  --el-border-color-light: #e4e7ed;
  --el-border-color-lighter: #ebeef5;
  --el-border-color-extra-light: #f2f6fc;
  --el-border-color-dark: #d4d7de;
  --el-border-color-darker: #cdd0d6;
  --el-fill-color: #f0f2f5;
  --el-fill-color-light: #f5f7fa;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-dark: #ebedf0;
  --el-fill-color-darker: #e6e8eb;
  --el-fill-color-blank: #ffffff;
  --el-box-shadow:
    0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
  --el-box-shadow-dark:
    0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12),
    0px 8px 16px -8px rgba(0, 0, 0, 0.16);
  --el-disabled-bg-color: var(--el-fill-color-light);
  --el-disabled-text-color: var(--el-text-color-placeholder);
  --el-disabled-border-color: var(--el-border-color-light);
  --el-overlay-color: rgba(0, 0, 0, 0.8);
  --el-overlay-color-light: rgba(0, 0, 0, 0.7);
  --el-overlay-color-lighter: rgba(0, 0, 0, 0.5);
  --el-mask-color: rgba(255, 255, 255, 0.9);
  --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
  --el-border-width: 1px;
  --el-border-style: solid;
  --el-border-color-hover: var(--el-text-color-disabled);
  --el-border: var(--el-border-width) var(--el-border-style)
    var(--el-border-color);
  --el-svg-monochrome-grey: var(--el-border-color);
  --el-menu-horizontal-height: 81px;
  --el-menu-base-level-padding: 14px;
  --el-menu-hover-text-color: var(--el-color-primary);
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
body {
  font-family:
    'Regular', Arial, 'localArial', 'Microsoft Yahei', 'Hiragino Sans GB',
    'Heiti SC', 'WenQuanYi Micro Hei', sans-serif !important;
}

#app {
  margin: 0 auto;
  padding: 0;
  min-height: 100vh;
  background: $background-color;
}

.breadcrumb {
  margin: 7px 0;

  span {
    font-size: 12px;

    &:first-child {
      color: #878787;
    }
  }

  svg {
    width: 15px;
    height: 9px;
    margin: 0 7px;
    color: #999;
  }
}
a {
  color: #656565;
  transition: all 0.2s;
  text-decoration: none;
}
a:hover {
  color: #f60;
  text-decoration: none;
}
a:focus {
  color: #f60;
  text-decoration: none;
}
.icon-primary {
  color: var(--el-color-primary);
}
.font-weight-bold {
  font-weight: bold;
}
.el-table {
  th {
    background-color: #f5f5f5 !important;
    font-size: 14px;

    font-weight: 500;
    color: #000000;
    line-height: 22px;
    //height: 64px;
  }
}
// .el-input__inner,
// .el-textarea.is-disabled .el-textarea__inner {
//   border: 0 !important;
//   border-bottom: 1px solid #1b1b1b !important;
//   border-radius: 0 !important;
//   &:hover {
//     border-color: #1b1b1b !important;
//   }
// }
.el-input-group__prepend {
  border: 0;
  border-bottom: 1px solid #1b1b1b;
  border-radius: 0;
  background-color: #fff;
}
.el-input-group__append {
  border: 0;
  border-bottom: 1px solid #1b1b1b;
  border-radius: 0;
  background-color: #fff;
}
.el-input__wrapper,
.el-select__wrapper {
  border: 0;
  border-bottom: 1px solid #1b1b1b;
  border-radius: 0;
  background-color: #fff;
  box-shadow: none;
  padding: 1px 0;
}
.el-input__wrapper.is-focus {
  box-shadow: none;
}
.el-input__wrapper:hover {
  box-shadow: none;
}
.el-input__wrapper.is-disabled {
  box-shadow: none;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  border-radius: 0;
}
.el-input.is-disabled .el-input__wrapper {
  box-shadow: none;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  border-radius: 0;
}
.el-select__wrapper.is-disabled {
  box-shadow: none;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  border-radius: 0;
}
.el-select__wrapper.is-disabled,
.el-select__wrapper.is-disabled:hover {
  box-shadow: none;
}
.el-select__wrapper.is-hovering:not(.is-focused) {
  box-shadow: none;
}
.el-select__wrapper.is-focused {
  box-shadow: none;
}

.el-collapse-item__content {
  padding-bottom: 0;
}

.el-collapse-item__wrap {
  border-bottom: 0;
}
/*  */
.el-descriptions__label {
  text-align: right;
  width: 140px;
  display: inline-block;
  font-weight: normal;
  color: $text-color-value;
}
.el-descriptions__label:not(.is-bordered-label) {
  color: $text-color-value;
}
.el-descriptions__content:not(.is-bordered-label) {
  color: $text-color;
}
.el-button {
  border-radius: 0;
}
.el-select__selection.is-near {
  margin-left: 0;
}
.el-form .is-required .el-form-item__content {
  padding-left: 10px;
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-sub-menu__title {
  padding: 6px 10px;
  height: auto;
}
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  color: $primary-color;
}
.el-sub-menu__title:hover {
  color: $primary-color !important;
}
.top-bar__title {
  padding: 0 100px 0 115px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: inherit;
  font-weight: 400;
  .el-menu-item {
    height: 80px !important;
  }
}
.top-bar__title .navigation-item-wrapper .el-sub-menu .el-sub-menu__title {
  height: 80px !important;
  font-size: 20px;
  > span {
    position: relative;
    top: 2px;
  }
}
.el-pagination {
  justify-content: end;
  margin-top: $module-margin-vertical;
  .el-input__wrapper {
    border-bottom: none;
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color))
      inset;
  }
  .el-select__wrapper {
    border-bottom: none;
    box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
    text-align: center;
  }
}

table span.strikethrough {
  text-decoration: line-through;
}

.sgs-footer {
  height: 70px;
  text-align: center;
  vertical-align: middle;
  line-height: 70px;
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  z-index: 1000;
  background-color: #fff;
  border-top: 1px solid #e6e6e6;
}

// 按钮样式类
.custom-primary-button {
  border-radius: 20px;
  border-color: $primary-color;
  color: $primary-color;
  background-color: #fff;
}
.custom-info-button {
  background-color: #fff;
  border-color: #909399;
  color: #909399;
  border-radius: 20px;
}
.button-default {
  border: 1px solid rgb(220, 223, 230);
  font-size: 12px;
}
.button-default:hover {
  color: rgb(255, 102, 0);
  background-color: rgb(255, 240, 230);
  border: 1px solid rgb(255, 209, 179);
}
.button-default-focus {
  font-size: 12px;
  color: rgb(255, 102, 0);
  background-color: rgb(255, 240, 230);
  border: 1px solid rgb(255, 209, 179);
}
.reviewConclusion .el-input .el-input__inner {
  border-bottom: 1px solid #c0c4cc !important;
}
.el-date-editor .el-range-input {
  border: none !important;
}
