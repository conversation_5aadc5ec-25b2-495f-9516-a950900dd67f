<template>
    <div class="row" style="background-color: #ffffff;padding:0px;" v-if="hiddenButton">
        <div class="page-no-print" style="position:fixed; right: 100px; top: 100px;z-index:100; ">
            <el-button v-loading.fullscreen.lock="fullscreenLoading" type="primary" class="btn sgs_btn"
                       @click="handlePrintNew">{{$t('operation.print')}}
            </el-button>
            <!--<el-button  v-loading.fullscreen.lock="fullscreenLoading" class="btn sgs_btn" @click="getPdf('pdf')">{{$t('operation.downLoad')}}</el-button>-->
            <el-button v-loading.fullscreen.lock="fullscreenLoading" type="primary" class="btn sgs_btn"
                       @click="handlePrintBarCode">{{$t('trf.downLoadBarcode')}}
            </el-button>
        </div>
        <el-row>
            <el-col :span="24">
               <!-- <div id="htmlView" v-if="template.formType!=2" v-html="htmlView"></div>-->
                <div id="htmlView">
                    <preview-t-r-f
                            :data="trfObj"
                            :form-template="template"
                    ></preview-t-r-f>
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="18" :offset="3">
                <div v-show="false" id="htmlView2" style="padding:50px 0px;" v-html="htmlView2"></div>
            </el-col>
        </el-row>

    </div>
</template>
<script>

    import {previewTrfPDF, v2TrfPrint, exportTrfBarCode, downLoadTrfPDF} from '@/api/trf/trf'
    import {fileExport, fileExportData} from '@/views/util'
    import html2Canvas from 'html2canvas'
    import {mapGetters} from "vuex";
    import JsPDF from 'jspdf'
    import moment from 'moment'
    import PreviewTRF from "./previewTRF";

    const A4_WIDTH = 595.28
    const A4_HEIGHT = 841.89
    export default {
        name: 'PreviewQVC',
        components: {PreviewTRF},
        provide(){
            return{
                loadFinish:this.loadEnd
            }
        },
        data() {
            return {
                htmlView: "",
                htmlView2: "",
                trfNo: '',
                title: "PDF",
                fullscreenLoading: false,
                hiddenButton: true,
                loadingObj:null,
                trfObj:{},
                template:{},
                showForm:false,
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language"]),
        },
        beforeDestroy() {
            window.removeEventListener("beforeprint",this.beforePrint);
            window.removeEventListener("afterprint",this.afterPrint);
        },
        mounted() {
            window.addEventListener("beforeprint",this.beforePrint);
            window.addEventListener("afterprint",this.afterPrint);
        },
        created: function () {

            this.viewPDF();
            //this.title = this.userInfo.
        },
        watch: {
            //监听语言变化
            language: function (newVal) {
                this.viewPDF();
            }
        },
        methods: {
            handlePrintNew(){
                var printDate = moment(new Date()).format("DD-MM-YYYY HH:mm:ss");
                var userName = this.userInfo.userName;
                document.getElementById("printDate_span").innerHTML = this.$t('trf.printTime') + printDate + "  by " + userName;
                setTimeout(() => {
                    window.print();//打印
                }, 500)
                //记录日志打印
                v2TrfPrint(Object.assign(this.$route.query, {trfId: this.$route.query.id})).then(res => {}, error => {});
            },
            handlePrintBarCode() {
                const style = `
                  <style>
                      body{
                        min-width:1000px;
                      }
                      .avue-form__menu,.el-table__fixed-right,.avue-crud__search,.avue-crud__menu, .avue-crud__pagination,.avue-crud__tip{
                        display: none;
                      }
                      .el-select,
                      .el-date-editor.el-input,
                      .el-date-editor.el-input__inner,
                      .el-cascader,
                      .avue-input-number {
                        width: 100% !important;
                      }
                  </style >`;
                let domHtml = '';
                let allHtml = document.documentElement.innerHTML;
                let bodyHtmlStart = allHtml.substr(0, allHtml.indexOf('<body'));
                bodyHtmlStart = bodyHtmlStart + '<body >';
                let bodyHtmlEnd = '</body>';
                var id = 'htmlView2';
                if (id) {
                    let obj = document.getElementById(id);
                    if (!obj) {
                        return false;
                    }
                    domHtml = '<body>' + style + document.getElementById(id).innerHTML + bodyHtmlEnd;
                }
                var newWin = window.open("");//新打开一个空窗口
                newWin.document.body.innerHTML = domHtml;
                newWin.document.close();//在IE浏览器中使用必须添加这一句
                newWin.focus();//在IE浏览器中使用必须添加这一句
                //加入延迟 防止页面元素为加载
                setTimeout(() => {
                    newWin.print();//打印
                    newWin.close();//关闭窗口
                }, 500)
            },
            loadEnd(){
                if(this.loadingObj){
                    this.loadingObj.close();
                }
            },
            viewPDF: function () {
                this.hiddenButton = false;
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                this.loadingObj = loading;
                previewTrfPDF(Object.assign(this.$route.query,{language: this.language})).then(res => {
                    if(res.data.data.reportUrl){
                      loading.close();
                      window.location.href = res.data.data.reportUrl;
                    }else {
                      this.hiddenButton = true;
                      this.htmlView = res.data.data.trfPdf.html;
                      this.trfObj = res.data.data.trfObj;
                      this.template = res.data.data.template;
                      this.htmlView2 = res.data.data.barCodePdf.html;
                      this.trfNo = res.data.data.trfPdf.trfNo;
                      document.title = this.trfNo;
                      this.fullscreenLoading = false;
                      this.showForm = true;
                      if(this.template.formType!=2){
                          loading.close();
                      }
                    }

                },error=>{
                  this.$notify({
                    title: this.$t('tip'),
                    message: res.data.msg,
                    type: 'warning'
                  });
                  loading.close();
                })
            },
            viewBarcode() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                exportTrfBarCode(this.$route.query).then(res => {
                    this.htmlView2 = res.data
                    setTimeout(() => {
                        this.$Print({
                            id: 'htmlView2'
                        });
                    }, 200)
                    loading.close();
                })
            },
            getBarCode() {
                this.viewBarcode();
            },
            downTrfPDF: function () {
                downLoadTrfPDF(this.$route.query).then(res => {
                    debugger;
                    fileExportData(res.data, this.trfNo + '.pdf')
                    console.log(res);
                    const content = res.data
                    const blob = new Blob([content])
                    const fileName = this.trfNo + '.pdf';
                    if ('download' in document.createElement('a')) {
                        const elink = document.createElement('a')
                        elink.download = fileName
                        elink.style.display = 'none'
                        elink.href = URL.createObjectURL(blob)
                        document.body.appendChild(elink)
                        elink.click()
                        URL.revokeObjectURL(elink.href) // 释放URL 对象
                        document.body.removeChild(elink)
                    } else { // IE10+下载
                        navigator.msSaveBlob(blob, fileName)
                    }
                })
            },
            getPdf() {

                this.fullscreenLoading = true;
                //window.pageYOffset = 0;
                document.documentElement.scrollTop = 0
                document.body.scrollTop = 0
                const vue = this;
                setTimeout(() => {
                    this.getPdfDown(vue);
                }, 500);
            },
            getPdfDown(vue) {
                var title = this.trfNo;
                this.exportPDF(document.querySelector('#htmlView'), title);
            },

            splitPage($dom) {

                const pageOffsetTop = $dom.offsetTop
                const pageOffsetWidth = $dom.offsetWidth
                const pageOffsetHeight = $dom.offsetHeight
                const $unitElements = $dom.querySelectorAll('.minimum-unit')

                const peerPageHeight = pageOffsetWidth / A4_WIDTH * A4_HEIGHT // 获取缩放后的一页页面高度
                const pages = [
                    [
                        {
                            top: 0, // 起点初始化
                            offsetTop: 0
                        }
                    ]
                ]
                // 遍历最小单元格
                // 获取单元格底部距离顶部的高度 top，以及 offsetTop
                // 根据 top 值，算出该单元格的页码，放入数组 pages
                $unitElements.forEach($element => {
                    const offsetTop = $element.offsetTop - pageOffsetTop
                    const top = offsetTop + $element.offsetHeight
                    const pageIndex = parseInt(top / peerPageHeight)

                    // 新的一页
                    if (typeof pages[pageIndex] === 'undefined') {
                        pages[pageIndex] = []
                    }

                    pages[pageIndex].push({
                        top,
                        offsetTop
                    })
                })
                console.log(pages)
                return pages
            },
            exportPDF($dom, filename) {
                // 滚动到页面顶部，避免导出不全
                document.documentElement.scrollTop = 0;
                html2Canvas($dom, {
                    allowTaint: true,
                    scale: 2
                }).then((canvas) => {
                    const pdf = new JsPDF('', 'pt', 'a4')
                    const contentWidth = canvas.width
                    const contentHeight = canvas.height
                    const pageData = canvas.toDataURL('image/jpeg', 1.0)
                    const pageHeight = contentWidth / A4_WIDTH * A4_HEIGHT // 内容缩放后的高度
                    let imgHeight = 592.28 / contentWidth * contentHeight
                    const pages = this.splitPage($dom)
                    pages.forEach((page, index) => {
                        const {offsetTop} = page[0]
                        const {top} = page[page.length - 1]
                        if (index > 0) {
                            pdf.addPage()
                        }
                        pdf.addImage(pageData, 'JPEG', 0, -1 * offsetTop * 2 / contentHeight * imgHeight, A4_WIDTH, imgHeight)
                        if (index < pages.length - 1) {
                            pdf.setDrawColor(0)
                            pdf.setFillColor(255, 255, 255)
                            const surplus = A4_HEIGHT / pageHeight * ((index + 1) * pageHeight - top * 2)
                            pdf.rect(0, (A4_HEIGHT - surplus), A4_WIDTH, surplus, 'F')
                        }
                    })
                    debugger;
                    pdf.save(`${filename}.pdf`)
                    this.fullscreenLoading = false;
                })
            },
            beforePrint(){
            },
            afterPrint(){
                document.getElementById("printDate_span").innerHTML = '';
            },

        }
    }
</script>
<style scoped>
    body {
        font-family: Arial;
        font-size: 14px;
        line-height: 1.42857143;
    }

    #htmlView {
        font-size: 14px !important;
        min-width: 1024px;
    }

    .buyer-supplier-h5 {
        margin-top: 5px;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .logo {
        width: 150px;
    }

    label {
        max-width: 100%;
        margin-bottom: 1px;
        font-size: 10px;
        font-weight: 600;
        cursor: default;
    }

    .inline {
        border-bottom-width: 0.2px;
        border-bottom-style: solid;
        border-bottom-color: black;
        font-size: 12px;
    }

    table {
        width: 100%;
        display: table;
        border-color: 1px solid black;
        border-collapse: collapse;
    }

    tbody {
        display: table-row-group;
        border-color: inherit;
    }

    tr {
        display: table-row;
        vertical-align: inherit;
        border-color: inherit;
    }

    th {
        font-weight: 600;
    }

    td, th {
        display: table-cell;
    }

    .table-bordered > tbody > tr > td,
    .table-bordered > tbody > tr > th,
    .table-bordered > tfoot > tr > td,
    .table-bordered > tfoot > tr > th,
    .table-bordered > thead > tr > td,
    .table-bordered > thead > tr > th {
        border: 0.2px solid black;
        padding: 2px;
    }

    p {
        margin: 2px;
        font-weight: 200;
    }

    @page {
        margin: 15px;
    }

    #testRequest > table {
        border: 1px !important;
    }
</style>
