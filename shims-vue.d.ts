/* eslint-disable */
// declare module '*.vue' {
//   import type { DefineComponent } from 'vue'
//   const component: DefineComponent<{}, {}, any>
//   export default component
// }
declare module 'vuex' {
    import { Store } from 'vuex';
    export function createStore<S>(options: StoreOptions<S>): Store<S>;
}

declare module 'crypto-js' {
    export function MD5(message: string): string;
    export function SHA256(message: string): string;
    // Add other functions as needed
}

declare module 'path-browserify' {
    export function join(...paths: string[]): string;
    export function resolve(...paths: string[]): string;
    // Add other functions as needed
}

declare module 'element-plus/dist/locale/zh-cn.mjs' {
    const zhCn: any;
    export default zhCn;
}