<template>
  <el-drawer
    :title="title"
    :visible="visible"
    @update:visible="$emit('update:visible', false)"
    size="40%"
    direction="rtl">
    <div ref="supplierTestLine" style="width: 100%;height:100%">

    </div>
    <div id="tip" ref="tip" class="tipname" v-if="isShow" v-html="htmlView2"></div>
  </el-drawer>
</template>

<script>
  import * as utils from '@/views/util'
  export default {
    name: 'SupplierDrawer',
    data () {
      return {
        echartsInstance: null,
        isShow:true,
        htmlView2:"",
        option: {
          tooltip : {
            trigger: 'axis',
            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
              type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params, ticket, callback) {
              return utils.formatTip(params, ticket, callback)
            }
          },
          legend: {
            data: [this.$t('dataAnalytics.pass'), this.$t('dataAnalytics.fail'), this.$t('dataAnalytics.seeResult')],
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis:  {
            type: 'value'
          },
          yAxis: {
            minInterval : 1,
            type: 'category',
            data: []
          },
          series: [
            {
              name: 'Pass',
              type: 'bar',
              stack: '总量',
              label: {
                normal: {
                  show: true,
                  position: 'insideRight',
                  formatter: function (num){
                    if (num.value > 0) {
                      return num.value
                    } else {
                      return ''
                    }
                  }
                }
              },
              data: []
            },
            {
              name: 'Fail',
              type: 'bar',
              stack: '总量',
              label: {
                normal: {
                  show: true,
                  position: 'insideRight',
                  formatter: function (num){
                    if (num.value > 0) {
                      return num.value
                    } else {
                      return ''
                    }
                  }
                }
              },
              data: []
            },
            {
              name: 'See Result',
              type: 'bar',
              stack: '总量',
              label: {
                normal: {
                  show: true,
                  position: 'insideRight',
                  formatter: function (num){
                    if (num.value > 0) {
                      return num.value
                    } else {
                      return ''
                    }
                  }
                }
              },
              data: []
            },
          ]
        }
      }
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      optionData: {
        type: Object,
        default: () => {return {}}
      },
      title: String
    },
    watch: {
      optionData: {
        deep: true,
        handler(val, old){
          this.renderEchat()
        }
      }
    },
    methods: {
      renderEchat() {
        if (!this.$refs.supplierTestLine) {
          return;
        }
        if (!this.echartsInstance) {
          this.echartsInstance = echarts.init(this.$refs.supplierTestLine)
        }
        let defaults = this.$lodash.partialRight(this.$lodash.assignInWith, (objValue, srcValue) => srcValue ? srcValue : objValue);
        this.echartsInstance.setOption(defaults(this.option, this.optionData))

        this.echartsInstance.on('mouseover', function (params) {
          if( params.componentType == 'yAxis' ){
            //var tt = $('#tip');
            //var tt = this.refs("tid")
            this.htmlView2 = params.value
            this.isShow = true;
            //tt.html(params.value);
            //tt.css('left', params.event.event.layerX+10);
            //tt.css('top', params.event.event.layerY+20);
            //tt.show();
          }
        });
        this.echartsInstance.on('mouseout', function (params) {
          //$('#tip').hide();
          this.isShow = false;
        })
      }
    },
    beforeDestroy() {
      if (this.echartsInstance) {
        echarts.dispose(this.echartsInstance)
      }
    },
    mounted() {
      this.$nextTick(() => this.renderEchat())
    }
  }
</script>

<style scoped>
  .tipname {
    position: absolute;
    background: rgba(0,0,0,0.5);
    border-radius: 5px;
    max-width: 400px;
    padding: 5px;
    z-index: 1;
    color: #fff;
  }
</style>
