import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/factory/add',
        method: 'post',
        data: form
    })
}

export const updateFactoryStatus = (form) => {
    return request({
        url: '/api/sgs-mart/factory/updateFactoryStatus',
        method: 'post',
        data: form
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/factory/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/factory/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/factory/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/factory/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const setDefault = (id,isDefault) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/set-default',
        method: 'get',
        params: {
            id,
            isDefault
        }
    })
}

