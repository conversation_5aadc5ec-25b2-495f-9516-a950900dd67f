<template>
    <basic-container>
        <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.customerApprove')}}</el-breadcrumb-item>
        </el-breadcrumb>
        <el-row>
            <el-form :inline="true" :model="formInline" size="medium">
                <el-form-item :label="$t('customer.name')">
                    <el-input v-model="query.customerNameZh" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                    <el-button type="primary" @click="add">{{$t('operation.add')}}</el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <el-row>
            <el-table
                :data="tableData"
                style="width: 100%" >
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
                <el-table-column
                        :label="$t('account.title.default')"
                        width="240"
                        align="center">
                    <template scope="scope">
                       <span v-if="scope.row.account.account !== ''">{{scope.row.account.account}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        :show-overflow-tooltip='true'
                        prop="bossNo"
                        width="160"
                        :label="$t('customer.sgs.customerNo')">
                </el-table-column>

                <!--<el-table-column
                        :show-overflow-tooltip='true'
                        prop="customerGroupCode"
                        width="210"
                        :label="$t('term.customerGroupCode')">
                </el-table-column>-->

                <el-table-column
                        :show-overflow-tooltip='true'
                        prop="customerNameZh"
                        :label="$t('customer.name')">
                </el-table-column>


                <el-table-column
                        prop="status"
                        :label="$t('common.status.title')"
                        width="160"
                        align="center">
                    <template scope="scope">
                        <span v-if="scope.row.approveStatus==1">{{$t('customer.status.todo')}}</span>
                        <span v-if="scope.row.approveStatus==10">{{$t('customer.status.emailConfirm')}}</span>
                        <span v-if="scope.row.approveStatus==80">{{$t('customer.status.refused')}}</span>
                        <span v-if="scope.row.approveStatus==90">{{$t('customer.status.approved')}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="updateTime"
                        :label="$t('common.operationTime')"
                        width="120"
                        align="center">
                </el-table-column>
                <el-table-column
                        prop="updateUser"
                        :label="$t('common.operator')"
                        width="120">
                </el-table-column>
                <!--<el-table-column prop="isVip" label="VIP" width="80">
                    <template slot-scope="scope">
                        <el-switch
                                v-model="scope.row.isVip"
                                @change="isDisabledTemplate(scope.row)"
                                active-color="#ff6600"
                                inactive-color="#ff4949"
                                active-text="" :active-value=1  inactive-text=""  :inactive-value=0  >
                        </el-switch>
                    </template>
                </el-table-column>-->
                <el-table-column
                        :label="$t('operation.title')"
                        width="150"
                        align="center">
                    <template slot-scope="scope">
                        <el-button v-if="permissionList.viewBtn" @click="rowView(scope.row)" type="text" size="small" icon="el-icon-view">{{$t('operation.view')}}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-row>

        <customer-detail-dialog :v-if="selectParentDialogVisible"
                                :visible.sync="selectParentDialogVisible"
                                :customerId.sync="form.id"
                                @submit = "onSearch"
        >
        </customer-detail-dialog>
    </basic-container>
</template>

<script>
    import {getList} from "@/api/customer/customerRegister";
    import {updateById} from "@/api/customer/customer";
    import {mapGetters} from "vuex";
    import {validatenull} from "../../../util/validate";
    export default {
        components: {
            CustomerDetailDialog: resolve => require(['../../../components/dialog/customer-detail-dialog'], resolve)
        },
        data() {
            return {
                selectParentDialogVisible: false,
                name: "list",
                tableData: [],
                query:{},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                form: {},
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    viewBtn: this.vaildData(this.permission['sgs:customer:approve:view'],false),
                };
            }
        },
        methods: {
            onSearch() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            add(){
                this.$router.push( {path: '/customer/thirdLabRegister',query:{ id:''}});
            },
            onLoad(page, params = {}) {
                //判断是否有筛选条件
                if(validatenull(this.query.customerNameZh)){
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('register.companyNameBlur'),
                        type: 'warning'
                    });
                    return false;
                }
                getList(page.currentPage, page.pageSize, Object.assign(params, this.query,this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            //分页查询
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.onLoad(this.page);
            },
            rowView(row){
                /*this.form = row;
                this.selectParentDialogVisible=true;*/
                 this.$router.push( {path: '/customer/offlineRegister',query:{ id:row.id}});
            },
            isDisabledTemplate(row){
                this.$confirm(this.$t('common.isChangeStatus'), this.$t('tip'), {
                    confirmButtonText:  this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    updateById( Object.assign({id: row.id, isVip: row.isVip})).then(res => {
                        this.$message({
                            type: 'success',
                            message:  this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                }).catch(() => {
                    if (row.isVip === 1 || row.isVip === '1') {
                        row.isVip = 0;
                    }else {
                        row.isVip = 1;
                    }
                });
            }
        },
        created() {
            debugger;
            if(this.$route.query.searchVal!=undefined){
               this.$set(this.query, 'customerNameZh', this.$route.query.searchVal);
                this.onLoad(this.page);
            }
           // this.onLoad(this.page);
        }
    }
</script>

<style scoped>

</style>
