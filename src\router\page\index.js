import Layout from '@/page/index/'
export default [
    {
        path: '/login',
        name: '登录页',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/login/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'login'
        }
    },
    {
        path: '/sso',
        name: '单点登录',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/login/authredirect'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'sso'
        }
    },{
        path: '/register',
        name: 'register',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/register/register'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'register'
        }
    },{
        path: '/retrievePassword',
        name: 'retrievePassword',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/register/retrievePassword'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'retrievePassword'
        }
    },
    {
        path: '/register/success',
        name: 'Register Success',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/register/success'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'registerSuccess'
        }
    },
    {
        path: '/simpleRegister',
        name: 'simpleRegister',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/simpleRegister/register'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'simpleRegister'
        }
    },/*{
        path: '/retrievePassword',
        name: 'retrievePassword',
        component: () =>
            import( /!* webpackChunkName: "page" *!/ '@/page/register/retrievePassword'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },*/
    {
        path: '/simpleRegister/success',
        name: 'Register Success',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/simpleRegister/success'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'simpleRegisterSuccess'
        }
    },{
        path: '/simpleRegister/successInfo',
        name: 'Register Success',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/simpleRegister/taxNoSuccess'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'simpleRegisterSuccessInfo'
        }
    },
   /* {
        path: '/demo',
        component: Layout,
        redirect: '/demo/index',
        children: [{
            path: 'sample',
            name: 'SampleBreakdown',
            component: () =>
                import( /!* webpackChunkName: "page" *!/ '@/page/demo/sample'),
            meta: {
                keepAlive: true,
                isTab: false,
                isAuth: false,
                i18n: 'demo'
            }
        },
        {
            path: 'pdf',
            name: 'PDF Review',
            component: () =>
                import( /!* webpackChunkName: "page" *!/ '@/page/demo/pdf'),
            meta: {
                keepAlive: true,
                isTab: false,
                isAuth: false,
                i18n: 'pdf'
            }
        } ]
    },*/
    {
        path: '/lock',
        name: '锁屏页',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/lock/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'lock'
        }
    },
    {
            path: '/404',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/error-page/404'),
        name: '404',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: '404'
        }

    },
    {
        path: '/403',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/error-page/403'),
        name: '403',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: '403'
        }
    },
    {
        path: '/500',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/error-page/500'),
        name: '500',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: '500'
        }
    },
    {
        path: '/systemUpdate',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/page/systemUpdate/index'),
        name: 'systemUpdate',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'systemUpdate'
        }
    },

    {
        path: '/paySuccess',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/payment/paySuccess'),
        name: 'paySuccess',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'paySuccess'
        }
    },
    {
        path: '/payError',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/components/payment/payError'),
        name: 'payError',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'payError'
        }
    },
    {
        path: '/',
        name: '主页',
        redirect: '/wel'
    },
    {
        path: '/myiframe',
        component: Layout,
        redirect: '/myiframe',
        children: [{
            path: ":routerPath",
            name: 'iframe',
            component: () =>
                import( /* webpackChunkName: "page" */ '@/components/iframe/main'),
            props: true
        }]

    },
    {
        path: '*',
        redirect: '/404'
    },
    {
        path: '/customer',
        component: Layout,
        //redirect: '/customer/management',
        children: [{
            path: 'management',
            name: '管理',
            meta: {
                i18n: 'customerManagement'
            },
            component: resolve => require([ '@/views/customer/management' ], resolve)
        },{
            path: 'authority',
            name: '授权管理',
            meta: {
                i18n: 'customerAuthority'
            },
            component: resolve => require([ '@/views/customer/authority' ], resolve)
        }]
    },
    {
        path: '/todo/quotationAndInvoice',
        component: Layout,
        //redirect: '/todo/quotationAndInvoice/list',
        children: [{
            path: 'quotationAndInvoice',
            name: '报价 & 对账',
            meta: {
                i18n: 'quotationAndInvoice'
            },
            component: resolve => require([ '@/views/todo/quotationAndInvoice/list' ], resolve)
        }]
    },
    {
        path: '/inspection',
        component: Layout,
        name: 'inspection',
        component: () =>
            import( /* webpackChunkName: "page" */ '@/views/inspection/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'inspection'
        }
    },
    {
        path: '/accountPer',
        component: Layout,
        name: 'accountPer',
        // component: () =>
        //     import( /* webpackChunkName: "page" */ '@/views/accountPer/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false,
            i18n: 'accountPer'
        },
        children: [{
            path: 'index',
            name: '授权展示页',
            meta: {
              i18n: 'accountPer'      
            },
            component: resolve => require([ '@/views/accountPer/index' ], resolve)
        },{
            path: 'authority',
            name: '授权管理',
            meta: {
                i18n: 'accountPerAuthority'
            },
            component: resolve => require([ '@/views/accountPer/authority' ], resolve)
        }]
    },
]
