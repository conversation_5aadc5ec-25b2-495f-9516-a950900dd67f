const router = require("koa-router")();
const Api = require("./../../request");

// protocol 协议
router.post("/api/sgs-mart/protocol/v1/detail", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/protocol/v1/detail", ctx);
  ctx.body = datas;
});

// 协议模板
router.post("/api/sgs-mart/protocol/v1/template/detail", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/protocol/v1/template/detail", ctx);
  ctx.body = datas;
});

// 获取协议模板属性
router.post("/api/sgs-mart/attr/queryAttr", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/attr/queryAttr", ctx);
  ctx.body = datas;
});

// 保存协议模板属性
router.post("/api/sgs-mart/attr/saveAtt", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/attr/saveAtt", ctx);
  ctx.body = datas;
});

// 保存协议模板备注
router.post("/api/sgs-mart/attr/updateAttrRemarkById", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/attr/updateAttrRemarkById", ctx);
  ctx.body = datas;
});

// 协议保存
router.post("/api/sgs-mart/protocol/v1/save", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/protocol/v1/save", ctx);
  ctx.body = datas;
});

module.exports = router;
