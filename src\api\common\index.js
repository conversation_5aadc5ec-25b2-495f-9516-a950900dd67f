import request from '@/router/axios';

export const getDictByCode = (code) => {
    return request({
        url: '/api/nkop-system/dict/dictionary',
        method: 'get',
        params: {
            code,
        }
    })
}
export const getSpecificByProductLineId = (params) => {
    return request({
        url: '/api/sgs-mart/specific/list',
        method: 'get',
        params: {
           ...params,
        }
    })
}
export const getProductLine = () => {
    return request({
        //url: '/sgs-api/FrameWorkApi/trims/api/v1/queryLabList',
        url:'/api/sgs-mart/sgs-api/product-lines',
        method: 'get'
    })
}
export const getDffList = (param) => {
    return request({
        /*url:'/api/sgsapi//DFFV2Api/dff/queryDffFormGeneral',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        type:'application/x-www-form-urlencoded',
        data: {
            customerGroupID: groupID,
            buCode: "SL",
            systemCode: "PreOrder",
            type: "FORM",
            moduleCode: "TRF-Product-Header"
        }*/
        url:'/api/sgs-mart/sgs-api/queryDff',
        method: 'get',
        params: {
            ...param,
        }
    })
}

export const getFilterDffList = (param) => {
    return request({
        url:'/api/sgs-mart/sgs-api/queryDff-filterCustomerGroupCode',
        method: 'get',
        params: {
            ...param,
        }
    })
}
//获取客户组
export const getCustomerGroup = () => {
    return request({
        url: '/api/sgs-mart/sgs-api/customer-groups',
        method: 'get',
    })
}
//条件获取客户组
export const getCustomerGroupByParms = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/queryCustomerGroupsByParam',
        method: 'get',
        params: {
            ...params,
        }
    })
}
//获取客户&客户组
export const getCustomerAndGroup = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/customersAndGroup',
        method: 'post',
        data: params,
    })
}
//条件获取agent客户和客户组信息
export const getAgentCustomerGroupByParms = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/buyerLogo',
        method: 'post',
        data: params
    })
}

//条件获取agent客户和客户组信息
export const getLabLocations = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/queryLabLocations',
        method: 'post',
        data: params
    })
}

//查看文件地址
export const getCloudFileURL = (cloudID) => {
    var formData = new FormData();
    formData.append("cloudID", cloudID);
    return request({
        url: '/api/sgsapi/FrameWorkApi/file/downloadByCloudID?systemID=1&networkType=2',
        method: 'post',
        /*params: {
            cloudID,
        },*/
        data:formData,
        //headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        type:'application/x-www-form-urlencoded',
        /*data:{cloudID:cloudID}*/
    })
}
//根据url获取文件流
export const getFileInputStreamByURL = (pdfUrl) => {
    return request({
        url: '/api/sgs-mart/file/queryFileStreamByUrl',
        method: 'get',
        params: {
            pdfUrl,
        }
    })
}

//根据url获取文件流
export const queryFileUrl = (cloudId) => {
    return request({
        url: '/api/sgs-mart/file/download',
        method: 'get',
        params: {
            cloudId,
        }
    })
}
//获取服务类型
export const getServiceType = (buID,language) => {
    return request({
        url: '/api/sgs-mart/sgs-api/getServiceType',
        method: 'get',
        params: {
            buID,
            language
        }
    })
}
// 模糊查询客户信息
export const getSgsCustomer = (form) => {
    return request({
        url: '/api/sgsapi/CustomerApi/customer/PageInfo/queryCustomerList',
        method: 'post',
        data: form
    })
}
// 模糊查询天眼查公司信息信息
export const getTianyanchaCustomer = (keyWord) => {
    return request({
        url: '/api/sgs-mart/tianyancha-api/search',
        method: 'get',
       params: {
            keyWord,
        }
    })
}

// 查询服务类别
export const serviceTypeList = (language) => {
    return request({
        url: '/api/sgs-mart/serve/list',
        method: 'get',
       params: {
            language
        }
    })
}

// 查询Country And Region
export const getCountryRegions = (language) => {
    return request({
        url: '/api/sgs-mart/sgs-api/queryCountryAndRegion',
        method: 'get',
        params: {
            language
        }
    })
}

export const getDffFormComfirmIdBatchByIds = (dffFormIds) => {
    return request({
        url: '/api/sgs-mart/sgs-api/getDffFormComfirmIdBatchByIds',
        method: 'post',
        data: dffFormIds
    })
}

export const checkDffFormIdSmapleGroup = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/checkFormIsSameGroup',
        method: 'post',
        data: form
    })
}

export const addPowerBiLog = (form) => {
    return request({
        url: '/api/sgs-mart/powerBiLog/add',
        method: 'post',
        data: form
    })
}
//path为请求后缀
export const saveLog = (path) => {
    return request({
        url: '/api/sgs-mart/log/'+path,
        method: 'get',
    })
}
//path为请求后缀
export const saveLoginLog = () => {
    return request({
        url: '/api/sgs-mart/log/saveLoginLog',
        method: 'get',
    })
}

export const queryDictionary = (params)=>{
    return request({
        url: '/api/sgs-mart/sgs-api/getDictionaryData?sysKeyGroup='+params['sysKeyGroup']+"&systemID="+params['systemID'],
        method: 'get',
        data: params
    })
}
//获取字典项数据，可传入语言信息 EN/CHI
export const queryDictionaryByLanguage = (params)=>{
    return request({
        url: '/api/sgsapi/FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?sysKeyGroup='+params['sysKeyGroup']+"&systemID="+params['systemID']+"&languageCode="+params['languageCode'],
        method: 'get',
        data: params
    })
}

export const queryDataEntryUpdate = () => {
    return request({
        url: '/api/sgs-mart/sysUpdate/dataEntryIsUpdate',
        method: 'get',
    })
}

export const updatePolicy = (params)=>{
    return request({
        url:'/api/sgs-mart/customerUser/updateTcpp',
        method:'post',
        params
    })
}

export const queryBuSetting = (params)=>{
    return request({
        url:"/api/sgs-mart/sgs-api/queryBuParamSetting",
        method:'post',
        data:params
    })
}
/**
 * 文件流返回
 * @param url 接口地址
 * @param params 接口参数
 */
export const exportBlob = (url, params) => {
    return request({
      url: url,
      params: params,
      method: 'get',
      responseType: 'blob',
    });
  };
export const queryCustomerDffConfig = (params)=>{
    return request({
        url:"/api/sgs-mart/customer/dffConfig/getConfigByCustomerGroupCode",
        method:'post',
        data:params
    })
}