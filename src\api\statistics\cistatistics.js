import request from '@/router/axios';
/*
export function executiveSummaryStatistics(data) {
  return request({
    method: 'post',
    url: `/cistatistics/executiveSummaryStatistics`,
    data
  })
}*/

export const executiveSummaryStatistics = (preformanceParams) => {
    return request({
        url: '/api/sgs-mart/statistics/executiveSummaryStatistics',
        method: 'post',
        data:preformanceParams
    })
}
export function executiveSummaryStatisticsExcel(param) {
  return request({
    method: 'post',
    responseType: 'blob',
    url: '/api/sgs-mart/statistics/excelExportExceAll',
    data: param
  })
}
export const selectCountTestPerformance = (preformanceParams) => {
    return request({
        url: '/api/sgs-mart/statistics/selectCountTestPerformance',
        method: 'post',
        data:preformanceParams
    })
}
export const getSupplierByTestLine = (preformanceParams) => {
    return request({
        url: '/api/sgs-mart/statistics/testline',
        method: 'post',
        data:preformanceParams
    })
}
export const selectSupplierPerformance = (preformanceParams) => {
    return request({
        url: '/api/sgs-mart/statistics/selectSupplierPerformance',
        method: 'post',
        data:preformanceParams
    })
}
export const selectTestLineBySupplier = (preformanceParams) => {
    return request({
        url: '/api/sgs-mart/statistics/selectTestLineBySupplier',
        method: 'post',
        data:preformanceParams
    })
}

