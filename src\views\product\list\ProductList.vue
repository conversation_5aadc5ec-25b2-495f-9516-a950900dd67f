<template>
  <div class="sgs-content">
    <PageTitle title="Product List" />

    <div class="table-container">
      <div class="operate-block">
        <div>
          <el-dropdown>
            <el-button type="primary" class="create-button">
              Create Product
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showDownloadTemplate">
                  New Product
                </el-dropdown-item>
                <el-dropdown-item @click="showImportTemplate">
                  Import Product
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown>
            <el-button type="primary">
              Create TRF
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="createTrf('individualSample')">
                  Individual Creation
                </el-dropdown-item>
                <el-dropdown-item @click="createTrf('groupSample')">
                  Group Creation
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="operate-block-right">
          <FilterView
            source-type="PRODUCT"
            @changeFilter="changeView"
            @resetFilter="resetForm"
          />
          <ButtonGroup
            :buttonConfigs="buttonGroup"
            @reset="handleReset"
            @export="handleExport"
          />
        </div>
      </div>
      <div :style="{'height': tableMinHeight + 'px'}">
        <el-table
          ref="samplesTable"
          :data="dataList"
          :max-height="tableHeight"
          class="table-header-no-filter"
          fit
          border
          resizable
        >
          <el-table-column
            type="selection"
            fixed="left"
            :selectable="disabledRow"
          ></el-table-column>
          <el-table-column v-for="item in tableHeaderOptions" :prop="item.fieldCode" :label="item.displayName" width="200px">
            <template #header>
              <TableFilter :title="item.displayName">
                <el-input  v-if="item.fieldType === 'input'" :placeholder="item.displayName" clearable size="mini" v-model="searchForm[item.fieldCode]">
                  <template slot="append">
                    <i class="el-icon-search filter-search-icon"></i>
                  </template>
                </el-input>
                <el-select v-if="item.fieldType === 'select'" style="width: 100%" size="mini" v-model="searchForm[item.fieldCode]" filterable clearable
                  :placeholder="item.displayName">
                  <el-option v-for="(st, index) in item.sourceValueList" :key="'mt_' + index" :label="st.name" :value="st.code"></el-option>
                </el-select>
                <el-select v-if="item.fieldType === 'select2'" style="width: 100%" size="mini" v-model="searchForm[item.fieldCode]" filterable clearable multiple
                  :placeholder="item.displayName">
                  <el-option v-for="(st, index) in item.sourceValueList" :key="'mt_' + index" :label="st.name" :value="st.code"></el-option>
                </el-select>
                <el-select
                  v-if="item.fieldCode === 'productLineName'"
                  style="width: 100%"
                  size="small"
                  clearable
                  placeholder="Product Line"
                  filterable
                  v-model="searchForm.buCode"
                >
                  <el-option
                    v-for="(pl, index) of filterList.productLineList"
                    :key="'pl_' + index"
                    :label="pl.productLineName"
                    :value="pl.productLineCode"
                  ></el-option>
                </el-select>
                <el-select
                  v-if="item.fieldCode === 'buyerCustomerGroupName'"
                  style="width: 100%"
                  size="small"
                  clearable
                  placeholder="Buyer"
                  filterable
                  v-model="searchForm.buyerCustomerGroupCode"
                >
                  <el-option
                    v-for="(cl, index) of filterList.customerList"
                    :key="'cl_' + index"
                    :label="cl.customerGroupName"
                    :value="cl.customerGroupCode"
                  ></el-option>
                  <el-option
                    key="cl_general"
                    label="General"
                    value="General"
                  ></el-option>
                </el-select>
                <el-select
                  v-if="item.fieldCode === 'sampleStatusName'"
                  size="small"
                  style="width: 100%"
                  placeholder="Product Status"
                  multiple
                  filterable
                  clearable
                  v-model="searchForm.sampleStatus"
                >
                  <el-option
                    key="PS1"
                    label="In-Progress"
                    value="1"
                  ></el-option>
                  <el-option key="PS2" label="Submitted" value="2"></el-option>
                  <el-option key="PS3" label="Approved" value="3"></el-option>
                  <el-option key="PS4" label="Cancelled" value="4"></el-option>
                  <el-option key="PS5" label="Not In Use" value="5"></el-option>
                </el-select>
              </TableFilter>
            </template>
            <template #default="{ row }">
              <a @click="toDetail('detail', row)" style="color: #ff6600; font-size: 12px; cursor: pointer" v-if="item.fieldCode === 'sampleNo'">{{
                row.sampleNo }}</a>
                <template v-else-if="item.fieldCode==='sampleCategory'">
                    {{row.sampleCategoryName}}
                </template>
              <template v-else-if="item.fieldCode === 'relationships'">
                <span v-if="row.relationships &&  row.relationships.length==1"
                      @click="toTrfDetail(row.relationships[0])"
                      style="color: #ff6600; font-size: 14px; cursor: pointer">
                      {{row.relationships[0].relNo }}</span>
                  <el-popover
                          v-if="row.relationships &&  row.relationships.length>1"
                          placement="top-start"
                          title="TRF No"
                          width="150"
                          trigger="hover"
                          content="">
                      <ul style="width: 100%; list-style: none;padding:0;margin:0;">
                          <li v-for="(rel,reIndex) in row.relationships"
                              :key="'rel_'+reIndex"
                              style="padding: 5px"
                          >
                              <span @click="toTrfDetail(rel)"
                                style="color: #ff6600; font-size: 14px; cursor: pointer">
                                  {{rel.relNo }}</span>
                          </li>
                      </ul>
                      <span slot="reference" style="cursor: pointer">
                          <span @click="toTrfDetail(row.relationships[0])"
                              style="color: #ff6600; font-size: 14px; cursor: pointer">
                                      {{row.relationships[0].relNo}}</span>
                      </span>
                  </el-popover>
              </template>
              <template v-else-if="item.fieldCode === 'sampleStatus'">
                <Tag :color="setStatusColor(row.sampleStatus)">
                  {{ row.sampleStatusName }}
                </Tag>
              </template>
              <template v-else-if="item.fieldCode === 'trfNo'">
                <a  v-if="row.trfNo &&  row.trfNo.length==1"
                    @click="toTrfDetail(row.trfNo[0])"
                    style="color: #ff6600; font-size: 14px; cursor: pointer">
                    {{row.trfNo[0].relNo }}</a>
                <el-popover
                        v-if="row.trfNo &&  row.trfNo.length>1"
                        placement="top-start"
                        title="TRF No"
                        width="150"
                        trigger="hover"
                        content="">
                    <ul
                            style="width: 100%; list-style: none"
                            size="small"
                    >
                        <li v-for="(rel,reIndex) in row.trfNo"
                            :key="'rel_'+reIndex"
                            style="padding: 5px"
                        >
                            <span @click="toTrfDetail(rel)"
                                  style="color: #ff6600; font-size: 14px; cursor: pointer">
                                {{rel.relNo }}</span>
                        </li>
                    </ul>
                    <template #reference>
                          <span @click="toTrfDetail(row.trfNo[0])"
                                style="color: #ff6600; font-size: 14px; cursor: pointer">
                                    {{row.trfNo[0].relNo}}</span>
                    </template>
                </el-popover>
              </template>
              <template v-else>
                <template v-if="item.configType === 'dff'">
                  {{ getMaterialExtendFieldValue(row, item.fieldCode) }}
                </template>
                <template v-else>
                  {{ row[item.fieldCode] }}
                </template>
              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" prop="id" label="Action" width="150px">
            <template #default="{ row }">
              <el-tooltip content="Edit" placement="top">
                <el-icon
                  :size="16"
                  color="#ff6600"
                  class="table-action-icon"
                  v-if="btnRole(row, 'Edit')"
                >
                  <EditPen @click="toDetail('detail', row)" />
                </el-icon>
              </el-tooltip>
              <el-tooltip content="Copy" placement="top">
                <el-icon :size="16" color="#ff6600" class="table-action-icon">
                  <CopyDocument @click="toDetail('copy', row)" />
                </el-icon>
              </el-tooltip>
              <el-tooltip content="Cancel" placement="top">
                <el-popconfirm
                  confirm-button-text="Confirm"
                  cancel-button-text="Cancel"
                  :icon="InfoFilled"
                  icon-color="red"
                  width="200px"
                  title="Cancel the data?"
                  @confirm="cancelSample(row)"
                >
                  <template #reference>
                    <el-icon
                      :size="16"
                      class="table-action-icon"
                      v-if="btnRole(row, 'Cancel')"
                    >
                      <Delete />
                    </el-icon>
                  </template>
                </el-popconfirm>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="sizeChange"
        @current-change="currentChange"
        v-model:current-page="page.page"
        :page-sizes="page.sizes"
        :page-size="page.rows"
        :layout="page.layout"
        :total="page.total"
        size="small"
        background
      ></el-pagination>
    </div>
    <el-dialog
      v-model="showDownloadTemplateModal"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      title="Product Template"
    >
      <download-template
        v-if="showDownloadTemplateModal"
        :supplier-list="filterList.supplierList"
        :template-show-download="templateShowDownload"
        :customer-list="filterList.customerList"
        :product-line-list="filterList.productLineList"
        form-purpose="Product"
        @cancelDia="closeDialog()"
      >
        <template #downloadTemplateSlot v-if="templateShowDownload">
          <el-button type="primary" size="small" @click="showImportSamples">
            Import Samples
          </el-button>
        </template>
      </download-template>
    </el-dialog>
    <batch-upload
      v-if="isLoadUpload"
      title="Upload"
      append-to-body
      :systemID="1"
      :limit="5"
      :handle-upload-success="uploadSuccess"
      :handle-upload-error="uploadError"
      ref="batchUploadRef"
      accept=".xlsx,.xls"
      upload-url="/api/sgs-pbm/sample/web/v1/upload?dataType=product"
      :attachment-type-options="[]"
      attachment-type-default-value=""
      :file-max-sizes="20"
    ></batch-upload>
  </div>
</template>
<script setup>
import {
  ref,
  onMounted,
  onUnmounted,
  computed,
  watch,
  provide,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import {
  Search,
  EditPen,
  CopyDocument,
  Delete,
  InfoFilled,
  Refresh,
  Download,
  ArrowDown,
} from '@element-plus/icons-vue'
import PageTitle from '@/components/PageTitle/PageTitle.vue'
import Tag from '@/components/Tag/Tag.vue'
import TableFilter from '@/components/TableFilter/TableFilter.vue'
import ButtonGroup from '@/components/ButtonGroup/ButtomGroup.vue'
import FilterView from '@/components/FilterView/FilterView.vue'
import DownloadTemplate from './DownloadTemplate.vue'
import productApi from '@/api/product'
import {
  querySamplesListPage,
  queryProductViewCustomerList,
  exportSampleData,
  checkToTrf,
  actionSamples,
  filedList
} from '@/api/sample'
import { queryScmCustomer } from '@/api/customer'
import { getStatusColor } from '@/utils/status'
import BatchUpload from '@/components/BatchUpload/index.vue'
import { debounce } from 'lodash'
import { ElNotification } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { generateRouteUrl } from '@/utils/util'

const { t } = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)

watch(language, (newVal) => {
  initTableHeader();
  reloadCategorySelect();
})

const dataList = ref([])
const searchForm = ref({
  sampleNo: '',
  buCode: '',
  sampleCategory: '',
  sampleCode: '',
  sampleName: '',
  buyer: '',
  buyerCustomerGroupCode: '',
  supplier: '',
  supplierName: '',
  approvalStatus: [],
  sampleStatus: [],
  dataType: 'product',
  productId: '',
  productCode: '',
  productLine: '',
  productName: '',
    trfNo:'',
  current: 1,
  size: 20,
})
const filterList = ref({
  buyerList: [],
  supplierList: [],
  customerList: [],
  productLineList: [],
  materialType: [
    { label: 'Textiles', value: 'textiles' },
    { label: 'Knit Wear', value: 'knitWear' },
    { label: 'Woven', value: 'woven' },
  ],
  materialStatus: [
    { label: 'In-Progress', value: '1' },
    { label: 'Submitted', value: '2' },
    { label: 'Approved', value: '3' },
    { label: 'Cancelled', value: '4' },
    { label: 'Not In Use', value: '5' },
  ],
})

const buttonGroup = computed(() => [
  {
    clickType: 'reset',
    icon: 'icon-all iconxiangpica1 reset-icon',
    buttonTip: t('operation.resetTip')
  },
  {
    text: t('operation.export'),
    clickType: 'export',
    icon: Download,
  },
])
const toTrfDetail = (row)=>{
    let {relId,relNo,signature} = row;
    window.open('/#/trf/trfDetail?id=' + relId + '&title=' + relNo + '&trfNo=' + relNo + '&hash=' + new Date().getTime() + '' + '&actionType=detail' + '&signature=' + signature, '_blank');

}
const toDetail = (action, obj) => {
  let query = { action }
  if (obj) {
    let { id } = obj
    query['id'] = id
  }
  const winUrl = generateRouteUrl(`/web/customer/newProduct/detail`, query)
  window.open(winUrl, '_blank')
}

const cancelSample = (row) => {
  let { id } = row
  actionSamples({ ids: [id], action: 'Cancel' })
    .then((res) => {
      ElNotification.success('Success')
      initTable()
    })
    .catch((err) => {})
}

const btnRole = ({ permissions }, code) => {
  return (permissions || []).map((p) => p.action).includes(code)
}

const tableMaxHeight = () => {}
const disabledRow = (row) => {
  let { permissions } = row
  return (permissions || []).map((p) => p.action).includes('toTrf')
}

const setStatusColor = (status) => {
  return getStatusColor(status)
}
// 列表接口查询代码块
const page = ref({
  page: 1,
  rows: 20,
  sizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  total: 0,
})

watch(
  searchForm,
  (newvalue) => {
    page.value.page = 1
    debounceSearch()
  },
  {
    immediate: false,
    deep: true,
  },
)
const debounceSearch = debounce(() => {
  initTable()
}, 500)

const initTable = () => {
  searchForm.value.userProductLineCode = userInfo.value.productLineCode
  querySamplesListPage(searchForm.value).then((res) => {
    dataList.value = res.data.records || []
    page.value.total = res.data.total || 0
    page.value.rows = res.data.size || 10
    page.value.page = res.data.current || 1
    calculateTableHeight();
    reloadCategorySelect();
  })
}
const sizeChange = (newSize) => {
  searchForm.value.size = newSize
  searchForm.value.dataType = 'product'
}

const currentChange = (newPage) => {
  searchForm.value.current = newPage
  searchForm.value.size = page.value.rows
}

//初始化sampleType
const allBuSampleList = ref([]);
const categoryBuList = ref({});
const initBuSampleList = (cb) => {
    productApi.queryBuConfig({paramCode: 'ProductCategory'}).then(res => {
        if (res.status == 200 && res.data) {
            categoryBuList.value = res.data || {};
        }
        if (cb) {
            cb();
        }
    })
}
const reloadCategorySelect= ()=> {
    if (!categoryBuList.value || Object.keys(categoryBuList.value) == 0) {
        return
    }
    let lan = language.value;
    allBuSampleList.value = [];
    let allBuKeys = Object.keys(categoryBuList.value);
    allBuKeys.forEach(bu => {
        let allLanTypeList = {}
        try {
            allLanTypeList = JSON.parse(categoryBuList.value[bu]);
        } catch (e) {
        }
        let typeList = allLanTypeList[lan] || [];
        typeList.forEach(tl => {
            let {label, code} = tl;
            allBuSampleList.value.push({name: label, code});
        })
    })
    //重置sampleType的类型
    tableHeaderOptions.value.forEach(item => {
        let {fieldCode} = item;
        if (fieldCode == 'sampleCategory') {
            item.fieldType = 'select';
            item.sourceValueList = allBuSampleList.value;
        }
    })
    dataList.value.forEach(da => {
        let {productLineCode, sampleCategory} = da;
        let lanTypeLis = {};
        try {
            lanTypeLis = JSON.parse(categoryBuList.value[productLineCode]);
        } catch (e) {
        }
        let categoryList = lanTypeLis[lan];
        let sourceValue = (categoryList || []).find(f => f.code == sampleCategory) || {label: ''};
        da['sampleCategoryName'] = sourceValue.label || sampleCategory;
    })
}

const initProductLine = () => {
  productApi.getProductLine().then((res) => {
    if (res.data) {
      const data = res.data || []
      let currentUserPL = (userInfo.value.productLineCodes || '').split(',')
      if (
        !userInfo.value.productLineCodes &&
        userInfo.value.productLineCode == 'all'
      ) {
        filterList.value.productLineList = data
        return
      }
      filterList.value.productLineList = data.filter((da) =>
        currentUserPL.includes(da.productLineCode),
      )
    }
  })
}
const initClientRelationCustomerList = async (relationshipType) => {
  const params = {}
  const list = []
  const item = {}
  item.relationshipType = relationshipType
  params.page = 1
  params.rows = 1000
  // 使用 userInfo.value 访问计算属性的值
  item.buCode =
    userInfo.value.productLineCode === 'all'
      ? userInfo.value.defaultProductLineCode
      : userInfo.value.productLineCode
  item.customerNo = userInfo.value.bossNo
  list.push(item)
  params.list = list

  const res = await queryScmCustomer(params)
  if (res.data) {
    return res.data.rows
  } else {
    return false
  }
}
const resetCustomerList = (customerList) => {
  let newList = []
  let cgs = []
  customerList
    .filter((c) => c.customerGroupCode)
    .forEach((c) => {
      let { customerGroupCode } = c
      if (!cgs.includes(customerGroupCode)) {
        newList.push(c)
        cgs.push(customerGroupCode)
      }
    })
  filterList.value.customerList = newList
}

const initTemplateCustomerListForSupplier = async () => {
  // 再查询所有的模板 + 自身关联的 buyer - 自身的模板
  let buyerList = await initClientRelationCustomerList('buyer')
  let buyerGroupCode = (buyerList || [])
    .filter((b) => b.scmCustomerGroupCode)
    .map((b) => b.scmCustomerGroupCode)

  let currentUser = userInfo.value.userName
  let param = {
    currentUser, // 必传
    formPurpose: 'Product', // 必传
  }

  // 查询自身的模板
  const selfTemplateRes = await queryProductViewCustomerList(param)
  let supplierCustomerList = []
  if (
    selfTemplateRes.status === 200 &&
    selfTemplateRes.data &&
    selfTemplateRes.data.status === 200
  ) {
    supplierCustomerList = selfTemplateRes.data.data || []
  }

  const allTemplateRes = await queryProductViewCustomerList({
    formPurpose: 'Product',
  })
  if (
    allTemplateRes.status === 200 &&
    allTemplateRes.data &&
    allTemplateRes.data.status === 200
  ) {
    let customerList = allTemplateRes.data.data || []
    if (roleInfo.value.isSupplier) {
      customerList = customerList.filter((c) =>
        buyerGroupCode.includes(c.customerGroupCode),
      )
    }
    customerList = [...new Set([...customerList, ...supplierCustomerList])]
    resetCustomerList(customerList)
  }
}

const initTemplateCustomerList = async () => {
  let currentUser = roleInfo.value.isSGS ? '' : userInfo.value.userName
  let param = {
    currentUser, // 必传
    formPurpose: 'Product', // 必传
  }

  const res = await queryProductViewCustomerList(param)
  let customerList = res.data || []
  resetCustomerList(customerList)
}

const handleReset = (data) => {
  searchForm.value = {
    approvalStatus: '',
    buyer: '',
    current: 1,
    dataType: 'product',
    productId: '',
    productLine: '',
    sampleCategory: '',
    sampleCode: '',
    sampleName: '',
    sampleStatus: [],
    size: 20,
    supplier: '',
  }
  initTable()
}

const handleExport = (data) => {
  let queryForm = Object.assign({}, searchForm.value)
  // this.exportLoading = true;
  queryForm['dataType'] = 'product'
  queryForm['userProductLineCode'] = userInfo.value.productLineCode
  // 实际导出逻辑
  exportSampleData(queryForm).then((res) => {
    const { data, headers } = res
    const blob = new Blob([data], { type: headers['content-type'] })
    if ('application/json' == data.type) {
      let fileReader = new FileReader()
      fileReader.readAsText(blob, 'utf-8')
      fileReader.onload = () => {
        let msg = JSON.parse(fileReader.result)
        ElNotification.error(msg || 'Export fail')
      }
      return
    }
    let patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
    let disposition = patt.exec(headers['content-disposition'])
    let fileName = disposition && disposition[1] ? disposition[1] : 'template'
    let dom = document.createElement('a')
    let url = window.URL.createObjectURL(blob)
    dom.href = url
    dom.download = decodeURIComponent(fileName)
    dom.style.display = 'none'
    document.body.appendChild(dom)
    dom.click()
    dom.parentNode.removeChild(dom)
    window.URL.revokeObjectURL(url)
  })
}
const changeView = (filterForm) => {
  const defaultSearchForm = {
    approvalStatus: '',
    buyer: '',
    current: 1,
    dataType: 'product',
    productId: '',
    productLine: '',
    sampleCategory: '',
    sampleCode: '',
    sampleName: '',
    sampleStatus: [],
    size: 20,
    supplier: '',
  }
  // 合并对象并更新 ref 的值
  searchForm.value = Object.assign({}, defaultSearchForm, filterForm)
}
const resetForm = () => {
  searchForm.value = {
    productId: '',
    sampleCode: '',
    productLine: '',
    sampleCategory: '',
    sampleName: '',
    buyer: '',
    supplier: '',
    approvalStatus: '',
    sampleStatus: [],
    // 保留原有的分页和数据类型信息
    dataType: 'product',
    page: 1,
    pageSize: 20,
  }
}
// 控制下载模板逻辑
const showDownloadTemplateModal = ref(false)
const templateShowDownload = ref(false)
const showDownloadTemplate = () => {
  templateShowDownload.value = false
  showDownloadTemplateModal.value = true
}

const hideDownloadTemplateModal = () => {
  showDownloadTemplateModal.value = false
}

const closeDialog = () => {
  showDownloadTemplateModal.value = false
}
// 上传逻辑
const isLoadUpload = ref(false)
const batchUploadRef = ref(null)
const showImportSamples = () => {
  isLoadUpload.value = false
  nextTick(() => {
    isLoadUpload.value = true
    nextTick(() => {
      batchUploadRef.value.open()
    })
  })
}
const uploadError = () => {}
const uploadSuccess = (fileData) => {
  isLoadUpload.value = false
  showDownloadTemplateModal.value = false
  initTable()
}
const showImportTemplate = () => {
  templateShowDownload.value = true
  showDownloadTemplateModal.value = true
}

const samplesTable = ref()
const createTrf = (action) => {
  const selection = samplesTable.value.getSelectionRows()
  if (!selection || selection.length == 0) {
    ElNotification.error('Please select at least one piece of data')
    return
  }
  if (action == 'individualSample' && selection.length > 1) {
    ElNotification.error('Only support select one piece of data')
    return
  }

  if (selection.length > 5) {
    ElNotification.error('Supports up to 5 pieces of data')
    return
  }
  let objectIds = [...new Set(selection.map((row) => row.id))]
  let param = {
    action, objectIds,
  }
  checkToTrf(param)
    .then((res) => {
        if(res.status!=200){
            return
        }
        let testId = res.data;
      let customerGroupCode = selection[0].buyerCustomerGroupCode
      let bossNo = selection[0].buyerCustomerBossNo
      let customer = {
        bossNo,
        customerGroupCode,
      }
      customer = encodeURIComponent(JSON.stringify(customer))
      let bu = selection[0].productLineCode
      const query = {
        actionType: action,
          testId,
        flag: 1,
        customer,
        bu,
      }
      const winUrl = generateRouteUrl(`/#/trf/trfForm`, query)
      window.open(winUrl, '_blank')
    })
    .catch((err) => {
      console.log('check trf err', err)
    })
}

// 定义计算表格高度的函数
let tableHeight = ref(0)
let tableMinHeight = ref(500)
const calculateTableHeight = () => {
  const windowHeight = window.innerHeight

  const headerHeight = document.querySelector('.list-header').offsetHeight
  const searchBlockHeight =
    document.querySelector('.operate-block').offsetHeight
  const paginationHeight = document.querySelector('.el-pagination').offsetHeight
  const containerHeight =
    document.querySelector('.table-container').offsetHeight
  tableHeight.value =
    containerHeight - headerHeight - searchBlockHeight - paginationHeight - 10
  const vhHeight = window.innerHeight * 1 - 438;
  if(vhHeight< 500){
    tableMinHeight = 500
  }else{
    tableMinHeight = vhHeight
  }

}
const tableHeaderOptions = ref([])// 表格表头配置
const initTableHeader = () => {
  console.log('userInfo.value.productLineCode', userInfo.value.productLineCode)
  filedList({ dataType: "product",userProductLineCode: userInfo.value.productLineCode}).then((res) => {
    console.log('res', res)
    tableHeaderOptions.value = res.data || []
  })
}

const getMaterialExtendFieldValue = (row, fieldCode) => {
  if (row.fieldList && row.fieldList.length > 0) {
    const foundField = row.fieldList.find(f => f.fieldCode === fieldCode);
    return foundField ? foundField.materialExtendFieldValue : '';
  }
  return '';
}
provide('loadSearchForm', () => {
  return searchForm.value
})

let unwatch
onMounted(() => {
    initBuSampleList(()=>{
        initTableHeader();
        initTable();
    })
  calculateTableHeight() // 初始化时计算一次高度
  window.addEventListener('resize', calculateTableHeight)

  unwatch = watch(
    [userInfo, roleInfo],
    ([newUserInfo, newRoleInfo]) => {
      if (
        Object.keys(newUserInfo).length > 0 &&
        Object.keys(newRoleInfo).length > 0
      ) {
        // 取消监听
        if (typeof unwatch === 'function') {
          unwatch()
        }
        if (roleInfo.value.isSupplier) {
          initTemplateCustomerListForSupplier()
        } else {
          initTemplateCustomerList()
        }
        initProductLine();
        initTableHeader();
        reloadCategorySelect();
      }
    },
    {
      immediate: true, // 立即执行一次监听
    },
  )
})
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
  if (unwatch) {
    unwatch()
  }
})
</script>
<style scoped lang="scss">
@use '@/assets/style/unit.module.scss' as *;

.table-action-icon {
  margin: 0 10px;
}
.product-content-block {
  background-color: #ffffff;
  padding: $module-padding-vertical;
}
.operate-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: $module-padding-vertical;
  &-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
.create-button {
  margin-right: $inline-element-spacing;
}
.table-scroll {
  height: calc(100vh - 415px);
}
</style>
