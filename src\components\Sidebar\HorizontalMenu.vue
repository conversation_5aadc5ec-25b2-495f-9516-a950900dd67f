<template>
  <el-menu
    mode="horizontal"
    :default-active="activeId"
    background-color="#3c515b"
    text-color="#fff"
    active-text-color="#F60"
    menu-trigger="hover"
    unique-opened
    class="custom-horizontal-menu"
    v-if="menu.length > 0"
  >
    <MenuNode v-for="item in menu" :key="item.id" :menu-item="item" />
  </el-menu>
</template>

<script setup>
import { useStore } from 'vuex'
import { ref, nextTick, onMounted, computed } from 'vue'
import MenuNode from './MenuNode.vue' // 引入递归组件

const store = useStore()
const activeId = ref('')
const menu = computed(() => store.state.user.menu)

// 根据当前路径找到对应的 id
const findIdByPath = (path) => {
  const flatMenu = (menu) => {
    let result = []
    menu.forEach((item) => {
      result.push(item)
      if (item.children) {
        result = result.concat(flatMenu(item.children))
      }
    })
    return result
  }
  const allMenuItems = flatMenu(menu.value)
  const foundItem = allMenuItems.find((item) => item.path === path)
  return foundItem ? foundItem.id : ''
}

onMounted(() => {
  const currentPath = window.location.pathname + window.location.search
  activeId.value = findIdByPath(currentPath)

  // 监听路由变化，更新激活的菜单项
  window.addEventListener('popstate', () => {
    const newPath = window.location.pathname + window.location.search
    activeId.value = findIdByPath(newPath)
  })
})
</script>

<style></style>
