import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { staticRouter } from './static'

export default () => {
  let routes: Array<RouteRecordRaw> = staticRouter
  const router = createRouter({
    history: createWebHistory(),
    routes,
    scrollBehavior() {
      return { top: 0 }
    },
  })

  // 全局路由前置守卫
  router.beforeEach((_, __, next) => {
    next()
  })

  // 安装自定义路由插件

  return router
}
