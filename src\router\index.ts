import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { staticRouter } from './static'

export default () => {
  let routes: Array<RouteRecordRaw> = staticRouter
  const router = createRouter({
    history: createWebHistory('/web/'),
    routes,
    scrollBehavior() {
      return { top: 0 }
    },
  })

  // 全局路由前置守卫
  router.beforeEach((to, _, next) => {
    if (to.path === '/layout') {
      next('/404'); 
      return;
    }
    next()
  })

  return router
}
