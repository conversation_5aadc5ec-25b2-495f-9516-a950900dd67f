<template>
    <div class="page-content">

        <!-- 导航栏 -->
        <div :style="{ width: isNavVisible ? '18%' : '0%' }" style="margin-top: -5px;" class="nav">
            <NavigationBar ref="navigator" @update:regulations="updateRegulations" class="knowledge-nav" />
        </div>

        <!-- 搜索栏 -->
        <div class="main-content" :style="{ width: isNavVisible ? '70%' : '70%' }">

            <div class="search-container">
                <div class="bg-layer">
                    <div class="menu-toggle" @click="toggleNav">
                        <img :src="toggleUrl">
                    </div>

                    <div style="position: relative;">
                        <form @submit.prevent="pushToContent(query)" class="search-bar">
                            <div class="search-input-container">
                                <input type="text" :placeholder="$t('work.knowledgeBaseHome.searchPlaceholder')"
                                    v-model="query" @input="debouncedFetchSuggestions" @focus="handleInputFocus()"
                                    @blur="handleInputBlur()" class="search-input">
                            </div>
                            <button type="submit" class="search-button">
                                <img src="/knowledgeBaseHome/ze-search.svg">
                                {{ $t('work.knowledgeBaseHome.search') }}
                            </button>

                            <ul v-if="suggestions.length > 0" class="suggestions-display">
                                <li v-for="(item, index) in suggestions" :key="index"
                                    @click="pushToContent(item.toString())" class="suggestions-display-item">
                                    <div class="suggestions-display-item-title">
                                        {{ item }}
                                    </div>
                                    <el-icon class="delete-icon" @click.stop="deleteSearchHistory(index)">
                                        <Close />
                                    </el-icon>
                                </li>
                            </ul>
                        </form>

                        <!-- 热门标签 -->
                        <div class="lable-display">
                            <span class="lable-title">{{ $t('work.knowledgeBaseHome.hotTags') }}</span>
                            <div class="lable-list">

                                <span v-for="(tag, index) in tags.slice(0, 5)" :key="index"
                                    @click="pushToContentPage(index)">
                                    <ContentTooltip :content="tag?.tagNameCn" :id="`item-hot-tag-${index}`"
                                        :placement="'bottom'">
                                        <template #content>
                                            <div class="lable-item" :id="`item-hot-tag-${index}`"
                                                :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">
                                                {{ tag?.tagNameCn
                                                }}</div>
                                        </template>
                                    </ContentTooltip>
                                </span>

                            </div>
                            <div v-if="tags?.length === 0">
                                <span class="lable-item">{{ $t('work.knowledgeBaseHome.tagLoadFailed') }}</span>
                            </div>
                        </div>
                    </div>

                </div>

            </div>

            <div class="hot-content">
                <!-- 知识推荐 -->
                <div class="hot-card">
                    <div class="card-header">
                        <div class="header-left">
                            <img src="/knowledgeBaseHome/fire.svg">
                            <span>{{ $t('work.knowledgeBaseHome.knowledgeRecommend') }}</span>
                        </div>
                        <div class="header-right" @click="knowledgeRecommendSwitch">
                            <img src="/knowledgeBaseHome/riLine-refresh-line 1.svg">
                            <span>{{ $t('work.knowledgeBaseHome.refresh') }}</span>
                        </div>
                    </div>
                    <ul class="knowledge-list" v-if="isKnowledgeRecommendRefreshing">
                        <li v-for="(item, index) in paginatedKnowledgeRecommend" :key="index" class="knowledge-item"
                            @click="pushToDetail(item)">

                            <div class="item-left" style="width: 80%;">
                                <span class="item-index"
                                    :style="{ display: 'flex', alignItems: 'center', justifyContent: 'center', color: getColor(index), fontWeight: 700, marginRight: '4px', fontSize: '14px', width: '18px', maxWidth: '18px', minWidth: '18px' }">{{
                                        index + 1 }}{{ " "
                                    }}</span>
                                <ContentTooltip :content="item?.title" :id="`item-hotcard-${index}`">
                                    <template #content>
                                        <span class="item-title" :id="`item-hotcard-${index}`"
                                            :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                                item?.title }}</span>
                                    </template>
                                </ContentTooltip>
                            </div>

                            <div class="item-right" style="width: 20%;">
                                <img src="/knowledgeBaseHome/md-visibility ehEx5mG.svg">
                                <!-- 浏览次数，如果大于999，显示999+ -->
                                <span class="item-date"
                                    style="width: 40px; margin-left: 8px; margin-right: -20px; font-size: 12px; opacity: 0.4; font-weight: 400;">{{
                                        item?.viewCount > 9999 ? '9999+' : (item?.viewCount || 0) }}</span>
                            </div>
                        </li>
                        <!-- <li v-if="knowledgeRecommend?.length === 0">
                            <span>{{ $t('work.knowledgeBaseHome.knowledgeLoadFailed') }}</span>
                        </li> -->
                    </ul>
                </div>

                <!-- 最新知识 -->
                <div class="hot-card">
                    <div class="card-header">
                        <div class="header-left">
                            <img src="/knowledgeBaseHome/md-autorenew 3UfjXS4.svg">
                            <span>{{ $t('work.knowledgeBaseHome.latestKnowledge') }}</span>
                        </div>
                        <div class="header-right" @click="knowledgeLatestSwitch">
                            <img src="/knowledgeBaseHome/riLine-refresh-line 1.svg">
                            <span>{{ $t('work.knowledgeBaseHome.refresh') }}</span>
                        </div>
                    </div>
                    <ul class="knowledge-list" v-if="isKnowledgeLatestRefreshing">
                        <li v-for="(item, index) in paginatedKnowledgeLatest" :key="index" class="knowledge-item"
                            @click="pushToDetail(item)" style="width: 95%; ">

                            <div class="item-left" style="width: 70%;">
                                <span class="item-index"
                                    :style="{ display: 'flex', alignItems: 'center', justifyContent: 'center', color: getColor(index), fontWeight: 700, marginRight: '4px', fontSize: '14px', width: '18px', maxWidth: '18px', minWidth: '18px' }">{{
                                        index + 1 }}{{ " "
                                    }}</span>
                                <!-- <el-tooltip class="condition-tooltip" :content="item.title" placement="top"
                                    :disabled="!isTitleTruncated(`title-${index}`)">
                                    <span class="item-title"
                                        :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                            item.title }}</span>
                                </el-tooltip> -->
                                <ContentTooltip :content="item.title" :id="`item-latestcard-${index}`">
                                    <template #content>
                                        <span class="item-title" :id="`item-latestcard-${index}`"
                                            :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                                item.title }}</span>
                                    </template>
                                </ContentTooltip>
                            </div>

                            <div class="item-right"
                                style="width: 30%; display: flex; align-items: center; justify-content: flex-end; opacity: 0.7; font-size: 12px; font-weight: 400;">
                                <!-- time display format yyyy/mm/dd -->
                                <span class="item-date">{{ item.updateTime.split(' ')[0].split('-').join('/') }}</span>
                            </div>
                        </li>
                        <!-- <li v-if="knowledgeLatest?.length === 0">
                            <span>{{ $t('work.knowledgeBaseHome.knowledgeLoadFailed') }}</span>
                        </li> -->
                    </ul>
                </div>

                <!-- 最热资料 -->
                <div class="hot-card">
                    <div class="card-header">
                        <div class="header-left">
                            <img src="/knowledgeBaseHome/md-get_app wzQAwVg.svg">
                            <span>{{ $t('work.knowledgeBaseHome.hotMaterials') }}</span>
                        </div>
                        <div class="header-right" @click="hostMaterialSwitch">
                            <img src="/knowledgeBaseHome/riLine-refresh-line 1.svg">
                            <span>{{ $t('work.knowledgeBaseHome.refresh') }}</span>
                        </div>
                    </div>
                    <ul class="knowledge-list" v-if="isHostMaterialRefreshing">
                        <li v-for="(item, index) in paginatedHostMaterial" :key="index" class="knowledge-item">

                            <div class="item-left" @click="downloadFile(item)" style="width: 80%;">
                                <span class="item-index"
                                    :style="{ display: 'flex', alignItems: 'center', justifyContent: 'center', color: getColor(index), fontWeight: 700, marginRight: '4px', fontSize: '14px', width: '18px', maxWidth: '18px', minWidth: '18px' }">{{
                                        index +
                                        1 }}{{ " "
                                    }}</span>



                                <img style="height: 20px; width: 20px; margin: 0px 9px 0px 6px; position: relative;"
                                    :src="getFileIcon(item?.fileName)">

                                <!-- <el-tooltip class="condition-tooltip" :content="item.fileName" placement="top">
                                        <span style="margin-bottom: 5px;"
                                            :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                                item?.fileName }}</span>
                                    </el-tooltip> -->
                                <ContentTooltip :content="item?.fileName" :id="`item-hostmaterial-${index}`">
                                    <template #content>
                                        <span class="item-title" :id="`item-hostmaterial-${index}`"
                                            style="font-size: 14px; color: rgb(57, 111, 198);"
                                            :ref="el => { if (el) titleRefs[`title-${index}`] = el as HTMLElement }">{{
                                                item?.fileName }}</span>
                                    </template>
                                </ContentTooltip>

                            </div>

                            <div class="item-right" style="width: 17%; margin-right: 3%;">
                                <img src="/knowledgeBaseHome/download.svg">
                                <!-- 下载次数，如果大于999，显示999+ -->
                                <span class="item-date"
                                    style="width: 23px; margin-left: 8px; margin-right: -20px; font-size: 14px; opacity: 0.4;">{{
                                        item?.downloadCount > 999 ? '999+' :
                                            (item?.downloadCount || 0) }}</span>
                            </div>
                        </li>
                        <!-- <li v-if="hostMaterial?.length === 0">
                            <span style="margin-left: 20px;">{{ $t('work.knowledgeBaseHome.materialLoadFailed') }}</span>
                        </li> -->
                    </ul>
                </div>


            </div>
        </div>

    </div>
</template>

<script lang="ts" setup>
import { Close } from "@element-plus/icons-vue";
import { onMounted, ref, computed, nextTick, watch } from "vue";
import NavigationBar from '../../components/navigationBar/navigationBar.vue'
import { knowledgeMainpageGet } from "@/api/knowledgeBaseHome/knowledgeBaseHome";
import { getCloudFileURL, attachmentviewcount } from '@/api/getCloudFileUrl.ts';
import { knowledgeSearchSuggestionGet } from "@/api/knowledgeBaseHome/knowledgeSearchSuggestion";
import { debounce } from "lodash-es";
import { useRouter } from 'vue-router';
const router = useRouter();
import ContentTooltip from '@/components/Tooltip/Tooltip.vue';
// import { pa } from "element-plus/es/locales.mjs";
// import { Download } from "@element-plus/icons-vue";
//$router.push
const isNavVisible = ref(false);
const toggleUrl = ref("/knowledge/knowledgeBaseHome/iconPark-menu-unfold-one 1.svg");

const isKnowledgeRecommendRefreshing = ref(true);
const isKnowledgeLatestRefreshing = ref(true);
const isHostMaterialRefreshing = ref(true);

interface Tag {
    tagNameCn: string;
    [key: string]: any;
}

interface Knowledge {
    title: string;
    viewCount: number;
    updateTime: string;
    [key: string]: any;
}

interface Material {
    fileName: string;
    downloadCount: number;
    cloudId: string;
    [key: string]: any;
}

interface Suggestion {
    title: string;
    [key: string]: any;
}

const query = ref('');
const suggestions = ref<Suggestion[]>([]);

const tags = ref<Tag[]>([]);
const knowledgeRecommend = ref<Knowledge[]>([] as Knowledge[]);;
const knowledgeLatest = ref<Knowledge[]>([]);
const hostMaterial = ref<Material[]>([]);

const knowledgeRecommendSwitcher = ref(0);
const knowledgeLatestSwitcher = ref(0);
const hostMaterialSwitcher = ref(0);
const navigator = ref();
interface Regulation {
    id?: string;
    title: string;
    tag?: string[];
    url?: string;
    coverPlan?: string;
    status?: string;
    createTime?: string;
    updateTime?: string;
    viewCount?: number;
    labels?: string;
    createUser?: string;
    content?: string;
    description?: string;
    knowledgeAttachmentList?: any[]
    knowledgeTagEntityList?: { name: string; value: string; tagNameCn: string; }[];
    categoryId?: string;
}

// Add the updateRegulations function to handle events from NavigationBar
const updateRegulations = (params: any) => {
    router.push({ name: 'contentPage', query: { categoryId: params.categoryId || '0' } });
    // params.categoryId || '0';
    // console.log('Received regulations update:', params);
    // // Implement any logic needed when navigation changes
    // // For example, you might want to filter content based on categoryId
    // if (params.categoryId) {
    //     // Handle category selection
    // }
};


//本地搜索历史
const pushToContent = (title: string) => {

    //获取搜索历史并排除空字符串
    let searchHistory = JSON.parse(localStorage.getItem('sgsSearchHistory') || '[]');
    searchHistory = searchHistory.filter((item: string) => item !== '');
    if (!Array.isArray(searchHistory)) {
        searchHistory = [];
    }

    // Add new title to history (Avoid duplicates and void string)
    if (!searchHistory.includes(title) && title !== '') {
        searchHistory.push(title)
    }


    // Save updated history back to localStorage
    localStorage.setItem('sgsSearchHistory', JSON.stringify(searchHistory));

    router.push({ name: 'contentPage', query: { query: title } });
}

// 搜索框获取焦点时，获取搜索历史并显示
const handleInputFocus = () => {
    // Get search history and filter out empty strings
    let searchHistory = JSON.parse(localStorage.getItem('sgsSearchHistory') || '[]');
    searchHistory = searchHistory.filter((item: string) => item && item.trim() !== '');

    // Only show suggestions if there's no current search query
    if (query.value === '' && searchHistory.length > 0) {
        suggestions.value = searchHistory.slice(-10);
    }
}

// 删除搜索历史记录
const deleteSearchHistory = (index: number) => {
    let searchHistory = JSON.parse(localStorage.getItem('sgsSearchHistory') || '[]');
    searchHistory.splice(index, 1);
    localStorage.setItem('sgsSearchHistory', JSON.stringify(searchHistory));
    suggestions.value = searchHistory;
    if (searchHistory.length === 0) {
        suggestions.value = [];
    }
};


const pushToContentPage = (index: number) => {
    router.push({ name: 'contentPage', query: { tag: tags.value[index].id } });
}

const handleInputBlur = () => {
    // Hide suggestions when input loses focus
    setTimeout(() => {
        suggestions.value = [];
    }, 200); // Delay to allow clicks on suggestions before hiding
};

const fetchSuggestions = () => {
    if (!query.value.trim()) {
        suggestions.value = [];
        return;
    }
    try {
        // Simulated API request (replace with actual API call)
        knowledgeSearchSuggestionGet({ keyword: query.value }).then(
            (res) => {
                suggestions.value = res.data;
            }
        )

    } catch (error) {
        console.error("Error fetching suggestions:", error);
    }
}

const debouncedFetchSuggestions = debounce(fetchSuggestions, 500);

const hostMaterialSwitch = () => {
    isHostMaterialRefreshing.value = false;
    hostMaterialSwitcher.value = hostMaterialSwitcher.value === 0 ? 1 : 0;
    setTimeout(() => {
        isHostMaterialRefreshing.value = true;
    }, 200);
}

const knowledgeLatestSwitch = () => {
    isKnowledgeLatestRefreshing.value = false;
    knowledgeLatestSwitcher.value = knowledgeLatestSwitcher.value === 0 ? 1 : 0;
    setTimeout(() => {
        isKnowledgeLatestRefreshing.value = true;
    }, 200);
}

const knowledgeRecommendSwitch = () => {
    isKnowledgeRecommendRefreshing.value = false;
    knowledgeRecommendSwitcher.value = knowledgeRecommendSwitcher.value === 0 ? 1 : 0;
    setTimeout(() => {
        isKnowledgeRecommendRefreshing.value = true;
    }, 200);
}

const downloadFile = (item: Material) => {
    getCloudFileURL({ cloudID: item.cloudId, systemID: 1, networkType: 2 }).then((res: any) => {
        console.log("item=>", item)
        const ids = [{ id: item.id.toString() }]
        attachmentviewcount(ids).then((res: any) => {
            if (res?.code === 200) {
                console.log("file download count=>", res);

                // 更新下载次数并刷新页面
                const index = hostMaterial.value.findIndex(m => m.cloudId === item.cloudId);
                console.log("index=>", index)
                if (index !== -1) {
                    hostMaterial.value[index].downloadCount = (hostMaterial.value[index].downloadCount || 0) + 1;
                }

            } else {
                console.error("file download count error=>", res);
            }
        })
        window.open(res)

    })
}
onMounted(() => {
    knowledgeMainpageGet().then((res) => {
        // Destructure the response to get the required data
        const { hotTagIds = [], hotViewKnowledges = [], latestKnowledges = [], hotDownloadFiles = [] } = res.data;

        // Assign the values to the reactive variables
        tags.value = hotTagIds;
        knowledgeRecommend.value = hotViewKnowledges;
        knowledgeLatest.value = latestKnowledges;
        hostMaterial.value = hotDownloadFiles;

        // After data is loaded, check for text truncation on the next tick
        nextTick(() => {
            // This will ensure all DOM is updated before we check
            Object.keys(titleRefs.value).forEach(key => {
                isTitleTruncated(key);
            });
        });
    }).catch(() => {
        console.error("Failed to load data");
        // Optionally, you can set default values here if needed
        tags.value = [];
        knowledgeRecommend.value = [];
        knowledgeLatest.value = [];
        hostMaterial.value = [];
    });
});

const toggleNav = () => {
    isNavVisible.value = !isNavVisible.value;
    toggleUrl.value = isNavVisible.value ?
        "/knowledge/knowledgeBaseHome/iconPark-menu-unfold-one 1.svg" :
        "/knowledge/knowledgeBaseHome/iconPark-menu-fold-one 1.svg";
}

const getColor = (index: number) => {
    const colors = ["rgb(255, 0, 0)", "rgb(255, 102, 0)", "rgb(52, 168, 83)"];
    return index < 3 ? colors[index] : 'rgba(44, 62, 80, 0.3)';
}


const getFileIcon = (filename: string) => {
    // icon for use
    // exceldocicon.svg
    // generaldocicon.svg
    // imgdocicon.svg
    // linkdocicon.svg
    // packageddocicon.svg
    // pdfdocicon.svg
    // powerpointdocicon.svg
    // txtdocicon.svg
    // videodocicon.svg
    // worddocicon.svg
    // xmldocicon.svg
    //判断样式
    const fileType = filename?.split(".").pop()?.toLowerCase();
    switch (fileType) {
        case "docx": return "/knowledge/knowledgeBaseHome/worddocicon.svg"
        case "doc": return "/knowledge/knowledgeBaseHome/exceldocicon.svg"
        case "pdf": return "/knowledge/knowledgeBaseHome/pdfdocicon.svg"
        case "pptx": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg"
        case "ppt": return "/knowledge/knowledgeBaseHome/powerpointdocicon.svg"
        case "xls": return "/knowledge/knowledgeBaseHome/exceldocicon.svg"
        case "xlsx": return "/knowledge/knowledgeBaseHome/exceldocicon.svg"
        case "txt": return "/knowledge/knowledgeBaseHome/txtdocicon.svg"
        case "md": return "/knowledge/knowledgeBaseHome/xmldocicon.svg"
        case "png": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "jpg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "jpeg": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "gif": return "/knowledge/knowledgeBaseHome/videodocicon.svg"
        case "bmp": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "ico": return "/knowledge/knowledgeBaseHome/imgdocicon.svg"
        case "zip": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "rar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "7z": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "tar": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "gz": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "bz2": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "iso": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "dmg": return "/knowledge/knowledgeBaseHome/packageddocicon.svg"
        case "url": return "/knowledge/knowledgeBaseHome/xmldocicon.svg"

        default: return "/knowledge/knowledgeBaseHome/generaldocicon.svg"
    }
}

// 根据切换功能生成数组切片
const paginatedKnowledgeRecommend = computed(() => {
    if (!knowledgeRecommend.value || knowledgeRecommend.value.length === 0) return [];
    if (knowledgeRecommend.value.length <= 10) return knowledgeRecommend.value;
    const start = knowledgeRecommendSwitcher.value * 10;
    const end = start + 10;
    return knowledgeRecommend.value.slice(start, end);
});

const paginatedKnowledgeLatest = computed(() => {
    if (!knowledgeLatest.value || knowledgeLatest.value.length === 0) return [];
    if (knowledgeLatest.value.length <= 10) return knowledgeLatest.value;
    const start = knowledgeLatestSwitcher.value * 10;
    const end = start + 10;
    return knowledgeLatest.value.slice(start, end);
});

const paginatedHostMaterial = computed(() => {
    if (!hostMaterial.value || hostMaterial.value.length === 0) return [];
    if (hostMaterial.value.length <= 10) return hostMaterial.value;
    const start = hostMaterialSwitcher.value * 10;
    const end = start + 10;
    return hostMaterial.value.slice(start, end);
});

// Move the function definition up in the file, before it's referenced
const titleRefs = ref<{ [key: string]: HTMLElement }>({});

// Add new function to check text overflow
const isTitleTruncated = (refKey: string): boolean => {
    const element = titleRefs.value[refKey];
    if (!element) return false;

    return element.scrollWidth > element.clientWidth;
};

// Add watches for pagination changes
watch([knowledgeRecommendSwitcher, knowledgeLatestSwitcher, hostMaterialSwitcher], () => {
    nextTick(() => {
        // Check text truncation after pagination changes
        Object.keys(titleRefs.value).forEach(key => {
            isTitleTruncated(key);
        });
    });
});

const pushToDetail = (item: Regulation) => {

    router.push({ name: 'detailedArticle', query: { id: item.id, categoryId: item.categoryId } });
}

</script>

<style lang="scss" scoped>
.page-content {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: start;
    width: 100%;
}

.nav {
    overflow: hidden;
    transition: all 0.3s ease-in-out;

    :deep(.el-card) {
        height: 780px !important;
        box-shadow: none;
        overflow-y: auto;
        border: 0;
        border-radius: 0;
    }
}

.main-content {
    margin: 15px 10px;
}

.search-container {
    width: 100%;
    height: 300px;
    min-width: 960px;
    background-image: url("/knowledgeBaseHome/searchBackground.png");
    background-position: center;
    background-size: 100% auto;
    background-repeat: no-repeat;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;

    .bg-layer {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .menu-toggle {
        position: absolute;
        height: 30px;
        width: 30px;
        top: 10px;
        left: 10px;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.search-bar {
    width: 700px;
    max-width: 600px;
    background-color: white;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    position: relative;

    .search-input-container .search-input {
        width: 440px;
        background: white;
        flex: 1;
        border: none;
        outline: none;
        padding: 2px 0 2px 0;
        font-size: 16px;
        color: #000000;
        height: 30px;
        margin: 0 0 0 20px;
        font-weight: 400;
    }
    
    .suggestions-display {
        position: absolute;
        left: 0;
        top: 50px;
        width: 460px;
        background: white;
        border-top: 2px solid rgba(0, 0, 0, 0.4);
        border-radius: 0 0 8px 8px;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10;
        list-style-type: none;

        display: flex;
        flex-direction: column;
        justify-content: center;

        .suggestions-display-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 6px 10px 6px 14px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);


            .suggestions-display-item-title {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.6);
            }

            .delete-icon {
                cursor: pointer;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.6);
                visibility: hidden;
            }

            &:hover {
                background-color: rgba(0, 0, 0, 0.1);

                .delete-icon {
                    visibility: visible;
                }
            }
        }
    }

    .search-button {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        border: 0;
        width: 140px;
        height: 55px;
        cursor: pointer;
        color: #fff;
        background-color: rgb(255, 102, 0);
        border-radius: 0 5px 5px 0;
        font-weight: 700;

        img {
            width: 18px;
            height: 18px;
            margin-right: 5px;
        }
    }
}

::placeholder {
    color: rgba(28, 28, 29, 0.5);
}

.lable-display {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #fff;
    white-space: nowrap;
    margin-top: 7px;
    margin-right: -10px;

    img {
        width: 24px;
        height: 24px;
        margin: 5px 5px 5px 0;
        font-size: 15px;
    }

    .lable-title {
        font-size: 14px;
        font-weight: 700;
    }

    .lable-list {
        flex-wrap: nowrap;
        height: 35px;
        overflow: hidden;

        display: flex;
        align-items: center;
        font-size: 15px;



        .lable-item {
            margin: 0 0 0 10px;
            font-weight: 400;
            font-size: 14px;
            cursor: pointer;
            border: 0;
            color: #fff;
            padding: 2px 5px;
            background-color: rgba(0, 0, 0, 0.4);
            border-radius: 5px;
            height: 25px;
            max-width: 97px;

            overflow-x: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;


            &:hover {
                color: rgba(255, 102, 0, 0.8);
                background-color: rgba(255, 255, 255, 0.9);
                padding: 2px 5px;
                border-radius: 5px;
            }
        }
    }
}

.hot-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
}

.hot-card {
    height: 450px;
    min-width: 320px;
    width: 32%;
    background-color: #fff;
    padding: 5px;
}

.card-header {
    margin: 9px 0px 0px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid rgb(235, 235, 235);
    font-size: 18px;
    font-weight: 400;
    padding-bottom: 5px;

    img {
        height: 22px;
        width: 22px;
        margin: 0 4px 10px 4px;
    }
}

.header-left,
.header-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left span {
    margin: 0 10px 10px 4px;
    font-size: 16px;
    font-weight: 700;
}

.header-right {
    img {
        height: 11px;
        width: 11px;
        margin: 0 0 10px 4px;
        cursor: pointer;
    }

    span {
        margin: 0 10px 10px 4px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.4);
        cursor: pointer;
    }
}

.knowledge-list {
    margin: 5px 0px;
    padding: 5px 0px;
    padding: 0;
}

.knowledge-item {
    list-style: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 15px;
    cursor: pointer;
    height: 22px;
    width: 90%;


    &:hover .item-title {
        text-decoration: underline;
        color: rgba(255, 102, 0, 0.8);
    }
}

.item-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    span {
        white-space: nowrap;
        margin-left: 3px;
        opacity: 0.7;
    }
}

.item-left {
    display: flex;
    align-items: center;

    .item-index {
        width: 12px;
        margin-right: 4px;
    }

    span:nth-child(2) {
        margin-left: 5px;
        width: 90%;
    }

    .item-title {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.condition-tooltip {
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
</style>