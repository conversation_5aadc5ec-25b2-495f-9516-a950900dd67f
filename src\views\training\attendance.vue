<template>
    <basic-container>
        <el-row>
            <el-col :span="15">

            </el-col>
            <el-col :span="2">
                <el-button size="small" type="primary" @click="allPass">{{ $t('training.AllPass') }}</el-button>
            </el-col>
            <el-col :span="2">
                <el-button size="small" type="primary" @click="onSubmit">{{ $t('training.Save') }}</el-button>
            </el-col>
            <el-col :span="3">
                <el-button size="small" type="primary" @click="exportExcel">{{ $t('training.ExportToExcel') }}
                </el-button>
            </el-col>
            <el-col :span="2">
                <el-button size="small" type="primary" @click="toClassList">{{ $t('training.Cancel') }}</el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="1"></el-col>
            <el-col :span="23">
                <el-table :data="showData" stripe style="width:100%" v-loading="listLoading">
                    <el-table-column prop="traineeCustomerName" :label="$t('training.Company')"></el-table-column>
                    <el-table-column prop="traineeUserName" :label="$t('training.Name')"
                                     width="160px"></el-table-column>
                    <el-table-column prop="isRegistered" :label="$t('training.Register')" align="center" width="120px">
                        <template slot-scope="scope">
                            <el-switch
                                    v-model="scope.row.isRegistered"
                                    active-color="#ff6600"
                                    inactive-color="#ff4949"
                                    active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isEnrolled" :label="$t('training.Enrolled')" align="center" width="120px">
                        <template slot-scope="scope">
                            <el-switch
                                    v-model="scope.row.isEnrolled"
                                    @change="enrolledChange(scope.row)"
                                    active-color="#ff6600"
                                    inactive-color="#ff4949"
                                    active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isPass" :label="$t('training.Result')">
                        <template slot-scope="scope">
                            <el-radio-group v-model="scope.row.isPass" :disabled="scope.row.isEnrolled!=1">
                                <el-radio :label="1">Pass</el-radio>
                                <el-radio :label="0">Fail</el-radio>
                                <el-radio :label="2">Not Attended</el-radio>
                            </el-radio-group>
                        </template>
                    </el-table-column>
                    <el-table-column prop="score" :label="$t('training.Score')" width="120px">
                        <template slot-scope="scope">
                            <el-input type="number" placeholder="score" v-show="show" v-model="scope.row.score"
                                      :disabled="scope.row.isEnrolled!=1"></el-input>
                            <span v-show="!show">{{scope.row.score}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark" :label="$t('training.Remark')">
                        <template slot-scope="scope">
                            <el-input :placeholder="$t('training.Remark')" v-show="show"
                                      v-model="scope.row.remark"></el-input>
                            <span v-show="!show">{{scope.row.remark}}</span>
                        </template>
                    </el-table-column>
                    <!--<el-table-column label="Action">
                      <template scope="scope">
                        <el-button type="primary" size="small" @click="opendialog(scope.row)">Edit</el-button>
                        <el-button  size="small" @click="handleDel(scope.row)">Delete</el-button>
                      </template>
                    </el-table-column>-->
                </el-table>


                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                ></el-pagination>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="5" style="text-align: center;">
                {{ $t('training.TotalNumOfAttendance') }}:
            </el-col>
            <el-col :span="2">
                <el-button size="small" style="width: 60px" type="primary">{{ classResult.totalOfAttended }}</el-button>
            </el-col>
            <el-col :span="18">
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="5" style="text-align: center;">
                {{ $t('training.PassRate') }}:
            </el-col>
            <el-col :span="2">
                <el-button size="small" style="width: 60px" type="warning">{{ classResult.RateOfPass | rateOfPassDataFormat }} %</el-button>
            </el-col>
            <el-col :span="18">
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="5" style="text-align: center;">
                {{ $t('training.TotalNumber') }}:
            </el-col>
            <el-col :span="2">
                <el-button size="small" style="width: 60px" type="success">{{ classResult.total }}</el-button>
            </el-col>
            <el-col :span="18">
            </el-col>
        </el-row>
        <el-table :data="showData" stripe style="width: 1px" type="hidden" v-loading="listLoading" id="exportTab">
            <el-table-column prop="traineeCustomerName" label="Company"></el-table-column>
            <el-table-column prop="traineeUserName" label="Name"></el-table-column>
            <el-table-column prop="isRegistered" label="Register" :formatter="totextOfRegister">

            </el-table-column>
            <el-table-column prop="isEnrolled" label="Enrolled" :formatter="totextOfEnrolled">
            </el-table-column>
            <el-table-column prop="isPass" label="Result" :formatter="totextOfResult">

            </el-table-column>
            <el-table-column prop="score" label="Score">
                <template slot-scope="scope">
                    <el-input placeholder="score" v-show="show" v-model="scope.row.score"></el-input>
                    <span v-show="!show">{{scope.row.score}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="Remark">
                <template slot-scope="scope">
                    <el-input placeholder="remark" v-show="show" v-model="scope.row.remark"></el-input>
                    <span v-show="!show">{{scope.row.remark}}</span>
                </template>
            </el-table-column>
            <!--<el-table-column label="Action">
              <template scope="scope">
                <el-button type="primary" size="small" @click="opendialog(scope.row)">Edit</el-button>
                <el-button  size="small" @click="handleDel(scope.row)">Delete</el-button>
              </template>
            </el-table-column>-->
        </el-table>
    </basic-container>
</template>


<script>
    import {selClassTraineeList, add, getClassResult} from "@/api/training/attendance";
    import {validatenull} from "@/util/validate";
    import FileSaver from 'file-saver'
    import XLSX from 'xlsx'

    export default {
        data() {
            return {
                showData: [],
                form: {},
                classResult: {},
                total: 0,
                pageSize: 10,
                listLoading: false,
                currentPage: 1,
                show: true,
                radio: '',
            }
        },
        props: ['classId'],

        created: function () {
            this.selClassTraineeList(this.currentPage, this.pageSize, this.$route.query.classId);
        },
        filters: {
            rateOfPassDataFormat(msg) {
                var result=0;
                if(!validatenull(msg)){
                    result= msg.toFixed(2);
                }
                return result ;
            }
        },
        methods: {

            enrolledChange(row) {
                if (row.isEnrolled == 0) {
                    row.score = 0;
                    row.isPass = null;
                }

            },
            currentChange(pageCurrent) {
                this.currentPage = pageCurrent;
                this.selClassTraineeList(this.currentPage, this.pageSize, this.$route.query.classId);
            },
            //分页查询
            sizeChange(pageSize) {
                this.pageSize = pageSize;
                this.selClassTraineeList(this.currentPage, this.pageSize, this.$route.query.classId);
            },
            totextOfRegister(val) {
                if (val.isRegistered === 0) {
                    return this.$t('training.notRegister')
                } else {
                    return this.$t('training.Register')
                }
            }, totextOfEnrolled(val) {
                if (val.isEnrolled === 0) {
                    return this.$t('training.notEnrolled')
                } else {
                    return this.$t('training.Enrolled')
                }
            }, totextOfResult(val) {
                if (val.isPass === 0) {
                    return this.$t('training.Fail')
                } else if (val.isPass === 1) {
                    return this.$t('training.Pass')
                } else {
                    return this.$t('training.NotAttended')
                }
            },
            allPass() {
                this.showData.forEach(item => {
                    item.isPass = 1
                })
            },
            selClassTraineeList(currentPage, pageSize, classId) {
                debugger;
                this.form.classId = classId
                selClassTraineeList(currentPage, pageSize, this.form).then(result => {
                    //console.log(result);
                    this.listLoading = false;
                    this.showData = result.data.data.records;
                    this.total = Number(result.data.data.total)
                });
                getClassResult(this.form).then(result => {
                    console.log(result);
                    this.classResult = result.data;
                })
            },
            onSubmit() {
                this.showData.map((i, index) => {
                    i.show = false
                    Vue.set(this.showData, index, i)
                })
                //提交后的其他操作


                add(this.showData).then(() => {
                    this.isDisabled = false;
                    this.$message({
                        type: "success",
                        message: this.$t('training.success')
                    });
                    this.selClassTraineeList(this.currentPage, this.pagesize, this.$route.query.classId);
                }, error => {
                    console.log(error);

                });

            },
            toClassList() {
                this.$router.push({path: '/training/classManager'})
            },
            exportExcel() {
                /* generate workbook object from table */
                var xlsxParam = {raw: true} // 导出的内容只做解析，不进行格式转换
                var wb = XLSX.utils.table_to_book(document.querySelector('#exportTab'), xlsxParam)
                console.log(wb);
                /* get binary string as output */
                var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'array'})
                try {
                    FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), 'result.xlsx')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log(e, wbout)
                    }
                }
                return wbout
            }


        },
    }


</script>

<style scoped>

</style>
