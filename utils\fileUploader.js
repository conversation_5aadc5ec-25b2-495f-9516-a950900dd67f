const fs = require("fs");

class FileUploader {
  static async appendFilesToFormData(formData, files) {
    if (!files) return formData;

    // 处理单个文件对象
    if (files.file && !Array.isArray(files.file)) {
      await this._appendSingleFile(formData, files.file);
    }
    // 处理文件数组
    else if (Array.isArray(files.file)) {
      for (const file of files.file) {
        await this._appendSingleFile(formData, file);
      }
    }

    return formData;
  }

  static async _appendSingleFile(formData, file) {
    try {
      if (file.filepath && file.originalFilename && file.mimetype) {
        formData.append("file", fs.createReadStream(file.filepath), {
          filename: file.originalFilename,
          contentType: file.mimetype,
        });
      } else {
        console.error("文件对象缺少必要属性:", file);
      }
    } catch (error) {
      console.error("读取文件时出错:", error);
      throw error;
    }
  }
}

module.exports = FileUploader;
