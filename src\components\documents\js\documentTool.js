"use strict";

import {validatenull} from "@/util/validate";
import {getCloudFileURL} from "@/api/common";

const documentTool = {};

documentTool.documentLibraryClick = function (files, url) {
    if (validatenull(files)) {
        if(!url){
            this.$notify({
                title: this.$t('tip'),
                message: this.$t('authorization.visibleOnlyTip'),
                type: 'warning'
            });
            return;
        }
        let preHttps = url.substr(0,5);
        if(preHttps.indexOf("http")==-1 || preHttps.indexOf("https")=="-1"){
            url = "https://"+url;
        }
        window.open(url);
    } else {
        this.download(files[0].fileUrl);
    }
}

documentTool.newServiceClick = function (fileUrl, serviceUrl) {
    if (validatenull(fileUrl)) {
        let preHttps = serviceUrl.substr(0,5);
        if(preHttps.indexOf("http")==-1 || preHttps.indexOf("https")=="-1"){
            serviceUrl = "https://"+serviceUrl;
        }
        window.open(serviceUrl);
    } else {
        this.download(fileUrl);
    }
}

documentTool.download = function (cloudId) {
    getCloudFileURL(cloudId).then((res) => {
        window.open(res.data, "_blank");
    });
}

export {
    documentTool
}
