import request from '@/router/axios';


const auditLogApi = {
    /**
     *
     * @param limit 每页条数
     * @param offset 起始条数
     * @param params
     */
    queryAuditLogPage : (limit, offset, params) => {
        return request({
            url: '/api/sgs-mart/auditLog/queryPage',
            method: 'post',
            data: {
                ...params, limit, offset
            }
        })
    },

    queryTrfAndReportByTrfId:(params)=>{
        return request({
            url: '/api/sgs-mart/v2/trf/queryTrfAndReportByTrfId',
            method: 'post',
            data: params
        })
    },
    queryLogFromSci:(param)=>{
        return request({
            url:'/api/sgsapi/sciops/sci/tool/trfSyncConditionList',
            method:'post',
            data:param
        })
    }
}

export default auditLogApi;