<template>
    <div class="smart_views_newRegister" id="smart_views_newRegister">
        <el-form class="login-form" :model="accountApplyForm" status-icon ref="accountApplyForm" size="medium" label-width="0px" v-if="showBasicForm">
            <el-form-item v-if="accountApplyForm.applyType == 1" prop="email" :rules=" [
                { required: true, message: this.$t('register.emailBlur'), trigger: 'change' },
                { type: 'email', message: this.$t('register.emailRigthBlur'), trigger: 'change' }]">
                <el-input @input="changeAccount" v-model="accountApplyForm.email" maxlength="50"
                          show-word-limit
                          :placeholder="$t('register.emailBlur')">
                    <i slot="prefix"><img src="/img/icon/email.png" style="width: 20px;height:20px; margin-top: 6px;"/></i>
                </el-input>
            </el-form-item>
            <el-form-item v-if="accountApplyForm.applyType == 2" prop="mobile" :rules="[
                        { required: true, message: this.$t('register.phoneBlur'), trigger: 'change' },
                        { pattern: /^1[2-9]\d{9}$/, message: this.$t('register.telePhoneBlur'), trigger: 'change' }]">
                <el-input @input="changeAccount" v-model="accountApplyForm.mobile" maxlength="11"
                          :placeholder="$t('login.phone')"
                          oninput="this.value=this.value.replace(/[^\d.]/g,'')"
                >
                    <i slot="prefix"><img src="/img/icon/mobile.png" style="margin-top: 6px;"/></i>
                </el-input>
            </el-form-item>
            <el-form-item prop="userName" :rules="[
                        { required: true, message: this.$t('register.accountBlur'), trigger: 'blur' },
                        { min: 3, max: 50, message: this.$t('register.userNameLength'), trigger: 'blur'}]">
                <el-input v-model="accountApplyForm.userName" maxlength="50" show-word-limit
                          :placeholder="$t('register.accountBlur')">
                    <i slot="prefix"><img src="/img/icon/guide_buyer.png" style="margin-top:7px;"/></i>
                </el-input>
            </el-form-item>
            <div class="login-menu text-right" style="margin-top: -10px;">
                <el-link  type="primary" :underline="false" style="border-bottom: 1px solid #f60" @click="registerTypeChange">
                    {{
                        accountApplyForm.applyType == 1 ? $t('register.phoneRegister') : $t('register.emailRegister')
                    }}
                </el-link>
            </div>
            <el-form-item>
                <el-button
                        type="primary"
                        class="login-submit"
                        :disabled="applySubmitDisabled"
                        :loading="applySubmitLoading"
                        @click="handlerVerify"
                        style="width: 100%;">
                    {{
                        accountApplyForm.applyType == 2 ? $t('register.newRegister.verifyPhone') :  $t('register.newRegister.verifyEmail')
                    }}
                </el-button>
            </el-form-item>
        </el-form>
        <!--   verify code form     -->
        <el-form :model="accountApplyForm" ref="verifyAccountForm" v-if="showVerifyForm" label-width="0px"  @submit.native.prevent>
            <el-row>
                <el-col :span="24">
                    <span>
                        {{$t('register.newRegister.sendEmailTo')}}
                        <u> {{accountApplyForm.applyType==1?accountApplyForm.email:accountApplyForm.mobile}}</u>{{$t('register.newRegister.commaSymbol')}}
                        <span style="display: block">
                            {{$t('register.newRegister.belowCode')}}
                        </span>
                    </span>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="verificationCode"
                                  :rules="[
                                       { required: true, message: $t('register.verificationCodeBlur'), trigger: 'blur' },
                                       { pattern: /^[0-9]{6}$/, message: $t('register.newRegister.length6'), trigger: 'blur' }
                                  ]">
                        <el-input v-model="accountApplyForm.verificationCode"
                                  maxlength="6"
                                  show-word-limit
                                  clearable
                                  @input="onInputVerificationCode"
                                  :placeholder="$t('register.verificationCodeBlur')">
                            <i slot="prefix" class=""><img src="/img/icon/safecode.png" style="width:20px;height: 20px; margin-top:7px;"/></i>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row style="text-align: center;padding-bottom: 10px;margin-top: 10px;">
                <el-col>
                    <el-button
                            type="primary"
                            class="login-submit"
                            :disabled="verifyBtnDisabled"
                            :loading="verifyBtnLoading"
                            @click="handlerVerifyCode"
                            style="width: 100%; margin-top: -10px;">
                        {{$t('register.newRegister.verify')}}
                    </el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col>
                    <el-button
                            class="login-submit"
                            :disabled="isCounting"
                            :loading="resendBtnLoading"
                            @click="resendCode"
                            style="width: 100%; margin-top: -10px;"
                    >
                        {{codeButtonText}}
                    </el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col>
                    <span style="display: block">{{$t('register.newRegister.donotGetCode')}}</span>
                    <span style="display: block">
                       {{$t('register.newRegister.sendIn5M')}}
                    </span>
                    <span>
                       {{ accountApplyForm.applyType == 1? $t('register.newRegister.checkSpamMail') : $t('register.newRegister.checkSpamSms')}}
                    </span>
                </el-col>
            </el-row>
        </el-form>

        <div v-if="showBasicForm">
            <div class="register_checkbox">
                <el-checkbox v-model="isArgee"
                             @change="agreeCondition"
                             style="padding-left: 0;color: #555555">
                </el-checkbox>
                <span style="padding-left: 5px;color:#555555;">{{ $t('login.readAgree') }}</span>
                <span>
                    <a @click="openPolicy(1,true)" style="cursor: pointer;color: #FF6600;border-bottom: 1px solid #FF6600">
                      {{ $t('login.serviceTrems') }}
                    </a>
                    {{ $t('login.serviceTremsEnd') }}
                  </span>
            </div>
            <div class="register_checkbox">
                <el-checkbox v-model="agreeMarketing" style="padding-left: 0;color: #555555"></el-checkbox>
                <span style="padding-left: 5px;color:#555555;">{{$t('login.readMarketing')}}
                     <a @click="openPolicy(2,true)" style="cursor: pointer;color: #FF6600;border-bottom: 1px solid #FF6600 ">
                      {{ $t('agreement.privacyPolicy') }}
                    </a>
                </span>
                <span>{{ $t('login.readMarketingAfter') }}</span>
            </div>
        </div>

        <el-dialog
                :visible.sync="policyDia"
                width="70%"
                top="5vh"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                append-to-body
                show-close
                @close="beforePolicyDiaClose">
            <policy
                    v-if="policyDia"
                    @agree="agreePolicy"
                    :tabs-index="tabsIndex"
                    :just-open-policy="justOpenPolicy"
            ></policy>
        </el-dialog>
    </div>
</template>

<script>
import {mapGetters} from 'vuex'


import registerApi from '@/api/register'
import registerCrypto from "@/util/registerCrypto";
import TopLang from "../index/top/top-lang";
import policy from "./policy";
export function isvalidPhone(str) {
    const reg = /^1[23456789]\d{9}$/;
    return reg.test(str)
}

export default {
    name: "newRegister",
    data() {

        return {
            isArgee:false,//条款
            agreeMarketing:false,//市场推送
            justOpenPolicy:false,
            /* 按钮控制*/
            applySubmitDisabled:false,
            applySubmitLoading :false,
            verifyBtnDisabled:false,
            verifyBtnLoading :false,
            resendBtnDisabled:false,
            resendBtnLoading :false,
            /* 表单控制*/
            showBasicForm:true,
            showVerifyForm:false,
            policyDia :false,
            tabsIndex:1,
            /* 倒计时*/
            isCounting:true,
            countDown:60,
            countDownInterval:null,
            /*表单对象*/
            accountApplyForm: {
                email:'',
                userName: '',
                mobile:'',
                applyType: 1,
                language: this.language,
                verificationCode:''
            }
        }
    },
    methods: {
        changeAccount(){
            this.isArgee = false;
        },
        beforePolicyDiaClose(){
            this.applySubmitDisabled = false;
            this.applySubmitLoading = false;
        },
        registerTypeChange() {
            this.accountApplyForm.applyType ^= 3;
            let val = this.accountApplyForm.applyType;
            if (val == 2) {//手机号注册
                this.$set(this.accountApplyForm, 'email', '');
            } else {//邮箱注册
                this.$set(this.accountApplyForm, 'mobile', '');
            }
        },
        handlerVerify(){
            this.$refs.accountApplyForm.validate(async (valid) => {
                if(!valid){
                    return;
                }
                this.accountApplyForm.language = this.language;
                let checkAcc = await this.checkAccount();
                if(!checkAcc){
                    return;
                }
                if(!this.isArgee){
                    this.openPolicy(1,false);
                    return;
                }
                //获取验证信息
                this.getVerify();
            })
        },
        checkAccount(){
            return new Promise((resolve,reject)=>{
                this.$refs['accountApplyForm'].validate((valid) => {
                    this.applySubmitDisabled = true;
                    this.applySubmitLoading = true;
                    //后端验证，然后发送验证码给邮箱或者手机号
                    let {email,userName,mobile,applyType,language} = this.accountApplyForm;
                    let param = {email,userName,mobile,applyType,language}
                    registerApi.checkAccount(param).then(res=>{
                        console.log("checkAccount",res);
                        resolve(res.status==200 && res.data && res.data.status==200);
                    }).catch(err=>{
                        console.log("checkAccount",err);
                        this.applySubmitLoading = false;
                        this.applySubmitDisabled = false;
                        resolve(false);
                    })
                });
            })
        },
        getVerify(){
            this.$refs['accountApplyForm'].validate((valid) => {
                this.applySubmitDisabled = true;
                this.applySubmitLoading = true;
                //后端验证，然后发送验证码给邮箱或者手机号
                let {email,userName,mobile,applyType,language} = this.accountApplyForm;
                let param = {email,userName,mobile,applyType,language}
                registerApi.verificationCoded(param).then(res=>{
                    console.log("get verification code",res);
                    if(res.status==200 && res.data && res.data.status==200){
                        //跳转到验证码的form
                        this.showBasicForm = false;
                        this.showVerifyForm = true;
                        this.startCountdown();
                    }
                }).catch(err=>{
                    console.log("get verification code",err);
                    this.applySubmitLoading = false;
                    this.applySubmitDisabled = false;
                })
            });
        },
        handlerVerifyCode(){
            this.$refs['verifyAccountForm'].validate((valid) => {
                if (!valid) {
                    return;
                }
                this.verifyBtnDisabled = true;
                this.verifyBtnLoading = true;
                registerApi.checkCode(this.accountApplyForm).then(res =>{
                    console.log("check success",res);
                    if(res.status==200 && res.data && res.data.status==200){
                        let token = res.data.data || '';
                        this.verifySuccess(token);
                    }else{
                        this.$notify.error("验证码不正确");
                        this.verifyBtnDisabled = false;
                        this.verifyBtnLoading = false;
                    }
                }).catch(err=>{
                    this.verifyBtnDisabled = false;
                    this.verifyBtnLoading = false;
                })
            });
        },
        verifySuccess(token){
            let { email,
                userName,
                mobile,
                applyType,
                verificationCode,
                language
            } = this.accountApplyForm;
            let sign = registerCrypto.md5(email+userName+mobile+applyType+verificationCode+language+token);
            let param = {
                email,
                userName,
                mobile,
                applyType,
                verificationCode,
                sign,
                language,
                token,
                receiveMarketingCommunication:this.agreeMarketing?1:0
            };
            //加密
            sessionStorage.setItem('registerInfo',sign);
            let urlParam = encodeURIComponent(registerCrypto.encrypt(JSON.stringify(param)));
            let url = window.location.origin+'/web/register/info?param='+urlParam+'&hash='+new Date().getTime();
            //alert("生成的md5"+sign);
            window.location.href = url;
            //window.open(url,'_blank');
        },
        resendCode(){
            let {email,userName,mobile,applyType,language} = this.accountApplyForm;
            let param = {email,userName,mobile,applyType,language}
            registerApi.verificationCoded(param).then(res=>{
                console.log("resend verification code",res);
                if(res.status==200 && res.data && res.data.status==200){
                    this.$notify.success("Success");
                    this.startCountdown();
                }else{
                    this.$notify.error("Resend Code Fail");
                }
            }).catch(err=>{
                console.log("get verification code",err);
            })
        },
        agreeCondition(){
            if(this.isArgee){
                this.openPolicy(1,true);
                this.$nextTick(()=>{
                    this.$set(this,'isArgee',false)
                })
            }
        },
        closePolicyDia(){
            this.applySubmitDisabled =false;
            this.applySubmitLoading = false;
            this.policyDia = false;
        },
        openPolicy(tabsIndex,justOpenPolicy){
            this.justOpenPolicy=justOpenPolicy;
            this.tabsIndex = tabsIndex;
            this.policyDia = true;
        },
        agreePolicy(flag){
            this.closePolicyDia();
            this.isArgee = flag;
            if(!flag){
                return;
            }
            if(this.justOpenPolicy){
                return;
            }
            //存在注册信息再继续走注册逻辑
            let {email,userName,mobile} = this.accountApplyForm;
            if(this.accountApplyForm.applyType==1){
                if(!email || !userName){
                    return;
                }
            }
            if(this.accountApplyForm.applyType==2){
                if(!mobile || !userName){
                    return;
                }
            }
            this.getVerify();
        },
        startCountdown(){
            this.isCounting = true;
            this.countDown = 60
            const timer = setInterval(() => {
                this.countDown--;
                if(this.countDown <= 0){
                    clearInterval(timer);
                    this.isCounting = false;
                }
            }, 1000);
        },
        onInputVerificationCode(value){
            //console.log("onInputVerificationCode",value)
            this.accountApplyForm.verificationCode = value.replace(/[^\d.]/g,'')
        },
    },
    created() {
    },
    mounted() {
    },
    watch: {},
    computed: {
        ...mapGetters(['permission', 'userInfo', 'language']),
        codeButtonText() {
            return this.$t('register.newRegister.resend') + (this.isCounting ? (`( ${this.countDown}`+ this.$t('register.newRegister.second')+ ')') : '');
        },
    },
    props: {},
    updated() {
    },
    beforeDestroy() {
    },
    destroy() {
    },
    components: {TopLang,policy}
}
</script>

<style lang="scss">
.smart_views_newRegister {
  position: relative;
  padding-left: 2px;

  .el-dialog__header {
    padding: 0 !important;
  }

  .login-submit {
    height: 48px;
    margin-top: 10px;
  }

  .login-form {
    /deep/ .el-input__inner {
      height: 40px;
      padding-left: 36px !important;
    }
    input{
      width: calc(100% - 62px);
    }
    .el-form-item__content .el-input__suffix{
      border-bottom: solid 1px black !important;
      min-width: 67px;
    }
  }

  .el-input__prefix {
    line-height: 45px;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #555555;
  }
}
</style>