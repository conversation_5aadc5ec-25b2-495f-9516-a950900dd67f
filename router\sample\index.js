const router = require("koa-router")();
const Api = require("./../../request");

// 客户查询
router.post(
  "/api/sgsapi/DFFV2Api/dff/queryProductViewCustomerList",
  async (ctx, next) => {
    const datas = await Api.post(
      "sgsapi/DFFV2Api/dff/queryProductViewCustomerList",
      ctx
    );
    ctx.body = { ...datas, code: datas.status };
  }
);

router.post("/api/sgs-pbm/sample/web/v1/export", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/export", ctx);
  ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
  ctx.body = datas;
});

// 删除product
router.post("/api/sgs-pbm/sample/web/v1/action", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/action", ctx);
  ctx.body = datas;
});

// 商品列表
router.post("/api/sgs-pbm/sample/web/v1/page", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/page", ctx);
  ctx.body = datas;
});

// 模板列表
router.post("/api/sgs-pbm/sample/web/v1/templateList", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/templateList", ctx);
  ctx.body = datas;
});

// sample下载
router.post("/api/sgs-pbm/sample/web/v1/download", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/download", ctx);
  ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
  ctx.body = datas;
});

router.post("/api/sgs-pbm/sample/web/v1/check", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/check", ctx);
  ctx.body = datas;
});

// table 动态表头获取
router.post("/api/sgs-pbm/sample/web/v1/filed/list", async (ctx, next) => {
  const datas = await Api.post("sgs-pbm/sample/web/v1/filed/list", ctx);
  ctx.body = datas;
});

module.exports = router;
