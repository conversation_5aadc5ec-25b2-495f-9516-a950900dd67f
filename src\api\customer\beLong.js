import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/add',
        method: 'post',
        data: form
    })
}
export const updateLongStatus = (form) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/updateLongStatus',
        method: 'post',
        data: form
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}
export const getBeLongs = (customerId) => {
    return request({
        url: '/api/sgs-mart/customer/beLong/list-noPage',
        method: 'get',
        params: {
            customerId,
        }
    })
}

