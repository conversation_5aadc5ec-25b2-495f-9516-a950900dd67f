<template>
    <basic-container v-loading="allPageLoading">
        <el-row>
            <el-col :span="16">
                <h1 class="top-title">
                    {{ $t("wel1.myTrf") }}
                    <el-tooltip content="Back to new trf list">
                        <router-link
                            style="font-size: 18px; color: #ff6600"
                            :to="{ path: '/trf/newList' }"
                        >
                            <i
                                class="icon-all iconfanhui1"
                                style="font-size: 20px !important"
                            ></i>
                        </router-link>
                    </el-tooltip>
                </h1>
            </el-col>
            <el-col :span="8">
                <div class="trf-o-btn text-right">
                    <a
                        style="display: none"
                        id="batchDownloadA"
                        :href="batchDownloadHref"
                        download="Report List.zip"
                        target="_blank"
                    ></a>

                    <el-popover
                        v-if="permissionList.trfDownLoadBtn"
                        width="220"
                        trigger="hover"
                        placement="bottom"
                    >
                        <ul class="list-unstyled add-menu">
                            <li>
                                <div
                                    style="cursor: pointer"
                                    v-if="permissionList.trfDownLoadBtn"
                                    :loading="downLoading"
                                    @click="exportExcelClick"
                                >
                                    {{ $t("operation.exportTRF") }}
                                </div>
                            </li>
                            <li>
                                <div
                                    style="cursor: pointer"
                                    v-if="permissionList.trfDownLoadBtn"
                                    :loading="downLoading"
                                    @click="exportReportExcelClick"
                                >
                                    {{ $t("operation.exportReportFiles") }}
                                </div>
                            </li>
                        </ul>
                        <el-button
                            class="line-btn"
                            slot="reference"
                            :loading="downLoading"
                        >
                            <img
                                style="vertical-align: text-bottom"
                                src="/img/icon/export.png"
                            />
                            {{ $t("operation.export") }}
                            <i
                                class="el-icon-arrow-down pull-right"
                                style="padding-top: 6px; font-weight: bold"
                            ></i>
                        </el-button>
                    </el-popover>

                    <el-popover
                        v-if="menu.length > 0 && menu[0].children.length > 0"
                        width="260"
                        trigger="hover"
                        placement="bottom"
                    >
                        <ul
                            class="list-unstyled add-menu"
                            v-if="menu.length > 0"
                        >
                            <li
                                v-for="item in menu[0].children[0].children[0]
                                    .children"
                                :key="item.id"
                            >
                                <el-tooltip
                                    class="item"
                                    effect="dark"
                                    :content="item.name"
                                    placement="top"
                                >
                                    <router-link
                                        target="_blank"
                                        :to="{ path: item.path }"
                                    >
                                        {{
                                            item.name.length > 30
                                                ? item.name.substr(0, 28) +
                                                  "..."
                                                : item.name
                                        }}
                                    </router-link>
                                </el-tooltip>
                            </li>
                        </ul>
                        <el-button
                            class="line-btn add-trf-btn"
                            id="add-trf-btn"
                            slot="reference"
                        >
                            <img
                                style="vertical-align: text-bottom"
                                src="/img/icon/addTrf.png"
                            />
                            {{ $t("trf.createtrf") }}
                            <i
                                class="el-icon-arrow-down pull-right"
                                style="padding-top: 6px; font-weight: bold"
                            ></i>
                        </el-button>
                    </el-popover>
                </div>
            </el-col>
        </el-row>

        <div class="wrap" v-loading="loading">
            <el-form :inline="true" :model="query" size="medium">
                <div class="tools clearfix">
                    <div
                        id="trf-list-filter"
                        :class="
                            language == 'en-US'
                                ? 'en-trf-list-filter'
                                : 'cn-trf-list-filter'
                        "
                    >
                        <el-form-item>
                            <el-input
                                :placeholder="$t('trfList.query.searchFor')"
                                v-model.trim="query.order.properties.queryValue"
                                @keyup.enter.native="handelSubmit"
                                clearable
                                @clear="handelSubmit"
                            >
                                <i
                                    slot="prefix"
                                    class="el-input__icon el-icon-search"
                                    style="left: -10px; position: relative"
                                ></i>
                            </el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-date-picker
                                class="filter-date"
                                :value="[
                                    query.order.properties.startDate,
                                    query.order.properties.endDate,
                                ]"
                                type="daterange"
                                format="yyyy-MM-dd"
                                range-separator="-"
                                :unlink-panels="true"
                                :clearable="true"
                                @input="(val) => updateDate(val)"
                                :start-placeholder="
                                    $t('trfList.query.selStartDate')
                                "
                                :end-placeholder="
                                    $t('trfList.query.selEndDate')
                                "
                            ></el-date-picker>

                            <!-- <el-col :span="11">
                                <el-date-picker type="date" :placeholder="$t('trfList.query.selStartDate')"
                                    format="yyyy-MM-dd"
                                    :value="query.order.properties.startDate"
                                    @input="val => updateStartDate(val)"
                                    style="width: 100%;"></el-date-picker>
                            </el-col>
                            <el-col class="line" :span="2">-</el-col>
                            <el-col :span="11">
                                <el-date-picker :placeholder="$t('trfList.query.selEndDate')" format="yyyy-MM-dd"
                                    :value="query.order.properties.endDate"
                                    @input="val => updateEndDate(val)"
                                    style="width: 100%;"></el-date-picker>
                            </el-col> -->
                        </el-form-item>
                        <el-form-item class="date-group">
                            <el-button
                                @click="selectedDate(1, 'w', 0)"
                                :class="{ active: currentDate == 0 }"
                            >
                                {{ $t("trfList.query.oneWeek") }}
                            </el-button>
                            <el-button
                                @click="selectedDate(1, 'M', 1)"
                                :class="{ active: currentDate == 1 }"
                            >
                                {{ $t("trfList.query.oneMonth") }}
                            </el-button>
                            <el-button
                                @click="selectedDate(6, 'M', 2)"
                                :class="{ active: currentDate == 2 }"
                            >
                                {{ $t("trfList.query.sixMonth") }}
                            </el-button>
                            <el-button
                                @click="selectedDate(12, 'M', 3)"
                                :class="{ active: currentDate == 3 }"
                            >
                                {{ $t("trfList.query.oneYear") }}
                            </el-button>
                            <!--  <el-button type="primary" @click="selectedDate(1, 'y')"> {{$t('trfList.query.oneYear')}}</el-button>-->
                            <el-button
                                class="mt_0 ml_1em bg_orange"
                                @click="clear()"
                            >
                                <img src="/img/clear.png" />
                                {{ $t("operation.reset") }}
                            </el-button>
                            <!-- <el-button type="primary" v-if="permissionList.trfDownLoadBtn" :loading="downLoading"
                                    @click="exportExcelClick"> {{$t('operation.export')}}
                            </el-button> -->
                            <span class="check-time-out-1">
                                <el-checkbox
                                    :value="checkedTimeOutTrf"
                                    @change="(val) => checkTimeOutHandle(val)"
                                >
                                    {{ $t("trfList.query.timeOutTrf") }}
                                </el-checkbox>
                            </span>
                        </el-form-item>
                        <!-- <el-form-item>
                            <el-checkbox :value="checkedTimeOutTrf" @change="(val) => checkTimeOutHandle(val)">
                                {{$t('trfList.query.timeOutTrf')}}
                            </el-checkbox>
                        </el-form-item> -->

                        <!--<el-form-item label="按" label-width="20px">
                            <el-select v-model="query.order.properties.column" @change="sortChange" clearable filterable
                                    style="width: 100%;">
                                <el-option v-for="(item, index) in selectedVal"
                                        :label="$t(item.displayName)"
                                        :value="item.fieldCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label-width="0px">
                            <el-select v-model="query.order.properties.sort" @change="sortChange" filterable
                                    style="width: 100%;">
                                <el-option label="升序" value="asc"></el-option>
                                <el-option label="降序" value="desc"></el-option>
                            </el-select>
                        </el-form-item>-->
                    </div>
                    <div
                        class="sort-btn"
                        :class="
                            language == 'en-US' ? 'en-sort-btn' : 'cn-sort-btn'
                        "
                    >
                        <!-- <span class="check-time-out-2 pull-left">
                            <el-checkbox :value="checkedTimeOutTrf" @change="(val) => checkTimeOutHandle(val)">
                                {{$t('trfList.query.timeOutTrf')}}
                            </el-checkbox>
                        </span> -->
                        <el-button
                            @click="submitDateSortClick(0)"
                            label="1"
                            class="plain-black"
                            :class="{ active: currentSortType == 0 }"
                        >
                            {{ $t("trfList.submitDate") }}
                            <!-- <i v-if="submitDateSort=='desc'" class="el-icon-sort-down el-icon--right"></i>
                            <i v-else class="el-icon-sort-up el-icon--right"></i> -->
                            <img
                                src="/img/icon/sort_icon.png"
                                class="trf-sort-icon"
                                :class="
                                    submitDateSort == 'desc' ? 'isSort' : ''
                                "
                            />
                        </el-button>
                        <el-button
                            v-if="isShowReviewConclusion"
                            @click="buyerConclusionSortClick(1)"
                            label="1"
                            class="plain-black"
                            :class="{ active: currentSortType == 1 }"
                        >
                            {{ $t("trfList.buyerReviewConclusion") }}
                            <!-- <i v-if="buyerReviewConclusionSort=='desc'" class="el-icon-sort-down el-icon--right"></i>
                            <i v-else class="el-icon-sort-up el-icon--right"></i> -->
                            <img
                                src="/img/icon/sort_icon.png"
                                class="trf-sort-icon"
                                :class="
                                    buyerReviewConclusionSort == 'desc'
                                        ? 'isSort'
                                        : ''
                                "
                            />
                        </el-button>
                        <el-button
                            @click="reportDateSortClick(2)"
                            label="1"
                            class="plain-black"
                            :class="{ active: currentSortType == 2 }"
                        >
                            {{ $t("trfList.reportIssuedTime") }}
                            <!-- <i v-if="reportAppoveDateSort=='desc'" class="el-icon-sort-down el-icon--right"></i>
                            <i v-else class="el-icon-sort-up el-icon--right"></i> -->
                            <img
                                src="/img/icon/sort_icon.png"
                                class="trf-sort-icon"
                                :class="
                                    reportAppoveDateSort == 'desc'
                                        ? 'isSort'
                                        : ''
                                "
                            />
                        </el-button>
                        <el-button
                            @click="trfNoSortClick(3)"
                            label="1"
                            class="plain-black"
                            :class="{ active: currentSortType == 3 }"
                        >
                            {{ $t("trfList.trfNo") }}
                            <!-- <i v-if="trfNoSort=='desc'" class="el-icon-sort-down el-icon--right"></i>
                            <i v-else class="el-icon-sort-up el-icon--right"></i> -->
                            <img
                                src="/img/icon/sort_icon.png"
                                class="trf-sort-icon"
                                :class="trfNoSort == 'desc' ? 'isSort' : ''"
                            />
                        </el-button>
                    </div>
                </div>
            </el-form>
            <div style="position: relative">
                <TabSelect
                    style="
                        position: absolute;
                        z-index: 9;
                        right: 20px;
                        top: 13px;
                    "
                    :trf-template="dffTemplateData"
                    ref="tabSelect"
                    @updateVal="updateSelectedHandle"
                    :userInfo="userInfo"
                />

                <el-table
                    ref="trfTable"
                    id="trfTable"
                    v-if="isShowTrfTable"
                    class="trfListTable"
                    :row-key="getRowKeys"
                    :row-class-name="getRowClassName"
                    fixed
                    @expand-change="trfReportChange"
                    :expand-row-keys="expands"
                    :element-loading-text="$t('loading')"
                    :data="trfData"
                    style="width: 100%"
                    height="500"
                >
                    <el-table-column
                        type="expand"
                        class-name="expand"
                        width="20"
                    >
                        <template slot-scope="scope">
                            <div>
                                <el-row
                                    v-if="scope.row.trfStatus == 5"
                                    v-loading="reportsLoading"
                                    v-for="(item, index) in reports"
                                    :key="index"
                                >
                                    <el-form
                                        label-position="right"
                                        inline
                                        :class="{ 'demo-table-expand': true }"
                                    >
                                        <el-form-item label="Report No:">
                                            <span>{{ item.reportNo }}</span>
                                            <span
                                                v-if="item.reportFiles"
                                                v-for="(
                                                    file, index
                                                ) in item.reportFiles"
                                            >
                                                <i
                                                    @click="
                                                        downloadReport(
                                                            file.cloudId,
                                                        )
                                                    "
                                                    v-if="file.cloudId"
                                                    title="Download Report"
                                                    style="
                                                        cursor: pointer;
                                                        color: red;
                                                    "
                                                    class="el-icon-document"
                                                ></i>
                                            </span>
                                            <span
                                                :style="{
                                                    color: getReportColor(
                                                        item.conclusion,
                                                    ),
                                                    'margin-left': '30px',
                                                }"
                                            >
                                                {{ item.customerConclusion }}
                                            </span>
                                        </el-form-item>
                                    </el-form>
                                </el-row>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="Job Status" width="150">
                        <template slot="header" slot-scope="scope">
                            <div>{{ $t("trfList.jobStatus") }}</div>
                            <el-select
                                size="small"
                                v-model="query.order.properties.trfStatus"
                                @change="trfStatusChange"
                                id="trf-list-status-filter"
                                @clear="handelSubmit"
                                :placeholder="$t('select')"
                                clearable
                            >
                                <el-option
                                    :value="null"
                                    :label="$t('trfStatus.all')"
                                ></el-option>
                                <el-option
                                    :value="1"
                                    :label="$t('trfStatus.draft')"
                                ></el-option>
                                <el-option
                                    :value="2"
                                    :label="$t('trfStatus.submitted')"
                                ></el-option>
                                <el-option
                                    :value="3"
                                    :label="$t('trfStatus.preparation')"
                                ></el-option>
                                <el-option
                                    v-if="
                                        userInfo.productLineCode == 'AFL' ||
                                        role.isSGS
                                    "
                                    :value="13"
                                    :label="$t('trfStatus.confirmingQuotation')"
                                ></el-option>
                                <el-option
                                    v-if="
                                        userInfo.productLineCode == 'AFL' ||
                                        role.isSGS
                                    "
                                    :value="14"
                                    :label="$t('trfStatus.confirmQuotation')"
                                ></el-option>
                                <el-option
                                    :value="4"
                                    :label="$t('trfStatus.testing')"
                                ></el-option>
                                <el-option
                                    :value="5"
                                    :label="$t('trfStatus.completed')"
                                ></el-option>
                                <el-option
                                    :value="6"
                                    :label="$t('trfStatus.cancel')"
                                ></el-option>
                                <el-option
                                    :value="11"
                                    :label="$t('trfStatus.pending')"
                                ></el-option>
                            </el-select>
                        </template>
                        <template slot-scope="scope">
                            <div
                                :data-row-key="scope.row.id"
                                :key="scope.row.id"
                                name="expand"
                            >
                                <AFLTRFStatus
                                    v-if="scope.row.productLineCode == 'AFL'"
                                    :status="scope.row.trfStatus"
                                />
                                <TRFStatus
                                    v-else
                                    :status="scope.row.trfStatus"
                                    :reason="scope.row.pendingReason"
                                />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="trfNo"
                        label="TRF No"
                        width="auto"
                        min-width="160"
                    >
                        <template slot="header" slot-scope="scope">
                            <div>{{ $t("trfList.trfNo") }}</div>
                            <el-input
                                size="small"
                                :value="query.order.properties.trfNo"
                                :placeholder="$t('trfList.trfNo')"
                                @keyup.enter.native="handelSubmit"
                                @input="
                                    (val) =>
                                        (query.order.properties.trfNo = val)
                                "
                                clearable
                                @clear="handelSubmit"
                            ></el-input>
                        </template>
                        <template slot-scope="scope">
                            <i
                                v-show="
                                    scope.row.trfStatus === 2 &&
                                    scope.row.isSubmissionTimeout === 1
                                "
                                class="el-icon-alarm-clock"
                                title="Submission Timeout"
                                style="cursor: pointer; color: red"
                            ></i>
                            <a
                                @click="trfDetailClick(scope.row)"
                                style="
                                    color: #ff6600;
                                    font-size: 14px;
                                    cursor: pointer;
                                "
                            >
                                {{ scope.row.trfNo }}
                            </a>
                        </template>
                    </el-table-column>

                    <el-table-column
                        v-for="(item, index) in selectedVal"
                        :key="item.fieldCode + index"
                        :show-overflow-tooltip="true"
                        :label="$t(item.displayName)"
                        :width="displayNameWidth(item.fieldCode)"
                    >
                        <template slot="header" slot-scope="scope">
                            <template>
                                <div>{{ $t(item.displayName) }}</div>
                                <template
                                    v-if="
                                        item.fieldCode === 'sampleReceiveDate'
                                    "
                                >
                                    <el-date-picker
                                        class="date-input"
                                        size="small"
                                        style="width: 100%; padding-left: 0"
                                        type="date"
                                        format="yyyy-MM-dd"
                                        :placeholder="$t('trfList.sampleDate')"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .sampleReceiveDateStr
                                        "
                                        @input="
                                            (val) => sampleReceiveDateQuery(val)
                                        "
                                    ></el-date-picker>
                                </template>
                                <template
                                    v-else-if="item.fieldCode === 'dueDate'"
                                >
                                    <el-date-picker
                                        class="date-input"
                                        size="small"
                                        style="width: 100%; padding-left: 0"
                                        type="date"
                                        format="yyyy-MM-dd"
                                        :placeholder="$t('trfList.dueDate')"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.dueDateStr
                                        "
                                        @input="(val) => dueDateQuery(val)"
                                    ></el-date-picker>
                                </template>
                                <template v-else>
                                    <el-date-picker
                                        class="date-input"
                                        size="small"
                                        style="width: 100%; padding-left: 0"
                                        v-if="
                                            item.fieldCode ==
                                            'trfSubmissionDate'
                                        "
                                        type="date"
                                        format="yyyy-MM-dd"
                                        :placeholder="$t('trfList.submitDate')"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.trfSubmitDate
                                        "
                                        @input="
                                            (val) =>
                                                changeSubmissionDateDateQuery(
                                                    val,
                                                )
                                        "
                                    ></el-date-picker>
                                    <el-input
                                        v-if="
                                            item.fieldCode == 'serviceTypeName'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .serviceTypeName
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.serviceTypeName =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'extendOrderNo'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.extendOrderNo
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.extendOrderNo =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'templateName'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.templateName
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.templateName =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode ==
                                            'buyerCustomerGroupName'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .buyerCustomerGroupName
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.buyerCustomerGroupName =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'applyNameEn'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .applyCustomerName
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.applyCustomerName =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'applyContact'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.applyContact
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                        @input="
                                            (val) =>
                                                (query.order.properties.applyContact =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'labName'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.labName"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.labName =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode ==
                                            'productDescription'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .productDescription
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.productDescription =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'styleNo'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.styleNo"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.styleNo =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'itemNo'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.itemNo"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.itemNo =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'poNo'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.poNo"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.poNo =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'refCode5'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.refCode5"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.refCode5 =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'productColor'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.productColor
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.productColor =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'refCode3'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.refCode3"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.refCode3 =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode == 'previousReportNo'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .previousReportNo
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.previousReportNo =
                                                    val)
                                        "
                                    ></el-input>

                                    <el-input
                                        v-if="
                                            item.fieldCode == 'fiberComposition'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .fiberComposition
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.fiberComposition =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'collection'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.collection
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.collection =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="item.fieldCode == 'season'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.season"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.season =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode ==
                                            'buyerOrgannization1'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .buyerOrgannization1
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.buyerOrgannization1 =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode == 'productionStage'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .productionStage
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.productionStage =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode ==
                                            'specialProductAttribute10'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .specialProductAttribute10
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.specialProductAttribute10 =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode ==
                                            'firstTimeApplicationFlag'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .firstTimeApplicationFlag
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.firstTimeApplicationFlag =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode ==
                                            'buyerSourcingOffice'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .buyerSourcingOffice
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.buyerSourcingOffice =
                                                    val)
                                        "
                                    ></el-input>
                                    <el-input
                                        v-if="
                                            item.fieldCode == 'productCategory1'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .productCategory1
                                        "
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.productCategory1 =
                                                    val)
                                        "
                                    ></el-input>

                                    <el-input
                                        v-if="item.fieldCode == 'reportNo'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="query.order.properties.reportNo"
                                        :placeholder="$t(item.displayName)"
                                        @keyup.enter.native="handelSubmit"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.reportNo =
                                                    val)
                                        "
                                    ></el-input>

                                    <el-date-picker
                                        class="date-input"
                                        size="small"
                                        style="width: 100%; padding-left: 0"
                                        v-if="
                                            item.fieldCode ==
                                            'reportApprovedDate'
                                        "
                                        type="date"
                                        format="yyyy-MM-dd"
                                        :placeholder="
                                            $t('trfList.reportApprovedDate')
                                        "
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .reportApprovedDateStr
                                        "
                                        @input="
                                            (val) => changeReportDateQuery(val)
                                        "
                                    ></el-date-picker>

                                    <el-input
                                        v-if="
                                            item.fieldCode ===
                                            'reportConclusion'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .reportConclusion
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.reportConclusion =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>

                                    <el-input
                                        v-if="item.fieldCode == 'pendingReason'"
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties.pendingReason
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.pendingReason =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>

                                    <el-input
                                        v-if="
                                            item.fieldCode ===
                                            'buyerReviewConclusion'
                                        "
                                        size="small"
                                        clearable
                                        @clear="handelSubmit"
                                        :value="
                                            query.order.properties
                                                .buyerReviewConclusion
                                        "
                                        :placeholder="$t(item.displayName)"
                                        :aria-placeholder="
                                            $lodash.last(item.fieldCode)
                                        "
                                        @input="
                                            (val) =>
                                                (query.order.properties.buyerReviewConclusion =
                                                    val)
                                        "
                                        @keyup.enter.native="handelSubmit"
                                    ></el-input>
                                </template>
                            </template>
                        </template>
                        <template slot-scope="scope">
                            <template>
                                <template
                                    v-if="item.fieldCode === 'reportConclusion'"
                                >
                                    <span
                                        v-if="
                                            $lodash.get(
                                                scope.row,
                                                'reportConclusion',
                                            ) && scope.row.trfStatus === 5
                                        "
                                        :style="{
                                            color:
                                                $lodash.get(
                                                    scope.row,
                                                    'reportConclusion',
                                                    '',
                                                ) === 'Pass'
                                                    ? 'green'
                                                    : $lodash.get(
                                                          scope.row,
                                                          'reportConclusion',
                                                          '',
                                                      ) === 'Fail'
                                                    ? 'red'
                                                    : '',
                                        }"
                                        v-text="
                                            $lodash.get(
                                                scope.row,
                                                'reportConclusion',
                                                '',
                                            )
                                        "
                                    ></span>
                                    <span
                                        v-else-if="scope.row.trfStatus === 5"
                                        :style="{
                                            color:
                                                $lodash.get(
                                                    scope.row,
                                                    'reportConclusion',
                                                    '',
                                                ) === 'Pass'
                                                    ? 'green'
                                                    : $lodash.get(
                                                          scope.row,
                                                          'reportConclusion',
                                                          '',
                                                      ) === 'Fail'
                                                    ? 'red'
                                                    : '',
                                        }"
                                        v-text="
                                            $lodash.get(
                                                scope.row,
                                                'reportConclusion',
                                                '',
                                            )
                                        "
                                    ></span>
                                    <span v-else v-text=""></span>
                                </template>
                                <template
                                    v-else-if="item.fieldCode === 'applyNameEn'"
                                >
                                    <span v-if="language == 'en-US'">
                                        {{
                                            $lodash.get(
                                                scope.row,
                                                "applyNameEn",
                                            )
                                                ? scope.row.applyNameEn
                                                : scope.row.applyNameCn
                                        }}
                                    </span>
                                    <span v-else>
                                        {{
                                            $lodash.get(
                                                scope.row,
                                                "applyNameCn",
                                            )
                                                ? scope.row.applyNameCn
                                                : scope.row.applyNameEn
                                        }}
                                    </span>
                                </template>
                                <template
                                    v-else-if="
                                        item.fieldCode === 'trfSubmissionDate'
                                    "
                                >
                                    <span>
                                        {{
                                            currentTz_YMD(
                                                $lodash.get(
                                                    scope.row,
                                                    item.fieldCode,
                                                    "",
                                                ),
                                            )
                                        }}
                                    </span>
                                </template>
                                <template
                                    v-else-if="
                                        item.fieldCode === 'sampleReceiveDate'
                                    "
                                >
                                    <span>
                                        {{
                                            currentTz_YMD(
                                                $lodash.get(
                                                    scope.row,
                                                    item.fieldCode,
                                                    "",
                                                ),
                                            )
                                        }}
                                    </span>
                                </template>
                                <template
                                    v-else-if="item.fieldCode === 'dueDate'"
                                >
                                    <span>
                                        {{
                                            currentTz_YMD(
                                                $lodash.get(
                                                    scope.row,
                                                    item.fieldCode,
                                                    "",
                                                ),
                                            )
                                        }}
                                    </span>
                                </template>
                                <template
                                    v-else-if="
                                        item.fieldCode === 'reportApprovedDate'
                                    "
                                >
                                    <span>
                                        {{
                                            currentTz_YMD(
                                                $lodash.get(
                                                    scope.row,
                                                    item.fieldCode,
                                                    "",
                                                ),
                                            )
                                        }}
                                    </span>
                                </template>

                                <template
                                    v-else-if="item.fieldCode === 'reportNo'"
                                >
                                    <span
                                        v-if="
                                            $lodash.get(scope.row, 'reportNo')
                                        "
                                        :style="{
                                            color:
                                                $lodash.get(
                                                    scope.row,
                                                    'reportNo',
                                                    '',
                                                ) === 'Pass'
                                                    ? 'green'
                                                    : $lodash.get(
                                                          scope.row,
                                                          'reports.0.reportConclusion',
                                                          '',
                                                      ) === 'Fail'
                                                    ? 'red'
                                                    : '',
                                        }"
                                        v-text="
                                            $lodash.get(
                                                scope.row,
                                                'reportNo',
                                                '',
                                            )
                                        "
                                    ></span>
                                    <span
                                        v-else
                                        :style="{
                                            color:
                                                $lodash.get(
                                                    scope.row,
                                                    'reportNo',
                                                    '',
                                                ) === 'Pass'
                                                    ? 'green'
                                                    : $lodash.get(
                                                          scope.row,
                                                          'reportNo',
                                                          '',
                                                      ) === 'Fail'
                                                    ? 'red'
                                                    : '',
                                        }"
                                        v-text="
                                            $lodash.get(
                                                scope.row,
                                                'reportNo',
                                                '',
                                            )
                                        "
                                    ></span>
                                </template>
                                <template v-else>
                                    <span>
                                        {{
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                                "",
                                            )
                                        }}
                                    </span>
                                </template>
                                <template
                                    v-else-if="item.fieldCode === 'reportDate'"
                                >
                                    <span>
                                        {{
                                            currentTz_YMD(
                                                $lodash.get(
                                                    scope.row,
                                                    item.fieldCode,
                                                    "",
                                                ),
                                            )
                                        }}
                                    </span>
                                </template>

                                <template
                                    v-else-if="
                                        item.fieldCode === 'pendingReason'
                                    "
                                >
                                    <span
                                        v-if="
                                            $lodash.get(
                                                scope.row,
                                                'pendingReason',
                                            ) &&
                                            (scope.row.trfStatus === 11 ||
                                                scope.row.trfStatus === 12)
                                        "
                                        v-text="
                                            $lodash.get(
                                                scope.row,
                                                'pendingReason',
                                                '',
                                            )
                                        "
                                    ></span>
                                </template>
                                <template v-else>
                                    <span
                                        v-if="
                                            item.fieldCode ===
                                            'reportConclusion'
                                        "
                                    >
                                        <span v-if="scope.row.trfStatus === 5">
                                            {{
                                                $lodash.get(
                                                    scope.row,
                                                    item.fieldCode,
                                                )
                                            }}
                                        </span>
                                    </span>
                                    <span v-else>
                                        {{
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                            )
                                        }}
                                    </span>
                                </template>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        :label="$t('operation.title')"
                        width="160"
                        align="left"
                    >
                        <template slot-scope="scope">
                            <el-tooltip
                                :content="$t('trfList.copyTrf')"
                                placement="top"
                            >
                                <i
                                    class="el-icon-copy-document menu-icon"
                                    v-if="
                                        permissionList.copyTrfBtn &&
                                        userInfo.userMgtId ==
                                            scope.row.createUserId &&
                                        scope.row.trfType != '20' &&
                                        scope.row.trfSourceType !=
                                            'ORDER_TO_TRF'
                                    "
                                    @click="copyTRF(scope.row)"
                                ></i>
                            </el-tooltip>
                            <el-tooltip
                                :content="$t('operation.modify')"
                                placement="top"
                            >
                                <el-button
                                    type="text"
                                    size="medium"
                                    icon="el-icon-edit menu-icon"
                                    :disabled="scope.row.trfStatus > 2"
                                    v-if="
                                        permissionList.trfEditBtn &&
                                        userInfo.userMgtId ==
                                            scope.row.createUserId &&
                                        scope.row.trfSourceType !=
                                            'ORDER_TO_TRF'
                                    "
                                    @click="editTrf(scope.row)"
                                ></el-button>
                            </el-tooltip>

                            <el-tooltip :content="$t('operation.chat')">
                                <i
                                    class="el-icon-chat-dot-square menu-icon"
                                    v-if="
                                        scope.row.imIsCreated &&
                                        scope.row.imIsJoined
                                    "
                                    @click="openChat(scope.row, scope.$index)"
                                >
                                    <img
                                        src="@/images/new1.png"
                                        alt=""
                                        class="newIcon"
                                        v-if="scope.row.imHasUnread"
                                    />
                                </i>
                            </el-tooltip>
                            <el-tooltip
                                :content="$t('trfList.viewEfilling')"
                                placement="top"
                            >
                                <i
                                    class="el-icon-view menu-icon"
                                    v-if="
                                        scope.row.efilingIds &&
                                        scope.row.efilingIds.length
                                    "
                                    @click="viewEfilling(scope.row)"
                                ></i>
                            </el-tooltip>
                            <el-tooltip
                                :content="$t('trfList.createEfilling')"
                                placement="top"
                            >
                                <i
                                    class="el-icon-files menu-icon"
                                    v-if="
                                        !scope.row.efilingIds ||
                                        !scope.row.efilingIds.length
                                    "
                                    @click="handleEfilling(scope.row)"
                                ></i>
                            </el-tooltip>
                            <!--<el-button v-if="scope.row.imIsCreated&&scope.row.imIsJoined"
                                       @click="openChat(scope.row,scope.$index)"
                                       type="text">
                                {{ $t('operation.chat') }}
                                <img src="@/images/new1.png" alt="" class="newIcon" v-if="scope.row.imHasUnread">
                                &lt;!&ndash; <span class="newIcon" v-if="scope.row.imHasUnread">new</span> &ndash;&gt;
                            </el-button>-->
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="query.pageNo"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="query.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="query.total"
            ></el-pagination>

            <el-table
                :data="excelData"
                stripe
                style="width: 0px; height: 0px"
                type="hidden"
                id="exportTable"
            >
                <el-table-column
                    :label="$t('trfList.jobStatus')"
                    prop="trfStatus"
                    width="120"
                    :formatter="trfStatusFormatter"
                ></el-table-column>
                <el-table-column
                    :label="$t('trfList.trfNo')"
                    prop="trfNo"
                    width="120"
                ></el-table-column>
                <el-table-column
                    v-for="(item, index) in selectedVal"
                    :key="item.fieldCode + index"
                    :label="$t(item.displayName)"
                    :width="displayNameWidth(item.fieldCode)"
                >
                    <template slot-scope="scope">
                        <template>
                            <template
                                v-if="
                                    item.fieldCode === 'reportConclusion' &&
                                    scope.row.trfStatus === 5
                                "
                            >
                                <span
                                    v-if="
                                        $lodash.get(
                                            scope.row,
                                            'reportConclusion',
                                        ) && scope.row.trfStatus === 5
                                    "
                                    :style="{
                                        color:
                                            $lodash.get(
                                                scope.row,
                                                'reportConclusion',
                                                '',
                                            ) === 'Pass'
                                                ? 'green'
                                                : $lodash.get(
                                                      scope.row,
                                                      'reportConclusion',
                                                      '',
                                                  ) === 'Fail'
                                                ? 'red'
                                                : '',
                                    }"
                                    v-text="
                                        $lodash.get(
                                            scope.row,
                                            'reportConclusion',
                                            '',
                                        )
                                    "
                                ></span>
                                <span
                                    v-else-if="scope.row.trfStatus === 5"
                                    :style="{
                                        color:
                                            $lodash.get(
                                                scope.row,
                                                'reportConclusion',
                                                '',
                                            ) === 'Pass'
                                                ? 'green'
                                                : $lodash.get(
                                                      scope.row,
                                                      'reportConclusion',
                                                      '',
                                                  ) === 'Fail'
                                                ? 'red'
                                                : '',
                                    }"
                                    v-text="
                                        $lodash.get(
                                            scope.row,
                                            'reportConclusion',
                                            '',
                                        )
                                    "
                                ></span>
                                <span v-else v-text=""></span>
                            </template>
                            <template
                                v-else-if="
                                    item.fieldCode === 'trfSubmissionDate'
                                "
                            >
                                <span>
                                    {{
                                        currentTz_YMD(
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                                "",
                                            ),
                                        )
                                    }}
                                </span>
                            </template>
                            <template
                                v-else-if="
                                    item.fieldCode === 'sampleReceiveDate'
                                "
                            >
                                <span>
                                    {{
                                        currentTz_YMD(
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                                "",
                                            ),
                                        )
                                    }}
                                </span>
                            </template>
                            <template v-else-if="item.fieldCode === 'dueDate'">
                                <span>
                                    {{
                                        currentTz_YMD(
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                                "",
                                            ),
                                        )
                                    }}
                                </span>
                            </template>
                            <template
                                v-else-if="item.fieldCode === 'reportDate'"
                            >
                                <span>
                                    {{
                                        currentTz_YMD(
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                                "",
                                            ),
                                        )
                                    }}
                                </span>
                            </template>
                            <template
                                v-else-if="
                                    item.fieldCode === 'reportApprovedDate'
                                "
                            >
                                <span>
                                    {{
                                        currentTz_YMD(
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                                "",
                                            ),
                                        )
                                    }}
                                </span>
                            </template>
                            <template
                                v-else-if="
                                    item.fieldCode === 'reportNo' &&
                                    scope.row.trfStatus === 5
                                "
                            >
                                <span
                                    v-if="$lodash.get(scope.row, 'reportNo')"
                                    :style="{
                                        color:
                                            $lodash.get(
                                                scope.row,
                                                'reportNo',
                                                '',
                                            ) === 'Pass'
                                                ? 'green'
                                                : $lodash.get(
                                                      scope.row,
                                                      'reports.0.reportConclusion',
                                                      '',
                                                  ) === 'Fail'
                                                ? 'red'
                                                : '',
                                    }"
                                    v-text="
                                        $lodash.get(scope.row, 'reportNo', '')
                                    "
                                ></span>
                                <span
                                    v-else
                                    :style="{
                                        color:
                                            $lodash.get(
                                                scope.row,
                                                'reportNo',
                                                '',
                                            ) === 'Pass'
                                                ? 'green'
                                                : $lodash.get(
                                                      scope.row,
                                                      'reportNo',
                                                      '',
                                                  ) === 'Fail'
                                                ? 'red'
                                                : '',
                                    }"
                                    v-text="
                                        $lodash.get(scope.row, 'reportNo', '')
                                    "
                                ></span>
                            </template>

                            <template v-else>
                                <span
                                    v-if="item.fieldCode === 'reportConclusion'"
                                >
                                    <span v-if="scope.row.trfStatus === 5">
                                        {{
                                            $lodash.get(
                                                scope.row,
                                                item.fieldCode,
                                            )
                                        }}
                                    </span>
                                </span>
                                <span v-else>
                                    {{ $lodash.get(scope.row, item.fieldCode) }}
                                </span>
                            </template>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <chatDialog
            ref="chatDialog"
            @hideChatDialog="hideChatDialog"
        ></chatDialog>
        <el-dialog
            title="eFiling"
            :visible.sync="eFillingDialogVisible"
            class="efiling-dialog"
            width="55%"
            append-to-body
            v-if="eFillingDialogVisible"
        >
            <eFillingForm
                ref="eFilling"
                :formModel="formModel"
                :productInfoList="productInfoList"
            ></eFillingForm>
            <template slot="footer">
                <el-button type="primary" @click="handleSave(1)">
                    {{ $t("crud.saveBtn") }}
                </el-button>
                <el-button type="success" @click="handleSave(2)">
                    {{ $t("work.cpscTrfInfo.column.submit") }}
                </el-button>
                <el-button @click="eFillingDialogVisible = false">
                    {{ $t("crud.cancelBtn") }}
                </el-button>
            </template>
        </el-dialog>
    </basic-container>
</template>

<script>
import {
    getList,
    downLoadFile,
    exportTrfs,
    exportTrfs_new,
    queryTrfDetail,
    exportTrfReportFile,
    batchDownloadFile,
    submitRelation,
} from "@/api/trf/trf"
import TabSelect from "./../../components/dff/TabSelect"
import moment from "moment"
import { mapGetters } from "vuex"
import XLSX from "xlsx"
import FileSaver from "file-saver"
import { objectIsNull, validatenull } from "@/util/validate"
import { deepClone } from "@/util/util"

//import JSZip from 'jszip'

import { getReports, getAflReports } from "@/api/trf/communication"
import { getServiceType } from "@/api/common"
import { tzFormChina, tzToChina } from "@/util/datetimeUtils"
import { dateFormatYmd } from "@/util/date"
import {
    add,
    doMapping,
    listForSelect,
    tradeSelect,
} from "@/api/cpscTrfInfo/cpscTrfInfo"
export default {
    name: "trfList",
    components: {
        TRFStatus: (resolve) =>
            require(["../../components/trf/TRFStatus"], resolve),
        AFLTRFStatus: (resolve) =>
            require(["../../components/trf/AFLTRFStatus"], resolve),
        TabSelect,
        chatDialog: (resolve) => require(["./form/chatDialog"], resolve),
        eFillingForm: (resolve) =>
            require(["./form/eFillingForm.vue"], resolve),
    },
    created() {
        console.log("进入list页面")
        this.debouncedQuery = this.$lodash.debounce(this.onSubmit, 500)
        console.log(this.userInfo.userMgtId)
        if (this.$route.query.from == 0) {
            this.query.order.properties.trfStatus = parseInt(
                this.$route.query.trfStatus,
            )
            this.query.order.properties.startDate = this.stringToDate(
                this.$route.query.startDate,
            )
            this.query.order.properties.endDate = this.stringToDate(
                this.$route.query.endDate,
            )
            //this.onLoadFromWel();
        } else if (this.$route.query.from == 2) {
            //首页搜索
            this.query.order.properties.queryValue =
                this.$route.query.queryValue
        } else if (this.$route.query.from == 1) {
            this.query.order.properties.startDate = this.stringToDate(
                this.$route.query.startDate,
            )
            this.query.order.properties.endDate = this.stringToDate(
                this.$route.query.endDate,
            )
            if (this.$route.query.reportResult == "all") {
                this.query.order.properties.actualConclusion = null
            } else {
                this.query.order.properties.actualConclusion =
                    this.$route.query.reportResult
            }
        }
        //如果没有时间传递 首次默认查询近30天的数据
        if (validatenull(this.query.order.properties.startDate)) {
            this.query.order.properties.startDate = moment()
                .subtract("days", 30)
                .toDate()
        }
        if (validatenull(this.query.order.properties.endDate)) {
            this.query.order.properties.endDate = new Date()
        }
        this.initDff()
    },

    data() {
        const BC = new BroadcastChannel("notice")
        BC.onmessage = (e) => {
            console.log("接收到刷新TRF List消息，执行刷新操作")
            this.onSubmit()
        }
        return {
            productInfoList: [{}],
            eFillingDialogVisible: false,

            formModel: {
                trfStatus: 0,
                attachments: [],
            },
            batchDownloadHref: "",
            minScreen: false,
            currentSortType: 3,
            currentDate: -1,
            loadLoading: {},
            isShowTrfTable: true,
            submitDateSortInfo: "",
            buyerConclusionSortInfo: "",
            reportIssuedTimeInfo: "",
            trfNoInfo: "info",
            submitDateSort: "desc",
            buyerReviewConclusionSort: "desc",
            reportAppoveDateSort: "desc",
            trfNoSort: "desc",
            sortRedio: "3",
            reportsLoading: false,
            isCheckTime: true,
            tableFlag: true,
            downLoading: false,
            allPageLoading: false,
            excelData: [],
            expands: [],
            loading: false,
            trfData: [],
            selectedVal: [],
            dffTemplateData: [],
            checkedTimeOutTrf: false,
            getRowKeys(row) {
                return row.id
            },

            /*  queryForm: {
                      trfNo: '',
                      startDate: '',
                      endDate: '',
                      isSubmissionTimeout: null,
                      trfStatus: '',
                      columns: [],
                  },*/
            query: {
                isShowReviewConclusion: false,
                pageNo: 1,
                pageSize: 10,
                total: 0,
                order: {
                    properties: {
                        productLineCode: "",
                        queryValue: "",
                        templateName: "",
                        buyerCustomerGroupName: "",
                        applyCustomerName: "",
                        trfStatus: null,
                        startDate: null,
                        endDate: null,
                        trfNo: "",
                        extendOrderNo: "",
                        reportNo: "",
                        productDescription: "",
                        styleNo: "",
                        itemNo: "",
                        poNo: "",
                        refCode5: "",
                        productColor: "",
                        refCode3: "",
                        previousReportNo: "",
                        fiberComposition: "",
                        collection: "",
                        season: "",
                        buyerOrgannization1: "",
                        specialProductAttribute10: "",
                        productionStage: "",
                        firstTimeApplicationFlag: "",
                        buyerSourcingOffice: "",
                        productCategory1: "",
                        pendingReason: "",
                        reportConclusion: "",
                        buyerReviewConclusion: "",
                        trfSubmitDate: null,
                        isSubmissionTimeout: null,
                        sampleReceiveDateStr: null,
                        dueDateStr: null,
                        reportApprovedDateStr: null,
                        labName: "",
                        applyContact: "",
                        language: "",
                        sortColumn: "", //排序字段
                        sort: "desc", //排序方式
                        serviceTypeName: "",
                    },
                },
            },
            data: [],
            reports: [],
            trfParamForm: {
                startDate: "",
                endDate: "",
            },
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            pickerOptions1: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7
                },
            },
            chatItemIndex: "",
        }
    },
    computed: {
        ...mapGetters([
            "userInfo",
            "permission",
            "language",
            "menu",
            "dimensions",
        ]),
        permissionList() {
            return {
                trfAddBtn: this.vaildData(
                    this.permission["sgs:trfList:addTrf"],
                    false,
                ),
                trfDownLoadBtn: this.vaildData(
                    this.permission["sgs:trfList:downLoadTrf"],
                    false,
                ),
                trfPrintBtn: this.vaildData(
                    this.permission["sgs:trfList:printBtn"],
                    false,
                ),
                trfEditBtn: this.vaildData(
                    this.permission["sgs:trfList:editTrf"],
                    false,
                ),
                copyTrfBtn: this.vaildData(
                    this.permission["sgs:trfList:copyTrf"],
                    false,
                ),
            }
        },
        role() {
            return {
                isSGS:
                    this.haseRole("SGSUserRole", "SgsAdmin") ||
                    this.haseRole("SGSUserRole", "SgsLabUser"),
                isBuyer: this.haseRole("UserRole", "Buyer"),
            }
        },
        //监听接收到的消息
        socketTask() {
            return this.$store.getters.socketTask
        },
    },
    watch: {
        "$store.state.user.taskType"(newVal) {
            console.log("newVal", newVal)
            if (newVal == "trfList" && this.trfData.length <= 0) {
                this.trfData.push({
                    trfNo: "TRF2F00001",
                    trfStatus: 2,
                    buyerCustomerGroupName: "General",
                    templateName: "SL Test template",
                })
            } else {
                this.initOrderList()
            }
        },
        socketTask() {
            if (this.socketTask) {
                if (this.chatItemIndex && this.trfData.length > 0) {
                    this.trfData[this.chatItemIndex].imHasUnread = false
                }
                this.$refs.chatDialog.show()
            }
        },
        //监听语言变化
        language: function (newVal) {
            this.openLoading()
            //this.initOrderList();
            this.isShowTrfTable = false
            this.initDff()
        },
        /*'query.order.properties': {
                deep: true,
                handler(val, old) {
                    /!*if ((val.startDate && !val.endDate ) || (val.endDate && !val.startDate)) {
                        this.$info({message: 'Please select the correct start date and end date'})
                        return false
                    }*!/
                    this.debouncedQuery()
                }
            },*/
        //监听语言变化
        /* language: function (newVal) {
                 //重新获取一遍selectVal
                 this.initDff();
                 console.log(this.$refs.tabSelect);
                 console.log(this.selectedVal);
                this.debouncedQuery()
              },*/
    },
    mounted() {
        this.$nextTick(() => {
            let menuFlag = this.validateTrfMenu()
            let sgsRole = this.role.isSGS
            console.log("是否sgs", menuFlag, sgsRole)

            if (this.language == "en-US") this.minScreen = true
            this.minScreen = document.body.offsetWidth <= 1600 ? true : false

            window.addEventListener("resize", () => {
                this.minScreen =
                    document.body.offsetWidth <= 1600 ? true : false
                console.log(this.minScreen, document.body.offsetWidth)
            })

            // 用户首次进入相关页面显示功能引导
            let that = this
            // let timer = setInterval(function () {
            //     if (document.readyState === 'complete') {
            //         window.clearInterval(timer)
            //         let isFinish = JSON.parse(localStorage.getItem('finish_trfList'))
            //         if(isFinish == undefined) localStorage.setItem('finish_trfList', false)
            //         if(!isFinish && menuFlag && !sgsRole) {
            //             that.$store.commit('SET_TASK_TYPE', 'trfList')
            //         }
            //     }
            // }, 300)

            let timer = setInterval(() => {
                if (document.readyState === "complete") {
                    window.clearInterval(timer)
                    let list = JSON.parse(localStorage.getItem("loginUserList"))
                    let user = list.find(
                        (item) => item.userMgtId == this.userInfo.userMgtId,
                    )
                    let finish = user.fnList[1]["finish_trfList"]

                    if (this.role.isBuyer) {
                        // buyer直接打开功能引导
                        if (!finish)
                            this.$store.commit("SET_TASK_TYPE", "trfList")
                    } else {
                        let autoClose = JSON.parse(
                            localStorage.getItem("AUTO_OFF"),
                        )
                        if (
                            (!this.userInfo.guide || autoClose) &&
                            !finish &&
                            menuFlag &&
                            !sgsRole
                        ) {
                            this.$store.commit("SET_TASK_TYPE", "trfList")
                        }
                    }
                }
            }, 300)
        })
    },
    filters: {
        formtterDate: function (value) {
            if (!value) return ""
            value = value.toString()
            return moment(value).format("YYYY-MM-DD")
        },
        //转换为当前时区
        /*currentTz :function(value,format){
              if (!value) return ''
              if(!format){
                format='YYYY-MM-DD';
              }
              value = tzFormChina(value,format);
              value = value.toString();
              return value;
            }*/
        //转换为当前时区
        /* currentTz :function(value){
            if (!value) return ''
            value = tzFormChina(value);
            value = value.toString();
            return value;
          }*/
    },
    methods: {
        getNewTrfNo() {
            const now = new Date()
            const year = now.getFullYear()
            const month = (now.getMonth() + 1).toString().padStart(2, "0") // 月份是从0开始的，所以+1
            const day = now.getDate().toString().padStart(2, "0")
            const hours = now.getHours().toString().padStart(2, "0")
            const minutes = now.getMinutes().toString().padStart(2, "0")
            const seconds = now.getSeconds().toString().padStart(2, "0")
            const endStr = Math.floor(Math.random() * 900) + 100

            // 连接成纯数字字符串
            const formattedTime = `${year}${month}${day}${hours}${minutes}${seconds}${endStr}`
            //const formattedTime = `${minutes}${seconds}`;
            return "eFiling" + formattedTime
        },
        async handleSave(trfStatus) {
            const fun = (trfStatus) => {
                let productInfoList = JSON.parse(
                    JSON.stringify(this.$refs.eFilling.productInfoList),
                )

                const { flag, msg } = this.checkProductInfo(productInfoList)
                let efilingFormData = ""
                if (flag) {
                    let formModel = this.$refs.eFilling.newFormModel
                    // if(!formModel.referenceNo){
                    //     formModel.referenceNo= this.getNewTrfNo()
                    // }

                    efilingFormData = JSON.stringify({
                        formModel,
                        productInfoList,
                    })
                    //  trfObj.efilingList=efilingList
                } else {
                    return
                }
                productInfoList.forEach((productInfo) => {
                    let form = JSON.parse(
                        JSON.stringify({
                            ...this.$refs.eFilling.newFormModel,
                            ...productInfo,
                        }),
                    )
                    form.dataSource = 2
                    form.trfInfoStastus = trfStatus

                    //form.referenceNo = this.getNewTrfNo()
                    //获取 primay key
                    if (form.formList && form.formList.length) {
                        const primary = form.formList.find(
                            (item) => item.primaryId === "yes",
                        )

                        form.productId = primary.productId
                        form.productIdType = primary.productIdType
                    }
                    if (form.productModelName) {
                        form.productName = form.productModelName
                    }

                    form.productJson = JSON.stringify(productInfo)

                    // 点击save 和 submit时 新增数据，所以需要将 form中的id属性设置为null
                    add(form).then(
                        (res) => {
                            this.$message({
                                type: "success",
                                message: "Success!",
                            })
                        },
                        (error) => {
                            window.console.log(error)
                        },
                    )
                })
                submitRelation({
                    trfId: this.$refs.eFilling.newFormModel.trfId,
                    efilingFormData,
                }).then(async (res) => {
                    this.eFillingDialogVisible = false
                    await this.initOrderList()
                })
            }

            if (trfStatus == 1) {
                fun(trfStatus)
            } else {
                let formValidResult = await this.$refs.eFilling.formValidate()
                console.log("动态表单的check--eFilling:", formValidResult)
                if (formValidResult) {
                    fun(trfStatus)
                }
            }
        },
        viewEfilling(row) {
            this.$router.push("/cpscTrfInfo/cpscTrfInfo?trfNo=" + row.trfNo)
        },

        checkProductInfo(productInfoList) {
            for (let productInfo of productInfoList) {
                if (
                    !productInfo ||
                    !productInfo.formList.length ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    )
                ) {
                    this.$message.error(
                        this.$t("work.cpscCustomerTrade.tip.primary"),
                    )
                    return {
                        flag: false,
                        msg: this.$t("work.cpscCustomerTrade.tip.primary"),
                    }
                }
                if (
                    !productInfo ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    ).productIdType ||
                    !productInfo.formList.find(
                        (item) => item.primaryId === "yes",
                    ).productId
                ) {
                    this.$message.error(
                        this.$t("work.cpscCustomerTrade.tip.productInfo"),
                    )
                    return {
                        flag: false,
                        msg: this.$t("work.cpscCustomerTrade.tip.productInfo"),
                    }
                }
                const { hasDuplicateIdType, hasDuplicateId } =
                    this.checkDuplicateProductInfo(productInfo.formList)
                if (hasDuplicateIdType)
                    return { flag: false, msg: "ProductIdType Repeat" }
                if (hasDuplicateId)
                    return { flag: false, msg: "ProductId Repeat" }
            }
            return { flag: true }
        },
        checkDuplicateProductInfo(data) {
            // 创建两个对象来分别存储 productIdType 和 productId 的计数
            const productIdTypeCount = {}
            const productIdCount = {}

            // 遍历数组中的每个实体
            for (const item of data) {
                const { productIdType, productId } = item

                // 检查 productIdType 是否重复
                productIdTypeCount[productIdType] =
                    (productIdTypeCount[productIdType] || 0) + 1
                if (productIdTypeCount[productIdType] > 1) {
                    return { hasDuplicateIdType: true, hasDuplicateId: false }
                }

                // 检查 productId 是否重复
                productIdCount[productId] = (productIdCount[productId] || 0) + 1
                if (productIdCount[productId] > 1) {
                    return { hasDuplicateIdType: false, hasDuplicateId: true }
                }
            }

            // 没有发现重复
            return { hasDuplicateIdType: false, hasDuplicateId: false }
        },
        async handleEfilling(row) {
            this.productInfoList = [
                {
                    referenceNo: this.getNewTrfNo(),
                },
            ]
            this.formModel = {
                trfStatus: 0,
                attachments: [],
            }
            this.eFillingDialogVisible = true
            const { data } = await queryTrfDetail({
                trfNo: row.trfNo,
                trfId: row.id,
                signature: row.signature,
            })
            let buyerName =
                data.data.trfCustomer.buyerCustomerNameEn ||
                data.data.trfCustomer.buyerCustomerGroupName
            const res = await listForSelect(buyerName)
            this.$refs.eFilling.getBuyerCustomerId(buyerName)
            const buyerCustomerList = res.data.data
            const buyer = buyerCustomerList.find(
                (item) => item.bossName == buyerName,
            )
            console.log(buyer)
            let type = this.language == "en-US" ? "EN" : "CN" //EN
            const formData = JSON.parse(data.data.dffFormData)[type]
            if (!formData && !formData.factoryName) {
                let param = {}
                param.trfId = row.trf_trfId
                param.buyerCustomerId = buyer ? buyer.id : undefined
                param.basicLabAddress = data.data.trfLab.labAddress
                param.basicServiceType = data.data.serviceType
                param.basicLabId = data.data.trfLab.labCode
                param.trfReferenceNo = row.trf_trfNo
                this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =
                    data.data.trfAttachments
                this.$refs.eFilling.newFormModel = Object.assign(
                    this.formModel,
                    param,
                )
                const mapping = await doMapping({
                    productLineCode: data.data.productLineCode,
                    productCategory: "",
                    customerCode: data.data.trfCustomer.buyerCustomerGroupCode,
                    formData: JSON.stringify(formData),
                    formList: JSON.stringify(
                        JSON.parse(data.data.dffGridData)[type],
                    ),
                })
                this.productInfoList = this.productInfoList.map((item) => {
                    return { ...item, ...mapping.data.data }
                })
            } else {
                const resTrade = await tradeSelect(
                    "Manufacture",
                    buyer ? buyer.customerId : "",
                    formData.factoryName,
                )
                const manufactureCustomerIds = resTrade.data.data
                const manufacture = manufactureCustomerIds.find(
                    (item) => item.tradeName == formData.factoryName,
                )
                const mapping = await doMapping({
                    productLineCode: data.data.productLineCode,
                    productCategory: "",
                    customerCode: data.data.trfCustomer.buyerCustomerGroupCode,
                    formData: JSON.stringify(formData),
                    formList: JSON.stringify(
                        JSON.parse(data.data.dffGridData)[type],
                    ),
                })
                let param = {}
                param.buyerCustomerId = buyer ? buyer.id : undefined
                param.trfId = row.id
                param.basicLabAddress = data.data.trfLab.labAddress
                // param.basicLabContactName=data.data.trfLabContact.contactName
                param.basicServiceType = data.data.serviceType
                param.basicLabId = data.data.trfLab.labCode
                param.trfReferenceNo = row.trfNo
                console.log(param)
                this.$refs.eFilling.$refs.trfFile.trfAttachmentsNew =
                    data.data.trfAttachments
                this.$refs.eFilling.newFormModel = Object.assign(
                    this.formModel,
                    param,
                )

                manufacture &&
                    (await this.$refs.eFilling.setPOC(
                        "Manufacture",
                        buyer ? buyer.customerId : "",
                        formData.factoryName,
                    ))
                manufacture &&
                    this.$refs.eFilling.handleTradeChange(
                        manufacture.id,
                        manufacture,
                    )
                let manufactureSub = {}
                if (manufacture) {
                    manufactureSub.manufactureContactId =
                        manufacture.cpscCustomerTradeContactVO.id
                    manufactureSub.manufactureContactName =
                        manufacture.cpscCustomerTradeContactVO.contactName
                    manufactureSub.manufactureContactTelephone =
                        manufacture.cpscCustomerTradeContactVO.telphone
                    manufactureSub.manufactureCustomerAddress =
                        manufacture.tradeAddress
                    manufactureSub.manufactureCustomerAddressLocal =
                        manufacture.tradeAddress2
                    manufactureSub.manufactureCustomerCityId =
                        manufacture.tradeCity
                    manufactureSub.manufactureCustomerCityName =
                        manufacture.tradeCity
                    manufactureSub.manufactureCustomerCountryId =
                        manufacture.tradeCountryId
                    manufactureSub.manufactureCustomerCountryName =
                        manufacture.tradeCountryName
                    manufactureSub.manufactureCustomerId = manufacture.id
                    manufactureSub.manufactureCustomerName =
                        manufacture.tradeName
                    manufactureSub.manufactureCustomerNameLocal =
                        manufacture.tradeName
                    manufactureSub.manufactureContactEmaill =
                        manufacture.cpscCustomerTradeContactVitem
                }
                this.productInfoList = this.productInfoList.map((item) => {
                    return { ...item, ...mapping.data.data, ...manufactureSub }
                })
                buyer && this.$refs.eFilling.handleBuyerChange(buyer.id)
            }
        },
        currentTz_YMD(val) {
            if (!val) return ""
            let value = tzFormChina(val, "YYYY-MM-DD HH:mm:ss")
            return moment(value).format("YYYY-MM-DD")
        },
        currentTz(value, format) {
            if (!value) return ""
            if (!format) {
                format = "YYYY-MM-DD HH:mm:ss"
            }
            value = tzFormChina(value, format)
            return value
        },
        timestampToYYYYMMDD(timestamp) {
            const date = new Date(timestamp * 1000) // 将时间戳转换为Date对象
            const year = date.getFullYear()
            const month = ("0" + (date.getMonth() + 1)).slice(-2) // 月份需要加1，且保证两位数显示
            const day = ("0" + date.getDate()).slice(-2) // 保证日期显示两位数
            return year + month + day
        },
        tzToChina(val) {
            if (!val) return ""
            let value = tzToChina(val)
            return value
        },
        //判断是否存在创建TRF菜单
        validateTrfMenu() {
            let result = false
            if (!validatenull(this.menu)) {
                let menuStr = JSON.stringify(this.menu)
                if (!validatenull(menuStr)) {
                    if (menuStr.indexOf("/ccl/trf/newTrf") != -1) {
                        result = true
                    }
                }
            }
            return result
        },
        haseRole(type, role) {
            if (validatenull(type) || validatenull(role)) {
                return false
            }
            if (validatenull(this.dimensions)) {
                return false
            } else {
                if (this.dimensions.hasOwnProperty(type)) {
                    if (this.dimensions[type].indexOf(role) >= 0) {
                        return true
                    } else {
                        return false
                    }
                } else {
                    return false
                }
            }
        },
        openLoading() {
            this.loadLoading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            })
        },
        closeLoading() {
            this.loadLoading.close ? this.loadLoading.close() : undefined
        },
        async sortChange(val) {
            console.log(val)
            if (val == "1" || val == 1) {
                this.query.order.properties.sortColumn =
                    " t.trf_submission_date "
            } else if (val == "2" || val == 2) {
                this.query.order.properties.sortColumn =
                    " t.buyer_review_conclusion "
            } else {
                this.query.order.properties.sortColumn = ""
            }
            await this.initOrderList()
        },
        async cancelSort() {
            this.query.order.properties.sortColumn = ""
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async submitDateSortClick(index) {
            // this.submitDateSortInfo = 'info';
            // this.buyerConclusionSortInfo = '';
            // this.reportIssuedTimeInfo = '';
            // this.trfNoInfo = '';
            this.currentSortType = index
            if (this.submitDateSort == "desc") {
                this.submitDateSort = "asc"
            } else {
                this.submitDateSort = "desc"
            }
            this.buyerReviewConclusionSort = "desc"
            this.reportAppoveDateSort = "desc"
            this.trfNoSort = "desc"
            this.query.order.properties.sortColumn = " t.trf_submission_date "
            this.query.order.properties.sort = this.submitDateSort
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async buyerConclusionSortClick(index) {
            this.currentSortType = index
            // this.submitDateSortInfo = '';
            // this.buyerConclusionSortInfo = 'info';
            // this.reportIssuedTimeInfo = '';
            // this.trfNoInfo = '';
            if (this.buyerReviewConclusionSort == "desc") {
                this.buyerReviewConclusionSort = "asc"
            } else {
                this.buyerReviewConclusionSort = "desc"
            }
            this.submitDateSort = "desc"
            this.reportAppoveDateSort = "desc"
            this.trfNoSort = "desc"
            this.query.order.properties.sortColumn =
                " t.buyer_review_conclusion "
            this.query.order.properties.sort = this.buyerReviewConclusionSort
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async reportDateSortClick(index) {
            // this.submitDateSortInfo = '';
            // this.buyerConclusionSortInfo = '';
            // this.reportIssuedTimeInfo = 'info';
            // this.trfNoInfo = '';
            this.currentSortType = index
            if (this.reportAppoveDateSort == "desc") {
                this.reportAppoveDateSort = "asc"
            } else {
                this.reportAppoveDateSort = "desc"
            }
            this.submitDateSort = "desc"
            this.trfNoSort = "desc"
            this.buyerReviewConclusionSort = "desc"
            this.query.order.properties.sortColumn = " t.approved_date "
            this.query.order.properties.sort = this.reportAppoveDateSort
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async trfNoSortClick(index) {
            //  this.submitDateSortInfo = '';
            // this.buyerConclusionSortInfo = '';
            // this.reportIssuedTimeInfo = '';
            // this.trfNoInfo = 'info';
            this.currentSortType = index
            if (this.trfNoSort == "desc") {
                this.trfNoSort = "asc"
            } else {
                this.trfNoSort = "desc"
            }
            this.submitDateSort = "desc"
            this.reportAppoveDateSort = "desc"
            this.buyerReviewConclusionSort = "desc"
            this.query.order.properties.sortColumn = ""
            this.query.order.properties.sort = this.trfNoSort
            this.query.pageNo = 1
            await this.initOrderList()
        },

        //排序事件
        // async sortChange(val) {
        //     this.query.pageNo = 1;
        //     await this.initOrderList();
        // },
        // 打开聊天弹框
        openChat(item, index) {
            let trfNo = item.trfNo
            this.chatItemIndex = index
            console.log(this.chatItemIndex)
            let userInfo = window.localStorage.getItem("SGS-userInfo")
            userInfo = userInfo ? JSON.parse(userInfo) : ""
            if (userInfo) {
                let userId = userInfo.content.userMgtId
                this.$store.dispatch("webSocketInit", {
                    trfNo: trfNo,
                    userId: userId,
                })
            }
        },
        // 监听沟通弹框隐藏
        async hideChatDialog() {
            this.chatItemIndex = ""
            await this.initOrderList()
        },
        trfDetailClick(row) {
            let hash = new Date().getTime() + ""
            console.log(row)
            if (row.trfType == 30) {
                window.open(
                    "/#/afl/trf/trfDetail?id=" +
                        row.id +
                        "&title=" +
                        row.trfNo +
                        "&trfNo=" +
                        row.trfNo +
                        "&hash=" +
                        hash +
                        "&actionType=detail" +
                        "&signature=" +
                        row.signature,
                    "_blank",
                )
            } else {
                window.open(
                    "/#/trf/trfDetail?id=" +
                        row.id +
                        "&title=" +
                        row.trfNo +
                        "&trfNo=" +
                        row.trfNo +
                        "&hash=" +
                        hash +
                        "&actionType=detail" +
                        "&signature=" +
                        row.signature,
                    "_blank",
                )
            }
        },
        trfReportChange(row, expandedRows) {
            this.$refs.trfTable.setCurrentRow()
            this.currentRow = row
            if (this.expands.join(",").indexOf(row.id) === -1) {
                this.expands = [this.currentRow.id]
                this.searchTrfReportList(row.id, row.trfType)
            } else {
                this.expands.splice(0, this.expands.length)
            }
        },
        async searchTrfReportList(trfId, trfType) {
            if (trfType == 30) {
                this.reportsLoading = true
                let res = await getAflReports(trfId)
                this.reports = res.data.data
                this.reportsLoading = false
            } else {
                this.reportsLoading = true
                let res = await getReports(trfId)
                this.reports = res.data.data
                this.reportsLoading = false
            }
        },
        async handelSubmit() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async trfStatusChange() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async reportConclusionChange() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async currentChange(pageCurrent) {
            this.query.pageNo = pageCurrent
            await this.initOrderList()
        },
        getRowClassName({ row, rowIndex }) {
            if (row.trfStatus != 5) {
                return "row-expand-cover"
            }
        },
        downloadReport(cloudId) {
            if (cloudId != "" && cloudId != null) {
                downLoadFile(cloudId).then(
                    (res) => {
                        var pdfUrl = res.data.data
                        this.downLoadReportOts(pdfUrl)
                        /*this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });*/
                    },
                    (error) => {
                        this.$message.error(this.$t("api.error"))
                    },
                )
            }
            //window.open(pdfUrl, '_blank')
        },
        downLoadReportOts(pdfUrl) {
            window.open(pdfUrl, "_blank")
        },
        getReportColor(reportConclusion) {
            if (reportConclusion === "Pass") {
                return "green"
            } else if (reportConclusion === "Fail") {
                return "red"
            } else {
                return null
            }
        },
        copyTRF(row) {
            this.actionTRF(row, "Copy TRF", "copy")
        },

        editTrf(row) {
            this.actionTRF(row, row.trfNo, "edit")
        },
        actionTRF(row, title, action) {
            let url =
                row.trfType == 30 ? "/afl/trf/trfDetail" : "/trf/trfDetail"
            let flag = row.trfType == 30 ? 3 : 1
            let params = {
                id: row.id,
                title: title,
                trfNo: row.trfNo,
                signature: row.signature,
                actionType: action,
                flag: flag,
            }
            this.$router.push({
                path: url,
                query: params,
            })
        },

        retestFormtter(row, column) {
            var reTest = row[column.property]
            var reTestStr = this.$t("common.yes")
            if (reTest == 1) {
                reTestStr = this.$t("common.no")
            }
            return reTestStr
        },
        trfStatusFormatter(row, column) {
            var trfStatus = row[column.property]
            var statusName = "Drafting"
            switch (trfStatus) {
                case 2:
                    statusName = "Submitted"
                    break
                case 3:
                    statusName = "Application Accepted"
                    break
                case 4:
                    statusName = "Testing"
                    break
                case 5:
                    statusName = "Report Issued"
                    break
                case 6:
                    statusName = "Cancel"
                    break
                case 11:
                    statusName = "Pending"
                    break
                case 12:
                    statusName = "Pending"
                    break
                default:
                    statusName = "Drafting"
                    break
            }

            return statusName
        },
        async clear() {
            this.query.order.properties.queryValue = ""
            this.query.order.properties.buyerCustomerGroupName = ""
            this.query.order.properties.trfStatus = null
            this.query.order.properties.startDate = ""
            this.query.order.properties.endDate = ""
            this.query.order.properties.trfNo = ""
            this.query.order.properties.reportNo = ""
            this.query.order.properties.templateName = ""
            this.query.order.properties.pendingReason = ""
            this.query.order.properties.productDescription = ""
            this.query.order.properties.styleNo = ""
            this.query.order.properties.itemNo = ""
            this.query.order.properties.poNo = ""
            this.query.order.properties.refCode5 = ""

            this.query.order.properties.productColor = ""
            this.query.order.properties.refCode3 = ""
            this.query.order.properties.previousReportNo = ""
            this.query.order.properties.fiberComposition = ""
            this.query.order.properties.collection = ""
            this.query.order.properties.season = ""
            this.query.order.properties.buyerOrgannization1 = ""
            this.query.order.properties.specialProductAttribute10 = ""
            this.query.order.properties.productionStage = ""
            this.query.order.properties.firstTimeApplicationFlag = ""
            this.query.order.properties.buyerSourcingOffice = ""
            this.query.order.properties.productCategory1 = ""

            this.query.order.properties.reportConclusion = ""
            this.query.order.properties.trfSubmitDate = null
            this.query.order.properties.sampleReceiveDateStr = null
            this.query.order.properties.dueDateStr = null
            this.query.order.properties.reportApprovedDateStr = null
            this.query.order.properties.labName = ""
            this.query.order.properties.applyContact = ""
            this.query.pageNo = 1
            this.currentDate = -1
            await this.initOrderList()
        },
        async changeSubmissionDateDateQuery(val) {
            debugger
            /*console.log("TRF提交时间===",val)
                let trfSubmissionDate = null
                if (val) {
                    trfSubmissionDate = moment(val).format('YYYY-MM-DD')
                }*/
            this.query.order.properties.trfSubmitDate = val
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async changeReportDateQuery(val) {
            /*  let reportDate = null;
                if (val) {
                    reportDate = moment(val).format('YYYY-MM-DD')
                }*/
            this.query.order.properties.reportApprovedDateStr = val
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async sampleReceiveDateQuery(val) {
            /* let sampleReceiveDate = null
                if (val) {
                    sampleReceiveDate = moment(val).format('YYYY-MM-DD')
                }*/
            this.query.order.properties.sampleReceiveDateStr = val
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async dueDateQuery(val) {
            this.query.order.properties.dueDateStr = val
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async updateSelectedHandle(val) {
            console.log("获取的配置表头：", val)
            this.query.pageNo = 1
            this.selectedVal = val
            this.isShowTrfTable = true
            await this.initOrderList()
            this.closeLoading()
        },
        async checkTimeOutHandle(val) {
            this.$set(this, "checkedTimeOutTrf", val)
            this.query.order.properties.isSubmissionTimeout = val ? 1 : 0
            await this.initOrderList()
        },
        async exportExcel() {
            let columns = this.selectedVal.map((item) => item.fieldCode)
            columns.push("trfStatus", "id")
            columns = this.$lodash.uniq(columns)
            this.query.order.propertiescolumns = columns
            var params = {}
            this.downLoading = true
            debugger
            let queryParam = deepClone(this.query)

            //转换时区
            debugger
            if (!objectIsNull(queryParam.order.properties.startDate)) {
                queryParam.order.properties.startDate = this.tzToChina(
                    moment(queryParam.order.properties.startDate).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }
            if (!objectIsNull(queryParam.order.properties.endDate)) {
                queryParam.order.properties.endDate = this.tzToChina(
                    moment(queryParam.order.properties.endDate).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }
            if (!objectIsNull(queryParam.order.properties.trfSubmitDate)) {
                queryParam.order.properties.trfSubmitDate = this.tzToChina(
                    moment(queryParam.order.properties.trfSubmitDate).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }

            if (
                !objectIsNull(queryParam.order.properties.sampleReceiveDateStr)
            ) {
                queryParam.order.properties.sampleReceiveDateStr =
                    this.tzToChina(
                        moment(
                            queryParam.order.properties.sampleReceiveDateStr,
                        ).format("YYYY-MM-DD HH:mm:ss"),
                    )
            }
            if (!objectIsNull(queryParam.order.properties.dueDateStr)) {
                queryParam.order.properties.dueDateStr = this.tzToChina(
                    moment(queryParam.order.properties.dueDateStr).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }
            if (
                !objectIsNull(queryParam.order.properties.reportApprovedDateStr)
            ) {
                queryParam.order.properties.reportApprovedDateStr =
                    this.tzToChina(
                        moment(
                            queryParam.order.properties.reportApprovedDateStr,
                        ).format("YYYY-MM-DD HH:mm:ss"),
                    )
            }
            queryParam.order.properties.productLineCode =
                this.userInfo.productLineCode
            let res = await exportTrfs_new(
                Object.assign({
                    columns: columns,
                    trfQuery: queryParam.order.properties,
                    pageNo: -1,
                    pageSize: 3000,
                }),
            )
            this.downLoading = false
            const data = res.data.data
            //this.page.total = data.total;
            this.excelData = data.records
            this.$nextTick(function () {
                var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
                var wb = XLSX.utils.table_to_book(
                    document.querySelector("#exportTable"),
                    xlsxParam,
                )
                var wbout = XLSX.write(wb, {
                    bookType: "xlsx",
                    bookSST: true,
                    type: "array",
                })
                try {
                    FileSaver.saveAs(
                        new Blob([wbout], { type: "application/octet-stream" }),
                        "TestRequestList.xlsx",
                    )
                } catch (e) {
                    if (typeof console !== "undefined") {
                        console.log(e, wbout)
                    }
                }
                return wbout
            })
        },
        stringToDate(dateString) {
            var parts = dateString.split("-")
            var year = parseInt(parts[0], 10)
            var month = parseInt(parts[1], 10) - 1 // 月份是从0开始的
            var day = parseInt(parts[2], 10)

            return new Date(year, month, day)
        },
        downloadZip(reportListParam, data) {
            // 转换数据为CSV格式
            //下载批量文件
            const csvContent =
                "\ufeff" + data.map((row) => row.join(",")).join("\n")
            // 创建Blob对象
            const blob = new Blob([csvContent], {
                type: "text/csv;charset=utf-8;",
            })
            FileSaver.saveAs(blob, "Report list.csv")
            this.downLoading = false
            let { systemId, cloudIDs } = reportListParam
            let cs = []
            cloudIDs.forEach((c) => {
                c = encodeURIComponent(c)
                cs.push(`cloudIDs=${c}`)
            })
            let cloudIdsParam = cs.join("&")
            let param = `fileName=Report List.zip&systemId=${systemId}&${cloudIdsParam}`
            let downloadUrl =
                "/api/sgsapi/FrameWorkApi/file/batchDownloadByCloudIDForGet?"
            downloadUrl = `${downloadUrl}${param}`
            this.batchDownloadHref = downloadUrl
            setTimeout(() => {
                document.getElementById("batchDownloadA").click()
            }, 500)
            this.allPageLoading = false
        },
        exportReportExcelClick() {
            this.batchDownloadHref = ""
            if (this.query.total <= 0) {
                this.$notify.error(this.$t("operation.exportReportTips"))
                return
            }
            this.handlerExportReportExcelClick()
        },
        handlerExportReportExcelClick() {
            let data = this.trfData
            let trfIds = data.map((da) => da.id)
            this.allPageLoading = true
            exportTrfReportFile({ trfIds })
                .then((res) => {
                    try {
                        let data = res.data.data
                        if ((data || []).length > 20) {
                            this.allPageLoading = false
                            this.$notify.warning(
                                this.$t("operation.exportReportMaxTips"),
                            )
                            return
                        }
                        //生成一份说明文件：
                        const csvData = [["TRF No", "File Name"]]
                        ;(data || []).forEach((da) => {
                            let { trfNo, fileName } = da
                            let obj = []
                            obj.push(trfNo)
                            obj.push(fileName)
                            csvData.push(obj)
                        })
                        //report文件
                        let cloudIDs = (data || []).map((c) => c.cloudId)
                        let reportListParam = {
                            systemId: 2,
                            cloudIDs,
                        }
                        this.downloadZip(reportListParam, csvData)
                    } catch (e) {
                        this.allPageLoading = false
                    }
                })
                .catch((err) => {
                    this.allPageLoading = false
                })
        },
        async exportExcelClick() {
            //判断当前导出条数是否>3000  >3000给出确认提示
            if (this.query.total > 3000) {
                this.$confirm(
                    this.$t("trf.confirmExportTrfList"),
                    this.$t("tip"),
                    {
                        confirmButtonText: this.$t("submitText"),
                        cancelButtonText: this.$t("cancelText"),
                        type: "warning",
                    },
                )
                    .then(() => {
                        this.exportExcel()
                    })
                    .catch(() => {
                        /* this.btnSubmitLoading=false;
                         this.$message.error(this.$t('api.error'));*/
                    })
            } else {
                this.exportExcel()
            }
        },
        async selectedDate(num, dateName, index) {
            this.currentDate = index
            let endDate = new Date()
            var startDate
            if (dateName != "w") {
                //月份计算
                startDate = moment(
                    new Date(new Date().setMonth(new Date().getMonth() - num)),
                ).toDate()
            } else {
                //一周
                startDate = moment().subtract(num, dateName).toDate()
            }
            this.query.order.properties.startDate = startDate
            this.query.order.properties.endDate = endDate
            await this.initOrderList()
        },
        async updateDate(val) {
            debugger
            console.log("DATE:::", val)
            let date = []
            if (val) {
                let testDate = (date[0] = moment(val[0]).toDate())
                date[1] = moment(val[1]).toDate()
            }
            this.query.order.properties.startDate = date[0]
            this.query.order.properties.endDate = date[1]
            await this.initOrderList()
        },
        async updateStartDate(val) {
            /*let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }*/
            this.query.order.properties.startDate = val
            await this.initOrderList()
        },
        async updateEndDate(val) {
            /* let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }*/
            this.query.order.properties.endDate = val
            await this.initOrderList()
        },

        checkTime() {
            var begintime = this.query.order.properties.startDate
            var endtime = this.query.order.properties.endDate
            if (validatenull(endtime)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.endDateValidate"),
                    type: "warning",
                })
                return false
            }

            var time1 = new Date(begintime).getTime()
            var time2 = new Date(endtime).getTime()
            if (validatenull(begintime)) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.startDateValidate"),
                    type: "warning",
                })
                return false
            }
            if (validatenull(endtime == "")) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.endDateValidate"),
                    type: "warning",
                })
                return false
            }
            if (time1 > time2) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.endDateErrorValidate"),
                    type: "warning",
                })
                return false
            }

            //判断时间跨度是否大于6个月  修改为12个月
            debugger
            // begintime = this.timestampToYYYYMMDD(begintime);
            // endtime = this.timestampToYYYYMMDD(endtime);
            // var arr1 = begintime.split('-');
            // var arr2 = endtime.split('-');
            // arr1[1] = parseInt(arr1[1]);
            // arr1[2] = parseInt(arr1[2]);
            // arr2[1] = parseInt(arr2[1]);
            // arr2[2] = parseInt(arr2[2]);
            var arr1 = []
            var arr2 = []
            arr1[0] = begintime.getFullYear()
            arr2[0] = endtime.getFullYear()
            arr1[1] = begintime.getMonth() + 1
            arr1[2] = begintime.getDate()
            arr2[1] = endtime.getMonth() + 1
            arr2[2] = endtime.getDate
            var flag = true
            if (arr1[0] == arr2[0]) {
                //同年
                if (arr2[1] - arr1[1] > 12) {
                    //月间隔超过6个月
                    flag = false
                } else if (arr2[1] - arr1[1] == 12) {
                    //月相隔3个月，比较日
                    if (arr2[2] > arr1[2]) {
                        //结束日期的日大于开始日期的日
                        flag = false
                    }
                }
            } else {
                //不同年
                if (arr2[0] - arr1[0] > 1) {
                    flag = false
                } else if (arr2[0] - arr1[0] == 1) {
                    if (arr1[1] < 1) {
                        //开始年的月份小于1时，不需要跨年
                        console.log("arr1[1] < 7")
                        flag = false
                    } else if (arr1[1] + 12 - arr2[1] < 12) {
                        //月相隔大于12个月
                        console.log("arr1[1]+12-arr2[1] < 12")
                        flag = false
                    } else if (arr1[1] + 12 - arr2[1] == 12) {
                        //月相隔3个月，比较日
                        if (arr2[2] > arr1[2]) {
                            //结束日期的日大于开始日期的日
                            console.log("截止日 arr2[2] > " + arr2[2])
                            console.log("开始日 arr1[2] > " + arr1[2])
                            flag = false
                        }
                    }
                }
            }
            if (!flag) {
                this.$notify({
                    title: this.$t("tip"),
                    message: this.$t("dateValidate.betweenDateValidate"),
                    type: "warning",
                })
                return false
            }
            return true
        },
        changeAccountDate() {
            if (
                !validatenull(this.query.order.properties.startDate) &&
                !validatenull(this.query.order.properties.endDate)
            ) {
                //获取开始时间和结束时间的时间差
                const oneYearDays = moment(
                    this.query.order.properties.endDate,
                ).diff(moment(this.query.order.properties.startDate), "d")
                console.log(oneYearDays)
            }
        },
        addTrf() {
            this.$router.push({
                path: "/trf/trfDetail",
                query: { id: "", flag: 1, title: "New TRF" },
            })
            //this.$router.push( {path: '/trf/trfForm',query:{ id:''}});
        },
        statusFormat: function (row, column) {
            var status = row.status
            var statusStr = ""
            if (status == 1 || status == "1") {
                statusStr = "正常"
            } else {
                statusStr = "禁用"
            }
            return statusStr
        },
        customerGroupFormat: function (row, column) {
            var customerGroupId = row.customerGroupId
            var dataList = this.customerGroupData
            var customerGroupStr = ""
            dataList.forEach(function (value, key, dataList) {
                if (customerGroupId == value.customerGroupId) {
                    customerGroupStr = value.groupName
                }
            })
            return customerGroupStr
        },
        beforeRouteEnter(to, from, next) {
            next((vm) => vm.init())
        },
        async init() {
            const loading = this.$loading({
                lock: true,
                text: "Loading",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.3)",
            })
            try {
                // 初始化supplier 对应的 buyer 数组
                //  await this.initBuyer()
                //  await this.initRouteParam()
                this.initDff()
                await this.initOrderList()
            } catch (e) {
                this.$error({ message: e.message || "Loading Data Is Fail!" })
            }
            loading.close()
        },
        initDff() {
            this.$nextTick(() => {
                setTimeout(() => {
                    this.$refs.tabSelect.init("all")
                }, 500)
            })
        },
        async onSubmit() {
            this.query.pageNo = 1
            await this.initOrderList()
        },
        async initOrderList() {
            var submitFlag = true

            if (validatenull(this.query.order.properties.queryValue)) {
                if (
                    validatenull(this.query.order.properties.startDate) &&
                    validatenull(this.query.order.properties.endDate)
                ) {
                    this.$notify({
                        title: this.$t("tip"),
                        message: this.$t(
                            "dateValidate.startDateAndEndDateValidate",
                        ),
                        type: "warning",
                    })
                    return false
                }
                submitFlag = this.checkTime()
            }
            if (!submitFlag) {
                return false
            }
            let columns = this.selectedVal.map((item) => item.fieldCode)
            columns.push("trfStatus", "id")
            columns = this.$lodash.uniq(columns)
            if (columns.indexOf("buyerReviewConclusion") > -1) {
                this.isShowReviewConclusion = true
            } else {
                this.isShowReviewConclusion = false
            }

            this.query.order.properties.columns = columns
            this.query.order.properties.language = this.language
            var params = {}
            this.loading = true
            console.log(
                "copy前的日期：",
                this.query.order.properties.trfSubmitDate,
            )
            debugger
            let queryParam = deepClone(this.query)

            //转换时区
            debugger
            if (!objectIsNull(queryParam.order.properties.startDate)) {
                queryParam.order.properties.startDate = this.tzToChina(
                    moment(queryParam.order.properties.startDate).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }
            if (!objectIsNull(queryParam.order.properties.endDate)) {
                queryParam.order.properties.endDate = this.tzToChina(
                    moment(queryParam.order.properties.endDate).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }
            if (!objectIsNull(queryParam.order.properties.trfSubmitDate)) {
                queryParam.order.properties.trfSubmitDate = this.tzToChina(
                    moment(queryParam.order.properties.trfSubmitDate).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }

            if (
                !objectIsNull(queryParam.order.properties.sampleReceiveDateStr)
            ) {
                queryParam.order.properties.sampleReceiveDateStr =
                    this.tzToChina(
                        moment(
                            queryParam.order.properties.sampleReceiveDateStr,
                        ).format("YYYY-MM-DD HH:mm:ss"),
                    )
            }
            if (!objectIsNull(queryParam.order.properties.dueDateStr)) {
                queryParam.order.properties.dueDateStr = this.tzToChina(
                    moment(queryParam.order.properties.dueDateStr).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
                )
            }
            if (
                !objectIsNull(queryParam.order.properties.reportApprovedDateStr)
            ) {
                queryParam.order.properties.reportApprovedDateStr =
                    this.tzToChina(
                        moment(
                            queryParam.order.properties.reportApprovedDateStr,
                        ).format("YYYY-MM-DD HH:mm:ss"),
                    )
            }
            console.log(
                "copy后的日期：",
                queryParam.order.properties.trfSubmitDate,
            )
            queryParam.order.properties.productLineCode =
                this.userInfo.productLineCode
            /* 接口已删除
                 let res = await getList_new(Object.assign({
                    columns: columns,
                    trfQuery: queryParam.order.properties,
                    pageNo: this.query.pageNo,
                    pageSize: this.query.pageSize
                }));
                this.loading = false;
                const data = res.data.data;
                this.query.total = data.total;
                this.trfData = data.records;
                console.log(this.trfData)
                if (this.$store.state.user.taskType == 'trfList' && this.trfData.length <= 0) {    // 新用户功能引导时的假数据
                    this.trfData.push({
                        "trfNo": "TRF2F00001",
                        "trfStatus": 2,
                        "buyerCustomerGroupName": "General",
                        "templateName": "SL Test template",
                    })
                }*/
        },
        displayNameWidth(fieldCode) {
            if (fieldCode == "applyNameEn") {
                return 300
            } else if (fieldCode == "buyerCustomerGroupName") {
                //buyer
                return 250
            } else if (
                fieldCode == "trfSubmissionDate" ||
                fieldCode == "sampleReceiveDate" ||
                fieldCode == "dueDate" ||
                fieldCode == "productDescription"
            ) {
                return 200
            } else {
                return 170
            }
        },
        onLoad(page) {
            this.loading = true
            var params = {}
            getList(
                this.page.currentPage,
                this.page.pageSize,
                Object.assign(params, this.query, this.sort),
            ).then((res) => {
                this.loading = false
                const data = res.data.data
                this.page.total = data.total
                this.trfData = data.records
            })
            let fn = JSON.parse(localStorage.getItem("finish_trfList"))
            if (!fn && this.trfData.length <= 0) {
                // 新用户功能引导时的假数据
                this.trfData.push({
                    trfNo: "TRF2F00001",
                    trfStatus: 2,
                    buyerCustomerGroupName: "General",
                    templateName: "SL Test template",
                })
            }
        },
        onLoadFromWel() {
            this.loading = true
            var params = {}
            this.query.order.properties.trfStatus = this.$route.query.trfStatus
            this.query.order.properties.startDate = this.$route.query.startDate
            this.query.order.properties.endDate = this.$route.query.endDate
            getList(
                this.page.currentPage,
                this.page.pageSize,
                Object.assign(params, this.query, this.sort),
            ).then((res) => {
                this.loading = false
                const data = res.data.data
                this.page.total = data.total
                this.trfData = data.records
            })
        },
        /*trfTableDateFormtter(row, column) {
                var date = row[column.property];
                if (date == undefined || date == '') {
                    return this.$t('trf.notSubmit');
                }
                ;
                return moment(date).format("YYYY-MM-DD")
            },*/

        //分页查询
        async sizeChange(pageSize) {
            this.query.pageSize = pageSize
            await this.initOrderList()
        },
        selectLabTypeChange(value) {
            this.labForm.labType = value
        },
    },
}
</script>

<style lang="scss">
.top-title {
    font-size: 24px;
    // font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 32px;
    margin: 0px 0 17px;
}

.el-icon-arrow-right:before {
    font-weight: 700;
}

.trf-o-btn {
    margin-top: -14px;
}

.f-sort a {
    float: left;
    padding: 0 9px;
    height: 23px;
    border: 1px solid #ccc;
    line-height: 23px;
    margin-right: -1px;
    background: #fff;
    color: #333;
}

a.curr {
    border-color: #919191;
    background: #919191;
    color: #fff;
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #ffffff;
    background-color: #919191;
    border-color: #919191;
    -webkit-box-shadow: -1px 0 0 0 #919191;
    box-shadow: -1px 0 0 0 #919191;
}

.el-table__fixed-right {
    height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
    bottom: 17px; // 固定列默认设置了定位，    position: absolute;top: 0;left: 0;只需要再设置一下bottom的值，让固定列和父元素的底部出现距离即可
    background: #fff;
}

.otherActiveClass {
    color: #fff;
    background-color: #ebeef5;
}

.row-expand-cover {
    .el-table__expand-icon {
        visibility: hidden;
    }
}

.otherActiveClass:hover {
    background: #ebeef5;
    color: #fff;
}

.newIcon {
    position: absolute;
    right: 0;
    top: 0;
}

.wrap {
    background: #fff;
    padding: 24px 32px;
}

.date-group {
    button {
        border: 0;
        border-bottom: 1px solid transparent;
        border-radius: 0;
        padding: 10px 0;
        margin: 0 10px;

        &:hover,
        &:focus {
            background-color: initial;
            border-color: transparent;
        }

        &.active {
            border-bottom-color: #f60;
            color: #f60;
        }
    }

    .bg_orange {
        color: #ea4336;
        margin-right: 20px;

        img {
            margin-top: -7px;
        }
    }
}

.btn-sort {
    .active {
        color: #ff6600;
        border-color: #ffd1b3;
        background-color: #fff0e6;
    }
}

.trfListTable {
    .cell {
        white-space: nowrap !important;
    }

    th {
        padding-top: 16px;

        &.is-left {
            padding-top: 22px;
        }

        .el-input__inner {
            background: transparent;
            border-color: #d8d8d8;
            padding-left: 0;
            /* color: #000; */
            &::-webkit-input-placeholder {
                /* color: transparent; */
            }
        }

        .date-input {
            .el-input__inner {
                padding-left: 24px;
            }
        }

        .cell {
            > div {
                &:last-of-type {
                    margin-top: 8px;
                }
            }

            .el-select .el-input__inner:focus {
                border-color: #d8d8d8;
            }

            .el-select .el-input .el-select__caret {
                color: #d8d8d8;
            }

            .el-input__prefix {
                left: -5px;
            }
        }

        &:last-of-type {
            .cell {
                margin-top: -49px;
            }

            .operatop > span {
                float: right;
            }
        }
    }
}

#trf-list-filter {
    float: left;
    width: fit-content;
    /* margin-bottom: 24px; */
    .el-form-item {
        margin-bottom: 0;
    }
}

.sort-btn {
    float: right;

    button {
        border-color: #ccc;
    }
}

#add-trf-btn {
    width: 248px;
    text-align: left;
    margin-left: 24px;
}

.add-menu {
    li {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        display: inline-block;
        padding-left: 10px;
        transition: all 0.2s;

        a {
            display: block;
        }

        &:hover {
            background: rgba(255, 102, 0, 0.1);
            color: #ff6600;
        }
    }
}

.plain-black {
    height: 40px;
    padding-top: 0;
    padding-bottom: 0;
}

.trf-sort-icon {
    /* transition: .2s all; */
    &.isSort {
        transform: rotate(180deg);
    }
}

.tools {
    /* display: flex;
    justify-content: space-between; */
    margin-bottom: 24px;
}

.filter-date {
    width: 260px !important;
    padding-right: 0 !important;
}

.check-time-out-2 {
    display: none;
}
.menu-icon {
    font-size: 20px;
    cursor: pointer;
    margin: 0px 10px;
    color: #ff6600;
}
@media screen and (max-width: 1900px) {
    #trf-list-filter {
        float: left;
    }
    .en-trf-list-filter {
        margin-bottom: 15px;
    }
    .en-sort-btn {
        width: 100%;
    }
}

@media screen and (max-width: 1600px) {
    .cn-trf-list-filter {
        margin-bottom: 15px;
    }
    .cn-sort-btn {
        float: left;
    }
}

@media screen and (max-width: 1366px) {
    #trf-list-filter .el-input__inner,
    .filter-date {
        width: 260px !important;
    }
    .en-trf-list-filter .check-time-out-1 {
        /* display: none; */
    }
    .en-sort-btn {
        /* text-align: right; */
        .check-time-out-2 {
            /* display: inline-block;
        height: 40px;
        line-height: 40px; */
        }

        button {
            /* margin-left: 16px !important; */
        }
    }
    .cn-sort-btn {
        .check-time-out-2 {
            /* display: none; */
        }
    }
}
</style>
