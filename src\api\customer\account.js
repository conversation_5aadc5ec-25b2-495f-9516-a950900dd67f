import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/account/add',
        method: 'post',
        data: form
    })
}

export const createAccount = (form) => {
    return request({
        url: '/api/sgs-mart/customer/account/add-account',
        method: 'post',
        data: form
    })
}

export const updateAccount = (form) => {
    return request({
        url: '/api/sgs-mart/customer/account/update-account',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/account/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/account/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/account/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const updateDefaultServiceUnit = (serviceUnitCode) => {
    return request({
        url: '/api/sgs-mart/customer/account/updateServiceUnit',
        method: 'get',
        params: {
            serviceUnitCode,
        }
    })
}


export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/account/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const approve = (form) => {
    return request({
        url: '/api/sgs-mart/customer/account/approve',
        method: 'post',
        data: form
    })
}
