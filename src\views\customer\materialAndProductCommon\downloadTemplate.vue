<template>
    <div class="sgs_smart_new_material_downloadTemplate" id="sgs_smart_new_material_downloadTemplate">
        <el-form :model="templateForm"
                 :rules="rules"
                 ref="downloadTemplateForm"
                 label-position="left"
                 label-width="140px"
                 size="small">
            <el-form-item label="Product Line" prop="productLineCode">
                <el-select style="width: 100%"
                           clearable
                           filterable
                           :disabled="disabledCustomerInfo"
                           v-model="templateForm.productLineCode"
                           @change="queryTemplateList">
                    <el-option
                            v-for="(pl,index) of lists.productLineCodes"
                            :key="'pl_'+index"
                            :label="pl.productLineName"
                            :value="pl.productLineCode"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="Customer" prop="customerGroupCode">
                <el-select clearable filterable
                           style="width: 100%"
                           :disabled="disabledCustomerInfo"
                           @change="queryTemplateList"
                           v-model="templateForm.customerGroupCode">
                    <el-option
                            v-for="(ms,index) of lists.customerList"
                            :key="'ms_'+index"
                            :label="ms.customerGroupName"
                            :value="ms.customerGroupCode">
                        {{ms.customerGroupName }} ({{ms.customerGroupCode}})
                    </el-option>
                    <el-option label="General" value="General"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item :label="formPurpose+' Template'" prop="templateId">
                <el-select clearable filterable style="width: 100%" v-model="templateForm.templateId">
                    <el-option
                            v-for="(te,index) of lists.templateList"
                            :key="'te_'+index"
                            :label="te.templateName"
                            :value="te.templateId"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <el-row>
            <el-col style="text-align: right">
                <slot name="downloadTemplateSlot"></slot>
                <el-button type="primary" size="small" v-if="!templateShowDownload" @click="createNewMaterial('new')">Create {{formPurpose}}</el-button>
                <el-button type="primary" size="small" v-if="templateShowDownload" @click="downloadTemplate">Download Template</el-button>
                <el-button type="info" size="small" @click="$emit('cancelDia')">Cancel</el-button>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import api from '../../../api/newSamples'
    import {mapGetters} from "vuex";
    export default {
        name: "downloadTemplate",
        data() {
            return {
                lists:{
                    productLineCodes:[],
                    customerList:[],
                    templateList:[]
                },
                rules: {
                    productLineCode: [{required: true, message: 'Please select', trigger: 'change'}],
                    customerGroupCode: [{required: true, message: 'Please select', trigger: 'change'}],
                    templateId:[{required: true, message: 'Please select', trigger: 'change'}],
                },
                templateForm:{
                    productLineCode:'',
                    customerGroupCode:'',
                    templateId:''
                }
            }
        },
        methods: {
            createNewMaterial(){
                this.$refs.downloadTemplateForm.validate((valid)=>{
                    if(!valid){
                        return
                    }
                    let query = {
                        action:'new',
                        templateId: this.templateForm.templateId,
                        sourceType:this.sourceType,
                        sampleId:this.sampleId
                    }
                    let winUrl =  this.$router.resolve({
                        path:'/customer/new'+this.formPurpose+'/detail',
                        query
                    })
                    window.open(winUrl.href,'_blank');
                    this.$emit('cancelDia')
                });
            },
            downloadTemplate(){
                this.$refs.downloadTemplateForm.validate((valid)=>{
                    if(!valid){
                        return;
                    }
                    //download template
                    let param = {
                        templateId: this.templateForm.templateId,
                        formPurpose:this.formPurpose
                    }
                    api.downloadMaterialTemplate(param,()=>{
                        //this.$emit('cancelDia');
                    },err=>{
                        this.$notify.error(err);
                    })
                })

            },
            queryTemplateList(){
                this.lists.templateList = [];
                this.templateForm.templateId = '';
                if(!this.templateForm.productLineCode){
                    return;
                }
                let param = {
                    "buCode": this.templateForm.productLineCode,
                    "customerGroupCode":this.templateForm.customerGroupCode,
                    "queryAllVersion": 0,
                    "rows": 10,
                    "page":1,
                    formPurpose:this.formPurpose
                }
                api.geSampleTemplateList(param).then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        this.lists.templateList = res.data.data || []
                        if(this.lists.templateList.length==1){
                            this.templateForm.templateId = this.lists.templateList[0].templateId;
                        }
                    }
                })
            },
        },
        mounted() {
        },
        created() {
            let customerList = this.customerList || [];
            let currentUserInConfig = customerList.find(c=>c.customerGroupCode==this.userInfo.customerGroupCode);
            if(!currentUserInConfig){
                let {customerGroupCode,customerGroupId,customerGroupName} = this.userInfo;
                if(customerGroupCode){
                    customerList.push({customerGroupCode,customerGroupId,customerGroupName})
                }
            }
            this.lists.customerList = customerList;
            this.lists.productLineCodes = this.productLineList || [];
            if(this.disabledCustomerInfo){
                this.templateForm.customerGroupCode = this.customerList[0].customerGroupCode;
                this.templateForm.productLineCode = this.productLineList[0].productLineCode;
                this.queryTemplateList();
            }
        },
        watch: {},
        computed: {
            ...mapGetters([
                "permission",
                "userInfo"
            ]),
        },
        props: {
            templateShowDownload:{
              type:Boolean,
              default(){
                  return false;
              },
            },
            customerList:{
                type:Array,
                default(){
                    return []
                }
            },
            productLineList:{
                type:Array,
                default(){
                    return []
                }
            },
            disabledCustomerInfo:{
              type:Boolean,
              default:false,
            },
            formPurpose: {
                type:String,
                default(){
                    return ''
                }
            },
            sourceType:{
                type:String,
                default:''
            },
            sampleId:{
                type:String,
                default:''
            }
        },
        updated() {
        },
        components: {}
    }
</script>

<style scoped>
    .sgs_smart_new_material_downloadTemplate {
    }
</style>