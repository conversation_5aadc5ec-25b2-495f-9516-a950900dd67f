import request from '../request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */

// 分页查询知识库
export const tabsPagePage = (params:any) => {
    return request({
      url: `/sgs-e-filling/sgs-knowledge/knowledge/adminpage?hash=${new Date().getTime()}`,
      method: 'get',
      params,
     
    })
  }
  //新增文章
  export const tabsPageSave = (params:any) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/save',
      method: 'post',
      params,
     
    })
  }
  //编辑文章
  export const tabsPageUpdate = (params:any) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/update',
      method: 'post',
      params,
     
    })
  }
  //删除文章
  export const tabsPageRemove = (params:any)   => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/remove',
      method: 'post',
      params,
     
    })
  }
  //查看回收站分页
  export const tabsPageRecyclebinpage = (params:any) => {
    return request({
      url: `/sgs-e-filling/sgs-knowledge/knowledge/recyclebinpage?hash=${new Date().getTime()}`,
      method: 'get',
      params,
     
    })
}
//查看文章详情
export const tabsPageDetail = (params:any) => {
    return request({
      url: `/sgs-e-filling/sgs-knowledge/knowledge/detail?hash=${new Date().getTime()}`,
      method: 'get',
      params,
     
    })
}
//恢复单个文章
export const tabsPageRecyclebinupdate = (data:any) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/recyclebinupdate',
      method: 'post',
      data,
     
    })
}
//批量恢复文章
export const tabsPageRecyclebinupdatebybatch = (data:any) => {
    return request({
      url: '/sgs-e-filling/sgs-knowledge/knowledge/recyclebinupdatebybatch',
      method: 'post',
      data,
     
    })
}
//更改发布状态
export const tabsPagePulishflagupdate = (data:any) => {
  return request({
    url: '/sgs-e-filling/sgs-knowledge/knowledge/pulishflagupdate',
    method: 'post',
    data,
   
  })
}
export const tabsPagecount = (params:any) => {
  return request({
    url: `/sgs-knowiedge/knowledge/attachmentviewcount?hash=${new Date().getTime()}`,
    method: 'get',
    params,
   
  })
}