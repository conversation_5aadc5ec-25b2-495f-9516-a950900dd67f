<template>
  <div class="smart_views_WorkBook"
       id="smart_views_WorkBook">
    <CommonTable border
                 v-loading="tableOption.loading"
                 ref="manufactureInfoTable"
                 style="width:100%"
                 row-key="id"
                 :size="tableOption.size"
                 :data="tableOption.dataList"
                 :page="tableOption.page"
                 :menu-show="tableOption.menuShow"
                 :option="tableOption.option"
                 :filters="tableOption.filters">
      <template #menuRight>
        <!-- <el-button text type="primary" size="mini" @click="openReject" :icon="ChatSquare">Reject</el-button>
                <el-button text type="primary" size="mini" @click="openComment" :icon="ChatSquare">Comment</el-button> -->
        <el-button text
                   type="primary"
                   size="mini"
                   @click="handlerUploadAdd"
                   :icon="Plus">Add</el-button>
        <el-button text
                   type="primary"
                   size="mini"
                   @click="linkObj"
                   :icon="Link">Link</el-button>
      </template>
      <template #docName="{row,$index}">
        <el-button text
                   type="primary"
                   @click="downloadWorkbook(row)">{{row.docName}}</el-button>
      </template>
      <template #lockStatus="{row,$index}">
        <!-- lockStatus 1: checked out, 0: checked in -->
        <template v-if="row.lockStatus === 1">
          <div>
            <el-button text
                       type="primary"
                       @click="handleCheckIn(row)">Check In</el-button>
          </div>
          <div>
            <el-button text
                       type="primary"
                       @click="cancelCheckOut(row)">Cancel Check Out</el-button>
          </div>
        </template>
        <div v-if="row.lockStatus === 0">
          <el-button text
                     type="primary"
                     @click="checkOut(row)">Check Out</el-button>
        </div>
      </template>

      <template #actionColumn="{row,$index}">

        <el-button text
                   type="primary"
                   size="mini"
                   @click="linkDetail(row)">Link Detail</el-button>
        <!-- lockStatus 1: checked out, 0: checked in -->
        <template v-if="row.lockStatus === 0">
          <el-button text
                     type="primary"
                     size="mini"
                     @click="showHistory(row)">History</el-button>
          <ConfirmButton title="Are you sure delete this data?"
                         @confirm="deleteWorkbook(row)">Delete</ConfirmButton>
        </template>
        <template v-if="row.lockStatus === 1">
          <el-button text
                     type="primary"
                     size="mini"
                     @click="unLink(row)">Un Link</el-button>
        </template>
        <!-- 
        <el-button text
                   type="primary"
                   size="mini"
                   @click="approve(row,'APPROVED')">Approve</el-button>
        <el-button text
                   type="primary"
                   size="mini"
                   @click="approve(row,'REJECTED')">Reject</el-button>
        <el-button text
                   type="primary"
                   size="mini"
                   @click="approveReturn(row)">Return Approve</el-button> -->
      </template>
    </CommonTable>
    <batch-upload v-if="isLoadUpload"
                  title="Add Workbooks"
                  append-to-body
                  :systemID="1"
                  :limit="5"
                  :handle-upload-success="uploadSuccess"
                  :handle-upload-error="uploadError"
                  ref="batchUploadRef"
                  showDesc
                  accept=".xlsx,.xls"
                  upload-url="/api/sgsapi/FrameWorkApi/file/doUpload"
                  :attachment-type-options="[]"
                  attachment-type-default-value=""
                  :file-max-sizes="20"></batch-upload>
    <Comments v-if="showComment"
              :object-id="objectId"
              object-type="workbook"
              ref="workbookComments"></Comments>

    <rejectProvided v-if="rejectShow"
                    :object-id="objectId"
                    object-type="workbook"
                    @cancelDia="rejectShow=false"
                    @rejectSuccess="rejectSuccess"></rejectProvided>

    <el-dialog v-model="openLinkObject"
               title="Library"
               width="75%"
               lock-scroll
               show-close
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               draggable>
      <LinkObject v-if="openLinkObject"
                  :objectId="objectId"
                  @linkSuccess="loadTableData"></LinkObject>
    </el-dialog>

    <el-dialog v-model="openHistory"
               title="History"
               width="60%"
               lock-scroll
               show-close
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               draggable>
      <WorkBookHistory v-if="openHistory"
                       :objectId="objectId"
                       :relationId="historyRelationId"></WorkBookHistory>
    </el-dialog>

    <el-dialog v-model="openLinkDetail"
               title="Product the workbook is linked to"
               width="60%"
               lock-scroll
               show-close
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               draggable>
      <WorkBookLinkDetail v-if="openLinkDetail"
                          :objectId="objectId"
                          :relationId="linkDetailRelationId"></WorkBookLinkDetail>
    </el-dialog>

    <el-dialog v-model="data.workbooks.dialogVisible"
               title="Add Workbooks"
               width="500px"
               lock-scroll
               show-close
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               draggable>
      <div class="add-workbooks-dialog">
        <el-form label-width="100px"
                 :model="data.workbooks"
                 ref="workbookForm">
          <el-form-item label="File">
            <Upload :action="upload.action"
                    :show-file-list='upload.showFileList'
                    :multiple='upload.multiple'
                    :limit='upload.limit'
                    :size='upload.size'
                    :accept='upload.accept'
                    @uploadEmit='uploadEmitFun'></Upload>
          </el-form-item>
          <el-form-item label="Description">
            <el-input v-model="data.workbooks.description"
                      type="textarea"
                      maxlength="100"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="data.workbooks.dialogVisible = false">Cancel</el-button>
          <el-button type="primary"
                     @click="handeleSave">
            Unload
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>


<script setup lang="ts">
import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  computed,
  watch,
  provide,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElNotification } from 'element-plus'
import { useI18n } from 'vue-i18n'
import CommonTable from '@/components/TableList/CommonTable.vue'
import ConfirmButton from '@/components/ConfirmButton/index.vue'
import Comments from './Comments.vue'
import { Plus, Link, ChatSquare, InfoFilled } from '@element-plus/icons-vue'
import BatchUpload from '@/components/BatchUpload/index.vue'
import LinkObject from './LinkObject.vue'
import WorkBookHistory from './WorkBookHistory.vue'
import WorkBookLinkDetail from './WorkBookLinkDetail.vue'
import rejectProvided from './rejectProvided.vue'
import productApi from '@/api/product.ts'
import { saveAttachment, getFileUrlByCloudId } from '@/api/common'
import Upload from '@/components/Upload/Upload.vue'

const { t } = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)
defineOptions({
  name: 'WorkBook',
})
const data = reactive({
  workbooks: {
    dialogVisible: false,
    checkIn: {},
    description: '',
  },
  attachment: [],
})
const props = defineProps({
  objectId: {
    type: String,
    default: '',
  },
})

const upload = reactive({
  action: '/api/sgsapi/FrameWorkApi/file/doUpload',
  showFileList: true,
  multiple: false,
  limit: 20,
  size: 1,
  accept: '.xlsx,.xls',
})
const uploadEmitFun = (val) => {
  if (val) {
    let objId = props.objectId
    let fileList = (val || []).map((item) => {
      const attachment = {
        object: 'productWorkBook',
        objectId: objId,
        attachmentId: item.id,
        fileName: item.attachmentName + '.' + item.suffixes,
        fileUrl: item.path,
        fileType: 'File',
        languageId: 1,
      }
      return attachment
    })
    console.log('fileList', fileList)
    data.attachment = fileList
  }
  //   console.log(val)
}

const handeleSave = () => {
  if (data.workbooks.checkIn.attachmentId) {
    checkIn()
  } else {
    saveAttachment(data.attachment).then((res) => {
      if (res.status == 200 && res.data) {
        let fileObj = res.data[0]
        let { attachmentId } = fileObj
        addDocument(attachmentId)
      } else {
        ElNotification.error('Save fail error')
      }
    })
  }
}

watch(language, (newVal) => {})
onMounted(() => {
  loadTableData()
})

const rejectShow = ref(false)
const openReject = () => {
  rejectShow.value = true
}

const showComment = ref(false)
const openComment = () => {
  showComment.value = true
}

const tableOption = reactive({
  dataList: [],
  originalDataList: [],
  loading: false,
  menuShow: true,
  size: 'small',
  option: {
    hideRowColor: true,
    selection: false,
    selectionDis: () => {
      return true
    },
    sortSelectionFiledName: '',
    showSortIcon: true,
    index: true,
    action: true,
    actionWidth: 150,
    actionAlign: '',
    disableOption: {},
    column: [
      //     {
      //     prop: 'relationId',
      //     label: 'relationId',
      //     hide: false,
      //     filter: false,
      //     slot: false,
      //     type: 'Input',
      //   },
      {
        prop: 'docName',
        label: 'Workbook',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'description',
        label: 'Description',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'versionNo',
        label: 'Revision',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'updateTime',
        label: 'Update Time',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'updateUser',
        label: 'Update By',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'lockStatus',
        label: 'Checked Out To',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'approvalStatus',
        label: 'Approval Status',
        hide: false,
        filter: true,
        slot: false,
        type: 'Select',
        tagWidth: '100px',
        dicData: [
          { label: 'APPROVED', value: 'APPROVED', tag: true, type: 'success' },
          { label: 'REJECTED', value: 'REJECTED', tag: true, type: 'error' },
          { label: 'CANCEL', value: 'CANCEL', tag: true, type: 'cancel' },
          { label: 'PENDING', value: 'PENDING', tag: true, type: 'warn' },
        ],
      },
    ],
  },
  sort: {
    sortBy: '',
    sortOrder: '',
  },
  filters: {},
  page: {
    show: false,
    size: 1,
    page: 1,
    rows: 20,
    small: true,
    sizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
    total: 100,
  },
})

const isLoadUpload = ref(false)
const batchUploadRef = ref(null)
const handlerUploadAdd = () => {
  data.workbooks.checkIn = {}
  data.workbooks.description = ''
  isLoadUpload.value = true
  nextTick(() => {
    handlerUpload()
  })
}
const handlerUpload = () => {
  isLoadUpload.value = false
  nextTick(() => {
    data.workbooks.dialogVisible = true
  })
}

const uploadSuccess = (data) => {
  isLoadUpload.value = false
  if (data) {
    let objId = props.objectId
    let fileList = (data.data || []).map((item) => {
      const attachment = {
        object: 'productWorkBook',
        objectId: objId,
        attachmentId: item.id,
        fileName: item.attachmentName + '.' + item.suffixes,
        fileUrl: item.path,
        fileType: 'File',
        languageId: 1,
      }
      return attachment
    })
    saveAttachment(fileList).then((res) => {
      if (res.status == 200 && res.data) {
        let fileObj = res.data[0]
        let { attachmentId } = fileObj
        addDocument(attachmentId)
      } else {
        ElNotification.error('Save fail error')
      }
    })
  }
}
const uploadError = (err) => {}

const loadTableData = () => {
  tableOption.dataList = []
  productApi.workbookApi
    .loadTableData({
      objectId: props.objectId,
      objectType: 'Product',
      bizType: 'WB',
    })
    .then((res) => {
      if (res.status == 200) {
        tableOption.dataList = res.data?.documents?.WB || []
        data.workbooks.dialogVisible = false
      }
    })
}

const openLinkObject = ref(false)
const linkObj = () => {
  openLinkObject.value = true
}
const approve = (row, approvalStatus) => {
  let objectId = props.objectId
  let { relationId } = row
  let param = {
    objectId,
    relationId,
    approvalStatus,
  }
  productApi.workbookApi.approve(param).then((res) => {
    if (res.status == 200) {
      loadTableData()
    }
  })
}
const approveReturn = (row) => {
  let objectId = props.objectId
  let { relationId } = row
  productApi.workbookApi.approveReturn({ objectId, relationId }).then((res) => {
    if (res.status == 200) {
      loadTableData()
    }
  })
}

const unLink = (row) => {
  let objectId = props.objectId
  let { relationId } = row
  productApi.workbookApi.unLink({ relationId, objectId }).then((res) => {
    console.log('unLink res', res)
    if (res.status == 200) {
      loadTableData()
    }
  })
}
const deleteWorkbook = (row) => {
  let objectId = props.objectId
  let { relationId } = row
  productApi.workbookApi
    .deleteWorkbook({ objectId, relationId })
    .then((res) => {
      console.log('deleteWorkbook res', res)
      if (res.status == 200) {
        loadTableData()
      }
    })
}
const openHistory = ref(false)
const historyRelationId = ref('')
const showHistory = (row) => {
  let { relationId } = row
  historyRelationId.value = relationId
  openHistory.value = true
}

const openLinkDetail = ref(false)
const linkDetailRelationId = ref('')
const linkDetail = (row) => {
  let { relationId } = row
  linkDetailRelationId.value = relationId
  openLinkDetail.value = true
}
const downloadWorkbook = async (row) => {
  const { fileUrl } = row
  const res = await getFileUrlByCloudId(fileUrl)
  const downloadURL = res.data
  if (downloadURL) {
    window.open(downloadURL, '_blank')
  }
}
const handleCheckIn = (row) => {
  debugger
  data.workbooks.checkIn = row
  handlerUpload()
}
const checkIn = () => {
  let objectId = props.objectId
  let { relationId, attachmentId } = data.workbooks.checkIn
  debugger
  productApi.workbookApi
    .checkIn({ relationId, objectId, attachmentId })
    .then((res) => {
      console.log('checkIn res', res)
      if (res.status == 200) {
        loadTableData()
      }
    })
}
const checkOut = (row) => {
  let objectId = props.objectId
  let { relationId } = row
  productApi.workbookApi.checkOut({ relationId, objectId }).then((res) => {
    console.log('checkOut res', res)
    if (res.status == 200) {
      loadTableData()
    }
  })
}
const cancelCheckOut = (row) => {
  let objectId = props.objectId
  let { relationId } = row
  productApi.workbookApi.unCheckout({ relationId, objectId }).then((res) => {
    console.log('cancelCheckOut res', res)
    if (res.status == 200) {
      loadTableData()
    }
  })
}
const addDocument = (attachmentId) => {
  let objectId = props.objectId
  let param = {
    objectId,
    attachmentId,
    objectType: 'Product',
    bizType: 'WB',
  }
  productApi.workbookApi.addDocument(param).then((res) => {
    loadTableData()
  })
}
</script>

<style lang="scss">
.smart_views_WorkBook {
}
</style>