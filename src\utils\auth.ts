import Cookies from 'js-cookie'
import { JSEncrypt } from 'jsencrypt'

// 定义 Token 的键名，类型为字符串
const TokenKey: string = 'sgsToken'

/**
 * 获取 Token 的函数
 * @returns 返回存储的 Token 字符串，如果不存在则返回 undefined
 */
export function getToken(): string | undefined {
  // 从 Cookies 中获取 Token
  return Cookies.get(TokenKey)
}

/**
 * 设置 Token 的函数
 * @param token - 要设置的 Token 字符串
 * @returns 如果成功设置 Token 则返回设置结果，否则返回 undefined
 */
export function setToken(token: string | undefined): void | undefined {
  if (token) {
    // 将 Token 存储到 sessionStorage 中
    sessionStorage.setItem(TokenKey, token)
    // 将 Token 存储到 Cookies 中，并设置过期时间为 120 分钟后
    return Cookies.set(TokenKey, token, {
      expires: new Date(new Date().getTime() + 120 * 60 * 2000),
    })
  }
  return undefined
}

/**
 * 加密密码的函数
 * @param password - 要加密的密码字符串
 * @returns 返回加密后的密码字符串，如果加密失败则返回 null
 */
export function encryptor(password: string): string | null {
  // 创建 JSEncrypt 实例
  let encryptor: JSEncrypt = new JSEncrypt()
  // 设置公钥
  encryptor.setPublicKey(
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJ3JpeGoxzx4VfoQu88IVlJ+0x5OUDPhAp0Ttwsvd3YpY+SsKiJrscxRU1oyPGi4pNDNuQmEYmdyv6GbNYIMxXR+CuQiLYwI1/xhHd9DcYPC/eTXNln7Q2gQ4zrEhiez+LJf7pwrXaCEq03E9zGxb9unQgY0digv0NGD5l8pTCQwIDAQAB',
  )
  // 对密码进行加密
  const encryptedPassword = encryptor.encrypt(password)
  // 检查加密结果是否为 false
  if (encryptedPassword === false) {
    // 若为 false，说明加密失败，返回 null
    return null
  }
  // 若加密成功，返回加密后的密码
  return encryptedPassword
}

/**
 * 移除 Token 的函数
 * @returns 移除成功返回 true
 */
export function removeToken(): boolean {
  // 从 sessionStorage 中移除 Token
  sessionStorage.removeItem(TokenKey)
  // 从 Cookies 中移除 Token
  Cookies.remove(TokenKey)
  return true
}
