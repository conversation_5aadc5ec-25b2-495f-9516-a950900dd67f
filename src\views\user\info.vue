<template>
  <basic-container>
    <!-- <el-breadcrumb class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('navbar.userinfo')}}</el-breadcrumb-item>
    </el-breadcrumb> -->
    <h1 class="top-title">{{$t('navbar.userinfo')}}</h1>
    
    <el-row>
      <el-tabs type="border-card">
        <el-tab-pane :label="$t('user.updPassword')">
          <change-password></change-password>
        </el-tab-pane>
        <el-tab-pane :label="$t('route.info')" >
          <upd-user-info></upd-user-info>
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </basic-container>
</template>

<script>


  export default {
      components: {
        UpdUserInfo: resolve => require(['./updUserInfo'], resolve),
        ChangePassword: resolve => require(['@/views/user/changePassword'], resolve)
      },
      data() {
        return {

        }
      },
  };
</script>

<style>
</style>
