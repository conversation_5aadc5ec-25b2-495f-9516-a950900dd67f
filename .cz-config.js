module.exports = {
  types: [
    { value: 'feat', name: 'Feature: 新增功能' },
    { value: 'fix', name: 'Fix: 修复Bug' },
    { value: 'docs', name: 'Docs: 文档变更' },
    { value: 'style', name: 'Style: 代码格式（不影响功能）' },
    { value: 'refactor', name: 'Refactor: 代码重构' },
    { value: 'perf', name: 'Pref: 性能优化' },
    {
      value: 'chore',
      name: 'Chore: 其他修改,比如构建流程,依赖管理、版本号修正',
    },
  ],
  scopes: [
    { name: 'Components' },
    { name: 'Utils' },
    { name: 'Styles' },
    { name: 'Deps' },
    { name: 'FrameUI' },
    { name: 'Other' },
  ],
  allowCustomScopes: true,
  allowBreakingChanges: ['feat', 'fix'],
}
