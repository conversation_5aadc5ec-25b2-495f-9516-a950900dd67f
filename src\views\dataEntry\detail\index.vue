<template>
  <div class="sgs_wrap">
    <div v-if="show">
<!--嵌入DataEntry组件  -->
     <sgs-mart-dataentry-form :trf-id="selectedTrf.id" :env="currentEnv" ref="frm"></sgs-mart-dataentry-form>
    </div>
    <div v-else>
      <h3>No Auth Access! Please Login.</h3>
    </div>
  </div>
</template>
<script>

  import { mapState } from 'vuex'
  import {queryTrfDetail} from "../../../api/trf/trf";
  import {currentEnv} from '@/config/env';
  export default {
    name: 'home',

    data() {
      return {
        currentEnv:'',
        testLineDisableFlag: false,
        show: false,
        key: new Date().getTime(),
        selectedTrf: {},
        loading: false,
        generalOrderID: '',
        sgsToken: '',
        reviewConclusionArray: [],
        testResult: {
          list: [{
            title: 'New TestResult',
            reportStatus: '1',
            testResultDisableFlag: false,
            testlineResults: [],
            reportResultHistory: []
          }]
        },
        listVisible: false
      }
    },
    components: {
    },
    beforeRouteEnter(to, from, next) {
      next(async vm => {
        vm.init(vm.sgsToken)
        vm.generalOrderID = vm.$route.query.generalOrderID;
        vm.$store.commit('SET_COMPANYTYPE', vm.$route.query.companyType);
        vm.sgsToken = vm.$route.query.token;
        vm.signature = vm.$route.query.signature;

        vm.show = true
        vm.selectedTrf.id = vm.$route.query.id;

        await vm.handleTrfNoClick(vm.$route.query)
      })
    },
    computed: {
      ...mapState({
        companyType: 'companyType',
        disabledFlag: 'disabledFlag'
      })
    },
    methods: {
      // 进入页面初始化数据
      async init(token) {
        //获取当前环境
        this.currentEnv=currentEnv;
        this.selectedTrf = {}
      },

      async handleTrfNoClick(val) {
        if (val) {
          console.log("获取trf基本信息", val);
          this.loading = true
          await queryTrfDetail({
            trfId: val.id,
            trfNo: val.trfNo,
            sgsToken: this.sgsToken,
            signature: val.signature
          }).then(res => {
            this.selectedTrf = res.data.data
          }).finally(() => {
            this.loading = false;
          });
          debugger;
          console.log(this.selectedTrf)
          //this.$refs.testResult.myErrors = {}
          if (this.selectedTrf.trfStatus == 5) {
            this.$store.commit('SET_DISABLEDFLAG', true)
          } else {
            this.$store.commit('SET_DISABLEDFLAG', false)
          }
          this.selectedTrf.trfStatus = parseInt(this.selectedTrf.trfStatus)
          this.$store.commit('SET_SELECTEDDATA', this.selectedTrf)
          this.loading = true
          this.$refs.frm.reload();

          //滚动到Report-Resut
          let anchorElement = document.getElementById('testresult')
          if (anchorElement) {
            anchorElement.scrollIntoView()
          }
          this.loading = false
        }
      }
    }
  }
</script>

<style lang="scss">
  .border0 {
    border-bottom: 0px !important;
  }
</style>
<style lang="scss" scoped>
  .sgs_wrap {
    background: #f5f5f5;
    border-radius: 2px;
    /* width: 70%; */
    margin: 0px auto;
    padding: 24px 32px 110px;
    /* height: 88vh; */
  }

  /deep/ table {
    th, td, .cell {
      font-size: 12px;
    }

    th {
      padding: 5px 0px;

      div {
        line-height: 20px;
      }
    }
  }
</style>
