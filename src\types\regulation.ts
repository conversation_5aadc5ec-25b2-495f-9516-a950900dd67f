export interface Regulation {
    title: string;
    tag?: string[];
    url: string;
    status: string;
    publishDate: string;
    effectiveDate: string;
    views: number;
    labels?: string;
    author: string;
    content: string;
}

export interface RegulationState {
    regulations: Regulation[];
    currentPage: number;
    totalPages: number;
    filter: string;
    searchQuery: string;
} 