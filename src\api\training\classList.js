import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-training/course/insertModule',
        method: 'post',
        data: form
    })
}
export const selectClassList = (params,current,size) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/class/select-training-class-list",
        changeOrigin: true,
        params: {
            ...params,
            current,
            size,
        }
        //data:queryInfo
    })
}
export const handleDel2 = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/class/delete-class-by-id",
        changeOrigin: true,
        params: {"id":id}
    })
}

export const sel_group = () => {
    return request({
        url: '/api/sgs-training/course/selGroup',
        method: 'post',
    })
}
/*export const selModuleById = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selModuleById",
        changeOrigin: true,
        data: id
    })
}*/
export const selModuleMaterialFileUrl = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selModuleMaterialFileUrl",
        changeOrigin: true,
        data: id
    })
}
export const updModuleById = (form) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/updModuleById",
        changeOrigin: true,
        data: form
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
