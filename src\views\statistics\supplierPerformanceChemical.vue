<template>
    <div class='sgs_wrap'>
        <div class='container-fluid'>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-sm-6">
                    <h2 style="margin-top:0">{{$t('dataAnalytics.supplier.supplierChemicalPerf')}}</h2>
                </div>
                <div class="col-sm-3 text-right" v-if="false">
                  <el-select v-model="productLineCode" clearable filterable
                             @change="productLineChange" style="width: 30%;">
                    <el-option v-for="(productLine,index) in productLineCodes"
                               :label="productLine.key"
                               :value="productLine.value"></el-option>
                  </el-select>
                </div>
                <div class="col-sm-6 text-right">
                    <el-date-picker
                        style="float: right; margin-top: -15px; width: 272px;"
                        class="time-select"
                        @change="rangeChange"
                        @focus="handleDateFocus"
                        v-model="valueData"
                        type="monthrange"
                        align="right"
                        unlink-panels
                        range-separator="-"
                        :start-placeholder="$t('datePicker.startTime')"
                        :end-placeholder="$t('datePicker.endTime')"
                        value-format="yyyy-MM-dd"
                        :picker-options="pickerOptionsMonth">
                    </el-date-picker>
                </div>
            </div>
            <div class="row" style="margin-top: 10px">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins" style="padding-bottom: 0px;">
                        <div class="ibox-content" style="padding-bottom: 0px;">

                            <!-- <div class="row">
                                <SliderDate :value-data="valueData" @input="rangeChange"/>
                            </div> -->
                            <div class="row">
                                <div class="col-lg-12">
                                    <div style="overflow-y:auto;margin-bottom: 0px;margin-top: 15px;">
                                        <div id="bar" style="height:500px;" class="col-lg-12">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-bottom: 0px;margin-top: 0px;">
                                <div class="col-lg-12">
                                    <h4>{{$t('dataAnalytics.supplier.supplierChemicalPerfByCountry')}}</h4>
                                </div>
                            </div>
                            <!--<div id="supplierAnylsisOverallDiv" v-html="htmlView2"></div>-->
                            <div class="row">
                                <div class="col-lg-12" v-for="item in countryArray">
                                    <div style="overflow-y:auto;margin-bottom: 20px;margin-top: 15px;">
                                        <div :id="name+'Bar'" style="height:400px;" class="col-lg-12"
                                             v-for="name in item">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <supplier-drawer :visible.sync="visible" :key="visible" :title="this.$t('dataAnalytics.supplier.supplierTestLine')"
                             :optionData="supplierData"></supplier-drawer>
        </div>
    </div>
</template>
<script>

    import * as utils from '@/views/util'
    import i18n from '@/lang'
    import moment from 'moment'
    import {selectSupplierPerformance, selectTestLineBySupplier} from '@/api/statistics/cistatistics'
    import {mapGetters} from 'vuex'
    import {saveLog} from "@/api/common/index";
    import _ from 'lodash';// 导入loadsh

    Vue.prototype.$lodash = _;//注入工具
    export default {
        data() {
            let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD') //moment().format('YYYY-MM-DD');
            return {
                countryArray: [],
                valueData: [startDate, endDate],
                visible: false,
                htmlView2: "",
                supplierData: {
                    yAxis: {
                        type: 'category',
                        data: [],
                        axisLabel: {
                            interval: 0,
                            formatter: function (name) {
                                if (!name) return ''
                                if (name.length > 20) {
                                    name = name.slice(0, 20) + '...'
                                }
                                return name
                            }
                        },
                        triggerEvent: true
                    },
                    series: null
                },
                statisticsData: {},
                queryParam: {
                    categoryName: 'Chemical',
                    startDate: startDate,
                    endDate: endDate,
                    buyerGroupCode: null,
                    productLineCode:null
                },
                pickerOptionsMonth: {
                    shortcuts: [{
                    text: this.$t('datePicker.lastWeek'),
                    onClick(picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                    }, {
                    text: this.$t("datePicker.lastMonth"),
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date();
                      start.setMonth(start.getMonth() - 1);
                      picker.$emit('pick', [start, end]);
                    }
                    }, 
                    // {
                    //   text: '今年至今',
                    //   onClick(picker) {
                    //     const end = new Date();
                    //     const start = new Date(new Date().getFullYear(), 0);
                    //     picker.$emit('pick', [start, end]);
                    //   }
                    // }, 
                    {
                    text: this.$t("datePicker.lastHalfYear"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        picker.$emit('pick', [start, end]);
                    }
                    }, {
                    text: this.$t("datePicker.lastYear"),
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 12);
                        picker.$emit('pick', [start, end]);
                    }
                    }]
                },
                productLineCode:"",
                productLineCodes:[
                  {key:'SL',value:'SL'},
                  {key:'HL',value:'HL'}
                ]
            }
        },
        watch: {
            "$i18n.locale": function () {
                this.rangeChange(this.valueData);
                this.initEchart()
            },
        },
        components: {
            SliderDate: resolve => require(['@/components/statistics/SliderDate'], resolve),
            SupplierDrawer: resolve => require(['@/components/statistics/SupplierDrawer'], resolve),
        },
        computed: {
            ...mapGetters({
                companyType: 'companyType',
                user: 'userInfo',
                language:'language'
            })
        },
        beforeRouteEnter(to, from, next) {
            next(vm => {
                vm.initData()
                vm.saveLog();
            })
        },
        methods: {
            handleDateFocus(e) {
                let nodes = document.querySelectorAll('.el-picker-panel__shortcut')
                let lang = ["datePicker.lastWeek", "datePicker.lastMonth", "datePicker.lastHalfYear", "datePicker.lastYear"]
                nodes.forEach((item, index) => {
                    item.innerText = this.$t(lang[index%4])
                })
            },
             /**
             * 构建成功失败横向柱状图
             */
            initTestPerformanceOption(titleText, yData, yFailValue, yPassValue, ySeeValue, bWidth) {
                return utils.initPerformanceOption(titleText, yData, yFailValue, yPassValue, ySeeValue, bWidth, 11, i18n)
            },
            saveLog() {
                saveLog("supplierChemical").then(res => {

                })
            },
            async rangeChange(data) {
              let startDate = moment().startOf('month').format('YYYY-MM-DD'),
                  endDate = moment().startOf('month').subtract('month', -1).format('YYYY-MM-DD')
              if(data){
                startDate = moment(data[0]).format("YYYY-MM-DD")
                endDate = moment(data[1]).format("YYYY-MM-DD")
              }
                this.$set(this.queryParam, 'startDate', startDate);
                this.$set(this.queryParam, 'endDate', endDate);
                this.valueData = data
                await this.initData();
                /* this.$set(this.queryParam, 'startDate', data[0]+"-01")
                 this.$set(this.queryParam, 'endDate', data[1]+"-31")
                 this.valueData = data
                 await this.initData()*/
            },
            getCurrentMonthFirst(date) {
                var startdate = new Date(date);
                startdate.setDate(1);
                return moment(startdate).format("YYYY-MM-DD");
            },
            //获取所选月份的最后一天
            getCurrentMonthLast(date) {
                var endDate = new Date(date);
                var month = endDate.getMonth();
                var nextMonth = month;
                var nextMonthFirstDay = new Date(endDate.getFullYear(), nextMonth, 1);
                var oneDay = 1000 * 60 * 60 * 24;
                var newEndDate = new Date(nextMonthFirstDay - oneDay);
                return moment(newEndDate).format("YYYY-MM-DD");
            },
            initData() {
                if (!this.$lodash.isEmpty(this.$route.query.customerGroupCode)) {
                    this.queryParam.buyerGroupCode = this.$route.query.customerGroupCode
                    this.queryParam.customerUsage = 'buyer'
                }

                if (this.companyType === 5) {
                    this.queryParam.agentGroupCode = this.user.customer.customerGroupCode
                    this.queryParam.customerUsage = 'agent'
                }

                if (this.companyType === 1) {
                    this.queryParam.buyerGroupCode = this.user.customer.customerGroupCode
                    this.queryParam.customerUsage = 'buyer'
                }

                /*let userInfo=localStorage.getItem("SGS-userInfo")
                let customerGroupCode=JSON.parse(userInfo).content.customerGroupCode;
                if(customerGroupCode) {
                    this.queryParam.buyerGroupCode = customerGroupCode;
                }*/
                this.queryParam.buyerGroupCode = this.user.customerGroupCode;
                this.queryParam.language = this.language;
                let productLineCode=this.user.productLineCode;
                if(productLineCode==='all'){
                  productLineCode='';
                }
                this.queryParam.productLineCode = productLineCode
                let loadData = async () => {
                    /*let {data} = await selectSupplierPerformance(this.queryParam)
                    this.statisticsData = data.result*/
                    selectSupplierPerformance(this.queryParam).then(res => {
                        this.statisticsData = res.data.data;
                        var countryArray = []
                        this.$lodash.forEach(this.statisticsData.resultCountry, item => {
                            if (item.DataCountryName && this.$lodash.indexOf(countryArray, item.DataCountryName) === -1) {
                                countryArray.push(item.DataCountryName)
                            }
                        })
                        let chunkCountryArray = this.$lodash.chunk(countryArray, 4)
                        this.countryArray = chunkCountryArray
                        this.$nextTick(() => {
                            this.initEchart()
                        })
                    });
                }
                loadData().then(() => {
                    this.$nextTick(() => {
                        this.initEchart()
                    })
                })
            },
            initEchart() {
                const vm = this

                // OverAll
                // 全局变量
                var yData = []
                var yRateValue = []
                var yFailData = []
                var ySeeData = []
                vm.$lodash.forEach(this.statisticsData.resultAll, item => {
                    if (item.CustomerNameEN && yData.indexOf(item.CustomerNameEN) == -1) {
                        let pass = this.$lodash.result(this.$lodash.find(this.statisticsData.resultAll, {
                            CustomerNameEN: item.CustomerNameEN,
                            OverallResult: "Pass"
                        }), 'countValue', 0)
                        let fail = this.$lodash.result(this.$lodash.find(this.statisticsData.resultAll, {
                            CustomerNameEN: item.CustomerNameEN,
                            OverallResult: "Fail"
                        }), 'countValue', 0)
                        let see = this.$lodash.result(this.$lodash.find(this.statisticsData.resultAll, {
                            CustomerNameEN: item.CustomerNameEN,
                            OverallResult: "See Result"
                        }), 'countValue', 0)
                        if (pass + fail + see > 0) {
                            yData.push(item.CustomerNameEN);
                        }
                    }
                })
                yData.forEach(item => {
                    let resultAll = this.statisticsData.resultAll
                    let pass = this.$lodash.result(this.$lodash.find(resultAll, {
                        CustomerNameEN: item,
                        OverallResult: "Pass"
                    }), 'countValue', 0)
                    let fail = this.$lodash.result(this.$lodash.find(resultAll, {
                        CustomerNameEN: item,
                        OverallResult: "Fail"
                    }), 'countValue', 0)
                    let see = this.$lodash.result(this.$lodash.find(resultAll, {
                        CustomerNameEN: item,
                        OverallResult: "See Result"
                    }), 'countValue', 0)
                     /*var yPassValueObj = {};
                    yPassValueObj.value = pass;
                    yPassValueObj.groupId = this.$lodash.result(this.$lodash.find(resultAll, {
                        CustomerNameEN: item,
                        OverallResult: "Pass"
                    }), 'applicantCode', null)

                    var yFailValueObj = {};
                    yFailValueObj.value = fail;
                    yFailValueObj.groupId = this.$lodash.result(this.$lodash.find(resultAll, {
                        CustomerNameEN: item,
                        OverallResult: "Fail"
                    }), 'applicantCode', null)

                    var ySeeValueObj = {};
                    ySeeValueObj.value = see;
                    ySeeValueObj.groupId = this.$lodash.result(this.$lodash.find(resultAll, {
                        CustomerNameEN: item,
                        OverallResult: "See Result"
                    }), 'applicantCode', null)
                    yRateValue.push(yPassValueObj)
                    yFailData.push(yFailValueObj);
                    ySeeData.push(ySeeValueObj);*/
                    yRateValue.push(pass)
                    yFailData.push(fail)
                    ySeeData.push(see)
                })
                var overAll = echarts.init(document.getElementById('bar'))
                this.$once('hook:beforeDestroy', function () {
                    // echarts.dispose(overAll)
                    overAll.clear()
                })
                // yData = this.$lodash.sortBy(yData)

                let containerHeight = 500;
                if (yData.length > 35) {
                    containerHeight = yData.length * 15
                }

                //$('#bar').css("height",containerHeight)

                let packageData = utils.packageData(yData, yRateValue, yFailData, ySeeData)

                var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.supplier.supplierChemicalPerf'), vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 6, 3)


                overAll.setOption(overAllOption)

                overAll.off('click')
                overAll.on('click', function (params) {
                      var customerName= params.name;
                    // var applicantCode = vm.$lodash.result(vm.$lodash.find( vm.statisticsData.resultAll, {
                    //     CustomerNameEN: customerName
                    // }), 'applicantCode', null)
                    vm.initTestLineData(
                        {
                            "agentGroupCode": null,
                            //"buyerGroupCode": vm.$lodash.get(vm.user, 'customer.customerGroupCode'),
                            "buyerGroupCode": vm.queryParam.buyerGroupCode,
                            "countryFieldCode": null,
                            "startDate": vm.queryParam.startDate,
                            "endDate": vm.queryParam.endDate,
                            "categoryName": vm.queryParam.categoryName,
                            "supplierName": params.name,
                            //"applicantCode":  applicantCode,
                            "countryValue": null,
                            "language":vm.language,
                            "productLineCode":vm.productLineCode
                        }
                    )
                    vm.visible = true
                });

                overAll.off('legendselectchanged')
                overAll.on('legendselectchanged', function (params) {
                    const PASS = params.selected.PASS
                    const FAIL = params.selected.FAIL
                    const SEE = params.selected['SEE RESULT']
                    const ALL = FAIL && PASS && SEE
                    if (ALL) {
                        packageData = vm.$lodash.orderBy(packageData, 'all', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.supplier.overall'), vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    } else if (PASS && !FAIL && !SEE) {
                        packageData = vm.$lodash.orderBy(packageData, 'pass', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.supplier.overall'), vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    } else if (!PASS && FAIL && !SEE) {
                        packageData = vm.$lodash.orderBy(packageData, 'fail', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.supplier.overall'), vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    } else if (!PASS && !FAIL && SEE) {
                        packageData = vm.$lodash.orderBy(packageData, 'see', 'asc')
                        var overAllOption = this.initTestPerformanceOption(this.$t('dataAnalytics.supplier.overall'), vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                        overAll.setOption(overAllOption)
                    }
                }, overAll);

                var countryData = []
                vm.$lodash.forEach(vm.statisticsData.resultCountry, item => {
                    if (item.DataCountryName && countryData.indexOf(item.DataCountryName) == -1) {
                        var failCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            DataCountryName: item.DataCountryName,
                            OverallResult: 'Fail'
                        }), 'countValue', 0)
                        var passCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            DataCountryName: item.DataCountryName,
                            OverallResult: 'Pass'
                        }), 'countValue', 0)
                        var seeCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            DataCountryName: item.DataCountryName,
                            OverallResult: 'See Result'
                        }), 'countValue', 0)
                        if (passCount + failCount + seeCount > 0) {
                            countryData.push(item.DataCountryName);
                        }
                    }
                })
                this.countryArray = countryData;
                /*  let containerHeight_country = 500;
                  if (countryData.length > 35) {
                    containerHeight_country = countryData.length * 15
                  }

                  // 初始化需要显示国家的层
                  var countries = countryData.map(item =>{
                    return utils.countryDiv(item, containerHeight_country)
                  })

                  this.htmlView2 = utils.initCountryDiv(countries)*/
                //$('#supplierAnylsisOverallDiv').html(utils.initCountryDiv(countries))
                // China

                this.countryArray.forEach(item => {
                    var yChinaData = []
                    vm.$lodash.filter(vm.statisticsData.resultCountry, {DataCountryName: item}).forEach(countryItem => {
                        if (countryItem.CustomerNameEN && vm.$lodash.indexOf(yChinaData, countryItem.CustomerNameEN) === -1) {
                            var failCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                                CustomerNameEN: countryItem.CustomerNameEN,
                                DataCountryName: item,
                                OverallResult: 'Fail'
                            }), 'countValue', 0)
                            var passCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                                CustomerNameEN: countryItem.CustomerNameEN,
                                DataCountryName: item,
                                OverallResult: 'Pass'
                            }), 'countValue', 0)
                            var seeCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                                CustomerNameEN: countryItem.CustomerNameEN,
                                DataCountryName: item,
                                OverallResult: 'See Result'
                            }), 'countValue', 0)
                            if (passCount + failCount + seeCount > 0) {
                                yChinaData.push(countryItem.CustomerNameEN);
                            }
                        }
                    })

                    var yChinaFailValue = []
                    var yChinaRateValue = []
                    var yChinaSeeValue = []

                    vm.$lodash.forEach(yChinaData, chinaItem => {
                        var failCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            CustomerNameEN: chinaItem,
                            DataCountryName: item,
                            OverallResult: 'Fail'
                        }), 'countValue', 0)
                        var passCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            CustomerNameEN: chinaItem,
                            DataCountryName: item,
                            OverallResult: 'Pass'
                        }), 'countValue', 0)
                        var seeCount = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            CustomerNameEN: chinaItem,
                            DataCountryName: item,
                            OverallResult: 'See Result'
                        }), 'countValue', 0)
                        /*var yChinaPassValueObj = {};
                        yChinaPassValueObj.value = passCount;
                        yChinaPassValueObj.groupId = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            CustomerNameEN: chinaItem,
                            DataCountryName: item,
                            OverallResult: 'Pass'
                        }), 'applicantCode', null)
                        yChinaRateValue.push(yChinaPassValueObj)

                        var yChinaFailValueObj = {};
                        yChinaFailValueObj.value = failCount;
                        yChinaFailValueObj.groupId = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            CustomerNameEN: chinaItem,
                            DataCountryName: item,
                            OverallResult: 'Fail'
                        }), 'applicantCode', null)
                        yChinaFailValue.push(yChinaFailValueObj)

                        var yChinaSeeValueObj = {};
                        yChinaSeeValueObj.value = seeCount;
                        yChinaSeeValueObj.groupId = vm.$lodash.result(vm.$lodash.find(vm.statisticsData.resultCountry, {
                            CustomerNameEN: chinaItem,
                            DataCountryName: item,
                            OverallResult: 'See Result'
                        }), 'applicantCode', null)
                        yChinaSeeValue.push(yChinaSeeValueObj)*/

                        yChinaRateValue.push(passCount)
                        yChinaFailValue.push(failCount)
                        yChinaSeeValue.push(seeCount)
                    })
                    var yChinaDatatemp = yChinaData
                    // yChinaDatatemp = vm.$lodash.sortBy(yChinaData)

                    var ChinaChart = echarts.init(document.getElementById(item + 'Bar'))
                    this.$once('hook:beforeDestroy', function () {
                        // echarts.dispose(ChinaChart)
                        ChinaChart.clear()
                    })

                    let china_containerHeight = 500;
                    if (yChinaDatatemp.length > 35) {
                        china_containerHeight = yChinaDatatemp.length * 15
                    }
                    //$('#'+item+'Bar').css("height",china_containerHeight)

                    let packageData = utils.packageData(yChinaDatatemp, yChinaRateValue, yChinaFailValue, yChinaSeeValue)

                    var ChinaOption = this.initTestPerformanceOption(item +' '+ this.$t('dataAnalytics.supplier.title')+' ', vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 6)

                    ChinaChart.setOption(ChinaOption)

                    ChinaChart.off('click')
                    ChinaChart.on('click', param => {
                        debugger;
                        var customerName = param.name;
                        // var applicantCode = vm.$lodash.result(vm.$lodash.find( vm.statisticsData.resultCountry, {
                        // CustomerNameEN: customerName
                        // }), 'applicantCode', null)
                        vm.initTestLineData(
                            {
                                "agentGroupCode": null,
                                "buyerGroupCode": vm.$lodash.get(vm.user, 'customerGroupCode'),
                                "countryFieldCode": null,
                                "startDate": vm.queryParam.startDate,
                                "endDate": vm.queryParam.endDate,
                                "categoryName": vm.queryParam.categoryName,
                                "supplierName": param.name,
                                //"applicantCode": applicantCode,
                                "countryValue": item,
                                "language":vm.language,
                                "productLineCode":vm.productLineCode
                            }
                        )
                        vm.visible = true
                    })

                    ChinaChart.off('legendselectchanged')
                    ChinaChart.on('legendselectchanged', function (params) {
                        const PASS = params.selected.PASS
                        const FAIL = params.selected.FAIL
                        const SEE = params.selected['SEE RESULT']
                        const ALL = FAIL && PASS && SEE
                        if (ALL) {
                            packageData = vm.$lodash.orderBy(packageData, 'all', 'asc')
                            var overAllOption = this.initTestPerformanceOption(item +' '+ this.$t('dataAnalytics.supplier.title')+' ', vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                            ChinaChart.setOption(overAllOption)
                        } else if (PASS && !FAIL && !SEE) {
                            packageData = vm.$lodash.orderBy(packageData, 'pass', 'asc')
                            var overAllOption = this.initTestPerformanceOption(item +' '+ this.$t('dataAnalytics.supplier.title')+' ', vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                            ChinaChart.setOption(overAllOption)
                        } else if (!PASS && FAIL && !SEE) {
                            packageData = vm.$lodash.orderBy(packageData, 'fail', 'asc')
                            var overAllOption = this.initTestPerformanceOption(item +' '+ this.$t('dataAnalytics.supplier.title')+' ', vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                            ChinaChart.setOption(overAllOption)
                        } else if (!PASS && !FAIL && SEE) {
                            packageData = vm.$lodash.orderBy(packageData, 'see', 'asc')
                            var overAllOption = this.initTestPerformanceOption(item +' '+ this.$t('dataAnalytics.supplier.title')+' ', vm.$lodash.map(packageData, 'name'), vm.$lodash.map(packageData, 'fail'), vm.$lodash.map(packageData, 'pass'), vm.$lodash.map(packageData, 'see'), 10)
                            ChinaChart.setOption(overAllOption)
                        }
                    }, ChinaChart);

                })
            },
            initTestLineData(param) {
                const vm = this
                const _ = this.$lodash
                selectTestLineBySupplier(param).then((res) => {
                    if (res.data.data) {
                        let result = res.data.data || []

                        let testLineEvaluations = _.uniq(result.map(item => item.testLineEvaluation))
                        let testLineEvaluationsOrder = testLineEvaluations.map(item => {
                            let pass = _.get(_.find(result, {
                                testLineEvaluation: item,
                                conclusion: 'Pass'
                            }), 'countValue', 0)
                            let fail = _.get(_.find(result, {
                                testLineEvaluation: item,
                                conclusion: 'Fail'
                            }), 'countValue', 0)
                            let seeResult = _.get(_.find(result, {
                                testLineEvaluation: item,
                                conclusion: 'See Result'
                            }), 'countValue', 0)
                            return {
                                testLineEvaluation: item,
                                order: seeResult + pass + fail
                            }
                        })
                        testLineEvaluations = _.orderBy(testLineEvaluationsOrder, ['order'], ['asc']).map(item => item.testLineEvaluation)
                        _.set(vm.supplierData, 'yAxis.data', testLineEvaluations)
                        vm.supplierData.series = [
                            {
                                name: this.$t('dataAnalytics.pass'),
                                type: 'bar',
                                stack: 'Total',
                                barWidth: 10,
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'insideRight',
                                        formatter: function (num) {
                                            if (num.value > 0) {
                                                return num.value
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                itemStyle: {
                                    normal: {
                                        color: '#92D050'
                                    }
                                },
                                data: testLineEvaluations.map(item => {
                                    let data = _.find(result, {testLineEvaluation: item, conclusion: 'Pass'}) || {}
                                    return data.countValue || 0
                                })
                            },
                            {
                                name: this.$t('dataAnalytics.fail'),
                                type: 'bar',
                                stack: 'Total',
                                barWidth: 10,
                                itemStyle: {
                                    normal: {
                                        color: '#FF0000'
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'insideRight',
                                        formatter: function (num) {
                                            if (num.value > 0) {
                                                return num.value
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                data: testLineEvaluations.map(item => {
                                    let data = _.find(result, {testLineEvaluation: item, conclusion: 'Fail'}) || {}
                                    return data.countValue || 0
                                })
                            },
                            {
                                name: this.$t('dataAnalytics.seeResult'),
                                type: 'bar',
                                stack: 'Total',
                                barWidth: 10,
                                itemStyle: {
                                    normal: {
                                        color: '#adadad'
                                    }
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'insideRight',
                                        formatter: function (num) {
                                            if (num.value > 0) {
                                                return num.value
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                data: testLineEvaluations.map(item => {
                                    let data = _.find(result, {
                                        testLineEvaluation: item,
                                        conclusion: 'See Result'
                                    }) || {}
                                    return data.countValue || 0
                                })
                            },
                        ]
                    }
                }).catch(error => {
                })
            },
          async productLineChange(value){
            await this.initData();
          }
        },
        mounted() {
        }
    }
</script>
