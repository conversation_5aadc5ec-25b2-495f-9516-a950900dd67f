<template>
  <div class="TableHeaderFilterSave" id="TableHeaderFilterSave">
    <el-row>
      <el-col style="text-align: right" :span="24">
        <el-popover ref="saveFilterTableList" title="" placement="bottom" width="400" trigger="click">
          <el-table
            :data="filterData"
            style="width: 100%"
            :max-height="600"
            :show-header="false"
            size="mini"
            :cell-class-name="cellClassName"
            @row-click="filterTableRowClick"
          >
            <el-table-column prop="name">
                <template slot-scope="{row}">
                    <span style="cursor: pointer">{{row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column align="right" width="100" prop="id">
              <template slot-scope="{ row }">
                <el-button type="text" @click="setFilterDefault(row)">
                  {{ row.defaultFlag - 0 == 1 ? "" : $t("trfList.filter.set") }}
                  {{ $t("trfList.filter.default") }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column width="80" prop="id">
              <template slot-scope="{ row }">
                <el-popconfirm
                  confirm-button-text="Submit"
                  cancel-button-text="Cancel"
                  icon="el-icon-info"
                  icon-color="red"
                  title="Delete the filter?"
                  @confirm="deleteFilter(row.id)"
                >
                  <el-button v-if="row.self" slot="reference" type="text">{{
                    $t("trfList.filter.delete")
                  }}</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <template slot="reference">
            <div class="filter_select_btn">
              <el-input
                style="width: 100%"
                :size="size || 'medium'"
                readonly
                v-model="filterClickName"
                type="text"
                :placeholder="$t('trfList.filter.filterPlaceHolder')"
                @clear="clearFilterObj"
              ></el-input>
              <u
                v-if="filterClickName"
                style="
                  position: absolute;
                  right: 10px;
                  line-height: 40px;
                  cursor: pointer;
                "
              >
                <i class="el-icon-circle-close" @click="clearFilterObj"></i>
              </u>
            </div>
          </template>
        </el-popover>
        <el-popover
          :title="$t('trfList.filter.saveFilter')"
          placement="bottom"
          ref="saveFilterPopover"
          width="300"
          @show="showSaveFilter"
          trigger="click"
        >
          <el-form
            ref="filterSaveForm"
            @submit.native.prevent
            :model="filterObj"
            label-width="20px"
            size="mini"
          >
            <el-form-item prop="replaceOrNew">
              <el-radio
                v-model="filterObj.replaceOrNew"
                v-if="filterObj.self"
                label="0"
                @change="changeSaveType"
                >{{ $t("trfList.filter.replaceAnExisting") }}</el-radio
              >
              <el-radio
                v-model="filterObj.replaceOrNew"
                label="1"
                @change="changeSaveType"
                >{{ $t("trfList.filter.saveAsNew") }}</el-radio
              >
            </el-form-item>
            <el-form-item
              prop="name"
              required
              label=" "
              :rules="[
                {
                  required: true,
                  message: 'Name is required',
                  trigger: ['change', 'blur'],
                },
              ]"
            >
              <el-input
                type="text"
                :size="size || 'medium'"
                maxlength="50"
                show-word-limit
                v-model="filterObj.name"
              ></el-input>
            </el-form-item>
            <el-form-item
              prop="publicType"
            >
              <el-radio-group v-model="filterObj.publicType" size="medium">
                <el-radio label="0">{{
                  $t("trfList.filter.saveAsLocal")
                }}</el-radio>
                <el-radio label="1">{{
                  $t("trfList.filter.saveAsPublic")
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
              <el-form-item prop="defaultFlag">
                  <el-checkbox v-model="filterObj.defaultFlag" :true-label="1" :false-label="0"> {{ $t("trfList.filter.saveAsDefault") }}</el-checkbox>
              </el-form-item>
            <el-form-item style="text-align: center">
              <el-button type="primary" :size="size || 'small'" @click="save">{{
                $t("trfList.filter.save")
              }}</el-button>
            </el-form-item>
          </el-form>
          <el-button
            slot="reference"
            type="primary"
            class="filter_save_btn"
            :size="size || 'small'"
          >
            {{ $t("trfList.filter.saveFilter") }}
          </el-button>
        </el-popover>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import api from "./api";

export default {
  name: "",
  inject: ["size", "tableObj", "filterConfigObj", "resetQueryForm"],
  data() {
    return {
      filterObj: {
        id: "",
        replaceOrNew: "0",
        publicType: "0",
        name: "",
        content: {},
        defaultFlag:0,
        self:true,
      },
      filterClickName: "",
      filterData: [],
    };
  },
  methods: {
    filterTableRowClick(row, column, event) {
      let { property } = column;
      if ("id" == property) {
        return;
      }
      let { name, id } = row;
      let { currentId } = this.filterConfigObj.getFilterContentObj();
      if (id == currentId) {
        return;
      }
      this.filterClickName = name;
      this.filterConfigObj.changeFilter(row.id);
      this.$refs.saveFilterTableList.handleBlur();
    },
    //DB交互
    setFilterDefault(row) {
      if (row.defaultFlag - 0 == 1) {
        return;
      }
      api.filterSetDefault(row.id)
        .then(
          (res) => {
            this.$notify.success("Success");
            this.filterConfigObj.updateFilterDefault(row.id);
            this.initFilterDataAndName();
          },
          (error) => {
            console.log("set detault error", error);
          }
        ).catch((err) => {
          console.log("set detault catch", err);
          this.$notify.error("Set default error");
        });
    },
    deleteFilter(id) {
      //console.log("delete id",id)
      api
        .filterDelete(id)
        .then((res) => {
          this.$notify.success("Delete Success");
          this.filterConfigObj.delFilterConfig(id);
          this.initFilterDataAndName();
        })
        .catch((err) => {
          this.$notify.error(this.$t("api.error"));
          console.log("delete filter error", err);
        });
    },
    changeSaveType() {
      //console.log("filterObj.replaceOrNew",this.filterObj.replaceOrNew)
      this.filterObj.publicType = "0";
      this.filterObj.content = "";
      this.filterObj.defaultFlag = 0;
      if (this.filterObj.replaceOrNew - 0 == 1) {
        this.filterObj.id = "";
        this.filterObj.name = "";
      } else {
        let { currentConfig } = this.filterConfigObj.getFilterContentObj();
          let { id, name,publicType,defaultFlag } = currentConfig;
          this.filterObj.id = id;
          this.filterObj.name = name;
          this.filterObj.publicType = publicType+'';
          this.filterObj.defaultFlag = defaultFlag;
      }
    },
    save() {
      this.$refs.filterSaveForm.validate((valid) => {
        if (!valid) {
          return;
        }
        let columns = this.tableObj.getColumns();
        let cols = [];
        (columns || []).forEach((col, seq) => {
          let { hide, prop } = col;
          cols.push({ hide, prop, seq });
        });
        let queryModal = this.tableObj.getQueryModal();
        //组装filter 保存数据
        let content = JSON.stringify({
          filter: queryModal,
          columns: cols,
        });
        let { id } = this.filterObj;
        let create = !id;
        let param = Object.assign(
          {},
          this.filterObj,
          { content },
          { sourceType: this.sourceType }
        );
        api
          .filterSave(param)
          .then((res) => {
            //console.log("saveFilter",res)

              this.$refs.saveFilterPopover.handleBlur();
            this.$notify.success(this.$t("api.successfully"));
            let { data } = res.data;
            if (create) {
              this.filterConfigObj.addFilterConfig(data);
            } else {
              this.filterConfigObj.updFilterConfig(data);
            }
            //重新加载table list
            this.initFilterDataAndName();
          })
          .catch((err) => {
            console.log("saveFilter err", err);
            this.$notify.error(this.$t("api.error"));
          });
      });
    },
    cellClassName({ row }) {
      let { id } = row;
      let { currentId } = this.filterConfigObj.getFilterContentObj();
      if (id == currentId) {
        return "select_filter_cell";
      }
    },
    showSaveFilter() {
      let { currentConfig } = this.filterConfigObj.getFilterContentObj();
      if (!currentConfig) {
        this.filterObj.id = "";
        this.filterObj.name = "";
        return;
      }
      let { id, name,publicType,defaultFlag,self } = currentConfig;
      this.filterObj.replaceOrNew = self?"0":"1";
      this.filterObj.publicType = "0";
      this.filterObj.content = "";
      this.filterObj.id = self?id:'';
      this.filterObj.self = self;
      this.filterObj.name = name;
      this.filterObj.defaultFlag = defaultFlag;
    },
    matchUrlParam(param){
        let queryString = window.location.hash;
        var reg = new RegExp("(^|&)"+ param +"=([^&]*)(&|$)");
        var r = queryString.match(reg);
        if(r){
            return unescape(r[2]);
        }
        return '';
    },
    initFilterDataAndName() {
      this.$nextTick(() => {
        let { filterList, currentConfig } = this.filterConfigObj.getFilterContentObj();
        this.filterData = filterList || [];
        let cacheName = "";
        if (this.sourceType == "TRF") {
          let cacheFilterId = this.matchUrlParam('filterId');// this.$route.query.filterId;
          try {
            let cacheFilter = (filterList || []).find((f) => f.id == cacheFilterId) || {};
            cacheName = cacheFilter.name;
            this.filterConfigObj.updFilterConfig(cacheFilter);
          } catch (e) {}
        }
        let { name } = currentConfig || {};
        this.filterClickName = name || cacheName || "";
        //console.log("保存default 被触发 名字是",name)
      });
    },
    clearFilterObj() {
      this.$set(this, "filterClickName", "");
      this.resetQueryForm();
    },
  },
  mounted() {
    this.initFilterDataAndName();
  },
  created() {},
  watch: {},
  computed: {},
  props: {
    sourceType: {
      type: String,
      default: "TRF",
    },
  },
  components: {},
};
</script>

<style lang="scss">
@import "@/styles/unit.scss";

.TableHeaderFilterSave {
  width: 348px;
  .select_filter_cell.el-table__cell {
    color: $primary-color !important;
  }
  .el-input__inner {
    padding-left: 0px;
    padding-right: 40px;
    cursor: pointer;
  }
  .filter_save_btn {
    background: #ffffff;
    color: $primary-color;
    border-color: $primary-color;
    margin-left: 6px;
  }
  .filter_select_btn {
    position: relative;
    width: 232px;
    display: inline-block;
  }
}
</style>
