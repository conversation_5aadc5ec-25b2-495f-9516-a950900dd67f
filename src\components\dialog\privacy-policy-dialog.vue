<template>
    <el-main>
        <el-dialog :visible="clauseVisible" width="70%" top="10vh"
                   :show-close="false">
            <div class="modal-body second el-dialog-div">
                <img src="/img/logo.png" style="width: 200px"/>
                <div class="right">
                    <top-lang ref="languageRef1" :loadMenu="false" :is-load-lanage-by-ip="false"></top-lang>
                </div>
                <h3 style="font-weight: 600">{{$t('agreement.useConditions')}}</h3>
                <br/>
                <br/>

                <!--<p><a target="_blank" style="color: #ff6600" href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
                </p>-->
                {{$t('agreement.head')}}
                <h3> {{$t('agreement.definition')}}</h3>
                <p> {{$t('agreement.one_1')}}</p>
                <p> {{$t('agreement.one_2')}}
                <p>
                <h3> {{$t('agreement.accountRegisterAndUse_2')}}</h3>
                <p> {{$t('agreement.two_1')}}</p>
                <p> {{$t('agreement.two_2')}}
                <p>
                <p> {{$t('agreement.two_3')}}</p>
                <p> {{$t('agreement.two_4')}}
                <p>
                <p> {{$t('agreement.two_5')}}</p>
                <p> {{$t('agreement.two_6')}}
                <p>
                <h3> {{$t('agreement.userInfoProtection_3')}}</h3>
                <p> {{$t('agreement.three_1')}}</p>
                <p> {{$t('agreement.three_2')}}
                <p>
                <p> {{$t('agreement.three_3')}}</p>
                <h3> {{$t('agreement.rightsAndObligations_4')}}</h3>
                <p> {{$t('agreement.four_1')}}</p>
                <p> {{$t('agreement.four_2')}}
                <p>
                <p> {{$t('agreement.four_3')}}</p>
                <p> {{$t('agreement.four_4')}}</p>
                <p> {{$t('agreement.four_5')}}
                <p>
                <p> {{$t('agreement.four_6')}}</p>
                <p> {{$t('agreement.four_6_1')}}</p>
                <p> {{$t('agreement.four_6_2')}}</p>
                <p> {{$t('agreement.four_6_3')}}</p>
                <p> {{$t('agreement.four_6_4')}}</p>
                <p> {{$t('agreement.four_6_5')}}</p>
                <p> {{$t('agreement.four_6_6')}}</p>
                <p> {{$t('agreement.four_6_7')}}</p>
                <p> {{$t('agreement.four_6_8')}}</p>
                <p> {{$t('agreement.four_6_9')}}</p>
                <p> {{$t('agreement.four_7')}}</p>
                <p> {{$t('agreement.four_7_1')}}</p>
                <p> {{$t('agreement.four_7_2')}}</p>
                <p> {{$t('agreement.four_7_3')}}</p>
                <p> {{$t('agreement.four_7_4')}}</p>
                <p> {{$t('agreement.four_7_5')}}</p>
                <p> {{$t('agreement.four_8')}}
                <p>
                <p> {{$t('agreement.four_8_1')}}
                <p>
                <p> {{$t('agreement.four_8_2')}}
                <p>
                <p> {{$t('agreement.four_8_3')}}
                <p>
                <p> {{$t('agreement.four_8_4')}}
                <p>
                <p> {{$t('agreement.four_8_5')}}
                <p>
                <p> {{$t('agreement.four_8_6')}}
                <p>
                <p> {{$t('agreement.four_8_7')}}
                <p>
                <p> {{$t('agreement.four_8_8')}}
                <p>
                <p> {{$t('agreement.four_9')}}</p>
                <p> {{$t('agreement.four_10')}}</p>
                <p> {{$t('agreement.sgsRightsAndDuties_5')}}</p>
                <p> {{$t('agreement.five_1')}}</p>
                <p> {{$t('agreement.five_2')}}</p>
                <p> {{$t('agreement.five_3')}}</p>
                <p> {{$t('agreement.five_4')}}</p>
                <p> {{$t('agreement.five_5')}}</p>
                <p> {{$t('agreement.intellectualProperty_6')}}</p>
                <p> {{$t('agreement.six_1')}}</p>
                <p> {{$t('agreement.six_2')}}</p>
                <p> {{$t('agreement.six_3')}}</p>
                <p> {{$t('agreement.six_4')}}</p>
                <p> {{$t('agreement.six_5')}}</p>
                <p> {{$t('agreement.six_6')}}</p>
                <p> {{$t('agreement.privacy_7')}}</p>
                <p> {{$t('agreement.seven_1')}}</p>
                <p> {{$t('agreement.seven_2')}}</p>
                <p> {{$t('agreement.seven_3')}}</p>
                <p> {{$t('agreement.seven_3_1')}}</p>
                <p> {{$t('agreement.seven_3_2')}}</p>
                <p> {{$t('agreement.seven_3_3')}}</p>
                <p> {{$t('agreement.seven_3_4')}}</p>
                <p> {{$t('agreement.seven_4')}}</p>
                <p> {{$t('agreement.seven_5')}}</p>
                <p> {{$t('agreement.legalResponsibility_8')}}</p>
                <p> {{$t('agreement.eight_1')}}</p>
                <p> {{$t('agreement.eight_2')}}</p>
                <p> {{$t('agreement.eight_3')}}</p>
                <p> {{$t('agreement.eight_4')}}</p>
                <p> {{$t('agreement.eight_5')}}</p>
                <p> {{$t('agreement.eight_6')}}</p>
                <p> {{$t('agreement.otherAgreements_9')}}</p>
                <p> {{$t('agreement.nine_1')}}</p>
                <p> {{$t('agreement.nine_2')}}</p>
                <p> {{$t('agreement.nine_3')}}</p>
                <p> {{$t('agreement.nine_4')}}</p>
                <p> {{$t('agreement.nine_5')}}</p>
                <p> {{$t('agreement.nine_6')}}</p>
            </div>
            <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
                <el-button type="primary" @click="clauseVisible = false">Agree and continue</el-button>
            </div>
        </el-dialog>
    </el-main>
</template>


<script>
    import {mapGetters} from "vuex";

    export default {
        components: {
            TopLang: resolve => require(['../../page/index/top/top-lang'], resolve)
        },
        name: "privacy-policy-dialog",
        props: ['clauseVisible', 'dialogLanguage'],
        data() {
            return {};
        },
        computed: {
            ...mapGetters(["language"]),
        },
        watch: {
            clauseVisible: function (newVal) {
                debugger;
            },
            dialogLanguage: function (newVal) {
                debugger;
            },
        },
        created() {
        },
        mounted() {
            /*  var language = this.dialogLanguage;
                //获取中英文
                if (validatenull(language)) {
                    language = 'en-US'
                }
                this.$i18n.locale = language;
                this.$store.commit("SET_LANGUAGE", language);
                this.$refs.languageRef1.changeLanguage(language);*/
        },
        methods: {
            closePrivacy() {
                this.visible = false;
            }
        }
    };
</script>

<style>
    .second p {
        text-indent: 2em;
    }

    .modal-body {
        overflow: auto;
        height: 600px;
    }

</style>
