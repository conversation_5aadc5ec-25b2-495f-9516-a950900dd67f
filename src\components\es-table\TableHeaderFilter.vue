<template>
    <div id="TableHeaderFilter" class="TableHeaderFilter">
        <el-form
                label-width="120px"
                ref="sgs_form_search_form"
                inline
                :model="queryModel.general"
                :size="size || 'medium'">
            <table-header-general-filter
                :form-data="queryModel.general"
                @moreSearch="handlerMoreSearch"
            ></table-header-general-filter>
        </el-form>
        <!--<el-collapse-transition>
            <div v-show="showCollapseFilter">
                <el-form
                        class="transition-box"
                        label-width="120px"
                        ref="sgs_form_search_dynamic_form"
                        label-position="left"
                        :model="queryModel.dynamicForm"
                        :size="size || 'medium'">
                    <collapse-filter
                            :form-data="queryModel.dynamicForm"
                            :filter-config="filterConfig"
                    >
                    </collapse-filter>
                </el-form>
            </div>
        </el-collapse-transition>-->
    </div>
</template>

<script>
    import tableHeaderGeneralFilter from './TableHeaderGeneralFilter'
    import filterSave from './TableHeaderFilterSave'
    import collapseFilter from './TableHeaderCollapseFilter'
    export default {
        name: "TableHeaderFilter",
        inject:['loadFilterSuccess','searchSubmit'],
        data() {
            return {
                showCollapseFilter:false,
            }
        },
        methods: {
            handlerMoreSearch() {
                this.showCollapseFilter=!this.showCollapseFilter;
                this.queryModel.general.showCollapseFilter = this.showCollapseFilter;
            }
        },
        created() {
            this.showCollapseFilter = this.queryModel.general.showCollapseFilter;
        },
        mounted() {
            this.loadFilterSuccess();
        },
        props:{
            size:0,
            queryModel: {
                type:Object,
                default:()=>{
                    return {
                        general:{},
                        dynamicForm:{}
                    }
                }
            },
            filterConfig:{
                type:Array,
                default:()=>{
                    return []
                }
            }

        },
        components: {tableHeaderGeneralFilter,collapseFilter,filterSave}
    }
</script>

<style scoped >
    .TableHeaderFilter {
    }

</style>