<template>
    <div class="customer-container">
        <el-form :model="form" label-position="left" label-width="200px" class="sgs-form" ref="form" :rules="rules"
                 size="medium">
            <el-row :gutter="20">
                <el-col :span="12" offset="6">
                    <el-form-item :label="$t('customer.name')">
                        <el-input maxlength="100" v-model="form.customer.customerNameZh" disabled="true"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12" offset="6">
                    <el-form-item :label="$t('customer.taxNo')">
                        <el-input maxlength="50" v-model="form.customer.taxNo" disabled="true"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <!--<el-col span="12">-->
            <!--<el-form-item :label="$t('customer.nameEn')">-->
            <!--<el-input v-model="form.customer.customerNameEn"></el-input>-->
            <!--</el-form-item>-->
            <!--</el-col>-->
            <el-row :gutter="20">
                <el-col :span="12" offset="6">
                    <el-form-item :label="$t('customer.contactMobile')">
                        <el-input maxlength="20" v-model="form.customer.contactMobile" disabled="true"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12" offset="6">
                    <el-form-item :label="$t('customer.address')" prop="customer.customerAddressZh"
                                  :rules="{required:true,message:this.$t('register.addressBlur'),trigger:'blur',pattern: '[^ \x22]+' }">
                        <el-input maxlength="100" v-model="form.customer.customerAddressZh"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <!--<el-col span="12">
                <el-form-item :label="$t('customer.sgs.customerNo')">
                    <el-input maxlength="30" v-model="form.customer.bossNo"></el-input>
                </el-form-item>
            </el-col>
            <el-col span="12">
                <el-form-item :label="$t('customer.sgs.reportNo')">
                    <el-input maxlength="100" v-model="form.customer.reportNo"></el-input>
                </el-form-item>
            </el-col>-->
            <!--<el-row :gutter="20">
                <el-col span="12" offset="6">
                    <el-form-item :label="$t('customer.certificateNew')">
                        <el-image
                                style="width: 100px;"
                                :src="qualificationUrl"
                                :preview-src-list="[qualificationUrl]">
                        </el-image>
                    </el-form-item>
                </el-col>
            </el-row>-->
            <el-row :gutter="20">
                <el-col span="12" offset="6">
                    <el-form-item :label="$t('customer.certificate')">
                        <el-upload action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                   class="avatar-uploader"
                                   drag
                                   :show-file-list="false"
                                   :on-success="uploadSuccess"
                                   :disabled="form.customer.approveStatus==90 ||form.customer.approveStatus==1 ">

                            <img v-if="form.customer.qualificationUrl" :src="form.customer.qualificationUrl"
                                 style="width: 100%;height: 100%;">
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col span="12" offset="6">
                    <el-form-item :label="$t('register.serveType')" prop="customer.productLineCode">
                        <el-select v-model="form.customer.productLineCode" clearable
                                   @change="selectServiceTypeChange"
                                   style="width: 100%;"
                                   :disabled="form.customer.approveStatus==90 ||form.customer.approveStatus==1 "

                        >
                            <el-option v-for="(serviceType,index) in serviceTypeData"
                                       :label="serviceType.serviceName"
                                       :value="serviceType.productLineCode"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col span="12" offset="6">
                    <el-form-item :label="$t('customer.authenticationStatus')">
                        <span v-if="form.customer.approveStatus==10">{{$t('customer.approveStatus.emailConfirm')}}</span>
                        <span v-if="form.customer.approveStatus==1">{{$t('customer.approveStatus.todo')}}</span>
                        <span v-if="form.customer.approveStatus==80">{{$t('customer.approveStatus.refused')}}</span>
                        <span v-if="form.customer.approveStatus==90">{{$t('customer.approveStatus.approved')}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" v-if="form.customer.approveStatus==80">
                <el-col span="12" offset="6">
                    <el-form-item :label="$t('customer.rejectionReasons')">
                        <span>{{form.customer.refuseReason}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col span="12" offset="22">
                    <el-button v-if="form.customer.approveStatus==10 || form.customer.approveStatus==80" size="small"
                               type="primary"
                               @click="authentication()">{{$t('register.authentication')}}
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
    import {detailForCurrentUser, authenticationCustomer} from "@/api/customer/customerRegister";
    import {getCloudFileURL, serviceTypeList} from "@/api/common/index";
    import {add} from "@/api/customer/customer";
    import {validatenull} from "../../util/validate";
    import {mapGetters} from "vuex";

    export default {
        name: "detail",
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        data() {
            return {
                serviceTypeData: [],
                form: {
                    customer: {
                        qualificationUrl: '',
                    },
                },
                qualificationUrl: '',
                rules: {
                    'customer.productLineCode': [
                        {required: true, message: this.$t('register.serveTypeBlur'), trigger: 'change'},
                    ],
                }
            };
        },
        computed: {
            ...mapGetters(["language"]),
        },
        watch: {
            'language': function () {
                this.queryServiceType();
            }
        },
        methods: {
            selectServiceTypeChange(val) {
                let obj = {};
                obj = this.serviceTypeData.find((item) => {
                    return item.productLineCode === val;
                });
                if (!validatenull(obj)) {
                    this.$set(this.form.customer, 'serviceName', obj.serviceName);
                } else {
                    this.$set(this.form.customer, 'serviceName', '');
                }
            },
            queryServiceType() {
                serviceTypeList(this.language).then(res => {
                    const data = res.data.data;
                    this.serviceTypeData = data;
                });
            },
            authentication() {
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        authenticationCustomer(this.form).then(() => {
                            debugger;
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            //重新请求数据
                            this.onLoad();
                        }, error => {
                            console.log(error);
                        });

                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            rowSave() {
                //校验必填项
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        add(this.form.customer).then(() => {
                            this.onLoad();
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                        }, error => {
                            console.log(error);
                        });
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });


            },
            onLoad() {
                this.customerId = this.customerId || 1;
                detailForCurrentUser(this.customerId).then(res => {
                    debugger;
                    this.form = res.data.data;
                    if (!validatenull(this.form.customer.qualification.attachmentId)) {
                        getCloudFileURL(this.form.customer.qualification.attachmentId).then(res => {
                            //this.form.customer.qualificationUrl = res.data;
                            this.$set(this.form.customer, 'qualificationUrl', res.data);
                        });
                    }

                });
            },
            // uploadSuccess(res, file) {
            //     this.form.qualification = res.data[0].id;
            // },
            uploadSuccess(res, file) {
                debugger;
                this.$set(this.form.customer, 'qualificationUrl', {});
                this.$set(this.form.customer, 'qualification', {});
                this.$set(this.form.customer.qualification, 'attachmentId', res.data[0].cloudID);
                // this.form.customer.qualification = {};
                // this.form.customer.qualification.attachmentId = res.data[0].cloudID;
                getCloudFileURL(this.form.customer.qualification.attachmentId).then(res => {
                    this.$set(this.form.customer, 'qualificationUrl', res.data);
                    console.log(this.form.customer.qualificationUrl);
                });
            },
        },
        created() {
            this.onLoad();
            //获取服务类别
            this.queryServiceType();
        },
        watch: {
            customerId: function (data) {
                this.onLoad(data);
            },
        }
    }
</script>

<style lang="scss">
    .customer-container {

        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }

        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 148px;
            height: 148px;
            line-height: 178px;
            text-align: center;
        }
    }
</style>
