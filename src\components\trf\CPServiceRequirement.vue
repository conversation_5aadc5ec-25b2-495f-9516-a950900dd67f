<template>
  <div>
    <div class="item-warp clearfix">
      <el-col :span="12">
        <el-form-item :label="$t('service.returnSample')">
          <el-checkbox-group v-model="returnSampleArray" :disabled="trfDisabled"
                             @change="returnSampleChange">
            <el-checkbox name="returnSampleCheckbox"
                         v-for="(returnSample,index) in returnSampleList"
                         :label="returnSample.sysKey" :key="returnSample.sysKey">
              {{returnSample.sysValue}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </div>
    <div class="item-warp clearfix">
      <el-col :span="12">
        <el-form-item :label="$t('service.certificateRequired')">
          <el-select v-model="trfServiceRequire.certificateRequired"
                     :placeholder="$t('operation.pleaseSelect')"
                     :disabled="trfDisabled">
            <el-option
                v-for="option in certificateOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </div>
    <div class="item-warp clearfix">
        <!-- 评论（需要评论请选择） -->
        <el-col :span="8">
            <el-form-item :label="$t('service.comment')">
                <el-radio-group v-model="trfServiceRequire.isComment" :disabled="trfDisabled">
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
        <!-- 纸质报告 -->
        <el-col :span="8">
            <el-form-item :label="$t('service.hardCopy')">
                <el-radio-group v-model="trfServiceRequire.isCopy" :disabled="trfDisabled">
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
        <!-- 拍照（如需拍照请选择 -->
        <el-col :span="8">
            <el-form-item :label="$t('service.takePhoto')">
                <el-radio-group v-model="trfServiceRequire.isPhoto" :disabled="trfDisabled">
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
    </div>

    <div class="item-warp clearfix">
        <!-- 首页确认（是否需要首页确认） -->
        <el-col :span="8">
            <el-form-item :label="$t('service.coverPageConfirmation')">
                <el-radio-group v-model="trfServiceRequire.isConfimCover" :disabled="trfDisabled">
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
        <!-- 报价 -->
        <el-col :span="12">
            <el-form-item :label="$t('service.quotation')">
                <el-radio-group v-model="trfServiceRequire.isQuotation" :disabled="trfDisabled">
                    <el-radio :label="1"> {{$t('service.required')}}</el-radio>
                    <el-radio :label="0">{{$t('service.notRequired')}}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-col>
    </div>
  </div>
</template>

<script>
import serviceRequirement from "@/components/trf/js/serviceRequirement";
import { objectIsNull } from "@/util/validate";
import {getReturnSampleArryCN} from "@/api/trf/trf";
import {mapGetters} from "vuex";

export default {
  name: "SLServiceRequirement",
  props: {
    trfServiceRequire: {
      type: Object,
      required: true,
      default: {},
      description: "服务需求对象"
    },
    trfDisabled: {
      type: Boolean,
      required: true,
      default: false,
      description: '是否可操作'
    }
  },
  watch: {
    language: function (newVal) {
      this.initResultSampleList();
    },
    returnSampleArray: {
      handler(newVal){
        if(!objectIsNull(newVal)) {
          let values = [...newVal];   //先赋值，再排序; 不赋值直接排序会重复执行监听，不排序则存入的顺序不一致
          values.sort();
          this.trfServiceRequire.returnSampleRequire = values.join(',');
          let choiceReturnSample = [];
          this.returnSampleList.forEach(item => {
            values.indexOf(item.sysKey) >= 0 ? choiceReturnSample.push(item.sysValue) : null;
          });
          this.trfServiceRequire.returnSampleName = choiceReturnSample.join(',');
        }
      }
    }
  },
  computed: {
    ...mapGetters(["language"]),
  },
  data() {
    return {
      returnSampleList: [],
      returnSampleArray: [],
      returnSampleMap: new Map([]),
      certificateOptions: [
        { value: 'COC', label: this.$t('service.COC') },
        { value: 'GPC', label: this.$t('service.GPC') },
        { value: 'CPC', label: this.$t('service.CPC') },
      ],
    }
  },

  mounted() {
    console.log("SLServiceRequirement", this.returnSampleData);
    this.initResultSampleList();
    this.returnSampleArray = objectIsNull(this.trfServiceRequire.returnSampleRequire) ? [] : this.trfServiceRequire.returnSampleRequire.split(',');
  },

  methods: {
    returnSampleChange(values) {
      if(objectIsNull(values)) {
        this.returnSampleArray = [];
        this.trfServiceRequire.returnSampleName = null;
        this.trfServiceRequire.returnSampleRequire = null;
      }
      else {
        this.returnSampleArray = values;
      }
    },
    //初始化退样要求
    async initResultSampleList() {
      if(this.returnSampleMap.get(this.language) === undefined) {
        await getReturnSampleArryCN(this.language).then(res => {
          if(res.data && res.data.length > 0) {
            this.returnSampleMap.set(this.language, res.data);
          }
          else {
            this.returnSampleMap.set(this.language, []);
          }
        });
      }
      this.returnSampleList = this.returnSampleMap.get(this.language);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
}
</style>
