<template>
    <div class="sgs_smart__rejectProvided" id="sgs_smart__rejectProvided">
        <el-dialog
                title="Reject Provided information"
                class="sgs_smart_product_rejectProvided"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                append-to-body
                :lock-scroll="false"
                show-close
                v-model="showDia"
                width="30%"
        >
            <el-row>
                <el-col>Please add some comments.</el-col>
            </el-row>
            <el-row>
                <el-col>
                    <textarea class="reject_content" :disabled="false" :rows="5" v-model="content"></textarea>
                </el-col>
            </el-row>
            <div slot="footer" class="dialog-footer" style="text-align: right">
                <el-button :disabled="false" @click="cancelDia">Cancel</el-button>
                <el-button :disabled="false" type="primary" @click="saveComment">Save</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import commentApi from '@/api/comments.ts'
defineOptions({
    name: 'RejectProvided'
})
const props = defineProps({
    mainObjectId:{
      type:String,
      default: ''
    },
    objectId: {
        type: [String, Number],
        default: ''
    },
    objectType: {
        type: [String, Number],
        default: ''
    }
})

const emit = defineEmits(['cancelDia', 'rejectSuccess'])


const showDia = ref(false)
const content = ref('')


const cancelDia = () => {
    showDia.value = false
    emit('cancelDia')
}

const saveComment = () => {
    if (!content.value) return

    const param = {
        objectId: props.objectId,
        objectType: props.objectType,
        content: content.value,
        mainId: props.mainObjectId
    }

    commentApi.addComment(param).then(res => {
        if (res.status === 200) {
            emit('rejectSuccess', props.objectType)
            showDia.value = false
        }
    }).catch(err => {
        this.$notify.error("Error")
    })
}



onMounted(() => {
    showDia.value = true
})
</script>

<style lang="scss">
.sgs_smart__rejectProvided {
  font-family: 'Arial' !important;
  background: #fff;
}
.sgs_smart_product_rejectProvided {
  .reject_content {
    min-width: 100%;
    border: solid 1px #8c8c8c;
    border-radius: 5px;
  }
}
</style>
