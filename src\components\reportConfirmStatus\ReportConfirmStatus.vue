<template>
  <span class="report-confirm-status">
    <!-- 根据不同的枚举值展示不同内容 -->
    <!-- <template v-if="reportConfirmFlag === 'NoRequired'">
      <i class="el-icon-check approved_icon"></i>
      <span>No confirmation required</span>
    </template> -->
    <template v-if="reportConfirmFlag === 'Pending'">
      <el-button
        type="primary"
        icon="el-icon-warning"
        plain
        size="small"
        class="confirm-text"
        @click="showConfirmDialog"
        >{{ $t("operation.toBeConfirm") }}</el-button
      >
      <!-- 二次确认弹窗 -->
      <el-dialog
        :title="$t('operation.confirm')"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
      >
        <span>{{ $t("trf.customerConfirmText") }}</span>
        <template slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">{{
            $t("operation.cancel")
          }}</el-button>
          <el-button type="primary" @click="confirmReport">{{
            $t("operation.confirm")
          }}</el-button>
        </template>
      </el-dialog>
    </template>
    <template v-if="reportConfirmFlag === 'Confirmed'">
      <el-button icon="el-icon-success" type="text" class="complete-text">{{
        $t("operation.complete")
      }}</el-button>
    </template>
  </span>
</template>

<script>
import { syncTrfReportConfirm } from "@/api/trf/trf.js";
export default {
  name: "ReportConfirmStatus",
  props: {
    // 接收 reportConfirmFlag 枚举值
    reportConfirmFlag: {
      type: String,
      required: true,
      validator: function (value) {
        return ["NoRequired", "Pending", "Confirmed"].includes(value);
      },
    },
    reportInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      // 控制确认弹窗的显示与隐藏
      dialogVisible: false,
    };
  },
  methods: {
    // 显示确认弹窗
    showConfirmDialog() {
      this.dialogVisible = true;
    },
    // 确认 Report Confirm 操作，触发自定义事件通知父组件
    confirmReport() {
      let data = {
        trfId: this.reportInfo.trfId,
        trfNo: this.reportInfo.trfNo,
        reportList: [{ reportNo: this.reportInfo.reportNo }],
      };
      syncTrfReportConfirm(data).then((res) => {
        if (res.status == 200 && res.data && res.data.data) {
          this.dialogVisible = false;
          this.$message.success(this.$t("api.success"));
          this.$emit("confirmReportSuccess");
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped>
.report-confirm-status {
  display: flex;
  align-items: center;
  font-weight: normal;
  font-size: 14px;
  text-transform: none;
}

.approved_icon {
  margin-right: 5px;
}
.complete-text {
  margin-left: 10px;
  color: #67c23a;
}
.confirm-text {
  margin-left: 10px;
}
</style>
