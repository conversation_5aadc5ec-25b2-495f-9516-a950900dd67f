<template>
  <div>
    <training-dashboard></training-dashboard>
  </div>
</template>

<script>
  import {validatenull} from "@/util/validate";
  import { mapGetters } from "vuex";
  export default {
    name: "dashboard1",
    components: {
      ClassList: resolve => require(['./classList'], resolve),
      TrainingDashboard: resolve => require(['@/views/dashboard/trainingDashboard'], resolve)
    },
    computed: {
      ...mapGetters([
        "userInfo","dimensions"
      ]),
      role() {
        return {
          isBuyer: this.haseRole('UserRole','Buyer'),
          isSupplier: this.haseRole('UserRole','Supplier'),
          isTrainingAdmin: this.haseRole('UserRole','TrainingAdmin'),
          isTrainee: this.haseRole('UserRole','Trainee'),
          isSGS: this.haseRole('SGSUserRole','SgsAdmin')|| this.haseRole("SGSUserRole", "SgsLabUser"),
        };
      }
    },
    methods: {
      haseRole(type,role){
        if(validatenull(type)||validatenull(role)){
          return false;
        }
        if(validatenull(this.dimensions)){
          return false;
        }else{
          if(this.dimensions.hasOwnProperty(type)){
            if(this.dimensions[type].indexOf(role)>=0){
              return true;
            }else{
              return false;
            }
          }else{
            return false;
          }
        }
      },
    }
  }
</script>

<style scoped>

</style>
