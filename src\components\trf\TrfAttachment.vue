<template>
  <div class="container">
    <el-card  :class="['sgs-box', showStep >= 5 ? 'content-item':'']" id="attachment_card">
      <div class="sgs-group" style="position: relative">
        <h3>{{$t('trf.attachment')}}</h3>
          <el-button type="text"
                     style="position: absolute;left: 185px;top: -3px;"
                     @click="downloadAllFile">
              {{$t('attachment.downloadAll')}}<i class="el-icon-download"></i>
          </el-button>
      </div>
      <el-table :data="trfAttachmentsNew" width="100%"
                :element-loading-text="$t('uploadLoadingText')"
                element-loading-spinner="el-icon-loading"
                v-loading="uploadLoading">
        <el-table-column
            type="index"
            fixed
            label="#"
            width="30">
        </el-table-column>
        <el-table-column
            :show-overflow-tooltip="true"
            prop="fileName"
            header-align="left"
            :label="$t('attachment.name')" width="300">
          <template slot="header" slot-scope="scope">
<!--            <span >{{ $t('attachment.name') }}</span>-->
            <div  class="rigbox">
              {{ $t('attachment.name') }}
              <el-dropdown @command="handleCommand" class="rig_icon">
                <!-- <span class="sortable">⇃↾</span> -->
                <span class="sortable businessWeight">
                            <span :class="des">⇃</span>
                            <span :class="asc">↾</span>
                        </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="businessWeight ascending">{{$t('ascendingOrder')}}&nbsp;↾</el-dropdown-item>
                  <el-dropdown-item command="businessWeight descending">{{$t('descendingOrder')}}&nbsp;⇃</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <el-input size="small"
                      :placeholder="$t('attachment.name')"
                      @keyup.enter.native="filterAttachments"
                      @clear="filterAttachments"
                      v-model="searchName"
                       clearable
            ></el-input>
          </template>
          <template slot-scope="scope">
              <span  v-text="$lodash.get(scope.row, 'fileName', '')"></span>
          </template>

        </el-table-column>
        <el-table-column
            prop="attachmentType"
            label="Attachment Type"
            width="220">
          <template slot="header" slot-scope="scope">
            <div>{{ $t('attachment.attachmentType') }}</div>
            <el-select v-model="searchType" clearable filterable size="small"
                       @change="filterAttachments"
                       :placeholder="$t('attachment.attachmentType')"
                       style="width: 100%;">
              <el-option v-for="(attachmentType,index) in attachmentTypeOptions" :key="index"
                         :label="attachmentType.label"
                         :value="attachmentType.value"></el-option>
            </el-select>
          </template>
          <template slot-scope="scope">
            <el-select v-model="scope.row.attachmentType"  filterable
                       :disabled="trfDisabled"
                       :placeholder="$t('attachment.attachmentType')"
                       style="width: 100%;">
              <el-option v-for="(attachmentType,index) in attachmentTypeOptions" :key="index"
                         :label="attachmentType.label"
                         :value="attachmentType.value"></el-option>
            </el-select>
          </template>
        </el-table-column>



        <el-table-column
            :show-overflow-tooltip="true"
            prop="attachmentSource"
            :label="$t('attachment.attachmentSource')"
            width="160">
          <template slot="header" slot-scope="scope">
            <div>{{ $t('attachment.attachmentSource') }}</div>
            <div style="width:260px;height: 32px"></div>
          </template>
          <template slot-scope="scope">
            <span  v-text="scope.row.attachmentSource==attachmentSource.code?  language === LanguageEnums.CN.name? attachmentSource.cnKey: attachmentSource.enKey  :''  "></span>
          </template>
        </el-table-column>
        <el-table-column
            :show-overflow-tooltip="true"
            prop="lastModifiedTime"
            :label="$t('attachment.updateTime')" width="160">
          <template slot="header" slot-scope="scope">
            <div>{{ $t('attachment.updateTime') }}</div>
            <div style="width:260px;height: 32px"></div>
          </template>
          <template slot-scope="scope">
             <span>
                {{   currentTzDate($lodash.get(scope.row, 'lastModifiedTime')) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            :show-overflow-tooltip="true"
            prop="updateUser"
            :label="$t('attachment.updateUser')" width="140">
          <template slot="header" slot-scope="scope">
            <div>{{ $t('attachment.updateUser') }}</div>
            <div style="width:260px;height: 32px"></div>
          </template>
        </el-table-column>

        <el-table-column
            :label="$t('operation.title')"
            width="180"
            fixed="right"
            >
          <template slot="header" slot-scope="scope">
            <div>{{ $t('operation.title') }}</div>
            <div style="width:260px;height: 32px"></div>
          </template>
          <template slot-scope="scope">
            <el-button type="text" @click="downloadAttachmentRow(scope.row)"
                       size="small" icon="el-icon-download">{{$t('operation.download')}}
            </el-button>
            <el-button v-if="!trfDisabled" @click="removeAttachmentRow(scope.$index)" type="text"
                       size="small" icon="el-icon-delete">{{$t('operation.remove')}}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <el-button class="upload-btn" plain v-if="trf.trfStatus?trf.trfStatus < 2:true" type="primary" :disabled="trfDisabled" @click="openUpload">
          <i class="el-icon-upload2"></i>
          {{$t('operation.upload')}}
        </el-button>
        <sgs-batch-upload :systemID="1" :limit="5"
                          :handle-upload-success="uploadSuccess"
                          ref="batchUpload"
                          append-to-body
                          :accept="accept"
                          :upload-url="uploadUrl"
                          :handle-upload-error="handleUploadError"
                          :attachment-type-options="attachmentTypeOptionsForUpload"
                          :attachment-type-default-value="attachmentTypeDefaultValue"
                          :file-max-sizes="fileMaxSizes"></sgs-batch-upload>
      </div>
    </el-card>
  </div>
</template>

<script>
import { objectIsNull, validateEmail } from "@/util/validate";
import {DictionaryEnums} from "@/commons/enums/DictionaryEnums";
import { LanguageEnums } from "@/commons/enums/LanguageEnums";
import {deepClone} from '@/util/util'
import {mapGetters} from "vuex";
import {getCloudFileURL} from "@/api/common";
import {batchDownloadFile} from "@/api/trf/trf";
import {tzFormChina} from '@/util/datetimeUtils'
export default {
  //TRF 服务需求
  name: "TrfAttachment",
  components: {
  },
  props: {
      fileMaxSizes:{
          type:Number,
          default:10
      },
    showStep:{
      type: Number,
      required: true,
      default: 5,
      description: ''
    },
    trfAttachments: {
      type: Array,
      required: true,
      default: [],
      description: 'TRF附件信息'
    },
    trf: {
      type: Object,
      required: true,
      default: {},
      description: 'TRF信息'
    },

    trfDisabled: {
      type: Boolean,
      required: true,
      default: false,
      description: '是否可操作'
    }
  },
  watch: {
    language: function (newVal) {
      this.initAttachmentTypeData();
    },
    trfAttachmentsNew:{
      handler(){   //注意此处就是handler
        console.log(this.trfAttachmentsNew);
        this.trfAttachments.forEach(item => {
              this.trfAttachmentsNew.forEach(itemNew => {
                  if(itemNew.attachmentId==item.attachmentId){
                    item.attachmentType=itemNew.attachmentType;
                  }
              });
        })
        console.log('附件更新后结果',this.trfAttachments);
      },
      deep:true,
      immediate: true
    },

  },
  computed: {
    ...mapGetters(["language","userInfo"]),
  },
  data() {
    return {
      uploadUrl: '/api/sgsapi/FrameWorkApi/file/doUpload',
      accept: '.txt,.ppt,.pptx,.xls,.xlsx,.doc,.docx,.png,.jpg,.jpeg,.pdf,.msg,.eml',
      uploadLoading: false,
      attachmentTypeOptions: [{
        value: 'SupportingDocuments',
        label: 'Supporting Documents'
      }, {
        value: 'SamplePhoto',
        label: 'Sample Photo'
      }],
      attachmentTypeOptionsForUpload: [{
        value: 'SupportingDocuments',
        label: 'Supporting Documents'
      }, {
        value: 'SamplePhoto',
        label: 'Sample Photo'
      }],
      attachmentTypeDefaultValue: DictionaryEnums.Attachment.AttachmentType.items[0].code,
      searchName:'',
      searchType:'',
      des:'des',
      asc:'asc',
      trfAttachmentsNew: [],
      dictionaryEnums: DictionaryEnums,
      LanguageEnums: LanguageEnums,
      attachmentSource:DictionaryEnums.Attachment.AttachmentSource
    }
  },
  mounted() {
    console.log("进入加载附件",this.trfAttachments);
    this.initData(this.trfAttachments);
    //初始化类型国际化
    this.initAttachmentTypeData();
    console.log("trfAttachment", this.trfAttachments);
    console.log("trf", this.trf);
    console.log("trfDisabled", this.trfDisabled);
  },
  methods: {
    currentTzDate(val){
      if (!val) return ''
      let value = tzFormChina(val,'YYYY-MM-DD HH:mm');
      return value;
    },
    initData(value){
      
      console.log("加载附件数据value",value);
      this.trfAttachmentsNew = deepClone(value);
    },
    initAttachmentTypeData() {
      debugger;
      console.log("组合附件类型数据,原数据",this.attachmentTypeOptions);
      this.attachmentTypeOptions = [];
      this.attachmentTypeOptionsForUpload = [];
      this.dictionaryEnums.Attachment.AttachmentType.items.forEach(item => {
        let label = item.enKey;
        let value = item.code;
        if (this.language === this.LanguageEnums.CN.name) {
          label = item.cnKey;
          value = item.code;
        }
        const option = {
          'label': label,
          'value': value
        }
        this.attachmentTypeOptions.push(option);
        let labelForUpload = item.enKey;
        let valueForUpload = item.code;
        const optionForUpload = {
          'label': labelForUpload,
          'value': valueForUpload
        }
        this.attachmentTypeOptionsForUpload.push(optionForUpload);
      })
    },
    filterAttachments(el){
      debugger;
      //过滤
      if(objectIsNull(this.searchName) &&  objectIsNull(this.searchType) ){
        this.trfAttachmentsNew = deepClone(this.trfAttachments);
        return false;
      }

      if(!objectIsNull(this.searchName) && ! objectIsNull(this.searchType) ){
        this.trfAttachmentsNew = this.trfAttachments.filter(item => item.fileName.toLowerCase().includes(this.searchName.toLowerCase())).filter(item =>item.attachmentType.includes(this.searchType));
      }else if(objectIsNull(this.searchName)){
        this.trfAttachmentsNew = this.trfAttachments.filter(item =>item.attachmentType==this.searchType );
      }else if(objectIsNull(this.searchType)){
        this.trfAttachmentsNew = this.trfAttachments.filter(item => item.fileName.toLowerCase().includes(this.searchName.toLowerCase()));
      }else{
        this.trfAttachmentsNew = deepClone(this.trfAttachments);
      }

    },
    handleCommand(command){ // 前端表格排序
      let arr = command.split(' ');
       const lightSort=arr[1].slice(0,3)
       this.asc='asc'
       this.des='des'
      if(lightSort=='asc'){ //升序
        this.asc='asc lightSort'
        this.trfAttachmentsNew= this.trfAttachmentsNew.sort((a, b) => a.fileName.localeCompare(b.fileName));
      }else{//降序
        this.des='des lightSort'
        this.trfAttachmentsNew= this.trfAttachmentsNew.sort((a, b) => b.fileName.localeCompare(a.fileName));
      }
    },
    /**
     * 附件上传成功钩子函数
     * */
    uploadSuccess(data) {
      debugger;

      if(data){
        let attachmentType=data.attachmentType;
        data.data.forEach(item =>{
          const attachment = {
            'attachmentId': item.cloudID,
            'fileUrl': item.path,
            'fileName': item.attachmentName+"."+item.suffixes,
            'size': item.size,
            'attachmentType': attachmentType,
            'attachmentSource':this.attachmentSource.code,
            'updateUser':this.userInfo.userName,
          }
          this.trf.trfAttachments.push(attachment);
        });
        this.trfAttachmentsNew=this.trf.trfAttachments;
      }
      this.$refs.batchUpload.close();

    },
    openUpload(){
      let _self = this;
      _self.$nextTick(()=>{
        this.$refs.batchUpload.open();
      });
    },
    handleUploadError(){},
    /**
     * 附件上传钩子函数
     * */
    uploadChange(file, fileList) {
      if (file.status == 'ready') {
        //开启loading效果
        this.uploadLoading = true;
      } else {
        this.uploadLoading = false;
      }
    },
    async downAttachment(cloudId) {
      getCloudFileURL(cloudId).then(res => {
        window.open(res.data, "_blank");
      });

    },
    downloadAttachmentRow(row) {
      getCloudFileURL(row.attachmentId).then(res => {
        window.open(res.data, "_blank");
      });
    },
    removeAttachmentRow(index) {
      this.trf.trfAttachments.splice(index, 1);
      this.trfAttachmentsNew= this.trf.trfAttachments;
   },
      downloadAllFile(){
          let cloudIDs = [];
          this.trfAttachments.forEach(att=>{
              if(att.attachmentId){
                  cloudIDs.push(att.attachmentId);
              }
          })
          let param = {
              systemId:2,
              cloudIDs
          }
          batchDownloadFile(param,this.trf.trfNo,error => {
              this.$message.error(this.$t('api.error'));
          })
      },
  }

}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  background-color: white;
}
.card {
  width: 100%;
  border-style: none;
  height: auto;
}
.lightSort{
  color: red;
}
</style>
