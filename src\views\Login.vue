<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { onMounted } from 'vue'
const { t } = useI18n()
import { useStore } from 'vuex'
const store = useStore()
const initLogin = async () => {
    try {
    const userInfo = {
      tenantId: '000000',
      username: 'apac\\ext_gary_yuan',
      password: 'haiyang5213~!',
      type: 'account',
      receiveMarketingCommunication: 1
    }
    await store.dispatch('LoginByUsername', userInfo)
  } catch (error) {
    console.error('Login failed:', error)
  }
  
 
}
onMounted(async () => {
  await initLogin()
  // init()
 
})
</script>

<template>
  <div>
    {{ t('navbar.login') }}
  </div>
</template>