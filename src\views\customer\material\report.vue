<template>
    <div>
        <el-row>
            <el-form ref="query" :inline="true" :model="query" label-width="2px" size="medium">
                <el-form-item>
                    <el-select v-model="materialConfigId" clearable
                               @change="selectMaterialConfigChange"
                               style="width: 100%;"
                               :placeholder="$t('customerMaterialConfig.configTemplate')">
                        <el-option v-for="(template,index) in materialConfigData"
                                   :label="template.dffFormName"
                                   :value="template.materialConfigId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input  v-model="query.trfNo"  :placeholder="$t('customerMaterialConfig.applicationNo')" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="query.conclusion" :placeholder="$t('customerMaterialConfig.conclusion')" clearable>
                        <el-option value="All" label="All"></el-option>
                        <el-option value="Pass" label="Pass"></el-option>
                        <el-option value="Fail" label="Fail"></el-option>
                        <el-option value="See Result" label="See Result"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :prop="$t('term.supplier')">
                    <el-input style="width: 100%;" v-model="query.customerName"
                              :placeholder="$t('trf.applicant')"
                              clearable></el-input>
                </el-form-item>
                <el-form-item :prop="col.dffFieldCodeFirstLow" v-for="col in queryCols">
                    <el-input style="width: 100%;" v-model="query[col.dffFieldCodeFirstLow]"
                              :placeholder="col.dffFieldMaterialName"
                              clearable></el-input>
                </el-form-item>
            </el-form>
        </el-row>

        <el-row>
            <el-col :span="24" style="text-align: right">
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                <el-button type="primary"  v-if="permissionList.exportExcelBtn" @click="exportExcel">{{$t('customerMaterialConfig.exportExcel')}}</el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="20">

            </el-col>
            <el-col :span="4" style="text-align: right">
                <el-popover
                        placement="bottom"
                        width="300"
                        height="500"
                        trigger="click">
                    <el-table row-key="dffFieldCodeFirstLow"
                              height="500" :data="isMasterList" @selection-change="masterChange"
                              style="width: 100%" size="medium">
                        <el-table-column type="selection" :reserve-selection="true" width="50"></el-table-column>
                        <el-table-column prop="dffFieldMaterialName" label="Material"></el-table-column>
                    </el-table>
                    <el-button slot="reference" icon="el-icon-s-grid"></el-button>
                </el-popover>
            </el-col>
        </el-row>
        <el-table
                row-key="dffFieldCodeFirstLow"
                ref="customerMaterialTable"
                :data="customerMaterialTableData"
                style="width: 100%"
                v-loading="loading"
                @selection-change="materialChange"
                size="medium">
            <el-table-column type="index" label="#" width="50"></el-table-column>
            <el-table-column :show-overflow-tooltip='true' label="Application No." prop="trfNo" width="160"></el-table-column>
               <el-table-column label="Applicant" :show-overflow-tooltip='true' prop="customerName" width="200">
                <template slot-scope="scope">
                    <span>{{scope.row.customerName}}</span>
                </template>
            </el-table-column>

             <el-table-column label="Buyer" :show-overflow-tooltip='true' prop="customerGroupName" width="200">
                <template slot-scope="scope">
                    <span>{{scope.row.customerGroupName}}</span>
                </template>
            </el-table-column>


            <el-table-column :key="col.dffFieldCodeFirstLow"
                             v-for="col in cols"
                             :fit="true"
                             width="240"
                             :prop="col.dffFieldCodeFirstLow"
                             :label="col.dffFieldMaterialName"
                             :show-overflow-tooltip='true'>
            </el-table-column>
            <el-table-column :show-overflow-tooltip='true' label="Report No" prop="reportNo" width="160"></el-table-column>
            <el-table-column :show-overflow-tooltip='true' label="Conclusion" prop="conclusion" width="160"></el-table-column>
            <el-table-column label="Failed Item" :show-overflow-tooltip='true' prop="failedItem" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.failedItem}}</span>
              </template>
            </el-table-column>
            <el-table-column label="Report Issued Date" :show-overflow-tooltip='true' prop="reportIssuedDate" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.reportIssuedDate}}</span>
              </template>
            </el-table-column>
            <el-table-column label="Test Report" width="180" align="center">
                <template slot-scope="scope">

                     <span v-if="scope.row.trfReportFiles" v-for="file in scope.row.trfReportFiles">
                         <img src="/img/pdf.png" v-if="file.languageId==2"
                              style="width: 20px;cursor: pointer;margin-left: 20px" title="Download Report"
                              @click="downloadReport(file.cloudId)"/>
                             <img src="/img/pdf.png" v-else style="width: 20px;cursor: pointer;margin-left: 20px"
                                  title="Download Report"
                                  @click="downloadReport(file.cloudId)"/>
                     </span>

                  <!--  <span>
                        <a @click="downloadReport(scope.row.cloudId)" v-if="scope.row.cloudId"
                           style="cursor: pointer;color: #aeacbb;">Download </a>
                    </span>-->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-table :data="showData" stripe style="width: 1px" type="hidden"  id="exportTab">
            <el-table-column label="Application No." prop="trfNo" width="120"></el-table-column>
              <el-table-column label="Applicant" :show-overflow-tooltip='true' prop="customerName" width="200">
                <template slot-scope="scope">
                    <span>{{scope.row.customerName}}</span>
                </template>
            </el-table-column>

             <el-table-column label="Buyer" :show-overflow-tooltip='true' prop="customerGroupName" width="200">
                <template slot-scope="scope">
                    <span>{{scope.row.customerGroupName}}</span>
                </template>
            </el-table-column>
            <el-table-column :key="col.dffFieldCodeFirstLow"
                             v-for="col in cols"
                             :prop="col.dffFieldCodeFirstLow"
                             :label="col.dffFieldMaterialName">
            </el-table-column>
            <el-table-column label="Report No" prop="reportNo" width="200"></el-table-column>
            <el-table-column label="Conclusion" prop="conclusion" width="120"></el-table-column>
            <el-table-column label="Failed Item" :show-overflow-tooltip='true' prop="failedItem" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.failedItem}}</span>
              </template>
            </el-table-column>
            <el-table-column label="Report Issued Date" :show-overflow-tooltip='true' prop="reportIssuedDate" width="180">
              <template slot-scope="scope">
                <span>{{scope.row.reportIssuedDate}}</span>
              </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
    import {detail,getConfigList} from "@/api/martin/customerMaterialConfig";
    import {validatenull} from "@/util/validate";
    import {getPageByUser, getList} from "@/api/customer/buyerRelationship";
    import {
        getPage,
        remove,
        downloadExcel,
        uploadExcelData,
        reportMaterialPage,
        exportReportMaterials
    } from "@/api/martin/materialListManager";
    import {downLoadFile} from "@/api/trf/trf";
    import {
        getDictByCode,
        getCustomerGroup,
        getProductLine,
        getDffList,
        getSpecificByProductLineId
    } from "@/api/common/index";
    import moment from 'moment'
    import {mapGetters} from "vuex";
    import FileSaver from 'file-saver'
    import XLSX from 'xlsx'

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        name: "customerMaterialConfig",
        data() {
            return {
                loading:false,
                materialConfig:{},
                materialConfigData: [],
                selIds: [],
                selMaterialsData: [],
                customerMaterialTableData: [],
                isMasterList: [],
                allCols: [],//存放所有的 cols
                masterMultipleSelection: [],//存放 master 数据
                materialMultipleSelection: [],//存放 material 数据
                showData: [],//存放 excel 数据
                query: {},
                btnGuestbookSubmit: false,
                title: "",
                cols: [],
                queryCols: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                fileTemp: "",
                limitUpload: 1,
                downloadExcel: "/customer/materials/download-excel",
                materialConfigId: "",
                uploadDialog: false,
                uploadForm: {},
                show: false,
                radio: '',
                supplierList: [],
                customerGroupCode: "",
                bossNo: ""
            }
        },
        watch: {
            "radio": function (newVal) {
                if (newVal === "2") {
                    this.show = true;
                } else {
                    this.customerId = "";
                    this.customerNameZh = "";
                    this.show = false;
                }
            }
        },
        mounted() {
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    exportExcelBtn: this.vaildData(this.permission['sgs:customer:material:report:export'], false),
                };
            }
        },
        methods: {
            initLoadMaterialConfig(params = {}){
                params.customerGroupCode = this.userInfo.customerGroupCode
                getConfigList(Object.assign(params, this.materialConfig)).then(res => {
                    this.materialConfigData = res.data.data;
                    if(this.materialConfigData.length==1){
                        this.materialConfigId= this.materialConfigData[0].materialConfigId;
                        this.initCols();
                    }
                  var materialConfigId = this.$route.query.materialConfigId;
                  if(materialConfigId!=null && materialConfigId!='' && materialConfigId!=undefined){
                      this.materialConfigId=materialConfigId;
                      this.queryCols=[];
                      this.customerMaterialTableData=[];
                      this.initCols();
                  }
                });
            },
            selectMaterialConfigChange(val) {
                this.customerMaterialTableData=[];
                this.query = {};
                this.$refs['query'].resetFields();
                this.initCols();
            },
            downloadReport(cloudId) {
                if (cloudId != '' && cloudId != null) {
                    downLoadFile(cloudId).then(res => {
                        var pdfUrl = res.data.data;
                        this.downLoadReportOts(pdfUrl);
                        /*this.$message({
                            type: 'success',
                            message: this.$t('api.success')
                        });*/
                    }, error => {
                        this.$message.error(this.$t('api.error'));
                    });
                }
                //window.open(pdfUrl, '_blank')
            },
            downLoadReportOts(pdfUrl) {
                window.open(pdfUrl, '_blank')
            },
            openUpload() {

                getList(1, 1000, {"customerGroupCode": this.customerGroupCode}).then(res => {
                    this.supplierList = res.data.data.records
                })
                this.title = this.$t('customerMaterialConfig.openExcel')
                this.uploadDialog = true;
            },
            customerGroupCodeChange(val) {
                let obj = {};
                obj = this.supplierList.find((item) => {
                    return item.customerId === val;
                });
                if (obj != undefined && obj != null) {
                    debugger
                    this.customerId = obj.customerId;
                    this.customerNameZh = obj.customerNameZh;
                    this.bossNo = obj.customerNumber
                }
            },
            onSearch() {
                this.page.currentPage =1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                this.loading = true;
                this.query.materialConfigId = this.materialConfigId;
                reportMaterialPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.loading = false;
                    this.customerMaterialTableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                }).catch(error=>{
                    this.loading = false;
                })
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            formtterDate(row, column) {
                var date = row[column.property];

                if (date == undefined || date == '') {
                    return ''
                }
                ;

                return moment(date).format("YYYY-MM-DD")
            },
            changeStatus(row) {
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                add(modifiedForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            onReset() {
                this.$refs['query'].resetFields();
            },
            handleChange(file, fileList) {
                this.fileTemp = file.raw
            },

            handleRemove(file, fileList) {
                this.fileTemp = null
            },
            onDelete() {
                if (this.materialMultipleSelection.length === 0) {
                    this.$message({
                        type: "info",
                        message: this.$t('customerMaterialConfig.needChooseOne')
                    });
                } else {
                    this.$confirm(this.$t('operation.confirmDelete'), {
                        confirmButtonText: this.$t('operation.confirm'),
                        cancelButtonText: this.$t('operation.cancel'),
                        type: "warning"
                    }).then(() => {
                        remove(this.materialMultipleSelection).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.onLoad(this.page);
                            this.materialMultipleSelection = []
                            this.$refs.customerMaterialTable.clearSelection();
                        });
                    })
                }

            },
            exportExcel() {
                let params = {}
                exportReportMaterials(1, 3000, Object.assign(params, this.query)).then(res => {
                    this.showData = res.data.data;
                    this.$nextTick(() => {
                        var xlsxParam = {raw: true} // 导出的内容只做解析，不进行格式转换
                        var wb = XLSX.utils.table_to_book(document.querySelector('#exportTab'), xlsxParam)
                        console.log(wb);
                        /* get binary string as output */
                        var wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'array'})
                        try {
                            FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), 'Material Data.xlsx')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log(e, wbout)
                            }
                        }
                        return wbout
                    })
                });
            },
            onDownloadExcel() {
                window.open("/api/sgs-mart/customer/materials/download-excel?materialConfigId=" + this.$route.query.materialConfigId, '_blank');
            },

            // 修改页大小
            handleSizeChange(val) {
                this.dffSize = val;
            },
            // 修改页码
            handleCurrentChange(val) {
                this.dffPage = val;
            },
            masterChange(val) {
                this.masterMultipleSelection = val;
                // 现删除所有 isMaster2 标记为1的
                for (var i = 0; i < this.cols.length; i++) {
                    if (this.cols[i].isMaster2 === 1) {
                        this.cols.splice(i--, 1)
                    }
                }
                // 循环补充全部的，这样可以保证试试同步，不用再写删除了
                val.forEach(item => {
                    item.isMaster2 = 1;
                    this.cols.push(item);
                })
            },
            materialChange(val) {
                this.materialMultipleSelection = val;
                this.selMaterialsData = val;
                let ids = [];
                val.forEach(ele => {
                    ids.push(ele.id);
                });
                this.selIds = ids.join(",");
            },
            initCols() {
                this.queryCols=[];
                this.cols=[];
                this.isMasterList=[];
                this.allCols=[];
                this.title = this.$t('customerMaterialConfig.title.edit');
                let params = {"materialConfigId": this.materialConfigId};
                detail(params).then(res => {
                    this.customerGroupCode = res.data.data[0].customerGroupCode
                    this.customerGroupName = res.data.data[0].customerGroupName
                    //获取后台数据付给页面，并打开
                    res.data.data.forEach(item => {
                        if (item.isMr === 1 && item.isMaterial === 1) {
                            this.cols.push(item)
                        }
                        if (item.isMaterial === 1 && item.isMr !== 1) {
                            this.isMasterList.push(item)
                        }
                        if (item.isMaterial === 1) {
                            this.allCols.push(item);
                        }
                    });
                    console.log(this.isMasterList);
                    res.data.data.forEach(item => {
                        if (item.isSearch === 1) {
                            this.queryCols.push(item)
                        }
                    });
                    this.onLoad(this.page);
                });
            },
        },
        created() {
            this.initLoadMaterialConfig();
        }

    }
</script>

<style scoped>
    .inline-block {
        display: inline-block;
        padding-left: 3%;
        padding-right: 3%;
    }
</style>
