const fetch = require("node-fetch"); // 用于获取外部接口
const axios = require("axios");
const moment = require("moment");
const FormData = require("form-data");
const fs = require("fs");
const { host } = require("./../config");
const logs = require(".././log");
const cryptoAes = require("./../utils/crypto");
const FileUploader = require("./../utils/fileUploader");
const {
  handleFileResponse,
  handleJsonResponse,
} = require('../utils/responseHandler')

class RequestApi {
  constructor() {}
  // 公共请求方法
  async request(url, method, ctx) {
    let params = ctx.query;
    let data = ctx.request.body;
    let headers = ctx.request.headers;
    let formData = new FormData();
    if (ctx.requestType === "formData" && ctx.uploadFile=='true') {
      // 添加普通字段到 form-data
      for (const key in ctx.request.body) {
        formData.append(key, ctx.request.body[key]);
      }
      try {
        // 添加文件到 form-data
        formData = await FileUploader.appendFilesToFormData(
          formData,
          ctx.request.files
        );
      } catch (error) {
        logs.createLogs({
          reqAddress: host + url,
          reqParams: params,
          info: error,
          type: "fail",
          time: {
            startTime: moment().unix(),
            endTime: moment().unix(),
          },
        });
      }
    }

    try {
      const startTime = moment().unix();
      const option = {
        method,
        headers: {
          "Content-Type": "application/json",
          sgstoken: headers.sgstoken,
          sgslanguage: headers.sgslanguage,
          credentials: "include",
        },
      };
      // 检查签名
      if (
        headers.sign &&
        headers.timestamp ===
          cryptoAes.decrypt(headers.sign, "cnsgsmart-uat.sgs.net")
      ) {
        // 处理请求参数
        const queryString = new URLSearchParams(params).toString();
        url += `?${queryString}`;
        let useFormDataReq = false;
        if (method === "POST") {
          // 合并 formData 的 headers
          if (ctx.requestType === "formData") {
            for (const key in ctx.request.body) {
              formData.append(key, ctx.request.body[key]);
            }
            option.body = formData;
            delete option.headers["Content-Type"];
            Object.assign(option.headers, formData.getHeaders());
            useFormDataReq = true;
          } else {
            option.headers["Content-Type"] = "application/json";
            option.body = JSON.stringify(data);
          }
        }
        let body ;
        if(useFormDataReq){
          const res = await axios.post(host + url, formData, {
            headers: {
              ...option.headers,
              ...formData.getHeaders(),
            },
          })
          body = res.data;
        }else{
          const response = await fetch(host + url, option);
          const contentType = response.headers?.get('content-type') || '';
          body = contentType && contentType.startsWith('application/') 
            ? handleFileResponse(ctx, response) 
            : await handleJsonResponse(response);
        }
        const endTime = moment().unix();

        // 记录日志
        logs.createLogs({
          reqAddress: host + url,
          reqParams: method === "POST" ? option.body : url,
          info: body,
          type: "success",
          time: {
            startTime,
            endTime,
          },
        });

        return body;
      } else {
        // 签名不正确的拦截
        const endTime = moment().unix();
        logs.createLogs({
          reqAddress: host + url,
          reqParams: params,
          info: "签名错误",
          type: "fail",
          time: {
            startTime,
            endTime,
          },
        });

        throw new Error("签名错误");
      }
    } catch (err) {
      const endTime = moment().unix();
      logs.createLogs({
        reqAddress: host + url,
        reqParams: params,
        info: err.message || "未知错误",
        type: "fail",
        time: {
          startTime: moment().unix(),
          endTime,
        },
      });

      throw err;
    }
  }

  // POST 请求方法
  async post(url, ctx) {
    return this.request(url, "POST", ctx);
  }

  // GET 请求方法
  async get(url, ctx) {
    return this.request(url, "GET", ctx);
  }
}

module.exports = new RequestApi();
