import request from '@/router/axios';




export const getList = (data) => {
    return request({
        url: '/api/sgs-mart/invoice/order/list',
        method: 'post',
        data:data
    })
}
export const getPayType = () => {
    return request({
        url: '/api/sgs-mart/invoice/queryPayType',
        method: 'post'
    })
}
export const cancel = (data) => {
    return request({
        url: '/api/sgs-mart/invoice/order/cancelPaymentOrder',
        method: 'post',
        data:data
    })
}

export const goPayment = (data) => {
    return request({
        url: '/api/sgs-mart/invoice/order/goPayment',
        method: 'post',
        data:data
    })
}

export const detail = (data) => {
    return request({
        url: '/api/sgs-mart/invoice/order/detail',
        method: 'post',
        data:data
    })
}



