<template>
  <div class="main" v-loading="docLoading">
    <div class="title">
      <h3>{{ $t("wel1.SGSNewService") }}</h3>
      <el-input style="width: 50%" placeholder="Please input filter"
                clearable
                v-model="searchData" @input="search" size="medium"></el-input>
    </div>
    <div class="page-example3 document"  @click="chooseService($event)">
      <vue-seamless-scroll :data="filterDataList" class="seamless-warp"
                           :class-option="classOption">
        <ul class="list">
          <!-- @click="newServiceClick(item.filePath, item.serviceUrl)" -->
          <li v-for="(item, index) in filterDataList" class="listItem" :data="JSON.stringify(item)" :key="index">
            <span v-if="item.isNew" class="pull-right new-tag">NEW</span>
            <a class="lib-a" target="_blank"
               >
              {{ item.textDisplay }}
            </a>
          </li>
        </ul>
      </vue-seamless-scroll>
    </div>
    <div class="documents_more_btn">
      <el-button type="text" @click="readMore"><span style="color: #f60;">More >>></span> </el-button>
    </div>
  </div>
</template>

<script>
import {queryNewServiceList} from "@/api/welIndex";
import {documentTool} from "@/components/documents/js/documentTool";
import {validatenull} from "@/util/validate";
import {roleEnums} from "@/commons/enums/RoleEnums";
import { LanguageEnums } from "@/commons/enums/LanguageEnums";
import {mapGetters} from "vuex";

export default {
  name: "SgsService",
  data() {
    return {
      searchData:"",
      chooseIndex:0,
      docLoading: false,
      roleList: roleEnums,
      newServiceList: [],
      filterDataList:[]
    }
  },
  components: {
    vueSeamlessScroll: (resolve) => require(["vue-seamless-scroll"], resolve)
  },
  computed: {
    ...mapGetters(["userInfo", "permission","language"]),
    classOption() {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 5, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    },
  },
  mounted() {
    this.initLoadNewService();
    console.log("加载组件 ===>", "SgsService");
  },
  watch: {
    //监听语言变化
    language: function (newVal) {
      this.initLoadNewService();
    },
  },
  methods: {
    readMore(){
      this.$router.push({
        path:'/document/more',
        query:{
          type:'2'
        }
      })
    },
    search(){
      if(!this.searchData){
        this.filterDataList = this.newServiceList.filter(s=>s);
        return
      }
      let text = this.searchData.toLowerCase();
      this.filterDataList = this.newServiceList
              .filter(s=>(s.textDisplay || '').toLowerCase().indexOf(text)>-1)
    },
    chooseService(e){
      debugger;
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /listItem/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      this.newServiceClick(data.filePath, data.serviceUrl);
    },
    initLoadNewService() {
      //将当前语言放入请求中
      let languageId=LanguageEnums.EN.code;
      if(LanguageEnums.EN.name==this.language){
        languageId=LanguageEnums.EN.code;
      }else{
        languageId=LanguageEnums.CN.code;
      }
      this.docLoading = true;
      queryNewServiceList(0,this.userInfo.productLineCode,languageId,1).then((res) => {
        this.newServiceList = res.data.data;
        this.filterDataList = res.data.data;
        this.docLoading = false;
      }).finally(() => {
        this.docLoading = false;
      });
    },

    newServiceClick(fileUrl, url) {
      return documentTool.newServiceClick(fileUrl, url);
    },
  }
}
</script>

<style scoped lang="scss">
  @import "./style/doc.scss";
</style>
