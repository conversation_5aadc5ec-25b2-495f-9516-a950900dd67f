<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.reconciliationService')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.reconciliationService')}}</h1>
        <el-card shadow="never" class="sgs-box" >
            <el-row>
                <el-form :inline="true" :model="query" size="medium">
                    <el-form-item>
                        <el-col :span="11">
                            <el-date-picker type="date" :placeholder="$t('invoice.fapiaoIssuedStartDate')"
                                            format="yyyy-MM-dd"
                                            :value="query.issuedStartDate"
                                            @input="val => updateStartDate(val)"
                                            :picker-options="pickerOptions"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                        <el-col class="line" :span="2">-</el-col>
                        <el-col :span="11">
                            <el-date-picker :placeholder="$t('invoice.fapiaoIssuedEndDate')" format="yyyy-MM-dd"
                                            :value="query.issuedEndDate"
                                            @input="val => updateEndDate(val)"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                        <span style="color: red"> *{{$t('dateValidate.startMaxError')}}</span>
                    </el-form-item>

                    <el-form-item>
                        <el-input v-model="query.invoiceNo" clearable maxlength="20"
                                :placeholder="$t('invoice.invoiceNo')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model="query.fapiaoNo" clearable maxlength="20"
                                :placeholder="$t('invoice.fapiaoNo')"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input v-model="query.contactName" clearable maxlength="30"
                                :placeholder="$t('invoice.contactName')"></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-select clearable filterable :placeholder="$t('invoice.paymentStatus')"
                                v-model="query.paymentStatus" style="width:100%">
                            <el-option label="全部"
                                    :value="0"></el-option>
                            <el-option label="已支付"
                                    :value="1"></el-option>
                            <el-option label="待支付"
                                    :value="2"></el-option>

                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSearch" v-if="permissionList.searchBtn">
                            {{$t('operation.search')}}
                        </el-button>
                        <el-button type="primary" @click="mergePayBtnPay" v-if="permissionList.mergePayBtn">
                            {{$t('invoice.mergePay')}}
                        </el-button>
                        <reset-button @click="onReset"></reset-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row class="table-list">
                <!--:data="tableData"-->
                <el-table
                        stripe="true"
                        :data="newTableData"
                        v-loading="loading"
                        :element-loading-text="$t('loading')"
                        style="width: 100%"
                        size="medium"
                        @filter-change="filterChange"
                        @selection-change="handleSelectionChange"
                >
                    <!--<el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>-->
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column
                            fixed
                            width="120px"
                            prop="invoiceNo"
                            align="left"
                            :label="$t('invoice.invoiceNo')"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="fapiaoNo"
                            width="120px"
                            align="left"
                            :label="$t('invoice.fapiaoNo')">
                    </el-table-column>
                    <el-table-column
                            prop="awbNo"
                            width="120px"
                            align="left"
                            :label="$t('invoice.awbNo')">
                    </el-table-column>
                    <el-table-column
                            prop="fapiaoIssuedDate"
                            width="140px"
                            align="left"
                            :label="$t('invoice.fapiaoIssuedDate')">
                    </el-table-column>
                    <el-table-column
                            show-overflow-tooltip="true"
                            prop="reportNo"
                            width="180px"
                            align="left"
                            :label="$t('invoice.reportNo')">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('invoice.reportNo')}}</div>
                            <el-input v-model="conditions.reportNo" size="mini" clearable
                                    :placeholder="$t('invoice.reportNo')"/>
                        </template>
                    </el-table-column>
                    <el-table-column
                            show-overflow-tooltip="true"
                            width="180px"
                            prop="clientReferenceNo"
                            align="left"
                            :label="$t('invoice.clientReferenceNo')">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('invoice.clientReferenceNo')}}</div>
                            <el-input v-model="conditions.clientReferenceNo" size="mini" clearable
                                    :placeholder="$t('invoice.clientReferenceNo')"/>
                        </template>
                    </el-table-column>

                    <el-table-column
                            align="left"
                            prop="currency"
                            width="90px"
                            :label="$t('invoice.currency')">
                    </el-table-column>
                    <el-table-column
                            align="left"
                            width="90px"
                            prop="totalAmount"
                            :label="$t('invoice.totalAmount')">
                        <template slot-scope="scope">
                            <span>{{scope.row.totalAmount | rounding}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="left"
                            prop="invoiceBalance"
                            width="120px"
                            :label="$t('invoice.invoiceBalance')">
                        <template slot-scope="scope">
                            <span>{{scope.row.invoiceBalance| rounding}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="left"
                            show-overflow-tooltip="true"
                            width="100px"
                            prop="contactName"
                            :label="$t('invoice.contactName')">
                    </el-table-column>
                    <el-table-column
                            align="left"
                            show-overflow-tooltip="true"
                            width="200px"
                            prop="labName"
                            :label="$t('invoice.payee')">
                        <template slot="header" slot-scope="scope">
                            <div>{{$t('invoice.payee')}}</div>
                            <el-input v-model="conditions.labName" size="mini" clearable
                                    :placeholder="$t('invoice.payee')"/>
                        </template>
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            :label="$t('invoice.statementAndInvoice')"
                            width="200"
                            align="left">
                        <template slot-scope="scope">
                            <el-button type="text" v-if="permissionList.billFileBtn"
                                    :disabled="scope.row.invoicePDFFileUrl=='' || scope.row.invoicePDFFileUrl== null"
                                    @click="billClick(scope.row)">
                                {{$t('invoice.bill')}}
                            </el-button>
                            <el-button type="text" v-if="permissionList.invoiceFileBtn"
                                    :disabled="scope.row.efapiaoFileUrl == null || scope.row.efapiaoFileUrl==''"
                                    @click="eFapiaoClick(scope.row)">
                                {{$t('invoice.electronicInvoice')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="query.pageNo"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="query.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>

                <el-dialog :title="$t('invoice.paymentInfo')" :visible.sync="dialogPayInvoiceInfo" width="70%" top="10vh"
                >
                    <div class="modal-body">
                        <el-table :data="selectedInvocieData">
                            <el-table-column property="invoiceNo" :label="$t('invoice.invoiceNo')" width="180">

                            </el-table-column>
                            <el-table-column
                                    prop="fapiaoNo"
                                    width="120px"
                                    align="left"
                                    :label="$t('invoice.fapiaoNo')">
                            </el-table-column>
                            <el-table-column property="reportNo" :label="$t('invoice.reportNo')"
                            ></el-table-column>
                            <el-table-column property="invoiceBalance"
                                            width="150px"
                                            :label="$t('invoice.invoiceBalance')">
                                <template slot-scope="scope">
                                    <span>{{scope.row.invoiceBalance| rounding}}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="fc-price-info" style="text-align: right;margin-top: 10px; line-height: 25px">
                            <span class="price-tit">{{$t('invoice.totalPayable')}}：</span>
                            <span class="price-num" id="sumPayPriceId">￥{{totalAmout}}</span>
                            <p>{{$t('invoice.payTop1')}}<span style="color: #f16a6a ">{{$t('invoice.payTop2')}}</span>{{$t('invoice.payTop3')}}
                            </p>
                        </div>
                        <div class="bg_title_tint">
                            <h3 class="line_h_3em pl_3 c_title text_14em">{{$t('invoice.validate.selectPayType')}}</h3>
                        </div>
                        <div>
                            <el-radio v-model="payType" v-for="(item,index) in payTypes" size="medium" :label="item.code"
                                    :key="item.code">
                                <img v-if="item.code==100000" src="@/images/alipay.png" class="file_icon"
                                    style="width: 80px">
                                <img v-if="item.code==100001" src="@/images/wechat.png" class="file_icon"
                                    style="width: 80px">
                                <img v-if="item.code==100002" src="@/images/unionPay.png" class="file_icon"
                                    style="width: 80px">
                                {{item.name}}
                            </el-radio>

                            <!-- <el-radio v-model="payType" size="medium" label="2"><img src="@/images/wechat.png"
                                                                                    class="file_icon" style="width: 60px">微信
                            </el-radio>

                            <el-radio v-model="payType" size="medium" label="2"><img src="@/images/unionPay.png"
                                                                                    class="file_icon" style="width: 60px">银联
                            </el-radio>-->

                        </div>


                    </div>
                    <div slot="footer" class="dialog-footer" style="text-align: right;top: 5px;">
                        <el-button type="primary" @click="submitPayment">{{$t('invoice.submitPay')}}</el-button>
                    </div>
                </el-dialog>

                <el-dialog title="微信支付" :visible.sync="dialogPay" width="70%" top="10vh"
                >
                    <div style="display: flex;flex-direction: column;justify-content: center;align-items:center">

                        <!--<div data-v-4a453f5a="" id="qrcode" class="qrcode" style="height: 300px;" title="weixin://wxpay/bizpayurl?pr=isKr1rTzz"><canvas width="300" height="300" style="display: none;"></canvas><img alt="Scan me!" src="data:image/png;base64,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" style="display: block;"></div>-->


                        <div v-loading="qrLoading" id="qrcode" class="qrcode" ref="qrCodeUrl" style="height: 300px;"></div>
                        <div style="text-align: center;margin-top: 8px">
                            <P>请使用微信扫一扫</P>
                            <P>扫描二维码支付</P>
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer" style="text-align: right;top: 5px;">
                        <span style="margin-right: 8px;font-size: 16px ;color: red">温馨提示：完成支付后，订单状态更新稍有延迟，如已完成支付，请点击</span>
                        <el-button type="primary" @click="completePayment">{{$t('invoice.completePayment')}}</el-button>
                    </div>
                </el-dialog>

            </el-row>
        </el-card>

    </basic-container>
</template>

<script>
    import {getList, getPayType, createInvoiceOrder} from "@/api/invoice/invoice";
    import {mapGetters} from "vuex";
    import moment from 'moment'
    import {validatenull} from "@/util/validate";
    import QRCode from 'qrcodejs2'
    import resetButton from "@/components/resetButton/resetButton.vue";

    export default {
        components: {
            SgsDateRange: resolve => require(['./../../components/date/date-range'], resolve),
            resetButton
        },
        data() {
            return {
                 pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < new Date('2022-01-01')-8.64e7;
                    },
                },
                qrLoading: false,
                wechatUrl: '',
                payHtml: '',
                dialogPay: false,
                payTypes: [],
                payType: null,
                totalAmout: 0,//总金额
                dialogPayInvoiceInfo: false,
                selectedInvocieData: [],
                conditions: {
                    reportNo: '',
                    clientReferenceNo: '',
                    labName: ''
                },
                tableData: [],
                searchReport: '',
                search: '',
                loading: false,
                form: {},
                query: {
                    pageSize: 10,
                    pageNo: 1,
                    issuedStartDate: null,
                    issuedEndDate: null,
                    fapiaoNo: '',
                    contactName: '',
                    invoiceNo: '',
                    paymentStatus: ''
                },
                submitForm: {
                    payType: '',
                    totalAmount: '',
                    frontUrl: '',
                    invoiceList: []
                },
                page: {
                    total: 0
                },

            }
        },
        filters: {
            rounding(value) {
                return value.toFixed(2)
            }
        },
        watch: {
            'conditions.reportNo': function (newVal) {
                debugger;
                //获取路由参数
            },
        },
        methods: {
            //完成支付，关闭支付弹框，重新触发刷新
            completePayment() {
                this.dialogPay = false;
                //设置默认查询时间最近一个月
                this.query.pageNo = 1;
                this.query.issuedStartDate = moment().subtract('days', 30).format('YYYY-MM-DD');
                this.query.issuedEndDate = moment().format('YYYY-MM-DD');
                this.onLoad();
            },
            submitPayment() {
                //验证是否选择支付类型
                if (validatenull(this.payType)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('invoice.validate.selectPayType'),
                        type: 'warning'
                    });
                    return false;
                }
                this.submitForm.payType = this.payType;
                //传入总金额
                this.submitForm.totalAmount = this.totalAmout;
                //初始化数据
                this.submitForm.invoiceList = [];
                //封装需要支付的Invoice信息
                this.selectedInvocieData.find((invoice) => {
                    var inovice = {};
                    inovice.fcode = invoice.fcode;
                    inovice.invoiceNo = invoice.invoiceNo;
                    this.submitForm.invoiceList.push(inovice);
                });
                //开启loading
                const loading = this.$loading({
                    lock: true,
                    text: '正在前往支付页面',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                })
                //提交建单数据
                createInvoiceOrder(this.submitForm).then(res => {
                    let result = res.data.data;
                    if (!validatenull(result)) {
                        if (result.errorCode != 0) {//错误 给出提示
                            loading.close();
                            this.$notify({
                                title: this.$t('tip'),
                                message: result.errorMessage,
                                type: 'warning'
                            });
                            return false;
                        }
                        loading.close();
                        var payHtml = result.result.payResult;
                        if (this.submitForm.payType == '100000') {
                            var doc = window.open().document;
                            doc.write("<html><head><title></title></head><body><br/>");
                            doc.write(payHtml);
                            doc.write("</body></html>");
                        } else if (this.submitForm.payType == '100002') {
                            var doc = window.open().document;
                            doc.write(payHtml);
                        } else if (this.submitForm.payType == '100001') {//微信支付
                            this.dialogPay = true;
                            this.qrLoading = true;
                            // new QRCode(document.getElementById("qrcode"), payHtml);
                            setTimeout(() => {
                                this.wechatUrl = payHtml;
                                //先清空原有qrCode
                                document.getElementById("qrcode").innerHTML = "";
                                var qrcode = new QRCode(this.$refs.qrCodeUrl, {
                                    text: this.wechatUrl,
                                    width: 300,
                                    height: 300,
                                    colorDark: '#000000',
                                    colorLight: '#ffffff',
                                    correctLevel: QRCode.CorrectLevel.H
                                })
                                this.qrLoading = false;
                            }, 1000);
                        }
                         this.onLoad();
                    }
                }).catch(() => {
                    loading.close();
                });
                this.dialogPayInvoiceInfo = false;

                /*this.$router.push({
                    path: '/payError',
                })*/
            },
            //合并支付
            mergePayBtnPay() {
                //校验
                if (this.selectedInvocieData.length == 0) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('invoice.validate.selectPayInvice'),
                        type: 'warning'
                    });
                    return false;
                }
                //验证是否存在已支付的账单，存在的话 不允许再次支付
                var payFlag = true;
                var payStatusFlag = true;
                var invoiceNo = "";
                var sumAmout = 0;
                debugger;
                this.selectedInvocieData.find((invoice) => {
                    if (invoice.paymentStatus == 1) {
                        payFlag = false;
                        invoiceNo = invoice.invoiceNo;
                    }
                    if(!validatenull(invoice.batchNo)){
                        payStatusFlag=false;
                    }
                    sumAmout += parseFloat(invoice.invoiceBalance)
                });

                if (!payStatusFlag) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: invoiceNo + ' ' + this.$t('invoice.validate.payOrderTip'),
                        type: 'warning'
                    });
                    return false;
                }

                if (!payFlag) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: invoiceNo + ' ' + this.$t('invoice.validate.alreadyPaid'),
                        type: 'warning'
                    });
                    return false;
                }
                //1、计算总金额，并予以展示
                this.totalAmout = parseFloat(sumAmout).toFixed(2);
                //2、获取支付方式，并展示
                this.payType = null;
                this.dialogPayInvoiceInfo = true;
            },
            queryPayType() {
                getPayType().then(res => {
                    let payTypesData = res.data.data;
                    if (!validatenull(payTypesData)) {
                        this.payTypes = res.data.data;
                    }
                });
            },
            handleSelectionChange(val) {
                this.selectedInvocieData = [];
                this.selectedInvocieData = val;
            },
            searchReportMethod() {
                this.tableData = this.tableData.filter(data => !this.searchReport || data.reportNo.toLowerCase().includes(this.searchReport.toLowerCase()));
            },
            filterChange(filterObj) {
                debugger;
                console.log(filterObj)
            },
            async updateStartDate(val) {

                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.issuedStartDate = date;
            },
            async updateEndDate(val) {
                debugger;
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.issuedEndDate = date;
            },
            checkTime() {
                var begintime = this.query.issuedStartDate;
                var endtime = this.query.issuedEndDate;
                if (validatenull(endtime)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.endDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }

                var time1 = new Date(begintime).getTime();
                var time2 = new Date(endtime).getTime();
                if (validatenull(begintime)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.startDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                //判断开始时间是否早于2022-01-01

                if(time1 < new Date('2022-01-01').getTime()){
                      debugger;
                     this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.startMaxError'),
                        type: 'warning'
                    });
                    return false;
                }

                if (validatenull(endtime == '')) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.endDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                if (time1 > time2) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.endDateErrorValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                debugger;
                //判断时间跨度是否大于6个月  修改为12个月
                var flag = this.checkDateSection(begintime, endtime);
                if (!flag) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.betweenDateValidate'),
                        type: 'warning'
                    });
                    return false;
                }
                //验证当前时间最多向前一年
                endtime = moment().format('YYYY-MM-DD');
                flag = this.checkDateSection(begintime, endtime);
                if (!flag) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.betweenDateValidateOneYear'),
                        type: 'warning'
                    });
                    return false;
                }


                return true;
            },

            billClick(row) {
                window.open(row.invoicePDFFileUrl, '_blank')
            },
            eFapiaoClick(row) {
                window.open(row.efapiaoFileUrl, '_blank')
            },
            async updateStartDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.issuedStartDate = date;
                //this.onLoad();
            },
            async updateEndDate(val) {
                let date = null
                if (val) {
                    date = moment(val).format('YYYY-MM-DD')
                }
                this.query.issuedEndDate = date;
                // await this.onLoad();

            },

            onReset() {
                this.query.pageNo = 1;
                this.query.issuedStartDate = moment().subtract('days', 30).format('YYYY-MM-DD');
                this.query.issuedEndDate = moment().format('YYYY-MM-DD');
                this.query.fapiaoNo = '';
                this.query.contactName = '';
                this.query.invoiceNo = '';
                this.query.paymentStatus = '';
                this.onLoad();
            },
            checkDateSection(begintime, endtime) {
                var flag = true;
                var arr1 = begintime.split('-');
                var arr2 = endtime.split('-');

                arr1[1] = parseInt(arr1[1]);
                arr1[2] = parseInt(arr1[2]);
                arr2[1] = parseInt(arr2[1]);
                arr2[2] = parseInt(arr2[2]);
                debugger;
                if (arr1[0] == arr2[0]) {//同年
                    if (arr2[1] - arr1[1] > 12) { //月间隔超过6个月
                        flag = false;
                    } else if (arr2[1] - arr1[1] == 12) { //月相隔3个月，比较日
                        if (arr2[2] > arr1[2]) { //结束日期的日大于开始日期的日
                            flag = false;
                        }
                    }
                } else { //不同年
                    if (arr2[0] - arr1[0] > 1) {
                        flag = false;
                    } else if (arr2[0] - arr1[0] == 1) {
                        if (arr1[1] < 1) { //开始年的月份小于1时，不需要跨年
                            console.log("arr1[1] < 7");
                            flag = false;
                        } else if (arr1[1] + 12 - arr2[1] < 12) { //月相隔大于12个月
                            console.log("arr1[1]+12-arr2[1] < 12");
                            flag = false;
                        } else if (arr1[1] + 12 - arr2[1] == 12) { //月相隔3个月，比较日
                            if (arr2[2] > arr1[2]) { //结束日期的日大于开始日期的日
                                console.log("截止日 arr2[2] > " + arr2[2]);
                                console.log("开始日 arr1[2] > " + arr1[2]);
                                flag = false;
                            }
                        }
                    }
                }
                return flag;
            },
            onSearch() {
                this.query.pageNo = 1;
                this.onLoad();
            },
            onLoad() {
                // this.query.issuedStartDate = '';
                // this.query.issuedEndDate = '';
                var loadFlag = true;
                if (validatenull(this.query.issuedStartDate) && validatenull(this.query.issuedEndDate)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('dateValidate.startDateAndEndDateValidate1'),
                        type: 'warning'
                    });
                    return false;
                }
                loadFlag = this.checkTime();
                if (!loadFlag) {
                    return false;
                }
                this.loading = true;
                getList(this.query).then(res => {
                    this.loading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(pageNo) {
                this.query.pageNo = pageNo;
                this.onLoad();
            },
            sizeChange(pageSize) {
                this.query.pageNo = 1;
                this.query.pageSize = pageSize;
                this.onLoad();
            },

            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                    .then(() => {
                        remove(row.id);
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad();
                    });

            }
        },
        created() {
            //设置默认查询时间最近一个月
            this.query.pageNo = 1;
            this.query.issuedStartDate = moment().subtract('days', 30).format('YYYY-MM-DD');
            this.query.issuedEndDate = moment().format('YYYY-MM-DD');
            this.onLoad();
            //查询当前用户的支付方式
            this.queryPayType();
        },
        computed: {
            ...mapGetters([
                "userInfo",
                "permission"
            ]),
            permissionList() {
                return {
                    searchBtn: this.vaildData(this.permission['sgs:invoice:searchBtn'], false),
                    mergePayBtn: this.vaildData(this.permission['sgs:invoice:mergePayBtn'], false),
                    billFileBtn: this.vaildData(this.permission['sgs:invoice:billFileBtn'], false),
                    invoiceFileBtn: this.vaildData(this.permission['sgs:invoice:invoiceFileBtn'], false),
                };
            },
            newTableData() {
                debugger;
                return Object.keys(this.conditions).reduce((list, key) => {
                    if (!this.conditions[key]) return list;
                    list = list.filter(item =>
                        (validatenull(item[key]) ? "" : item[key]).toLowerCase().includes(this.conditions[key].toLowerCase())
                    );
                    return list
                }, this.tableData);
            }
        },

    }
</script>

<style scoped>


    .ml_05em {
        margin-left: 0.5em !important;
    }

    .mt_03em {
        margin-top: 0.3em !important;
    }

    .mr_3 {
        margin-right: 3%;
    }

    .fl {
        float: left !important;
    }

    .bg_title_tint {
        background-color: #eeeff6 !important;
    }

    .price-num {
        color: #e4393c;
        font-family: Verdana;
        font-weight: 700;
        font-size: 18px;
        min-width: 122px;
        _width: 122px;
        float: right;
        *float: none;
        text-align: right;
    }
    .table-list /deep/ .el-table th > .cell {
        height: 100%;
    }
    .table-list /deep/ .el-table th .el-input__inner {
        background: transparent;
        padding-left: 0;
    }
</style>
