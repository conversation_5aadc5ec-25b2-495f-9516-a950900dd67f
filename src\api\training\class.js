import request from '@/router/axios';
import {trainingUrl} from '@/config/env';

export const add = (classParam, fileList, traineeList) => {
    return request({
        url: '/api/sgs-training/class/submit',
        method: 'post',
        data: classParam
    })
}

export const selSuppliesList = (customerGroupCode, isLanguage) => {
    return request({
        url: '/api/sgs-training/class/sel-supplies-list',
        method: 'post',
        params: {
            "customerGroupCode" : customerGroupCode,
            "language" : isLanguage
        }
    })
}
export const Get_Course = () => {
    return request({
        url: '/api/sgs-training/course/page',
        method: 'post',
        data: {}
    })
}
export const selectClassById = (classId) => {
    return request({
        url: '/api/sgs-training/class/sel-class-by-id',
        method: 'post',
        data: {"id":classId}
    })
}
/*export const selLocationList = (locationName) => {
    return request({
        url: 'https://apidev.sgsonline.com.cn/FrameWorkApi/trims/api/v1/queryLocationList',
        method: 'post',
        contentType: "application/json",
        data:locationName
    })
}*/
export const selLocationList = (params) => {
    return request({
        url: '/api/sgs-mart/sgs-api/queryLocations',
        method: 'post',
        data: params
    })
}


export const selCourseModule = (courseId) => {
    return request({
        url: '/api/sgs-training/course/selModule',
        method: 'post',
        data: courseId
    })
}
export const selModuleById = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selModuleById",
        changeOrigin: true,
        data: id
    })
}
export const uploadClassMaterials = (cloudId) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selFileUrl",
        changeOrigin: true,
        data: cloudId
    })
}
export const approve = (id,approveStatus) => {
    return request({
        url: '/api/sgs-mart/customer/approve',
        method: 'get',
        params: {
            id,
            approveStatus
        }
    })
}
