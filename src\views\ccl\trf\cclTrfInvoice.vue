<template>
    <div class="sgs-box">
        <h4 class="sgs-title">{{$t('invoice.name')}}</h4>
        <el-row>
            <div style="position: relative;text-align: left; margin-left: 260px; margin-top: -60px;">
                <el-form-item label-width="0px" v-if="valiUsable('customerInvoiceId')"  prop="invoice.customerInvoiceId" :rules="{required:valiRequired('customerInvoiceId'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                    <el-select v-model="invoice.customerInvoiceId" @change="invoiceChange" clearable
                               style="width:40.5%">
                        <el-option v-for="(obj,index) in invoiceData"
                                   :label="obj.invoiceTitle"
                                   :value="obj.id"></el-option>
                    </el-select>
                    <el-button type="primary" :loading="btnSubmitLoading" style="margin-left: 12px;"
                               @click="addInvoiceClick()">{{$t('operation.add')}}
                    </el-button>
                </el-form-item>
            </div>
        </el-row>
        <div v-if="invoiceShowFlag">
            <el-row :gutter="20" style="margin-top:10px">
                <el-col span="24">
                    <el-form-item :label="$t('invoice.invoiceTitle')"  v-if="valiUsable('invoiceTitle')" prop="invoice.invoiceTitle" :rules="{required:valiRequired('invoiceTitle'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                        <el-input maxlength="200" v-model="invoice.invoiceTitle" :disabled="invoiceDisableFlag"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" style="margin-top:10px">
                <el-col :span="12">
                    <el-form-item :label="$t('invoice.invoiceType')" v-if="valiUsable('invoiceType')" prop="invoice.invoiceType" :rules="{required:valiRequired('invoiceType'),message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                        <el-radio-group v-model="invoice.invoiceType" @change="invoiceTypeChange"
                                        :disabled="invoiceDisableFlag" clearable>
                            <el-radio v-for="(obj,index) in invoiceTypeData"
                                      :label="obj.sysKey"
                                      :value="obj.sysKey">{{obj.sysValue}}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item :label="$t('invoice.taxNo')" v-if="valiUsable('taxNo')" prop="invoice.taxNo" :rules="{required:valiRequired('taxNo'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                        <el-input maxlength="50" v-model="invoice.taxNo" :disabled="invoiceDisableFlag"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" class="contact">
                <el-col :span="12">
                    <el-form-item :label="$t('invoice.address')" v-if="valiUsable('invoiceAddress')" prop="invoice.invoiceAddress" :rules="{required:valiRequired('invoiceAddress'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                        <el-input maxlength="200" v-model="invoice.invoiceAddress"
                                  :disabled="invoiceDisableFlag"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item :label="$t('invoice.tel')" v-if="valiUsable('invoiceTel')" prop="invoice.invoiceTel" :rules="{required:valiRequired('invoiceTel'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                        <el-input maxlength="100" v-model="invoice.invoiceTel" :disabled="invoiceDisableFlag"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" class="contact">
                <el-col span="12">
                    <el-form-item :label="$t('invoice.bankName')" v-if="valiUsable('bankName')" prop="invoice.bankName" :rules="{required:valiRequired('bankName'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                        <el-input maxlength="100" v-model="invoice.bankName" :disabled="invoiceDisableFlag"></el-input>
                    </el-form-item>
                </el-col>
                <el-col span="12">
                    <el-form-item :label="$t('invoice.bankAccount')" v-if="valiUsable('bankAccount')" prop="invoice.bankAccount" :rules="{required:valiRequired('bankAccount'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                        <el-input maxlength="100" v-model="invoice.bankAccount" :disabled="invoiceDisableFlag"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
    import {validatenull,validateEmail} from "@/util/validate";
    import {getInvoiceType,checkExistsInvoice} from "@/api/customer/customerInvoice";
    import {getCustomerInvoiceList} from "@/api/trf/trf";
    import {mapGetters} from "vuex";
    export default {
        name: "trfCustomer",
        /*props: {
            trfId:String,
            bossNo:String,
            isSgs:{
                type:Boolean,
                default:false
            },
        },*/
        props:['trfId','bossNo','isSgs','trfInvoice','fieldSettingsData'],
        data() {
            return {
                invoiceShowFlag: false,
                invoiceDisableFlag: true,
                invoiceData:[],
                invoice:{},
                invoiceTypeData: [],
                invoiceParam:{},
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission"]),
        },
        watch: {
            invoice:{
                handler(){   //注意此处就是handler
                    console.log('trfInvoice',this.invoice);
                    this.$emit('update:trfInvoice',this.invoice);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            //监听语言变化
            language: function (newVal) {
                //触发查询
                this.queryInvoiceType();
            },
        },
        created() {
            var companyId = this.userInfo.companyId
            this.$set(this.invoiceParam,'customerId',companyId);
            this.$set(this.invoiceParam,'status','1');
            //查询发票类型
            this.queryInvoiceType();
            //查询发票信息
            this.searchInvoice(this.invoiceParam);
            //判断是否为SGS用户
            if(this.isSgs){

            }

        },
        methods: {
            //验证表单项是否展示方法
            valiUsable(code){
                var usableFlag=true;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        usableFlag=  this.fieldSettingsData[code].usable!=1?false:true
                    }
                }
                return usableFlag;
            },
            //验证必填项方法
            valiRequired(code){
                var requiredFlag=false;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        requiredFlag=  this.fieldSettingsData[code].required==1?true:false
                    }
                }
                return requiredFlag;
            },
            invoiceChange(val) {
                this.invoice = {};
                if (val == '' || val == null) {
                    this.invoiceShowFlag = false;
                }
                let obj = {};
                obj = this.invoiceData.find((item) => {
                    return item.id === val;
                });
                if(!validatenull(obj)){
                    this.invoiceShowFlag = true;
                    this.invoiceDisableFlag = true;
                    this.invoice=obj;
                  /*  this.$set(this.trf.invoice,'customerInvoiceId', obj.id);
                    this.$set(this.trf.invoice,'invoiceTitle', obj.invoiceTitle);
                    this.$set(this.trf.invoice,'invoiceType', obj.invoiceType+'');
                    this.$set(this.trf.invoice,'invoiceTypeName', obj.invoiceTypeName);
                    this.$set(this.trf.invoice,'taxNo', obj.taxNo);
                    this.$set(this.trf.invoice,'invoiceAddress', obj.invoiceAddress);
                    this.$set(this.trf.invoice,'invoiceTel', obj.invoiceTel);
                    this.$set(this.trf.invoice,'bankName', obj.bankName);
                    this.$set(this.trf.invoice,'bankAccount', obj.bankAccount);*/
                }
            },
            addInvoiceClick() {
                this.invoice = {};
                this.invoiceChange('');
                this.invoiceShowFlag = true;
                this.invoiceDisableFlag = false;
            },
            queryInvoiceType() {
                getInvoiceType(this.language).then(res => {
                    const data = res.data;
                    if (data.length > 0) {
                        this.invoiceTypeData = data;
                    }
                });
            },
            //查询发票数据
            searchInvoice(invoiceParam) {
                var param = {};
                getCustomerInvoiceList(Object.assign(param, invoiceParam)).then(res => {
                    const data = res.data.data;
                    this.invoiceData = data;
                    if (data) {
                        let obj = {};
                        obj = this.invoiceData.find((item) => {
                            return item.isDefault === 1;
                        });

                        if(!validatenull(obj)){
                            this.invoiceShowFlag = true;
                            this.invoiceDisableFlag = true;
                            this.invoice=obj;
                           /* this.$set(this.invoice,'customerInvoiceId', obj.id);
                            this.$set(this.trf.invoice,'invoiceTitle', obj.invoiceTitle);
                            this.$set(this.trf.invoice,'invoiceType', obj.invoiceType+'');
                            this.$set(this.trf.invoice,'invoiceTypeName', obj.invoiceTypeName);
                            this.$set(this.trf.invoice,'taxNo', obj.taxNo);
                            this.$set(this.trf.invoice,'invoiceAddress', obj.invoiceAddress);
                            this.$set(this.trf.invoice,'invoiceTel', obj.invoiceTel);
                            this.$set(this.trf.invoice,'bankName', obj.bankName);
                            this.$set(this.trf.invoice,'bankAccount', obj.bankAccount);*/
                        }
                    }
                });

            },
        }
    }
</script>

<style scoped>

</style>
