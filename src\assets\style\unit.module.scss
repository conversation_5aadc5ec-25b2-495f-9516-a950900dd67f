// Primary Color (主色是网站的核心颜色，通常用于网站的标志、导航栏、按钮等重要元素，是最能代表品牌形象的颜色。)
$primary-color: #ff6600;

// Secondary Color(辅助色用于补充主色，丰富视觉效果，通常用于背景、边框、文本链接等元素。)
$secondary-color: #ebeef5;

// Neutral Colors(中性色通常包括黑色、白色、灰色等，用于平衡其他颜色，提供背景或分隔元素。)
$neutral-color-1: #f5f5f5;
$neutral-color-2: #303133;
$neutral-color-3: #1b1b1b;
$neutral-color-4: #000000;

// Background Color(背景色是网站页面的底色，通常用于衬托其他元素。)
$background-color: #f5f5f5;

// Text Colors (文本色用于正文、标题、链接等文字内容。)
$text-color: #000;
$text-color-alt: #1b1b1b;
$text-color-value: #656565;

// 文字分割线颜色
$text-divider-color: #e6e6e6;

// 占位符颜色
$placeholder-color: #999;

// Interactive Color (交互色用于表示可交互元素的状态，如按钮的悬停、点击状态，链接的访问状态等。)
$interactive-color: #ff6600;

// 状态颜色
$status-in-progress: #ffb82d;
$status-submitted: #67c23a;
$status-approved: #ff6600;
$status-cancelled: #cacaca;
$status-not-in-use: #618cd2;

// Line Height (行距)
$line-height: 1.5; // 或者 $line-height: 24px;

// Paragraph Spacing (段落间距)
$paragraph-spacing: 16px;

// 模块内间距
$module-padding: 20px 30px;
$module-padding-horizontal: 30px;
$module-padding-vertical: 20px;

// 模块外间距
$module-margin: 20px 30px;
$module-margin-horizontal: 30px;
$module-margin-vertical: 20px;

// 行内元素间距
$inline-element-spacing: 10px;

// Text Indent (首行缩进)
$text-indent: 2em;

// Headline Font Sizes (标题字体大小)
$h1-font-size: 32px;
$h2-font-size: 24px;
$h3-font-size: 18px;
$module-title-font-size: 20px;
$module-sub-title-font-size: 14px;

// Body Font Size (正文字体大小)
$body-font-size: 14px;

// Secondary Font Size (辅助字体大小)
$secondary-font-size: 12px;

// Font Weights (字体粗细)
$font-weight-regular: 400;
$font-weight-bold: 700;
$font-weight-light: 300;

// 边框颜色
$border-color: #d9d9d9; 


// Usage examples
h1 {
  font-size: $h1-font-size;
  font-weight: $font-weight-bold;
}

h2 {
  font-size: $h2-font-size;
  font-weight: $font-weight-regular;
}

h3 {
  font-size: $h3-font-size;
  font-weight: $font-weight-regular;
}

body {
  font-size: $body-font-size;
  font-weight: $font-weight-regular;
}

.secondary-text {
  font-size: $secondary-font-size;
  font-weight: $font-weight-light;
}

:export {
  statusInProgress: $status-in-progress;
  statusSubmitted: $status-submitted;
  statusApproved: $status-approved;
  statusCancelled: $status-cancelled;
  statusNotInUse: $status-not-in-use;
}
