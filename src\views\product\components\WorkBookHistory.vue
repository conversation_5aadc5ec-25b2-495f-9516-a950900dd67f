<template>
    <div class="smart_views_WorkBookHistory" id="smart_views_WorkBookHistory">
        <CommonTable
                border
                v-loading="tableOption.loading"
                ref="linkDocTable"
                style="width:100%"
                row-key="id"
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
        >
            <template #docName="{row,$index}">
                <el-button text type="primary" @click="downloadObj(row)">{{row.docName}}</el-button>
            </template>
            <template #actionColumn="{row}">
                <el-button text type="primary" @click="selectObject(row)">Select</el-button>
            </template>
        </CommonTable>
    </div>
</template>


<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'
import CommonTable from "@/components/TableList/CommonTable.vue";
import productApi from '@/api/product.ts'
import {getFileUrlByCloudId} from '@/api/common'

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)
const props = defineProps({
    objectId:{
        type:String,
        default:''
    },
    relationId:{
        type:String,
        default:''
    }
})
defineOptions({
    name: 'WorkBookHistory'
})
watch(language, (newVal) => {

})
onMounted(() => {
    initTableList()
})

const initTableList = ()=>{
    let param = {
        objectId:props.objectId,
        relationId:props.relationId
    }
    productApi.workbookApi.queryHistory(param).then(res=>{
        console.log("queryHistory List",res)
        if(res.status==200 && res.data ){
            tableOption.dataList = res.data || [];
        }
    })
}
const downloadObj =async (row)=>{
    const { fileUrl } = row
    const res = await getFileUrlByCloudId(fileUrl)
    const downloadURL = res.data
    if (downloadURL) {
        window.open(downloadURL, '_blank')
    }
}


const tableOption = reactive({
    dataList: [],
    originalDataList: [],
    loading: false,
    menuShow: true,
    size: 'small',
    option: {
        hideRowColor: true,
        selection: false,
        selectionDis: ()=>{return true},
        sortSelectionFiledName: '',
        showSortIcon: false,
        index: false,
        action: false,
        actionWidth: 150,
        disableOption:{},
        column: [
            {prop: 'versionNo', label:  'Revision', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateTime', label: 'Date Uploaded', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateUser', label: 'Uploaded By', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'docNo', label: 'Workbook No', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'docName', label: 'Workbook', hide: false, filter: true, slot: true, type: 'Input'},
            {prop: 'description', label: 'Description', hide: false, filter: true, slot: false, type: 'Input'},

        ]
    },
    sort:{
        sortBy:"",
        sortOrder:""
    },
    filters: {},
    page: {
        show: false,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})
</script>

<style lang="scss">
.smart_views_WorkBookHistory {

}
</style>