<template>
  <div class="chat_wrap" v-show="isShow">
    <div class="chat_box">
        <div class="user_list">
            <p class="user_title">
                <img src="@/images/icon7.png" alt="">
            </p>
            <ul class="group_list">
                <li v-for="item in imGroupUsers" @click="userChange(item)">
                    <p class="avatar">{{getImg(item.userName)}}</p>
                    <span class="name">{{item.userName}}</span>
                </li>
            </ul>
        </div>
        <div class="chat">
            <div class="chat_title">
                <span>{{$store.getters.trfNo}}</span>
                <img src="@/images/icon9.png" class="close_icon" alt="" @click="hide">
            </div>
            <div class="chat_list" v-loading='listLoading'>
                <div class="chat_list_inner">
                    <p v-show="listLoading" class="loading">加载中...</p>
                    <div class="chat_item" v-for="(item,index) in list">
                        <p class="starttime">{{getMsgTime(item.senderTime,index)}}</p>
                        <div class="chat_item_info" :class="item.userId==imGroupUser.userId?'right':''">
                            <p class="avatar">{{getImg(item.userName)}}</p>
                            <div class="chat_item_con">
                                <p class="name">{{item.userName}}</p>
                                <div class="content" v-if="item.contentType=='text'">{{item.content}}</div>
                                <div class="content" v-else-if="item.contentType=='image'">
                                    <img :src="item.content" alt="" class="chat_img">
                                </div>
                                <div class="content file_content" v-else-if="item.contentType=='file'" @click="downFile(item)">
                                    <p>{{item.content}}</p>
                                    <img src="@/images/file.png" class="file_icon" alt="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form_box" v-loading="uploadLoading">
                <div class="img_box">
                    <el-upload
                        class="upload-demo"
                        action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                        :on-success="uploadSuccess"
                        :on-change="uploadChange"
                        accept='.jpg,.png,.jpeg'
                        :show-file-list="false">
                        <img src="@/images/icon4.png" alt="">
                    </el-upload>
                    <el-upload
                        class="upload-demo"
                        action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                        :on-success="uploadSuccess1"
                        :on-change="uploadChange"
                        accept='.doc,.pdf,.docx'
                        :show-file-list="false">
                        <img src="@/images/icon5.png" alt="">
                    </el-upload>
                    
                    <!-- <img src="@/images/icon6.png" alt=""> -->
                    <!-- <div class="more">
                        <img src="@/images/icon8.png" alt="">
                    </div> -->
                </div>
                <textarea v-model="con" class="textarea"></textarea>
                <div class="btn_box">
                    <p class="btn" @click="sendText">发送</p>
                </div>
            </div>
        </div>
        <div class="user_info" v-if="groupItem">
            <div class="info_top">
                <p class="avatar">{{getImg(groupItem.userName)}}</p>
                <div class="info">
                    <p class="name">{{groupItem.userName}}</p>
                    <p class="email">{{groupItem.userName}}</p>
                </div>
            </div>
            <p class="concat">联系人</p>
            <div class="concact_info">
                <img src="@/images/icon1.png" alt="">
                <p>{{groupItem.email}}</p>
            </div>
            <div class="concact_info">
                <img src="@/images/icon2.png" alt="">
                <p>{{groupItem.phoneNumber}}</p>
            </div>
            <!-- <div class="concact_info">
                <img src="@/images/icon3.png" alt="">
                <p>{{groupItem.userSource}}</p>
            </div> -->
        </div>
    </div>
  </div>
</template>

<script>
import {downloadByCloudID} from '@/api/trf/trf'
import {mkRslt,checkCh,add0} from '@/util/chat.js'
export default {
    data() {
        return {
            isShow:false,
            isLast:false,
            con:'',// 发送的内容
            kg:true,
            groupItem:'',
            list:[],
            imGroupUsers:[],
            imGroupUser:'',
            uploadLoading:false,
            pages:{
                size:10,time:''
            },
            listLoading:false,
            scrollSt:null,
            scrollHeight:0,
            dom:null,
            init:true,
            trfNo:'',
            isFirst:true, // 是否是第一次加载
            isTips:false // 是否已打开会话关闭弹框
        }
    },
    computed: {
        //监听接收到的消息
        socketMsgs() {
            return this.$store.getters.socketMsgs
        },
        //监听是否连接和重连次数
        socketTask() {
            return {
                webSocketReconnectCount:this.$store.getters.webSocketReconnectCount,
                socketTask:this.$store.getters.socketTask,
            }
        }
    },
    mounted() {
        this.dom = document.querySelector('.chat_list_inner')
    },
    watch: {
        socketTask() {
            if (this.isShow && !this.socketTask.socketTask && this.socketTask.webSocketReconnectCount>=2 && !this.isTips) {
                this.isTips = true
                this.$alert('当前会话已关闭，请重新进入房间再试', '提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                        this.hide()
                    }
                });
            }
        },
        'socketMsgs': {
        //处理接收到的消息
            handler: function() {
                let sMsg = this.socketMsgs
                console.log('sMsg:'+sMsg.bstp,sMsg)
                if (sMsg.bstp == 1003) {
                    // 用户列表更新
                    this.imGroupUsers = sMsg.imGroupUsers
                } else if (sMsg.bstp == 1002) {
                    // 收到群聊消息
                    this.kg = true
                    let imContent = sMsg.imContent
                    for(let key in sMsg.imGroupUser) {
                        if (key!='id') {
                            imContent[key] = sMsg.imGroupUser[key]
                        }
                    }
                    if (imContent.contentType == 'image') {
                        this.downloadByCloudID(imContent.contentFileId,imContent.content,(url)=>{
                            imContent.content = url
                            this.list.push(imContent)
                            this.con = ''
                        })
                    } else if(imContent.contentType == 'file'){
                        let arr = imContent.content.split('/')
                        imContent.content = arr[arr.length-1]
                        this.list.push(imContent)
                        this.con = ''
                    } else {
                        this.list.push(imContent)
                        this.con = ''
                    }
                    this.kg = true;
                    if (imContent.userId == this.imGroupUser.userId) {
                        this.getImgLoadEd(()=>{
                            this.scrollHeight = this.dom.scrollHeight
                            this.scrollToBottom()
                        })
                    }
                } else if (sMsg.bstp == 1004) {
                    // 收到聊天记录
                    setTimeout(()=>{
                        this.listLoading = false
                    },1000)
                    if (sMsg.size == this.pages.size && sMsg.time == this.pages.time) {
                        let arr = sMsg.imContentList
                        if (arr.length < 10) {
                            this.isLast = true
                        } else {
                            this.isLast = false
                        }
                        arr.forEach(ele=>{
                          console.log("读取的历史记录：")
                          console.log(ele)
                            this.list.unshift(ele)
                            if (ele.contentType == 'image') {
                                this.downloadByCloudID(ele.contentFileId,ele.content,(url)=>{
                                    ele.content = url
                                    if (this.isFirst) {
                                        this.$nextTick(()=>{
                                            this.getImgLoadEd(()=>{
                                                setTimeout(()=>{
                                                    this.scrollToBottom()
                                                },1000)
                                            })
                                        })
                                    }
                                })
                            } else if(ele.contentType == 'file'){
                                let arr = ele.content.split('/')
                                ele.content = arr[arr.length-1]
                                if (this.isFirst) {
                                    this.$nextTick(()=>{
                                        this.scrollToBottom()
                                    })
                                }
                            } else {
                                if (this.isFirst) {
                                    this.$nextTick(()=>{
                                        this.scrollToBottom()
                                    })
                                }
                            }
                        })
                        if (!this.isFirst) {
                            this.$nextTick(()=>{
                                
                                this.getImgLoadEd(()=>{
                                    console.log(222)
                                    setTimeout(()=>{
                                        let scrollHeight = this.dom.scrollHeight
                                        this.scrollHeight = scrollHeight - this.scrollHeight
                                        this.scrollToBottom()
                                    },1000)
                                })
                            })
                        }
                    }
                } else if (sMsg.bstp == 1005) {
                    // 收到当前用户信息
                    this.imGroupUser = sMsg.imGroupUser
                    if (this.init) {
                        this.list = []
                        this.pages.time = new Date().getTime()
                        this.sendGetListMsg()
                        this.init = false
                        this.isFirst = true
                    }
                }
            }
        }
    },
    methods:{
        show() {
            this.isShow = true
            this.dom.addEventListener('scroll',(e)=>{
                if (e.target.scrollTop <= 0 && this.list.length>0 && !this.isLast) {
                    if (this.scrollSt) {
                        clearTimeout(this.scrollSt)
                    }
                    this.listLoading = true
                    this.isFirst = false
                    this.scrollSt = setTimeout(()=>{
                        this.pages.time = this.list[0].senderTime
                        this.scrollHeight = this.dom.scrollHeight
                        this.sendGetListMsg()
                    },1500)
                }
            })
            this.$nextTick(()=>{
                this.getImgLoadEd(()=>{
                    this.scrollHeight = 9999999
                    console.log(this.scrollHeight)
                    this.scrollToBottom()
                })
            })
        },
        getImgLoadEd(callback) {
            // 查询容器下所有图片
            let images = document.querySelectorAll('.chat_img')
            if (images.length == 0) {
                callback && callback();
                return
            }
            // Array.prototype.slice.call将images类数组转换成合法数组
            const promises = Array.prototype.slice.call(images).map(node => {
                return new Promise((resolve, reject) => {
                    let loadImg = new Image();
                    loadImg.src = node.src;
                    loadImg.onload = () => {
                        resolve(node);
                    }
                    loadImg.complete = ()=>{
                        resolve(node);
                    }
                });
            });
            // 利用Promise.all监听所有图片加载完成
            Promise.all(promises).then(results => {
                callback && callback();
            }).catch(e => {
                callback && callback();
            })
        },
        scrollToBottom() {
            this.$nextTick(()=>{
                this.dom.scrollTop = this.scrollHeight
                // console.log(this.dom.scrollTop,this.dom.scrollHeight)
            })
        },
        hide() {
            this.list = []
            this.imGroupUsers = []
            this.imGroupUser = ''
            this.groupItem = ''
            this.isFirst = true
            this.init = true
            this.isLast = false
            this.con = ''
            this.kg = true
            this.uploadLoading = false
            this.pages.time = ''
            this.listLoading = false
            this.scrollSt = null
            this.scrollHeight = 0
            this.trfNo = ''
            this.isShow = false
            this.$store.dispatch('webSocketClose');
            this.$emit('hideChatDialog')
        },
        sendText() {
            if (!this.kg) return
            let con = this.con
            if (con == ''){
                this.setScrollTop()
            } else {
                this.kg = false;
                this.sendMsg(con,'text','')
            }
        },
        uploadSuccess(res, file) {
            console.log(res)
            if (res.status == 200 && res.data && res.data.length>0) {
                this.sendMsg(res.data[0].path,'image',res.data[0].cloudID)
            } else {
                this.$message('上传失败')
            }
            
        },
        uploadSuccess1(res, file) {
            if (res.status == 200 && res.data && res.data.length>0) {
                let path = res.data[0].path
                let type = 'file'
                if (/\.(gif|jpg|jpeg|png|GIF|JPEG|JPG|PNG)$/.test(path)) {
                    type = 'image'
                }
                this.sendMsg(path,type,res.data[0].cloudID)
            } else {
                this.$message('上传失败')
            }
        },
        uploadChange(file, fileList) {
            if (file.status == 'ready') {
                //开启loading效果
                this.uploadLoading = true;
            } else {
                this.uploadLoading = false;
            }
        },
        // 获取聊天内容
        sendGetListMsg() {
            this.$store.dispatch('webSocketSend',{
                bstp:1004,
                time:this.pages.time,
                size:this.pages.size
            });
        },
        // 发送聊天内容
        sendMsg(msg,file_type,contentFileId) {
            this.$store.dispatch('webSocketSend',{
                bstp:1002,
                contentType:file_type,
                content:msg,
                contentFileId:contentFileId
            });
        },
        downloadByCloudID(cloudId,fileName,callback) {
            let param = {
                systemID:1,
                networkType:2,
                hash:new Date().getTime(),
                cloudID:cloudId
            }
            downloadByCloudID(param).then(res=>{
                if (res.data) {
                    let link = res.data.replace(/\s*/g,'')
                    let fileUrl = encodeURIComponent(link)
                    let url = '/api/sgs-mart/afl/trf/fileDownload?fileUrl='+fileUrl+'&fileUrlName='+fileName
                    callback&&callback(url)
                }
            }).catch(err=>{
                
            })
        },
        // 下载文件
        downFile(item) {
            this.downloadByCloudID(item.contentFileId,item.content,(url)=>{
                let dom = document.createElement('a')
                dom.className = 'downDocFile'
                dom.setAttribute('href',url)
                dom.click()
                dom.remove()
            })
        },
        userChange(item) {
            this.groupItem = item
        },
        // 根据名字返回头像
        getImg(str) {
            if (/^[a-zA-Z]+/g.test(str)) {
                let arr = str.split(' ')
                let name = ''
                arr.forEach(n=>{
                    name+=n.slice(0,1).toUpperCase()
                })
                return name[0]
            }
            var arrResult = new Array();
            for(var i=0,len=str.length;i<len;i++){
                var ch = str.charAt(i);
                arrResult.push(checkCh(ch));
            }
            let arr = mkRslt(arrResult)
            return arr.length>0?arr[0]:'';
        },
        //根据两个时间对比判断是否显示时间
        getMsgTime(time,index){
            if (index == 0) return this.getTimeDate(time)
            let time1 = this.list[index-1].senderTime
            if (time - time1 > 5*60*1000) return this.getTimeDate(time)
        },
        // 根据时间戳转日期
        getTimeDate(shijianchuo) {
            var time = new Date(shijianchuo);
            var y = time.getFullYear();
            var m = time.getMonth()+1;
            var d = time.getDate();
            var h = time.getHours();
            var mm = time.getMinutes();
            var s = time.getSeconds();
            var time1 = new Date()
            var y1 = time1.getFullYear();
            var m1 = time1.getMonth()+1;
            var d1 = time1.getDate();
            if (y1 - y >= 1) {
                return y+'年'+add0(m)+'月'+add0(d)+'日 '+add0(h)+':'+add0(mm);
            } else if (m1 - m >= 1 || d1 - d > 1) {
                let am = ''
                if (h < 12) {
                    am = '上午'
                } else if (h < 18) {
                    am = '下午'
                } else {
                    am = '晚上'
                }
                return add0(m)+'月'+add0(d)+'日 '+am+add0(h)+':'+add0(mm);
            } else if (d1 - d == 1) {
                return '昨天 '+add0(h)+':'+add0(mm);
            } else {
                return add0(h)+':'+add0(mm);
            }
        },
    }
}
</script>

<style scoped>
    .chat_wrap{
        position: fixed;
        left: 0;
        top:0;
        bottom: 0;
        right:0;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999;
    }
    .chat_box{
        height: 620px;
        background: #fff;
        display: flex;
        align-items: center;
        border:1px solid #ddd;
        box-sizing: border-box;
    }
    .user_list{
        width: 260px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .chat{
        width: 630px;
        height: 100%;
        background: #f3f3f3;
        display: flex;
        flex-direction: column;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        box-sizing: border-box;
    }
    .user_info{
        width: 360px;
        height: 100%;
        padding:0 20px;
        box-sizing: border-box;
    }
    .user_title{
        height: 90px;
        background: #ee6600;
        padding:20px 10px 0 10px;
        box-sizing: border-box;
        margin:0;
    }
    .user_title img{
        width: 112px;
        height: 50px;
    }
    .group_list{
        padding:20px;
        list-style: none;
        flex:1;
        min-height: 0;
        overflow-y: auto;
    }
    .group_list li{
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        cursor: pointer;
    }
    .avatar{
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        border-radius: 5px;
        margin:0 15px 0 0;
        border:1px solid #eee;
        text-align: center;
        line-height: 40px;
        color:#ee6600;
        background: #e2e2e2;
        user-select: none;
    }
    .group_list .name{
        font-size: 14px;
        white-space: nowrap;
        text-overflow:ellipsis;
        overflow:hidden;
    }
    .chat_title{
        font-size:28px;
        height: 50px;
        line-height: 50px;
        padding:0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .close_icon{
        cursor: pointer;
        padding:20px 0;
    }
    .chat_list{
        flex:1;
        min-height: 0;
        background-color: #e4e4e4;
    }
    .chat_list_inner{
        height: 100%;
        overflow-y: scroll;
        padding:40px 20px 20px;
        box-sizing: border-box;
        position: relative;
    }
    .chat_list_inner::-webkit-scrollbar {
      display: none;/*隐藏滚动条*/
    }
    .loading{
        text-align: center;
        padding-top:10px;
        position: absolute;
        left: 0;
        top:0;
        width:100%;
        color:#ee6600;
    }
    .chat_item{
        margin-bottom: 20px;
    }
    .starttime{
        text-align: center;
        font-size: 14px;
        line-height: 30px;
    }
    .chat_item_info{
        display: flex;
    }
    .chat_item .name{
        font-size: 16px;
        margin-bottom: 4px;
    }
    .chat_item .avatar{
        font-size: 16px;
        width: 40px;
        height: 40px;
        line-height: 40px;
        background: white;
    }
    .chat_item .content{
        background: #fff;
        border-radius: 5px;
        padding:10px;
        font-size: 14px;
        display: inline-block;
    }
    .content img{
        max-width: 300px;
    }
    .chat_item_info.right{
        flex-direction:row-reverse;
    }
    .chat_item_info.right .avatar{
        margin:0 0 0 15px;
    }
    .chat_item_info.right .name{
        text-align: right;
    }
    .chat_item_info.right .content{
        background: #ee6600;
        color:#fff;
        float: right;
    }
    .file_content{
        display: flex!important;
        align-items: center;
        cursor: pointer;
    }
    .file_content p{
        flex:1;
        min-width: 0;
        margin:0 10px 0 0;
        font-size: 18px;
        word-break: break-all;
        word-wrap: break-word;
    }
    .file_content img{
        width: 30px;
        height: 30px;
        flex-shrink: 0;
    }
    .form_box{
        height: 210px;
        border-top: 1px solid #ddd;
        background: #fff;
        display: flex;
        flex-direction: column;
    }
    .img_box{
        padding:10px 20px;
        display: flex;
        align-items: center;
    }
    .img_box img{
        margin-right: 20px;
        cursor: pointer;
    }
    .more{
        flex:1;
        min-width: 0;
        text-align: right;
    }
    .more img{
        margin:0;
    }
    .textarea{
        width: 100%;
        border:none;
        outline: none;
        flex:1;
        min-height: 0;
        resize: none;
        background: #fff;
        padding:10px;
        box-sizing: border-box;
    }
    .btn_box{
        text-align: right;
        padding:20px;
    }
    .btn_box .btn{
        padding:6px 20px;
        color:#fff;
        background: #ee6600;
        border-radius: 4px;
    }
    .info_top{
        padding:30px 0;
        display: flex;
        align-items: center;
    }
    .info{
        flex:1;
        min-width: 0;
    }
    .info .name{
        font-size: 14px;
        margin: 0;
    }
    .info .email{
        font-size: 16px;
        margin: 0;
        color:#a1a1a1;
    }
    .concat{
        font-size: 17px;
        color:#333333;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .concact_info{
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
    .concact_info p{
        flex:1;
        min-width: 0;
        padding-left: 10px;
        box-sizing: border-box;
        font-size: 17px;
        color:#525252;
        margin:0;
    }
</style>