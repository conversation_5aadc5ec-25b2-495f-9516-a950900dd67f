<template>
    <div class="register-container">

        <el-card>
            <el-steps :active="active" align-center>
                <el-step :title="$t('register.setUserName')"></el-step>
                <el-step :title="$t('register.accountInfo')"></el-step>
                <el-step :title="$t('register.registSuccess')"></el-step>
            </el-steps>

            <div class="sgs-group">
                <el-row>
                    <div class="right">
                        <top-lang :loadMenu="false"></top-lang>
                    </div>
                </el-row>
            </div>
            <el-form v-if="active==1" style="margin-top: 50px" :model="accountApplyForm" ref="accountApplyForm"
                     :rules="applyRules" label-width="160px"
                     class="demo-ruleForm" size="small">
                <div v-if="accountApplyForm.applyFlag==0">
                    <el-row>
                        <el-col :span="12" :offset="5">
                            <el-form-item :label="$t('account.email')" prop="email">
                                <el-input v-model="accountApplyForm.email" maxlength="150"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12" :offset="5">
                            <a @click="accountApplyForm.applyFlag = 1" style="cursor: pointer;color: #ff6600">
                                {{$t('register.phoneRegister')}}</a>
                        </el-col>
                    </el-row>
                </div>
                <div v-if="accountApplyForm.applyFlag==1">
                    <el-row>
                        <el-col :span="12" :offset="5">
                            <el-form-item :label="$t('customer.contactMobile')" prop="applyContactMobile">
                                <el-input
                                        clearable
                                        v-model="accountApplyForm.applyContactMobile"
                                        maxlength="11">
                                    <el-button :disabled="applyShowSendSmsBtn" slot="append" @click="applySendSMSCode">
                                        <span v-show="applyShowCounter">{{$t('sms.sendCode')}}</span>
                                        <span v-show="!applyShowCounter" class="count">{{applyCounter}} s</span>
                                    </el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12" :offset="5">
                            <el-form-item :label="$t('sms.code')" prop="checkCode" :error="codeErrorMsg">
                                <el-input
                                        clearable
                                        maxlength="4"
                                        v-model="accountApplyForm.checkCode"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12" :offset="5">
                            <a @click="accountApplyForm.applyFlag=0" style="cursor: pointer;color: #ff6600">{{$t('register.emailRegister')}}</a>
                        </el-col>
                    </el-row>
                </div>
                <el-row style="text-align: center;margin-top: 30px;">
                    <router-link type="text" :to="{path:'/'}" style="padding-right: 20px;">
                        {{$t('register.returnLogin')}}
                    </router-link>
                    <el-button size="small" type="primary" :disabled="applySubmitDisabled" :loading="applySubmitLoading"
                               @click="onSubmitApply">{{$t('register.submitRegister')}}
                    </el-button>
                </el-row>
            </el-form>

            <el-form v-if="active==2" :model="form" ref="registerForm" status-icon :rules="rules" label-width="160px"
                     class="demo-ruleForm" size="small">
                <div class="sgs-group" style="width: 95%;">
                    <h3>{{$t('customer.title.admin')}}</h3>
                </div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('register.loginAccount')">
                            <el-input
                                    maxlength="200"
                                    clearable v-model="loginAccount" :disabled="true"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.userName')" prop="userName" :rules="{required:true,message:this.$t('register.accountBlur'),trigger:'blur',pattern: '[^ \x22]+'}" >
                            <el-input
                                    maxlength="100"
                                    clearable v-model="form.userName" autoComplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.email')" prop="email">
                            <el-input
                                    maxlength="150"
                                    clearable v-model="form.email" :placeholder="$t('register.emailBlur')"
                                    :disabled="emailDisabled"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('register.phoneNumber')">
                            <el-input
                                    maxlength="11"
                                    clearable v-model="form.contactMobile" :disabled="contactMobileDisabled"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.password')" prop="password">
                            <el-input
                                    maxlength="50"
                                    clearable type="password" v-model="form.password" autocomplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.passwordConfirm')" prop="doublePassword">
                            <el-input
                                    maxlength="50"
                                    clearable type="password" v-model="form.doublePassword"
                                    auto-complete="new-password"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--<el-row>
                    <el-form-item :label="$t('systemLanguage')" >
                        <el-select v-model="form.language"
                                      :placeholder="$t('service.serviceType')"
                                      clearable>
                                 <el-option label="中文"
                                          value="zh-CN"></el-option>
                               <el-option label="English"
                                          value="en-US"></el-option>
                           </el-select>
                       </el-form-item>
                </el-row>-->

                <div class="sgs-group">
                    <el-row>
                        <h3>{{$t('customer.title.base')}}</h3>
                    </el-row>
                </div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.name')" prop="customerName"  :rules="{required:true,message:this.$t('register.companyNameBlur'),trigger:'blur',pattern: '[^ \x22]+'}" >
                            <el-autocomplete ref="autocomplete"
                                             clearable
                                             v-model="form.customerName"
                                             :fetch-suggestions="querySearch"
                                             :placeholder="$t('register.companyNameBlur')"
                                             :trigger-on-focus="false"
                                             @select="handleSelect"
                                             @blur="checkExists"
                                             maxlength="200"
                                             style="width: 100%"
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.taxNo')" prop="taxNo"
                                      :placeholder="$t('register.taxNoBlur')">
                            <el-input clearable ref="taxNo" v-model="form.taxNo"
                                      maxlength="50"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div v-if="isShow">
                    <!--<el-row>
                        <el-col :span="12">
                            <el-form-item :label="$t('customer.contactMobile')" prop="contactMobile">
                                <el-input
                                        clearable
                                        v-model="form.contactMobile"
                                        maxlength="11">
                                    <el-button :disabled="showSendSmsBtn" slot="append" @click="sendSMSCode">
                                        <span v-show="showCounter">{{$t('sms.sendCode')}}</span>
                                        <span v-show="!showCounter" class="count">{{counter}} s</span>
                                    </el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item :label="$t('sms.code')" prop="checkCode" :error="codeErrorMsg">
                                <el-input
                                        clearable
                                        maxlength="4"
                                        v-model="form.checkCode"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>-->
                    <el-row>
                        <el-form-item :label="$t('customer.address')" prop="customerAddressZh" :rules="{required:true,message:this.$t('register.addressBlur'),trigger:'blur',pattern: '[^ \x22]+' }" >
                            <el-input
                                    maxlength="200"
                                    clearable v-model="form.customerAddressZh"></el-input>
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item :label="$t('customer.sgs.customerNo')" prop="bossNo">
                                <el-input disabled="bossNoDisabled"
                                          maxlength="50"
                                          clearable v-model="form.bossNo"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item :label="$t('customer.sgs.reportNo')" prop="reportNo">
                                <el-input
                                        maxlength="50"
                                        clearable v-model="form.reportNo"
                                        :placeholder="$t('register.sgsReportNoBlur')"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-form-item :label="$t('customer.certificate')" prop="qualificationUrl">
                            <el-upload action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                                       class="avatar-uploader"
                                       drag
                                       :show-file-list="false"
                                       :on-success="uploadSuccess">
                                <img v-if="form.qualificationUrl" :src="form.qualificationUrl"
                                     style="width: 100%;height: 100%;">
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                    </el-row>
                </div>

                <el-row style="text-align: center;margin-top: 30px;">
                    <router-link type="text" :to="{path:'/'}" style="padding-right: 20px;">
                        {{$t('register.returnLogin')}}
                    </router-link>
                    <el-button size="small" type="primary" :disabled="submitDisabled" :loading="registerSubmitLoading"
                               @click="onSubmit">{{$t('register.submitRegister')}}
                    </el-button>
                </el-row>
            </el-form>
        </el-card>
        <el-dialog :title="$t('register.message.title')" :visible="emailApplyDialog" width="60%" top="10vh"
                   :show-close="false">
            <div class="modal-body">
                <h3>{{$t('register.message.emailSendTip')}} <span
                        style="color: #ff6600">{{accountApplyForm.email}}</span></h3>
                <h4>{{$t('register.message.emailSendMessage')}}</h4>
            </div>
            <div slot="footer" class="dialog-footer" style="text-align: right;top: 5px;">
                <router-link type="text" :to="{path:'/'}"><p style="cursor: pointer;color: #ff6600">
                    {{$t('register.message.returnLogin')}}</p></router-link>
            </div>
        </el-dialog>
        <el-dialog title="General Conditions of Service" :visible="clauseVisible" width="70%" top="10vh"
                   :show-close="false">
            <div class="modal-body">
                <img src="/img/logo.png" style="width: 200px"/>
                <h3 style="font-weight: 600">TERMS AND CONDITIONS OF USE</h3>
                <br/>
                <br/>
                <p><a target="_blank" style="color: #ff6600" href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
                </p>
                <p style="word-wrap:break-word">
                    SGS Supplier Chain Knowledge Sharing services include a compilation(s) of data which are mainly
                    based on the sources publicly available at the relevant time for which SGS bears no responsibility.
                    Unless specifically advised in advance, the compilation of data is a generic analysis which does not
                    cover specific product(s). SGS may have extracted from the compiled data specific criteria which are
                    not intended to be substitute for the relevant legislation and/or standards.
                    Unless agreed otherwise in writing, SGS's services and liability are governed by the general
                    Conditions of Service <a style="color: #ff6600" target="_blank"
                                             href="http://www.sgs.com/terms_and_conditions.htm">http://www.sgs.com/terms_and_conditions.htm</a>
                    . All material developed for the clients including the relevant systematic and methodology have been
                    developed by the company and are copyrighted by it. Clients shall not copy reproduce or disclose to
                    third parties the data as provided by the company without its prior written consent. All information
                    provided by SGS is for strategic and informational purposes only, and should not be construed as
                    company-specific legal compliance advice or counsel. SGS makes no representation whatsoever
                    about the suitability of the information and services contained herein for resolving any question of
                    law. SGS does not provide legal services.
                </p>
            </div>
            <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
                <el-button type="primary" @click="clauseVisible = false">Agree and continue</el-button>
            </div>
        </el-dialog>
    </div>
</template>


<script>
    import {
        add,
        addAccount,
        checkCustomer,
        checkAccount,
        sendVerificationCode,
        registerDetail,
        validateAccount
    } from "@/api/customer/customerRegister";
    import {getCloudFileURL, getSgsCustomer} from "@/api/common/index";
    import {validatenull} from "@/util/validate";
    import {mapGetters} from "vuex";

    const TIME_COUNT = 60; //更改倒计时时间
    export function isvalidPhone(str) {
        const reg = /^1[********]\d{9}$/;
        return reg.test(str)
    }

    export default {
        components: {
            TopLang: resolve => require(['../index/top/top-lang'], resolve)
        },
        data() {
            var validPhone = (rule, value, callback) => {
                if (!value) {
                    callback(this.$t('register.telePhoneBlur'))
                } else if (!isvalidPhone(value)) {
                    callback(this.$t('register.telePhoneBlur'))
                } else {
                    checkAccount({telephone: this.accountApplyForm.applyContactMobile}).then(res => {
                        //判断显示客户需要填写的信息
                        if (res.data.success === 1 || res.data.success === '1') {
                            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                                callback(new Error(this.$t('register.submitPhoneInfo')));
                            } else {
                                callback(new Error(this.$t('register.lockError')));
                            }
                        } else {
                            callback();
                        }
                    });
                }
            }
            var validatePass = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('register.passwordBlur')));
                } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/.test(value))) {
                    callback(new Error(this.$t('register.passwordError')));
                } else {
                    callback();
                }
            };
            var validatePass2 = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('register.doublePasswordBlur')));
                } else if (value !== this.form.password) {
                    callback(new Error(this.$t('register.checkPassNo')));
                } else {
                    callback();
                }
            };
            var validateAccount = (rule, value, callback) => {
                if (!validatenull(value)) {
                    checkAccount({email: this.accountApplyForm.email}).then(res => {
                        //判断显示客户需要填写的信息
                        if (res.data.success === 1 || res.data.success === '1') {
                            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                                callback(new Error(this.$t('register.submitInfo')));
                            } else {
                                callback(new Error(this.$t('register.lockError')));
                            }
                            //setTimeout(this.$router.push('/'),3000);
                        } else {
                            callback();
                        }
                    });
                }
            };
            var validateAccount1 = (rule, value, callback) => {
                if (!validatenull(value)) {
                    checkAccount({email: this.form.email}).then(res => {
                        //判断显示客户需要填写的信息
                        if (res.data.success === 1 || res.data.success === '1') {
                            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                                callback(new Error(this.$t('register.submitInfo')));
                            } else {
                                callback(new Error(this.$t('register.lockError')));
                            }
                            //setTimeout(this.$router.push('/'),3000);
                        } else {
                            callback();
                        }
                    });
                }
            };
            return {
                bossNoDisabled: false,
                registerMode: 1,
                emailApplyDialog: false,
                hash: '',
                applySubmitDisabled: false,
                applySubmitLoading: false,
                applyShowSendSmsBtn: false,
                applyShowCounter: true,
                applyCounter: 60,
                applyTimer: null,
                accountApplyForm: {
                    applyFlag: 0,
                    language: this.language,
                },
                active: 1,
                codeErrorMsg: null,
                name: "customerRegister",
                loginAccount: '',
                emailDisabled: false,
                contactMobileDisabled: false,
                accountId:'',
                form: {
                    qualificationUrl: '',
                    taxNo: '',
                    bossNo: '',
                    language: this.language,
                },
                submitDisabled: false,
                registerSubmitLoading: false,
                showSendSmsBtn: false,
                isShow: false,
                showCounter: true,
                clauseVisible: true,
                counter: 60,
                timer: null,
                customerData: {},
                rules: {
                    /*customerName: [
                        {required: true, message: this.$t('register.companyNameBlur'), trigger: 'blur,change'},
                    ],*/
                    /*contactMobile: [
                        {
                            required: true,
                            pattern: /^1[********]\d{9}$/,//可以写正则表达式呦呦呦
                            message: this.$t('register.telePhoneBlur'),
                            trigger: 'blur'
                        },
                    ],*/
                    contactMobile: [
                        {required: true, trigger: 'blur', validator: validPhone}//这里需要用到全局变量
                    ],
                    checkCode: [
                        {required: true, message: this.$t('register.pleaseInputCode'), trigger: 'blur'},
                    ],
                   /* customerAddressZh: [
                        {required: true, message: this.$t('register.addressBlur'), trigger: 'blur'},
                    ],*/
                    email: [
                        {required: true, message: this.$t('register.emailBlur'), trigger: 'blur'},
                        {type: 'email', message: this.$t('register.emailRigthBlur'), trigger: ['blur', 'change']},
                        {validator: validateAccount1, trigger: 'blur'}
                    ],
                    /*userName: [
                        {required: true, message: this.$t('register.accountBlur'), trigger: 'blur'},
                    ],*/
                   /* password: [
                        {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
                        {validator: validatePass, trigger: 'blur'}
                       /!* {
                            pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
                            message: this.$t('register.passwordError')
                        }*!/
                    ],*/
                     password: [{required: true,  validator: validatePass, trigger: 'blur' }],
                     doublePassword: [{required: true,  validator: validatePass2, trigger: 'blur' }],
                    /*doublePassword: [
                        {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
                        {validator: validatePass2, trigger: 'blur'}
                    ],*/
                },
                applyRules: {
                    applyContactMobile: [
                        {required: true, trigger: 'blur', message: this.$t('register.telePhoneBlur')},//这里需要用到全局变量
                        {validator: validPhone, trigger: 'blur'}
                    ],
                    checkCode: [
                        {required: true, message: this.$t('register.pleaseInputCode'), trigger: 'blur'},
                    ],
                    email: [
                        {required: true, message: this.$t('register.emailBlur'), trigger: 'blur'},
                        {type: 'email', message: this.$t('register.emailRigthBlur'), trigger: ['blur', 'change']},
                        {validator: validateAccount, trigger: 'blur'}
                    ],

                },
            }
        },
        computed: {
            ...mapGetters(["language"]),
        },
        watch: {
            'hash': function (newVal) {
                //获取路由参数
                this.getParams();
            },
        },
        mounted() {
            //获取路由参数
            this.getParams();
        },
        methods: {
            getParams() {
                const active = this.$route.query.active;
                const applyFlag = this.$route.query.applyFlag;
                const phone = this.$route.query.phone;
                const email = this.$route.query.email;
                const customerId = this.$route.query.id;
                if (active == '' || active == undefined || active == null) {
                    this.active = 1;
                } else if (active == 2) {
                    this.active = active;
                    //判断是否有ID
                    if (!validatenull(customerId)) {//存在ID  标识为用户重新修改注册信息
                        this.loadAccountInfo(customerId,email);
                    }
                    if (applyFlag == 0) {//邮箱
                        this.registerMode = 1;
                        this.loginAccount = email;
                        this.form.email = email;
                        this.emailDisabled = true;
                    } else {//手机注册
                        this.registerMode = 2;
                        this.loginAccount = phone;
                        this.form.contactMobile = phone;
                        this.contactMobileDisabled = true;
                    }

                }
            },
            loadAccountInfo(customerId,email) {
                //查询customer & Account数据
                registerDetail(customerId).then(res => {
                    debugger;
                    const customer = res.data.data.customer;
                    if(customer.approveStatus!=80 ){
                        this.$router.push({ path: '/login'})
                    }
                    const account = customer.account;
                    /*if(account.email!=email){
                        this.$router.push({ path: '/login'})
                    }*/
                    this.$set(this.form, 'customerName', customer.customerNameEn);
                    this.$set(this.form, 'email', account.email);
                    this.$set(this.form, 'userName', account.userName);
                    this.$set(this.form, 'contactMobile', account.mobile);
                    this.$set(this.form, 'taxNo', customer.taxNo);
                    this.$set(this.form, 'customerAddressZh', customer.customerAddressZh);
                    this.$set(this.form, 'bossNo', customer.bossNo);
                    this.$set(this.form, 'reportNo', customer.reportNo);
                    this.accountId = account.id;
                    this.loginAccount = account.account;
                    this.registerMode = account.registerMode;
                    this.isShow = true;
                    if (!validatenull(customer.qualification)) {
                        if (!validatenull(customer.qualification.attachmentId)) {
                            getCloudFileURL(customer.qualification.attachmentId).then(res => {
                                  this.$set(this.form, 'qualificationUrl', res.data);
                            });
                        }
                    }
                   this.$set(this.form, 'id', customer.id);
                });
            },
            onSubmitApply() {
                this.accountApplyForm.language = this.language;
                this.$refs['accountApplyForm'].validate((valid) => {
                    if (valid) {
                        this.applySubmitDisabled = true;
                        this.applySubmitLoading = true;
                        addAccount(this.accountApplyForm).then(res => {
                            if (this.accountApplyForm.applyFlag == 0) {//邮箱
                                //弹框提示
                                this.emailApplyDialog = true;
                            } else {
                                //路由重新跳转登录页
                                let hashVal = new Date().getTime()
                                this.$router.push({
                                    path: '/register',
                                    query: {
                                        active: 2,
                                        applyFlag: this.accountApplyForm.applyFlag,
                                        phone: this.accountApplyForm.applyContactMobile,
                                        email: this.accountApplyForm.email,
                                        hash: hashVal
                                    }
                                })
                                this.hash = hashVal;
                            }

                        }, error => {
                            this.applySubmitDisabled = false;
                            this.applySubmitLoading = false;
                            console.log(error);
                        });
                        /*} else {
                            this.submitDisabled = false;
                            this.registerSubmitLoading = false;
                            this.$nextTick(() => {this.codeErrorMsg=this.$t('sms.smsCodeValidateError');})
                            this.$message({
                                type: "error",
                                message: this.$t('sms.smsCodeValidateError')
                            });
                        }*/
                        //});
                    } else {
                        this.submitDisabled = false;
                        this.registerSubmitLoading = false;
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            async onSubmit() {
                //验证该账号是否在SGSmart中注册过
                let validateRes = await validateAccount(this.loginAccount);
                console.log(validateRes);
                if (!validateRes.data.data) {//该用户已注册
                    this.$message({
                        type: "error",
                        message: this.$t('register.registerError')
                    });
                    return false;
                }
                this.submitDisabled = true;
                this.registerSubmitLoading = true;
                this.$refs['registerForm'].validate((valid) => {
                    if (valid) {
                        debugger;
                        this.submitDisabled = true;
                        this.registerSubmitLoading = true;
                        //验证短信验证码是否过期
                        //checkSmsCode(this.form.contactMobile, this.form.checkCode).then(res => {
                        //  if (res.data.data) {//验证通过
                        const register = {};
                        register.customer = this.form;
                        register.checkCode = this.form.checkCode;
                        register.customer.customerNameZh = this.form.customerName;
                        register.customer.customerNameEn = this.form.customerName;
                        register.customer.qualification = this.form.qualification;
                        register.customer.account = {
                            id:this.accountId,
                            email: this.form.email,
                            userName: this.form.userName,
                            password: this.form.password,
                            mobile: this.form.contactMobile,
                            account: this.loginAccount,
                            language: this.language,
                            registerMode: this.registerMode
                        };
                        add(register).then(() => {
                            this.submitDisabled = false;
                            this.registerSubmitLoading = false;
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.$router.push('/register/success');
                        }, error => {
                            this.submitDisabled = false;
                            this.registerSubmitLoading = false;
                            console.log(error);
                        });
                        /*} else {
                            this.submitDisabled = false;
                            this.registerSubmitLoading = false;
                            this.$nextTick(() => {this.codeErrorMsg=this.$t('sms.smsCodeValidateError');})
                            this.$message({
                                type: "error",
                                message: this.$t('sms.smsCodeValidateError')
                            });
                        }*/
                        //});
                    } else {
                        this.submitDisabled = false;
                        this.registerSubmitLoading = false;
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            checkExists() {
                if (this.form.customerName != '' && this.form.customerName != undefined && this.form.customerName.length >= 1) {
                    checkCustomer(this.form.customerName).then(res => {
                        //判断显示客户需要填写的信息
                        if (!validatenull(res.data.data)) {
                            this.customerData = res.data.result;
                            //this.form.bossNo=this.customerData.number;
                            this.isShow = false;
                        } else {
                            this.isShow = true;
                            this.form.id = null;
                        }
                    });
                }
            },
            querySearch(query, callback) {
                this.loading = false;
                if (query != '' && query != undefined && query.length >= 2) {
                    this.loading = true;
                    getSgsCustomer({customerName: query, rows: 5}).then(res => {
                        const results = [];
                        res.data.rows.forEach((currentValue, index) => {
                            if (this.language == 'en-US') {
                                results.push({
                                    'value': currentValue.nameEN,
                                    "taxNo": currentValue.taxNo,
                                    "bossNo": currentValue.number
                                });
                            } else {
                                results.push({
                                    'value': currentValue.nameCN,
                                    "taxNo": currentValue.taxNo,
                                    "bossNo": currentValue.number
                                });
                            }
                        });
                        this.form.bossNo = '';
                        this.form.taxNo = '';
                        callback(results);
                    }, error => {
                        console.log(error);
                    });
                }
            },
            handleSelect(item) {
                this.checkExists();
                this.form.bossNo = item.bossNo;
                if (item.bossNo != undefined && item.bossNo != '' && item.bossNo != null) {
                    this.bossNoDisabled = true;
                } else {
                    this.bossNoDisabled = false;
                }
                this.form.taxNo = item.taxNo;
                this.$refs['taxNo'].focus();
            },
            uploadSuccess(res, file) {
                this.form.qualification = {};
                this.form.qualification.attachmentId = res.data[0].cloudID;
                getCloudFileURL(this.form.qualification.attachmentId).then(res => {
                    this.form.qualificationUrl = res.data;
                });
            },
            sendSMSCode() {
                this.showSendSmsBtn = true;
                //验证手机号
                this.$refs.registerForm.validateField("contactMobile", errMsg => {
                    if (!errMsg) {//校验通过
                        console.log("手机号校验通过");
                        sendVerificationCode(this.form.contactMobile,1).then(res => {
                            this.showCounter = false;//展示时间倒计时
                            if (!this.timer) {
                                this.counter = TIME_COUNT;
                                this.showCounter = false;
                                this.timer = setInterval(() => {
                                    if (this.counter > 0 && this.counter <= TIME_COUNT) {
                                        this.counter--;
                                    } else {
                                        this.showSendSmsBtn = false;
                                        this.showCounter = true;//时间计数完毕重新展示发送按钮
                                        clearInterval(this.timer);  // 清除定时器
                                        this.timer = null;
                                    }
                                }, 1000)
                            } else {
                                this.showCounter = true;
                            }
                        });
                    } else {
                        //手机号码验证失败
                        this.showSendSmsBtn = false;
                    }
                });
            },
            applySendSMSCode() {
                this.applyShowSendSmsBtn = true;
                //验证手机号
                if (!this.accountApplyForm.applyContactMobile) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('register.telePhoneBlur'),
                        type: 'warning'
                    });
                    this.applyShowSendSmsBtn = false;
                    return false;
                } else if (!isvalidPhone(this.accountApplyForm.applyContactMobile)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('register.telePhoneBlur'),
                        type: 'warning'
                    });
                    this.applyShowSendSmsBtn = false;
                    return false;
                } else {
                    this.$refs['accountApplyForm'].validateField("applyContactMobile", errMsg => {
                        console.log("手机号校验通过");
                        //校验是否已注册
                        checkAccount({telephone: this.accountApplyForm.applyContactMobile}).then(res => {
                            //判断显示客户需要填写的信息
                            if (res.data.success === 1 || res.data.success === '1') {
                                if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                                    this.$notify({
                                        title: this.$t('tip'),
                                        message: this.$t('register.submitPhoneInfo'),
                                        type: 'warning'
                                    });
                                } else {
                                    this.$notify({
                                        title: this.$t('tip'),
                                        message: this.$t('register.lockError'),
                                        type: 'warning'
                                    });
                                }
                                this.applyShowSendSmsBtn = false;
                                //setTimeout(this.$router.push('/'),3000);
                            } else {
                                this.applyShowSendSmsBtn = false;
                                sendVerificationCode(this.accountApplyForm.applyContactMobile,1).then(res => {
                                    this.applyShowCounter = false;//展示时间倒计时
                                    if (!this.applyTimer) {
                                        this.applyCounter = TIME_COUNT;
                                        this.applyShowCounter = false;
                                        this.applyShowSendSmsBtn = true;
                                        this.applyTimer = setInterval(() => {
                                            if (this.applyCounter > 0 && this.applyCounter <= TIME_COUNT) {
                                                this.applyCounter--;
                                            } else {
                                                this.applyShowSendSmsBtn = false;
                                                this.applyShowCounter = true;//时间计数完毕重新展示发送按钮
                                                this.applyShowSendSmsBtn = false;
                                                clearInterval(this.applyTimer);  // 清除定时器
                                                this.applyTimer = null;
                                            }
                                        }, 1000)
                                    } else {
                                        this.applyShowCounter = true;
                                    }
                                });
                            }
                        });

                    });
                }

            },
        }
    }
</script>

<style lang="scss">
    .register-container {
        background: url("/img/loginBackground.png") center no-repeat;
        background-size: cover;
        width: 100%;
        height: 100%;
        padding: 50px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-card {
            width: 70%;
            height: 90%;
            overflow-y: auto;
        }

        .top-icon {
            color: #303133 !important;
        }

        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }

        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 148px;
            height: 148px;
            line-height: 178px;
            text-align: center;
        }
    }
</style>
