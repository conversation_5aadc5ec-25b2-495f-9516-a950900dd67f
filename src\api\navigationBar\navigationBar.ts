import request from '../request'

/**
 * 更新用户语言设置
 * @param language - 用户语言，类型为字符串
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const navigationBarList = (params: Object) => {
    return request({
      url: `/sgs-e-filling/sgs-knowledge/knowledgeCategory/list?hash=${new Date().getTime()}`,
      method: 'get',
      params
    })
}
export const navigationBarTree = () => {
  return request({
    url: `/sgs-e-filling/sgs-knowledge/knowledgeCategory/tree?hash=${new Date().getTime()}`,
    method: 'get',
    loading: false
  })
}