<template>
    <div class="linkForm">
        <el-form :model="props.form" @submit.native.prevent :label-width="labelWidth" label-position="right" :rules="rules" ref="formRef">

             
<el-row>
    <el-card class="link-card">
        <el-form-item :label="$t('work.pageCreate.title') + ':'" style="width: 100%;" prop="title" required>
            <el-input :placeholder="$t('work.pageCreate.enterTitle')" type="textarea" :rows="2" v-model="props.form.title" maxlength="500" show-word-limit></el-input>
        </el-form-item>
        <el-form-item :label="$t('work.pageCreate.linkContent') + ':'" style="width: 100%;margin-top: 20px;margin-bottom: 20px;">
            <el-input-tag 
                trigger="Enter"
                v-model="props.form.linkFormLink" 
                :placeholder="$t('work.pageCreate.enterLinkAndConfirm')"
                @keyup.enter.stop="handleLink"
                @removeTag="handleTagRemove"
            ></el-input-tag>
        </el-form-item>
        <!-- <el-form-item  style="width: 100%;margin-top: 20px;"> -->
             <!-- <el-form-item label="链接预览:" style="width: 100%; overflow-y: auto;" class="content"> -->
                <div v-if="!props.form.linkFormContent" class="no-data" >{{ $t('work.pageCreate.pleaseFillLink') }}</div>
        <div v-else class="content-container">
            <div class="link-preview">
                <el-button type="primary" @click="previewLink">{{ $t('work.pageCreate.previewLink') }}</el-button>
                <div class="link-url">{{ props.form.linkFormContent }}</div>
            </div>
        </div>
        <!-- </el-form-item> -->
         
    <!-- </el-form-item> -->
        <!-- <el-form-item :label="$t('work.pageCreate.attachments') + ':'" style="width: 100%;" class="attachment-form-item">
            <div class="attachment-list">
                <div v-if="!props.form.knowledgeAttachmentDTOList?.length" class="no-attachment">
                    {{ $t('work.pageCreate.noAttachments') }}
                </div>
                <div v-else v-for="(item, index) in props.form.knowledgeAttachmentDTOList" :key="index" class="attachment-item">
                    <span class="attachment-name" @click="openFile(item.cloudId)">{{ item.cloudId.substring(item.cloudId.lastIndexOf('/') + 1) }}</span>
                    <el-icon class="delete-icon" @click="handleDeleteAttachment(index)"><DeleteFilled /></el-icon>
                </div>
            </div>
            <el-upload
                class="attachment-upload"
                :show-file-list="false"
                :before-upload="beforeAttachmentUpload"
                :http-request="handleAttachmentUpload"
            >
            <el-button type="primary" size="small">{{ $t('work.pageCreate.addAttachment') }}</el-button>
            </el-upload>
        </el-form-item> -->
    </el-card>
</el-row>

</el-form>
    </div>

</template>
<script lang="ts" setup>
//import { uploadFileAttachment } from '@/api/upload'
import { ElMessage } from 'element-plus'
//import { getCloudFileURL } from '@/api/getCloudFileUrl.ts'
//import { useI18n } from 'vue-i18n'
import { useI18n } from 'vue-i18n'
// const { t } = useI18n()
import { computed, ref } from 'vue'
const { t, locale } = useI18n()
const labelWidth=computed(()=>{
    if(locale.value=='zh-CN'){
        return '80px'
    }else if(locale.value=='en-US'){
        return '120px'
    }
})

// Form validation related
const formRef = ref()
const rules =computed(()=>{
  return {
  title: [
    { required: true, message: t('work.pageCreate.pleaseEnter') + t('work.pageCreate.title'), trigger: 'blur' }
  ]
}
})

// Make validate method available for parent component
defineExpose({
  validate: () => {
    if (formRef.value) {
      return formRef.value.validate()
    }
    return Promise.resolve(true)
  }
})

const props=defineProps({
    form:{
        type:Object,
        default:() => ({
            knowledgeAttachmentDTOList: []
        })
    }
})

// Initialize knowledgeAttachmentDTOList if it doesn't exist
if (!props.form.knowledgeAttachmentDTOList) {
    props.form.knowledgeAttachmentDTOList = []
}

// const beforeAttachmentUpload = (file: File) => {
//     const isLt10M = file.size / 1024 / 1024 < 10
//     if (!isLt10M) {
//         ElMessage.error('附件大小不能超过 10MB!')
//         return false
//     }
//     return true
// }

// const handleAttachmentUpload = async (options: any) => {
//     try {
//         const formData = new FormData()
//         formData.append('file', options.file)
//         formData.append('systemID', '59')
//         const res: any = await uploadFileAttachment(formData)
//         if (res?.data && res.data.length > 0) {
//             props.form.knowledgeAttachmentDTOList.push({
//                 cloudId: res.data[0].cloudID,
//                 fileName:options.file.name,
//                 fileType:options.file.type,
//                 status:1,
//             })
//         }
//     } catch (error) {
//         ElMessage.error('上传失败')
//     }
// }

// const handleDeleteAttachment = (index: number) => {
//     props.form.knowledgeAttachmentDTOList.splice(index, 1)
// }

const handleLink = () => {
   
    if (!props.form.linkFormLink.length) {
        ElMessage.warning(t('work.pageCreate.enterLinkFirst'))
        return
    }else if(props.form.linkFormLink.length>1){
        props.form.linkFormLink.splice(0,1)
    }

    // Store the direct link URL instead of iframe content
    props.form.linkFormContent = props.form.linkFormLink[0]
    
    // Open the link in a new tab for preview
    window.open(props.form.linkFormContent, '_blank')
}

// Function to preview the link again if needed
const previewLink = () => {
    if (props.form.linkFormContent) {
        window.open(props.form.linkFormContent, '_blank')
    }
}

// Handle tag removal
const handleTagRemove = () => {
    // Clear content when tag is removed
    props.form.linkFormContent = ''
}

// const openFile = (fileId: string) => {
//     getCloudFileURL({ cloudID: fileId, systemID: 1, networkType: 2 }).then((res:any) => {
//         window.open(res)
//     })
// }
</script>
<style lang="scss">
.linkForm{
    height: 100%;

    overflow-y: auto;
    .link-card{
        padding-top: 30px !important;
        padding-left:30px !important;
        .el-form-item{
            margin: 5px 0px;
        }
    }
   
    .el-card{
        .attachment-form-item{
            .el-form-item__label{
                height: 80%;
                align-items: center;
            }
            .el-form-item__content{
                display: flex;
                align-items: baseline;
                gap: 10px;
            }
        }
    width: 100%;
    .el-card__body{
        width: 100%;
        padding: 10px 10px;
        .no-data{
            width: 100%;
            height: calc(100vh - 57px);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .content-container{
            width: 100%;
            padding: 20px;
            
            .link-preview {
                display: flex;
                flex-direction: column;
                gap: 10px;
                
                .link-url {
                    word-break: break-all;
                    padding: 10px;
                    background-color: #f5f7fa;
                    border-radius: 4px;
                    border: 1px solid #e4e7ed;
                }
            }
        }
        .el-form-item{
            margin-bottom: 0px;
        }
        .content{
            height: 700px;
            .el-form-item__content{
                display: inline;
                div{
                    height: 98%;
                }
            }
         
        }
        .attachment-list {
            margin-bottom: 10px;
            .no-attachment {
                color: #909399;
                font-size: 14px;
                padding: 10px 0;
            }
            .attachment-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px;
                background: #f5f7fa;
                margin-bottom: 8px;
                border-radius: 4px;
                .attachment-name {
                    flex: 1;
                    margin-right: 10px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: rebeccapurple;
                    cursor: pointer;
                }
                .delete-icon {
                    cursor: pointer;
                    color: #f56c6c;
                    &:hover {
                        color: #f78989;
                    }
                }
            }
        }
        flex-wrap: wrap;
        .attachment-upload {
            margin-top: 10px;
        }
    }
}
}

</style>