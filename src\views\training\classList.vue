<template>
  <basic-container>
    <!-- <el-breadcrumb class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('navbar.trainingList')}}</el-breadcrumb-item>
    </el-breadcrumb> -->
    <h1 class="top-title">{{$t('navbar.trainingList')}}</h1>
    <el-card shadow="never" class="sgs-box">
      <el-row>
        <el-form :inline="true" ref="form" :model="form" label-width="200px" size="medium">
        <el-form-item>
          <el-input clearable v-model="form.courseTitle"  :placeholder="$t('training.CourseTitle')"></el-input>
        </el-form-item>
        <el-form-item>
            <el-input clearable v-model="form.trainingLocation"  :placeholder="$t('training.TrainingLocation')"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="searchChange('0')">{{ $t('operation.search') }}</el-button>
        </el-form-item>
        <!--<el-form-item style="float: right;margin-right: 50px;">-->
            <!--<el-button type="primary" @click="toNewClassManager('0')">{{ $t('training.NewClassManager') }}</el-button>-->
            <!--<el-button type="primary" @click="toNewClass('0')">{{ $t('training.NewClass') }}</el-button>-->
            <!--<el-button type="primary" @click="toNewCourse('0')">{{ $t('training.NewCourse') }}</el-button>-->
        <!--</el-form-item>-->
      </el-form>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-tabs v-model="activeName" @tab-click="handleClick" v-loading.fullscreen.lock="fullscreenLoading">
              <el-tab-pane :label="$t('training.OngoingClass')" name="first">
                <el-card v-if="list.length == 0">
                  <div style="text-align: center;color: #909399">
                    <h5>{{ $t('training.NoData') }}</h5>
                  </div>
                </el-card>
                <el-card v-for="item in list">
                  <el-divider></el-divider>
                  <el-col :span="18">
                    <div>
                      <el-lab>
                        {{ item.courseTitle }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.moduleName }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.courseLevel }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.classIntroduction }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.remark }}
                      </el-lab>
                    </div>

                  <!-- <el-lab  style="margin-left: 80%">
                      <el-lab  style="color: #ffbb01" @click="attendance(item.id)">{{ $t('training.Attendance') }}</el-lab>
                    </el-lab>-->
                    <el-lab  style="margin-left: 80%">
                      <el-popover placement="left" width="500" trigger="click">
                          <el-table :data="fileList" width="100%">
                            <el-table-column type="index" fixed label="#" width="50">
                            </el-table-column>
                            <el-table-column prop="documentName" :label="$t('attachment.name')">
                            </el-table-column>
                            <el-table-column :label="$t('operation.title')" width="180">
                              <template slot-scope="scope">
                                <el-button type="text" size="small" @click="downloadAttachmentRow(scope.row)" icon="el-icon-download">{{$t('operation.download')}}</el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                        <el-button  type="text" size="small"  @click="selectAttachment(item.id)"  slot="reference">{{ $t("info.attachment") }}</el-button>
                      </el-popover>
                    </el-lab>
                    <el-table :data = list2>
                      <el-table-column prop="courseTitle" :label="$t('training.Date')"   >{{ item.trainingDate }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.TrainingAddress')"  >{{ item.trainingAddress }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.Trainer')"  >{{ item.trainerName }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.Location')"  >{{ item.trainingLocation }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.InvitationNumber')"  >{{ item.total }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.EnrolledNumber')"  >{{ item.totalOfEnrolled }}</el-table-column>
                    </el-table>
                  </el-col>
                  <el-col :span="6">
                    <el-image :src="item.uploadCourseImage">
                      <div slot="placeholder" class="image-slot">
                        {{ $t('training.loading') }}<span class="dot">...</span>
                      </div>
                      <div slot="error"  >
                        {{ $t('training.loadingFail') }}
                      </div>
                    </el-image>
                  </el-col>

                </el-card>
                <el-card v-if="list.length != 0 && list.length != 1">
                  <div style="text-align: center;color: #909399" v-show="loadingMoreShow">
                    <el-button type="text" @click="loadingMore" size="small" v-loading.fullscreen.lock="fullscreenLoading">{{ $t('training.LoadingMore') }}</el-button>
                  </div>
                </el-card>
              </el-tab-pane>
              <el-tab-pane :label="$t('training.ComingClass')" name="second" >
                <el-card v-if="listComing.length == 0">
                  <div style="text-align: center;color: #909399">
                    <h5>{{ $t('training.NoData') }}</h5>
                  </div>
                </el-card>
                <el-card v-for="item in listComing">
                  <el-divider></el-divider>
                  <el-col :span="18">
                    <div>
                      <el-lab>
                        {{ item.courseTitle }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.moduleName }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.courseLevel }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.classIntroduction }}
                      </el-lab>
                    </div>
                    <div>
                      <el-lab>
                        {{ item.remark }}
                      </el-lab>
                    </div>
                    <!--<el-lab  style="margin-left: 80%">
                      <el-lab  style="color: #ffbb01" @click="editClass(item.id)">{{ $t('operation.edit') }}</el-lab>
                    </el-lab>-->
                    <el-lab>
                      &#12288;
                    </el-lab>
                    <!--<el-lab  style="color: #ffbb01" @click="handleDel(item.id)">{{ $t('operation.remove') }}</el-lab>-->
                    <el-lab  style="margin-left: 80%">
                      <el-popover placement="left" width="500" trigger="click">
                        <el-table :data="fileList" width="100%">
                          <el-table-column type="index" fixed label="#" width="50">
                          </el-table-column>
                          <el-table-column prop="documentName" :label="$t('attachment.name')">
                          </el-table-column>
                          <el-table-column :label="$t('operation.title')" width="180">
                            <template slot-scope="scope">
                              <el-button type="text" size="small" @click="downloadAttachmentRow(scope.row)" icon="el-icon-download">{{$t('operation.download')}}</el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                        <el-button  type="text" size="small"  @click="selectAttachment(item.id)"  slot="reference">{{ $t("info.attachment") }}</el-button>
                      </el-popover>
                    </el-lab>
                    <el-table :data = list2>
                      <el-table-column prop="courseTitle" :label="$t('training.Date')"  >{{ item.trainingDate }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.TrainingAddress')"  >{{ item.trainingAddress }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.Trainer')"  >{{ item.trainerName }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.Location')"  >{{ item.trainingLocation }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.InvitationNumber')"  >{{ item.total }}</el-table-column>
                      <el-table-column prop="courseTitle" :label="$t('training.EnrolledNumber')"  >{{ item.totalOfEnrolled }}</el-table-column>
                    </el-table>
                  </el-col>
                  <el-col :span="6">
                    <el-image :src="item.uploadCourseImage">
                      <div slot="placeholder" class="image-slot">
                        {{ $t('training.loading') }}<span class="dot">...</span>
                      </div>
                      <div slot="error"  >
                        {{ $t('training.loadingFail') }}
                      </div>
                    </el-image>
                  </el-col>

                </el-card>
                <el-card v-if="listComing.length != 0  && listComing.length != 1 ">
                  <div style="text-align: center;color: #909399" v-show="loadingMoreShow">
                    <el-button type="text" @click="loadingMore" size="small" v-loading.fullscreen.lock="fullscreenLoading">{{ $t('training.LoadingMore') }}</el-button>
                  </div>
                </el-card>
              </el-tab-pane>
              <el-tab-pane :label="$t('training.CompleteClass')" name="third" >
                <el-card v-if="listComplete.length == 0">
                  <div style="text-align: center;color: #909399">
                    <h5>{{ $t('training.NoData') }}</h5>
                  </div>
                </el-card>
                <el-card v-for="item in listComplete" :key="item.id" style="margin-bottom: 24px;">
                  <el-divider></el-divider>
                  <el-row :gutter="20">
                    <el-col :span="20">
                      <div>
                        <el-lab>
                          {{ item.courseTitle }}
                        </el-lab>
                      </div>
                      <div>
                        <el-lab>
                          {{ item.moduleName }}
                        </el-lab>
                      </div>
                      <div>
                        <el-lab>
                          {{ item.courseLevel }}
                        </el-lab>
                      </div>
                      <div>
                        <el-lab>
                          {{ item.classIntroduction }}
                        </el-lab>
                      </div>
                      <div>
                        <el-lab>
                          {{ item.remark }}
                        </el-lab>
                      </div>
                      <!--<el-lab  style="margin-left: 80%">
                        <el-lab  style="color: #ffbb01" @click="attendance(item.id)">{{ $t('training.Attendance') }}</el-lab>
                      </el-lab>-->
                      <el-lab  style="margin-left: 80%">
                        <el-popover placement="left" width="500" trigger="click">
                          <el-table :data="fileList" width="100%">
                            <el-table-column type="index" fixed label="#" width="50">
                            </el-table-column>
                            <el-table-column prop="documentName" :label="$t('attachment.name')">
                            </el-table-column>
                            <el-table-column :label="$t('operation.title')" width="180">
                              <template slot-scope="scope">
                                <el-button type="text" size="small" @click="downloadAttachmentRow(scope.row)" icon="el-icon-download">{{$t('operation.download')}}</el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-button  type="text" size="small"  @click="selectAttachment(item.id)"  slot="reference">{{ $t("info.attachment") }}</el-button>
                        </el-popover>
                      </el-lab>
                      <el-table :data = list2>
                        <el-table-column prop="courseTitle" :label="$t('training.Date')" width="100">{{ item.trainingDate }}</el-table-column>
                        <el-table-column prop="courseTitle" :label="$t('training.TrainingAddress')" width="200">{{ item.trainingAddress }}</el-table-column>
                        <el-table-column prop="courseTitle" :label="$t('training.Trainer')" width="200">{{ item.trainerName }}</el-table-column>
                        <el-table-column prop="courseTitle" :label="$t('training.Location')" width="200">{{ item.trainingLocation }}</el-table-column>
                        <el-table-column prop="courseTitle" :label="$t('training.InvitationNumber')" width="200">{{ item.total }}</el-table-column>
                        <el-table-column prop="courseTitle" :label="$t('training.EnrolledNumber')"  >{{ item.totalOfEnrolled }}</el-table-column>
                      </el-table>
                      <el-row>
                        <el-col :span="1"></el-col>
                        <el-col :span="22">
                          <el-table :data = item.classTrainee>
                            <el-table-column label="" width="55"></el-table-column>
                            <el-table-column prop="traineeCustomerName" :label="$t('training.Company')" width="200"></el-table-column>
                            <el-table-column prop="traineeUserName" :label="$t('training.Name')" width="200"></el-table-column>
                            <el-table-column prop="isPass" :label="$t('training.Result')"  :formatter="totextOfResult" width="200"></el-table-column>
                            <el-table-column prop="score" :label="$t('training.Score')"></el-table-column>
                          </el-table>
                        </el-col>
                        <el-col :span="1"></el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="4">
                      <el-image :src="item.uploadCourseImage">
                        <div slot="placeholder" class="image-slot">
                          {{ $t('training.loading') }}<span class="dot">...</span>
                        </div>
                        <div slot="error"  >
                          {{ $t('training.loadingFail') }}
                        </div>
                      </el-image>
                    </el-col>
                  </el-row>
                </el-card>
                <el-card v-if="listComplete.length != 0 && listComplete.length != 1 ">
                  <div style="text-align: center;color: #909399" v-show="loadingMoreShow">
                    <el-button type="text" @click="loadingMore" size="small" v-loading.fullscreen.lock="fullscreenLoading">{{ $t('training.LoadingMore') }}</el-button>
                  </div>
                </el-card>
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
        <el-row>
        </el-row>
      </el-row>
    </el-card>
  </basic-container>
</template>



<script>
    import {selectClassList, deleteClassById, handleDel2} from "@/api/training/classList";
    import {selectClassById} from "@/api/training/class";
    import {getCloudFileURL} from "@/api/common/index";
    export default {
        name: "classList",
        data() {
            return {
                form: {},
                //imageUrl: '',
                uploadCourseImage: '',
                editForm: '',
                //pris: '',
                /*courseLevelId2:'',
                courseLevel1: '',*/
                activeName: 'first',
                queryInfo:'',//查询条件
                tabIndex:'0',//当前tab页编号----0，1，2
                list:[],//正在进行的课程
                listComing:[],//将要进行课程
                listComplete:[],//完成的课程
                list2:[{
                    "courseTitle":"1","course":"12","title":"123",
                }],
                src: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
                pickerOptions1: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                },
                page: {
                    pageSize: 2,
                    currentPage: 1,
                    total: 0
                },
                fullscreenLoading:false,
                fileList:[]
            }
        },

        created: function () {
            this.selectClassList('0');
        },
        methods: {
              searchChange(tab){
                this.page.currentPage=1;
                  this.selectClassList(tab);
              },
            selectClassList(tab){
                this.page.currentPage = 1;
                this.fullscreenLoading = true;
                this.loadingMoreShow = true;//加载更多
                selectClassList(
                    Object.assign({'trainingLocation':this.form.trainingLocation,'courseTitle':this.form.courseTitle,'dateType' : this.tabIndex}),
                    this.page.currentPage, this.page.pageSize,
                ).then(res => {
                    console.log(res);
                    if (this.tabIndex == 0) {
                        this.list = res.data.data.records;
                    }else if(this.tabIndex == 1){
                        this.listComing = res.data.data.records;
                    }else {
                        this.listComplete = res.data.data.records;
                    }
                    this.fullscreenLoading = false;
                })
            },
            totextOfResult(val){
                if (val.isPass === 0) {
                    return this.$t('training.Fail')
                }else if (val.isPass === 1){
                    return this.$t('training.Pass')
                }else {
                    return this.$t('training.NotAttended')
                }
            },
            loadingMore(){
                this.fullscreenLoading = true;
                this.page.currentPage = this.page.currentPage +1;
                selectClassList(Object.assign({'trainingLocation':this.form.trainingLocation,'courseTitle':this.form.courseTitle,'dateType' : this.tabIndex}),
                    this.page.currentPage, this.page.pageSize,
                ).then(res => {
                    console.log(res);
                    var data = res.data.data.records;
                    if (data.length != 0) {
                        if (this.tabIndex == 0) {
                            data.forEach(item =>{
                                this.list.push(item);
                            })
                        }else if(this.tabIndex == 1){
                            data.forEach(item =>{
                                this.listComing.push(item);
                            })
                        }else {
                            data.forEach(item =>{
                                this.listComplete.push(item);
                            })
                        }
                    }else {
                        this.$message({
                            type: "success",
                            message: this.$t('training.Loaded')
                        });
                        this.loadingMoreShow = false;
                    }
                    this.fullscreenLoading = false;
                })
            },
            handleDel(id){
                handleDel2(id).then(() => {
                    this.isDisabled = false;
                    this.$message({
                        type: "success",
                        message: this.$t('training.success')
                    });
                    this.selectClassList(this.tabIndex);
                }, error => {
                    console.log(error);
                });
            },
            editClass(classId){
                this.$router.push({path: '/training/class?classId='+classId})
            },
            attendance(classId){
                this.$router.push({path: '/training/attendance?classId='+classId})
            },
            handleClick(tab, event) {
                // console.log(tab, event);
                //console.log(tab.index); /// 获得唯一的index
                this.tabIndex = tab.index;
                this.selectClassList(this.tabIndex);
            },
            selectAttachment(classId){
                this.fileList = [];
                selectClassById(classId).then(result => {
                    result.data.materials.forEach( (productLineId) => {
                        this.fileList.push(productLineId)
                    })
                });
            },
            downloadAttachmentRow(row){
                getCloudFileURL(row.cloudId).then(res => {
                    window.open(res.data,"_blank");
                });
            },
            /*onSubmit(val) {
                this.form.uploadCourseImage = this.imageUrl;
                this.form.courseLevel = this.courseLevelId2;
                this.form.courseLevelId = this.courseLevel1;
                this.form.isPublish = val;

                add(this.form).then(() => {
                    this.isDisabled = false;
                    this.$message({
                        type: "success",
                        message: this.$t('training.success')
                    });
                }, error => {
                    console.log(error);

                });
            },*/
            /*handleAvatarSuccess(res, file) {
                this.imageUrl = URL.createObjectURL(file.raw);
                this.uploadCourseImage = URL.createObjectURL(file.raw);
            },
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg';
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isJPG) {
                    this.$message.error('上传图片只能是 JPG 格式!');
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过 2MB!');
                }
                return isJPG && isLt2M;
            },
            selectRoleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.pris.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.courseLevel1 = obj.id;//提交数据使用，此处可忽略
                this.courseLevelId2 = obj.name;//用于回显名称
            },*/
            toNewClass(){
                this.$router.push({path: '/training/class'})
            },
            toNewClassManager(){
                this.$router.push({path: '/training/classManager'})
            },
            toNewCourse(){
                this.$router.push({path: '/training/course'})
            },
            toEditClass(){
                this.$router.push({path: '/training/class'})
            },
            /*getTime:function(){
                var _this = this;
                let yy = new Date().getFullYear();
                let mm = new Date().getMonth()+1;
                let dd = new Date().getDate();
                /!*let hh = new Date().getHours();
                let mf = new Date().getMinutes()<10 ? '0'+new Date().getMinutes() : new Date().getMinutes();
                let ss = new Date().getSeconds()<10 ? '0'+new Date().getSeconds() : new Date().getSeconds();*!/
                _this.gettime = yy+'-'+mm+'-'+dd/!*+' '+hh+':'+mf+':'+ss*!/;
            },*/
            noFinalsh(){
                //alert("Coding....")
            }
        }
    }


</script>

<style scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .box-card {
    border-radius: 1px;
  }
</style>
