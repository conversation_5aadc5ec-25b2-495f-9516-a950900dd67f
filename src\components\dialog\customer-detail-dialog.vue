<template>
    <el-drawer :title="$t('customer.title.approve')" :visible.sync="visible" size="70%" @close="close">
        <div>
            <el-form :model="form"
                 ref="form"
                 label-width="200px"
                 label-position="top"
                 size="medium"
                 class="sgs-form"
                 @submit.native.prevent>
                <div class="sgs-group">
                    <h3>{{$t('customer.title.base')}}</h3>
                </div>
                <el-row :gutter="20">
                    <el-col span="24">
                        <el-form-item :label="$t('customer.name')">
                            <span>{{language=='zh-CN'? (form.customer.customerNameZh || form.customer.customerNameEn):(form.customer.customerNameEn || form.customer.customerNameZh)}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.taxNo')">
                            <span>{{form.customer.taxNo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.contactMobile')">
                            <span>{{form.customer.contactMobile}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.address')">
                            <span>{{language=='zh-CN' ? (form.customer.customerAddressZh || form.customer.customerAddressEn) :(form.customer.customerAddressEn || form.customer.customerAddressZh) }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.sgs.customerNo')">
                            <span>{{form.customer.bossNo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('customer.sgs.reportNo')">
                            <span>{{form.customer.reportNo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col span="24">
                      <el-form-item :label="$t('register.serveType')">
                        <div class="tag-group">
                          <el-tag  v-for="(serviceType,index) in serviceTypeNames" style="margin-left: 2px" >{{ serviceType }}</el-tag>
<!--                            <span>{{serviceTypeName}}</span>-->
                            <el-button v-if="form.customer.approveStatus==1 || form.customer.approveStatus==10 " style="margin-left: 15px; "
                                    type="text" @click="editServiceType(form.customer)">
                                {{$t('operation.edit')}}
                            </el-button>
                        </div>
                        </el-form-item>

                    </el-col>
                <!--默认选择的Unit  -->
                  <el-col :span="12">
                    <el-form-item :label="$t('common.isDefault')">
                      <div class="tag-group">
                        <el-tag style="margin-left: 2px" >{{ showDefualtServiceUnitName }}</el-tag>
                      </div>
                    </el-form-item>
                  </el-col>
                    <el-col span="24">
                        <el-form-item :label="$t('customer.certificate')">
                            <el-image
                                    style="width: 100px;"
                                    :src="qualificationUrl"
                                    :preview-src-list="[qualificationUrl]">
                            </el-image>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="sgs-group">
                    <h3>{{$t('customer.title.admin')}}</h3>
                </div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item :label="$t('account.userName')">
                            <span>{{form.customer.account.userName}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.email')">
                            <span>{{form.customer.account.email}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="sgs-group">
                    <h3>{{$t('customer.title.sgsSettings')}}</h3>
                </div>
                <el-row :gutter="20" class="sgs-customer-style">
                    <sgs-customer
                            v-on:customer-data='receiveApplicant'
                            displayType="account"
                            :show-customer-group=true
                            :customer-url="customerUrl"
                            :default-customer="customerUnit">
                    </sgs-customer>
                    <el-col span="24">
                        <el-form-item :label="$t('common.other')">
                            <el-checkbox v-model="customerApprove.isBuyer">{{$t('customer.joinBuyerProgram')}}</el-checkbox>
                            <el-checkbox v-model="customerApprove.relationshipNeedApprove">
                                {{$t('customer.customerRelationshipNeedApprove')}}
                            </el-checkbox>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customer.sgs.customerNo')">
                            <el-input v-model="customerApprove.bossNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customerGroup.code')">
                            <el-input v-model="customerApprove.customerGroupCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customerGroup.name')">
                            <el-input v-model="customerApprove.customerGroupName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <div class="sgs-bottom">
                        <el-button @click="close()">{{$t('operation.cancel')}}</el-button>
                        <el-button type="primary" v-if="permissionList.approveBtn" @click="onApprove()"
                                :disabled="isdisabled">{{$t('common.pass')}}
                        </el-button>
                        <el-button type="primary" v-if="permissionList.refuseBtn" @click="onrefuse()"
                                :disabled="isdisabled">{{$t('common.reject')}}
                        </el-button>
                    </div>
                </el-row>
            </el-form>
        </div>
        <el-dialog append-to-body="true" :title="$t('customer.editServiceUnit')"
                   :visible.sync="editProductCategoryDialogFormVisible" width="800px">
            <el-form :model="productCategoryForm" ref="productCategoryForm"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     :rules="rules"
                     class="sgs-form">
                <el-form-item :label="$t('register.serviceUnit')">
                  <el-select v-model="selServiceUnits"
                             multiple
                             collapse-tags
                             :required="true"
                             :placeholder="$t('register.serviceUnitBlur')"
                             @change="selectServiceTypeChange" style="width: 100%;">
                    <el-option v-for="(serviceUnit, index) in serviceUnits" :key="serviceUnit.serviceUnitCode" :label="serviceUnit.serviceUnitName"
                               :value="serviceUnit.serviceUnitCode">
                    </el-option>
                  </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editProductCategoryDialogFormVisible = false">{{$t('operation.cancel')}}
                </el-button>
                <el-button type="primary" :loading="btnProductCategoryLoading"
                           @click="productCategorySubmit('productCategoryForm')">{{$t('operation.confirm')}}
                </el-button>
            </div>
        </el-dialog>
    </el-drawer>
</template>
<script>
    import {detail, approve, updateCustomerProductCategory} from "@/api/customer/customerRegister";
    import {validatenull} from "@/util/validate";
    import {getCloudFileURL, serviceTypeList} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import pendCount from "@/api/common/pendCount";
    import {deepClone} from '@/util/util'
    import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";
    import {objectIsNull} from "../../util/validate";

    export default {
        name: "customer-detail-dialog",
        props: ['visible', 'customerId'],
        data() {
            return {
              selServiceUnits:[],
              selDefaultServiceUnit:null,
              defaultServiceUnitDatas:[],
              showSelServiceUnits:[],
              showDefualtServiceUnitName:'',
              serviceUnits:[],
                serviceTypeName: '',
                serviceTypeNames:[],
                editProductCategoryDialogFormVisible: false,
                btnProductCategoryLoading: false,
                serviceTypeData: [],
                productCategoryForm: {
                  serviceUnits:'',
                  serviceDomains:'',
                  productLineCodes:''
                },
                form: {
                    customer: {
                        account: {},
                    },
                },
                customer: {},
                customerUnit: {
                    bossNo: '',
                    customerGroupName: '',
                    customerGroupCode: '',
                    isBuyer: false,
                    relationshipNeedApprove: false,
                },
                customerApprove: {
                    bossNo: '',
                    customerGroupName: '',
                    customerGroupCode: '',
                    isBuyer: false,
                    relationshipNeedApprove: false,
                    productLineCodes:'',
                    serviceUnits:'',
                    serviceDomains:''

                },
                qualificationUrl: '',
                isdisabled: false,
                customerUrl: '/api/sgsapi/CustomerApi/preOrderCustomer/contactAddress/query'
            };
        },
        filters: {
            /*serviceTypeFiler: function (value) {
              if (!value) return '';
              value = value.toString();
               let obj = {};
                  obj = this.serviceTypeData.find((item) => {
                      return item.productLineCode === value;
                  });
                  if (!validatenull(obj)) {
                       return  obj.serviceName;
                  } else {
                        return  '';
                  }
            }*/
        },
        computed: {
            ...mapGetters(["permission", "language"]),
            permissionList() {
                return {
                    approveBtn: this.vaildData(this.permission['sgs:customer:approve:approve'], false),
                    refuseBtn: this.vaildData(this.permission['sgs:customer:approve:refuse'], false),
                };
            }
        },
        methods: {
            productCategorySubmitClick() {
                updateCustomerProductCategory(this.productCategoryForm).then(res => {
                    this.editProductCategoryDialogFormVisible = false;
                    pendCount.$emit('demo', 'msg');
                    this.$emit('submit');
                    this.close();
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                }).catch(error => {
                    //关闭弹框
                    this.updateTrfTemplateDialog = false;
                });
            },
            productCategorySubmit() {
                if (this.productCategoryForm.approveStatus != 1 && this.productCategoryForm.approveStatus != 10) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customer.approveStatusErrMsg'),
                        type: 'warning'
                    });
                    return false;
                }
                if (objectIsNull(this.selServiceUnits)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('register.serviceUnitBlur'),
                        type: 'warning'
                    });
                    return false;
                }
                this.$confirm(this.$t('customer.changeProductCatoryMsg'),this.$t('tip'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: 'warning'
                }).then(() => {
                  this.productCategorySubmitClick();
                }).catch(() => {
                   //取消
                });
            },
            selectServiceTypeChange(values) {
              debugger;
              //将默认的设置为null
              this.selDefaultServiceUnit=null;
              let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, values,this.selServiceUnits);
              debugger;
              this.mergeFormServiceUnitData(serviceUnitObj);

              /*  let obj = {};
                obj = this.serviceTypeData.find((item) => {
                    return item.productLineCode === val;
                });
                if (!validatenull(obj)) {
                    this.$set(this.productCategoryForm, 'serviceName', obj.serviceName);
                } else {
                    this.$set(this.productCategoryForm, 'serviceName', '');
                }*/
            },
          mergeFormServiceUnitData(serviceUnitObj){
              debugger
            if(!objectIsNull(serviceUnitObj)){
              this.$set(this.productCategoryForm, 'productLineCodes', JSON.stringify(serviceUnitObj.productLines));
              this.$set(this.productCategoryForm, 'serviceUnits', JSON.stringify(serviceUnitObj.serviceUnits));
              this.$set(this.productCategoryForm, 'serviceDomains', JSON.stringify(serviceUnitObj.serviceDomains));
            }
          },
            editServiceType(customer) {
                //验证是否可以修改
                if (customer.approveStatus != 1 &&  customer.approveStatus != 10) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('customer.approveStatusErrMsg'),
                        type: 'warning'
                    });
                    return false;
                }
                //打开修改产品类别窗口
                this.selServiceUnits=this.showSelServiceUnits;
                this.editProductCategoryDialogFormVisible = true;
                this.productCategoryForm.productLineCodes='';
                this.productCategoryForm.serviceDomains='';
                this.productCategoryForm.serviceUnits='';
                this.productCategoryForm.id = customer.id;
                this.productCategoryForm.approveStatus = customer.approveStatus;
            },
          async queryServiceType() {
            debugger;
            let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
            this.serviceUnits=serviceUnitsDatas;
          },
            close() {
                this.visible = false;
                this.$emit('update:visible', this.visible);
            },
            onApprove() {
                //加入校验 如果勾选择加入买家计划 则验证是否选择客户组
                this.customerApprove.customerId = this.form.customer.id;
                this.customerApprove.approveStatus = 90;
                if (this.customerApprove.isBuyer == true && validatenull(this.customerApprove.customerGroupId)) {
                    this.$message({
                        type: "error",
                        message: this.$t('customer.error.buyerNotCustomerGroup')
                    });
                } else {
                    approve(this.customerApprove).then(res => {
                        pendCount.$emit('demo', 'msg');
                        this.$emit('submit');
                        this.close();
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    });
                }
            },
            onrefuse() {
                this.customerApprove.customerId = this.form.customer.id;
                this.customerApprove.approveStatus = 80;//修改状态为拒绝
                this.customerApprove.sendEmail = this.form.customer.account.email;
                this.$prompt(this.$t('operation.confirmReject'), {
                    confirmButtonText: this.$t('submitText'),
                    //inputValidator:'请输入拒绝原因'
                }).then(({value}) => {
                    this.customerApprove.refuseReason = value;
                    approve(this.customerApprove).then(res => {
                        this.$emit('submit');
                        this.close();
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        pendCount.$emit('demo', 'msg');
                    });
                });
            },
            receiveApplicant(data) {
                console.log(data);
                //回填客户组信息
                this.customerApprove.customerGroupId = data.customerGroupId;
                this.customerApprove.customerGroupName = data.customerGroupName;
                this.customerApprove.customerGroupCode = data.customerGroupCode;
                this.customerApprove.bossNo = data.number;
                this.customerApprove.number = data.number;
                console.log(this.customerApprove.customerGroupName);
            },
            showServiceTypeName(){
              let serviceNames=[];
              for (let serviceUnitName of this.showSelServiceUnits) {
                for(let serviceUnitObj of this.serviceUnits){
                  if(serviceUnitName==serviceUnitObj.serviceUnitCode){
                    serviceNames.push(serviceUnitObj.serviceUnitName);
                    continue;
                  }
                }
              }
              this.serviceTypeNames=serviceNames;
            },
            async queryCustomer() {
                //const serviceTypeRes = await serviceTypeList(this.language);
                detail(this.customerId).then(res => {
                    this.form = res.data.data;
                    if (this.form.customer.approveStatus === 90) {
                        this.isdisabled = true;
                    } else {
                        this.isdisabled = false;
                    }
                    this.customerApprove.number = this.form.customer.bossNo;
                    this.customerApprove.bossNo = this.form.customer.bossNo;
                    this.customerApprove.customerGroupId = this.form.customer.customerGroupId;
                    this.customerApprove.customerGroupName = this.form.customer.customerGroupName;
                    this.customerApprove.customerGroupCode = this.form.customer.customerGroupCode;
                    this.customerApprove.isBuyer = (this.form.customer.isBuyer == 1);
                    this.customerApprove.relationshipNeedApprove = (this.form.customer.relationshipNeedApprove == 1);
                    this.customerUnit = deepClone(this.customerApprove);
                    //处理ServiceUnit回显
                  debugger;
                    if (!validatenull(this.form.customer.serviceUnits)){
                      let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits,this.form.customer.serviceUnits);
                      this.showSelServiceUnits = resultObj.serviceUnitDataItems;
                      this.showDefualtServiceUnitName = resultObj.defaultServiceUnitName;
                      //处理显示Tag
                      this.showServiceTypeName();
                      //获取默认的名称
                      let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, this.showSelServiceUnits,this.showSelServiceUnits);
                      this.defaultServiceUnitDatas=serviceUnitObj.selServiceUnitDatas;
                      this.selDefaultServiceUnit= serviceUnitTool.getDefaultServiceUnitDatas(this.form.customer.serviceUnits)
                    }


                   /* if (!validatenull(this.form.customer.productLineCodes)) {
                        const serviceTypeData = serviceTypeRes.data.data;
                        let obj = {};
                        obj = serviceTypeData.find((item) => {
                            return item.productLineCode === this.form.customer.productLineCode;
                        });
                        if (!validatenull(obj)) {
                            this.serviceTypeName = obj.serviceName;
                        }
                    }
                    console.log(this.form);*/
                    if (!validatenull(this.form.customer.qualification.attachmentId)) {
                        getCloudFileURL(this.form.customer.qualification.attachmentId).then(res => {
                            this.qualificationUrl = res.data;
                        });
                    }
                });
            },
          async initData(){
            await  this.queryServiceType();
            //加载该公司的Service Unit
            await  this.queryCustomer(this.customerId);
          }

        },
        watch: {
            customerId: function (data) {
                if (!validatenull(this.customerId)) {
                    this.initData();
                }
            },
        }
    };
</script>

<style lang="scss" scoped>
.sgs-customer-style {
    padding: 0 13px;
    /deep/ .col-md-6 {
        &:last-of-type {
            > h4 {
                margin: 0;
                height: 10px;
            }
        }
    }
    /deep/ .form-horizontal {
        .form-group {
            border: 0 !important;
            .control-label {
                font-weight: normal;
                color: #626066;
                font-size: 14px;
                width: 100%;
                text-align: left;
            }
            .col-sm-7 {
                width: 98%;
            }
        }
        .form-control {
            border-width: 0;
            border-bottom: 1px solid #1b1b1b;
            border-radius: 0;
            box-shadow: none;
        }
        .input-group-btn {
            border-bottom: 1px solid #1b1b1b;
        }
        .input-group-btn:last-child > .btn {
            border-radius: 0;
            background: transparent;
            color: #1b1b1b;
            border: 0;
            &:hover {
                color: #f60;
            }
        }
    }
}
</style>