const protocolTaxonomyList = [
    {
        "id": "",
        "code": "HAL00000",
        "name": "APL/ Auto Performance"
    },
    {
        "id": "",
        "code": "HBP00000",
        "name": "Brand Protection"
    },
    {
        "id": "",
        "code": "HFF00000",
        "name": "Fire technology Services"
    },
    {
        "id": "",
        "code": "HFI00000",
        "name": "Filtration"
    },
    {
        "id": "",
        "code": "HFU00000",
        "name": "Furniture"
    },
    {
        "id": "",
        "code": "HGA00000",
        "name": "Gas appliances (Portable and Fixed)"
    },
    {
        "id": "",
        "code": "HID00000",
        "name": "Home improvement, DIY"
    },
    {
        "id": "",
        "code": "HMD00000",
        "name": "Medical Devices, Medical Aids & Medical Supplies"
    },
    {
        "id": "",
        "code": "HOT00000",
        "name": "Others - Hardlines"
    },
    {
        "id": "",
        "code": "HPM00000",
        "name": "Packaging & Packaging material"
    },
    {
        "id": "",
        "code": "HPP00000",
        "name": "Personal protective equipment"
    },
    {
        "id": "",
        "code": "HSL00000",
        "name": "Sport & Leisure"
    },
    {
        "id": "",
        "code": "HST00000",
        "name": "Stationery, Household & Jewellery"
    },
    {
        "id": "",
        "code": "HTRP0000",
        "name": "TRP Lab Testing"
    },
    {
        "id": "",
        "code": "TJC00000",
        "name": "Juvenile/Nursery and Infant/Children Product/ Playground"
    },
    {
        "id": "",
        "code": "TOY00000",
        "name": "oys, Premium Novelty & Child Appealing products"
    }
]
const toolbarKeys = [
    "bold",
    "underline",
    "italic",
    "through",
    "sub",
    "sup",
    "clearStyle",
    "color",
    "bgColor",
    "fontSize",
    "fontFamily",
    "indent",
    "delIndent",
    "justifyLeft",
    "justifyRight",
    "justifyCenter",
    "justifyJustify",
    "divider",
    "bulletedList",
    "numberedList",
    "headerSelect",
    "redo",
    "undo"]
export default {protocolTaxonomyList,toolbarKeys};