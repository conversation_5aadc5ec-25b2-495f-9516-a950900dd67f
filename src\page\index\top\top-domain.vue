<template>
    <div id="serviceDomain" class="dis_block fr">
      <a @click="handleSetDomain('Testing')" ref='testing_a'  style="cursor:pointer">Testing</a>
      <span class="line">|</span>
      <a @click="handleSetDomain('Inspection')" ref='inspection_a' style="cursor:pointer">Inspection</a>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {validatenull} from "../../../util/validate";

    export default {
        name: "top-domain",
        data() {
            return {
                data: {
                  isActive: false
                }
            };
        },
        created() {
            
            
        },
        mounted() {
            //240519不上prod 去掉
            //获取用户默认的serviceDomain
            //this.queryUserDefaultDomain();
        },
        computed: {
            ...mapGetters(["language", "tag","userInfo"])
        },
        props: {
            loadMenu: {
                type: Boolean,
                default: true
            },

        },
        methods: {
            changeServiceDomain(domain) {
              if(domain=='Inspection') {   
                this.$refs.testing_a.className = "";
                this.$refs.inspection_a.className = "domain-active";        
              } else {
                this.$refs.testing_a.className = "domain-active";
                this.$refs.inspection_a.className = "";
              }
            },
            handleSetDomain(domain) {
                this.changeServiceDomain(domain);               
              
            },
        
            //查询当前用户配置的语言信息
            queryUserDefaultDomain() {
                if(!validatenull(this.userInfo.defaultServiceDomain)){
                    this.changeServiceDomain(this.userInfo.defaultServiceDomain);
                }     
            }
        }
    };
</script>

<style lang="scss" scoped>
#serviceDomain {
  font-size: 14px;
  background: #f2f2f2;
  height: 30px;
  line-height: 30px;
  padding: 0 4px;
  border-radius: 3px;
  > a {
    padding: 0 3px;
  }
  .line {
    padding: 0 5px;
  }
}
.domain-active {
  background: #f60;
  color: #fff !important;
  border-radius: 3px;
}
.dis_block {
  display: block;
  }
.fr {
  float: right !important;
}
.c_title {
  color: #f60 !important;
}

</style>
