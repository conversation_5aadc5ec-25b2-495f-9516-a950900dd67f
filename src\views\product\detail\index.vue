<template>
  <div class="smart_views_product_detail_index"
       id="smart_views_product_detail_index">
    <el-row :gutter="20">
      <el-col :span="24">
        <!--    头部信息，公用组件            -->
        <DetailHeader :headerInfo="{}"></DetailHeader>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="4">
        <!--    左侧锚点列表，公用组件  点击跳转是通过toTarget 自行处理   -->
        <NavList :navList="navList"
                 :toTarget="toTarget"></NavList>
      </el-col>
      <el-col :span="16">
        <!--   中间区域，动态渲染     -->
        <!-- <el-form :model="productForm"
                 label-position="left"
                 label-width="150px"> -->

        <el-card v-for="(sec, index) in data.sectionList"
                 :key="'sce_' + index"
                 class="sgs-box content-item model-card"
                 id="smart_views_care_label_card">
          <CollapseCard v-if="sec.sectionCode == 'HeaderInfo'"
                        :title="sec.sectionName">
            <HeaderInfo :formData="sec.fieldList"
                        @testSave="emitSample"></HeaderInfo>
          </CollapseCard>
          <CollapseCard v-if="sec.sectionCode == 'Workbooks'"
                        :title="sec.sectionName">
            <WorkBook :objectId="productForm.id"></WorkBook>
          </CollapseCard>
          <CollapseCard v-if="sec.sectionCode == 'Scop_CFCA'"
                        :title="sec.sectionName">
            <ScopeCertificates :objectId="productForm.id"></ScopeCertificates>
          </CollapseCard>
          <CollapseCard v-if="sec.sectionCode == 'TRACS_CFCA'"
                        :title="sec.sectionName">
            <TransactionCertificates :objectId="productForm.id"></TransactionCertificates>
          </CollapseCard>
          <CollapseCard v-if="sec.sectionCode == 'OR_DOCS'"
                        :title="sec.sectionName">
            <OtherResponsible :objectId="productForm.id"></OtherResponsible>
          </CollapseCard>

          <!-- <CollapseCard v-if="sec.sectionCode=='combineTable'"
                          :title="sec.sectionName">
              <CombineTable :formData="productForm"></CombineTable>
            </CollapseCard>
            <CollapseCard v-if="sec.sectionCode=='CareLabel'"
                          :title="sec.sectionName">
              <CareLabel customerGroupCode="CG000505"></CareLabel>
            </CollapseCard>
           
            <CollapseCard v-if="sec.sectionCode=='ReportCertificate'"
                          :title="sec.sectionName">
              <div>Report & Certificate 组件</div>
            </CollapseCard>
            <CollapseCard v-if="sec.sectionCode=='BillOfMaterials'"
                          :title="sec.sectionName">
              <div>Bill of Materials 组件</div>
            </CollapseCard> -->
        </el-card>
        <!-- </el-form> -->
      </el-col>
      <el-col :span="4">
        <div class="sider">
          <el-card class="model-card">
            <ProductImage v-model:uploadImgList="productImage.yourImgList"
                          :productForm="productImage.productForm"
                          :btnRole="productImage.btnRole"
                          :createNew="productImage.createNew"></ProductImage>
          </el-card>
          <el-card class="model-card">
            <ProductComment></ProductComment>
          </el-card>
        </div>
      </el-col>
    </el-row>
    <el-row class="sgs-footer page-no-print"
            style="position: fixed">
      <!-- v-if="!data.pageLoading && !data.viewPage" -->
      <el-col v-if="!data.pageLoading"
              style="text-align: center">
        <el-button class="custom-primary-button"
                   type="primary"
                   @click="saveProduct"
                   :disabeld="data.disabledSaveBtn">
          Save
        </el-button>
        <el-button v-if="data.action === 'new' || btnRole('Edit')"
                   class="custom-primary-button"
                   type="primary"
                   @click="actionProduct('Submit')"
                   :disabeld="data.disabledSaveBtn">
          Submit
        </el-button>
        <el-button v-if="btnRole('Submit')"
                   class="custom-primary-button"
                   type="primary"
                   @click="createTRF"
                   :disabeld="data.disabledSaveBtn">
          Create TRF
        </el-button>
        <el-button v-if="btnRole('ToTrf')"
                   class="custom-primary-button"
                   type="primary"
                   @click="actionProduct('Return')"
                   :disabeld="data.disabledSaveBtn">
          Return
        </el-button>
        <el-button v-if="btnRole('Return')"
                   class="custom-primary-button"
                   type="primary"
                   @click="actionProduct('Approved')"
                   :disabeld="data.disabledSaveBtn">
          Approved
        </el-button>
        <!-- v-if="data.permissionList.approvedBtn && btnRole(productForm,'Approved')" -->
        <el-button v-if="btnRole('Approved')"
                   class="custom-primary-button"
                   type="primary"
                   @click="actionProduct('NotInUse')"
                   :disabeld="data.disabledSaveBtn">
          Not In Use
        </el-button>
        <el-button v-if="btnRole('NotInUse')"
                   class="custom-primary-button"
                   type="primary"
                   @click="actionProduct('InUse')"
                   :disabeld="data.disabledSaveBtn">
          In Use
        </el-button>
        <!-- v-if="btnRole('InUse')" -->
        <el-button class="custom-primary-button"
                   type="primary"
                   @click="handleChangeAssign"
                   :disabeld="data.disabledSaveBtn">
          Change Assignee
        </el-button>
        <el-button v-if="btnRole('Cancel')"
                   class="custom-info-button"
                   type="info"
                   @click="actionProduct('Cancel')"
                   :disabeld="data.disabledSaveBtn">
          Cancel
        </el-button>
      </el-col>
    </el-row>
  </div>
  <ChangeAssignee :changeAssign="data.changeAssign" @emit:changeAssignee="emitChangeAssignee"></ChangeAssignee>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  computed,
  watch,
  provide,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElNotification, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import NavList from '@/components/NavList/index.vue'
import DetailHeader from '@/components/DetailHeader/index.vue'
import productApi from '@/api/product.ts'
import CollapseCard from '@/components/CollapseCard/index.vue'
import HeaderInfo from '../components/HeaderInfo.vue'
import CombineTable from '../components/CombineTable.vue'
import CareLabel from '../components/CareLabel.vue'
import WorkBook from '../components/WorkBook.vue'
import ScopeCertificates from '../components/ScopeCertificates.vue'
import TransactionCertificates from '../components/TransactionCertificates.vue'
import OtherResponsible from '../components/OtherResponsible.vue'
import ProductComment from '../components/ProductComment.vue'
// import ProductComment from "./components/ProductComment.vue";
import ProductImage from '@/components/product/image/ProductImage.vue'
import { sampleTemplate, parseTemplateData } from './sampleTemplate.js'
import { actionSamples } from '@/api/sample'
import ChangeAssignee from '@/components/product/ChangeAssignee/ChangeAssignee.vue'

const { t } = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)

const data = reactive({
  sectionList: [],
  sampleId: '',
  templateId: '',
  sampleDetail: {},
  pageLoading: false,
  viewPage: 'preview', //  会有其他状态
  generalFieldConfig: [],
  disabledSaveBtn: false,
  createNew: false,
  permissionList: {},
  action: 'new', // new 新建， detail: 编辑
  savaParams: {}, // 保存入参
  changeAssign: {
    visibale: false,
    customerGroupCode: '',
  },
})

const actionProduct = (action) => {
  ElMessageBox.confirm(action + ' the product?', 'Tips', {
    confirmButtonText: 'Confirm',
    cancelButtonText: 'Cancel',
    type: 'warning',
  })
    .then(() => {
      if (data.sampleId) {
        actionSamples({ ids: [data.sampleId], action })
          .then((res) => {
            debugger
          })
          .catch((err) => {})
      }
    })
    .catch(() => {})

  // this.$confirm(action+' the product?', 'Tips', {
  //     confirmButtonText: 'Confirm',
  //     cancelButtonText: 'Cancel',
  //     type: 'warning'
  // }).then(() => {
  //     let {id} = this.productForm;
  //     api.actionSamples({ids:[id],action}).then(res=>{
  //         if(res.status==200 && res.data){
  //             this.$notify.success("Success");
  //             if(callback){
  //                 callback();
  //             }else{
  //                 window.location.reload();
  //             }
  //         }else{
  //             this.$notify.error(res.message || "Operation Fail");
  //         }
  //     }).catch(err=>{
  //     })
  // }).catch(() => {
  // });
}
const createTRF = () => {}

const handlerCancelProduct = () => {}
const btnRole = (code) => {
  return (data.sampleDetail?.permissions || [])
    .map((p) => p.action)
    .includes(code)
}

const navList = ref([])
// navlist 的点击事件，页面跳转到对应seciton位置
const toTarget = (item) => {
  console.log(item)
}

watch(language, (newVal) => {})
/**
 * 页面渲染结构处理开始
 * */
// 定义product Detail页面数据结构对象
const productForm = reactive({
  id: '0d6c01c922a8137ace725e7bac95d748',
  checkboxTableData: [],
})

/* 0. 解析页面路由参数*/
const parsePathParam = () => {
  const route = router.currentRoute.value
  const path = route.path
  const query = route.query
  let { action, id, template } = query
  //区分是新增，修改，还是仅回显
  if (action == 'add') {
  }
  if (action == 'edit') {
  }
  if (action == 'preview') {
  }
  //使用参数加载模板
  initPageTemplate()
}
/* 1. 获取页面渲染模板*/
const templateSectionList = ref([])
const initPageTemplate = () => {
  // 使用导入的模板数据进行解析
  const parsedData = parseTemplateData(sampleTemplate)

  // 设置模板解析后的数据
  templateSectionList.value = parsedData.templateSectionList
  navList.value = parsedData.navList

  // 打印模板信息供调试
  console.log('Template Info:', parsedData.templateInfo)
  console.log('Template Sections:', templateSectionList.value)
  console.log('Nav List:', navList.value)
  console.log('Original sectionList:', sampleTemplate.sectionList)

  //调用detail接口，获取页面业务数据，进行数据处理
  initPageData()
}
/* 2. 加载页面基础数据/回显数据*/
const initPageData = () => {
  // 初始化表单数据
  initFormDataFromTemplate()

  //api success
  initListData()
}

/* 2.1 从模板初始化表单数据 */
const initFormDataFromTemplate = () => {
  templateSectionList.value.forEach((section) => {
    if (section.generalFieldList && section.generalFieldList.length > 0) {
      section.generalFieldList.forEach((field) => {
        if (field.fieldCode && !productForm[field.fieldCode]) {
          productForm[field.fieldCode] = field.defaultValue || ''
        }
      })
    }
  })
  console.log('初始化表单数据完成:', productForm)
}
/* 3. 加载动态渲染组件中的list集合 */
const initListData = () => {
  // 回显数据
  parseCodeToLabel()
  parseData()
}
/* 4. 处理回显数据中，code 显示为 label*/
const parseCodeToLabel = () => {}
/* 5. 页面数据/结构解析处理*/
const parseData = () => {}

/* 6. 获取字段选项标签 */
const getFieldOptionLabel = (sectionCode, fieldCode, value) => {
  const section = templateSectionList.value.find(
    (s) => s.sectionCode === sectionCode,
  )
  if (section && section.generalFieldList) {
    const field = section.generalFieldList.find(
      (f) => f.fieldCode === fieldCode,
    )
    if (field && field.sourceValue) {
      const option = field.sourceValue.find((opt) => opt.code === value)
      return option ? option.name : value
    }
  }
  return value
}

/* 7. 验证必填字段 */
const validateRequiredFields = () => {
  const errors = []
  templateSectionList.value.forEach((section) => {
    if (section.generalFieldList) {
      section.generalFieldList.forEach((field) => {
        if (
          field.isRequired === 1 &&
          (!productForm[field.fieldCode] || productForm[field.fieldCode] === '')
        ) {
          errors.push({
            fieldCode: field.fieldCode,
            fieldLabel: field.fieldLabel,
            sectionName: section.sectionName,
            message: `${field.fieldLabel}为必填项`,
          })
        }
      })
    }
  })
  return errors
}

/* 8. 保存表单数据 */
const handleSaveForm = () => {
  const errors = validateRequiredFields()
  if (errors.length > 0) {
    console.error('表单验证失败:', errors)
    ElNotification.error({
      title: '表单验证失败',
      message: `${errors.length}个必填字段未填写`,
    })
    return false
  }

  const submitData = {
    templateId: sampleTemplate.templateId,
    templateName: sampleTemplate.templateName,
    formData: productForm,
    customerGroupName: sampleTemplate.customerGroupName,
    buName: sampleTemplate.buName,
    countryName: sampleTemplate.countryName,
  }

  console.log('准备提交的数据:', submitData)

  // 这里可以调用API保存数据
  // templateApi .saveProduct(submitData).then(...)
  return true
}

/**
 * 页面渲染结构处理结束
 * */
/* product image */
const productImage = reactive({
  yourImgList: [],
  productForm: productForm,
  btnRole: (form, action) => {
    // 根据 form 和 action 返回按钮权限
    return true // 示例返回值
  },
  createNew: false,
})

/* 获取页面模板 */
const qryTemplate = () => {
  productApi.templateApi
    .templateList({
      buCode: 'SL',
      customerGroupCode: 'CG0000050',
      formPurpose: 'Product',
      queryAllVersion: 0,
      current: 1,
      size: 10,
    })
    .then((res) => {
      data.sectionList = res.data[0].generalFieldConfig || []
      data.templateId = res.data[0].id
      qryDetail()
    })
}
/*  获取页面详情数据 */
const qryDetail = () => {
  productApi.templateApi.detail({ id: data.sampleId || '' }).then((res) => {
    data.sampleDetail = res?.data
    data.permissions = res?.data?.permissions || []
    data.changeAssign.customerGroupCode = res?.data?.buyerCustomerGroupCode || ''
    /* 处理header info 显示以及回显 */
    handleHeaderInfo()
  })
}
/* 处理header info 显示以及回显 */
const handleHeaderInfo = () => {
  const fieldInfo = data.sampleDetail.fieldInfo || []
  //   const headerInfo = fieldInfo.filter((item) => item.isGeneral === 1)
  //   productForm.headerInfo = headerInfo
  fieldInfo.forEach((v) => {
    data.sectionList.forEach((section) => {
      if (section.sectionCode === 'HeaderInfo') {
        section.fieldList.forEach((field) => {
          if (field.fieldCode === v.fieldCode) {
            field[field.fieldCode] = v.fieldValue
          }
        })
      }
    })
  })
  console.log('data.sectionList', data.sectionList)
}

const emitSample = (val) => {
  console.log(val, 'aaaaaaaaaaaaaaaaaaa')
  const fieldInfo = []
  if (val.length) {
    val.forEach((item) => {
      if (item[item.fieldCode]) {
        fieldInfo.push({
          isGeneral: item.isGeneral,
          fieldCode: item.fieldCode,
          fieldValue: item[item.fieldCode],
        })
      }
    })
  }
  data.savaParams = {
    id: data.sampleDetail?.id || '',
    objectId: data.sampleDetail?.objectId || '',
    dataType: data.sampleDetail?.dataType || 'product',
    sourceType: data.sampleDetail?.sourceType || '',
    templateId: data.templateId,
    fieldInfo,
  }
  /* 
  {
        id: data.sampleDetail?.id || '',
        objectId: '',
        dataType: 'product',
        sourceType: '',
        templateId: data.templateId,
        fieldInfo: [
          {
            isGeneral: 1,
            fieldCode: 'serviceType',
            fieldValue: '2',
          },
          {
            isGeneral: 1,
            fieldCode: 'labCode',
            fieldValue: 'AIX SL',
          },
          {
            isGeneral: 0,
            fieldCode: 'ProductCategory',
            fieldValue: ['1', '2', '3'],
          },
          {
            isGeneral: 0,
            fieldCode: 'chemicalTestingType',
            fieldValue: ['2025-02-14', '2025-03-24'],
          },
          {
            isGeneral: 0,
            fieldCode: 'chemicalTestingType',
            fieldValue: [
              {
                seq: 1,
                ingredients: '123',
                percentage: 1,
              },
              {
                seq: 2,
                ingredients: '123',
                percentage: 12,
              },
              {
                seq: 3,
                ingredients: '123',
                percentage: 87,
              },
            ],
          },
          {
            isGeneral: 0,
            fieldCode: 'factoryID',
            fieldValue: 'test1245',
          },
        ],
      }
  */
}
const saveProduct = () => {
  data.pageLoading = true
  productApi.templateApi.create(data.savaParams).then((res) => {
    debugger
    data.pageLoading = false
  })
}

/* ChangeAssignee 模块 */
const handleChangeAssign = () => {
  data.changeAssign.visibale = true
}
const emitChangeAssignee = (val) => {
    const params = JSON.parse(JSON.stringify(data.sampleDetail))
    data.savaParams = params
    data.savaParams.assigneeId = val
    saveProduct()
}
onMounted(() => {
  const { action, id } = router.currentRoute.value.query
  data.sampleId = id
  data.action = action
  parsePathParam()
  qryTemplate()
})
</script>

<style lang="scss">
@use '@/assets/style/unit.module.scss' as *;
.smart_views_product_detail_index {
  padding: $module-padding;
}
</style>
