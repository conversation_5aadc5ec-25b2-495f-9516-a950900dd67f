<template>
  <el-descriptions
    :column="2"
    label-width="140"
    class="protocol-detail-descriptions"
  >
    <template
      v-for="(fl, readOnlyIndex) in [
        templateObj.readOnlyFieldList || [],
        templateObj.generalFieldList || [],
        templateObj.customerFieldList || [],
      ]"
      :key="'readonlyField_' + readOnlyIndex"
    >
      <template v-for="(f, index) in fl" :key="'gen_cus_' + index">
        <el-descriptions-item
          :label="f.fieldLabel"
          width="50%"
          label-class-name="item-label"
          class-name="item-content"
        >
          <span v-if="['number'].includes(f.fieldType)">
            {{ props.protocolForm[f.fieldCode] }}
          </span>
          <span v-if="['date'].includes(f.fieldType)">
            {{ props.protocolForm[f.fieldCode] }}
          </span>
          <span
            v-if="!f.fieldType || ['input', 'textArea'].includes(f.fieldType)"
          >
            {{ props.protocolForm[f.fieldCode] }}
          </span>
          <template v-if="['select', 'select2'].includes(f.fieldType)">
            <el-tag
              v-for="(value, tagIndex) in getSelectedValues(f)"
              :key="tagIndex"
              type="info"
              class="tag"
            >
              {{ value.name }}
            </el-tag>
          </template>
        </el-descriptions-item>
      </template>
    </template>
  </el-descriptions>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  templateObj: {
    type: Object,
    required: true,
  },
  protocolForm: {
    type: Object,
    required: true,
  },
})

const getSelectedValues = computed(() => {
  return (f) => {
    const values = props.protocolForm[f.fieldCode]
    if (Array.isArray(values)) {
      const data = values.map((value) => {
        const option = f.sourceValue.find((o) => o.code === value)
        return option
      })
      return data
    }
    const option = f.sourceValue.find((o) => o.code === values)
    return [option]
  }
})
</script>

<style lang="scss" scoped>
.protocol-detail-descriptions {
  .tag {
    margin-right: 10px;
    margin-bottom: 4px;
  }
}
:deep(.item-label) {
  vertical-align: top;
}
:deep(.item-content) {
  display: inline-block;
  max-width: 250px;
  word-break: break-word;
  white-space: normal;
}
</style>
