// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/qingdao/smart-web3/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/qingdao/smart-web3/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "file:///C:/Users/<USER>/qingdao/smart-web3/node_modules/path-browserify/index.js";

// package.json
var package_default = {
  name: "smart-web3",
  code: "project_smart",
  version: "24.10.15",
  author: "<EMAIL>",
  description: "smart web vue3",
  private: true,
  type: "module",
  scripts: {
    dev: "vite",
    test: "vue-tsc -b && vite build --mode test",
    uat: "vue-tsc -b && vite build --mode uat",
    build: "vue-tsc -b && vite build --mode production && rm -rf ./dist/*.map",
    preview: "vite preview",
    lint: "eslint src",
    fix: "eslint src --fix"
  },
  dependencies: {
    "@element-plus/icons-vue": "^2.3.1",
    "@wangeditor/editor": "^5.1.23",
    "@wangeditor/editor-for-vue": "^5.1.12",
    axios: "^0.21.1",
    "element-plus": "^2.9.5",
    sass: "^1.54.9",
    vue: "^3.4.31",
    "vue-i18n": "^10.0.4",
    "vue-router": "^4.4.0",
    vuex: "^4.1.0"
  },
  devDependencies: {
    "@eslint/js": "^8.57.1",
    "@intlify/unplugin-vue-i18n": "^6.0.3",
    "@sentry/tracing": "7.43.0",
    "@sentry/vue": "7.43.0",
    "@sentry/webpack-plugin": "1.20.0",
    "@types/crypto-js": "^4.2.2",
    "@types/js-cookie": "^3.0.6",
    "@types/node": "^22.13.1",
    "@types/path-browserify": "^1.0.3",
    "@vitejs/plugin-vue": "^5.0.5",
    "@vue/test-utils": "^2.4.6",
    commitizen: "^4.3.1",
    "crypto-js": "^4.2.0",
    "cz-conventional-changelog": "^3.3.0",
    eslint: "^8.57.1",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-prettier": "^5.2.1",
    "eslint-plugin-vue": "^9.27.0",
    globals: "^15.8.0",
    "js-cookie": "^3.0.5",
    "js-md5": "^0.7.3",
    jsencrypt: "^3.3.1",
    "path-browserify": "^1.0.1",
    prettier: "^3.3.3",
    terser: "^5.39.0",
    typescript: "^5.7.3",
    "typescript-eslint": "^7.16.1",
    vite: "^5.3.4",
    "vite-plugin-sentry": "^1.1.7",
    vitest: "^3.0.6",
    "vue-svg-loader": "^0.16.0",
    "vue-tsc": "^2.2.0"
  },
  config: {
    commitizen: {
      path: "node_modules/cz-conventional-changelog"
    }
  }
};

// vite.config.ts
import viteSentry from "file:///C:/Users/<USER>/qingdao/smart-web3/node_modules/vite-plugin-sentry/dist/index.mjs";
import vueI18n from "file:///C:/Users/<USER>/qingdao/smart-web3/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\qingdao\\smart-web3";
var vite_config_default = defineConfig((mode) => {
  const currentEnv = mode.mode;
  const sentryConfig = {
    configFile: "./.sentryclirc",
    release: package_default.version,
    // 版本号
    deploy: {
      env: currentEnv
    },
    skipEnvironmentCheck: true,
    // 可以跳过环境检查
    sourceMaps: {
      include: ["./dist/assets"],
      ignore: ["node_modules"],
      urlPrefix: "~/assets"
      // 注意这里设置正确，否则sourcemap上传不正确
    }
  };
  return {
    plugins: [
      vue(),
      vueI18n({
        runtimeOnly: true,
        compositionOnly: true,
        include: [path.resolve(__vite_injected_original_dirname, "./src/locales/**")]
      }),
      currentEnv === "production" ? viteSentry(sentryConfig) : null
    ],
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "/src"),
        assets: path.resolve(__vite_injected_original_dirname, "/src/assets"),
        components: path.resolve(__vite_injected_original_dirname, "/src/components"),
        request: path.resolve(__vite_injected_original_dirname, "/src/request"),
        router: path.resolve(__vite_injected_original_dirname, "/src/router"),
        store: path.resolve(__vite_injected_original_dirname, "/src/store"),
        type: path.resolve(__vite_injected_original_dirname, "/src/type"),
        util: path.resolve(__vite_injected_original_dirname, "/src/utils"),
        views: path.resolve(__vite_injected_original_dirname, "/src/views")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern"
          //   additionalData: '@import "src/assets/style/mixin.scss";',
          // additionalData: '@import "./src/assets/style/element-variarbles.scss";'
        }
      }
    },
    server: {
      host: "0.0.0.0",
      port: 9537,
      // 自定义端口号
      open: true,
      // 启动后是否直接打开浏览器
      proxy: {
        "/api": {
          target: "http://sgs-gateway.etoak.net:8001",
          //代理的地址
          changeOrigin: true,
          rewrite: (path2) => path2.replace(/^\/api/, "")
        }
      }
    },
    build: {
      // 开启sourcemap
      sourcemap: true,
      minify: "terser",
      // 删除console预计debugger
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
