import { Directive, computed, ref } from 'vue'
import store from '../store'
import { RootState } from '../type/store'

const permission: Directive = {
  mounted(el, binding) {
    const isLoading = ref(true)
    const userInfo = computed(() => (store.state as RootState).user.userInfo)
    const permissions = computed(
      () => (store.state as RootState).user.permission,
    )

    const checkPermission = () => {
      if (isLoading.value) {
        return
      }
      const requiredPermission = binding.value
      if (!permissions.value[requiredPermission]) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }

    // 等待权限数据加载完成
    const waitForPermissions = () => {
      if (userInfo.value.account) {
        isLoading.value = false
        checkPermission()
      } else {
        setTimeout(waitForPermissions, 100)
      }
    }
    waitForPermissions()
  },
}

export default permission
