import { Directive, nextTick } from 'vue'
import store from '../store'
import { RootState } from '../type/store'

const permission: Directive = {
  mounted(el, binding) {
    nextTick(() => {
      const permissions = (store.state as RootState).user.permission
      const requiredPermission = binding.value
      if (!permissions[requiredPermission]) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    })
  },
}

export default permission
