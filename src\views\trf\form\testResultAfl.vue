<template>
    <div style="margin-top: 24px;">
        <el-card>
            <el-table :data="data" border style="width: 100%;">
    <!--            <el-table-column prop="slimSubJobNo" label="SlimSubJobNo" width="300" align='center'></el-table-column>-->
    <!--            <el-table-column prop="orderNo" label="OrderNo" width="300" align='center'></el-table-column>-->
    <!--            <el-table-column prop="orderId" label="OrderId" width="150" align='center'></el-table-column>-->
                <el-table-column prop="cloudId" label="报告" align='center'></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="150" align='center'></el-table-column>
                <el-table-column label="操作" width="150" align='center'>
                    <template slot-scope="scope">
                        <el-button
                        size="mini"
                        type='primary'
                        @click="downFile(scope.row)">报告下载</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="param.current"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="param.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script>
    import {getReportList,downloadByCloudID} from '@/api/trf/trf';
    export default {
        name: "trfList",
        data() {
            return {
                data:'',
                param:{
                    size:10,
                    current:0,
                    isDeleted:0,
                    trfId:''
                },
                total:0
            }
        },
        mounted() {
            this.param.trfId = this.$route.query.id
            this.getReportList()
        },
        methods: {
            getReportList() {
                let param = new FormData()
                param.append('size',this.param.size)
                param.append('current',this.param.current)
                param.append('isDeleted',this.param.isDeleted)
                param.append('trfId',this.param.trfId)
                getReportList(param).then(res=>{
                    if (res.data.code == 200) {
                        this.data = res.data.data.records
                        this.total = res.data.data.total
                    }
                })
            },
            handleSizeChange(val) {
                this.param.size = val
                this.param.current = 1
                this.getReportList()
            },
            handleCurrentChange(val) {
                this.param.current = val
                this.getReportList()
            },
            downFile(row) {
                let loading = this.$loading({
                    lock: true,
                    text: '加载中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                let param = {
                    systemID:1,
                    networkType:2,
                    hash:new Date().getTime(),
                    cloudID:row.cloudId
                }
                downloadByCloudID(param).then(res=>{
                    loading.close()
                    if (res.data) {
                        let link = res.data.replace(/\s*/g,'')
                        let fileUrl = encodeURIComponent(link)
                        let arr = row.cloudId.split('/')
                        let fileName = arr[arr.length-1]
                        let url = '/api/sgs-mart/afl/trf/fileDownload?fileUrl='+fileUrl+'&fileUrlName='+fileName
                        let dom = document.createElement('a')
                        dom.className = 'downDocFile'
                        dom.setAttribute('href',url)
                        dom.click()
                        dom.remove()
                    }
                }).catch(err=>{
                    
                })
            }
        },
    };
</script>