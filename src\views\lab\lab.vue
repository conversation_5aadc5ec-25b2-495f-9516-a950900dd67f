<template>
    <basic-container >
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.labManagement')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.labManagement')}}</h1>

        <el-card shadow="never" class="box-card">
            <el-row>
                <el-form :inline="true" :model="query" size="medium" label-width="200px" >
                <el-form-item>
                    <el-select clearable v-model="query.labType" :placeholder="$t('lab.labType')" >
                        <el-option v-for="(labType,index) in labTypeData" :disabled="labType.sysKey=='1'"  :label="labType.sysValue" :value="labType.sysKey"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select clearable :placeholder="$t('term.productLine')" v-model="query.productLineCode" filterable style="width:100%" >
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
                <!--<el-form-item>
                    <el-select clearable :placeholder="$t('term.customerGroup')" v-model="query.customerGroupCode" filterable style="width:100%">
                        <el-option v-for="(customerGroup,index) in customerGroupData" :label="customerGroup.customerGroupName"
                                :value="customerGroup.customerGroupCode"></el-option>
                    </el-select>
                </el-form-item>-->
                <el-form-item>
                    <el-input maxlength="200"  clearable :placeholder="$t('lab.labName')"  v-model="query.labName" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchLabList()"  v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.search')}}</el-button>
                    <el-button type="primary"  @click="openUpload">
                      {{$t('customerMaterialConfig.openExcel')}}
                    </el-button>
                    <reset-button @click="clear"></reset-button>
                </el-form-item>
            </el-form>
            </el-row>
            <el-row>
                <el-row align="right" style="margin: 5px 0px">
                    <el-button  type="primary" icon="el-icon-circle-plus-outline" size="medium" v-if="permissionList.addLabBtn" @click="addLab()">{{$t('operation.add')}}</el-button>
                </el-row>
                <el-table  ref="labTable"
                        :row-key="getRowKeys"
                        v-loading="loading"
                        :element-loading-text="$t('loading')"
                        :data="data"  style="width: 100%"
                        @expand-change="labContactChange"
                        :expand-row-keys="expands">
                    <el-table-column type="index" label="#" width="50" fixed> </el-table-column>
                    <el-table-column type="expand"  >
                        <template slot-scope="props">
                            <el-table :data="tableData"  v-loading="contactloading" :element-loading-text="$t('loading')" style="width: 100%">
                                <el-table-column
                                        prop="contactName"
                                        :label="$t('labContact.contactName')"
                                        width="200"
                                >
                                </el-table-column>
                                <el-table-column
                                        prop="contactTel"
                                        width="160"
                                        :label="$t('labContact.contactTel')">
                                </el-table-column>
                                <el-table-column
                                        prop="contactEmail"
                                        width="240"
                                        :label="$t('labContact.contactEmail')">
                                </el-table-column>
                                <!--<el-table-column
                                        prop="contactAddress"
                                        :label="$t('labContact.contactAddress')">
                                </el-table-column>-->
                                <el-table-column :label="$t('operation.title')" width="180">
                                    <template slot-scope="scope">
                                        <el-button v-if="permissionList.editLabContactBtn" @click="rowContactUpdate(scope.row)" type="text">{{$t('operation.edit')}}</el-button>
                                        <el-button v-if="permissionList.delLabContactBtn" @click="deleteLabContact(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>
                    <el-table-column fixed prop="labName" :show-overflow-tooltip="true" :label="$t('lab.labName')" width="260"></el-table-column>
                    <el-table-column prop="productLineName" :label="$t('term.productLine')"  width="200" ></el-table-column>
                    <!--el-table-column prop="customerGroupName" :label="$t('term.customerGroup')" width="260" ></el-table-column>-->
                    <el-table-column prop="labTypeName"  :show-overflow-tooltip="true" :label="$t('lab.labType')"  width="140" ></el-table-column>
                    <el-table-column prop="companyName" :show-overflow-tooltip="true" :label="$t('customer.name')" width="220" ></el-table-column>
                    <el-table-column prop="countryName"  :show-overflow-tooltip="true" :label="$t('lab.labCountry')" width="140" ></el-table-column>
                    <el-table-column prop="locationName"  :show-overflow-tooltip="true" :label="$t('lab.labCity')"width="140" ></el-table-column>

                    <el-table-column prop="labAddress"  :show-overflow-tooltip="true" :label="$t('lab.labAddress')" ></el-table-column>
                <!-- <el-table-column prop="updateUser" :label="$t('common.operator')" width="140" ></el-table-column>
                    <el-table-column prop="updateTime" :label="$t('common.operationTime')" width="140" ></el-table-column>-->
                    <el-table-column :label="$t('operation.title')" width="240" align="center" fixed="right">
                        <template slot-scope="scope">
                        <!-- <el-button @click="showLabDetail(scope.row)" type="text" size="mini">查看</el-button>-->
                            <el-button v-if="permissionList.addLabContactBtn" @click="addLabContact(scope.row)" type="text" size="mini" icon="el-icon-circle-plus-outline">{{$t('lab.addContact')}}</el-button>
                            <el-button v-if="permissionList.editLabBtn" @click="rowUpdate(scope.row)" type="text" size="mini" icon="el-icon-edit">{{$t('operation.edit')}}</el-button>
                            <el-button v-if="permissionList.delLabBtn" @click="deleteLab(scope.row)" type="text" size="mini" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
        <el-drawer :before-close="closeLabDrawer" :title="labFormTitle" :visible.sync="dialogLabFormVisible" size="60%">
            <el-form :model="labForm" :rules="rules"  label-position="left"  ref="labForm" label-width="200px" size="medium" class="sgs-form">
              <div class="sgs-group">
                <h4 class="sgs-title">{{$t('lab.labInfo')}}</h4>
              </div>
                <el-form-item :label="$t('lab.labType')"  prop="labType">
                    <el-select v-model="labForm.labType" clearable :placeholder="$t('lab.selLabType')" @change="selectLabTypeChange" style="width:100%">
                        <el-option v-for="(labType,index) in labTypeData" :disabled="labType.sysKey=='1'" :label="labType.sysValue" :value="labType.sysKey"></el-option>
                    </el-select>
                </el-form-item>
                 <el-form-item :label="$t('customer.sgs.customerNo')"  prop="bossNo" >
                        <!--<el-autocomplete ref="autocomplete"
                                             clearable
                                             v-model="bossNo"
                                             :fetch-suggestions="querySearch"
                                             :placeholder="$t('register.bossNo')"
                                             :trigger-on-focus="false"
                                             @select="handleSelect"
                                             maxlength="200"
                                             style="width: 100%"
                            ></el-autocomplete>-->
                     <el-select v-model="labForm.bossNo" filterable clearable
                                       :placeholder="$t('register.bossNo')"
                                       reserve-keyword
                                       remote
                                       allow-create
                                       :remote-method="querySearch"
                                       @change="handleSelect"
                                       style="width: 100%;">
                                <el-option v-for="(customer,index) in customerData"
                                           :label="customer.number"
                                           :value="customer.number"></el-option>
                            </el-select>

                 </el-form-item>
                 <el-form-item :label="$t('customer.name')"  prop="companyName">
                    <el-input maxlength="200" v-model="labForm.companyName" disabled="true" :placeholder="$t('customer.name')"  clearable autocomplete="off"></el-input>
                 </el-form-item>

                <!--<sgs-customer
                            v-on:customer-data='receiveApplicant'
                            displayType="account"
                            :show-customer-group=false
                            :customer-url="customerUrl"
                            :default-customer="customerUnit">
                    </sgs-customer>-->
               <!-- <el-form-item :label="$t('term.customerGroup')" prop="customerGroupCode">
                    <el-select v-model="labForm.customerGroupCode" clearable filterable style="width:100%" @change="customerGroupCodeChange">
                        <el-option v-for="(customerGroup,index) in customerGroupData" :label="customerGroup.customerGroupName"
                                   :value="customerGroup.customerGroupCode"></el-option>
                    </el-select>
                </el-form-item>-->
                <el-form-item :label="$t('term.productLine')" prop="productLineCode">
                    <el-select v-model="labForm.productLineCode" filterable clearable style="width:100%" @change="selecProductLineCodeChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                   :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>


                <el-form-item :label="$t('lab.labName')" prop="labName">
                    <el-input maxlength="200" v-model="labForm.labName" clearable autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item :label="$t('lab.labCode')" prop="labCode">
                    <el-input maxlength="200" v-model="labForm.labCode" autocomplete="off"></el-input>
                </el-form-item>


              <el-form-item :label="$t('lab.labCountry')" prop="labCountry">
                <el-select v-model="labForm.countryCode" filterable clearable
                           :placeholder="$t('lab.labCountry')"
                           reserve-keyword
                           @change="handleCountrySelect"
                           style="width: 100%;">
                  <el-option v-for="(country,index) in countryDatas"
                             :label=" language === LanguageEnums.CN.name ? country.countryNameCn : country.countryNameEn "
                             :value="country.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lab.labCity')" prop="labCountry">
                <el-select v-model="labForm.locationCode" filterable clearable
                           :placeholder="$t('lab.labCity')"
                           @change="handleLocationSelect"
                           style="width: 100%;">
                  <el-option v-for="(location,index) in locationDatas"
                             :label="location.locationName"
                             :value="location.locationCode"></el-option>
                </el-select>
              </el-form-item>

                <el-form-item :label="$t('lab.labAddress')" prop="labAddress">
                    <el-input maxlength="200" v-model="labForm.labAddress" clearable autocomplete="off"></el-input>
                </el-form-item>

              <div class="sgs-group">
                <h4 class="sgs-title">{{$t('lab.labCertificate')}}</h4>
              </div>
              <el-row>
                <div class="right">
                  <el-button type="primary" @click="add" size="small">
                    {{$t('operation.add')}}
                  </el-button>
                </div>
              </el-row>

              <el-table
                  size="mini"
                  :data="labCertificateOption.data"
                  fixed
                  style="width: 100%;margin:auto"
                  highlight-current-row
              >
                <el-table-column type="index">#</el-table-column>
                <el-table-column
                    v-for="(item,index) in labCertificateOption.columns"
                    :label="item.label"
                    :prop="item.prop"
                    :key="index"
                >
                  <template slot-scope="scope">
                    <span v-if="item.prop=='certificateLabId'">
                      <el-input placeholder="请输入内容" v-model="scope.row[item.prop]"></el-input>
                    </span>
                    <span v-else-if="item.prop=='certificateType'">
                      <el-select v-model="scope.row[item.prop]" filterable clearable
                                 @change="handlelabCertificateTypeSelect($event,scope.row)"
                                 style="width: 100%;">
                        <el-option v-for="(certType,index) in labCertificateTypeData" :label="certType.sysValue" :value="certType.sysKey"></el-option>
                      </el-select>
                    </span>
                    <span v-else-if="item.prop=='labContactId'">
                      <el-select v-model="scope.row[item.prop]" filterable clearable
                                 @change="handlelabContactSelect($event,scope.row)"
                                 style="width: 100%;">
                        <el-option v-for="(labContact,index) in labContactData"
                                   :label="labContact.contactName"
                                   :value="labContact.id"></el-option>
                      </el-select>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button size="mini" type="danger" @click="deleteRow(scope.$index,labCertificateOption.data)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>


                <div class="sgs-bottom">
                    <el-button @click="closeLabDrawer" :disabled="isDisabled">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary"  @click="submitForm('labForm')" :disabled="isDisabled">{{$t('operation.confirm')}}</el-button>
                </div>
            </el-form>
        </el-drawer>
        <el-drawer :before-close="closeLabContactDrawer" :title="labContactFormTitle" :visible.sync="dialogLabContactFormVisible" size="60%">
            <el-form :model="labContactForm" :rules="contactRules" label-position="left" ref="labContactForm" label-width="200px" class="sgs-form">
                <el-form-item :label="$t('labContact.contactName')" prop="contactName">
                    <el-input maxlength="100" v-model="labContactForm.contactName" clearable autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item :label="$t('labContact.contactTel')" prop="contactTel">
                    <el-input maxlength="20"  v-model="labContactForm.contactTel" clearable autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item :label="$t('labContact.contactEmail')" prop="contactEmail">
                    <el-input maxlength="100"  v-model="labContactForm.contactEmail" clearable autocomplete="off"></el-input>
                </el-form-item>
             <!--   <el-form-item :label="$t('labContact.contactAddress')" prop="contactEmail">
                    <el-input v-model="labContactForm.contactAddress" autocomplete="off"></el-input>
                </el-form-item>-->
                <div class="sgs-bottom">
                    <el-button @click="closeLabContactDrawer" :disabled="isDisabled">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary"  @click="submitLabContactForm('labContactForm')" :disabled="isDisabled">{{$t('operation.confirm')}}</el-button>
                </div>
            </el-form>

        </el-drawer>

      <el-dialog :title="uploadTitle" width="50%" height="80%" :visible.sync="uploadDialog">
        <el-form ref="uploadForm"
                 label-width="200px"
                 label-position="left"
                 size="medium"
                 :model="uploadForm" class="sgs-form">

          <el-row>
            <el-form-item :label="$t('customerMaterialConfig.uploadFile')" prop="fileProp">
              <el-upload
                  ref="upload"
                  action=""
                  class="upload-demo"
                  :on-change="handleChange"
                  :on-exceed="handleExceed"
                  :on-remove="handleRemove"
                  :on-success="go"
                  :file-list="fileListUpload"
                  :limit="limitUpload"
                  accept=".xlsx,.xls"
                  :auto-upload="false">
                <el-button size="small" type="primary">{{$t('customerMaterialConfig.choseExcel')}}
                </el-button>
              </el-upload>
            </el-form-item>

          </el-row>
        </el-form>
        <template #footer>
                <span class="dialog-footer">
                   <el-button type="primary" size="small" :loading="btnGuestbookSubmit" @click="go">
                            {{$t('customerMaterialConfig.uploadExcel')}}
                        </el-button>
                </span>
        </template>
      </el-dialog>
    </basic-container>
</template>

<script>
    import {getList, remove, update, add,getLabTypeList,detail,getLabContactList,updateContact,detailContact,removeContact,deleteLab,queryCountryDatas,queryLocationDatas,getLabCertificateTypeList,uploadExcelData} from "@/api/lab/lab";
    import {mapGetters} from "vuex";
    import {getDictByCode, getCustomerGroup, getProductLine,getSgsCustomer,getCountryRegions} from "@/api/common/index";
    import {validatenull,objectIsNull} from "../../util/validate";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import resetButton from "@/components/resetButton/resetButton.vue";

    export default {
        components: { resetButton },
        created() {
          //加载Country、location数据
            this.quertCountryData();
            this.searchLabList();
           // this.queryCustomerGroupData();
            this.queryproductLineData();
            this.queryLabTypeSelData();
            this.queryLabCertificateTypeSelData();
        },

        data() {
            const validateBossNo = (rule, value, callback) => {
                if(!validatenull(value)){
                    callback(new Error(this.$t('register.selCompany')))
                }else{
                    callback()
                }
            }
            var validateLabCode = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('lab.selLabCode')));
              } else {
                callback();
              }
            }
            var validateLabName = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('lab.selLabName')));
              } else {
                callback();
              }
            }
            var validateLabAddress = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('lab.labAddressBlur')));
              } else {
                callback();
              }
            }

            var validateContactName = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('labContact.selContactName')));
              } else {
                callback();
              }
            }
            var validateContactTel = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('labContact.selContactTel')));
              } else {
                callback();
              }
            }
            var validateContactEmail = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('labContact.selContactEmail')));
              } else {
                callback();
              }
            }
            return {
                LanguageEnums:LanguageEnums,
                countryDatas:[],
                locationDatas:[],
                customerUrl: '/api/sgsapi/CustomerApi/preOrderCustomer/contactAddress/query',
                getRowKeys(row) {
                    return row.id
                },
                isThirdParty:false,
                currentRow:{},
                expands:[],
                dialogLabFormVisible:false,
                dialogLabContactFormVisible:false,
                isDisabled: false,
                fullscreenLoading: false,
                //formLabelWidth: '120px',
                labFormTitle:this.$t('operation.add'),
                labContactFormTitle:this.$t('operation.add'),
                query: {},
                sort: {descs:'update_time'},
                loading:false,
                contactloading:false,
                total: '',
                modalFlag: false,
                data: [],
                customerData:[],
                tableData:[],
                labTypeData:[],
                labCertificateTypeData:[],
                labContactData:[],
                productLineData: [],
                customerGroupData: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                contactForm:{
                    labId:'',
                },
                labForm:{
                    labName:'',
                    labAddress:'',
                    labCode:'',
                    labType:'',
                    labTypeName:'',
                    bossNo:'',
                    companyName:'',
                    customerGroupName:'',
                    productLineCode:'',
                    productLineName:'',
                    id:'',
                    countryCode:'',
                    countryName:'',
                    locationCode:'',
                    locationName:'',
                    labCertificateList: [],
                },
                labContactForm:{
                    contactName:'',
                    contactTel:'',
                    contactEmail:'',
                    labId:'',
                    labCode:'',
                    id:''
                },
                labId:'',
                rules: {
                    labName: [
                        { required: true, validator:validateLabName, trigger: 'blur' },
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    labCode: [
                        { required: true, validator:validateLabCode, trigger: 'blur' },
                    ],
                    labType: [
                        { required: true, message: this.$t('lab.selLabType') }
                    ],
                    bossNo:[
                        {trigger: 'change', required: false, message: this.$t('register.bossNoBlur')},
                    ],
                    labAddress: [
                        { required: true, validator:validateLabAddress }
                    ],
                },
                contactRules:{
                    contactName: [
                        { required: true, message: this.$t('labContact.selContactName'), trigger: 'blur' },
                        /*{ max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' },*/
                    ],
                    contactTel: [
                        { required: true, message: this.$t('labContact.selContactTel'), trigger: 'blur' },
                        /*{ max: 20, message: '电话长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    contactEmail: [
                        { required: true, message: this.$t('labContact.selContactEmail'), trigger: 'blur' },
                        /*{ max: 30, message: '邮箱长度不可大于30个字符', trigger: 'blur' }*/
                    ],
                },
                labCertificateOption: {
                  sel: null, //选中行
                  columns: [
                    {
                      prop: "certificateType",
                      label: "Certificate Type",

                    },
                    {
                      prop: "certificateLabId",
                      label: "Certificate Lab ID"
                    },
                    {
                      prop: "labContactId",
                      label: "Certificate Lab Contact"
                    },
                  ],
                  data: []
                },
              fileTemp: "",
              btnGuestbookSubmit: false,
              uploadDialog: false,
              uploadForm:{},
              uploadTitle:"",
              limitUpload: 1,
            }
        },
        computed: {
            ...mapGetters(["permission","language"]),
            permissionList() {
                return {
                    addLabBtn: this.vaildData(this.permission['sgs:lab:add'],false),
                    viewLabBtn: this.vaildData(this.permission['sgs:lab:view'],false),
                    delLabBtn: this.vaildData(this.permission['sgs:lab:delete'],false),
                    editLabBtn: this.vaildData(this.permission['sgs:lab:edit'],false),
                    addLabContactBtn: this.vaildData(this.permission['sgs:labContact:add'],false),
                    viewLabContactBtn: this.vaildData(this.permission['sgs:labContact:view'],false),
                    delLabContactBtn: this.vaildData(this.permission['sgs:labContact:delete'],false),
                    editLabContactBtn: this.vaildData(this.permission['sgs:labContact:edit'],false)
                };
            }
        },
        watch: {
            //监听语言变化
            language: function (newVal) {
                //触发查询
                this.queryLabTypeSelData();
                if (this.$refs['labForm'] != undefined) {
                  this.$refs['labForm'].fields.forEach(item => {
                    if (item.validateState === 'error') {
                      this.$refs['labForm'].validateField(item.labelFor)
                    }
                  })
                }
              if (this.$refs['labContactForm'] != undefined) {
                this.$refs['labContactForm'].fields.forEach(item => {
                  if (item.validateState === 'error') {
                    this.$refs['labContactForm'].validateField(item.labelFor)
                  }
                })
              }
            },
        },
        methods: {
          handleLocationSelect(val){
            this.labForm.locationName='';
            let obj = this.locationDatas.find((item) => {
              return item.locationCode === val;
            });
            if (!objectIsNull(obj)) {
              this.labForm.locationName=obj.locationName;
            }
          },
          handleCountrySelect(val) {
            this.locationDatas=[];
            this.labForm.locationName=null;
            this.labForm.locationCode=null;
            //切换国家重新清空Location
            let obj = this.countryDatas.find((item) => {
              return item.code === val;
            });
            if (!objectIsNull(obj)) {
              let countryName=obj.countryNameEn;
              if(this.language===LanguageEnums.CN.name){
                countryName=obj.countryNameCn;
              }
              this.labForm.countryName=countryName;
              //重新加载Location
              this.queryCountryLocation(obj.countryNameEn);
            }

          },
          queryCountryLocation(countryName) {
            queryLocationDatas(countryName).then(res => {
              debugger;
              let data = res.data.data;
              if (!objectIsNull(data)) {
                this.locationDatas = data;
              }
            }, error => {
              console.log('加载Country Location失败', error);
            });

          },
          quertCountryData() {
            queryCountryDatas().then(res => {
              debugger;
              let data = res.data.data;
              if (!objectIsNull(data)) {
                this.countryDatas = data;
              }
            }, error => {
              console.log('加载Country失败', error);
            });
          },
          querySearch(val) {
            if (val != '' && val != undefined && val.length >= 3) {
              getSgsCustomer({number: val, rows: 5}).then(res => {
                const results = [];
                this.customerData = res.data.rows;
                /* res.data.rows.forEach((currentValue, index) => {
                         results.push({
                             'value': currentValue.nameEN,
                             'bossNo': currentValue.number,
                             "companyName": currentValue.nameEN
                         });
                 });
                 callback(results);*/
              }, error => {
                console.log(error);
              });
            }
          },
          handleSelect(val) {
            debugger;
            let obj = {};
            obj = this.customerData.find((item) => {
              return item.number === val;
            });
            if (!validatenull(obj)) {
              this.labForm.bossNo = obj.number;
              this.labForm.companyName = obj.nameEN;
            } else {
              this.labForm.bossNo = '';
              this.labForm.companyName = '';
            }


          },
          closeLabDrawer(done) {
            this.$refs['labForm'].resetFields();
            this.dialogLabFormVisible = false;
          },
          closeLabContactDrawer(done) {
            this.$refs['labContactForm'].resetFields();
            this.dialogLabContactFormVisible = false;
          },
          selecProductLineCodeChange(val) {
            let obj = {};
            obj = this.productLineData.find((item) => {
              return item.productLineCode === val;
            });
            if (validatenull(obj)) {
              this.labForm.productLineName = '';
            } else {
              this.labForm.productLineName = obj.productLineName;
            }

          },
          customerGroupCodeChange(val) {
            let obj = {};
            obj = this.customerGroupData.find((item) => {
              return item.customerGroupCode === val;
            });
            this.labForm.customerGroupName = obj.customerGroupName;
          },
          receiveApplicant(data) {
            debugger;
            console.log(data);
            //回填客户组信息
            this.labForm.bossNo = data.number;
          },

          rowContactUpdate(row) {
            //请求lab对象数据
            detailContact(row.id).then(res => {
              const data = res.data.data;
              this.labContactForm = data;
              this.labId = row.id;
              this.labContactFormTitle = this.$t('lab.editContact');
              this.dialogLabContactFormVisible = true;
            }, error => {
              this.$message.error(this.$t('api.error'));
              console.log(error);
            });
          },
          submitLabContactForm() {
            this.isDisabled = true;
            //校验
            this.$refs['labContactForm'].validate((valid) => {
              if (valid) {
                updateContact(this.labContactForm).then(() => {
                  this.dialogLabContactFormVisible = false;
                  this.isDisabled = false;
                  //this.onLoad(this.page);
                  this.searchLabContactList(this.labContactForm.labId);
                  this.$message({
                    type: "success",
                    message: this.$t('api.success')
                  });
                }, error => {
                  this.isDisabled = false;
                  this.$message.error(this.$t('api.error'));
                  console.log(error);
                });
              } else {
                this.isDisabled = false;
                console.log('error submit!!');
                return false;
              }
            });

          },
          labContactChange(row, expandedRows) {
            this.$refs.labTable.setCurrentRow();
            this.currentRow = row
            if (this.expands.join(',').indexOf(row.id) === -1) {
              this.expands = [this.currentRow.id]
              this.searchLabContactList(row.id);
            } else {
              this.expands.splice(0, this.expands.length)
            }
            /* if(vlaue2.length!=0){//展开
                 this.searchLabContactList(value1.id);
             }*/
          },
          //请求实验室联系人数据
          searchLabContactList(id) {
            var params = {};
            this.contactForm.labId = id;
            this.contactloading = true;
            getLabContactList(Object.assign(params, this.contactForm)).then(res => {
              const data = res.data.data;
              const index = this.data.findIndex(data => data.id === id);
              if (data && data.length) {
                data.forEach(item => {
                  item.labId = id
                })
              }
              this.$set(this.data[index], 'tableData', data)
              this.tableData = data;
              this.contactloading = false;
            });
          },
          getLabContactSelect(id){
            var params = {};
            params.labId = id;
            getLabContactList(params).then(res => {
              const data = res.data.data;
              if (data && data.length) {
                data.forEach(item => {
                  item.labId = id
                })
              }
              this.labContactData = data;
            });
          },
          addLabContact(row) {
            this.labContactFormTitle = this.$t('lab.addContact');
            this.labContactForm.labId = row.id;
            this.labContactForm.labCode = row.labCode;
            this.labContactForm.id = '';
            this.labContactForm.contactEmail = '';
            this.labContactForm.contactName = '';
            this.labContactForm.contactTel = '';
            this.dialogLabContactFormVisible = true;
          },
          clear() {
            this.query = {};
            this.onLoad(this.page);
          },
          searchLabList() {
            this.page.currentPage = 1;
            this.onLoad(this.page);
          },
          onLoad(page) {
            this.fullscreenLoading = true;
            var params = {};
            getList(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
              this.loading = false;
              this.fullscreenLoading = false;
              const data = res.data.data;
              this.page.total = data.total;
              this.data = data.records;
            });
          },

          deleteLabContact(obj) {
            this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
              confirmButtonText: this.$t('submitText'),
              cancelButtonText: this.$t('cancelText'),
              type: 'warning'
            }).then(() => {
              removeContact(obj.id).then(res => {
                this.$message({
                  type: 'success',
                  message: this.$t('api.success')
                });
                console.log("labId", obj.labId);
                this.searchLabContactList(obj.labId);
              });
            }).catch(() => {
              /*this.$message({
                  type: 'info',
                  message: this.$t('api.error')
              });*/
            });

          },
          //删除实验室
          deleteLab(obj) {
            this.$confirm(this.$t('operation.confirmDelete'), this.$t('tip'), {
              confirmButtonText: this.$t('submitText'),
              cancelButtonText: this.$t('cancelText'),
              type: 'warning'
            }).then(() => {
              deleteLab(obj.id).then(res => {
                this.$message({
                  type: 'success',
                  message: this.$t('api.success')
                });
                this.onLoad(this.page);
              });

            }).catch(() => {
              /* this.$message({
                   type: 'info',
                   message: '已取消删除'
               });*/
            });

          },
          //分页查询
          sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
          },
          currentChange(pageCurrent) {
            this.page.currentPage = pageCurrent;
            this.onLoad(this.page);
          },
          selectLabTypeChange(val) {
            debugger;
            // this.isThirdParty=true;
            let obj = {};
            obj = this.labTypeData.find((item) => {
              return item.sysKey === val;
            });
            if (obj != undefined && obj != null) {
              this.labForm.labTypeName = obj.sysValue;
            }
          },
          addLab() {
            //清空表单内容
            this.labFormTitle = this.$t('operation.add');
            this.labForm.labType = '';
            this.labForm.labName = '';
            this.labForm.id = '';
            this.labForm.labAddress = '';
            this.labForm.labCode = '';
            this.labForm.bossNo = '';
            this.labForm.companyName = '';
            this.labForm.productLineName = '';
            this.labForm.productLineCode = '';
            this.labForm.countryCode = '';
            this.labForm.countryName = '';
            this.labForm.locationCode = '';
            this.labForm.locationName = '';
            this.dialogLabFormVisible = true;
            this.customerData = [];

            //初始化下拉
            this.queryLabCertificateTypeSelData();
          },
          //新增提交表单
          submitForm() {
            this.fullscreenLoading = true;
            this.isDisabled = true;
            this.$refs['labForm'].validate((valid) => {
              if (valid) {
                this.labForm.labCertificateList = this.labCertificateOption.data
                update(this.labForm).then(() => {
                  this.dialogLabFormVisible = false;
                  this.isDisabled = false;
                  this.fullscreenLoading = false;
                  this.onLoad(this.page);
                  this.$message({
                    type: "success",
                    message: this.$t('api.success')
                  });
                }, error => {
                  this.isDisabled = false;
                  this.fullscreenLoading = false;
                  // this.$message.error(this.$t('api.error'));
                  console.log(error);
                });
              } else {
                this.fullscreenLoading = false;
                this.isDisabled = false;
                console.log('error submit!!');
                return false;
              }
            });

          },
          //获取实验室类型下拉数据
          queryLabTypeSelData() {
            getLabTypeList(this.language).then(res => {
              const data = res.data;
              var newLabTypeData = [];
              //删除sgs数据
              if (data != null && data.length > 0 && data != undefined) {
                data.forEach((item, index, array) => {
                  if (item.sysKey != '1') {
                    newLabTypeData.push(item);
                  }
                })
              }
              this.labTypeData = newLabTypeData;
            });
          },
          // 编辑Lab
          rowUpdate(row) {
            var that = this;
            //请求lab对象数据
            detail(row.id).then(res => {
              const data = res.data.data;
              //根据Code 重新找到对应的lab City
              debugger;
              if(!objectIsNull(data) && !objectIsNull(data.countryCode)){
                let obj = this.countryDatas.find((item) => {
                  return item.code === data.countryCode;
                });
                if (!objectIsNull(obj)) {
                  //重新加载Location
                  this.queryCountryLocation(obj.countryNameEn);
                }
              }

              that.labForm = data;
              that.selectLabTypeChange(this.labForm.labType + '');
              that.labFormTitle = this.$t('operation.edit');
              that.dialogLabFormVisible = true;

              //初始化下拉
              if (!that.labForm.labCertificateList){
                that.labForm.labCertificateList = [];
              }
              that.labCertificateOption.data = that.labForm.labCertificateList
              this.queryLabCertificateTypeSelData();
              this.getLabContactSelect(row.id);
            }, error => {
              this.$message.error(this.$t('api.error'));
              console.log(error);
            });

          },
          queryCustomerGroupData() {
            getCustomerGroup().then(res => {
              const data = res.data.data;
              this.customerGroupData = data;
            });
          },
          queryproductLineData() {
            getProductLine().then(res => {
              const data = res.data.data;
              this.productLineData = data;
            });
          },
          deleteRow(index, rows) {
            //删除
            rows.splice(index, 1);
          },
          add() {
            if (this.labContactData.length == 0){
              this.$message({
                type: "warning",
                message: "Please add a lab contact first"
              });
              return;
            }
            let j = {
              labId:'',
              certificateType: "",
              certificateTypeLabel: '',
              certificateLabId: '',
              labContactId: '',
              labContact: {},
            };
            this.labCertificateOption.data.push(j);
            this.labCertificateOption.sel = j;
          },
          //获取实验室类型下拉数据
          queryLabCertificateTypeSelData() {
            /*getLabCertificateTypeList(this.language).then(res => {
              const data = res.data;
              var newLabCertificateTypeData = [];
              //删除sgs数据
              if (data != null && data.length > 0 && data != undefined) {
                data.forEach((item, index, array) => {
                  if (item.sysKey != '1') {
                    newLabCertificateTypeData.push(item);
                  }
                })
              }
              this.labCertificateTypeData = newLabCertificateTypeData;
            });*/
            this.labCertificateTypeData = [{sysValue:'CPSC',sysKey:'CPSC'}]
          },
          handlelabCertificateTypeSelect(val,row){
            let obj = this.labCertificateTypeData.find((item) => {
              return item.sysKey === val;
            });
            if (obj){
              row.certificateTypeLabel = obj.sysValue;
            }
          },
          handlelabContactSelect(val,row){
            let obj = this.labContactData.find((item) => {
              return item.id === val;
            });
            if (obj){
              row.labContact = obj;
            }
          },
          importLab(obj) {
            let _this = this;
            // 通过DOM取文件数据
            this.file = obj
            var rABS = false; //是否将文件读取为二进制字符串
            var f = this.file;
            var reader = new FileReader();
            //
            var formData = new FormData();
            formData.append("file", this.fileTemp);
            uploadExcelData(formData).then(res => {
              _this.uploadDialog = false;
              if (res.data.data.success === false) {
                _this.$message({
                  type: "error",
                  message: "Import Failed:" + res.data.data.msg
                });
              } else {
                _this.$message({
                  type: "success",
                  message: _this.$t('api.success')
                });
                _this.onLoad(_this.page);
              }
              _this.btnGuestbookSubmit = false;
            }).catch(error => {
              _this.btnGuestbookSubmit = false;
            })
          },
          openUpload() {
            this.uploadTitle = this.$t('customerMaterialConfig.openExcel');
            this.uploadForm = {};
            this.uploadDialog = true;
            this.$refs.upload.clearFiles();
          },
          handleChange(file, fileList) {
            this.fileTemp = file.raw
          },

          handleRemove(file, fileList) {
            this.fileTemp = null
          },
          go() {
            this.btnGuestbookSubmit = true;
            if (this.fileTemp) {
              this.importLab(this.fileTemp)
            } else {
              this.$message({
                type: 'warning',
                message: this.$t('customerMaterialConfig.materialFile')
              })
              this.btnGuestbookSubmit = false;
            }
          },
        },
    };
</script>

<style>
</style>
