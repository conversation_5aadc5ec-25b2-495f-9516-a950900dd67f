<template>
    <el-tooltip effect="dark" :content="resetTip" placement="top">
      <el-button
          size="medium"
          v-if="text"
          :style="{ marginLeft: marginLeft + 'px' }"
          @click="handleClick"
      >
          <i class="icon-all iconxiangpica1 reset-icon"></i>
          {{ $t("operation.reset") }}
      </el-button>
      <el-button
          size="medium"
          v-else
          :style="{ marginLeft: marginLeft + 'px' }"
          @click="handleClick"
      ><i class="icon-all iconxiangpica1 reset-icon"></i>
      </el-button>
    </el-tooltip>
</template>

<script>
export default {
    name: "resetButton",
    props: {
        marginLeft: {
            type: Number,
            required: false,
            default: 10,
        },
        text: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    computed: {
        resetTip() {
            return this.$t("operation.resetTip")
        },
    },
    methods: {
        handleClick() {
            this.$emit("click") // 触发自定义事件，将点击事件传递出去
        },
    },
}
</script>
<style scoped>
.reset-icon{
    font-size: 14px!important;
    color: #606266;
}
.el-button:hover .reset-icon{
    color: #ff6600;
}
</style>