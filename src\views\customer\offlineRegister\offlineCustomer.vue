<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.newCustomer')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.newCustomer')}}</h1>
        <el-card shadow="never" class="sgs-box" >
            <el-form :model="form"
                     ref="form"
                     :rules="rules"
                     label-width="200px"
                     label-position="top"
                     size="medium"
                     class="sgs-form"
                     @submit.native.prevent>
                <div class="sgs-group">
                    <h4 style="margin-bottom: 0;">{{$t('customer.title.base')}}</h4>
                </div>
                <el-row :gutter="24">
                    <div class="sgs-customer-style">
                        <sgs-customer
                            v-on:customer-data='receiveApplicant'
                            displayType="account"
                            :show-customer-group=true
                            :customer-url="customerUrl"
                            :default-customer="customerUnit">
                        </sgs-customer>
                        <div class="customer_button">
                            <el-button @click="updateCompany" type="primary">Update company</el-button>
                        </div>
                    </div>

                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customer.sgs.customerNo')">
                            <el-input v-model="form.bossNo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customerGroup.code')">
                            <el-input v-model="form.customerGroupCode"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col style="display: none" span="24">
                        <el-form-item :label="$t('customerGroup.name')">
                            <el-input v-model="form.customerGroupName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <div class="sgs-group">
                    <h4>{{$t('customer.title.admin')}}</h4>
                </div>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item :label="$t('register.loginAccount')" prop="account">
                            <el-input
                                    maxlength="200"
                                    clearable v-model="form.account"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.userName')" prop="userName">
                            <el-input
                                    maxlength="100"
                                    clearable v-model="form.userName" autoComplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item :label="$t('account.email')" prop="email">
                            <el-input
                                    maxlength="150"
                                    clearable v-model="form.email" :placeholder="$t('register.emailBlur')"
                                    :disabled="emailDisabled"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('register.phoneNumber')">
                            <el-input
                                    maxlength="11"
                                    clearable v-model="form.contactMobile"
                                    :disabled="contactMobileDisabled"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item :label="$t('language.default')">
                    <el-select v-model="form.language" :placeholder="$t('language.default')" style="width: 100%;">
                      <el-option  key="en-US" :label="$t('language.en')" value="en-US"> </el-option>
                      <el-option key="zh-CN" :label="$t('language.zh')" value="zh-CN"> </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
                <!--<el-row>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.password')" prop="password">
                            <el-input
                                    maxlength="50"
                                    clearable type="password" v-model="form.password"
                                    autocomplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('account.passwordConfirm')" prop="doublePassword">
                            <el-input
                                    maxlength="50"
                                    clearable type="password" v-model="form.doublePassword"
                                    auto-complete="new-password"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>-->
                <div class="sgs-group">
                    <h4>{{$t('customer.title.other')}}</h4>
                </div>
                <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item :label="$t('register.serviceUnit')"
                                    required
                                    :rules="[{ required: true, message: $t('account.validate.serviceUnitBlur'), trigger: 'change' }]"
                                    prop="selServiceUnits">
                        <el-select v-model="form.selServiceUnits"
                                   multiple
                                   collapse-tags
                                   :placeholder="$t('register.serviceUnitBlur')"
                                   @change="selectServiceTypeChange" style="width: 100%;">
                          <el-option v-for="(serviceUnit, index) in serviceUnits" :key="serviceUnit.serviceUnitCode" :label="serviceUnit.serviceUnitName"
                                     :value="serviceUnit.serviceUnitCode">
                          </el-option>
                        </el-select>
                      </el-form-item>

<!--                        <el-form-item :label="$t('register.serveType')" prop="productLineCode">
                            <el-select v-model="form.productLineCode" clearable
                                       :placeholder="$t('register.serveTypeBlur')"
                                       @change="selectServiceTypeChange"
                                       style="width: 100%;"

                            >
                                <el-option v-for="(serviceType,index) in serviceTypeData"
                                           :label="serviceType.serviceName"
                                           :value="serviceType.productLineCode"></el-option>
                            </el-select>
                        </el-form-item>-->
                    </el-col>
                </el-row>
                <el-col span="24">
                    <el-form-item :label="$t('common.other')">
                        <el-checkbox v-model="form.isBuyer">{{$t('customer.joinBuyerProgram')}}
                        </el-checkbox>
                        <el-checkbox v-model="form.relationshipNeedApprove">
                            {{$t('customer.customerRelationshipNeedApprove')}}
                        </el-checkbox>
                    </el-form-item>
                </el-col>
                <!--<el-row :gutter="20">
                    <el-col span="12">
                        <el-form-item :label="$t('account.userName')">
                            <span>{{form.customer.account.userName}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col span="12">
                        <el-form-item :label="$t('account.email')">
                            <span>{{form.customer.account.email}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>-->

                <el-row :gutter="24">
                    <div class="sgs-bottom">
                        <el-button type="primary" @click="goBackList">{{$t('operation.goBack')}}</el-button>
                        <el-button v-if="submitBtnFlag" type="primary" @click="onSubmit()"
                                   :disabled="isdisabled">{{$t('operation.submit')}}
                        </el-button>
                        <!--  <el-button type="primary" v-if="permissionList.refuseBtn" @click="onrefuse()"
                                     :disabled="isdisabled">{{$t('common.reject')}}
                          </el-button>-->
                    </div>
                </el-row>
            </el-form>
        </el-card>
    </basic-container>
</template>

<script>
    import {
        addOfflineRegister,
        detail,
        checkAccount,
        validateAccount,
        updateCompanyNameAndAddress
    } from "@/api/customer/customerRegister";
    import {serviceTypeList} from "@/api/common/index";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {deepClone} from '@/util/util'
    import {mapGetters} from "vuex";
    import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";

    export default {
        components: {},
        created() {
            //获取路由参数
            this.getParams();
        },
        data() {
            var validateEmail = (rule, value, callback) => {
                if (!validatenull(value)) {
                    checkAccount({email: this.form.email}).then(res => {
                        //判断显示客户需要填写的信息
                        if (res.data.success === 1 || res.data.success === '1') {
                            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                                callback(new Error(this.$t('register.submitInfo')));
                            } else {
                                callback(new Error(this.$t('register.lockError')));
                            }
                        } else {
                            callback();
                        }
                    });
                }
            };
            //验证密码
            /*var validatePass2 = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error(this.$t('register.doublePasswordBlur')));
                } else if (value !== this.form.password) {
                    callback(new Error(this.$t('register.checkPassNo')));
                } else {
                    callback();
                }
            };*/
            return {
                serviceUnits:[],
                serviceTypeData: [],
                submitBtnFlag: true,
                customerId: '',
                customerUnit: {
                    bossNo: '',
                    customerGroupName: '',
                    customerGroupCode: '',
                    isBuyer: false,
                    relationshipNeedApprove: false,
                },
                isdisabled: false,
                form: {
                    bossNo: '',
                    customerGroupCode: '',
                    customerGroupName: '',
                    account: '',
                    userName: '',
                    email: '',
                    contactMobile: '',
                    // password: '',
                    // doublePassword: '',
                    isBuyer: false,
                    relationshipNeedApprove: false,
                    productLineCode: '',
                    language:'en-US',
                    productLineCodes:'',
                    serviceUnits:'',
                    serviceDomains:'',
                    selServiceUnits:[]
                },
                customerUrl: '/api/sgsapi/CustomerApi/preOrderCustomer/contactAddress/query',
                rules: {
                    account: [
                        {required: true, message: this.$t('register.loginAccountBlur'), trigger: 'blur'},
                    ],
                    email: [
                        {required: true, message: this.$t('register.emailBlur'), trigger: 'blur'},
                        {type: 'email', message: this.$t('register.emailRigthBlur'), trigger: ['blur', 'change']},
                        {validator: validateEmail, trigger: 'blur'}
                    ],
                    userName: [
                        {required: true, message: this.$t('register.accountBlur'), trigger: 'blur'},
                    ],
                    /*password: [
                        {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
                        {
                            pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
                            message: this.$t('register.passwordError')
                        }
                    ],
                    doublePassword: [
                        {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
                        {validator: validatePass2, trigger: 'blur'}
                    ],*/
                    productLineCode: [
                        {required: true, message: this.$t('register.serveTypeBlur'), trigger: 'change'},
                    ],
                }
            };
        },
        watch: {
            'language': function () {
                this.queryServiceType();
            }
        },
        computed: {
            ...mapGetters(["permission", "language"]),
            permissionList() {
                return {
                    /*  addBtn: this.vaildData(this.permission['sgs:template:add'], false),
                      addContactBtn: this.vaildData(this.permission['sgs:template:addContact'], false),*/
                };
            },
        },
        methods: {
          async queryServiceType() {
            /*serviceTypeList(this.language).then(res => {
              const data = res.data.data;
              this.serviceTypeData = data;
            });*/
            debugger;
            let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
            this.serviceUnits=serviceUnitsDatas;
            console.log(serviceUnitsDatas);
          },
          selectServiceTypeChange(values) {
            //将默认的设置为null
            this.selDefaultServiceUnit=null;
            let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, values,this.form.selServiceUnits);
            debugger;
            this.mergeFormServiceUnitData(serviceUnitObj);
          },
          mergeFormServiceUnitData(serviceUnitObj){
            if(!objectIsNull(serviceUnitObj)){
              this.form.serviceUnits= JSON.stringify(serviceUnitObj.serviceUnits);
              this.form.productLineCodes=JSON.stringify(serviceUnitObj.productLines);
              this.form.serviceDomains=JSON.stringify(serviceUnitObj.serviceDomains);
              //this.defaultServiceUnitDatas=serviceUnitObj.selServiceUnitDatas;
            }
          },
           /* selectServiceTypeChange(val) {
                let obj = {};
                obj = this.serviceTypeData.find((item) => {
                    return item.productLineCode === val;
                });
            },*/
           /* queryServiceType() {
                serviceTypeList(this.language).then(res => {
                    const data = res.data.data;
                    this.serviceTypeData = data;
                });
            },*/
            //返回列表页
            goBackList() {
                this.$router.push('/customer/offlineRegister/list');
            },
            getParams() {
                // 取到路由带过来的参数
                const routerParams = this.$route.query.id
                // 将数据放在当前组件的数据内
                this.customerId = routerParams;
                debugger;
                if (!objectIsNull(routerParams)) {
                    //查询用户详情
                    this.submitBtnFlag = false;
                    this.initData(routerParams);
                }else{//创建
                    this.queryServiceType();
                }
            },
          async initData(){
            await  this.queryServiceType();
            //加载该公司的Service Unit
            await  this.queryCustomer(this.customerId);
          },
          async queryCustomer(id) {
              debugger;
                detail(id).then(res => {
                    console.log(this.customerUnit);
                    var data = res.data.data.customer;
                    this.form.customerNameCn = data.customerNameZh;
                    this.form.customerNameEn = data.customerNameEn;
                    this.form.customerAddressEn = data.customerAddressEn;
                    this.form.customerAddressZh = data.customerAddressZh;
                    this.form.qualification = data.qualification;
                    this.form.isBuyer = data.isBuyer == 1 ? true : false;
                    this.form.relationshipNeedApprove = data.relationshipNeedApprove == 1 ? true : false;
                     this.form.productLineCode = data.productLineCode;
                    this.form.email = data.account.email;
                    this.form.userName = data.account.userName;
                    // this.form.password = data.account.password;
                    // this.form.doublePassword = data.account.password;
                    this.form.contactMobile = data.account.mobile;
                    this.form.account = data.account.account;
                    this.form.language = data.account.language;
                  //处理ServiceUnit回显
                  debugger;
                  if (!objectIsNull(data.serviceUnits)){
                    let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits,data.serviceUnits);
                    this.$set(this.form, 'selServiceUnits', resultObj.serviceUnitDataItems);
                  }else{
                    this.$set(this.form, 'selServiceUnits', []);
                  }
                    var customerUnitObj = {};
                    customerUnitObj['bossNo'] = data.bossNo;
                    customerUnitObj['customerNameEn'] = data.customerNameEn;
                    customerUnitObj['customerNameCn'] = data.customerNameZh;
                    customerUnitObj['addressCn'] = data.customerAddressZh;
                    customerUnitObj['addressEn'] = data.customerAddressEn;
                    customerUnitObj['customerGroupId'] = data.customerGroupId;
                    customerUnitObj['customerGroupCode'] = data.customerGroupCode;
                    customerUnitObj['customerGroupName'] = data.customerGroupName;
                    customerUnitObj['number'] = data.bossNo;
                    this.customerUnit = customerUnitObj;
                });
            },
            async onSubmit() {
                this.isdisabled=true;
                //验证公司地址是否填入
                if (validatenull(this.form.customerAddressEn) && validatenull(this.form.customerAddressZh)) {
                    this.$message({
                        type: "error",
                        message: this.$t('register.addressBlur')
                    });
                     this.isdisabled=false;
                    return false;
                }
                //验证该账号是否在SGSmart中注册过
                if (!validatenull(this.form.account)) {
                    let validateRes = await validateAccount(this.form.account);
                    debugger;
                    console.log(validateRes);
                    if (!validateRes.data.data) {//该用户已注册
                        this.$message({
                            type: "error",
                            message: this.$t('register.registerError')
                        });
                         this.isdisabled=false;
                        return false;
                    }
                }

                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        const register = {};
                        register.customer = deepClone(this.form);
                        register.customer.customerNameZh = this.form.customerNameCn;
                        register.customer.customerNameEn = this.form.customerNameEn;
                        register.customer.customerAddressEn = this.form.customerAddressEn;
                        register.customer.customerAddressZh = this.form.customerAddressZh;
                        register.customer.qualification = this.form.qualification;
                        register.customer.isBuyer = this.form.isBuyer ? 1 : 0;
                        register.customer.relationshipNeedApprove = this.form.relationshipNeedApprove ? 1 : 0;
                        register.customer.productLineCode = this.form.productLineCode;
                        register.customer.account = {
                            email: this.form.email,
                            userName: this.form.userName,
                            //password: this.form.password,
                            mobile: this.form.contactMobile,
                            account: this.form.account,
                            language:  this.form.language,
                            registerMode: this.registerMode,
                            productLineCodes: this.form.productLineCodes,
                            serviceDomains: this.form.serviceDomains,
                            serviceUnits: this.form.serviceUnits
                        };
                        //加入loading效果
                        const loading = this.$loading({
                            lock: true,
                            text: this.$t('loading'),
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        addOfflineRegister(register).then(() => {
                            loading.close();
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.$router.push('/customer/offlineRegister/list?searchVal=' + this.form.customerNameCn);
                             this.isdisabled=false;
                        }, error => {
                             this.isdisabled=false;
                            loading.close();
                            console.log(error);
                        });

                    }else{
                         this.isdisabled=false;
                    }
                });
                console.log(this.form);
            },
            receiveApplicant(data) {
                console.log(data);
                //回填客户组信息
                this.form.customerNameCn = data.customerNameCn;
                this.form.customerNameEn = data.customerNameEn;
                this.form.customerAddressEn = data.addressEn;
                this.form.customerAddressZh = data.addressCn;
                this.form.customerGroupId = data.customerGroupId;
                this.form.customerGroupName = data.customerGroupName;
                this.form.customerGroupCode = data.customerGroupCode;
                this.form.bossNo = data.number;
                this.form.number = data.number;
                console.log(this.form);
            },
            updateCompany(){
                let {customerNameCn,customerNameEn,customerAddressEn,customerAddressZh} = this.form;
                let params = {
                    id:this.customerId,
                    customerNameZh:customerNameCn,
                    customerNameEn,
                    customerAddressEn,
                    customerAddressZh
                }
                updateCompanyNameAndAddress(params).then(res=>{
                    if(res.status==200){
                        this.$notify.success(this.$t('api.success'));
                        window.location.reload();
                    }
                }).catch(err=>{
                    this.$notify.error(this.$t('api.error'))
                    console.log("更新异常",err)
                })
            },
        }
    };
</script>

<style lang="scss" scoped>
h4 {
    margin-bottom: 20px;
}
.sgs-customer-style {
    padding: 0 13px;
    /deep/ .col-md-6 {
        &:last-of-type {
            > h4 {
                margin: 0;
                height: 10px;
            }
        }
    }
    /deep/ .form-horizontal {
        .form-group {
            border: 0 !important;
            .control-label {
                font-weight: normal;
                color: #626066;
                font-size: 14px;
                width: 100%;
                text-align: left;
            }
            .col-sm-7 {
                width: 98%;
            }
        }
        .form-control {
            border-width: 0;
            border-bottom: 1px solid #1b1b1b;
            border-radius: 0;
            box-shadow: none;
        }
        .input-group-btn {
            border-bottom: 1px solid #1b1b1b;
        }
        .input-group-btn:last-child > .btn {
            border-radius: 0;
            background: transparent;
            color: #1b1b1b;
            border: 0;
            &:hover {
                color: #f60;
            }
        }
    }
    .customer_button{
        position: absolute;
        bottom: 30px;
        right: 15px;
    }
}
</style>
