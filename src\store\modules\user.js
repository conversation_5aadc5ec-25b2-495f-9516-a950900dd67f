import { setToken, removeToken, encryptor } from '@/util/auth'
import { setStore, getStore } from '@/util/store'
import { isURL, validatenull } from '@/util/validate'
import { deepClone, setPageTitle } from '@/util/util'
import webiste from '@/config/website'
import { loginByUsername, validateToken, getUserInfo, getMenu, getTopMenu, logout, refeshToken, getButtons,routes,loginEFiling } from '@/api/user'
import i18n from "@/lang";


function addPath(ele, first) {
    const menu = webiste.menu;
    const propsConfig = menu.props;
    const propsDefault = {
        label: propsConfig.label || 'name',
        path: propsConfig.path || 'path',
        icon: propsConfig.icon || 'icon',
        children: propsConfig.children || 'children'
    }
    const icon = ele[propsDefault.icon];
    ele[propsDefault.icon] = validatenull(icon) ? menu.iconDefault : icon;
    const isChild = ele[propsDefault.children] && ele[propsDefault.children].length !== 0;
    if (!isChild) ele[propsDefault.children] = [];
    if (!isChild && first && !isURL(ele[propsDefault.path])) {
        ele[propsDefault.path] = ele[propsDefault.path] + '/index'
    } else {
        ele[propsDefault.children].forEach(child => {
            addPath(child);
        })
    }

}
// 新增递归函数，用于将 path 和 name 存储到 menuMap 中
function buildMenuMap(menu, menuMap) {
    const propsDefault = {
        label: 'name',
        path: 'path',
        children: 'children'
    };
    menu.forEach(item => {
        if (item[propsDefault.path] && item[propsDefault.label]) {
            menuMap[item[propsDefault.path]] = item[propsDefault.label];
        }
        if (item[propsDefault.children] && item[propsDefault.children].length > 0) {
            buildMenuMap(item[propsDefault.children], menuMap, propsDefault);
        }
    });
}
const user = {
    state: {
        userInfo: getStore({ name: 'userInfo' }) || [],
        permission: getStore({ name: 'permission' }) || {},
        dimensions: getStore({ name: 'dimensions' }) || {},
        posts: getStore({ name: 'posts' }) || [],
        roles: [],
        menu: getStore({ name: 'menu' }) || [],
        menuAll: [],
        menuMap: {}, // 菜单映射，用于匹配path和name
        token: getStore({ name: 'token' }) || '',
        // 新手引导
        taskListDialog: null,
        taskType: '',
        addBuyer: false,
        addContact: false,
        setAddress: false,
        nextStep: -1,
    },
    actions: {
        //根据用户名登录
        LoginByUsername({ commit }, userInfo) {
            return new Promise((resolve, reject) => {
                let pass = encryptor(userInfo.password);
                let promission =[]
                loginByUsername(userInfo.tenantId, userInfo.username, pass, userInfo.type,userInfo.receiveMarketingCommunication).then(res => {
                    const data = res.data.data;
                    // data.guide = true   // 模拟数据，发布删除，true 表示需要展示新手引导
                    commit('SET_TASK_DIALOG', data.guide)
                    commit('SET_TOKEN', data.accessToken);
                    commit('SET_USERIFNO', data);
                    commit('DEL_ALL_TAG');
                    commit('CLEAR_LOCK');

                    promission.push(...data.permissions.split(','))
                    commit('SET_DIMENSIONS',data.dimensions);
                    commit('SET_POSTS',data.posts);
                    let {tcpp,userMgtId} = data;
                    commit('SET_PERMISSION', promission);
                    //保留uat自己的按钮权限，如果e-filing的按钮权限能正常给个全的总量
                    routes().then(res=>{
                        let data =res.data.data
                        promission.push(...data.buttons.map(item=>item.code))
                        commit('SET_PERMISSION', promission);
                    })
                    resolve({tcpp,userMgtId});
                    loginEFiling(data.accessToken).then(res => {console.log("Login E-Filing Success.")});
                }).catch(error => {
                  reject(error);
                })
            })
        },
        //根据用户名登录
        LoginByToken({ commit }, token) {
            return new Promise((resolve, reject) => {
                validateToken(token).then(res => {
                    const data = res.data.data;
                    commit('SET_TOKEN', data.accessToken);
                    commit('SET_USERIFNO', data);
                    commit('DEL_ALL_TAG');
                    commit('CLEAR_LOCK');
                    commit('SET_PERMISSION', data.permissions.split(','));
                    commit('SET_DIMENSIONS',data.dimensions);
                    commit('SET_POSTS',data.posts);
                    resolve();
                }).catch(error => {
                    reject(error);
                })
            })
        },
        GetButtons({ commit }) {
            return new Promise((resolve) => {
                getButtons().then(res => {
                    const data = res.data.data;
                    commit('SET_PERMISSION', data);
                    resolve();
                })
            })
        },
        //根据手机号登录
        LoginByPhone({ commit }, userInfo) {
            return new Promise((resolve) => {
                loginByUsername(userInfo.phone, userInfo.code).then(res => {
                    const data = res.data.data;
                    commit('SET_TOKEN', data);
                    commit('DEL_ALL_TAG');
                    commit('CLEAR_LOCK');
                    resolve();
                })
            })
        },
        GetUserInfo({ commit }) {
            return new Promise((resolve, reject) => {
                getUserInfo().then((res) => {
                    const data = res.data.data;
                    commit('SET_ROLES', data.roles);
                    resolve(data);
                }).catch(err => {
                    reject(err);
                })
            })
        },
        //刷新token
        RefeshToken({ state, commit }) {
            return new Promise((resolve, reject) => {
                refeshToken(state.refeshToken).then(res => {
                    const data = res.data.data;
                    commit('SET_TOKEN', data);
                    resolve(data);
                }).catch(error => {
                    reject(error)
                })
            })
        },
        // 登出
        LogOut({ commit }) {
            return new Promise((resolve, reject) => {
                logout().then(() => {
                    debugger;
                    commit('SET_TOKEN', '')
                    commit('SET_MENU', [])
                    commit('SET_ROLES', [])
                    commit('DEL_ALL_TAG');
                    commit('CLEAR_LOCK');
                    removeToken()
                    resolve()
                    localStorage.removeItem('guideTask')
                    localStorage.removeItem('AUTO_OFF')
                }).catch(error => {
                    reject(error)
                })
            })
        },
        //注销session
        FedLogOut({ commit }) {
            return new Promise(resolve => {
                commit('SET_TOKEN', '')
                commit('SET_MENU', [])
                commit('SET_ROLES', [])
                commit('DEL_ALL_TAG');
                commit('CLEAR_LOCK');
                removeToken()
                resolve()
            })
        },
        GetTopMenu() {
            return new Promise(resolve => {
                getTopMenu().then((res) => {
                    const data = res.data.data || []
                    resolve(data)
                })
            })
        },
        //获取系统菜单
        GetMenu({ commit, dispatch }, parentId) {
            return new Promise(resolve => {
                getMenu(parentId).then((res) => {
                    const data = res.data.data
                    let menu = deepClone(data);
                    menu.forEach(ele => {
                        addPath(ele, true);
                    })
                    commit('SET_MENU', menu)
                    // 构建 menuMap
                    const menuMap = {};
                    buildMenuMap(menu, menuMap);
                    commit('SET_MENU_MAP', menuMap);
                    const currentPath = document.location.hash.replace('#', '')
                    const menuName = menuMap[currentPath]
                    if(menuName){
                        const defaultTitle = i18n.t('title');
                        setTimeout(() => {
                            document.title = menuName + ' - ' + defaultTitle;
                        }, 100);
                    }
                  //  dispatch('GetButtons');
                    resolve(menu)
                })
            })
        },
    },
    mutations: {
        SET_TASK_DIALOG: (state, bool) => {
            state.taskListDialog = bool
        },
        SET_TASK_TYPE: (state, type) => {
            state.taskType = type
        },
        SET_GUIDE: (state, obj) => {
            state[obj.name] = obj.val
        },
        SET_NEXTSTEP: (state, num) => {
            state.nextStep = num
        },
        SET_TOKEN: (state, token) => {
            setToken(token)
            state.token = token;
            setStore({ name: 'token', content: state.token, type: 'session' })
        },
        SET_USERIFNO: (state, userInfo) => {
            state.userInfo = userInfo;
            setStore({ name: 'userInfo', content: state.userInfo })
        },
        SET_MENU: (state, menu) => {
            state.menu = menu
            setStore({ name: 'menu', content: state.menu, type: 'session' })
        },
        SET_MENU_ALL: (state, menuAll) => {
            state.menuAll = menuAll;
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles;
        },
        SET_PERMISSION: (state, permission) => {
            state.permission = {};
            permission.forEach(ele => {
                state.permission[ele] = true;
            });
            setStore({ name: 'permission', content: state.permission, type: 'session' })
        },
        SET_DIMENSIONS: (state, dimensions) => {
            state.dimensions = dimensions;
            setStore({ name: 'dimensions', content: state.dimensions, type: 'session' })
        },
        SET_POSTS: (state, posts) => {
            state.posts = posts;
            setStore({ name: 'posts', content: state.posts, type: 'session' })
        },
        SET_MENU_MAP: (state, menuMap) => {
            state.menuMap = menuMap;
        },
    }

}
export default user
