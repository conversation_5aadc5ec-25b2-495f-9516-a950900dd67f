<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_template_filterView" id="sgs_smart_customer_template_filterView">
            <filter-save v-if="loadFilter" ref="filterSave" :source-type="sourceType"></filter-save>
        </div>
    </basic-container>
</template>

<script>
    import FilterSave from "../../../components/es-table/TableHeaderFilterSave";
    import filterApi from "../../../components/es-table/api";
    export default {
        name: "filterView",
        inject:['loadSearchForm'],
        provide(){
            return{
                size:'medium',
                resetQueryForm: this.resetFilter,
                tableObj:{
                    getQueryModal:()=>{
                        return this.getParentQueryModal();
                    },
                    getColumns:()=>{ return []}
                },
                filterConfigObj:{
                    getFilterContentObj:()=>{return this.filterContentObj},
                    changeFilter:this.changeFilter,
                    addFilterConfig:this.addFilterConfig,
                    delFilterConfig:this.delFilterConfig,
                    updFilterConfig:this.updFilterConfig,
                    updateFilterDefault:this.updateFilterDefault,
                },
            }
        },
        data() {
            return {
                pageLoading: false,
                loadFilter:false,
                filterContentObj:{//从DB获取的filter保存
                    filterList:[],//所有的filter 用来后续切换filter
                    currentContent:{},//当前filter内容
                    currentConfig:{}, //是config ，仅仅是当前的页面UI的配置，不是完整的content
                    currentId:''
                },
            }
        },
        methods: {
            initPage() {
                //加载filter的保存结果
                this.loadFilter = false;
                filterApi.filterQuery(this.sourceType).then(res=>{
                    this.loadFilter = true;
                    let {data} = res.data;
                    if(!data || data.length==0){
                        return;
                    }
                    this.filterContentObj.filterList = data;
                    let defaultData = data.filter(da=>da.defaultFlag-0==1);
                    if(!defaultData || defaultData.length==0){
                        return;
                    }
                    //处理默认filter
                    defaultData = defaultData[0];
                    let {content,id} = defaultData;
                    let filterContent = JSON.parse(content);
                    this.filterContentObj.currentConfig = defaultData;
                    this.filterContentObj.currentContent = filterContent;
                    this.filterContentObj.currentId = id;
                    this.$emit('changeFilter',this.filterContentObj.currentContent.filter);
                }).catch(err=>{
                    this.loadFilter = true;
                    console.log("filterQuery err",err)
                })
            },
            getParentQueryModal(){
                return this.loadSearchForm();
            },
            resetFilter(){ //清空filter列表
                this.$set(this.filterContentObj,'currentContent',{});
                this.$set(this.filterContentObj,'currentConfig',{});
                this.$set(this.filterContentObj,'currentId','');
                this.$emit('resetFilter');
            },
            changeFilter(rowId){
                let changeFilter = this.filterContentObj.filterList.find(f=>f.id==rowId);
                let {content,id} = changeFilter;
                this.filterContentObj.currentConfig = changeFilter;
                this.filterContentObj.currentContent = content ? JSON.parse(content) : {};
                this.filterContentObj.currentId = id ;
                //通知list 进行查询
                this.$emit('changeFilter',this.filterContentObj.currentContent.filter);
            },
            addFilterConfig(config){
                this.filterContentObj.filterList.push(config);
                this.filterContentObj.currentConfig = config;
                this.filterContentObj.currentId = config.id;
            },
            delFilterConfig(id){
                this.filterContentObj.filterList = this.filterContentObj.filterList.filter(f=>f.id!=id);
                if(this.filterContentObj.currentId == id){
                    this.filterContentObj.currentConfig = {};
                    this.filterContentObj.currentId = '';
                }
            },
            updFilterConfig(config){
                let ind = -1;
                this.filterContentObj.filterList.forEach((f,index)=>{
                    if(f.id == config.id){
                        ind = index;
                    }
                })
                if(ind>-1){
                    this.filterContentObj.filterList.splice(ind,1,config);
                    this.filterContentObj.currentConfig = config;
                }
            },
            updateFilterDefault(id){
                this.filterContentObj.filterList.forEach(f=>{
                    f.defaultFlag = f.id == id?1:0;
                })
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            sourceType:{
                type:String,
                default:'MATERIAL'
            }
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {FilterSave}
    }
</script>

<style lang="scss">
    .sgs_smart_customer_template_filterView {
    }
</style>