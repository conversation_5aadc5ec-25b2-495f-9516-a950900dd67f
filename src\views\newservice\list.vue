<template>
    <basic-container>
        <el-row>
            <el-form :inline="true" :model="formInline" size="medium">
                <el-form-item>
                    <el-input v-model="query.textDisplay" clearable
                              :placeholder="$t('newService.textTitle')"></el-input>
                </el-form-item>
              <el-form-item>
                <el-select v-model="query.productLineCode" style="width:100%" @change="selecProductLineCodeChange" clearable filterable :placeholder="$t('productLine.name')" :no-data-text="$t('NoData')">
                  <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                             :value="productLine.productLineCode"></el-option>
                </el-select>
              </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                    <el-button type="primary" @click="addRow">{{$t('operation.add')}}</el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <el-row>
            <el-table
                    :data="tableData"
                    style="width: 100%"
                    size="medium">
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
                <el-table-column
                        fixed
                        prop="productLineName"
                        :label="$t('productLine.name')"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="textDisplay"
                        :label="$t('newService.textTitle')">
                </el-table-column>
                <el-table-column
                        prop="serviceUrl"
                        :label="$t('newService.url')">
                </el-table-column>
                 <el-table-column
                        prop="isHot"
                       :label="$t('wel1.hot')" :formatter="isHotFormatter">
                </el-table-column>
                <el-table-column
                        prop="releaseDate"
                        :label="$t('common.effectiveDate')"
                        width="150">
                </el-table-column>
                <el-table-column
                        prop="updateUser"
                        :label="$t('common.operator')"
                        width="120">
                </el-table-column>
                <el-table-column
                        prop="updateTime"
                        :label="$t('common.operationTime')"
                        width="120">
                </el-table-column>
                <el-table-column
                        prop="status"
                        :label="$t('common.status.title')"
                        width="80"
                        align="center">
                    <template slot-scope="scope">
                        <el-tooltip
                                :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                placement="top">
                            <el-switch
                                    v-model="scope.row.status"
                                    active-color="#ff6600"
                                    inactive-color="#c8c8c8"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @change="changeStatus(scope.row)">
                            </el-switch>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="pin"
                        label="Pin"
                        width="80"
                        align="center">
                    <template slot-scope="scope">
                        <el-tooltip
                                :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                placement="top">
                            <el-switch
                                    :disabled="scope.row.status-0!=1"
                                    v-model="scope.row.pin"
                                    active-color="#ff6600"
                                    inactive-color="#c8c8c8"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @change="changePin(scope.row)">
                            </el-switch>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                        :label="$t('operation.title')"
                        width="130"
                        align="center">
                    <template slot-scope="scope">
                        <el-button type="text" @click="detailRow(scope.row)" size="small" icon="el-icon-edit">
                            {{$t('operation.edit')}}
                        </el-button>
                        <el-button @click="removeRow(scope.row)" type="text" size="small" icon="el-icon-delete">
                            {{$t('operation.remove')}}
                        </el-button>
                    </template>
                </el-table-column>

            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-row>
        <el-drawer :title="title" :visible.sync="dialogFormVisible" size="60%">
            <el-form ref="form" :model="form" label-width="200px" label-position="left" size="medium" class="sgs-form">
                <el-form-item :label="$t('productLine.name')">
                    <el-select v-model="form.productLineCode" style="width:100%"
                               @change="selecProductLineCodeChange"
                               :placeholder="$t('operation.pleaseSelect')"
                               :no-data-text="$t('NoData')"
                               filterable clearable>
                        <el-option v-for="(productLine,index) in productLineData" :label="productLine.productLineName"
                                   :value="productLine.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
<!--                <el-form-item :label="$t('newService.textTitle')"
                              :rules="{ required: true, message: this.$t('newService.validate.textTitleBlur'), trigger: 'blur' }"
                              prop="textDisplay">
                    <el-input v-model="form.textDisplay"
                              maxlength="150"
                              clearable></el-input>
                </el-form-item>-->
<!--                <el-form-item :label="$t('newService.url')">
                    <el-input v-model="form.serviceUrl"
                              maxlength="200"
                              clearable></el-input>
                </el-form-item>-->
                <el-form-item :label="$t('common.effectiveDate')">
                    <el-col :span="11">
                        <el-date-picker type="date" :placeholder="$t('common.effectiveDate')" v-model="form.releaseDate"
                                        style="width: 100%;"></el-date-picker>
                    </el-col>
                    <el-col class="line" :span="1">-</el-col>
                    <el-col :span="11">
                        <el-date-picker :placeholder="$t('common.invalidDate')" v-model="form.untilDate"
                                        style="width: 100%;" :picker-options="selectedTime"></el-date-picker>
                    </el-col>
                </el-form-item>
                <el-form-item :label="$t('wel1.hot')" style="height: 35px">
                    <el-checkbox-group v-model="isHotFlag" style="padding: 4px;">
                        <el-checkbox name="type"></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-row>
                  <el-col :span="16">
                    <h5 class="top-title">{{$t('language.manage')}}</h5>
                  </el-col>
                  <el-col :span="8">
                    <div class="text-right">
                      <el-button type="primary" @click="addLanguage" size="small">{{$t('language.add')}}</el-button>
                    </div>
                  </el-col>
                </el-row>
<!--                <el-form-item :label="$t('training.Attachment')">
                    <el-upload ref="newServiceUpload"
                            class="upload-demo"
                            action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                            :on-remove="handleRemove"
                            :on-change="onChange"
                            :on-success="uploadSuccess"
                            :on-preview="handlePreview"
                            :file-list="fileList">
                        <el-button type="primary">{{$t('term.uploadSubmit')}}</el-button>
                    </el-upload>
                </el-form-item>-->
              <!--多语言Table-->
              <el-table
                  :data="newServiceLangDatas"
                  style="width: 100%">
                <el-table-column
                    prop="languageId"
                    :label="$t('language.name')"
                    width="180">
                  <template slot-scope="scope">
                    <span>{{ scope.row.languageId === LanguageEnums.EN.code?
                        language=== LanguageEnums.EN.name? LanguageEnums.EN.enLabel:LanguageEnums.EN.cnLabel
                        :  language=== LanguageEnums.CN.name? LanguageEnums.CN.cnLabel:LanguageEnums.CN.enLabel  }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                    prop="textDisplay"
                    :label="$t('newService.textTitle')">
                </el-table-column>
                <el-table-column
                    prop="serviceUrl"
                    :label="$t('newService.url')">
                </el-table-column>

                <el-table-column
                    prop="fileName"
                    :label="$t('attachment.title')"
                >
                </el-table-column>


                <el-table-column  :label="$t('language.default')"
                                  width="120">
                  <template slot-scope="scope">
                    <el-tooltip
                        :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')"
                        placement="top">
                      <el-switch
                          v-model="scope.row.isDefault"
                          active-color="#ff6600"
                          inactive-color="#c8c8c8"
                          :active-value="1"
                          :inactive-value="0"
                          @change="changeDefault(scope.row)">
                      </el-switch>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column
                    :label="$t('operation.title')"
                    width="120">
                  <template slot-scope="scope">
                    <el-button @click="handleLanguageEditClick(scope.row)" type="text">{{$t('operation.edit')}}</el-button>
                    <el-button @click="handleLanguageDeleteClick(scope.row)" type="text">
                      {{$t('operation.remove')}}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
                <div class="sgs-bottom">
                    <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">
                        {{$t('operation.submit')}}
                    </el-button>
                </div>
            </el-form>
          <!--多语言新增/编辑-->
          <el-dialog
              :title="$t('language.add')"
              :close-on-click-modal="false"
              :close-on-press-escape="false"
              :visible.sync="langaugeDialogVisible"
              width="600"
              append-to-body>
            <el-form ref="languageForm" :model="languageForm" label-width="200px" label-position="left"  size="medium" class="sgs-form" :rules="languageRules">

              <el-form-item :label="$t('language.curLanguage')" prop="languageId" v-if="langaugeDialogVisible">
                <el-select filterable clearable v-model="languageForm.languageId" style="width:100%"
                           :placeholder="$t('operation.pleaseSelect')"
                           :no-data-text="$t('NoData')" clearable>
                  <el-option v-for="(languageObj,index) in languageDatas" :label="language === 'zh-CN'? languageObj.cnLabel:languageObj.enLabel"
                             :value="languageObj.code"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('newService.textTitle')" v-if="langaugeDialogVisible"
                            :rules="{ required: true, message: this.$t('newService.validate.textTitleBlur'), trigger: 'blur' }"
                            prop="textDisplay">
                <el-input v-model="languageForm.textDisplay"
                          maxlength="150"
                          clearable></el-input>
              </el-form-item>
              <el-form-item :label="$t('newService.url')" v-if="langaugeDialogVisible">
                <el-input v-model="languageForm.serviceUrl"
                          maxlength="200"
                          clearable></el-input>
              </el-form-item>

              <el-form-item :label="$t('language.default')" v-if="langaugeDialogVisible">
                <el-tooltip
                    :content="languageForm.isDefault==1?$t('common.yes'):$t('common.no')"
                    placement="top">
                  <el-switch
                      v-model="languageForm.isDefault"
                      active-color="#ff6600"
                      inactive-color="#c8c8c8"
                      :active-value="1"
                      :inactive-value="0"
                  >
                  </el-switch>
                </el-tooltip>
              </el-form-item>
              <el-form-item :label="$t('training.Attachment')">
                <el-upload ref="newServiceUpload"
                           class="upload-demo"
                           action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                           :on-remove="handleRemove"
                           :on-change="onChange"
                           :on-success="uploadSuccess"
                           :on-preview="handlePreview"
                           :file-list="fileList">
                  <el-button type="primary">{{$t('term.uploadSubmit')}}</el-button>
                </el-upload>
              </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="langaugeDialogVisible=false">{{$t('operation.cancel')}}</el-button>
              <el-button type="primary" v-loading="uploadLoading" @click="submitLanguageForm('languageForm')" >
                {{$t('operation.submit')}}
              </el-button>
            </div>
          </el-dialog>
        </el-drawer>
    </basic-container>
</template>

<script>
    import {getList, add, remove, detail,changeServicePin} from "@/api/newservice/newService";
    import {getProductLine, getCloudFileURL} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import {validatenull,objectIsNull} from "@/util/validate";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {deepClone} from '@/util/util'
    export default {

        data() {
          var validateLanguage = (rule,value,callback) => {
            if(!value){
              callback(new Error(this.$t('language.validate.selLanguageBlur')))
            }else {
              callback();
            }
          }
            return {
                uploadLoading:false,
                removeLangIds:[],
                newServiceLangDatas:[],
                editFlag:false,
                LanguageEnums:LanguageEnums,
                langaugeDialogVisible:false,
                languageForm:{
                  id:'',
                  newId:'',
                  languageId:'',
                  textDisplay:'',
                  serviceUrl:'',
                  flyerName:'',
                  fileName:'',
                  filePath:'',
                  isDefault:0
                },
                isHotFlag: false,
                name: "list",
                title: '',
                dialogFormVisible: false,
                tableData: [],
                productLineData: [],
                btnGuestbookSubmit: false,
                fileList: [],
                form: {},
                query: {},
                sort: {descs: 'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                selectedTime: {
                    /* disabledDate(time) {
                         return time.getTime() < Date.now() - 8.64e7;
                     },*/
                    disabledDate: (time) => {
                        return time.getTime() < this.form.releaseDate;
                    },
                },
                languageDatas:[
                  LanguageEnums.EN,
                  LanguageEnums.CN,
                ],
                languageRules: {
                  languageId:[
                    { required: true, validator:validateLanguage, trigger: 'change' }
                  ]

                },
            }
        },
        watch: {
            isHotFlag: function (newVal) {
                this.form.isHot = 0;
                if (newVal) {
                    this.form.isHot = 1;
                }
            },
            //监听语言变化
            language: function (newVal) {
              this.onLoad(this.page);
            },
        },
        methods: {
          generateRandomId() {
            const uuid = require('uuid'); // 导入uuid库
            return uuid.v4(); // 使用uuid库生成随机ID并赋值给data中的变量
          },
          submitLanguageForm(form) {
            this.$refs[form].validate((valid) => {
              if(valid){
                debugger
                let attachmentList = [];
                if(!objectIsNull(this.languageForm)){
                  if(!objectIsNull(this.languageForm.filePath)){
                    attachmentList.push(this.languageForm.filePath);
                  }
                  /*this.newServiceLangDatas.forEach(newService=>{
                    if(!objectIsNull(newService.filePath)){
                      attachmentList.push(newService.filePath);
                    }
                  })*/
                }
                //必选选择URL或上传File
                if (validatenull(this.languageForm.serviceUrl) && validatenull(attachmentList)) {
                  this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('newService.validate.urlAndFileValidate'),
                    type: 'warning'
                  });
                  return false;
                }
                //判断附件和url只能二选一
                if (!validatenull(this.languageForm.serviceUrl) && !validatenull(attachmentList)) {
                  this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('newService.validate.urlAndFileError'),
                    type: 'warning'
                  });
                  return false;
                }

                let newData = deepClone(this.languageForm);
                if (!this.editFlag) {
                  newData.id=null;
                  newData.newId = this.generateRandomId();
                  //校验是否已存在相同语种
                  if (!validatenull(this.newServiceLangDatas)) {
                    let repeatLanguaugeData = this.newServiceLangDatas.filter(item => item.languageId === newData.languageId);
                    if (!objectIsNull(repeatLanguaugeData)) {//存在已有语种
                      this.$message({
                        message: this.$t('language.repeatLanguageMsg'),
                        type: 'warning'
                      });
                      return false;
                    }
                  }
                  this.setNewServiceData(newData);
                  this.newServiceLangDatas.push(newData);
                  this.langaugeDialogVisible = false;
                }else{//编辑 校验是否已存在相同语种
                  //校验
                  let r = this.checkLangauge(newData);
                  if (!r) {
                    return false;
                  }
                  let newGridDatas=[];
                  this.newServiceLangDatas.forEach(newServiceData => {
                    if (objectIsNull(newServiceData.id)) {
                      if (newServiceData.newId === newData.newId) {
                        newServiceData = newData;
                      }
                    } else {
                      if (newServiceData.id === newData.id) {
                        newServiceData = newData;
                      }
                    }
                    newGridDatas.push(newServiceData);
                  })
                  this.setNewServiceData(newData);
                  this.newServiceLangDatas = newGridDatas;
                  this.langaugeDialogVisible = false;
                }


              }else{
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('trf.trfValidateError'),
                  type: 'warning'
                });
                return false;
              }
            })
          },
          checkLangauge(newData) {
            //先根据ID判断，如果不存在iD的话，认为页面新增数据，再根据newId判断
            if(!objectIsNull(newData.id)){
              let repeatLanguaugeData = this.newServiceLangDatas.filter(item => item.id != newData.id);
              if(!objectIsNull(repeatLanguaugeData)){
                if(repeatLanguaugeData[0].languageId===newData.languageId){
                  this.$message({
                    message: this.$t('language.repeatLanguageMsg'),
                    type: 'warning'
                  });
                  return false;
                }
              }
            }else{
              let repeatLanguaugeData = this.newServiceLangDatas.filter(item => item.newId != newData.newId);
              if(!objectIsNull(repeatLanguaugeData)){
                if(repeatLanguaugeData[0].languageId===newData.languageId){
                  this.$message({
                    message: this.$t('language.repeatLanguageMsg'),
                    type: 'warning'
                  });
                  return false;
                }
              }
            }
            return true;
          },
          setNewServiceData(langForm){
            //赋值默认
            if(langForm.isDefault){
              this.$set(this.form, 'textDisplay', langForm.textDisplay);
              this.$set(this.form, 'serviceUrl', langForm.serviceUrl);
              this.$set(this.form, 'flyerName', langForm.flyerName);
              this.$set(this.form, 'fileName', langForm.fileName);
              this.$set(this.form, 'filePath', langForm.filePath);
            }
            //处理原数据的默认状态
            this.newServiceLangDatas.forEach(newServiceLangData => {
              debugger;
              if (objectIsNull(langForm.id)) {
                if (newServiceLangData.newId != langForm.newId && langForm.isDefault===1) {
                  newServiceLangData.isDefault=0;
                }
              } else {
                if (newServiceLangData.id != langForm.id && langForm.isDefault===1) {
                  newServiceLangData.isDefault=0;
                }
              }
            })
          },
          changeDefault(row){
            if(objectIsNull(row.id)){//用NewId
              this.newServiceLangDatas.filter(item => item.newId != row.newId).forEach(obj=>{
                if(row.isDefault===1){
                  obj.isDefault=0
                }
              });
            }else{
              this.newServiceLangDatas.filter(item => item.id != row.id).forEach(obj=>{
                if(row.isDefault===1){
                  obj.isDefault=0
                }
              });
            }

            if(row.isDefault===1){
              this.$set(this.form, 'textDisplay', row.textDisplay);
              this.$set(this.form, 'serviceUrl', row.serviceUrl);
              this.$set(this.form, 'flyerName', row.flyerName);
              this.$set(this.form, 'fileName', row.fileName);
              this.$set(this.form, 'filePath', row.filePath);
            }else{
              this.$set(this.form, 'textDisplay', '');
              this.$set(this.form, 'serviceUrl', '');
              this.$set(this.form, 'flyerName', '');
              this.$set(this.form, 'fileName', '');
              this.$set(this.form, 'filePath', '');
            }
          },
          handleLanguageEditClick(row){
            this.editFlag=true;
            this.editData=row;
            this.languageForm=deepClone(row);
            this.$set(this.languageForm, 'editFlag', true)
            this.langaugeDialogVisible=true;
          },
          //删除语言
          handleLanguageDeleteClick(row){
            debugger;
            //判断当前行是否有id，有id的话将isDeleted置为1
            if(!objectIsNull(row.id)){//存在ID
              row.isDeleted=1;
              this.newServiceLangDatas=this.newServiceLangDatas.filter(item => item.isDeleted === 0);
              this.removeLangIds.push(row.id);
            }else{//新增删除 使用newId处理删除
              this.newServiceLangDatas=this.newServiceLangDatas.filter(item => item.newId != row.newId);
            }
          },
          //添加语言
          addLanguage() {
            this.editFlag = false;
            this.languageForm.textDisplay = '';
            this.languageForm.serviceUrl = '';
            this.languageForm.flyerName = '';
            this.languageForm.fileName = '';
            this.languageForm.filePath = '';
            this.languageForm.LanguageId = '';
            this.languageForm.isDefault = 1;
            this.languageForm.isDeleted=0;
            this.$set(this.languageForm, 'editFlag', false)
            this.langaugeDialogVisible = true;
            this.$nextTick(() => {
              this.$refs.newServiceUpload.clearFiles();
            })
          },
          isHotFormatter(row, column) {
            var reTest = row[column.property];
            var reTestStr = this.$t('common.no');
            if (reTest == 1) {
              reTestStr = this.$t('common.yes');
            }
            return reTestStr;
          },

          onSearch() {
            this.page.currentPage = 1;
            this.onLoad(this.page);
          },
          onLoad(page, params = {}) {
            //将当前语言放入请求中
            if(LanguageEnums.EN.name==this.language){
              this.query.languageId=LanguageEnums.EN.code;
            }else{
              this.query.languageId=LanguageEnums.CN.code;
            }
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
              this.tableData = res.data.data.records;
              this.page.total = res.data.data.total;
            });
          },
          currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page);
          },
          sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
          },
          validatelanguage() {
            //验证是否存在语言数据
            if (objectIsNull(this.newServiceLangDatas)) {
              this.$message({
                type: "warning",
                message: this.$t('language.validate.selLanguageBlur')
              });
              return false;
            }
            //验证是否有语言默认
           /* let repeatLanguaugeData = this.newServiceLangDatas.filter(item => item.isDefault === 1);
            if(objectIsNull(repeatLanguaugeData)){
              this.$message({
                type: "warning",
                message: this.$t('language.validate.setDefaultLanguage')
              });
              return false;
            }*/
            return true;
          },
          submitForm(form) {

            this.$refs[form].validate((valid) => {
              if (valid) {
                let b = this.validatelanguage();
                if (!b) {
                  return false;
                }

                this.form.newServiceLangDatas=this.newServiceLangDatas;
                this.btnGuestbookSubmit = true;
                add(this.form).then(res => {
                  this.$message({
                    type: "success",
                    message: this.$t('api.success')
                  });
                  this.btnGuestbookSubmit = false;
                  this.dialogFormVisible = false;
                  this.onLoad(this.page);
                }).catch(() => {
                  this.btnGuestbookSubmit = false;
                });
              } else {
                return false;
              }
            });
          },
          removeRow(row) {
            this.$confirm(this.$t('operation.confirmDelete'), {
              confirmButtonText: this.$t('operation.confirm'),
              cancelButtonText: this.$t('operation.cancel'),
              type: "warning"
            }).then(() => {
                  remove(row.id).then(() => {
                    this.$message({
                      type: "success",
                      message: this.$t('api.success')
                    });
                    this.onLoad(this.page);
                  });
            });

          },
          detailRow(row) {
            this.title = this.$t('newService.title.edit');
            detail(row.id).then(res => {
              //获取后台数据付给页面，并打开
              this.dialogFormVisible = true;
              this.form = res.data.data;
              this.newServiceLangDatas = this.form.newServiceLangDatas;
              this.removeLangIds=[];
              if (this.form.isHot == 1) {
                this.isHotFlag = true;
              } else {
                this.isHotFlag = false;
              }
              this.fileList = [];
              if (this.form.fileName !== '') {
                this.fileList.push({name: this.form.fileName});
              }

            });

          },
          selecProductLineCodeChange(val) {
            let obj = {};
            obj = this.productLineData.find((item) => {
              return item.productLineCode === val;
            });
            this.form.productLineName = obj.productLineName;
          },
          uploadSuccess(res, file, fileList) {
            debugger;
            this.uploadLoading=false;
            this.languageForm.flyerName = res.data[0].cloudID;
            this.languageForm.fileName = file.name;
            this.languageForm.filePath = res.data[0].path;

          },

          onChange(file, fileList) {
            debugger;
            // console.log(file)
            if (file.status == 'ready') {
              //开启loading效果
              this.uploadLoading = true;
            } else {
              this.uploadLoading = false;
            }
            if (fileList.length > 0) {
              this.fileList = [fileList[fileList.length - 1]]//这一步，是 展示最后一次选择文件
            }
          },


          handleRemove(file, fileList) {
            this.languageForm.flyerName = '';
            this.languageForm.fileName = '';
            this.languageForm.filePath = '';
          },
          handlePreview(file) {
            getCloudFileURL(this.languageForm.flyerName).then(res => {
              window.open(res.data, "_blank");
            });
          },
          addRow() {
            this.removeLangIds=[];
            this.title = this.$t('newService.title.add');
            this.form = {};
            this.isHotFlag=false;
            this.newServiceLangDatas=[];
            debugger;
            this.dialogFormVisible = true;
            // 清空未上传的文件
           /* this.$nextTick(() => {
              this.$refs.newServiceUpload.clearFiles();
            })*/

          },
          datacheck() {
            //alert(this.form.releaseDate);
          },
            changePin(row){
                let {id,pin} = row;
                changeServicePin({id,pin}).then(res=>{
                    if (res.data.success == 1) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onSearch();
                    } else {
                        this.$message({
                            type: "fail",
                            message: res.data.message || this.$t('api.error'),
                        });
                    }
                })

            },
          changeStatus(row) {
            const modifyForm = {};
            this.form = row;
            modifyForm.modifiedby = this.userInfo.account;
            modifyForm.id = this.form.id;
            modifyForm.status = this.form.status;
            add(modifyForm).then(res => {
              if (res.data.success == 1) {
                this.$message({
                  type: "success",
                  message: this.$t('api.success')
                });
                this.onSearch();
              } else {
                this.$message({
                  type: "fail",
                  message: res.data.message,
                });
              }
            });
          },
        },
        created() {
            this.removeLangIds=[];
            this.onLoad(this.page);
            getProductLine().then(res => {
                const data = res.data.data;
                this.productLineData = data;
            });
        },
        computed: {
            ...mapGetters([
                "userInfo",
                "language"
            ])
        },
    }
</script>

<style scoped>

</style>
