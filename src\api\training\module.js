import request from '@/router/axios';


export const addModule = (form) => {
    return request({
        url: '/api/sgs-training/course/insertModule',
        method: 'post',
        data: form
    })
}
export const showTable2 = (current, size,params) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selTrainingModuleList",
        changeOrigin: true,
        /*data: {
            "current":currentPage,
            "size":pageSize,
        }*/
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const handleDelModule = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/delTrainingModuleById",
        changeOrigin: true,
         params: {
            id,
        }
    })
}
export const sel_group = () => {
    return request({
        url: '/api/sgs-training/course/selGroup',
        method: 'post',
    })
}
export const selModuleById = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selModuleById",
        changeOrigin: true,
        data: id
    })
}
export const selModuleMaterialFileUrl = (id) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/selModuleMaterialFileUrl",
        changeOrigin: true,
        data: id
    })
}
/*export const updModuleById = (form) => {
    return request({
        method: "POST",
        url: "/api/sgs-training/course/updModuleById",
        changeOrigin: true,
        data: form
    })
}*/
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const approve = (id,approveStatus) => {
    return request({
        url: '/api/sgs-mart/customer/approve',
        method: 'get',
        params: {
            id,
            approveStatus
        }
    })
}