.main {
  padding: 0 16px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  min-height: 350px;

  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    h3 {
      color: #1b1b1b;
      font-weight: bold;
      text-rendering: optimizeLegibility;
      text-transform: uppercase;
    }
  }
  .documents_more_btn{
    position: absolute;
    z-index: 9;
    bottom: 5px;
    right: 20px;
  }
}

.page-example3 {
  max-height: 260px;
  overflow: hidden;

  ul.list {
    margin-top: 20px;
    list-style: none;
    padding: 0;
  }

  .ul-scoll {
    li {
      margin: 6px;
      padding: 5px;
      background: rgba(198, 142, 226, 0.4);
    }
  }
}

.new-tag {
  color: #fff;
  background: #f60;
  font-size: 12px;
  font-weight: 400;
  height: auto;
  line-height: normal;
  padding: 0px 4px;
  margin-top: 15px;
  margin-left: 12px;
}

.lib-a {
  font-size: 16px;
  font-family:  "Regular",<PERSON><PERSON>, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  font-weight: 400;
  line-height: 48px;
  cursor: pointer;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
