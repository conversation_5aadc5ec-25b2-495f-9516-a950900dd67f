import request from '@/router/axios';


const sendPost = (url,param)=>{
    return request({
        url,
        method:'post',
        data:param
    })
}
const sendGet = (url)=>{
    return request({
        url,
        method:'get',
        params:{}
    })
}
const sendPostFormData = (url,params)=>{
    return request({
        url,
        method:'post',
        headers:{
            'Content-Type':'multipart/form-data'
        },
        params
    });
}


const api = {
    checkAccount:(param)=>{
        return sendPost('/api/sgs-mart/register/account/checkAccount',param)
    },
    verificationCoded:(param)=>{
        return sendPost('/api/sgs-mart/register/account/verificationCode',param)
    },
    checkCode:(param)=>{
        return sendPost('/api/sgs-mart/register/account/checkCode',param)
    },

}
export default api;