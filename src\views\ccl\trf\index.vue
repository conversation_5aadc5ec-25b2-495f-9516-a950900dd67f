<template>
    <basic-container ref="homePage" :class="isTrfTab ?'trf-active':''" >

        <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.trfForm')}}</el-breadcrumb-item>
        </el-breadcrumb>

        <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane name="trfDetail" v-if="permissionList.cclTrfTab" :label="$t('navbar.trfForm')">
                <trf-detail
                        v-if="tabRefresh.trfDetail"
                        @trfId="getTrfId"
                        :trfNo.sync="trfNo"
                        :trfStatus.sync="trfStatus"
                        :trfFlag.sync="trfFlag"
                ></trf-detail>
            </el-tab-pane>
           <!-- <el-tab-pane name="testResult"  v-if="permissionList.testResultUnit && trfStatus==5" :label="$t('testResult.title')" lazy="true">
                <test-result
                        v-if="tabRefresh.testResult"
                        :trfNo="trfNo"  :testResultSaveFlag="permissionList.testResultSaveBtn">
                </test-result>
            </el-tab-pane>
            <el-tab-pane name="quotation" v-if="permissionList.quotationTab && trfStatus>=1" :label="$t('quotation.title')" lazy="true">
                <trf-quotation
                        v-if="tabRefresh.quotation"
                        approve.sync="false"
                        :trfId="trfId" :trfFlag="trfFlag">
                </trf-quotation>
            </el-tab-pane>-->
        </el-tabs>
    </basic-container>
</template>

<script>
    import {mapGetters} from "vuex";
     export default {
        name: "TestRequestForm",
        components: {
            TrfDetail: resolve => require(['./ccl_trfDetail'], resolve)
        },
        data(){
            return{
                isTrfTab:true,
                activeName:'trfDetail',
                trfId:null,
                trfNo:null,
                trfStatus:null,
                trfFlag:null,
                tabRefresh: {
                    trfDetail: true,
                    testResult: false,
                    quotation: false
                }
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    cclTrfTab:this.vaildData(this.permission['sgs:cclTrf:cclTrfTab'],false),
                };
            }
        },
        methods: {
            getTrfId(trfId){
                this.trfId=trfId;
            },
            handleClick(tab, event) {
                if('trfDetail'==tab.name){
                    this.isTrfTab = true;
                }else{
                    this.isTrfTab = false;
                }

                Object.keys(this.tabRefresh).forEach(item=>{
                    this.tabRefresh[item]=false;
                })
                this.tabRefresh[tab.name]=true;
            }
        }
    }
</script>

<style scoped lang="scss">

    .trf-active {
        margin-top: 45px;
        margin-bottom: 100px;
    }

</style>
