import request from '@/router/axios';

export const getCustomerList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/listForAflTemplateManagement',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const queryCompanyEmployeeInfoByUserCompanyId = (params) => {
    return request({
        url: '/api/sgs-mart/customer/queryCompanyEmployeeInfoByUserCompanyId',
        method: 'post',
        params:params,
    })
}

export const selectWithOutTrfTemplateData = (params) => {
    return request({
        url: '/api/sgs-mart/trfUserTemplate/selectWithOutTrfTemplateData',
        method: 'get',
        params:params,
    })
}
export const setDefaultTemplateByApplicationUser = (params) => {
    return request({
        url: '/api/sgs-mart/trfUserTemplate/setDefaultTemplateByApplicationUser',
        method: 'get',
        params:params,
    })
}
export const removeTrfTemplate = (params) => {
    return request({
        url: '/api/sgs-mart/trf/removeTrfTemplate',
        method: 'post',
        params:params,
    })
}