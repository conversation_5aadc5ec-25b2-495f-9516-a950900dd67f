<template>
    <basic-container>
        <el-dialog title="选择测试包" :visible.sync="visible" @close = "close" width="70%">
            <el-form :inline="true" :model="form" size="medium" label-position="left" label-width="200px;">
              <el-col :span="12">
                <el-form-item :label="$t('term.productLine')">
                    <el-select clearable filterable v-model="query.productLineCode" :disabled="productLineDisabled" style="width: 300px;">
                        <el-option v-for="item in productLineOptions" :key="item.id" :label="item.productLineName"
                                   :value="item.productLineCode"></el-option>
                    </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('term.customerGroup')">
                    <el-select clearable filterable v-model="query.customerGroupCode" :disabled="customerGroupDisabled" style="width: 300px;">
                        <el-option v-for="item in testPackageCustomerGroupData" :key="item.id" :label="item.customerGroupName"
                                   :value="item.customerGroupCode"></el-option>
                    </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('term.customer')">
                  <el-input clearable v-model="query.customerName" :placeholder="$t('testpackage.inputCustomerName')" style="width: 320px;"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('testpackage.name')">
                    <el-input clearable v-model="query.testPackageName" :placeholder="$t('testpackage.name')" style="width: 280px;"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchReset" size="small" v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.search')}}</el-button>
                </el-form-item>
              </el-col>
            </el-form>
            <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                    row-key="id"
                    lazy
                    :load="loadTree"
                    :element-loading-text="$t('loading')"
                    :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                <el-table-column label="#"
                                 type="index">
                </el-table-column>
                <el-table-column
                        prop="productLineName"
                        :label="$t('term.productLine')"
                        width="180">
                </el-table-column>
                <el-table-column
                        prop="customerGroupName"
                        :label="$t('term.customerGroup')"
                        width="180">
                </el-table-column>
              <el-table-column
                  prop="customerName"
                  :label="$t('term.customer')"
                  width="180">
              </el-table-column>
                <el-table-column
                        prop="testPackageName"
                        :label="$t('testpackage.name')">
                </el-table-column>
                <el-table-column
                        :label="$t('operation.title')"
                        width="180">
                    <template slot-scope="scope">
                        <el-button type="text"
                                   size="small"
                                   icon="el-icon-collection-tag"
                                   @click="handleRowClick(scope.row)">{{$t('operation.select')}}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-row style="margin: 5px 0px;">
                <el-pagination
                        class="pull-right clearfix"
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page.sync="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-dialog>
    </basic-container>
</template>


<script>
    import {add,getList,getPage,remove,getTestPackageTree} from "@/api/lab/test-package";
    import {getProductLine,getCustomerGroup,dictionaryFilter} from "@//api/common";
    import {mapGetters} from "vuex";

    export default {
        name: "test-package-dialog",
        props:['visible','testpackage','productLineCode','customerGroupCode','testPackageCustomerGroupData','customerName'],
        data() {
            return {
                productLineDisabled:false,
                customerGroupDisabled:false,
                name: "list",
                dialogFormVisible:false,
                selectParentDialogVisible: false,
                isDisabled: false,
                form:{
                    testPackageName:'',
                    productLineId:'',
                    parentId:''
                },
                query:{
                    testPackageName:'',
                },
                tableData:[],
                selectionList: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                productLineOptions: [],
                customerGroupOptions: [],
                loading: true,
                fullscreenLoading: false
            }
        },
        watch: {
            watch1: {
                handler: function(newval , oldval) {
                    if(newval.productLineCode!=null && newval.productLineCode!=''
                        && ((newval.customerGroupCode!=null && newval.customerGroupCode!='')
                            || (newval.customerName!=null && newval.customerName!=''))){
                        this.productLineDisabled=true;
                        Vue.set( this.query,"productLineCode",this.productLineCode);
                        //this.customerGroupDisabled=true;
                        Vue.set( this.query,"customerGroupCode",this.customerGroupCode);
                        Vue.set( this.query,"customerName",this.customerName);
                        this.onLoad(this.page);
                    }
                },
                deep: true
            }
            /*productLineCode:function(newVal){//监听是否传入productLineCode
                if(newVal!=''&& newVal!=null){

                    if(this.customerGroupCode!=null && this.customerGroupCode!=''){
                        Vue.set( this.query,"customerGroupCode",this.customerGroupCode);
                        this.onLoad(this.page);
                    }
                }
            },
            customerGroupCode:function(newVal){//监听是否传入productLineCode
                if(newVal!=''&& newVal!=null){

                }
            },*/
        },
        computed: {
            watch1() {
                const { productLineCode, customerGroupCode,customerName } = this
                return {
                    productLineCode,
                    customerGroupCode,
                    customerName
                }
            },
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['nkop:user:add'],false),
                    viewBtn: this.vaildData(this.permission['nkop:user:view'],false),
                    delBtn: this.vaildData(this.permission['nkop:user:delete'],false),
                    editBtn: this.vaildData(this.permission['nkop:user:edit'],false)
                };
            },
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            },
        },
        created() {
            this.onLoad(this.page);
            getProductLine().then(res => {
                this.productLineOptions = res.data.data;
            });
            getCustomerGroup().then(res => {
                this.customerGroupOptions = res.data.data;
            });
        },
        methods: {
            searchReset() {
                //this.query = {};
                this.onLoad(this.page);
            },
            searchChange(params,done) {
                this.query = params;
                this.onLoad(this.page, params);
                done();
            },
            selectionChange(list) {
                this.selectionList = list;
            },
            beforeOpen(done, type) {
                if (["edit", "view"].includes(type)) {
                    getTestPackage(this.form.id).then(res => {
                        this.form = res.data.data;
                    });
                }
                done();
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                params = {
                    parentId : -1,
                    productLineId:this.testpackage.productLineId,
                    customerGroupId:this.testpackage.customerGroupId,
                    customerName:this.customerName
                };
                // if((JSON.stringify(this.query) == "{}")){
                //     params = {parentId : -1}
                // }
                getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.loading = false;
                    this.fullscreenLoading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            loadTree(tree, treeNode, resolve){
                getPage(this.page.currentPage, this.page.pageSize, Object.assign({parentId:tree.id}, this.query)).then(res => {
                    this.loading = false;
                    this.fullscreenLoading = false;
                    resolve(res.data.data.records);
                });
            },
            handleRowClick(row, event, column){
                this.form.parentId = row.id;
                this.form.parentTestPackageName = row.testPackageName;
                this.$emit('selection',row);
            },
            close(){
                this.visible = false;
                this.$emit('update:visible',this.visible);
            }
        }
    };
</script>

<style>
</style>
