<template>
    <div>
        <h3>{{ form.customer.customerNameEn==''||form.customer.customerNameEn==null?form.customer.customerNameZh :form.customer.customerNameEn }}</h3>
        <div class="contact">
            <p><img src="/img/icon/phone.png" /> {{ form.customer.contactMobile }}</p>
            <p><img src="/img/icon/address.png" /> {{ form.customer.customerAddressZh }}</p>
        </div>

        <ul>
            <li>
                <p>{{ $t('customer.taxNo') }}</p>
                {{ form.customer.taxNo }}
            </li>
            <li>
                <p>{{ $t('customer.sgs.customerNo') }}</p>
                {{ form.customer.bossNo }}
            </li>
            <li>
                <p>{{ $t('customer.sgs.reportNo') }}</p>
                {{ form.customer.reportNo }}
            </li>
            <li>
                <p>{{ $t('customer.address') }}</p>
                {{ form.customer.customerAddressZh }}
            </li>
            <li>
                <p>{{ $t('customer.certificateNew') }}</p>
                <el-image
                    style="width: 100px;"
                    :src="qualificationUrl"
                    :preview-src-list="[qualificationUrl]">
                    <div slot="error" class="image-slot" style="font-size: 14px; color: #999;">
                        {{ $t('customer.certificateUpload') }}
                    </div>
                </el-image>
            </li>
        </ul>
    </div>
    
</template>

<script>
    import {detailForCurrentUser} from "@/api/customer/customerRegister";
    import {getCloudFileURL} from "@/api/common/index";
    import {add} from "@/api/customer/customer";
    import {validatenull} from "../../util/validate";

    export default {
        name: "detail",
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        data() {
            return {
                form: {
                    customer: {},
                },
                qualificationUrl: '',
            };
        },
        methods: {
            rowSave() {
                add(this.form.customer).then(() => {
                    this.onLoad();
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                }, error => {
                    console.log(error);
                });
            },
            onLoad() {
                this.customerId = this.customerId || 1;
                detailForCurrentUser(this.customerId).then(res => {
                    this.form = res.data.data;
                    if (!validatenull(this.form.customer.qualification.attachmentId)) {
                        getCloudFileURL(this.form.customer.qualification.attachmentId).then(res => {
                            this.qualificationUrl = res.data;
                        });
                    }

                });
            },
            uploadSuccess(res, file) {
                this.form.qualification = res.data[0].id;
            }
        },
        created() {
            this.onLoad();
        },
        watch: {
            customerId: function (data) {
                this.onLoad(data);
            },
        }
    }
</script>

<style lang="scss" scoped>
h3 { margin: 0; line-height: 30px; margin-bottom: 33px; }
.contact {
    p {
        img {
            vertical-align: text-bottom;
        }
    }
}
ul {
    list-style: none;
    margin-top: 56px;
    padding-left: 0;
    li {
        margin-bottom: 32px;
        font-size: 16px;
        font-weight: 400;
        color: #1B1B1B;
        line-height: 22px;
        p {
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            line-height: 20px;
            margin-bottom: 8px;
        }
    }
}
</style>
