<template>
    <div class="sgs_smart_customer_reportCertificate" id="sgs_smart_customer_reportCertificate" v-loading="pageLoading">
        <el-card class="sgs-box content-item">
            <el-collapse-item style="position: relative" title="Report" name="Report">
                <template slot="title">
                    <h4 class="sgs-title'">
                        Report
                    </h4>
                </template>
                <el-row>
                    <el-col :span="24" style="text-align: right">
                        <el-button type="text" v-if="editRule" @click="uploadFile(1)"><i class="el-icon-plus"></i> Add</el-button>
                    </el-col>
                </el-row>
                <section>
                    <el-table
                            fit
                            border
                            size="mini"
                            :data="dataList">
                        <el-table-column type="index" width="50">
                            <template slot="header">#</template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="documentName" label="Document Name">
                            <template slot-scope="{row}">
                                <el-button type="text" @click="downloadFile(row)" :disabled="false">
                                    {{row.documentName}}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="relNo" label="TRF Number">
                            <template slot-scope="{row}">
                                <a v-text="row.relNo" style="cursor: pointer;color: #f60" @click="jumpTRF(row)"></a>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="relStatus" label="TRF Status"></el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="conclusion" label="Overall Conclusion"></el-table-column>
                        <el-table-column width="250" prop="issueDate" label="Issue Date">
                            <template slot-scope="{row}">
                                <span v-if="!row.edit">{{row.issueDate}}</span>
                                <el-date-picker
                                        v-else
                                        v-model="row.issueDate"
                                        @change="changeRowIssueDate(row)"
                                        value-format="yyyy-MM-dd"
                                        type="date"
                                        placeholder="Please select date">
                                </el-date-picker>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="250" prop="expiryDate" label="Expiry Date">
                            <template slot-scope="{row}">
                                <span v-if="!row.edit">{{row.expiryDate}}</span>
                                <el-date-picker
                                        v-else
                                        v-model="row.expiryDate"
                                        value-format="yyyy-MM-dd"
                                        type="date"
                                        placeholder="Please select date">
                                </el-date-picker>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="reviewConclusion" label="Review Conclusion"></el-table-column>
                        <el-table-column min-width="250" prop="remark" label="Remark">
                            <template slot-scope="{row}">
                                <span v-if="!row.edit">{{row.remark}}</span>
                                <el-input v-else v-model="row.remark" placeholder="Remark" clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column width="130" prop="id" fixed="right" label="Action" align="center">
                            <template slot-scope="{row}">
                                <div v-if="row.objectType=='report' && row.testFrom!='customer'">
                                    <el-button
                                            v-if="!createNew"
                                            :disabled="false"
                                            :icon="getCommentIcon(row)"
                                            style="padding:2px 5px" type="text"
                                            @click="openComment(row.id,'report')"
                                    ></el-button>
                                </div>
                                <div v-if="row.relType!=3 && row.testFrom=='customer'">
                                    <el-button
                                            v-if="!createNew"
                                        :disabled="false"
                                        :icon="getCommentIcon(row)"
                                        style="padding:2px 5px" type="text"
                                        @click="openComment(row.id,'report')"
                                    ></el-button>
                                    <i v-if="showApproved && [0,1].includes(row.approvedStatus)"
                                    :disabled="false"
                                    class="el-icon-success approved_icon"
                                    :class="getApprovedClass(row.approvedStatus,'Approved')"
                                    style="color: #00d26a;"
                                    @click="approvedObj(row.id,'report',1,row.approvedStatus)">
                                    </i>
                                    <i v-if="showApproved && [0,2].includes(row.approvedStatus)"
                                    :disabled="false"
                                    class="el-icon-error approved_icon"
                                    :class="getApprovedClass(row.approvedStatus,'Reject')"
                                    style="color: #ff6600;"
                                    @click="approvedObj(row.id,'report',2,row.approvedStatus)"
                                    ></i>
                                    <i v-if="showApproved  && [1,2].includes(row.approvedStatus)"
                                    :disabled="false"
                                    class="el-icon-more approved_icon more_icon"
                                    circle
                                    @click="approvedObj(row.id,'report',0,row.approvedStatus)"
                                    ></i>
                                    <el-button icon="el-icon-edit"
                                            v-if="!row.edit && editRule"
                                            key="edit_btn"
                                            type="text"
                                            style="padding:2px 5px"
                                            @click="$set(row,'edit',true)"></el-button>
                                    <el-button icon="el-icon-finished"
                                            v-if="row.edit  && editRule"
                                            key="save_btn"
                                            type="text"
                                            style="padding:2px 5px"
                                            @click="saveRowData(row)"></el-button>
                                    <el-popconfirm
                                            v-if="editRule && row.testFrom=='customer'"
                                            confirm-button-text='Delete'
                                            cancel-button-text='Cancel'
                                            icon="el-icon-info"
                                            icon-color="red"
                                            title="Delete the data?"
                                            @confirm="deleteFile(row)"
                                    >
                                        <i slot="reference"  style="color:#ff6600;padding:2px 5px;cursor: pointer" class="el-icon-close"></i>
                                    </el-popconfirm>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </section>
            </el-collapse-item>
        </el-card>
        <el-card class="sgs-box content-item" v-if="!createNew">
            <el-collapse-item style="position: relative" title="Test Line" name="TestLine">
                <template slot="title">
                    <h4 class="sgs-title'">
                        Test Line
                    </h4>
                </template>
                <section >
                    <el-table
                            fit
                            border
                            size="small"
                            max-height="400"
                            :data="testLineDataList"
                            :disabled="false"
                    >
                        <el-table-column type="index" width="50">
                            <template slot="header">#</template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="260" label="Protocol/Package" prop="ppName">
                            <template #header>
                                <table-filter
                                    title="Protocol/Package">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-select :disabled="false" size="mini" style="width: 100%" v-model="testLineFiler['ppName']['condition']">
                                                <el-option label="In" value="in"></el-option>
                                                <el-option label="Not In" value="notIn"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-input :disabled="false" size="mini" clearable v-model="testLineFiler['ppName']['conditionValue']" placeholder="Protocol/Package"></el-input>
                                        </el-col>
                                    </el-row>
                                </table-filter>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="260" label="Test Name" prop="evaluationAlias">
                            <template #header>
                                <table-filter
                                    title="Test Name">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-select :disabled="false" size="mini" style="width: 100%" v-model="testLineFiler['evaluationAlias']['condition']">
                                                <el-option label="In" value="in"></el-option>
                                                <el-option label="Not In" value="notIn"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-input :disabled="false" size="mini" clearable v-model="testLineFiler['evaluationAlias']['conditionValue']" placeholder="Test Name"></el-input>
                                        </el-col>
                                    </el-row>
                                </table-filter>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip min-width="300" label="Test Result" prop="customerConclusion">
                            <template #header>
                                <table-filter
                                    title="Test Result">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-select :disabled="false" size="mini" style="width: 100%" v-model="testLineFiler['customerConclusion']['condition']">
                                                <el-option label="In" value="in"></el-option>
                                                <el-option label="Not In" value="notIn"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-select :disabled="false" size="mini" clearable filter multiple
                                            collapse-tags
                                            v-model="testLineFiler['customerConclusion']['conditionValue']"
                                            placeholder="Test Result">
                                            <el-option
                                            v-for="(tr,ind) in testResultSelectList"
                                            :key="'tr_'+ind"
                                            :label="tr.label"
                                            :value="tr.value"
                                            ></el-option>
                                        </el-select>
                                    </el-col>
                                </el-row>
                            </table-filter>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip width="250" label="Report Number" prop="trfReportNo">
                            <template #header>
                                <table-filter
                                    title="Report Number">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-select :disabled="false" size="mini" style="width: 100%" v-model="testLineFiler['trfReportNo']['condition']">
                                                <el-option label="In" value="in"></el-option>
                                                <el-option label="Not In" value="notIn"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-input :disabled="false" size="mini" clearable v-model="testLineFiler['trfReportNo']['conditionValue']" placeholder="Report Number"></el-input>
                                        </el-col>
                                    </el-row>
                                </table-filter>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip min-width="250" label="Remark" prop="remark">
                            <template #header>
                                <table-filter
                                    title="Remark">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-select :disabled="false" size="mini" style="width: 100%" v-model="testLineFiler['remark']['condition']">
                                                <el-option label="In" value="in"></el-option>
                                                <el-option label="Not In" value="notIn"></el-option>
                                            </el-select>
                                        </el-col>
                                        <el-col :span="16">
                                            <el-input :disabled="false" size="mini" clearable v-model="testLineFiler['remark']['conditionValue']" placeholder="Remark"></el-input>
                                        </el-col>
                                    </el-row>
                                </table-filter>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" width="130" label="Action" prop="id" align="center">
                            <template slot-scope="{row}">
                            <!-- <el-button
                                        :disabled="false"
                                        icon="el-icon-chat-square"
                                        style="padding:2px 5px"
                                        type="text"
                                        @click="openComment(row.id,'testLine')"
                                ></el-button>-->
                            </template>
                        </el-table-column>
                    </el-table>
                </section>
            </el-collapse-item>
        </el-card>
        <!--Certificate -->
        <el-card class="sgs-box content-item">
            <el-collapse-item style="position: relative" title="Certificate" name="Certificate">
                <template slot="title">
                    <h4 class="sgs-title'">
                        Certificate
                    </h4>
                </template>
                <el-row>
                    <el-col :span="24" style="text-align: right">
                        <el-button type="text" v-if="editRule" @click="uploadFile(2)"><i class="el-icon-plus"></i> Add</el-button>
                    </el-col>
                </el-row>
                <section>
                    <el-table
                            fit
                            border
                            size="mini"
                            :data="certificateDataList">
                        <el-table-column type="index" width="50">
                            <template slot="header">#</template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="documentName" label="Document Name">
                            <template slot-scope="{row}">
                                <el-button type="text" @click="downloadFile(row)" :disabled="false">
                                    {{row.documentName}}
                                </el-button>
                            </template>
                        </el-table-column>
                        <el-table-column show-overflow-tooltip width="200" prop="documentType" label="Document Type"></el-table-column>
                        <el-table-column width="250" prop="issueDate" label="Issue Date">
                            <template slot-scope="{row}">
                                <span v-if="!row.edit">{{row.issueDate}}</span>
                                <el-date-picker
                                        v-else
                                        v-model="row.issueDate"
                                        @change="changeRowIssueDate(row)"
                                        value-format="yyyy-MM-dd"
                                        type="date"
                                        placeholder="Please select date">
                                </el-date-picker>
                            </template>
                        </el-table-column>
                        <el-table-column width="250" prop="expiryDate" label="Expiry Date">
                            <template slot-scope="{row}">
                                <span v-if="!row.edit">{{row.expiryDate}}</span>
                                <el-date-picker
                                        v-else
                                        v-model="row.expiryDate"
                                        value-format="yyyy-MM-dd"
                                        type="date"
                                        placeholder="Please select date">
                                </el-date-picker>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="250" show-overflow-tooltip prop="remark" label="Remark">
                            <template slot-scope="{row}">
                                <span v-if="!row.edit">{{row.remark}}</span>
                                <el-input v-else v-model="row.remark" placeholder="Remark" clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column width="130" prop="id" fixed="right" label="Action" align="center">
                            <template slot-scope="{row}">
                                <div v-if="row.testFrom!='customer'">
                                    <el-button
                                            v-if="!createNew"
                                        :disabled="false"
                                        :icon="getCommentIcon(row)"
                                        style="padding:2px 5px"
                                        type="text"
                                        @click="openComment(row.id,'certificate')"
                                    ></el-button>
                                </div>
                                <div v-if="row.testFrom=='customer'">
                                    <el-button
                                            :disabled="false"
                                            v-if="!createNew"
                                            :icon="getCommentIcon(row)"
                                            style="padding:2px 5px"
                                            type="text"
                                            @click="openComment(row.id,'certificate')"
                                    ></el-button>
                                    <i v-if="showApproved && [0,1].includes(row.approvedStatus)"
                                    :disabled="false"
                                    class="el-icon-success approved_icon"
                                    :class="getApprovedClass(row.approvedStatus,'Approved')"
                                    style="color: #00d26a;"
                                    @click="approvedObj(row.id,'certificate',1,row.approvedStatus)"
                                    ></i>
                                    <i v-if="showApproved && [0,2].includes(row.approvedStatus)"
                                    :disabled="false"
                                    class="el-icon-error approved_icon"
                                    :class="getApprovedClass(row.approvedStatus,'Reject')"
                                    style="color: #ff6600;"
                                    @click="approvedObj(row.id,'certificate',2,row.approvedStatus)"
                                    ></i>
                                    <i v-if="showApproved && [1,2].includes(row.approvedStatus)"
                                    :disabled="false"
                                    class="el-icon-more approved_icon more_icon"
                                    @click="approvedObj(row.id,'certificate',0,row.approvedStatus)"></i>

                                    <el-button icon="el-icon-edit"
                                            v-if="!row.edit && editRule"
                                            key="edit_btn"
                                            type="text"
                                            style="padding:2px 5px"
                                            @click="$set(row,'edit',true)"></el-button>
                                    <el-button icon="el-icon-finished"
                                            v-if="row.edit && editRule"
                                            key="save_btn"
                                            type="text"
                                            style="padding:2px 5px"
                                            @click="saveRowData(row)"></el-button>
                                    <el-popconfirm
                                            v-if="editRule && row.testFrom=='customer'"
                                            confirm-button-text='Delete'
                                            cancel-button-text='Cancel'
                                            icon="el-icon-info"
                                            icon-color="red"
                                            title="Delete the data?"
                                            @confirm="deleteFile(row)"
                                    >
                                        <i slot="reference"  style="color:#ff6600;padding:2px 5px;cursor: pointer" class="el-icon-close"></i>
                                    </el-popconfirm>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </section>
            </el-collapse-item>
        </el-card>

        <reject-provided v-if="rejectShow"
                         :object-id="reportCertificateId"
                         :object-type="objectType"
                         @cancelDia="rejectShow=false"
                         @rejectSuccess="rejectSuccess"
        ></reject-provided>
        <chat-view
                v-if="showChat"
                :object-id="chat.objectId"
                :object-type="chat.objectType"
                @cancelDia="showChat=false"
        ></chat-view>
        <!-- 上传-->
        <sgs-batch-upload
                v-if="isLoadUpload"
                :title="batchUploadObj.uploadTitle"
                append-to-body
                :systemID="1"
                :limit="5"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :handle-upload-success="uploadSuccess"
                :handle-upload-error="uploadError"
                ref="batchUpload"
                upload-url="/api/sgsapi/FrameWorkApi/file/doUpload"
                :accept="batchUploadObj.accept"
                :attachment-type-options="batchUploadObj.attachmentTypeOptionsForUpload"
                :attachment-type-default-value="batchUploadObj.attachmentTypeDefaultValue"
                :file-max-sizes="20">
        </sgs-batch-upload>

    </div>
</template>

<script>
    import api from '../../../api/newSamples'
    import {queryDictionaryByLanguage} from "@/api/common";
    import {DictionaryEnums} from "@/commons/enums/DictionaryEnums";
    import moment from 'moment'
    import {mapGetters} from "vuex";
    import RejectProvided from "../materialAndProductCommon/rejectProvided";
    import ChatView from "../materialAndProductCommon/chatView";
    import tableFilter from "../../../components/tableFilter/tableFilter.vue";

    export default {
        name: "reportCertificate",
        data() {
            return {
                pageLoading:false,
                isLoadUpload:false,
                showChat:false,
                attachmentFileCodeMap:{},
                dataList:[],
                testLineFiler:{
                    ppName:{
                        condition:'in',
                        conditionValue:''
                    },
                    evaluationAlias:{
                        condition:'in',
                        conditionValue:''
                    },
                    customerConclusion:{
                        condition:'in',
                        conditionValue:[]
                    },
                    trfReportNo:{
                        condition:'in',
                        conditionValue:''
                    },
                    remark:{
                        condition:'in',
                        conditionValue:''
                    },
                },
                testLineDataList:[],
                testLineDataListOriginal:[],
                certificateDataList:[],
                reportCertificateId:'',
                objectType:'',
                rejectShow:false,
                batchUploadObj:{
                    type:'',
                    uploadTitle:'',
                    accept: '.txt,.ppt,.pptx,.xls,.xlsx,.doc,.docx,.png,.jpg,.jpeg,.pdf',
                    attachmentTypeOptionsForUpload:[],
                    attachmentTypeDefaultValue:''
                },
                chat:{
                    objectId:'',
                    objectType:''
                },
                testResultSelectList:[
                    {
                        "label": "Inconclusive",
                        "value": "INCONCLUSIVE"
                    },
                    {
                        "label": "NA",
                        "value": "NA"
                    },
                    {
                        "label": "Exempt",
                        "value": "EXEMPT"
                    },
                    {
                        "label": "Data Only",
                        "value": "DATA ONLY"
                    },
                    {
                        "label": "Fail",
                        "value": "FAIL"
                    },
                    {
                        "label": "Pass",
                        "value": "PASS"
                    },
                    {
                        "label": "See Result",
                        "value": "SEE RESULT"
                    }
                ],
            }
        },
        methods: {
            openComment(rowId,objectType){
                this.chat.objectId = rowId;
                this.chat.objectType = objectType;
                this.showChat = true;
            },
            getApprovedClass(approvedStatus,btnType){
                if(!approvedStatus || approvedStatus==0){
                    return 'approve_gary';
                }
                if(approvedStatus==1){
                    if(btnType=='Approved'){
                        return 'approve_green';
                    }
                    if(btnType=='Reject'){
                        return 'approve_gary';
                    }
                }
                if(approvedStatus==2){
                    if(btnType=='Approved'){
                        return 'approve_gary';
                    }
                    if(btnType=='Reject'){
                        return 'approve_red';
                    }
                }

            },
            handlerApproved(param,refreshPage=false){
                api.approved(param).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        let {data} = res.data;
                        if(data=='confirm'){
                            //需要弹窗确认，再次请求，然后刷新整个页面
                            this.$confirm('All info has been approved. Please confirm you would like to set the product status to "Approved"',
                                'Tips', {
                                    confirmButtonText: 'Confirm',
                                    cancelButtonText: 'Cancel',
                                    type: 'warning'
                                }).then(() => {
                                param['checkApproved'] = false;
                                this.handlerApproved(param,true);
                            }).catch(err=>{})
                            return;
                        }
                        this.$notify.success("Success")
                        if(refreshPage){
                            window.location.reload();
                            return;
                        }

                        this.loadData();
                    }else{
                        this.$notify.error("Fail")
                    }
                }).catch(err=>{
                    this.$notify.error("Fail")
                })
            },
            rejectSuccess(approvedType){
                let param ={
                    sampleId: this.objectId,
                    objectId : this.reportCertificateId,
                    approvedType,
                    approvedStatus:2
                }
                this.rejectShow = false;
                this.handlerApproved(param);
            },
            approvedObj(objectId,approvedType,approvedStatus,rowApprovedStatus){
                if(approvedStatus==rowApprovedStatus){
                    return
                }
                let tips = "";
                if(approvedStatus==0){
                    tips = "Return"
                }
                if(approvedStatus==1){
                    tips = "Approve"
                }
                if(approvedStatus==2){
                    tips = "Reject"
                }
                this.$confirm(tips+' the '+approvedType+'?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    if(approvedStatus==2){
                        this.reportCertificateId = objectId;
                        this.objectType = approvedType;
                        this.rejectShow = true;
                        return;
                    }
                    let param ={
                        sampleId: this.objectId,
                        objectId,
                        approvedType,
                        approvedStatus
                    }
                    this.handlerApproved(param);
                }).catch(err=>{})
            },
            changeRowIssueDate(row){
                let {issueDate} = row;
                let expiryDate = null;
                if(issueDate){
                    expiryDate = moment(issueDate).add(365,'days').format('YYYY-MM-DD');
                }
                this.$set(row,'expiryDate', expiryDate );
            },
            uploadError(){},
            uploadSuccess(data){
                //console.log("上传成功之后的data",data);
                if(data){
                    let attachmentType=data.attachmentType;
                    let documentList =[];
                    let fileList = (data.data || []).map(item =>{
                        const attachment = {
                            'object': this.batchUploadObj.type==1?'materialReport':'materialCertificate',
                            'objectId':this.objectId,
                            'attachmentId': item.id,
                            'fileName': item.attachmentName+"."+item.suffixes,
                            'fileUrl': item.path,
                            'fileType': attachmentType
                        }
                        documentList.push({
                            "objectType": this.batchUploadObj.type,
                            "documentType": attachmentType,
                            "attachmentId": null,
                            cloudId:item.path,
                            fileId:item.id,
                            "documentNo": "",
                            "documentName": item.attachmentName+"."+item.suffixes,
                        })
                        return attachment;
                    });
                    api.saveAttachment(fileList).then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            let attIdMap = {};
                            (res.data.data || []).forEach(da=>{
                                let {attachmentId,id} = da;
                                attIdMap[attachmentId] = id;
                            })
                            documentList.forEach(doc=>{
                                let id =  attIdMap[doc.fileId];
                                doc['attachmentId'] = id;
                            })
                            documentList = documentList.filter(d=>d.attachmentId);
                            let saveList = {
                                objectId:this.objectId,
                                testFrom: this.fileTestFrom,
                                documentList,
                                dataType:this.testFrom
                            }
                            api.saveReportInfo(saveList).then(res=>{
                                if(res.status==200 && res.data && res.data.code==200){
                                    this.loadData();
                                }else{
                                    this.$notify.error("Upload Fail");
                                }
                            }).catch(err=>{
                                    this.$notify.error("Upload Fail");
                            });
                        }
                    })
                }
                this.$refs.batchUpload.close();
            },
            jumpTRF(row){
                let hash = new Date().getTime() + '';
                window.open('/#/trf/trfDetail?id=' + row.relId + '&title=' + row.relNo + '&trfNo=' + row.relNo + '&hash=' + hash + '&actionType=detail' + '&signature=' + row.signature, '_blank');
            },
            downloadFile(row){
                let {cloudId} = row;
                api.downLoadFile(cloudId).then(res=>{
                    let downloadURL = res.data.data;
                    if(downloadURL){
                       window.open(downloadURL, '_blank')
                    }
                })
            },
            deleteFile(row){
                let {id,testId} = row;
                let testFromObjectId = this.objectId;
                let testFrom = this.fileTestFrom;
                let param ={id,testId,testFromObjectId,testFrom};
                api.deleteTestComplianceDocument(param).then(res=>{
                    if(res.status==200){
                        this.$notify.success("Success");
                        this.loadData();
                    }else{
                        this.$notify.error("Delete Fail");
                    }
                }).catch(err=>{
                    this.$notify.error("Delete Fail");
                })

            },
            showReportComment(){
                this.$notify.warning("Please wait for the functionality to be implemented.")
            },
            saveRowData(row){
                let {id,issueDate,expiryDate,remark,testId} = row;
                let testFromObjectId = this.objectId;
                let testFrom = this.fileTestFrom;
                let param ={id,issueDate,expiryDate,remark,testId,testFromObjectId,testFrom};
                api.modifyReportData(param).then(res=>{
                    if(res.status==200){
                        this.$notify.success("Success");
                    }else{
                        this.$notify.error("Update Fail");
                    }
                }).catch(err=>{
                    this.$notify.error("Update Fail");
                })
                this.$set(row,'edit',false);
            },
            getCommentIcon(rows){
                let {commentsNum,readStatus} = rows;
                //没有对话信息
                if(!commentsNum || commentsNum.length==0){
                    return 'el-icon-chat-square';
                }else {
                    return 'el-icon-s-comment';
                }
                //有对话信息，已读
                // if(readStatus-0==1){
                //     return 'el-icon-s-comment';
                // }
                //有对话信息，未读
                // return 'el-icon-warning';

            },
            loadData(){
                this.dataList = [];
                let param = {
                    id:this.objectId,
                    testFrom:this.testFrom
                }
                this.pageLoading = true;
                api.getMaterialRelationShip(param)
                    .then(res=>{
                        this.pageLoading = false;
                        //console.log("res",res)
                        if(res.status==200 && res.data && res.data.code==200){
                            let {data} = res.data;
                            let reportDataList = [];
                            let certificateDataList = [];
                            //接口组装页面table数据
                            (data || []).forEach(da=>{
                                let {documents,relationships,testFrom,testLines} = da;
                                if(testLines && testLines.length>0){
                                    testLines.forEach(tl=>{
                                        this.testLineDataList.push(tl);
                                        this.testLineDataListOriginal.push(tl);
                                    })
                                }
                                let trfList = (relationships || []).filter(rel=>rel.relType==3);
                                let trfMap = {};
                                let reportTrfInfo = [];
                                (trfList || []).forEach(trf=>{
                                    let {testId,relNo,signature,relId,relStatus,id,relType} = trf;
                                    trfMap[testId] = trf;
                                    reportTrfInfo.push({relNo,signature,relId,relStatus,testId,id,relType,testFrom})
                                });
                                //report维度
                                let reportFiles = (documents || []).filter(doc=>doc.objectType=='report');
                                //report data + trf
                                reportFiles.forEach(re=>{
                                    re['edit'] = false;
                                    re['testFrom']=testFrom;
                                    let {testId} = re;
                                    let trf =  trfMap[testId];
                                    if(!trf){
                                        re['relType'] = 1;
                                        return
                                    }
                                    let {relNo,relId,relStatus,relType} = trf;
                                    //这一步是排除掉重复的trf
                                    reportTrfInfo = reportTrfInfo.filter(r=>r.id!=trf.id);
                                    re = Object.assign(re,{relNo,relId,relStatus,testId,relType});
                                })
                                reportDataList = [...reportTrfInfo,...reportFiles,...reportDataList];
                                //certificate
                                let certificates = (documents || []).filter(doc=>doc.objectType!='report');
                                certificates.forEach(ce=>{ce['edit']=false;ce['testFrom']=testFrom});
                                certificateDataList = [...certificateDataList,...certificates];
                            })
                            reportDataList.sort((a,b)=>{return b.relType-a.relType});
                            this.dataList = reportDataList;
                            this.certificateDataList = certificateDataList;
                        }
                }).catch(err=>{
                    this.pageLoading = false;
                })
            },
            uploadFile(type){
                this.isLoadUpload = false;
                this.batchUploadObj.type = type==1?'report':'certificate';
                this.batchUploadObj.uploadTitle = type==1?'Upload Report File':'Upload Certificate File';
                this.$nextTick(()=>{
                    this.isLoadUpload = true;
                    this.$nextTick(()=>{
                        this.$refs.batchUpload.open();
                    })
                })
            },
            initUploadDictionary(){
                this.batchUploadObj.attachmentTypeOptionsForUpload = [];
                //根据当前语言查询附件类型字典项数据
                let langaugeCode = 'EN';
                if(this.language=='zh-CN'){
                    langaugeCode='CHI'
                }
                let params = {"sysKeyGroup":"OrderAttachmentType","systemID":1,"languageCode":langaugeCode}
                queryDictionaryByLanguage(params).then(res=>{
                    let  attachmentTypes = res.data;
                    attachmentTypes.forEach(item => {
                        let label = item.sysValue;
                        let value = item.sysKey;
                        const option = {
                            'label': label,
                            'value': value
                        }
                        this.attachmentFileCodeMap[value] = label;  // 建立key-value对，方便查询名称
                        this.batchUploadObj.attachmentTypeOptionsForUpload.push(option);
                    })
                    let values = DictionaryEnums.Attachment.CommentAttachmentType.items.map(i=>i.code);
                    let smartFileTyleList = this.batchUploadObj.attachmentTypeOptionsForUpload.filter(d=>values.includes(d.value));
                    this.batchUploadObj.attachmentTypeOptionsForUpload=smartFileTyleList;
                    this.batchUploadObj.attachmentTypeDefaultValue=this.batchUploadObj.attachmentTypeOptionsForUpload[0].value;
                });
            },
            testLinePageSearch(){
                if(!this.testLineDataListOriginal || this.testLineDataListOriginal.length==0){
                    return;
                }
                let props = Object.keys(this.testLineFiler);
                let afterFilterData = JSON.parse(JSON.stringify(this.testLineDataListOriginal));
                props.forEach(prop=>{
                    let {condition,conditionValue} = this.testLineFiler[prop];
                    if(conditionValue){
                        afterFilterData = afterFilterData.filter(d=> {
                            //select ，数组内容判断
                            if(prop=='customerConclusion'){
                                if(!conditionValue || conditionValue.length==0){
                                    return true;
                                }
                                if(condition=='in'){
                                    return (conditionValue || []).includes((d['conclusion'] || '').toUpperCase());
                                }
                                return !(conditionValue || []).includes((d['conclusion'] || '').toUpperCase());
                            }
                            //其他条件是input 字符判断
                            if(condition=='in'){
                                return (d[prop] || '').toUpperCase().indexOf(conditionValue.toUpperCase()) > -1;
                            }
                            return (d[prop] || '').toUpperCase().indexOf(conditionValue.toUpperCase()) == -1;
                        })
                    }
                })
                this.testLineDataList = afterFilterData;
            },
        },
        mounted() {
        },
        created() {
            //this.initUploadDictionary();
        },
        watch: {
            testLineFiler:{
                immediate:false,
                deep:true,
                handler(newV,oldV){
                    this.testLinePageSearch();
                }
            },
            objectId:{
                immediate:true,
                handler(newV,oldV){
                    if(newV){
                        this.loadData();
                    }
                }
            }
        },
        computed: {
            ...mapGetters(["language","userInfo"]),
        },
        props: {
            objectId:'',
            testFrom:'',
            fileTestFrom:'',
            editRule:true,
            createNew:false,
            showApproved:{
                type:Boolean,
                default(){
                    return false
                }
            },

        },
        updated() {
        },
        components: {ChatView, RejectProvided, tableFilter}
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";
    .sgs_smart_customer_reportCertificate {
        .add_new_btn{
            border: dotted 1px;
            padding: 2px 8px;
            border-radius: 5px;
        }
        h5 {
            font-size: $h3-font-size;
        }
        .el-input__inner {
            padding-left: 15px !important;
        }
    }
</style>