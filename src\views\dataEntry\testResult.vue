<template>
    <div>
        <div>
<!--            <iframe style="width: 100%; height: 100vh; border: 0;" :src="this.testResultUrl + userInfo.accessToken">
                <p>您的浏览器不支持  iframe 标签。</p>
            </iframe>-->

         <trf-list ref="trfList" @trfNoClick="handleTrfNoClick" :list-visible.sync="listVisible"></trf-list>
        </div>
    </div>
</template>

<script>
    import {inputResultUrl} from '@/config/env';
    import {mapGetters} from "vuex";
    import {getToken} from "@/util/auth"
    export default {
        name: "dataEntry",
        components: {
          TrfList: resolve => require(['@/views/dataEntry/list/TrfList'], resolve)
        },
        created() {
            // debugger;
             this.inputResultUrl = inputResultUrl;
            // //跳转Data Entry系统
            // //dev
            // debugger;
            // window.open(this.testResultUrl+this.userInfo.accessToken);
            // //路由跳转至TrfList页面
            // this.$router.push({path: '/trf/list', query: {}});
            // //uat
            // //window.open("https://uat.sgsmart-online.com/resultweb/#/?token="+this.userInfo.accessToken);
        },
        data() {
            return {
                listVisible:true,
                inputResultUrl:'',
            }
        },
        computed: {
            ...mapGetters(["userInfo","permission"]),
            permissionList() {
                return {
                };
            }
        },
        watch: {},
        methods: {
          handleTrfNoClick(row) {
              let hash = new Date().getTime() + '';
              window.open('/#/trf/trfDetail?clickTab=dataEntryResult&id=' + row.trf_trfId + '&title=' + row.trf_trfNo + '&trfNo=' + row.trf_trfNo + '&hash=' + hash + '&actionType=detail' + '&signature=' + row.signature, '_blank');
          }

        },
    };
</script>
