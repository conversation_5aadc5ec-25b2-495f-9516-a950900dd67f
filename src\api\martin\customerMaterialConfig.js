import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const queryDffDisplayData = (id,languageID) => {
  return request({
    url: '/api/sgs-mart/sgs-api/queryDffDisplayData',
    method: 'get',
    params: {
      id,languageID
    }
  })
}
export const add = (classParam) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/submit',
    method: 'post',
    data: classParam
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const getConfigList = (params) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/list/notPage',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const getListDff = (params) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/list-dff',
    method: 'post',
    data:params
  })
}
export const detail = (customerMaterialConfig) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/detail',
    method: 'post',
    data:customerMaterialConfig
  })
}
export const remove = (materialConfigId) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/remove',
    method: 'get',
    params: {
      materialConfigId,
    }
  })
}
export const changeStatus = (classParam) => {
  return request({
    url: '/api/sgs-mart/martin/customer-material-config/change-status',
    method: 'post',
    data: classParam
  })
}
