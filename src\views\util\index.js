/**
 * Created by jiachenpan on 16/11/18.
 */
import _ from 'lodash'
import {validatenull} from "../../util/validate";

var i18nVal;

export function parseTime(time, cFormat) {
    if (arguments.length === 0) {
        return null
    }
    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
        date = time
    } else {
        if (('' + time).length === 10) time = parseInt(time) * 1000
        date = new Date(time)
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
    }
    const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        if (key === 'a') return ['一', '二', '三', '四', '五', '六', '日'][value - 1]
        if (result.length > 0 && value < 10) {
            value = '0' + value
        }
        return value || 0
    })
    return time_str
}

export function formatTime(time, option) {
    time = +time * 1000
    const d = new Date(time)
    const now = Date.now()

    const diff = (now - d) / 1000

    if (diff < 30) {
        return '刚刚'
    } else if (diff < 3600) { // less 1 hour
        return Math.ceil(diff / 60) + '分钟前'
    } else if (diff < 3600 * 24) {
        return Math.ceil(diff / 3600) + '小时前'
    } else if (diff < 3600 * 24 * 2) {
        return '1天前'
    }
    if (option) {
        return parseTime(time, option)
    } else {
        return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
    }
}

// 格式化时间
export function getQueryObject(url) {
    url = url == null ? window.location.href : url
    const search = url.substring(url.lastIndexOf('?') + 1)
    const obj = {}
    const reg = /([^?&=]+)=([^?&=]*)/g
    search.replace(reg, (rs, $1, $2) => {
        const name = decodeURIComponent($1)
        let val = decodeURIComponent($2)
        val = String(val)
        obj[name] = val
        return rs
    })
    return obj
}

/**
 *get getByteLen
 * @param {Sting} val input value
 * @returns {number} output value
 */
export function getByteLen(val) {
    let len = 0
    for (let i = 0; i < val.length; i++) {
        if (val[i].match(/[^\x00-\xff]/ig) != null) {
            len += 1
        } else {
            len += 0.5
        }
    }
    return Math.floor(len)
}

export function cleanArray(actual) {
    const newArray = []
    for (let i = 0; i < actual.length; i++) {
        if (actual[i]) {
            newArray.push(actual[i])
        }
    }
    return newArray
}

export function param(json) {
    if (!json) return ''
    return cleanArray(Object.keys(json).map(key => {
        if (json[key] === undefined) return ''
        return encodeURIComponent(key) + '=' +
            encodeURIComponent(json[key])
    })).join('&')
}

export function param2Obj(url) {
    const search = url.split('?')[1]
    if (!search) {
        return {}
    }
    return JSON.parse('{"' + decodeURIComponent(search).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}')
}

export function html2Text(val) {
    const div = document.createElement('div')
    div.innerHTML = val
    return div.textContent || div.innerText
}

export function objectMerge(target, source) {
    /* Merges two  objects,
       giving the last one precedence */

    if (typeof target !== 'object') {
        target = {}
    }
    if (Array.isArray(source)) {
        return source.slice()
    }
    Object.keys(source).forEach((property) => {
        const sourceProperty = source[property]
        if (typeof sourceProperty === 'object') {
            target[property] = objectMerge(target[property], sourceProperty)
        } else {
            target[property] = sourceProperty
        }
    })
    return target
}

export function scrollTo(element, to, duration) {
    if (duration <= 0) return
    const difference = to - element.scrollTop
    const perTick = difference / duration * 10
    setTimeout(() => {
        console.log(new Date())
        element.scrollTop = element.scrollTop + perTick
        if (element.scrollTop === to) return
        scrollTo(element, to, duration - 10)
    }, 10)
}

export function toggleClass(element, className) {
    if (!element || !className) {
        return
    }
    let classString = element.className
    const nameIndex = classString.indexOf(className)
    if (nameIndex === -1) {
        classString += '' + className
    } else {
        classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length)
    }
    element.className = classString
}

export const pickerOptions = [
    {
        text: '今天',
        onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().toDateString())
            end.setTime(start.getTime())
            picker.$emit('pick', [start, end])
        }
    }, {
        text: '最近一周',
        onClick(picker) {
            const end = new Date(new Date().toDateString())
            const start = new Date()
            start.setTime(end.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
        }
    }, {
        text: '最近一个月',
        onClick(picker) {
            const end = new Date(new Date().toDateString())
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
        }
    }, {
        text: '最近三个月',
        onClick(picker) {
            const end = new Date(new Date().toDateString())
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
        }
    }]

export function getTime(type) {
    if (type === 'start') {
        return new Date().getTime() - 3600 * 1000 * 24 * 90
    } else {
        return new Date(new Date().toDateString())
    }
}

export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result

    const later = function () {
        // 据上一次触发时间间隔
        const last = +new Date() - timestamp

        // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
        if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last)
        } else {
            timeout = null
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
                result = func.apply(context, args)
                if (!timeout) context = args = null
            }
        }
    }

    return function (...args) {
        context = this
        timestamp = +new Date()
        const callNow = immediate && !timeout
        // 如果延时不存在，重新设定延时
        if (!timeout) timeout = setTimeout(later, wait)
        if (callNow) {
            result = func.apply(context, args)
            context = args = null
        }

        return result
    }
}

export function deepClone(source) {
    if (!source && typeof source !== 'object') {
        throw new Error('error arguments', 'shallowClone')
    }
    const targetObj = source.constructor === Array ? [] : {}
    Object.keys(source).forEach((keys) => {
        if (source[keys] && typeof source[keys] === 'object') {
            targetObj[keys] = source[keys].constructor === Array ? [] : {}
            targetObj[keys] = deepClone(source[keys])
        } else {
            targetObj[keys] = source[keys]
        }
    })
    return targetObj
}

export function getSelectedLegend(legend, $l) {
    const data = legend.data
    const selectObj = legend.selected
    if ($l.isEmpty(selectObj)) {
        return data
    }
    var selectedArray = []
    for (var i = 0; i < data.length; i++) {
        var n = data[i]
        if (!$l.has(selectObj, n)) {
            selectedArray.push(n)
            continue
        } else if (selectObj[n]) {
            selectedArray.push(n)
            continue
        }
    }
    return selectedArray
}

export function replaceArrayPassFail(selectedArray) {
    var findIndexPass = selectedArray.indexOf('PASS')
    var findIndexFail = selectedArray.indexOf('FAIL')
    if (findIndexPass !== -1) {
        selectedArray[findIndexPass] = 'Passed'
    }
    if (findIndexFail !== -1) {
        selectedArray[findIndexFail] = 'Failed'
    }
}


export const fileExport = function (data, filename, mime) {
    window.open(data)
}

export const fileExportData = function (data, filename, mime) {
    var blob = new Blob([data], {type: mime || 'application/octet-stream'})
    if (typeof window.navigator.msSaveOrOpenBlob !== 'undefined') {
        // IE workaround for "HTML7007: One or more blob URLs were
        // revoked by closing the blob for which they were created.
        // These URLs will no longer resolve as the data backing
        // the URL has been freed."
        window.navigator.msSaveOrOpenBlob(blob, filename)
    } else {
        var blobURL = window.URL.createObjectURL(blob)
        var tempLink = document.createElement('a')
        tempLink.style.display = 'none'
        tempLink.href = blobURL
        tempLink.setAttribute('download', filename)

        // Safari thinks _blank anchor are pop ups. We only want to set _blank
        // target if the browser does not support the HTML5 download attribute.
        // This allows you to download files in desktop safari if pop up blocking
        // is enabled.
        if (typeof tempLink.download === 'undefined') {
            tempLink.setAttribute('target', '_blank')
        }

        document.body.appendChild(tempLink)
        tempLink.click()
        document.body.removeChild(tempLink)
        window.URL.revokeObjectURL(blobURL)
    }
}


export const formatTip = function (params, ticket, callback) {
    var showValue = '<strong style="display:block;max-width:230px;white-space:normal;">' + params[0].name + '</strong><br/>'
    var total = 0
    // 求和
    params.forEach(function (item) {
        if (!validatenull(item.data.value)) {
            total += Number(item.data.value)
        } else {
            total += Number(item.data)
        }

    })
    // 计算百分比
    params.forEach(function (item, index) {
        var totalShow = ''
        if (index === 0) {
            // 添加上totaltest
            totalShow = i18nVal.tc('dataAnalytics.totalTest') + ' ' + total + ' <br />'
        }
        var num = 0;
        if (!validatenull(item.data.value)) {
            num = item.data.value
        } else {
            num = item.data;
        }
        var showColor = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>'
        let tem = ((num * 100 / total) || 0).toFixed(2)
        var precent = tem + '%'
        showValue += totalShow + showColor + item.seriesName + ':' + num + '(' + precent + ')<br />'
    })
    return showValue
}

export const initPerformanceOption = function (titleText, yData, yFailValue, yPassValue, ySeeValue, bWidth, bottomHeight, i18n) {
    i18nVal = i18n;
    const option = {
        dataZoom: [
            {
                type: 'slider',
                show: true,
                yAxisIndex: [0],
                left: '93%',
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#fff',
                    shadowBlur: 3,
                    shadowColor: 'rgba(0, 0, 0, 0.6)',
                    shadowOffsetX: 2,
                    shadowOffsetY: 2
                }
            },
            {
                type: 'inside',
                yAxisIndex: [0],
            }
        ],
        title: {
            text: titleText,
            x: 'center',
            y: 'top'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function (params, ticket, callback) {
                return formatTip(params, ticket, callback)
            },
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            orient: 'horizontal',
            show: true,
            x: 'right',
            y: 'top',
            data: [i18n.tc('dataAnalytics.pass'), i18n.tc('dataAnalytics.fail'), i18n.tc('dataAnalytics.seeResult')]
        },
        grid: {
            left: '2%',
            right: '10%',
            bottom: bottomHeight + '%',
            containLabel: true
        },
        xAxis: [
            {
                name: i18n.tc('dataAnalytics.noOfJob'),
                max: 'dataMax',
                nameLocation: 'middle',
                nameGap: '35',
                nameTextStyle: {
                    fontWeight: 'bold',
                    fontSize: 14
                },
                type: 'value'
            }
        ],
        yAxis: [
            {
                type: 'category',
                //inverse: true,//倒叙
                axisTick: {show: false},
                axisLabel: {
                    interval: 0,
                    rotate: 0,
                    margin: 2
                },
                data: yData
            }
        ],
        series: [
            {
                //realtimeSort: true,
                name: i18n.tc('dataAnalytics.pass'),
                type: 'bar',
                barWidth: bWidth, // 柱图宽度
                stack: 'total',
                itemStyle: {
                    normal: {
                        color: '#92D050'
                    }
                },
                data: yPassValue
            }, {
                //realtimeSort: true,
                name: i18n.tc('dataAnalytics.fail'),
                type: 'bar',
                barWidth: bWidth, // 柱图宽度
                stack: 'total',
                itemStyle: {
                    normal: {
                        color: '#FF0000'
                    }
                },
                data: yFailValue
            },
            {
                //realtimeSort: true,
                name: i18n.tc('dataAnalytics.seeResult'),
                type: 'bar',
                barWidth: bWidth, // 柱图宽度
                stack: 'total',
                itemStyle: {
                    normal: {
                        color: '#adadad'
                    }
                },
                data: ySeeValue
            }
        ],
       /* animationDuration: 0,
        animationDurationUpdate: 3000,
        animationEasing: 'linear',
        animationEasingUpdate: 'linear'*/
    }
    return option
}

/**
 * 设置层的id和高度
 * 初始化的时候id会自动添加 "_bar" 后缀，高度为像素
 */
export const countryDiv = function (id, height) {
    var o = {}
    o.id = id
    o.height = height
    return o
}
/**
 * 加载哪些国家需要层来显示
 */
export const initCountryDiv = function (countries) {
    var reHtml = ''
    countries.forEach(function (country) {
        reHtml += '<div class=\'row\'><div  class=\'col-lg-12\'> ' +
            '<div style=\'overflow-y:auto;margin-bottom: 20px;margin-top: 20px;\'>' +
            '<div id=\'' + country.id + 'Bar\' style=\'height:' + country.height + 'px;\' class=\'col-lg-12\'>' +
            '</div></div></div></div>'
    })
    return reHtml
}
export const initTestPerformanceOption = function (titleText, yData, yFailValue, yPassValue, bWidth, i18n) {
    return initPerformanceOption(titleText, yData, yFailValue, yPassValue, bWidth, 11, i18n)
}


export const packageData = function (labData, passData, failData, seeData = []) {
    let packageData = _.map(labData, (item, index) => {
        return {
            name: item,
            pass: passData[index],
            fail: failData[index],
            see: seeData[index],
            all: passData[index] + failData[index] + seeData[index]
        }
    })
    return _.orderBy(packageData, 'all', 'asc')
}

export const refreshChart = function (packageData, chartInstance, title, func) {
    let packageDatatemp = _.orderBy(packageData, 'fail', 'asc')
    var overAllOption = func(title, _.map(packageData, 'name'), _.map(packageData, 'fail'), _.map(packageData, 'pass'), 10)
    chartInstance.setOption(overAllOption)
}
