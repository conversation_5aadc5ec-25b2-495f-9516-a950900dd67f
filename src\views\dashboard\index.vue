<template>
  <div>
    <training-dashboard v-if="role.isTrainingAdmin"></training-dashboard>
    <class-list v-else-if="role.isTrainee"></class-list>
    <!--<customer-dashboard v-else-if="role.isBuyer || role.isSupplier || userInfo.productLineCode=='AFL' "></customer-dashboard>-->
    <frame-index v-else-if="role.isBuyer || role.isSupplier || userInfo.productLineCode=='AFL' " :layout="componentsList || []"></frame-index>
    <trf-list v-else-if="role.isKam || role.isSGS || role.isThirdPartyLab &&  !role.isCcl"></trf-list>
    <ccl-trf-list v-else-if="role.isCcl"></ccl-trf-list>
    <trf-list v-else></trf-list>
  </div>
</template>

<script>
    import {validatenull} from "@/util/validate";
    import {mapGetters} from "vuex";
    export default {
        name: "dashboard",
      props:{
        componentsList:[]
      },
        components: {
            CclTrfList: resolve => require(['../ccl/trf/list'], resolve),
            TrfList: resolve => require(['../trf/newList'], resolve),
            ClassList: resolve => require(['../training/classList'], resolve),
            CustomerDashboard: resolve => require(['./customerDashboard'], resolve),
            TrainingDashboard: resolve => require(['./trainingDashboard'], resolve),
            FrameIndex: resolve => require(['./FrameIndex'], resolve),
        },

        computed: {
            ...mapGetters([
                "userInfo", "dimensions", "posts"
            ]),
            role() {
              return {
                isBuyer: this.haseRole('UserRole', 'Buyer'),
                isSupplier: this.haseRole('UserRole', 'Supplier'),
                isTrainingAdmin: this.hasePost('TrainingAdmin'),
                isTrainee: this.haseRole('UserRole', 'Trainee'),
                isSGS: this.haseRole('SGSUserRole', 'SgsAdmin') || this.haseRole("SGSUserRole", "SgsLabUser"),
                isKam: this.haseRole('SGSUserRole', 'KAM'),
                isThirdPartyLab: this.haseRole('UserRole', 'ThirdPartyLab'),
                isCcl: this.haseCclPost('SGSUserRole', 'CCL'),
              };
            }
        },
        created(){
            console.log('角色：',this.role.isKam,this.role.isBuyer,this.role.isSupplier,this.role.isTrainingAdmin,this.role.isTrainee,this.role.isSGS);
        },
        methods: {
            haseRole(type, role) {
                debugger;
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            haseCclPost(post) {
                if (validatenull(post) || validatenull(post)) {
                    return false;
                }
                if (validatenull(this.posts)) {
                    return false;
                } else {
                    if (this.posts.includes("CCL Admin") || this.posts.includes("CCLUser")) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            hasePost(post) {
                if (validatenull(post) || validatenull(post)) {
                    return false;
                }
                if (validatenull(this.posts)) {
                    return false;
                } else {
                    if (this.posts.includes(post)) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
        }
    }
</script>

<style scoped>

</style>
