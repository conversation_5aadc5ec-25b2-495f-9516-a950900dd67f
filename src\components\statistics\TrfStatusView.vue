<template>
    <div class="main" v-loading="loading" id="status-list">
      <div class="title">
        <h3>{{ $t("wel1.requisitionStatus") }}</h3>
        <h3>
          <el-date-picker
              type="monthrange"
              align="right"
              class="time-select"
              unlink-panels
              range-separator="-"
              v-model="dateVal"
              value-format="yyyy-MM-dd"
              :start-placeholder="$t('datePicker.startTime')"
              :end-placeholder="$t('datePicker.endTime')"
              :picker-options="pickerOptions"
              @change="loadTrfStatData(handleDateChange($event))">
          </el-date-picker>
        </h3>
      </div>
      <ul>
        <li v-for="(item, index) in trfStatusList" :key="index">
            <router-link class="status-link" tag="a"
                        :to="{
                        path: '/trf/newList?from=0&trfStatus=' + item.trfStatus,
                        query: {
                          startDate: encodeURIComponent(params.startDate),
                          endDate: encodeURIComponent(params.endDate),
                          hash: new Date().getTime()
                        }}">
              <img :src="'/img/icon/status/' + item.trfStatus + '.png'" />
              <div class="status-info">
                <span class="status-name">{{ $t(item.lang) }}</span>
                <span :class="numberStyle">{{statusTrfCount[item.value]}}</span>
              </div>
            </router-link>
        </li>
      </ul>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {TrfStatusEnums} from "@/commons/enums/TrfEnums";
    import {dateTimeTool, dateFormatEnums, dateUnitEnums} from "@/util/dateTimeUtil";
    import {trfStatCount} from "@/api/welIndex";
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
    import {objectIsNull,validatenull} from "@/util/validate";
    import moment from 'moment'
    import {deepClone} from '@/util/util'

    export default {
        name: "TrfStatusView",
        data() {
            return {
                loading: true,
                dateVal: 'month',
                params: {
                  startDate: dateTimeTool.startDate,
                  endDate: dateTimeTool.endDate,
                },
                trfStatusList: TrfStatusEnums,
                numberStyle: 'large',
                statusTrfCount: {
                  draftTrf: 0,
                  trfSubmitted: 0,
                  jobPreparation:0,
                  quotationToBeConfirm: 0,
                  quotationConfirm: 0,
                  testingInProgress: 0,
                  reportCompleted: 0
                }
            }
        },
        computed: {
          ...mapGetters(["userInfo", "language"]),
          pickerOptions() {
            dateTimeTool.pickerOptions.shortcuts.forEach(item => {
              item.text = this.$t(item.code);
            });
            return dateTimeTool.pickerOptions;
          }
        },
        mounted() {
            this.dateVal = [this.params.startDate, this.params.endDate];
            let productLineCode = this.userInfo.productLineCode ==='all'?this.userInfo.defaultProductLineCode :this.userInfo.productLineCode;
            if(productLineCode != 'AFL') {
              this.trfStatusList = this.trfStatusList.filter(item => item.type != 'AFL');
            }
            else {
              this.numberStyle = 'normal';
            }
            this.loadTrfStatData(this.handleDateChange(this.dateVal));
        },
        methods: {
          tzToChina(val){
            if (!val) return ''
            let value = tzToChina(val);
            return value;
          },
          handleDateChange(e) {
            if(e === null) return;
            this.params.startDate = e[0];
            this.params.endDate = e[1];
            return this.params;
          },
          loadTrfStatData(val) {
            if (val === null || !dateTimeTool.dateSpanValid(val)) return;
            let params = deepClone(this.params)
            if(!objectIsNull(params.startDate)){
              params.startDate=this.tzToChina(moment(params.startDate).format("YYYY-MM-DD HH:mm:ss"))
            }
            if(!objectIsNull(params.endDate)){
              params.endDate=this.tzToChina(moment(params.endDate).format("YYYY-MM-DD HH:mm:ss"))
            }
            params.productLineCode=this.userInfo.productLineCode
            trfStatCount(params).then(res => {
              debugger
              let data = res.data.data
              if (data) {
                Object.keys(this.statusTrfCount).forEach(key => {
                  this.statusTrfCount[key] = data[key] ? data[key] : 0;
                });
              }
            }).finally(() => {
              this.loading = false;
            });
          },
        }
    }
</script>

<style scoped lang="scss">
    .main {
      padding: 0 16px;
      background-color: transparent;
      display: flex;
      flex-direction: column;

      ::v-deep .time-select {
        background: transparent;
        justify-content: center;
        align-items: center;
      }

      .title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        h3 {
          color: #1b1b1b;
          font-weight: bold;
          text-rendering: optimizeLegibility;
          text-transform: uppercase;
        }
      }

      ul {
        padding: 24px 0 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        list-style: none;
        li {
          position: relative;
          width: 100%;
          .status-link {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding-left: 24px;
            width: 100%;
          }

          .status-info {
            display: flex;
            flex-direction: column;
            margin: 0 0 0 32px;
            justify-content: space-between;
          }

          .status-info:last-child {
            color: #1B1B1B;
          }

          .status-name {
            font-size: 16px;
            font-weight: 400;
            color: #737373;
          }
        }
      }
      ul li:first-child .status-link {
        padding-left: 0;
      }

      ul li::before {
        content: '';
        position: absolute;
        border-left: 1px solid #ccc;
        height: 100%;
      }
      ul li:first-child::before {
        content: none;
      }
    }

    .large {
      font-size: 56px;
    }

    .normal {
      font-size: 40px;
    }

    .small {
      font-size: 32px;
    }

    @media screen and (max-width: 1024px) {
      //.main ul {
      //  //flex-wrap: wrap; /* 在小屏幕上换行显示 */
      //}
      //.main ul li {
      //  flex: 0 0 calc(50% - 10px); /* 适当调整每个项目的宽度 */
      //}
      .main ul {
        li {
          .status-name {
            font-size: 12px;
          }
        }
      }

      .large {
        font-size: 24px;
      }

      .normal {
        font-size: 20px;
      }

      .small {
        font-size: 16px;
      }
    }
</style>
