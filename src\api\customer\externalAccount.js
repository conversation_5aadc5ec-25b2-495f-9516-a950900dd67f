import request from '@/router/axios';

export const getUsers = (form) => {
    form['deleteFlag'] = 1;
    return request({
        url: '/api/sgsapi/UserManagementApi/conpanyEmployee/queryCompanyEmployeeInfoByCompanyID',
        method: 'post',
        data: form
    })
}

//@Deprecated 可废弃, 未检查到其他调用
export const add = (form) => {
    return request({
        url: '/api/sgsapi/UserManagementApi/conpanyEmployee/saveOrUpdateCompanyEmployee',
        method: 'post',
        data: form
    })
}

export const getPosts = (form) => {
    return request({
        url: '/api/sgsapi/UserManagementApi/post/queryCompanyPostBySystemName',
        method: 'post',
        data: form
    })
}

export const saveAccountPosts = (form) => {
    return request({
        url: '/api/sgsapi/UserManagementApi/post/saveCompanyEmployeePost',
        method: 'post',
        data: form
    })
}
