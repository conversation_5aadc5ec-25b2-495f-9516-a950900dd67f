@use './element-variarbles.scss';
@use './element.scss';
@use './mixin.scss';
@use './reset.scss';
@use './unit.module.scss' as *;

[class^='icon-all'],
[class*='icon-all'] {
  font-family: 'iconfont' !important;
  font-size: 35px !important;
  font-style: normal;
  color: #ff6600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.table-container {
  padding: $module-padding-vertical;
  background: #ffffff;
}
.sgs-content {
  padding: $module-margin;
}

/* 模块标题容器 */
.module-title-container {
  padding: $module-padding-vertical 0;
}
/* 模块标题文本 */
.module-title-text {
  font-size: $module-title-font-size;
  font-weight: 700;
  color: $text-color;
}
/* 模块标题子-容器 */
.module-sub-title-container {
  padding: 5px 0;
}
/* 模块标题子-文本 */
.module-sub-title-text {
  font-size: $module-sub-title-font-size;
  font-weight: 500;
  color: $text-color;
}

.model-card {
  margin-top: $module-margin-vertical;
}
