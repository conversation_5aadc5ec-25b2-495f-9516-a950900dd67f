<template>
    <div class="demo" id="demo">
        <sgs-filter-table
                :loading="tableLoading"
                filterable
                :frontPaging="false"
                :data="tableData"
                :filters="filters"
                :option="option"
                :pageShow="true"
                :page="page"
                :menuShow="true"
                :filter-config="filterConfig"
                isExport
                listName="subReportList"
                @on-load="initTable"
        >
            <template slot="content" slot-scope="{row}">
                <pre style="max-height: 200px">
                    {{JSON.parse(row.content)}}
                </pre>
            </template>
            <template slot="action" slot-scope="{row}">
                <el-button icon="el-icon-edit"></el-button>
            </template>
        </sgs-filter-table>
    </div>
</template>

<script>
    import axios from 'axios'
    import {getList} from "@/api/form/form";
    export default {
        name: "demo",
        data() {
            return {
                showPop:true,
                tableLoading: false,
                tableData: [],
                filters:{},
                filterConfig:[
                    {
                        "fieldSeq": "1",
                        "displayName": "TRF No",
                        "fieldCode": "trfNo",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "TRF No",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true}",
                        "linkType": "",
                        "fieldType": "text",
                        "fieldId": 2696421
                    },
                    {
                        "fieldSeq": "2",
                        "displayName": "Created Date",
                        "fieldCode": "createdDate",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "to",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Created Date",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true}",
                        "linkType": "",
                        "fieldType": "date",
                        "fieldId": 2696422
                    },
                    {
                        "fieldSeq": "3",
                        "displayName": "Created By",
                        "fieldCode": "createdBy",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Created By",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true,\"remote\":true,\"remoteUrl\":\"/UserManagementApi/employee/queryUserInfoList\",\"remoteValueCode\":\"regionAccount\",\"remoteLabelCode\":\"regionAccount\",\"remoteReqType\":\"FormData\",\"remoteParamKeyCode\":\"regionAccount\",\"resultCodes\":[\"list\"]}",
                        "linkType": "",
                        "fieldType": "select",
                        "fieldId": 2696423
                    },
                    {
                        "fieldSeq": "4",
                        "displayName": "Supplier",
                        "fieldCode": "supplier",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Supplier",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true,\"trans\":\"APPLY_ORG_NAME\"}",
                        "linkType": "",
                        "fieldType": "text",
                        "fieldId": 2696424
                    },
                    {
                        "fieldSeq": "5",
                        "displayName": "Style No",
                        "fieldCode": "styleNo",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Style No",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true,\"trans\":\"FG_MAT_CODE\"}",
                        "linkType": "",
                        "fieldType": "text",
                        "fieldId": 2696425
                    },
                    {
                        "fieldSeq": "6",
                        "displayName": "Brand",
                        "fieldCode": "brand",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Brand",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true,\"trans\":\"BRAND_DISPLAY\"}",
                        "linkType": "",
                        "fieldType": "text",
                        "fieldId": 2696426
                    },
                    {
                        "fieldSeq": "7",
                        "displayName": "Year/Season",
                        "fieldCode": "yearSeason",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Year",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true,\"trans\":\"YEAR_SEASONS\"}",
                        "linkType": "",
                        "fieldType": "text",
                        "fieldId": 2696442
                    },
                    {
                        "fieldSeq": "8",
                        "displayName": "Product Standard",
                        "fieldCode": "productStandard",
                        "length": 50,
                        "scene_id": "b4e0e458-6e3c-11ed-c34e-90fca236261b",
                        "rules": "",
                        "destFieldId": 0,
                        "parentFieldId": 0,
                        "required": "false",
                        "tips": "Product Standard",
                        "router": "",
                        "width": 50,
                        "options": "{\"clearable\":true,\"trans\":\"CHECK_STANDARD\"}",
                        "linkType": "",
                        "fieldType": "text",
                        "fieldId": 2696427
                    }
                ],
                page: {
                    show: true,
                    page: 0,
                    rows: 10,
                    small: true,
                    sizes: [10, 20, 50, 100],
                    layout: 'total, sizes, prev, pager, next',
                    total: 0
                },
                option:{
                    action:true,
                    filterable:true,
                    column: [
                        {label: "表单名称",prop: "name",filter: true,slot: false, width: 220,sortable:true},
                        {
                            label: "表单编码",
                            prop: "code",
                            filter: true,slot: false, width: 220,sortable:true
                        },
                        {
                            label: "表单内容",
                            prop: "content",
                            filter: true,slot: true, width: 220,sortable:true
                        },
                        {
                            label: "备注",
                            prop: "remark",
                            filter: false,slot: false,sortable:true
                        }
                    ],
                }
            }
        },
        methods:{
            substrContent(content){
                return (content || "").substring(0,50);
            },
            initTable(page = {}) {
                this.tableLoading = true;
                getList(page.page || 0,  20, {})
                    .then(res => {
                    let {records,total} = res.data.data
                    this.$set(this,'tableData',records)
                    this.$set(this.page,'total',total)
                });
            },
        },
        mounted() {
            this.initTable();
        },
        created() {
        },
        watch: {},
        props: {},
        components: {}
    }
</script>

<style scoped>

</style>