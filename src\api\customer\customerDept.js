import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/depts/add',
        method: 'post',
        data: form
    })
}
export const updateDeptStatus = (form) => {
    return request({
        url: '/api/sgs-mart/customer/depts/updateDeptStatus',
        method: 'post',
        data: form
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/depts/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/depts/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/depts/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/depts/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const getDepts = (customerId) => {
    return request({
        url: '/api/sgs-mart/customer/depts/department-name',
        method: 'get',
        params: {
            customerId,
        }
    })
}
