import CryptoJS from 'crypto-js'

// 辅助函数：调整密钥长度为 16 字节（128 位）
function adjustKey(keyStr: string): CryptoJS.lib.WordArray {
  const keyWords = CryptoJS.enc.Utf8.parse(keyStr).words.slice(0, 4) // 16 字节需要 4 个 32 位的字
  return CryptoJS.lib.WordArray.create(keyWords, 16)
}

export default {
  // 加密
  encrypt(word: number | string, keyStr: string): string {
    try {
      const key = adjustKey(keyStr)
      const srcs = CryptoJS.enc.Utf8.parse(word.toString())
      const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      })
      return encrypted.toString()
    } catch (error) {
      console.error('加密过程中出现错误:', error)
      return ''
    }
  },
  // 解密
  decrypt(word: number | string, keyStr: string): string {
    try {
      const key = adjustKey(keyStr)
      const decrypt = CryptoJS.AES.decrypt(word.toString(), key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      })
      return CryptoJS.enc.Utf8.stringify(decrypt).toString()
    } catch (error) {
      console.error('解密过程中出现错误:', error)
      return ''
    }
  },
}
