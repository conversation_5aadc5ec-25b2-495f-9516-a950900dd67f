<template>
    <basic-container >
        <el-row>
            <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
                <el-form-item>
                    <el-input 
                        v-model="query.customerName"
                        @input="onSearch"
                        @clear="onSearch"
                        :placeholder="$t(relationshipType+'.customerName')"
                        clearable>
                        <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn">
                        <i class="el-icon-circle-plus-outline"></i> {{$t('operation.add')}}
                    </el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <el-row>
          <div style="margin: -10px 0 10px;" v-if="relationshipType == 'buyer'">
            <span style="color:red;padding-top: 6px; display: block;">
                {{$t('operation.remark')}} {{$t('buyer.customerGroupTip')}}
            </span>
          </div>
            <el-table
                    :data="tableData"
                    style="width: 100%">
                <el-table-column
                        type="index"
                        fixed
                        label="#"
                        width="50">
                </el-table-column>
              <el-table-column
                  v-if="relationshipType === 'supplier' || relationshipType === 'manufacturer'"
                  prop="scmCustomerNo"
                  width="300"
                  :label="$t('sgs.sgsCustomerNo')">
              </el-table-column>
                <el-table-column
                        v-if="relationshipType != 'buyer'"
                        prop="scmCustomerReferenceCode"
                        width="300"
                        :label="$t(relationshipType+'.customerReferenceCode')">
                </el-table-column>
              <el-table-column
                  v-if="relationshipType === 'factory'"
                  prop="scmCustomerName"
                  :label="$t(relationshipType+'.customerName')"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.scmCustomerGroupCode && !scope.row.scmCustomerNo">
                      <i class="icon-all iconjituangongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  <span v-if="!scope.row.scmCustomerNo && !scope.row.scmCustomerGroupCode">
                      <i class="icon-all iconbianji" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  <span v-if="scope.row.scmCustomerNo">
                       <i class="icon-all icongongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  {{scope.row.scmCustomerName}}
                </template>
              </el-table-column>
                <el-table-column
                        v-if="relationshipType === 'buyer'"
                        prop="scmCustomerName"
                        :label="$t(relationshipType+'.customerName')"
                        >
                  <template slot-scope="scope">
                    <span :style="{'margin-left': scope.row.isSgsUser == 1 && relationshipType === 'buyer'?  '5px': '10px' }">
                      <span v-if="scope.row.isSgsUser == 1 && relationshipType === 'buyer'" style="color: red;vertical-align: top">*</span>
                    </span>
                    <span v-if="scope.row.scmCustomerGroupCode && !scope.row.scmCustomerNo">
                      <i class="icon-all iconjituangongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                    <span v-if="!scope.row.scmCustomerNo && !scope.row.scmCustomerGroupCode">
                      <i class="icon-all iconbianji" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                    <span v-if="scope.row.scmCustomerNo">
                       <i class="icon-all icongongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                    <span v-if="!scope.row.scmCustomerGroupCode">
                      {{scope.row.scmCustomerNameEN}}
                    </span>
                    <span v-if="scope.row.scmCustomerGroupCode">
                      {{scope.row.scmCustomerGroupName}}
                    </span>
                  </template>
                </el-table-column>
              <el-table-column
                  v-if="relationshipType === 'supplier' || relationshipType === 'manufacturer'"
                  prop="scmCustomerNameEN"
                  :label="$t(relationshipType+'.customerNameEN')"
              >
                <template slot-scope="scope">
                    <span :style="{'margin-left': '10px' }">
                      <span v-if=" relationshipType === 'supplier' || relationshipType === 'manufacturer'" style="color: red;vertical-align: top">*</span>
                    </span>
                  <span v-if="scope.row.scmCustomerGroupCode && !scope.row.scmCustomerNo">
                      <i class="icon-all iconjituangongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  <span v-if="!scope.row.scmCustomerNo && !scope.row.scmCustomerGroupCode">
                      <i class="icon-all iconbianji" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  <span v-if="scope.row.scmCustomerNo">
                       <i class="icon-all icongongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  {{scope.row.scmCustomerNameEN}}
                </template>
              </el-table-column>
              <el-table-column
                  v-if="relationshipType === 'supplier' || relationshipType === 'manufacturer'"
                  prop="scmCustomerNameCN"
                  :label="$t(relationshipType+'.customerNameCN')"
              >
                <template slot-scope="scope">
                    <span :style="{'margin-left': '10px' }">
                      <span v-if=" relationshipType === 'supplier' || relationshipType === 'manufacturer'" style="color: red;vertical-align: top">*</span>
                    </span>
                  <span v-if="scope.row.scmCustomerGroupCode && !scope.row.scmCustomerNo">
                      <i class="icon-all iconjituangongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  <span v-if="!scope.row.scmCustomerNo && !scope.row.scmCustomerGroupCode">
                      <i class="icon-all iconbianji" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  <span v-if="scope.row.scmCustomerNo">
                       <i class="icon-all icongongsi" style="font-size:20px!important;margin-left: 5px"></i>
                    </span>
                  {{scope.row.scmCustomerNameCN}}
                </template>
              </el-table-column>
              <el-table-column
                  v-if="relationshipType == 'buyer'"
                  prop="scmCustomerGroupCode"
                  width="300"
                  :label="$t(relationshipType+'.customerGroupCode')">
                <template slot-scope="scope">
                  {{scope.row.scmCustomerGroupCode}}
                </template>
              </el-table-column>
                <el-table-column
                        :label="$t('operation.title')"
                        width="200">
                    <template slot-scope="scope">
                        <el-button type="text" @click="detailRow(scope.row)" v-if="permissionList.editBtn && relationshipType !== 'buyer'" size="small" icon="el-icon-edit">{{$t('operation.edit')}}</el-button>
                        <el-button  @click="removeRow(scope.row)" v-if="permissionList.deleteBtn" type="text" size="small" icon="el-icon-delete">{{$t('operation.remove')}}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total">
            </el-pagination>
        </el-row>
        <el-dialog :title="$t(relationshipType+'.title.add')" :visible.sync="dialogFormVisible">
            <el-form ref="form" :model="form"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     class="sgs-form"
            >
              <el-form-item :label="$t(relationshipType +'.customerName')" required>
                <el-select v-model="form.scmCustomerName"
                           style="width: 100%"
                           :placeholder="$t('search')"
                           filterable
                           remote
                           :allow-create="relationshipType !== 'buyer' && relationshipType !== 'supplier' && relationshipType !== 'manufacturer'"
                           :remote-method="searchCustomerOrGroupDatas"
                           @change="scmCustomerChange">
                  <el-option
                      v-for="item in customerOrGroupData"
                      :label="item.nameEn"
                      :value="item.code">
                    <span style="float: left;" v-if="item.type == 'Customer'">
                      <i class="icon-all icongongsi" style="font-size:20px!important;"></i>
                    </span>
                    <span style="float: left;" v-if="item.type == 'Group'">
                      <i class="icon-all iconjituangongsi" style="font-size:20px!important;"></i>
                    </span>
                    <span style="float: left">{{ item.nameEn }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="relationshipType!='buyer'" :label="$t(relationshipType +'.customerReferenceCode')" style="margin-left: 10px" label-width="190px">
                <el-input
                    clearable
                    v-model="form.scmCustomerReferenceCode"
                    maxlength="100"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="relationshipType === 'supplier' || relationshipType === 'manufacturer'" :label="$t('sgs.sgsCustomerNo')" style="margin-left: 10px" label-width="190px">
                <el-input
                    disabled="true"
                    v-model="form.scmCustomerNo"
                    maxlength="100"
                ></el-input>
              </el-form-item>
<!--              <el-form-item :label="$t('customer.relationship.title.scope')">-->
<!--                  <el-radio v-model="form.scope" :label="1">Public (Visible throughout the company)</el-radio>-->
<!--                  <el-radio v-model="form.scope" :label="2">Private (Visible to one's own company)</el-radio>-->
<!--              </el-form-item>-->
            </el-form>
            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="submitForm('form')" :loading="antiDuplicateSubmission">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
    import {validatenull,objectIsNull} from "@/util/validate";
    import {getUsers,getList,add,getPageByUser,detail,remove,updateFactoryStatus} from "@/api/customer/factory";
    import {mapGetters} from "vuex";
    import {deleteScmCustomer,queryScmCustomer,saveScmCustomer,saveBuyerScmCustomer,customersOrGroup} from "@/api/customer/scmCustomer";
    import _debounce from 'lodash/debounce';// 导入loadsh
    export default {
        props:['relationshipType'],
        data(){
            return{
                name: "externalAccount",
                dialogFormVisible: false,
                antiDuplicateSubmission: false,
                tableData: [],
                customerOrGroupData:[],
                form: {
                  id:'', //修改时传值
                  relationshipType: '',
                  customerGroupCode: '',
                  customerId: '',
                  customerNo: '',
                  customerNameZh: '',
                  customerNameEn: '',
                  scmCustomerId: '',
                  scmCustomerReferenceCode: '',
                  scmCustomerGroupCode: '',
                  scmCustomerNo: '',
                  scmCustomerName: '',
                  createdBy:'',
                  modifiedBy:'',
                  buCode:'',
                },
                query:{
                  customerName:''
                },
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                editModel: false,
            }
        },
        methods:{
          searchCustomerOrGroupDatas:_debounce(function(val) {
            this.getCustomerOrGroupData(val);
          },1000),
            addRow(){
                this.form = {};
                this.customerOrGroupData = [];
                this.editModel = false;
                this.dialogFormVisible =true;
            },
            onSearch(val){
                console.log("重新查询",this.query.customerName);
                this.page.currentPage = 1;
                this.onLoad(this.page,this.query.customerName);
            },
            onLoad(page,customerName) {
              const params = {};
              const list = [];
              const item = {};
              item.relationshipType = this.relationshipType;
              if(customerName != null){
                item.customerName = customerName;
              }
              params.page = page.currentPage;
              params.rows = page.pageSize;
              item.buCode = this.userInfo.productLineCode;
              item.customerNo = this.userInfo.bossNo;
              list.push(item);
              params.list = list;
              queryScmCustomer(params).then(res => {
                    console.log("result",res);
                    this.tableData = res.data.rows;
                    this.page.total = res.data.records;
                });
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page,null);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page,null);
            },
            submitForm(){
              if(this.form.scmCustomerName == null || this.form.scmCustomerName == ''){
                this.$message({
                  type: "warning",
                  message: this.$t(this.relationshipType + '.selectMeg')
                });
                return;
              }
              if((this.relationshipType === 'supplier' || this.relationshipType === 'manufacturer')&&
                  (this.form.scmCustomerNo == null || this.form.scmCustomerNo == '')){
                this.$message({
                  type: "warning",
                  message: this.$t(this.relationshipType + '.selectMeg1')
                });
                return;
              }
              if(this.form.id == null || this.form.id == ''){
                //表示新增
                this.form.relationshipType = this.relationshipType;
                this.form.customerNo = this.userInfo.bossNo;
                this.form.customerNameEn = this.userInfo.customerName;
                this.form.createdBy = this.userInfo.userName;
                this.form.modifiedBy  = this.userInfo.userName;
                this.form.buCode = this.userInfo.productLineCode ==='all'?this.userInfo.defaultProductLineCode :this.userInfo.productLineCode;
              }
              if(this.relationshipType == 'buyer'){
                saveBuyerScmCustomer(this.form).then(res =>{
                  if(res.status == 200){
                    this.$message({
                      type: "success",
                      message: this.$t('api.success')
                    });
                  } else{
                    this.$message({
                      type: "warning",
                      message: res.msg
                    });
                  }
                  this.antiDuplicateSubmission = false;
                  this.dialogFormVisible = false;
                  this.onLoad(this.page,null);
                });
              } else {
                saveScmCustomer(this.form).then(res =>{
                  if(res.status == 200){
                    this.$message({
                      type: "success",
                      message: this.$t('api.success')
                    });
                  } else{
                    this.$message({
                      type: "warning",
                      message: res.msg
                    });
                  }
                  this.antiDuplicateSubmission = false;
                  this.dialogFormVisible = false;
                  this.onLoad(this.page,null);
                });
              }
            },
            getCustomerOrGroupData(val){
              const param = {};
              param.customerName = val;
              param.buCode = this.userInfo.productLineCode ==='all'?this.userInfo.defaultProductLineCode :this.userInfo.productLineCode;
              param.pageSize = 10;
              param.relationshipType = this.relationshipType;
              customersOrGroup(param).then(res => {
                console.log(res.data.data);
                this.customerOrGroupData = res.data.data;
              });
            },
            detailRow(row){
              console.log("row",row);
              this.title = this.$t('address.title.edit');
              this.dialogFormVisible = true;
              this.customerOrGroupData = [];
              this.form = row;
            },
            scmCustomerChange(val){
              console.log("useeinfo",this.userInfo);
              let obj = this.customerOrGroupData.find((item) => {
                return item.code === val;
              });
              if(!objectIsNull(obj)){
                //选择客户组 按照客户组存储
                if(obj.type == "Group"){
                  this.form.scmCustomerGroupCode = obj.code;
                  this.form.scmCustomerName = obj.nameEn;
                  this.form.scmCustomerNo = '';
                  this.form.modifiedBy = this.userInfo.userName;
                } else if (obj.type == "Customer"){
                  this.form.scmCustomerNo = obj.code;
                  this.form.scmCustomerName = obj.nameEn;
                  this.form.scmCustomerGroupCode = '';
                  this.form.modifiedBy = this.userInfo.userName;
                } else{
                  this.form.modifiedBy = this.userInfo.userName;
                }
              }
            },
            removeRow(row){
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                    .then(() => {
                      const param = {}
                      const id = [];
                      id.push(row.id);
                      param.id = id;
                        deleteScmCustomer(param).then(() => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.onLoad(this.page,null);
                        });
                    })
            },
            changeStatus(row){
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                updateFactoryStatus(modifiedForm).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
        },
        created() {
            this.onLoad(this.page);
            const  customerId=this.userInfo.companyId;
        },
        computed: {
            ...mapGetters([
                "permission",
                "userInfo"
            ]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:'+this.relationshipType+':add'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:'+this.relationshipType+':edit'],false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:'+this.relationshipType+':delete'],false),
                };
            }
        },

    }
</script>

<style>
</style>
