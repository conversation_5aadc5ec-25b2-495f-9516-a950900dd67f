<template>
    <div>
        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
            <el-form-item>
                <el-input
                    @input="onSearch"
                    @keyup.enter.native="onSearch"
                    @clear="onSearch"
                    v-model="query.contactName"
                    :placeholder="$t('contact.nameFull')"
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <!-- <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button> -->
                <!-- <el-button type="primary" v-if="permissionList.addBtn" @click="addRow">{{$t('operation.add')}}</el-button> -->

                <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn" id="add-contact">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>

        <!-- <el-form :inline="true" :model="formInline" size="medium">
            <el-form-item>
                <el-input v-model="query.contactName" :placeholder="$t('contact.nameFull')" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                <el-button v-if="permissionList.addBtn" type="primary" @click="addRow">{{$t('operation.add')}}</el-button>
            </el-form-item>
        </el-form> -->
        <el-table
                :data="tableData"
                style="width: 100%"
                size="medium">
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="contactName"
                    :label="$t('contact.name')"
                    show-overflow-tooltip
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="contactTel"
                    :label="$t('contact.phone')"
                    width="150">
            </el-table-column>
            <el-table-column
                    prop="contactEmail"
                    show-overflow-tooltip
                    :label="$t('contact.email')">
            </el-table-column>
            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')"
                    width="150"
                    align="center">
            </el-table-column>
            <el-table-column
                    prop="updateUser"
                    :label="$t('common.operator')"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="isDefault"
                    :label="$t('common.isDefault')"
                    width="80"
                    align="center">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')" placement="top">
                        <el-switch
                                v-model="scope.row.isDefault"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9"
                                :active-value = "1"
                                :inactive-value = "0"
                                @change="changeDefault(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    prop="status"
                    :label="$t('common.status.title')"
                    width="80"
                    align="center">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                        <el-switch
                                v-model="scope.row.status"
                                active-color="#FF6600"
                                inactive-color="#D9D9D9"
                                :active-value = "1"
                                :inactive-value = "0"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="150"
                    align="center">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)">{{$t('operation.edit')}}</el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="rowDel(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                </template>
            </el-table-column>

        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        
        <el-dialog 
            width="660px"
            id="add-contact-dialog"
            class="z-index-cover"
            append-to-body="true" 
            @close="handleClose"
            :modal="$store.state.user.addContact ? false : true" 
            :close-on-click-modal="false" 
            :title="title"
            :visible.sync="dialogFormVisible">
            <el-form ref="form" :model="form" id="add-contact-form"
                     label-width="100px"
                     label-position="left"
                     size="medium"
                     class="sgs-form">
                <el-form-item :label="$t('contact.name')"
                              :rules="{ required: true, message:  $t('contact.validate.nameBlur'), trigger: 'blur' }"
                              prop="contactName">
                    <el-input
                            v-model="form.contactName"
                            maxlength="50">
                    </el-input>
                </el-form-item>
                <el-form-item :label="$t('contact.phone')"
                              :rules="{ required: true,  message: $t('contact.validate.phoneValidate'), trigger: 'blur' }"
                              prop="contactTel">
                    <el-input
                            v-model="form.contactTel"
                            maxlength="20"
                    ></el-input>
                </el-form-item>

                <el-form-item :label="$t('contact.email')" :rules="[
                      { required: true, message: $t('contact.validate.emailBlur'), trigger: 'blur' },
                      { type: 'email', message:  $t('contact.validate.emailWaring'), trigger: ['blur', 'change'] }
                    ]"  prop="contactEmail">
                    <el-input v-model="form.contactEmail"
                              maxlength="100">
                    </el-input>
                </el-form-item>
            </el-form>
            <div id="stepContent" :class="{'submit-tip-step': submitTips}"></div>
            <br>
            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button id="add-contact-confirm" size="small" type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {getPageByUser,add,remove,detail,setDefault} from "@/api/customer/customerContact";
    import {mapGetters} from "vuex";
    export default {
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        data(){

            return{
                name: "contactPerson",
                title:'',
                dialogFormVisible: false,
                btnGuestbookSubmit: false,
                tableData: [],
                form: {},
                query: {},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                stepContent: '',
                submitTips: false,
            }
        },
        watch: {
            '$store.state.user.addContact' (newVal, oldVal) {
                this.dialogFormVisible = newVal
                if(newVal) {    // DOM操作
                    setTimeout(() => {
                        document.querySelector("#add-contact-dialog #stepContent").appendChild(document.querySelector('.step-content'))
                    }, 200)
                }
            },
            '$store.state.user.taskType'(newVal, oldVal) {
                if(newVal == '') {
                    this.dialogFormVisible = false
                }
            },
            'form': {
                deep: true,
                handler: function(newVal) {
                    if(newVal && this.$store.state.user.taskType != '') {
                        this.submitTips = true
                        document.querySelector("#stepContent .tit").innerText = this.$t('guide.clickSubmit')
                        document.querySelector("#stepContent .step-num span").innerText = this.$t('guide.step4') + this.$t('guide.total4')
                    }
                }
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:contact:add'],false),
                    editBtn: this.vaildData(this.permission['sgs:customer:contact:edit'],false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:contact:delete'],false),
                };
            }
        },
        methods:{
            handleClose() {
                this.$store.commit('SET_TASK_TYPE', '')
                this.$store.commit('SET_GUIDE', { name: 'addContact', val: false }) // 关闭step中的添加弹窗
                document.querySelectorAll("#stepContent").forEach(item => item.innerHTML = '')
                this.form.contactName = ''
                this.form.contactTel = ''
                this.form.contactEmail = ''
                this.submitTips = false
            },
            onSearch() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query,this.sort)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form){
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        if(this.$store.state.user.taskType != '') { // 完成添加联系人引导
                            let task = JSON.parse(localStorage.getItem('guideTask'))
                            let index = task.findIndex(item => item.type == 'addContact')
                            if(!task[index].val) {
                                task[index].val = true  // 设置当前任务为完成状态
                                this.$notify({
                                    title: this.$t('success'),
                                    message: this.$t('guide.addContactSuccess'),
                                    type: 'success',
                                    duration: 1500
                                });
                            }
                            localStorage.setItem('guideTask', JSON.stringify(task))
                            let complete = task.every(item => item.val == true);
                            if(!complete) {
                                setTimeout(()=>{
                                    this.$store.commit('SET_TASK_DIALOG', true) // 2s后打开任务列表
                                }, 2000)
                            }
                        }
                        this.btnGuestbookSubmit=true;
                        add(this.form).then(res =>{
                            this.btnGuestbookSubmit=false;
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.dialogFormVisible=false;
                            this.onLoad(this.page);
                        });
                    } else {
                        return false;
                    }
                });
            },
            rowDel(row){
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                }).then(() => {
                    remove(row.id).then(() => {
                        this.$message({
                            type: "success",
                            message:  this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                })
            },
            detailRow(row){
                this.title = this.$t('contact.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form=res.data.data;
                });

            },
            addRow(){
                this.form = {};
                this.title = this.$t('contact.title.add');
                this.dialogFormVisible =true;
            },
            changeStatus(row){
                const modifiedForm = {
                    id: row.id,
                    status: row.status
                };
                add(modifiedForm).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            },
            changeDefault(row){
                setDefault(row.id,row.isDefault).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            }
        },

        created() {
            this.onLoad(this.page);
        }

    }
</script>

<style lang="scss" scoped>
#add-contact-dialog {
    .el-dialog {
        .el-form-item__label {
            padding-top: 6px;
        }
        #stepContent {
            position: absolute;
            left: 90px;
            bottom: 90px;
            transition: all .2s;
        }
        .submit-tip-step {
            transform: translate(244px, 58px);
        }
    }
}
</style>
