const CryptoJS = require("crypto-js");

// 辅助函数：调整密钥长度为 16 字节（128 位）
function adjustKey(keyStr) {
  const keyWords = CryptoJS.enc.Utf8.parse(keyStr).words.slice(0, 4); // 16 字节需要 4 个 32 位的字
  return CryptoJS.lib.WordArray.create(keyWords, 16);
}

module.exports = {
  // 加密函数
  encrypt(word, keyStr) {
    try {
      const key = adjustKey(keyStr);
      const srcs = CryptoJS.enc.Utf8.parse(word.toString());
      const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      });
      return encrypted.toString();
    } catch (error) {
      console.error("加密过程中出现错误:", error);
      return "";
    }
  },
  // 解密函数
  decrypt(word, keyStr) {
    try {
      const key = adjustKey(keyStr);
      const decrypt = CryptoJS.AES.decrypt(word.toString(), key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      });
      const decryptedStr = CryptoJS.enc.Utf8.stringify(decrypt);
      if (decryptedStr) {
        return decryptedStr;
      } else {
        console.error("解密失败，可能是密钥不匹配或加密字符串格式错误");
        return "";
      }
    } catch (error) {
      console.error("解密过程中出现错误:", error);
      return "";
    }
  },
};
