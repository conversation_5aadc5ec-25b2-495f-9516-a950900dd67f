import request from './request'
// 定义通用的请求参数类型
type RequestParams = Record<string, any>

const sendPost = (url:string,param:RequestParams,headers?:RequestParams) : Promise<any> =>{
    headers = headers?headers:{}
    param['source'] = 'SMART';
    return request({
        url,
        method:'post',
        headers:{
            ...headers
        },
        data:param
    })
}
/*const sendGet = (url:string,param:RequestParams,headers?:RequestParams) : Promise<any> =>{
    url = url+'?hash='+new Date().getTime();
    return request({
        url,
        method:'get',
        headers:{
            loading:'false',
            ...headers
        },
        params:param
    })
}*/

const api = {
    queryCustomerInfo: (param: RequestParams )=> {
        return sendPost('/api/sgs-scm/customer/company/query', param,{loading:'false'});
    },
    querySCM:(param: RequestParams,headers?:RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/query',param,headers)
    },
    queryManufacture:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/manufacture',param);
    },
    handlerApply:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/company/apply',param);
    },
    changeStatus:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/status',param);
    },
    changAssignCode:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/changAssignCode',param,{loading:'false'});
    },
    queryContacts:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/company/contacts',param);
    },
    querySCMContacts:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/queryContacts',param);
    },
    applyRelation:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/applyRelationShip',param);
    },
    updateContacts:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/contact',param);
    },
    approveReject:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/scm/approve',param);
    },
    addBuyer:(param: RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/company/addBuyer',param);
    },
    queryCompanyList:(param:RequestParams)=>{
        return sendPost('/api/sgs-scm/customer/company/get',param,{loading:"false"})
    }
}


export default api;