<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import en from 'element-plus/es/locale/lang/en';

const { locale } = useI18n();

const currentLocale = computed(() => {
  if (locale.value === 'zh-CN') {
    return zhCn;
  } else if (locale.value === 'en-US') {
    return en;
  }
  return en; 
});
</script>
<style lang="scss">
@font-face {
  font-family: "Regular";
  src: url("/fonts/SuisseIntl-Regular-WebS.woff") format("woff");
}
@font-face {
  font-family: "Roboto-Regular";
  src: url("/fonts/Roboto-Regular.ttf") format("truetype");
}
@font-face {
  font-family: "UniversLT";
  src: url("/fonts/Univers-LT-55-Roman.ttf") format("truetype");
}
@font-face {
  font-family: "localArial";
  src: url("/fonts/Arial.ttf") format("truetype");
}
@font-face {
  font-family: "思源黑体";
  font-weight: 350;
  src: url("//at.alicdn.com/wf/webfont/280FyQakdv9L/2PrR3QzsBPm5DH8ZtGVEc.woff2") format("woff2"),
       url("//at.alicdn.com/wf/webfont/280FyQakdv9L/zwqP3zyrOYxnoT6LnbgXb.woff") format("woff");
  font-display: swap;
}
</style>