<template>
  <div id="app">
    <router-view  v-if="isRouterAlive"/>
    <el-dialog
      custom-class="publish_dialog"
      :visible.sync="publishDiaShow"
      modal
      top="30vh"
      width="680px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
    <publish-dia
        v-if="publishDiaShow"
        :publish-data="publishData"
        ref="publishDialog"
        @closeDia="publishDiaShow=false"
      ></publish-dia>
    </el-dialog>
  </div>
</template>

<script>
  import {mapGetters} from "vuex";
  import {tzFormChina} from '@/util/datetimeUtils'
  import {validatenull,objectIsNull} from "@/util/validate";
  import publishDia from "./views/appDia/publishDia";
  import {getNotifyMsg} from "@/api/system/notify";
  import { LanguageEnums } from "@/commons/enums/LanguageEnums";
  import {getToken} from "@/util/auth"
  import sockJs from 'sockjs-client'
  import Stomp from 'stompjs'
export default {
  name: "app",
  provide() {    //父组件中通过provide来提供变量，在子组件中通过inject来注入变量。
      return {
        reload: this.reload,
      }
    },
  data() {
    return {     
        whileHosts:['cnsgsmart-uat.sgs.net','cnsgsmart.sgs.net','localhost'],
        LanguageEnums: LanguageEnums,
        publishDiaShow:false,
        publishData:{},
        socketInstance:null,
        timeoutId :null,
        intervalObj:null,
        wsConnect:false,
        currentRoutePath:'',
        constStr:{
          publish_sgsMart_data:'publish_sgsMart_data',
          publish_sgsMart_time:'publish_sgsMart_time',
        },
      isRouterAlive: true,
      defferScroll: function (event) {
        event.preventDefault()
      }
    };
  },
  watch: {
    '$store.state.user.taskType' (newVal, oldVal) {
        if(newVal != '') {
          // console.log('禁止滚动')
          document.body.addEventListener("touchmove", this.defferScroll, {passive: false});
          document.body.addEventListener("wheel", this.defferScroll, {passive: false});
        } else {
          // console.log('恢复滚动')
          document.body.removeEventListener("touchmove", this.defferScroll, {passive: false});
          document.body.removeEventListener("wheel", this.defferScroll, {passive: false});
        }
    },
    "$route":{
        immediate:true,
        handler(newV,oldV){
          let {path} = newV;
          this.currentRoutePath = path;
          if(path!="/"){
            //this.initDialog();
          }
        }
      }
  },
  components:{publishDia},
  created() {
    //获取当前域名信息
    const domainName = window.location.hostname;
    //包含白名单域名的话 无需请求维护信息
    if(!this.whileHosts.includes(domainName)){
      //进入页面先加载一次系统维护信息
      this.loadSysNotifyMessage();
      //定时请求系统维护信息
      this.initTimer();
    }
    // new Promise((resolve,reject)=>{
    //     this.initWs();
    //     resolve();
    //   })
    //   window.addEventListener('beforeunload',()=>{
    //     this.disconnect(this.socketInstance,this.wsConnect);
    //     console.log("关闭")
    //   });
  },
  methods: {
    currentTzDate(val){
      if (!val) return ''
      let value = tzFormChina(val,'YYYY-MM-DD HH:mm');
      return value;
    },
    reload() {
        this.isRouterAlive = false
        this.$nextTick(function() {
          this.isRouterAlive = true
        })
      },
      handlerMsg(body){
        if(this.includeWhiteList(body)){
          console.log("处理msg信息 白名单，不做任何处理")
          return;
        }
        try{
          sessionStorage.setItem(this.constStr.publish_sgsMart_data,body);

          let publishDia = JSON.parse(body);
          let {finalPublishStatus,weekDay,startDateTime,finishDateTime} = publishDia;

          if(finalPublishStatus==1){//预发布
            this.prePublish(startDateTime);
          }
          if(finalPublishStatus==2){//发布中
            this.publishIng(publishDia);
          }
          if(finalPublishStatus == 0){//正常
            this.publishFinish();
          }
        }catch (e) {
          console.log("弹窗异常",e)
        }
      },
      prePublish(startDate){
        startDate = this.currentTzDate(startDate);
        let startD = +new Date(startDate);
        let publish_sgsMart = sessionStorage.getItem(this.constStr.publish_sgsMart_time);
        if(publish_sgsMart == startD){
          return;
        }
        //已经点击过确定按钮，知道是提示信息，这里的cookie要严格设置好过期时间
        let currentDate = +new Date();
        let distance =  startD - currentDate;
        //说明已经过了发布时间，但是状态没有改变，不用提示
        if(distance<0){
          sessionStorage.removeItem(this.constStr.publish_sgsMart_time);
          return;
        }
        let h = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        let m = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        /*//早于1h ，不提示
        if(h-0>1){
          this.$assetsTool.delCookie(this.constStr.publish_sgsMart_time);
          return;
        }*/
        sessionStorage.setItem(this.constStr.publish_sgsMart_time, startD);
        //TODO 国际化修改  时区修改
        if(this.language === this.LanguageEnums.EN.name){
          this.$alert(`System maintenance will start at 【${startDate}】, to prevent any data loss, please save all your work and log off before the maintenance begins.`, 'System Maintenance Alert', {
              confirmButtonText: this.$t('operation.confirm'),
              callback: action => {
              }
            });
        }else{
            this.$alert(`系统定于【${startDate}】进行维护，请提前保存工作并注销，以确保数据安全`, '系统维护通知', {
              confirmButtonText:  this.$t('operation.confirm'),
              callback: action => {
              }
            });
        }
     
      },
      publishIng(publishDia){
        publishDia.language = this.language;
        console.log("维护页面语言111",this.language)
        //时区处理
        if(!objectIsNull(publishDia)){
          publishDia.finishDateTime = this.currentTzDate(publishDia.finishDateTime);
          if(!objectIsNull(publishDia.publishRemark)){
            publishDia.publishRemark=JSON.parse(publishDia.publishRemark);
          }
        }
        sessionStorage.removeItem(this.constStr.publish_sgsMart_time);
        if( !this.publishDiaShow){
          this.publishData = publishDia;
          this.publishDiaShow = true;
        }else{
          this.$refs.publishDialog.refreshData(publishDia);
        }
      },
      publishFinish(){
        sessionStorage.removeItem(this.constStr.publish_sgsMart_time);
        sessionStorage.removeItem(this.constStr.publish_sgsMart_data);
        if(this.publishDiaShow){
          this.publishDiaShow = false;
        }
      },
      connectSuccess(client){
        let systemId = 101;//SGSmart SYSTEM ID       
        let sgsToken = getToken();      
        client.send("/app/queryPublishStatus",{sgsToken},JSON.stringify({systemId}));
      },
      //请求SMART IM服务，获取redis中的维护信息
      loadSysNotifyMessage(){
        getNotifyMsg().then(res =>{
            let data=res.data.data;
            if(data){
              this.handlerMsg(JSON.stringify(data));
            }                  
           }).catch(err => {
            console.error('获取维护信息异常',err);
            this.publishFinish();     
           });
      },
      initTimer(){
        let _this = this;
      // 创建定时器
        let timerNum = 30 * 60 * 1000;
        if( [0,6].includes(new Date().getDay())){
          timerNum=10 * 60 * 1000;
        }
        setInterval(function() {
          _this.loadSysNotifyMessage();
        }, timerNum); // 5分钟的时间间隔，以毫秒为单位
        // if(this.wsConnect){
        //   return;
        // }
        // let path = "api/wsSgsapi/notescommonapi/api/publishWebSocket";
        // let socket = new sockJs(path);
        // this.socketInstance = Stomp.over(socket);
        // //this.socketInstance.debug = false;
        // //若使用STOMP 1.1 版本，默认开启了心跳检测机制（默认值都是10000ms）
        // this.socketInstance.heartbeat.outgoing = 10000;
        // //客户端不从服务端接收心跳包
        // this.socketInstance.heartbeat.incoming = 0;
        // let randomSessionId = parseInt(Math.random() * 1e10);
        // let header = {
        //   authorization: randomSessionId,
        //   sessionId:randomSessionId
        // }
        // this.socketInstance.connect({...header}, frame=> {
        //   console.log('Connected success: ' , frame);
        //   this.wsConnect = true;
        //   this.socketInstance.subscribe('/sub/SgsMart/publishData', result=>{
        //     console.log("client 接收到消息/sub/SgsMart/publishData ->",result)
        //     let {body} = result;
        //     this.handlerMsg(body);
        //     result.ack();
        //   });
        //   this.connectSuccess(this.socketInstance);
        // },err=>{
        //   console.log("连接异常",err)
        //   this.disconnect(this.socketInstance,this.wsConnect);
        //   this.initDialog();
        //   //10s 重新连接，
        //   setTimeout(()=>{
        //     this.initWs();
        //   },10*1000)
        // });
      },
      includeWhiteList(bodyData){
        let users = sessionStorage.getItem("access-user");
        if(!users){
          return false;
        }
        users = JSON.parse(users);
        let {userInfo} = users;
        if(!userInfo){
          return false;
        }
        let {regionAccount} = userInfo;
        if(!regionAccount){
          return false;
        }
        regionAccount = regionAccount.toUpperCase();
        let publishDia = bodyData || sessionStorage.getItem(this.constStr.publish_sgsMart_data);
        if(publishDia){
          publishDia = JSON.parse(publishDia);
          let {whiteList} = publishDia;
          if(whiteList){
            whiteList = whiteList.toUpperCase().split(",");
          }
          if(whiteList.includes(regionAccount)){
            console.log(regionAccount+"是白名单，不用处理弹窗")
            return true;
          }
        }
        return false;

      },
      initDialog(){
        if(this.includeWhiteList()){
          console.log("初始化dialog 白名单，不处理")
          return;
        }
        if(this.wsConnect){
          console.log("ws 已经连接，不读取cookie")
          return;
        }
        let publishDia = sessionStorage.getItem(this.constStr.publish_sgsMart_data);
        if(publishDia){
          console.log("ws 没有连接，尝试读取cookie",JSON.parse(publishDia));
          this.handlerMsg(publishDia);
        }else{
          console.log("没有cookie信息")
        }
      },
      disconnect(instance,wsCon){
        this.wsConnect = false;
        if(instance){
          instance.disconnect();
          instance = null;
          console.log("断开ws 连接")
        }
      },
  },
  mounted() {},
  computed: {
    ...mapGetters(["language"]),
  }
};
</script>
<style scoped lang="scss">
  .app{
    .el-dialog__body,.el-dialog__header{
      padding: 0 !important;
      background: #dddddd !important;
    }
  }
</style>
<style lang="scss">

@font-face {
  font-family: "Regular";
  src: url("/fonts/SuisseIntl-Regular-WebS.woff") format("truetype");
}
@font-face {
  font-family: "Roboto-Regular";
  src: url("/fonts/Roboto-Regular.ttf") format("truetype");
}
@font-face {
  font-family: "UniversLT";
  src: url("/fonts/Univers-LT-55-Roman.ttf") format("truetype");
}
@font-face {
  font-family: "localArial";
  src: url("/fonts/Arial.ttf") format("truetype");
}
@font-face {
  font-family: "思源黑体";
  font-weight: 350;
  src: url("//at.alicdn.com/wf/webfont/280FyQakdv9L/2PrR3QzsBPm5DH8ZtGVEc.woff2") format("woff2"),
       url("//at.alicdn.com/wf/webfont/280FyQakdv9L/zwqP3zyrOYxnoT6LnbgXb.woff") format("woff");
  font-display: swap;
}
#app {
  width: 100%;
  font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
  padding: 0 !important;
}
body {
  font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
}
.el-table {
  th {
    /* background-color: #f60 !important; */
  }
}
.publish_dialog{
    background: transparent !important;
    .el-dialog__header{
      display: none;
    }
    .el-dialog__body,.el-dialog__header{
      padding: 0 !important;
    }
  }
</style>
