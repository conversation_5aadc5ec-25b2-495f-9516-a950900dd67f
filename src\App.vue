<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import en from 'element-plus/es/locale/lang/en';

const { locale } = useI18n();

const currentLocale = computed(() => {

  if (locale.value === 'zh-CN') {
    return zhCn;
  } else if (locale.value === 'en-US') {
    return en;
  }
  return en; 
});
</script>