<template>
    <div class="container">
        <el-card class="sgs-group">
            <h3>{{ $t("service.servicRequire") }}</h3>
            <el-row :gutter="20">
                <div
                    class="item-warp clearfix"
                    v-if="
                        trfServiceRequire.reportLanguage ==
                            languageEnums.CN.code ||
                        trfServiceRequire.reportLanguage ==
                            languageEnums.DOUBLE.code ||
                        trfServiceRequire.reportLanguage ==
                            languageEnums.DOUBLE_6.code
                    "
                >
                    <el-col span="11">
                        <el-form-item
                            :label="$t('service.reportHeaderZh')"
                            :rules="{
                                required: false,
                                message: $t('trf.validate.requiredBlur'),
                                trigger: 'blur',
                            }"
                        >
                            <el-input
                                type="textarea"
                                maxlength="510"
                                v-model="trfServiceRequire.reportHeader"
                                :disabled="trfDisabled"
                                autocomplete="off"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            :label="$t('service.reportAddressZh')"
                            :rules="{
                                required: false,
                                message: $t('trf.validate.requiredBlur'),
                                trigger: 'blur',
                            }"
                        >
                            <el-input
                                type="textarea"
                                maxlength="1010"
                                v-model="trfServiceRequire.reportAddress"
                                :disabled="trfDisabled"
                                autocomplete="off"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </div>
                <div
                    class="item-warp clearfix"
                    v-if="
                        trfServiceRequire.reportLanguage ==
                            languageEnums.EN.code ||
                        trfServiceRequire.reportLanguage ==
                            languageEnums.DOUBLE.code ||
                        trfServiceRequire.reportLanguage ==
                            languageEnums.DOUBLE_6.code
                    "
                >
                    <el-col :span="11">
                        <el-form-item
                            :label="$t('service.reportHeaderEn')"
                            :rules="{
                                required: false,
                                message: $t('trf.validate.requiredBlur'),
                                trigger: 'blur',
                            }"
                        >
                            <el-input
                                type="textarea"
                                maxlength="510"
                                v-model="trfServiceRequire.reportHeaderEn"
                                :disabled="trfDisabled"
                                autocomplete="off"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            :label="$t('service.reportAddressEn')"
                            :rules="{
                                required: false,
                                message: $t('trf.validate.requiredBlur'),
                                trigger: 'blur',
                            }"
                        >
                            <el-input
                                type="textarea"
                                maxlength="1010"
                                v-model="trfServiceRequire.reportAddressEn"
                                :disabled="trfDisabled"
                                autocomplete="off"
                                clearable
                            />
                        </el-form-item>
                    </el-col>
                </div>
                <div
                    class="item-warp clearfix"
                    v-if="
                        productLineCode === productLineEnums.HL.code
                            ? isGeneralRequest !== 1
                                ? true
                                : false
                            : true
                    "
                >
                    <el-col :span="12">
                        <el-form-item :label="$t('service.reportSuccessSend')">
                            <el-select
                                v-model="emailAddressesValue"
                                :disabled="trfDisabled"
                                @change="reportDeliveredToChange"
                                :placeholder="$t('operation.pleaseSelect')"
                                multiple
                                filterable
                                allow-create
                                default-first-option
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="(obj, index) in emailGroupData"
                                    :label="obj.emailGroupName"
                                    :key="obj.id"
                                    :value="obj.emailGroupName"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </div>
                <div
                    class="item-warp clearfix"
                    v-if="productLineCode === productLineEnums.SL.code"
                >
                    <el-col :span="12">
                        <el-form-item :label="$t('service.reportErrorSend')">
                            <el-select
                                v-model="failEmailAddressesValue"
                                @change="failReportDeliveredToChange"
                                :placeholder="$t('operation.pleaseSelect')"
                                multiple
                                filterable
                                allow-create
                                default-first-option
                                :disabled="trfDisabled"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="obj in emailGroupData"
                                    :label="obj.emailGroupName"
                                    :key="obj.id"
                                    :value="obj.emailGroupName"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </div>
                <SLServiceRequirement
                    v-show="
                        productLineCode === productLineEnums.SL.code &&
                        isGeneralRequest === 1
                    "
                    :trfServiceRequire="trfServiceRequire"
                    :trfDisabled="trfDisabled"
                />
                <CPServiceRequirement
                    v-show="isCpRequest === 1"
                    :trfServiceRequire="trfServiceRequire"
                    :trfDisabled="trfDisabled"
                />
                <HLServiceRequirement
                    v-if="
                        productLineCode === productLineEnums.HL.code &&
                        isGeneralRequest === 1
                    "
                    :trfServiceRequire="trfServiceRequire"
                    :trfDisabled="trfDisabled"
                    ref="hlRequirementRef"
                    :trfDataLoadStatus="trfDataLoadStatus"
                    @serviceItemChange="serviceItemChange"
                />
                <el-col :span="23">
                    <el-form-item
                        :label="$t('service.otherRequest')"
                        prop="servicRequire.otherRequire"
                        :rules="{
                            required: false,
                            message: $t('trf.validate.requiredBlur'),
                            trigger: 'change',
                        }"
                    >
                        <el-input
                            type="textarea"
                            maxlength="200"
                            v-model="trfServiceRequire.otherRequire"
                            :placeholder="$t('service.otherTip')"
                            clearable
                            :disabled="trfDisabled"
                            autocomplete="off"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import {LanguageEnums} from "@/commons/enums/LanguageEnums"
import {ProductLineEnums} from "@/commons/enums/ProductLineEnums"
import serviceRequirement from "@/components/trf/js/serviceRequirement"
import {objectIsNull, validateEmail} from "@/util/validate"
import {mapGetters} from "vuex"

const SLServiceRequirement = () =>
    import("@/components/trf/SLServiceRequirement")

const CPServiceRequirement = () =>
    import("@/components/trf/CPServiceRequirement")
const HLServiceRequirement = () =>
    import("@/components/trf/HLServiceRequirement")

export default {
    //TRF 服务需求
    name: "BaseServiceRequirement",
    components: {
        SLServiceRequirement,
        HLServiceRequirement,
        CPServiceRequirement,
    },
    props: {
        productLineCode: {
            type: String,
            required: true,
            default: null,
            description: "产品线code",
        },
        trfServiceRequire: {
            type: Object,
            required: true,
            default: {},
            description: "服务需求对象",
        },
        reportDeliveredTo: {
            type: String,
            required: true,
            default: null,
            description: "报告送达邮箱列表",
        },
        failedReportDeliveredTo: {
            type: String,
            required: true,
            default: null,
            description: "不通过报告邮箱列表",
        },
        emailGroupData: {
            type: Array,
            required: true,
            default: [],
            description: "可选邮箱组数据",
        },
        isGeneralRequest: {
            type: Number,
            required: true,
            default: 0,
            description:
                "是否为标准请求(1:标准请求, 按业务线展示服务需求组件; 0:非标准请求, 隐藏业务线服务需求组件)",
        },
        isCpRequest: {
            type: Number,
            required: true,
            default: 0,
            description:
                "是否为标准请求(1:标准请求, 按业务线展示服务需求组件; 0:非标准请求, 隐藏业务线服务需求组件)",
        },
        trfDisabled: {
            type: Boolean,
            required: true,
            default: false,
            description: "是否可操作",
        },
        trfDataLoadStatus: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    watch: {
        reportDeliveredTo: function (newVal, oldVal) {
            if (objectIsNull(this.emailAddressesValue)) this.initDeliveredTo()
        },

        failedReportDeliveredTo: function (newVal, oldVal) {
            if (objectIsNull(this.failEmailAddressesValue))
                this.initDeliveredTo()
        },
    },
    computed: {
        ...mapGetters(["language"]),
    },
    data() {
        return {
            emailAddressesValue: [],
            failEmailAddressesValue: [],
            productLineEnums: ProductLineEnums,
            languageEnums: LanguageEnums,
        }
    },
    mounted() {
        console.log("reportDeliveredTo", this.reportDeliveredTo)
        this.initDeliveredTo()
    },
    methods: {
        //初始化邮箱选项传值
        initDeliveredTo() {
            this.emailAddressesValue = objectIsNull(this.reportDeliveredTo)
                ? []
                : this.reportDeliveredTo.split(",")
            this.failEmailAddressesValue = objectIsNull(
                this.failedReportDeliveredTo,
            )
                ? []
                : this.failedReportDeliveredTo.split(",")
        },
        //回传报告发送邮箱选项
        reportDeliveredToChange(values) {
            this.emailAddressesValue = serviceRequirement.changeContacts(
                this.emailGroupData,
                values,
                this.emailAddressesValue,
            )
            this.$emit("onChangeDeliveredTo", this.emailAddressesValue)
        },
        //回传不通过报告发送邮箱选项
        failReportDeliveredToChange(values) {
            this.failEmailAddressesValue = serviceRequirement.changeContacts(
                this.emailGroupData,
                values,
                this.failEmailAddressesValue,
            )
            this.$emit("onChangeFailDeliveredTo", this.failEmailAddressesValue)
        },

        //包装 Service Require 对象
        packagingServiceRequireData() {
            if (this.productLineCode === ProductLineEnums.HL.code) {
                if (this.isGeneralRequest === 1) {
                    this.trfServiceRequire =
                        this.$refs.hlRequirementRef.packagingComponentValue()
                } else {
                    //用默认值覆盖当前的HL配置项
                    this.trfServiceRequire = Object.assign(
                        this.trfServiceRequire,
                        ProductLineEnums.HL.ServiceRequireDefault,
                    )
                    this.trfServiceRequire.isValid = true
                }
            }
            if (this.productLineCode === ProductLineEnums.SL.code) {
                if (this.isGeneralRequest != 1) {
                    this.trfServiceRequire = Object.assign(
                        this.trfServiceRequire,
                        ProductLineEnums.SL.ServiceRequireDefault,
                    )
                }
            }
            return this.trfServiceRequire
        },
        validateForm() {
            return this.$refs.hlRequirementRef.validateForm()
        },
        serviceItemChange(item) {
            this.$emit("serviceItemChange", item)
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    background-color: white;
}
.card {
    width: 100%;
    border-style: none;
    height: auto;
}
</style>
