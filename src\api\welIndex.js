import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const dashboardPassAndFail = (form) => {
    return request({
        method: 'post',
        url: "/api/sgs-mart/statistics/searchTRF",
        data:form
    })
}
export const trfSupplierCount = (form) => {
    return request({
        method: 'post',
        url: "/api/sgs-mart/statistics/searchTRF",
        data:form
    })
}

//trf数量统计
export const trfStatCount = (form) => {
    return request({
        method: 'post',
        url: "/api/sgs-mart/search/count",
        data:form
    })
}

//report 数量统计(仪表盘)
export const reportStatCount = (form) => {
    return request({
        method: 'post',
        url: "/api/sgs-mart/search/report/pie",
        data:form
    })
}

//report 数量统计 按月（折线图）
export const reportStatCountByMonth = (form) => {
    return request({
        method: 'post',
        url: "/api/sgs-mart/search/report/line",
        data:form
    })
}

export const documentLibrary = (productLineCode,languageId,pin) => {
    return request({
        url: '/api/sgs-mart/library/documentLibrary',
        method: 'get',
        params: {
            productLineCode,
            languageId,
            pin
        }
    })
}

export const removeTrfTemplate = (ids) => {
    return request({
        url: '/api/sgs-mart/trf/removeTrfTemplate',
        method: 'post',
        params: {
            ids,
        }
    })
}
export const queryTrfTemplateList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/trf/trfTemplatePage',
        method: 'post',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const queryNewServiceList = (isHot,productLineCode,languageId,pin,needDate=1) => {
    return request({
        url: '/api/sgs-mart/newService/list-noPage',
        method: 'get',
        params: {
            isHot,
            productLineCode,
            languageId,
            pin,
            needDate
        }
    })
}
