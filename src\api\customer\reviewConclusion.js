import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/reviewConclusion/add',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/reviewConclusion/page/by-customerGroupCode',
        method: 'post',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/reviewConclusion/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/reviewConclusion/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}
