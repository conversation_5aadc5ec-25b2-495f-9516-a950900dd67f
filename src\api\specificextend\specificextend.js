import request from '@/router/axios';

export const submitSpecificform = (template) => {
    return request({
        url: '/api/sgs-mart/specificExtend/submit',
        method: 'post',
        data: template
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/specificExtend/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}


export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/specificExtend/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/specificExtend/detail',
        method: 'get',
        params: {
            id,
        }
    })
}



