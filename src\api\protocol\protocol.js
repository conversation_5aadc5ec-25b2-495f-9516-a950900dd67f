import request from '@/router/axios';

const post = (url,data)=>{
    return request({
        url,
        method:'post',
        data
    })
}
const sendGet = (url,data = {})=>{
    return request({
        url,
        method:'get',
        params:{
            ...data
        }
    })
}
const postForm = (url,data)=>{
    return request({
        url,
        method:'post',
        params:{
            ...data
        }
    })
}

let api = {
    queryTemplateList:(param)=>{
        return post('/api/sgs-mart/protocol/v1/template/list',param)
    },
    queryTemplateDetail:(param)=>{
      return post('/api/sgs-mart/protocol/v1/template/detail',param)
    },
    saveEditDetail:(param)=>{
        return post('/api/sgs-mart/protocol/v1/save',param)
    },
    queryPage:(param)=>{
        return post('/api/sgs-mart/protocol/v1/page',param)
    },
    queryProtocolDetail:(param)=>{
        return post('/api/sgs-mart/protocol/v1/detail',param)
    },
    handlerAction:(param)=>{
        return post('/api/sgs-mart/protocol/v1/action',param)
    },
    queryScmCustomerList:(param)=>{
        return post( '/api/sgs-mart/customer/scm/query',param)
    },
    saveAttachment: (attachment) => {
        return post('/api/sgs-mart/file/saveAttachment',attachment)
    },
    downloadFile:(cloudId)=>{
        return postForm( '/api/sgs-mart/trf/downLoadFileByCloudId',{cloudId})
    },
    deleteAttrById:(id)=>{
        return post('/api/sgs-mart/attr/deleteAttrById',{id});
    },
    updateAttrRemarkById:(param)=>{
        return post('/api/sgs-mart/attr/updateAttrRemarkById',param);
    },
    queryAttr:(param)=>{
        return post('/api/sgs-mart/attr/queryAttr',param);
    },
    saveAtt:(attachment)=>{
        return post('/api/sgs-mart/attr/saveAtt',attachment)
    },
    queryDestination:()=> {
        return sendGet('/api/sgsapi/FrameWorkApi/trims/api/v3/queryCountryAndRegion?1=1&allLanguage=yes')
    },
    workbook:(param)=>{
        return post('/api/sgs-pbm/sample/web/v1/workbook',param);
    },
}

export default api;