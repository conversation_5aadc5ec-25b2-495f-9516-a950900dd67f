<template>
  <span
    class="custom-tag"
    :style="{
      backgroundColor: color,
    }"
  >
    <slot></slot>
  </span>
</template>

<script>
export default {
  name: "Tag",
  props: {
    color: {
      type: String,
      default: "#cacaca",
    },
  },
};
</script>

<style scoped>
.custom-tag {
  color: #fff;
  height: 21px;
  padding: 3px 8px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 10px;
}
</style>
