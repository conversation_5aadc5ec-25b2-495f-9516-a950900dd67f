<template>
    <div class="dataEntryResult" id="dataEntryResult">
        <div v-if="show">
            <sgs-mart-dataentry-form
                    :show-base-info="false"
                    :trf-id="trfId"
                    :env="env"
                    :detail-signature="detailSignature"
                    :sgs-token="sgsToken"
                    :show-e-filling="showEfilling"
                    :show-test-result-to-customer="showTestResultToCustomer"
                    @sgssmart-form-e-completed="trfCompleted"
                    @sgssmart-form-e-trf-test="trfTest"
                    @sgssmart-form-e-trf-return="trfReturn"
                    @sgssmart-form-e-report-reworked="reportReworked"
                    @sgssmart-form-e-trf-report-modified="reportModified"
                    @sgssmart-form-e-trf-report-result="handleShowReportTestResult"
                    ref="frm">
                <template :slot="currentSlotName" v-if="showTestResultToCustomer">
                    <common-table
                        :data="tableOption.dataList"
                        border
                        fit
                        style="width: 100%"
                        row-key="id"
                        ref="dataentry_lululemon_table"
                        stripe
                        height="400"
                        max-height="400"
                        :size="tableOption.size"
                        :option="tableOption"
                        :filters="tableOption.filters"
                        filter-by-local
                        @sortBy="sortResultTable"
                    >
                        <template slot="result" slot-scope="{row}">
                            <el-tag
                                    size="small"
                                    v-if="row.result"
                                    :type="getDicLabel2(row).type"
                            >
                                <el-tooltip :content="row.result" placement="top">
                                    <div style="min-width: 74px;text-align: center;" class="conclusionTag">{{ $t(getDicLabel2(row).label)}}</div>
                                </el-tooltip>
                            </el-tag>
                        </template>
                    </common-table>
                </template>

            </sgs-mart-dataentry-form>

        </div>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import Cookies from 'js-cookie'
    import commonTable from "@/components/tableList/commonTable";
    import {queryTestResult} from "@/api/trf/trf"
    export default {
        name: "dataEntryResult",
        inject:['directPage'],
        data() {
            return {
                env: '',
                show:false,
                trfId: '',
                sgsToken:'',
                detailSignature:'',
                showEfilling:false,
                currentSlotName:'',
                showTestResultToCustomer:true,
                tableOption: {
                    dataList:[],
                    size: "small",
                    border: true,
                    menuShow: false,
                    filters: {},
                    showSortIcon: true,
                    sort: {
                        sortBy: "",
                        sortOrder: "",
                    },
                    index: true,
                    action: false,
                    actionWidth: 80,
                    column:[
                        {prop: "fromCustomerTrf",label: "Test Customer TRF",
                            filter: true,slot: false,width: 250,
                            type: "Select",
                            dicData:[
                                {label:'Yes',value:true,type:'success'},
                                {label:'No',value:false,type:'error'},
                            ]
                        },
                        {prop: "customerTestPackage",label: "Test Package",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "customerTestProperty",label: "Test Property",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "customerCategory",label: "Test Category",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "customerSubcategory",label: "Test Subcategory Name",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "result",label: "Test Result",
                            filter: true,slot: true,
                            width: 250, type:"Input",multiple:true,
                            dicData: [
                                {label: 'PASS', value: 'Pass',tag2:true,type:'success'},
                                {label: 'FAIL', value: 'Fail',tag2:true,type:'danger'},
                            ]
                        },
                        {prop: "pp",label: "SGS PP",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "testLine",label: "SGS TestLine",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "citation",label: "SGS Citation",filter: true,slot: false,width: 250,type: "Input"},
                        {prop: "conclusion",label: "Conclusion",filter: true,
                            slot: false,width: 250,type: "Select",multiple:true,
                            dicData:[
                                {
                                    "label": "Inconclusive",
                                    "value": "Inconclusive",
                                    tag:true,type:'info'
                                },
                                {
                                    "label": "NA",
                                    "value": "NA",
                                    tag:true,type:'info'
                                },
                                {
                                    "label": "Exempt",
                                    "value": "Exempt",
                                    tag:true,type:'info'
                                },
                                {
                                    "label": "Data Only",
                                    "value": "Data Only",
                                    tag:true,type:'info'
                                },
                                {
                                    "label": "Fail",
                                    "value": "Fail",
                                    tag:true,type:'danger'
                                },
                                {
                                    "label": "Pass",
                                    "value": "Pass",
                                    tag:true,type:'success'
                                },
                                {
                                    "label": "See Result",
                                    "value": "SEE RESULT",
                                    tag:true,type:'info'
                                }
                            ]
                        },
                    ]
                }

            }
        },
        methods:{
            init(){
                if(!this.trfId){
                    return
                }
                this.show = true;
                this.$nextTick(()=>{
                    this.$refs.frm.reload();
                })
            },
            trfCompleted(){
                location.reload();
            },
            trfTest(){
                location.reload();
            },
            trfReturn(){
                this.directPage('trfDetail')
                location.reload();
            },
            reportReworked(){
                location.reload();
            },
            reportModified(){
                location.reload();
            },
            handleShowReportTestResult(res){
                //console.log("handleShowReportTestResult",res);
                let {data,slotName} = res;
                this.currentSlotName = slotName;
                let {labCode} = this.trf.trfLab;
                //接口 调用后端
                queryTestResult(data, {labCode}).then(res=>{
                    console.log("query test result res",res)
                    if(res.status==200 && res.data){
                        let resultData = res.data.data || [];
                        this.tableOption.dataList = resultData;
                    }
                }).catch(err=>{
                    console.log("query test result err",err)
                })
            },
            getDicLabel2(row) {
                if(row.result.toUpperCase() === 'PASS'){
                    return { label: row.result, type: "success" };
                }else if(row.result.toUpperCase() === 'FAIL'){
                    return { label: row.result, type: "danger" };
                }else{
                    return { label: row.result, type: "info" };
                }
            },
            sortResultTable({prop,orderBy}){
                let originalData = JSON.parse(JSON.stringify(this.tableOption.dataList));
                //按照orderBy是desc 还是asc 还是空  对tableOption.dataList 中的prop排序
                originalData.sort((a,b)=>{
                    if(orderBy === 'Asc'){
                        return (a[prop] || '') - (b[prop] || '');
                    }
                    if(orderBy === 'Desc'){
                        return (b[prop] || '') - (a[prop] || '');
                    }
                    return 0;
                })
                this.tableOption.dataList = [];
                this.$nextTick(()=>{
                    this.tableOption.dataList = originalData;
                })
            }
        },
        created(){
            this.env =  this.$currentEnv;
            this.sgsToken = Cookies.get('sgsToken');
            let {id,signature,customerList} =this.trf;
            let buyer = (customerList||[]).find(c=>c.customerUsage==3) || {};
            this.showEfilling = buyer.customerGroupCode =='CG0000219';
            this.showTestResultToCustomer = false ;//buyer.customerGroupCode =='CG0000505';
            this.trfId = id;
            this.detailSignature = signature;
        },
        computed: {
            ...mapGetters(["token"])
        },
        mounted() {
            this.init();
        },
        watch: {},
        props: {
            trf:Object
        },
        components: {commonTable}
    }
</script>

<style lang="scss">
    .dataEntryResult{
        .bottom-btn{
            height: 70px !important;
            line-height: 70px !important;
        }
        .avue-dynamic table.el-table__body .el-table__row td .el-checkbox-group,
        .avue-dynamic table.el-table__body .el-table__row td .el-radio-group
        {
            display: flex !important;
            flex-wrap: wrap !important;
            label{
                word-break: break-all !important;;
                white-space: break-spaces !important;
                span.el-checkbox__label{
                    display: inline !important;
                }
            }

        }
        .productSample_DFFGrid{
            .el-form-item{
                margin: 10px !important;
            }
        }
    }
.queryAssociationTable{
    .el-icon-search {
        background: none !important;
        display: none !important;
        width: none !important;
        height: none !important;
        position: none !important;
        top: none !important;
        left: none !important;
    }
}


</style>
