<template>
  <basic-container>

    <el-form :inline="true" ref="formSelect" :model="formSelect" label-width="200px" size="medium">
      <el-form-item :label="$t('training.CourseTitle')"  prop="courseTitle">
        <el-input v-model="formSelect.courseTitle"></el-input>
      </el-form-item>
      <el-form-item :label="$t('training.ModulesName')"  prop="moduleName">
        <el-input v-model="formSelect.moduleName"></el-input>
      </el-form-item>
      <el-form-item :label="$t('training.TrainingCourseLevel')"  prop="courseLevel">
        <el-input v-model="formSelect.courseLevel"></el-input>
      </el-form-item>
      <el-form-item :label="$t('training.CustomerGroup')"  prop="customerGroup">
        <el-input v-model="formSelect.customerGroupName"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showTableButton">{{ $t('training.Seek') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row align="right" style="margin: 5px 0px">
      <el-button size="small" type="primary" @click="openDialogOfAdd">{{ $t('training.Add') }}</el-button>
    </el-row>

    <el-table :data="showData" stripe style="width:100%" v-loading="listLoading">
      <el-table-column type="index" label="#" width="50"> </el-table-column>
      <el-table-column prop="customerGroupName" :label="$t('training.CustomerGroup')"></el-table-column>
      <el-table-column prop="courseTitle" :label="$t('training.CourseTitle')"  ></el-table-column>
      <el-table-column prop="courseLevel" :label="$t('training.TrainingCourseLevel')"  ></el-table-column>
      <el-table-column prop="moduleName" :label="$t('training.CourseModule')"  ></el-table-column>
      <el-table-column :label="$t('training.Action')" align="center" width="200px;">
        <template scope="scope">
          <el-button type="text" size="small" icon="el-icon-edit" @click="opendialog(scope.row)">{{ $t('training.Edit') }}</el-button>
          <el-button type="text" size="small" icon="el-icon-delete" @click="handleDel(scope.row)">{{ $t('training.Delete') }}</el-button>
          <el-button type="text" size="small" icon="el-icon-paperclip" @click="openFileDialog(scope.row)">{{ $t('training.Attachment') }}</el-button>
        </template>
      </el-table-column>
    </el-table>


    <el-pagination
            @size-change="sizeChange"
            @current-change="currentChange"
            :current-page="page.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total">
    </el-pagination>

    <el-form ref="form" :model="form" label-width="200px"  class="demo-ruleForm" size="medium" :rules="rules">
      <el-drawer size="60%" title="Add Module" :visible.sync="dialogFormVisibleOfAdd">
        <el-form-item :label="$t('training.CourseTitle')" prop="courseTitle" >
          <!--<el-input v-model="form.courseTitle"></el-input>-->
          <el-select v-model="form.courseTitle" @change="selectTitleChange" :placeholder="$t('training.CourseTitle')" style="width: 100%">
            <el-option v-for="item in courseList" :key="item.id" :lable="item.id" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('training.TrainingCourseLevel')">
          <el-input v-model="form.courseLevel" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('training.CourseIntrodution')" >
          <el-input type="textarea" :rows="6" v-model="form.courseIntroduction" disabled></el-input>
        </el-form-item>
        <el-form-item :label="$t('training.CustomerGroup')" prop="customerGroupName">
          <el-select v-model="form.customerGroupName" @change="selectRoleChange" :placeholder="$t('training.CustomerGroup')" style="width:100%">
            <el-option v-for="item in pris" :key="item.id" :lable="item.id" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('training.TrainingCourseModules')"  prop="moduleName">
          <el-input v-model="form.moduleName"></el-input>
        </el-form-item>
        <el-form-item :label="$t('training.TrainingMaterials')">
          <el-upload
                  class="upload-demo"
                  action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                 :on-change="uploadChange"
                  :on-success="handleAvatarSuccess"
                  :file-list="fileList">
            <el-button size="small" type="primary">{{ $t('training.Browse') }}</el-button>
            <!--<div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>-->
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('training.Save') }}</el-button>
          <el-button type="primary" @click="resetForm('form')">{{ $t('training.Clear') }}</el-button>
          <el-button type="" @click="dialogFormVisibleOfAdd=false">{{ $t('training.Close') }}</el-button>
        </el-form-item>
      </el-drawer>
      <el-drawer size="60%" title="Edit Module" :visible.sync="dialogFormVisible">
        <el-form ref="formDialog" :model="formDialog" label-width="120px" size="small" :rules="rules">
          <el-form-item :label="$t('training.CourseTitle')" prop="courseTitle" >
            <el-input v-model="formDialog.courseTitle" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('training.TrainingCourseLevel')">
            <el-input v-model="formDialog.courseLevel" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('training.CourseIntrodution')" >
            <el-input type="textarea" :rows="6" v-model="formDialog.courseIntroduction" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('training.CustomerGroup')"  prop="customerGroupName" >
            <el-select v-model="formDialog.customerGroupName" @change="selectRoleChange" style="width: 100%;">
              <el-option v-for="item in pris" :key="item.id" :lable="item.id" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('training.ModulesName')" prop="moduleName">
            <el-input v-model="formDialog.moduleName"></el-input>
          </el-form-item>
        </el-form>
        <div class="bottom clearfix " style="text-align: center">
          <el-button size="small" @click="dialogFormVisible = false">{{ $t('training.Close') }}</el-button>
          <el-button size="small" type="primary" @click="updDailog">{{ $t('training.Save') }}</el-button>
        </div>
      </el-drawer>
      <el-drawer size="60%" :title="$t('training.TrainingMaterials')" :visible.sync="dialogFileVisible">
        <ui>
          <li v-for="item in fileDialogFileUrlList" >
            <a class='download' :href='item.moduleMaterialFileUrl' download=""  title="下载">{{ item.moduleMaterialFileUrlName }}</a>
            <el-divider></el-divider>
          </li>
        </ui>
      </el-drawer>
    </el-form>

  </basic-container>
</template>


<script>
    import {add, sel_group, showTable2, handleDel2, selModuleById, updModuleById, selModuleMaterialFileUrl} from "@/api/training/module";
    import {Get_Course,selCourseModule} from "@/api/training/class";
    export default {
        name: "Dashbord",
        data() {
            return {
                name: "course",
                form: {},
                formDialog: {},
                formSelect: {},//查询form
                showData:[],
                listLoading: false,
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                pris: '',//group
                groupId:'',
                groupName:'',
                uploadFileList:[],
                uploadFileName:[],
                courseList:'',   //课程标题select使用
                courseId:'',   //课程标题select使用
                dialogFormVisible:false,     //模态框是否显示
                dialogFormVisibleOfAdd:false,     //添加模态框是否显示
                dialogFileVisible:false,     //查看文件模态框是否显示
                fileDialogFileUrlList:'',    //文件展示列表，后台已转换，可直接下载
                addLoading: false,       //是否显示loading
                rules:{
                    courseTitle :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    customerGroupName :[{required: true, message: 'Please enter', trigger: 'blur'}],
                    moduleName :[{required: true, message: 'Please enter', trigger: 'blur'}],
                },
            }
        },
        created(){
            //this.getUsers();
            this.showTable(this.currentPage,this.pageSize);
            this.selectRoleList();//group
            this.selectCourseList();//课程列表
        },
        inject: ["reload"], //注入reload方法
        methods: {
            //获取客户组
            selectRoleList() {//初始化下拉框动态数据
                let obj = [];
                sel_group({//封装的axios请求，大家可修改为自己的请求

                }).then(res => {
                    //console.log(res);
                    res.data.data.forEach((item, index) => {//关键的是将后台返回的数据进行遍历，并封装好
                        obj.push({
                            id: item.customerGroupCode,//id必须品啊
                            name: item.customerGroupName//name必须品啊
                        });
                    });
                });
                this.pris = obj;
                return obj;
            },
            showTableButton(){
                this.showTable(this.currentPage,this.pageSize);
            },
            selectTitleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.courseList.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.courseId = obj.id;
                this.form.courseId = obj.id;
                this.form.courseLevel = obj.courseLevel;//回填表单信息，展示用
                this.form.courseIntroduction = obj.courseIntroduction;
                var objOfModule = [];
                selCourseModule({
                    courseId:this.courseId
                }).then(res => {
                    //console.log(res);
                    res.data.data.forEach((item, index) => {
                        objOfModule.push({
                            id: item.id,
                            name: item.moduleName,
                            courseLevel : item.courseLevel,
                            courseIntroduction : item.courseIntroduction
                        });
                    });
                });
                this.courseModuleList = objOfModule;
                return objOfModule;
                //this.courseLevelId2 = obj.name;//用于回显名称*/
            },
            selectCourseList() {//初始化课程下拉框动态数据
                let obj = [];
                Get_Course({

                }).then(res => {
                    console.log(res);
                    res.data.data.forEach((item, index) => {
                        obj.push({
                            id: item.id,
                            name: item.courseTitle,
                            courseLevel : item.courseLevel,
                            courseIntroduction : item.courseIntroduction,
                        });
                    });
                });
                this.courseList = obj;
                return obj;
            },
            opendialog:function(id){    //打开模态框
                selModuleById(id).then(res => {
                    console.log(res);
                    this.formDialog = res.data.trainingModule;
                    //this.formDialog.s
                });
                this.dialogFormVisible = true
            },
            openFileDialog:function(id){    //打开文件模态框
                selModuleMaterialFileUrl(id).then(res => {
                    console.log(res);
                    this.fileDialogFileUrlList = res.data;
                    //this.formDialog.s
                });
                this.dialogFileVisible = true
            },
            openDialogOfAdd:function(){
                this.dialogFormVisibleOfAdd = true
            },
            updDailog:function(){    //保存修改，并关闭模态框
                this.$refs['formDialog'].validate((valid) => {
                    if (!valid) {
                        return false;
                    } else {
                        updModuleById(this.formDialog).then(() => {
                            this.isDisabled = false;
                            this.$message({
                                type: "success",
                                message: this.$t('training.api.success')
                            });
                            this.showTable("", "");
                            this.dialogFormVisible = false;
                        }, error => {
                            console.log(error);
                        });
                    }
                })
            },
            handleAvatarSuccess(res, file) {
                //console.log(res);
                //this.uploadFileList.push(URL.createObjectURL(file.raw));
                this.uploadFileList.push(res.data[0].cloudID);
                this.uploadFileName.push(res.data[0].attachmentName);
                //console.log(this.uploadFileList);
                //console.log(this.uploadFileNameList);
              this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('uploadSuccess'),
                    type: 'warning'
                });
            },
           uploadChange(file, fileList) {
                if(file.status == 'ready'){
                    //开启loading效果
                    this.uploadLoading=true;
                }else{
                      this.uploadLoading=false;
                }
            },
            onSubmit() {
                this.$refs['form'].validate((valid) => {
                    if (!valid) {
                            return false;
                        }else {
                            this.form.customerGroupName = this.groupName;
                            this.form.customerGroupId = this.groupId;
                            this.form.uploadFileList = this.uploadFileList.toString();

                            add(this.form).then(() => {
                                this.isDisabled = false;
                                this.$message({
                                    type: "success",
                                    message: this.$t('training.api.success')
                                });
                                this.showTable("","");
                                this.dialogFormVisibleOfAdd = false;
                                this.resetForm('form');
                            }, error => {
                                console.log(error);

                            });
                        }
                    })
            },
            resetForm(searchForm) {
                this.$refs[searchForm].resetFields()
            },
            handleDel(id){
                handleDel2(id).then(() => {
                    this.isDisabled = false;
                    this.$message({
                        type: "success",
                        message: this.$t('training.api.success')
                    });
                    this.showTable("","");
                }, error => {
                    console.log(error);

                });
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.showTable();
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.showTable();
            },
            showTable(currentPage,pageSize){
                this.listLoading = true;
                showTable2(currentPage,pageSize,this.formSelect).then(result => {
                    console.log(result);
                    this.listLoading = false;
                    this.showData = result.data.data;
                    this.page.total = Number(result.data.msg)
                });
            },
            selectRoleChange(val) {//回显关键性代码，每一次选择后执行，val是上面dom的value值
                var obj = {};
                obj = this.pris.find(function (item) {//obj是选中的对象
                    return item.name === val;
                });
                this.groupId = obj.id;//提交数据使用，此处可忽略
                this.groupName = obj.name;//用于回显名称
            },
            toClassList(){
                this.$router.push({path: '/training/classList'})
            },
        },
    }
</script>

<style scoped>

</style>
