import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomerTrade/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomerTrade/detailTradeInfo',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomerTrade/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomerTrade/saveForm',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomerTrade/updateForm',
    method: 'post',
    data: row
  })
}

