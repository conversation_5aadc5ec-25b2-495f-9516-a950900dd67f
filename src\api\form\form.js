import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/sgs-form/form/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const remove = (ids) => {
  return request({
    url: '/api/sgs-form/form/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sgs-form/form/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sgs-form/form/submit',
    method: 'post',
    data: row
  })
}

export const formDataAdd = (row) => {
    return request({
        url: '/api/sgs-form/form/data/submit',
        method: 'post',
        data: row
    })
}

export const getFormDataList = (current, size, formCode ,params) => {
    return request({
        url: '/api/sgs-form/form/data/'+formCode+'/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getFormOption = (formCode) => {
    return request({
        url: '/api/sgs-form/form/'+formCode+'/option',
        method: 'get'
    })
}

export const getFormDetail = (formCode) => {
    return request({
        url: '/api/sgs-form/form/'+formCode+'/detail',
        method: 'get'
    })
}
export const getFormList = (formCodesStr) => {
    return request({
        url: '/api/sgs-form/form/formListByCodes',
        method: 'get',
        params: {
            formCodesStr,
        }
    })
}
