<template>
  <avue-form :option="optionCom" v-model="form" ref="crud" @submit="rowSave">

  </avue-form>
</template>
<script>
//import option from "@/option/cpscCustomerTrade/cpscCustomerTrade";
import { add } from "@/api/cpscCustomerTrade/cpscCustomerTrade";
import { getStore } from '@/util/store'
export default {
  props: {
    cpscCustomerId: String,
    type: String
  },
  data() {
    return {
      vali_alternateId: '',
      vali_gln: '',
      option: {
        menuTitle: this.$t('work.columnAction'),
        labelWidth: '140',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "组织id",
            prop: "tenantId",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "数据版本",
            prop: "tradeVersion",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "数据编号",
            prop: "tradeId",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "数据时效性;1为使用中，0为过时，2为已失效，3为临时",
            prop: "status",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "客户ID;cpsc系统中的客户id",
            prop: "cpscCustomerId",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeType'),

            prop: "tradeType",
            type: "select",
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=trade_type",
            dataType: "string",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            disabled: true,
            span: 24,
            hide: true,
            rules: [{
              required: true,
              message: this.$t('work.cpscCustomerTrade.column.selectType_msg'),
              trigger: "blur"
            }],
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.keyword'),
            prop: "keyword",
            type: "input",
            search: true,
            hide: true,
            display: false,
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeName'),

            prop: "tradeName",
            type: "input",
            rules: [{
              required: true,
              message: this.$t('work.cpscCustomerTrade.column.enterName_msg'),
              trigger: "blur"
            }],
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.alternateId'),

            prop: "alternateId",
            type: "input",
            hide: true,
            rules: [{
              validator: this.validateAlternateIdOrGln,
              trigger: "blur"
            }],
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.gln'),

            prop: "gln",
            type: "input",
            hide: true,
            rules: [{
              validator: this.validateAlternateIdOrGln,
              trigger: "blur"
            }],
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeAddress'),

            prop: "tradeAddress",
            type: "input",
            hide: true,
            rules: [{
              required: true,
              message: this.$t('work.cpscCustomerTrade.column.enterAddress1_msg'),
              trigger: "blur"
            },{max:50,message:this.$t('work.cpscCustomerTrade.column.enterAddress1_msg2'),trigger:'blur'}],
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.alternateId'),

            prop: "viewAlternateId",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.gln'),

            prop: "viewGln",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.telphone'),

            prop: "viewTelphone",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.email'),

            prop: "viewEmail",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.address'),

            prop: "fullAddress",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeAddress2'),

            prop: "tradeAddress2",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.apartment'),

            prop: "apartment",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeCountryId'),

            prop: "tradeCountryId",
            type: "select",
            filterable:true,
            dicUrl: "/api/sgs-e-filling/sgs-system/dict/dictionary?code=country_list_cs",
            dataType: "string",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: this.$t('work.cpscCustomerTrade.column.countryId_required_msg'),
              trigger: "blur"
            }],

            hide: true,
          },
          {
            label: "国家",
            prop: "tradeCountryName",
            type: "input",
            hide: true,
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeProvince'),

            prop: "tradeProvince",
            type: "input",
            hide: true,
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradeCity'),

            prop: "tradeCity",
            type: "input",
            hide: true,
            rules: [{
              required: true,
              message: this.$t('work.cpscCustomerTrade.column.cityName_required_msg'),
              trigger: "blur"
            }],
          },
          {
            label: this.$t('work.cpscCustomerTrade.column.tradePostalCode'),

            prop: "tradePostalCode",
            type: "input",
          },
          {
            label: "web代表网页创建，api代表接口同步来的",
            prop: "dataSource",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "关联id",
            prop: "tradeContactId",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
      label: this.$t('work.cpscCustomerTrade.column.telphone'),
       
      prop: "telphone",
      type: "input",
      hide: true,
      placeholder:'+86 18888888888',
      rules:[{required:true,  message: this.$t('work.cpscCustomerTrade.column.requireTelphone')},{
        pattern: /^(\+\d{1,3} \d{11}|\d{11})$/,
        message: this.$t('work.cpscCustomerTrade.column.validPhone'),
        trigger: 'blur'
      }]
    },
    {
      label: this.$t('work.cpscCustomerTrade.column.email'),
       
      prop: "email",
      type: "input",
      hide: true,
      rules:[{required:true, message: this.$t('work.cpscCustomerTrade.column.requireEmail')}, { 
        pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
        message: this.$t('work.cpscCustomerTrade.column.validEmail'),
        trigger: 'blur'
      }]
    },

          {
            label: "联系人数据版本",
            prop: "contactVersion",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "联系人数据编号",
            prop: "contactId",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "是否已删除，系统默认字段",
            prop: "isDeleted",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建部门，系统默认字段",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            display: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ]
      },
      form: {
        tradeType: this.type
      }
    }
  },
  mounted() {
    this.setOption()
  },
  computed: {
    optionCom() {
      return this.option
    },
  },
  methods: {
    validateAlternateIdOrGln(rule, value, callback, source) {
      if (rule.field == "gln") {
        this.vali_gln = value
      } else {
        this.vali_alternateId = value
      }
      if ((this.vali_alternateId == null || this.vali_alternateId == '' || this.vali_alternateId == undefined) && (this.vali_gln == null || this.vali_gln == '' || this.vali_gln == undefined)) {
        callback(new Error(this.$t('work.cpscCustomerTrade.column.alternateId_gln_required_msg')));
      } else {
        callback();
      }
    },
    setOption() {
      //const column = this.findObject(this.option.column, 'tradeType')
      const column = this.option.column.find(item=>item.prop==='tradeType')
      column.disabled = true
    },
    rowSave() {
      add({ ...this.form, cpscCustomerId: this.cpscCustomerId }).then(() => {

        this.$message({
          type: "success",
          message: this.$t("work.operationSuccessful")
        });
        this.$emit('closeCommenDialog', this.type,this.form.tradeName)
      }, error => {

        window.console.log(error);
      });
    },
  }
}
</script>
<style></style>