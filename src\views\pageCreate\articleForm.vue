<template>
    <div class="articleForm">

        <el-form :model="props.form" @submit.native.prevent label-position="right" :label-width="labelWidth"
            :rules="rules" ref="formRef">

            <el-card class="article-card">
                <el-form-item :label="$t('work.pageCreate.title') + ':'" style="width: 95%;" prop="title" required>
                    <el-input type="textarea" :placeholder="$t('work.pageCreate.enterTitle')" :rows="2"
                        v-model="props.form.title" maxlength="150" show-word-limit></el-input>
                </el-form-item>
                <el-form-item :label="$t('work.pageCreate.attachments') + ':'" style="width: 95%"
                    class="attachment-form-item">

                    <div class="attachment-list">
                        <el-upload class="attachment-upload" :show-file-list="false"
                            :before-upload="beforeAttachmentUpload" :http-request="handleUploadDialogFiles"
                            :multiple="true" ref="uploadRef" :auto-upload="false"
                            :style="{ marginTop: props.form.knowledgeAttachmentDTOList.length ? '0px' : '10px' }">
                            <el-button
                                :style="{ borderRadius: '3px', width: locale == 'zh-CN' ? '126px' : '150px', height: '36px' }"
                                type="primary" size="default" @click.stop="openUploadDialog">
                                <img src="/pageCreate/upload.svg"
                                    style="width: 14px; height: 14px; margin-right: 5px;" />
                                {{ $t('work.pageCreate.addAttachment') }}</el-button>
                        </el-upload>
                        <div v-if="!props.form.knowledgeAttachmentDTOList?.length" class="no-attachment">
                            {{ $t('work.pageCreate.noAttachments') }}
                        </div>
                        <div v-else v-for="(item, index) in props.form.knowledgeAttachmentDTOList" :key="index"
                            class="attachment-item">
                            <span class="attachment-name" @click="openFile(item.cloudId)">
                                <img :src="getFileIcon(item.fileName)"
                                    style="width: 16px;height: 16px;margin-right: 5px;">
                                {{ item.fileName }}
                            </span>
                            <el-icon class="delete-icon" @click="handleDeleteAttachment(index)">
                                <CircleCloseFilled />
                            </el-icon>
                            <!-- <el-switch v-model="item.authorization" :active-value="1" :inactive-value="0" /> -->
                        </div>

                    </div>

                </el-form-item>
                <el-form-item :label="$t('work.pageCreate.articleContent') + ':'" style="width: 95%;margin-top: 30px;">
                    <div class="content-container" style="border: 1px solid #ccc">
                        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef"
                            :defaultConfig="toolbarConfig" :mode="mode" />
                        <Editor style="height: 500px; overflow-y: hidden;" v-model="props.form.articleFormContent"
                            :defaultConfig="editorConfig" :mode="mode" @onCreated="handleCreated" />
                    </div>
                </el-form-item>
            </el-card>


        </el-form>
        <UploadDialog v-model="uploadDialogVisible" :attachments="props.form.knowledgeAttachmentDTOList"
            :limit="['.jpg', '.jepg', '.gif', '.png', '.bmp', '.pdf', '.docx', '.xlsx']"
            @update:attachments="updateAttachments" type="attachment" />
    </div>
</template>

<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, ref, shallowRef, watch, computed } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { uploadFile } from '@/api/upload'
import { ElMessage } from 'element-plus'
import Cookies from 'js-cookie'
import { apiHost } from '@/utils/util'
import crypto from '@/utils/crypto'
import { getCloudFileURL } from '@/api/getCloudFileUrl.ts'
import { useI18n } from 'vue-i18n'
import UploadDialog from './components/UploadDialog.vue'
import { i18nChangeLanguage } from '@wangeditor/editor'
//import { DomEditor } from '@wangeditor/editor'

const { t, locale } = useI18n()
const labelWidth = computed(() => {
    if (locale.value == 'zh-CN') {
        return '80px'
    } else if (locale.value == 'en-US') {
        return '120px'
    }
})

// Form validation related
const formRef = ref()
const rules = computed(() => {
    return {
        title: [
            { required: true, message: t('work.pageCreate.pleaseEnter') + t('work.pageCreate.title'), trigger: 'blur' }
        ]
    }
})
// Make validate method available for parent component
defineExpose({
    validate: () => {
        if (formRef.value) {
            return formRef.value.validate()
        }
        return Promise.resolve(true)
    }
})

// Set Wang Editor language based on current app locale
const setEditorLanguage = () => {
    //debugger
    // Map your app locales to Wang Editor locales ('zh-CN' or 'en')
    const langMap: Record<string, string> = {
        'zh-CN': 'zh-CN',
        'en-US': 'en'
    }

    // Convert your application locale to Wang Editor's supported locale
    const editorLang = langMap[locale.value] || 'zh-CN'

    // Set the Wang Editor language
    i18nChangeLanguage(editorLang)
}

// Set editor language when component is created
setEditorLanguage()


// Watch for language changes and update the editor language
watch(locale, () => {


    // Change the Wang Editor's built-in language
    setEditorLanguage()

    // Update both toolbar and editor configs for the new language
    // updateEditorLanguageConfigs()
})

const props = defineProps({
    form: {
        type: Object,
        default: () => ({
            knowledgeAttachmentDTOList: []
        })
    }
})

// Initialize knowledgeAttachmentDTOList if it doesn't exist
if (!props.form.knowledgeAttachmentDTOList) {
    props.form.knowledgeAttachmentDTOList = []
}

const beforeAttachmentUpload = (file: File) => {
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
        ElMessage.error(t('work.pageCreate.fileSizeLimit'))
        return false
    }
    return true
}

// For attachment upload dialog
const uploadDialogVisible = ref(false)
const uploadRef = ref()

const openUploadDialog = (event: Event) => {
    event.preventDefault()
    event.stopPropagation()
    uploadDialogVisible.value = true
}

const updateAttachments = (newAttachments: any[]) => {
    props.form.knowledgeAttachmentDTOList = newAttachments
}

const handleUploadDialogFiles = () => {
    // This function won't be called directly now, but kept for compatibility
    return false
}

const handleDeleteAttachment = (index: number) => {
    props.form.knowledgeAttachmentDTOList.splice(index, 1)
}

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

// 内容 HTML
//const valueHtml = ref('<p>hello</p>')

// 模式
const mode = ref('default')

// 获取请求头
const getHeaders = () => {
    const timestamp = new Date().valueOf()
    const sign = crypto.encrypt(timestamp, 'cnsgsmart-uat.sgs.net')
    const sgstoken = Cookies.get('sgsToken')
    return {
        // 上传文件时不需要设置 Content-Type，让浏览器自动设置为 multipart/form-data
        'sgs-system-id': 59,
        'sgs-system-name': 'CPSC E-Filing',
        sign,
        timestamp,
        sgstoken,
    }
}

// 工具栏配置
const toolbarConfig = {
    excludeKeys: [],

    // Create a function to get all localized toolbar button texts
    getLocale: () => {
        return {
            // Text for toolbar buttons
            bold: { title: t('work.pageCreate.bold') },
            italic: { title: t('work.pageCreate.italic') },
            underline: { title: t('work.pageCreate.underline') },
            through: { title: t('work.pageCreate.strikeThrough') },
            code: { title: t('work.pageCreate.code') },
            insertLink: { title: t('work.pageCreate.insertLink') },
            insertImage: { title: t('work.pageCreate.insertImage') },
            uploadImage: { title: t('work.pageCreate.uploadImage') },
            insertVideo: { title: t('work.pageCreate.insertVideo') },
            uploadVideo: { title: t('work.pageCreate.uploadVideo') },
            insertTable: { title: t('work.pageCreate.insertTable') },
            headerSelect: { title: t('work.pageCreate.headerStyle') },
            fontSize: { title: t('work.pageCreate.fontSize') },
            fontFamily: { title: t('work.pageCreate.fontFamily') },
            lineHeight: { title: t('work.pageCreate.lineHeight') },
            bulletedList: { title: t('work.pageCreate.bulletedList') },
            numberedList: { title: t('work.pageCreate.numberedList') },
            justify: { title: t('work.pageCreate.justifyLeft') },
            justifyCenter: { title: t('work.pageCreate.justifyCenter') },
            justifyRight: { title: t('work.pageCreate.justifyRight') },
            justifyJustify: { title: t('work.pageCreate.justifyJustify') },
            indent: { title: t('work.pageCreate.increaseIndent') },
            delIndent: { title: t('work.pageCreate.decreaseIndent') },
            color: { title: t('work.pageCreate.textColor') },
            bgColor: { title: t('work.pageCreate.backgroundColor') },
            clearStyle: { title: t('work.pageCreate.clearStyle') },
            undo: { title: t('work.pageCreate.undo') },
            redo: { title: t('work.pageCreate.redo') }
        }
    }
}

// 编辑器配置
const editorConfig = {
    placeholder: t('work.pageCreate.enterContent'),
    MENU_CONF: {
        uploadImage: {
            server: `${apiHost}/sgsapi/FrameWorkApi/file/uploadPublicFile`,
            fieldName: 'file',
            headers: getHeaders(),
            maxFileSize: 10 * 1024 * 1024, // 10M
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            // 自定义上传
            customUpload(file: File, insertFn: any) {
                // 创建 FormData
                const formData = new FormData()
                formData.append('file', file)
                formData.append('systemID', '59')
                // 发起上传请求
                uploadFile(formData)
                    .then((res: any) => {
                        if (res?.rows && res.rows.length > 0) {
                            // 上传成功，插入图片到编辑器
                            insertFn(res.rows[0])
                        }
                    })
            }
        },
        uploadVideo: {
            server: `${apiHost}/sgsapi/FrameWorkApi/file/uploadPublicFile`,
            fieldName: 'file',
            headers: getHeaders(),
            maxFileSize: 100 * 1024 * 1024, // 100M
            maxNumberOfFiles: 5,
            allowedFileTypes: ['video/*'],
            // 自定义上传
            customUpload(file: File, insertFn: any) {
                // 创建 FormData
                const formData = new FormData()
                formData.append('file', file)
                formData.append('systemID', '59')
                // 发起上传请求
                uploadFile(formData)
                    .then((res: any) => {
                        if (res?.rows && res.rows.length > 0) {
                            // 上传成功，插入视频到编辑器
                            insertFn(res.rows[0])
                        }
                    })
            }
        }
    }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const handleCreated = (editor: any) => {
    editorRef.value = editor // 记录 editor 实例，重要！

    // Apply localization to the editor
    setEditorLanguage()

    // Make sure the placeholder and other language-dependent configs are set correctly
    // updateEditorLanguageConfigs()
}

const openFile = (fileId: string) => {
    getCloudFileURL({ cloudID: fileId, systemID: 1, networkType: 2 }).then((res: any) => {
        window.open(res)
    })
}

// Function to get appropriate icon based on file type
const getFileIcon = (fileName: string) => {
    if (!fileName) return '/pageCreate/file-default.svg'

    const type = fileName.toLowerCase()
    if (type.includes('image') || ['.jpg', '.jpeg', '.png', '.gif', '.bmp'].some(ext => type.includes(ext))) {
        return '/pageCreate/imgIcon.svg'
    } else if (type.includes('pdf') || type.includes('.pdf')) {
        return '/pageCreate/pdfIcon.svg'
    } else if (type.includes('word') || type.includes('docx')) {
        return '/pageCreate/wordIcon.svg'
    } else if (type.includes('excel') || type.includes('sheet') || type.includes('xlsx')) {
        return '/pageCreate/excelIcon.svg'
    }

    return '/pageCreate/file-default.svg'
}
</script>

<style lang="scss">
.articleForm {
    height: 100%;
    overflow-y: auto;

    .content-container {
        border-radius: 5px;
    }

    .article-card {

        padding-top: 30px !important;
        padding-left: 30px !important;

        .el-form-item {
            margin: 5px 0px;
        }
    }

    .el-card {
        width: 100%;

        .attachment-form-item {
            margin-top: 20px;

            .el-form-item__label {
                height: 80%;
                align-items: center;
            }

            .el-form-item__content {
                display: flex;
                align-items: baseline;
                gap: 10px;
            }
        }

        .el-card__body {
            .content-container {
                width: 100%;
                height: calc(100vh - 81px); // Adjust this value based on your layout
                // min-height: 400px; // Minimum height
                // max-height: 800px; // Maximum height
                // overflow-y: auto;
                padding: 10px;

                &.w-e-full-screen-container {
                    z-index: 99;
                }
            }

            width: 100%;
            padding: 10px 10px;

            .el-form-item {
                margin-bottom: 0px;
            }

            .attachment-list {
                margin-bottom: 10px;
                display: flex;
                column-gap: 10px;
                flex-wrap: wrap;

                .no-attachment {
                    color: #909399;
                    font-size: 14px;
                    padding: 10px 30px;
                }

                .attachment-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0px 8px;
                    // background: #f5f7fa;
                    margin-bottom: 8px;
                    border-radius: 4px;

                    .attachment-name {
                        display: flex;
                        align-items: center;
                        flex: 1;
                        margin-right: 10px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        color: #256DE1;
                        cursor: pointer;
                    }

                    .delete-icon {
                        cursor: pointer;
                        color: rgb(179, 179, 179);
                        margin: 0 10px;

                        &:hover {
                            opacity: 0.8;
                        }
                    }
                }
            }

            flex-wrap: wrap;

        }
    }
}

// Styles for upload dialog
.upload-dialog-content {
    max-height: 300px;
    overflow-y: auto;

    .no-files {
        text-align: center;
        color: #909399;
        font-size: 14px;
        padding: 20px 0;
        margin-bottom: 10px;
    }

    .selected-file-item {
        margin-left: 16%;
        width: 78%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        //background: #f5f7fa;
        margin-bottom: 8px;
        border-radius: 4px;
        cursor: pointer;

        .delete-icon {
            display: none;
        }

        &:hover {
            background: #f5f7fa;

            .delete-icon {
                font-size: 20px;
                display: inline-block;
                cursor: pointer;
                color: rgb(179, 179, 179);
                margin: 0 10px;

                &:hover {
                    opacity: 0.8;
                }
            }
        }

        .file-name {
            display: flex;
            align-items: center;
            flex: 1;
            margin-right: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }


    }

    .file-selector {
        margin-top: 15px;
        padding: 15px;
        // border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 40px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        // &:hover{
        //             color: rgb(255,102,0);
        //             background-color: #ccc;
        //         }
    }
}
</style>
