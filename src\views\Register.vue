<template>
  <div class="container">
    <router-view></router-view>
    <Footer class="footer"></Footer>
  </div>
</template>

<script lang="ts" setup>
import Footer from './register/foot.vue';
</script>

<style lang="scss" scoped>
@use '@/assets/style/unit.module.scss' as *;
.container {
  background: url("/img/loginBackground.png") center no-repeat;
  background-size: cover;
  width: 100%;
  min-height: 100vh;
  padding: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
