import request from '@/router/axios';

export const add = (form) => {
    return request({
        url: '/api/sgs-mart/guestbooks/submit',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/guestbooks/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
