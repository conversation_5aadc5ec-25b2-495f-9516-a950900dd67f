<template>
    <el-popconfirm
            :confirm-button-text="confirmButtonText"
            :cancel-button-text="cancelButtonText"
            :icon="iconName"
            :icon-color="iconColor"
            :title="t(title)"
            @confirm="handlerConfirm"
            @cancel="handlerCancel"
    >
        <template #reference>
            <el-button text :type="refButtonType" :size="refButtonSize">
                <slot ></slot>
            </el-button>
        </template>
    </el-popconfirm>
</template>


<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'
import {InfoFilled} from '@element-plus/icons-vue'

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)
defineOptions({
    name: 'index'
})
const emits = defineEmits(['confirm','cancel'])
const props = defineProps({
    confirmButtonText:{
        type:String,
        default:'Yes'
    },
    cancelButtonText:{
        type:String,
        default:'No'
    },
    title:{
        type:String,
        required:true
    },
    iconName:{
      type:Object,
      default:InfoFilled
    },
    iconColor:{
        type:String,
        default:'#FF6600'
    },
    refButtonType:{
        type:String,
        default:'primary'
    },
    refButtonSize:{
        type:String,
        default:'mini'
    }
})
const handlerConfirm = ()=>{
    emits('confirm')
}
const handlerCancel = ()=>{
    emits('cancel')
}
watch(language, (newVal) => {
})
onMounted(() => {
})
</script>

<style lang="scss">
.smart_views_index {

}
</style>