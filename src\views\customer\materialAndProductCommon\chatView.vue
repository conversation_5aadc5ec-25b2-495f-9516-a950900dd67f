<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart__chatView" id="sgs_smart__chatView">
            <el-dialog
                    :visible.sync="showChat"
                    v-dialog-drag
                    width="60%"
                    title="Comment"
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    @close="$emit('cancelDia')"
            >
                <div class="chat_content">
                    <el-table
                            ref="commentTable"
                            v-loading="loading"
                            style="width: 100%"
                            class="comment_chat_table"
                            :data="commentDataList"
                            :border="false"
                            size="mini"
                            :show-header="false"
                            empty-text="No Data"
                            fit
                    >
                        <el-table-column
                                prop="content"
                                label=""
                        >
                            <template slot-scope="{row}">
                                <el-row>
                                    <el-col :offset="leftOrRight(row)=='right' ? 10:0" :span="14">
                                        <div :class="'comment_table_chat_cell_'+leftOrRight(row)">
                                            <el-row>
                                                <el-col :span="20">
                                                    {{row.createUser}}
                                                    {{transTime(row.createTime)}}
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col>
                                                    <div style="white-space: pre-wrap">{{row.content}}</div>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-col>
                                </el-row>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-row>
                    <el-col>
                        <el-form ref="commentForm" :model="commentForm" @submit.native.prevent>
                            <el-form-item prop="content">
                                <el-input
                                        type="textarea"
                                        :rows="3"
                                        clearable
                                        show-word-limit
                                        name="Content"
                                        maxlength="5000"
                                        class="comment_textarea"
                                        style="width: calc(100% - 200px)"
                                        size="medium"
                                        placeholder="comment"
                                        v-model="commentForm.content"
                                ></el-input>
                                <el-button
                                        style="width: 100px"
                                        type="primary"
                                        size="medium"
                                        @click="sendComment"
                                        :loading="saveBtnLoading">
                                    Send
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col v-if="needSendEmail">
                        <el-form label-position="left" ref="emailForm" :model="commentForm" label-width="140px">
                            <el-form-item label="Notification to">
                                <el-select
                                        multiple
                                        filterable
                                        allow-create
                                        default-first-option
                                        v-model="commentForm.mailGroup"
                                        style="width: calc(100% - 200px)"
                                        @change="changeEmailGroup"
                                        placeholder="Email group">
                                    <el-option v-for="(e,ind) in NotificationTo"
                                               :key="'eg_'+ind"
                                               :label="e.display"
                                               :value="e.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label=" ">
                                <el-select
                                        multiple
                                        filterable
                                        allow-create
                                        default-first-option
                                        v-model="commentForm.mailList"
                                        @change="changeMailList"
                                        style="width: calc(100% - 200px)"
                                        placeholder="Email address">
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
    import {tzFormChina} from "@/util/datetimeUtils";
    import api from "../../../api/newSamples";
    export default {
        name: "chatView",
        inject:['getMainObjectId'],
        data() {
            return {
                pageLoading: false,
                showChat: false,
                saveBtnLoading:false,
                loading:false,
                commentDataList:[],
                NotificationToMap:{},
                NotificationTo:[],
                oldMailGroup:[],
                commentForm:{
                    content:'',
                    mailGroup:[],
                    mailList:[]
                }
            }
        },
        methods: {
            initPage() {
                let param = {
                    objectId:this.objectId,
                    objectType:this.objectType
                }
                this.loading = true;
                api.queryCommentList(param).then(res=>{
                    this.loading = false;
                    if(res.status==200 && res.data && res.data.data){
                        this.commentDataList = res.data.data;
                        this.scrollToBottom();
                    }
                }).catch(err=>{
                    console.log("查询到的err",err)
                })
                this.showChat = true;
            },
            initNotification(){
                api.queryNotification(this.getMainObjectId()).then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        let {data} = res.data;
                        (data || []).forEach(da=>{
                            let {code,display,mailList} = da;
                            this.NotificationTo.push({code,display});
                            this.NotificationToMap[code] = mailList;
                        })
                    }
                }).catch(err=>{
                    console.log("queryNotification err",err);
                })
            },
            sendComment(){
                if(!this.commentForm.content){
                    return
                }
                let param = {
                    objectId:this.objectId,
                    objectType:this.objectType,
                    content:this.commentForm.content,
                    mailList:this.commentForm.mailList,
                    mainId:this.getMainObjectId()
                }
                this.saveBtnLoading = true;
                api.addComment(param).then(res=>{
                    if(res.status==200){
                        this.$notify.success('Success');
                        this.$emit('update')
                        this.resetPage();
                    }
                    this.saveBtnLoading = false;
                }).catch(err=>{
                    console.log("send comment err",err);
                    this.saveBtnLoading = false;
                    this.$notify.error("Send Error")
                })
            },
            resetPage(){
                this.commentForm.content = '';
                if(this.needSendEmail){
                    this.commentForm.mailGroup=[];
                    this.oldMailGroup = [];
                    this.commentForm.mailList=[];
                }
                this.initPage();
            },
            changeMailList(val){
                console.log("changeMailList",val)
                let emailList = val;
                if(val && val.length>0){
                    const re = /^[\w\.\-]+@([\w\-]+\.)+[\w\-]+$/;
                    emailList = val.filter(v=>re.test(v));
                }
                this.commentForm.mailList = [...emailList];
            },
            changeEmailGroup(){
                let {mailGroup} = this.commentForm;
                //当前选项比上一次选择得少，进行了删除
                if(this.oldMailGroup.length>mailGroup.length){
                    let deleteGroup = this.oldMailGroup.filter(m=>!mailGroup.includes(m));
                    let dg = deleteGroup[0]
                    let delMailList = this.NotificationToMap[dg];
                    //进行删除
                    delMailList.forEach(dm=>{
                        let delIndex = this.commentForm.mailList.findIndex(ml=>ml==dm);
                        if(delIndex>-1){
                            this.commentForm.mailList.splice(delIndex,1);
                        }
                    })
                    this.oldMailGroup = [...mailGroup];
                    return;
                }
                //当前选项比上一次得多，进行追加
                let addGroup = mailGroup.filter(m=>!this.oldMailGroup.includes(m));
                let ag = addGroup[0];
                let addMailList = this.NotificationToMap[ag];
                addMailList.forEach(am=>{
                    let addIndex = this.commentForm.mailList.findIndex(ml=>ml==am);
                    if(addIndex==-1){
                        this.commentForm.mailList.push(am);
                    }
                });
                this.oldMailGroup = [...mailGroup];
            },
            isInternal(){
                return !this.userInfo.companyId;
            },
            transTime(time){
                if(!time){
                    return '';
                }
                return tzFormChina(time, 'YYYY-MM-DD HH:mm:ss');
            },
            leftOrRight(row){
                //从留言对象中获取关键属性，然后返回left 还是 right
                let {myComment} = row;
                //自己的语言，放在右边
                if(myComment){
                    return 'right';
                }
                return  'left';
            },
            scrollToBottom(){
                setTimeout(()=>{
                    document.getElementsByClassName("chat_content")[0].scrollTop
                        = document.getElementsByClassName("chat_content")[0].scrollHeight;
                },500)
            },
        },
        mounted() {
        },
        created() {
            if(this.needSendEmail){
                this.initNotification();
            }
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            objectId:'',
            objectType:'',
            needSendEmail:{
                type:Boolean,
                default:true
            }
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart__chatView {
        background: #fff;

        .el-form.el-form--label-left .el-form-item__label{
            float: left !important;
        }
        height: 600px;
        overflow: auto;
        &::-webkit-scrollbar{
            width: 2px !important;
        }
        .chat_body{
            height: 530px;
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 0 !important;
            }
        }
        .chat_content{
            width: 100%;
            height: 320px;
            max-height: 360px;
            overflow-y: auto;
            border-bottom: solid 1px #CCCCCC;
            margin-bottom: 10px;
            transition-property: all;
            transition-duration: 1s;
            /*&::-webkit-scrollbar{
                width: 2px !important;
            }*/
        }
        .comment_textarea .el-textarea__inner{
            max-height: 150px !important;
            border-top: none;
            border-right: none;
            border-left: none;
        }
        .comment_chat_table td{
            border: none !important;
        }
        .el-table::v-deep tbody tr:hover{
            background-color: transparent !important;
        }
        .el-table::v-deep tbody tr:hover>td{
            background-color: transparent !important;
        }
        .comment_table_chat_cell_left,.comment_table_chat_cell_right{
            position: relative;
            background-color: #f1f1f1;
            border-radius: 10px;
            padding:2px 10px;
            margin: 0 20px;
        }
        .comment_table_chat_cell_left::before{
            content: "";
            position: absolute;
            left: -10px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-right: 10px solid #f1f1f1;
            border-bottom: 10px solid transparent;
        }
        .comment_table_chat_cell_right::before{
            content: "";
            position: absolute;
            right: -20px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 10px solid #f1f1f1;
        }
        .commentTableHeader{

        }

    }
</style>