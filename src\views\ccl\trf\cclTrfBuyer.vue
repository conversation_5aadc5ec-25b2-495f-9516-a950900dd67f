<template>
    <div class="sgs-box">
            <el-row>
                <h4 class="sgs-title">{{$t('trf.buyerAndAgentInfo')}}</h4>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item :label="$t('buyer.label')">
                        <!--<el-input v-model="customer.customerGroupId" autocomplete="off"></el-input>-->
                        <el-select v-model="buyerCustomerGroupCode" clearable filterable
                                   :placeholder="$t('customerGroup.name')"
                                   @change="selectBuyerCustomerGroupChange" style="width: 100%;">
                            <el-option v-for="(customerGroup,index) in buyerCustomerGroupData"
                                       :label="customerGroup.customerGroupName"
                                       :value="customerGroup.customerGroupCode"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item :label="$t('agent.label')">
                        <el-select v-model="agentCustomerGroupCode" filterable clearable
                                   :placeholder="$t('agentCustomerGroup.name')"
                                   reserve-keyword
                                   remote
                                   :remote-method="searchAgentCustomerGroup"
                                   @change="selectAgentCustomerGroupChange"
                                   style="width: 100%;">
                            <el-option v-for="(customerGroup,index) in agentCustomerGroupData"
                                       :label="customerGroup.customerGroupName"
                                       :value="customerGroup.customerGroupCode"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item :label="$t('trf.trfTemplate')" prop="trfTemplateId">
                        <el-select v-model="trfTemplateId" clearable
                                   @change="selectTemplateChange"
                                   style="width: 100%;"
                                   :placeholder="$t('trf.placeholder.template')">
                            <el-option v-for="(template,index) in templateData"
                                       :label="template.templateName"
                                       :value="template.id"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

    </div>
</template>

<script>
    import {getCustomerGroup,getCustomerGroupByParms} from "@/api/common/index";
    import {validatenull,validateEmail} from "@/util/validate";
    import {
        getTemplateList,buyerPkAgent,getBuyerInfoPage
     } from "@/api/trf/trf";
    import {mapGetters} from "vuex";
    export default {
        name: "trfCustomer",
        /*props: {
            trfId:String,
            agentCustomerGroupCode:String,
            buyerCustomerGroupCode:String,
            trfTemplateId:String,
            bossNo:String,
            isSgs:{
                type:Boolean,
                default:true
            }
        },*/
        props:['trfId','agentCustomerGroupCode','buyerCustomerGroupCode','trfTemplateId','bossNo','isSgs','fieldSettingsData','isShow'],
        data() {
            return {
                agentCustomerGroupCode:'',
                buyerCustomerGroupCode:'',
                trfTemplateId:'',
                buyerCustomerGroupData:[],
                agentCustomerGroupData:[],
                templateData:[],
                buyerCustomerGroup:{},
                template:{},
                //search对象
                buyerCustomerGroupParam:{},
                customerGroupParam:{},
                buyerPkAgentParam:{},
                templateDataParam:{},
                page: {
                    currentPage: 1,
                    pageSize: 1000,
                },
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission"]),
        },
        watch:{
             isShow:{
                  handler(){   //注意此处就是handler 回显处理
                      if(this.isSgs){
                          this.queryCustomerGroupData();
                      }else{
                          //买方客户组 后期关联登录用户查询
                          this.queryBuyerCustomerGroupData(this.bossNo);
                      }
                    if(this.isShow){//回显的话加载模板数据
                        //加载模板
                        this.$set(this.templateDataParam,'customerGroupCode',this.buyerCustomerGroupCode);
                        this.searchTemplateData();
                    }
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
        },
        created() {


        },
        methods: {
            selectBuyerCustomerGroupChange(val){
                this.trfTemplateId='';
                this.buyerCustomerGroup = this.buyerCustomerGroupData.find((item) => {
                    return item.customerGroupCode === val;
                });
                this.$set(this.templateDataParam,'customerGroupCode',val);
                this.$set(this.buyerPkAgentParam,'buyerGroupCode',val);
                if (!validatenull(this.agentCustomerGroupCode) && !validatenull(val)) {
                    this.buyerPkAgent();
                }else {
                    if(validatenull(val)){//无选择buyer
                        //判断是否选择agent
                        if(!validatenull(this.agentCustomerGroupCode)){
                            this.$set(this.templateDataParam,'customerGroupCode',this.agentCustomerGroupCode);
                        }else{
                            this.$set(this.templateDataParam,'customerGroupCode',"General");
                        }
                    }
                }
                //查询模板
                this.searchTemplateData();
                //返回buyer对象至父组件
                this.$emit('getBuyerCustomerGroup', this.buyerCustomerGroup);
            },
            selectAgentCustomerGroupChange(val){
                this.$set(this.buyerPkAgentParam,'agentGroupCode',val);
                if (!validatenull(this.buyerCustomerGroupCode) && !validatenull(val)) {
                    this.buyerPkAgent();
                }else if(validatenull(val)){
                    if(!validatenull(this.buyerCustomerGroupCode)){
                        //查询模板
                        this.$set(this.templateDataParam,'customerGroupCode',this.buyerCustomerGroupCode);
                        this.searchTemplateData();
                    }
                }
            },
            selectTemplateChange(val){
                this.template = this.templateData.find((item) => {
                    return item.id === val;
                });
                //返回template对象至父组件
                this.$emit('getTemplate', this.template);
            },
            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.buyerCustomerGroupData = data;
                });
            },
            //Buyer买方客户组 后期修改为查询关联登录用户相关的客户组
            queryBuyerCustomerGroupData: function (bossNo) {
                var params = {}
                this.$set(this.buyerCustomerGroupParam,'customerNumber',bossNo);
                getBuyerInfoPage(this.page.currentPage, this.page.pageSize, Object.assign(params, this.buyerCustomerGroupParam)).then(res => {
                    var customerGroupData = res.data.rows;
                    if (customerGroupData != null && customerGroupData != undefined) {
                        customerGroupData.forEach((item, index, array) => {
                            if (item.customerGroupCode != this.userInfo.customerGroupCode) {
                                this.buyerCustomerGroupData.push(item);
                            }
                        })
                        //添加公司的group信息
                        var groupObj = {};
                        groupObj.customerGroupId=this.userInfo.customerGroupId;
                        groupObj.customerGroupCode = this.userInfo.customerGroupCode;
                        groupObj.customerGroupName = this.userInfo.customerGroupName;
                        if (groupObj.customerGroupCode != '' && groupObj.customerGroupName != '') {
                            this.buyerCustomerGroupData.push(groupObj);
                        }
                        var generalObj = {};
                        generalObj.customerGroupId="";
                        generalObj.customerGroupCode = "General";
                        generalObj.customerGroupName = "General";
                        this.buyerCustomerGroupData.push(generalObj);
                        this.page.total = res.data.records;
                    }
                });
            },
            //代理商客户组查询 输入3个字符后再执行查询匹配 最大查询五条数据，防止全部客户组暴露
            searchAgentCustomerGroup(val) {
                this.$set(this.customerGroupParam,'groupName',val);
                if (val.length >= 3) {//输入满足三个字符后查询客户组数据
                    this.queryAgentCustomerGroupData();
                }
            },
            queryAgentCustomerGroupData() {
                var params = {};
                getCustomerGroupByParms(Object.assign(params, this.customerGroupParam)).then(res => {
                    const data = res.data.data;
                    this.agentCustomerGroupData = data;
                });
            },
            buyerPkAgent() {
                var params = {};
                buyerPkAgent(Object.assign(params, this.buyerPkAgentParam)).then(res => {
                    const data = res.data.data;
                    //查询模板数据
                    this.$set(this.templateDataParam,'customerGroupCode',data);
                    this.searchTemplateData();
                });
            },
            searchTemplateData() {
                var params = {};
                getTemplateList(Object.assign(params, this.templateDataParam)).then(res => {
                    const data = res.data.data;
                    this.templateData = data;
                });
            },
        }
    }
</script>

<style scoped>

</style>
