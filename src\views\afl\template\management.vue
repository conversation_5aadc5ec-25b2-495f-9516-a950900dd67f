<template>
  <basic-container>
    <!-- <el-breadcrumb class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">{{ $t('navbar.dashboard') }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ $t('navbar.aflTemplateManagement') }}</el-breadcrumb-item>
    </el-breadcrumb> -->

    <h1 class="top-title">{{ $t('navbar.aflTemplateManagement') }}</h1>
    <el-card shadow="never" class="box-card">
      <el-row>
        <el-form :inline="true" :model="formInline" size="medium" label-width="200px" label-position="left">
          <el-form-item>
            <el-input clearable
                      v-model="query.customerNameZh"
                      :placeholder="$t('search')"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">{{ $t('operation.search') }}</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <el-table
            :data="tableData"
            style="width: 100%"
            @expand-change='expandChange'
            :row-key='id'
            size="medium" :empty-text="$t('NoData')">
          <el-table-column
              type="expand"
              >
            <template slot-scope="props">
              <el-table
                :data="props.row.children"
                v-loading="props.row.loading"
                style="width: 100%"
                @expand-change='expandChange1'
                size="medium" :empty-text="$t('NoData')">
                <el-table-column
                    type="expand"
                    >
                  <template slot-scope="props1">
                    <el-table
                      :data="props1.row.children1"
                      v-loading="props1.row.loading1"
                      style="width: 100%"
                      @expand-change='expandChange1'
                      size="medium" :empty-text="$t('NoData')">
                      <el-table-column
                          prop="trfTemplateName"
                          :label="$t('template.templateName')">
                      </el-table-column>
                      <el-table-column
                          prop="isAflDefault"
                          :label="$t('template.isAflDefault')">
                          <template slot-scope="scope">
                            <el-switch
                              v-model="scope.row.isAflDefault"
                              :active-value='1'
                              :inactive-value='0'
                              :disabled="!permissionList.setDefaultAflUserTemplateUnit"
                              @change='isAflDefaultChange(scope.row,$event,props1.row.children1)'>
                            </el-switch>
                          </template>
                      </el-table-column>
                      <el-table-column
                          prop="createTime"
                          :label="$t('template.createTime')">
                      </el-table-column>
                      <el-table-column
                          prop="isAflCreatedByAdmin"
                          :label="$t('template.isAflCreatedByAdmin')">
                          <template slot-scope="scope">
                            {{scope.row.isAflCreatedByAdmin==1?'管理员':'用户'}}
                          </template>
                      </el-table-column>
                      <el-table-column
                          :label="$t('training.Action')"
                          width="120">
                          <template slot-scope="props2">
                            <el-button size="mini" v-if="props2.row.isAflCreatedByAdmin==1 && permissionList.removeAflUserTemplateUnit" type="primary" @click="delTemplate(props2.row.id,props2,props1.row.children1)" >{{$t('aflTemplate.management.delTemplate')}}</el-button>
                          </template>
                      </el-table-column>
                    </el-table>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="name"
                    :label="$t('account.customerName')">
                </el-table-column>
                <el-table-column
                    prop="telephone"
                    :label="$t('customer.contactMobile')">
                </el-table-column>
                <el-table-column
                    prop="email"
                    :label="$t('account.email')">
                </el-table-column>
                <el-table-column
                    prop="templateNumbers"
                    :label="$t('account.customerNum')">
                </el-table-column>
                <el-table-column
                  :label="$t('training.Action')"
                  width="120">
                  <template slot-scope="props2">
                    <el-button v-if="permissionList.addAflUserTemplateUnit" size="mini" type="primary" @click="addTemplate(props2.row.id,props.row.id)" >{{$t('aflTemplate.management.addTemplate')}}</el-button>
                  </template>
              </el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column
              prop="customerNameZh"
              :label="$t('account.customerName')+' Zh'"
              width="300">
          </el-table-column>
          <el-table-column
              prop="customerNameEn"
              :label="$t('account.customerName')+' En'"
              width="300">
          </el-table-column>
          <el-table-column
              prop="updateUser"
              :label="$t('common.operator')"
              width="300">
          </el-table-column>
          <el-table-column
              prop="updateTime"
              :label="$t('common.operationTime')"
              width="auto">
          </el-table-column>
        </el-table>
        <el-pagination
            @size-change="sizeChange"
            @current-change="currentChange"
            :current-page="page.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total">
        </el-pagination>
      </el-row>
    </el-card>
  </basic-container>
</template>

<script>
import {mapGetters} from "vuex";
import {getCustomerList,queryCompanyEmployeeInfoByUserCompanyId,selectWithOutTrfTemplateData,setDefaultTemplateByApplicationUser,removeTrfTemplate} from "@/api/afl/template/management";

export default {
  name: "management",
  data() {
    return {
      tableData:[],
      query: {
        isAflCustomer: 1
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      loading:false
    }
  },
  methods: {
    onSearch() {
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    onLoad(page, params = {}) {
      getCustomerList(page.currentPage, page.pageSize, Object.assign(params, this.query, this.sort)).then(res => {
        this.tableData = res.data.data.records;
        this.page.total = res.data.data.total;
        console.log(this.tableData)
      });
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    expandChange(row,expandedRows) {
      if (expandedRows.length==0) return
      this.$set(row,'loading',true)
      let param = {
        userCompanyId:row.userCompanyId
      }
      queryCompanyEmployeeInfoByUserCompanyId(param).then(res => {
        res.data.data.map(item => {
          item.children1 = [];
          item.loading1 = false
        });
        this.$set(row,'children',res.data.data)
        row.loading = false
      });
    },
    expandChange1(row,expandedRows) {
      if (expandedRows.length==0) return
      row.loading1 = true
      let param = {
        applicantUser:row.id
      }
      selectWithOutTrfTemplateData(param).then(res => {
        row.children1 = res.data.data
        row.loading1 = false
      });
    },
    isAflDefaultChange(item,val,arr) {
      console.log(arr)
      let param = {
        applicationUser:item.applicantUser,
        id:item.id,
        isAflDefault:val
      }
      setDefaultTemplateByApplicationUser(param).then(res=>{
        // this.$message('修改成功')
        arr.forEach(element => {
          if (element.id!=item.id) {
            element.isAflDefault = 0
          }
        });
      })
    },
    addTemplate(id,customerId) {
      this.$router.push(`/afl/trf/newTrf?flag=3&templateId=2&applicationUser=${id}&customerId=${customerId}`)
    },
    delTemplate(id,props,arr) {
      let param = {
        ids:id
      }
      console.log(id,props.$index,arr)
      removeTrfTemplate(param).then(res=>{
        this.$message('删除成功')
        arr.splice(props.$index,1)
      })
    }
  },
  created() {
    this.onLoad(this.page);
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addAflUserTemplateUnit: this.vaildData(this.permission['sgs:afl:template:add'], false),
        removeAflUserTemplateUnit: this.vaildData(this.permission['sgs:afl:template:remove'], false),
        setDefaultAflUserTemplateUnit: this.vaildData(this.permission['sgs:afl:template:default'], false),
      };
    },
  },
}
</script>

<style scoped>

</style>