<template>
  <div class="sgs-contail"
       :class="{'avue--collapse':isCollapse}">
    <div class="sgs-header page-no-print">
      <!-- 顶部导航栏 -->
      <top />
    </div>
    <div class="sgs-main" :style="{'marginTop': hideInTrfDetail ? 80 : 0 + 'px'}">
      <!-- 主体视图层 -->
      <keep-alive>
        <router-view class="sgs-view" v-if="$route.meta.keepAlive" :key = "$route.path"/>
      </keep-alive>
      <router-view class="sgs-view" v-if="!$route.meta.keepAlive" :key = "$route.path"/>
    </div>
    <smart-footer></smart-footer>
    <contact-us></contact-us>
    <!-- 任务列表 -->
    <guide></guide>
    <!-- 功能引导 -->
    <task></task>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import admin from "@/util/admin";
import { validatenull } from "@/util/validate";
import SmartFooter from "../../components/footer/smartFooter";
export default {
  components: {
    SmartFooter,
    ContactUs: resolve => require(['./contact/contact-us'], resolve),
    top: resolve => require(['./top/'], resolve),
    tags: resolve => require(['./tags'], resolve),
    sidebar: resolve => require(['./sidebar/'], resolve),
    guide: resolve => require(['./guide'], resolve),
    task: resolve => require(['@/components/guide/taskStep'], resolve),
  },
  name: "index",
  data() {
    return {
      hideInTrfDetail: true,
      //刷新token锁
      refreshLock: false,
      //刷新token的时间
      refreshTime: ""
    };
  },
  created() {
    // || this.$route.path == '/afl/trf/newTrf'
     if(this.$route.path == '/trf/trfDetail' || this.$route.path == '/ccl/trf/newTrf') this.hideInTrfDetail = false
    //实时检测刷新token
    this.refreshToken();
  },
  mounted() {
    this.$nextTick(() => {
      this.init();
    })
  },
  // computed: mapGetters(["isLock", "isCollapse", "website"]),
  computed: {
    ...mapGetters(["userInfo", "isLock", "isCollapse", "website", "permission", "dimensions", "menu"]),
    role() {
      return {
        isSGS: this.haseRole("SGSUserRole", "SgsAdmin"),
        isBuyer: this.haseRole('UserRole', 'Buyer'),
      }
    }
  },
  props: [],
  methods: {
    haseRole(type, role) {
      if (validatenull(type) || validatenull(role)) {
        return false;
      }
      if (validatenull(this.dimensions)) {
        return false;
      } else {
        if (this.dimensions.hasOwnProperty(type)) {
          if (this.dimensions[type].indexOf(role) >= 0) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      }
    },
    showCollapse() {
      this.$store.commit("SET_COLLAPSE");
    },
    // 屏幕检测
    init() {
      this.$store.commit("SET_SCREEN", admin.getScreen());
      window.onresize = () => {
        setTimeout(() => {
          this.$store.commit("SET_SCREEN", admin.getScreen());
        }, 0);
      };

      // 功能引导
      let list = localStorage.getItem('loginUserList')
      if(list == null) { // 没账号登录过
        list = []
        list.push({
          userName: this.userInfo.userName,
          companyId: this.userInfo.companyId,
          userMgtId: this.userInfo.userMgtId,
          fnList: [
            {'finish_dashboard': this.userInfo.guide ? false : true},
            {'finish_trfList': this.userInfo.guide ? false : true},
            {'finish_createTrf': this.userInfo.guide ? false : true},
          ]
        })
        localStorage.setItem('loginUserList', JSON.stringify(list))
      } else {  // 多账号登录
        let list = JSON.parse(localStorage.getItem('loginUserList'))
        let index = list.findIndex(item => item.userMgtId == this.userInfo.userMgtId)
        if(index == -1) {
          console.log('第一次登录')
          list.push({
            userName: this.userInfo.userName,
            companyId: this.userInfo.companyId,
            userMgtId: this.userInfo.userMgtId,
            fnList: [
              {'finish_dashboard': this.userInfo.guide ? false : true},
              {'finish_trfList': this.userInfo.guide ? false : true},
              {'finish_createTrf': this.userInfo.guide ? false : true},
            ]
          })
          localStorage.setItem('loginUserList', JSON.stringify(list))
        } else {
          console.log('登录过了的')
        }
      }
    },
    // 10分钟检测一次token
    refreshToken() {
      /*this.refreshTime = setInterval(() => {
        const token = getStore({
          name: "token",
          debug: true
        });
        const date = calcDate(token.datetime, new Date().getTime());
        if (validatenull(date)) return;
        if (!(date.seconds >= this.website.tokenTime) && !this.refreshLock) {
          this.refreshLock = true;
          this.$store
            .dispatch("RefeshToken")
            .then(() => {
              this.refreshLock = false;
            })
            .catch(() => {
              this.refreshLock = false;
            });
        }
      }, 10000);*/
    }
  }
};
</script>
