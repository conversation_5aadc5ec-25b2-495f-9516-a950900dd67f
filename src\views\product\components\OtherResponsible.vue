<template>
  <div class="smart_views_scope_certificates"
       id="smart_views_scope_certificates">
    <CommonTable border
                 v-loading="tableOption.loading"
                 ref="manufactureInfoTable"
                 style="width:100%"
                 row-key="id"
                 :size="tableOption.size"
                 :data="tableOption.dataList"
                 :page="tableOption.page"
                 :menu-show="tableOption.menuShow"
                 :option="tableOption.option"
                 :filters="tableOption.filters">
      <template #menuRight>
        <el-button text
                   type="primary"
                   size="mini"
                   @click="handlerUploadAdd"
                   :icon="Plus">Add</el-button>
      </template>
      <template #docName="{row ,$index}">
        <el-button text
                   type="primary"
                   @click="downloadWorkbook(row)">{{row.docName}}</el-button>
      </template>
      <template #actionColumn="{row,$index}">
        <ConfirmButton title="Are you sure delete this data?"
                       @confirm="deleteWorkbook(row)">Delete</ConfirmButton>
      </template>
    </CommonTable>
    <!-- 添加  Certificates/Document -->
    <el-dialog v-model="data.scopeCertificates.dialogVisible"
               title="Add Certificates/Document"
               width="700px"
               lock-scroll
               show-close
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               draggable>
      <div class="add-scopeCertificates-dialog">
        <el-form label-width="180px"
                 label-position="right"
                 :model="data.scopeCertificates"
                 :rules="data.rule"
                 ref="workbookForm">
          <el-form-item label="File" prop="checkIn">
            <Upload :action="upload.action"
                    :show-file-list='upload.showFileList'
                    :multiple='upload.multiple'
                    :limit='upload.limit'
                    :size='upload.size'
                    :accept='upload.accept'
                    @uploadEmit='uploadEmitFun'></Upload>
          </el-form-item>
          <el-form-item label="Linked To">
            <el-select v-model="data.scopeCertificates.checkIn"
                       placeholder="Please select"
                       style="width:100%">
              <el-option label="Product"
                         value="Product"></el-option>
              <el-option label="Product Category"
                         value="Product Category"></el-option>
              <el-option label="Product Sub Category"
                         value="Product Sub Category"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Materials">
            <el-select v-model="data.scopeCertificates.checkIn"
                       placeholder="Please select"
                       style="width:100%">
              <el-option label="Product"
                         value="Product"></el-option>
              <el-option label="Product Category"
                         value="Product Category"></el-option>
              <el-option label="Product Sub Category"
                         value="Product Sub Category"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Description">
            <el-input v-model="data.scopeCertificates.description"
                      type="textarea"
                      maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="Certificate Number">
            <el-input v-model="data.scopeCertificates.description"
                      maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="Product Seller Name">
            <el-input v-model="data.scopeCertificates.description"
                      maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="Quantity / Weight Shipped">
            <el-input v-model="data.scopeCertificates.description"
                      maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="Units">
            <!-- radio group -->
            <el-radio-group v-model="data.scopeCertificates.checkIn">
              <el-radio label="Product"></el-radio>
              <el-radio label="Product Category"></el-radio>
              <el-radio label="Product Sub Category"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="Quantity / Weight Shipped">
            <!-- el date -->
            <el-date-picker v-model="data.scopeCertificates.date"
                            type="date"
                            placeholder="Select date"
                            style="width:100%">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="data.scopeCertificates.dialogVisible = false">Cancel</el-button>
          <el-button type="primary"
                     @click="handeleSave">
            Unload
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  onMounted,
  onUnmounted,
  computed,
  watch,
  provide,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElNotification } from 'element-plus'
import { useI18n } from 'vue-i18n'
import CommonTable from '@/components/TableList/CommonTable.vue'
import ConfirmButton from '@/components/ConfirmButton/index.vue'
import productApi from '@/api/product.ts'
import { saveAttachment, getFileUrlByCloudId } from '@/api/common'
import Upload from '@/components/Upload/Upload.vue'

const { t } = useI18n()
const store = useStore()
const language = computed(() => store.state.common.language)

defineOptions({
  name: 'ScopeCertificates',
})
const tableOption = reactive({
  dataList: [],
  originalDataList: [],
  loading: false,
  menuShow: true,
  size: 'small',
  option: {
    hideRowColor: true,
    selection: false,
    selectionDis: () => {
      return true
    },
    sortSelectionFiledName: '',
    showSortIcon: true,
    index: false,
    action: true,
    actionWidth: 150,
    actionAlign: '',
    disableOption: {},
    column: [
        {
        prop: 'docName',
        label: 'Linked To',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'docName',
        label: 'Document Name',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'docName',
        label: 'Description',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'description',
        label: 'Certificate Number',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'versionNo',
        label: 'Document Type',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'updateTime',
        label: 'Certified Company Name',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'updateUser',
        label: 'Issue Date',
        hide: false,
        filter: true,
        slot: false,
        type: 'Input',
      },
      {
        prop: 'lockStatus',
        label: 'Expiry Date',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'lockStatus',
        label: 'Comments / LWG rating etc',
        hide: false,
        filter: true,
        slot: true,
        type: 'Input',
      },
      {
        prop: 'approvalStatus',
        label: 'Status',
        hide: false,
        filter: true,
        slot: false,
        type: 'Select',
        tagWidth: '100px',
        dicData: [
          { label: 'APPROVED', value: 'APPROVED', tag: true, type: 'success' },
          { label: 'REJECTED', value: 'REJECTED', tag: true, type: 'error' },
          { label: 'CANCEL', value: 'CANCEL', tag: true, type: 'cancel' },
          { label: 'PENDING', value: 'PENDING', tag: true, type: 'warn' },
        ],
      },
    ],
  },
  sort: {
    sortBy: '',
    sortOrder: '',
  },
  filters: {},
  page: {
    show: false,
    size: 1,
    page: 1,
    rows: 20,
    small: true,
    sizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
    total: 100,
  },
})

const data = reactive({
  scopeCertificates: {
    dialogVisible: false,
    checkIn: {},
    description: '',
  },
  attachment: [],
  rule: {
    checkIn: [
      { required: true, message: 'Please select a check-in option', trigger: 'change' },
    ],
    description: [
      { required: true, message: 'Please enter a description', trigger: 'blur' },
    ],
  },
})
const props = defineProps({
  objectId: {
    type: String,
    default: '',
  },
})

/* 上传组件 */
const upload = reactive({
  action: '/api/sgsapi/FrameWorkApi/file/doUpload',
  showFileList: true,
  multiple: false,
  limit: 20,
  size: 1,
  accept: '.xlsx,.xls',
})
const uploadEmitFun = (val) => {
  if (val) {
    let objId = props.objectId
    let fileList = (val || []).map((item) => {
      const attachment = {
        object: 'productWorkBook',
        objectId: objId,
        attachmentId: item.id,
        fileName: item.attachmentName + '.' + item.suffixes,
        fileUrl: item.path,
        fileType: 'File',
        languageId: 1,
      }
      return attachment
    })
    console.log('fileList', fileList)
    data.attachment = fileList
  }
  //   console.log(val)
}
</script>

<style lang="scss">
.smart_views_scope_certificates {
}
</style>