<template>
    <basic-container class="sgs_smart_approve">
        <h1 class="top-title">{{ $t('navbar.customerApprove') }}</h1>
        <el-card shadow="never" class="box-card">
            <div class="header_filter">
                <div class="header_filter_left">{{$t('register.serveType')}}: &nbsp;&nbsp;</div>
                <div class="header_filter_right">
                    <el-form label-width="80px" inline label-position="left">
                        <el-form-item v-for="(suList,domain) in serviceUnitMap" :label="domain">
                            <el-select
                                v-model="headerFilter[domain]"
                                clearable
                                size="mini"
                                style="width: 100%"
                                :placeholder="$t('register.serveType')"
                                @change="changeHeaderFilter(domain)"
                            >
                                <el-option
                                    v-for="(su,ind) in suList"
                                    :key="'su_'+ind"
                                    :label="su"
                                    :value="su"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <el-row>
                <el-table
                    :data="tableData"
                    v-loading="tableLoading"
                    border
                    size="medium"
                    height="500"
                    style="width: 100%;" >
                    <el-table-column
                            type="index"
                            fixed
                            label="#"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            fixed
                            :show-overflow-tooltip='true'
                            min-width="220"
                            prop="customerNameEn"
                            :label="$t('customer.companyNameEn')">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('customer.companyNameEn')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        v-model="filter.customerNameEn"
                                        clearable
                                        size="mini"
                                        :placeholder="$t('customer.companyNameEn')"/>
                            </div>
                        </template>
                        <template scope="{row}">
                            {{row.customerNameEn}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            fixed
                            :show-overflow-tooltip='true'
                            min-width="220"
                            prop="customerNameZh"
                            :label="$t('customer.companyNameCn')">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('customer.companyNameCn')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        v-model="filter.customerNameZh"
                                        clearable
                                        size="mini"
                                        :placeholder="$t('customer.companyNameCn')"/>
                            </div>
                        </template>
                        <template scope="{row}">
                            {{row.customerNameZh}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            :label="$t('register.serveType')"
                            prop="serviceUnits"
                            min-width="260"
                            :show-overflow-tooltip='true'
                    >
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('register.serveType')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-select  v-model="filter.serviceUnits"
                                            clearable
                                            size="mini"
                                            style="width: 100%"
                                            :placeholder="$t('register.serveType')">
                                    <el-option
                                        v-for="(su,ind) in serviceUnits"
                                        :key="ind"
                                        :value="su.serviceUnitCode"
                                        :label="su.serviceUnitName"
                                    ></el-option>
                                </el-select>
                            </div>
                        </template>
                        <template slot-scope="{row}">
                            <p   v-for="(serviceType,index) in getServiceUnitTag(row.serviceUnits)"
                                 :key="index">
                                {{ serviceType }}
                            </p>
                        </template>
                    </el-table-column>
                    <el-table-column
                            :label="$t('customer.sgs.customerNo')"
                            prop="bossNo"
                            min-width="220"
                            :show-overflow-tooltip='true'
                    >
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('customer.sgs.customerNo')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        v-model="filter.bossNo"
                                        clearable
                                        size="mini"
                                        :placeholder="$t('customer.sgs.customerNo')"/>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                            :label="$t('account.email')"
                            prop="account.email"
                            min-width="220"
                            :show-overflow-tooltip='true'
                    >
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('account.email')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        v-model="filter.email"
                                        clearable
                                        size="mini"
                                        :placeholder="$t('account.email')"/>
                            </div>
                        </template>
                        <template slot-scope="{row}">
                            {{row.account.email}}
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="approveStatus"
                            :label="$t('common.status.title')"
                            width="160"
                            align="center">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('common.status.title')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-select
                                    size="mini"
                                    clearable
                                    v-model="filter.approveStatus"
                                    style="width: 100%"
                                    :placeholder="$t('common.status.title')"
                                >
                                    <el-option value="1"  :label="$t('customer.status.todo')"></el-option>
                                    <el-option value="10" :label="$t('customer.status.emailConfirm')"></el-option>
                                    <el-option value="80" :label="$t('customer.status.refused')"></el-option>
                                    <el-option value="90" :label="$t('customer.status.approved')"></el-option>
                                </el-select>
                            </div>
                        </template>
                        <template scope="scope">
                            <span v-if="scope.row.approveStatus==1">{{$t('customer.status.todo')}}</span>
                            <span v-if="scope.row.approveStatus==10">{{$t('customer.status.emailConfirm')}}</span>
                            <span v-if="scope.row.approveStatus==80">{{$t('customer.status.refused')}}</span>
                            <span v-if="scope.row.approveStatus==90">{{$t('customer.status.approved')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="updateTime"
                            :label="$t('common.operationTime')"
                            min-width="220"
                            align="center">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('common.operationTime')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-date-picker
                                                type="daterange"
                                                format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd"
                                                clearable
                                                size="mini"
                                                :placeholder="$t('common.operationTime')"
                                                v-model="headerOptionTime"
                                                @change="changeHeaderOptionTime"
                                                style="width: 100%;"></el-date-picker>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="updateUser"
                            :label="$t('common.operator')"
                            min-width="220">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('common.operator')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        clearable
                                        v-model="filter.updateUser"
                                        size="mini"
                                        :placeholder="$t('common.operator')"/>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="reportNo" min-width="220" :label="$t('trfList.reportNo')">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('trfList.reportNo')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        clearable
                                        v-model="filter.reportNo"
                                        size="mini"
                                        :placeholder="$t('trfList.reportNo')"/>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="contacts" min-width="220" :label="$t('common.contacts')">
                        <template slot="header" slot-scope="scope">
                            <div>
                                {{$t('common.contacts')}}
                            </div>
                            <div style="margin-top: 8px">
                                <el-input
                                        clearable
                                        v-model="filter.contacts"
                                        size="mini"
                                        :placeholder="$t('common.contacts')"/>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isVip" label="VIP" width="80">
                        <template slot="header" slot-scope="scope">
                            <div> VIP </div>
                            <div style="margin-top: 8px">
                                <el-select
                                        size="mini"
                                        clearable
                                        v-model="filter.isVip"
                                        style="width: 100%"
                                        placeholder="VIP"
                                >
                                    <el-option value="1" label="Y"></el-option>
                                    <el-option value="0" label="N"></el-option>
                                </el-select>
                            </div>
                        </template>
                        <template slot-scope="scope">
                            <el-switch
                                    v-model="scope.row.isVip"
                                    @change="isDisabledTemplate(scope.row)"
                                    active-color="#FF6600"
                                    inactive-color="#D9D9D9"
                                    active-text="" :active-value=1  inactive-text=""  :inactive-value=0  >
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column
                            :label="$t('operation.title')"
                            width="150"
                            fixed="right"
                            align="center">
                        <template slot-scope="scope">
                            <el-button v-if="permissionList.viewBtn" @click="rowView(scope.row)" type="text">{{$t('operation.view')}}</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
            </el-row>
        </el-card>
        <customer-detail-dialog :v-if="selectParentDialogVisible"
                                :visible.sync="selectParentDialogVisible"
                                :customerId.sync="form.id"
                                @submit = "onSearch"
        >
        </customer-detail-dialog>
    </basic-container>
</template>

<script>
    import {getList} from "@/api/customer/customerRegister";
    import {updateById} from "@/api/customer/customer";
    import {mapGetters} from "vuex";
    import {debounce} from 'lodash'
    import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";
    let vm;
    export default {
        components: {
            CustomerDetailDialog: resolve => require(['../../components/dialog/customer-detail-dialog'], resolve),
        },
        data() {
            return {
                filter:{
                    customerNameZh:'',
                    productCategory:'',
                    sgsCustomerNo:'',
                    email:'',
                    approveStatus:'',
                    serviceUnits:'',
                    updateUser:'',
                    updateTimeStart:null,
                    updateTimeEnd:null,
                    reportNo:'',
                    contacts:'',
                },
                headerFilter:{},
                serviceUnits:[],
                serviceUnitMap:{},
                headerOptionTime:[],
                selectParentDialogVisible: false,
                name: "list",
                tableData: [],
                tableLoading:false,
                query:{},
                sort: {descs:'update_time'},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                form: {},
            }
        },
        computed: {
            ...mapGetters(["permission","language"]),
            permissionList() {
                return {
                    viewBtn: this.vaildData(this.permission['sgs:customer:approve:view'],false),
                };
            }
        },
        /*03-14*/
        watch:{
            language:{
              immediate:false,
              handler(newV,oldV){
                  this.queryServiceType();
              }
            },
            filter:{
              immediate:false,
              deep:true,
              handler(newV,oldV){
                  this.deboundceSearch();
              }
            },
          selectParentDialogVisible:function(val){
              if(!val){
                   this.form = {};
              }
          }
        },
        methods: {
            onSearch() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            changeHeaderFilter(domain){
                let keys = Object.keys(this.headerFilter);
                (keys || []).forEach(key=>{
                    if(key != domain){
                        this.$set(this.headerFilter,key,'');
                    }
                });
                this.$set(this.filter,'serviceUnits',this.headerFilter[domain]);
            },
            deboundceSearch:debounce(function(val){
                vm.onSearch();
            },500),
            onLoad(page, params = {}) {
                this.tableLoading = true;
                getList(page.currentPage, page.pageSize, Object.assign(params, this.query,this.filter, this.sort)).then(res => {
                    this.tableLoading = false;
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                }).catch(err=>{
                    this.tableLoading = false;
                });
            },
            //分页查询
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            currentChange(pageCurrent){
                this.page.currentPage = pageCurrent;
                this.onLoad(this.page);
            },
            rowView(row){
                this.form = row;
                this.selectParentDialogVisible=true;
            },
            isDisabledTemplate(row){
                this.$confirm(this.$t('common.isChangeStatus'), this.$t('tip'), {
                    confirmButtonText:  this.$t('submitText'),
                    cancelButtonText: this.$t('cancelText'),
                    type: 'warning'
                }).then(() => {
                    updateById( Object.assign({id: row.id, isVip: row.isVip})).then(res => {
                        this.$message({
                            type: 'success',
                            message:  this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
                }).catch(() => {
                    if (row.isVip === 1 || row.isVip === '1') {
                        row.isVip = 0;
                    }else {
                        row.isVip = 1;
                    }
                });
            },
            changeHeaderOptionTime(val){
                console.log("changeHeaderOptionTime",val,this.headerOptionTime)
                this.filter.updateTimeStart = null;
                this.filter.updateTimeEnd = null;
                if(val && val.length==2){
                    this.filter.updateTimeStart = val[0]+" 00:00:00";
                    this.filter.updateTimeEnd = val[1] + " 23:59:59";
                }
            },
            async queryServiceType(){
                let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
                this.serviceUnits=serviceUnitsDatas;
                let map = {};
                this.headerFilter = {};
                serviceUnitsDatas.forEach(item=>{
                    let {serviceDomain,serviceUnitCode} = item;
                    if(serviceUnitCode == 'testing-sl'){
                        this.filter.serviceUnits = serviceUnitCode;
                    }
                    map[serviceDomain] = [...(map[serviceDomain] || []),serviceUnitCode];
                })
                if(map.hasOwnProperty('Testing')){
                    this.headerFilter['Testing'] = 'testing-sl';
                }
                this.serviceUnitMap = map;
            },
            getServiceUnitTag(serviceUnitJSON){
                if(!serviceUnitJSON){
                    return []
                }

                let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits,serviceUnitJSON);
                let serviceNames=[];
                for (let serviceUnitName of  resultObj.serviceUnitDataItems) {
                    for(let serviceUnitObj of this.serviceUnits){
                        if(serviceUnitName==serviceUnitObj.serviceUnitCode){
                            serviceNames.push(serviceUnitObj.serviceUnitName);
                            continue;
                        }
                    }
                }
                return serviceNames;
            }
        },
        created() {
            vm = this;
            this.queryServiceType();
            this.filter.approveStatus = '1';
            this.onLoad(this.page);
        }
    }
</script>

<style scoped lang="scss">
.sgs_smart_approve{
  .header_filter{
    display: flex;
    justify-content: flex-start;
    align-content: center;
    align-items: center;
    .header_filter_left{
      height: 67px;
      line-height: 38px;
    }
  }
}
</style>
