<template>
  <div class="detail-header">
    <div class="basic-info">
      <div>
        <h2>{{ info.title }}</h2>
        <div class="name-status">
          <h3 v-if="info.subTitle">{{ info.subTitle }}</h3>
          <span v-else class="no-data">No Data</span>
        </div>
      </div>
      <div class="info-container">
        <div class="label-group">
          <span class="label">Create Time</span>
          <span class="value">{{ createTime }}</span>
        </div>
        <div class="label-group">
          <span class="label">Update Time</span>
          <span class="value">{{ updateTime }}</span>
        </div>
      </div>
    </div>
    <div class="info-container">
      <div class="item-group">
        <div
          class="label-group"
          v-for="(item, index) in info.keys"
          :key="index"
        >
          <span class="label">{{ item.label }}</span>
          <el-tooltip
            effect="dark"
            :content="item.value"
            placement="top"
            v-if="(item.value || '').length > 20"
          >
            <span>{{ item.value.slice(0, 20) + "..." }}</span>
          </el-tooltip>
          <span class="value" v-else>{{ item.value }}</span>
        </div>
      </div>
      <!-- <div class="button-group">
          <el-button type="text">Edit</el-button>
          <el-button type="text">Edit</el-button>
          <el-button type="text">Edit</el-button>
        </div> -->
    </div>
    <!-- <div class="divider"></div> -->
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      required: true,
      default: () => ({
        title: "",
        subTitle: "",
        keys: [],
        createTime: null,
        updateTime: null,
      }),
    },
    nowDate: {
      type: String,
      required: true,
    },
  },
  computed: {
    createTime() {
      return this.info.createTime || this.nowDate;
    },
    updateTime() {
      return this.info.updateTime || this.nowDate;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/styles/unit.scss";

.detail-header {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: $module-padding;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);

  h2 {
    font-size: 16px;
    color: $text-color-value;
  }

  .name-status {
    display: flex;
    align-items: center;
    padding-top: $inline-element-spacing;

    h3 {
      font-size: 24px;
    }

    span {
      padding: $inline-element-spacing;
    }
  }
  .basic-info {
    display: flex;
  }
  .info-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-group {
      display: flex;
      justify-content: flex-end;
    }

    .label-group {
      font-size: 14px;
      margin-left: $module-margin-horizontal;
      display: flex;
      flex-direction: column;

      .label {
        color: $text-color-value;
      }

      .value {
        // color: $text-color-value;
        padding-top: $inline-element-spacing;
      }
    }

    .button-group {
      margin-left: $module-margin-horizontal;
    }
  }
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #dcdfe6;
  margin-top: 16px;
}
.no-data {
  color: #999;
  font-size: 14px;
}
</style>
