<template>
    <div class="table-filter">
        <span>{{ title }}</span>
        <span>
            <i 
                class="el-icon-top table-icon-top" 
                v-show="enableSort && sort === 'desc'" 
                @click="handleSort('asc')"
            ></i>
            <i 
                class="el-icon-bottom table-icon-bottom" 
                v-show="enableSort && sort === 'asc'" 
                @click="handleSort('desc')"
            ></i>
            <el-popover
                placement="bottom"
                :width="300"
                trigger="click"
            >
                <template #reference>
                    <span>
                        <i class="el-icon-more table-icon-more"></i>
                    </span>
                </template>
                <slot></slot>
            </el-popover>
        </span>
    </div>
</template>

<script>
/**
 * 表格过滤器组件
 * @module TableFilter
 * @description 用于表格列的过滤功能，支持自定义过滤内容和标题
 * @example
 * <TableFilter
 *   title="过滤器标题"
 *   enableSort
 *   field="columnName"
 *   :defaultSort="'asc'"
 *   @sort="handleSort"
 * >
 *   <el-input placeholder="输入过滤条件" />
 * </TableFilter>
 */
export default {
    name: 'TableFilter',
    props: {
       title: {
           type: String,
           default: ''
       },
       field: {
           type: String,
           default: ''
       },
       defaultSort: {
           type: String,
           default: 'desc' // 可选值：'asc', 'desc', ''
       },
       enableSort: {
           type: Boolean,
           default: false
       }
    },
    data() {
        return {
            sort: this.defaultSort
        }
    },
    methods: {
        handleSort(order) {
            this.sort = order;
            this.$emit('sort', { field: this.field, order });
        }
    },
    
}
</script>

<style scoped>
.table-filter{
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}
.filter-icon{
  width: 16px;
  cursor: pointer;
  transform: translateY(2px);
}
.table-icon-more{
    font-size: 16px;
    cursor: pointer;
    transform: rotate(90deg); 
}
.table-icon-bottom, .table-icon-top{
    font-size: 16px;
    cursor: pointer; 
}
</style>