import request from '@/router/axios';


export const addRelationship = (form) => {
    return request({
        url: '/api/sgsapi/CustomerApi/customerGroup/supplier/external/save',
        method: 'post',
        data: form
    })
}
export const addBuyerRelationship = (form) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/add',
        method: 'post',
        data: form
    })
}

export const addRelationshipNeedApprove = (form) => {
    return request({
        url: '/api/sgs-mart/customer/buyerRelationship/addRelationshipNeedApprove',
        method: 'post',
        data: form
    })
}

export const addSupplier = (form) => {
    return request({
        url: '/api/sgsapi/CustomerApi/customerGroup/supplier/external/save',
        method: 'post',
        data: form
    })
}
export const submitCustomerSupplier = (form) => {
    return request({
        url: '/api/sgs-mart/customer/editCustomerSupplier',
        method: 'post',
        data: form
    })
}


export const getPage = (page, rows, params) => {
    return request({
       // url: '/api/sgsapi/CustomerApi/preOrderCustomer/detail/getCustomerGroupAndSupplyInfo?isAll=1',
        url: '/api/sgs-mart/customer/getSupplierPage',
        method: 'post',
        params: {
            ...params,
            page,
            rows,
        }
    })
}
export const getSupplierPage = (page, rows, params) => {
    return request({
        url: '/api/sgs-mart/customer/getSupplierPageByGroupCode',
        method: 'post',
        params: {
            ...params,
            page,
            rows,
        }
    })
}



export const getCustomerGroupPage = (current, rows, params) => {
    return request({
        url: '/api/sgs-mart/customer/customerGroupInfoPage',
        method: 'post',
        params: {
            ...params,
            current,
            rows,
        }
    })
}



export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/account/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/account/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (form) => {
    return request({
        url: '/api/sgsapi/CustomerApi/customerGroup/supplier/external/delete',
        method: 'post',
        data: form
    })
}

export const approve = (form) => {
    return request({
        url: '/api/sgs-mart/customer/account/approve',
        method: 'post',
        data: form
    })
}
