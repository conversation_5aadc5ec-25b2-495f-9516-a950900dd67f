{"name": "smart-web3", "code": "project_smart", "version": "24.10.15", "author": "Ken<PERSON>@sgs.com", "description": "smart web vue3", "private": true, "type": "module", "scripts": {"local": "vite", "dev": "vite", "test": "vue-tsc -b && vite build --mode test", "uat": "vue-tsc -b && vite build --mode uat", "prod": "vue-tsc -b && vite build --mode production && rm -rf ./dist/*.map", "preview": "vite preview", "lint": "eslint src", "fix": "eslint src --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^0.21.1", "dayjs": "^1.11.13", "element-plus": "^2.7.7", "lodash": "^4.17.21", "sass": "^1.54.9", "vue": "^3.4.31", "vue-i18n": "^10.0.4", "vue-router": "^4.4.0", "vuedraggable": "^4.1.0", "vuex": "^4.1.0"}, "devDependencies": {"@eslint/js": "^8.57.1", "@intlify/unplugin-vue-i18n": "^6.0.3", "@sentry/tracing": "7.43.0", "@sentry/vue": "7.43.0", "@sentry/webpack-plugin": "1.20.0", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.1", "@types/path-browserify": "^1.0.3", "@vitejs/plugin-vue": "^5.0.5", "@vue/test-utils": "^2.4.6", "commitizen": "^4.3.1", "crypto-js": "^4.2.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "globals": "^15.8.0", "js-cookie": "^3.0.5", "js-md5": "^0.7.3", "jsencrypt": "^3.3.1", "path-browserify": "^1.0.1", "prettier": "^3.3.3", "terser": "^5.39.0", "typescript": "^5.7.3", "typescript-eslint": "^7.16.1", "vite": "^5.3.4", "vite-plugin-sentry": "^1.1.7", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.6", "vue-tsc": "^2.2.0"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}}