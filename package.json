{"name": "smart-nodes", "code": "project_nodes", "version": "24.10.15", "author": "Ken<PERSON>@sgs.com", "description": "smart nodejs service", "main": "app.js", "scripts": {"local": "cross-env NODE_ENV=local nodemon app.js", "dev": "cross-env NODE_ENV=dev nodemon app.js", "test": "cross-env NODE_ENV=test nodemon app.js", "uat": "cross-env NODE_ENV=uat nodemon app.js", "prod": "cross-env NODE_ENV=prod nodemon app.js"}, "license": "ISC", "dependencies": {"@sentry/node": "^7.119.2", "@sentry/utils": "^8.55.0", "axios": "^1.8.4", "cross-env": "^7.0.3", "crypto": "^1.0.1", "crypto-js": "^4.0.0", "form-data": "^4.0.2", "koa": "^2.7.0", "koa-body": "^6.0.1", "koa-bodyparser": "^4.2.1", "koa-convert": "^1.2.0", "koa-cors": "0.0.16", "koa-csrf": "^3.0.8", "koa-generic-session": "^2.0.4", "koa-helmet": "^5.2.0", "koa-router": "^7.4.0", "log4js": "^6.9.1", "moment": "^2.24.0", "node-fetch": "^2.6.0", "nodemon": "^2.0.7"}, "private": true}