{"name": "SGSmart", "version": "0.0.1", "description": "#### SGSmart Plus", "private": true, "author": "<PERSON>", "license": "LGPL", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build && rm -rf ./dist/*.map", "test": "vue-cli-service build --mode test", "uat": "vue-cli-service build --mode uat", "test-serve": "vue-cli-service serve --mode test", "uatd": "vue-cli-service serve --mode uat", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@babel/runtime": "^7.8.4", "@riophae/vue-treeselect": "0.0.38", "@sentry/tracing": "7.43.0", "@sentry/vue": "7.43.0", "@sgs/component-v2": "1.0.001", "@sgs/dff-v2": "1.0.021", "@sgs/framework-v2": "1.0.66", "@sgs/sci-todo-list-v2": "1.0.13-RC17-smart", "@sgs/scm-component": "1.5.8", "@sgs/sgs-mart-dataentry-form": "1.0.81", "@sgs/sgs-plugin-form-design-render": "1.2.1", "@smallwei/avue": "2.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "clipboard": "^2.0.1", "echarts": "5.1.0", "el-tree-transfer": "2.2.6", "element-ui": "2.15.13", "exceljs": "4.3.0", "file-saver": "^2.0.2", "html2canvas": "^1.0.0-rc.5", "js-base64": "^2.6.4", "js-cookie": "^2.2.0", "jsencrypt": "^3.3.1", "jspdf": "2.5.1", "jszip": "^3.10.1", "lodash": "^4.17.15", "moment": "^2.29.3", "moment-timezone": "^0.5.43", "node-ipc": "9.1.1", "node-sass": "4.14.1", "normalize.css": "^8.0.0", "nprogress": "^0.2.0", "qrcodejs2": "0.0.2", "script-loader": "^0.7.2", "shepherd.js": "^10.0.0", "sockjs-client": "^1.6.1", "sortablejs": "^1.10.2", "stompjs": "^2.3.3", "viewerjs": "^1.2.0", "vue": "^2.6.11", "vue-axios": "^2.1.2", "vue-i18n": "^8.15.3", "vue-pdf": "^4.1.0", "vue-router": "^3.1.5", "vue-scrollto": "^2.17.1", "vue-seamless-scroll": "^1.1.23", "vue-slider-component": "^3.1.1", "vuedraggable": "^2.16.0", "vuex": "^3.0.1", "xlsx": "^0.15.5"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-transform-runtime": "^7.8.3", "@sentry/webpack-plugin": "1.20.0", "@types/ace": "0.0.42", "@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-service": "^3.1.4", "babel-core": "^6.26.3", "babel-plugin-component": "^1.1.1", "chai": "^4.1.2", "compression-webpack-plugin": "^1.1.12", "core-js": "^2.6.11", "crypto-js": "^4.2.0", "less": "^3.11.1", "less-loader": "^5.0.0", "rollup": "^0.57.1", "rollup-plugin-babel": "^3.0.7", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-uglify-es": "0.0.1", "rollup-plugin-vue": "^3.0.0", "sass-loader": "^7.0.1", "terser-webpack-plugin": "^1.4.3", "vue-grid-layout": "2.1.12", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}, "main": "babel.config.js", "keywords": ["component", "vue", "form", "element-ui", "auto"], "babel": {"presets": ["@vue/app"]}, "eslintConfig": {"root": true, "extends": ["plugin:vue/essential"]}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}