<template>
    <div class="panel panel-default" style="border-radius: 0; border: 0; margin-top: 24px;">
        <!-- <div v-if="step==1">
          <pdf :src='pdfSrc' ref="pdf"></pdf>
        </div>
        <div v-if="step==2">
          <pdf :src='pdfSrc' ref="pdf"></pdf>
        </div> -->

        <el-card>
            <div class="top_btn">
                <el-button size="mini" type="primary" @click="viewPdf(1)">预览报价单</el-button>
                <el-button size="mini" type="primary" @click="viewPdf(2)" v-if="data.contractDocFile">预览合同单</el-button>
            </div>
            <div v-if="step==3">
                <el-table :data="data.quotations" :span-method="objectSpanMethod" border style="width: 100%">
                    <el-table-column type="index" label="序号" width="60"></el-table-column>
                    <el-table-column
                        v-for="(col,index) in allColumn.filter(co=>!hideColumn.includes(co.prop))"
                        :key="index"
                        :label="col.label"
                        :prop="col.prop"
                    >

                    </el-table-column>
                    <el-table-column label="报价状态" width="150" prop="trfStatus">
                        <template slot-scope="scope">
                            {{data.trfStatus==13?'待确认':data.trfStatus==3?'已拒绝':data.trfStatus!=1?'已确认':''}}
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="btn_box" v-if="(step==1||step==2)&&data.quotationFile">
                <el-button size="mini" type="primary" @click="next">下一步</el-button>
            </div>
            <div class="btn_box" v-else-if="step==3&&data.contractDocFile">
                <el-button size="mini" style="margin-right:10px" v-if="data.trfStatus==13" @click="submit(0)">拒绝报价
                </el-button>
                <el-button size="mini" type="primary" v-if="data.trfStatus==13" @click="submit(1)">确认报价</el-button>
                <!-- <el-button size="mini" style="margin-right:10px" v-else-if="data.trfStatus==2">已拒绝该报价</el-button>
                <el-button size="mini" style="margin-right:10px" v-else-if="data.trfStatus!=1">已接受该报价</el-button> -->
            </div>
        </el-card>

        <el-dialog :title="fileType==1?'预览报价单':'预览合同单'" :visible.sync="dialogVisible" width="1000px">
            <pdf :src='pdfSrc' ref="pdf" :page="currentPage" @num-pages="pageCount=$event"
                 @page-loaded="currentPage=$event" @loaded="loadPdfHandler"></pdf>
            <p class="arrow" v-if="pageCount>1">
                <el-button @click="changePdfPage(0)" class="turn" size="mini">上一页</el-button>
                {{currentPage}} / {{pageCount}}
                <el-button @click="changePdfPage(1)" class="turn" size="mini">下一页</el-button>
            </p>
            <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false" size="mini">关闭</el-button>
            <el-button size="mini" type="primary" @click="downLoad" v-if="fileType==1">
                <a :href="downFile">下载报价单</a>
            </el-button>
            <el-button size="mini" type="primary" v-else-if="fileType==2">
                 <a :href="downcontractPdfFile"> 下载合同单</a>
            </el-button>
        </span>
        </el-dialog>
    </div>
</template>

<script>
    import {selectQuotationInfoByTrfId, downloadByCloudID, quotationConfirmed} from '@/api/trf/trf'
    import pdf from 'vue-pdf'
    import {queryBuSetting} from '@/api/common'

    export default {
        data() {
            return {
                data: '', // 数据
                step: 3, // 当前第几步
                trfId: '', // 报价单id
                pdfSrc: '', // pdf src
                pageCount: 0, // pdf文件总页数
                currentPage: 0,
                loading: null, // 加载中
                downFile: '', // 报价单下载地址
                downcontractPdfFile: '',
                fileType: '', // 报价单还是合同单
                dialogVisible: false, // 弹框的显示和隐藏
                num:0,
                hideColumn:[],
                allColumn:[
                    {label:'服务项目',prop:'serviceItemNameZh'},
                    {label:'数量',prop:'qty'},
                    {label:'原价',prop:'standardPrice'},
                    {label:'折扣',prop:'coefficient'},
                    {label:'净值',prop:'amount'},
                    {label:'税后金额',prop:'afterTaxed'},
                    {label:'总金额',prop:'totalAmount'},
                    {label:'税后总金额',prop:'afterTaxedTotalAmount'},
                ]
            }
        },
        components: {
            pdf
        },
        created(){
            this.loadBuSetting();
        },
        mounted() {
            this.trfId = this.$route.query.id
            this.selectQuotationInfoByTrfId()
        },
        methods: {
            selectQuotationInfoByTrfId() {
                this.loading = this.$loading({
                    lock: true,
                    text: '加载中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                let param = new FormData()
                param.append('trfId', this.trfId)
                selectQuotationInfoByTrfId(param).then(res => {
                    this.loading.close();
                    if (res.data.code == 200) {
                        this.data = res.data.data
                        debugger;
                        if (this.data.quotations) {
                           this.num = this.data.quotations.length
                        }
                    } else {
                        this.$message(res.data.msg)
                    }
                }).catch(err => {
                    this.loading.close();
                })
            },
            downloadByCloudID(cloudId) {
                let param = {
                    systemID: 1,
                    networkType: 2,
                    hash: new Date().getTime(),
                    cloudID: cloudId
                }
                downloadByCloudID(param).then(res => {
                    this.loading.close();
                    if (res.data) {
                        let link = res.data.replace(/\s*/g, '')
                        let fileUrl = encodeURIComponent(link)
                        if (this.fileType == 1) {
                            let fileName = this.data.quotationFile.fileName
                            let url = '/api/sgs-mart/afl/trf/fileDownload?fileUrl=' + fileUrl + '&fileUrlName=' + fileName
                            this.downFile = url
                            this.pdfSrc = pdf.createLoadingTask({
                                url: url,
                                cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.5.207/cmaps/',
                                cMapPacked: true
                            })
                        } else if (this.fileType == 2) {
                            let fileName = this.data.contractPdfFile.fileName
                            let url = '/api/sgs-mart/afl/trf/fileDownload?fileUrl=' + fileUrl + '&fileUrlName=' + fileName
                            // this.pdfSrc = pdf.createLoadingTask(url)
                            this.downcontractPdfFile = url
                            this.pdfSrc = pdf.createLoadingTask({
                                url: url,
                                cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.5.207/cmaps/',
                                cMapPacked: true
                            })
                            // this.pdfSrc = 'https://view.xdocin.com/xdoc?_xdoc='+encodeURIComponent(res.data)
                        }

                    }
                }).catch(err => {
                    this.loading.close();
                })
            },
            // 改变PDF页码,val传过来区分上一页下一页的值,0上一页,1下一页
            changePdfPage(val) {
                // console.log(val)
                if (val === 0 && this.currentPage > 1) {
                    this.currentPage--
                    // console.log(this.currentPage)
                }
                if (val === 1 && this.currentPage < this.pageCount) {
                    this.currentPage++
                    // console.log(this.currentPage)
                }
            },
            // pdf加载时
            loadPdfHandler(e) {
                this.currentPage = 1 // 加载的时候先加载第一页
            },
            downLoadDoc() {
                debugger;
                let param = {
                    systemID: 1,
                    networkType: 2,
                    hash: new Date().getTime(),
                    cloudID: this.data.contractPdfFile.cloudId
                }
                downloadByCloudID(param).then(res => {
                    if (res.data) {
                        let dom = document.createElement('a')
                        dom.className = 'downPdfFile'
                        dom.setAttribute('href', res.data)
                        dom.click()
                        dom.remove()
                    }
                }).catch(err => {
                    this.loading.close();
                })
            },
            viewPdf(fileType) {
                this.pdfSrc = ''
                this.fileType = fileType
                this.dialogVisible = true
                if (fileType == 1) {
                    this.downloadByCloudID(this.data.quotationFile.cloudId)
                } else {
                    this.downloadByCloudID(this.data.contractPdfFile.cloudId)
                }
            },
            submit(condirmedFlag) {
                let param = new FormData()
                param.append('trfId', this.trfId)
                param.append('condirmedFlag', condirmedFlag)
                param.append('contractFileId', this.data.contractDocFile.id)
                const loading = this.$loading({
                    lock: true,
                    text: '提交中...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
                quotationConfirmed(param).then(res => {
                    loading.close();
                    if (res.data.code == 200) {
                        this.selectQuotationInfoByTrfId()
                    } else {
                        this.$message(res.data.msg)
                    }
                }).catch(err => {
                    loading.close();
                })
            },
            next() {
                if (this.step == 1) {
                    this.step = 2
                    if (this.data.contractPdfFile) {
                        this.downloadByCloudID(this.data.contractPdfFile.cloudId)
                    }
                } else if (this.step == 2) {
                    this.step = 3
                }
            },
            objectSpanMethod({row, column, rowIndex, columnIndex}) {
                debugger;
                if ( columnIndex === 7 || columnIndex === 8 || columnIndex === 9) {
                    //选择合并哪一列
                    if (rowIndex % this.num === 0 ) {//合并多少行
                        return {
                            rowspan: this.num,  //要合并的行数
                            colspan: 1
                        };
                    } else {
                        debugger;
                        return {
                            rowspan: 0,
                            colspan: 0
                        };
                    }
                }
            },
            loadBuSetting(){
                let param = {
                    systemId:15,
                    groupCode:'QuotationColumnHide',
                    productLineCode:'SGS',
                    paramCode:'hideColumn'
                }
                queryBuSetting(param).then(res=>{
                    console.log("配置返回",res)
                    if(res.status==200 && res.data && res.data.data){
                        let {data} = res.data;
                        if(!data || data.length==0){
                            return;
                        }

                        data = data[0];
                        let {paramValue} = data;
                        try{
                            let values = JSON.parse(paramValue);
                            let af = (values || []).find(v=>v.bu=='AF');
                            if(!af){
                                return;
                            }
                            let {hideColumn} = af;
                            this.hideColumn = hideColumn;
                        }catch (e) {}
                    }
                }).catch(err=>{
                    console.log("配置返回 err",err)
                })
            },
        }
    }
</script>

<style scoped>
    .btn_box {
        text-align: center;
        margin-top: 20px;
    }

    a {
        color: #fff;
    }

    .top_btn {
        text-align: right;
        margin-bottom: 20px;
    }
</style>