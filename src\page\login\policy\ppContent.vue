<template>
    <div class="ppContent" id="ppContent">
        <div
            :is="componentObj.currentView"
        ></div>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import ppContentenus from './ppContentenus';
    import ppContentzhcn from "./ppContentzhcn";

    export default {
        name: "ppContent",
        inject:['calculateHeight'],
        data() {
            return {
                componentObj:{
                    currentView:ppContentenus,
                    ppContentenus:ppContentenus,
                    ppContentzhcn:ppContentzhcn,
                }
            }
        },
        methods: {},
        mounted() {
            this.calculateHeight('pp');
        },
        created() {
            this.componentObj.currentView = this.componentObj['ppContent'+ (this.language.replace("-","").toLowerCase())]
        },
        watch: {
            language:{
                immediate:true,
                handler(newV){
                    this.componentObj.currentView = this.componentObj['ppContent'+ (newV.replace("-","").toLowerCase())]
                }
            }
        },
        computed: {
            ...mapGetters(["language"])
        },
        props: {},
        updated() {
        },
        beforeDestory() {
        },
        destoryed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .ppContent {
        word-wrap: break-word;
        white-space: normal;
        word-break: break-word;
        p.indent2{
            text-indent: 2em;
        }
        p.indent4{
            text-indent: 4em;
        }
        p.indent6{
            text-indent: 6em;
        }
        p.indent8{
            text-indent: 8em;
        }
        p.indent10{
            text-indent: 10em;
        }
        .section{
            font-weight: bold;
            padding-bottom: 5px;
        }
        .textCenter{
            text-align: center;
        }
        a{
            color:#ff6600;
        }
    }
</style>