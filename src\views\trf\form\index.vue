<template>
    <basic-container ref="homePage" class="trf-wrap" :class="isTrfTab ?'trf-active':''">
        <div class="trf-base page-no-print" @mouseleave="progressLeave">
            <div class="info">
                <div>
                    <div class="left">
                        <div class="status">
                            <h4>{{ $t('trfList.trfNo') }}</h4>
                            <span v-if="trf && trf.trfStatus != 0" class="trf-number">{{trf.customerReferenceNo || trf.trfNo}}</span>
                            <span v-else class="trf-no-data">{{ $t('training.NoData') }}</span>
                            <TRFStatus v-if="trf && trf.trfStatus != 0" :status="trf.trfStatus" style="display: inline-block; vertical-align: top; margin-left: 20px;" :reason="trf.pendingReason"/>
                        </div>
                        <!-- <span v-if="trf" class="status-tag" :class="statusArr[trf.trfStatus-1]">{{ $t(status[trf.trfStatus-1]) }}</span> -->
                        <div class="dates">
                            <span class="date" v-if="trf && trf.trfStatus != 0"><span class="tit">{{ $t('user.createTime') }}：</span>{{ trf ? currentTzDate_YMD(trf.newCreateTime,'') : ''}}</span>&nbsp;&nbsp;
                             <span class="date" v-if="trf && trf.trfStatus != 0"><span class="tit">{{ $t('trfList.submitDate') }}：</span>{{ trf && trf.trfSubmissionDate ? currentTzDate(trf.trfSubmissionDate) : $t('trf.notSubmit')  }}</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="item">
                            <p class="tit">{{ $t('buyer.name') }}:</p>
                            <el-tooltip effect="dark" :content="trf ? showBuyerCustomerName : ''" placement="top" v-if="trf && showBuyerCustomerName && showBuyerCustomerName > 20">
                              <strong>{{ trf && showBuyerCustomerName ? showBuyerCustomerName.slice(0,20)+(showBuyerCustomerName.length > 20 ? '...' : '') : $t('training.NoData') }}</strong>
                            </el-tooltip>
                            <strong v-else>{{ trf && showBuyerCustomerName ? showBuyerCustomerName : $t('training.NoData') }}</strong>
                            <span class="is-error" v-if="trfDetail && showBuyerCustomerName == ''">{{$t('trf.buyerSel')}}</span>
                        </div>
                        <div class="item">
                            <p class="tit">{{ $t('quickAccess.other') }}:</p>
                            <el-tooltip effect="dark" :content="trf ? trf.templateName : ''" placement="top" v-if="trf && trf.templateName && trf.templateName.length > 20">
                                <strong>{{ trf && trf.templateName ? trf.templateName.slice(0,20)+(trf.templateName.length > 20 ? '...' : '') : $t('training.NoData') }}</strong>
                            </el-tooltip>
                            <strong v-else>{{ trf && trf.templateName ? trf.templateName : $t('training.NoData') }}</strong>
                            <span class="is-error" v-if="trfDetail && trfDetail.trfTemplateId == ''">{{$t('trf.placeholder.template')}}</span>
                        </div>
                        <div class="item">
                            <p class="tit">{{ $t('service.reportLanguage') }}:</p>
                            <!-- trf.servicRequire.reportLanguageName -->
                            <strong>{{ trf && trf.servicRequire.reportLanguage ? coverLangage(trf.servicRequire.reportLanguage) : $t('training.NoData') }}</strong>
                            <span class="is-error" v-if="trfDetail && trfDetail.servicRequire.reportLanguage == ''">{{$t('select')}}{{$t('service.reportLanguage')}}</span>
                        </div>
                        <div class="item" style="padding-right: 0; border: 0; display: flex;">
                            <el-button @click="modify" v-if="trf && trf.trfStatus < 2 || trf == null" type="text" id="trf-modify-btn">{{ $t('operation.modify') }}</el-button>
                        </div>
                        <!-- <span>{{ $t('buyer.name') }}：{{ trf ? trf.trfCustomer.buyerCustomerGroupName : $t('training.NoData') }}</span>
                        <span>{{ $t('quickAccess.other') }}：{{ trf ? trf.templateName : $t('training.NoData') }}</span>
                        <span>{{ $t('service.reportLanguage') }}：{{ trf ? trf.servicRequire.reportLanguageName : $t('training.NoData') }}</span> -->
                        <!-- <span>{{ $t('user.updateTime') }}：{{ trf ? trf.updateTime : $t('training.NoData') }}</span> -->
                    </div>
                </div>
            </div>

            <div class="clearfix">
                <div class="tits pull-left" id="trf-tabs">
                    <el-button :class="{'active': tabRefresh.trfDetail}" type="text" v-if="permissionList.trfTab" @click="handleClick('trfDetail')">{{ $t('navbar.trfForm') }}</el-button>
                    <!---->
                    <el-button
                                v-if="permissionList.dataEntryTab && trf && trf.id"
                               :class="{'active': tabRefresh.dataEntryResult}"
                                :disabled="[1,2].includes(trf.trfStatus)"
                               type="text"
                               @click="handleClick('dataEntryResult')">{{ $t('navbar.dataEntryForm') }}
                    </el-button>
                    <el-button :class="{'active': tabRefresh.testResult}" type="text" :disabled="permissionList.testResultUnit && trfStatus!=5" @click="handleClick('testResult')">{{ $t('testResult.title') }}</el-button>
                    <el-button :class="{'active': tabRefresh.quotation}" type="text" :disabled="trfStatus==1 || trfStatus=='' || trfStatus==undefined " v-if="permissionList.quotationTab" @click="handleClick('quotation')">{{ productLineCode=='HL' ?  $t('quotation.statement')  :$t('quotation.title') }}</el-button>
                    <el-button :class="{'active': tabRefresh.invoice}" :disabled="trfStatus==1 || trfStatus=='' || trfStatus==undefined " type="text" v-if="permissionList.invoiceTab" @click="handleClick('invoice')">{{ $t('invoice.title') }}</el-button>
                    <el-button :class="{'active': tabRefresh.auditTrail}" type="text" v-if="permissionList.auditTrail" @click="handleClick('auditTrail')">Audit Trail</el-button>
                </div>
                <!-- <top-lang style="margin-top: 17px;"></top-lang> -->
            </div>
            <div class="top" v-if="tabRefresh.trfDetail && (trf && trf.trfStatus < 2 || trf == null)" @mouseenter="progressEnter">
                <div :style="{ width: progress + '%' }">
                    <span class="txt">{{progress}}%</span>
                </div>
            </div>
        </div>

        <div class="content">
            <trf-detail
                ref="trfDetail"
                v-if="tabRefresh.trfDetail"
                @trfId="getTrfId"
                @trf="getTrf"
                @trfDetail="getTrfDetail"
                @formProgress="getProgress"
                @productLineCode="getTrfProductLineCode"
                @reportLanguageData="getReportLanguageData"
                :trfNo.sync="trfNo"
                :trfStatus.sync="trfStatus">
            </trf-detail>
            <data-entry-result
                v-if="tabRefresh.dataEntryResult"
                :trf="trf"
            >
            </data-entry-result>
            <test-result
                v-if="tabRefresh.testResult"
                :trf="trf" :trfNo="trfNo" :testResultSaveFlag="permissionList.testResultSaveBtn">
            </test-result>
            <trf-quotation
                v-if="tabRefresh.quotation"
                approve.sync="false"
                :productLineCode="productLineCode"
                :trfId="trfId">
            </trf-quotation>
            <trf-invoice
                    v-if="tabRefresh.invoice"
                    approve.sync="false"
                    :trfId="trfId">
            </trf-invoice>
            <audit-trail
                v-if="tabRefresh.auditTrail"
                :trfId="trfId"
                :trfNo="trfNo"
                :objectDate="trf.newCreateTime"
            ></audit-trail>
        </div>
    </basic-container>
</template>

<script>
    import {mapGetters} from "vuex";
    import {tzFormChina} from '@/util/datetimeUtils'
    import moment from "moment";
    import {validatenull,objectIsNull,validateEmail} from "@/util/validate";
    import {CustomerUsageEnums} from "@/commons/enums/CustomerUsageEnums";
    export default {
        name: "TestRequestForm",
        components: {
            TestResult: resolve => require(['@/views/trf/form/testResult'], resolve),
            DataEntryResult: resolve => require(['@/views/trf/form/dataEntryResult'], resolve),
            AuditTrail: resolve => require(['@/views/trf/form/auditlog/auditTrail'], resolve),
            TrfDetail: resolve => require(['@/views/trf/form/trfDetail'], resolve),
            TrfQuotation: resolve => require(['@/views/trf/form/trfQuotation'], resolve),
            TRFStatus: resolve => require(['@/components/trf/TRFStatus'], resolve),
            topLang: resolve => require(['@/page/index/top/top-lang'], resolve),
            TrfInvoice: resolve => require(['@/views/trf/form/trfInvoice'], resolve),
        },
        provide(){
            return{
                directPage:this.updateQueryParam
            }
        },
        data() {
            return {
                CustomerUsageEnums:CustomerUsageEnums,
                reportLanguageDatas:[],
                showBuyerCustomerName:'',
                languageNumber:1,
                minScreen: false,
                trf: null,
                isTrfTab: true,
                activeName: 'trfDetail',
                trfId: null,
                productLineCode:'',
                trfNo: null,
                trfStatus: null,
                tabRefresh: {
                    trfDetail: true,
                    dataEntryResult:false,
                    testResult: false,
                    quotation: false,
                    invoice: false,
                    auditTrail:false
                },
                statusArr: ['toSubmitted', 'submitted', 'accepted', 'test', 'completed', 'cancelled'],
                status: ['wel1.draftTrf', 'wel1.trfSubmitted', 'wel1.jobPreparation', 'wel1.testingInProgress', 'wel1.reportCompleted'],
                trfDetail: null,
                progress: 0
                }
            },
        filters: {
          //转换为当前时区
          currentTz :function(value){
            if (!value) return ''
            value = tzFormChina(value);
            return value;
          }
        },
        computed: {
            ...mapGetters(["permission", "language"]),
            permissionList() {
                return {
                    trfTab: this.vaildData(this.permission['sgs:trf:trfTab'], false),
                    dataEntryTab: this.vaildData(this.permission['sgs:trf:dataEntryTab'], false),
                    quotationTab: this.vaildData(this.permission['sgs:trf:quotationTab'], false),
                    invoiceTab: this.vaildData(this.permission['sgs:trf:invoice:tab'], false),
                    auditTrail: this.vaildData(this.permission['sgs:trf:auditTrail:tab'], false),
                    testResultUnit: this.vaildData(this.permission['sgs:trf:testResult'], false),
                    testResultSaveBtn: this.vaildData(this.permission['sgs:trf:testResult:save:btn'], false),
                };
            }
        },
        watch: {
            //监听语言变化
            language: function (newVal) {
                this.coverLangage(this.trf.servicRequire.reportLanguage)
            },
        },
        methods: {
          currentTzDate(val){
            if (!val) return ''
            let value = tzFormChina(val,'YYYY-MM-DD HH:mm');
            return value;
          },
          currentTzDate_YMD(val){
            if (!val) return ''
            let value = tzFormChina(val,'YYYY-MM-DD HH:mm:ss');
            return moment(value).format('YYYY-MM-DD');
          },
            coverLangage(langage){
                let obj = this.reportLanguageDatas.find((item) => {
                    return item.sysKey === langage;
                });
                if(!objectIsNull(obj)){
                    return obj.sysValue;
                }
               return '';
                // let en=['English Report', 'Chinese Report', 'Chinese&English Report']
                // let cn=['英文报告', '中文报告', '中英文报告']
                // return this.language=='zh-CN' ? cn[langage-1] : en[langage-1]
            },
            progressEnter(e) {
                this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                let style = document.querySelector('.trf-base').style
                console.log('ENTER', style.position)
                if(style.position == 'fixed') {
                    if(this.minScreen) {
                        style.transform = 'translateY(150px)'
                    } else {
                        style.transform = 'translateY(172px)'
                    }
                }
            },
            progressLeave(e) {
                console.log('LEAVE')
                let style = document.querySelector('.trf-base').style
                if(style.position == 'fixed') style.transform = 'translateY(0px)'
            },
            getProgress(val) {
                //console.log('获取填写进度：', val);
                this.progress = val;
                // 进度条完成后，更新表单填写状态
                if (!this.isUpdatingFilledStatus) {
                    this.isUpdatingFilledStatus = true;
                    if (
                        this.$refs.trfDetail &&
                        this.$refs.trfDetail.$refs.serviceRequireRef &&
                        this.$refs.trfDetail.$refs.serviceRequireRef.$refs.hlRequirementRef
                    ) {
                        this.$refs.trfDetail.$refs.serviceRequireRef.$refs.hlRequirementRef.updateFilledStatus();
                    }
                    this.isUpdatingFilledStatus = false;
                }
            },
            getTrfProductLineCode(productLineCode){
              this.productLineCode = productLineCode;
            },
            getTrfId(trfId) {
                this.trfId = trfId;
            },
            getTrf(trf) {
                this.trf = trf;
                this.showBuyerCustomerName=this.getBuyerCustomerName();
                console.log('GETTRF:::', this.trf)
                //获取路由配置，刷新页面后，回到之前的tab页面
                let {clickTab} = this.$route.query;
                let keys = Object.keys(this.tabRefresh)
                if(keys.includes(clickTab) && !this.tabRefresh[clickTab]){
                    this.handleClick(clickTab);
                }
            },
            getTrfDetail(trf) {
                console.log('TRFDETAIL::', trf)
                this.trfDetail = trf
                this.trf = trf
                this.showBuyerCustomerName=this.getBuyerCustomerName();
                this.productLineCode=this.trf.productLineCode
            },
            getReportLanguageData(reportLanguageData){
                this.reportLanguageDatas = reportLanguageData;
            },
            getBuyerCustomerName(){
                debugger;
              let name=this.trf.trfCustomer.buyerCustomerGroupName;
              if(objectIsNull(name)){
                name=this.trf.trfCustomer.buyerCustomerName;
              }
              if(!objectIsNull(name)){
                return name;
              }
              if(!objectIsNull(this.trf.customerList)){
                let buyerCustomerObj = this.trf.customerList.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.BUYER.code) {
                        return item;
                    }
                });
                name= objectIsNull(buyerCustomerObj.customerGroupName)?buyerCustomerObj.customerName :buyerCustomerObj.customerGroupName;
              }

              return name;
            },
            handleClick(tab, event) {
                let style = document.querySelector('.trf-base').style
                style.position = 'relative'
                style.width = '100%'
                style.top = '0'
                style.left = '0'

                if ('trfDetail' == tab) {
                    this.isTrfTab = true;
                } else {
                    this.isTrfTab = false;
                }

                Object.keys(this.tabRefresh).forEach(item => {
                    this.tabRefresh[item] = false;
                })
                this.tabRefresh[tab] = true;
                this.updateQueryParam(tab)
                // document.title = "TRF Detail";
            },
            modify() {
                this.$refs.trfDetail.editTemplate()
            },
            updateQueryParam(value){
                let uri = window.location.href;
                let key = 'clickTab';
                let re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
                let separator = uri.indexOf('?') !== -1 ? "&" : "?";
                let newUrl = uri;
                if (uri.match(re)) {
                    newUrl = uri.replace(re, '$1' + key + "=" + value + '$2');
                } else {
                    newUrl = uri + separator + key + "=" + value;
                }
                //console.log("2222222222",value)
                window.location.href = newUrl;
            },
        },
        created() {
            // document.title = 'TRF Detail'
        },
        mounted() {
            let that = this
            if(this.language=='zh-CN'){
                this.languageNumber=2;
            } else {
                this.languageNumber=1;
            }
        }
    }
</script>

<style scoped lang="scss">
@import "@/styles/unit.scss";

.trf-base {
    background: #fff;
    padding: 22px 32px 11px;
    margin-top: 0;
    position: relative;
    width: 100%;
    left: 0;
    z-index: 9;
    top: 0;
    transition: transform .2s;
    .trf-number {
        font-size: 24px;
        font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
        color: #000000;
        line-height: 24px;
    }
    .trf-no-data {
        color: #999;
        font-size: 14px;
    }
    .info {
        border-bottom: 1px solid #E6E6E6;
        padding-bottom: 24px;
        /* margin-bottom: 15px; */
        h4 {
            font-size: 16px;
            font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
            color: #656565;
            line-height: 20px;
        }
        > div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .left {
                .status {
                    float: left;
                }
                .dates {
                    float: left;
                    margin-top: 43px;
                }
                .date {
                    font-size: 14px;
                    font-weight: 400;
                    color: #656565;
                    vertical-align: text-top;
                    margin-top: -2px;
                    display: inline-block;
                }
            }
            .right {
                display: flex;
                margin-top: 38px;
                .item {
                    padding: 0 24px;
                    font-size: 14px;
                    font-family: "Regular",Arial, "localArial", "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
                    font-weight: 400;
                    color: #000;
                    line-height: 22px;
                    border-right: 1px solid  #e6e6e6;
                    p {
                        margin-bottom: 2px;
                        display: inline-block;
                    }
                    strong {
                        font-weight: 400;
                        padding-left: 10px;
                    }
                    button {
                        padding: 0;
                    }
                }
            }
        }
    }
}
.tits {
    border: 1px solid #E6E6E6;
    margin-top: 11px;
    padding: 4px;
    width: fit-content;
    button {
        padding: 0 16px;
        height: 32px;
        line-height: 32px;
        &:not(.is-disabled) {
            color: #1b1b1b;
            cursor: pointer;
            &.active {
                color: $primary-color;
            }
        }
    }
}

.trf-wrap {
    margin-top: 0px;
    .content {
        /* margin-top: 70px; */
        padding-bottom: 200px;
    }
}

.status-tag {
  font-size: 12px;
  font-weight: 400;
  padding: 0 6px;
  height: 24px;
  line-height: 24px;
  display: inline-block;
  text-align: center;
  margin: 0 10px;

  &.toSubmitted {
    background: rgba(251, 189, 4, 0.08);
    color: rgb(251, 189, 4);
  }
  &.submitted {
    background: rgba(67, 133, 244, 0.08);
    color: rgb(67, 133, 244);
  }
  &.accepted {
    background: rgba(255, 102, 0, 0.08);
    color: rgb(255, 102, 0);
  }
  &.test {
    background: rgba(234, 67, 54, 0.08);
    color: rgb(234, 67, 54);
  }
  &.completed {
    background: rgba(52, 168, 83, 0.08);
    color: rgb(52, 168, 83);
  }
  &.cancelled {
    background: rgba(165, 165, 165, 0.08);
    color: rgb(165, 165, 165);
  }
}
.is-error {
    color: red;
    font-size: 12px;
    display: block;
}
.top {
    background: #E6E6E6;
    height: 6px;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -5px;
    cursor: pointer;
    > div {
        background: rgb(255, 102, 0);
        height: 100%;
        transition: all 1s;
        position: relative;
        .txt {
            background: #f60;
            height: 16px;
            position: absolute;
            right: -15px;
            top: 10px;
            font-size: 12px;
            line-height: 14px;
            padding: 2px 5px;
            color: #fff;
            &::after {
                content: ' ';
                position: absolute;
                top: -8px;
                width: 0;
                height: 0;
                border: 4px solid transparent;
                border-bottom-color: #f60;
                left: 41%;
            }
        }
    }
}
@media screen and (max-width: 1600px) {
    .trf-base {
        .info > div {
            .left {
                .dates {
                    margin-top: 25px;
                }
                .date {
                    .tit {
                        display: block;
                    }
                }
            }
            .right {
                margin-top: 22px;
                .item {
                    p {
                        display: block;
                        color: #656565;
                    }
                    strong {
                        padding-left: 0;
                    }
                }
            }
        }
    }
}
@media screen and (max-width:1366px) {
    .trf-base {
        padding-top: 7px;
        padding-bottom: 10px;
        .info {
            padding-bottom: 11px;
        }
    }
}
</style>
