<template>
  <div class="login-container" style="overflow-y: hidden; position: relative">
    <div class="login-container-div"></div>
    <el-row class="login-section" @keyup.enter.native="handleLogin">
      <div class="clearfix" style="width: 100%; position: relative">
        <el-col :span="16">
          <h2 class="login-section-title">
            SGS SMART is a one-stop supply chain management customer portal for
            testing, inspection and analytics.
          </h2>
          <!--<p class="sub-title"><span>We are SGS</span> <span> – the world's leading testing, inspection and certification company.</span></p>-->

          <div
            class="login-section-video"
            style="width: 560px; height: 370px; position: absolute; bottom: 5px"
          >
            <div
              id="playVideoDiv"
              @click="playVideo"
              class="playVideoDiv"
              style="
                position: absolute;
                width: 560px;
                height: 320px;
                z-index: 3;
              "
            ></div>
            <video
              style="z-index: 2"
              zIndex="2"
              id="homeVideo"
              src="/video/SGSSMARTpromovideo.mp4"
              controls
              preload="none"
              x-webkit-airplay="false"
              webkit-playsinline="true"
              width="560px"
              height="320px"
            ></video>
            <h4
              style="
                color: #fff;
                margin-top: 20px;
                font-weight: 600;
                font-size: 21px;
              "
            >
              Click
              <a
                href="https://try.sgs.com/en/cp/sgs-smart/"
                target="_blank"
                style="color: white"
                ><u>HERE</u></a
              >
              to learn more about SGS SMART
            </h4>
          </div>
        </el-col>
        <!--<el-col :span="6" :offset="2">
            <div class="logo text-right"><img src="/img/SGS_logo.png" width="auto" height="120" alt="SGS"></div>
          </el-col>-->

        <el-col :span="6" :offset="2">
          <el-card class="pull-right">
            <div class="logo text-left" style="position: relative">
              <img
                src="/img/SGS_logo.png"
                width="auto"
                height="100"
                alt="SGS"
              />
              <top-lang
                isLogin
                :loadMenu="false"
                :is-load-lanage-by-ip="true"
                style="position: absolute; right: 0; bottom: -50px; z-index: 9"
              ></top-lang>
            </div>
            <el-tabs v-model="tabName">
              <el-tab-pane :label="$t('login.title')" name="login">
                <div class="login-wrap">
                  <userLogin></userLogin>
                  <!-- <userLogin :key="Math.random()" v-if="activeName==='user'"></userLogin>
                    <codeLogin v-else-if="activeName==='code'"></codeLogin>
                    <thirdLogin v-else-if="activeName==='third'"></thirdLogin>-->
                </div>
                <div class="login-menu" style="text-align: left"></div>
              </el-tab-pane>
              <el-tab-pane :label="$t('login.toRegister')" name="register">
                  <div class="login-wrap">
                    <new-register></new-register>
                  </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </div>
    </el-row>

    <div id="footer" class="footer">
      <div class="wrap-lg text-center" style="color: #fff" v-if="isShowFiling">
        <span>© {{ currentYear }} SGS SA</span>
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="http://beian.miit.gov.cn"
          >京ICP备16004943号-7</a
        >
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802032115"
        >
          <img src="@/images/gaicon.png" />
          <span>京公网安备 11010802032115号</span>
        </a>
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="https://www.sgsonline.com.cn/static/upload/2019/09/10/1tif.png"
          >增值电信业务经营许可证合字B2-20190019</a
        >
        <a
          style="margin-left: 10px"
          rel="nofollow"
          target="_blank"
          href="https://www.sgsgroup.com.cn/zh-cn/privacy-at-sgs"
          >隐私政策</a
        >
      </div>
    </div>
  </div>
</template>
<script>
import { refeshToken, queryLoginLanguage } from "@/api/user";
import { LanguageEnums } from "@/commons/enums/LanguageEnums";
import { changePasswordUrl } from "@/config/env";
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
import { validatenull } from "@/util/validate";
import NewRegister from "@/page/login/newRegister";
export default {
  name: "login",
  components: {
      NewRegister:(resolve)=>require(["./newRegister"], resolve),
    register: (resolve) => require(["./register"], resolve),
    userLogin: (resolve) => require(["./userlogin"], resolve),
    codeLogin: (resolve) => require(["./codelogin"], resolve),
    thirdLogin: (resolve) => require(["./thirdlogin"], resolve),
    topLang: (resolve) => require(["@/page/index/top/top-lang"], resolve),
    topColor: (resolve) => require(["@/page/index/top/top-color"], resolve),
  },
  data() {
    return {
      currentYear: new Date().getFullYear(),
      LanguageEnums: LanguageEnums,
      isShowFiling: false,
      changePasswordUrl: "",
      time: "",
      activeName: "user",
      tabName: "login",
      token: this.$route.params.token,
      redirect: this.$route.params.redirect,
    };
  },
  watch: {
    $route() {
      if (!validatenull(this.token)) {
        this.loginByToken();
      }
    },
  },
  created() {
    //this.reload();
    //初始化登录页 设置为英文
    var lang = "en-US";
    this.$i18n.locale = lang;
    this.$store.commit("SET_LANGUAGE", lang);
    let tag = this.tag;
    let title = this.$router.$avueRouter.generateTitle(
      tag.label,
      (tag.meta || {}).i18n
    );
    //根据当前的标签也获取label的值动态设置浏览器标题
    // this.$router.$avueRouter.setTitle(title);
    /*if(this.loadMenu){
      this.$store.dispatch("GetMenu",this.language).then(data => {
        if (data.length === 0) return;
        this.$router.$avueRouter.formatRoutes(data, true);
      });
    }*/

    this.changePasswordUrl = changePasswordUrl;
    this.getTime();
    setInterval(() => {
      this.getTime();
    }, 1000);
  },
  mounted() {
    this.queryLoginLanguage();
  },
  computed: {
    ...mapGetters(["website", "language", "tag"]),
  },
  props: [],
  methods: {
    queryLoginLanguage() {
      queryLoginLanguage()
        .then((res) => {
          var language = res.data.data;
          if (language != LanguageEnums.CN.name) {
            this.isShowFiling = false;
          } else {
            this.isShowFiling = true;
          }
        })
        .catch((error) => {
          this.isShowFiling = false;
        });
    },
    openChangePassword() {
      window.open(this.changePasswordUrl);
    },
    getTime() {
      this.time = dateFormat(new Date());
    },
    loginByToken() {
      const loading = this.$loading({
        lock: true,
        text: `登录中,请稍后。。。`,
        spinner: "el-icon-loading",
      });

      this.$store
        .dispatch("LoginByToken", this.token)
        .then(() => {
          if (!validatenull(this.redirect)) {
            this.$store.dispatch("GetMenu").then((data) => {
              if (data.length === 0) return;
              this.$router.$avueRouter.formatRoutes(data, true);
              this.$router.push({ path: this.redirect });
              loading.close();
            });
          } else {
            this.$router.push({ path: "/" });
            loading.close();
          }
        })
        .catch(() => {
          loading.close();
        });
    },
    playVideo() {
      let videoDom = document.getElementById("homeVideo");
      videoDom.play();
      document.getElementById("playVideoDiv").style.display = "none";
    },
  },
};
</script>

<style lang="scss">
.login-container {
  background: url("/img/loginBackground.png") no-repeat center bottom / cover;
  &::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    background: white;
    opacity: 0.5;
  }
  height: 100%;
  .login-section {
    width: 1440px;
    height: 100%;
    margin: 0 auto;
    padding-top: 14vh;
    .login-section-title {
      font-size: 48px;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 0 -120px;
    }
    .login-section-video {
      margin: 0 0 0 -120px;
      .playVideoDiv {
        background: url("/video/videoPage.jpg") no-repeat center bottom / cover;
      }
    }
    .sub-title {
      font-size: 20px;
      font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
        "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
      color: #3c515b;
      line-height: 30px;
      margin: 0 0 28px -120px;
    }
    .login-title {
      margin: 0;
    }
    .login-wrap {
      margin-top: 20px;
    }
    .el-tabs {
      .el-tabs__nav {
        .el-tabs__item {
          font-size: 24px;
          /* padding: 0 12px; */
          font-weight: 400;
          color: #999999;
          &.is-active {
            font-weight: 500;
            color: #1b1b1b;
          }
          &:first-child {
            padding-right: 24px;
          }
          &:last-of-type {
            padding: 0;
          }
        }
      }
    }
    .el-card {
      position: relative;
      padding: 10px 20px 10px;
      border-radius: 0;
      width: 470px;
      height: 550px;
    }
  }
  .classification {
    margin-top: 50px;
    .el-card {
      img {
        width: 100%;
      }
      .el-card__text {
        padding: 14px;
        height: 230px;
      }
    }
  }
  .featured {
    background: url("/img/bg/homepage-mountains.jpg") center no-repeat;
    background-size: cover;
    height: 400px;
    .el-card {
      margin-top: 35%;
      text-align: center;
      background-color: transparent;
      dd {
        font-size: 4.8em;
      }
    }
  }
  .services {
    background-color: #ffffff;
    text-align: center;
  }
  .about {
    background: url("/img/bg/about.jpg") center no-repeat;
    background-size: cover;
    height: 200px;
    .content {
      padding: 48px;
      padding: 3rem;
      border: 1px solid #fff;
      text-align: center;
      color: #fff;
    }
  }

  .top-icon {
    color: #303133 !important;
  }
  /*.login-container-div{
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: #252525;
      opacity: 0.3;
    }*/
}
.wrap-lg {
  span {
    color: #fff !important;
  }
  /* position: fixed;
    bottom: 16px;
    width: 100%;
    color: #fff;
    font-size: 16px;
    margin-top: -45px; */
  position: relative;
  top: -40px;
  font-size: 16px;
  color: #fff;
  a {
    span {
      &:hover {
        color: #23527c !important;
      }
    }
    color: #fff !important;
    &:hover {
      color: #23527c !important;
    }
  }
}
@media screen and (max-width: 1700px) {
  .login-container {
    .login-section {
      padding-top: 15vh;
      .login-section-title,
      .sub-title {
        margin-left: -30px;
      }
      .login-section-video {
        margin-left: -30px;
      }
    }
  }
}
@media screen and (max-width: 1600px) {
  .login-container {
    .login-section {
      padding-top: 8vh;
      .login-section-title,
      .sub-title {
        margin-left: 30px;
      }
      .login-section-video {
        margin-left: 30px;
      }
    }
  }
}
@media screen and (max-width: 1440px) {
  .login-container {
    .login-section {
      padding-top: 6vh;
      width: 1200px;
      .login-section-title,
      .sub-title,
      .login-section-video {
        margin: 10px;
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .login-container {
    .login-section {
      padding-top: 4vh;
      width: 1200px;
      .login-section-title {
        font-size: 38px;
      }
      .sub-title {
        margin-bottom: 0px;
        /* white-space: nowrap; */
      }
      .logo {
        padding-top: 20px;
      }
      .el-card__body {
        padding: 0;
      }
      .login-wrap {
        .el-form-item {
          margin-bottom: 14px !important;
        }
      }
    }
  }
  .wrap-lg {
    top: -25px;
    font-size: 14px;
  }
}
</style>
