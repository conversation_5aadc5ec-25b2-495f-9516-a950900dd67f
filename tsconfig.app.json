{
  "compilerOptions": {
    "baseUrl": ".",
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "paths": {
      "@/*": ["src/*"],
      "vuex": ["./node_modules/vuex/types/index.d.ts"],
      "@intlify/vite-plugin-vue-i18n": [
        "./node_modules/@intlify/vite-plugin-vue-i18n/lib/index.d.ts"
      ]
    },

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",
    "typeRoots": ["node_modules/@types", "node_modules/vuex/types"],

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]
}
