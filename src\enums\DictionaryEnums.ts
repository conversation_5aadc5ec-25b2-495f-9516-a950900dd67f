"use strict";
//字典枚举

export const DictionaryEnums = {
    ServiceRequirement: {
        ReturnSample: {
            name: 'ReturnSampleWay',
            buId: 1,
            systemId: 0,
            cnKey: 'ReturnSampleCN',
            enKey: 'ReturnSample',
            desc: "退样要求"
        },
        Accreditation: {
            name: 'Accreditation',
            buId: 2,
            systemId: 15,
            cnKey: 'AccreditationType',
            enKey: 'AccreditationType',
            desc: '报告资质要求'
        },
        ReportSymbol: {
            name: 'reportSymbol',
            buId: 1,
            systemId: 1,
            cnKey: 'ReportSymbol',
            enKey: 'ReportSymbol',
            desc: '报告需出具标志'
        },
        report: {
            name: 'Accreditation',
            buId: 2,
            systemId: 15,
            cnKey: 'AccreditationType',
            enKey: 'AccreditationType',
            desc: '报告资质要求'
        },
        SoftCopyDeliverTo: {
            code: 1,
            name: 'SoftCopyDeliverTo',
            desc: '电子报告发送联系人(已选角色)'
        },
        SoftCopyDeliverToOther: {
            name: 'SoftCopyDeliverToOther',
            desc: '电子报告发送联系人(其他角色, 实际录入)'
        },
        HardCopyDeliverTo: {
            code: 2,
            name: 'HardCopyDeliverTo',
            desc: '纸质报告发送联系人(已选角色)'
        },
        HardCopyDeliverToOther: {
            name: 'HardCopyDeliverToOther',
            desc: '纸质报告发送联系人(其他角色, 实际录入)'
        },
        InvoiceDeliverTo: {
            code: 5,
            name: 'InvoiceDeliverTo',
            desc: '发票邮寄联系人(已选角色)'
        },
        InvoiceDeliverToOther: {
            name: 'InvoiceDeliverToOther',
            desc: '发票邮寄联系人(其他角色, 实际录入)'
        },
        ReturnSampleDeliverTo: {
            code: 4,
            name: 'ReturnSampleDeliverTo',
            desc: '退样发送联系人(已选角色)'
        },
        ReturnSampleDeliverToOther: {
            name: 'ReturnSampleToOther',
            desc: '退样发送联系人(其他角色, 实际录入)'
        },
        CustomerRoles: {
            name: 'CustomerRoles',
            items: [{
                code: 1,
                enKey: 'Applicant',
                cnKey: '申请方',
                desc: '申请方',
                inputFlag: false
            }, {
                code: 2,
                enKey: 'Payer',
                cnKey: '付费方',
                desc: '付费方',
                inputFlag: false
            }, {
                code: 3,
                enKey: 'Buyer',
                cnKey: '买家',
                desc: '买家',
                inputFlag: false
            }, {
                code: 5,
                enKey: 'Other',
                cnKey: '其他',
                desc: '其他',
                inputFlag: true
            }]
        },
        InvoiceDeliverWays: {
            name: 'DeliverWays',
            items: [
                {
                    code: "Softcopy by E-Mail",
                    enKey: 'Softcopy by E-Mail',
                    cnKey: '邮件发送',
                    desc: '',
                    inputFlag: false
                },
                {
                code: "Express-Freight Prepaid",
                enKey: 'Express-Freight Prepaid',
                cnKey: '快递预付',
                desc: '',
                inputFlag: false
            },{
                code: "Express-Freight Collect",
                enKey: 'Express-Freight Collect',
                cnKey: '快递到付',
                desc: '',
                inputFlag: false
            },{
                code: "Client Pick Up",
                enKey: 'Client Pick Up',
                cnKey: '客户自取',
                desc: '',
                inputFlag: false
            },{
                code: "To Be Determined",
                enKey: 'To Be Determined',
                cnKey: '待确认',
                desc: '',
                inputFlag: false
            },{
                code: "Other",
                enKey: 'Other',
                cnKey: '其它',
                desc: '',
                inputFlag: false
            }],
            desc: "deliver 方式"
        },
        DeliverWays: {
            name: 'DeliverWays',
            items: [               
                {
                code: "Express-Freight Prepaid",
                enKey: 'Express-Freight Prepaid',
                cnKey: '快递预付',
                desc: '',
                inputFlag: false
            },{
                code: "Express-Freight Collect",
                enKey: 'Express-Freight Collect',
                cnKey: '快递到付',
                desc: '',
                inputFlag: false
            },{
                code: "Client Pick Up",
                enKey: 'Client Pick Up',
                cnKey: '客户自取',
                desc: '',
                inputFlag: false
            },{
                code: "To Be Determined",
                enKey: 'To Be Determined',
                cnKey: '待确认',
                desc: '',
                inputFlag: false
            },{
                code: "Other",
                enKey: 'Other',
                cnKey: '其它',
                desc: '',
                inputFlag: false
            }],
            desc: "deliver 方式"
        },
        LiquidInkType: {
            name: 'LiquidInkType',
            items: [{
                code: 1,
                enKey: 'Drying Testing 105°C',
                cnKey: '105°C加热烘干',
                desc: '105°C加热烘干'
            },
            {
                code: 2,
                enKey: 'Direct Testing',
                cnKey: '直接测试',
                desc: '直接测试'
            }],
            desc: '油墨、液体样品处理方式'
        },
        InvoiceType: {
            name: 'VatType',
            items: [{
                code: 1,
                enKey: 'Special Invoice',
                cnKey: '增值税专用发票',
                desc: '增值税专用发票'
            },
            {
                code: 2,
                enKey: 'Ordinary Invoice',
                cnKey: '普通发票',
                desc: '普通发票'
            }],
        desc: '发票类型'
        },
        desc: 'Trf Service requirement 字典'
    },
    Attachment: {
        AttachmentType: {
            name: 'AttachmentType',
            items: [{
                code: 'SupportingDocuments',
                enKey: 'Supporting Documents',
                cnKey: '支持文件',
                desc: '支持文件'
            },
                {
                    code: 'SamplePhoto',
                    enKey: 'Sample Photo',
                    cnKey: '样品图',
                    desc: '样品图'
                }],
            desc: '上传类型'
        },
        AttachmentSource:{
            code: 'Upload',
            enKey: 'Upload',
            cnKey: '上传',
            desc: '上传'
        },
        CommentAttachmentType:{
            name:'commentAttachmentType',
            items:[
                {
                    code: 'AP',
                    enKey: 'Action Plans',
                    cnKey: '行动计划',
                    desc: '行动计划'
                },
                {
                    code: 'BM', 
                    enKey: 'Bill of Materials', 
                    cnKey: '物料清单', 
                    desc: '物料清单'
                },
                {
                    code: 'COC',
                    enKey: 'Certificates of Compliance - COC',
                    cnKey: '合格证书',
                    desc: '合格证书'
                },
                {
                    code: 'CPC',
                    enKey: 'Children Product Certification - CPC',
                    cnKey: '儿童产品认证',
                    desc: '儿童产品认证'
                },
                {   
                    code: 'CP',
                    enKey: 'Corrective Actions', 
                    cnKey: '整改措施', 
                    desc: '整改措施'
                },
                {
                    code: 'GCC',
                    enKey: 'General Conformity Certificate - GCC',
                    cnKey: '一般合格证书',
                    desc: '一般合格证书,'
                },
                {
                    code: 'PS', 
                    enKey: 'Product Specification',
                    cnKey: '产品规格', 
                    desc: '产品规格'},
                {
                    code: 'SD',
                    enKey: 'Supporting Documents',
                    cnKey: '支持文件',
                    desc: '支持文件'
                },
                {
                    code: 'RWD',
                    enKey: 'Report without DSS',
                    cnKey: 'Report without DSS',
                    desc: 'Report without DSS'
                },
            ]

        }
    },
    ServiceUnit:{
        items:[
            {
                code: 'SL Testing',
                enKey: 'SL Testing',
                cnKey: 'SL 测试',
                desc: 'SL 测试',
                default:true,
            },
            {
                code: 'SL Inspection',
                enKey: 'SL Inspection',
                cnKey: 'SL 抽检',
                desc: 'SL 抽检',
                default:false,
            },
            {
                code: 'HL Testing',
                enKey: 'HL Testing',
                cnKey: 'HL 测试',
                desc: 'HL 测试',
                default:false,
            },
            {
                code: 'HL Inspection',
                enKey: 'HL Inspection',
                cnKey: 'HL 抽检',
                desc: 'HL 抽检',
                default:false,
            }
        ]
    }
}
