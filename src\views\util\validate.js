import _ from 'lodash'
/**
 * validate email
 * @param email
 * @returns {boolean}
 */
export function validateEmail(email) {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return re.test(email)
}

/**
 * 生成订单 验证数据 规则描述
 */
import store from './../store'
export function generalOrderDescriptor(productInstances) {
  // 获取dff head dff 配置
  let dffHeadData = store.getters.dffHeadData
  let dffGridData = store.getters.dffGridData
  let productInstanceFields = {}, changeAttrField = []
  if (dffHeadData) {
    dffHeadData.forEach(item => {
      if (!item || item.displayInSystem.indexOf('1') == -1) {
        return
      }
      const dffFormAttrEvents = item.dffFormAttrEvents
      if (dffFormAttrEvents && dffFormAttrEvents.length > 0) {
        let filterChangeAttr = _.filter(dffFormAttrEvents, {eventType: "onchange-attribute"})
        if (filterChangeAttr && filterChangeAttr.length > 0) {
          let source = null
            _.each(filterChangeAttr, filterItem => {
            _.each(filterItem.dffFormEventEffects, effect=> {
              source = effect.sourceAttrFieldCode
              let effectValue = effect.dffAttrValues.map(value => {
                return value.valueCode
              })
              changeAttrField.push({field:_.trim(_.lowerFirst(effect.effectedAttrFieldCode)), value: effectValue, source: _.lowerFirst(source)})
            })
          })
        }
      }
      if (item.mandatoryFlag == 1) {
        productInstanceFields[_.lowerFirst(item.fieldCode)] = {type: 'string', required: true}
      }
    })
    _.forOwn(productInstanceFields, (value, key) => {
      let filter = _.filter(changeAttrField, {field: key})
      if (filter && filter.length>0 ) {
        let filterItem = filter[0]
        productInstanceFields[key] = []
        productInstanceFields[key].push({validator(rule, value, callback, source, options) {
          let errors = []
          let b = false
          if (source[filterItem.source]) {
            source[filterItem.source].split(',').forEach(item => {
              if (filterItem.value.indexOf(item) > -1 ){
                b = true;
                return
              }
            })
          }
          if (b && _.isEmpty(value)) {
            errors.push(new Error())
          }
          callback(errors);
        }})

      }
    })
  }
  if (dffGridData && productInstances) {
    let fileds = {}
    productInstances.forEach((productItem, productIndex) => {
      let fieldsItem = {type: 'object', fields: {}}
      dffGridData.forEach(item => {
        if (!item || item.displayInSystem.indexOf('1') == -1) {
          return
        }
        if (item.mandatoryFlag == 1) {
          fieldsItem.fields[_.lowerFirst(item.fieldCode)] =  {type: 'string', required: true, message: 'the field is required'}
        }
        if (item.fieldCode.toUpperCase() === 'NoOfSample'.toUpperCase()) {
          fieldsItem.fields[_.lowerFirst(item.fieldCode)] =  [
            {validator(rule, value, callback, source, options) {
                let errors = []
                if (item.mandatoryFlag == 1 && !value) {
                  errors.push(new Error("the field is required"))
                }
                if (value) {
                  try {
                    if (_.isNaN(_.toNumber(value))) {
                      errors.push(new Error("the field value is number type"))
                    }
                  } catch (error){
                    errors.push(new Error("the field value is number type"))
                  }
                }
                callback(errors)
              }}
          ]
        }
      })
      fileds[productIndex] = fieldsItem
    })
    productInstanceFields['productInstances'] = {type: 'array', fields: fileds}
  }
  return {
    generalOrder: {
      type: 'object',
      fields: {
        serviceLevel: {type: 'number', required: true}
      }
    },
    labInstance: {
      type: 'object',
      fields: {
        labId: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value) {
                error.push(new Error("the field is required"))
              }
              callback(error)
            }
          }
        ],
        labContact: {type: 'string', required: true}
      }
    },
    productInstance: {
      type: 'object',
      fields: productInstanceFields
    },
    applicantCustomerInstance: {
      type: 'object',
      required: true,
      fields: {
        customerAddressCn: [
          {
            validator(rule, value, callback, source, options) {
              let error = []
              // 二者选其一
              if (!value && !source.customerAddressEn) {
                error.push(new Error("Address is required!"))
              }
              callback(error)
            }
          }
        ],
        customerAddressEn: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.customerAddressCn) {
                error.push(new Error("Address is required!"))
              }
              callback(error)
            }
          }
        ],
        contactPersonEmail: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.contactPersonPhone2 && !source.contactPersonPhone1) {
                error.push(new Error("Contact No.1 or Contact No.2  or Email address is required!"))
              }

              if (value && !validateEmail(value)) {
                error.push(new Error("Provide a valid email address."))
              }
              callback(error);
            }
          }
        ],
        contactPersonPhone1: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.contactPersonPhone2 && !source.contactPersonEmail) {
                error.push(new Error("Contact No.1 or Contact No.2  or Email address is required!"))
              }
              callback(error)
            }
          }
        ],
        contactPersonPhone2: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.contactPersonPhone1 && !source.contactPersonEmail) {
                error.push(new Error("Contact No.1 or Contact No.2  or Email address is required!"))
              }
              callback(error)
            }
          }
        ],
        contactPersonName: {type: 'string', required: true, message: 'the field is required!'},
      }
    },
    payerCustomerInstance: {
      type: 'object',
      required: true,
      fields: {
        customerAddressCn: [
          {
            validator(rule, value, callback, source, options) {
              let error = []
              // 二者选其一
              if (!value && !source.customerAddressEn) {
                error.push(new Error("Address(Local Lang) or Address(English) is required!"))
              }
              callback(error)
            }
          }
        ],
        customerAddressEn: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.customerAddressCn) {
                error.push(new Error("Address(Local Lang) or Address(English) is required!"))
              }
              callback(error)
            }
          }
        ],
        customerNameCn: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.customerNameEn) {
                error.push(new Error("Company Name(Local Lang) or Company Name(English) is required!"))
              }
              callback(error)
            }
          }
        ],
        customerNameEn: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.customerNameCn) {
                error.push(new Error("Company Name(Local Lang) or Company Name(English) is required!"))
              }
              callback(error)
            }
          }
        ],
        contactPersonEmail: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.contactPersonPhone2 && !source.contactPersonPhone1) {
                error.push(new Error("Contact No.1 or Contact No.2  or Email address is required!"))
              }
              if (value && !validateEmail(value)) {
                error.push(new Error("Provide a valid email address."))
              }
              callback(error)
            }
          }
        ],
        contactPersonPhone1: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.contactPersonPhone2 && !source.contactPersonEmail) {
                error.push(new Error("Contact No.1 or Contact No.2  or Email address is required!"))
              }
              callback(error)
            }
          }
        ],
        contactPersonPhone2: [
          {
            validator(rule, value, callback, source, options){
              let error = []
              // 二者选其一
              if (!value && !source.contactPersonPhone1 && !source.contactPersonEmail) {
                error.push(new Error("Contact No.1 or Contact No.2  or Email address is required!"))
              }
              callback(error)
            }
          }
        ],
        contactPersonName: {type: 'string', whitespace: true, required: true, message: 'the field is required!'},
      }
    }
  }
}

