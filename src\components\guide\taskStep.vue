<template>
    <div class="guide-dialog">
      <template v-if="$store.state.user.taskType != ''">
        <div class="task-step" :style="style" @click="handleClick" @mouseenter="mouseEnter" :class="{'only-set-notifaction': $store.state.user.taskType == 'setNotice' && nextStep == 1}">
          <!-- 公司管理下拉模拟菜单 -->
          <a href="javascript:void(0);" class="company" v-show="toAddBuyerMenu" @click="addBuyerNext">
            <i class="el-icon-s-custom" style="position: relative;"></i> {{ $t('navbar.customerManagement') }}
          </a>
          <div class="step-content" 
          :style="{ 
            bottom: config.step[this.nextStep].attachTo.on == 'top' ? 'auto' : contentHeight + 'px', 
            left: config.step[nextStep].isFooter ? '47%':'',
            top: config.step[this.nextStep].attachTo.on == 'top' ? contentHeight + 'px' : 'auto'
          }"
          :class="{
            'in-right': config.step[this.nextStep].openDialog != undefined || config.step[this.nextStep].inRight,
            'dialog-input':  config.step[this.nextStep].dialogInput,
            'arrow-bottom': config.step[nextStep].isFooter || config.step[this.nextStep].attachTo.on == 'top',
            [config.step[this.nextStep].attachTo.on]: config.step[this.nextStep].attachTo.on
          }">
            <h4>
              <span class="tit">{{ convertLang(config.step[nextStep].text.split('||')[0]) }}</span>
              <span class="skip" @click.stop="skip">{{ language == 'en-US' ? 'Skip':'跳过'}}</span>
            </h4>
            <div v-if="config.isFunction" class="sub-txt">{{ convertLang(config.step[nextStep].subTxt) }}</div>
            <div class="step-num">
              <span>
                {{ convertLang(config.step[nextStep].text.split('||')[1]) }}
                {{ convertLang(config.step[nextStep].text.split('||')[2]) }}
              </span>
              <span v-if="config.isFunction" style="margin-left: 80px;">
                <el-button v-if="nextStep > 0" @click.stop="handlePrev" type="primary" size="mini" plain>{{ $t('guide.prev') }}</el-button>
                <el-button @click.stop="handleNext" type="primary" size="mini">{{ config.step.length == nextStep+1 ? $t('guide.learned') : $t('guide.next') }}</el-button>
              </span>
            </div>
          </div>
        </div>
        <div class="task-mask"></div>
      </template>
      
      <!-- 完成弹窗 -->
      <el-dialog :visible.sync="completeVisible" :close-on-click-modal="false" top="20vh" width="480px">
        <div v-if="false" slot="title"></div>
        <div class="text-center" style="padding-bottom: 20px;">
          <img src="/img/icon/complete.png" alt="">
          <h4 style="margin: 32px 0 24px; font-size: 18px; font-weight: 500; color: #000;">{{ $t('guide.completeTasl') }}</h4>
          <p>{{ $t('guide.autoClose.start') }}{{ closeTime }}{{ $t('guide.autoClose.end') }}</p>
        </div>
      </el-dialog>
    </div>
</template>

<script>
import { setGuide } from '@/api/user'
import { mapGetters } from "vuex";
import guide from '@/util/guide'  // 新手引导所有步骤的配置

export default {
  name: "task",
  data() {
    return {
      config: {},
      style: {},
      nextStep: -1,
      w: 0,
      h: 0,
      top: 0,
      left: 0,
      toAddBuyerMenu: false,
      tabs: ['relationship', 'contact', 'address', 'notifaction'],
      tabType: ['addBuyer', 'addContact', 'setAddress', 'setNotice'],
      timer: null,
      taskCompleteNum: 0,
      completeVisible: false,
      closeTime: 3,
      contentHeight: -123,
    };
  },
  mounted() {
    const timer = setInterval(()=>{
      if (document.readyState === 'complete') {
        window.clearInterval(timer)
        
      }
    }, 300)
  },
  destroyed() { 
  },
  computed: {
    ...mapGetters(["userInfo", "language"]),
  },
  watch: {
    '$store.state.user.nextStep' (newVal) {
      // console.log('newVal', newVal, this.nextStep)
      this.nextStep++
    },
    '$store.state.user.taskType' (currentType) {
      if(currentType == '') {
        this.nextStep = -1

        let task = JSON.parse(localStorage.getItem('guideTask'))
        console.log('关闭时监听任务状态：', task)
        let complete = task.every(item => item.val == true);

        if(complete) {
          if(this.userInfo.guide) {
            this.userInfo.guide = false
            this.$store.commit('SET_USERIFNO', this.userInfo)
            setGuide().then(res => {
              this.completeVisible = true
              console.log('发送完成请求', this.completeVisible)
              const data = res.data.data;
            });
          } else {
            
          }
          let interTimer = setInterval(() => {
            if(this.closeTime <= 1) {
              clearInterval(interTimer)
              this.completeVisible = false
            } else {
              this.closeTime--
            }
          }, 1000);
        }
      }
      // console.log('引导开始了', this.nextStep, currentType, guide[currentType])
      if(currentType != '') {
        this.config = guide[currentType]
        this.nextStep++
        setTimeout(()=>{ 
          let dom = document.querySelector('.step-content')
          if(dom) {
            this.contentHeight = '-'+(dom.offsetHeight + 20) || -123 
          }
        }, 200)
      } else {
        this.toAddBuyerMenu = false
        this.style = {}
        clearInterval(this.timer)
      }
    },
    nextStep: function (val) {
      console.log('步骤发生了变化：', val+1)
      if(this.config != '') {
        let dom = document.querySelector('.step-content')
        this.getElement()
        if(dom) {
          setTimeout(() => {
            this.contentHeight = '-'+(dom.offsetHeight + 20) || -123 
          }, 300)
        }
      } else {
        clearInterval(this.timer)
      }
    },
  },
  methods: {
    handleFnTaskStatus() {
      /** 处理功能引导 */ 
      let list = JSON.parse(localStorage.getItem('loginUserList'))
      let user = list.find(item => item.userMgtId == this.userInfo.userMgtId)
      user.fnList.forEach(item => {
        for (let key in item) {
          if('finish_'+this.config.type == key) {
            item[key] = true  // 设置当前功能引导为完成状态
          }
        }
      })
      localStorage.setItem('loginUserList', JSON.stringify(list))
    },
    skip() {
      this.handleFnTaskStatus()
      this.$store.commit('SET_TASK_TYPE', '')
      this.nextStep = -1

      // 上线临时处理 --> 新手任务、功能引导全部完成
      let task = JSON.parse(localStorage.getItem('guideTask'))
      let list = JSON.parse(localStorage.getItem('loginUserList'))
      let user = list.find(item => item.userMgtId == this.userInfo.userMgtId)
      // console.log('当前登录用户：', user)
      user.fnList.forEach(item => {
        for (let key in item) {
          item[key] = true  // 设置功能引导全部完成
        }
      })
      localStorage.setItem('loginUserList', JSON.stringify(list))

      task.forEach(item => item.val = true) // 设置新手任务全部完成
      localStorage.setItem('guideTask', JSON.stringify(task))

      if(this.userInfo.guide) {
        this.userInfo.guide = false
        this.$store.commit('SET_USERIFNO', this.userInfo)
        setGuide().then(res => {
          const data = res.data.data;
          console.log('跳过表示完成：', data)
        });
      }
    },
    convertLang(langStr) {
      let lang = this.$t(langStr)
      return lang
    },
    computeNum(task) {
      // console.log('计算完成数量', task)
      let n = 0
      task.forEach(item => {
        if(item.val) n++
      });
      this.taskCompleteNum = n
      return n
    },
    getElement() {
      debugger;
       console.log('第几步：', this.nextStep+1, this.config, this.config.step[this.nextStep])
      let scroll = this.config.step[this.nextStep] != undefined ? this.config.step[this.nextStep].scroll : void 0
      console.log('是否滚动：', scroll)
      let offsetHeight = document.body.offsetHeight
      console.log('offsetHeight', offsetHeight)
      if(scroll) {
          // let dom = document.querySelector(this.config.step[this.nextStep].attachTo.element)
          // document.documentElement.scrollTop = dom.getBoundingClientRect().top + document.documentElement.scrollTop - 400
          document.querySelector(this.config.step[this.nextStep].attachTo.element).scrollIntoView({
              behavior: "smooth",
              block: "center",
              inline: "start",
          })
          if(offsetHeight < 650 && this.config.type == 'dashboard' && this.nextStep == 4) {
            //document.documentElement.scrollTop = document.documentElement.scrollTop - 200 // 处理兼容工作台“测试结果”
          } else if(this.config.type == 'createTrf' && this.nextStep == 4) {
            console.log('处理Next')
            document.documentElement.scrollTop = document.documentElement.scrollTop + 100
          } else {
            console.log('不需要兼容处理')
          }
        
      }else {
        document.documentElement.scrollTop = 0
      }
      this.timer = setInterval(() => {
        let ele = this.nextStep >= 0 ? document.querySelector(this.config.step[this.nextStep].attachTo.element) : null
        console.log('开始跳转定位元素', ele)
        if(ele) {
          console.log('找到元素', ele)
          clearInterval(this.timer)
           
          // ele.scrollIntoView({
          //   behavior: 'smooth'
          // });

          // return false;
          let position = this.getPosition(ele)
          console.log('目标位置', position)
          this.w = ele.clientWidth + 11
          this.h = ele.clientHeight + 11
          this.top = position.top - 5
          this.left = position.left - 5

           console.log('已获取到元素，并设定位置') 
          this.style = {
            width: this.w + 'px',
            height: this.h + 'px',
            top: (scroll ? this.top - document.documentElement.scrollTop : this.top) + 'px',
            left: this.left + 'px'
          }
        } else {
          if(this.nextStep < 0) clearInterval(this.timer)
          console.log('没获取到元素', this.nextStep)
        }
      }, 100)
    },
    // 获取坐标
    getPosition(node) {
      debugger;
      //获取元素相对于其父元素的left值var left
      var left = node.offsetLeft; 
      console.log('left',left)
      var top = node.offsetTop;
      // 取得元素的offsetParent
      console.log('top',left)
      var current = node.offsetParent; 
      console.log('current',current)
      // 一直循环直到根元素
      while(current != null) {
        left += current.offsetLeft;
        top += current.offsetTop;
        current = current.offsetParent;
      }
      return {
        "left": left,
        "top": top  
      };
    },
    handlePrev() {
      if(this.nextStep <= -1) {
        this.nextStep = -1
      } else {
        this.nextStep--
      }
    },
    handleNext() {
      if(this.config && this.config.step[this.nextStep].end) {
        this.nextStep = -1
        clearInterval(this.timer)
        this.$store.commit('SET_TASK_TYPE', '')
        this.handleFnTaskStatus()
      } else {
        this.nextStep++
      }
    },
    handleClick() {
      if(this.config && this.config.isFunction) return

      if(this.config && this.config.step[this.nextStep].end) {
        // this.nextStep = -1
        // clearInterval(this.timer)

        // let task = JSON.parse(localStorage.getItem('guideTask'))
        // let n = this.computeNum(task)

        // let index = task.findIndex(item => item.type == this.config.type)
        // if(!task[index].val) {
        //   task[index].val = true  // 设置当前任务为完成状态
        //   this.$notify({
        //     title: this.$t('api.success'),
        //     message: this.$t(this.handleComplateTips(this.config.type)),
        //     type: 'success',
        //     duration: 1500
        //   });
        // }
        // // task.forEach(item => {
        // //   if(item.type == this.config.type) item.val = true  // 设置当前任务为完成状态
        // // });
        // localStorage.setItem('guideTask', JSON.stringify(task))
        // let complete = task.every(item => item.val == true);

        // this.$store.commit('SET_GUIDE', { name: this.config.type, val: false }) // 关闭step中的添加弹窗
        // this.$store.commit('SET_TASK_TYPE', '')

        // if(complete) {
        //   // console.log('全部任务已完成', this.userInfo.guide)
        //   if(this.userInfo.guide) {
        //     this.completeVisible = true  // 完成所有后再打开就不提示全部完成了
        //     this.userInfo.guide = false
        //     this.$store.commit('SET_USERIFNO', this.userInfo)
        //     setGuide().then(res => {
        //       const data = res.data.data;
        //       console.log('新手引导接口：', data)
        //     });
        //   }
        //   let interTimer = setInterval(() => {
        //     if(this.closeTime <= 1) {
        //       clearInterval(interTimer)
        //       this.completeVisible = false
        //     } else {
        //       this.closeTime--
        //     }
        //   }, 1000);
        // } else if(n == 4) {

        // } else {
        //   this.$message({
        //     type: "success",
        //     message: this.$t('guide.addContactSuccess')
        //   });
        //   setTimeout(()=>{
        //     this.$store.commit('SET_TASK_DIALOG', true) // 2s后才能打开任务列表
        //   }, 2000)
        // }
      } else {
        if(this.config && this.config.type == 'addBuyer' && this.nextStep == 0) {  // 处理“添加买家”任务
          this.addBuyerNext()
        }
        if(this.config && this.config.step[this.nextStep].openDialog) {
          this.$store.commit('SET_GUIDE', { name: this.config.type, val: true })
        }
        this.nextStep++
      }
    },
    handleComplateTips(type) {
      switch(type) {
        case 'addBuyer':
          return 'guide.ddBuyerSuccess'
          break;
        case 'addContact':
          return 'guide.addContactSuccess'
          break;
        case 'setAddress':
          return 'guide.addAddressSuccess'
          break;
        case 'setNotice':
          return 'guide.notificationSuccess'
          break;
      }
    },
    mouseEnter() {
      if(this.config && this.config.type == 'addBuyer' && this.nextStep == 0) {
        this.style.width = this.w + 100 + 'px'
        this.style.height = this.h + 50 + 'px'
        setTimeout(() => {
          this.toAddBuyerMenu = true
        }, 100)
      }
    },
    // 添加买家下拉菜单
    addBuyerNext() {
      this.toAddBuyerMenu = false
      this.$router.push({
        path: '/customer/management', 
        query: {
          'type': 'addBuyer',
          'tab': 'relationship'
        } 
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.task-step {
  position: fixed;
  z-index: 9996;
  -webkit-box-shadow: rgba(33,33,33, .5) 0px 0px 0px 5000px;
  box-shadow: rgba(33,33,33, .5) 0px 0px 0px 5000px;
  transition: all .2s;
  cursor: pointer;
  .content {
    position: absolute;
  }
  .company {
    box-shadow: 2px 1px 5px #ccc;
    padding: 10px 20px;
    position: absolute;
    bottom: 6px;
    font-size: 14px;
    left: 10px;
    background: #fff;
    display: block;
    white-space: nowrap;
    display: block;
    min-width: 170px;
    &:hover {
      color: #f60;
    }
  }

  .step-content {
    position: absolute;
    background: rgba(255, 255, 255, 1);
    bottom: -123px;
    padding: 24px;
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    h4 {
      margin: 0;
      white-space: nowrap;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span.skip {
        font-size: 12px;
        font-weight: 400;
        color: #808080;
      }
    }
    .sub-txt {
      color: #999;
      font-size: 16px;
      margin-top: 16px;
    }
    .step-num {
      padding-top: 16px;
      font-size: 14px;
      font-weight: 400;
      white-space: nowrap;
      color: #656565;
      display: flex;
      justify-content: space-between;
    }
    &::after {
      content: ' ';
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-bottom-color: #fff;
      position: absolute;
      top: -20px;
    }
  }
  .arrow-bottom {
    &::after {
      content: ' ';
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: #fff;
      position: absolute;
      bottom: -20px;
      top: auto;
    }
  }
  .in-right {
    right: 0;
    &::after {
      right: 18px;
    }
  }
  .dialog-input {
    left: 168px;
  }
}
.task-mask {
  background: rgba(33,33,33, 0);
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9995;
}
.only-set-notifaction {
  z-index: 9995 !important;
}
</style>
