import { Directive, nextTick } from 'vue'
import store from '../store'
import { RootState } from '../type/store'

/**
 * 定义 Dimensions 接口，用于描述角色维度对象的结构
 * 该对象的键为字符串，值为字符串数组
 */
interface Dimensions {
  [key: string]: string[]
}

/**
 * 辅助函数，用于检查用户是否具有指定类型和名称的角色
 * @param dimensions - 角色维度对象，包含不同类型的角色列表
 * @param requiredRoleType - 需要检查的角色类型，如 'SGSUserRole'
 * @param requiredRole - 需要检查的具体角色名称，如 'SgsAdmin'
 * @returns 如果用户具有指定角色，返回 true；否则返回 false
 */
function hasRole(
  dimensions: Dimensions,
  requiredRoleType: string,
  requiredRole: string,
) {
  // 检查 dimensions 对象是否存在，或者是否包含指定的角色类型
  if (!dimensions || !dimensions[requiredRoleType]) {
    return false
  }
  // 检查指定的角色是否存在于对应类型的角色列表中
  return dimensions[requiredRoleType].includes(requiredRole)
}

/**
 * 自定义 Vue 指令，用于根据用户角色权限控制元素的显示与隐藏
 */
const role: Directive = {
  /**
   * 指令挂载时的钩子函数
   * @param el - 指令绑定的 DOM 元素
   * @param binding - 指令绑定的值，包含所需的角色类型和角色名称数组
   */
  mounted(el, binding) {
    // 等待下一个 DOM 更新周期结束后执行回调
    nextTick(() => {
      // 从 store 中获取用户的角色维度信息，并将其类型断言为 Dimensions
      const dimensions = (store.state as RootState).user
        .dimensions as Dimensions
      // 从指令绑定的值中获取角色条件数组
      const roleConditions = binding.value
      // 标记是否有满足条件的角色
      let hasValidRole = false

      // 遍历每个角色条件
      for (const [requiredRoleType, requiredRole] of roleConditions) {
        // 调用 hasRole 函数检查用户是否具有指定角色
        if (hasRole(dimensions, requiredRoleType, requiredRole)) {
          hasValidRole = true
          break
        }
      }

      // 如果没有满足条件的角色，从 DOM 中移除该元素
      if (!hasValidRole) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    })
  },
}

// 导出自定义指令
export default role
