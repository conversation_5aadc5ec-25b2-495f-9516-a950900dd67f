"use strict";

import {objectIsNull, validateEmail} from "@/util/validate";
import {getDictionarySettings} from "@/api/trf/trf";

const serviceRequirement = {};

/**
 * @param           emailGroupData          邮件组
 * @param           items                   当前选中项
 * @param           values                  当前的选中值集合
 * **/
serviceRequirement.changeContacts = function (emailGroupData, items, values) {
    //调用通用方法
    let obj = {};
    let pasteEmailArr = []

    values.find((item1) => {
        obj = emailGroupData.find((item) => {
            if (item1 == item.emailGroupName) {//判断当前邮件是否和
                return item;
            }
        });
        if (objectIsNull(obj)) {//手动输入邮箱验证
            let validateRes = validateEmail(item1);
            if (validateRes) {
                if (pasteEmailArr.indexOf(item1) == -1) {
                    pasteEmailArr.push(item1);
                }
            }
        } else {
            if (obj.type != 2) {//邮件组数据
                var contactEmail = obj.contacts;
                contactEmail.find((item2) => {
                    if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
                        pasteEmailArr.push(item2.contactEmail);
                    }
                });
            } else {
                if (pasteEmailArr.indexOf(item1) == -1) {
                    pasteEmailArr.push(item1);
                }
            }
        }
    });
    return pasteEmailArr;
   /* this.applyEmailValue = pasteEmailArr;
    if (this.applyEmailValue.length != 0) {
        this.trf.trfCustomerContact.applyContactEmail = this.applyEmailValue.join(',')
    } else {
        this.trf.trfCustomerContact.applyContactEmail = '';
    }*/


    /*debugger;
    let emails = [];
    for (let item of values) {
        let index = emailGroupData.findIndex(i => i.emailGroupName == item);
        //手动输入
        if (index <0 && validateEmail(item) && emails.indexOf(item) < 0) {
            emails.push(item);
            continue;
        }

        let group = emailGroupData[index];
        if(!group) continue;
        switch (group.type) {
            case 2:
                if (validateEmail(item) && emails.indexOf(item) < 0) emails.push(item);
                break;

            default:
                let contacts = group.contacts;
                if (!objectIsNull(contacts)) {
                    contacts.forEach(p => {
                        if (validateEmail(p.contactEmail) && emails.indexOf(p.contactEmail < 0) && values.indexOf(p.contactEmail) < 0) {
                            emails.push(p.contactEmail);
                        }
                    });
                }
                break;
        }
    }
    values = emails;
    return values;*/
};

const dictionaryMap = new Map();
serviceRequirement.queryDictionary = async function (key, dicSetting, language) {
    debugger;
    if(dictionaryMap.get(key) === undefined) {
        dictionaryMap.set(key, new Map());
    }
    if(objectIsNull(dictionaryMap.get(key).get(language))) {
        await getDictionarySettings(dicSetting, language).then(res => {
            res.data && res.data.length > 0 ? dictionaryMap.get(key).set(language, res.data) : dictionaryMap.get(key).set(language, []);
        });
    }
    return dictionaryMap.get(key).get(language);
}
export default serviceRequirement;

