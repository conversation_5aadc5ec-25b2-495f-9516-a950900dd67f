import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/submit',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}


export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const getNotPageByUser = (params) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/notPage/by-user',
        method: 'get',
        params: {
            ...params
        }
    })
}
export const getMailGroupAndContactEmail = (params) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/notPage/getMailGroupAndContactEmail',
        method: 'get',
        params: {
            ...params
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const emailGroupPersons = (id) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/persons',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/emailgroups/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}
