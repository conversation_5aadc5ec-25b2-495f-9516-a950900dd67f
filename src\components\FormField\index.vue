<template>
  <!-- formData[fieldCode]： {{ formData[fieldCode] }}
  <br />
  fieldCode : {{ fieldCode }} -->
  <el-form-item :label="fieldLabel"
                :prop="fieldCode"
                :rules="formRules">
    <el-input-number v-if="fieldType === 'number'"
                     :style="inputStyle"
                     v-model="formData[fieldCode]"
                     controls-position="right"
                     :min="1" />
    <el-date-picker v-if="fieldType === 'date' || fieldType === 'daterange'"
                    :disabled="isReadOnly === 1"
                    :style="inputStyle"
                    v-model="formData[fieldCode]"
                    :type="fieldType"
                    range-separator="To"
                    value-format="YYYY-MM-DD"
                    placeholder="Please select" />
    <!-- :maxlength="length" 如没有值传入，会影响输入框 -->
    <el-input v-if="!fieldType || fieldType === 'input' || fieldType === 'Input' || fieldType === 'textArea'"
              :type="fieldType === 'textArea' ? 'textarea' : 'text'"
              :disabled="isReadOnly === 1"
              :style="inputStyle"
              placeholder="Please input"
              :rows="3"
              clearable
              :show-word-limit="!!length"
              v-model="formData[fieldCode]" />
    <el-select v-if="fieldType === 'select' || fieldType === 'select2'"
               :style="inputStyle"
               :disabled="isReadOnly === 1"
               clearable
               filterable
               :multiple="fieldType === 'select2'"
               placeholder="Please select"
               v-model="formData[fieldCode]">
      <el-option v-for="(o, ind) in sourceValue[0].sourceValueList"
                 :key="'o_' + ind"
                 :label="o.name"
                 :value="o.code" />
    </el-select>
    <el-checkbox-group v-if="fieldType === 'checkboxGroup'"
                       :style="inputStyle"
                       :disabled="isReadOnly === 1"
                       v-model="formData[fieldCode]">
      <el-checkbox v-for="(o, ind) in sourceValue"
                   :key="'o_' + ind"
                   :label="o.code">{{ o.name }}</el-checkbox>
    </el-checkbox-group>
    <el-checkbox v-if="fieldType === 'checkbox'"
                 :disabled="isReadOnly === 1"
                 v-model="formData[fieldCode]"
                 :label="fieldCode">{{ fieldLabel }}</el-checkbox>
    <el-radio-group v-if="fieldType === 'radioGroup'"
                    :style="inputStyle"
                    :disabled="isReadOnly === 1"
                    v-model="formData[fieldCode]">
      <el-radio v-for="(o, ind) in sourceValue"
                :key="'o_' + ind"
                :label="o.code">{{ o.name }}</el-radio>
    </el-radio-group>
    <el-radio v-if="fieldType === 'radio'"
              :disabled="isReadOnly === 1"
              v-model="formData[fieldCode]"
              :label="fieldCode">{{ fieldLabel }}</el-radio>
    <Percentage v-if="fieldType === 'percentage'"
                :formData="formData"
                :fieldCode="fieldCode" />
    <Percentage v-if="fieldType === 'percentageSelect'"
                :formData="formData"
                :fieldCode="fieldCode"
                :useSelect="true" />
    <div style="position: absolute;right: 0">
      <slot name="extendIcon"></slot>
    </div>
  </el-form-item>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import Percentage from '../Percentage/index.vue'

const { t } = useI18n()

const props = defineProps({
  formData: { type: Object, default: () => {} },
  tips: { type: String, default: '' },
  useI18nText: { type: Boolean, default: false },
  fieldCode: { type: String, required: true },
  fieldType: { type: String, default: '' },
  isRequired: { type: Number, default: 0 },
  isReadOnly: { type: Number, default: 0 },
  fieldLabel: { type: String, required: true },
  length: { type: Number, default: 0 },
  sourceValue: { type: Array, default: () => [] },
  fieldWith: { type: String, default: '' },
})

onMounted(() => {
  console.log(props.formData, '-----------------')
})

const inputStyle = computed(() => ({
  width: props.fieldWith || '100%',
}))

const formRules = computed(() => ({
  required: props.isRequired === true || props.isRequired === 1,
  //   message: ['percentage', 'percentageSelect'].includes(props.fieldType)
  //     ? ''
  //     : (props.tips
  //         ? (props.useI18nText ? t(props.tips) : props.tips)
  //         : (!props.fieldType || props.fieldType === 'input'
  //             ? 'please input'
  //             : 'please select')),
  trigger: ['blur', 'change'],
}))
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>