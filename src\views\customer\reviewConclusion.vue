<template>
    <div>
        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
<!--            <el-form-item>
                <el-input v-model="query.customerName"
                @input="onSearch"
                @keyup.enter.native="onSearch"
                @clear="onSearch"
                :placeholder="$t('search')"
                          clearable></el-input>
            </el-form-item>-->
            <el-form-item>
                <!-- <el-button type="primary" @click="onSearch" class="line-btn">{{$t('operation.search')}}</el-button> -->
                <!-- <el-button v-if="addBtnFlag" type="primary" @click="addRow" class="line-btn">{{$t('operation.add')}}</el-button> -->
                <el-button v-if="addBtnFlag" @click="addRow" class="line-btn" id="add-address">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
                :data="tableData"
                style="width: 100%"
                size="medium">
            <el-table-column
                    type="index"
                    fixed
                    label="#"
                    width="40">
            </el-table-column>
            <el-table-column
                    fixed
                    prop="customerGroupName"
                    :label="$t('customerGroup.name')"
                    width="210">
            </el-table-column>
           <!-- <el-table-column
                    fixed
                    prop="reviewCode"
                    :label="$t('reviewConclusion.reviewCode')"
                    width="180">
            </el-table-column>-->
            <el-table-column
                    fixed
                    prop="reviewValue"
                    :label="$t('reviewConclusion.reviewValue')">
            </el-table-column>
            <el-table-column
                    prop="updateTime"
                    :label="$t('common.operationTime')" :formatter="formtterDate"
                    width="135">
            </el-table-column>
            <el-table-column
                    prop="updateUser"
                    :label="$t('common.operator')"
                    width="120">
            </el-table-column>
            <el-table-column
                    prop="status"
                    :label="$t('common.status.title')"
                    width="80"
                    align="center">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.status==1?$t('common.status.enable'):$t('common.status.disable')"
                                placement="top">
                        <el-switch
                                v-model="scope.row.status"
                                active-color="#ff6600"
                                inactive-color="#ff4949"
                                :active-value="1"
                                :inactive-value="0"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="130">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)">{{$t('operation.edit')}}</el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text">{{$t('operation.remove')}}</el-button>
                    <!-- <el-button v-if="permissionList.editBtn" type="text" @click="detailRow(scope.row)" size="small"
                               icon="el-icon-edit">{{$t('operation.edit')}}
                    </el-button>
                    <el-button v-if="permissionList.deleteBtn" @click="removeRow(scope.row)" type="text" size="small"
                               icon="el-icon-delete">{{$t('operation.remove')}}
                    </el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog :title="title" :visible.sync="dialogFormVisible">
            <el-form ref="form" :model="form"
                     label-width="200px"
                     label-position="left"
                     size="medium"
                     :rules="rules"
                     class="sgs-form"
            >
                <!--<el-form-item :label="$t('account.customerName')"  prop="customerName">
                    <el-input v-model="form.customerName"></el-input>
                </el-form-item>-->
               <!-- <el-form-item :label="$t('reviewConclusion.reviewCode')" prop="reviewCode">
                    <el-input v-model="form.reviewCode"
                              maxlength="100"></el-input>
                </el-form-item>-->
                <el-form-item :label="$t('reviewConclusion.reviewValue')" prop="reviewValue">
                    <el-input v-model="form.reviewValue"
                              maxlength="150"></el-input>
                </el-form-item>
                <!--<el-form-item :label="$t('common.isDefault')">
                    <el-radio-group v-model="form.isDefault">
                        <el-radio :label="1">{{$t('common.yes')}}</el-radio>
                        <el-radio :label="0">{{$t('common.no')}}</el-radio>
                    </el-radio-group>
                </el-form-item>-->
            </el-form>
            <div class="sgs-bottom">
                <el-button @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button type="primary" @click="submitForm('form')" :loading="btnGuestbookSubmit">
                    {{$t('operation.submit')}}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {getPageByUser, add, detail, remove} from "@/api/customer/reviewConclusion";
    import moment from 'moment'
    import {mapGetters} from "vuex";
    import {validatenull} from "../../util/validate";

    export default {
        props: {
            customerId: {
                type: Number,
                default: null,
            }
        },
        name: "reviewConclusion",
        data() {
            return {
                name: "reviewConclusion",
                dialogFormVisible: false,
                btnGuestbookSubmit: false,
                addBtnFlag: true,
                title: '',
                tableData: [],
                form: {},
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                rules: {
                    customerName: [{required: true, message: 'Please enter', trigger: 'blur'}],
                    reviewCode: [{required: true, message: 'Please enter', trigger: 'blur'}],
                    reviewValue: [{required: true, message: 'Please enter', trigger: 'blur'}],
                },
            }
        },
        computed: {
            ...mapGetters(["userInfo", "permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:review:add'], false),
                    editBtn: this.vaildData(this.permission['sgs:customer:review:edit'], false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:review:delete'], false),
                };
            }
        },
        methods: {
            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.btnGuestbookSubmit = true;
                        add(this.form).then(res => {
                            if (res.data.data == 'Double') {
                                this.$message({
                                    type: "info",
                                    message: this.$t('reviewConclusion.RepeatToAdd')
                                });
                            } else {
                                this.$message({
                                    type: "success",
                                    message: this.$t('api.success')
                                });
                                this.btnGuestbookSubmit = false;
                                this.dialogFormVisible = false;
                                this.onLoad(this.page);
                            }
                            this.btnGuestbookSubmit = false;
                        }).catch(() => {
                            this.btnGuestbookSubmit = false;
                        });
                    } else {
                        return false;
                    }
                });

            },
            removeRow(row) {
                this.$confirm(this.$t('operation.confirmDelete'), {
                    confirmButtonText: this.$t('operation.confirm'),
                    cancelButtonText: this.$t('operation.cancel'),
                    type: "warning"
                })
                    .then(() => {
                        remove(row.id);
                    })
                    .then(() => {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.onLoad(this.page);
                    });
            },
            detailRow(row) {
                console.log(row);
                this.title = this.$t('reviewConclusion.title.edit');
                detail(row.id).then(res => {
                    //获取后台数据付给页面，并打开
                    this.dialogFormVisible = true;
                    this.form = res.data.data;
                });

            },
            addRow() {
                this.form = {};
                this.title = this.$t('reviewConclusion.title.add');
                this.dialogFormVisible = true;
            },
            formtterDate(row, column) {
                var date = row[column.property];

                if (date == undefined || date == '') {
                    return ''
                }
                ;

                return moment(date).format("YYYY-MM-DD")
            },
            changeStatus(row) {
                const modifiedForm = {
                    id: row.id,
                    status: row.status,
                    reviewValue: row.reviewValue
                };
                add(modifiedForm).then(res => {
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.page.currentPage = 1;
                    this.onLoad(this.page);
                });
            }
        },
        created() {

        },
        mounted() {
            debugger;
            //判断当前登录用户是否有customerGroupCode，没有的话 不允许新增数据
            if (this.permissionList.addBtn) {
                if (validatenull(this.userInfo.customerGroupCode)) {
                    this.addBtnFlag = false;
                }
                ;
            } else {
                this.addBtnFlag = false;
            }

            this.onLoad(this.page);
        }

    }
</script>

<style scoped>

</style>
