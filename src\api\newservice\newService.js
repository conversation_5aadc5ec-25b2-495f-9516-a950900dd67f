import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/newService/add',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/newService/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/newService/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/newService/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const changeServicePin = (param)=>{
    return request({
        url: '/api/sgs-mart/newService/updatePin',
        method: 'post',
        data: param
    })
}