<template>
    <basic-container v-loading="pageLoading">
        <div class="smart_views_auditTrailTable" id="smart_views_auditTrailTable">
            <el-card class="sgs-box content-item">
                <section>
                    <h3>{{sectionName}}</h3>
                </section>
                <el-table :data="dataList" border size="mini" :max-height="380">
                    <el-table-column prop="operationBy" label="Operation User"></el-table-column>
                    <el-table-column prop="operation" label="Operation"></el-table-column>
                    <el-table-column prop="objectNo" :label="handlerObjectNoTitle"></el-table-column>
                    <el-table-column prop="originalValue" label="Original Value">
                        <template slot-scope="{row}">
                            <el-tooltip placement="top">
                                <div slot="content">
                                    <div v-html="row.originalValue"></div>
                                </div>
                                <span v-html="row.originalValue"></span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="newValue" label="New Value">
                        <template slot-scope="{row}">
                            <el-tooltip placement="top">
                                <div slot="content">
                                    <div v-html="row.newValue"></div>
                                </div>
                                <span v-html="row.newValue"></span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column prop="operationTime" label="Operation Time">
                        <template slot-scope="{row}">
                            {{getTime(row.operationTime)}}
                        </template>
                    </el-table-column>
                    <!--
                    <el-table-column prop="createdDate" label="Created Date">
                        <template slot-scope="{row}">
                            {{getTime(row.createdDate)}}
                        </template>
                    </el-table-column>-->
                </el-table>
                <el-pagination
                        @size-change="initData"
                        @current-change="initData"
                        :current-page.sync="page.page"
                        :page-sizes="page.sizes"
                        :page-size.sync="page.rows"
                        :layout="page.layout"
                        :total="page.total"
                >
                </el-pagination>
            </el-card>
        </div>
    </basic-container>

</template>

<script>
import sampleApi from '@/api/newSamples';
import {tzFormChina,tzToChina} from '@/util/datetimeUtils';
import {mapGetters} from "vuex";
export default {
    name: "auditTrailTable",
    data() {
        return {
            pageLoading: false,
            dataList:[],
            page:{//分页对象
                show: true,
                page: 1,
                rows: 20,
                small: true,
                sizes: [10, 20, 50, 100],
                layout: 'total, sizes, prev, pager, next',
                total: 0
            }
        }
    },
    methods: {
        getTime(time){
            if(!time){
                return time;
            }
            return tzFormChina(time);
        },
        initData(){
            if(!this.objectNo || !this.objectType){
                return;
            }
            this.loading = true;

            let limit = this.page.rows;
            let offset = limit * (this.page.page-1);
            let param = {
                objectType:this.objectType,
                objectNo:this.objectNo,
                rootObjectId:this.rootObjectId,
                objectDate:this.objectDate,
                limit,
                offset,
            }
            this.pageLoading = true;
            sampleApi.queryLog(param).then(res=>{
                this.pageLoading = false;
                if(res.status==200 && res.data && res.data.data){
                    let {records,total} = res.data.data;
                    this.page.total = total;
                    this.dataList = records || [];
                }
            }).catch(err=>{
                console.error("query log err",err)
                this.pageLoading = false;
            })
        }
    },
    created() {
    },
    mounted() {
        this.initData();
    },
    watch: {},
    computed: {
        ...mapGetters(['permission', 'userInfo', 'language']),
        handlerObjectNoTitle(){
            return 'Object No';
        },
    },
    props: {
        objectType:'',
        objectNo:'',
        sectionName:'',
        rootObjectId:'',
        objectDate:null,
    },
    updated() {
    },
    beforeDestroy() {
    },
    destroy() {
    },
    components: {}
}
</script>

<style lang="scss">
.smart_views_auditTrailTable {

}
</style>