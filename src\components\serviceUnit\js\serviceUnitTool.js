"use strict";

import {objectIsNull} from "@/util/validate";
import {getDictionarySettings} from "@/api/trf/trf";
import {queryServiceUnits} from "@/api/serviceunit/serviceUnits";


import _ from "lodash";

const serviceUnitTool = {};
const SERVICE_UNIT_LANGUAGE_CHI='CHI';
const SERVICE_UNIT_LANGUAGE_EN='EN';


/**
 * @param           serviceUnits            加载的字典项数据
 * @param           items                   当前选中项
 * @param           values                  当前的选中值集合
 * @return          Object                  包含productLineCodes、serviceDomains、ServiceUnits
 * **/
serviceUnitTool.changeServiceUnits = function (serviceUnitDatas, items, values) {
    let productLines=[];
    let serviceDomains=[];
    let serviceUnits=[];
    let selServiceUnitDatas=[];//已被选择的serviceUnitDatas原数据
    for (let serviceUnitObj of values) {
        let obj = serviceUnitDatas.find((item) => {
            return item.serviceUnitCode === serviceUnitObj;
        });
        if(!objectIsNull(obj)){
            let productLine={};
            productLine.productLineCode =obj.productLineCode;  //substrServiceUnit(obj.serviceUnitCode).productLineCode;
            productLine.isDefault=false;
            let serviceDomain={};
            serviceDomain.serviceDomain = obj.serviceDomain//substrServiceUnit(obj.serviceUnitCode).serviceDomain;
            serviceDomain.isDefault=false;
            let serviceUnit={};
            serviceUnit.serviceUnit =obj.serviceUnitCode;
            serviceUnit.isDefault=false;
            /*if(defaultServiceUnit==obj.serviceUnitCode){
                productLine.isDefault=true;
                serviceDomain.isDefault=true;
                serviceUnit.isDefault=true;
            }*/
            productLines.push(productLine);
            serviceDomains.push(serviceDomain);
            serviceUnits.push(serviceUnit);
            selServiceUnitDatas.push(obj)
        }
    }
    //为第一个设置为默认
    if(!objectIsNull(productLines))productLines[0].isDefault=true;
    if(!objectIsNull(serviceDomains))serviceDomains[0].isDefault=true;
    if(!objectIsNull(serviceUnits))serviceUnits[0].isDefault=true;
    let result={};
    result.productLines=productLines;
    result.serviceDomains = serviceDomains;
    result.serviceUnits = serviceUnits;
    result.selServiceUnitDatas = selServiceUnitDatas;
    return result;
};
/*function substrServiceUnit(serviceUnitStr){
    let obj={};
    obj.serviceDomain='';
    obj.productLineCode='';
    if(objectIsNull(serviceUnitStr))return obj;
    let serviceUnitS = serviceUnitStr.split('-');
    if(objectIsNull(serviceUnitS))return obj;
    obj.serviceDomain=serviceUnitS[0];
    obj.productLineCode=serviceUnitS[1];
    return obj
};*/

/** 将保存的json在页面回显 返回数组格式
 * @param           serviceUnitJsonStr      JSON字符串数据
 * @param           items                   当前选中项
 * @param           values                  当前的选中值集合
 * @return          Object                  包含productLineCodes、serviceDomains、ServiceUnits
 * **/
serviceUnitTool.showSelServiceUnitDatas = function(serviceUnitJsonStr){
    if(objectIsNull(serviceUnitJsonStr))return [];
    let resultServiceUnits=[];
    let serviceUnits = JSON.parse(serviceUnitJsonStr);
    for (let serviceUnitObj of serviceUnits) {
        resultServiceUnits.push(serviceUnitObj.serviceUnit);
    }
    return resultServiceUnits;
};
/** 将保存的json在页面回显
 * @param           serviceUnitJsonStr      JSON字符串数据
 * @param           items                   当前选中项
 * @param           values                  当前的选中值集合
 * @return          Object                  包含productLineCodes、serviceDomains、ServiceUnits
 * **/
serviceUnitTool.showSelServiceUnitDatas = function(serviceUnitDtas,serviceUnitJsonStr){
    let resultObj={};
    if(objectIsNull(serviceUnitJsonStr))return resultObj;
    let resultServiceUnits=[];
    let defaultServiceUnit='';
    let defaultServiceUnitName='';
    let serviceUnits = JSON.parse(serviceUnitJsonStr);
    for (let serviceUnitObj of serviceUnits) {
        if(serviceUnitObj.isDefault){
            defaultServiceUnit=serviceUnitObj.serviceUnit
        }
        resultServiceUnits.push(serviceUnitObj.serviceUnit);
    }
    if(!objectIsNull(defaultServiceUnit)){
        for (let serviceUnitObj of serviceUnitDtas) {
            if(serviceUnitObj.serviceUnitCode==defaultServiceUnit){
                defaultServiceUnitName=serviceUnitObj.serviceUnitName;
            }
        }
    }
    resultObj.serviceUnitDataItems=resultServiceUnits;
    resultObj.defaultServiceUnitName=defaultServiceUnitName;
    return resultObj;
};

/** 获取默认的Service Unit 示例：Testing-SL
 * @param           serviceUnitJsonStr      JSON字符串数据
 * @param           items                   当前选中项
 * @param           values                  当前的选中值集合
 * @return          Object                  包含productLineCodes、serviceDomains、ServiceUnits
 * **/
serviceUnitTool.getDefaultServiceUnitDatas = function(serviceUnitJsonStr){
    if(objectIsNull(serviceUnitJsonStr))return [];
    let serviceUnits = JSON.parse(serviceUnitJsonStr);
    let defaultServiceUnit = null;
    for (let serviceUnitObj of serviceUnits) {
        if(serviceUnitObj.isDefault){
            defaultServiceUnit= serviceUnitObj.serviceUnit;
            break
        }
    }
    return defaultServiceUnit;
};

/** 将保存的json在页面回显 返回逗号分隔字符串
 * @param           serviceUnitJsonStr      JSON字符串数据
 * @param           items                   当前选中项
 * @param           values                  当前的选中值集合
 * @return          Object                  包含productLineCodes、serviceDomains、ServiceUnits
 * **/
serviceUnitTool.showSelServiceUnitDatasStr = function(serviceUnitJsonStr){
    if(objectIsNull(serviceUnitJsonStr))return [];
    let resultServiceUnits=[];
    let serviceUnits = JSON.parse(serviceUnitJsonStr);
    for (let serviceUnitObj of serviceUnits) {
        resultServiceUnits.push(serviceUnitObj.serviceUnit);
    }
    return resultServiceUnits.join(',');
};

const dictionaryMap = new Map();
serviceUnitTool.queryDictionary = async function (key, dicSetting, language) {
    if(dictionaryMap.get(key) === undefined) {
        dictionaryMap.set(key, new Map());
    }
    if(objectIsNull(dictionaryMap.get(key).get(language))) {
        await getDictionarySettings(dicSetting, language).then(res => {
            res.data && res.data.length > 0 ? dictionaryMap.get(key).set(language, res.data) : dictionaryMap.get(key).set(language, []);
        });
    }
    return dictionaryMap.get(key).get(language);
}
const serviceUnitMap = new Map();
serviceUnitTool.queryServiceUnits = async function (language) {
    let newLanguage = convertLanguage(language);
    let requestObj={};
    requestObj.languageCode=[newLanguage];
    await queryServiceUnits(requestObj).then(res => {
        if(!objectIsNull(res.data.data)){
            if(newLanguage===SERVICE_UNIT_LANGUAGE_CHI){//中文取值逻辑不同
                for(let serviceUnit of res.data.data){
                    if(!objectIsNull(serviceUnit.languages)){
                        for(let languageObj of serviceUnit.languages){
                            if(languageObj.languageCode===newLanguage){
                                serviceUnit.serviceUnitName=languageObj.serviceUnitName;
                            }
                        }
                    }
                }
            }
            serviceUnitMap.set('serviceUnits',res.data.data);
        }

    });
    return serviceUnitMap.get('serviceUnits');
}
function  convertLanguage(language){
    let newLanguage=SERVICE_UNIT_LANGUAGE_EN;//默认英文
    switch (language) {
        case 'zh-CN':
            newLanguage=SERVICE_UNIT_LANGUAGE_CHI
            break
        default:
            newLanguage=SERVICE_UNIT_LANGUAGE_EN;
            break
    }
    return newLanguage;
}


export default serviceUnitTool;

