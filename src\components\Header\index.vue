<template>
  <div class="sgs-top">
    <div class="top-bar__left">
      <logo></logo>
    </div>
    <div class="top-bar__title">
      <div class="top-bar__item" id="site-nav">
        <HorizontalMenu></HorizontalMenu>
      </div>
    </div>
    <div class="top-bar__right">
      <top-lang></top-lang>
      <el-badge
        :value="pendCount"
        :style="{ margin: `0 ${((pendCount + '').length - 0) * 6}px 0 0` }"
      >
        <el-icon @click="pendClick" class="chat-dot-square-icon">
          <ChatDotSquare color="#ffffff" />
        </el-icon>
      </el-badge>
      <el-dropdown>
        <span class="el-dropdown-link">
          <i class="sgs-header-user-icon">
            <el-icon color="#ffffff" size="15"><User /></el-icon>
          </i>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item disabled>
              {{ userInfo.userName }}
            </el-dropdown-item>
            <el-dropdown-item divided class="sgs_top_left_user_menu">

                <div @click="openPage('/')">
                  {{ $t('navbar.dashboard') }}
                </div>

            </el-dropdown-item>
            <el-dropdown-item class="sgs_top_left_user_menu">

                <div @click="openPage('/info/index')">
                  {{ $t('navbar.userinfo') }}
                </div>

            </el-dropdown-item>
            <el-dropdown-item class="sgs_top_left_user_menu">

                <div @click="openCurrentPage('/company/info')">
                  {{ $t('navbar.companyInfo') }}
                </div>

            </el-dropdown-item>
            <el-dropdown-item class="sgs_top_left_user_menu">

                <div @click="openPage('/personality')">
                  {{ $t('navbar.personalitySetting') }}
                </div>

            </el-dropdown-item>
            <el-dropdown-item divided @click="logout">
              {{ $t('navbar.logOut') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <pend-dialog
      :dialogVisible="dialogPendVisible"
      @update:dialogVisible="dialogPendVisible = $event"
    />
  </div>
</template>

<script>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  defineComponent,
} from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { queryPendList, updatePendReadStatus, queryPendCount } from '@/api/pend'
import { validatenull, objectIsNull } from '@/utils/validate'
import Logo from '@/components/Logo/index.vue'
import HorizontalMenu from '../Sidebar/HorizontalMenu.vue'
import TopLang from '@/components/TopLang/index.vue'
import { useI18n } from 'vue-i18n'
// 导入 PendDialog 组件
import PendDialog from '../PendDialog/index.vue'

export default defineComponent({
  name: 'top',
  components: {
    Logo,
    HorizontalMenu,
    TopLang,
    // 注册 PendDialog 组件
    PendDialog,
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const { t } = useI18n()
    const queryVal = ref('')
    const hideInTrfDetail = ref(true)
    const pendCount = ref(null)
    const dialogPendVisible = ref(false)
    const pendList = ref([])
    const page = ref({
      pageSize: 5,
      currentPage: 1,
      total: 0,
    })

    const showMenu = computed(() => store.state.common.showMenu)
    const userInfo = computed(() => store.state.user.userInfo)

    const readFormtter = (row, column) => {
      const type = row[column.property]
      return type === 0 ? $t('common.no') : $t('common.yes')
    }

    const initLoadPendCount = () => {
       queryPendCount({ isHandle_equal: 0, descs: 'update_time' }).then(
         (res) => {
           pendCount.value = res.data || null
         },
       )
    }

    const pendClick = () => {
      dialogPendVisible.value = true
    }

    const toList = () => {
      router.push({
        path: '/trf/newList',
        query: { from: 2, queryValue: queryVal.value },
      })
    }

    const logout = () => {
      store
        .dispatch('LogOut')
        .then(() => {
          window.location.href = '/#/login'
        })
        .catch(() => {
          window.location.href = '/#/login'
        })
    }
    const openPage = (path) => {
      window.open('/#' + path, '_blank')
    }
    const hideNav = () => {
      hideInTrfDetail.value = !['/trf/trfDetail', '/ccl/trf/newTrf'].includes(
        router.currentRoute.value.path,
      )
    }

  
    const openCurrentPage = (path) => {
      router.push({
        path: path,
      })
    }

    onMounted(() => {
      initLoadPendCount()
      hideNav()
      router.afterEach(() => {
        hideNav()
      })
    })

    watch(() => router.currentRoute.value, hideNav)

    return {
      queryVal,
      hideInTrfDetail,
      pendCount,
      dialogPendVisible,
      pendList,
      page,
      showMenu,
      userInfo,
      readFormtter,
      pendClick,
      toList,
      logout,
    }
  },
})
</script>

<style lang="scss">

.sgs-top {
  position: relative;
  .el-sub-menu {
  height: 100%;
  .el-sub-menu__title {
    font-size:20px;
    height: 100%;
  }
}
}
.sgs-header-user-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  color: #ffffff;
  padding: 2px;
  border: solid 2px #ffffff;
  border-radius: 50%;
}
.top-bar__left {
  position: absolute;
  top: 0;
  left: 20px;
}
.top-bar__right {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px 0 0;
  border-left: 1px solid #ffffff;
}

.chat-dot-square-icon {
  font-size: 28px;
  cursor: pointer;
}

.top-bar__item {
  position: relative;
  height: 80px;
  margin: 0 10px 0 10px;
  font-size: 16px;
}
</style>