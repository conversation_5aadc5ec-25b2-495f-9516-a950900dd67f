import request from './request'

// 定义通用的请求参数类型
type RequestParams = Record<string, any>

/**
 * 查询模板列表
 * @param param - 查询参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryTemplateList = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/protocol/v1/template/list',
    method: 'post',
    data: param,
  })
}

/**
 * 查询模板详情
 * @param param - 查询参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryTemplateDetail = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/protocol/v1/template/detail',
    method: 'post',
    data: param,
  })
}

/**
 * 保存编辑详情
 * @param param - 保存的数据
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const saveEditDetail = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/protocol/v1/save',
    method: 'post',
    data: param,
  })
}

/**
 * 查询分页数据
 * @param param - 查询参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryPage = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/protocol/v1/page',
    method: 'post',
    data: param,
  })
}

/**
 * 查询协议详情
 * @param param - 查询参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryProtocolDetail = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/protocol/v1/detail',
    method: 'post',
    data: param,
  })
}

/**
 * 处理操作
 * @param param - 操作参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const handlerAction = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/protocol/v1/action',
    method: 'post',
    data: param,
  })
}

/**
 * 查询 SCM 客户列表
 * @param param - 查询参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryScmCustomerList = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/customer/scm/query',
    method: 'post',
    data: param,
  })
}

/**
 * 保存附件
 * @param attachment - 附件数据
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const saveAttachment = (attachment: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/file/saveAttachment',
    method: 'post',
    data: attachment,
  })
}

/**
 * 下载文件
 * @param cloudId - 文件的云 ID
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const downloadFile = (cloudId: string): Promise<any> => {
  return request({
    url: '/api/sgs-mart/trf/downLoadFileByCloudId',
    method: 'post',
    params: { cloudId },
  })
}

/**
 * 根据 ID 删除属性
 * @param id - 属性的 ID
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const deleteAttrById = (id: string): Promise<any> => {
  return request({
    url: '/api/sgs-mart/attr/deleteAttrById',
    method: 'post',
    data: { id },
  })
}

/**
 * 根据 ID 更新属性备注
 * @param param - 更新参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const updateAttrRemarkById = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/attr/updateAttrRemarkById',
    method: 'post',
    data: param,
  })
}

/**
 * 查询属性
 * @param param - 查询参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryAttr = (param: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/attr/queryAttr',
    method: 'post',
    data: param,
  })
}

/**
 * 保存附件
 * @param attachment - 附件数据
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const saveAtt = (attachment: RequestParams): Promise<any> => {
  return request({
    url: '/api/sgs-mart/attr/saveAtt',
    method: 'post',
    data: attachment,
  })
}

/**
 * 查询目的地信息
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const queryDestination = (): Promise<any> => {
  return request({
    url: '/api/sgsapi/FrameWorkApi/trims/api/v3/queryCountryAndRegion?1=1&allLanguage=yes',
    method: 'get',
  })
}
