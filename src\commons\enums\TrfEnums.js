"use strict";

const TrfStatusEnums = [
    {name: '待递交', trfStatus: 1, lang: 'wel1.draftTrf', value: 'draftTrf', icon: '1.png'},
    {name: '已提交', trfStatus: 2, lang: 'wel1.trfSubmitted', value: 'trfSubmitted', icon: '2.png'},
    {name: '报价单待确认', trfStatus: 13, lang: 'wel1.quotationToBeConfirmed', value: 'quotationToBeConfirm', type: 'AFL', icon: '3.png'},
    {name: '已确认报价单', trfStatus: 14, lang: 'wel1.quotationConfirmed', value: 'quotationConfirm', type: 'AFL', icon: '4.png'},
    {name: '已受理', trfStatus: 3, lang: 'wel1.jobPreparation', value: 'jobPreparation', icon: '5.png'},
    {name: '测试中', trfStatus: 4, lang: 'wel1.testingInProgress', value: 'testingInProgress', icon: '6.png'},
    {name: '完成', trfStatus: 5, lang: 'wel1.reportCompleted', value: 'reportCompleted', icon: '7.png'}
];

//TRF 建单方式枚举
const TrfTypeEnums = {
    AFL_PREORDER: {
        NAME: "Afl_preorder",
        CODE: 30,
        DESC: "AFL PO建单"
    },
    USER_TEMPLATE: {
        NAME: "User_Template",
        CODE: 1,
        DESC: "用户模板建单"
    },
    DEFAULT_TEMPLATE: {
        NAME: "DEFAULT_Template",
        CODE: 10,
        DESC: "配置模板建单"
    },
    COPY: {
        NAME: "Copy",
        CODE: 2,
        DESC: "Copy TRF"
    },
    MATERIAL: {
        NAME: "Material",
        CODE: 11,
        DESC: "Material TRF"
    }
};

//TRF 来源枚举
const TrfSourceEnums = {
    ONLINE_TRF: {
        NAME: "ONLINE_TRF",
        CODE: 2,
        DESC: "线上(web)"
    },
    ORDER_TO_TRF: {
        NAME: "ORDER_TO_TRF",
        CODE: 3,
        DESC: "order to TRF"
    },
    REPORT_ENTRY_TRF: {
        NAME: "CUSTOMER_TRF",
        CODE: 4,
        DESC: "customer TRF"
    }
};

export {
    TrfStatusEnums, TrfTypeEnums, TrfSourceEnums
}
