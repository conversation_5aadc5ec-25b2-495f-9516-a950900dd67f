<template>
  <div class="smartFooter page-no-print" id="smartFooter">
    <el-row>
      <el-col :span="24" style="text-align: center">
        <span class="wyntbs"><b>When you need to be sure</b></span>
      </el-col>
    </el-row>
    <el-row>
      <el-col>&nbsp;</el-col>
    </el-row>
    <el-row class="smartFooter_bar">
      <el-col :offset="3" :span="6">
        <a
          class="smart_footer_a"
          :href="$t('footerBar.termsOfUsePdfPath')"
          target="_blank"
        >
          {{ $t('footerBar.termsOfUse') }}
        </a>
      </el-col>
      <el-col :span="6">
        <a
          class="smart_footer_a"
          :href="$t('footerBar.DPPPdfPath')"
          target="_blank"
        >
          {{ $t('footerBar.DPP') }}
        </a>
      </el-col>
      <el-col :span="6">
        <span>&copy; SGS Société Générale de Surveillance SA. (2024)</span>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { onMounted, onUnmounted } from 'vue'

export default {
  name: 'smartFooter',
  setup() {
    // 定义方法
    const initBaidu = () => {
      var _hmt = _hmt || []
      var hm = document.createElement('script')
      hm.src = 'https://hm.baidu.com/hm.js?b42460c118cbaa723a1945fdf6b677d8'
      var s = document.getElementsByTagName('script')[0]
      s.parentNode.insertBefore(hm, s)
    }

    // 生命周期钩子
    onMounted(() => {
      const currentEnv = import.meta.env.MODE
      if (currentEnv === 'production') {
        initBaidu()
      }
    })

    // 如果需要在组件销毁前或销毁时执行的逻辑，可以在这里添加
    onUnmounted(() => {
      // 清理逻辑（如果有）
    })

    // 返回一个空对象，因为这里没有需要暴露的响应式数据或方法
    return {}
  },
}
</script>

<style scoped lang="scss">
.smartFooter {
  .wyntbs {
    font-size: 18px;
    color: #ff6600;
  }
  .smartFooter_bar {
    text-align: center;
    background-color: #3c515b;
    height: 40px;
    line-height: 40px;
    font-size: 12px;
    color: white !important;
    .smart_footer_a {
      color: white !important;
    }
    .smart_footer_a:hover {
      color: #ff6600 !important;
    }
  }
}
</style>
