<template>
    <div class="sgs_smart__chatView" id="sgs_smart__chatView">
        <el-dialog
                v-model="showChat"
                width="60%"
                title="Comment"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                @close="emit('cancelDia')"
        >
            <div class="chat_content">
                <el-table
                        ref="commentTable"
                        v-loading="loading"
                        style="width: 100%"
                        class="comment_chat_table"
                        :data="commentDataList"
                        :border="false"
                        size="mini"
                        :show-header="false"
                        empty-text="No Data"
                        fit
                >
                    <el-table-column prop="content" label="">
                        <template #default="{ row }">
                            <el-row>
                                <el-col :offset="leftOrRight(row) === 'right' ? 10 : 0" :span="14">
                                    <div :class="'comment_table_chat_cell_' + leftOrRight(row)">
                                        <el-row>
                                            <el-col :span="20">
                                                {{ row.createUser }}
                                                {{ transTime(row.createTime) }}
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col>
                                                <div style="white-space: pre-wrap">{{ row.content }}</div>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </el-col>
                            </el-row>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <el-row>
                <el-col>
                    <el-form ref="commentForm" :model="commentForm" @submit.native.prevent>
                        <el-form-item prop="content">
                            <el-input
                                    type="textarea"
                                    :rows="3"
                                    clearable
                                    show-word-limit
                                    name="Content"
                                    maxlength="5000"
                                    class="comment_textarea"
                                    style="width: calc(100% - 200px)"
                                    size="medium"
                                    placeholder="comment"
                                    v-model="commentForm.content"
                            />
                            <el-button
                                    style="width: 100px"
                                    type="primary"
                                    size="medium"
                                    @click="sendComment"
                                    :loading="saveBtnLoading"
                            >
                                Send
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-col>

                <el-col v-if="needSendEmail">
                    <el-form label-position="left" ref="emailForm" :model="commentForm" label-width="140px">
                        <el-form-item label="Notification to">
                            <el-select
                                    v-model="commentForm.mailGroup"
                                    multiple
                                    filterable
                                    allow-create
                                    default-first-option
                                    style="width: calc(100% - 200px)"
                                    @change="changeEmailGroup"
                                    placeholder="Email group"
                            >
                                <el-option
                                        v-for="(e, ind) in NotificationTo"
                                        :key="'eg_' + ind"
                                        :label="e.display"
                                        :value="e.code"
                                />
                            </el-select>
                        </el-form-item>

                        <el-form-item label=" ">
                            <el-select
                                    v-model="commentForm.mailList"
                                    multiple
                                    filterable
                                    allow-create
                                    default-first-option
                                    style="width: calc(100% - 200px)"
                                    @change="changeMailList"
                                    placeholder="Email address"
                            />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>


<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)
import { tzFormChina } from '@/utils/datetimeUtils.ts'
import api from '@/api/comments.ts'

defineOptions({
    name: 'Comments'
})
// Props
const props = defineProps({
    mainObjectId:{
        type:String,
        default:''
    },
    objectId: {
        type: String,
        required: true
    },
    objectType: {
        type: String,
        required: true
    },
    needSendEmail: {
        type: Boolean,
        default: true
    }
})

// Emits
const emit = defineEmits(['update', 'cancelDia'])



// Reactive state
const pageLoading = ref(false)
const showChat = ref(false)
const saveBtnLoading = ref(false)
const loading = ref(false)
const commentDataList = ref([])
const NotificationToMap = ref({})
const NotificationTo = ref([])
const oldMailGroup = ref([])
const commentForm = reactive({
    content: '',
    mailGroup: [],
    mailList: []
})

// Methods
function initPage() {
    const param = {
        objectId: props.objectId,
        objectType: props.objectType
    }
    loading.value = true
    api.queryCommentList(param).then(res => {
        loading.value = false
        if (res.status === 200 && res.data && res.data.data) {
            commentDataList.value = res.data.data
            scrollToBottom()
        }
    }).catch(err => {
        console.log("查询到的err", err)
    })
    showChat.value = true
}

function initNotification() {
    api.queryNotification(props.mainObjectId).then(res => {
        if (res.status === 200 && res.data && res.data.data) {
            const { data } = res.data
            data.forEach(da => {
                const { code, display, mailList } = da
                NotificationTo.value.push({ code, display })
                NotificationToMap.value[code] = mailList
            })
        }
    }).catch(err => {
        console.log("queryNotification err", err)
    })
}

function sendComment() {
    if (!commentForm.content) return
    const param = {
        objectId: props.objectId,
        objectType: props.objectType,
        content: commentForm.content,
        mailList: commentForm.mailList,
        mainId: props.mainObjectId
    }
    saveBtnLoading.value = true
    api.addComment(param).then(res => {
        if (res.status === 200) {
            // 假设你有类似 $notify 的插件支持
            this.$notify.success('Success')
            emit('update')
            resetPage()
        }
        saveBtnLoading.value = false
    }).catch(err => {
        console.log("send comment err", err)
        saveBtnLoading.value = false
        this.$notify.error("Send Error")
    })
}

function resetPage() {
    commentForm.content = ''
    if (props.needSendEmail) {
        commentForm.mailGroup = []
        oldMailGroup.value = []
        commentForm.mailList = []
    }
    initPage()
}

function changeMailList(val) {
    let emailList = val
    if (val && val.length > 0) {
        const re = /^[\w\.\-]+@([\w\-]+\.)+[\w\-]+$/
        emailList = val.filter(v => re.test(v))
    }
    commentForm.mailList = [...emailList]
}

function changeEmailGroup() {
    const { mailGroup } = commentForm
    if (oldMailGroup.value.length > mailGroup.length) {
        const deleteGroup = oldMailGroup.value.filter(m => !mailGroup.includes(m))
        const dg = deleteGroup[0]
        const delMailList = NotificationToMap.value[dg]
        delMailList.forEach(dm => {
            const delIndex = commentForm.mailList.findIndex(ml => ml === dm)
            if (delIndex > -1) {
                commentForm.mailList.splice(delIndex, 1)
            }
        })
        oldMailGroup.value = [...mailGroup]
        return
    }

    const addGroup = mailGroup.filter(m => !oldMailGroup.value.includes(m))
    const ag = addGroup[0]
    const addMailList = NotificationToMap.value[ag]
    addMailList.forEach(am => {
        const addIndex = commentForm.mailList.findIndex(ml => ml === am)
        if (addIndex === -1) {
            commentForm.mailList.push(am)
        }
    })
    oldMailGroup.value = [...mailGroup]
}

function transTime(time) {
    if (!time) return ''
    return tzFormChina(time, 'YYYY-MM-DD HH:mm:ss')
}

function leftOrRight(row) {
    return row.myComment ? 'right' : 'left'
}

function scrollToBottom() {
    setTimeout(() => {
        const chatContent = document.querySelector('.chat_content')
        if (chatContent) {
            chatContent.scrollTop = chatContent.scrollHeight
        }
    }, 500)
}

// Lifecycle
onMounted(() => {
    if (props.needSendEmail) {
        initNotification()
    }
    initPage()
})
</script>

<style lang="scss">
.sgs_smart__chatView {
  background: #fff;

  .el-form.el-form--label-left .el-form-item__label {
    float: left !important;
  }

  height: 600px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 2px !important;
  }

  .chat_content {
    width: 100%;
    height: 320px;
    max-height: 360px;
    overflow-y: auto;
    border-bottom: solid 1px #CCCCCC;
    margin-bottom: 10px;
    transition-property: all;
    transition-duration: 1s;
  }

  .comment_textarea .el-textarea__inner {
    max-height: 150px !important;
    border-top: none;
    border-right: none;
    border-left: none;
  }

  .comment_chat_table td {
    border: none !important;
  }

  .el-table::v-deep tbody tr:hover {
    background-color: transparent !important;
  }

  .el-table::v-deep tbody tr:hover>td {
    background-color: transparent !important;
  }

  .comment_table_chat_cell_left,
  .comment_table_chat_cell_right {
    position: relative;
    background-color: #f1f1f1;
    border-radius: 10px;
    padding: 2px 10px;
    margin: 0 20px;
  }

  .comment_table_chat_cell_left::before {
    content: "";
    position: absolute;
    left: -10px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-right: 10px solid #f1f1f1;
    border-bottom: 10px solid transparent;
  }

  .comment_table_chat_cell_right::before {
    content: "";
    position: absolute;
    right: -20px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 10px solid #f1f1f1;
  }
}
</style>