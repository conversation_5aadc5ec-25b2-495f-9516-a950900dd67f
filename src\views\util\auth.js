import Cookies from 'js-cookie'

const Token<PERSON>ey = 'sgsToken'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  sessionStorage.setItem(TokenKey,token);
  return Cookies.set(TokenKey, token)
}
export function removeToken(tokenKey) {
  sessionStorage.removeItem(TokenKey);
  return Cookies.remove(TokenKey)
}

/**
 *  获取local stage 中设置的值
 * @param key
 * @param value
 */
export function setItem(key, value) {
  if (key) {
    window.localStorage.setItem(key, JSON.stringify(value))
  }
}

export function removeItem(key) {
  window.localStorage.removeItem(key)
}

export function getItem(key) {
  if (!key) {
    return null
  }
  let buyer = window.localStorage.getItem(key)
  try {
    return JSON.parse(buyer)
  } catch (err) {
    return null
  }
}


/*获取supper 选中的buyer 信息 */
export function setBuyer(buyer) {
  setItem('buyer', buyer)
}

export function getBuyer() {
  return getItem('buyer')
}
