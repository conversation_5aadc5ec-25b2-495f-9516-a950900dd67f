<template>
    <div class="smart_views_customer_relationship_component_EditContactDialog" id="smart_views_customer_relationship_component_EditContactDialog">
        <el-dialog
                v-model="openContact"
                :title="t('scm.contact.editContact')"
                width="75%"
                lock-scroll
                :show-close="false"
                :close-on-press-escape="false"
                :close-on-click-modal="false"
                draggable>
            <EditContact v-if="openContact && showContact" ref="contactsRef" :contacts="contactsList"></EditContact>
            <el-row style="padding-top: 20px">
                <el-col style="text-align: center">
                    <el-button type="primary" plain class="footer_btn" @click="submitForm">{{t('scm.btnName.save')}}</el-button>
                    <el-button  plain class="footer_btn" @click="cancelForm">{{t('scm.btnName.cancel')}}</el-button>
                </el-col>
            </el-row>
        </el-dialog>
    </div>

</template>

<script setup>
import {mapGetters} from 'vuex'
import {ref, watch,onMounted} from 'vue'
import EditContact from './EditContact.vue'
import customerRelationApi from "@/api/customerRelation";
import { ElNotification, ElMessageBox } from 'element-plus';
import {useI18n} from 'vue-i18n';
const { t } = useI18n();

const openContact = ref(false)
const showContact = ref(false)
const emit= defineEmits(['submitForm','cancelDia'])
const prop = defineProps({
    companyId:{
        type:String,
        default:''
    }
})

const contactsRef = ref()
const submitForm = async ()=>{
    //还没有更新contact接口
    const contacts = await contactsRef.value.getContactSaveData();
    let param ={
        scmId:prop.companyId,
        contacts
    }
    customerRelationApi.updateContacts(param).then(res=>{
        if(res.status==200){
            initOpen();
            ElNotification.success({ message: t('success'), duration: 2000});
            cancelForm();
        }
    }).catch(err=>{

    })
}
const cancelForm = ()=>{
    openContact.value=false;
    emit('cancelDia')
}
const contactsList = ref([]);
const initOpen = ()=>{
    showContact.value=false;
    if(!prop.companyId){
        openContact.value=true;
        showContact.value=true;
        return;
    }
    //查询contact
    customerRelationApi.querySCMContacts({scmId:prop.companyId}).then(res=>{
        if(res.status==200 && res.data){
            contactsList.value = res.data || [];
            openContact.value=true;
            showContact.value=true;
        }else{
            cancelForm();
        }
    }).catch(err=>{
        cancelForm();
    })
}

onMounted(()=>{
    initOpen();
})

</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_component_EditContactDialog {


}
</style>