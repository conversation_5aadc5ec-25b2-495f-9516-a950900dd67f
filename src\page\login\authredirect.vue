<template>
  <div>
  </div>
</template>

<script>
    import {refeshToken} from "@/api/user"
    import { validatenull } from "@/util/validate";
    import { queryBuSetting} from "@/api/common/index";
    import {getToken } from '@/util/auth'
    export default {
        name: "index",
        data() {
            return {
                token: this.$route.query.token,
                redirect: this.$route.query.redirect,
                systemId:this.$route.query.systemId,
                loading:null,
                checkToken:''
            }
        },
        methods: {
            querySetting(){
              return new Promise(resolve => {
                let params ={
                  systemId:15,
                  groupCode:'SGSSmartRedirectedUrl',
                  productLineCode:'SGS',
                  paramCode:this.systemId
                }
                queryBuSetting(params).then(res=>{
                  if(res.data && res.data.data && res.data.data.length>0){
                    let configObj = res.data.data[0];
                    let {paramValue} = configObj;
                    //不存在配置地址
                    if(!paramValue){
                      resolve(false);
                      return
                    }
                    this.loading.close();
                    let newSkipUrl = encodeURIComponent(this.redirect)+"&token="+this.checkToken+"&systemId="+this.systemId;
                    let rootUrl = paramValue;
                    let redirectUrl = rootUrl+newSkipUrl;
                    window.location.href = redirectUrl;
                    resolve(true);
                  }else{
                    resolve(false);
                  }
                },error=>{
                  resolve(false);
                }).catch(e=>{
                  resolve(false);
                })
              })
            },
            async checkAndRedirect(isLogin,callback){
              //不存在systemId参数，不用读取配置，按照老的逻辑进行跳转
              if(!this.systemId){
                callback()
                return
              }
              //token无效，跳转到登录页面,由登录页面接管处理
              if(!isLogin){
                //拼接参数到login页面，login页面读取的是redirectFrom
                this.loading.close();
                this.$router.replace({
                  path: "/login",
                  query:{
                    redirectedFrom:this.redirect,
                    token:this.checkToken,
                    systemId:this.systemId
                  }
                });
              }else{
                //token有效，读取配置进行跳转
                let redirectTo = await this.querySetting();
                this.loading.close();
                //没有获取到配置，获取配置出现异常，走默认逻辑
                if(!redirectTo){
                  callback();
                }
              }
            },
            handleLogin() {
                this.loading = this.$loading({
                    lock: true,
                    text: `Login...`,
                    spinner: "el-icon-loading"
                });

                this.$store.dispatch("LoginByToken", this.checkToken).then(() => {
                  //验证systemId以及后续处理
                  this.checkAndRedirect(true,()=>{
                    if(!validatenull(this.redirect)){
                          this.$store.dispatch("GetMenu").then(data => {
                              if (data.length === 0) return;
                              this.$router.$avueRouter.formatRoutes(data, true);
                              this.$router.push({ path: this.redirect });
                              this.loading.close();
                          });
                      }else{
                          this.$router.push({ path: "/" });
                          this.loading.close();
                      }
                  });

                }).catch(() => {
                    this.checkAndRedirect(false,()=>{
                      this.loading.close();
                      this.$router.push({ path: "/403" });
                    });
                });
            }
        },
        created() {
           this.checkToken = (getToken() || this.token);
           this.handleLogin()
        }
    }
</script>

<style scoped>

</style>
