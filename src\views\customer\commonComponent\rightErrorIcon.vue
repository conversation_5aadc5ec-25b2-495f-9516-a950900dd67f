<template>
    <div class="sgs_smart_common_rightErrorIcon"
         id="sgs_smart_common_rightErrorIcon"
         v-bind="$attrs"
         @click="$emit('click')"
         :style="{
        'width':width+'px',
        'height':height+'px',
        'color':'#fff',
        'background':(right?'#00d26a':'#bfbfbf')}">
        {{right?'✅':'X'}}
    </div>
</template>

<script>
    export default {
        name: "rightErrorIcon",
        props: {
            width:{
                type:Number,
                default(){
                    return 20
                }
            },
            height:{
                type:Number,
                default(){
                    return 20
                }
            },
            right:{
                type:Boolean,
                default(){
                    return true
                }
            }
        }
    }
</script>

<style lang="scss">
    .sgs_smart_common_rightErrorIcon {
        font-family: 'Arial' !important;
        display: inline-flex;
        border-radius: 50%;
        overflow: hidden;
        align-content: space-around;
        justify-content: center;
        cursor: pointer;
        margin: 0 0 0 5px;
        box-shadow: 1px 1px 2px lightgray;
    }
</style>