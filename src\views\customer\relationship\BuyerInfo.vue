<template>
    <div class="smart_views_customer_relationship_buyer" id="smart_views_customer_relationship_buyer">
        <common-table
                v-loading="tableOption.loading"
                border
                fit
                style="width:100%"
                row-key="id"
                ref="buyerInfoTable"
                stripe
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
                @sortBy="changeSort"
                @selection-change="selectionChange"
                @changePage="changePage"
        >
            <template #menuRight>
                <el-button plain type="primary" icon="Plus" @click="openSearchCustomer = true">{{t('scm.linkName.linkBuyer')}}</el-button>
            </template>
            <template #actionColumn="{row}">
                <div class="action_icon">
                    <el-row :gutter="5">
                        <el-col :span="8" v-if="btnRole(row,'Query')">
                            <el-tooltip placement="top" :content="t('scm.linkName.linkManufacture')">
                                <el-icon class="icon-rel-manufacture"  @click="handlerOpenManufacture(row)">
                                    <Histogram />
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="row.status=='1' && btnRole(row,'Disable')" >
                            <el-tooltip placement="top" :content="t('scm.btnName.disable')">
                                <el-icon class="icon-disabled disabled-color" @click="handlerChangeStatus(row,'0')">
                                    <Remove/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="row.status=='0' && btnRole(row,'Enable')">
                            <el-tooltip placement="top" :content="t('scm.btnName.enable')">
                                <el-icon  @click="handlerChangeStatus(row,'1')">
                                    <Remove/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="btnRole(row,'Approve')" >
                            <el-tooltip placement="top" :content="t('scm.btnName.approve')">
                                <el-icon @click="handlerApproveReject(row,'APPROVED')">
                                    <CircleCheck/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="btnRole(row,'Reject')">
                            <el-tooltip placement="top" :content="t('scm.btnName.reject')">
                                <el-icon  @click="handlerApproveReject(row,'REJECTED')">
                                    <CircleClose/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                    </el-row>
                </div>
            </template>
        </common-table>
    </div>

    <SearchCustomer
            v-if="openSearchCustomer"
            @cancelDia="openSearchCustomer = false"
            @saveSuccess="initDataList"
            createIfNotExist
            roleType="BUYER"
            systemId="UM"
            useParent
    ></SearchCustomer>

    <el-dialog
            v-model="openManufacture"
            :title="t('scm.manufacturer')"
            width="75%"
            lock-scroll
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            draggable
    >
        <Manufacture
            v-if="openManufacture"
            :parentId="linkManufactureProps.parentId"
            :tier="linkManufactureProps.tier"
            :currentRole="linkManufactureProps.currentRole"
            :targetRole="linkManufactureProps.targetRole"
            onlyQueryRelationCustomer
            :showLinkBuyer="false"
            :showContacts="false"
        ></Manufacture>
    </el-dialog>

</template>

<script setup>
import {mapGetters} from 'vuex'
import {ref, defineOptions, reactive, onBeforeMount, watch} from 'vue'
import CommonTable from "../../../components/TableList/CommonTable.vue";
import SearchCustomer from "./innerComponent/SearchCustomer.vue";
import Manufacture from './ManufactureInfo.vue'
import customerRelationApi from "@/api/customerRelation";
import { ElNotification, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
const {t,locale} = useI18n();
import dayjs from 'dayjs';

watch(locale, (newV)=>{
    initDataList();
})

defineOptions({
    name:'BuyerInfo'
})
const buyerInfoTable = ref();
const openSearchCustomer = ref(false);
const props = defineProps({
    parentId: {
        type: String,
        default: ''
    },
    tier:{
        type:String,
        default:'T1'
    },
    currentRole:{
        type:String,
        default:'SUPPLIER'
    },
    targetRole:{
        type:String,
        default:'BUYER'
    }
})

const tableOption = reactive({
    dataList: [],
    loading: false,
    menuShow: true,
    size:'small',
    option: {
        hideRowColor: true,
        selection: false,
        selectionDis: false,
        sortSelectionFiledName:'',
        showSortIcon:true,
        index: true,
        action: true,
        actionWidth: 150,
        disableOption:{
            disableNeedStrikethrough:true,
            disableValue:'0',
            disableProp:'status'
        },
        column: [
            {prop: 'companyName', label: 'scm.buyerName', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'sgsCode', label: 'scm.sgsCode', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'board',
                label: 'scm.sgsBoard',
                hide: false,
                filter: false,
                slot: false,
                notSort:true,
                dicData: [
                    {label:'On Board',value:'sgs'},
                    {label:'Not on Board',value:'notSgs'}
                ],
                type: 'Input'
            },
            {
                prop: 'approvalStatus',
                label: 'scm.approveStatus',
                hide: false,
                filter: true,
                slot: false,
                multiple:true,
                i18n:true,
                dicData: [
                    {label: 'scm.approveStatusEnumName.approve', value: 'APPROVED',tag:true,type:'success'},
                    {label: 'scm.approveStatusEnumName.inProgress', value: 'PENDING',tag:true,type:'warning'},
                    {label: 'scm.approveStatusEnumName.noRequired', value: 'NOT_REQUIRED',tag:true,type:'info'},
                    {label: 'scm.approveStatusEnumName.reject', value: 'REJECTED',tag:true,type:'primary'},
                ],
                type: 'Select'
            },
            {prop: 'updateUserRole', label: 'scm.updateUser', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateTime', label: 'scm.updateTime', hide: false, filter: true, slot: false, type: 'DateRange', minWidth: 280},

        ],
    },
    filters: {},
    sort:{
        sortBy:"",
        sortOrder:""
    },
    page: {
        show: true,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})

const changePage = (page)=>{
    tableOption.page.page = page.page;
    tableOption.page.rows = page.rows;
    initDataList();
}
const selectionChange = (val)=>{
}
const changeSort=(sort)=>{
    tableOption.sort.sortOrder = sort.prop;
    tableOption.sort.sortBy = sort.orderBy;
    initDataList();
}
const selectCustomer = (customer)=>{
    initDataList()
}
const openManufacture = ref(false)
const linkManufactureProps = reactive({
    parentId:'',
    tier:'',
    currentRole:'',
    targetRole:'',
    childCompanyId:''
});
const handlerOpenManufacture = (row)=>{
    let {id,childCompanyId} = row;
    //查询加载列表时使用
    linkManufactureProps.parentId = id;
    linkManufactureProps.tier = 'T2';
    linkManufactureProps.currentRole = 'SUPPLIER';
    linkManufactureProps.targetRole = 'MANUFACTURER';
    //保存依赖关系时使用
    linkManufactureProps.childCompanyId = childCompanyId;
    openManufacture.value = true;
}

const handlerChangeStatus = (row,status)=>{
    let {id} = row;
    customerRelationApi.changeStatus({id,status}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    }).catch(err=>{

    })
}
const handlerApproveReject = (row,approvalStatus)=>{
    let {id} = row;
    customerRelationApi.approveReject({shipId:id,approvalStatus}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    })
}

watch(()=>tableOption.filters,()=>{
    initDataList();
},{deep:true});


const initDataList = ()=>{
    let startUpdateTime = '';
    let endUpdateTime = '';
    if (tableOption.filters.updateTime && tableOption.filters.updateTime.length === 2) {
        startUpdateTime = dayjs(tableOption.filters.updateTime[0]).format('YYYY-MM-DD');
        endUpdateTime = dayjs(tableOption.filters.updateTime[1]).format('YYYY-MM-DD');
    }
    let param = {
        id: props.parentId,
        "currentRole": props.currentRole,
        "targetRole": props.targetRole,
        "tier": props.tier,
        ...tableOption.filters,
        ...tableOption.sort,
        current:tableOption.page.page,
        size:tableOption.page.rows,
        startUpdateTime,
        endUpdateTime
    }
    customerRelationApi.querySCM(param).then(res=>{
        //tableOption.loading=false;
        if(res.status==200 && res.data){
            let {records,total} = res.data;
            tableOption.dataList = records;
            tableOption.page.total = total;
        }
    }).catch(err=>{

    })
}
const btnRole = ({permissions},code)=>{
    return (permissions || []).includes(code);
}

onBeforeMount(()=>{
    initDataList();
})


</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_buyer {

}
</style>