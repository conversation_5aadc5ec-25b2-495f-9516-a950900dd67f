<template>
    <basic-container v-loading="loading">
        <div class="sgs_smart_more" id="sgs_smart_more">
            <div class="backToHomepage">
                <el-row>
                    <el-col :span="12">
                        <el-input placeholder="Search for..." v-model="searchData" size="mini"></el-input>
                    </el-col>
                    <el-col :span="12">
                        <router-link class="backToHomepage_btn" style="font-size: 14px;color:white" :to="{ path: '/' }">
                            {{$t("wel1.backToHome")}}
                        </router-link>
                    </el-col>
                </el-row>
            </div>
            <el-tabs v-model="activeType">
                <el-tab-pane :label='$t("wel1.documentLibrary")' name="1"></el-tab-pane>
                <el-tab-pane :label='$t("wel1.SGSNewService")' name="2"></el-tab-pane>
                <el-tab-pane :label='$t("wel1.hot")' name="3"></el-tab-pane>
            </el-tabs>
            <el-card :body-style="{height:'600px',overflow:'auto'}">
                <el-row
                    class="document_box"
                     v-for="(td,index) in (tableData.filter(t=>(t.title || '').indexOf(searchData)>-1))"
                     :key="index">
                    <el-col :span="18">
                        <div class="document_title">
                            <a @click="openPage(td)"
                               style="font-size: 16px;cursor: pointer;color:#ff6600;font-weight: bold;">
                                {{td.title}}
                            </a>
                        </div>
                    </el-col>
                    <el-col :span="18">
                        <div class="document_desc">
                            {{td.desc}}
                        </div>
                    </el-col>
                    <el-col :span="18">
                        <div class="document_time">
                            {{td.updateTime || td.createTime}}
                        </div>
                        <el-divider></el-divider>
                    </el-col>
                </el-row>

            </el-card>
        </div>
    </basic-container>
</template>

<script>
    import {documentLibrary,queryNewServiceList} from "@/api/welIndex";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {mapGetters} from "vuex";
    import {getCloudFileURL} from "@/api/common";
    export default {
        name: "more",
        data() {
            return {
                searchData:'',
                loading:false,
                activeType:'',
                originalTableData:[],
                count:20,
                tableData:[]
            }
        },
        methods: {
            openPage(rows){
                let {url , filePath} = rows;
                if(!url && !filePath){
                    return
                }
                if(url){
                    let preHttps = url.substr(0,5);
                    if(preHttps.indexOf("http")==-1 || preHttps.indexOf("https")=="-1"){
                        url = "https://"+url;
                    }
                    //this.$notify.success("跳转的地址："+url);
                    window.open(url,'_blank');
                }else if(filePath){
                    getCloudFileURL(filePath).then((res) => {
                        window.open(res.data, "_blank");
                    });
                }
            },
            load(){
                this.count+=5;
                let newData = this.originalTableData.slice(20,5);
                newData.forEach(d=>{
                    this.tableData.push(d);
                })
            },
            initPage(){
                console.log("这是more",this.$route.query)
                let query = this.$route.query;
                if(query){
                    let {type} = query;
                    if(["1","2","3"].includes(type)){
                        this.activeType = type;
                    }else{
                        this.activeType = "1";
                    }
                }else {
                    this.activeType = "1";
                }
            },
            onLoadLibrary(){ //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                    languageId=LanguageEnums.EN.code;
                }else{
                    languageId=LanguageEnums.CN.code;
                }
                this.loading = true;
                documentLibrary(this.userInfo.productLineCode,languageId).then((res) => {
                    let datas = res.data.data;
                    (datas || []).forEach(da=>{
                        let {documentTitle,documentDec,documentUrl,createTime,attachments,updateTime} = da;
                        let filePath = '';
                        if(attachments && attachments.length>0){
                            filePath = attachments[0].fileUrl;
                        }
                        this.tableData.push({
                            title:documentTitle,
                            desc:documentDec,
                            url:documentUrl,
                            createTime,
                            updateTime,
                            filePath
                        })
                        this.originalTableData = this.tableData;
                    })
                    this.loading = false;
                }).finally(() => {
                    this.loading = false;
                });
            },
            onLoadNewService(isHot=0){
                //将当前语言放入请求中
                let languageId=LanguageEnums.EN.code;
                if(LanguageEnums.EN.name==this.language){
                    languageId=LanguageEnums.EN.code;
                }else{
                    languageId=LanguageEnums.CN.code;
                }
                this.loading = true;
                queryNewServiceList(isHot,this.userInfo.productLineCode,languageId,null,0).then((res) => {
                    let datas = res.data.data;
                    (datas || []).forEach(da=>{
                        let {serviceUrl,createTime,textDisplay,filePath,updateTime} = da;
                        this.tableData.push({
                            title:textDisplay,
                            desc:'',
                            url:serviceUrl,
                            createTime,
                            updateTime,
                            filePath
                        });
                        this.originalTableData = this.tableData;
                    })
                    this.loading = false;
                }).finally(() => {
                    this.loading = false;
                });
            },
            initPageData(){
                let newV = this.activeType;
                this.searchData = '';
                this.tableData = [];
                if(newV-0==1){
                    this.onLoadLibrary();
                }
                if(newV-0==2){
                    this.onLoadNewService();
                }
                if(newV-0==3){
                    this.onLoadNewService(1);
                }
            }
        },
        computed: {
            ...mapGetters([
                "userInfo",
                "language"
            ])
        },
        created() {
            this.initPage();
        },
        watch: {
            activeType:{
                immediate:false,
                handler(newV){
                    this.initPageData();
                }
            },
            language:{
                immediate:false,
                handler(newV){
                    this.initPageData();
                }
            }
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_more {
        background: #fff;
        padding: 24px 36px;
        .backToHomepage{
            position: absolute;
            right: 75px;
            z-index: 99;
            .backToHomepage_btn{
                border: solid 1px #ff6600;
                padding: 8px;
                border-radius: 5px;
                margin-left: 2px;
                background: #ff6600;
                cursor: pointer;
            }
        }
        .sgs_smart_more_content{
            padding: 30px;

            .document_box{
                .document_title{
                }

                .document_desc{
                    height: 2em;
                }

                .document_time{
                    height: 0.5em;
                    color:gray;
                }
            }

        }
    }
</style>