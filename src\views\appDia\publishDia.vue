<template>
    <div id="publishDia" class="publishDia">

      <div class="publish_main">
        <div class="publish_main_cloud"></div>
        <div class="publish_main_rocket">
          <svg version="1.1" class="rocket_svg" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 511.999 511.999" style="enable-background:new 0 0 511.999 511.999;" xml:space="preserve">
					<path style="fill:#FFE470;" d="M206.047,392.377c-4.5,17.404-13.801,33.906-27.304,47.409
						c-31.206,30.906-146.653,67.741-159.555,71.641c-5.102,1.5-10.802,0-14.702-3.901s-5.403-9.602-3.901-15.003
						c3.901-12.601,41.034-127.751,71.938-158.955c13.502-13.502,30.005-22.804,47.409-27.306l42.907,42.909L206.047,392.377z">
					</path>
            <g>
						<path style="fill:#FFD400;" d="M162.84,349.172l43.207,43.205c-4.5,17.404-13.801,33.906-27.304,47.409
							c-31.206,30.906-146.653,67.741-159.555,71.641c-5.102,1.5-10.802,0-14.702-3.901L162.84,349.172z"></path>
              <path style="fill:#FFD400;" d="M167.942,365.373l-42.308,42.606c-3,2.701-6.9,4.201-10.501,4.201c-3.901,0-7.801-1.5-10.803-4.201
							v-0.3c-5.701-6-5.701-15.303,0-21.003l42.608-42.308c5.701-6.001,15.003-6.001,20.704,0h0.3
							C173.941,350.071,173.941,359.672,167.942,365.373z"></path>
					</g>
            <path style="fill:#FDBF00;" d="M167.942,344.37c6,5.701,6,15.303,0,21.004l-42.308,42.606c-3,2.701-6.9,4.201-10.501,4.201
						c-3.901,0-7.801-1.5-10.803-4.201v-0.3l63.312-63.311L167.942,344.37L167.942,344.37z"></path>
            <path style="fill:#FF7816;" d="M174.454,147.549c-106.552,0-140.178,90.082-142.876,93.092c-2.344,4.645-2.124,10.183,0.615,14.621
						c2.725,4.439,7.56,7.149,12.775,7.149c61.578,5.445,100,8.842,96.93,8.57l51.03-123.434h-18.474V147.549z"></path>
            <path style="fill:#FF4B00;" d="M241.013,369.921c0.233,3.073,7.754,102.226,7.267,95.802c0,11.172,11.801,18.41,21.772,13.391
						c3.004-2.692,93.092-36.96,93.092-142.876v-18.475L241.013,369.921z"></path>
            <path style="fill:#FF9F00;" d="M253.155,386.976c-15.902,8.102-31.805,15.904-46.808,23.405c-5.701,3-12.601,1.8-17.404-2.701
						l-84.614-84.614c-4.5-4.8-5.701-11.701-2.699-17.402c7.501-15.003,15.303-30.906,23.403-46.808
						c4.802-9.302,39.607,11.402,44.409,1.8l81.913,81.913C241.753,347.369,262.457,382.176,253.155,386.976z"></path>
            <path style="fill:#FF7816;" d="M253.155,386.976c-15.902,8.102-31.805,15.904-46.808,23.405c-5.701,3-12.601,1.8-17.404-2.701
						l-42.306-42.306l63.911-63.611l40.807,40.807C241.753,347.369,262.457,382.176,253.155,386.976z"></path>
            <path style="fill:#ECECF1;" d="M499.497,149.037c-13.502,51.61-37.806,93.616-75.614,129.923
						c-44.107,42.606-114.62,79.813-170.729,108.017l-63.911-63.911l-64.211-64.21c28.205-56.11,65.413-126.923,108.019-170.729
						c36.305-37.806,78.313-62.111,129.923-75.614l30.303,11.102c0.601,26.104,11.102,49.809,28.205,66.91
						c17.103,17.103,40.808,27.606,66.912,28.205L499.497,149.037z"></path>
            <path style="fill:#E2E2E7;" d="M499.497,149.037c-13.502,51.61-37.806,93.616-75.614,129.923
						c-44.107,42.606-114.62,79.813-170.729,108.017l-63.911-63.911l232.239-232.54c17.103,17.103,40.808,27.606,66.912,28.205
						L499.497,149.037z"></path>
            <path style="fill:#ff6600;" d="M380.076,131.933c-29.104-29.104-76.813-29.104-105.917,0c-29.106,29.406-29.106,76.514,0,105.919
						v0.3c29.104,29.104,76.813,29.104,105.917,0C409.482,208.746,409.482,161.34,380.076,131.933z"></path>
            <path style="fill:#FF9F00;" d="M499.497,149.037c-2.401,0-9.302-0.3-11.702-0.3c-34.507-0.599-65.11-14.401-87.614-36.905
						c-22.504-22.504-36.307-53.11-36.907-87.614c0-2.401-0.298-9.302-0.298-11.702C405.582,1.413,453.289-1.288,497.098,0.512
						c3.901,0,7.501,1.501,10.203,4.201c2.699,2.701,4.2,6.302,4.2,10.203C513.298,58.72,510.299,106.429,499.497,149.037z"></path>
            <path style="fill:#5A5A5A;" d="M252.554,280.459c-5.701,6.001-15.301,6.001-21.003,0c-6.001-5.701-6.001-15.303,0-21.004
						c5.701-6,15.301-6,21.003,0C258.556,265.155,258.556,274.758,252.554,280.459z"></path>
            <path style="fill:#FF7816;" d="M499.497,149.037c-2.401,0-9.302-0.3-11.702-0.3c-34.507-0.599-65.11-14.401-87.614-36.905
						L507.299,4.713c2.699,2.701,4.2,6.302,4.2,10.203C513.298,58.72,510.299,106.429,499.497,149.037z"></path>
            <path style="fill:#ff6600;" d="M380.076,238.152c-29.104,29.104-76.813,29.104-105.917,0v-0.3l105.917-105.919
						C409.482,161.34,409.482,208.746,380.076,238.152z"></path>
            <path style="fill:#444444;" d="M252.554,280.459c-5.701,6.001-15.301,6.001-21.003,0l21.003-21.004
						C258.556,265.155,258.556,274.758,252.554,280.459z"></path>
					</svg>
        </div>
        <div class="publish_content">
          <div class="publish_content_header" >
            <h1 v-if="data.language === LanguageEnums.CN.name">系统维护通知</h1>
            <h1 v-else>System Maintenance Notice</h1>
          </div>
          <div class="publish_content_text" v-if="data.language === LanguageEnums.CN.name">
            <p>我们的系统目前正在进行维护。</p>
            <p style="color: #ff6600">预计将在 {{data.finishDateTime}}恢复所有服务。</p>
            <p>对于由此给您带来的不便，我们深表歉意，并感谢您的耐心等待。</p>
            
          </div>
          <div class="publish_content_text" v-else>
            <p>Our system is currently undergoing maintenance.</p>
            <p style="color: #ff6600">We expect to have everything back up and running at {{data.finishDateTime}}.</p>
            <p>We apologize for any inconvenience this may cause and appreciate your patience.</p>
          </div>
          <div class="publish_content_footer" v-if="data.language === LanguageEnums.CN.name">
           {{ data.publishRemark.cn}}
          </div>
          <div class="publish_content_footer" v-else>
            {{data.publishRemark.en }} 
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import {mapGetters} from "vuex";
import { LanguageEnums } from "@/commons/enums/LanguageEnums";

    export default {
        name: "publishDia",
        data() {
            return {
              LanguageEnums: LanguageEnums,
              interval:null,
              data:null,
              language:LanguageEnums.EN.name
            }
        },
        ...mapGetters(["language"]),
        methods: {
          close(){
            this.$emit("closeDia")
          },
          refreshData(data){
            console.log("维护页面语言",this.language);
            if(data.language){
              this.language=data.language;
            }           
            this.$set(this,'data',data);
          },
          createTime(){
            debugger;
            let finishDate = +new Date(this.data.finishDate);
            //let day = document.querySelector(".content_time_day");
            let hour = document.querySelector(".content_time_hour");
            if(hour == null){
              return;
            }
            let min = document.querySelector(".content_time_min");
            let sec = document.querySelector(".content_time_sec");
            let labelH = document.querySelector(".publish_text_en label.en_h");
            let labelM = document.querySelector(".publish_text_en label.en_m");
            let labelS = document.querySelector(".publish_text_en label.en_s");

            let currentDate = +new Date();
            let distance = finishDate - currentDate;
            var d = Math.floor(distance / (1000 * 60 * 60 * 24));
            var h = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var m = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            var s = Math.floor((distance % (1000 * 60)) / 1000);

            if(distance<0){
              d = h = m = s =0;
            }

            s = s<10?('0'+s):s;
            m = m<10?('0'+m):m;
            h = h<10?('0'+h):h;
            d = d<10?('0'+d):d;

            //day.innerHTML = d;
            hour.innerHTML = h;
            min.innerHTML = m;
            sec.innerHTML = s;
            labelH.innerHTML = h;
            labelM.innerHTML = m;
            labelS.innerHTML = s;

            setTimeout(()=>{
              this.createTime();
            },1000)
          },

        },
        created() {
          console.log("维护页面语言",this.language)
          if(this.publishData.language){
              this.language=this.publishData.language;
          }  
          this.data = this.publishData;
        },
        mounted() {
          //this.createTime();
        },
        props:{
          publishData:Object,
        },
        components: {}
    }
</script>

<style scoped lang="scss">
  .publish_main{
    /*position: absolute;*/
    /*left: calc(50% - 340px);*/
    /*top:calc(50% - 200px);*/
    width: 680px;
    height: 350px;
    background: white;
    border-radius: 0 0 20px 20px;
    .publish_content{
      position: absolute;
      top: 0;
      left: 0;
      width: 680px;
      height: 330px;

      .publish_content_header{
        text-align: center;
      }
      .publish_content_text{
        padding: 20px  100px 0 100px;
        max-height: 200px;
        overflow: hidden;
        font-size: 16px;
        word-break: auto-phrase;
      }
      .publish_content_footer{
        position: absolute;
        bottom: 0;
        left: 50px;
        height: 80px;
        width: 580px;
        border-top: solid 1px gray;
      }
    }
  }
  .publish_main_cloud{
    content: '';
    position: absolute;
    top: -30px;
    width: 60px;
    height: 60px;
    background-color: white;
    border-radius: 50%;
    box-shadow:
      #fff 0 0 0 0 ,
      #fff 45px -20px 0 5px,
      #fff 95px -10px 0 8px,
      #fff 155px 0px 0 8px,
      #fff 215px -20px 0 5px,
      #fff 275px -10px 0 5px,

      #fff 310px 0px 0 5px,

      #fff 345px -10px 0 5px,
      #fff 405px -20px 0 5px,
      #fff 465px 0px 0 8px,
      #fff 525px -10px 0 8px,
      #fff 575px -20px 0 5px,
      #fff 615px 0px 0 5px;
  }
  .publish_main_rocket{
    position: absolute;
    width: 80px;
    height: 160px;
    top: -100px;
    left: 300px;
  }
  .rocket_svg{
    transform: rotate(-45deg);
  }
</style>
