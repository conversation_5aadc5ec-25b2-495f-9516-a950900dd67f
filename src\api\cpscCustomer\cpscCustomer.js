import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/detailCustomerInfo',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/saveForm',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/updateForm',
    method: 'post',
    data: row
  })
}

export const updateStatus = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/updateStatus',
    method: 'post',
    data: row
  })
}

export const syncTradeData = (row) => {
  return request({
    url: '/api/sgs-e-filling/sgs-work/cpscCustomer/syncTradeData',
    method: 'post',
    data: row
  })
}

