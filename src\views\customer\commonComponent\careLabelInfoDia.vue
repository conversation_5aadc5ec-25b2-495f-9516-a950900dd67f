<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_common_careLabelInfoDia" id="sgs_smart_common_careLabelInfoDia">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane :label="'Buyer'+customerCode" name="buyerList"></el-tab-pane>
                <el-tab-pane label="General" name="generalList"></el-tab-pane>
            </el-tabs>
            <div v-if="activeName=='buyerList' && showImg">
                <el-row class='module-margin-vertical'>
                    <el-col :span="4">
                        Reference Care Code:
                    </el-col>
                    <el-col :span="20">
                        <div class="search-btn">
                            <el-input v-model="rccCode"></el-input>
                            <el-button type="primary" @click="queryCareLabelCode">Search</el-button>
                        </div>
                    </el-col>
                </el-row>
                <el-table
                    :data="rccDataList" class='module-margin-vertical'>
                    <el-table-column prop="" label="Action" width="80px">
                        <template slot-scope="{row}">
                            <input type="checkbox"  checked="checked">
                        </template>
                    </el-table-column>
                    <el-table-column prop="rccCode" label="Reference Care Code(RCC)" width="280px"></el-table-column>
                    <el-table-column prop="selectCareLabel" label="Image">
                        <template slot-scope="{row}">
                            <img
                                v-for="(item,index) in row.selectCareLabel"
                                :key="'img_'+index"
                                v-show="item.picture"
                                :src="'data:image/png;base64,'+item.picture" />
                        </template>
                    </el-table-column>
                </el-table>
                <el-table
                        :data="rccDescDataList" class='module-margin-vertical'>
                    <el-table-column prop="" label="Action" width="80px">
                        <template slot-scope="{row}">
                            <input type="checkbox" v-model="row.selectDetailsDescription">
                        </template>
                    </el-table-column>
                    <el-table-column prop="careLableDetailsDescription" label="CareLabel Details Description"></el-table-column>
                </el-table>
            </div>
            <div v-if="activeName=='generalList' && showImg">
                <el-row>
                    <el-col :span="4">
                        Country:
                    </el-col>
                    <el-col :span="8">
                        <el-select
                            style="width: 100%"
                            v-model="selectCountry"
                            @change="changeCountry"
                            filterable>
                            <el-option v-for="(item,index) in countryArray"
                                       :key="'count_'+index"
                                       :value="item.countryRegionName"
                                       :label="item.countryRegionName">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="12">
                        <draggable v-model="generalSelectCareLabelArray"
                                   style="display: inline"
                                   group="careLabel"
                                   animation="300">
                            <transition-group>
                                <img
                                    v-for="(type,indd) in generalSelectCareLabelArray"
                                    :name="'section_ind'+indd"
                                    :key="'section_ind'+indd"
                                    :src="'data:image/png;base64,'+type.picture"/>
                            </transition-group>
                        </draggable>
                        <a @click="clearCareLabel" v-show="generalSelectCareLabelArray && generalSelectCareLabelArray.length>0">
                            <i class="el-icon-remove-outline"></i>
                        </a>
                    </el-col>
                </el-row>
                <el-table
                    class='module-margin-vertical'
                    :data="careLabelArray"
                    v-loading="imgTableLoading"
                >
                    <el-table-column prop="conditionType" label="Condition Type" width="120px">
                        <template slot-scope="{row}">
                            {{row.conditionType}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="imgArray" label="Care Image">
                        <template slot-scope="{row}">
                            <el-row :gutter="20">
                                <el-col :span="2"
                                        v-for="(itemImg,indexImg) in (row.imgArray || []).slice(0,12)"
                                        :key="'img_'+indexImg"
                                        style="text-align: center">
                                    <img @click="itemImg.checked = !itemImg.checked,checkboxChange()"
                                         style="cursor: pointer"
                                         :src="'data:image/png;base64,'+itemImg.picture"/><br>
                                    <input type="checkbox"
                                           @change="checkboxChange"
                                           v-model="itemImg.checked"
                                           :checked="itemImg.checked"
                                           :true-value="true"
                                           :false-value="false"
                                           :key="indexImg"/>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20" v-show="row.showMore">
                                <el-col :span="2"
                                        v-for="(itemImg,indexImg) in (row.imgArray || []).slice(12)"
                                        :key="'img_'+indexImg"
                                        style="text-align: center">
                                    <img :src="'data:image/png;base64,'+itemImg.picture"/><br>
                                    <input type="checkbox"
                                           @change="checkboxChange(itemImg)"
                                           v-model="itemImg.checked"
                                           :checked="itemImg.checked"
                                           :true-value="true"
                                           :false-value="false"
                                           :key="indexImg"/>
                                </el-col>
                            </el-row>
                        </template>
                    </el-table-column>
                    <el-table-column prop="showMore" width="80">
                        <template slot-scope="{row}">
                            <a v-if="row.imgArray.length>12" @click="row.showMore = !row.showMore" style="cursor: pointer">MORE</a>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div v-if="showDesc">
                <el-table
                    ref="descTable"
                    v-if="showDescTable"
                    :data="descMap[activeName]"
                    :border="false"
                    fit
                    size="mini"
                >
                    <el-table-column type="selection"></el-table-column>
                    <el-table-column prop="warningId" label="Text Code" width="120px"></el-table-column>
                    <el-table-column prop="warningDescription" label="Additional Care Text Information(ACI)Description"></el-table-column>
                </el-table>
            </div>
        </div>
    </basic-container>
</template>

<script>
    import productApi from '@/api/newSamples'
    export default {
        name: "careLabelInfoDia",
        data() {
            return {
                pageLoading: false,
                showDescTable:false,
                imgTableLoading:false,
                activeName:'buyerList',
                rccDataList:[],
                rccDescDataList:[],
                careLabelArray:[],
                selectCountry:'',
                countryArray:[],
                descMap:{
                  buyerList:[],
                  generalList:[]
                },
                rccCode:'',
                generalSelectCareLabelArray:[]
            }
        },
        methods: {
            initPage() {
                let param = {
                    warningType: 'General',
                }
                productApi.careLabelApi.queryWarning(param).then(res=>{
                    if(res.status==200) {
                        this.descMap.generalList = res.data || [];
                    }
                })
                if(!!this.customerCode){
                    param = {
                        customerCode: this.customerCode,
                        warningType: "Client",
                        customerType: "Group"
                    }
                    productApi.careLabelApi.queryWarning(param).then(res=>{
                        if(res.status==200){
                            this.descMap.buyerList = res.data || [];
                        }
                    })
                }
                productApi.careLabelApi.queryCareLabelCountry().then(res=>{
                    //console.log("queryCareLabelCountry",res);
                    if(res.status==200){
                        this.countryArray = res.data || [];
                    }
                })
                this.initObjData();
                this.showDescTable = true;
            },
            initObjData(){
                if(this.showImg &&  this.careLabelObject && this.careLabelObject.selectImgIds){
                    let {careLabelSeq,productItemNo,imgArray,radioType,selectCountry,
                        selectImgIds,imgPathYun,careLabelFileId,cloudId,careInstruction} = this.careLabelObject;
                    this.activeName = radioType==2?"generalList" :"buyerList";
                    //选中国家
                    this.selectCountry = selectCountry;
                    this.clearCareLabel();
                    this.queryCareImg(selectImgIds,()=>{
                        //选中的图片
                        this.checkboxChange();
                    });
                }
            },
            queryCareLabelCode(){
                if(!this.customerCode || !this.rccCode){
                    return;
                }
                this.rccDataList = [];
                this.rccDescDataList = [];
                let params = {
                    countryOrRegion: '',
                    usageType: '',
                    requestType: 'Client',
                    customerCode: this.customerCode,
                    careLabelCode: this.rccCode,
                    customerType: 'Group',
                    careLabelDetailDescFlag: "true"
                }
                productApi.careLabelApi.queryBuyerCareLabel(params).then(res=>{
                    console.log("query res",res);
                    let {data} = res;
                    if(!data || data.length==0){
                        return;
                    }
                    let len = data.length;
                    let careLabelArr = [];
                    for(let i = 0 ;i<len-2;i++){
                        careLabelArr.push(data[i]);
                    }
                    let obj = {
                        selectCareLabel:careLabelArr,
                        rccCode:this.rccCode
                    }
                    this.rccDataList.push(obj)
                    //第二个table的数据
                    let detailDescObj = data[len-2];
                    let otherLans = data[len-1];
                    let {careLabelDetailsDescription} = detailDescObj || {};
                    let careLableDetailsDescription = careLabelDetailsDescription;
                    let selectDetailsDescription = !!careLabelDetailsDescription;
                    //todo 多语言处理
                    let descObj = {
                        selectDetailsDescription,
                        careLableDetailsDescription
                    }
                    this.rccDescDataList.push(descObj)
                })

            },
            changeCountry(){
                this.clearCareLabel();
                this.queryCareImg();
            },
            clearCareLabel(){
                this.careLabelArray.forEach(item=>{
                    (item.imgArray || []).forEach(img=>{
                        this.$set(img,'checked',false);
                    })
                })
                this.generalSelectCareLabelArray = [];

            },
            queryCareImg(selectImgIds,callback){
                let params =  {
                    countryOrRegion: this.selectCountry,
                    usageType: 'Washing',
                    requestType: 'General'
                };
                let selectIds =(selectImgIds || '').split(",");
                this.imgTableLoading = true;
                productApi.careLabelApi.queryCareLabel(params,data=>{
                    console.log("queryCareLabel",data)
                    this.imgTableLoading = false;
                    data.forEach((da,i)=>{
                        da.showMore = false;
                        if(da.imgArray && da.imgArray.length>0){
                            da.imgArray.forEach((im,ind)=>{
                                im.checked = selectIds.includes(im.imageDetailId);
                            });
                        }
                    });
                    this.careLabelArray = data;
                    if(callback){
                        callback();
                    }
                });
            },
            getSaveData(){
                return new Promise((resolve,reject)=>{
                    let result;
                    if(this.showImg){
                        result = this.getImgData();
                    }
                    if(this.showDesc){
                        result = this.getDescData();
                    }
                    if(!result){
                        resolve("error");
                    }else{
                        resolve(result);
                    }
                })
            },
            async getImgData(){
                //组装洗唛结构
                let selectImgIds = '';
                let imgArray = [];
                if(this.activeName =='buyerList'){
                    if(this.rccDataList && this.rccDataList.length>0){
                        let imgIds = [];
                        this.rccDataList[0].selectCareLabel.forEach(c=>{
                            imgIds.push(c.imageDetailId);
                            imgArray.push(c);
                        })
                        selectImgIds = imgIds.join(",");
                    }
                }else{
                    selectImgIds = this.generalSelectCareLabelArray.map(c=>c.imageDetailId).join(",");
                    imgArray = this.generalSelectCareLabelArray;
                }
                //没有选择图片的话，直接清空
                if(!imgArray || imgArray.length==0){
                    let radioType = this.activeName=='buyerList'?1:2;
                    let result = {
                        careLabelSeq:1,
                        productItemNo:['SupplierCode_1'],
                        imgArray:[],
                        radioType,
                        selectCountry:'',
                        selectImgIds,
                        imgPathYun:[],
                        careLabelFileId:'',
                        cloudId:'',
                    }
                    return result;
                }
                //执行小图片上传，拼接为大图片
                let updateParam = {
                    imgArray,
                    orderId:1
                }
                let uploadResult = await productApi.careLabelApi.uploadCareLabel(updateParam);
                if(uploadResult.status!=200 || !uploadResult.data || !uploadResult.data.data){
                    this.$notify.error("Merge care image failed, please save again")
                    return false;
                }
                let uploadResp = uploadResult.data.data;
                let {careLabelId,careLabelFileId,path,cloudId} = uploadResp;
                //组装imgPathyun 结构
                let imgPathResp = await productApi.careLabelApi.downloadPathById({key:careLabelFileId});
                let imgPathYun = [];
                if(imgPathResp.status==200 && imgPathResp.data){
                    let path = imgPathResp.data;
                    imgPathYun.push({
                        path,
                        key:careLabelFileId
                    })
                }else{
                    this.$notify.error("Merge care image failed, please save again");
                    return false;
                }
                let radioType = this.activeName=='buyerList'?1:2;

                let result = {
                    careLabelSeq:1,
                    productItemNo:['SupplierCode_1'],
                    imgArray,
                    radioType,
                    selectCountry:this.selectCountry,
                    selectImgIds,
                    imgPathYun,
                    careLabelFileId,
                    cloudId,
                }
                return result;
            },
            getDescData(){
                let descSelection = this.$refs.descTable.selection;
                let careInstruction = (descSelection || []).map(d=>d.warningDescription).join("/");
                return {careInstruction};
            },
            checkboxChange(){
                let selected = [];
                this.careLabelArray.forEach(item=>{
                    let imgArray = item.imgArray || [];
                    if(!imgArray || imgArray.length==0){
                        return
                    }
                    selected = [...selected,...imgArray.filter(img=>img.checked)];

                })
                this.generalSelectCareLabelArray = selected;

            },
            handleClick(){
                this.showDescTable = false;
                this.$nextTick(()=>{
                    this.showDescTable = true;
                })
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {},
        props: {
            customerCode:{
                type:String,
                default(){
                    return ''
                }
            },
            careLabelObject:{
                type:Object,
                default(){
                    return {}
                }
            },
            selectImgIds:{
                type:String,
                default(){
                    return ''
                }
            },
            showImg:{
                type:Boolean,
                default(){
                    return false
                }
            },
            showDesc:{
                type:Boolean,
                default(){
                    return false
                }
            }
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";
    .sgs_smart_common_careLabelInfoDia {
        font-family: 'Arial' !important;
        background: #fff;
        .sgs_smart_carelabel_button_tabs{
            border: 1px solid #E6E6E6;
            padding: 4px;
            width: fit-content;
            button{
                padding: 0 16px;
                height: 32px;
                line-height: 32px;
                &.active{
                    color: #fff;
                    background: #f60;
                }
            }
        }
        .search-btn {
            position: relative;
            .el-button {
                position: absolute;
                top:0; 
                right: 0;
            }
        }
        .module-margin-vertical {
            margin-top: $module-margin-vertical;
        }
    }
</style>