import axios from '@/router/axios';

let api = {
    filterSave:(data)=>{
        let url = '/api/sgs-mart/search/filter/save';
        return axios({
            url,
            method:'POST',
            data
        })
    },
    /**
     * 参数支持 TRF,MATERIAL,PRODUCT
     * */
    filterQuery:(sourceType)=>{
        let url = '/api/sgs-mart/search/filter/query'
        return axios({
            url,
            method:'POST',
            data:{sourceType:sourceType?sourceType:'TRF'}
        })
    },
    filterDelete:(id)=>{
        let url = '/api/sgs-mart/search/filter/delete'
        return axios({
            url,
            method:'POST',
            data:{id}
        })
    },
    filterSetDefault:(id)=>{
      let url = "/api/sgs-mart/search/filter/default"
      return axios({
          url,
          method:'POST',
          data:{
              id,
              defaultFlag:1
          }
      })
    },
    dimensionQuery:(param)=>{
        let url ='/api/sgs-mart/search/dimension/list'
        return axios({
            url,
            method:'POST',
            data:param
        })
    },
    query:(param)=>{
        let url ='/api/sgs-mart/search/list'
        let timezone = (0 - new Date().getTimezoneOffset())/60;
        return axios({
            url,
            method:'POST',
            data:param,
            headers:{
                timezone
            }
        })
    }
}

export default api;