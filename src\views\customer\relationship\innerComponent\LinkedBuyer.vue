<template>
    <div class="smart_views_customer_relationship_LinkedBuyer" id="smart_views_customer_relationship_LinkedBuyer">
        <common-table
                v-loading="tableOption.loading"
                border
                style="width:100%"
                row-key="id"
                :height="450"
                ref="linkedbuyerInfoTable"
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
                @sortBy="changeSort"
        >
            <template #menuRight>
                <el-button type="primary" plain size="mini" @click="handleAdd" icon="Plus">{{t('scm.linkName.linkBuyer')}}</el-button>
            </template>

            <template #childAssignCode="{row}">
                <div class="table_column_slot">
                    <div class="table_column_left">
                        <span v-if="!row.edit">
                            {{row.childAssignCode}}
                        </span>
                        <el-input  :ref="(el)=>setAssignCodeInputRef(el, row.id)" clearable show-word-limit maxlength="50"  size="small" v-model="row.childAssignCode" @blur="updateAssignCode(row)" v-if="row.edit"></el-input>
                    </div>
                    <div class="table_column_right_icon" v-show="!row.edit">
                        <el-icon @click="editAssignCode(row)"><EditPen/></el-icon>
                    </div>
                    <div class="table_column_right_icon" v-show="row.edit">
                        <el-icon @click="updateAssignCode(row)"><Finished/></el-icon>
                    </div>
                </div>
            </template>

            <template #actionColumn="{row}">
                <div class="action_icon">
                    <el-tooltip placement="top" content="Disable">
                        <el-icon class="icon-disabled disabled-color" v-if="row.status=='1'" @click="handlerChangeStatus(row,'0')">
                            <Remove/>
                        </el-icon>
                    </el-tooltip>
                    <el-tooltip placement="top" content="Enable">
                        <el-icon v-if="row.status=='0'" @click="handlerChangeStatus(row,'1')">
                            <Remove/>
                        </el-icon>
                    </el-tooltip>
                </div>
            </template>
        </common-table>
    </div>
    <el-dialog
            v-model="linkedBuyerDia"
            :title="t('scm.linkName.linkBuyer')"
            width="40%"
            append-to-body
            :show-close="false"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            class="searchBuyerCustomerDia"
            draggable
    >
        <el-form label-position="top"
                 style="padding-top: 20px"
                 :model="buyerForm"
                 ref="buyerFormRef">
            <el-form-item :label="t('scm.companyName')"
                          prop="id"
                          :rules="[{validator: (rule, value, callback) => {
                                      if(!buyerForm.id){
                                          callback(new Error(t('scm.validate.companyName')));
                                      }else {
                                          callback();
                                      }
                                }},
                                {required:true, message: t('scm.validate.companyName'), trigger: 'change' }]">
                <el-select
                    style="width: 100%"
                    v-model="buyerForm.id"
                    clearable
                    filterable
                    @change="handleCompanyChange"
                    @clear="handleCompanyClear"
                >
                    <el-option
                        v-for="(comp,index) in buyerDataList"
                        :label="comp.companyName"
                        :value="comp.id"
                    >
                        {{comp.companyName}} {{comp.sgsCode ? '( '+comp.sgsCode+' )' : ''}}
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div style="text-align: center;padding-top: 20px">
                <el-button type="primary" plain @click="handlerSaveBuyerCustomer">{{t('scm.btnName.add')}}</el-button>
                <el-button @click="handlerCancelSearch">{{t('scm.btnName.cancel')}}</el-button>
            </div>
        </template>
    </el-dialog>



</template>

<script setup>
import {mapGetters} from 'vuex'
import {ref, reactive, watch, onBeforeMount, nextTick} from "vue";
import CommonTable from "../../../../components/TableList/CommonTable.vue";
import SearchCustomer from "./SearchCustomer.vue";
import customerRelationApi from "@/api/customerRelation";
const linkedbuyerInfoTable = ref();
const linkedBuyerDia = ref(false);
const companyName = ref('');
import { useI18n } from 'vue-i18n';
import {ElNotification} from "element-plus";
const {t} = useI18n();
import dayjs from 'dayjs';

defineOptions({
    name:'LinkedBuyer'
})
const props = defineProps({
    parentId: {
        type: String,
        default: ''
    },
    tier:{
        type:String,
        default:'T2'
    },
    currentRole:{
        type:String,
        default:'MANUFACTURER'
    },
    targetRole:{
        type:String,
        default:'BUYER'
    },
    roleType:{
        type:String,
        default:''
    },
    childCompanyId:{
        type:String,
        default:''
    }
})

const tableOption = reactive({
    dataList: [],
    loading: false,
    menuShow: true,
    size: 'small',
    option: {
        hideRowColor: true,
        selection: false,
        selectionDis: false,
        sortSelectionFiledName: '',
        showSortIcon: true,
        index: true,
        action: true,
        actionWidth: 100,
        disableOption:{
            disableNeedStrikethrough:true,
            disableValue:'0',
            disableProp:'status'
        },
        column: [
            {prop: 'companyName', label: 'scm.buyerName', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'sgsCode', label: 'scm.sgsCode', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'childAssignCode', label: 'scm.assignedCode', hide: false, filter: true, slot: true, type: 'Input'},
            {
                prop: 'approvalStatus',
                label: 'scm.approveStatus',
                multiple:true,
                hide: false,
                filter: true,
                slot: false,
                i18n:true,
                dicData: [
                    {label: 'scm.approveStatusEnumName.approve', value: 'APPROVED',tag:true,type:'success'},
                    {label: 'scm.approveStatusEnumName.inProgress', value: 'PENDING',tag:true,type:'warning'},
                    {label: 'scm.approveStatusEnumName.noRequired', value: 'NOT_REQUIRED',tag:true,type:'danger'},
                    {label: 'scm.approveStatusEnumName.reject', value: 'REJECTED',tag:true,type:'info'},
                ],
                type: 'Select'
            },
            {prop: 'updateUserRole', label: 'scm.updateUser', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateTime', label: 'scm.updateTime', hide: false, filter: true, slot: false, type: 'DateRange', minWidth: 280}
        ],
    },
    filters: {},
    sort:{
        sortBy:"",
        sortOrder:""
    },
    page: {
        show: false,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})

const changeSort=(sort)=>{
    tableOption.sort.sortOrder = sort.prop;
    tableOption.sort.sortBy = sort.orderBy;
    initDataList();
}
const assignCodeInputRefs = ref({});
const setAssignCodeInputRef = (el,id)=>{
    assignCodeInputRefs.value[id] = el;
}
const editAssignCode = (row)=>{
    row.edit=true
    let {id} = row;
    nextTick(()=>{
        assignCodeInputRefs.value[id].focus();
    })
}
const updateAssignCode = (row)=>{
    let {id,childAssignCode,oldChildAssignCode} = row;
    if(oldChildAssignCode == childAssignCode){
        row.saveLoading=false;
        row.edit = false;
        return;
    }
    row.saveLoading = true;
    customerRelationApi.changAssignCode({id,childAssignCode}).then(res=>{
        row.saveLoading=false;
        row.edit = false;
        row.oldChildAssignCode = childAssignCode;
    }).catch(err=>{
        row.saveLoading=false;
    })
}
const handlerChangeStatus = (row,status)=>{
    let {id} = row;
    customerRelationApi.changeStatus({id,status}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    }).catch(err=>{

    })
}


watch(()=>tableOption.filters,()=>{
    initDataList();
},{deep:true});

const initDataList = ()=>{
    let startUpdateTime = '';
    let endUpdateTime = '';
    if (tableOption.filters.updateTime && tableOption.filters.updateTime.length === 2) {
        startUpdateTime = dayjs(tableOption.filters.updateTime[0]).format('YYYY-MM-DD');
        endUpdateTime = dayjs(tableOption.filters.updateTime[1]).format('YYYY-MM-DD');
    }
    let param = {
        id: props.parentId,
        "currentRole": props.currentRole,
        "targetRole": props.targetRole,
        "tier": props.tier,
        ...tableOption.sort,
        ...tableOption.filters,
        startUpdateTime,
        endUpdateTime
    }
    //tableOption.loading=true;
    customerRelationApi.queryManufacture(param).then(res=>{
        //tableOption.loading=false;
        if(res.status==200 && res.data){
            let {records,total} = res.data;
            records.forEach(re=>{
                re['oldChildAssignCode'] = re.childAssignCode;
            })
            tableOption.dataList = records;
            tableOption.page.total = total;
        }
    }).catch(err=>{

    })
}

onBeforeMount(() => {
    initDataList();
})

const handleAdd = () => {
    //打开新的弹窗 进行添加
    queryRelationBuyerCustomer();
    linkedBuyerDia.value = true;
}
const handlerCancelSearch = ()=>{
    handleCompanyClear();
    linkedBuyerDia.value = false;
}
const buyerDataList = ref([])
const buyerForm = reactive({
    companyName: '',
    id: ''
})
const buyerFormRef = ref();
const handlerSaveBuyerCustomer =async ()=>{
    try{
        await buyerFormRef.value.validate();
    }catch (e){
        return
    }
    let param = {
        refRootId:'',
        refParentId: buyerForm.id,
        header: {
            child: {
                companyId:props.childCompanyId,
                roleType: props.roleType
            }
        },
        domain: "CUSTOMER"
    }
    customerRelationApi.applyRelation(param).then(res=>{
        if(res.status==200){
            ElNotification.success({ message: t('success'), duration: 2000});
            handlerCancelSearch();
            initDataList();
        }
    })
}
const handleCompanyChange = (item)=>{
}
const handleCompanyClear = ()=>{
    buyerForm.id = '';
    buyerForm.companyName = ''
}
const queryRelationBuyerCustomer = ()=>{
    handleCompanyClear();
    buyerDataList.value = [];
    let startUpdateTime = '';
    let endUpdateTime = '';
    if (tableOption.filters.updateTime && tableOption.filters.updateTime.length === 2) {
        startUpdateTime = dayjs(tableOption.filters.updateTime[0]).format('YYYY-MM-DD');
        endUpdateTime = dayjs(tableOption.filters.updateTime[1]).format('YYYY-MM-DD');
    }
    let param = {
        id: '',
        "currentRole": 'SUPPLIER',
        "targetRole": props.targetRole,
        "tier": 'T1',
        current:1,
        size:1000,
        startUpdateTime,
        endUpdateTime
    }
    customerRelationApi.querySCM(param,{loading:'false'}).then(res=>{
        //tableOption.loading=false;
        if(res.status==200 && res.data){
            let {records} = res.data;
            buyerDataList.value = records;
        }
    }).catch(err=>{

    })
}

</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_LinkedBuyer {
}


</style>