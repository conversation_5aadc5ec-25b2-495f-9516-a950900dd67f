<template>
    <basic-container>
        <el-row>
            <el-col span="24">
                <div ref="report" style="height: 900px"></div>
            </el-col>
        </el-row>
    </basic-container>
</template>

<script>
    import {mapGetters} from "vuex";
    import {getReport1Param} from "@/api/common/powerbi";

    export default {
        name: "powerbi",
        data() {
            return {
                activeNames: ['1', '2', '3', '5'],
                logActiveNames: ['13']
            };
        },
        computed: {
            ...mapGetters(["userInfo"]),
        },
        created() {
            this.initPowerReport();
        },
        methods: {
            async initPowerReport() {

                let reportId = this.$route.query.reportId;
                let workspaceId = this.$route.query.workspaceId;
                let libraryId = this.$route.query.libraryId;

                if (!reportId || !workspaceId) {
                    // this.$info({message: 'Report and Workspace is required!'})
                    // return false
                    // workspaceId = '414269ce-eabc-4496-836b-ef674a061798';
                    // reportId = '39aeaefa-43a1-4c29-b53c-01b9a568c1c0';

                    workspaceId = 'fcf75139-fbd5-459b-9a1c-6adb8a8b7736';
                    reportId = '0f8d8724-f1b9-454e-9e78-f196a7549902';
                }
                this.loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.3)',
                })
                this.loading.close()
                let pbiType = this.$route.query.pbiType;
                if(!pbiType){
                    pbiType = 0;
                }
                let res = await getReport1Param({"workspaceId": workspaceId,   "reportId": reportId, "pbiType": pbiType,libraryId });
                debugger;
                if (res.data.code === 200) {
                    this.config = res.data.data;
                    this.$nextTick(() => {
                        this.renderReport()
                    })
                } else {
                    this.loading.close()
                    // this.$warn({message: 'Loading PowerBI Report is Fail'})
                }
            },
            renderReport () {
                if (this.config == null) {
                    return
                }

                const accessToken = this.config.embedToken.token;

                // Read embed URL from Model
                const embedUrl = this.config.embedUrl;

                // Read report Id from Model
                const embedReportId = this.config.id;

                // Get models. models contains enums that can be used.
                const models = window['powerbi-client'].models;

                this.reportInstance = powerbi.embed(this.$refs.report, {
                    type: 'report',
                    tokenType: models.TokenType.Embed,
                    accessToken: accessToken,
                    embedUrl: embedUrl,
                    id: embedReportId,
                    permissions: models.Permissions.All,
                    settings: {
                        filterPaneEnabled: true,
                        navContentPaneEnabled: true
                    }
                })

                // Report.off removes a given event handler if it exists.
                this.reportInstance.off("rendered");

                // Report.on will add an event handler which prints to Log window.
                this.reportInstance.on("rendered", () => this.loading.close());

            },
            destory () {
                if (this.reportInstance) {
                    this.reportInstance.service.reset(this.$refs.report)
                    this.reportInstance = null
                }
            }
        }
    };
</script>

<style lang="scss">
    .ibox {
        margin-bottom: 5px;
        padding: 5px;

        .title{
            height: 60px;
            width: 60px;
            text-align: center;
            line-height: 60px;
            i {
                font-size: 30px;
                color: #FFFFFF;
                line-height: 60px;
            }
        }

        .content {
            padding: 0px 20px;

            h4{
                font-weight: bold;
            }
            span{
                font-weight: bold;
            }
        }
    }
</style>
