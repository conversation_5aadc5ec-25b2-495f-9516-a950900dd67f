<template>
    <el-main>
        <el-drawer :title="$t('labContact.selLabContact')" :visible.sync="visible" @close="close" size="80%">
            <el-card class="box-card">

                <!--实验室联系人-->
                <el-form :model="searchForm" @submit.native.prevent rules="searchFormRules" ref="searchForm" label-position="left" size="medium" label-width="200px">
                    <div class="sgs-group">
                        <h3>{{$t('contact.contactInfo')}}</h3>
                    </div>
                    <el-row align="right" style="margin: 5px 0px">
                        <el-col :span="12">
                            <el-form-item :label="$t('contact.nameFull')">
                                    <el-input :placeholder="$t('labContact.contactName')" v-model="searchForm.contactName"
                                              autocomplete="off" clearable>
                                        <el-button slot="append" icon="el-icon-search" type="primary" @click="searchLabContactList()"
                                                    v-loading.fullscreen.lock="fullscreenLoading">{{$t('operation.search')}}
                                        </el-button>
                                    </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <!--实验室联系人table-->
                <el-table :data="tableData" v-loading="contactloading" :element-loading-text="$t('loading')"
                          style="width: 100%" @selection-change="selectionChange">
                    <el-table-column
                            type="selection"
                            width="55">
                    </el-table-column>
                    <el-table-column
                            prop="contactName"
                            :label="$t('labContact.contactName')">
                    </el-table-column>
                    <el-table-column
                            prop="contactTel"
                            :label="$t('labContact.contactTel')"
                            width="280">
                    </el-table-column>
                    <el-table-column
                            prop="contactEmail" width="280"
                            :label="$t('labContact.contactEmail')">
                    </el-table-column>
                </el-table>
                <el-pagination
                        @size-change="sizeChange"
                        @current-change="currentChange"
                        :current-page="page.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total">
                </el-pagination>
                <div class="bottom clearfix " style="text-align: center">
                    <el-button size="small" @click="visible = false" :disabled="isDisabled">{{$t('operation.cancel')}}</el-button>
                    <el-button size="small"  type="primary" @click="submitLabContactForm" :disabled="isDisabled">
                        {{$t('operation.confirm')}}
                    </el-button>
                </div>
            </el-card>
        </el-drawer>
    </el-main>
</template>


<script>
    import {
        getPage,
        getList,
        getTestPackage
    } from "@/api/lab/test-package";
    import {getLabList, getLabContactListPage} from "@/api/lab/lab";
    import {mapGetters} from "vuex";

    export default {
        name: "lab-contact-dialog",
        props: ['visible','productLineCode','customerGroupCode','labCode','labType'],
        data() {
            return {
                labData: [],
                contactloading: false,
                searchForm: {
                    sgsToken:'',
                   // labId: '',
                    labCode:'',
                    contactName: '',
                    labType:'',
                },
                contactForm: {
                    labId: '',
                },
                labForm: {
                    labName: '',
                },
                tableData: [],
                form: {},
                selectionList: [],
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                data: [],
                searchFormRules:{
                }
            };
        },
        computed: {
            ...mapGetters(["userInfo","permission","token"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['nkop:user:add'], false),
                    viewBtn: this.vaildData(this.permission['nkop:user:view'], false),
                    delBtn: this.vaildData(this.permission['nkop:user:delete'], false),
                    editBtn: this.vaildData(this.permission['nkop:user:edit'], false)
                };
            },
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            },
        },
        watch: {
            'labCode':{
                handler(){   //注意此处就是handler
                    this.searchLabContactList();
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },

            'labType': function (newVal) {
                //获取路由参数
            },
        },

        created() {
            // this.searchForm.contactName = '';
            // this.searchLabContactList();
        },
        methods: {
            dataFilter(val) {
                this.labForm.labName = val;
                this.selectLab();
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            submitLabContactForm() {
                this.visible = false;
                this.selectionList.forEach(contact => {
                    contact.labCode = this.labCode;
                    contact.labType = this.labType;
                    contact.isDefault = 0;
                });
                this.$emit('labContact', this.selectionList);
                //20-05-06逻辑修改 实验室联系人非必填，选择实验室即可
                /*if(this.selectionList.length==0){//将实验室数据放入
                    var lab={};
                    lab['labId']=this.labForm.labId;
                    lab['labCode'] = this.labForm.labCode;
                    lab['labName'] = this.labForm.labName;
                    lab['labType'] = this.labForm.labType;
                    lab['labAddress'] = this.labForm.labAddress;
                    this.$emit('labContact', [lab]);
                }else{
                    this.selectionList.forEach(contact => {
                        contact.labId = this.labForm.labId;
                        contact.labCode = this.labForm.labCode;
                        contact.labName = this.labForm.labName;
                        contact.labType = this.labForm.labType;
                        contact.labAddress = this.labForm.labAddress;
                    });
                    this.$emit('labContact', this.selectionList);
                }*/
            },
            selectLabChange(val) {
                this.searchForm.contactName = '';
                let obj = {};
                obj = this.labData.find((item)=>{
                    return item.labCode === val;
                });
                if(obj!=null && obj!=undefined){
                    this.labForm.labName = obj.labName;
                    this.labForm.labCode = obj.labCode;
                    this.labForm.labAddress = obj.labAddress;
                    this.labForm.labType = obj.labType;
                    this.labForm.labId = obj.id;
                    this.searchForm.labType=obj.labType;
                }
                //根据实验室id查询实验室联系人
                this.searchLabContactList();
            },
            //请求实验室联系人数据
            searchLabContactList() {
                var params = {};
                this.contactloading = true;
                this.searchForm.sgsToken=this.token;
                this.searchForm.labCode=this.labCode;
                var new_labType=this.labType;
                if(new_labType==null || new_labType==undefined || new_labType==''){
                    new_labType='1';
                }
                this.searchForm.labType = new_labType;
                getLabContactListPage(this.page.currentPage, this.page.pageSize, Object.assign(params, this.searchForm)).then(res => {
                    const data = res.data.data;
                    this.contactloading = false;
                    this.page.total = data.total;
                    this.tableData = data.records;
                }, error => {
                    this.contactloading = false;
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
            labNamechange(value) {
                //清空已查询数据
                this.labData = [];
                this.searchForm.labId = '';
                //模糊查询实验室
                this.selectLab();
            },
            selectLab() {
                var params = {"labName":this.labForm.labName,"productLineCode":this.productLineCode,"customerGroupCode":this.customerGroupCode};
                getLabList(Object.assign(params)).then(res => {
                    const data = res.data.data;
                    this.labData = data;
                });
            },
            selectionChange(list) {
                this.selectionList = list;
            },
            beforeOpen(done, type) {
                if (["edit", "view"].includes(type)) {
                    getTestPackage(this.form.id).then(res => {
                        this.form = res.data.data;
                    });
                }
                done();
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.searchLabContactList();
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            close() {
                this.visible = false;
                this.$emit('update:visible', this.visible);
            }
        }
    };
</script>

<style>
</style>
