import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/add',
        method: 'post',
        data: form
    })
}

export const updateAddrStatus = (form) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/updateAddrStatus',
        method: 'post',
        data: form
    })
}
export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const setDefault = (id,isDefault) => {
    return request({
        url: '/api/sgs-mart/customer/addresses/set-default',
        method: 'get',
        params: {
            id,
            isDefault
        }
    })
}

