import request from '@/router/axios';
import qs from 'qs'

export const searchCustomer = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/customers',
        method: 'post',
        data: form
    })
}

export const searchCustomerAndGroup = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/customers',
        method: 'get',
        params: form
    })
}
export const searchCustomerContact = (form) => {
    return request({
        url: '/api/sgs-mart/sgs-api/customer/contacts',
        method: 'post',
        data: form
    })
}

export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/submit',
        method: 'post',
        data: form
    })
}


export const updateById = (form) => {
    return request({
        url: '/api/sgs-mart/customer/update',
        method: 'post',
        data: form
    })
}
export const queryCustomerByBossNoexport = (bossNo) => {
    return request({
        url: '/api/sgs-mart/customer/update',
        method: 'post',
        data: bossNo
    })
}
export const updateUserLanguage = (language) => {
    return request({
        url: '/api/sgs-mart/userSetting/updateUserLanguage',
        method: 'get',
        params: {
            language,
        }
    })
}

//根据customerId查询Customer信息
export const queryCustomerForId = (id) => {
    return request({
        url: '/api/sgs-mart/customer/detail-for-id',
        method: 'get',
        params: {
            id,
        }
    })
}
export const queryUserLanguage = () => {
    return request({
        url: '/api/sgs-mart/userSetting/queryUserLanguage',
        method: 'get'
    })
}

//获取已授权的用户列表(授权方)
export const proxyCustomers = (params) => {
    return request({
        url: '/api/sgs-mart/customer/proxy/list-customer-auth',
        method: 'post',
        data: params
    })
}
//获取已授权的用户列表(授权方) 无分页
export const proxyCustomersNoPage = (params) => {
    return request({
        url: '/api/sgs-mart/customer/proxy/list-customer-auth-noPage',
        method: 'post',
        data: params
    })
}

//授权校验接口
export const validateAuth = (params) => {
    console.log("授权接口参数", params);
    return request({
        url: '/api/sgs-mart/customer/proxy/validateAuth',
        method: 'post',
        data: params
    });
}

export const queryCustomerByBuCodeAndCustomerId = (params)=>{
    let remoteUrl = '/api/sgsapi/CustomerApi/preOrderCustomer/detail/query';
    return request.post(remoteUrl,qs.stringify(params))
}

