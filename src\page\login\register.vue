<template>
  <div class="register-containerHome" style="position: relative;">
    <el-form :model="accountApplyForm" ref="accountApplyForm" :rules="applyRules" label-width="0px">
      <!-- 使用邮箱 -->
      <div v-if="accountApplyForm.applyFlag == 0">
        <el-row>
          <el-col :span="24">
            <el-form-item prop="email">
              <el-input @input="changeAccount" v-model="accountApplyForm.email" type="email" maxlength="150" :placeholder="$t('register.emailBlur')">
                <i slot="prefix"><img src="/img/icon/mobile.png" style="margin-top: 6px;" /></i>
                <template #suffix>
                  <el-button type="text" @click="registTypeChange(1)" style="margin-top: 0px;">
                    {{ $t('register.phoneRegister') }}
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="emailCaptcha" :error="errorMsg">
              <el-input v-model="accountApplyForm.emailCaptcha" maxlength="10" :placeholder="$t('login.code')">
                <i slot="prefix"><img src="/img/icon/code.png" style="margin-top:7px;" /></i>
                <template #suffix>
                  <img :src="imageCodeData" :title="$t('register.pictureCaptcha.clickChange')"
                    style="width: 90px;height: 39px;margin-right: -4px; padding-bottom: 4px;" @click="getImageCode">
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 使用手机 -->
      <div v-if="accountApplyForm.applyFlag == 1">
        <el-row>
          <el-col :span="24">
            <el-form-item prop="applyContactMobile">
              <el-input @input="changeAccount" v-model="accountApplyForm.applyContactMobile" maxlength="11" :placeholder="$t('login.phone')">
                <i slot="prefix"><img src="/img/icon/mobile.png" style="margin-top: 6px;" /></i>
                <template #suffix>
                  <el-button type="text" @click="registTypeChange(0)" style="margin-top: 0px;">
                    {{ $t('register.emailRegister') }}
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item prop="checkCode" :error="codeErrorMsg">
              <el-input maxlength="6" v-model="accountApplyForm.checkCode" :placeholder="$t('login.code')">
                <i slot="prefix"><img src="/img/icon/code.png" style="margin-top: 6px;" /></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="1" class="text-right">
            <el-button :disabled="applyShowSendSmsBtn" @click="applySendSMSCode" type="primary" plain>
              <span v-show="applyShowCounter">{{ $t('sms.sendCode') }}</span>
              <span v-show="!applyShowCounter" class="count">{{ applyCounter }} s</span>
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-row style="text-align: center;">
        <el-button
          type="primary"
          @click="onSubmitApply"
          class="login-submit"
          :disabled="applySubmitDisabled" :loading="applySubmitLoading"
          style="width: 100%; margin-top: -10px;">
            {{ $t('register.continue') }}
        </el-button>
      </el-row>
      <div class="checkbox">
        <el-checkbox v-model="isArgee"
                     @change="agreeCondition"
                     style="padding-left: 0;color: #555555">
        </el-checkbox>
        <span style="padding-left: 5px;color:#555555;">{{ $t('login.readAgree') }}</span>
        <span>
          <a @click="openPolicy(1,true)" style="cursor: pointer;color: #FF6600;border-bottom: 1px solid #FF6600">
            {{ $t('login.serviceTrems') }}
          </a>
          {{ $t('login.serviceTremsEnd') }}
        </span>
      </div>
      <div class="checkbox">
        <el-checkbox v-model="agreeMarketing"
                     style="padding-left: 0;color: #555555">
        </el-checkbox>
        <span style="padding-left: 5px;color:#555555;">
          {{ $t('login.readMarketing') }}
           <a @click="openPolicy(2,true)" style="cursor: pointer;color: #FF6600;border-bottom: 1px solid #FF6600 ">
            {{ $t('agreement.privacyPolicy') }}
          </a>
        </span>
        <span>
          {{ $t('login.readMarketingAfter') }}
        </span>

      </div>
    </el-form>

    <el-dialog
               :visible.sync="policyDia"
               width="70%"
               top="5vh"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               append-to-body
               show-close
               @close="beforePolicyDiaClose">
      <policy
        v-if="policyDia"
        @agree="agreePolicy"
        :tabs-index="tabsIndex"
        :just-open-policy="justOpenPolicy"
      ></policy>
    </el-dialog>


    <el-dialog style="position: absolute; margin: 0; overflow: hidden;" :close-on-click-modal="false"
      :visible="emailApplyDialog" @close="handleClose" width="100%" top="0px" :show-close="true">
      <div v-if="false" slot="title">标题</div>
      <div class="text-center">
        <span class="el-icon-message" style="color: #4385F4; font-size: 50px;"></span>
        <div class="msg-content">
          {{ $t('register.message.emailSendTip') }} <span style="color: #ff6600">{{ accountApplyForm.email }}</span> <br>
          {{ $t('register.message.emailSendMessage') }}
        </div>
        <div slot="footer" class="dialog-footer" style="top: 5px;">
          <el-button @click="handleIKnow" class="black-btn">{{ $t('register.iGotIt') }}({{ iKnow }}s)</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="General Conditions of Service" :visible="clauseVisibleEn" width="70%" top="10vh"
      :show-close="false">
      <div class="modal-body">
        <img src="/img/logo.png" style="width: 200px" />
        <h3 style="font-weight: 600">TERMS AND CONDITIONS OF USE</h3>
        <br />
        <br />
        <p><a target="_blank" style="color: #ff6600"
            href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
        </p>
        <p style="word-wrap:break-word">
          SGS Supplier Chain Knowledge Sharing services include a compilation(s) of data which are mainly
          based on the sources publicly available at the relevant time for which SGS bears no responsibility.
          Unless specifically advised in advance, the compilation of data is a generic analysis which does not
          cover specific product(s). SGS may have extracted from the compiled data specific criteria which are
          not intended to be substitute for the relevant legislation and/or standards.
          Unless agreed otherwise in writing, SGS's services and liability are governed by the general
          Conditions of Service <a style="color: #ff6600" target="_blank"
            href="http://www.sgs.com/terms_and_conditions.htm">http://www.sgs.com/terms_and_conditions.htm</a>
          . All material developed for the clients including the relevant systematic and methodology have been
          developed by the company and are copyrighted by it. Clients shall not copy reproduce or disclose to
          third parties the data as provided by the company without its prior written consent. All information
          provided by SGS is for strategic and informational purposes only, and should not be construed as
          company-specific legal compliance advice or counsel. SGS makes no representation whatsoever
          about the suitability of the information and services contained herein for resolving any question of
          law. SGS does not provide legal services.
        </p>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
        <el-button type="primary" @click="addAccount">Agree and continue</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible="clauseVisible" width="70%" top="10vh" :show-close="false">
      <div class="modal-body second el-dialog-div">
        <img src="/img/logo.png" style="width: 200px" />
        <!-- <div class="right">
                        <top-lang ref="languageRef" :loadMenu="false" :is-load-lanage-by-ip="false"></top-lang>
                    </div>-->
        <h3 style="font-weight: 600">{{ $t('agreement.useConditions') }}</h3>
        <br />
        <br />

        <!--<p><a target="_blank" style="color: #ff6600" href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
                </p>-->
        {{ $t('agreement.head') }}
        <h3> {{ $t('agreement.definition') }}</h3>
        <p> {{ $t('agreement.one_1') }}</p>
        <p> {{ $t('agreement.one_2') }}</p>
        <h3> {{ $t('agreement.accountRegisterAndUse_2') }}</h3>
        <p> {{ $t('agreement.two_1') }}</p>
        <p> {{ $t('agreement.two_2') }}</p>
        <p> {{ $t('agreement.two_3') }}</p>
        <p> {{ $t('agreement.two_4') }}</p>
        <p> {{ $t('agreement.two_5') }}</p>
        <p> {{ $t('agreement.two_6') }}</p>
        <h3> {{ $t('agreement.userInfoProtection_3') }}</h3>
        <p> {{ $t('agreement.three_1') }}</p>
        <p> {{ $t('agreement.three_2') }}</p>
        <p> {{ $t('agreement.three_3') }}</p>
        <h3> {{ $t('agreement.rightsAndObligations_4') }}</h3>
        <p> {{ $t('agreement.four_1') }}</p>
        <p> {{ $t('agreement.four_2') }}</p>
        <p> {{ $t('agreement.four_3') }}</p>
        <p> {{ $t('agreement.four_4') }}</p>
        <p> {{ $t('agreement.four_5') }}</p>
        <p> {{ $t('agreement.four_6') }}</p>
        <p> {{ $t('agreement.four_6_1') }}</p>
        <p> {{ $t('agreement.four_6_2') }}</p>
        <p> {{ $t('agreement.four_6_3') }}</p>
        <p> {{ $t('agreement.four_6_4') }}</p>
        <p> {{ $t('agreement.four_6_5') }}</p>
        <p> {{ $t('agreement.four_6_6') }}</p>
        <p> {{ $t('agreement.four_6_7') }}</p>
        <p> {{ $t('agreement.four_6_8') }}</p>
        <p> {{ $t('agreement.four_6_9') }}</p>
        <p> {{ $t('agreement.four_7') }}</p>
        <p> {{ $t('agreement.four_7_1') }}</p>
        <p> {{ $t('agreement.four_7_2') }}</p>
        <p> {{ $t('agreement.four_7_3') }}</p>
        <p> {{ $t('agreement.four_7_4') }}</p>
        <p> {{ $t('agreement.four_7_5') }}</p>
        <p> {{ $t('agreement.four_8') }}</p>
        <p> {{ $t('agreement.four_8_1') }}</p>
        <p> {{ $t('agreement.four_8_2') }}</p>
        <p> {{ $t('agreement.four_8_3') }}</p>
        <p> {{ $t('agreement.four_8_4') }}</p>
        <p> {{ $t('agreement.four_8_5') }}</p>
        <p> {{ $t('agreement.four_8_6') }}</p>
        <p> {{ $t('agreement.four_8_7') }}</p>
        <p> {{ $t('agreement.four_8_8') }}</p>
        <p> {{ $t('agreement.four_9') }}</p>
        <p> {{ $t('agreement.four_10') }}</p>
        <p> {{ $t('agreement.sgsRightsAndDuties_5') }}</p>
        <p> {{ $t('agreement.five_1') }}</p>
        <p> {{ $t('agreement.five_2') }}</p>
        <p> {{ $t('agreement.five_3') }}</p>
        <p> {{ $t('agreement.five_4') }}</p>
        <p> {{ $t('agreement.five_5') }}</p>
        <p> {{ $t('agreement.intellectualProperty_6') }}</p>
        <p> {{ $t('agreement.six_1') }}</p>
        <p> {{ $t('agreement.six_2') }}</p>
        <p> {{ $t('agreement.six_3') }}</p>
        <p> {{ $t('agreement.six_4') }}</p>
        <p> {{ $t('agreement.privacy_7') }}</p>
        <p> {{ $t('agreement.seven_1') }}</p>
        <p> {{ $t('agreement.seven_2') }}</p>
        <p> {{ $t('agreement.seven_3') }}</p>
        <p> {{ $t('agreement.seven_3_1') }}</p>
        <p> {{ $t('agreement.seven_3_2') }}</p>
        <p> {{ $t('agreement.seven_3_3') }}</p>
        <p> {{ $t('agreement.seven_3_4') }}</p>
        <p> {{ $t('agreement.seven_4') }}</p>
        <p> {{ $t('agreement.seven_5') }}</p>
        <p> {{ $t('agreement.legalResponsibility_8') }}</p>
        <p> {{ $t('agreement.eight_1') }}</p>
        <p> {{ $t('agreement.eight_2') }}</p>
        <p> {{ $t('agreement.eight_3') }}</p>
        <p> {{ $t('agreement.eight_4') }}</p>
        <p> {{ $t('agreement.eight_5') }}</p>
        <p> {{ $t('agreement.eight_6') }}</p>
        <p> {{ $t('agreement.otherAgreements_9') }}</p>
        <p> {{ $t('agreement.nine_1') }}</p>
        <p> {{ $t('agreement.nine_2') }}</p>
        <p> {{ $t('agreement.nine_3') }}</p>
        <p> {{ $t('agreement.nine_4') }}</p>
        <p> {{ $t('agreement.nine_5') }}</p>
        <p> {{ $t('agreement.nine_6') }}</p>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
        <el-button type="primary" @click="addAccount">{{ $t('agreement.agreeAndContinue') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import {
  add,
  addRegister,
  addAccount,
  checkCustomer,
  checkAccount,
  sendVerificationCode,
  registerDetail,
  checkSmsCode,
  validateAccount,
  getImageCode,
  checkImageCode
} from "@/api/customer/customerRegister";
import { searchCustomer } from "@/api/customer/customer";
import { getCloudFileURL, getSgsCustomer, getTianyanchaCustomer, serviceTypeList } from "@/api/common/index";
import { validatenull, validateEmail } from "@/util/validate";
import { mapGetters } from "vuex";
import TopLang from "../index/top/top-lang";
import policy from "./policy";

const TIME_COUNT = 60; //更改倒计时时间
export function isvalidPhone(str) {
  const reg = /^1[********]\d{9}$/;
  return reg.test(str)
}

export default {
  components: {
    // PrivacyPolicyDialog: resolve => require(['../../components/dialog/privacy-policy-dialog'], resolve),
    TopLang,policy
  },
  data() {
    var validPhone = (rule, value, callback) => {
      if (!value) {
        callback(this.$t('register.phoneBlur'))
      } else if (!isvalidPhone(value)) {
        callback(this.$t('register.telePhoneBlur'))
      } else {
        checkAccount({ telephone: this.accountApplyForm.applyContactMobile }).then(res => {
          //判断显示客户需要填写的信息
          if (res.data.success === 1 || res.data.success === '1') {
            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
              callback(new Error(this.$t('register.submitPhoneInfo')));
            } else {
              callback(new Error(this.$t('register.lockError')));
            }
          } else {
            callback();
          }
        });
      }
    }
    var validCheckCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.pleaseInputCode')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.pleaseInputCode')));
      } else {
        callback();
      }
    };
    var validateUserName = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.accountBlur')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.accountBlur')));
      } else {
        callback();
      }
    };
    var validateCustomer = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.companyNameBlur')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.companyNameBlur')));
      } else {
        callback();
      }
    };

    var validateProductLineCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.serveTypeBlur')));
      } else {
        callback();
      }
    };
    var validateAddress = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.addressBlur')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.addressBlur')));
      } else {
        callback();
      }
    };

    var validateEmailMethod = (rule, value, callback) => {
      debugger;
      if (!value) {
        callback(new Error(this.$t('register.emailBlur')));
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('register.emailRigthBlur')));
      } else {
        callback();
      }
    };
    var validateEmailCaptcha = (rule, value, callback) => {
      debugger;
      if (!value) {
        callback(new Error(this.$t('register.pictureCaptcha.validate')));
      } else {
        callback();
      }
    };

    var validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.passwordBlur')));
      } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/.test(value))) {
        callback(new Error(this.$t('register.passwordError')));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.doublePasswordBlur')));
      } else if (value !== this.accountApplyForm.password) {
        callback(new Error(this.$t('register.checkPassNo')));
      } else {
        callback();
      }
    };
    var validateAccount = (rule, value, callback) => {
      if (!validatenull(value)) {
        checkAccount({ email: this.accountApplyForm.email }).then(res => {
          //判断显示客户需要填写的信息
          if (res.data.success === 1 || res.data.success === '1') {
            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
              callback(new Error(this.$t('register.submitInfo')));
            } else {
              callback(new Error(this.$t('register.lockError')));
            }
          } else {
            callback();
          }
        });
      }
    };
    var validateAccount1 = (rule, value, callback) => {
      debugger;
      if (!value) {
        callback(new Error(this.$t('register.emailBlur')));
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('register.emailRigthBlur')));
      } else {
        if (!validatenull(this.customerId)) {
          callback();
        }
        checkAccount({ email: this.form.email }).then(res => {
          //判断显示客户需要填写的信息
          if (res.data.success === 1 || res.data.success === '1') {
            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
              callback(new Error(this.$t('register.submitInfo')));
            } else {
              callback(new Error(this.$t('register.lockError')));
            }
          } else {
            callback();
          }
        });
      }
    };
    return {
      policyDia:false,
      justOpenPolicy:false,
      tabsIndex:1,
      isArgee:false,
      agreeMarketing:false,
      iKnow: 4,
      customerId: '',
      taxDisabledFlag: false,
      errorMsg: null,
      imageCodeData: '',
      serviceTypeData: [],
      bossNoDisabled: false,
      registerMode: 1,
      emailApplyDialog: false,
      hash: '',
      applySubmitDisabled: false,
      applySubmitLoading: false,
      applyShowSendSmsBtn: false,
      applyShowCounter: true,
      applyCounter: 60,
      applyTimer: null,
      accountApplyForm: {
        emailCaptcha: '',
        applyFlag: 0,
        password: '',
        doublePassword: '',
        language: this.language,
      },
      active: 1,
      codeErrorMsg: null,
      name: "customerRegister",
      loginAccount: '',
      emailDisabled: false,
      contactMobileDisabled: false,
      accountId: '',
      form: {
        qualificationUrl: '',
        taxNo: '',
        bossNo: '',
        language: this.language,
        productLineCode: '',
      },
      submitDisabled: false,
      registerSubmitLoading: false,
      showSendSmsBtn: false,
      isShow: false,
      showCounter: true,
      clauseVisible: false,
      clauseVisibleEn: false,
      counter: 60,
      timer: null,
      customerData: {},
      rules: {
        userName: [
          { required: true, trigger: 'blur', validator: validateUserName }
        ],
        customerAddressZh: [
          { required: true, trigger: 'blur', validator: validateAddress }
        ],
        contactMobile: [
          { required: true, trigger: 'blur', validator: validPhone }//这里需要用到全局变量
        ],
        email: [
          { required: true, validator: validateAccount1, trigger: 'blur' }
        ],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        doublePassword: [{ required: true, validator: validatePass2, trigger: 'blur' }],
        customerName: [
          { required: true, trigger: 'blur', validator: validateCustomer }
        ],
        productLineCode: [
          { required: true, trigger: 'change', validator: validateProductLineCode }
        ]
      },
      applyRules: {
        applyContactMobile: [
          /* { trigger: 'blur', message: this.$t('register.telePhoneBlur')},*///这里需要用到全局变量
          { required: true, validator: validPhone, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, validator: validCheckCode, trigger: 'blur' },
        ],
        email: [
          { required: true, validator: validateEmailMethod, trigger: 'blur' },
        ],
        emailCaptcha: [
          { required: true, validator: validateEmailCaptcha, trigger: 'blur' },
        ],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        doublePassword: [{ required: true, validator: validatePass2, trigger: 'blur' }],
      }
    }
  },
  computed: {
    ...mapGetters(["language", 'tag']),
  },
  watch: {
    'hash': function (newVal) {
      //获取路由参数
      this.getParams();
    },
    'language': function () {
      this.queryServiceType();
    },
    "$i18n.locale": function () {
      debugger;
      if (this.$refs['accountApplyForm'] != undefined) {
        this.$refs['accountApplyForm'].fields.forEach(item => {
          if (item.validateState === 'error') {
            this.$refs['accountApplyForm'].validateField(item.labelFor)
          }
        })
      }
      if (this.$refs['registerForm'] != undefined) {
        this.$refs['registerForm'].fields.forEach(item => {
          if (item.validateState === 'error') {
            this.$refs['registerForm'].validateField(item.labelFor)
          }
        })
      }
    },
    "accountApplyForm.applyFlag": function () {
      this.$refs['accountApplyForm'].clearValidate();
    }
  },
  mounted() {
    //获取路由参数
    this.getParams();

    //获取服务类别
    this.queryServiceType();
    if (this.active == 1) {
      this.getImageCode();
    }
  },
  methods: {
    changeAccount(){
      this.isArgee = false;
    },
    beforePolicyDiaClose(){
      //console.log("before dia close")
      this.applySubmitDisabled = false;
      this.applySubmitLoading = false;
      this.getImageCode()
    },
    openPolicy(tabsIndex,justOpenPolicy){
      this.justOpenPolicy=justOpenPolicy;
      this.tabsIndex = tabsIndex;
      this.policyDia = true;
    },
    agreeCondition(){
      if(this.isArgee){
        this.openPolicy(1,false);
        this.$nextTick(()=>{
          this.$set(this,'isArgee',false)
        })
      }
    },
    closePolicyDia(){
      this.clauseVisibleEn = false
      this.clauseVisible = false
      this.applySubmitDisabled =false;
      this.applySubmitLoading = false;
      this.policyDia = false;
    },
    agreePolicy(flag){
      this.closePolicyDia();
      if(this.justOpenPolicy){
        return;
      }
      this.isArgee = flag;
      if(!flag){
        return;
      }
      //存在注册信息再继续走注册逻辑
      if(this.accountApplyForm.applyFlag==0){
        let {email,emailCaptcha} = this.accountApplyForm;
        if(!email || !emailCaptcha){
          return;
        }
      }
      if(this.accountApplyForm.applyFlag==1){
        let {applyContactMobile,checkCode} = this.accountApplyForm;
        if(!applyContactMobile || !checkCode){
          return;
        }
      }
      this.addAccount();
    },
    handleIKnow() {
      this.emailApplyDialog = false
      this.applySubmitDisabled = false
      this.applySubmitLoading = false
    },
    handleClose() {
      this.iKnow = 4
      this.getImageCode()
      this.accountApplyForm.emailCaptcha = ''
    },
    registTypeChange(val) {
      if (val == 1) {//手机号注册
        this.$set(this.accountApplyForm, 'email', '');
        this.accountApplyForm.applyFlag = 1
        // this.getImageCode();
      } else {//邮箱注册
        this.$set(this.accountApplyForm, 'applyContactMobile', '');
        this.accountApplyForm.applyFlag = 0
        this.getImageCode();
      }
    },
    /**
     * 获取图片验证码
     */
    getImageCode() {
      getImageCode().then(resp => {
        this.imageCodeData = 'data:image/png;base64,' + resp.data;
      })
    },
    /**
     * 检查图片验证码
     */
    checkImageCode() {
      return checkImageCode({ code: this.accountApplyForm.emailCaptcha }).then(resp => {
        this.authCodeBtnLock = !resp.data.success
        this.authCodeBtnLock && this.$message.warning(resp.msg || '图片验证码错误')
        return this.authCodeBtnLock
      }).catch(() => {
        this.authCodeBtnLock = true
        return this.authCodeBtnLock
      })
    },
    selectServiceTypeChange(val) {
      let obj = {};
      obj = this.serviceTypeData.find((item) => {
        return item.productLineCode === val;
      });
    },
    queryServiceType() {
      serviceTypeList(this.language).then(res => {
        const data = res.data.data;
        this.serviceTypeData = data;
      });
    },
    getParams() {
      const active = this.$route.query.active;
      const applyFlag = this.$route.query.applyFlag;
      const phone = this.$route.query.phone;
      const email = this.$route.query.email;
      const customerId = this.$route.query.id;
      this.customerId = customerId;
      var language = this.$route.query.language;
      //获取中英文
      // debugger;
      if (!validatenull(language)) {
        this.$i18n.locale = language;
        this.$store.commit("SET_LANGUAGE", language);
      }
      // this.$refs.languageRef.changeLanguage(this.language);
      // if (this.language == 'en-US') {
      //   this.clauseVisibleEn = true;
      // } else {
      //   this.clauseVisible = true;
      // }

      let tag = this.tag;
      let title = this.$router.$avueRouter.generateTitle(
        tag.label,
        (tag.meta || {}).i18n
      );

      if (active == '' || active == undefined || active == null) {
        this.active = 1;
      } else if (active == 2) {
        this.active = active;
        //判断是否有ID
        if (!validatenull(customerId)) {//存在ID  标识为用户重新修改注册信息
          this.loadAccountInfo(customerId, email);
        }
        if (applyFlag == 0) {//邮箱
          this.registerMode = 1;
          this.loginAccount = email;
          this.form.email = email;
          this.emailDisabled = true;
        } else {//手机注册
          this.registerMode = 2;
          this.loginAccount = phone;
          this.form.contactMobile = phone;
          this.contactMobileDisabled = true;
        }

      }
    },
    loadAccountInfo(customerId, email) {
      //查询customer & Account数据
      registerDetail(customerId).then(res => {
        const customer = res.data.data.customer;
        if (customer.approveStatus != 80) {
          this.$router.push({ path: '/login' })
        }
        const account = customer.account;
        /*if(account.email!=email){
            this.$router.push({ path: '/login'})
        }*/
        this.$set(this.form, 'customerName', customer.customerNameEn);
        this.$set(this.form, 'email', account.email);
        this.$set(this.form, 'userName', account.userName);
        this.$set(this.form, 'contactMobile', account.mobile);
        this.$set(this.form, 'taxNo', customer.taxNo);
        this.$set(this.form, 'customerAddressZh', customer.customerAddressZh);
        this.$set(this.form, 'bossNo', customer.bossNo);
        this.$set(this.form, 'reportNo', customer.reportNo);
        this.accountId = account.id;
        this.loginAccount = account.account;
        this.registerMode = account.registerMode;
        this.isShow = true;
        if (!validatenull(customer.qualification)) {
          if (!validatenull(customer.qualification.attachmentId)) {
            getCloudFileURL(customer.qualification.attachmentId).then(res => {
              this.$set(this.form, 'qualificationUrl', res.data);
            });
          }
        }
        this.$set(this.form, 'id', customer.id);
      });
    },
    async onSubmitApply() {
      this.accountApplyForm.language = this.language;
      //如果注册方式为邮箱注册 则需校验图片验证码
      if (this.accountApplyForm.applyFlag == 0) {
        //console.log('检查验证码是否有效')
        let res = await checkImageCode(this.accountApplyForm.emailCaptcha);
        if (!res.data.data.success) {
          this.errorMsg = null;
          this.$nextTick(() => {
            if (res.data.msg == '4001') {//失效
              this.errorMsg = this.$t('register.pictureCaptcha.captchaInvalid');
            } else if (res.data.msg == '4003') {//超时
              this.errorMsg = this.$t('register.pictureCaptcha.captchaOvertime');
            } else {
              this.errorMsg = this.$t('register.pictureCaptcha.captchaError');
            }
          })
          return false;
        }
      }
      this.$refs['accountApplyForm'].validate((valid) => {
        if (valid) {
          //console.log('验证通过', this.accountApplyForm)
          this.applySubmitDisabled = true;
          this.applySubmitLoading = true;
          if(this.isArgee){
            //继续走注册逻辑
            this.addAccount();
          }else{
            this.openPolicy(1,false);
          }

        } else {
          this.submitDisabled = false;
          this.registerSubmitLoading = false;
          console.log('error submit!!');
          return false;
        }
      });
    },
    addAccount() {
      let param = Object.assign({},this.accountApplyForm,{
        receiveMarketingCommunication:this.agreeMarketing?1:0,
        tcpp:this.isArgee?1:0
      })
      addAccount(param).then(res => {
        if (this.accountApplyForm.applyFlag == 0) {//邮箱
          //弹框提示
          this.emailApplyDialog = true;
          let that = this
          let timer = setInterval(function() {
            if(that.iKnow <= 1) {
              that.emailApplyDialog = false
              that.applySubmitDisabled = false
              that.applySubmitLoading = false
              clearInterval(timer)
            }
            if(that.iKnow > 1 && that.iKnow <= 4) {
              that.iKnow--
            }
          }, 1000)
        } else { // 手机注册
          //路由重新跳转登录页
          let hashVal = new Date().getTime()
          this.$router.push({
            path: '/simpleRegister',
            query: {
              active: 2,
              applyFlag: this.accountApplyForm.applyFlag,
              phone: this.accountApplyForm.applyContactMobile,
              email: this.accountApplyForm.email,
              hash: hashVal
            }
          })
          this.hash = hashVal;
        }
      }, error => {
        this.applySubmitDisabled = false;
        this.applySubmitLoading = false;
        console.log(error);
      });
    },
    async onSubmit() {
      //验证该账号是否在SGSmart中注册过
      let validateRes = await validateAccount(this.loginAccount);
      //console.log(validateRes);
      if (!validateRes.data.data) {//该用户已注册
        this.$message({
          type: "error",
          message: this.$t('register.registerError')
        });
        return false;
      }
      //判断是否录入手机号，手机号存在的话，需要再次对手机号进行校验
      if (this.registerMode == 1) {//邮箱注册
        if (!validatenull(this.form.contactMobile)) {
          let validateMobileRes = await validateAccount(this.form.contactMobile);
          if (!validateRes.data.data) {//该用户已注册
            this.$message({
              type: "error",
              message: this.$t('register.registerError')
            });
            return false;
          }
        }
      }
      this.submitDisabled = true;
      this.registerSubmitLoading = true;
      this.$refs['registerForm'].validate((valid) => {
        if (valid) {
          this.submitDisabled = true;
          this.registerSubmitLoading = true;
          //验证短信验证码是否过期
          //checkSmsCode(this.form.contactMobile, this.form.checkCode).then(res => {
          //  if (res.data.data) {//验证通过
          const register = {};
          register.customer = this.form;
          register.checkCode = this.form.checkCode;
          register.customer.customerNameZh = this.form.customerName;
          register.customer.customerNameEn = this.form.customerName;
          register.customer.productLineCode = this.form.productLineCode;
          register.customer.qualification = this.form.qualification;
          register.customer.customerAddressZh = this.form.customerAddressZh;
          register.customer.account = {
            id: this.accountId,
            email: this.form.email,
            userName: this.form.userName,
            password: this.form.password,
            mobile: this.form.contactMobile,
            account: this.loginAccount,
            language: this.language,
            registerMode: this.registerMode
          };
          addRegister(register).then(() => {
            this.submitDisabled = false;
            this.registerSubmitLoading = false;
            this.$message({
              type: "success",
              message: this.$t('api.success')
            });
            //判断是否有税号
            if (validatenull(register.customer.taxNo)) {
              this.$router.push('/simpleRegister/success');
            } else {
              this.$router.push('/simpleRegister/successInfo');
            }
          }, error => {
            this.submitDisabled = false;
            this.registerSubmitLoading = false;
            console.log(error);
          });
          /*} else {
              this.submitDisabled = false;
              this.registerSubmitLoading = false;
              this.$nextTick(() => {this.codeErrorMsg=this.$t('sms.smsCodeValidateError');})
              this.$message({
                  type: "error",
                  message: this.$t('sms.smsCodeValidateError')
              });
          }*/
          //});
        } else {
          this.submitDisabled = false;
          this.registerSubmitLoading = false;
          console.log('error submit!!');
          return false;
        }
      });
    },
    checkExists() {
      if (this.form.customerName != '' && this.form.customerName != undefined && this.form.customerName.length >= 1) {
        checkCustomer(this.form.customerName).then(res => {
          //判断显示客户需要填写的信息
          if (!validatenull(res.data.data)) {
            this.customerData = res.data.result;
            //this.form.bossNo=this.customerData.number;
            this.isShow = false;
          } else {
            this.isShow = true;
            this.form.id = null;
          }
        });
      }
    },
    querySearch(query, callback) {
      this.loading = false;
      if (query != '' && query != undefined && query.length >= 2) {
        this.loading = true;
        getTianyanchaCustomer(query).then(res => {
          const results = [];

          if (!validatenull(res.data)) {
            if (!validatenull(res.data.result)) {
              res.data.result.items.forEach((currentValue, index) => {
                results.push({
                  'value': currentValue.name,
                  "taxNo": currentValue.creditCode,
                });
              });
            }
          }
          this.form.bossNo = '';
          this.form.taxNo = '';
          callback(results);
        })
      }
    },
    clearCustomer() {
      this.taxDisabledFlag = false;
      this.form.taxNo = '';
    },
    handleSelect(item) {
      debugger;
      this.checkExists();
      this.form.taxNo = item.taxNo;
      if (!validatenull(this.form.taxNo)) {
        this.taxDisabledFlag = true;
      } else {
        this.taxDisabledFlag = false;
      }
      this.$refs['taxNo'].focus();
    },
    uploadSuccess(res, file) {
      this.form.qualification = {};
      this.form.qualification.attachmentId = res.data[0].cloudID;
      getCloudFileURL(this.form.qualification.attachmentId).then(res => {
        this.form.qualificationUrl = res.data;
      });
    },
    sendSMSCode() {
      this.showSendSmsBtn = true;
      //验证手机号
      this.$refs.registerForm.validateField("contactMobile", errMsg => {
        if (!errMsg) {//校验通过
          //console.log("手机号校验通过");
          sendVerificationCode(this.form.contactMobile, 1).then(res => {
            this.showCounter = false;//展示时间倒计时
            if (!this.timer) {
              this.counter = TIME_COUNT;
              this.showCounter = false;
              this.timer = setInterval(() => {
                if (this.counter > 0 && this.counter <= TIME_COUNT) {
                  this.counter--;
                } else {
                  this.showSendSmsBtn = false;
                  this.showCounter = true;//时间计数完毕重新展示发送按钮
                  clearInterval(this.timer);  // 清除定时器
                  this.timer = null;
                }
              }, 1000)
            } else {
              this.showCounter = true;
            }
          });
        } else {
          //手机号码验证失败
          this.showSendSmsBtn = false;
        }
      });
    },
    applySendSMSCode() {
      this.applyShowSendSmsBtn = true;
      //验证手机号
      if (!this.accountApplyForm.applyContactMobile) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('register.telePhoneBlur'),
          type: 'warning'
        });
        this.applyShowSendSmsBtn = false;
        return false;
      } else if (!isvalidPhone(this.accountApplyForm.applyContactMobile)) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('register.telePhoneBlur'),
          type: 'warning'
        });
        this.applyShowSendSmsBtn = false;
        return false;
      } else {
        this.$refs['accountApplyForm'].validateField("applyContactMobile", errMsg => {
          //console.log("手机号校验通过");
          //校验是否已注册
          checkAccount({ telephone: this.accountApplyForm.applyContactMobile }).then(res => {
            //判断显示客户需要填写的信息
            if (res.data.success === 1 || res.data.success === '1') {
              if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('register.submitPhoneInfo'),
                  type: 'warning'
                });
              } else {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('register.lockError'),
                  type: 'warning'
                });
              }
              this.applyShowSendSmsBtn = false;
              //setTimeout(this.$router.push('/'),3000);
            } else {
              this.applyShowSendSmsBtn = false;
              sendVerificationCode(this.accountApplyForm.applyContactMobile, 1).then(res => {
                this.applyShowCounter = false;//展示时间倒计时
                if (!this.applyTimer) {
                  this.applyCounter = TIME_COUNT;
                  this.applyShowCounter = false;
                  this.applyShowSendSmsBtn = true;
                  this.applyTimer = setInterval(() => {
                    if (this.applyCounter > 0 && this.applyCounter <= TIME_COUNT) {
                      this.applyCounter--;
                    } else {
                      this.applyShowSendSmsBtn = false;
                      this.applyShowCounter = true;//时间计数完毕重新展示发送按钮
                      this.applyShowSendSmsBtn = false;
                      clearInterval(this.applyTimer);  // 清除定时器
                      this.applyTimer = null;
                    }
                  }, 1000)
                } else {
                  this.applyShowCounter = true;
                }
              });
            }
          });

        });
      }

    },
  }
}
</script>

<style lang="scss">
.register-containerHome {
  padding-top: 20px;
  .el-dialog__header {
    padding: 0 !important;
  }
}
.msg-content {
  font-size: 16px;
  font-weight: 400;
  color: #000000;
  padding: 20px 0 26px;
  line-height: 24px;
}
@media screen and (max-width:1366px) {
  .login-submit {
    margin-bottom: 10px;
  }
}

</style>
