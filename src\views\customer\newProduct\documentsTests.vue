<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_product_document" id="sgs_smart_product_document">
            <el-row>
                <el-col :span="4" v-if="!viewPage">
                   <nav-list :nav-array="navList" v-if="showNavList"></nav-list>
                </el-col>
                <el-col :offset="viewPage?1:0" :span="viewPage?22:20" v-if="showCompoment">
                    <el-form :model="productForm"
                             ref="productForm"
                             :disabled="!viewPage && !(createNew ||btnRole(productForm,'Edit'))"
                             label-position="top"
                             label-width="180px">
                        <el-collapse v-model="activeNames">
                            <el-card class="sgs-box content-item" v-for="(config,index) in (createNew? generalFieldConfig.filter(g=>g.sectionCode!='BillOfMaterials'):generalFieldConfig)" :key="'pgen_'+index">
                                <el-collapse-item style="position: relative" :title="config.sectionLabel" :name="config.sectionCode">
                                    <template slot="title">
                                        <h4 :class="('title_'+config.sectionCode) + ' sgs-title'">
                                            {{config.sectionLabel || config.sectionName}}
                                        </h4>
                                    </template>
                                    
                                    <el-row :gutter="20" v-if="config.sectionType=='normal'">
                                        <el-col :span="8" v-for="(field,ind) in config.fieldList" :key="'pcfl_'+ind">
                                            <el-form-item
                                                    :rules="{ required: field.isRequired==1 , message: 'please input', trigger: ['blur', 'change'] }"
                                                    :label="field.fieldLabel || 'No label name'"
                                                    :prop="lowerField(field.fieldCode)"
                                            >
                                                <el-input v-if="!field.fieldType || field.fieldType!='select'"
                                                          placeholder="Please input"
                                                          clearable
                                                          v-model="productForm[lowerField(field.fieldCode)]"></el-input>
                                                <el-select
                                                        v-if="field.fieldType=='select'"
                                                        style="width: 100%"
                                                        clearable
                                                        filterable
                                                        placeholder="Please select"
                                                        v-model="productForm[lowerField(field.fieldCode)]">
                                                    <el-option
                                                            v-for="(op,opIndex) in field.sourceValue"
                                                            :key="field.fieldCode+'_'+opIndex"
                                                            :label="op.name"
                                                            :value="op.code"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <!-- 下面开始是动态的了-->
                                    <work-book
                                            v-if="(viewPage == 'preview' || productForm.id) && config.sectionCode=='Workbooks'"
                                            :object-id="productForm.id"
                                            :create-new="createNew"
                                            :edit-rule="createNew ||btnRole(productForm,'Edit')"
                                            :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                            ref="workBook"
                                    ></work-book>
                                    

                                    <bill-of-materials
                                            v-if="(viewPage == 'preview' || productForm.id) && pageId && config.sectionCode=='BillOfMaterials'"
                                            :edit-rule="createNew ||btnRole(productForm,'Edit')"
                                            :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                            ref="billOfMaterials"
                                            :sample-id="productForm.id"
                                            :customer-obj="{
                                               customerGroupCode: productForm.buyerCustomerGroupCode,
                                               customerGroupName: productForm.buyerCustomerGroupName,
                                               productLineCode: productForm.productLineCode,
                                               productLineName: productForm.productLineName
                                            }"
                                    ></bill-of-materials>
                                </el-collapse-item>
                            </el-card>
                            <el-form>
                                <report-certificate
                                        v-if="(viewPage == 'preview' || productForm.id) && reportCertificateFieldConfig.length>0"
                                        :object-id="productForm.id"
                                        :edit-rule="createNew ||btnRole(productForm,'Edit')"
                                        :show-approved="permissionList.approvedBtn && btnRole(productForm,'Approved')"
                                        :create-new="createNew"
                                        test-from="product"
                                        file-test-from="customer"
                                ></report-certificate>
                            </el-form>
                        </el-collapse>
                    </el-form>
                </el-col>
            </el-row>
            <el-row class="sgs-footer page-no-print" style="position: fixed" v-if="!pageLoading && !viewPage">
                <el-col style="text-align: center">
                    <el-button class="custom-primary-button" type="primary" @click="saveProduct" :disabeld="disabledSaveBtn" v-if="createNew || btnRole(productForm,'Edit')">Save</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('Submit')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Submit')">Submit</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="createTRF" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'ToTrf')">Create TRF</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('Return')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Return')">Return</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('Approved')" :disabeld="disabledSaveBtn" v-if="permissionList.approvedBtn && btnRole(productForm,'Approved')">Approved</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('NotInUse')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'NotInUse')">Not In Use</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="actionProduct('InUse')" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'InUse')">In Use</el-button>
                    <el-button class="custom-primary-button" type="primary" @click="changeAssign" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Edit')">Change Assignee</el-button>
                    <el-button class="custom-info-button" type="info" @click="handlerCancelProduct" :disabeld="disabledSaveBtn" v-if="btnRole(productForm,'Cancel')">Cancel</el-button>
                </el-col>
            </el-row>
        </div>
        <el-dialog
                :visible.sync="showChangeAssignee"
                width="30%"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
        >
            <el-row>
                <el-col :span="8">Change Assignee</el-col>
                <el-col :span="16">
                    <el-select
                        style="width: 100%"
                        v-model="assigneeId"
                    >
                        <el-option
                                v-for="(ass,index) in dataList.assigneeList"
                                :key="'ass_'+index"
                                :label="ass.email?(ass.name+'('+ass.email+')'):ass.name"
                                :value="ass.id"
                        ></el-option>
                    </el-select>
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showChangeAssignee = false">Cancel</el-button>
                <el-button type="primary" @click="saveAssigneeId">Save</el-button>
            </span>
        </el-dialog>
        <reject-provided v-if="rejectShow"
                         :object-id="productForm.id"
                         object-type="product"
                         @cancelDia="rejectShow=false"
                         @rejectSuccess="rejectSuccess"
        ></reject-provided>
        <chat-view
            v-if="showChat"
            :object-id="productForm.id"
            object-type="product"
            @cancelDia="showChat=false"
        ></chat-view>
    </basic-container>
</template>

<script>
    import {mapGetters} from "vuex";
    import CareLabelInfo from "../commonComponent/careLabelInfo";
    import ReportCertificate from "../commonComponent/reportCertificate";
    import NavList from "../commonComponent/navList";
    import BillOfMaterials from "../materialAndProductCommon/billOfMaterials";
    import WorkBook from '../materialAndProductCommon/workBook'
    import api from "../../../api/newSamples";
    import _ from "lodash";
    import ChatView from "../materialAndProductCommon/chatView";
    import RejectProvided from "../materialAndProductCommon/rejectProvided";

    export default {
        name: "productInfo",
        provide(){
            return {
                getMainObjectId:()=>{return this.productForm.id || this.pageId}
            }
        },
        data() {
            return {
                pageLoading: false,
                disabledSaveBtn:false,
                showChangeAssignee:false,
                showChat:false,
                createNew:false,
                rejectShow:false,
                showCare:true,
                reloadCare:false,
                assigneeId:'',
                temp:'',
                activeNames:[],
                productForm:{},
                pageId:'',
                fieldList:[],
                generalFieldConfig:[],
                reportCertificateFieldConfig:[],// 新增report config，用于拆分Report&Certificate 模块样式
                navConfig:[], // 存储拆分后的nav
                imageUrl:'',
                dataList:{
                    billOfMaterialList:[],
                    workbookList:[],
                    assigneeList:[],
                    uploadImgList:[]
                },
                showNavList:false,
                showCompoment:false,
                navList:[],
                templateParam:{
                    templateId:''
                },
                commentInterVal:null,
                reportNav:[
                    {
                        sectionCode:'Report',
                        sectionLabel:'Report'
                    },
                    {
                        sectionCode:'TestLine',
                        sectionLabel:'Test Line'
                    },
                    {
                        sectionCode:'Certificate',
                        sectionLabel:'Certificate'  
                    },
                ]
            }
        },
        methods: {
            initPage() {
                this.loadPage();
            },
            lowerField(fieldCode){
                return _.lowerFirst(fieldCode)
            },
            reloadCarelabel(){
                this.showCare = false;
                this.reloadCare = true;
                this.queryProductDetail();
            },
            loadPage(){
                let query = this.$route.query;
                let {action,templateId,id} = query;
                this.action = action;
                //处理页面select
                if(this.viewPage && this.viewPage == 'preview'){
                    this.initPreview();
                }else if(action=='copy'){
                    this.handlerCopy(id);
                }else{
                    this.productForm.id = id;
                    this.pageId = id;
                    this.templateParam.templateId = templateId;
                    this.queryProductDetail(!id);
                }
            },
            initPreview(){
                let {fieldList,generalFieldConfig} = this.templateData || {};
                if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                    this.$notify.warning("There has no preview data")
                    return;
                }
                this.fieldList = fieldList;
                this.generalFieldConfig = generalFieldConfig;
                this.activeNames = generalFieldConfig.map(g=>g.sectionCode);
                this.renderPageComponent();
            },
            initSelectList(){
                let cusParam = {
                    customerGroupCode: this.productForm.customerGroupCode
                }
                api.queryEmployee(cusParam).then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        this.dataList.assigneeList = res.data.data || [];
                        let ass = (res.data.data || []).find(ass=>ass.id==this.productForm.assigneeId) || {};
                        this.$emit("assigneeObj",ass)
                    }
                }).catch(err=>{
                    console.log("query comp id err",err)
                })

            },
            getFieldSourceValues(index){
                let {sourceValueLanguage} = this.productForm.fieldList[index];
                if(!sourceValueLanguage || sourceValueLanguage.length==0){
                    return []
                }
                return this.handlerSourceValue(sourceValueLanguage[0].sourceValue);
            },
            handlerSourceValue(sourceValue){
                let listOfValuesDataList = [];
                if(!sourceValue || sourceValue.length==0){
                    return listOfValuesDataList;
                }
                try{
                    listOfValuesDataList = sourceValue.map(v=>JSON.parse(v));
                }catch(e){
                    console.log("转换json异常",e)
                }
                return listOfValuesDataList;
            },
            queryTemplate(templateId,useNew=false){
                this.pageLoading = true;
                let formPurpose = 'Product';
                let apiReq = useNew?api.getSampleNewTemplateList({templateId,formPurpose}) : api.geSampleTemplateList({templateId,formPurpose});
                apiReq.then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        if(res.data.data.length==0){
                            this.pageLoading = false;
                            this.disabledSaveBtn = true;
                            this.$notify.error("Load Template Fail!");
                            return;
                        }
                        let {fieldList,generalFieldConfig,customerGroupCode} = res.data.data[0];
                        if((!fieldList || fieldList.length==0 ) && (!generalFieldConfig || generalFieldConfig.length==0)){
                            this.$notify.warning("No template data")
                            return;
                        }
                        this.$emit("getTemplateInfo",res.data.data[0]);
                        this.fieldList = fieldList;
                        const fieldConfig = this.getSpecificSections(generalFieldConfig);
                        this.generalFieldConfig = fieldConfig.generalFieldConfigCopy
                        this.reportCertificateFieldConfig = fieldConfig.reportCertificateFieldConfigCopy
                        this.navConfig = this.reportCertificateFieldConfig.length>0?(this.generalFieldConfig.concat(this.reportNav)):this.this.generalFieldConfig
                        this.activeNames = this.navConfig.map(g=>g.sectionCode);
                        this.productForm.templateId= useNew ? res.data.data[0]['templateId'] : templateId;
                        this.productForm.customerGroupCode = customerGroupCode;
                        this.initSelectList();
                        this.renderPageComponent();
                        this.renderNavList();
                    }else{
                        this.disabledSaveBtn = true;
                        this.$notify.error("Load Template Fail!");
                    }
                    this.pageLoading = false;
                }).catch(err=>{
                    this.pageLoading = false;
                })
            },
            btnRole({permissions},code){
                if(!this.productForm.id && this.viewPage!='preview' && code=='Edit' ){
                    return true;
                }
                return (permissions || []).map(p=>p.action).includes(code);
            },
            renderNavList(){
                let navList = [];
                this.navConfig.forEach((gen,index)=>{
                    let {sectionLabel,sectionCode} = gen;
                    if(this.createNew && (sectionCode=='BillOfMaterials' || sectionCode=='TestLine')){
                        return
                    }
                    let nav = {name:sectionLabel,id:"#"+sectionLabel,seq:index+1,active:false,isSub:false,show:true}
                    navList.push(nav);
                })
                this.navList = navList;
                this.showNavList = true;
            },
            renderPageComponent(){
                let list = [];
                this.fieldList.forEach(fl=>{
                    let {tips,fieldCode,id,dispalyName,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = fl;
                    let materialExtendFieldValue = ['Checkbox','Select2','DateRange'].includes(fieldType)?[]: '';
                    list.push({
                        fieldCode,
                        dffFormFieldId: id,
                        dffFormFieldNameEn:dispalyName,
                        materialExtendFieldValue ,
                        mandatoryFlag,
                        fieldType,
                        sequence,
                        sourceValueLanguage,
                        tips
                    })
                    if(this.pageId){
                        let dbObj = this.productForm.fieldList.find(l=>l.dffFormFieldId==id);
                        if(!dbObj){
                            this.productForm.fieldList.push({
                                fieldCode,
                                dffFormFieldId: id,
                                dffFormFieldNameEn:dispalyName,
                                materialExtendFieldValue,
                                fieldType,
                                sequence,
                                sourceValueLanguage,
                                tips
                            });
                        }
                    }
                });
                list.sort((a,b)=> {
                    return a.sequence - b.sequence
                })
                if(this.pageId){
                    this.productForm.fieldList.forEach(f=>{
                        let {dffFormFieldId} = f;
                        let templateObj = list.find(l=>l.dffFormFieldId==dffFormFieldId);
                        if(templateObj){
                            let {tips,dffFormFieldNameEn,fieldType,sequence,mandatoryFlag,sourceValueLanguage} = templateObj;
                            this.$set(f,'dffFormFieldNameEn',dffFormFieldNameEn)
                            this.$set(f,'sourceValueLanguage',sourceValueLanguage)
                            this.$set(f,'mandatoryFlag',mandatoryFlag)
                            this.$set(f,'fieldType',fieldType)
                            this.$set(f,'sequence',sequence)
                            this.$set(f,'tips',tips)
                        }
                    })
                    this.productForm.fieldList.sort((a,b)=> {
                        return a.sequence - b.sequence;
                    })
                }else{
                    //设置默认值
                    this.generalFieldConfig.forEach(gf=>{
                        let {fieldList} = gf;
                        (fieldList || []).forEach(f=>{
                            let {fieldCode,defaultValue} = f;
                            this.$set( this.productForm,this.lowerField(fieldCode),defaultValue);
                        })
                    })
                    this.$set(this.productForm,'fieldList',list);
                }
                this.showCompoment = true;
            },
            createTRF(){
                let customerGroupCode = this.productForm.buyerCustomerGroupCode;
                let bossNo = this.productForm.buyerCustomerBossNo;
                let customer = {
                    bossNo,
                    customerGroupCode
                }
                customer = encodeURIComponent(JSON.stringify(customer));
                let ids = [this.productForm.id];
                let bu = this.productForm.productLineCode;
                let actionType="individualSample";
                let flag =1;
                this.$router.push({
                    path:'/trf/trfForm',
                    query:{flag,actionType,ids,customer,bu}
                })
            },
            handlerCancelProduct(){
                this.actionProduct('Cancel',()=>{
                    this.backToList(false);
                });
            },
            changeAssign(){
                this.showChangeAssignee = true;
            },
            saveAssigneeId(){
                this.productForm.assigneeId = this.assigneeId;
                this.showChangeAssignee = false;
                this.saveProduct();
            },
            rejectSuccess(approvedType){
                let param ={
                    sampleId: this.productForm.id,
                    objectId : this.productForm.id,
                    approvedType:'product',
                    approvedStatus:2
                }
                this.rejectShow = false;
                api.approved(param).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        this.$notify.success("Success")
                        this.queryProductDetail();
                    }else{
                        this.$notify.error("Fail")
                    }
                }).catch(err=>{
                    this.$notify.error("Fail")
                })
            },
            approvedObj(approvedType,approvedStatus){
                let tips = "";
                if(approvedStatus==0){
                    tips = "Return"
                }
                if(approvedStatus==1){
                    tips = "Approve"
                }
                if(approvedStatus==2){
                    tips = "Reject"
                }
                this.$confirm(tips+' the product?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    if(approvedStatus==2){
                        this.rejectShow = true;
                        return;
                    }
                    let param ={
                        sampleId: this.productForm.id,
                        objectId : this.productForm.id,
                        approvedType,
                        approvedStatus
                    }
                    this.handlerApproved(param);
                }).catch(err=>{})
            },
            handlerApproved(param,refreshPage=false){
                api.approved(param).then(res=>{
                    if(res.status==200 && res.data && res.data.status==200){
                        let {data} = res.data;
                        if(data=='confirm'){
                            //需要弹窗确认，再次请求，然后刷新整个页面
                            this.$confirm('All info has been approved. Please confirm you would like to set the product status to "Approved"',
                                'Tips', {
                                    confirmButtonText: 'Confirm',
                                    cancelButtonText: 'Cancel',
                                    type: 'warning'
                                }).then(() => {
                                param['checkApproved'] = false;
                                this.handlerApproved(param,true);
                            }).catch(err=>{})
                            return;
                        }
                        this.$notify.success("Success")
                        this.queryProductDetail();
                    }else{
                        this.$notify.error("Fail")
                    }
                }).catch(err=>{
                    this.$notify.error("Fail")
                })
            },
            actionProduct(action,callback){
                this.$confirm(action+' the product?', 'Tips', {
                    confirmButtonText: 'Confirm',
                    cancelButtonText: 'Cancel',
                    type: 'warning'
                }).then(() => {
                    let {id} = this.productForm;
                    api.actionSamples({ids:[id],action}).then(res=>{
                        if(res.status==200 && res.data){
                            this.$notify.success("Success");
                            if(callback){
                                callback();
                            }else{
                                window.location.reload();
                            }
                        }else{
                            this.$notify.error(res.message || "Operation Fail");
                        }
                    }).catch(err=>{
                    })
                }).catch(() => {
                });

            },
            handlerImage(cloudIds){
                cloudIds.forEach(cloudID=>{
                    api.fileApi.downloadFile(cloudID).then(res=>{
                        if(res.data && res.data.data){
                            let imageUrl = res.data.data;
                            this.dataList.uploadImgList.push({
                                id:Math.random(),
                                cloudID,
                                imageUrl
                            })
                        }
                    })
                })
            },
            handlerCopyData(oldData,newData){
                let {id} = newData;
                oldData['id'] = id;
                oldData.permissions = [{action:'Edit'}];
                return oldData;
            },
            handlerCopy(id){
                this.pageLoading = true;
                api.copySample({id}).then(res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        let dbData = res.data.data;
                        let {assigneeId, cloudId,id} = dbData;
                        this.pageId = id;
                        this.createNew = true;
                        this.assigneeId = assigneeId;
                        if(cloudId && cloudId.length>0){
                            this.handlerImage(cloudId);
                        }
                        this.productForm = dbData;
                        this.$emit("getInfo",this.productForm);
                        this.queryTemplate(this.productForm.templateId,true)
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no product detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    this.$notify.warning("Query no product detail data");
                })
            },
            queryProductDetail(createNew = false){
                this.pageLoading = true;
                let {id} = this.productForm;
                api.querySampleDetail({id,dataType:'product'}).then(async res=>{
                    if(res.status==200 && res.data && res.data.data && res.data.data.id){
                        if(createNew){
                            this.createNew = true;
                            this.productForm = res.data.data;
                            this.queryTemplate(this.templateParam.templateId)
                            return;
                        }
                        let dbData = res.data.data;
                        if(this.action == 'copy'){
                            delete res.data.data.id;
                            let copyObj = await api.querySampleDetail({id:'',dataType:'product'});
                            if(copyObj.status==200 && copyObj.data && copyObj.data.data){
                                dbData = this.handlerCopyData(res.data.data,copyObj.data.data)
                            }
                        }
                        let {assigneeId, cloudId} = dbData;
                        this.assigneeId = assigneeId;
                        if(cloudId && cloudId.length>0){
                            this.handlerImage(cloudId);
                        }
                        this.productForm = dbData;
                        this.$emit("getInfo",this.productForm);
                        if(this.reloadCare){
                            this.showCare = true;
                        }
                        this.queryTemplate(this.productForm.templateId,this.action == 'copy')
                    }else{
                        this.pageLoading = false;
                        this.$notify.warning("Query no product detail data");
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                    this.$notify.warning("Query no product detail data");
                })
            },
            addCare(addCare){},
            delCare(careIndex){},
            fileExceed(files,fileList){
                this.$notify({title: 'Message', message: 'You can upload 5 files at most', type: 'warning'});
            },
            handleAvatarSuccess(res, file,fileList) {
                if(res.status!=200){
                    this.$notify.error("Upload fail");
                    return;
                }
                res.data.forEach(f=>{
                    let {id,cloudID} = f;
                    let imageUrl = URL.createObjectURL(file.raw);
                    let obj = {
                        id,cloudID,imageUrl
                    }
                    this.dataList.uploadImgList.push(obj);
                })
            },
            delImg(img,imgIndex){
                this.dataList.uploadImgList.splice(imgIndex,1);
            },
            beforeAvatarUpload(file) {
                const isJPG = ['image/png','image/jpeg'].includes(file.type);
                const isLt20M = file.size / 1024 / 1024 < 20;

                if (!isJPG) {
                    this.$message.error('The file type must be .jpeg,.jpg,.png');
                }
                if (!isLt20M) {
                    this.$message.warning('file size should be less than 20 MB');
                }
                return isJPG && isLt20M;
            },
            backToList(showTips = true){
                if(!showTips || (!this.createNew && !this.btnRole(this.productForm,'Edit'))){
                    this.$router.push({
                        path:'/web/customer/newProduct/list',
                        query:{}
                    })
                }else{
                    this.$confirm("Please make sure you have saved the information before you close the window.",'Tips',{
                        confirmButtonText: 'Confirm',
                        cancelButtonText: 'Cancel',
                        type: 'warning'
                    }).then(res=>{
                        this.$router.push({
                            path:'/web/customer/newProduct/list',
                            query:{}
                        })
                    })
                }
            },
            saveProduct(){
                this.$refs['productForm'].validate((valid) => {
                    if(!valid){
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.trfValidateError'),
                            type: 'warning'
                        });
                        return
                    }
                    let param = Object.assign({},this.productForm);
                    param['dataType'] = 'product';
                    this.pageLoading = true;
                    if(this.$refs.care_label_info){
                        let careLabelData = this.$refs.care_label_info[0].getSaveData();
                        if(careLabelData){
                            param['careLabelList'] = [careLabelData];
                        }
                    }

                    if(this.dataList.uploadImgList){
                        param['cloudId'] = this.dataList.uploadImgList.map(img=>img.cloudID);
                    }

                    api.createMaterial(param).then(res=>{
                        this.pageLoading = false;
                        if(res.status==200 && res.data && res.data.data){
                            let {id} = res.data.data;
                            window.location.href = '/#/customer/newProduct/detail?id='+id;
                            window.location.reload()
                        }else{
                            this.$notify.error(res.message || 'Save Fail');
                        }
                    }).catch(err=>{
                        this.pageLoading = false;
                        console.log("save product err",err)
                    })
                })
            },
            getCommentIcon(type){
                let {comments,readStatus} = this.productForm;
                //没有对话信息
                if(!comments || comments.length==0){
                    return 'el-icon-chat-square';
                }
                //有对话信息，已读
                if(readStatus-0==1){
                    return 'el-icon-s-comment';
                }
                //有对话信息，未读
                return 'el-icon-warning';
            },
            showReportComment(type){
                //对应类型的chat数据
                this.showChat = true;
            },
            getApprovedClass(btnType){
                let {approvedStatus} = this.productForm;
                if(!approvedStatus || approvedStatus==0){
                    return 'approve_gary';
                }
                if(approvedStatus==1){
                    if(btnType=='Approved'){
                        return 'approve_green';
                    }
                    if(btnType=='Reject'){
                        return 'approve_gary';
                    }
                }
                if(approvedStatus==2){
                    if(btnType=='Approved'){
                        return 'approve_gary';
                    }
                    if(btnType=='Reject'){
                        return 'approve_red';
                    }
                }
            },
            calcCommentStyle(){
                setTimeout(()=>{
                    let ProductInfo = this.generalFieldConfig.find(g=>g.sectionCode=='ProductInfo');
                    if(!ProductInfo || !ProductInfo.sectionCode){
                        return
                    }
                    let titleDom = document.querySelector(".title_ProductInfo");
                    let leftPx = titleDom.offsetWidth-0+5;
                    document.querySelector("span.productInfoApproved").style.left = leftPx - 5 +'px';
                },500)
            },
            // 获取Document tests 中特定的section
            getSpecificSections (generalFieldConfig){
                const generalFieldConfigCopy = generalFieldConfig.filter(item => 
                    ['Workbooks', 'BillOfMaterials'].includes(item.sectionCode)
                );
                const reportCertificateFieldConfigCopy = generalFieldConfig.filter(item => 
                    ['ReportCertificate'].includes(item.sectionCode)
                );
                return { generalFieldConfigCopy, reportCertificateFieldConfigCopy };
            },
             // 检查必填项是否已填写
             checkRequiredFields(sectionCode) {
                let config = this.generalFieldConfig.find(g => g.sectionCode === sectionCode);
                if (!config || !config.fieldList) return true;

                return config.fieldList.every(field => {
                    if (field.isRequired == 1) {
                        let fieldValue = this.productForm[this.lowerField(field.fieldCode)];
                        return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
                    }
                    return true;
                });
            },

            // 更新 navList 的 pass 字段
            updateNavListPass() {
                this.navList.forEach(nav => {
                    nav.pass = this.checkRequiredFields(nav.id.replace('#', ''));
                });
            },

            // 在表单值变化时调用
            handleFormChange() {
                this.updateNavListPass();
            }
        },
        mounted() {

        },
        created() {
            this.initPage();
        },
        watch: {
            showCompoment:{
              immediate:false,
              handler(newV){
                  if(newV){
                      this.calcCommentStyle();
                  }
              }
            },
            language: {
                handler: function(newVal, oldVal) {
                    if(newVal=='zh-CN'){
                        this.languageId=2;
                    }else{
                        this.languageId=1;
                    }
                },
                immediate: true
            },
            // 监听表单变化
            productForm: {
                handler: 'handleFormChange',
                deep: true
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language", "permission", "dimensions"]),
            permissionList() {
                return {
                    approvedBtn:this.vaildData(this.permission['sgs:samples:approved'], true)
                };
            },
        },
        props: {
            viewPage:'',
            templateData:{}
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {RejectProvided, ChatView, BillOfMaterials, NavList, ReportCertificate, CareLabelInfo,WorkBook}
    }
</script>

<style lang="scss">
    @import "@/styles/unit.scss";
    
    .sgs_smart_product_document {
        .el-collapse{
            border-bottom: none !important;
            border-top: none !important;
        }
        .el-collapse-item__wrap,.el-collapse-item__header{
            border-bottom: none !important;
        }
        p.sample_tips{
            color:#c8c8c8;
        }
        i.approved_icon{
            font-size: 18px;
            cursor: pointer;
            padding:2px 5px
        }
        .approve_gary{
            color: #8c8c8c !important;
        }
        i.more_icon{
            border: solid 1px #8c8c8c;
            border-radius: 50%;
            padding: 0;
            font-size: 14px;
            position: relative;
            top: -1px;
            color: white;
            background: #8c8c8c;
        }

        .img_del{
            position: absolute;
            right: 5px;
            top: 5px;
            cursor: pointer;
        }
        .img_add{
            position: absolute;
            right: 25px;
            top: 5px;
            cursor: pointer;
        }
        span.productInfoApproved{
            position: absolute;
            top: 10px;
        }

        /* 重置input边距 */
        .el-input__inner {
            padding: 0;
        }
        .el-form-item__label {
            color: $text-color-value;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .el-collapse-item__content {
            padding-bottom: 0;
        }
        .el-row {
            display: flex;
            flex-wrap: wrap;
        }
        .el-date-editor {
            width: 100%;
        }
        .el-form-item__content {
            line-height: 40px;
            display: flex;
            align-items: flex-end;
            height: 40px;
        }
        .el-date-editor i.el-range__icon {
            left: 0;
        }
    }
</style>