export default {
    tip: 'TIP',
    reminder:'Reminder',
    title: 'SGS SMART',
    logoutTip: 'Exit the system, do you want to continue?',
    submitText: 'Submit',
    cancelText: 'Cancel',
    select: 'Please select',
    search: 'Please input search content',
    searchTxt: 'Please input content',
    menuTip: 'None menu list',
    loading: 'Loading...',
    uploadType: 'Upload pictures in JPG format only!',
    uploadType_PDF: 'Upload pictures in PDF format only!',
    uploadSize: 'Upload image size cannot exceed 2MB!',
    uploadFileSize: 'Upload file size cannot exceed 10MB!!',
    uploadFileSizeError: 'Upload file size cannot exceed ',
    uploadSuccess: 'Upload Success!',
    uploadLoadingText: 'File uploading…',
    uploadExceed_1:'Support uploading one attachment at most, please delete it and upload it again',
    systemLanguage: 'System language',
    number: 'NO.',
    piece: 'Piece',
    success: 'Success!',
    NoData: 'No Data',
    ascendingOrder: 'Asc',
    descendingOrder: 'Desc',
    language:{
        add:'Add Language',
        name:'Language',
        curLanguage:'Current language',
        default: 'Default language',
        zh: 'Chinese',
        en:'English',
        repeatLanguageMsg:'The same language data already exists, please do not add it again',
        manage:'Language',
        validate:{
            selLanguageBlur:'Please select language',
            setDefaultLanguage:'Please set the default language'
        }

    },
    form: {
        printBtn: 'Print',
        mockBtn: 'Mock',
        submitBtn: 'Submit',
        emptyBtn: 'Empty',
        exportTemplate: 'Download Template',
        importTip: 'Only .xls .xlsx files',
      },
    // tip: {
    //     select: 'Placse Select',
    //     input: 'Place Input',
    //   },
    operation: {
        title: 'Action',
        add: 'Add',
        edit: 'Edit',
        modify: 'Modify',
        remove: 'Delete',
        chat: 'Communicate',
        view: 'View',
        search: 'Search',
        submit: 'Submit',
        confirm: 'Confirm',
        toBeConfirm:'To be confirmed',
        cancel: 'Cancel',
        reset: 'Reset',
        resetTip: 'Clear All Filters',
        disable: 'Disable',
        approve: 'Approve',
        pass: 'Pass',
        reject: 'Reject',
        auth: 'Authorize',
        select: 'Select',
        upload: 'Upload',
        download: 'Download',
        detail:'Detail',
        confirmDelete: 'Confirm to delete the selected data！',
        confirmStatus: 'Confirm to update the selected data！',
        confirmEnable: 'Confirm enable selection data！',
        confirmDisable: 'Confirm disable selection data！',
        confirmReject: ' Please enter rejection reason ',
        confirmDeleteRoot: 'All contents under the item will be deleted simultaneously , continue?',
        save: 'Save',
        finalizeReview: 'Finalize Review',
        complete: 'Complete',
        isEnable: 'Is Enable',
        saveAsTemplate: 'Save As Template',
        back: 'Back',
        goBack: 'Go Back',
        backToList: 'Back To List',
        print: 'Review & Print',
        preview:'Preview',
        copy: 'Copy',
        downLoad: 'Download',
        export: 'Export',
        exportTRF:'Export TRF List',
        exportReportFiles:'Export Report Files',
        exportReportTips:'Please filter out TRF list first.',
        exportReportMaxTips:'You can only export up to 20 report files, please adjust your filter accordingly.',
        selected: 'Select',
        remark: 'Remark',
        more: 'More',
        pleaseSelect:'Please select',
        and:' and ',
        pleaseEnterContent:'Please enter content...',
        batchImportAuthorization:'Import Authorization'
    },
    notice: {
        maintenanceNotice: 'SGS SMART system maintenance notice',
        maintenanceMessage: 'SGS SMART will be upgraded from 19:00 Mar.26th to 00:00 March.28th (GMT+8) , during the period, the system won\'t be available. Sorry for inconvenience caused.',
    },
    page: {
        prePage: 'Previous page',
        nextPage: 'Next page',
        clock: 'Clockwise',
        counterClock: 'Anti-clockwise',
    },
    datePicker: {
        startTime: 'Start time',
        endTime: 'End time',
        lastWeek: 'Last week',
        lastMonth: 'Last month',
        lastThreeMonths: 'Last three months',
        lastHalfYear: 'Last 6 months',
        lastYear: 'Last year',
        error: {
            timeSpanMsg: 'Only support for one year.',
        }
    },
    wel1: {
        draftTrf: 'Draft',
        trfSubmitted: 'Submitted',
        jobPreparation: 'Application Accepted',
        testingInProgress: 'Testing',
        reportCompleted: 'Report Issued',
        quotationToBeConfirmed: 'Quotation To Be Confirmed',
        quotationConfirmed: 'Quotation Confirmed',
        myTrf: 'Testing Application',
        newApplication: 'New Application',
        pass: 'Pass',
        fail: 'Fail',
        seeResult: 'See Result',
        total: 'Total',
        statusTracking: 'Status Tracking',
        documentLibrary: 'Library',
        documentType: 'Document Type',
        pbiType:'Link Type',
        defaultPbiType: 'Common Link',
        AflPbiType:'Power BI',
        cpCustomerReport:'CP Customer Report',
        knowledgeSharing: 'For Knowledge Sharing',
        documentSharing: 'For Document Sharing',
        SGSNewService: 'SGS Service',
        SGSSafeguard: 'SGS Safeguard',
        overallPerformance: 'Overall Performance',
        newTestRequestForm: 'New Test Request',
        hot: "What's Hot",
        requisitionStatus: 'Status',
        backToHome:'Back To Homepage'
    },
    api: {
        success: 'Settings changed!',
        successfully: 'Save Successfully!',
        error: 'Operation failed!'
    },
    term: {
        productLine: 'Product line',
        customerGroup: 'Customer group',
        customer: 'Customer',//new
        supplier: 'Supplier',
        supplierOrCustomerGroup: 'Supplier/Customer group',
        uploadSubmit: 'Upload',
        customerGroupCode: 'Customer group code',
        customerGroupBlur: 'Please select a customer group',
        customerGroupOrCustomer:'Customer Group/Customer'//14new
    },
    common: {
        status: {
            title: 'Status',
            enable: 'Enable',
            disable: 'Disable',
            push: 'Push',
            notPush: 'Not Push',
            reject:'Reject'// 14new
        },
        all:'ALL',
        register: 'Register',
        effectiveDate: 'Effective Date',
        invalidDate: 'Invalid Date',
        operator: 'Modified By',
        time: 'Date',
        operationTime: 'Date Modified',
        other: 'Other',
        default:'Set Default',
        isDefault: 'Default',
        yes: 'Yes',
        no: 'No',
        enable: 'Enable',
        disable: 'Disable',
        approveStatus: 'Approve Status',
        pass: 'Approve',
        reject: 'Reject',
        isAdmin: 'Is Admin',
        isChangeStatus: 'Change Status？',
        inputFilter: 'Enter keywords to filter',
        opinion: 'Approve Opinion',
        contacts: 'Contacts',
    },
    info: {
        base: 'Basic Information',
        attachment: 'Attachment Information',
    },
    date:{
        start:'Start Date',
        end:'End Date'
    },
    attachment: {
        title:'Attachment',
        name: 'Attachment Name',
        documentFrom: 'Attachment From',
        attachmentType:'Attachment Type',
        attachmentSource:'Attachment Source ',
        upload: 'Upload',
        updateTime: 'Last Modified Time',
        updateUser: 'Last Modified By',
        numberAttachments:'Attachments',
        downloadAll:'Download All'//new
    },
    user: {
        updateUser: 'Update User',
        createUser: 'Create User',
        updateTime: 'Update Time',
        createTime: 'Create Time',
        createDate: 'Date Created',
        createBy:'Created By',
        status: 'Status',
        oldPassword: 'Old Password',
        newPassword: 'New Password',
        updPassword: 'Change Password',
        companyAuthentication: 'Company Authentication',
        updPasswordSuccess: 'Update Success',
        updPhoneSuccess: 'Update Phone Success',
        phone: 'Phone',
        checkCode: 'Verification Code',
    },
    accountPer:{
        title:'Account authorization',
        customerGroup:'Customer group',
        authCompanyName:'Authorization customer',
        authCustomerNo:'Authorization customer No.',
        customerValidateError:'Customer group or customer list must select one',
    },
    route: {
        info: 'Information',
        trf: 'TRF Detail',
        website: 'SGS SMART website',
        dashboard: 'Dashboard',
        tags: 'Tags',
        store: 'Store',
        permission: 'Permission',
        api: 'Api',
        logs: 'Logs',
        table: 'Table',
        form: 'Form',
        formDetail: 'Form detail',
        formManagement: 'Form Designer',
        formDesign: 'Form Designer',
        formCodeManagement: 'Form Designer List',
        formCode: 'Form Code',
        data: 'Data',
        error: 'Error',
        test: 'Test',
        createTRF: 'Create TRF',
        todoList:'To do list',
        personalInfomation:'User Information',
        personalSettings:'Account Settings',
        companyManagement:'Company Management',
        companyInformation:'Company Information',
        extendSetting: 'Extend Setting',
        authorityManage: 'Authority Management',
        inputResult: 'Input Test Result',
        tableDemo: 'CPSC E-Filing',
        documentList: 'Document List',
        documentAuthorization: 'Document Authorization',
        documentMore: 'Historical Document',
        thirdLabRegister: 'Third Lab Register',
        thirdLabLogin: 'Third Lab Login',
        login: 'Login',
        register: 'Register',
        registerSuccess: 'Registration Success',
        simpleRegister: 'Register',
        simpleRegisterSuccess: 'Registration Success',
        simpleRegisterSuccessInfo: 'Registration Success',
        lock: 'lock',
        404: '404',
        403: '403',
        500: '500',
        systemUpdate: 'System Update',
        paySuccess: 'Payment Success',
        payError: 'Payment Error',
        customerAuthority: 'Customer Authority',
        inspection: 'Inspection',
        accountPer: 'Account Permission',
        accountPerAuthority: 'Account Permission Authority',
        SCM: 'SCM',
        documentShareing: 'Supply Chain Knowledge',
        documentShareing1: 'Power of data and recall cases',
        templateSetting: 'Template Setting',
        offlineCustomer: 'Offline Customer',
        newClass: 'New Class',
        trainingAttendance: 'Training Attendance',
        trainingPerformance: 'Training Performance',
        trfPreviewCA2: 'Preview CA2',
        statisticsExecutiveSummary: 'Executive Summary',
        statisticsPhysicalTestPerformance: 'Physical Test Performance',
        statisticsSupplierPerformanceOverall: 'Supplier Performance Overall',
        statisticsSupplierPerformanceChemical: 'Supplier Performance Chemical',
        statisticsSupplierPerformancePhysical: 'Supplier Performance Physical',
        customerMaterialConfig: 'Customer Material Config',
        customerMaterialList: 'Customer Material List',
        materialReport: 'Material Report',
        materialList: 'Material List',
        materialDetail: 'Material Detail',
        productList: 'Product List',
        productDetail: 'Product Detail',
        materialTemplateList: 'Material Template List',
        productTemplateList: 'Product Template List',
        templateDetail: 'Template Detail',
        tableComponent: 'Table Component',
        formComponent: 'Form Component',
        quotationAndInvoice: 'Quotation And Invoice',
        invoiceOrder: 'Invoice Order',
        customerManagement: 'Customer Management',
        eFiling: 'E-Filing',
        ProtocolList: 'Protocol List',
        ProtocolDetail: 'Protocol Detail',
    },
    authority:{
        title:'Authority',
        authorityManage:'Authority Management',
        myAuthority:'My Authority',
        myApplication:'My Application',
        stopAuthority:'Stop Authority ',
        bossNo:'Customer No',
        account:'Login Account',
        validate:{
            bossNoVali:'Please fill Company No.',
            accountValidate:'Please fill Login Account ID of your client',
        },
        rejectAuthority:'Are you sure to reject authorization?',
        stopAuthorityConfirm:'Are you sure to stop authorization?',
        approveAuthorityConfirm:'Are you sure to approve authorization?',
        agree:'Approve',
        status:{
            toBeAudited:'To Be Audited',
            audited:'Audited',
            rejected:'Rejected',
            cancelled:'Cancelled'
        }
    },
    login: {
        readMarketing:'I agree to receive relevant SGS updates and invitations to events in accordance with',
        readMarketingAfter:'.',
        agreedBtn:'I have read and agreed to the above',
        title: 'Login ',
        info: 'Micro service Development Platform',
        tenantId: 'Please input tenantId',
        username: 'Please enter the registration email or mobile',
        password: 'Please input a password',
        rePassword: 'Please enter the password again',
        passwordValidate: {
            lengthValidate: 'The minimum password length is 6 digits',
        },
        toRegister: 'Register',
        forgetPassword: 'I’ve forgotten my password',
        wechat: 'Wechat',
        qq: 'QQ',
        phone: 'Please input a phone',
        code: 'Please input a code',
        submit: 'Login',
        userLogin: 'UserLogin',
        phoneLogin: 'PhoneLogin',
        thirdLogin: 'ThirdLogin',
        msgText: 'Send Code',
        msgSuccess: 'Reissued Code',
        readAgree: 'I have read and agreed to',
        serviceTrems: 'SGS Terms of Use',
        serviceTremsEnd: '.',
        agreeServiceTerms: 'Please read and agree with SGS Service Terms & Conditions and Privacy Policy',
    },
    register: {
        continue: 'Continue',
        iGotIt: 'I got it!',
        authentication: 'Authentication',
        selCompany: 'Please select a company',
        setUserName: 'Set user name',
        accountInfo: ' Account information',
        registSuccess: 'Register Success',
        loginAccount: 'Login account',
        phoneNumber: 'Phone No',
        pictureCaptcha: {
            title: 'Picture Captcha',
            validate: 'Please input the picture verification code',
            clickChange: 'Click to switch',
            captchaOvertime: 'The verification code has timed out. Please get it again',
            captchaInvalid: 'The verification code is invalid, please get it again',
            captchaError: 'Verification code error, please re-enter',
        },
        emailRegister: 'Use email to register',
        phoneRegister: 'Use phone number to register',
        returnLogin: 'Existing, return to login',
        submitRegister: 'Submit Register',
        doublePasswordBlur: 'Please enter the password again',
        passwordError: 'Must contain uppercase, lowercase, and numerals, at least 8 characters',
        addressBlur: 'Please enter company address',
        taxNoBlur: 'Please enter company taxNo.',
        companyNameBlur: 'Please enter company name',
        bossNo: 'Boss No.',
        bossNoBlur: 'BOSS number is not vaild',
        checkPassNo: 'The two passwords are inconsistent!',
        phoneBlur: 'Please enter your mobile phone number',
        verificationCodeBlur: 'Please enter verification code',
        telePhoneBlur: 'Currently, only mobile numbers in mainland China are supported',
        contactBlur: 'Only \'numeral\' or \'-\' is valid',
        pleaseInputCode: 'Please enter SMS verification code',
        emailBlur: 'Please enter company email',
        emailError: 'Please enter company email',
        loginAccountBlur: 'Please enter login account',
        emailRigthBlur: 'Please enter the correct email address',
        accountBlur: 'Please enter account name',
        userNameLength:'Length should be 3 to 50 characters',
        passwordBlur: 'Please input a password',
        sgsReportNoBlur: 'Please input a sgs report no.',
        submitInfo: 'Email has been registered, please login directly!',
        submitPhoneInfo: 'Mobile number registered, please login directly!',
        lockError: 'User locked please contact administrator!',
        registerError: 'This account has been registered, please change another account!',
        mobileRegisterError: 'The entered mobile phone number has been registered, please change other mobile phone number!',
        serveType: 'Product Category',
        serviceUnit:'Product Category',
        serviceUnitBlur: 'Please select the product category',
        serveTypeBlur: 'Please select the product category',
        tip: {
            emailconfim: 'Please log in the email for verification, and send it to SGS for approval after verification',
            sgsapprove: 'The audit will take 3-5 working days. Please wait patiently. You will be notified by email after the audit is passed',
            success: 'Registration successfully!',
            login1: 'You can already login SGS SMART to fill non-buyer application form. SGS SMART will check your account application and unlock full functions for you. Thank you!',
            login2: 'Login ',
            login3: 'To apply for your test request. ',
            success3: 'For further service from SGS, please complete corporate authentication, thanks!',
            perfectInfo: 'Please complete the information and we will verify it as soon as possible to unlock more functions for you'
        },
        steps: {
            emailconfim: 'Email confirm',
            sgsapprove: 'SGS approve'
        },
        message: {
            title: 'Registration notice',
            emailSendTip: 'A verification email has been to ',
            emailSendMessage: 'Please log in your email and click the activation link to complete the registration',
            returnLogin: 'Back to login',
        },
        newRegister: {
            sendEmailTo: 'We have sent the verification code to ',
            commaSymbol:'.',
            belowCode:'Please enter the code below.',
            verifyEmail:'Verify email address',
            verifyPhone:'Verify phone number',
            verify:'Verify',
            resend:'Resend Code',
            second:'s',
            donotGetCode:"Didn't get the code?",
            sendIn5M:'Codes can take up to 5 minutes to arrive.',
            checkSpamMail:'Check your spam folder.',
            checkSpamSms:'Check your spam sms.',
            length6:'Verification code should be 6 digits',
        },
    },
    invoice: {
        title: 'Invoice',
        invoiceDetail:'Invoice Detail',
        name: 'Invoice Information',
        addInvoice: 'Add Invoice',
        editInvoice: 'Edit Invoice',
        invoiceTitle: 'Company Name',
        invoiceType: 'Invoice Type',
        taxNo: "Tax Number",
        address: 'Company Address',
        tel: 'Tel Number',
        bankName: 'Bank Of Deposit',
        bankAccount: 'Bank Account',
        paymentStatus: 'Payment Status',
        fapiaoNo: 'Fapiao',
        awbNo:'Airway Bill Number',
        contactName: 'Bill Recipient',
        invoiceNo: 'Invoice',
        invoicePDF: 'Invoice PDF',
        fapiaoIssuedDate: 'Invoice Date',
        fapiaoIssuedStartDate: 'Date Of Start Issue ',
        fapiaoIssuedEndDate: 'Date Of End Issue ',
        currency: 'Currency',
        preTaxAmount: 'Pre Tax Amount',
        totalAmount: 'Total Amount',
        taxRate: 'Tax Rate',
        paidAmount: 'Amount Paid',
        invoiceBalance: 'Payment Balance',
        reportNo: 'Report No',
        clientReferenceNo: 'Customer Ref.',
        payee: 'Payee',
        statementAndInvoice: 'Bill And Invoice',
        bill: 'Invoice',
        electronicInvoice: 'E-receipt',
        mergePay: 'Pay',
        paymentInfo:'Payment Bill Information',
        totalPayable:'Total Payable',
        invoiceAmount:'Invoice Payable',
        invoiceDate:'Invoice Date',
        payTop1:'Please complete the payment within',
        payTop2:'15 minutes',
        payTop3:'After submitting the order, otherwise the order will be cancelled automatically.',
        submitPay:'Submit Payment',
        completePayment:'Complete Payment',
        toto:{
            payer:'Payer',
            attention:'Attention',
        },
        status:{
            title:'Invoice status',
            toBeConfirm:'To be confirmed',
            confirmed:'Confirmed',
            reject:'Reject'
        },
        validate: {
            invoiceTitleBlur: 'Please enter Company Name',
            invoiceTypeBlur: 'Please select invoice type',
            taxNoBlur: 'Please enter tax number',
            addressBlur: 'Please enter Company address',
            telBlur: 'Please enter the Tel number',
            bankNameBlur: 'Please enter the name of the Bank of deposit',
            bankAccountBlur: 'Please enter the account number of the opening bank',
            invoiceExists: 'Invoice Information already exists',
            selectPayInvice:'Please select the invoice to be paid',
            alreadyPaid:'Paid, please re select another invoice to pay',
            payOrderTip:'The Invoice already has a payment order. Go to the order interface to pay',
            selectPayType:'Please select payment method',
        },
        order:{
            title:'My order',
            orderNo:'Order No.',
            orderStatus:'Order status',
            orderPayable:'Total order amount',
            payPayable:'Payment amount',
            payType:'Payment method',
            payTime:'Payment time',
            payment:'Payment',
            cancelOrder:'Cancel order',
            detail:'Detail',
            cancelOrderTip:'Cancel this order'
        }
    },
    pay:{
        waitForPay:'Waiting for payment',
        paySuccess:'Payment successful',
        Paymentfailed:'Payment failed',
        Refunded:'Refunded',
        payType:{
            alipay:'Alipay',
            wechat:'Wechat',
            unionPay:'UnionPay'
        }
    },
    quotation: {
        title: 'Quotation',
        statement:'Statement',
        selectPayCurrency: 'Select payment currency',
        cny: 'CNY',
        usd: 'USD',
        hkd: 'HKD',
        quotationDetail: 'Quotation Detail',
        statementDetail: 'Statement Detail',
        referenceNo:'Reference No.',
        quotationNo:'Quotation No.',
        statementNo:'Statement No.',
        invoiceNo:'Invoice No.',
        totalTestItems:'Net Price',
        estimatedTax:'VAT',
        totalAmount:'Total Amount',
        invoicePDF:'Quotation PDF',
        statementPDF:'Statement PDF',
        commentBy:'Comment By',
        commentTime:'Quotation Comment Date',
        statementCommentTime:'Statement Comment Date',
        issueTime:'Quotation Issue Date',
        statementIssueTime:'Statement Issue Date',
        confirmExportQuotationList: 'Up to 500 pieces of data can be exported. Do you want to continue?',
        quotationConfirmDate:'Quotation Confirm Date',
        quotationIssuedDate:'Quotation Issue Date',
        status:{
            title:'Quotation status',
            statement:'Statement status',
            toBeConfirm:'To be confirmed',
            confirmed:'Confirmed',
            reject:'Amendment needed',
            cancel:'Cancel',
            toBePay:'To be paid'
        },
        rejectReason:'Reason for rejection',
        validate:{
            rejectReason:'Please enter the reason for rejection'
        }

    },
    invoiceTodo:{
        commentBy:'Comment by',
        commentTime:'Invoice comment date',
        paid:'Paid',
        status:{
            title:'Invoice status',
            toBeConfirm:'To be paid',
            confirmed:'Paid',
            reject:'Amendment needed',
            cancel:'Cancel'
        },
    },
    testResult: {
        title: 'Test Result',
    },
    pend: {
        name: 'Todo list',
        pendType: 'Type',
        pendDescribe: 'Task',
        isRead: 'Read',
        goHandle: 'Handle',
        processed: 'Completed',
    },
    navbar: {
        logOut: 'Log Out',
        searchTrf: 'Report NO/Test Request NO',
        userinfo: 'User Information',
        personalitySetting:'Account Setting',
        authentication: 'Company Authentication',
        companyInfo: 'Company Information',
        dashboard: 'Home',
        lock: 'Lock',
        bug: 'No Error Log',
        bugs: 'Error Logs',
        screenfullF: 'Quit Screen Full',
        screenfull: 'Screen Full',
        language: 'Language',
        theme: 'Theme',
        color: 'Color',
        customerManagement: 'Company Management',
        materialManagement: 'Material Management',
        trfList: 'Application List',
        trfForm: 'Test Request Form',
        dataEntryForm: 'Input Test Result',//14new
        generalFields: 'General Fields',
        trainingList: 'Training Management',
        classManager: 'Course Management',
        courseManager: 'Program Management',
        trainningPerformance: 'Performance',
        customerApprove: 'Customer Approve',
        labManagement: 'Lab Management',
        factoryManagement: 'Factory Management',
        testpackageManagement: 'Testpackage Management',
        templateManagement: 'Template Management',
        reconciliationService: 'Query',
        myOrder:'Payment Order',
        newTemplate: 'New Template',
        newCustomer: 'New Customer',
        feedback: 'Feedback',
        vipservice: 'Vip Service',
        documentLibrary: 'Document Library',
        documentSharing: 'Supply Chain Knowledge',
        documentSharing1: 'Power of data and recall cases',
        authorization: 'Authorization',
        aflTemplateManagement: 'Afl Template Management',
        quotationAndInvoice:'Quotation & Invoice',
        accountPer:'Account Permissions Management'
    },
    customerMaterialConfig: {
        configTemplate: 'Configure templates',
        title: {
            add: 'Add',
            edit: 'Edit',
        },
        buyer:'Buyer',
        conclusion: 'Conclusion',
        applicationNo: 'Application No',
        dffFormType: 'DFF Type',
        dffFieldName: 'DFF Name',
        isMaterial: 'Material',
        dffFieldMaterialName: 'Material Name',
        isMaster: 'Master?',
        isSearch: 'Search?',
        isUnique: 'Unique?',
        isMr: 'Is Mr?',
        material: 'Material',
        materialFrom2: "Submitted By",
        materialReport: 'Material Report',
        downloadExcel: 'Download Template',
        exportExcel: 'Export Excel',
        uploadExcel: 'Upload',
        choseExcel: 'Chose Excel',
        openExcel: 'Upload Excel',
        modifyTemplate: 'Modify Template',
        uploadFile: 'Excel File',
        needChooseOne: 'Need choose one',
        ownProducts: 'Own products',
        otherProducts: 'Other products',
        materialFrom: 'Please select the source！',
        chooseTemplate: 'Please select the template！',
        materialFile: 'Please select the attachment！',
        dffFieldMaterialNameRepeatToAdd: 'Material name repeat to add！',
        noDelete: 'Materil data already exists and cannot be deleted!',
        materialListName:'Material List Name',
        selectMaterial:'Please select one material at least',
        hasBoundTrf:'The selected material has been bounded to TRF',
        hasNoTemplate:'The selected material does not have a template, please configure template first'
    },
    quickAccess: {
        customer: 'Company Management',
        trfList: 'Applications',
        homepage: 'Home',
        other: 'Application Template'
    },
    tagsView: {
        menu: 'More',
        closeOthers: 'Close others',
        closeAll: 'Close all'
    },
    testpackage: {
        rootNode: 'Root',
        titleAdd: 'Add test packate',
        titleEdit: 'Edit test package',
        name: 'Package name',
        parent: 'Parent',
        selTestPackage: 'Select test package',
        inputTestPackageName: 'Please enter the test package name',
        inputCustomerName:"Please Enter The Customer Name",//14new
        testItem: 'Test item'
    },
    lab: {
        title: 'Lab',
        labInfo: 'Lab Information',
        labName: 'Lab Name',
        labNameAfl: 'SGS Branch',
        labCode: 'Lab Code',
        labType: 'Lab Type',
        labAddress: 'Lab Address',
        labAddressAfl: 'SGS branch address',
        labAddressBlur: 'Please enter the lab address',
        labLocation: 'Lab location',
        titleAdd: 'Add Contact',
        titleEdit: 'Edit Contact',
        addContact: 'Add Contact',
        editContact: 'Edit Contact',
        selLabName: 'Please enter the lab name',
        selLabCode: 'Please enter lab code',
        selLabType: 'Please select the lab type',
        selLab: 'Please select the lab',
        labChanged: 'Lab info was updated, please input again',
        labCountry:'Lab country',
        labCity:'Lab city',
        labCertificate:'Lab Certificate',
    },
    labContact: {
        selLabContact: 'Please select lab contact',
        labContactName: 'Lab Contact Email',
        labContactNameAfl: 'SGS Lab Contact Email',
        contactName: 'Full name',
        contactName1: 'Contact name',
        contactTel: 'Contact No.',
        contactEmail: 'Email address',
        contactAddress: 'Address',
        selContactName: 'Please enter contact name',
        selContactTel: 'Please enter the contact number',
        selContactEmail: 'Please enter contact email',
    },

    documentLibrary: {
        title: {
            default: 'Document',
            tab: 'Document',
            add: 'Add document',
            edit: 'Edit document',
            baseInfo: 'Basic Information',
            attachmentInfo: "Attachment information"
        },
        chooseFile:'Choose File',
        clearErrorData:'Delete Invalid Data',
        name: 'Name',
        nameFull: 'Document name',
        linkUrl: 'URL',
        imageCloudId: 'Photo',
        documentSort: 'Sort',
        documentDec: 'Description',
        theme: "Theme",
        validate: {
            typeBlur: 'Please select document type',
            nameBlur: 'Please enter a document name',
            imageUrl: 'Please Select Image',
            boosLengthErr:'Please enter a customer number with at least 4 digits and more',
            noCustomerTip:'The customer hasn\'t been registered in SGS SMART',
            productLineErr:'Please select Product Line',
            noTemplate:'Unable to upload. Please make sure the template is the latest and no change has been made to the header.',
            numberLength500:'You can only upload maximum 500 rows in one time. Please upload the records in batches.',
            interfaceError:'System error. Please try again later or contact SGS administrator.',
            hasInvalidData:'Unable to upload. Please delete the error data before uploading.',
            repeatDataTip:'There is duplicate data present'
        }
    },
    documentSharing: {
        attachments: 'Attachments - view All'
    },
    newService: {
        title: {
            default: 'New service',
            tab: 'New service',
            add: 'Add new service',
            edit: 'Edit new service',
        },
        url: 'Service URL',
        textTitle: 'Title',
        flyer: 'Flyer',
        validate: {
            textTitleBlur: 'Please enter a title',
            urlAndFileError: 'Only one URL or file can be selected',
            urlAndFileValidate: 'Please input URL or upload attachment',
            uploadImageError: 'Please upload image'
        }
    },
    training: {
        CourseTitle: 'Program Title',
        Course: 'Program',
        TrainingCourseLevel: 'Program Level',
        TrainingLevel: 'Training Level',
        CourseModule: 'Program Module',
        Module: 'Module',
        AddModule: 'Add Module',
        EditModule: 'Edit Module',
        AddCourse: 'Add Program',
        EditCourse: 'Edit Program',
        EnrollmentStartDate: 'Course Start Date',
        EnrollmentExpiredDate: 'Course End Date',
        ClassIntroduction: 'Course Introduction',
        UploadCourseImage: 'Upload Program Image',
        TrainingDate: 'Training Date',
        TrainingAddress: 'Training Address',
        TrainingLocation: 'Training Location',
        Trainer: 'Trainer',
        SelectTrainee: 'Select Trainee',

        TrainingMaterials: 'Training Materials',
        Browse: 'Browse',
        ModuleFile: 'Module File',
        Close: 'Close',
        DocumentName: 'Document Name',
        UploadedBy: 'Uploaded By',
        Action: 'Action',
        Download: 'Download',
        Delete: 'Delete',
        Save: 'Save',
        PublishCourse: 'Publish',
        NotifyAttendance: 'Notify Attendance',
        Cancel: 'Cancel',
        ClassInfo: 'Course Information',

        CourseIntroduction: 'Program Introduction',
        Seek: 'Seek',
        Add: 'Add',
        Edit: 'Edit',
        Clear: 'Clear',
        detail: 'Detail',
        CustomerGroup: 'Customer Group',
        TrainingCourseModules: 'Training Program Modules',
        ModulesName: 'Modules Name',
        Attachment: 'Attachment',
        Clickonthedownload: 'Click on the download',
        FileList: 'File List',

        NewClassManager: 'Course Manager',
        NewClass: 'New Courses',
        NewCourseModule: 'New Program Module',
        NewCourse: 'New Program',
        OngoingClass: 'Ongoing Courses',
        ComingClass: 'Coming Courses',
        CompleteClass: 'Completed Courses',
        Attendance: 'Attendance',
        loading: 'Loading',
        loadingFail: 'No Image',
        Date: 'Date',
        InvitationNumber: 'Invitation Number',
        EnrolledNumber: 'Enrolled Number',
        Location: 'Location',
        LoadingMore: 'Load More',
        Loaded: 'Loaded',

        AllPass: 'All Pass',
        ExportToExcel: 'Export To Excel',
        Register: 'Register',
        Name: 'Name',
        Company: 'Company',
        TotalNumOfAttendance: 'Total Num Of Attendance',
        PassRate: 'Pass Rate',
        TotalNumber: 'Total Number',
        Enrolled: 'Enrolled',
        Result: 'Result',
        Score: 'Score',
        Remark: 'Remarks',
        Pass: 'Pass',
        Fail: 'Fail',
        NotAttended: 'Not Attended',
        RepeatToAdd: 'Program title & level repeat ！',
        ModuleFileRepeatToAdd: 'Has been added ！',

        fsCode: 'FFC ID',
        tier: 'Tier',
        productCategory: 'Division',
        Supplier: 'Supplier',
        hadChoose: 'Selected Suppliers',
        InternalStaff: 'Internal Staff',
        ClassList: 'Course List',
        supplierName: 'Supplier Name',
        classStatus: 'Course Status',
        country: 'Country',
        region: 'Region',
        year: 'Year',
        className: 'Training Courses',
        trainingName: 'Training Name',
        success: 'Success',
        attendanceStatus: 'Disallow Operation',

        //导出
        notRegister: 'Not Register',
        notEnrolled: 'Not Enrolled',
        notPass: 'Not Pass',
        notAttend: 'Not Attend',
        isPublish: 'Is Publish',
        Publish: 'Publish',
        Published: 'Published',
        NotPublish: "Don't Publish",
        NoPublish: "Don't Publish",
        NoData: 'No Data',
        KeywordSearch: 'Keyword Search',
        validate: {
            courseTitle: 'Please enter the Program title',
            trainingCourseLevel: 'Please select Program level',
            courseIntroduction: 'Please enter the Program introduction',
        },
        //培训相关结束
        filter: 'Filter',
        confirmComplete: 'There will be no other operations after completion. Do you want to continue?',
    },
    reviewConclusion: {
        title: {
            tab: "Review Conclusion",
            add: 'Add Review Conclusion',
            edit: 'Edit Review Conclusion',
        },
        reviewCode: 'Review Code',
        reviewValue: 'Review Value',
        RepeatToAdd: 'Repeat!',
    },
    buyerRelationship: {
        title: {
            tab: "Relationship",
            add: 'Add Buyer Conclusionship',
            edit: 'Edit Buyer Conclusionship',
        },
    },
    ccl: {
        chemicalEnName: 'Chemical name (English)',
        chemicalZhName: 'Chemical name (Chinese)',
        casNo: ' CAS No.',
        composition: 'Composition (%)',
        casNoCheck: 'CAS No. error',
        add: 'Add',
        batchAdd: 'Batch add',
        batchAddBlurMsg: 'Please enter the number of new lines',
        batchAddErrorMsg: 'Please enter a value in the range of 0-100',
        validate: {
            componentNullError: 'This field must be completed',
            componentFormatError: 'Please check the validity of the input data',
        }
    },
    template: {
        productLine: 'Product Line',
        customerGroup: 'Customer Group',
        customer: 'Customer',
        test:"",
        dff: 'DFF Grid / Design Form',
        groupDff:'DFF',
        groupForm:'Design Form',
        productCategory: 'Product Category',
        customerName: 'Customer Name',
        defaultCustomer: 'Default Customer',
        templateName: 'Template Name',
        testPackage: 'Test Package',
        specific: 'Specific Tags',
        selectTestPackage: 'Select Test Package',
        labContactTitle: 'Lab Contact Email',
        baseInfoTitle: 'Basic Information',
        default: 'Default',
        selProductLine: 'Please select a product line',
        selCustomerGroup: 'Please select a customer group',
        selCustomer:'Please select a customer',
        selCustomerOrGroup: 'Please select a customer group or customer',
        selDff: 'Please select a dff grid',
        selProductCategory: 'Please select product category',
        selTestPackage: 'Please select test package',
        inputTemplateName: 'Please enter a template name',
        usable: 'Usable',
        required: 'Required',
        fieldSettings: 'Field Settings',
        isAflDefault: "Is Default",
        createTime: 'Creation Time',
        isDeleted: "Is Delete",
        isAflCreatedByAdmin: 'Creator',
        printTemplate: 'Print Template',
        defaultFlag: 'Default Template',
        flagTrue: 'True',
        flagFalse: 'False',
        dffDefaultFlag: 'DFF Default Template',
        serviceUnitDefaultFlag: 'BU Default Template',
        confirmSave: 'There is an existing defualt TRF template, do you want to replace it?'
    },
    cclTrf: {
        sdsStandard: 'SDS Standard',
        isDraftReport: 'Draft report needed?',
        needUfiCode: 'Need UFI code (For EU SDS)'
    },
    trf: {
        customerNo:'Customer No',//14new
        agentTip: 'If you apply for this agent, test and report will be updated to the agent',
        changeTemplate: 'Select Template',
        changeTemplateMsg: 'If you switch other templates, the original test data will be cleared. Do you want to continue?',
        templateCustomerError:'The template you are using is missing buyer information, please contact SGS for help.',
        customerTemplateExistError:'The customer you chose has customer templates. Please reselect a template.',
        selBuyerCustomerGroup:'Customer group',
        validate: {
            noTemplateMsg:'Please select a customer template first.',
            requiredBlur: 'Necessary information',
            contactNameLengthErrorInfo: 'The length must not be greater than 50',
            emailLengthError:'Length exceeds 200',
        },
        notes: 'Notes',
        selfReference: 'Self Reference',
        isEfillingFlag:"Require US CPSC eFiling",
        selfReferenceTipInfo: 'Even if self reference is checked, the buyer can still see the report',
        print: 'Review And Print Application',
        downLoad: 'Download TRF',
        downLoadBarcode: 'Download BarCode',
        createtrf: 'Create Test Request Form',
        customerInfo: 'Customer',
        composition: 'COMPOSITION/INGREDIENT',
        buyerInfo: 'Buyer Information',
        buyerAndAgentInfo: 'Buyer & Agent Information',
        agentInfo: 'Agent Information',
        applicantInfo: 'Applicant Information',
        applicant: 'Applicant',
        payInfo: 'Billing Information',
        sameApplicant: 'Same as Applicant',
        productAndSample: 'Product And Sample',
        dynamicForm: 'Form Information',
        requirements: 'Storage Requirements',
        sampleRetentionPeriod: 'Sample Retention Period',
        mainComponentsSample: 'Main Components',
        danger: 'Toxicities',
        productContent: 'Sample Content',
        productDescription: 'Description',
        addProduct: 'Add Sample',
        careLabelSymblosAndWording: 'Care label symbols and wording',
        trfTemplate: 'Application Template',
        templateName: 'Template',
        selOtherTemplate: 'Other Template',
        testPackage: 'Test Request',
        basicInfo: 'Basic Information',
        basicInfoAfl: 'Basic Information',
        isRetest: 'Retest?',
        previousReportNo: 'Previous report No.',
        sampleReceivedDate: 'Sample Received Date',
        dueDate: 'Expected Due Date',
        attachment: 'Attachments',
        remark: 'Remark',
        dffFormValidError: 'DFF form validation failed!',
        templateErrorInfo: 'The buyer updated test application form, your template can\'t be used any more, please fill the new application form again!',
        dffGridValidError: 'DFF grid validation failed!',
        trfValidateError: 'Necessary info in application is not completed',
        placeholder: {
            template: 'Please select test template',
            trfTemplateName: 'Please enter TRF template name',
        },
        confirmCancelTrf: 'Are you sure to cancel the Test Request Form?',
        confirmReturnTrf: 'Are you sure to Return the Test Request Form?',
        confirmRemoveTrf:'Are you sure to delete the Test Request Form? It can’t be restored.',
        confirmExportTrfList: 'Up to 3000 pieces of data can be exported. Do you want to continue?',
        confirmSample: 'Confirm Sample',
        trfTemplateName: 'TRF Template Name',
        trfTemplateList: 'TRF Template List',
        returnTrfSatus: 'Return',
        notSubmit: 'Not yet submitted',
        serviceTypeSel: 'Please select product category',
        templateSel: 'Please select Template',
        buyerSel: 'Please select Buyer',
        serviceClause: 'We have applied the above tests and agreed to the terms and conditions on the SGS website',
        printTrfTip: 'Please sign and stamp the printed test application form and send it to the lab along with the samples. Thanks!',
        dffGridTitle: 'Sample Information',
        printTrfTipAFL: 'In order to facilitate the laboratory to identify your sample more quickly, please click "Preview/Print Application Form" at the bottom of the page to print out the application form and send it along with the sample.',
        trfAddBtn: 'Add sample',
        trfDownBtn: 'Download template',
        trfUploadBtn: 'Batch upload',
        trfConfirmTit: 'Overwrite original content?',
        printTime:'Printing time',
        history:{
            title:'History',
            createBy:'Operation By',
            operationTime:'Date',
            status:'Operation Type',

        },
        more: 'More',
        hide: 'Hide',
        printMsg: 'Sorry, migrated data cannot be previewed or printed.',
        tipReference:'You have selected the TRF as “self reference”. The buyer would not be able to access to this TRF. Please confirm.',
        tipsConfirm:'Confirm',
        tipsCancel:'Cancel',
        HKCMS:'Click this link to place sample pickup order (Hong Kong only)',
        customerConfirmText: 'Please confirm that the report received meets your requirements!'
    },
    userTemplate:{
        name:'User Template Name',
        trfName:'TRF Name',
        customerName:'Customer',
        createDate:'Date Created',
        changeToTrfMsg:'Please confirm to create a new test application by this template?',
        outChangeToTrfMsg:'This template was phased out, and please confirm to create a new test application by this template?',
        phaseout:'Phase-out'
    },
    trfList: {
        query: {
            searchFor: 'Search for...',
            selStartDate: 'Select Start Date',
            selEndDate: 'Select End Date',
            oneWeek: '1 week',
            oneMonth: '1 month',
            sixMonth: '6 months',
            oneYear: '1 year',
            timeOutTrf: 'Time out TRF',
            moreSearch:'More Search',
        },
        filter:{
            set:'Set',
            default:'Default',
            delete:'Delete',
            saveFilter:'Save View',
            replaceAnExisting:'Replace an existing',
            saveAsNew:'Save as New',
            save:'Save',
            saveAsLocal:'Local',
            saveAsPublic:'Public',
            filterPlaceHolder:'My Saved Views',
            saveAsDefault:'As Default'
        },
        quickSorting:'Quick Sorting',
        copyTrf: 'Copy TRF',
        createEfilling:'Create eFiling',
        viewEfilling:'Show eFiling',
        jobStatus: 'Job Status',
        conclusion: 'Overall Conclusion',
        trfNo: 'Test Request Form Number',
        extendOrderNo: 'Order No',
        templateName: 'Template Name',
        buyer: 'Buyer',
        applyCustomer: 'Applicant',
        agent:'Agent',
        retest: 'Is retest',
        submitDate: 'Date of TRF Submission',
        sampleDate: 'Sample Received Date',
        sampleDateValidateMsg: 'Sample receive date is required',
        dueDate: 'Expected Due Date',
        applyContact: 'Contact Name',
        labName: 'Lab Name',
        dynamicMsg: 'Latest Status',
        reportNo: 'Report No',
        productDescription: 'Product Description',
        reportApprovedDate: 'Report Issued Date',
        styleNo: 'Style No',
        itemNo: 'Item No',
        poNo: 'PO No',
        articleNo: 'Article No',
        specialProduct: 'SKU No',
        createDate: 'TRF Create Date',
        completedTime: 'TRF Completed Date',
        reportIssuedTime: 'Report Issued Date',
        pendingReason: 'Pending Reason',
        buyerReviewConclusion: 'Review Conclusion',
        cancelSort:'Unsort',
        trfSourceType: 'Source Type',
        serviceTypeName:'Service Type'
    },

    trfStatus: {
        all: 'All',
        draft: 'Draft',
        submitted: 'Submitted',
        preparation: 'Application Accepted',
        testing: 'Testing',
        completed: 'Report Issued',
        cancel: 'Cancel',
        pending: 'Pending',
        confirmingQuotation: 'Quotation to be confirmed',
        confirmQuotation: 'Quotation Confirmed',
        rejectQuotation: 'Reject Quotation',
        reportIssued: 'Report Issued',
        applicationAccepted: 'Application Accepted',
        title: 'Quotation',
    },
    reportConclusion: {
        all: 'All',
        pass: 'Pass',
        fail: 'Fail',
        seeResult: 'See Result',
    },
    communicationLog: {
        title: 'Message ',
        comment: 'Content',
        comments: 'Content',
        commentBy: 'Send to',
        addComment: 'Add Comment',
        emailGroup: 'Email address group',
        addressBook: 'Recipient(s) from address book',
        pdfReport: 'Report PDF',
        validate: {
            comment: 'Please enter comment information',
        }
    },
    comment:{
        emailGroup:'Email Group',
        emailAddress:'Email Address',
        applicantVisible:'Visible To Applicant',
        comment: 'Comment',
        commentContent: 'Comment',
        copyComment: 'Copy Comment',
        addComment: 'Add Comment',
        viewAllComments: 'View All Comments',
        copied:'Copied From ',
        pdfReport:'Report PDF',
        commentName:'Comment',
        chatDialog:{
            tabComments:'Comments',
            tabAttachment:'Attachment',
            selectComment:'Select Comment',
            copy:'Copy',
            reply:'Reply',
            replayNotBuyer:'You are not able to comment if the review is Finalized.',
            send:'Send',
            attachTestReport:'Attach Test Report',
            notificationTo:'Notification To',
            visibleTo:'Visible To',
            internalOnly:'Internal',
            internalAndApplicants:'Internal and applicants',
            documentName:'Document Name',
            documentType:'Document Type',
            uploadedBy:'Uploaded By',
            date:'Create Date',
            action:'Action',
            attachmentIssueDate:'Issue Date',
            lengthOver10: 'The maximum number of email receipts is 10. Please adjust accordingly.'
        }
    },

    service: {
        contactEmail: 'Contact email',
        required: 'Required',
        notRequired: 'Not Required',
        yes: 'Yes',
        no: 'No',
        judgmentRule:{
            title:'Decision rules',
            rule1:'Legal, regulatory standard',
            rule2:'Applicable standard includes decision process',
            rule3:'w=0 (zero guard band)(Directly determine)',
            rule4:'Guard band: a.U guard band',
            rule5:'Guard band: b.Guard band producing≤2%PFA',
            rule6:'Other,Please specified',
            remark:'Unless laws and regulations or standards specified, if no specific requirement, lab choose simple acceptance (w=0) in stated in ILAC-G8:09/2019.'
        },
        reportSymbol:'Report with accreditation symbols',
        serviceType: 'Service Type',
        servicRequire: 'Other Request',
        comment: 'Conclusion Required',
        softCopy: 'Softcopy Required',
        hardCopy: 'Hardcopy Required',
        takePhoto: 'Photo Required',
        coverPageConfirmation: 'Cover Page Confirmation',
        quotation: 'Quotation Required',
        returnSample: 'Sample Return,If Request',
        accreditation: 'Accreditation Request',
        softCopyDeliverTo: 'SoftCopy Deliver To',
        softCopyDeliverToHolder: 'Select softcopy deliver to, please',
        softCopyDeliverOtherHolder: 'Input softcopy deliver to, please',
        softCopyDeliverOtherOverLength: 'Softcopy deliver to is too long',
        hardCopyDeliverTo: 'HardCopy Deliver To',
        hardCopyDeliverWay: 'HardCopy Report Deliver Way',
        hardCopyDeliverToHolder: 'Select hardcopy deliver to, please',
        hardCopyDeliverWayHolder:'Select hardcopy deliver way, please',
        hardCopyDeliverToOtherHolder: 'Input hardcopy deliver to, please',
        hardCopyDeliverWayOtherHolder: 'Input hardcopy deliver way, please',
        hardCopyDeliverToOtherOverLength: 'Hardcopy deliver to is too long',
        vatType: 'VAT Type',
        invoiceProforma: 'Proforma Invoice Required',
        invoiceDeliverTo: 'Invoice Deliver To',
        invoiceDeliverToHolder: 'Select invoice deliver to, please',
        invoiceDeliverToOtherHolder: 'Input invoice deliver to, please',
        invoiceDeliverWayOtherHolder: 'Input invoice deliver way, please',
        invoiceDeliverToOtherOverLength: 'Invoice deliver to is too long',
        invoiceDeliverWay: "Invoice Deliver Way",
        invoiceDeliverWayHolder: 'Select Invoice Deliver Way, please',
        returnSampleDeliverWay: 'Return Sample Way',
        returnSampleDeliverTo: 'Return Sample To',
        returnSampleDeliverToHolder: 'Select return sample deliver to, please',
        returnSampleDeliverToOtherHolder: 'Input return sample deliver to, please',
        returnSampleDeliverWayHolder: 'Select return sample Way, please',
        returnSampleDeliverWayOtherHolder: 'Input return sample deliver way, please',
        returnSampleDeliverToOtherOverLength: 'Return sample deliver to is too long',
        liquidInkSample: 'For liquid printing ink sample',
        acceptSelection: 'Whether to accept testing from subcontractor out side SGS (Accept if no selection)',
        legalProceeding: 'Is this application involved in legal proceeding? If yes, please specify the case',
        reportLanguage: 'Report Language',
        reportHeader: 'Report Header (If other than Applicant) (English)',
        reportHeaderEn: 'Report Header (If other than Applicant) (English)',
        reportHeaderZh: 'Report Header (If other than Applicant) (Chinese)',
        reportAddress: 'Report Address (English)',
        reportAddressEn: 'Report Address (English)',
        reportAddressZh: 'Report Address (Chinese)',
        otherRequest: 'Any additional requests',
        otherTip: 'Any additional request,please specify.e.g.Report Hardcopy needed',
        reportSuccessSend: 'Report Delivered To',
        reportErrorSend: 'Failed Report Delivered To',
        reportMethod: 'Reporting Method',
        reportConsistent: 'Report Title',
        reportType: 'Report Type',
        qualificationMark: 'Qualification Mark (CMA/CNAs)',
        isReportReflectCustomerInformation: 'Report Content',
        resultJudging: 'Comments needed?',
        judgementPrinciple: 'Evaluation Basis',
        reportReceivingEmail: 'Report Receiving Mailbox',
        reportRequire: 'Reporting Requirements',
        noAdd: 'Not Applicable',
        addCNAS: 'Add CNAS',
        addCMA: 'Add CMA',
        addCNASAndCMA: 'Add CNAS And CMA',
        electronic: 'Electronic Report (default)',
        paper: 'Paper report (RMB100/copy)',
        other: 'Other',
        productStandards: 'Type inspection report template needs to provide product standards',
        oneSample: 'One sample and one report',
        multipleSample: 'One report for multiple samples',
        chineseReport: 'Chinese report',
        enReport: 'English (please ensure that the English information is complete)',
        certificateRequired:'Certificate Required',
        COC:'COC',
        GPC:'GPC',
        CPC:'CPC'
    },
    customer: {
        //new 14new
        new:{
            payer:'Payer ',
            buyer:'Buyer ',
            agent:'Agent ',
            supplier:'Supplier ',
            manufacturer:'Manufacturer ',
            customerNo:'SGS Customer No.',
            customerReferenceNo:'Customer Code',
            companyNameEn:'Company Name ( English )',
            companyNameCn:'Company Name ( Local Language )',
            companyAddressEn:'Company Address ( English )',
            companyAddressCn:'Company Address ( Local Language )',
            manufacturerT2:'The Manufacturers related with Buyer',
            manufacturerT1:'My Manufacturers '
        },
        title: {
            base: 'Company Information',
            CompanyBase: 'Company Information',
            admin: 'User Information',
            thirdLab: '3rd party Lab',
            other: 'Other Information',
            sgsSettings: 'SGS Setting',
            approve: 'Customer Approval',
            default: 'Customer',
            tab: 'Customer',
            add: 'Add Customer',
            edit: 'Edit Customer',
            relationshipNeedApprove: 'Related buyer application has been submitted, please contact the buyer for review!',
            ViewLargeImage: 'View Large Image'
        },
        name: 'Company Name',
        companyNameCn: 'Company Name ( Chinese )',
        companyNameEn: 'Company Name ( English )',
        payNameEn: 'Payer Name ( English )',
        payNameCn: 'Payer Name ( Local Language )',
        payNameNew1: 'Invoice Header ( if different from the applicant, please fill in )',
        payAddressEn: 'Payer Address ( English )',
        payAddressCn: 'Payer Address ( Local Language )',
        nameZh: 'Chinese Name',
        nameEn: 'English Name',
        address: 'Company Address',
        addressZh: 'Chinese Address',
        addressEn: 'English Address',
        addressBlur: 'Please enter the address',
        status: {
            todo: 'To Be Audited',
            emailConfirm: 'Mail Confirmation',
            approved: 'Audited',
            refused: 'Rejected'
        },
        authenticationStatus: 'Authentication Status',
        approveStatus: {
            todo: 'To be certified',
            emailConfirm: 'Not certified',
            approved: 'Certified',
            refused: 'Authentication denied'
        },
        editProductCategory: 'Modify Product Category',
        editServiceUnit:'Modify Product Category',
        approveStatusErrMsg: 'Approved Customer cannot modify product category',
        rejectionReasons: 'Reasons for rejection',
        sgs: {
            customerNo: 'SGS Customer No.',
            reportNo: 'SGS Report No.',
        },
        taxNo: 'Tax No.',
        certificate: 'Certificate',
        certificateNew: 'Business License',
        certificateUpload: "Not Provided",
        contactMobile: 'Phone Number',
        relationship: {
            title: {
                default: 'My Client',
                tab: 'Relationship',
                tabBuyer: 'Buyers',
                buyerName: 'Buyer name',
                buyerCode: 'Buyer code',
                tabSupplier: 'Suppliers',
                add: 'Add relationship',
                edit: 'Edit relationship',
                scope: 'Relationship scope',// 14new
            },
        },
        joinBuyerProgram: 'Join the buyer program',
        customerRelationshipNeedApprove: 'Relationship need approve',
        error: {
            buyerNotCustomerGroup: 'The buyer has no customer group information'
        },
        changeProductCatoryMsg: 'You may not be able to view the account for approval, continue?',
        //selectModel 14new
        selectModel:[
            {
                label:"Group",
                value:"1"
            },
            {
                label:"Customer",
                value:"2"
            }
        ]
    },
    buyer: {
        title: {
            default: 'Buyer',
            tab: 'Buyer',
            add: 'Add Buyer',
            edit: 'Edit Buyer',
        },
        label: 'Buyer',
        name: 'Buyer',
        customerReferenceCode:"Buyer Code",//14new
        customerGroupCode:"Customer Group Code",//14new
        customerName: "Buyer Name",//14new
        selectMeg:'Please Select Buyer',//14new
        placeholder: {
            name: 'Please enter Buyer name',
        },
        customerGroupTip: 'The Buyers with * are SGS SMART on board, if you apply for their official testing, test status and report will be updated to the buyers.',
    },
    sgs: {
        sgsCustomerNo: "SGS Code"
    },
    agent: {
        title: {
            default: 'Agent',
            tab: 'Agent',
            add: 'Add Agent',
            edit: 'Edit Agent',
        },
        label: 'Agent',
        name: 'Agent',
        placeholder: {
            name: 'Please enter Agent name',
        },
    },
    supplier: {
        title: {
            default: 'Supplier',
            tab: 'Supplier',
            add: 'Add Supplier',
            edit: 'Edit Supplier',
        },
        label: 'Supplier',
        name: 'Supplier',
        customerReferenceCode:"Supplier Code",//14new
        selectMeg:'Please Select Supplier',//14new
        selectMeg1:'SGS code can not be null,Please Select supplier angin',
        customerName: "Supplier Name",//14new
        customerNameCN: "Supplier Name (CN)",
        customerNameEN: "Supplier Name (EN)",
        nameFull: 'Supplier nNme',
        nameFullCN: 'Supplier Name (CN)',
        nameFullEN: 'Supplier Name (EN)',
        supplierCode: 'Supplier Code',
        tier: 'Tier',
        productCategory: 'Division',
        department: 'Managed By (Department)',
        placeholder: {
            name: 'Please select supplier',
        },
        validate: {
            nameBlur: 'Please select supplier',
        }
    },
    productLine: {
        code: 'Product Line No.',
        name: 'Product Line Name',
        placeholder: 'Please select productline',
    },
    customerGroup: {
        code: 'Customer No.',
        name: 'Customer Group Name',
        placeholder: 'Please select customer',
    },
    agentCustomerGroup: {
        code: 'Agent No.',
        name: 'Agent Name',
        placeholder: 'Please select agent',
    },
    address: {
        title: {
            default: 'Address',
            tab: 'Address',
            add: 'Add Address',
            edit: 'Edit Address',
        },
        detail: 'Address',
        zh: 'Chinese Address',
        en: 'English Address',
        validate: {
            addressBlur: 'Please enter the address',
        }
    },
    account: {
        title: {
            default: 'Account',
            tab: 'Account',
            add: 'Add Account',
            edit: 'Edit Account',
            approve: 'Approve Account',
        },
        management: 'Account Management',
        approveTitle: 'Account Approval',
        account: 'Account',
        code: 'Login Account',
        userName: 'User Name',
        email: 'Email',
        mobile: 'Mobile',
        password: 'Password',
        passwordConfirm: 'Password Confirm',
        post: 'Access Role',
        validate: {
            accountBlur: 'Please enter account',
            passwordBlur: 'Please enter password',
            emailBlur: 'Please enter email',
            mobileBlur: 'Please enter contact information',
            userNameBlur: 'Please enter employee name',
            suplierNameBlur: 'Please select supplier',
            departmentBlur: 'Please select department',
            postBlur: 'Please select access role',
            opinionBlur: 'Please input opinion',
            serviceUnitBlur:'Please select product category ',
        },
        userManage: 'Account',
        suplierManage: 'Supplier',
        buyManage: 'Buyer',
        suplierName: 'Supplier',
        customerName: 'Customer Name',
        buyName: 'Buyer Name',
        approveStatus: {
            stayEmailConfirm: 'Waiting for email confirmation',
            toBeReviewed: 'To be Reviewed',
            emailConfirm: 'Mail Confirmation',
            auditPass: "Pass"
        },
        noDeptMessage: 'Please go to Me – Company management – Department and add department for your company.',
        editServiceUnitMessage: 'The product category will take effect after you log in again.',
    },
    vipProgram: {
        title: {
            default: 'VIP Program',
            tab: 'VIP Program',
            add: 'Add VIP Program',
            edit: 'Edit VIP Program',
        },
        name: 'Program Name',
        layoutType: 'Layout',
        validate: {
            nameBlur: 'Please enter program name',
        },
        showWhichMenu: 'Show on Which Menu'
    },
    authorization: {
        title: {
            default: 'Authorization',
            tab: 'Authorization',
            add: 'Add Authorization',
            edit: 'Edit Authorization',
        },
        selected: 'Select Date',
        authManage: 'Document Auth',
        noSelected: 'No authorization information selected please check!',
        validate:{
            customer:'If the \'customer\' has no data, it means that the document is open to all users under the product line',
            date:'If the \'effective date\' has no data, it means that the document is valid for a long time',
        },
        authorValid: 'Authority is no longer existed',
        visibleOnly: 'Visible Only',
        visibleOnlyTip: 'If you want to know more details, please contact with SGS account host.'
    },
    emailGroup: {
        title: {
            default: 'Mail Group',
            tab: 'Mail Group',
            add: 'Add Mail Group',
            edit: 'Edit Mail Group',
        },
        name: 'Name',
        nameFull: 'Mail Group Name',
        member: 'Members',
        memberFull: 'Mail Group Members',
        validate: {
            nameBlur: 'Please enter a mail group name',
            contactBlur: 'Please select a contact person',
        }

    },
    department: {
        title: {
            default: 'Department',
            tab: 'Department',
            add: 'Add Department',
            edit: 'Edit Department',
        },
        label: 'Department',
        name: 'Name',
        nameFull: 'Department Name',
        nameBlur: 'Please enter department name',
    },
    notifaction: {
        title: {
            default: 'Notification Settings',
            tab: 'Notification Settings',
            add: 'Add Notification',
            edit: 'Edit Notification',
        },
        selectAll:'Select All',
        trfStatusName:'TRF Status',
        tickedUpdated: 'When settings are changed, you will receive email notification of the changes.',
        finalReportAttached: 'Report Issued',
        allCommunicationLog: 'Message Received',
        confirmSampleReceived: 'Sample Received',
        confirmSampleReceivedValidate: 'Sample Received',
        trfStatusChange: 'Any Updates',
        pass: 'Pass',
        fail: 'Fail',
        seeResult: 'See Result',
        general: 'Notify Me When:',
        overAllResult: 'Notification of:',
        wechat:{
            title:'Wechat Notification',
            isWechatPush:'Enable Wechat Push',
            quotationConfirm:'Reminder of quotation to be confirmed',
            general: 'TRF Status Update Reminder:',
            reportReceiving:'Receive Report Reminder',
            noBindWechat:'The system detects that you have not bound wechat yet. You can enable this setting after binding wechat.',

        },
        email:{
            title:'Email Notification',
            isEmailPush:'Turn On Email Notification',
        },
        reportCompletedTips: 'When the test report is completed in SGS SMART, email notification will be sent to the contact specified in the TRF',
        by: 'When the test report is done in SGS SMART, and conclusion is fail, email notification will be sent to the contact of ‘Failed Report Delivered to’ on TRF',
        quotation: 'Quotation Notice'
    },
    feedback: {
        title: {
            default: 'Feedback',
        },
        validate: {
            nameBlur: 'Please enter your name',
            emailBlur: 'Please enter email address',
            emailWaring: 'Incorrect email address',
            phoneBlur: 'Please enter your phone number',
            contentBlur: 'Please enter the message content',
        },
        content: 'Feedback content',
    },
    sms: {
        sendCode: 'Send Code',
        code: 'SMS Code',
        smsCodeValidateError: 'SMS verification code verification failed, please resend',
    },
    contact: {
        title: {
            default: 'Contact',
            tab: 'Contact',
            add: 'Add Contact',
            edit: 'Edit Contact',
        },
        contactInfo: 'Contact Information',
        name: 'Name',
        nameFull: 'Contact Name',
        email: 'Email',
        emailFull: 'Contact Email',
        phone: 'Phone No.',
        phoneFull: 'Contact phone No.',
        fax: 'Fax',
        content: 'Feedback Content',
        placeholder: {
            name: 'Please enter contact name',
        },
        validate: {
            phonelBlur: 'Currently, only mobile numbers in mainland China are supported',
            emailBlur: 'Please enter email address',
            phoneValidate: 'Please enter the phone number',
            emailWaring: 'Please enter the correct email address',
            nameBlur: 'Please enter contact',
        }
    },
    factory: {
        title: {
            tab: 'My Factory',
            add: 'Add Factory',
            edit: 'Edit Factory'
        },
        name: 'Factory Details',
        customerReferenceCode: 'Factory Code',//14new
        customerName: 'Factory Name',
        factoryCode: 'Factory Code',
        factoryName: 'Factory Name',
        validate: {
            factoryCodeBlur: 'Please enter Factory Code',
            factoryNameBlur: 'Please enter Factory Name',
        }
    },
    //manufacturer 14new
    manufacturer: {
        title: {
            label:'Manufacturer',
            tab: 'My Manufacturer',
            add:'Add Manufacturer',
            edit:'Edit Manufacturer'
        },
        name: 'Manufacturer Manage',
        customerReferenceCode: 'Manufacturer Code',
        customerName: 'Manufacturer Name',
        customerNameCN: 'Manufacturer Name (CN)',
        customerNameEN: 'Manufacturer Name (EN)',
        selectMeg:'Please select manufacturer',
        selectMeg1:'SGS code can not be null,Please select manufacturer again',
        validate: {
            factoryCodeBlur: 'Please enter manufacturer code',
            factoryNameBlur: 'Please enter manufacturer name',
        }
    },
    dateValidate: {
        startDateValidate: 'Please select start time',
        endDateValidate: 'Please select the end time',
        betweenDateValidate: 'The time interval cannot be greater than one year',
        endDateErrorValidate: 'The end time cannot be greater than the start time',
        startDateAndEndDateValidate: 'If no search is entered, please select the filter time interval, and the maximum date range is 1 year',
        startDateAndEndDateValidate1: 'Please select the filter time interval, and the maximum date range is 1 year',
        companyNameValidate: 'Please enter the company name to search',
        betweenDateValidateOneYear: 'Maximum allowable query of data in recent year',
        startMaxError:'Only data after January 1, 2022 can be queried',
        dueDataAndSmapleDataValiMsg:'Expected due day cannot be earlier than sample received date.',
    },
    beLongTo: {
        title: 'Belong To',
        name: 'Belong Name',
        add: 'Add Belong',
        validate:{
            name:'Please enter the Belong name',
        },
    },
    report: {
        title: 'Report',
        conclusion: 'Overall Conclusion',
        testStartDate: 'Service Start Date',
        reportIssuedDate: 'Report Issued Date',
        reviewConclusion: 'Review Conclusion',
        reviewProgress: 'Review Progress',
        marketSegment:'Market Segment',
        originalReportNo:'Original Report Number',
        reportVersion:'Report Version',
        serviceStartDate:'Service Start Date',
        serviceEndDate:'Service End Date',
        rslStatus:'RSL Status',
        remark:'Solution Description',
        reviewConclusionStatus_0: 'Not Reviewed',
        reviewConclusionStatus_1: 'Reviewed',
        reviewConclusionStatus_2: 'Submitted',
        reviewConclusionStatus_3: 'Finalize Review',
        reportPDF: 'Report PDF',
        reportDetail: 'Report Detail',
        testLine: {
            categoryCode : 'Category Code',
            pp:'Protocol / Package',
            citation : 'Citation',
            sampleNo:'Sample No',
            testName: 'Test Name',
            testResult: 'Test Result',
            resultReview: 'Result Review',
            component: 'Component',
            color: 'Color',
            remark: 'Remark',
        }
    },
    guide: {
        title: 'Welcome to SGS SMART',
        subTitle: "Before we start, let's pre-set together",
        status: {
            start: '',
            endHead: ' of ',
            endFooter: ' Tasks completed'
        },
        newTask: 'New user guide',
        func: 'Guidance',
        task: {
            task1: {
                title: 'Add Buyer',
                tip: 'Create linkage with buyer before test application'
            },
            task2: {
                title: 'Add Contact',
                tip: 'Add contact, which can be used in new application'
            },
            task3: {
                title: 'Add Address',
                tip: 'Add address, which can be used in new application '
            },
            task4: {
                title: 'Notification setting',
                tip: 'When ticked status is updated, you will receive email notification'
            }
        },
        prev: 'Prev',
        next: 'Next',
        learned: 'Understood',
        step1: 'Step 1 of ',
        step2: 'Step 2 of ',
        step3: 'Step 3 of ',
        step4: 'Step 4 of ',
        step5: 'Step 5 of ',
        step6: 'Step 6 of ',
        step7: 'Step 7 of ',
        total3: '3',
        total4: '4',
        total5: '5',
        total6: '6',
        total7: '7',
        clickCompanyManage: 'Click Company management in settings',
        selectClient: 'Select My Client',
        clickAdd: 'Click Add',
        inputKeyword: 'Input Key words of buyer name, and select in drop down list',
        clickSubmit: 'Click Submit',
        clickContact: 'Click Contact',
        inputCOntactInfo: 'Input Name, Phone No. and Email',
        clickAddress: 'Click Address',
        inputAddress: 'Input Address',
        ClickNotification: 'Click Notification Settings',
        reportIssued: 'Click Report Issued',
        clickPass: 'Select',
        addBuyerSuccess: 'Add buyer successfully',
        addContactSuccess: 'Add contact successfully',
        addAddressSuccess: 'Add address successfully',
        notificationSuccess: 'Successful notification setting',
        completeTasl: 'Well done, all tasks completed!',
        autoClose: {
            start: 'Closed in ',
            end: ' Seconds'
        },
        // 功能引导
        nav: 'Navigation Bar is here',
        navTxt: 'Click tabs in Navigation Bar to go to the relevant page',
        trf: 'Here are the latest applications',
        trfTxt: 'When status changed, Applications are shown here',
        createTrf: 'Create new Application here',
        createTrfTxt: 'Click the button to create new application',
        trfStatus: 'Application Status ',
        trfStatusTxt: 'Here is Application status, you can set time range, and sort applications in different status ',
        testResult: 'Check Test Result',
        testResultTxt: 'Test result can be reviewed here, time range can be set, and you can click pie chart to go to the relevant page. ',
        switchLang: 'Change language ',
        switchLangTxt: 'Here English and Chinese can be switched',
        viewAgain: 'Play Again',
        viewAgainTxt: 'Click guide to see the guidance',
        trfCreate: 'Create new Application',
        trfCreateTxt: 'Click the button to create new Application',
        filterTrf: 'Sort Applications',
        filterTrfTxt: 'Sort Applications by time range and key word',
        locationTrf: 'Locate Application',
        locationTrfTxt: 'Sort and locate application',
        trfDetail: 'See Details',
        trfDetailTxt: 'Click TRF No. to check details of application',
        modify: 'Edit',
        modifyTxt: 'Click here to edit buyer and template info',
        tabSwitch: 'Application sections',
        tabSwitchTxt: 'Click to see other tabs ',
        quickBar: 'Quick Edit',
        quickBarTxt: 'Click and go to the relevant section',
        contentFill: 'Input Info',
        contentFillTxt: 'Input Application Form Info',
        process: 'Complete Status ',
        processTxt: 'It shows Complete status of Application form',
        continue: 'Next',
        continueTxt: 'Click to continue Application',
        actionBar: 'Action Bar',
        actionBarTxt: 'Use the Action Bar to Proceed',
        reviewAgain: 'Review Again',
        reviewAgainTxt: 'Click here to see the guidance again',
    },
    powerbi:{
        viewReport:'View report'
    },
    dataAnalytics: {
        count: 'Count',
        totalTest: 'Total Test',
        noOfJob: 'No. of Job',
        overall: 'Overall Performance',
        pass: 'Pass',
        fail: 'Fail',
        seeResult: 'See Result',
        overallTestByCountry: 'Overall Pass/Fail Performance Summary',
        overallChemicalTestPerf: 'Overall Chemical Test Performance',
        overallPhysicalTestPerf: 'Overall Physical Test Performance',
        chemicalTestPerformance: 'Chemical Test Performance',
        chemicalTestPerformanceByCountry: 'Chemical Test Performance by Country',
        physicalTestPerformance: 'Physical Test Performance',
        physicalTestPerformanceByCountry: 'Physical Test Performance by Country',
        supplier: {
            title: 'Supplier',
            supplierTestLine:'Supplier Test Line',
            overall: 'Supplier Performance (Overall)',
            supplierOverallByCountry: 'Supplier Performance (Overall) by Country',
            supplierChemicalPerf: 'Supplier Performance (Chemical)',
            supplierChemicalPerfByCountry: 'Supplier Performance (Chemical) by Country',
            supplierPhysicalPerf: 'Supplier Performance (Physical)',
            supplierPhysicalPerfByCountry: 'Supplier Performance (Physical) by Country',
        }
    },
    agreement:{
        agreeAndContinue:'Agree and continue',
        privacyPolicy:'SGS SMART Privacy Notice',
        useConditions:'TERMS AND CONDITIONS OF USE',
        head:'为了更好地为您提供服务，请您仔细阅读这份协议，理解认同后再进行注册。本协议是您与通标标准技术服务有限公司（以下均简称“通标公司”）就您注册、登录本平台及使用等所涉及的全部行为所订立的权利义务规范。您在注册过程中点击“同意”等按钮、及注册后登录和使用时，均表明您已完全充分理解、同意并接受本协议，愿意遵守本协议及的各项规则、规范的全部内容，若不同意则可停止注册、登录或使用平台。',
        definition:'一、定义',
        one_1:'1.1 您：指提交有效申请并注册后，在本平台登录、上传、发布、提供链接等以各种形式传播内容（包括文字、图片、音频、视频、图表、漫画等）的自然人、法人或其他组织。',
        one_2:'1.2 平台：是通标标准技术服务有限公司设立的销售服务网站，其域名为www.sgsonline.com.cn，运营方是通标标准技术服务有限公司。',
        accountRegisterAndUse_2:'二、帐户注册和使用',
        two_1:'2.1 在注册、使用和管理平台帐户时，请您使用真实、准确、合法、有效的相关身份证明材料及必要信息（包括您的姓名及电子邮件地址、联系电话、联系地址等），以便通标公司在必要时与您联系，并注意及时更新。为使您更好地使用平台的各项服务，请您按照相关法律规定及平台要求完成实名认证。您应当对您提供的帐号资料的真实性、合法性、准确性和有效性独立承担责任。如因此给平台公司或第三方造成损害的，您应当依法予以赔偿。',
        two_2:'2.2 为保障用户和公司利益，平台公司有权核查您提交的相关材料（如自然人身份证复印件、企业法人营业执照副本复印件、事业单位法人证书复印件、公司官方声明/说明等）后再决定是否核准您的注册申请。若您提交的材料或填写的信息不完整或不准确，则您可能无法使用本服务或在使用过程中受到限制。',
        two_3:'2.3 您所设置的帐户名不得违反国家法律法规及平台规则关于帐户名的管理规定，否则通标公司可对您的帐户名进行暂停使用或注销等处理，并向主管机关报告。',
        two_4:'2.4 您理解并承诺，您的帐户名称、头像和简介等注册信息中不得出现违法和不良信息，没有冒用、关联机构或社会名人，您在帐户注册过程中需遵守法律法规、社会主义制度、国家利益、公民合法权益、公共秩序、社会道德风尚和信息真实性等七条底线。',
        two_5:'2.5 您同意并授权，为了更好的为您提供服务以及确保您的帐户安全，通标公司可以根据您提供的手机号码、身份证号码等信息，向全国公民身份号码查询服务中心、电信运营商、金融服务机构等可靠单位发起用户身份真实性、用户征信记录、用户手机号码有效性状态等情况的查询。',
        two_6:'2.6 平台帐号的所有权归通标公司所有，您注册申请通过后，您将拥有平台帐号的使用权，可以登录平台使用平台的服务。平台帐号的所有权归通标公司所有，公司有权因经营需要收回帐号。同时，禁止任何赠与、借用、租用、转让或售卖帐号等的行为。如您违反本协议内容，通标公司有权对该帐户进行暂停使用、注销或停止提供服务等处理，且不承担任何法律责任，由此导致的包括并不限于您通讯中断、资料和虚拟道具等清空等损失由您自行承担。',
        userInfoProtection_3:'三、用户个人信息保护',
        three_1:'3.1 为了更好地为您提供服务和帮助、保护您的合法权益，请您保证申请服务时所提供的信息是真实、准确、合法、有效的，并注意及时更新，以免在使用过程中受到限制或无法使用。',
        three_2:'3.2 通标公司将保护用户个人信息作为公司发展的最基本原则之一，未经您的同意，不会向其他任何公司、组织或个人披露您的个人信息，法律法规另有规定的除外。',
        three_3:'3.3 请您在使用通标平台的过程中，不要以搜集、复制、存储、传播等任何方式使用其他用户的个人信息，否则，由此产生的后果需您自行承担。',
        rightsAndObligations_4:'四、您的权利和义务',
        four_1:'4.1 您需要对注册和使用时提交的信息及材料真实性、准确性、合法性、有效性负责，如因此引起的问题，由您承担全部法律责任。',
        four_2:'4.2 请您妥善保管您的帐户信息，并对此帐户下发生的一切活动承担全部法律责任。不向任何第三方透露帐户或密码信息，如出现或怀疑帐号和密码遭到他人使用，请尽快通知通标公司，以免您的利益受到损失。',
        four_3:'4.3 请您保证对在平台制作、复制、上传、发布、传播的任何内容享有合法权益，若您发布的内容发生权利纠纷或侵犯了任何第三方的合法权益，需您承担全部法律责任。',
        four_4:'4.4 请您遵守本协议的各项条款，并正确、适当地使用、运营、管理此平台账号，如您违反本协议中的任何条款，通标公司有权在任何时候依据本协议中止或终止对您提供服务。',
        four_5:'4.5 如您注册账号后连续二十四个月不登录该帐号，为避免资源浪费，通标公司有权收回该帐号，因此带来的损失将由您自行承担。',
        four_6:'4.6 您的言行应遵守《计算机信息网络国际联网安全保护管理办法》、《互联网信息服务管理办法》、《互联网电子公告服务管理规定》、《维护互联网安全的决定》等相关法律规定，您不能利用帐户制作、复制、上传、发布、传播任何与如下要求相悖的内容（该内容是指您使用平台过程中所制作、复制、上传、发布、传播的任何内容，包括但不限于帐户头像、名称、用户说明、注册信息及其他资料，或文字、语音、图片、视频、图文、图表、漫画等发送、回复消息、评论和相关链接页面，以及其他使用帐户平台服务所产生的内容）：',
        four_6_1:'(1) 反对宪法所确定的基本原则的；',
        four_6_2:'(2) 危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；',
        four_6_3:'(3) 损害国家荣誉和利益的；',
        four_6_4:'(4) 煽动民族仇恨、民族歧视，破坏民族团结的；',
        four_6_5:'(5) 破坏国家宗教政策，宣扬邪教和封建迷信的；',
        four_6_6:'(6) 散布谣言，扰乱社会秩序，破坏社会稳定的；',
        four_6_7:'(7) 散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；',
        four_6_8:'(8) 侮辱或者诽谤他人，侵害他人合法权益的；',
        four_6_9:'(9) 含有法律、法规和政策禁止的其他内容的信息。',
        four_7:'4.7 为保证的正常运营及用户的良好体验，请您不要利用平台制作、复制、上传、发布、传播如下内容',
        four_7_1:'(1) 含有任何性或性暗示以及任何其他低俗类信息；',
        four_7_2:'(2) 骚扰、垃圾广告；',
        four_7_3:'(3) 涉及他人隐私、个人信息或资料的任何信息；',
        four_7_4:'(4) 侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利的任何信息；',
        four_7_5:'(5) 含有其他干扰正常运营、侵犯其他用户或其他第三方合法权益内容的信息。',
        four_8:'4.8 为确保通标公司和用户的利益，您请在使用本平台时，不要进行如下行为（该行为是指使用帐户平台所进行的任何行为，包括但不限于注册登录、帐号运营、管理及推广以及其他行为）：',
        four_8_1:'(1) 提交、发布虚假信息，或冒充、利用他人名义进行相关活动；',
        four_8_2:'(2) 强制、诱导其他用户关注、点击链接页面或分享信息；',
        four_8_3:'(3) 虚构事实、隐瞒真相以误导、欺骗他人；',
        four_8_4:'(4) 侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利；',
        four_8_5:'(5) 未经通标公司书面许可使用插件、外挂或其他第三方工具、服务接入本服务和相关系统；',
        four_8_6:'(6) 利用平台及帐户从事违法犯罪活动；',
        four_8_7:'(7) 制作、发布与以上行为相关的方法、工具，或对此类方法、工具进行运营或传播；',
        four_8_8:'(8) 其他违反法律法规规定、侵犯其他用户合法权益、干扰产品正常运营或未经通标公司明示授权的行为。',
        four_9:'4.9 请您在任何情况下都不要私自使用通标公司的包括但不限于“SGS”在内的任何商标、服务标记、商号、域名、网站名称或其他显著品牌特征等。未经通标公司事先书面同意，您不得将本平台标识以任何方式展示、使用或申请注册商标、进行域名注册等，也不得实施向他人明示或暗示有权展示、使用、或其他有权处理本平台标识的行为。您由于非法使用本平台标识给本平台或他人造成损失的，由您承担相关法律责任',
        four_10:'4.10 若您需对本平台内容创作衍生品或投放商业广告，请您另外提交书面授权申请，在符合条件且得到通标公司同意下，您方可通过该平台进行广告或推广等商业活动。',
        sgsRightsAndDuties_5:'五、通标公司的权利和义务',
        five_1:'5.1 为保障用户和公司的利益，通标公司有权对您注册时提交的材料和信息进行审查，并有权要求您改正或补充相关材料，请您理解。如果您拒绝改正或补充相关材料，您可能无法使用本服务。通标公司的审查不代表对您提交的材料和信息的真实性、准确性、真实性、合法性负责。您应当对该材料和信息独立承担责任，如因此给通标公司或第三方造成损害的， 您应当承担法律责任并予以赔偿。',
        five_2:'5.2 通标公司为平台的开发、运营提供技术支持，并对该平台的开发和运营等过程中产生的所有数据和信息等享有全部权利。',
        five_3:'5.3 如果您停止使用本服务或服务被终止或取消，通标公司有权自主决定是否从服务器上永久地删除您的数据且无需向您返还任何数据。',
        five_4:'5.4 通标公司保留随时变更、暂停、限制、终止或撤销平台服务的权利。公司可通过网页公告、电子邮件、电话或信件传送等方式向您发出通知，通知在发送时即视为已送达收件人，届时公司无需向您承担任何责任。\n',
        five_5:'5.5 您充分理解并同意：本服务中可能包括通标公司针对个人或企业推出的信息发布或品牌推广服务，您同意通标公司有权在本平台显示平台和/或第三方供应商、合作伙伴的商业广告或商业信息。',
        intellectualProperty_6:'六、知识产权',
        six_1:'6.1 在本服务中，由您通过www.sgsonline.com.cn平台上传、发布的任何内容的知识产权归属您或原始版权人所有，以上内容您授权通标公司使用并授权通标公司对侵犯以上内容版权的行为进行维权。通标公司在本服务中提供的内容（包括但不限于网页、文字、图片、音频、视频、图表等）的知识产权属于通标公司所有。通标公司提供本服务时所依托的软件的著作权、专利权及其他知识产权均归通标公司所有。',
        six_2:'6.2 您应当是在注册资料中提交的网站的合法权利人。本协议的合作范围是您提交网站的全部内容，除非您另有明确表示，您在注册时点击同意，即表明您同意授权通标公司收录、链接您网站中的全部内容，并通过系统以您的注册帐户自动发布。如您对授权范围另有需求可以书面方式通知通标公司并另行签订授权协议。',
        six_3:'6.3 您理解并且同意，为持续改善通标为您提供的各项服务，您授予通标公司及其关联方、合作方对您上传发布的任何内容具有全世界范围内的、永久的、不可撤销的、免费的、非独家的使用权。',
        six_4:'6.4 本服务所包含的内容的知识产权均受到法律保护，未经通标公司、用户或相关权利人书面许可，任何人不得以任何形式进行使用或创造相关衍生作品。',
        privacy_7:'七、隐私政策',
        seven_1:'7.1 用户知悉并同意：个人隐私信息是指能够对用户进行个人辨识或涉及个人通信的信息，包括用户真实姓名、身份证号、手机号码、银行账户、IP地址等。非个人隐私信息是指用户对本服务的操作状态以及使用习惯等一些明确且客观反映在本平台服务器端的基本记录信息和其他一切个人隐私信息范围外的普通信息，以及用户同意公开的上述隐私信息。',
        seven_2:'7.2 因您使用平台不同服务内容时，为保证功能服务的完整体验，产品可能会收集到您的地理位置、读取您的通讯录、开启您使用工具的摄像头、话筒，如您不希望开启相关功能，可停止使用对应服务，通标公司不会开启与用户使用的服务无关的功能。',
        seven_3:'7.3 本平台不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本网站的非公开内容，但下列情况除外：',
        seven_3_1:'(1)事先获得用户的明确授权；',
        seven_3_2:'(2)根据有关的法律法规要求；',
        seven_3_3:'(3)按照相关政府主管部门的要求；',
        seven_3_4:'(4)该第三方同意承担与本平台同等的保护用户隐私的责任。',
        seven_4:'7.4 在不透露单个用户隐私资料的前提下，本平台有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。',
        seven_5:'7.5 为了运营和改善www.sgsonline.com.cn平台的技术和服务，便于本平台向您及用户提供更好的体验和提高服务质量，通标公司将可能会自行收集使用或向第三方提供您的非个人隐私信息。\n',
        legalResponsibility_8:'八、法律责任',
        eight_1:'8.1 若您提交的注册信息和材料不真实、不完整、不合法或无效，那么导致或产生的一切法律责任由您承担。通标公司有权随时封禁或删除您的平台帐号，以及中止或终止为您提供平台的相关服务。',
        eight_2:'8.2 您理解并认可，本平台为提供信息分享、传播及获取的平台，您在使用本平台时，请您自行对内容加以判断，并承担因使用内容而引起的所有风险。您须为自己注册帐户下的一切行为负责，包括您所发表内容的真实性、合法性、准确性、有效性，以及承担因账号使用、运营、管理行为产生的结果。您应对平台中的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容真实性、合法性、准确性、有效性的依赖而产生的风险。通标公司无法且不会对因用户行为而导致的损失或损害承担责任。 如果您发现任何人违反本协议规定或以其他不当的方式使用平台服务，请立即举报或投诉，我们将依法进行处理。',
        eight_3:'8.3 对违反有关法律法规或本协议规定的行为，通标公司将依法律规定及上述规则等加以合理判断进行处理，对违法违规的任何人士采取适当的法律行动，并依据法律法规保存有关信息并向有关部门报告等。',
        eight_4:'8.4 若您上传、发布的内容或其他在www.sgsonline.com.cn平台上从事的行为侵害他人利益并引发第三方的任何索赔、要求或赔偿的，需由您承担全部法律责任。若因此给通标公司或第三方造成任何损失，您应负责赔偿并使之免受损害，损失包括但不限于诉讼费用、律师费用、和解费用、罚款或生效法律文书中规定的损害赔偿金额及其他直接或间接支出费用。',
        eight_5:'8.5 若通标公司发现您不当使用本平台帐号或因您的帐号被他人举报投诉时，通标公司有权不经通知随时删除相关内容，并视行为情节对违规帐号进行处理，处理方式包括但不限于警告、删除部分或全部订阅用户、限制或禁止使用全部或部分功能、帐号封禁甚至注销，并有权视具体情况而公告处理结果。',
        eight_6:'8.6 因技术故障等不可抗事件影响到服务的正常运行的，本平台及其合作单位承诺在第一时间内与相关单位配合，及时处理进行修复，但您因第三方如电信部门的通讯线路故障、技术问题、网络、电脑故障、系统不稳定性及其他各种不可抗力原因而遭受的一切损失，本平台及其合作单位不承担责任。',
        otherAgreements_9:'九、其他约定',
        nine_1:'9.1 您使用本服务即视为您已阅读并同意受本协议的约束。',
        nine_2:'9.2 必要时通标公司会对本协议的部分内容进行修改。修改后，将在页面显著位置提示协议有更新，您应及时查看更新后的协议。如果您同意接受修改后的协议，您可以继续使用平台；如果您不接受则应停止使用www.sgsonline.com.cn平台服务',
        nine_3:'9.3 您和通标公司均是独立的主体，在任何情况下本协议不构成双方之间的代理、合伙、合营或雇佣关系。',
        nine_4:'9.4 本协议的成立、生效、履行、解释及纠纷解决，都适用于中华人民共和国的法律。本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。',
        nine_5:'9.5 如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决。协商不成时，诉讼管辖权归属本合同签订地法院。',
        nine_6:'9.6 本协议签订地为中华人民共和国上海市徐汇区。',
    },
    extendData:{
        title:'',
        specificCode:'Code',
        specificNameEn:'Extend Menu',
        specificNameCn:'Extend Menu(CN)',
        contentEn:'Content (EN)',
        contentCn:'Content (CN)',
        template:'TRF Template',
        isMenu:'Display in Menu',
        selTemplate:'Please select a template',
        specificCodeBlur:'Please input specificCode',
        specificNameEnBlur:'Please input extend Menu',
        contentEnBlur:'Please input content (EN)',
        newTitle:'New',
        editTitle:'Edit'
    },
    aflTemplate:{
        management:{
            addTemplate:'Add template',
            delTemplate:'Delete template',
        }
    },
    settings: {
        componentsList: 'Components list',
        selectPage:'Please select page',
        lock: 'Denied edit',
        unLock: 'Allow edit',
        error: {
            noBuCode: 'Sorry, please select BU.',
            noPageCode: 'Sorry, please select page'
        },
        pages: [{
            pageName: 'Home page',
            pageCode: 'client_home_page'
        }]
    },
    SEComment:{
        eq:'＝',
        notEq:'≠',
        in:"In",
        notIn:'Not In',
        isBlank:"Is Blank",
        isNotBlank:"Is Not Blank",
        columnTitle:'Display Settings',
        checkAll:'Check All',
        columnSelected:'column(s) selected',
        manageColumn:'Manage Columns',
        filterRule:'Filter Rule',
        filterRuleTip:'Select your corresponding customer role and view relevant TRF records',
    },
    trfPrint:{
        general:{
            serviceType:'Service Type',
            trfNo:'TRF No',
            buyerName:'Buyer Name',
            labName:'Lab Name',
            agent:'Agent',
            labAddress:'Lab Address',
            selfReference:'Self Reference',
            labContact:'Lab Contact Email',
            validLabContact:'The email format is incorrect',
            isEfilling:"Require US CPSC eFiling",
        },
        customerInformation:{
            applicant:'APPLICANT INFORMATION',
            payer:'BILLING INFORMATION',
            buyer:'BUYER INFORMATION',
            agent:'AGENT INFORMATION',
            supplier:'SUPPLIER INFORMATION',
            manufacture:'MANUFACTURER INFORMATION',
            applicantCustomerCode:'Applicant Customer Code',
            payerCustomerCode:'Payer Customer Code',
            buyerCustomerCode:'Buyer Customer Code',
            agentCustomerCode:'Agent Customer Code',
            supplierCustomerCode:'Supplier Customer Code',
            manufactureCustomerCode:'Manufacture Customer Code',
            sgsCustomerNo:'SGS Customer No',
            companyNameEn:'Company Name (English)',
            companyNameLocal:'Company Name (Local Language)',
            companyAddressEn:'Company Address ( English )',
            companyAddressLocal:'Company Address ( Local Language )',
            contact:'Contact',
            phone:'Phone No',
            email:'Email'
        },
        returnSample:{
            returnSampleRequire:'Sample Return',
            returnSampleRequest:'Sample Return,If Request',
            judgmentRule:'Decision rules',
            reportSymbols:'Report Symbols',
            isCommon:'Conclusion Required',
            isCopyName:'Hardcopy Required',
            photo:'Photo Required',
            isConfimCover:'Cover Page Confirmation',
            isQuotation:'Quotation Required',
            softCopyContactName:'SoftCopy Deliver To',
            vatType:'VAT Type',
            needProformaInvoice:'Proforma Invoice Required',
            invoiceDeliverWay:'Invoice Deliver Way',
            invoiceContactName:'Invoice Deliver To',
            returnSampleContactName:'Return Sample To',
            returnSampleDeliverWay:'Return Sample Way',
            liquid:'For liquid printing ink sample',
            isOutside:'Whether to accept testing from subcontractor out side SGS (Accept if no selection)',
            isLegalProceeding:'Is this application involved in legal proceeding?'
        },
        dff:{
            formTitle:'SAMPLE INFORMATION (*mark refers mandatory information to fill in)',
            gridTitle:'SAMPLE LIST',
            itemId:'Item ID',
            carelabelTitle:'Care Label Symbols and Wording',
            carelabelItemId:'Item ID',
            carelabelCareWording:'Care Wording',
            carelabelSymbols:'Symbols'
        },
        otherRequest:{
            title:'OTHER REQUEST',
            reportHeaderEn:'Report Header (English)',
            reportAddressEn:'Report Address (English)',
            reportDeliveredTo:'Report Delivered to',
            failedReportDeliveredTo:'Failed Report Delivered to',
            reportLanguage:'Report Language',
            additionalRequest:'Additional Request'
        },
        attach:{
            info:'ATTACHMENT',
            num:'No.',
            fileName:'File Name'
        },
        testRequest:'TEST REQUEST',
        remark:'REMARK',
        noData:'No Data',
        declaration1:'# The applicant requests & statement',
        declaration2:'The applicant requests the nominated payer as indicated in this application form to pay for all the fees related to this job application. The applicant confirms that the payer has been well notified and agrees on this payment arrangement. If the nominated payer refuses to pay, or make late or incorrect payment, the applicant unconditionally agrees to settle all amounts of outstanding invoices or any cost associated with the late and incorrect payment from the payer related to this application',
        availability:'# Subject to the applicability on the requested testing and the service availability to be determined by SGS.',
        website:'Our services are subject to the terms and conditions described in SGS website',
        sign:'Authorized Signature & Company Chop'
    },
    crud: {
        filter: {
          addBtn: 'Add',
          clearBtn: 'Clear',
          resetBtn: 'Reset',
          cancelBtn: 'Cancel',
          submitBtn: 'Submit',
        },
        column: {
          name: 'Name',
          hide: 'Hide',
          fixed: 'Fixed',
          filters: 'Filters',
          sortable: 'Sortable',
          index: 'Index',
          width: 'Width',
        },
        tipStartTitle: 'Currently selected',
        tipEndTitle: 'Items',
        editTitle: 'Edit',
        copyTitle: 'Copy',
        addTitle: 'Add',
        viewTitle: 'View',
        filterTitle: 'Filter',
        showTitle: 'ShowTitle',
        menu: 'Menu',
        addBtn: 'Add',
        show: 'Show',
        hide: 'Hide',
        open: 'Open',
        shrink: 'Shrink',
        printBtn: 'Print',
        excelBtn: 'Excel',
        updateBtn: 'Update',
        cancelBtn: 'Cancel',
        searchBtn: 'Search',
        emptyBtn: 'Empty',
        menuBtn: 'Menu',
        saveBtn: 'Save',
        viewBtn: 'View',
        editBtn: 'Edit',
        copyBtn: 'Copy',
        delBtn: 'Delete',
      },
    work:{
        operationSuccessful: "Operation successful",
        selectAtLeastOneData: "Please select at least one piece of data",
        confirmButtonText: "Confirm",
        cancelButtonText: "Cancel",
        confirm_delete_msg: "Are you sure you want to delete the selected data?",
        columnAction:"Action",
        btn:{

        },
        cpscCitation:{
          column:{
            status: "Status",
            citation_code: "Citation Code",
            citation_code_required_msg: "Please enter the citation code",
            citation_code_max_msg: "Citation code must be no more than 50 characters",
            regulation: "Regulation",
            regulation_required_msg: "Please enter the regulation",
            regulation_max_msg: "Regulation must be no more than 200 characters",
            product_category: "Product Category",
            product_category_required_msg: "Please enter the product category",
            product_category_max_msg: "Product category must be no more than 200 characters",
            certificate: "Certificate",
            certificate_required_msg: "Please select the certificate",
            keyword: "Keyword",
            keyword_max_msg: "Keyword must be no more than 50 characters"
          }
        },
        cpscExclusion:{
          column:{
            status: "Status",
            exclusion_code: "Exclusion Code",
            exclusion_code_required_msg: "Please enter the exclusion code",
            exclusion_code_max_msg: "Exclusion code must be no more than 50 characters",
            regulation: "Regulation",
            regulation_required_msg: "Please enter the regulation",
            regulation_max_msg: "Regulation must be no more than 200 characters",
            product_category: "Product Category",
            product_category_required_msg: "Please enter the product category",
            product_category_max_msg: "Product category must be no more than 200 characters",
            certificate: "Certificate",
            certificate_required_msg: "Please select the certificate",
            keyword: "Keyword",
            keyword_max_msg: "Keyword must be no more than 50 characters"
          }
        },
        cpscCustomer:{
          column: {
            status: "Status",
            bossName: "Importer Name",
            productPrimaryId: "Product Primary ID",
            secretKey: "Secret Key",
            token: "Token",
            expiringDate: "Expiring Date",
            certifierId: "Certifier ID",
            bossCustomerCountryId: "Country",
            bossCustomerCityName: "City",
            bossCustomerAddress: "Address",
            contactName: "Contact Name",
            telphone: "Phone",
            email: "Email",
            bossName_required_msg: "Please enter the customer name",
            productPrimaryId_required_msg: "Please select the product primary ID",
            syncCheck1:"Operation failed. Secret Key, Token, and Expiring Date must all be filled in",
            syncCheck2:"Operation failed, the secret key has expired",
          }
        },
        cpscCustomerTrade:{
            tip:{
                productInfo:'PrimaryId Need productIdType And ProductId',
                primary:'Product Info Need  primary'
            },
          column:{
            status:"Status",
            tradeType:"Trade Part Type",
            tradeName:"Trade Part Name",
            alternateId:"Alternate Id",
            gln:"GLN",
            tradeAddress:"Address Line 1",
            tradeAddress2:"Address Line 2",
            apartment:"Apartment",
            tradeCountryId:"Country",
            tradeProvince:"Province",
            tradeCity:"City",
            tradePostalCode:"Postal Code",
            telphone:"Phone",
            requireTelphone:'Please enter telphone',
            validPhone:'The phone format is incorrect',
            email:"Email",
            requireEmail:'Please enter E-mail',
            validEmail:'The E-mail format is incorrect',
            address:"Address",
            alternateId_gln_required_msg: "Please fill in at least one Alternate ID or GLN",
            selectType_msg: "Please select trade part type",
            enterName_msg: "Please enter trade part name",
            enterAddress1_msg: "Please enter address line 1",
            enterAddress1_msg2:'The maximum number of characters cannot exceed 50',
            keyword: "Keyword",
            countryId_required_msg: "Please select a country",
            cityName_required_msg: "Please enter the city",
          }
        },
        cpscTrfInfo:{
            buyerMessage:'Please select Customer',
            selectNoData:'Please key in key words of manufacturer',
            placeholder:{
                input:'Please Input ',
                select:'Please Select '
            },
            operationSuccessful: "Operation successful",
            selectAtLeastOneData: "Please select at least one piece of data",
            confirmButtonText: "Confirm",
            cancelButtonText: "Cancel",
            confirmDeleteMsg: "Are you sure you want to delete the selected data?",
            columnAction:"Action",
            confirmCopyMsg: "Do you want to new or update?",
            export:'Do you want to export?',
            lastOneData:'Choose A Data Please',
            btn:{
                copyAndAdd: "New",
                copyAndEdit: "Update"
            },
            column:{
                manufacture:"Manufacturer Name",
                certificateType:'Certificate Type',
                lastTestDate:'Last Test Date',
                trfInfoStastus:"eFiling Status",
                referenceNo:"eFiling No",
                buyerCustomerName:"Buyer",
                manufactureCustomerName:"Manufacturer",
                certVersion:"Version ID",
                updateTime:"End Date",
                Product_ID:"Product ID",
                cancel:'Cancel',
                ProductName:"Product",
                buyInfo:"Buyer Info",
                customerName:"Importer Name",
                customerNameLocal:"Importer Name (Local)",
                customerCountry:"Importer Country",
                customerCity:"Importer City",
                customerAddress:"Importer Address",
                customerAddressLocal:"Importer Address (Local)",
                contactName:"Contact Name",
                contactTelephone:"Contact Telephone",
                contactEmail:"Contact Email",
                createTime:'Create Time',
                createUser:'Create User',
                manufactureInfo:"Manufacturer Info",
                basicInfo:"Basic Info",
                serviceType:"Service Type",
                labName:"Lab Name",
                labContact:"Lab Contact Email",
                csEmailAddressMsg:"Please Input CS Email Address",
                labAddress:"Lab Address",
                poc:"Point Of Contact（POC) For Test Result Records",
                pocType:"POC Type",
                otherPoc:"Other POC",
                attachment:"Attachment",
                labInfo:"Lab Info",
                labType:"Lab Type",
                testReportId:"Test Report ID",
                testReportKey:"Test Report Key",
                reportUrl:"Report URL",
                citationsCode:"Citations Code",
                testingExclusions:"Testing Exclusions",
                certificateInfo:"Certificate Info",
                certificateVersion:"Version ID",
                placeholderCertificateVersion:"Please Input Version ID",
                cpscCollection:"CPSC Collection",
                save:"Save",
                submit:"Submit",
                validated:"Validated",
                rework:"Rework",
                exportCsv:"Export CSV",
                toCpsc:"To CPSC",
                toCpscLog:"To CPSC Log",
                crudEdit:"Edit",
                entry:'Entry',
                exportWord:"Export Word",
                copy:"Copy",
                pending:"Pending",
                unPending:"UnPending",
                trfReferenceNo:"TRF No",
                action:'Action',
            }
        },
        // 数据转换配置
        trfMapping:{
            column:{
                productLine:"Product Line",
                customerName:"Customer Name",
                productCategory:"Product Category",
                customerProductCategory:"Customer Product Category",
                dffTemplate:"DFF Template"
            }
        },
    },
    footerBar:{
        termsOfUse:'SGS SMART Terms of Use',
        DPP:'Data Privacy Policy',
        termsOfUsePdfPath:'/static/pdf/SGSSMARTTermsofUse.pdf',
        DPPPdfPath:'/static/pdf/SGSSMARTDataPrivacyNotice.pdf'
    }
}
