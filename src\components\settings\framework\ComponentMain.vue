<template>
    <div class="component_main">
        <grid-layout
                :layout.sync="layout"
                :col-num="12"
                :row-height="baseHeight"
                :is-draggable="lock"
                :is-resizable="lock"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[10, 10]"
                :use-css-transforms="true">
            <grid-item v-for="(item, index) in layout"
                       :x="item.x"
                       :y="item.y"
                       :w="item.w"
                       :h="item.h"
                       :i="item.i"
                       :key="index" class="gridItem" v-if="item.show">
                <component :is="item.tag" v-if="item.show" />
            </grid-item>
        </grid-layout>
    </div>
</template>

<script>
import Vue from 'vue';
import {GridItem, GridLayout} from 'vue-grid-layout';
import {getComponentsList, savePageConfig} from "@/api/system/settings/components/components";
import {mapGetters} from "vuex";

export default {
        //组件编辑区域
        name: "ComponentMain",
        components:{ GridLayout, GridItem },
        props: {
          lock: {
            type: Boolean,
            default: false
          }
        },
        data() {
            return {
                baseHeight: 30,
                layout: [],
                currentY: 0,
                componentMap: new Map()
            }
        },
        computed: {
          ...mapGetters(["userInfo", "permission"]),
          permissionList() {
            return {
              buyerOverallUnit: this.vaildData(this.permission['sgs:main:buyer:overall'], false),
              trfCountUnit: this.vaildData(this.permission['sgs:main:trfCount'], false),
              documentUnit: this.vaildData(this.permission['sgs:main:document'], false),
              newServiceUnit: this.vaildData(this.permission['sgs:main:newService'], false),
              safeguardUnit: this.vaildData(this.permission['sgs:main:safeguard'], false),
              testRequestFormUnit: this.vaildData(this.permission['sgs:main:testRequestForm'], false),
              supplierOverallUnit: this.vaildData(this.permission['sgs:main:supplier:overall'], false),
              newTrfBtn: this.vaildData(this.permission['sgs:main:supplier:addTrf'], false),
              newAflTrfBtn: this.vaildData(this.permission['sgs:main:afl:addTrf'], false),
            };
          },
        },
        created() {
          getComponentsList().then(res => {
            console.log("组件清单", res);
            let components = res.data.data;
            components.forEach(item => {
              this.layout.push(JSON.parse(item.configData));
            });
          }).then(() => {
            this.layout.forEach((item, index) => {
              if(item.name) {
                this.componentMap.set(item.i, index);
                Vue.component(item.tag, this.loadComponents(item.path, item.name));
              }
            });
          });
        },
        methods: {
          loadComponents(path, name) {
              return () => import(`@/components/${path}/${name}.vue`);
          },

          //子组件调用，重新计算高度
          calculatedHeight(values) {
            let rank = values[0];
            let height = values[1];
            this.layout[rank].h = height / this.baseHeight;
            console.log(rank, height);
          },

          toggleComponent: function (data, checked) {
            for(let i = 0; i < this.layout.length; i++) {
              if(this.layout[i].i == data.id) {
                this.layout[i].show = checked;
                if(checked) {
                  //显示且没有定位时，展示到到默认位置
                  if(!this.layout[i].location) {
                    this.layout[i].x = 0;
                    this.layout[i].y = this.currentY;
                    this.location = true;
                  }
                }
                this.currentY = checked ? this.currentY + this.layout[i].h : this.currentY - this.layout[i].h;
                this.currentY = this.currentY < 0 ? 0 : this.currentY;
                break;
              }
            }
          },

          showPageComponents(config) {
            if(config) {
              let arrays = JSON.parse(config);
              arrays.forEach(item => {
                let component = this.componentMap.get(item.i);
                this.layout[component].x = item.x;
                this.layout[component].y = item.y;
                this.layout[component].w = item.w;
                this.layout[component].h = item.h;
                this.layout[component].location = true;
              });
            }
          },

          submit(config) {
            config.configData = JSON.stringify(this.layout.filter(item => item.show));
            this.$emit('showLoading', null);
            savePageConfig(config).then(res => {
              if(res.data.data) {
                this.$message({
                  type: 'success',
                  message: this.$t('api.success')
                });
              }
              else {
                this.$message({
                  type: 'error',
                  message: this.$t('api.error')
                });
              }
            }).finally(() => {
              this.$emit('hiddenLoading', null);
            });
          },


        }
    }
</script>

<style scoped lang="scss">
    .component_main {
      width: 100%;
    }
</style>
