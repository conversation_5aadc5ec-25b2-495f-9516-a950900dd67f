import request from '@/router/axios';




export const querMyAuthorityList = (data) => {
    return request({
        url: '/api/sgs-mart/customer/proxy/mylist-customer-auth',
        method: 'post',
        data: data
    })
}
export const validateCustomer = (data) => {
    return request({
        url: '/api/sgs-mart/customer/proxy/validateProxy',
        method: 'post',
        data: data
    })
}
export const saveAuth = (data) => {
    return request({
        url: '/api/sgs-mart/customer/proxy/saveAuthStatus',
        method: 'post',
        data: data
    })
}
export const changeAuthStatus = (data) => {
    return request({
        url: '/api/sgs-mart/customer/proxy/changeAuthStatus',
        method: 'post',
        data: data
    })
}




