<template>
<!--  <title>System maintenance</title>-->
  <basic-container>
  <div class="notice-wrap">
    <div class="notice">
      <h3>System maintenance</h3>
      <p class="notice-cont">Data entry tool is under maintenance, </p>
      <p> <span>it will resume work at GMT*******-12-10 6:00 AM.</span></p>
      <p class="notice-tel">Sorry for any inconvenience caused.</p>

      <div class="notice-foot">
        <p>SGS SMART</p>
      </div>
    </div>
  </div>
  </basic-container>

</template>
<script>

export default {
  components: {},
  data() {
    return {}
  },
  computed: {

  },
  watch: {},
  mounted() {
  },
  methods: {},
}

</script>

<style lang="scss">
*{
  margin: 0;
  padding: 0;
}
body,html{
  width: 100%;
  height: 100%;
}
body{
 // background: #FF6600 !important;
  font-family: "Arial";
}
#app{
 // background: #FF6600 !important;
}
.notice-wrap{
  padding-top: 30px;
}
.notice{
  margin: 0 auto;
  width: 682px;
  height: 633px;
  background: url(../../../public/img/update-bg.png) no-repeat;
}
.notice h3{
  padding-top: 225px;
  font-size: 24px;
  color: #333;
  text-align: center;
}
.notice>p{
  margin: 0 62px;
  font-size: 18px;
  color: #666;
  text-indent: 36px;
  line-height: 40px;
}
p span{
  //color: #FF6600;
}
.notice .notice-cont{
  margin-top: 45px;
}
.notice .notice-tel{
  padding-bottom: 54px;
  border-bottom: 2px solid #f2f2f2;
}
.notice-foot{
  margin-top: 24px;
}
.notice-foot p{
  font-size: 18px;
  color: #666;
  text-align: center;}
</style>
