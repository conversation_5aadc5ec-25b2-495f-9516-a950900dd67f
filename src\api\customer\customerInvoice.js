import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/add',
        method: 'post',
        data: form
    })
}

export const checkExistsInvoice = (form) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/checkExistsInvoice',
        method: 'post',
        data: form
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/list',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getPageByUser = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/page/by-user',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const setDefault = (id,isDefault) => {
    return request({
        url: '/api/sgs-mart/customer/invoice/set-default',
        method: 'get',
        params: {
            id,
            isDefault
        }
    })
}
export const getInvoiceType = (language) => {
    var keyGroup='FapiaoType';
     if(language=='zh-CN'){
         keyGroup='FapiaoTypeCN';
     }
    return request({
        url: '/api/sgsapi//FrameWorkApi/dataDictionary/api/v1/get/dataDictionary?bUID=0&SystemID=0&sysKeyGroup='+keyGroup,
        method: 'get',
    })
}


