<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_protocol_list" id="sgs_smart_protocol_list">
            <div class="title_justify">
                <div style="display: inline">
                    <h3>Protocol Library</h3>
                </div>
            </div>
            <el-card :body-style="{padding:'10px'}">
                <el-form :model="headerSearch" ref="headerSearch" label-position="left" label-width="140px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="Customer" prop="buyCustomerGroupCode">
                                <el-select
                                    style="width: 100%"
                                    filterable
                                    clearable
                                    v-model="headerSearch.buyCustomerGroupCode"
                                >
                                    <el-option
                                        v-for="(cus,index) in filterList.customerList"
                                        :key="'cust_'+index"
                                        :label="cus.customerGroupName"
                                        :value="cus.customerGroupCode"
                                    ></el-option>
                                    <el-option v-if="!role.isSGS" label="General" value="General" key="Gener_op"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="Destination" prop="destination">
                                <el-select
                                        style="width: 100%"
                                        filterable
                                        clearable
                                        multiple
                                        v-model="headerSearch.destination"
                                >
                                    <el-option
                                            v-for="(cus,index) in filterList.destinationList"
                                            :key="'cust_'+index"
                                            :label="cus.code"
                                            :value="cus.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="Product Line" prop="productLineCode">
                                <el-select
                                        style="width: 100%"
                                        placeholder="Product Line"
                                        v-model="headerSearch.productLineCode"
                                        @change="changePL"
                                        clearable
                                        filterable
                                >
                                    <el-option
                                            v-for="(op,inde) in filterList.productLineList"
                                            :key="'op_'+inde"
                                            :label="op.productLineName"
                                            :value="op.productLineCode"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="Protocol Taxonomy" prop="protocolTaxonomy">
                                <el-select
                                        style="width: 100%"
                                        multiple
                                        filterable
                                        clearable
                                        v-model="headerSearch.protocolTaxonomy"
                                >
                                    <el-option
                                            v-for="(cus,index) in filterList.taxonomyList"
                                            :key="'cust_'+index"
                                            :label="cus.name"
                                            :value="cus.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row>
                    <el-col class="operate-block">
                        <div>
                            <el-button size="medium" v-if="permissionList.createNewBtn" type="primary" @click="handlerDetail">Create New</el-button>
                        </div>
                        <div>
                            <el-button size="medium" type="primary" @click="searchData">Search</el-button>
                            <reset-button @click="resetForm"></reset-button>
                        </div>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-table
                                ref="protocolTable"
                                :data="dataList"
                                :max-height="500"
                                size="mini"
                                class="sgs_table_protocol_table table-header-filter"
                                fit
                                border
                                resizable
                        >
                            <el-table-column show-overflow-tooltip label="Protocol Name" prop="protocolName" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Protocol Name</div>
                                    <div>
                                        <el-input placeholder="Protocol Name" v-model="searchForm.protocolName" clearable size="mini"></el-input>
                                    </div>
                                </template>
                                <template slot-scope="{row}">
                                    <div>
                                        <a @click="editRow(row, 'read')" style="color: #FF6600;font-size: 14px;cursor:pointer">{{ row.protocolName }}</a>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Protocol ID" prop="protocolId" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Protocol ID</div>
                                    <div>
                                        <el-input placeholder="Protocol ID" v-model="searchForm.protocolId" clearable size="mini"></el-input>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Protocol Description" prop="protocolDescription" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Protocol Description</div>
                                    <div>
                                        <el-input placeholder="Protocol Description" v-model="searchForm.protocolDescription" clearable size="mini"></el-input>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Version Number" prop="versionNumber" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Version Number</div>
                                    <div>
                                        <el-input
                                                type="text"
                                                oninput="this.value=this.value.replace(/\D/g,'')"
                                                size="mini"
                                                style="width: 100%"
                                                placeholder="Version Number"
                                                clearable
                                                v-model="searchForm.versionNumber">
                                        </el-input>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Status" prop="protocolStatus" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Status</div>
                                    <div>
                                        <el-select
                                                size="mini"
                                                style="width: 100%"
                                                placeholder="Status"
                                                v-model="searchForm.protocolStatus"
                                                multiple
                                                clearable
                                                filterable
                                        >
                                            <el-option
                                                    v-for="(ps,inde) in filterList.protocolStatusList"
                                                    :key="'ps_'+inde"
                                                    :label="ps.label"
                                                    :value="ps.value"
                                            ></el-option>
                                        </el-select>
                                    </div>
                                </template>
                                <template slot-scope="{row}">
                                    {{filterList.protocolStatusList.find(ps=>ps.value==row.protocolStatus).label}}
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Effective Date" prop="effectiveDate" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Effective Date</div>
                                    <div>
                                        <el-date-picker
                                                size="mini"
                                                style="width: 100%"
                                                v-model="searchForm.effectiveDate"
                                                type="daterange"
                                                value-format="yyyy-MM-dd"
                                                placeholder="Effective Date">
                                        </el-date-picker>
                                    </div>
                                </template>
                                <template slot-scope="{row}">
                                    {{tzDate(row.effectiveDate,'YYYY-MM-DD')}}
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Update By" prop="updateUser" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Update By</div>
                                    <div>
                                        <el-input
                                                type="text"
                                                size="mini"
                                                style="width: 100%"
                                                placeholder="Update By"
                                                clearable
                                                v-model="searchForm.updateBy">
                                        </el-input>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column show-overflow-tooltip label="Update Date" prop="updateTime" min-width="220px">
                                <template slot="header" slot-scope="scope">
                                    <div>Update Date</div>
                                    <div>
                                        <el-date-picker
                                                size="mini"
                                                style="width: 100%"
                                                v-model="searchForm.updateDate"
                                                type="daterange"
                                                value-format="yyyy-MM-dd"
                                                placeholder="Update Date">
                                        </el-date-picker>
                                    </div>
                                </template>
                                <template slot-scope="{row}">
                                    {{tzDate(row.updateTime)}}
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" label="Action" prop="" min-width="120px">
                                <template slot-scope="{row}">
                                    <el-tooltip content="Edit"  placement="top">
                                        <i class="el-icon-edit menu-icon" v-if="btnRole(row,'Edit')" @click="editRow(row,'edit')"></i>
                                    </el-tooltip>
                                    <el-tooltip content="Cancel"  placement="top">
                                        <el-popconfirm
                                                v-if="btnRole(row,'Cancel')"
                                                confirm-button-text='Confirm'
                                                cancel-button-text='Cancel'
                                                icon="el-icon-info"
                                                icon-color="red"
                                                title="Cancel the data?"
                                                @confirm="actionRow(row,'Cancel')"
                                        >
                                            <i slot="reference" style="font-weight: bold" class="el-icon-delete delete-icon"></i>
                                        </el-popconfirm>
                                    </el-tooltip>
                                    <el-tooltip content="Recovery"  placement="top">
                                        <el-popconfirm
                                                v-if="btnRole(row,'Recovery')"
                                                confirm-button-text='Confirm'
                                                cancel-button-text='Cancel'
                                                icon="el-icon-info"
                                                icon-color="red"
                                                title="Recovery the data?"
                                                @confirm="actionRow(row,'Recovery')"
                                        >
                                            <i slot="reference" class="icon-all iconhuishou-copy"></i>
                                        </el-popconfirm>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-pagination
                                @size-change="sizeChange"
                                @current-change="currentChange"
                                :current-page.sync="page.page"
                                :page-sizes="page.sizes"
                                :page-size.sync="page.rows"
                                :layout="page.layout"
                                :total="page.total"
                        >
                        </el-pagination>
                    </el-col>
                </el-row>
            </el-card>
        </div>
        <el-dialog
                :visible.sync="downloadTemplateDia"
                append-to-body
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :show-close="false"
                title="Select Template"
        >
            <query-template
                    v-if="downloadTemplateDia"
                    :product-line-list="filterList.productLineList"
                    :customer-list="filterList.customerList"
                    @cancelDia="downloadTemplateDia=false"
            >
            </query-template>

        </el-dialog>
    </basic-container>
</template>

<script>
    import protocolApi from '../../api/protocol/protocol.js'
    import QueryTemplate from "./templates/queryTemplate";
    import {tzFormChina,tzToChina} from '@/util/datetimeUtils'
    import {getProductLine,getCustomerGroup,queryBuSetting} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import {debounce} from "lodash";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {LanguageEnums} from "@/commons/enums/LanguageEnums";
    import tag from "@/components/tag/tag.vue";
    import resetButton from "@/components/resetButton/resetButton.vue";

    let vm ;
    export default {
        name: "list",
        data() {
            return {
                pageLoading: false,
                downloadTemplateDia: false,
                customerList:[],
                dataList:[],
                maxVersionNo:0,
                LanguageEnums:LanguageEnums,
                headerSearch:{
                    destination:[],
                    protocolTaxonomy:[],
                    buyCustomerGroupCode:'',
                    productLineCode:'',
                },
                searchForm:{
                    protocolName:'',
                    protocolId:'',
                    effectiveDate:[],
                    updateDate:[],
                    protocolStatus:[],
                    protocolDescription:'',
                    versionNumber:null,
                    updateBy:'',
                },
                page:{//分页对象
                    show: true,
                    page: 1,
                    rows: 20,
                    small: true,
                    sizes: [10, 20, 50, 100],
                    layout: 'total, sizes, prev, pager, next,jumper',
                    total: 0
                },
                filterList:{
                    productLineList:[],
                    customerList:[],
                    protocolStatusList:[
                        {label:'confirmed',value:1},
                        {label:'phaseout',value:2}
                    ],
                    taxonomyList:[],
                    destinationList:[]
                }
            }
        },
        methods: {
            editRow(row, type){
                let {id} = row;
                let routeUrl = this.$router.resolve({
                    path: type + '/protocol/detail',
                    query:{id}
                });
				const href = '/web/protocol/detail?action='+ type + '&id=' + id
                window.open(href ,'_blank');
            },
            actionRow(row,action){
                let {id}  = row;
                protocolApi.handlerAction({id,action}).then(res=>{
                    if(res.status==200 && res.data.status==200){
                        this.$notify.success(action + 'Success');
                        this.initTable();
                    }
                })
            },
            btnRole({permissions},code){
                return (permissions || []).map(p=>p.action).includes(code);
            },
            deboundceSearch:debounce(function(val){
                vm.page.page=1;
                vm.initTable();
            },500),
            searchData(){
                this.page.page=1;
                this.initTable();
            },
            initPage() {
                this.initTable();
                this.initProductLine();
                this.initCustomer();
                this.initDestination();
            },
            initDestination(){
              protocolApi.queryDestination().then(res=>{
                  if(res.status==200 && res.data){
                      this.filterList.destinationList = res.data || [];
                  }
              }).catch(err=>{
                  console.log("queryDestination err",err)
              })
            },
            initCustomer(){
                if(this.role.isSGS){
                    getCustomerGroup().then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            this.filterList.customerList = res.data.data || [];
                        }
                    }).catch(err=>{
                        console.log("查询customer err",err);
                    })
                }else{
                    let scmCustomerReq = {
                        page:1,
                        rows:9999,
                        list:[{
                        buCode : this.userInfo.productLineCode,
                        relationshipType : 'buyer',
                        customerNo : this.userInfo.bossNo
                        }]
                    }
                    let customerList = [];
                    protocolApi.queryScmCustomerList(scmCustomerReq).then(res=>{
                        if(res.status==200 && res.data && res.data.rows){
                            let customerData = res.data.rows;
                            customerData.forEach(c=>{
                                let {scmCustomerGroupCode,scmCustomerGroupName,scmCustomerNo,scmCustomerNameCN,scmCustomerNameEN} = c;
                                let customerName = '';
                                if(validatenull(scmCustomerGroupCode)){//客户
                                    if(this.language==this.LanguageEnums.CN.name){
                                        customerName = scmCustomerNameCN;
                                    }
                                    if(validatenull(customerName)){
                                        customerName=scmCustomerNameEN;
                                    }
                                }

                                customerList.push({
                                    customerGroupCode:scmCustomerGroupCode,
                                    customerGroupName:scmCustomerGroupName || customerName,
                                    bossNo:scmCustomerNo
                                })
                            })
                        }
                        if(!objectIsNull(this.userInfo.customerGroupCode)){
                            let groupObj = {
                                customerGroupCode : this.userInfo.customerGroupCode,
                                customerGroupName : this.userInfo.customerGroupName,
                                bossNo : this.userInfo.customerGroupCode?"":this.userInfo.bossNo
                            }
                            customerList.push(groupObj);
                        }
                        this.filterList.customerList = customerList.filter(c=>c.customerGroupCode);
                    }).catch(err=>{
                        if(!objectIsNull(this.userInfo.customerGroupCode) && this.userInfo.bossNo){
                            let groupObj = {
                                customerGroupCode : this.userInfo.customerGroupCode,
                                customerGroupName : this.userInfo.customerGroupName,
                                bossNo : this.userInfo.customerGroupCode?"":this.userInfo.bossNo
                            }
                            customerList.push(groupObj);
                        }
                        this.filterList.customerList = customerList.filter(c=>c.customerGroupCode);
                        console.log("query scm err",err)
                    })
                }
            },
            initProductLine(){
                getProductLine().then(res => {
                    if(res.status==200 && res.data){
                        const data = res.data.data || [];
                        let currentUserPL = (this.userInfo.productLineCodes || '').split(",");
                        if(!this.userInfo.productLineCodes && this.userInfo.productLineCode=='all'){
                            this.filterList.productLineList = data;
                            return
                        }
                        this.filterList.productLineList = data.filter(da=>currentUserPL.includes(da.productLineCode));
                    }
                });
            },
            changePL(){
                console.log("this.headerSearch.productLineCode",this.headerSearch.productLineCode)
                this.$set(this.filterList,'taxonomyList',[])
                this.$set(this.headerSearch,'protocolTaxonomy',[])
                if(!this.headerSearch.productLineCode){
                    return
                }
                //接口级联taxonomy
                let params ={
                    systemId:1,
                    groupCode:'Protocol',
                    productLineCode:this.headerSearch.productLineCode,
                    paramCode:'BuTaxonomy'
                }
                queryBuSetting(params).then(res=>{
                    console.log("query config ",res)
                    if(res.status==200 && res.data && res.data.data){
                        let config = res.data.data[0];
                        let {paramValue} = config;
                        try{
                            this.filterList.taxonomyList = JSON.parse(paramValue);
                        }catch(e){
                        }
                    }
                }).catch(err=>{
                    console.log("query config err",err)
                })
            },
            columnProductLine(row){
                let pl  = this.filterList.productLineList.find(c=>c.productLineCode==row.productLineCode);
                if(!pl){
                    return row.productLineCode;
                }
                return pl.productLineName;
            },
            columnCustomer(row){
                let cl = this.filterList.customerList.find(c=>c.customerGroupCode==row.customerGroupCode);
                if(!cl){
                    return row.customerGroupCode;
                }
                return cl.customerGroupName;
            },
            currentChange(currentPage) {
                this.initTable();
            },
            // 调整每页显示行数
            sizeChange(pageSize) {
                this.initTable();
            },
            resetForm(){
                this.$refs.headerSearch.resetFields();
                this.searchForm = {
                    protocolName:'',
                    protocolId:'',
                    effectiveDate:[],
                    protocolStatus:[],
                    productLineCode:'',
                    protocolDescription:'',
                    customerGroupCode:''
                }
            },
            initTable(){
                this.pageLoading = true;
                let {effectiveDate,updateDate} = this.searchForm;
                let startEffectiveDate = null;
                let endEffectiveDate = null;
                let startUpdateTime = null;
                let endUpdateTime = null;
                if(effectiveDate && effectiveDate.length>0){
                    startEffectiveDate = tzToChina(effectiveDate[0]+' 00:00:00')
                    endEffectiveDate = tzToChina(effectiveDate[1]+' 23:59:59')
                }
                if(updateDate && updateDate.length>0){
                    startUpdateTime = tzToChina(updateDate[0]+' 00:00:00')
                    endUpdateTime = tzToChina(updateDate[1]+' 23:59:59')
                }
                let number = "";
                if(this.headerSearch.buyCustomerGroupCode){
                    let cus = this.filterList.customerList.find(c=>c.customerGroupCode==this.headerSearch.buyCustomerGroupCode) || {};
                    number = cus.number;
                }
                let param = {...this.searchForm,...this.headerSearch,startEffectiveDate,endEffectiveDate, number,startUpdateTime,endUpdateTime, current:this.page.page,size:this.page.rows}
                protocolApi.queryPage(param).then(res=>{
                    this.pageLoading = false
                    if(res.status==200 && res.data && res.data.data && res.data.data.records){
                        let dataList = res.data.data.records;
                        let newList = [];
                        dataList.forEach(da=>{
                            let {customerList,versionNumber} = da;
                            let cus = (customerList || []).find(c=>c.customerUsage==3) || {};
                            da = Object.assign({},da,cus);
                            newList.push(da);
                        })
                        this.dataList = newList;
                        this.page.total = res.data.data.total;
                    }
                }).catch(err=>{
                    this.pageLoading = false
                    console.log("query page err",err);
                })
            },
            handlerDetail(){
                this.downloadTemplateDia = true;
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.dimensions)) {
                    return false;
                } else {
                    if (this.dimensions.hasOwnProperty(type)) {
                        if (this.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
            tzDate(dateStr,format){
              //转成时区对应时间，入参为北京时间
                if(!dateStr){
                    return dateStr;
                }
                return tzFormChina(dateStr,format);
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
            vm = this;
        },
        watch: {
            searchForm:{
              immediate:false,
              deep:true,
              handler(newV){
                  this.deboundceSearch();
              }
            },
        },
        computed: {
            ...mapGetters(["permission","language","userInfo","dimensions"]),
            role() {
                return {
                    isSGS: this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser"),
                    isBuyer: this.haseRole('UserRole', 'Buyer'),
                }
            },
            permissionList() {
                return {
                    createNewBtn:this.vaildData(this.permission['sgs:protocol:createNew'], false),
                }
            },
        },
        props: {},
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {QueryTemplate, tag, resetButton}
    }
</script>

<style lang="scss">
@import "@/styles/unit.scss";

    .sgs_smart_protocol_list {
        font-family: 'Arial' !important;
        background: #fff;
        padding: 24px 32px;
        .icon-base {
            font-size: 20px;
            cursor: pointer;
            margin: 0 10px;
        }

        .menu-icon {
            @extend .icon-base;
            color: $primary-color;
        }
      
        .delete-icon {
            @extend .icon-base;
            color: $icon-color-default;
            font-weight: normal!important;
        }
        .iconhuishou-copy{
            font-size: 24px !important;
            cursor: pointer;
        }
        .operate-block{
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
</style>