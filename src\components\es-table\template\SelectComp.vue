<template>
    <div id="SelectComp" class="SelectComp">
        <el-row>
            <el-col :span="8">
                <el-select
                        v-model="selectValue"
                        style="width: 100%"
                        filterable
                >
                    <el-option
                            v-for="(op,ind) in selectList"
                            :key="'col_'+ind"
                            :label="$t(op.i18n)"
                            :value="op.value"
                    ></el-option>
                </el-select>
            </el-col>
            <el-col :span="16">
                <div v-if="!configOptions.remote">
                    <el-select
                            v-model="dataValue"
                            filterable
                            @clear="doClear"
                            style="width: 100%"
                            v-bind="$attrs"
                            :clearable="configOptions.clearable"
                            no-data-text="No Data">
                        <el-option
                                v-for="(data,index) in dataList"
                                :key="data.id || index"
                                :label="$t(data[configOptions['remoteLabelCode']] || data.label)"
                                :value="data[configOptions['remoteValueCode']] || data.value">
                        </el-option>
                    </el-select>
                </div>
                <div v-if="configOptions.remote">
                    <el-select
                            v-model="dataValue"
                            filterable
                            remote
                            style="width: 100%"
                            v-bind="$attrs"
                            :clearable="configOptions.clearable"
                            reserve-keyword
                            @clear="doClear"
                            no-data-text="No Data"
                            loading-text="Loading..."
                            :remote-method="remoteQuery"
                            :loading="loading">
                        <el-option
                                v-for="(data,index) in dataList"
                                :key="data.id || index"
                                :label="data[configOptions['remoteLabelCode'] || 'label']"
                                :value="data[configOptions['remoteValueCode'] || 'value']">
                        </el-option>
                    </el-select>
                </div>
            </el-col>
        </el-row>

    </div>
</template>

<script>
    import api from "../commonTableApi"

    export default {
        name: "SelectComp",
        data() {
            return {
                selectValue:'',
                selectList:[
                    {i18n:'SEComment.in',value:'in'},
                    {i18n:'SEComment.notIn',value:'notIn'},
                    {i18n:'SEComment.eq',value:'eq'},
                    {i18n:'SEComment.notEq',value:'notEq'},
                ],
                loading:'',
                dataValue: '',
                options:{},//总配置
                dataList:[],
                configOptions:{},//配置项里面的options节点
            }
        },
        methods: {
            doClear(){
                if(this.configOptions.remote){
                    this.dataList = [];
                    return
                }
                this.$set(this,'dataValue',"");
            },
            remoteQuery(query){
                console.log("query:",this.configOptions)
                if(!(this.configOptions.remote && this.configOptions.remoteUrl)){
                    return;
                }

                this.dataList = [];
                if(!query){
                    return;
                }
                let reqType =  this.configOptions.remoteReqType;
                if(!reqType){
                    return;
                }
                let resultCodes = this.configOptions.resultCodes || ['list'];
                let param = {};
                param[this.configOptions.remoteParamKeyCode] = query;
                this.loading = true;
                switch (reqType) {
                    case "FormData":
                        api.doRemoteFormData(this.configOptions.remoteUrl,param,null,(res)=>{
                            this.loading = false;
                            if(res.status == 200 && res.data ){
                                let resultData = res.data;
                                resultCodes.forEach(code=>{
                                    resultData = resultData[code];
                                })
                                this.dataList = resultData || [];
                            }
                        })
                        break;
                    case "json":
                        api.doRemote(this.configOptions.remoteUrl,param,null,(res)=>{
                            this.loading = false;
                            if(res.status == 200 && res.data ){
                                let resultData = res.data;
                                resultCodes.forEach(code=>{
                                    resultData = resultData[code];
                                })
                                this.dataList = resultData || [];
                            }
                        })
                        break;
                }

            },
            initSelect(){
                this.dataList = this.configOptions.dicData || []
                let url = this.configOptions.remoteUrl;
                if(!url){
                    return;
                }
                let reqType =  this.configOptions.remoteReqType ;
                if(!reqType){
                    return;
                }
                let params  = this.configOptions.remoteParams || {};
                let resultCodes = this.configOptions.resultCodes || ['list'];
                switch (reqType) {
                    case 'get':
                        api.doRemoteGet(this.configOptions.remoteUrl,params).then(res=>{
                            if(res.status==200 && res.data){
                                let resultData = res.data;
                                if(resultCodes!='parentData'){
                                    resultCodes.forEach(code=>{
                                        resultData = resultData[code];
                                    })
                                }
                                this.dataList = resultData || [];

                            }
                        }).catch(err=>{

                        })
                        break;
                    case "FormData":
                        api.doRemoteFormData(this.configOptions.remoteUrl,params,null,(res)=>{
                            if(res.status == 200 && res.data ){
                                let resultData = res.data;
                                resultCodes.forEach(code=>{
                                    resultData = resultData[code];
                                })
                                this.dataList = resultData || [];
                            }
                        })
                        break;
                    case "json":
                        api.doRemote(this.configOptions.remoteUrl,param,null,(res)=>{
                            if(res.status == 200 && res.data ){
                                let resultData = res.data;
                                resultCodes.forEach(code=>{
                                    resultData = resultData[code];
                                })
                                this.dataList = resultData || [];
                            }
                        })
                        break;
                }
            }
        },
        created() {
            let includeCondition = false
            this.selectList.forEach(s=>{
                if(s.value == this.selectValue){
                    includeCondition = true;
                }
            })
            this.selectValue = includeCondition ?  this.formObj.condition : 'eq';
            this.dataValue = this.formObj ? this.formObj.conditionValue : '';
            //初始化配置
            this.configOptions = {};
            if(!this.allOptions.options){
                this.options = Object.assign({},this.allOptions,{options:{}});
            }else {
                this.options = Object.assign({},this.allOptions);
                this.configOptions = JSON.parse(this.options.options);
            }

            if(!this.configOptions.remote){
                this.initSelect();
            }
        },
        mounted() {
        },
        watch:{
            selectValue:{
                immediate:true,
                handler(newV){
                    let obj = {
                        condition:newV,
                        conditionValue:this.dataValue
                    }
                    this.$emit("update:value", obj)
                }
            },
            dataValue: {
                deep: true,
                immediate: true,
                handler(newV, oldV) {
                    let obj = {
                        condition:this.selectValue,
                        conditionValue:newV
                    }
                    this.$emit("update:value", obj)
                }
            },
            formObj:{
                deep:true,
                handler(newV,oldV){
                    if(newV){
                        let {conditionValue} = newV;
                        this.$set(this,'dataValue',conditionValue)
                    }
                }
            }
        },
        computed:{
            getClearable:{
                get(){
                    let cl = false;
                    try{
                        let op = JSON.parse(this.options.options)
                        cl = op.clearable;
                    }catch (e) {
                        //console.log(e);
                    }
                    return cl;
                },
                set(){}
            }
        },
        components: {},
        props:{
            value: null,
            allOptions: {},
            defaultFilter:String,
            formObj:{}
        }
    }
</script>

<style scoped>
    .SelectComp {
    }
</style>