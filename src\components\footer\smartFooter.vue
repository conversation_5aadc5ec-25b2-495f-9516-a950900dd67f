<template>
    <div class="smartFooter page-no-print" id="smartFooter">
        <el-row>
            <el-col :span="24" style="text-align: center">
                <span class="wyntbs"><b>WHEN YOU NEED TO BE SURE</b></span>
            </el-col>
        </el-row>
        <el-row>
            <el-col>
                &nbsp;
            </el-col>
        </el-row>
        <el-row class="smartFooter_bar">
            <el-col :offset="3" :span="6">
                <a class="smart_footer_a" :href="$t('footerBar.termsOfUsePdfPath')" target="_blank">{{$t('footerBar.termsOfUse')}}</a>
            </el-col>
            <el-col :span="6">
                <a class="smart_footer_a" :href="$t('footerBar.DPPPdfPath')" target="_blank">{{$t('footerBar.DPP')}}</a>
            </el-col>
            <el-col :span="6">
                <span>&copy; SGS Société Générale de Surveillance SA. (2025)</span>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    export default {
        name: "smartFooter",
        data() {
            return {}
        },
        methods: {
            initBaidu(){
                var _hmt = _hmt || [];
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?b42460c118cbaa723a1945fdf6b677d8";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            }
        },
        mounted() {
        },
        created() {
            //console.log("footer:env",process.env.NODE_ENV);
            if("production" == process.env.VUE_APP_ENV){
                this.initBaidu();
            }
        },
        watch: {},
        computed: {},
        props: {},
        updated() {
        },
        beforeDestory() {
        },
        destoryed() {
        },
        components: {}
    }
</script>

<style scoped lang="scss">
    .smartFooter {
        .wyntbs{
            font-size: 18px;
            color: #FF6600;
        }
        .smartFooter_bar{
            text-align: center;
            background-color: #3c515b;
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            color: white !important;
            .smart_footer_a{
                color: white !important;
            }
            .smart_footer_a:hover{
                color: #ff6600 !important;
            }
        }
    }
</style>