"use strict";
import i18n from "@/lang";

let color = ["#34A853", "#EA4336", "#D9D9D9"];
const reportLineOptions = {
    title: {
        // text: i18n.tc("dataAnalytics.overall"),
        // subtext: i18n.tc("dataAnalytics.noOfJob"),
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'line',
            lineStyle: {
                color: 'rgba(113, 113, 113, 1)',
            },
        },
        backgroundColor: 'rgba(255, 255, 255, 1)',
        borderColor: 'transparent'
    },
    color,
    legend: {
        icon: 'roundRect',
        itemWidth: 24,
        itemHeight: 3,
        itemGap: 15,
        top: 22,
        textStyle: {
            fontFamily: 'MicrosoftYaHei',
            fontSize: 14,
        },
        data: [],
        languageKey: [
            "dataAnalytics.pass",
            "dataAnalytics.fail",
            "dataAnalytics.seeResult"
        ]
    },
    grid: {
        top: '22%',
        left: '6%',
        right: '3%',
        bottom: '10%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        axisTick: {
            show: false // 不显示坐标轴刻度线
        },
        axisLine: {
            show: false, // 不显示坐标轴线
        },
        splitLine: {
            show: false // 不显示网格线
        },
        offset: 10,
        data: [],
    },
    yAxis: {
        minInterval:1,
        name: `(${i18n.t("dataAnalytics.noOfJob")})`,
        languageKey: "dataAnalytics.noOfJob",
        type: 'value',
        axisTick: {
            show: false // 不显示坐标轴刻度线
        },
        axisLine: {
            show: false, // 不显示坐标轴线
        },
        splitLine: {
            show: false // 不显示网格线
        },
        offset: 25,
    },
    series: [
        {
            name: i18n.t("dataAnalytics.pass"),
            languageKey: "dataAnalytics.pass",
            type: "line",
            code: 'Pass',
            data: [],
            areaStyle: {
                //折线图颜色半透明
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(32, 180, 38, 0.1)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(32, 180, 38, 0)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }
            },
        },
        {
            name: i18n.t("dataAnalytics.fail"),
            languageKey: "dataAnalytics.fail",
            type: "line",
            code: 'Fail',
            data: [],
            areaStyle: {
                //折线图颜色半透明
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(215, 41, 38, 0.1)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(215, 41, 38, 0)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }
            },
        },
        {
            name: i18n.t("dataAnalytics.seeResult"),
            languageKey: "dataAnalytics.seeResult",
            type: "line",
            code: 'See Result',
            data: [],
            areaStyle: {
                //折线图颜色半透明
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(147,147, 153, 0.1)' // 0% 处的颜色
                    }, {
                        offset: 1, color: 'rgba(147,147, 153, 0)' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                }
            },
        },
    ]
};

export {
    reportLineOptions
}
