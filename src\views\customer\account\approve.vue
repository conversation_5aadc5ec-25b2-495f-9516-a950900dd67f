<template>
    <div>
        <el-form :inline="true" :model="formInline" size="medium" class="text-right" @submit.native.prevent>
            <el-form-item>
                <el-input v-model="query.userName" @input="onSearch" @keyup.enter.native="onSearch" @clear="onSearch" :placeholder="$t('account.userName')" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <!-- <el-form-item>
                <el-button @click="onSearch" class="line-btn">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.search')}}
                </el-button>
            </el-form-item> -->
        </el-form>
        <el-table
                :data="tableData"
                style="width: 100%">
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="userName"
                    :label="$t('account.userName')">
            </el-table-column>
            <el-table-column
                    prop="email"
                    :label="$t('account.email')">
            </el-table-column>
            <el-table-column
                    prop="mobile"
                    :label="$t('account.mobile')">
            </el-table-column>

            <el-table-column
                    prop="approveStatus"
                    :label="$t('common.status.title')"
                    align="center">
                <template scope="scope">
                    <span v-if="scope.row.approveStatus=='1'">{{$t('account.approveStatus.toBeReviewed')}}</span>
                    <span v-if="scope.row.approveStatus=='10'">{{$t('account.approveStatus.emailConfirm')}}</span>
                    <span v-else-if="scope.row.approveStatus=='90'">{{$t('account.approveStatus.auditPass')}}</span>
                    <el-tooltip v-else-if="scope.row.approveStatus=='80'" class="item" effect="dark" :content="scope.row.opinion==''?$t('common.reject'):scope.row.opinion" placement="top">
                        <span>{{$t('common.reject')}}</span>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    :label="$t('common.operationTime')"
                    width="160">
            </el-table-column>
            <el-table-column
                    :label="$t('operation.title')"
                    width="160"
                    align="center">
                <template slot-scope="scope">
                    <el-button v-if="permissionList.passBtn" type="text" @click="rowView(scope.row,90)">{{$t('operation.pass')}}</el-button>
                    <el-button v-if="permissionList.rejectBtn" type="text" @click="rowView(scope.row,80)">{{$t('operation.reject')}}</el-button>
                </template>
            </el-table-column>

        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>

        <el-dialog :visible.sync="approveVisible"
                   :title="$t('account.title.approve')">
            <el-form :model="form" ref="approveForm" size="medium">
                <el-form-item :label="$t('department.label')" :rules="{ required: true, message: $t('account.validate.departmentBlur'), trigger: 'change' }" prop="customerDepartmentIds">
                    <el-select
                            clearable
                            multiple
                            v-model="form.customerDepartmentIds"   style="width:100%">
                        <el-option v-for="(dept,index) in deptData" :label="dept.departmentName"
                                   :value="dept.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('account.post')" :rules="[{ required: true, message: $t('account.validate.postBlur'), trigger: 'change' }]" prop="posts">
                    <el-select multiple clearable v-model="form.posts" style="width:100%">
                        <el-option v-for="(post) in postData"
                                   :key="post.postId"
                                   :label="post.postName"
                                   :value="post.postId"></el-option>
                    </el-select>
                </el-form-item>
              <el-form-item :label="$t('register.serviceUnit')"
                            required
                            :rules="[{ required: true, message: $t('account.validate.serviceUnitBlur'), trigger: 'change' }]"
                            prop="selServiceUnits">
                <el-select v-model="form.selServiceUnits"
                           multiple
                           collapse-tags
                           :placeholder="$t('register.serviceUnitBlur')"
                           @change="selectServiceTypeChange" style="width: 100%;">
                  <el-option v-for="(serviceUnit, index) in serviceUnits" :key="serviceUnit.serviceUnitCode" :label="serviceUnit.serviceUnitName"
                             :value="serviceUnit.serviceUnitCode">
                  </el-option>
                </el-select>
              </el-form-item>


            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="approveVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="approve()" :loading="submitLoading">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="rejectVisible"
                  >
            <el-form :model="form" ref="rejectForm" size="small">
                <el-form-item :label="$t('common.opinion')" :rules="[{ required: true, message: $t('account.validate.opinionBlur'), trigger: 'blur'}]" prop="opinion">
                    <el-input
                            v-model="form.opinion"
                            type="textarea"
                            :autosize="{ minRows: 2, maxRows: 4}"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="rejectVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="reject()" :loading="submitLoading">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

    import {getPageByUser,add,detail,remove,approve} from "@/api/customer/account";
    import {getPosts,saveAccountPosts,getUsers} from "@/api/customer/externalAccount";
    import {getDepts} from "@/api/customer/customerDept";
    import {addAccountDept} from "@/api/customer/accountDept";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {mapGetters} from "vuex";
    import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";
    import {queryCustomerForId} from "@/api/customer/customer";

    export default {
        name: "accountApprove",
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        data(){
            return{

                serviceUnits:[],
                dialogFormVisible: false,
                tableData: [],
                form: {
                    approveStatus:'',
                    customerDepartmentIds:[],
                    selServiceUnits:[],
                    serviceUnitCode:[],
                    defaultServiceUnitCode:''
                },
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                accountForm:{},
                approveVisible: false,
                rejectVisible:false,
                deptData:[],
                postData:[]
            }
        },
        computed: {
            ...mapGetters(["permission","userInfo","language"]),
            permissionList() {
                return {
                    passBtn: this.vaildData(this.permission['sgs:customer:account:pass'],false),
                    rejectBtn: this.vaildData(this.permission['sgs:customer:account:reject'],false),
                };
            }
        },
      watch: {
        'language': function () {
          this.initData();
        }
      },
        methods:{
          async initData(){
            await  this.queryServiceType();
            //加载该公司的Service Unit
            await  this.queryCustomerInfo();
          },
          async queryCustomerInfo() {
            if (!objectIsNull(this.userInfo.companyId)) {
              await queryCustomerForId(this.userInfo.companyId).then(res => {
                debugger;
                const data = res.data.data;
                if (!objectIsNull(data)) {
                  let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits, data.serviceUnits);
                  debugger;
                  this.serviceUnitDataItems = resultObj.serviceUnitDataItems;

                  if (!objectIsNull(this.serviceUnitDataItems)) {
                    let newServiceUnits = [];
                    for (let serviceUnitObj of this.serviceUnits) {
                      if (this.serviceUnitDataItems.includes(serviceUnitObj.serviceUnitCode)) {
                        newServiceUnits.push(serviceUnitObj)
                      }
                    }
                    //去除other的Serviceunit
                    newServiceUnits = newServiceUnits.filter(item => item.productLineCode != 'other');
                    this.serviceUnits = newServiceUnits;
                  }
                }
              });
            }
          },
          selectServiceTypeChange(values) {
            debugger;
            //将默认的设置为null
            this.selDefaultServiceUnit=null;
            let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, values,this.form.selServiceUnits);
            this.mergeFormServiceUnitData(serviceUnitObj);
          },
          mergeFormServiceUnitData(serviceUnitObj){
            if(!objectIsNull(serviceUnitObj)){
              this.$set(this.form, 'serviceUnits', JSON.stringify(serviceUnitObj.serviceUnits));
              this.$set(this.form, 'productLineCodes', JSON.stringify(serviceUnitObj.productLines));
              this.$set(this.form, 'serviceDomains', JSON.stringify(serviceUnitObj.serviceDomains));
              //this.defaultServiceUnitDatas=serviceUnitObj.selServiceUnitDatas;
            }
           /* debugger;
            this.$set(this.form, 'serviceUnitCode', this.form.selServiceUnits);
            if(!objectIsNull(serviceUnitObj)){
              let serviceUnits = serviceUnitObj.serviceUnits;
              let defaultServiceUnitsCode='';
              for(let serviceUnit of serviceUnits){
                if(serviceUnit.isDefault){
                  defaultServiceUnitsCode=serviceUnit.serviceUnit;
                  break;
                }
              }
              this.$set(this.form, 'defaultServiceUnitCode', defaultServiceUnitsCode);
            }*/
          },
          async queryServiceType() {
            let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
            //去除other的Serviceunit
            serviceUnitsDatas = serviceUnitsDatas.filter(item => item.productLineCode != 'other');
            this.serviceUnits=serviceUnitsDatas;
            console.log(serviceUnitsDatas);
          },
            onSearch() {
                this.page.currentPage=1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                // 查询审核状态未通过审核的数据
               // params['approveStatus_notequal']=90;
                getPageByUser(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    this.tableData = res.data.data.records;
                    this.page.total = res.data.data.total;
                });
            },
            submitForm(){
                add(this.form).then(res =>{
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                    this.dialogFormVisible=false;
                    this.onLoad(this.page);
                });
            },
            approve(){
                this.$refs['approveForm'].validate((valid) => {
                    if (valid) {

                        this.form.approveStatus = 90;
                        // 部门逻辑处理
                        const customerDepartments = [];
                        this.form.customerDepartmentIds.forEach((deptId) => {
                            let customerDept = {'id':deptId,'departmentName':this.departmentName(deptId)};
                            customerDepartments.push(customerDept);
                        })
                        this.form.depatments=customerDepartments;

                        approve(this.form).then(res => {

                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.approveVisible = false;
                            this.onLoad(this.page);
                        });
                    } else {
                        return false;
                    }
                })
            },
            addAccountDepts(accountForm){
                addAccountDept(accountForm).then(res =>{

                });
            },
            reject(){
                this.$refs['rejectForm'].validate((valid) => {
                    if (valid) {
                        this.form.approveStatus = 80;
                        approve(this.form).then(res => {
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.rejectVisible = false;
                            this.onLoad(this.page);
                        });
                    } else {
                        return false;
                    }
                })
            },
            rowView(row,approveStatus){
              this.form = row;
              //处理ServiceUnit回显
              debugger;
              if (!objectIsNull(row.serviceUnits)) {
                let resultObj = serviceUnitTool.showSelServiceUnitDatas(this.serviceUnits, row.serviceUnits);
                this.$set(this.form, 'selServiceUnits', resultObj.serviceUnitDataItems);
                this.$set(this.form, 'serviceUnitCode', resultObj.serviceUnitDataItems);
              } else {
                this.$set(this.form, 'selServiceUnits', []);
                this.$set(this.form, 'serviceUnitCode', []);
              }


              if (90 === approveStatus) {
                this.approveVisible = true;
              } else {
                this.rejectVisible = true;
              }
            },
            departmentName(id){
                let dept =this.deptData.filter((item)=>{
                    return item.id==id
                })
                if(!validatenull(dept)){
                    return dept[0].departmentName;
                }else{
                    return ''
                }
            },
            currentChange(currentPage) {
              this.page.currentPage = currentPage;
              this.onLoad(this.page);
            },
            sizeChange(pageSize) {
              this.page.pageSize = pageSize;
              this.onLoad(this.page);
            },

        },
        created() {
            this.initData();
            this.onLoad(this.page);
            const  customerId=this.userInfo.companyId;
            getDepts(customerId).then(res =>{
                const data = res.data.data;
                this.deptData = data;
            });
            getUsers({"companyId": this.userInfo.userMgtCompanyId,"id":this.userInfo.userMgtId}).then(res =>{
                const data = res.data.rows;
                if(!validatenull(data)){
                    this.postData = data[0].postEntityList;
                }
            });
        },


    }
</script>

<style scoped>

</style>
