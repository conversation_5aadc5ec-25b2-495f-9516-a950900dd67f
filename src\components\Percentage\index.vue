<template>
    <div class="smart_views_percentage" id="smart_views_percentage">
        <el-table style="width: 100%"
                  v-if="showTable" size="small"
                  fit
                  :border="false"
                  :show-header="false"
                  :data="getDataList">
            <el-table-column prop="percentage" width="100px">
                <template #default="{ row, $index }">
                    <div>
                        <el-input-number
                                style="width: 60px"
                                size="mini"
                                v-model.number="row.percentage"
                                :controls="false"
                                :step="1"
                                step-strictly
                                :min="1"
                                @blur="() => changePercentage(row, $index)"
                        ></el-input-number>
                        <el-button style="cursor: auto; color: black;" type="text">%</el-button>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="ingredients" width="200px">
                <template #default="{ row,$index }">
                    <el-input
                            v-if="!useSelect"
                            size="mini"
                            clearable
                            placeholder="Please input composition"
                            type="text"
                            v-model="row.ingredients"
                    ></el-input>
                    <el-select
                            v-if="useSelect"
                            size="mini"
                            v-model="row.ingredients"
                            @change="changeIngredients(row)"
                    >
                        <el-option
                            v-for="(item,ind) in row.sourceValue"
                            :key="'ing_'+ind"
                            :label="item.name"
                            :value="item.code"
                        ></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="" width="80">
                <template #default="{ row, $index }">
                    <el-button
                            size="medium"
                            v-if="lt100 && showAdd(row, $index)"
                            type="text"
                            :icon="Plus"
                            @click="addRow"
                    ></el-button>
                    <el-button
                            size="medium"
                            v-if="showDel(row, $index)"
                            type="text"
                            :icon="Delete"
                            @click="delRow($index)"
                    ></el-button>
                </template>
            </el-table-column>
            <el-table-column prop="extendField" v-if="useSelect" >
                <template #default="{ row, $index }">
                    <el-checkbox-group
                            style="width: 100%"
                            v-model="row.extendField"
                        >
                        <el-checkbox
                            v-for="(chItem,innd) in getCheckboxGroup(row)"
                            :key="'chItme_'+innd"
                            :label="chItem.code"
                        >
                            {{chItem.name}}
                        </el-checkbox>
                    </el-checkbox-group>
                </template>
            </el-table-column>
        </el-table>

        <div v-show="showTips" style="position: relative">
            <div class="el-form-item__error">The total composition should be 100%.</div>
        </div>
        <div v-show="showNullTips" style="position: relative">
            <div class="el-form-item__error">The composition can't be empty.</div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
    defineExpose
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'
import CommonTable from "@/components/TableList/CommonTable.vue";
import productApi from '@/api/product.ts'
import {Plus,Delete} from '@element-plus/icons-vue'

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)

// Props
const props = defineProps({
    formData: {
        type: Object,
        required: true
    },
    fieldCode:{
        type:String,
        required:true
    },
    useSelect:{
        type:Boolean,
        default:false
    }
})

// State
const showTips = ref(false)
const showNullTips = ref(false)
const showTable = ref(false)
const lt100 = ref(true)

const getDataList = computed(() => props.formData[props.fieldCode] || [])

// Methods
const delRow = (index)=> {
    props.formData[props.fieldCode].splice(index, 1)
    showTips.value = true
}

const showAdd = (row, rowIndex)=> {
    return rowIndex === props.formData[props.fieldCode].length - 1
}

const showDel = (row, rowIndex) =>{
    return props.formData[props.fieldCode].length > 1
}

const changePercentage = (row, rowIndex) =>{
    let currentValue = row.percentage
    if (currentValue == null || currentValue === '') {
        props.formData[props.fieldCode].forEach((da, index) => {
            if (index === rowIndex) {
                da.percentage = 1
            }
        })
        return
    }

    let sum = 0
    props.formData[props.fieldCode].forEach((da, index) => {
        if (index !== rowIndex) {
            sum += Number(da.percentage)
        }
    })

    if (currentValue + sum > 100) {
        props.formData[props.fieldCode][rowIndex].percentage = 100 - sum
    }
}

const addRow = ()=> {
    let percentage = 0
    props.formData[props.fieldCode].forEach(da => {
        percentage += Number(da.percentage)
    })

    if (percentage >= 100) {
        return
    }

    percentage = percentage === 0 ? 99 : percentage
    const obj = {
        seq: props.formData[props.fieldCode].length + 1,
        ingredients: '',
        percentage: 100 - percentage,
        extendField:[] // TODO Ken 这是预留的属性，真实属性名称需要看接口结构
    }
    if(props.useSelect){
        obj['sourceValue'] = [ //TODO Ken 这是暂定的联动属性结构
            {name:'Test1',code:'test1',checkGroupList:[]},
            {name:'Test2',code:'test2',
                checkGroupList:[
                {name:'Checkbox1',code:'code1'},
                {name:'Checkbox2',code:'code2'},
                {name:'Checkbox3',code:'code3'},
                {name:'Checkbox4',code:'code4'},
                {name:'Checkbox5',code:'code5'},
                {name:'Checkbox6',code:'code6'},
            ]}
        ]
    }
    props.formData[props.fieldCode].push(obj)
}
const changeIngredients = (row)=>{
    row.extendField = [];
}
const getCheckboxGroup = (row)=>{
    console.log("change row",row);
    let {ingredients,sourceValue} = row;
    if(!ingredients){
        return []
    }
    let item = sourceValue.find(s=>s.code==ingredients);
    let {checkGroupList} = item;
    return checkGroupList;

}
const parseformData = ()=> {
    if (!props.formData[props.fieldCode]
        || props.formData[props.fieldCode].length === 0) {
        props.formData[props.fieldCode] = []
        addRow()
    }
}
//外部组件save 或者submit时，调用这个方法，根据返回值决定流程是否继续
const validatePercentage = ()=> {
    let list = props.formData[props.fieldCode] || []
    let sum = 0
    let hasNull = false
    list.forEach(da => {
        let { percentage, ingredients } = da
        sum += Number(percentage)
        if (!ingredients) {
            hasNull = true
        }
    })

    if (sum !== 100) {
        showTips.value = true
        return false
    }
    showTips.value = false

    if (hasNull) {
        showNullTips.value = true
        return false
    }
    return true
}

// Watcher: 监听 formData[props.fieldCode]
watch(
    () => props.formData[props.fieldCode],
    val => {
        let sum = 0;
        (val || []).forEach(da => {
            sum += Number(da.percentage)
        });
        showTips.value =  sum < 100;
        lt100.value = sum < 100
    },
    { deep: true, immediate: false }
)

// Lifecycle
onMounted(() => {
    parseformData()
    // 模拟 nextTick
    setTimeout(() => {
        showTable.value = true
    }, 0)
})

defineExpose({validatePercentage})
</script>

<style lang="scss">
.smart_views_percentage {
  width: 100%;
  .el-table__body-wrapper {
    min-height: 53px;
  }
}
</style>
