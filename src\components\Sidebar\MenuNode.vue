<template>
  <div v-if="menuItem.children" class="navigation-item-wrapper">
    <!-- 实际可点击 -->
    <el-menu-item
      v-if="validatenull(menuItem[childrenKey]) && vaildRoles(menuItem)"
      :index="menuItem.id"
      @click="open(menuItem)"
      :key="menuItem[labelKey]"
    >
      <template #title>
        <span :alt="menuItem[pathKey]" class="nosub-menu">
          {{ menuItem.name }}
        </span>
      </template>
    </el-menu-item>
    <!-- 嵌套 -->
    <el-sub-menu
      :index="menuItem.id"
      :id="
        menuItem.name === '设置' || menuItem.name === 'Setting'
          ? 'guideSetting'
          : 'noSetting'
      "
    >
      <template #title>
        <span>{{ menuItem.name }}</span>
      </template>
      <template v-for="(child, cindex) in menuItem[childrenKey]">
        <!-- 有 child -->
        <MenuNode
          v-if="!validatenull(child[childrenKey])"
          :key="child[labelKey]"
          :menu-item="child"
        />
        <!-- 无 child -->
        <el-menu-item
          v-else
          :key="cindex"
          :index="`${child.id},${cindex}`"
          @click="open(child)"
        >
          <template #title>
            <span>{{ child.name }}</span>
          </template>
        </el-menu-item>
      </template>
    </el-sub-menu>
  </div>
</template>
<script setup>
import { computed } from 'vue'
import { validatenull } from '@/utils/validate'
import { useStore } from 'vuex'

const props = defineProps({
  menuItem: {
    type: Object,
    required: true,
  },
})

const store = useStore()

const roles = computed(() => store.state.user.roles)
const labelKey = 'label'
const pathKey = 'path'
const iconKey = 'icon'
const childrenKey = 'children'

const vaildRoles = (item) => {
  item.meta = item.meta || {}
  return item.meta.roles ? item.meta.roles.includes(roles.value) : true
}

const open = (item) => {
  if(/\/web\/|\/knowledge\//.test(item.path)){
    window.open(item.path, '_blank');
  }else{
    window.open('/#' + item[pathKey], '_blank')
  }
}
</script>

<style scoped>
.nosub-menu {
  font-size: 20px !important;
  /* position: relative;
  top: 2px; */
}
</style>
