export default {
    tip: '提示',
    reminder:'提醒',
    title: '通标标准技术服务有限公司门户网站',
    logoutTip: '退出系统, 是否继续?',
    submitText: '确定',
    cancelText: '取消',
    select: '请选择',
    search: '请输入搜索内容',
    searchTxt: '请输入',
    menuTip: '没有发现菜单',
    loading: '拼命加载中',
    uploadType: '上传图片只能是 JPG 格式!',
    uploadType_PDF: '上传附件只能是 PDF 格式!',
    uploadSize: '上传图片大小不能超过 2MB!',
    uploadFileSize: '上传文件大小不能超过10MB!',
    uploadFileSizeError: '上传文件大小不能超过',
    uploadSuccess: '上传成功',
    uploadLoadingText: '文件上传中…',
    uploadExceed_1:'最多支持上传一份附件，请删除后重新上传',
    systemLanguage: '系统语言',
    number: '编号',
    piece: '件',
    success: '成功',
    NoData: '暂无数据',
    ascendingOrder: '升序',
    descendingOrder: '降序',
    language:{
        add:'添加语言',
        name:'语言',
        curLanguage:'当前语言',
        default: '默认语言',
        zh: '中文',
        en:'英文',
        repeatLanguageMsg:'已存在相同语言数据，请勿重复添加',
        manage:'语言管理',
        validate: {
            selLanguageBlur: '请选择对应语言',
            setDefaultLanguage:'请设置默认语言'
        }
    },
    form: {
        printBtn: '打 印',
        mockBtn: '模 拟',
        submitBtn: '提 交',
        emptyBtn: '清 空',
        exportTemplate: '下载模板',
        importTip: '请上传 .xls .xlsx 标准格式文件',
      },
    // tip: {
    //     select: '请选择',
    //     input: '请输入',
    //   },
    operation: {
        title: '操 作',
        add: '添 加',
        edit: '编 辑',
        modify: '修 改',
        remove: '删 除',
        chat: '沟通',
        view: '查 看',
        search: '查 询',
        submit: '提 交',
        confirm: '确 认',
        toBeConfirm:'待确认',
        cancel: '取 消',
        reset: '清 空',
        resetTip: '清空过滤',
        disable: '禁 用',
        approve: '审 核',
        pass: '通 过',
        reject: '拒 绝',
        auth: '授 权',
        select: '选 择',
        upload: '点击上传',
        download: '下载',
        detail: '详情',
        confirmDelete: '确定将选择数据删除！',
        confirmStatus: '确定更新选择数据！',
        confirmEnable: '确定将启用选择数据！',
        confirmDisable: '确定将禁用选择数据！',
        confirmDeleteRoot: '存在子节点，确定删除吗',
        save: '保 存',
        finalizeReview: '完成评定',
        complete: '完 成',
        isEnable: '是否启用',
        saveAsTemplate: '另存为模板',
        back: '返回',
        goBack: '返 回',
        backToList: '返回列表',
        print: '预览/打印',
        preview:'预览',
        copy: '复制',
        downLoad: '导出',
        export: '导出',
        exportTRF:'导出申请表记录',
        exportReportFiles:'导出报告文件',
        exportReportTips:'请先筛选TRF。',
        exportReportMaxTips:'最多可以导出20个报告文件，请调整过滤条件。',
        selected: '选择',
        remark: '备注',
        more: '更多',
        pleaseSelect: '请选择',
        and: '和',
        pleaseEnterContent:'请输入内容...',
        batchImportAuthorization:'批量授权导入',
    },
    notice: {
        maintenanceNotice: 'SGS SMART 系统维护通知',
        maintenanceMessage: 'SGS SMART将在北京时间2021-03-26日19点到2021-03-28日0点升级，升级期间系统将不能使用。多有不便，敬请谅解！',
    },

    page: {
        prePage: '上一页',
        nextPage: '下一页',
        clock: '顺时针',
        counterClock: '逆时针',
    },
    datePicker: {
        startTime: '开始时间',
        endTime: '结束时间',
        lastWeek: '最近一周',
        lastMonth: '最近一个月',
        lastThreeMonths: '最近三个月',
        lastHalfYear: '最近半年',
        lastYear: '近一年',
        error: {
            timeSpanMsg: '只能查看一年内的数据.',
        }
    },
    wel1: {
        draftTrf: '待递交',
        trfSubmitted: '已提交',
        jobPreparation: '已受理',
        testingInProgress: '测试中',
        reportCompleted: '完成',
        quotationToBeConfirmed: '报价单待确认',
        quotationConfirmed: '已确认报价单',
        myTrf: '我的申请单',
        newApplication: '新建申请单',
        pass: '通过',
        fail: '未通过',
        seeResult: '不做评判',
        total: '总计',
        statusTracking: '状态跟踪',
        documentLibrary: '资料库',
        documentType: '文档类型',
        pbiType:'Link Type',
        defaultPbiType: 'Common Link',
        AflPbiType:'Power BI',
        cpCustomerReport:'CP客户定制报表',
        knowledgeSharing: '供应商知识分享',
        documentSharing: '文档分享',
        SGSNewService: 'SGS服务',
        SGSSafeguard: '最新资讯',
        newTestRequestForm: '最新申请单',
        hot: "热点",
        requisitionStatus: "申请单状态",
        backToHome:'回到首页'
    },
    api: {
        success: '操作成功',
        successfully: '保存成功',
        error: '操作失败'
    },
    term: {
        productLine: '产品线',
        customerGroup: '客户组',
        customer: '客户',
        supplier: '供应商',
        uploadSubmit: '点击上传',
        customerGroupCode: '客户组编码',
        customerGroupBlur: '请选择客户组',
        customerGroupOrCustomer:'客户组/客户'
    },
    common: {
        status: {
            title: '状态',
            enable: '启用',
            disable: '禁用',
            push: '已推送',
            notPush: '未推送',
            reject: '已拒绝'
        },
        all:'全部',
        register: '注册',
        effectiveDate: '生效日期',
        invalidDate: '失效日期',
        operator: '操作人',
        time: '时间',
        operationTime: '操作时间',
        other: '其它',
        default:'设为默认',
        isDefault: '默认',
        yes: '是',
        no: '否',
        enable: '启用',
        disable: '禁用',
        approveStatus: '审核状态',
        pass: '通过',
        reject: '拒绝',
        isAdmin: '是否管理员',
        isChangeStatus: '是否修改状态',
        inputFilter: '输入关键字进行过滤',
        opinion: '审核意见',
        contacts: '联系人',
    },
    info: {
        base: '基础信息',
        attachment: '附件信息',
    },
    date:{
        start:'开始时间',
        end:'结束时间'
    },
    attachment: {
        title: '附件',
        name: '附件名称',
        documentFrom: '附件来源',
        attachmentType:'附件类型',
        attachmentSource:'来源',
        upload: '上传',
        updateTime: '最后更新时间',
        updateUser: '最后更新人',
        downloadAll:'下载全部',
        numberAttachments:'个附件'
    },
    user: {
        updateUser: '更新用户',
        createUser: '创建用户',
        updateTime: '更新时间',
        createTime: '创建时间',
        createDate: '创建日期',
        createBy: '创建用户',
        status: '状态',
        oldPassword: '原密码',
        newPassword: '新密码',
        updPassword: '修改密码',
        companyAuthentication: '公司认证',
        updPasswordSuccess: '修改密码成功',
        updPhoneSuccess: '修改手机成功',
        phone: '手机号',
        checkCode: '验证码',
    },
    accountPer:{
        title:'账号授权',
        customerGroup:'客户组',
        authCompanyName:'授权客户',
        authCustomerNo:'授权客户编码',
        customerValidateError:'客户组或客户清必须选择一个',
    },

    route: {
        info: '个人信息',
        trf: '申请单',
        website: 'NKOP官网',
        avuexwebsite: 'avuex官网',
        dashboard: '首页',
        tags: '标签',
        store: '本地存储',
        api: '全局函数',
        logs: '日志监控',
        table: '表格',
        form: '表单',
        formDetail: '表单详情',
        formManagement: '表单管理',
        formDesign: '动态表单',
        formCodeManagement: '动态表单列表',
        formCode: '表单编码',
        data: '数据',
        permission: '权限',
        error: '异常',
        createTRF: '新建申请单',
        todoList:'待办列表',
        personalInfomation:'用户信息',
        personalSettings:'账号设置',
        companyManagement:'公司管理',
        companyInformation:'公司信息',
        extendSetting: '扩展信息设置',
        authorityManage: '授权管理',
        inputResult: '输入测试结果',
        tableDemo: 'CPSC E-Filing',
        documentList: '文档列表',
        documentAuthorization: '文档授权',
        documentMore: '历史文档',
        thirdLabRegister: '第三方实验室注册',
        thirdLabLogin: '第三方实验室登录',
        login: '登录',
        register: '注册',
        registerSuccess: '注册成功',
        simpleRegister: '快捷注册',
        simpleRegisterSuccess: '注册成功',
        simpleRegisterSuccessInfo: '注册成功',
        lock: 'lock',
        404: '404',
        403: '403',
        500: '500',
        systemUpdate: '系统升级',
        paySuccess: '支付成功',
        payError: '支付错误',
        customerManagement: '客户管理',
        customerAuthority: '客户授权',
        quotationAndInvoice: '报价 & 对账',
        inspection: '检验',
        accountPer: '账号权限',
        accountPerAuthority: '账号权限授权',
        SCM: 'SCM',
        documentShareing: '供应商知识共享',
        documentShareing1: '进阶数据分析',
        templateSetting: '模板设置',
        offlineCustomer: '线下注册',
        newClass: '班级信息',
        trainingAttendance: '培训考勤',
        trainingPerformance: '培训绩效',
        trfPreviewCA2: '预览',
        statisticsExecutiveSummary: '总体测试情况',
        statisticsPhysicalTestPerformance: '物理测试情况',
        statisticsSupplierPerformanceOverall: '供应商总体测试情况',
        statisticsSupplierPerformanceChemical: '供应商化学测试情况',
        statisticsSupplierPerformancePhysical: '供应商物理测试情况',
        customerMaterialConfig: '物料管理',
        customerMaterialList: '物料管理',
        materialReport: '物料报告',
        materialList: '物料列表',
        materialDetail: '物料详情',
        productList: '商品库',
        productDetail: '商品详情',
        materialTemplateList: '材料模板管理',
        productTemplateList: '商品模板管理',
        templateDetail: '模板详情',
        tableComponent: '表格组件',
        formComponent: '表单组件',
        quotationAndInvoice: '报价 & 对账',
        invoiceOrder: '支付订单',
        customerManagement: '公司管理',
        eFiling: 'E-Filing',
        ProtocolList: 'Protocol List',
        ProtocolDetail: 'Protocol Detail',
    },
    authority:{
        title:'授权',
        authorityManage:'授权管理',
        myAuthority:'我的授权',
        myApplication:'我的申请',
        stopAuthority:'取消授权',
        bossNo:'公司编码',
        account:'账号',
        validate:{
            bossNoVali:'请输入对方公司编码',
            accountValidate:'请输入对方账号',
        },
        rejectAuthority:'确认“拒绝授权”操作？',
        stopAuthorityConfirm:'确认“取消授权” 操作？',
        approveAuthorityConfirm:'确认“同意授权” 操作？',
        agree:'同意',
        status:{
            toBeAudited:'待审核',
            audited:'已审核',
            rejected:'已拒绝',
            cancelled:'已取消'
        }
    },
    login: {
        readMarketing:'我同意根据',
        readMarketingAfter:'接收相关的 SGS 更新和活动邀请。',
        agreedBtn:'我已阅读并同意',
        title: '登录 ',
        info: '微服务开发平台',
        tenantId: '请输入租户ID',
        username: '请输入注册邮箱或手机号',
        password: '请输入密码',
        rePassword: '请重复输入密码',
        passwordValidate: {
            lengthValidate: '密码长度最少为6位',
        },
        wechat: '微信',
        qq: 'QQ',
        phone: '请输入手机号',
        code: '请输入验证码',
        submit: '登录',
        toRegister: '注册',
        forgetPassword: '忘记密码',
        userLogin: '账号密码',
        phoneLogin: '手机号登录',
        thirdLogin: '第三方登录',
        msgText: '发送验证码',
        msgSuccess: '秒后重发',
        readAgree: '我已阅读并同意',
        serviceTrems: 'SGS 使用条款',
        serviceTremsEnd: '。',
        agreeServiceTerms: '请阅读并同意SGS服务条款和条件',
    },
    register: {
        continue: '继续',
        iGotIt: '我知道了',
        authentication: '认证',
        selCompany: '请选择公司',
        setUserName: '设置用户名',
        accountInfo: '完善账号信息',
        registSuccess: '注册成功',
        loginAccount: '登录账号',
        phoneNumber: '手机号',
        emailRegister: '使用邮箱注册',
        phoneRegister: '使用手机号注册',
        returnLogin: '已注册，请直接登录！',
        submitRegister: '提交注册申请',
        doublePasswordBlur: '请再次输入密码',
        passwordError: '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为 8 - 30位',
        addressBlur: '请输入公司地址',
        taxNoBlur: '请输入公司税号',
        bossNo: '客户编号',
        bossNoBlur: '无效的客户编号',
        companyNameBlur: '请输入公司名称',
        checkPassNo: '两次输入密码不一致!',
        phoneBlur: '请输入手机号码',
        verificationCodeBlur: '请输入验证码',
        telePhoneBlur: '目前只支持中国大陆的手机号码',
        contactBlur: '仅允许录入数字及特殊字符‘-’',
        pleaseInputCode: '请输入短信验证码',
        emailBlur: '请输入公司邮箱',
        loginAccountBlur: '请输入登录登录账号',
        emailRigthBlur: '请输入正确的邮箱地址',
        accountBlur: '请输入账号名称',
        userNameLength:'长度应为3到50个字符',
        passwordBlur: '请输入密码',
        sgsReportNoBlur: 'SGS历史测试报告号码',
        submitInfo: '邮箱已注册，请直接登录！',
        submitPhoneInfo: '手机号已注册，请直接登录！',
        lockError: '用户被注册，请联系管理员!',
        registerError: '该账号已被注册，请更换其他账号！',
        mobileRegisterError: '所录入的手机号已被注册，请更换其他手机号！',
        serveType: '产品类别',
        serviceUnit:'产品类别',
        serviceUnitBlur: '请选择产品类别',
        serveTypeBlur: '请选择产品类别',
        tip: {
            emailconfim: '请登录邮件进行验证，验证成功后发送给SGS审核',
            sgsapprove: '审核需要3~5个工作日，请耐心等待，审核通过后会邮件通知您',
            success: '注册成功！',
            login1: '目前您已可使用SGS SMART填写非买家申请单。在SGS 完成审核后，将会为您解锁更多的系统功能，谢谢！',
            login2: '登录',
            login3: '系统在线填写申请表。',
            success3: '如需获得更多来自SGS的服务，请在线完成企业认证，谢谢！',
            perfectInfo: '请完善信息，我们会尽快核实，为您解锁更多功能'
        },
        steps: {
            emailconfim: '邮件确认',
            sgsapprove: 'SGS审核'
        },
        message: {
            title: '注册通知',
            emailSendTip: '验证邮件已发送至 ',
            emailSendMessage: '请登录邮箱，点击激活链接完成注册',
            returnLogin: '返回登录页',
        },
        pictureCaptcha: {
            title: '图片验证码',
            validate: '请输入图片验证码',
            clickChange: '点击切换',
            captchaOvertime: '验证码已超时,请重新获取',
            captchaInvalid: '验证码已失效,请重新获取',
            captchaError: '验证码错误,请重新输入',
        },
        newRegister: {
            sendEmailTo: '验证码已发送至',
            commaSymbol:'，',
            belowCode:'请在下方输入验证码。',
            verifyEmail:'验证邮箱',
            verifyPhone:'验证手机号',
            verify:'验证',
            resend:'重新发送',
            second:'秒',
            donotGetCode:'未收到验证码？',
            sendIn5M:'验证码应在五分钟内送达。',
            checkSpamMail:'或者检查是否在垃圾邮件中。',
            checkSpamSms:'或者检查是否在垃圾短信中。',
            length6 :"验证码为6位数字",
        },
    },
    invoice: {
        title: '发票',
        name: '发票信息',
        invoiceDetail:'发票明细',
        addInvoice: '添加发票信息',
        editInvoice: '编辑发票信息',
        invoiceTitle: '公司名称',
        invoiceType: '发票类型',
        taxNo: '税号',
        address: '公司地址',
        tel: '联系电话',
        bankName: '开户银行',
        bankAccount: '开户银行账号',
        paymentStatus: '支付状态',
        fapiaoNo: '增值税发票号',
        fcCode: '',
        awbNo: '运单号',
        contactName: '账单收件人',
        invoiceNo: '付费编号',
        invoicePDF: '发票PDF',
        fapiaoIssuedDate: '出票日期',
        fapiaoIssuedStartDate: '对账单周期起始日期',
        fapiaoIssuedEndDate: '对账单周期截止日期',
        currency: '币种',
        preTaxAmount: '税前金额',
        totalAmount: '总金额',
        taxRate: '税率',
        paidAmount: '已支付金额',
        invoiceBalance: '待支付金额',
        reportNo: '报告号',
        clientReferenceNo: '客户参考信息',
        payee: '收款单位',
        statementAndInvoice: '对账单及发票',
        bill: '对账单',
        electronicInvoice: '电子发票',
        mergePay: '支付',
        paymentInfo: '支付账单信息',
        totalPayable: '应付总额',
        invoiceAmount: '账单金额',
        invoiceDate: '发票日期',
        payTop1: '请您在提交订单后',
        payTop2: '15分钟',
        payTop3: '内完成支付，否则订单会自动取消。',
        submitPay: '提交支付',
        completePayment: '完成支付',
        toto:{
          payer:'付款方',
          attention:'付款人',
        },
        status:{
          title:'发票单状态',
          toBeConfirm:'待确认',
          confirmed:'已确认',
          reject:'已拒绝'
        },
        validate: {
            invoiceTitleBlur: '请输入发票抬头',
            invoiceTypeBlur: '请选择发票类型',
            taxNoBlur: '请输入税号',
            addressBlur: '请输入单位地址',
            telBlur: '请输入联系电话',
            bankNameBlur: '请输入开户银行名称',
            bankAccountBlur: '请输入开户银行账号',
            invoiceExists: '发票信息已存在',
            selectPayInvice: '请选择需要支付的账单',
            alreadyPaid: '已支付，请重新选择其它账单支付',
            payOrderTip: '该账单已存在支付订单，前往订单界面进行支付',
            selectPayType: '请选择支付方式',

        },
        order: {
            title: '我的订单',
            orderNo: '订单编号',
            orderStatus: '订单状态',
            orderPayable: '订单总金额',
            payPayable: '支付金额',
            payType: '支付方式',
            payTime: '支付时间',
            payment: '前往支付',
            cancelOrder: '取消订单',
            detail: '详情',
            cancelOrderTip: '是否取消该订单'
        }
    },
    pay: {
        waitForPay: '待支付',
        paySuccess: '支付成功',
        Paymentfailed: '支付失败',
        Refunded: '已退款',
        payType: {
            alipay: '支付宝',
            wechat: '微信',
            unionPay: '银联'
        }
    },
    quotation: {
        title: '报价单',
        statement:'对账单',
        selectPayCurrency: '选择付款货币',
        cny: '人民币',
        usd: '美元',
        hkd: '港币',
        quotationDetail: '账单明细',
        statementDetail: '对账单明细',
        referenceNo: '参考编号',
        quotationNo: '报价单号',
        statementNo: '对账单号',
        invoiceNo: '发票号',
        totalTestItems: '不含税金额',
        estimatedTax: '税额',
        totalAmount: '总金额',
        invoicePDF: '报价单PDF附件',
        statementPDF:'对账单PDF附件',
        commentBy:'批复人',
        commentTime:'批复时间',
        statementCommentTime:'批复时间',
        issueTime:'完成时间',
        statementIssueTime:'完成时间',
        quotationConfirmDate:'报价确认时间',
        quotationIssuedDate:'报价出具时间',
        confirmExportQuotationList: '最多导出500条数据，是否继续？',
        status:{
          title:'报价单状态',
          statement:'对账单状态',
          toBeConfirm:'待确认',
          confirmed:'已确认',
          reject:'需要修改',
          cancel:'取消',
          toBePay:'待支付'
        },
        rejectReason:'拒绝原因',
        validate:{
            rejectReason:'请填写拒绝原因'
        }
    },
    invoiceTodo:{
        commentBy:'批复人',
        commentTime:'批复时间',
        paid:'付款',
        status:{
          title:'发票状态',
          toBeConfirm:'待付款',
          confirmed:'已付款',
          reject:'需要修改',
          cancel:'取消'
        },
    },
    testResult: {
        title: '测试结果',
    },
    pend: {
        name: '待办',
        pendType: '类型',
        pendDescribe: '待办事项',
        isRead: '是否已读',
        goHandle: '前往处理',
        processed: '已处理',
    },
    navbar: {
        logOut: '退出登录',
        searchTrf: '输入trf编号查询',
        userinfo: '个人信息',
        personalitySetting: '账号设置',
        authentication: '公司认证',
        companyInfo: '公司信息',
        dashboard: '首页',
        lock: '锁屏',
        bug: '没有错误日志',
        bugs: '条错误日志',
        screenfullF: '退出全屏',
        screenfull: '全屏',
        language: '中英文',
        theme: '主题',
        color: '换色',
        customerManagement: '公司管理',
        materialManagement: '物料管理',
        trfList: '测试申请单列表',
        trfForm: '测试申请单',
        dataEntryForm: '录入测试结果',
        generalFields: 'General Fields',
        trainingList: '培训列表',
        classManager: '班级管理',
        courseManager: '课程管理',
        trainningPerformance: '培训分析',
        customerApprove: '客户审核',
        labManagement: '实验室管理',
        factoryManagement: '工厂管理',
        testpackageManagement: '测试包管理',
        templateManagement: '模板管理',
        reconciliationService: '对账服务',
        myOrder: '支付订单',
        newTemplate: '新增模板',
        newCustomer: '新增客户',
        feedback: '留言板',
        vipservice: '会员服务',
        documentLibrary: '文档管理',
        documentSharing: '供应商知识共享',
        documentSharing1: '进阶数据分析',
        authorization: '授权',
        aflTemplateManagement: 'AFL客户模板管理',
        quotationAndInvoice:'报价单与发票',
        accountPer:'账号权限管理'
    },
    quickAccess: {
        customer: '公司管理',
        trfList: '申请单',
        homepage: '主页',
        other: '申请单模板'
    },
    tagsView: {
        menu: '更多',
        closeOthers: '关闭其它',
        closeAll: '关闭所有'
    },
    testpackage: {
        rootNode: '根节点',
        titleAdd: '添加测试包',
        titleEdit: '修改测试包',
        name: '测试包名称',
        parent: '所属测试包',
        selTestPackage: '选择测试包',
        inputTestPackageName: '请输入测试包名称',
        inputCustomerName:"请输入客户名称",
        testItem: '测试项'

    },
    lab: {
        title: '实验室',
        labInfo: '实验室信息',
        labName: '实验室名称',
        labNameAfl: 'SGS分支机构',
        labCode: '实验室编号',
        labType: '实验室类型',
        labAddress: '实验室地址',
        labAddressAfl: 'SGS分支机构地址',
        labAddressBlur: '请输入实验室地址',
        labLocation: '实验室位置',
        titleAdd: '添加实验室',
        titleEdit: '修改实验室',
        addContact: '添加联系人',
        editContact: '编辑联系人',
        selLabName: '请输入实验室名称',
        selLabNameAFL: '请输入SGS分支机构',
        selLabCode: '请输入实验室编号',
        selLabType: '请选择实验室类型',
        selLab: '请选择实验室',
        labChanged: '实验室信息已更新，请重新填写',
        labCountry:'实验室国家',
        labCity:'实验室城市',
        labCertificate:'资质信息',
    },
    labContact: {
        selLabContact: '选择实验室联系人',
        labContactName: '实验室联系人',
        labContactNameAfl: 'SGS联系人',
        contactName: '联系人名称',
        contactName1: '联系人名称',
        contactTel: '联系人电话',
        contactEmail: '联系人邮箱',
        contactAddress: '联系人地址',
        selContactName: '请输入联系人名称',
        selContactTel: '请输入联系人电话',
        selContactEmail: '请输入联系人邮箱',
    },
    documentLibrary: {
        title: {
            default: '文档',
            tab: '文档',
            add: '添加文档',
            edit: '编辑文档',
            baseInfo: '基础信息',
            attachmentInfo: "附件信息"
        },
        chooseFile:'选择文件',
        clearErrorData:'删除错误数据',
        name: '名称',
        nameFull: '文档名称',
        linkUrl: 'URL',
        imageCloudId: '图片',
        documentSort: '排序',
        documentDec: '文档描述',
        theme: "主题",
        validate: {
            typeBlur: '请选择文档类型',
            nameBlur: '请输入文档名称',
            imageUrl: '请选择Image',
            boosLengthErr: '请至少输入4位及4位长度以上的客户编号',
            noCustomerTip: '该客户尚未在SGS SMART注册',
            productLineErr:'请选择Product Line',
            noTemplate:'文件上传错误，请确保您已使用最新上传模板并无修改表头。',
            numberLength500:'上传数量超过500条，请分批上传。',
            interfaceError:'系统异常，请稍后重试或联系SGS管理员。',
            hasInvalidData:'上传数据有误，请清除有误数据之后重试。',
            repeatDataTip:'存在重复数据'
        }
    },
    documentSharing: {
        attachments: '附件 -  查看所有'
    },
    newService: {
        title: {
            default: 'SGS 服务',
            tab: 'SGS 服务',
            add: '添加SGS 服务',
            edit: '编辑SGS 服务',
        },
        url: '服务URL',
        textTitle: '标题',
        flyer: '宣传材料',
        validate: {
            textTitleBlur: '请输入标题',
            urlAndFileError: 'URL或附件只能选择一个',
            urlAndFileValidate: '请选择录入URL或上传附件',
            uploadImageError: '请上传图片',
        }
    },
    //培训相关开始
    training: {
        CourseTitle: '课程标题',
        Course: '课程',
        TrainingCourseLevel: '课程等级',
        TrainingLevel: '培训等级',
        CourseModule: '模块名',
        Module: '模块',
        AddModule: '新增模块',
        EditModule: '修改模块',
        AddCourse: '新增课程',
        EditCourse: '编辑课程',
        EnrollmentStartDate: '报名开始时间',
        EnrollmentExpiredDate: '报名结束时间',
        ClassIntroduction: '班级介绍',
        UploadCourseImage: '图片',
        TrainingDate: '培训日期',
        TrainingAddress: '培训地址',
        TrainingLocation: '培训区域',
        Trainer: '培训者',
        SelectTrainee: '学员',
        RepeatToAdd: '此课程和等级已经存在',
        ModuleFileRepeatToAdd: '已经存在！',

        TrainingMaterials: '培训材料',
        Browse: '上传',
        ModuleFile: '模块文件',
        Close: '关闭',
        DocumentName: '文件名',
        UploadedBy: '上传人',
        Action: '操作',
        Download: '下载',
        Delete: '删除',
        Save: '保存',
        PublishCourse: '发布',
        NotifyAttendance: '邮件',
        Cancel: '关闭',
        ClassInfo: '班级信息',

        CourseIntroduction: '课程介绍',
        Seek: '搜索',
        Add: '新增',
        Edit: '修改',
        Clear: '清空',
        detail: '详情',
        CustomerGroup: '客户组',
        TrainingCourseModules: '课程模块名',
        ModulesName: '课程模块名',
        Attachment: '附件',
        Clickonthedownload: '点击下载',
        FileList: '文件列表',

        NewClassManager: '班级管理',
        NewClass: '新增班级',
        NewCourseModule: '新增模块',
        NewCourse: '新增课程',
        OngoingClass: '正在进行',
        ComingClass: '即将开始',
        CompleteClass: '完成',
        Attendance: '录入成绩',
        loading: '加载中',
        Date: '日期',
        InvitationNumber: '邀请数量',
        EnrolledNumber: '登记数量',
        Location: '地点',
        LoadingMore: '加载更多',
        Loaded: '已全部加载',

        AllPass: '全部通过',
        ExportToExcel: '导出到Excel',
        Register: '报名',
        Name: '姓名',
        Company: '公司名',
        TotalNumOfAttendance: '总参加人数',
        PassRate: '通过率',
        TotalNumber: '总人数',
        Enrolled: '签到',
        Result: '结果',
        Score: '分数',
        Remark: '备注',
        Pass: '通过',
        Fail: '未通过',
        NotAttended: '未参加',


        fsCode: '供应商编号',
        tier: '供应商层级',
        productCategory: '产品类别',
        Supplier: '供应商',
        hadChoose: '已选择人员',
        InternalStaff: '内部员工',
        ClassList: '班级列表',
        classStatus: '班级状态',
        country: '地区',
        region: '地区',
        year: '年',
        className: '班级名称',
        trainingName: '培训名称',
        success: '操作成功',
        attendanceStatus: '不允许录入成绩',

        //导出
        notRegister: '未注册',
        notEnrolled: '未登记',
        notPass: '未通过',
        notAttend: '未参加',
        isPublish: '是否发布',

        Publish: '发布',
        NotPublish: '取消发布',
        Published: '已发布',
        NoPublish: '未发布',
        NoData: '暂无数据',

        KeywordSearch: '输入关键字查找',
        validate: {
            courseTitle: '请输入课程标题',
            trainingCourseLevel: '请选择课程等级',
            courseIntroduction: '请输入课程介绍',
        },
        filter: '筛选条件',
        confirmComplete: '完成之后将无法进行其他操作，是否继续？',
        //培训相关结束
    },
    reviewConclusion: {
        title: {
            tab: "审查结论",
            add: '添加审查结论',
            edit: '修改审查结论',
        },
        reviewCode: '审查代码',
        reviewValue: '审查描述',
        RepeatToAdd: '此信息已经存在',
    },
    customerMaterialConfig: {
        configTemplate: '配置模板',
        title: {
            add: '添加',
            edit: '修改',
        },
        buyer:'买家',
        conclusion: '结论',
        applicationNo: '申请单编号',
        dffFormType: 'DFF 类型',
        dffFieldName: 'DFF 名称',
        isMaterial: 'Material属性',
        dffFieldMaterialName: '物料属性名称',
        isMaster: '展示字段',
        isSearch: '查询字段',
        isUnique: '联合主键',
        isMr: '客户报表展示',
        material: '物料',
        materialFrom2: "申请人",
        materialReport: '报告页面',
        downloadExcel: '导出模版',
        exportExcel: '导出',
        uploadExcel: '导入',
        choseExcel: '选择文件',
        openExcel: '上传数据',
        uploadFile: '数据文件',
        modifyTemplate: '修改配置模板',
        needChooseOne: '请选择一条',
        ownProducts: '自产',
        otherProducts: '外采',
        materialFrom: '请选择来源！',
        chooseTemplate: '请选择模板！',
        materialFile: '请选择附件！',
        dffFieldMaterialNameRepeatToAdd: '自定义名称重复！',
        noDelete: '已经存在 materil 数据，不允许删除',
        materialListName: '物料清单',
        selectMaterial:'请至少选择一条物料信息',
        hasBoundTrf:'所选择的Material已被绑定TRF',
        hasNoTemplate:'所选择的Material未配置模板,请先配置模板'
    },
    buyerRelationship: {
        title: {
            tab: "关联关系",
            add: '添加 买家关系',
            edit: '修改 买家关系',
        },
    },
    template: {
        productLine: '产品线',
        customerGroup: '客户群组',
        customer: '客户',
        dff: 'DFF表格 / 动态表格',
        groupDff:'DFF表格',
        groupForm:'动态表格',
        productCategory: '产品类别',
        customerName: '客户名称',
        defaultCustomer: '默认客户',
        templateName: '模板名称',
        testPackage: '测试包',
        specific: '特定标签',
        selectTestPackage: '选择测试包',
        labContactTitle: '实验室联系人',
        baseInfoTitle: '基础信息',
        default: '默认',
        selProductLine: '请选择产品线',
        selCustomerGroup: '请选择客户组',
        selCustomer: '请选择客户',
        selCustomerOrGroup: '请选择客户组/客户',
        selDff: '请选择DFF表格',
        selProductCategory: '请选择产品类别',
        selTestPackage: '请选择测试包',
        inputTemplateName: '请输入模板名称',
        usable: '是否使用',
        required: '是否必填',
        fieldSettings: 'Dff 配置',
        isAflDefault: "是否默认",
        createTime: '创建时间',
        isDeleted: "是否删除",
        isAflCreatedByAdmin: '创建者',
        printTemplate: '打印模板',
        dffDefaultFlag: 'DFF 默认模板',
        serviceUnitDefaultFlag: 'BU 默认模板',
        flagTrue: '是',
        flagFalse: '否',
        confirmSave: '已有默认TRF模板，是否确认替换？',
    },
    ccl: {
        chemicalEnName: '化学品名称（英文）',
        chemicalZhName: '化学品名称（中文）',
        casNo: 'Cas 号码',
        composition: '重量百分比(%)',
        casNoCheck: 'Cas No 不合法',
        add: '新增',
        batchAdd: '批量新增',
        batchAddBlurMsg: '请输入新增行数',
        batchAddErrorMsg: '请输入0-100范围内的值',
        validate: {
            componentNullError: '比例为必填项！',
            componentFormatError: '请检查数据有效性！',
        }
    },
    cclTrf: {
        sdsStandard: 'SDS标准',
        isDraftReport: '是否需要报告草稿',
        needUfiCode: '需要唯一的配方标识(仅适用于EU SDS)'
    },
    trf: {
        customerNo:'客户编码',
        agentTip: '如您选择该代理商，测试进度和报告将与该代理商共享',
        changeTemplate: '选择模板',
        changeTemplateMsg: '如果切换其他模板，原有录入测试数据将被清空，是否继续？',
        templateCustomerError:'您使用的模板缺少买家信息，请联系SGS补充。',
        customerTemplateExistError:'买家存在专用模板，请重新选择模板。',
        selBuyerCustomerGroup:'买家组选择',
        validate: {
            noTemplateMsg:'请先选择申请表模板。',
            requiredBlur: '必填项不完整',
            contactNameLengthErrorInfo: '长度不可大于50',
            emailLengthError: '长度超出200',
        },
        notes: '注',
        selfReference: '自测',
        isEfillingFlag:"是否显示eFiling",
        selfReferenceTipInfo: '即使勾选了self-reference，买家依然可以看到报告',
        print: '预览/打印申请单',
        downLoad: '导出TRF',
        downLoadBarcode: '导出条形码',
        createtrf: '新建申请单',
        customerInfo: '客户信息',
        composition: '组成成分',
        buyerInfo: '买方信息',
        agentInfo: '代理商信息',
        buyerAndAgentInfo: '买家 & 代理商信息',
        applicantInfo: '申请方信息',
        applicant: '申请方',
        payInfo: '付款方信息',
        sameApplicant: '与申请方相同',
        productAndSample: '样品信息',
        dynamicForm: '表单信息',
        requirements: '存储要求',
        sampleRetentionPeriod: '样品保留期限',
        mainComponentsSample: '样品主要成分',
        danger: '危险性',
        productContent: '样品内容',
        productDescription: '产品信息',
        addProduct: '添加样品',
        careLabelSymblosAndWording: '洗水标及洗语',
        templateName: '模板',
        trfTemplate: '申请单模板',
        selOtherTemplate: '其他模板',
        testPackage: '测试项目',
        basicInfo: '基本信息',
        basicInfoAfl: '基础服务信息',
        isRetest: '重测?',
        previousReportNo: '原报告单号',
        sampleReceivedDate: '样品接收日期',
        sampleDateValidateMsg: '请选择样品接收日期',
        dueDate: '预计完成时间',
        attachment: '附件',
        remark: '备注',
        dffFormValidError: 'DFF Form 验证失败！',
        dffGridValidError: 'DFF Grid 验证失败！',
        templateErrorInfo: '买家更新了测试申请表，旧模板不能使用，请重新填写！',
        trfValidateError: '必填项不完整',
        placeholder: {
            template: '请选择申请单模板',
            trfTemplateName: '请输入TRF模板名称',
        },
        confirmCancelTrf: '确定取消该申请单吗？',
        confirmReturnTrf: '确定撤回该申请单吗？',
        confirmRemoveTrf:'确定删除这张申请单吗？删除后将不能恢复。',
        confirmExportTrfList: '最多导出3000条数据，是否继续？',
        confirmSample: '确认收样',
        confirmSampleReceived: '确认收样日期',
        confirmSampleReceivedValidate: 'Please select the confirm Sample Received Date',
        trfTemplateName: 'TRF模板名称',
        trfTemplateList: 'TRF 模板列表',
        returnTrfSatus: '撤回',
        notSubmit: '暂未提交',
        serviceTypeSel: '请选择服务类型',
        templateSel: '请选择模板',
        buyerSel: '请选择买家',
        serviceClause: '我们申请以上测试并同意SGS服务通用条款',
        printTrfTip: '请打印申请表并签名盖章，同时将样品及申请表一并寄给我们。谢谢！',
        dffGridTitle: '请填写或批量导入样品信息',
        printTrfTipAFL: '为了便于实验室更快速的辨识您的样品，请点击页面底部“预览/打印申请单”将申请单打印出来并随样品一起寄出。',
        trfAddBtn: '增加样品',
        trfDownBtn: '下载模板',
        trfUploadBtn: '批量上传',
        trfConfirmTit: '是否覆盖原内容？',
        printTime: '打印时间',
        history: {
            title: '历史记录',
            createBy: '操作人',
            operationTime: '操作时间',
            status: '操作类型',

        },
        more: '展开更多',
        hide: '收起',
        printMsg: '迁移数据不支持预览和打印。',
        tipReference:'您已勾选“自测“，买家将不可见此TRF，请确认。',
        tipsConfirm:'确认',
        tipsCancel:'取消',
        HKCMS:'點擊此連結到線上下單取樣品（僅限香港）',
        customerConfirmText: '请确认已收到的报告符合您的要求了！'
    },
    userTemplate:{
        name:'用户模版名称',
        trfName:'申请单名称',
        customerName:'客户',
        createDate:'创建时间',
        changeToTrfMsg:'请确认使用此模版创建新测试申请单吗?',
        outChangeToTrfMsg:'此模板为失效状态，请确认使用此模版创建新测试申请单吗?',
        phaseout:'失效'
    },
    trfList: {
        query: {
            searchFor: '搜索...',
            selStartDate: '开始日期',
            selEndDate: '截止日期',
            oneWeek: '一周',
            oneMonth: '一月',
            sixMonth: '半年',
            oneYear: '一年',
            timeOutTrf: '超时申请单',
            moreSearch:'高级搜索',
        },
        filter:{
            set:'设为',
            default:'默认',
            delete:'删除',
            saveFilter: '保存视图',
            replaceAnExisting:'替换当前过滤条件',
            saveAsNew:'保存为新的过滤条件',
            save:'保存',
            saveAsLocal:'存为私有条件',
            saveAsPublic:'存为公共条件',
            filterPlaceHolder:'我的视图列表',
            saveAsDefault:'设置为默认'
        },
        quickSorting:'快速排序',
        copyTrf: '复制 TRF',
        createEfilling:'新建eFiling',
        viewEfilling:'查看eFiling',
        jobStatus: '状态',
        conclusion: '结论',
        trfNo: 'TRF编号',
        extendOrderNo: 'Order No',
        templateName: '模板名称',
        buyer: '买家',
        applyCustomer: '申请方',
        agent:'代理商',
        retest: '是否重测',
        submitDate: '提交日期',
        sampleDate: '收样日期',
        dueDate: '预计完成日期',
        applyContact: '联系人',
        labName: '实验室',
        dynamicMsg: '最新动态',
        reportNo: '报告号',
        productDescription: '产品描述',
        reportApprovedDate: '报告完成时间',
        styleNo: '款号',
        itemNo: 'Item No',
        poNo: '订单号',
        articleNo: '货号',
        specialProduct: 'SKU No',
        createDate: '创建日期',
        completedTime: '完成时间',
        reportIssuedTime: '报告完成时间',
        pendingReason: '暂停原因',
        buyerReviewConclusion: '买家结论',
        cancelSort: '取消排序',
        trfSourceType: '订单来源',
        serviceTypeName:'服务类型'
    },
    trfStatus: {
        all: '全部',
        draft: '待递交',
        submitted: '已提交',
        preparation: '已受理',
        testing: '测试中',
        completed: '完成',
        cancel: '已取消',
        pending: '暂停',
        title: '报价单',
        confirmingQuotation: '报价单待确认',
        confirmQuotation: '已确认报价单',
        rejectQuotation: '已拒绝报价单',
        reportIssued: '报告发布',
        applicationAccepted: '申请已接受'
    },
    reportConclusion: {
        all: '全部',
        pass: '通过',
        fail: '未通过',
        seeResult: '不做评判',
    },
    communicationLog: {
        title: '留言',
        comment: '留言内容',
        comments: '留言内容',
        commentBy: '发送至',
        addComment: '添加评论',
        emailGroup: '邮件组',
        addressBook: '收件人',
        pdfReport: 'PDF 报告',
        validate: {
            comment: '请输入评论信息',
        }
    },
    comment: {
        emailGroup: '邮件组',
        emailAddress: '邮件地址',
        applicantVisible: '申请方可见',
        comment: '评语',
        commentContent: '评语内容',
        copyComment: '复制评语',
        addComment: '添加评语',
        viewAllComments: '查看全部评语',
        copied:'复制来自 ',
        pdfReport:'PDF报告',
        commentName:'留言',
        chatDialog:{
            tabComments:'留言',
            tabAttachment:'附件',
            selectComment:'选择留言',
            copy:'复制',
            reply:'输入',
            replayNotBuyer:'已完成评定的TRF无法评论',
            send:'发送',
            attachTestReport:'添加测试报告',
            notificationTo:'同时通知',
            visibleTo:'可见范围',
            internalOnly:'内部用户',
            internalAndApplicants:'内部和申请方',
            documentName:'附件名称',
            documentType:'附件类型',
            uploadedBy:'上传人',
            date:'上传时间',
            action:'操作',
            attachmentIssueDate:'生效日期',
            lengthOver10:'收件人邮箱上限为10，请调整收件人。',
        }
    },
    service: {
        contactEmail: '联系人邮箱',
        required: '需要',
        notRequired: '不需要',
        yes: '接受',
        no: '不接受',
        judgmentRule:{
            title:'判定规则',
            rule1:'依据法律法规',
            rule2:'依据测试标准',
            rule3:'简单接受 w=0 (直接判定)',
            rule4:'保护带: a.保护带同扩展不确定度U',
            rule5:'保护带: b.保护带≤2%PFA',
            rule6:'其它判定要求',
            remark:'除法律法规和标准规定外，如无特定要求，实验室通常参照ILAC-G8:09/2019，采用简单接受判定规则 (w=0)'
        },
        reportSymbol:'报告需出具标志',
        serviceType: '服务类型',
        servicRequire: '服务需求',
        reportRequire: '报告需求',
        comment: '判定（需要判定请选择）',
        softCopy: '电子报告',
        hardCopy: '正本报告',
        takePhoto: '拍照（如需拍照请选择）',
        coverPageConfirmation: '首页确认（是否需要首页确认）',
        quotation: '报价',
        returnSample: '退样要求',
        accreditation: '报告资质要求',
        softCopyDeliverTo: '电子报告邮寄联系人',
        softCopyDeliverToHolder: '请选择电子报告邮寄联系人',
        softCopyDeliverOtherHolder: '请输入电子报告邮寄联系人',
        softCopyDeliverOtherOverLength: '电子报告邮寄联系人超长',
        hardCopyDeliverTo: '纸质报告邮寄联系人',
        hardCopyDeliverWay: '正本报告寄送方式',
        hardCopyDeliverToHolder: '请选择纸质报告邮寄联系人',
        hardCopyDeliverWayHolder: '请选择正本报告寄送方式',
        hardCopyDeliverToOtherHolder: '请输入纸质报告邮寄联系人邮箱',
        hardCopyDeliverWayOtherHolder: '请输入正本报告寄送方式',
        hardCopyDeliverToOtherOverLength: '纸质报告邮寄联系人超长',
        vatType: '发票类型',
        invoiceProforma: '形式发票',
        invoiceDeliverTo: '发票邮寄联系人',
        invoiceDeliverToHolder: '请选择发票邮寄联系人',
        invoiceDeliverToOtherHolder: '请输入发票邮寄联系人邮箱',
        invoiceDeliverWayOtherHolder: '请输入发票寄送方式',
        invoiceDeliverToOtherOverLength: '发票邮寄联系人超长',
        invoiceDeliverWay: "发票寄送方式",
        invoiceDeliverWayHolder: '请选择发票寄送方式',
        returnSampleDeliverWay: '退样方式',
        returnSampleDeliverTo: '退样邮寄联系人',
        returnSampleDeliverToHolder: '请选择退样邮寄联系人',
        returnSampleDeliverToOtherHolder: '请输入退样邮寄联系人邮箱',
        returnSampleDeliverWayHolder: '请选择退样方式',
        returnSampleDeliverWayOtherHolder: '请输入退样寄送方式',
        returnSampleDeliverToOtherOverLength: '退样邮寄联系人超长',
        liquidInkSample: '液体、油墨样品处理方式',
        acceptSelection: '是否接受将样品交于SGS以外的机构做检测（如未选择，视为接受）',
        legalProceeding: '以上申请是否涉及法律纠纷？如果有请说明',
        reportLanguage: '报告语言',
        reportHeader: '报告抬头（如与申请方不同，请填写）(中文)',
        reportHeaderZh: '报告抬头（如与申请方不同，请填写）(中文)',
        reportHeaderEn: '报告抬头（如与申请方不同，请填写）(英文)',
        reportAddress: '报告抬头地址(中文)',
        reportAddressEn: '报告抬头地址(英文)',
        reportAddressZh: '报告抬头地址(中文)',
        otherRequest: '其他需求',
        reportReceivingEmail: '报告接收邮箱',
        otherTip: '如有特殊需求请注明，比如：需要纸质报告',
        otherTipAfl: '如有特殊需求请注明',
        reportSuccessSend: '报告发送至',
        reportErrorSend: '不通过报告发送至',
        reportMethod: '报告方式',
        reportType: '报告类型',
        qualificationMark: '资质标志CMA/CNAS标志',
        isReportReflectCustomerInformation: '报告是否体现目的国、买家、供应商及代理商信息',
        resultJudging: '结果评判',
        judgementPrinciple: '评判依据',
        reportConsistent: '报告抬头是否与申请方一致',
        noAdd: '不加',
        addCNAS: '加CNAS',
        addCMA: '加CMA',
        addCNASAndCMA: '加CNAS和CMA',
        electronic: '电子报告（默认）',
        paper: '纸质报告（RMB100元/份）',
        other: '其他',
        productStandards: '型检报告模板需提供产品标准',
        oneSample: '一个样品一份报告',
        multipleSample: '多个样品一份报告',
        chineseReport: '中文报告',
        enReport: '英文（请确保证英文信息完整）',
        certificateRequired:'是否需要证书',
        COC:'COC',
        GPC:'GPC',
        CPC:'CPC'
    },
    customer: {
        new:{
          payer:'付款方',
          buyer:'买家',
          agent:'代理商',
          supplier:'供应商',
          manufacturer:'生产工厂',
          customerNo:'SGS 客户编码',
          customerReferenceNo:'客户编码',
          companyNameEn:'公司名称（英文）',
          companyNameCn:'公司名称（本地语言）',
          companyAddressEn:'公司地址（英文）',
          companyAddressCn:'公司地址（本地语言）',
          manufacturerT2:'关联了买家的工厂',
          manufacturerT1:'我的工厂'
        },
        title: {
            base: '公司信息',
            CompanyBase: '公司信息',
            admin: '用户信息',
            thirdLab: '第三方实验室',
            other: '其他信息',
            sgsSettings: 'SGS设置',
            approve: '客户审核',
            default: '客户',
            tab: '客户',
            add: '添加客户',
            edit: '修改客户',
            relationshipNeedApprove: '已提交关联买家申请，请联系买家进行审核！',
            ViewLargeImage: '查看大图'
        },
        name: '公司名称',
        companyNameCn: '公司名称（中文）',
        companyNameEn: '公司名称（英文）',
        payNameEn: '付款方名称（英文）',
        payNameNew: '发票抬头',
        payNameNew1: '发票抬头（如与申请方不同时，请填写）',
        payNameCn: '付款方名称（本地语言）',
        payAddressEn: '付款方地址（英文）',
        payAddressCn: '付款方地址（本地语言）',
        nameZh: '中文名称',
        nameEn: '英文名称',
        address: '公司地址',
        addressZh: '中文地址',
        addressEn: '英文地址',
        addressBlur: '请输入地址信息',
        status: {
            todo: '待审核',
            emailConfirm: '已邮件确认',
            approved: '已审核',
            refused: '已拒绝'
        },
        authenticationStatus: '认证状态',
        approveStatus: {
            todo: '待认证',
            emailConfirm: '未认证',
            approved: '已认证',
            refused: '认证失败'
        },
        editProductCategory: '修改产品类别',
        editServiceUnit:'修改产品类别',
        approveStatusErrMsg: '已审核客户无法修改产品类别',
        rejectionReasons: '拒绝原因',
        sgs: {
            customerNo: 'SGS 客户编码',
            reportNo: 'SGS 报告号',
        },
        taxNo: '税号',
        certificate: '营业执照',
        certificateNew: '营业执照',
        certificateUpload: "暂无数据",
        contactMobile: '联系方式',
        relationship: {
            title: {
                default: '我的客户',
                tab: '我的买家',
                tabBuyer: '我的买家',
                buyerName: '买家名称',
                buyerCode: '买家编号',
                tabSupplier: '我的供应商',
                add: '添加关联关系',
                edit: '编辑关联关系',
                scope: '关系范围'
            },
        },
        joinBuyerProgram: '加入买家计划',
        customerRelationshipNeedApprove: '关联关系需要审核',
        error: {
            buyerNotCustomerGroup: '该买家没有客户组信息'
        },
        changeProductCatoryMsg: '此操作可能会导致您无法继续审核该账号, 是否继续?',
        selectModel:[
            {
                label:"客户组",
                value:"1"
            },
            {
                label:"客户",
                value:"2"
            }
        ]
    },
    buyer: {
        title: {
            default: '买家',
            tab: '买家',
            add: '添加买家关系',
            edit: '编辑买家关系',
        },
        label: '买家',
        name: '买家',
        selectMeg:'请选择买家',
        customerReferenceCode:"买家编码",
        customerGroupCode:"买家组编码",
        customerName: "买家名称",
        placeholder: {
            name: '请输入买家名称',
        },
        customerGroupTip: '以下加*的为SGS SMART已上线买家，如您申请这些买家的正式测试，测试进度和报告将与买家共享。',
    },
    sgs:{
        sgsCustomerNo: "SGS编码"
    },
    agent: {
        title: {
            default: '代理商',
            tab: '代理商',
            add: '添加代理商',
            edit: '编辑代理商',
        },
        label: '代理商',
        name: '代理商',
        placeholder: {
            name: '请输入代理商名称',
        },
    },
    supplier: {
        title: {
            default: '供应商',
            tab: '供应商',
            add: '添加供应商关系',
            edit: '编辑供应商关系',
        },
        label: '供应商',
        name: '供应商',
        nameFull: '供应商名称',
        nameFullCN: '供应商中文名称',
        nameFullEN: '供应商英文名称',
        supplierCode: '供应商编码',
        tier: '层级',
        productCategory: '产品类别',
        department: '由（部门）管理',
        customerReferenceCode:"供应商编码",
        customerName: "供应商名称",
        customerNameCN: "供应商名称 (中文)",
        customerNameEN: "供应商名称 (EN)",
        selectMeg:'请选择供应商',
        selectMeg1:'SGS编码不可为空,请重新选择供应商',
        placeholder: {
            name: '请选择供应商',
        },
        validate: {
            nameBlur: '请选择供应商',
        }
    },
    productLine: {
        code: '产品线编码',
        name: '产品线名称',
        placeholder: '请选择产品线',
    },
    customerGroup: {
        code: '客户组编码',
        name: '客户组名称',
        placeholder: '请选择客户组',
    },
    agentCustomerGroup: {
        code: '代理商编码',
        name: '代理商名称',
        placeholder: '请选择代理商',
    },
    address: {
        title: {
            default: '地址',
            tab: '地址',
            add: '添加地址',
            edit: '编辑地址',
        },
        detail: '地址',
        zh: '中文地址',
        en: '英文地址',
        validate: {
            addressBlur: '请输入地址',
        }
    },
    account: {
        title: {
            default: '账号',
            tab: '账号',
            add: '添加账号',
            edit: '编辑账号',
            approve: '审核账户',
        },
        management: '账号管理',
        approveTitle: '账号审核',
        code: '登录账号',
        account: '账户',
        userName: '用户名',
        email: '邮箱',
        mobile: '手机号',
        password: '密码',
        customerNum: '模板数量',
        passwordConfirm: '确认密码',
        post: '岗位角色',
        validate: {
            accountBlur: '请输入账号信息',
            passwordBlur: '请输入密码信息',
            emailBlur: '请输入邮箱信息',
            mobileBlur: '请输入手机信息',
            userNameBlur: '请输入用户名称',
            suplierNameBlur: '请选择供应商',
            departmentBlur: '请选择部门',
            postBlur: '请选择岗位角色',
            opinionBlur: '请输入您的意见',
            serviceUnitBlur:'请选择服务类型',
        },
        userManage: '账户',
        suplierManage: '供应商',
        buyManage: '买家',
        suplierName: '供应商名称',
        customerName: '客户名称',
        buyName: '买家名称',
        approveStatus: {
            stayEmailConfirm: '待邮件确认',
            toBeReviewed: '待审核',
            emailConfirm: '已邮件确认',
            auditPass: "审核通过"
        },
        noDeptMessage: '请转到 我-公司管理-部门，为贵公司添加部门。',
        editServiceUnitMessage: '修改产品类别后重新登录后生效',
    },
    vipProgram: {
        title: {
            default: 'Vip Program',
            tab: 'Vip Program',
            add: '添加 Vip Program',
            edit: '编辑 Vip Program',
        },
        name: '项目名称',
        layoutType: '布局',
        validate: {
            nameBlur: '请输入项目名称',
        },
        showWhichMenu: '展示在哪个菜单'
    },
    authorization: {
        title: {
            default: '授权',
            tab: '授权',
            add: '添加授权',
            edit: '编辑授权',
        },
        selected: '选择日期',
        authManage: '文档授权',
        noSelected: '没有授权信息!',
        validate: {
            customer: '如果 ‘客户’ 无数据，代表该文档开放给该产品线下所有用户',
            date: '如果 ‘生效日期’ 无数据，代表该文档长期有效',
        },
        authorValid: '授权已失效',
        visibleOnly: '仅浏览标题',
        visibleOnlyTip: '您可以联系SGS客户专员解锁功能，查看详情'
    },
    emailGroup: {
        title: {
            default: '邮件组',
            tab: '邮件组',
            add: '添加邮件组',
            edit: '编辑邮件组',
        },
        name: '名称',
        nameFull: '邮件组名称',
        member: '员工',
        memberFull: '邮件组员工',
        validate: {
            nameBlur: '请输入邮件组名称',
            contactBlur: '请选择联系人',
        }

    },
    department: {
        title: {
            default: '部门',
            tab: '部门',
            add: '添加部门',
            edit: '编辑部门',
        },
        label: '部门',
        name: '名称',
        nameFull: '部门名称',
        nameBlur: '请输入部门名称',
    },
    notifaction: {
        title: {
            default: '通知设置',
            tab: '通知设置',
            add: '添加通知设置',
            edit: '编辑通知设置',
        },
        selectAll:'全选',
        trfStatusName:'申请单状态',
        tickedUpdated: '设置当指定订单状态发生时，可收到邮件提醒。',
        finalReportAttached: '报告完成',
        allCommunicationLog: '收到留言',
        confirmSampleReceived: '实验室收样',
        trfStatusChange: '任何更新',
        pass: '通过',
        fail: '未通过',
        seeResult: '不做评判',
        general: '订单状态更新提醒',
        overAllResult: '按报告结论提醒',
        wechat: {
            title: '微信通知',
            isWechatPush: '开启微信推送',
            quotationConfirm: '待确认报价单提醒',
            general: '订单状态更新提醒',
            reportReceiving: '收到报告提醒',
            noBindWechat: '系统检测到您暂未绑定微信，绑定微信后可开启该设置。',

        },
        email: {
            title: '邮件通知',
            isEmailPush: '开启邮件通知',
        },
        reportCompletedTips: '当报告上传SGS SMART时，申请单上“报告发送至”的联系人可以收到邮件提醒。',
        by: '如勾选“报告完成”和“未通过”， 当报告上传SGS SMART时，申请单“不通过报告发送至”的联系人可以收到邮件提醒。',
        quotation: '报价单通知'
    },
    feedback: {
        title: {
            default: '留言板',
        },
        validate: {
            nameBlur: '请输入您的姓名',
            emailBlur: '请输入邮件地址',
            emailWaring: '邮件地址不正确',
            phoneBlur: '请输入您的电话号码',
            contentBlur: '请输入留言内容',
        },
        content: '留言内容',
    },
    sms: {
        sendCode: '获取验证码',
        code: '短信验证码',
        smsCodeValidateError: '短信验证码校验未通过，请重新发送',
    },
    contact: {
        title: {
            default: '联系人',
            tab: '联系人',
            add: '添加联系人',
            edit: '编辑联系人',
        },
        contactInfo: '联系人信息',
        name: '名称',
        nameFull: '联系人名称',
        email: '邮箱',
        emailFull: '联系人邮箱',
        phone: '电话',
        phoneFull: '联系人电话',
        fax: '传真',
        placeholder: {
            name: '请输入联系人名称',
        },
        content: '留言内容',
        validate: {
            phonelBlur: '目前只支持中国大陆的手机号码',
            phoneValidate: '请输入电话',
            emailBlur: '请输入邮件地址',
            emailWaring: '邮件地址不正确',
            nameBlur: '请输入联系人名称',
        }
    },
    factory: {
        title: {
            tab: '我的工厂',
            add: '添加工厂关系',
            edit: '编辑工厂关系'
        },
        name: '工厂管理',
        customerReferenceCode: '工厂编码',
        customerName: '工厂名称',
        selectMeg:'请选择工厂',
        validate: {
            factoryCodeBlur: '请输入工厂编码',
            factoryNameBlur: '请输入工厂名称',
        }
    },
    manufacturer: {
        title: {
            label:'生产工厂',
            tab: '我的制造商',
            add: '添加制造商关系',
            edit:'编辑制造商关系',
        },
        name: '制造商管理',
        selectMeg:'请选择制造商',
        selectMeg1:'SGS编码不可为空,请重新选择制造商',
        customerReferenceCode: '制造商编码',
        customerName: '制造商名称',
        customerNameCN: '制造商名称 (中文)',
        customerNameEN: '制造商名称 (EN)',
        validate: {
            factoryCodeBlur: '请输入制造商编码',
            factoryNameBlur: '请输入制造商名称',
        }
    },
    dateValidate: {
        startDateValidate: '请选择开始时间',
        endDateValidate: '请选择结束时间',
        betweenDateValidate: '时间区间不能大于一年',
        endDateErrorValidate: '结束时间不能大于开始时间',
        startDateAndEndDateValidate: '在没有录入搜寻的条件下请选择筛选时间区间，最大日期区间为1年',
        companyNameValidate: '请输入公司名称查询',
        startDateAndEndDateValidate1: '请选择筛选时间区间，最大日期区间为1年',
        betweenDateValidateOneYear: '最大允许查询近一年数据',
        startMaxError: '仅支持查询2022年1月1号之后的数据',
        dueDataAndSmapleDataValiMsg:'预计完成日期不能早于样品接收时间',
    },
    beLongTo: {
        title: '分类',
        name: '分类名称',
        add: '添加分类',
        validate: {
            name: '请输入分类名称',
        },
    },
    report: {
        title: '报告',
        conclusion: '报告结论',
        testStartDate: '服务开始时间',
        reportIssuedDate: '报告完成时间',
        reviewConclusion: '买家结论',
        reviewProgress:'评定状态',
        marketSegment:'市场细分',
        originalReportNo:'原报告号',
        reportVersion:'报告版本',
        serviceStartDate:'服务开始时间',
        serviceEndDate:'服务结束时间',
        rslStatus:'RSL Status',
        remark:'Solution Description',
        reportPDF: '报告 PDF',
        reportDetail: '测试详情',
        reviewConclusionStatus_0: '未查看',
        reviewConclusionStatus_1: '已查看',
        reviewConclusionStatus_2: '已提交',
        reviewConclusionStatus_3: '完成评定',
        testLine: {
            categoryCode : 'Category Code',
            pp:'Protocol / Package',
            citation : 'Citation',
            sampleNo:'样品信息',
            testName: '测试项目',
            testResult: '结论',
            resultReview: '买家单项结论',
            component: '测试部位',
            color: '颜色',
            remark: '备注',
        }
    },
    guide: {
        title: '欢迎使用 SGS SMART',
        subTitle: '在开始之前我们先做好预设吧～',
        status: {
            start: '已完成 ',
            endHead: ' 个任务，共 ',
            endFooter: '个任务'
        },
        newTask: '新手任务',
        func: '功能引导',
        task: {
            task1: {
                title: '添加买家',
                tip: '申请测试前，需要先和买家建立关联'
            },
            task2: {
                title: '添加联系人',
                tip: '预设联系人方便在填写申请单时被调用'
            },
            task3: {
                title: '设置地址',
                tip: '预设地址方便在填写申请单时被调用'
            },
            task4: {
                title: '通知设置',
                tip: '当指定订单状态变化时，可收到提醒'
            }
        },
        prev: '上一步',
        next: '下一步',
        learned: '我学会了',
        step1: '第1步，',
        step2: '第2步，',
        step3: '第3步，',
        step4: '第4步，',
        step5: '第5步，',
        step6: '第6步，',
        step7: '第7步，',
        total3: '共3步',
        total4: '共4步',
        total5: '共5步',
        total6: '共6步',
        total7: '共7步',
        clickCompanyManage: '点击设置里的「公司管理」',
        selectClient: '选择「我的客户」',
        clickAdd: '进行「添加」',
        inputKeyword: '输入买家名称关键字，在列表中选择并递交',
        clickSubmit: '点击「提交」',
        clickContact: '点击「联系人」',
        inputCOntactInfo: '输入名称、电话及邮箱',
        clickAddress: '点击「地址」',
        inputAddress: '输入地址',
        ClickNotification: '点击「通知设置」',
        reportIssued: '选择「报告完成」',
        clickPass: '选择',
        addBuyerSuccess: '添加买家成功！',
        addContactSuccess: '添加联系人成功！',
        addAddressSuccess: '添加地址成功',
        notificationSuccess: '通知设置成功',
        completeTasl: '恭喜您，完成所有任务!',
        autoClose: {
            start: '',
            end: 's 后自动关闭'
        },
        // 功能引导
        nav: '导航栏在这里',
        navTxt: '这里是导航栏，可以点击跳转到不同的功能页面去。',
        trf: '这里是最新申请单',
        trfTxt: '当测试申请单发生状态变化时，都会出现在这里～',
        createTrf: '在这里新建申请单',
        createTrfTxt: '点击右上角这里的按钮，选择类别后就可以新建测试申请单～',
        trfStatus: '申请单状态统计',
        trfStatusTxt: '这里可以查看不同状态的申请单，右上角选择时间范围，点击可以查看不同状态的申请单～',
        testResult: '查看测试结果',
        testResultTxt: '在这里查看测试结果，右上角选择时间范围，点击饼图还可以跳转页面查看。',
        switchLang: '中英文切换',
        switchLangTxt: '在这里切换选择中英文～',
        viewAgain: '再次查看',
        viewAgainTxt: '不论在哪个页面，点击选择「功能引导」就可以再次看到本教程哦～',
        trfCreate: '新建申请单',
        trfCreateTxt: '点击新建按钮创建新的申请单',
        filterTrf: '筛选申请单',
        filterTrfTxt: '可以通过时间和关键字筛选申请单',
        locationTrf: '定位申请单',
        locationTrfTxt: '通过这里的表头搜索可以快速的定位申请单',
        trfDetail: '查看详情',
        trfDetailTxt: '点击申请单TRF编号即可打开，查看申请单详情',
        modify: '修改',
        modifyTxt: '点击这里的修改，可以修改申请单买家或模板等内容',
        tabSwitch: '表单切换',
        tabSwitchTxt: '在这里切换申请单，测试结果和账单',
        quickBar: '快速定位',
        quickBarTxt: '在这里可以快速定位，切换查看申请单详情内容',
        contentFill: '内容填写区',
        contentFillTxt: '在这里完善表单内容',
        process: '显示填写进度',
        processTxt: '完善内容的同时，这里的进度条会同步显示当前完成进度。',
        continue: '继续',
        continueTxt: '在完成卡片里面的内容后，选择继续，展开更多内容。',
        actionBar: '操作栏',
        actionBarTxt: '这里是操作栏，根据表单状态对应不同的操作',
        reviewAgain: '再次查看',
        reviewAgainTxt: '点击问号，选择「功能引导」就可以再次看到本教程哦～',
    },
    powerbi:{
        viewReport:'查看报表'
    },
    dataAnalytics: {
        count: '统计',
        totalTest: '总数',
        noOfJob: '测试数量',
        overall: '总体测试情况',
        pass: '通过',
        fail: '未通过',
        seeResult: '不做评判',
        overallTestByCountry: '各国测试情况',
        overallChemicalTestPerf: '总体化学测试情况',
        overallPhysicalTestPerf: '总体物理测试情况',
        chemicalTestPerformance: '化学测试情况',
        chemicalTestPerformanceByCountry: '各国化学测试情况',
        physicalTestPerformance: '物理测试情况',
        physicalTestPerformanceByCountry: '各国物理测试情况',
        supplierChemicalPerf: '按国家/地区列出的供应商测试情况（化学品）',
        supplier: {
            title: '供应商',
            supplierTestLine: '供应商测试项',
            overall: '供应商总体测试情况',
            supplierOverallByCountry: '各国供应商测试情况',
            supplierChemicalPerf: '供应商化学测试情况',
            supplierChemicalPerfByCountry: '各国供应商化学测试情况',
            supplierPhysicalPerf: '供应商物理测试情况',
            supplierPhysicalPerfByCountry: '各国供应商物理测试情况',

        }
    },
    agreement: {
        agreeAndContinue: '同意并继续',
        privacyPolicy: 'SGS SMART 隐私声明',
        useConditions: '注册条款',
        head: '为了更好地为您提供服务，请您仔细阅读这份协议，理解认同后再进行注册。本协议是您与通标标准技术服务有限公司（以下均简称“通标公司”）就您注册、登录本平台及使用等所涉及的全部行为所订立的权利义务规范。您在注册过程中点击“同意”等按钮、及注册后登录和使用时，均表明您已完全充分理解、同意并接受本协议，愿意遵守本协议及的各项规则、规范的全部内容，若不同意则可停止注册、登录或使用平台。',
        definition_1: '一、定义',
        one_1: '1.1 您：指提交有效申请并注册后，在本平台登录、上传、发布、提供链接等以各种形式传播内容（包括文字、图片、音频、视频、图表、漫画等）的自然人、法人或其他组织。',
        one_2: '1.2 平台：是通标标准技术服务有限公司设立的服务网站，其域名为https://plus.sgsmart-online.com，运营方是通标标准技术服务有限公司。',
        accountRegisterAndUse_2: '二、帐户注册和使用',
        two_1: '2.1 在注册、使用和管理平台帐户时，请您使用真实、准确、合法、有效的相关身份证明材料及必要信息（包括您的姓名及电子邮件地址、联系电话、联系地址等），以便通标公司在必要时与您联系，并注意及时更新。为使您更好地使用平台的各项服务，请您按照相关法律规定及平台要求完成实名认证。您应当对您提供的帐号资料的真实性、合法性、准确性和有效性独立承担责任。如因此给平台公司或第三方造成损害的，您应当依法予以赔偿。',
        two_2: '2.2 为保障用户和公司利益，平台公司有权核查您提交的相关材料（如自然人身份证复印件、企业法人营业执照副本复印件、事业单位法人证书复印件、公司官方声明/说明等）后再决定是否核准您的注册申请。若您提交的材料或填写的信息不完整或不准确，则您可能无法使用本服务或在使用过程中受到限制。',
        two_3: '2.3 您所设置的帐户名不得违反国家法律法规及平台规则关于帐户名的管理规定，否则通标公司可对您的帐户名进行暂停使用或注销等处理，并向主管机关报告。',
        two_4: '2.4 您理解并承诺，您的帐户名称、头像和简介等注册信息中不得出现违法和不良信息，没有冒用、关联机构或社会名人，您在帐户注册过程中需遵守法律法规、社会主义制度、国家利益、公民合法权益、公共秩序、社会道德风尚和信息真实性等七条底线。',
        two_5: '2.5 您同意并授权，为了更好的为您提供服务以及确保您的帐户安全，通标公司可以根据您提供的手机号码、身份证号码等信息，向全国公民身份号码查询服务中心、电信运营商、金融服务机构等可靠单位发起用户身份真实性、用户征信记录、用户手机号码有效性状态等情况的查询。',
        two_6: '2.6 平台帐号的所有权归通标公司所有，您注册申请通过后，您将拥有平台帐号的使用权，可以登录平台使用平台的服务。平台帐号的所有权归通标公司所有，公司有权因经营需要收回帐号。同时，禁止任何赠与、借用、租用、转让或售卖帐号等的行为。如您违反本协议内容，通标公司有权对该帐户进行暂停使用、注销或停止提供服务等处理，且不承担任何法律责任，由此导致的包括并不限于您通讯中断、资料和虚拟道具等清空等损失由您自行承担。',
        userInfoProtection_3: '三、用户个人信息保护',
        three_1: '3.1 为了更好地为您提供服务和帮助、保护您的合法权益，请您保证申请服务时所提供的信息是真实、准确、合法、有效的，并注意及时更新，以免在使用过程中受到限制或无法使用。',
        three_2: '3.2 通标公司将保护用户个人信息作为公司发展的最基本原则之一，未经您的同意，不会向其他任何公司、组织或个人披露您的个人信息，法律法规另有规定的除外。',
        three_3: '3.3 请您在使用通标平台的过程中，不要以搜集、复制、存储、传播等任何方式使用其他用户的个人信息，否则，由此产生的后果需您自行承担。',
        rightsAndObligations_4: '四、您的权利和义务',
        four_1: '4.1 您需要对注册和使用时提交的信息及材料真实性、准确性、合法性、有效性负责，如因此引起的问题，由您承担全部法律责任。',
        four_2: '4.2 请您妥善保管您的帐户信息，并对此帐户下发生的一切活动承担全部法律责任。不向任何第三方透露帐户或密码信息，如出现或怀疑帐号和密码遭到他人使用，请尽快通知通标公司，以免您的利益受到损失。',
        four_3: '4.3 请您保证对在平台制作、复制、上传、发布、传播的任何内容享有合法权益，若您发布的内容发生权利纠纷或侵犯了任何第三方的合法权益，需您承担全部法律责任。',
        four_4: '4.4 请您遵守本协议的各项条款，并正确、适当地使用、运营、管理此平台账号，如您违反本协议中的任何条款，通标公司有权在任何时候依据本协议中止或终止对您提供服务。',
        four_5: '4.5 如您注册账号后连续二十四个月不登录该帐号，为避免资源浪费，通标公司有权收回该帐号，因此带来的损失将由您自行承担。',
        four_6: '4.6 您的言行应遵守《计算机信息网络国际联网安全保护管理办法》、《互联网信息服务管理办法》、《互联网电子公告服务管理规定》、《维护互联网安全的决定》等相关法律规定，您不能利用帐户制作、复制、上传、发布、传播任何与如下要求相悖的内容（该内容是指您使用平台过程中所制作、复制、上传、发布、传播的任何内容，包括但不限于帐户头像、名称、用户说明、注册信息及其他资料，或文字、语音、图片、视频、图文、图表、漫画等发送、回复消息、评论和相关链接页面，以及其他使用帐户平台服务所产生的内容）：',
        four_6_1: '(1)反对宪法所确定的基本原则的；',
        four_6_2: '(2)危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；',
        four_6_3: '(3)损害国家荣誉和利益的；',
        four_6_4: '(4)煽动民族仇恨、民族歧视，破坏民族团结的；',
        four_6_5: '(5)破坏国家宗教政策，宣扬邪教和封建迷信的；',
        four_6_6: '(6)散布谣言，扰乱社会秩序，破坏社会稳定的；',
        four_6_7: '(7)散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；',
        four_6_8: '(8)侮辱或者诽谤他人，侵害他人合法权益的；',
        four_6_9: '(9)含有法律、法规和政策禁止的其他内容的信息。',
        four_7: '4.7 为保证的正常运营及用户的良好体验，请您不要利用平台制作、复制、上传、发布、传播如下内容',
        four_7_1: '(1)含有任何性或性暗示以及任何其他低俗类信息；',
        four_7_2: '(2)骚扰、垃圾广告；',
        four_7_3: '(3)涉及他人隐私、个人信息或资料的任何信息；',
        four_7_4: '(4)侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利的任何信息；',
        four_7_5: '(5)含有其他干扰正常运营、侵犯其他用户或其他第三方合法权益内容的信息。',
        four_8: '4.8 为确保通标公司和用户的利益，您请在使用本平台时，不要进行如下行为（该行为是指使用帐户平台所进行的任何行为，包括但不限于注册登录、帐号运营、管理及推广以及其他行为）：',
        four_8_1: '(1)提交、发布虚假信息，或冒充、利用他人名义进行相关活动；',
        four_8_2: '(2)强制、诱导其他用户关注、点击链接页面或分享信息；',
        four_8_3: '(3)虚构事实、隐瞒真相以误导、欺骗他人；',
        four_8_4: '(4)侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利；',
        four_8_5: '(5)未经通标公司书面许可使用插件、外挂或其他第三方工具、服务接入本服务和相关系统；',
        four_8_6: '(6)利用平台及帐户从事违法犯罪活动；',
        four_8_7: '(7)制作、发布与以上行为相关的方法、工具，或对此类方法、工具进行运营或传播；',
        four_8_8: '(8)其他违反法律法规规定、侵犯其他用户合法权益、干扰产品正常运营或未经通标公司明示授权的行为。',
        four_9: '4.9 请您在任何情况下都不要私自使用通标公司的包括但不限于“SGS”在内的任何商标、服务标记、商号、域名、网站名称或其他显著品牌特征等。未经通标公司事先书面同意，您不得将本平台标识以任何方式展示、使用或申请注册商标、进行域名注册等，也不得实施向他人明示或暗示有权展示、使用、或其他有权处理本平台标识的行为。您由于非法使用本平台标识给本平台或他人造成损失的，由您承担相关法律责任',
        four_10: '4.10 若您需对本平台内容创作衍生品或投放商业广告，请您另外提交书面授权申请，在符合条件且得到通标公司同意下，您方可通过该平台进行广告或推广等商业活动。',
        sgsRightsAndDuties_5: '五、通标公司的权利和义务',
        five_1: '5.1 为保障用户和公司的利益，通标公司有权对您注册时提交的材料和信息进行审查，并有权要求您改正或补充相关材料，请您理解。如果您拒绝改正或补充相关材料，您可能无法使用本服务。通标公司的审查不代表对您提交的材料和信息的真实性、准确性、真实性、合法性负责。您应当对该材料和信息独立承担责任，如因此给通标公司或第三方造成损害的， 您应当承担法律责任并予以赔偿。',
        five_2: '5.2 通标公司为平台的开发、运营提供技术支持，并对该平台的开发和运营等过程中产生的所有数据和信息等享有全部权利。',
        five_3: '5.3 如果您停止使用本服务或服务被终止或取消，通标公司有权自主决定是否从服务器上永久地删除您的数据且无需向您返还任何数据。',
        five_4: '5.4 通标公司保留随时变更、暂停、限制、终止或撤销平台服务的权利。公司可通过网页公告、电子邮件、电话或信件传送等方式向您发出通知，通知在发送时即视为已送达收件人，届时公司无需向您承担任何责任。\n',
        five_5: '5.5 您充分理解并同意：本服务中可能包括通标公司针对个人或企业推出的信息发布或品牌推广服务，您同意通标公司有权在本平台显示平台和/或第三方供应商、合作伙伴的商业广告或商业信息。',
        intellectualProperty_6: '六、知识产权',
        six_1: '6.1 在本服务中，由您通过https://plus.sgsmart-online.com平台上传、发布的任何内容的知识产权归属您或原始版权人所有，以上内容您授权通标公司使用并授权通标公司对侵犯以上内容版权的行为进行维权。通标公司在本服务中提供的内容（包括但不限于网页、文字、图片、音频、视频、图表等）的知识产权属于通标公司所有。通标公司提供本服务时所依托的软件的著作权、专利权及其他知识产权均归通标公司所有。',
        six_2: '6.2 您应当是在注册资料中提交的网站的合法权利人。本协议的合作范围是您提交网站的全部内容，除非您另有明确表示，您在注册时点击同意，即表明您同意授权通标公司收录、链接您网站中的全部内容，并通过系统以您的注册帐户自动发布。如您对授权范围另有需求可以书面方式通知通标公司并另行签订授权协议。',
        six_3: '6.3 您理解并且同意，为持续改善通标为您提供的各项服务，您授予通标公司及其关联方、合作方对您上传发布的任何内容具有全世界范围内的、永久的、不可撤销的、免费的、非独家的使用权。',
        six_4: '6.4 本服务所包含的内容的知识产权均受到法律保护，未经通标公司、用户或相关权利人书面许可，任何人不得以任何形式进行使用或创造相关衍生作品。',
        privacy_7: '七、隐私政策',
        seven_1: '7.1 用户知悉并同意：个人隐私信息是指能够对用户进行个人辨识或涉及个人通信的信息，包括用户真实姓名、身份证号、手机号码、银行账户、IP地址等。非个人隐私信息是指用户对本服务的操作状态以及使用习惯等一些明确且客观反映在本平台服务器端的基本记录信息和其他一切个人隐私信息范围外的普通信息，以及用户同意公开的上述隐私信息。',
        seven_2: '7.2 因您使用平台不同服务内容时，为保证功能服务的完整体验，产品可能会收集到您的地理位置、读取您的通讯录、开启您使用工具的摄像头、话筒，如您不希望开启相关功能，可停止使用对应服务，通标公司不会开启与用户使用的服务无关的功能。',
        seven_3: '7.3 本平台不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本网站的非公开内容，但下列情况除外：',
        seven_3_1: '(1)事先获得用户的明确授权；',
        seven_3_2: '(2)根据有关的法律法规要求；',
        seven_3_3: '(3)按照相关政府主管部门的要求；',
        seven_3_4: '(4)该第三方同意承担与本平台同等的保护用户隐私的责任。',
        seven_4: '7.4 在不透露单个用户隐私资料的前提下，本平台有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。',
        seven_5: '7.5 为了运营和改善https://plus.sgsmart-online.com平台的技术和服务，便于本平台向您及用户提供更好的体验和提高服务质量，通标公司将可能会自行收集使用或向第三方提供您的非个人隐私信息。\n',
        legalResponsibility_8: '八、法律责任',
        eight_1: '8.1 若您提交的注册信息和材料不真实、不完整、不合法或无效，那么导致或产生的一切法律责任由您承担。通标公司有权随时封禁或删除您的平台帐号，以及中止或终止为您提供平台的相关服务。',
        eight_2: '8.2 您理解并认可，本平台为提供信息分享、传播及获取的平台，您在使用本平台时，请您自行对内容加以判断，并承担因使用内容而引起的所有风险。您须为自己注册帐户下的一切行为负责，包括您所发表内容的真实性、合法性、准确性、有效性，以及承担因账号使用、运营、管理行为产生的结果。您应对平台中的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容真实性、合法性、准确性、有效性的依赖而产生的风险。通标公司无法且不会对因用户行为而导致的损失或损害承担责任。 如果您发现任何人违反本协议规定或以其他不当的方式使用平台服务，请立即举报或投诉，我们将依法进行处理。',
        eight_3: '8.3 对违反有关法律法规或本协议规定的行为，通标公司将依法律规定及上述规则等加以合理判断进行处理，对违法违规的任何人士采取适当的法律行动，并依据法律法规保存有关信息并向有关部门报告等。',
        eight_4: '8.4 若您上传、发布的内容或其他在https://plus.sgsmart-online.com平台上从事的行为侵害他人利益并引发第三方的任何索赔、要求或赔偿的，需由您承担全部法律责任。若因此给通标公司或第三方造成任何损失，您应负责赔偿并使之免受损害，损失包括但不限于诉讼费用、律师费用、和解费用、罚款或生效法律文书中规定的损害赔偿金额及其他直接或间接支出费用。',
        eight_5: '8.5 若通标公司发现您不当使用本平台帐号或因您的帐号被他人举报投诉时，通标公司有权不经通知随时删除相关内容，并视行为情节对违规帐号进行处理，处理方式包括但不限于警告、删除部分或全部订阅用户、限制或禁止使用全部或部分功能、帐号封禁甚至注销，并有权视具体情况而公告处理结果。',
        eight_6: '8.6 因技术故障等不可抗事件影响到服务的正常运行的，本平台及其合作单位承诺在第一时间内与相关单位配合，及时处理进行修复，但您因第三方如电信部门的通讯线路故障、技术问题、网络、电脑故障、系统不稳定性及其他各种不可抗力原因而遭受的一切损失，本平台及其合作单位不承担责任。',
        otherAgreements_9: '九、其他约定',
        nine_1: '9.1 您使用本服务即视为您已阅读并同意受本协议的约束。',
        nine_2: '9.2 必要时通标公司会对本协议的部分内容进行修改。修改后，将在页面显著位置提示协议有更新，您应及时查看更新后的协议。如果您同意接受修改后的协议，您可以继续使用平台；如果您不接受则应停止使用https://plus.sgsmart-online.com平台服务',
        nine_3: '9.3 您和通标公司均是独立的主体，在任何情况下本协议不构成双方之间的代理、合伙、合营或雇佣关系。',
        nine_4: '9.4 本协议的成立、生效、履行、解释及纠纷解决，都适用于中华人民共和国的法律。本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。',
        nine_5: '9.5 如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决。协商不成时，诉讼管辖权归属本合同签订地法院。',
        nine_6: '9.6 本协议签订地为中华人民共和国上海市徐汇区。',


    },
    extendData:{
        title:'',
        specificCode:'Code',
        specificNameEn:'扩展菜单名(英文)',
        specificNameCn:'扩展菜单名(中文)',
        contentEn:'展示内容(英文)',
        contentCn:'展示内容(中文)',
        template:'申请表模板',
        isMenu:'菜单栏展示',
        selTemplate:'请选择模板',
        specificCodeBlur:'请输入specificCode',
        specificNameEnBlur:'请输入扩展菜单名(英文)',
        contentEnBlur:'请输入声明内容(英文)',
        newTitle:'新增',
        editTitle:'编辑'
    },
    aflTemplate:{
        management:{
            addTemplate:'添加模板',
            delTemplate:'删除模板',
        }
    },
    settings: {
        componentsList: '组件列表',
        selectPage:'请选择页面',
        lock: '锁定',
        unLock: '编辑',
        error: {
            noBuCode: '抱歉, 请选择产品线.',
            noPageCode: '抱歉, 请选择配置页面.'
        },
        pages: [{
            pageName: '首页',
            pageCode: 'client_home_page'
        }]
    },
    SEComment:{
        eq:'＝',
        notEq:'≠',
        in:"In",
        isBlank:"Is Blank",
        isNotBlank:"Is Not Blank",
        notIn:'Not In',
        columnTitle:'显示配置',
        checkAll:'全选',
        columnSelected:'列被选择',
        manageColumn:'设置展示列',
        filterRule:'角色过滤',
        filterRuleTip:'选择相应的客户角色并查看相关的TRF记录',
    },
    trfPrint:{
        general:{
            serviceType:'服务类型',
            trfNo:'TRF 编号',
            buyerName:'买家',
            labName:'实验室名称',
            agent:'代理商',
            labAddress:'实验室地址',
            selfReference:'是否自测',
            labContact:'实验室联系人',
            validLabContact:'请填写正确的邮箱格式',
            isEfilling:"是否eFiling",
        },
        customerInformation:{
            applicant:'申请方信息',
            payer:'付款方信息',
            buyer:'买家信息',
            agent:'代理商信息',
            supplier:'供应商信息',
            manufacture:'生产工厂信息',
            applicantCustomerCode:'申请方客户编码',
            payerCustomerCode:'付款方客户编码',
            buyerCustomerCode:'买家客户编码',
            agentCustomerCode:'代理商客户编码',
            supplierCustomerCode:'供应商客户编码',
            manufactureCustomerCode:'生产工厂客户编码',
            sgsCustomerNo:'SGS 客户编码',
            companyNameEn:'公司名称（英文）',
            companyNameLocal:'公司名称（本地语言）',
            companyAddressEn:'公司地址（英文）',
            companyAddressLocal:'公司地址（本地语言）',
            contact:'联系人',
            phone:'电话',
            email:'邮箱'
        },
        dff:{
            formTitle:'样品信息（*标记为必须填写的信息）',
            gridTitle:'样品列表',
            itemId:'样品ID',
            carelabelTitle:'洗水标及洗语言',
            carelabelItemId:'样品ID',
            carelabelCareWording:'洗语',
            carelabelSymbols:'洗水标'
        },
        otherRequest:{
            title:'服务需求',
            reportHeaderEn:'报告抬头(英文)',
            reportHeaderZh:'报告抬头(中文)',
            reportAddressEn:'报告抬头地址(英文)',
            reportAddressZh:'报告抬头地址(中文)',
            reportDeliveredTo:'报告发送至',
            failedReportDeliveredTo:'不通过报告发送至',
            reportLanguage:'报告语言',
            additionalRequest:'其他需求'
        },
        attach:{
            info:'附件信息',
            num:'编号',
            fileName:'文件名称'
        },
        returnSample:{
            returnSampleRequire:'退样要求',
            returnSampleRequest:'退样要求',
            judgmentRule:'判定规则',
            reportSymbols:'报告需出具标志',
            isCommon:'判定',
            isCopyName:'纸质报告',
            photo:'拍照',
            isConfimCover:'首页确认',
            isQuotation:'报价',
            softCopyContactName:'电子报告邮寄联系人',
            vatType:'发票类型',
            needProformaInvoice:'形式发票',
            invoiceDeliverWay:'发票寄送方式',
            invoiceContactName:'发票邮寄联系人',
            returnSampleContactName:'退样邮寄联系人',
            returnSampleDeliverWay:'退样方式',
            liquid:'液体、油墨样品处理方式',
            isOutside:'是否接受将样品交于SGS以外的机构做检测',
            isLegalProceeding:'以上申请是否涉及法律纠纷？'
        },
        testRequest:'测试请求',
        remark:'备注',
        noData:'无数据',
        declaration1:'# 申请人请求并声明',
        declaration2:'申请人要求本条所述的指定付款人 申请表，用于支付与此职位申请相关的所有费用。申请人确认： 付款人已得到充分通知，并同意此付款安排。如果指定付款人拒绝 若要支付，或延迟或不正确支付，申请人无条件同意结算所有金额 未付发票或与付款人延迟和错误付款相关的任何费用 与此应用程序相关。',
        availability:'# 根据SGS确定的要求测试的适用性和服务可用性。',
        website:'我们的服务受SGS网站所述条款和条件的约束',
        sign:'申请人签名及盖章'
    },
    footerBar:{
        termsOfUse:'SGS SMART 使用条款',
        DPP:'数据隐私声明',
        termsOfUsePdfPath:'/static/pdf/SGSSMARTTermsofUseCH.pdf',
        DPPPdfPath:'/static/pdf/SGSSMARTDataPrivacyNoticeCH.pdf'
    },
    crud: {
        filter: {
          addBtn: '新增条件',
          clearBtn: '清空数据',
          resetBtn: '清空条件',
          cancelBtn: '取 消',
          submitBtn: '确 定',
        },
        column: {
          name: '列名',
          hide: '隐藏',
          fixed: '冻结',
          filters: '过滤',
          sortable: '排序',
          index: '顺序',
          width: '宽度',
        },
        tipStartTitle: '当前表格已选择',
        tipEndTitle: '项',
        editTitle: '编 辑',
        copyTitle: '复 制',
        addTitle: '新 增',
        viewTitle: '查 看',
        filterTitle: '过滤条件',
        showTitle: '列显隐',
        menu: '操作',
        addBtn: '新 增',
        show: '显 示',
        hide: '隐 藏',
        open: '展 开',
        shrink: '收 缩',
        printBtn: '打 印',
        excelBtn: '导 出',
        updateBtn: '修 改',
        cancelBtn: '取 消',
        searchBtn: '搜 索',
        emptyBtn: '清 空',
        menuBtn: '功 能',
        saveBtn: '保 存',
        viewBtn: '查 看',
        editBtn: '编 辑',
        copyBtn: '复 制',
        delBtn: '删 除',
      },
    work:{
        operationSuccessful: "操作成功",
        selectAtLeastOneData: "请选择至少一条数据",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirm_delete_msg: "确定将选择数据删除?",
        columnAction:"操作",
        btn:{

        },
        cpscCitation:{
          column:{
            status:"状态",
            citation_code:"标准编码",
            citation_code_required_msg:"请输入标准编码",
            citation_code_max_msg:"标准编码最多50字",
            regulation:"章程",
            regulation_required_msg:"请输入章程",
            regulation_max_msg:"章程最多200字",
            product_category:"产品类别",
            product_category_required_msg:"请输入产品类别",
            product_category_max_msg:"产品类别最多200字",
            certificate:"证书",
            certificate_required_msg:"请选择证书",
            keyword:"关键词",
            keyword_max_msg:"关键词最多50字",
          }
        },
        cpscExclusion:{
          column:{
            status:"状态",
            exclusion_code:"豁免编码",
            exclusion_code_required_msg:"请输入豁免编码",
            exclusion_code_max_msg:"豁免编码最多50字",
            regulation:"章程",
            regulation_required_msg:"请输入章程",
            regulation_max_msg:"章程最多200字",
            product_category:"产品类别",
            product_category_required_msg:"请输入产品类别",
            product_category_max_msg:"产品类别最多200字",
            certificate:"证书",
            certificate_required_msg:"请选择证书",
            keyword:"关键词",
            keyword_max_msg:"关键词最多50字",
          }
        },
        cpscCustomer:{
          column:{
            status:"状态",
            bossName:"客户名称",
            productPrimaryId:"物料主ID",
            secretKey:"密钥",
            token:"令牌",
            expiringDate:"到期日期",
            certifierId:"认证ID",
            bossCustomerCountryId:"国家",
            bossCustomerCityName:"城市",
            bossCustomerAddress:"地址",
            contactName:"联系人姓名",
            telphone:"电话",
            email:"邮箱",
            bossName_required_msg: "请输入客户名称",
            productPrimaryId_required_msg: "请选择物料主ID",
            syncCheck1:"操作失败，密钥、令牌、到期日期需全部填写",
            syncCheck2:"操作失败，密钥已过期",
          }
        },
        cpscCustomerTrade:{
            tip:{
                productInfo:'请填写主产品的产品类型和产品ID',
                primary:'请选择产品主ID'
            },
          column:{
            status:"状态",
            tradeType:"类型",
            tradeName:"名称",
            alternateId:"Alternate Id",
            gln:"GLN",
            tradeAddress:"地址1",
            tradeAddress2:"地址2",
            apartment:"公寓号",
            tradeCountryId:"国家",
            tradeProvince:"省份",
            tradeCity:"城市",
            tradePostalCode:"邮政编码",
            telphone:"电话",
            requireTelphone:'请填写电话',
            validPhone:'电话格式不正确',
            email:"邮箱",
            requireEmail:'请填写邮箱',
            validEmail:'邮箱格式不正确',
            address:"地址",
            alternateId_gln_required_msg: "Alternate Id 或 GLN 至少填写一个",
            selectType_msg: "请选择类型",
            enterName_msg: "请输入名称",
            enterAddress1_msg: "请输入地址1",
            enterAddress1_msg2:'最大字符不允许超过50',
            keyword:"关键词",
            countryId_required_msg: "请选择国家",
            cityName_required_msg: "请输入城市",
          }
        },
        cpscTrfInfo:{
            buyerMessage:"请选择客户名称",
            selectNoData:'请输入关键词检索制造商',
            placeholder:{
                input:'请输入 ',
                select:'请选择 '
            },
            confirmCopyMsg: "直接复制新增还是复制修改？",
            confirmDeleteMsg: "是否确认删除本条数据？",
            export:'是否导出数据？',
            lastOneData:'请选择至少一条数据',
            btn:{
                copyAndAdd: "新增",
                copyAndEdit: "修改"
              },
          column:{
            manufacture:"制造商名称",
            certificateType:'认证类型',
            lastTestDate:'最后测试时间',
            trfInfoStastus:"信息状态",
            referenceNo:"参考单号",
            buyerCustomerName:"买家",
            manufactureCustomerName:"制造商",
            certVersion:"认证编码",
            updateTime:"修改时间",
            Product_ID:"产品ID",
            ProductName:"产品名称",
            buyInfo:"买家信息",
            cancel:'取 消',
            customerName:"客户名称",
            customerNameLocal:"客户名称 (本地)",
            customerCountry:"客户国家",
            customerCity:"客户城市",
            customerAddress:"客户地址",
            customerAddressLocal:"客户地址 (本地)",
            contactName:"联系人",
            contactTelephone:"联系人电话",
            contactEmail:"联系人邮箱",
            createTime:'创建时间',
            createUser:'创建人',
            manufactureInfo:"制造商信息",
            basicInfo:"基本信息",
            serviceType:"服务类别",
            labName:"实验室名称",
            labContact:"实验室联系人",
            csEmailAddressMsg:"请填写联系人邮箱地址",
            labAddress:"实验室地址",
            poc:"测试结果记录的接触点",
            pocType:"POC类型",
            otherPoc:"其他POC",
            attachment:"附件",
            labInfo:"实验室信息",
            labType:"实验室类型",
            testReportId:"测试报告编号",
            testReportKey:"测试报告密钥",
            reportUrl:"报告地址",
            citationsCode:"标准类别",
            testingExclusions:"测试排除项",
            certificateInfo:"认证信息",
            certificateVersion:"认证版号",
            placeholderCertificateVersion:"请填写认证版号",
            cpscCollection:"CPSC Collection",
            save:"暂 存",
            submit:"提 交",
            validated:"验 证",
            rework:"修 订",
            exportCsv:"导出Csv",
            entry:'报告',
            toCpsc:"上报CPSC",
            toCpscLog:"上报日志",
            crudEdit:"编 辑",
            exportWord:"导出Word",
            copy:"复制",
            pending:"暂停",
            unPending:"继 续",
            trfReferenceNo:"TRF申请单号",
            action:'更多操作',
          }
        },
        trfMapping:{
          dialog:{
            addTitle:'新增映射关系',
            editTitle:'编辑映射关系'
          },
          column:{
            status:"状态",
            productLine:"产品线",
            productLine_required_msg:"请输入产品线",
            productLine_max_msg:"产品线最多50字",
            customerName:"客户名称",
            customerName_required_msg:"请输入客户名称",
            customerName_max_msg:"客户名称最多200字",
            productCategory:"产品类别",
            productCategory_required_msg:"请输入产品类别",
            productCategory_max_msg:"产品类别最多200字",
            customerProductCategory:"客户产品类别",
            customerProductCategory_required_msg:"请输入客户产品类别",
            customerProductCategory_max_msg:"客户产品类别最多200字",
            dffTemplate:"数据模板",
            dffTemplate_required_msg:"请输入数据模板",
            dffTemplate_max_msg:"数据模板最多200字",
          },
        },
      }
}
