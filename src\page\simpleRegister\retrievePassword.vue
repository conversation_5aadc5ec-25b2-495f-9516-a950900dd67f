<template>
  <div class="register-container">
    <el-card>
      <el-form :model="form" ref="registerForm" status-icon :rules="rules" label-width="160px"
               class="demo-ruleForm" size="small">
        <div class="sgs-group">
          <el-row>
            <h3>找回密码</h3>
            <div class="right">
              <top-lang :loadMenu="false"></top-lang>
            </div>
          </el-row>
        </div>
        <el-row>
          <el-form-item label="账号" prop="account">
            <el-input clearable ref="account" v-model="form.account"
                      maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input clearable ref="taxNo" v-model="form.email"
                      maxlength="50"></el-input>
          </el-form-item>
        </el-row>
        <!--<div v-if="isShow">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('customer.contactMobile')" prop="contactMobile">
                <el-input
                        clearable
                        v-model="form.contactMobile"
                        maxlength="11">
                  <el-button :disabled="showSendSmsBtn" slot="append" @click="sendSMSCode">
                    <span v-show="showCounter">{{$t('sms.sendCode')}}</span>
                    <span v-show="!showCounter" class="count">{{counter}} s</span>
                  </el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sms.code')" prop="checkCode" :error="codeErrorMsg">
                <el-input
                        clearable
                        maxlength="4"
                        v-model="form.checkCode"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item :label="$t('customer.address')" prop="customerAddressZh">
              <el-input
                      maxlength="200"
                      clearable v-model="form.customerAddressZh"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('customer.sgs.customerNo')" prop="bossNo">
                <el-input
                        maxlength="50"
                        clearable v-model="form.bossNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('customer.sgs.reportNo')" prop="reportNo">
                <el-input
                        maxlength="50"
                        clearable v-model="form.reportNo"
                        :placeholder="$t('register.sgsReportNoBlur')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item :label="$t('customer.certificate')" prop="qualificationUrl">
              <el-upload action="/api/sgsapi/FrameWorkApi/file/doUpload?systemID=1"
                         class="avatar-uploader"
                         drag
                         :show-file-list="false"
                         :on-success="uploadSuccess">
                <img v-if="form.qualificationUrl" :src="form.qualificationUrl"
                     style="width: 100%;height: 100%;">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-row>
        </div>-->
        <!--<div class="sgs-group">
          <h3>{{$t('customer.title.admin')}}</h3>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('account.email')" prop="email">
              <el-input
                      maxlength="200"
                      clearable v-model="form.email" :placeholder="$t('register.emailBlur')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('account.userName')" prop="userName">
              <el-input
                      maxlength="100"
                      clearable v-model="form.userName" autoComplete="off"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('account.password')" prop="password">
              <el-input
                      maxlength="50"
                      clearable type="password" v-model="form.password" autocomplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('account.passwordConfirm')" prop="doublePassword">
              <el-input
                      maxlength="50"
                      clearable type="password" v-model="form.doublePassword"
                      auto-complete="new-password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="text-align: center;margin-top: 30px;">
          <router-link type="text" :to="{path:'/'}" style="padding-right: 20px;">
            {{$t('register.returnLogin')}}
          </router-link>
          <el-button size="small" type="primary" :disabled="submitDisabled" :loading="registerSubmitLoading"
                     @click="onSubmit">{{$t('register.submitRegister')}}
          </el-button>
        </el-row>-->
      </el-form>
    </el-card>
  </div>
</template>


<script>
  import {
    add,
    checkCustomer,
    checkAccount,
    sendVerificationCode,
  } from "@/api/customer/customerRegister";
  import {getCloudFileURL, getSgsCustomer} from "@/api/common/index";
  import {validatenull} from "@/util/validate";
  import {mapGetters} from "vuex";

  const TIME_COUNT = 60; //更改倒计时时间
  export function isvalidPhone(str) {
    const reg = /^1[********]\d{9}$/;
    return reg.test(str)
  }

  export default {
    components: {
      TopLang: resolve => require(['../index/top/top-lang'], resolve)
    },
    data() {
      var validPhone = (rule, value, callback) => {
        if (!value) {
          callback(this.$t('register.telePhoneBlur'))
        } else if (!isvalidPhone(value)) {
          callback(this.$t('register.telePhoneBlur'))
        } else {
          callback()
        }
      }
      var validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error(this.$t('register.doublePasswordBlur')));
        } else if (value !== this.form.password) {
          callback(new Error(this.$t('register.checkPassNo')));
        } else {
          callback();
        }
      };
      var validateAccount = (rule, value, callback) => {
        if (!validatenull(value)) {
          checkAccount({email: this.form.email}).then(res => {
            //判断显示客户需要填写的信息
            if (res.data.success === 1 || res.data.success === '1') {
              if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                callback(new Error(this.$t('register.submitInfo')));
              } else {
                callback(new Error(this.$t('register.lockError')));
              }
              //setTimeout(this.$router.push('/'),3000);
            } else {
              callback();
            }
          });
        }
      };
      return {
        codeErrorMsg:null,
        name: "customerRegister",
        form: {
          account:'',
          qualificationUrl: '',
          taxNo: '',
          bossNo: '',
        },
        submitDisabled: false,
        registerSubmitLoading: false,
        showSendSmsBtn: false,
        isShow: false,
        showCounter: true,
        counter: 60,
        timer: null,
        customerData: {},
        rules: {
          account: [
            {required: true, message: '请输入要找回密码的账号', trigger: 'blur'},
          ],
          /*contactMobile: [
              {
                  required: true,
                  pattern: /^1[********]\d{9}$/,//可以写正则表达式呦呦呦
                  message: this.$t('register.telePhoneBlur'),
                  trigger: 'blur'
              },
          ],*/
          contactMobile: [
            {required: true, trigger: 'blur', validator: validPhone}//这里需要用到全局变量
          ],
          checkCode: [
            {required: true, message: this.$t('register.pleaseInputCode'), trigger: 'blur'},
          ],
          customerAddressZh: [
            {required: true, message: this.$t('register.addressBlur'), trigger: 'blur'},
          ],
          email: [
            {required: true, message: this.$t('register.emailBlur'), trigger: 'blur'},
            {type: 'email', message: this.$t('register.emailRigthBlur'), trigger: ['blur', 'change']},
            {validator: validateAccount, trigger: 'blur'}
          ],
          userName: [
            {required: true, message: this.$t('register.accountBlur'), trigger: 'blur'},
          ],
          password: [
            {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
            {
              pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
              message: this.$t('register.passwordError')
            }
          ],
          doublePassword: [
            {required: true, message: this.$t('register.passwordBlur'), trigger: 'blur'},
            {validator: validatePass2, trigger: 'blur'}
          ],
        }
      }
    },
    computed: {
      ...mapGetters(["language"]),
    },
    methods: {
      onSubmit() {
        this.$refs['registerForm'].validate((valid) => {
          if (valid) {
            this.submitDisabled = true;
            this.registerSubmitLoading = true;
            //验证短信验证码是否过期
            //checkSmsCode(this.form.contactMobile, this.form.checkCode).then(res => {
            //  if (res.data.data) {//验证通过
            const register = {};
            register.customer = this.form;
            register.checkCode = this.form.checkCode;
            register.customer.customerNameZh = this.form.customerName;
            register.customer.customerNameEn = this.form.customerName;
            register.customer.qualification = this.form.qualification;
            register.customer.account = {
              email: this.form.email,
              userName: this.form.userName,
              password: this.form.password
            };
            add(register).then(() => {
              this.submitDisabled = false;
              this.registerSubmitLoading = false;
              this.$message({
                type: "success",
                message: this.$t('api.success')
              });
              this.$router.push('/register/success');
            }, error => {
              this.submitDisabled = false;
              this.registerSubmitLoading = false;
              console.log(error);
            });
            /*} else {
                this.submitDisabled = false;
                this.registerSubmitLoading = false;
                this.$nextTick(() => {this.codeErrorMsg=this.$t('sms.smsCodeValidateError');})
                this.$message({
                    type: "error",
                    message: this.$t('sms.smsCodeValidateError')
                });
            }*/
            //});
          } else {
            this.submitDisabled = false;
            this.registerSubmitLoading = false;
            console.log('error submit!!');
            return false;
          }
        });
      },
      checkExists() {
        if (this.form.customerName != '' && this.form.customerName != undefined && this.form.customerName.length > 3) {
          checkCustomer(this.form.customerName).then(res => {
            //判断显示客户需要填写的信息
            if (!validatenull(res.data.data)) {
              this.customerData = res.data.result;
              //this.form.bossNo=this.customerData.number;
              this.isShow = false;
            } else {
              this.isShow = true;
              this.form.id = null;
            }
          });
        }
      },
      querySearch(query, callback) {
        this.loading = false;
        if (query != '' && query != undefined && query.length >= 2) {
          this.loading = true;
          getSgsCustomer({customerName: query, rows: 5}).then(res => {
            const results = [];
            res.data.rows.forEach((currentValue, index) => {
              if (this.language == 'en-US') {
                results.push({
                  'value': currentValue.nameEN,
                  "taxNo": currentValue.taxNo,
                  "bossNo": currentValue.number
                });
              } else {
                results.push({
                  'value': currentValue.nameCN,
                  "taxNo": currentValue.taxNo,
                  "bossNo": currentValue.number
                });
              }
            });
            this.form.bossNo = '';
            this.form.taxNo = '';
            callback(results);
          }, error => {
            console.log(error);
          });
        }
      },
      handleSelect(item) {
        this.checkExists();
        this.form.bossNo = item.bossNo;
        this.form.taxNo = item.taxNo;
        this.$refs['taxNo'].focus();
      },
      uploadSuccess(res, file) {
        this.form.qualification = {};
        this.form.qualification.attachmentId = res.data[0].cloudID;
        getCloudFileURL(this.form.qualification.attachmentId).then(res => {
          this.form.qualificationUrl = res.data;
        });
      },
      sendSMSCode() {
        this.showSendSmsBtn = true;
        //验证手机号
        this.$refs.registerForm.validateField("contactMobile", errMsg => {
          if (!errMsg) {//校验通过
            console.log("手机号校验通过");
            sendVerificationCode(this.form.contactMobile,1).then(res => {
              this.showCounter = false;//展示时间倒计时
              if (!this.timer) {
                this.counter = TIME_COUNT;
                this.showCounter = false;
                this.timer = setInterval(() => {
                  if (this.counter > 0 && this.counter <= TIME_COUNT) {
                    this.counter--;
                  } else {
                    this.showSendSmsBtn = false;
                    this.showCounter = true;//时间计数完毕重新展示发送按钮
                    clearInterval(this.timer);  // 清除定时器
                    this.timer = null;
                  }
                }, 1000)
              } else {
                this.showCounter = true;
              }
            });
          } else {
            //手机号码验证失败
            this.showSendSmsBtn = false;
          }
        });
      },
    }
  }
</script>

<style lang="scss">
  .register-container {
    background: url("/img/loginBackground.png") center no-repeat;
    background-size: cover;
    width: 100%;
    height: 100%;
    padding: 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-card {
      width: 70%;
      height: 90%;
      overflow-y: auto;
    }

    .top-icon {
      color: #303133 !important;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 148px;
      height: 148px;
      line-height: 178px;
      text-align: center;
    }
  }
</style>
