const router = require("koa-router")();
const Api = require("./../../request");

// 天眼查查询
router.post("/api/sgs-mart/register/account/searchCompany", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/register/account/searchCompany", ctx);
  //console.log("result datas:", datas);
  ctx.body = datas;
});
// 校验phone/email是否存在
router.post("/api/sgs-mart/register/account/checkAccount", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/register/account/checkAccount", ctx);
  //console.log("result check customer  datas:",datas);
  ctx.body = datas;
}); 
//根据访问ip获取语言
router.get("/api/sgs-mart/userSetting/getLanguageByRemoteIp", async (ctx, next) => {
  const datas = await Api.get("sgs-mart/userSetting/getLanguageByRemoteIp", ctx);
  //console.log("result getLanguageByRemoteIp  datas:", datas);
  ctx.body = datas;
}); 
//获取手机验证码
router.post("/api/sgs-mart/register/account/verificationCode", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/register/account/verificationCode", ctx);
  //console.log("result send verification code  datas:", datas);
  ctx.body = datas;
});
//校验手机验证码
router.post("/api/sgs-mart/register/account/checkCode",async(ctx,next)=>{
  const datas = await Api.post("sgs-mart/register/account/checkCode", ctx);
  //console.log("result check sms code  datas:", datas);
  ctx.body = datas;
});
//查询service unit
router.post("/api/sgsapi/FrameWorkApi/serviceUnit/queryServiceUnit",async(ctx,next)=>{
  let datas = await Api.post("sgsapi/FrameWorkApi/serviceUnit/queryServiceUnit", ctx);
  //console.log("result query service unit  datas:", datas);
  if(datas && datas.length>0){
    datas = {
      status:200,
      data:datas
    }
  }
  ctx.body = datas;
});

router.post("/api/sgs-mart/register/account/companyExist",async(ctx,next)=>{
  const datas = await Api.post("sgs-mart/register/account/companyExist", ctx);
  //console.log("result tax no exist  datas:", datas);
  ctx.body = datas;
});
//注册提交
router.post("/api/sgs-mart/register/account/submit",async(ctx,next)=>{
  const datas = await Api.post("sgs-mart/register/account/submit", ctx);
  //console.log("result register  datas:", datas);
  ctx.body = datas;
});
// 注册发送二次手机验证码
router.post("/api/sgs-mart/register/account/sendSms",async(ctx,next)=>{
  const datas = await Api.post("sgs-mart/register/account/sendSms", ctx);
  //console.log("result register  datas:", datas);
  ctx.body = datas;
});
// 注册验证手机验证码
router.post("/api/sgs-mart/register/account/checkV2code",async(ctx,next)=>{
  const datas = await Api.post("sgs-mart/register/account/checkV2code", ctx);
  //console.log("result register  datas:", datas);
  ctx.body = datas;
});

router.post("/api/sgs-mart/register/account/checkEmail",async(ctx,next)=>{
  const datas = await Api.post("sgs-mart/register/account/checkEmail", ctx);
  ctx.body = datas;
})

router.get("/api/sgs-mart/customer/detail-for-user",async(ctx,next)=>{
  const datas = await Api.get("sgs-mart/customer/detail-for-user", ctx);
  ctx.body = datas;
})

router.get("/api/sgs-mart/common/tianyancha/search", async (ctx, next) => {
  const datas = await Api.get("sgs-mart/common/tianyancha/search", ctx);
  let resultData = {
    status:200,
    data:datas
  }
  ctx.body = resultData;
})

router.post("/api/sgs-mart/customer/updateCompanyInfo", async (ctx, next) => {
  const datas = await Api.post("sgs-mart/customer/updateCompanyInfo", ctx);
  ctx.body = datas;
})

module.exports = router;
