import { validatenull } from '@/utils/validate'
import website from '@/config/website'

// 获取存储键的前缀
const keyName = website.key + '-'

/**
 * 存储数据到 localStorage 或 sessionStorage
 * @param params - 存储参数对象
 * @param params.name - 存储的键名
 * @param params.content - 存储的内容
 * @param params.type - 存储类型，'session' 表示 sessionStorage，其他表示 localStorage
 */
export const setStore = (params: {
  name: string
  content: any
  type?: string
}) => {
  const { name, content, type } = params
  const fullName = keyName + name
  const obj = {
    dataType: typeof content,
    content,
    type,
    datetime: new Date().getTime(),
  }
  if (type === 'session') {
    window.sessionStorage.setItem(fullName, JSON.stringify(obj))
  } else {
    window.localStorage.setItem(fullName, JSON.stringify(obj))
  }
}

/**
 * 从 localStorage 或 sessionStorage 获取数据
 * @param params - 获取参数对象
 * @param params.name - 要获取的键名
 * @param params.debug - 是否返回原始存储对象，默认为 false
 * @returns 返回存储的内容，如果未找到则返回 undefined
 */
export const getStore = (params: { name: string; debug?: boolean }) => {
  const { name, debug = false } = params
  const fullName = keyName + name
  let obj: string | null = window.sessionStorage.getItem(fullName)
  if (validatenull(obj)) {
    obj = window.localStorage.getItem(fullName)
  }
  if (validatenull(obj)) {
    return
  }
  let parsedObj
  try {
    if (obj !== null) {
      parsedObj = JSON.parse(obj)
    } else {
      return null // 或者根据实际情况返回其他默认值
    }
  } catch {
    return obj
  }
  if (debug) {
    return parsedObj
  }
  let content
  if (typeof parsedObj === 'object' && parsedObj !== null) {
    switch (parsedObj.dataType) {
      case 'string':
        content = parsedObj.content
        break
      case 'number':
        content = Number(parsedObj.content)
        break
      case 'boolean':
        content = parsedObj.content === 'true';
        break
      case 'object':
        content = parsedObj.content
        break
      default:
        content = parsedObj.content
    }
  }
  return content
}

/**
 * 从 localStorage 或 sessionStorage 删除数据
 * @param params - 删除参数对象
 * @param params.name - 要删除的键名
 * @param params.type - 存储类型，'session' 表示 sessionStorage，其他表示 localStorage
 */
export const removeStore = (params: { name: string; type?: string }): void => {
  const { name, type } = params
  const fullName = keyName + name
  if (type === 'session') {
    window.sessionStorage.removeItem(fullName)
  } else {
    window.localStorage.removeItem(fullName)
  }
}

/**
 * 获取 localStorage 或 sessionStorage 中的所有数据
 * @param params - 获取所有数据的参数对象
 * @param params.type - 存储类型，'session' 表示 sessionStorage，其他表示 localStorage
 * @returns 返回包含所有存储数据的数组
 */
/**
 * 获取 localStorage 或 sessionStorage 中的所有数据
 * @param params - 获取所有数据的参数对象
 * @param params.type - 存储类型，'session' 表示 sessionStorage，其他表示 localStorage
 * @returns 返回包含所有存储数据的数组
 */
export const getAllStore = (
  params: { type?: string } = {},
): { name: string | null; content: any }[] => {
  const list: { name: string | null; content: any }[] = []
  const { type } = params
  const storage =
    type === 'session' ? window.sessionStorage : window.localStorage
  for (let i = 0; i < storage.length; i++) {
    const key = storage.key(i)
    if (key) {
      list.push({
        name: key,
        content: getStore({
          name: key,
        }),
      })
    }
  }
  return list
}

/**
 * 清空 localStorage 或 sessionStorage 中的所有数据
 * @param params - 清空参数对象
 * @param params.type - 存储类型，'session' 表示 sessionStorage，其他表示 localStorage
 */
export const clearStore = (params: { type?: string } = {}): void => {
  const { type } = params
  if (type === 'session') {
    window.sessionStorage.clear()
  } else {
    window.localStorage.clear()
  }
}
