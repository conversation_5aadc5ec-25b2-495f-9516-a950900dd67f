<template>
    <div class="sgs-box">
        <el-row>
            <h4 class="sgs-title">{{$t('trf.applicantInfo')}}</h4>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item :label="$t('customer.name')" v-if="valiUsable('customerNameEn')" prop="trfCustomer.customerNameEn" :rules="{required:valiRequired('customerNameEn'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                    <el-input maxlength="200" v-model="trfCustomer.customerNameEn" :disabled="showCustomerNameFlag"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="$t('customer.address')"  v-if="valiUsable('customerAddressEn')" prop="trfCustomer.customerAddressEn" :rules="{required:valiRequired('customerAddressEn'),message:$t('trf.validate.requiredBlur'),trigger:'change'}" >
                    <el-select v-model="trfCustomer.customerAddressEn" clearable
                               allow-create
                               @change="customerAddressChange"
                               filterable
                               style="width: 100%;">
                        <el-option v-for="(address,index) in customerAddressData"
                                   :label="address.addressDetail" :value="address.addressDetail"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="contact">
             <el-col :span="12">
                 <el-form-item :label="$t('contact.title.default')"  v-if="valiUsable('applyContactName')" prop="trfCustomerContact.applyContactName" :rules="{required:valiRequired('applyContactName'),message:$t('trf.validate.requiredBlur'),trigger:'change'}"  >
                     <el-select v-model="trfCustomerContact.applyContactName"
                                allow-create
                                @change="contactrNameChange" clearable
                                filterable
                                style="width: 100%;">
                         <el-option v-for="(contact,index) in customerContactData"
                                    :label="contact.contactName"
                                    :value="contact.contactName"></el-option>
                     </el-select>
                 </el-form-item>
                 <!--v-if="fieldSettingsData['applyContactTel'].usable==1"-->
                 <el-form-item :label="$t('contact.phone')"  v-if="valiUsable('applyContactTel')" prop="trfCustomerContact.applyContactTel" :rules="{required:valiRequired('applyContactTel'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomerContact.applyContactTel" clearable
                               autocomplete="off"></el-input>
                 </el-form-item>
             </el-col>
             <el-col :span="12">
                 <el-form-item :label="$t('contact.email')"  v-if="valiUsable('applyContactEmail')" prop="trfCustomerContact.applyContactEmail" :rules="{required:valiRequired('applyContactEmail'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                     <el-input maxlength="200" v-model="trfCustomerContact.applyContactEmail"
                               autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
                 <!--fieldSettingsData['applyContactFax'].required==1 ? true:false-->
                 <el-form-item :label="$t('contact.fax')" v-if="valiUsable('applyContactFax')" prop="trfCustomerContact.applyContactFax" :rules="{required:valiRequired('applyContactFax'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}">
                     <el-input maxlength="200" v-model="trfCustomerContact.applyContactFax"
                               autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
             </el-col>
         </el-row>
         <el-row>
             <h4 class="sgs-title">{{$t('trf.payInfo')}}</h4>
             <div style="position: relative;text-align: left; margin-left: 180px; margin-top: -50px;">
                 <el-checkbox-group v-model="identicalFlag" style="padding: 10px;">
                     <el-checkbox :label="$t('trf.sameApplicant')" name="type"></el-checkbox>
                 </el-checkbox-group>
             </div>
         </el-row>
         <el-row :gutter="20" v-if="!identicalFlag" style="margin-top:10px">
             <el-col :span="12">
                 <el-form-item :label="$t('customer.companyNameEn')"  v-if="valiUsable('payCustomerNameEn')"  prop="trfCustomer.payCustomerNameEn" :rules="{required:valiRequired('payCustomerNameEn'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomer.payCustomerNameEn" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
                 <el-form-item :label="$t('customer.addressEn')" v-if="valiUsable('payCustomerAddressEn')"  prop="trfCustomer.payCustomerAddressEn" :rules="{required:valiRequired('payCustomerAddressEn'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}"  >
                     <el-input maxlength="200" v-model="trfCustomer.payCustomerAddressEn" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
                 <el-form-item :label="$t('customer.companyNameCn')" v-if="valiUsable('companyNameCn')" prop="trfCustomer.companyNameCn" :rules="{required:valiRequired('companyNameCn'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}"  >
                     <el-input maxlength="200" v-model="trfCustomer.payCustomerNameZh" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
                 <el-form-item :label="$t('customer.addressZh')" v-if="valiUsable('payCustomerAddressZh')" prop="trfCustomer.payCustomerAddressZh" :rules="{required:valiRequired('payCustomerAddressZh'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomer.payCustomerAddressZh" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
             </el-col>
             <el-col :span="12">
                 <el-form-item :label="$t('contact.title.default')"  v-if="valiUsable('payContactName')" prop="trfCustomerContact.payContactName"  :rules="{required:valiRequired('payContactName'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomerContact.payContactName" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
                 <el-form-item :label="$t('contact.phone')"  v-if="valiUsable('payContactTel')" prop="trfCustomerContact.payContactTel" :rules="{required:valiRequired('payContactTel'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomerContact.payContactTel" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
                 <el-form-item :label="$t('contact.email')"  v-if="valiUsable('payContactEmail')" prop="trfCustomerContact.payContactTel" :rules="{required:valiRequired('payContactEmail'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomerContact.payContactEmail" clearable
                               autocomplete="off"></el-input>
                 </el-form-item>
                 <el-form-item :label="$t('contact.fax')" v-if="valiUsable('payContactFax')" prop="trfCustomerContact.payContactFax" :rules="{required:valiRequired('payContactFax'),message:$t('trf.validate.requiredBlur'),trigger:'blur'}" >
                     <el-input maxlength="200" v-model="trfCustomerContact.payContactFax" autocomplete="off"
                               clearable></el-input>
                 </el-form-item>
             </el-col>
         </el-row>
    </div>
</template>

<script>
    import {validatenull,validateEmail} from "@/util/validate";
    import {searchCustomer} from "@/api/customer/customer";
    import {getCustomerAddressList,getCustomerContactList
    } from "@/api/trf/trf";
    import {mapGetters} from "vuex";
    export default {
        name: "trf",
        props:['trfId','bossNo','isSgs','customer','contact','fieldSettingsData','isShow'],
        /*props: {
            trfId:String,
            bossNo:String,
            isSgs:{
                type:Boolean,
                default:false
            },
        },*/
        data() {
            return {
                applyField:{},
                trfCustomer:{},
                trfCustomerContact:{},
                identicalFlag: true,//默认与申请人一样
                showCustomerNameFlag:true,//是否可手动录入公司名称
                customerAddressData:[],
                customerContactData:[],
                customerContactParam:{},
                customerParam:{},

            }
        },
        computed: {
            /*fieldSettingWatch() {
                const { fieldSettingsData } = this
                return {
                    fieldSettingsData
                }
            },*/

            ...mapGetters(["userInfo", "language", "permission"]),
        },

        watch: {
            customer:{
                handler(){   //注意此处就是handler 回显处理
                    debugger;
                    this.queryCustomer(this.bossNo);
                    if(this.isShow){//回显数据
                        //回显地址和联系人
                        if(!validatenull(this.trfCustomer.customerId)){
                            this.$set(this.customerContactParam,'customerId',this.trfCustomer.customerId);
                            this.$set(this.customerContactParam,'status','1');
                            this.searchCustomerAddressData();//加载地址信息
                            this.searchCustomerContactData();//加载联系人信息
                        }
                    }else{//初始化页面
                        this.$set(this.trfCustomer, 'customerId', this.userInfo.companyId);
                        var companyId = this.userInfo.companyId
                        //查询客户地址信息
                        this.$set(this.customerContactParam,'customerId',companyId);
                        this.$set(this.customerContactParam,'status','1');
                        this.searchCustomerAddressData();//加载地址信息
                        this.searchCustomerContactData();//加载联系人信息
                        //判断是否存在bossNo
                        if(validatenull(this.bossNo)){
                            //申请方可手动输入
                            this.showCustomerNameFlag=false;
                        }
                    }
                },
                deep:true,
                //immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            contact:{
                handler(){   //注意此处就是handler 回显处理
                    debugger;
                    console.log(this.concat);
                    console.log(this.trfCustomerContact)
                    if(this.isShow){//回显数据

                    }
                },
                deep:true,
                //immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            /*fieldSettingWatch:{
                handler(newval , oldval){   //注意此处就是handler
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },*/
            trfCustomer:{
                handler(){   //注意此处就是handler
                    console.log(this.trfCustomerContact);
                    this.$emit('update:customer',this.trfCustomer);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            trfCustomerContact:{
                handler(){   //注意此处就是handler
                    console.log(this.trfCustomerContact);
                    this.$emit('update:contact',this.trfCustomerContact);
                },
                deep:true,
                immediate: true // watch 的一个特点是，最初绑定的时候是不会执行的，要等到 serviceList 改变时才执行监听计算。加上改字段让他最初绑定的时候就执行
            },
            identicalFlag: function (newVal) {
                this.$set(this.trfCustomer,'isSame',0);
                this.$set(this.trfCustomerContact,'isSame',0);
                if (newVal) {
                    this.$set(this.trfCustomer,'isSame',1);
                    this.$set(this.trfCustomerContact,'isSame',1);
                }
            },
        },
        created() {

            if(this.isSgs){

            }else{

            }

        },
        methods: {
            //验证表单项是否展示方法
            valiUsable(code){
                var usableFlag=true;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        usableFlag=  this.fieldSettingsData[code].usable!=1?false:true
                    }
                }
                return usableFlag;
            },
            //验证必填项方法
            valiRequired(code){
                var requiredFlag=false;
                if(this.fieldSettingsData!=null && this.fieldSettingsData!=undefined){
                    if(this.fieldSettingsData[code]!=null && this.fieldSettingsData[code]!=undefined){
                        requiredFlag=  this.fieldSettingsData[code].required==1?true:false
                    }
                }
                return requiredFlag;
            },
            queryCustomer(bossNo) {
                this.$set(this.customerParam, 'number', bossNo)
                var params = {};
                searchCustomer(Object.assign(params, this.customerParam)).then(res => {
                    const data = res.data.data;
                    if (data.length > 0) {
                        this.$set(this.trfCustomer, 'customerNameEn', data[0].nameEN);
                       /* if (this.servicRequire.reportHeader == '' || this.servicRequire.reportHeader == null) {
                            this.$set(this.servicRequire, 'reportHeader', data[0].nameEN)
                        }*/
                        this.$set(this.trfCustomer, 'sgsCustomerId', data[0].customerId);
                        this.$set(this.trfCustomer, 'sgsAccountId', data[0].accountId);
                        this.$set(this.trfCustomer, 'customerNameZh', data[0].nameCN);
                        this.$set(this.trfCustomer, 'customerNameEn', data[0].nameEN);
                       /* if (this.language == 'zh-CN') {
                            this.$set(this.trf.trfCustomer, 'customerNameEn', data[0].nameEN);
                            if (this.servicRequire.reportHeader == '' || this.servicRequire.reportHeader == null) {
                                this.$set(this.servicRequire, 'reportHeader', data[0].nameCN)
                            }
                        }*/
                    }
                });
            },
            searchCustomerAddressData() {
                var params = {};
                getCustomerAddressList(Object.assign(params, this.customerContactParam)).then(res => {
                    const data = res.data.data;
                    this.customerAddressData = data;
                    let obj = {};
                    obj = this.customerAddressData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if(!validatenull(obj)){
                        this.$set(this.trfCustomer,'customerAddressEn',obj.addressDetail);
                        /*if (this.servicRequire.reportAddress == '' || this.servicRequire.reportAddress == null) {
                            this.$set(this.servicRequire,'reportAddress',obj.addressDetail);
                        }*/
                    }
                });
            },
            searchCustomerContactData() {
                var params = {};
                getCustomerContactList(Object.assign(params, this.customerContactParam)).then(res => {
                    const data = res.data.data;
                    this.customerContactData = data;
                    let defaultObj = {};
                    defaultObj = this.customerContactData.find((item) => {
                        return item.isDefault === 1;
                    });
                    if(!validatenull(defaultObj)){
                        this.$set(this.trfCustomerContact,'applyContactName',defaultObj.contactName)
                        this.$set(this.trfCustomerContact,'applyContactTel',defaultObj.contactTel);
                        this.$set(this.trfCustomerContact,'applyContactEmail',defaultObj.contactEmail);
                    }
                });
            },
        }
    }
</script>

<style scoped>

</style>
