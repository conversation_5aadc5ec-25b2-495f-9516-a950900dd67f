import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/submit',
        method: 'post',
        data: form
    })
}


export const getPrograms = () => {
    return request({
        url: '/api/sgs-mart/authoriaztion/getAuthProgram',
        method: 'get',
    })
}

export const vipProgramList = (library) => {
    return request({
        url: '/api/sgs-mart/vipProgram/vipProgramList',
        method: 'post',
        data: library
    })
}


export const detail = (id) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/authoriaztion/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}