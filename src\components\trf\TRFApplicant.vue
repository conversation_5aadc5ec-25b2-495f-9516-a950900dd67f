<template>
  <div>
    <CustomerAuthDialog :dialogShow="customerDialog"
                        :createUserId="createUserId"
                        @choiceCustomer="choiceApplicant"
                        @closeDialog="invisibleComponents"/>
    <el-row class="content-item1">
      <h4 class="sgs-title">
        {{$t('trf.applicantInfo')}}
        <el-button type="text" size="medium" class="edit-applicant-btn"
                   v-if="changeApplicantStatus"
                   @click="customerDialog = true">{{ $t('operation.modify') }}</el-button>
      </h4>
    </el-row>
    <el-row :gutter="20">
      <div class="item-wrap clearfix">
        <el-col span="9">
          <!--非动态校验-->
          <el-form-item :label="$t('customer.name')" v-if="language === languageEnums.EN.name"
                        prop="trfCustomer.customerNameEn">
            <el-input maxlength="200" v-model="trfCustomer.customerNameEn"
                      @input="customerNameChange"
                      @clear="handleBlur"
                      :disabled="showCustomerNameFlag"></el-input>
          </el-form-item>
          <el-form-item :label="$t('customer.name')" v-if="language === languageEnums.CN.name"
                        prop="trfCustomer.customerNameZh">
            <el-input maxlength="200" v-model="trfCustomer.customerNameZh"
                      @input="customerNameChange"
                      @clear="handleBlur"
                      :disabled="showCustomerNameFlag"></el-input>
          </el-form-item>
        </el-col>
      </div>
      <!--非动态校验-->
      <div class="item-wrap clearfix">
        <el-col span="12">
          <el-form-item ref="trfCustomer.customerAddressEn" :label="$t('customer.address')" prop="trfCustomer.customerAddressEn">
            <el-select v-model="trfCustomer.customerAddressEn"
                       clearable
                       allow-create
                       default-first-option
                       :disabled="trfDisabled"
                       @change="customerAddressChange"
                       @clear="handleBlur"
                       :placeholder="$t('customer.address')"
                       filterable
                       style="width: 100%;">
              <el-option v-for="(address,index) in customerAddressData" :key="index"
                         :label="address.addressDetail" :value="address.addressDetail" />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
    </el-row>
    <el-row :gutter="20" class="contact">
      <!--非动态校验-->
      <div class="item-wrap clearfix">
        <el-col span="6">
          <el-form-item ref="trfCustomerContact.applyContactName" :label="$t('contact.title.default')" prop="trfCustomerContact.applyContactName">
            <el-select v-model="trfCustomerContact.applyContactName"
                       allow-create
                       default-first-option
                       :placeholder="$t('contact.title.default')"
                       @blur.capture.native="handleBlur"
                       @change="contactNameChange"
                       clearable
                       @clear="handleBlur"
                       filterable
                       :disabled="trfDisabled"
                       style="width: 100%;">
              <el-option v-for="(contact,index) in customerContactData" :key="index"
                         :label="contact.contactName"
                         :value="contact.contactName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col span="6">
          <el-form-item ref="trfCustomerContact.applyContactTel" :label="$t('contact.phone')" prop="trfCustomerContact.applyContactTel">
            <el-input maxlength="128" v-model="trfCustomerContact.applyContactTel"
                      clearable
                      @clear="handleBlur"
                      :disabled="trfDisabled"
                      autocomplete="off" />
          </el-form-item>
        </el-col>
      </div>
      <el-col span="9">
        <el-form-item ref="trfCustomerContact.applyContactEmail" :label="$t('contact.email')" prop="trfCustomerContact.applyContactEmail">
          <el-select v-model="applyEmailValue"
                     @change="applyEmailValueChange"
                     @blur.capture.native="handleBlur"
                     @focus="handleFocus"
                     multiple
                     filterable
                     allow-create
                     default-first-option
                     :disabled="trfDisabled"
                     :placeholder="$t('operation.pleaseSelect')"
                     style="width: 100%;" />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {getCustomerAddressList, getCustomerContactList} from "@/api/trf/trf";
import {queryCustomerForId, searchCustomer, validateAuth} from "@/api/customer/customer";
import {getUsers} from "@/api/customer/externalAccount";
import {objectIsNull,validateEmail} from "@/util/validate";
import {LanguageEnums} from "@/commons/enums/LanguageEnums";
import {ProxyEnums} from "@/commons/enums/ProxyEnums";
import serviceRequirement from "@/components/trf/js/serviceRequirement";
import {TrfActionTypeEnums} from "@/commons/enums/TrfActionTypeEnums";
import {mapGetters} from "vuex";

const CustomerAuthDialog = () => import("@/components/trf/dialog/CustomerAuthDialog");

export default {
  name: "TRFApplicant",
  components: { CustomerAuthDialog },
  props: {
    trfCreateUserId: {
      type: String,
      required: true,
      default: null,
      description: '创建人ID',
    },
    trfStatus: {
      type: Number,
      required: true,
      default: 0,
      description: 'Trf 状态'
    },
    showCustomerNameFlag: {
      type: Boolean,
      required: true,
      default: false,
      description: '客户名称是否可编辑'
    },
    trfCustomer: {
      type: Object,
      required: true,
      default: {},
      description: '申请人对象'
    },
    trfCustomerContact: {
      type: Object,
      required: true,
      default: {},
      description: '申请人联系方式对象'
    },
    emailGroupData: {
      type: Array,
      required: true,
      default: [],
      description: '可选邮箱组数据'
    },
    trfDisabled: {
      type: Boolean,
      required: true,
      default: false,
      description: '是否可操作'
    },
  },

  computed: {
    ...mapGetters(["userInfo", "language"]),
    changeApplicantStatus() {
      return (this.userInfo.userMgtId === this.createUserId && this.userInfo.companyId && this.trfStatus <= 1);
    }
  },

  watch: {
    'trfCustomer.customerId': function (newVal, oldVal) {
      if(objectIsNull(newVal)) return;
      this.trfCustomer.customerId = newVal;
      this.initTrfCustomer();
    },
    'trfCustomer.applyUserId': function (newVal, oldVal) {
      if (newVal == this.createUserId) return;
      if (this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code) {
        this.validateCurrentAuth().then(res => {
          if (!res) {
            this.trfCustomer.customerAddressEn = null;
            this.trfCustomerContact.applyContactName = null;
            this.trfCustomerContact.applyContactTel = null;
            this.trfCustomerContact.applyContactEmail = null;
            this.trfCustomer.customerId = this.userInfo.companyId;
            this.openTips();
          }
        });
      }
    },
    'trfCustomerContact.applyContactEmail': function (newVal, oldVal) {
      objectIsNull(this.applyEmailValue) ? (this.applyEmailValue = objectIsNull(newVal) ? [] : newVal.split(',')) : null;
    }
 },

  data() {
    return {
      actionType: null,
      languageEnums: LanguageEnums,
      createUserId: null,
      customerDialog: false,
      customerAddressData: [],
      customerContactData: [],
      applyEmailValue: [],
    }
  },

  mounted() {
    this.actionType = objectIsNull(this.$route.query.actionType) ? TrfActionTypeEnums.ADD.code : this.$route.query.actionType;
    this.createUserId = this.userInfo.userMgtId;
    if (this.actionType === TrfActionTypeEnums.ADD.code) {
      this.initTrfCustomer();
      return;
    }
    this.applyEmailValue = objectIsNull(this.trfCustomerContact.applyContactEmail) ? [] : this.trfCustomerContact.applyContactEmail.split(',');
  },

  methods: {
    async initTrfCustomer() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.applyEmailValue = [];
      this.customerAddressData = [];
      this.customerContactData = [];
      await this.searchBossNoByCustomerId(this.trfCustomer.customerId).then(async res => {
        if(res) await this.queryCustomers(res.bossNo);
      });
      await this.queryCustomerAddress(this.trfCustomer.customerId);
      await this.queryCustomerContact(this.trfCustomer.customerId);
      if(!objectIsNull(this.trfCustomerContact.applyContactEmail)) {
        this.applyEmailValue = this.trfCustomerContact.applyContactEmail.split(',');
      }
      loading.close();
    },

    handleBlur(e) {
      this.$emit('clear', e);
    },

    handleFocus(e) {
      this.$emit('focus', e);
    },

    customerNameChange(val) {
      this.$emit('input', val);
    },

    customerAddressChange(val) {
      this.trfCustomer.customerAddressEn = val;
    },

    contactNameChange(val) {
      // console.log("选中的默认联系人", val);
      this.applyEmailValue = [];
      let contact = this.customerContactData.find(item => item.contactName === val);
      if(!objectIsNull(contact)) {
        this.trfCustomerContact.applyContactName = contact.contactName;
        this.trfCustomerContact.applyContactTel = contact.contactTel;
        this.applyEmailValue.push(contact.contactEmail);
        this.applyEmailValueChange(this.applyEmailValue);
      }
    },

    applyEmailValueChange(values) {
      this.applyEmailValue = serviceRequirement.changeContacts(this.emailGroupData, values, this.applyEmailValue);
      this.trfCustomerContact.applyContactEmail = this.applyEmailValue.join(',');
    },

    //根据customerId查询客户信息
    async searchBossNoByCustomerId(customerId) {
      if(objectIsNull(customerId)) return;
      let res = await queryCustomerForId(customerId);
      if(!res.data.data || objectIsNull(res.data.data.bossNo)) return;
      return res.data.data;
    },

    //查询客户信息
    async queryCustomers(customerNo) {
      if(objectIsNull(customerNo)) return [];
      await searchCustomer({number: customerNo}).then(async res => {
        if(!res.data.data && result.data.data.length <= 0) return;
        let customer = res.data.data[0];
        this.trfCustomer.customerNameEn = customer.nameEN;
        this.trfCustomer.customerNameZh = objectIsNull(customer.nameCN) ? customer.nameEN :customer.nameCN;
        this.trfCustomer.sgsCustomerId = customer.customerId;
        this.trfCustomer.sgsAccountId = customer.accountId;
      });
    },

    //查询客户地址
    async queryCustomerAddress(customerId) {
      if(objectIsNull(customerId)) return [];
      if(this.trfCustomer.customerId != this.userInfo.companyId) return;
      getCustomerAddressList({customerId: customerId}).then(res => {
        this.customerAddressData = res.data.data ? res.data.data : [];
        if(this.actionType === TrfActionTypeEnums.ADD.code ||
            ((this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code) && objectIsNull(this.trfCustomer.customerAddressEn))) {
          let index = this.customerAddressData.findIndex(item => item.isDefault === 1);
          if (index >= 0) this.customerAddressChange(this.customerAddressData[index].addressDetail);
        }
      });
    },

    //查询客户联系人
    async queryCustomerContact(customerId) {
      if(objectIsNull(customerId)) return [];
      if(this.trfCustomer.customerId != this.userInfo.companyId) return;
      getCustomerContactList({customerId: customerId}).then(res => {
        this.customerContactData = res.data.data ? res.data.data : [];
        if(this.actionType === TrfActionTypeEnums.ADD.code ||
            ((this.actionType === TrfActionTypeEnums.COPY.code ||this.actionType === TrfActionTypeEnums.TEMPLATE.code) && objectIsNull(this.trfCustomerContact.applyContactName))) {
          let index = this.customerContactData.findIndex(item => item.isDefault === 1);
          if (index >= 0) this.contactNameChange(this.customerContactData[index].contactName);
        }
      });
    },
    invisibleComponents(val) {
      this.customerDialog = val;
    },

    async choiceApplicant(customer) {
      if(this.trfCustomer.customerId == customer.authCustomerId) return;
      this.trfCustomer.customerAddressEn = '';
      this.trfCustomerContact.applyContactName = customer.authUserName;
      let res = await this.searchBossNoByCustomerId(customer.authCustomerId);
      if(!res) return;
      getUsers({customerNo: res.bossNo, id: customer.authUserId}).then(user => {
        this.trfCustomer.applyUserId = customer.authUserId;
        this.trfCustomer.customerId = customer.authCustomerId;
        if(!user || !user.data || objectIsNull(user.data.rows)) return;
        let contact = user.data.rows[0];
        if(contact.deleteFlag && contact.enableStatus) {
          this.trfCustomerContact.applyContactTel = contact.telephone;
          this.trfCustomerContact.applyContactEmail = contact.email;
        }
        this.$emit('enterApplicant', customer);
      });
    },

    //校验当前授权是否有效
    async validateCurrentAuth() {
      // console.log("授权校验前");
      if (objectIsNull(this.trfCustomer.applyUserId)) return;
      return await validateAuth({
        authUserId:  this.trfCustomer.applyUserId,
        applyUserId: this.userInfo.userMgtId,
        authType: ProxyEnums.CREATE_TRF.code
      }).then(res => {
        // console.log("授权校验中");
        let authUser = res.data && res.data.code === 200 ? res.data.data : null;
        return authUser && authUser.deleteFlag && authUser.enableStatus ? true : false;
      });
    },

    packagingCustomerData(source) {
      if(objectIsNull(this.trfCustomer.applyUserId)) this.trfCustomer.applyUserId = this.createUserId;
      source = Object.assign(source, this.trfCustomer);
      return source;
    },

    packagingContactData(source) {
      source = Object.assign(source, this.trfCustomerContact);
      return source;
    },

    openTips() {
      this.$notify({
        title: this.$t('tip'),
        message: this.$t('authorization.authorValid'),
        type: 'warning'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
  .edit-applicant-btn {
    margin-left: 8px;
  }

  .contact {
    table {
      th {
        padding: 5px;
        text-align: center;
      }
      td {
        padding: 5px 20px;
      }
    }
  }
</style>
