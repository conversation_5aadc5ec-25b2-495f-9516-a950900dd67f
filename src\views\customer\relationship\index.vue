<template>
    <div class="smart_views_customer_relationship_index" id="smart_views_customer_relationship_index">
        <el-card>
            <div class="header_bar">
                <div class="header_bar_role">
                    <el-select
                            v-model="customerRole"
                            @change="changeRole"
                            class="select_tab">

                        <template #label="{ label, value }">
                            <div class="role_slot_div">
                                <span>
                                    <el-icon style="border: solid 1px #c8c8c8;border-radius: 50%;font-size: 16px;"><User/> </el-icon>
                                </span>
                                <span style="font-weight: bold">{{ label }}</span>
                            </div>
                        </template>

                        <el-option value="supplier" :label="$t('scm.iamSupplier')"></el-option>
                        <el-option v-if="isBuyerRole" value="buyer" :label="t('scm.iamBuyer')"></el-option>
                    </el-select>
                </div>
                <div class="header_bar_tabs">
                    <el-tabs
                            v-model="activeName"
                            type="card"
                            class="demo-tabs"
                    >
                        <el-tab-pane :label="t('scm.MySupplier')" name="supplier" v-if="customerRole=='buyer'"></el-tab-pane>
                        <el-tab-pane :label="t('scm.MyBuyer')" name="buyer" v-if="customerRole=='supplier'"></el-tab-pane>
                        <el-tab-pane :label="t('scm.MyManufacture')" name="manufactures" v-if="customerRole=='supplier'"></el-tab-pane>
                    </el-tabs>
                </div>
            </div>
            <BuyerInfo v-if="activeName=='buyer'"></BuyerInfo>
            <SupplierInfo v-if="activeName=='supplier'"></SupplierInfo>
            <ManufacturesInfo v-if="activeName=='manufactures'"></ManufacturesInfo>
        </el-card>
    </div>
</template>

<script setup>
import {useStore} from 'vuex'
import BuyerInfo from "./BuyerInfo.vue";
import SupplierInfo from "./SupplierInfo.vue";
import ManufacturesInfo from './ManufactureInfo.vue'
import {ref,reactive,onMounted,watch,computed} from 'vue'
const store = useStore()
const activeName = ref('');
const customerRole = ref('');
import {useI18n} from 'vue-i18n';
const { t } = useI18n();

const roleInfo = computed(() => store.state.user.roleInfo)

defineOptions({
    name: 'CustomerRelationship',
});
const isBuyerRole = ref(false);
watch(roleInfo, (newV)=>{
    let {isBuyer,isAgent} = newV;
    let isSupplier = true;
    let buyer = isBuyer || isAgent;
    isBuyerRole.value = buyer;
    customerRole.value = buyer ? 'buyer' : (isSupplier ? 'supplier':'');
    activeName.value = buyer ?'supplier' : (isSupplier ? 'buyer' :'' );
},{deep:true,immediate:true});


onMounted(()=>{
    //默认 i am a supplier , buyer tab页
    let {isBuyer,isAgent} = roleInfo.value;
    let isSupplier = true;
    let buyer = isBuyer || isAgent;
    isBuyerRole.value = buyer;
    customerRole.value = buyer ? 'buyer' : (isSupplier ? 'supplier':'');
    activeName.value = buyer ?'supplier' : (isSupplier ? 'buyer' :'' );
})

const changeRole = ()=>{
    if(customerRole.value=='supplier'){
        activeName.value ='buyer'
    }else{
        activeName.value ='supplier'
    }
}
</script>

<style lang="scss">
.smart_views_customer_relationship_index {
  .el-table__body-wrapper{
    .el-icon{
      color:#ff6600;
      cursor: pointer;
    }
  }
  div.action_icon {
    i.icon-disabled {
      transform: rotate(-45deg);
      &.disabled-color{
        color: #c8c8c8;
      }
    }
    .contact-div{
      display: flex;
      align-items: stretch;
      justify-content: center;
    }
    .contacts-user{
      border: solid 1px #ff6600;
      border-radius: 2px 0 2px 2px;
      border-bottom: double 3px #ff6600;
      width: 12px;
      margin-top: 4px;
      svg{
        font-size: 6px;
      }
    }
    .icon-rel-manufacture{
      border-bottom:solid 2px #ff6600;
    }
  }
  div.table_column_slot{
    display: flex;
    justify-content: space-evenly;
    .table_column_left{
      width: calc(100% - 22px);
      overflow: hidden;
    }
    .table_column_right_icon{
      width: 20px;
    }
  }

  div.header_bar{
    display: flex;
    justify-content: space-between;
    .header_bar_role{
      width: 180px;
      line-height: 47px;
    }
    .header_bar_tabs{
      width: calc(100% - 200px);
      .el-tabs--card>.el-tabs__header .el-tabs__item.is-active{
        background: #f5f5f5 !important;
      }
    }
  }

  div.role_slot_div{
    display: flex;
    align-content: space-between;
    justify-content: flex-start;
    align-items: center;
    span{
      line-height: 12px;
      padding: 5px 2px;
    }
  }

  span.el-tag{
    min-width: 90px;
  }

}
.footer_btn {
  min-width: 80px;
}
</style>