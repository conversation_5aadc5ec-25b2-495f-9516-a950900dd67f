<template>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="wrap-tab">
        <el-tab-pane name="buyer" v-if="permissionList.buyerTab" :label="$t('customer.relationship.title.tabBuyer')">
            <scm-list relationship-type="buyer" v-if="tabRefresh.buyer"></scm-list>
        </el-tab-pane>
        <el-tab-pane name="supplier" v-if="permissionList.supplierTab" :label="$t('customer.relationship.title.tabSupplier')" lazy="true">
<!--            <suppliers v-if="tabRefresh.supplier"></suppliers>-->
          <scm-list relationship-type="supplier"  v-if="tabRefresh.supplier"></scm-list>
        </el-tab-pane>
        <el-tab-pane  name="manufacturer" v-if="permissionList.manufactureTab" :label="$t('manufacturer.title.tab')"  lazy="true">
          <scm-list relationship-type="manufacturer"  v-if="tabRefresh.manufacturer"></scm-list>
        </el-tab-pane>
        <el-tab-pane  name="factory" v-if="permissionList.factoryTab" :label="$t('factory.title.tab')"  lazy="true">
            <scm-list relationship-type="factory"  v-if="tabRefresh.factory"></scm-list>
        </el-tab-pane>
        <el-tab-pane name="approve" v-if="permissionList.approveTab" :label="$t('buyerRelationship.title.tab')" lazy="true">
          <customer-buyerRelationship v-if="tabRefresh.approve"></customer-buyerRelationship>
        </el-tab-pane>
    </el-tabs>
</template>
<script>
    import {mapGetters} from "vuex";
    import {validatenull} from "@/util/validate";
    export default {
        components: {
            customerBuyerRelationship: resolve => require(['@/views/customer/buyerRelationship'], resolve),
            scmList: resolve => require(['@/views/customer/relationship/scm-list'], resolve)
        },
        data(){
            return{
                name: "customer-relationship",
                activeName: "buyer",
                tabRefresh: {
                    buyer: true,
                    supplier: false,
                    approve: false,
                    manufacturer: false,
                    factory:false,
                },
                isSupplier:null,
            }
        },
        created() {
            debugger;
            //判断当前登录用户是供应商还是buyer
            let isSupplier = true;
            if(!validatenull(this.userInfo.dimensions)&&!validatenull(this.userInfo.dimensions.UserRole)){
                let role = this.userInfo.dimensions.UserRole;
                role.forEach(item =>{
                    if (item === 'Buyer'){
                        isSupplier = false;
                    }
                });
            }
            if(!isSupplier){
                this.activeName="supplier";
            }else{
                this.activeName="buyer";
            }
            Object.keys(this.tabRefresh).forEach(item=>{
                    this.tabRefresh[item]=false;
            })
            this.tabRefresh[this.activeName]=true;
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    approveTab: this.vaildData(this.permission['sgs:customer:relationship:tab:approve'],false),
                    buyerTab: this.vaildData(this.permission['sgs:customer:tab:buyer'],false),
                    supplierTab: this.vaildData(this.permission['sgs:customer:tab:supplier'],false),
                    factoryTab: this.vaildData(this.permission['sgs:customer:tab:factory'],false),
                    manufactureTab:this.vaildData(this.permission['sgs:customer:tab:manufacture'],false),
                };
            }
        },
        methods: {
            handleClick(tab) {
                Object.keys(this.tabRefresh).forEach(item=>{
                    this.tabRefresh[item]=false;
                })
                this.tabRefresh[tab.name]=true;
            }
        }
    }
</script>

<style lang="scss" scoped>
.wrap-tab {
    ::v-deep .el-tabs__nav-wrap {
        &::after {
            position: inherit;
        }
    }
    ::v-deep .el-tabs__content {
        margin-top: -58px;
    }
}
@import '../tab.scss'
</style>
