<template>
    <div class="trf_customer_class">
        <el-collapse v-model="activeNames" class="collapse-wrap">
            <!--Application -->
            <el-form ref="customerForm" :model="customerForm">
                <el-collapse-item v-loading="applicationSaveLoading" :name="CustomerUsageEnums.APPLICATION.code">
                    <template slot="title">
                        <h4 class="sgs-title">
                            {{ $t('trf.applicantInfo') }}
                        </h4>
                    </template>
                    <el-row>
                        <el-col style="text-align: right">
                            <el-button v-if="applicationModifyDisabled && showModifyBtn('application')" type="primary"
                                       size="medium" @click="applicationModifyDisabled = false">
                                {{ $t('operation.modify') }}
                            </el-button>
                            <el-button v-if="!applicationModifyDisabled && showModifyBtn('application')" type="primary"
                                       size="medium" @click="updateModifyCustomer('application')">
                                {{ $t('operation.save') }}
                            </el-button>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <!--非动态校验-->
                                <el-form-item ref="applicationCustomer.customerNameEn"
                                              :label="$t('customer.new.companyNameEn')"
                                              prop="applicationCustomer.customerNameEn"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-select v-model="customerForm.applicationCustomer.customerNameEn"
                                               clearable
                                               default-first-option
                                               :allow-create="isSGS || isBasicUser"
                                               :disabled="disabledCustomer('application')"
                                               @change="applicationCustomerEnNameChange"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @clear="clearApplicant"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(customerData,index) in applicationCustomerNameData"
                                                   :key="index"
                                                   :label="customerData.customerNameEn"
                                                   :value="customerData.customerNameEn"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="applicationCustomer.customerNameZh"
                                              :label="$t('customer.new.companyNameCn')">
                                    <el-input v-model="customerForm.applicationCustomer.customerNameZh"
                                              :disabled="disabledCustomer('application')"
                                              @clear="clearApplicant"
                                              clearable
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('customer.new.customerNo')">
                                    <el-input :maxlength="13"
                                              v-model="customerForm.applicationCustomer.bossNo"
                                              oninput="this.value=this.value.replace(/\D/g,'')"
                                              :disabled="disabledCustomer('application') || !isSGS"
                                              @clear="handleBlur"
                                    >
                                        <template #append>
                                            <el-button icon="el-icon-search"
                                                       v-if="isSGS && !disabledCustomer('application')"
                                                       @click="sgsSearchCustomer('applicationCustomer','applicationCustomerNameData')"></el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                        <!--非动态校验-->
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="applicationCustomer.customerAddressEn"
                                              :label="$t('customer.new.companyAddressEn')"
                                              prop="applicationCustomer.customerAddressEn"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-select v-model="customerForm.applicationCustomer.customerAddressEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               @change="customerAddressChange"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @clear="handleBlur"
                                               :disabled="disabledCustomer('application')"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(address,index) in customerAddressData" :key="index"
                                                   :label="address.addressDetail" :value="address.addressDetail"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="applicationCustomer.customerAddressZh"
                                              :label="$t('customer.new.companyAddressCn')">
                                    <el-input v-model="customerForm.applicationCustomer.customerAddressZh"
                                              maxlength="500"
                                              :disabled="disabledCustomer('application')"
                                              clearable
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label=" $t('trf.applicant')+' '+$t('customer.new.customerReferenceNo')">
                                    <el-input maxlength="80"
                                              v-model="customerForm.applicationCustomer.customerReferenceNo"
                                              @input="customerReferenceNoChange"
                                              :disabled="disabledCustomer('application')"
                                              @clear="handleBlur"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>
                    <el-row :gutter="20" class="contact">
                        <!--非动态校验-->
                        <div class="item-wrap clearfix">
                            <el-col :span="6">
                                <el-form-item ref="applicationCustomer.contactName" :label="$t('contact.title.default')"
                                              prop="applicationCustomer.contactName"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-select v-model="customerForm.applicationCustomer.contactName"
                                               allow-create
                                               default-first-option
                                               :placeholder="$t('contact.title.default')"
                                               @blur.capture.native="handleBlur"
                                               @change="appContactNameChange"
                                               clearable
                                               @clear="handleBlur"
                                               :disabled="disabledCustomer('application')"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(contact,index) in customerContactData" :key="index"
                                                   :label="contact.contactName"
                                                   :value="contact.contactName"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item ref="applicationCustomer.contactTelephone" :label="$t('contact.phone')"
                                              prop="applicationCustomer.contactTelephone"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-input maxlength="128"
                                              v-model="customerForm.applicationCustomer.contactTelephone"
                                              clearable
                                              @clear="handleBlur"
                                              :disabled="disabledCustomer('application')"
                                              autocomplete="off"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item ref="applicationCustomer.contactEmail" :label="$t('contact.email')"
                                              prop="applicationCustomer.contactEmail"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-select v-model="applyEmailValue"
                                               @change="applyEmailValueChange"
                                               @blur.capture.native="handleBlur"
                                               @focus="handleFocus"
                                               multiple
                                               filterable
                                               allow-create
                                               default-first-option
                                               :disabled="disabledCustomer('application')"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               style="width: 100%;"/>
                                </el-form-item>
                            </el-col>
                        </div>
                        <el-col :span="9">

                        </el-col>
                    </el-row>
                </el-collapse-item>

                <!--Payer -->
                <el-collapse-item  style="position:relative;" :name="CustomerUsageEnums.PAYER.code">
                    <template slot="title">
                        <h4 class="sgs-title">
                            {{ $t('trf.payInfo') }}
                        </h4>
                        <div style="position: absolute;right: 150px;">
                            <el-checkbox-group v-model="identicalFlag" style="padding: 10px;"
                                               :disabled="disabledCustomer('application')">
                                <el-checkbox :label="$t('trf.sameApplicant')" name="type"></el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </template>
                    <el-row v-if="!identicalFlag">
                        <el-col style="text-align: right">
                            <el-button v-if="payerModifyDisabled && showModifyBtn('payer')" type="primary" size="medium"
                                       @click="payerModifyDisabled = false">{{ $t('operation.modify') }}
                            </el-button>
                            <el-button v-if="!payerModifyDisabled && showModifyBtn('payer')" type="primary"
                                       size="medium" @click="updateModifyCustomer('payer')">{{ $t('operation.save') }}
                            </el-button>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" v-if="!identicalFlag" style="margin-top:10px">
                        <!--非动态校验-->
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item :label="$t('customer.payNameEn')" prop="payerCustomer.customerNameEn"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-input maxlength="100" v-model="customerForm.payerCustomer.customerNameEn"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="clearPayer"
                                              autocomplete="off"
                                              clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item :label="$t('customer.payNameCn')">
                                    <el-input maxlength="100" v-model="customerForm.payerCustomer.customerNameZh"
                                              autocomplete="off"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="clearPayer"
                                              clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('customer.new.customerNo')">
                                    <el-input maxlength="13" v-model="customerForm.payerCustomer.bossNo"
                                              oninput="this.value=this.value.replace(/\D/g,'')"
                                              :disabled="disabledCustomer('payer') || !isSGS"
                                              @clear="handleBlur"
                                              autocomplete="off"
                                              clearable>
                                        <template #append>
                                            <el-button icon="el-icon-search" v-if="isSGS && !disabledCustomer('payer')"
                                                       @click="sgsSearchCustomer('payerCustomer')"></el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item :label="$t('customer.payAddressEn')"
                                              prop="payerCustomer.customerAddressEn"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-input maxlength="500" v-model="customerForm.payerCustomer.customerAddressEn"
                                              autocomplete="off"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="handleBlur"
                                              clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item :label="$t('customer.payAddressCn')">
                                    <el-input maxlength="500" v-model="customerForm.payerCustomer.customerAddressZh"
                                              autocomplete="off"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="handleBlur"
                                              clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                        :label=" $t('customer.new.payer')+ $t('customer.new.customerReferenceNo')">
                                    <el-input maxlength="80" v-model="customerForm.payerCustomer.customerReferenceNo"
                                              @input="customerReferenceNoChange"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="handleBlur"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                        <!--非动态校验-->
                        <div class="item-wrap clearfix">
                            <el-col :span="6">
                                <el-form-item :label="$t('contact.title.default')" prop="payerCustomer.contactName"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-input maxlength="50" v-model="customerForm.payerCustomer.contactName"
                                              autocomplete="off"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="handleBlur"
                                              clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('contact.phone')" prop="payerCustomer.contactTelephone"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-input maxlength="128" v-model="customerForm.payerCustomer.contactTelephone"
                                              autocomplete="off"
                                              :disabled="disabledCustomer('payer')"
                                              @clear="handleBlur"
                                              clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item :label="$t('contact.email')" prop="payerCustomer.contactEmail"
                                              :rules="[{required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'}]">
                                    <el-select v-model="payEmailValue"
                                               @change="payEmailValueChange"
                                               @blur.capture.native="handleBlur"
                                               @focus="handleFocus"
                                               multiple
                                               filterable
                                               allow-create
                                               default-first-option
                                               :disabled="disabledCustomer('payer')"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               style="width: 100%;">
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>

                </el-collapse-item>
                <!--Buyer -->
                <el-collapse-item :name="CustomerUsageEnums.BUYER.code"
                                  v-if="templateSpecificCode.indexOf('Buyer') != -1">
                    <template slot="title">
                        <h4 class="sgs-title">
                            {{ $t('buyer.name') }}
                        </h4>
                    </template>
                    <el-row :gutter="20">
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="buyerCustomer.customerNameEn"
                                              :label="$t('customer.new.companyNameEn')"
                                              prop="buyerCustomer.customerNameEn"
                                              :rules="{required:buyerRequried ,message:$t('trf.validate.requiredBlur'),trigger:'change'}">
                                    <el-select v-model="customerForm.buyerCustomer.customerNameEn"
                                               clearable
                                               :allow-create="buyerCreateFlag"
                                               default-first-option
                                               @change="buyerCustomerNameEnChange"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @clear="handleBlur"
                                               :disabled="trfDisabled || buyerCustomerDisable"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(customerData,index) in buyerCustomerNameData" :key="index"
                                                   :label="customerData.customerNameEn"
                                                   :value="customerData.customerNameEn"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="buyerCustomer.customerNameZh"
                                              :label="$t('customer.new.companyNameCn')"
                                              prop="buyerCustomer.customerNameZh"
                                              :rules="fieldRequired('Buyer','customerNameZh')">
                                    <el-input v-model="customerForm.buyerCustomer.customerNameZh"
                                              clearable
                                              max-length="250"
                                              :disabled="trfDisabled || buyerCustomerDisable || buyerNameCnDisable"
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('customer.new.customerNo')">
                                    <el-input maxlength="13" v-model="customerForm.buyerCustomer.bossNo"
                                              oninput="this.value=this.value.replace(/\D/g,'')"
                                              :disabled="buyerBossNoDisable || !isSGS || trfDisabled"
                                              @clear="handleBlur"
                                    >
                                        <template #append>
                                            <el-button icon="el-icon-search"
                                                       v-if="!(trfDisabled || buyerCustomerDisable || buyerBossNoDisable) && isSGS && !trfDisabled"
                                                       @click="sgsSearchCustomer('buyerCustomer','buyerCustomerNameData')"></el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>

                            </el-col>
                        </div>
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="buyerCustomer.customerAddressEn"
                                              :label="$t('customer.new.companyAddressEn')"
                                              prop="buyerCustomer.customerAddressEn"
                                              :rules="fieldRequired('Buyer','customerAddressEn')">
                                    <el-select v-model="customerForm.buyerCustomer.customerAddressEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               :disabled="trfDisabled"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @change="customerAddressChange"
                                               @clear="handleBlur"
                                               filterable
                                               style="width: 100%;">
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="buyerCustomer.customerAddressZh"
                                              :label="$t('customer.new.companyAddressCn')"
                                              prop="buyerCustomer.customerAddressZh"
                                              :rules="fieldRequired('Buyer','customerAddressZh')">
                                    <el-input v-model="customerForm.buyerCustomer.customerAddressZh"
                                              max-length="500"
                                              clearable
                                              :disabled="trfDisabled"
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label=" $t('customer.new.buyer')+ $t('customer.new.customerReferenceNo')"
                                              prop="buyerCustomer.customerReferenceNo"
                                              :rules="fieldRequired('Buyer','customerReferenceNo')">
                                    <el-input maxlength="80" v-model="customerForm.buyerCustomer.customerReferenceNo"
                                              @input="customerReferenceNoChange"
                                              :disabled="trfDisabled"
                                              @clear="handleBlur"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>
                    <el-row :gutter="20" class="contact">
                        <div class="item-wrap clearfix">
                            <el-col :span="6">
                                <el-form-item ref="buyerCustomer.contactName" :label="$t('contact.title.default')"
                                              prop="buyerCustomer.contactName"
                                              :rules="fieldRequired('Buyer','contactName')">
                                    <el-select v-model="customerForm.buyerCustomer.contactName"
                                               allow-create
                                               default-first-option
                                               :placeholder="$t('contact.title.default')"
                                               @blur.capture.native="handleBlur"
                                               @change="buyerContactNameChange"
                                               clearable
                                               @clear="handleBlur"
                                               filterable
                                               :disabled="trfDisabled"
                                               style="width: 100%;">
                                        <el-option v-for="(customerData,index) in buyerContactData" :key="index"
                                                   :label="customerData.contactName" :value="customerData.contactName"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item ref="buyerCustomer.contactTelephone" :label="$t('contact.phone')"
                                              prop="buyerCustomer.contactTelephone"
                                              :rules="fieldRequired('Buyer','contactTelephone')">
                                    <el-input maxlength="128" v-model="customerForm.buyerCustomer.contactTelephone"
                                              clearable
                                              @clear="handleBlur"
                                              :disabled="trfDisabled"
                                              autocomplete="off"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item ref="buyerCustomer.contactEmail" :label="$t('contact.email')"
                                              prop="buyerCustomer.contactEmail"
                                              :rules="fieldRequired('Buyer','contactEmail')">
                                    <el-select v-model="buyerEmailValue"
                                               @change="buyerEmailValueChange"
                                               @blur.capture.native="handleBlur"
                                               @focus="handleFocus"
                                               multiple
                                               filterable
                                               allow-create
                                               default-first-option
                                               :disabled="trfDisabled"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               style="width: 100%;"/>
                                </el-form-item>
                            </el-col>
                        </div>

                    </el-row>
                </el-collapse-item>
                <!-- Agent-->
                <el-collapse-item :name="CustomerUsageEnums.AGENT.code"
                                  v-if="templateSpecificCode.indexOf('Agent') != -1">
                    <template slot="title">
                        <h4 class="sgs-title">
                            {{ $t('agent.name') }}
                        </h4>
                    </template>
                    <el-row :gutter="20">
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="agentCustomer.customerNameEn"
                                              :label="$t('customer.new.companyNameEn')"
                                              prop="agentCustomer.customerNameEn"
                                              :rules="fieldRequired('Agent','customerNameEn')">
                                    <el-select v-model="customerForm.agentCustomer.customerNameEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               @change="agentCustomerNameEnChange"
                                               :disabled="trfDisabled || true"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @clear="handleBlur"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(customerData,index) in agentCustomerNameData" :key="index"
                                                   :label="customerData.customerNameEn"
                                                   :value="customerData.customerNameEn"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="agentCustomer.customerNameZh"
                                              :label="$t('customer.new.companyNameCn')"
                                              prop="agentCustomer.customerNameZh"
                                              :rules="fieldRequired('Agent','customerNameZh')">
                                    <el-input v-model="customerForm.agentCustomer.customerNameZh"
                                              max-length="250"
                                              :disabled="trfDisabled"
                                              clearable>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('customer.new.customerNo')">
                                    <el-input maxlength="13" v-model="customerForm.agentCustomer.bossNo"
                                              oninput="this.value=this.value.replace(/\D/g,'')"
                                              :disabled="trfDisabled || true"
                                              @clear="handleBlur"
                                    >
                                        <!-- <template #append>
                      <el-button icon="el-icon-search" v-if="isSGS && !trfDisabled" @click="sgsSearchCustomer('agentCustomer','agentCustomerNameData')"></el-button>
                    </template> -->
                                    </el-input>
                                </el-form-item>

                            </el-col>
                        </div>
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="agentCustomer.customerAddressEn"
                                              :label="$t('customer.new.companyAddressEn')"
                                              prop="agentCustomer.customerAddressEn"
                                              :rules="fieldRequired('Agent','customerAddressEn')">
                                    <el-select v-model="customerForm.agentCustomer.customerAddressEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               :disabled="trfDisabled"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @change="customerAddressChange"
                                               @clear="handleBlur"
                                               filterable
                                               style="width: 100%;">
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="agentCustomer.customerAddressZh"
                                              :label="$t('customer.new.companyAddressCn')"
                                              prop="agentCustomer.customerAddressZh"
                                              :rules="fieldRequired('Agent','customerAddressZh')">
                                    <el-input v-model="customerForm.agentCustomer.customerAddressZh"
                                              max-length="500"
                                              :disabled="trfDisabled"
                                              clearable>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label=" $t('customer.new.agent')+ $t('customer.new.customerReferenceNo')"
                                              prop="agentCustomer.customerReferenceNo"
                                              :rules="fieldRequired('Agent','customerReferenceNo')">
                                    <el-input maxlength="80" v-model="customerForm.agentCustomer.customerReferenceNo"
                                              @input="customerReferenceNoChange"
                                              :disabled="trfDisabled"
                                              @clear="handleBlur"
                                    ></el-input>
                                </el-form-item>
                            </el-col>

                        </div>
                    </el-row>
                    <el-row :gutter="20" class="contact">
                        <div class="item-wrap clearfix">
                            <el-col :span="6">
                                <el-form-item ref="agentCustomer.contactName" :label="$t('contact.title.default')"
                                              prop="agentCustomer.contactName"
                                              :rules="fieldRequired('Agent','contactName')">
                                    <el-select v-model="customerForm.agentCustomer.contactName"
                                               allow-create
                                               default-first-option
                                               :placeholder="$t('contact.title.default')"
                                               @blur.capture.native="handleBlur"
                                               @change="agentContactNameChange"
                                               clearable
                                               @clear="handleBlur"
                                               filterable
                                               :disabled="trfDisabled"
                                               style="width: 100%;">
                                        <el-option v-for="(customerData,index) in agentContactData" :key="index"
                                                   :label="customerData.contactName" :value="customerData.contactName"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item ref="agentCustomer.contactTelephone" :label="$t('contact.phone')"
                                              prop="agentCustomer.contactTelephone"
                                              :rules="fieldRequired('Agent','contactTelephone')">
                                    <el-input maxlength="128" v-model="customerForm.agentCustomer.contactTelephone"
                                              clearable
                                              @clear="handleBlur"
                                              :disabled="trfDisabled"
                                              autocomplete="off"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item ref="agentCustomer.contactEmail" :label="$t('contact.email')"
                                              prop="agentCustomer.contactEmail"
                                              :rules="fieldRequired('Agent','contactEmail')">
                                    <el-select v-model="agentEmailValue"
                                               @change="agentEmailValueChange"
                                               @blur.capture.native="handleBlur"
                                               @focus="handleFocus"
                                               multiple
                                               filterable
                                               allow-create
                                               default-first-option
                                               :disabled="trfDisabled"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               style="width: 100%;"/>
                                </el-form-item>
                            </el-col>
                        </div>
                        <el-col :span="9">

                        </el-col>
                    </el-row>
                </el-collapse-item>
                <!--  supplier -->
                <el-collapse-item style="position:relative;" v-loading="supplierSaveLoading" :name="CustomerUsageEnums.SUPPLIER.code"
                                  v-if="needSupplier || templateSpecificCode.indexOf('Supplier')  != -1">
                    <template slot="title">
                        <h4 class="sgs-title">
                            {{ $t('supplier.name') }}
                        </h4>
                        <div style="position: absolute;right: 150px;">
                            <el-checkbox v-model="supplierSameAsFlag" style="padding: 10px;"
                                         :disabled="disabledCustomer('supplier')"
                                         @change="changeSupplierSameFlag"
                                         :label="$t('trf.sameApplicant')" name="type"></el-checkbox>
                        </div>
                    </template>
                    <el-row v-if="!supplierSameAsFlag">
                        <el-col style="text-align: right">
                            <el-button v-if="supplierModifyDisabled && showModifyBtn('supplier')" type="primary"
                                       size="medium" @click="supplierModifyDisabled = false">
                                {{ $t('operation.modify') }}
                            </el-button>
                            <el-button v-if="!supplierModifyDisabled && showModifyBtn('supplier')" type="primary"
                                       size="medium" @click="updateModifyCustomer('supplier')">
                                {{ $t('operation.save') }}
                            </el-button>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" v-if="!supplierSameAsFlag">
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="supplierCustomer.customerNameEn"
                                              :label="$t('customer.new.companyNameEn')"
                                              prop="supplierCustomer.customerNameEn"
                                              :rules="fieldRequired('Supplier','customerNameEn')"
                                >
                                    <el-select v-model="customerForm.supplierCustomer.customerNameEn"
                                               ref="supplierCustomerSelect"
                                               clearable
                                               allow-create
                                               default-first-option
                                               @change="suppliercustomerNameChange"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               :disabled="disabledCustomer('supplier')"
                                               @clear="clearSupplier"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(customerData,index) in supplierCustomerNameData" :key="index"
                                                   :label="language === languageEnumObj.CN.name ? validatenull(customerData.scmCustomerNameCN)?customerData.scmCustomerNameEN :customerData.scmCustomerNameCN : customerData.scmCustomerNameEN"
                                                   :value="language === languageEnumObj.CN.name ? validatenull(customerData.scmCustomerNameCN)?customerData.scmCustomerNameEN :customerData.scmCustomerNameCN : customerData.scmCustomerNameEN"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="supplierCustomer.customerNameZh"
                                              :label="$t('customer.new.companyNameCn')"
                                              prop="supplierCustomer.customerNameZh"
                                              :rules="fieldRequired('Supplier','customerNameZh')">
                                    <el-input v-model="customerForm.supplierCustomer.customerNameZh"
                                              max-length="250"
                                              :disabled="disabledCustomer('supplier')"
                                              clearable
                                              @clear="clearSupplier"
                                              @keyup.native="event=>handleDelKey(event,'supplier')"
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('customer.new.customerNo')">
                                    <el-input maxlength="13" v-model="customerForm.supplierCustomer.bossNo"
                                              :disabled="disabledCustomer('supplier') || !isSGS"
                                              oninput="this.value=this.value.replace(/\D/g,'')"
                                              @clear="handleBlur"
                                    >
                                        <template #append>
                                            <el-button icon="el-icon-search"
                                                       v-if="isSGS && !disabledCustomer('supplier')"
                                                       @click="sgsSearchCustomer('supplierCustomer','supplierCustomerNameData')"></el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>

                            </el-col>
                        </div>
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="supplierCustomer.customerAddressEn"
                                              :label="$t('customer.new.companyAddressEn')"
                                              prop="supplierCustomer.customerAddressEn"
                                              :rules="fieldRequired('Supplier','customerAddressEn')">
                                    <el-select v-model="customerForm.supplierCustomer.customerAddressEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               :disabled="disabledCustomer('supplier')"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @change="customerAddressChange"
                                               @clear="handleBlur"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(address,index) in supplierCustomerAddressData" :key="index"
                                                   :label="address.addressDetail" :value="address.addressDetail"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="supplierCustomer.customerAddressZh"
                                              :label="$t('customer.new.companyAddressCn')"
                                              prop="supplierCustomer.customerAddressZh"
                                              :rules="fieldRequired('Supplier','customerAddressZh')">
                                    <el-input v-model="customerForm.supplierCustomer.customerAddressZh"
                                              max-length="500"
                                              :disabled="disabledCustomer('supplier')"
                                              clearable>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                        :label=" $t('customer.new.supplier')+ $t('customer.new.customerReferenceNo')"
                                        prop="supplierCustomer.customerReferenceNo"
                                        :rules="fieldRequired('Supplier','customerReferenceNo')">
                                    <el-input maxlength="80" v-model="customerForm.supplierCustomer.customerReferenceNo"
                                              @input="customerReferenceNoChange"
                                              :disabled="disabledCustomer('supplier')"
                                              @clear="handleBlur"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>
                    <el-row :gutter="20" class="contact" v-if="!supplierSameAsFlag">
                        <div class="item-wrap clearfix">
                            <el-col :span="6">
                                <el-form-item ref="supplierCustomer.contactName" :label="$t('contact.title.default')"
                                              prop="supplierCustomer.contactName"
                                              :rules="fieldRequired('Supplier','contactName')">
                                    <el-select v-model="customerForm.supplierCustomer.contactName"
                                               allow-create
                                               default-first-option
                                               :placeholder="$t('contact.title.default')"
                                               @blur.capture.native="handleBlur"
                                               clearable
                                               @change="changeManufactureSupplierContact('supplierCustomer',supplierContactList)"
                                               @clear="handleBlur"
                                               filterable
                                               :disabled="disabledCustomer('supplier')"
                                               style="width: 100%;">
                                        <el-option v-for="(con ,ind) in supplierContactList"
                                                   :key="'supp_con_'+ind"
                                                   :value="con.contact"
                                                   :label="con.contact"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item ref="supplierCustomer.contactTelephone" :label="$t('contact.phone')"
                                              prop="supplierCustomer.contactTelephone"
                                              :rules="fieldRequired('Supplier','contactTelephone')">
                                    <el-input maxlength="128" v-model="customerForm.supplierCustomer.contactTelephone"
                                              clearable
                                              @clear="handleBlur"
                                              :disabled="disabledCustomer('supplier')"
                                              autocomplete="off"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item ref="supplierCustomer.contactEmail" :label="$t('contact.email')"
                                              prop="supplierCustomer.contactEmail"
                                              :rules="fieldRequired('Supplier','contactEmail')">
                                    <el-select v-model="supplierEmailValue"
                                               @change="supplierEmailValueChange"
                                               @blur.capture.native="handleBlur"
                                               @focus="handleFocus"
                                               multiple
                                               filterable
                                               allow-create
                                               default-first-option
                                               :disabled="disabledCustomer('supplier')"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               style="width: 100%;"/>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>
                </el-collapse-item>
                <!-- Manufacturer-->
                <el-collapse-item :name="CustomerUsageEnums.MANUFACTURE.code"
                                  v-if="needManufacture || templateSpecificCode.indexOf('Manufacture') != -1">
                    <template slot="title">
                        <h4 class="sgs-title">
                            {{ $t('manufacturer.title.label') }}
                        </h4>
                    </template>
                    <el-row>
                        <el-col style="text-align: right">
                            <el-button v-if="manufactureModifyDisabled && showModifyBtn('manufacture')" type="primary"
                                       size="medium" @click="manufactureModifyDisabled = false">
                                {{ $t('operation.modify') }}
                            </el-button>
                            <el-button v-if="!manufactureModifyDisabled && showModifyBtn('manufacture')" type="primary"
                                       size="medium" @click="updateModifyCustomer('manufacture')">
                                {{ $t('operation.save') }}
                            </el-button>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <div class="item-wrap clearfix">
                            <el-col :span="9">
                                <el-form-item ref="manufactureCustomer.customerNameEn"
                                              :label="$t('customer.new.companyNameEn')"
                                              prop="manufactureCustomer.customerNameEn"
                                              :rules="fieldRequired('Manufacture','customerNameEn')"
                                >
                                    <el-select v-model="customerForm.manufactureCustomer.customerNameEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               ref="manufactureCustomerSelect"
                                               @change="manufactureCustomerNameChange"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @clear="clearManufacture"
                                               :disabled="disabledCustomer('manufacture')"
                                               filterable
                                               style="width: 100%;">
                                        <el-option-group
                                                v-for="(tierCustomer,groupIndex) in [
                            {label:$t('customer.new.manufacturerT2'),data:manufactureCustomerNameData.filter(m=>m.tier=='T2')},
                            {label:$t('customer.new.manufacturerT1'),data:manufactureCustomerNameData.filter(m=>m.tier=='T1')},
                            {label:'',data:manufactureCustomerNameData.filter(m=>m.tier=='T0')},
                        ]"
                                                :key="'manu_'+tierCustomer.tier+groupIndex"
                                                :label="tierCustomer.label"
                                        >
                                            <el-option v-for="(customerData,index) in tierCustomer.data" :key="index"
                                                       :label="language === languageEnumObj.CN.name ? validatenull(customerData.scmCustomerNameCN)?customerData.scmCustomerNameEN :customerData.scmCustomerNameCN : customerData.scmCustomerNameEN"
                                                       :value="language === languageEnumObj.CN.name ? validatenull(customerData.scmCustomerNameCN)?customerData.scmCustomerNameEN :customerData.scmCustomerNameCN : customerData.scmCustomerNameEN"
                                            />
                                        </el-option-group>

                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="9">
                                <el-form-item ref="manufactureCustomer.customerNameZh"
                                              :label="$t('customer.new.companyNameCn')"
                                              prop="manufactureCustomer.customerNameZh"
                                              :rules="fieldRequired('Manufacture','customerNameZh')"
                                >
                                    <el-input v-model="customerForm.manufactureCustomer.customerNameZh"
                                              max-length="250"
                                              :disabled="disabledCustomer('manufacture')"
                                              clearable
                                              @clear="clearManufacture"
                                              @keyup.native="event=>handleDelKey(event,'manufacture')"
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item :label="$t('customer.new.customerNo')">
                                    <el-input maxlength="13" v-model="customerForm.manufactureCustomer.bossNo"
                                              oninput="this.value=this.value.replace(/\D/g,'')"
                                              :disabled="disabledCustomer('manufacture') || !isSGS"
                                              @clear="handleBlur"
                                    >
                                        <template #append>
                                            <el-button icon="el-icon-search"
                                                       v-if="isSGS && !disabledCustomer('manufacture')"
                                                       @click="sgsSearchCustomer('manufactureCustomer','manufactureCustomerNameData')"></el-button>
                                        </template>
                                    </el-input>
                                </el-form-item>

                            </el-col>
                        </div>
                        <div class="item-wrap clearfix">
                            <el-col :span="8">
                                <el-form-item ref="manufactureCustomer.customerAddressEn"
                                              :label="$t('customer.new.companyAddressEn')"
                                              prop="manufactureCustomer.customerAddressEn"
                                              :rules="fieldRequired('Manufacture','customerAddressEn')">
                                    <el-select v-model="customerForm.manufactureCustomer.customerAddressEn"
                                               clearable
                                               allow-create
                                               default-first-option
                                               :disabled="disabledCustomer('manufacture')"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               @change="customerAddressChange"
                                               @clear="handleBlur"
                                               filterable
                                               style="width: 100%;">
                                        <el-option v-for="(address,index) in manufactureCustomerAddressData"
                                                   :key="index"
                                                   :label="address.addressDetail" :value="address.addressDetail"/>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item ref="manufactureCustomer.customerAddressZh"
                                              :label="$t('customer.new.companyAddressCn')"
                                              prop="manufactureCustomer.customerAddressZh"
                                              :rules="fieldRequired('Manufacture','customerAddressZh')">
                                    <el-input v-model="customerForm.manufactureCustomer.customerAddressZh"
                                              max-length="500"
                                              :disabled="disabledCustomer('manufacture')"
                                              clearable>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                        :label=" $t('customer.new.manufacturer')+ $t('customer.new.customerReferenceNo')"
                                        prop="manufactureCustomer.customerReferenceNo"
                                        :rules="fieldRequired('Manufacture','customerReferenceNo')">
                                    <el-input maxlength="80"
                                              v-model="customerForm.manufactureCustomer.customerReferenceNo"
                                              @input="customerReferenceNoChange"
                                              :disabled="disabledCustomer('manufacture')"
                                              @clear="handleBlur"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>
                    <el-row :gutter="20" class="contact">
                        <div class="item-wrap clearfix">
                            <el-col :span="6">
                                <el-form-item ref="manufactureCustomer.contactName" :label="$t('contact.title.default')"
                                              prop="manufactureCustomer.contactName"
                                              :rules="fieldRequired('Manufacture','contactName')">
                                    <el-select v-model="customerForm.manufactureCustomer.contactName"
                                               allow-create
                                               default-first-option
                                               :placeholder="$t('contact.title.default')"
                                               @blur.capture.native="handleBlur"
                                               clearable
                                               @clear="handleBlur"
                                               @change="changeManufactureSupplierContact('manufactureCustomer',manufactureContactList)"
                                               filterable
                                               :disabled="disabledCustomer('manufacture')"
                                               style="width: 100%;">
                                        <el-option v-for="(con ,ind) in manufactureContactList"
                                                   :key="'manu_con_'+ind"
                                                   :value="con.contact"
                                                   :label="con.contact"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item ref="manufactureCustomer.contactTelephone" :label="$t('contact.phone')"
                                              prop="manufactureCustomer.contactTelephone"
                                              :rules="fieldRequired('Manufacture','contactTelephone')">
                                    <el-input maxlength="128"
                                              v-model="customerForm.manufactureCustomer.contactTelephone"
                                              clearable
                                              @clear="handleBlur"
                                              :disabled="disabledCustomer('manufacture')"
                                              autocomplete="off"/>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item ref="manufactureCustomer.contactEmail" :label="$t('contact.email')"
                                              prop="manufactureCustomer.contactEmail"
                                              :rules="fieldRequired('Manufacture','contactEmail')">
                                    <el-select v-model="manufactureEmailValue"
                                               @change="manufactureEmailValueChange"
                                               @blur.capture.native="handleBlur"
                                               @focus="handleFocus"
                                               multiple
                                               filterable
                                               allow-create
                                               default-first-option
                                               :disabled="disabledCustomer('manufacture')"
                                               :placeholder="$t('operation.pleaseSelect')"
                                               style="width: 100%;"/>
                                </el-form-item>
                            </el-col>
                        </div>
                    </el-row>
                </el-collapse-item>
                <el-dialog :title="$t('trf.selBuyerCustomerGroup')" :visible.sync="showSelGroupDialog">
                    <el-form-item>
                        <div>
                            <el-radio v-for="(item, index) in selBuyerGroupDatas" :key="index"
                                      v-model="selBuyerGroupCode" :label="item.customerGroupCode">
                                {{ item.customerGroupName }}
                            </el-radio>
                        </div>
                    </el-form-item>
                    <div slot="footer" class="dialog-footer">
                        <el-button size="small" @click="showSelGroupDialog = false">{{ $t('operation.cancel') }}
                        </el-button>
                        <el-button size="small" type="primary" :disabled="trfDisabled"
                                   @click="updateBuyerCustomerGroup()">{{ $t('operation.confirm') }}
                        </el-button>
                    </div>
                </el-dialog>
            </el-form>

        </el-collapse>
    </div>
</template>

<script>
import {LanguageEnums} from "@/commons/enums/LanguageEnums";
import {
    getCustomerAddressList,
    getCustomerContactList,
    queryScmCustomerList,
    updateTrfCustomer,
    queryScmList,
    isNewScm
} from "@/api/trf/trf";
import {
    queryCustomerForId, searchCustomer,
    validateAuth, searchCustomerAndGroup,
    searchCustomerContact, queryCustomerByBuCodeAndCustomerId
} from "@/api/customer/customer";
import {CustomerUsageEnums} from "@/commons/enums/CustomerUsageEnums";
import {validatenull, objectIsNull, validateEmail} from "@/util/validate";
import {ProxyEnums} from "@/commons/enums/ProxyEnums";
import serviceRequirement from "@/components/trf/js/serviceRequirement";
import {TrfActionTypeEnums} from "@/commons/enums/TrfActionTypeEnums";
import {proxyCustomersNoPage} from "@/api/customer/customer";
import {getCustomerNotPageList} from "@/api/customer/customerRegister";
import {getUsers} from "@/api/customer/externalAccount";
import {getCustomerAndGroup} from "@/api/common/index";
import {getTemplateById} from "@/api/template/template";
import {
    getCustomerGroupByParms,
    getSgsCustomer
} from "@/api/common/index";
import {mapGetters} from "vuex";


const CUSTOMER_AUTH_TYPE = "CREATE_TRF";     //授权业务类型
const CUSTOMER_AUTH_STATUS = 1;             //授权状态
export default {
    name: "TRFApplicant",
    components: {},
    props: {
        supplierSameAsApplicant:Boolean,
        languageNumber: Number,
        trfCreateUserId: {
            type: String,
            required: false,
            default: null,
            description: '创建人ID',
        },
        customerUsage: {
            type: Number,
            required: false,
            default: null,
            description: '客户类型',
        },
        trfStatus: {
            type: Number,
            required: true,
            default: 0,
            description: 'Trf 状态'
        },
        showCustomerNameFlag: {
            type: Boolean,
            required: false,
            default: false,
            description: '客户名称是否可编辑'
        },
        trf: {
            type: Object,
            required: false,
            default: {},
            description: '申请单'
        },
        templateDataObj: {
            type: Object,
            required: true,
            default: {},
            description: '模板对象'
        },
        trfCustomer: {
            type: Object,
            required: true,
            default: {},
            description: '申请人对象'
        },
        trfCustomerContact: {
            type: Object,
            required: false,
            default: () => {
                return {}
            },
            description: '申请人联系方式对象'
        },
        emailGroupData: {
            type: Array,
            required: false,
            default: [],
            description: '可选邮箱组数据'
        },
        trfCustomers: {
            type: Array,
            required: false,
            default: [],
            description: '客户集合'
        },
        trfDisabled: {
            type: Boolean,
            required: false,
            default: false,
            description: '是否可操作'
        },
        productLineCode: {
            type: String,
            required: false,
            default: null,
            description: '产品线BU',
        },
        templateSpecificCode: {
            type: String,
            required: false,
            default: null,
            description: '模版对应标签',
        }
    },

    computed: {
        ...mapGetters(["userInfo", "language", "permission"]),
        permissionList() {
            return {
                supplierModifyBtn: this.vaildData(this.permission['sgs:trf:customer:supplier:modify:btn'], false),
                manufactureModifyBtn: this.vaildData(this.permission['sgs:trf:customer:manufacture:modify:btn'], false),
                applicationModifyBtn: this.vaildData(this.permission['sgs:trf:customer:application:modify:btn'], false),
                payerModifyBtn: this.vaildData(this.permission['sgs:trf:customer:payer:modify:btn'], false),
            }
        },
        changeApplicantStatus() {
            return (this.userInfo.userMgtId === this.createUserId && this.userInfo.companyId && this.trfStatus <= 1);
        },
        payEmailValue: {
            get() {
                if (this.customerForm.payerCustomer.contactEmail) {
                    return this.$lodash.split(this.customerForm.payerCustomer.contactEmail, ',')
                }
                return []
            },
            set(val) {
                this.$set(this.customerForm.payerCustomer, 'contactEmail', this.$lodash.join(val, ','))
            }
        }
    },

    watch: {
        'customerForm.applicationCustomer':{
          immediate:false,
          deep:true,
          handler(newV){
              if(this.trfStatus-0>2 || !this.supplierSameAsFlag){
                  return;
              }
              this.changeSupplierSameFlag();
          }
        },
        needManufacture: function (newV) {
            //this.customerRules["manufactureCustomer.customerNameEn"][0].required = newV;
            if (newV) {
                this.activeNames.push(CustomerUsageEnums.MANUFACTURE.code)
            } else {
                let index = this.activeNames.findIndex(c => c == CustomerUsageEnums.MANUFACTURE.code)
                this.activeNames.splice(index, 1)
            }
        },
        templateSpecificCode:function (newV){
            if(this.templateSpecificCode.indexOf('Supplier') != -1){
                this.changeSupplierSameFlag();
            }else {
                this.handleDeleteSupplier();
            }
        },
        needSupplier: function (newV) {
            console.log("trf customer needSupplier", newV)
            //this.customerRules["supplierCustomer.customerNameEn"][0].required = newV;
            if (newV) {
                this.activeNames.push(CustomerUsageEnums.SUPPLIER.code)
                this.changeSupplierSameFlag();
            } else {
                let index = this.activeNames.findIndex(c => c == CustomerUsageEnums.SUPPLIER.code)
                this.activeNames.splice(index, 1);
                if(this.templateSpecificCode.indexOf('Supplier') == -1){
                    this.handleDeleteSupplier();
                }

            }
        },
        "customerForm.buyerCustomer.bossNo": function (newV) {
            console.log("触发了bossNo的变动", newV);
            //this.queryCustomerExtByBossNo(newV);
        },
        identicalFlag: function (newVal) {
            this.trfCustomer.isSame = 0;
            //this.$set(this.trf.trfCustomerContact, 'isSame', 0);
            if (newVal) {
                this.trfCustomer.isSame = 1;
                //this.$set(this.trf.trfCustomerContact, 'isSame', 1);
            }
        },
        'trfCustomer.applyUserId': function (newVal, oldVal) {
            if (newVal == this.createUserId) return;
            if (this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code) {
                this.validateCurrentAuth().then(res => {
                    if (!res) {
                        this.trfCustomer.customerAddressEn = null;
                        this.trfCustomerContact.applyContactName = null;
                        this.trfCustomerContact.applyContactTel = null;
                        this.trfCustomerContact.applyContactEmail = null;
                        this.trfCustomer.customerId = this.userInfo.companyId;
                        this.openTips();
                    }
                });
            }
        },
        'trfCustomerContact.applyContactEmail': function (newVal, oldVal) {
            objectIsNull(this.applyEmailValue) ? (this.applyEmailValue = objectIsNull(newVal) ? [] : newVal.split(',')) : null;
        },
        templateDataObj: {
            immediate: true,
            deep: true,
            handler(newV, oldV) {
                console.log("templateDataObj 变化被监听到", newV, oldV);
                this.initTemplateCustomerFieldConfig();
            }
        },
        "$i18n.locale": function () {
            //this.$refs['trf'].clearValidate();
            if (this.$refs['customerForm'] != undefined) {
                this.$refs['customerForm'].fields.forEach(item => {
                    if (item.validateState === 'error') {
                        this.$refs['customerForm'].validateField(item.labelFor)
                    }
                })
            }
        },
    },

    data() {
        return {
            selBuyerGroupCode: '',
            showSelGroupDialog: false,//控制是否展示买家组选择
            selBuyerGroupDatas: [],//选择多个买家组集合
            applicationModifyDisabled: true,
            applicationSaveLoading: false,
            payerModifyDisabled: true,
            payerSaveLoading: false,
            supplierModifyDisabled: true,
            supplierSaveLoading: false,
            manufactureModifyDisabled: true,
            manufactureSaveLoading: false,
            needSupplier: false,
            needManufacture: false,
            buyerNameCnDisable: false,
            buyerBossNoDisable: false,
            buyerCustomerDisable: false,
            buyerCreateFlag: true,//是否允许新建Buyer
            isSGS: false,
            isBasicUser: false,
            buyerCustomerCnDisable: false,
            defaultAddress: '',
            newInterface: false,
            reloadScmList:false,
            manufactureContactList: [],
            supplierContactList: [],
            customerForm: {
                applicationCustomer: {},
                payerCustomer: {},
                buyerCustomer: {},
                agentCustomer: {},
                supplierCustomer: {
                    customerNameEn: ''
                },
                manufactureCustomer: {
                    customerNameEn: ''
                },
            },
            identicalFlag: true,//默认与申请人一样
            supplierSameAsFlag: true,//默认与申请人一样
            applicationCustomerNameData: [],
            buyerCustomerNameData: [],
            agentCustomerNameData: [],
            supplierCustomerNameData: [],
            manufactureCustomerNameData: [],
            newScmBossNo: [],//当前选中的buyer，接口获取bossNo
            CustomerUsageEnums: CustomerUsageEnums,
            activeNames: [CustomerUsageEnums.APPLICATION.code, CustomerUsageEnums.PAYER.code],
            actionType: null,
            languageEnumObj: {},
            createUserId: null,
            customerDialog: false,
            customerAddressData: [],
            supplierCustomerAddressData: [],
            manufactureCustomerAddressData: [],
            customerContactData: [],
            buyerContactData: [],
            agentContactData: [],
            applyEmailValue: [],
            buyerEmailValue: [],
            supplierEmailValue: [],
            agentEmailValue: [],
            manufactureEmailValue: [],
            scmCustomerReqParam: {
                page: 1,
                rows: 9999,
                list: []
            },
            validateCustomerProperties: [
                'customerNameEn',
                'customerNameZh',
                'bossNo',
                'customerGroupCode',
                'customerGroupName',
                'customerAddressEn',
                'customerAddressZh',
                'customerReferenceNo',
                'contactName',
                'contactTelephone',
                'contactEmail'
            ],
            buyerRequried: false,//Buyer是否必填
            customerRules: {
                'applicationCustomer.customerNameEn': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'applicationCustomer.customerAddressEn': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'applicationCustomer.contactName': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'applicationCustomer.contactTelephone': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'applicationCustomer.contactEmail': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],

                'payerCustomer.customerNameEn': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'payerCustomer.customerAddressEn': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'payerCustomer.contactName': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'payerCustomer.contactTelephone': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                'payerCustomer.contactEmail': [
                    {required: true, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
                ],
                /*'manufactureCustomer.customerNameEn':[
            {required: false, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
          ],
          'supplierCustomer.customerNameEn':[
            {required: false, message: this.$t('trf.validate.requiredBlur'), trigger: 'change'},
          ],*/
            },
            customerRequireFieldMap: {},
        }
    },
    created() {
        this.languageEnumObj = LanguageEnums;
        this.reloadScmList = false;
        this.actionType = objectIsNull(this.$route.query.actionType) ? TrfActionTypeEnums.ADD.code : this.$route.query.actionType;
        if(this.trf.id || this.actionType == TrfActionTypeEnums.TEMPLATE.code){
            this.supplierSameAsFlag = this.supplierSameAsApplicant;
        }
    },
    mounted() {
        this.isSGS = this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser");
        this.isBasicUser = objectIsNull(this.userInfo.bossNo) ? true : false;
        this.splitCustomerInfo();
        if (this.trfCustomer.isSame == 1 || this.trfCustomer.isSame == '1') {
            this.identicalFlag = true;
        } else {
            this.identicalFlag = false;
        }
        this.createUserId = this.userInfo.userMgtId;
        if ([TrfActionTypeEnums.ADD.code, TrfActionTypeEnums.TEMPLATE.code, 'material', 'individualSample', 'groupSample'].includes(this.actionType)) {
            this.initTrfCustomer();
            return;
        } else if (this.trfStatus === 1) {//草稿
            //重新加载客户信息
            this.initTrfCustomer(1);
        }
    },

    methods: {
        updateBuyerCustomerGroup() {
            //将选择的GroupCode赋值给TRf
            if (!validatenull(this.selBuyerGroupCode)) {
                let buyerGroupObj = null;
                if (!objectIsNull(this.buyerCustomerNameData)) {
                    buyerGroupObj = this.selBuyerGroupDatas.find((item) => {
                        return item.customerGroupCode === this.selBuyerGroupCode;
                    });
                }
                if (!validatenull(buyerGroupObj)) {
                    this.trfCustomer.buyerCustomerGroupCode = buyerGroupObj.customerGroupCode;
                    this.trfCustomer.buyerCustomerGroupName = buyerGroupObj.customerGroupName;
                    this.trfCustomer.buyerCustomerGroupName = buyerGroupObj.customerGroupId;
                    //将Buyer信息回传至TRF主页
                    this.$emit('updateBuyerGroupInfo', buyerGroupObj);
                }
            }
            this.showSelGroupDialog = false;
        },
        //SGS 根据BossNo查询
        async sgsSearchCustomer(customerUsageObj, nameDataListObj) {
            if (validatenull(this.customerForm[customerUsageObj].bossNo) || (this.customerForm[customerUsageObj].bossNo + '').trim().length < 4) {
                this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('documentLibrary.validate.boosLengthErr'),
                    type: 'warning'
                });
                return;
            }
            var params = {};
            params.bossNo = this.customerForm[customerUsageObj].bossNo;
            let res = await getCustomerNotPageList(params);
            let data = res.data.data;
            if (validatenull(data)) {
                //data没有数据 再查询CS
                let reqBoss = {
                    number: params.bossNo
                }
                let bossResData = await getSgsCustomer(reqBoss);
                if (bossResData.status == 200 && bossResData.data && bossResData.data.rows && bossResData.data.rows.length > 0) {
                    let {number, nameEN, nameCN} = bossResData.data.rows[0];
                    //组装csmdata list
                    let scmObj = {
                        scmCustomerName: nameEN,
                        scmCustomerNo: number,
                        scmCustomerReferenceCode: '',
                        customerNameEn: nameEN,
                        customerNameZh: nameCN
                    }

                    let newArr = [];
                    newArr.push(scmObj)
                    //如果给了对应的customer的名称下拉属性，就赋值，否则就给默认data
                    this.$set(this.customerForm[customerUsageObj], 'customerNameZh', nameCN);
                    data = newArr;
                }
                //cs 也没有获取到数据，正常提示
                if (validatenull(data)) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('documentLibrary.validate.noCustomerTip'),
                        type: 'warning'
                    });
                    return;
                }
            } else {
                data.forEach(da => {
                    let {customerNameEn, bossNo} = da;
                    this.$set(da, 'scmCustomerName', customerNameEn);
                    this.$set(da, 'scmCustomerNo', bossNo);
                })
            }
            if (nameDataListObj) {
                this.$set(this, nameDataListObj, data);
            }
            var customerObj = data[0];
            if (!validatenull(customerObj)) {
                this.$set(this.customerForm[customerUsageObj], 'customerNameEn', objectIsNull(customerObj.customerNameEn) ? customerObj.customerNameZh : customerObj.customerNameEn);
                this.$set(this.customerForm[customerUsageObj], 'customerNameZh', objectIsNull(customerObj.customerNameZh) ? customerObj.customerNameEn : customerObj.customerNameZh);
                //为SGSmart trfCustomer对象赋值
                if (customerUsageObj == 'applicationCustomer') {
                    this.trfCustomer.customerNameEn = objectIsNull(customerObj.customerNameEn) ? customerObj.customerNameZh : customerObj.customerNameEn;
                    this.trfCustomer.customerNameZh = objectIsNull(customerObj.customerNameZh) ? customerObj.customerNameEn : customerObj.customerNameZh;
                    this.trfCustomer.customerId = customerObj.id;
                }
            }
            //SMART-3981/SMART-3838 买家查询Group信息
            if (customerUsageObj == 'buyerCustomer') {
                //根据BossNo查询group信息
                let customerParams = {};
                customerParams.number = this.customerForm[customerUsageObj].bossNo;
                customerParams.pageSize = 10;
                customerParams.rows = 10;
                let groupDatas = await getCustomerAndGroup(customerParams).then(res => {
                    return res.data.data;
                });
                if (!validatenull(groupDatas)) {
                    if (groupDatas.length > 1) {//存在多个则让用户自行选择
                        //加载选择Group弹框
                        this.selBuyerGroupDatas = groupDatas;
                        this.showSelGroupDialog = true;
                    } else {//只有一个Group的场景
                        this.trfCustomer.buyerCustomerGroupCode = groupDatas[0].customerGroupCode;
                        this.trfCustomer.buyerCustomerGroupName = groupDatas[0].customerGroupName;
                        this.trfCustomer.buyerCustomerGroupName = groupDatas[0].customerGroupId;
                        //将Buyer信息回传至TRF主页
                        this.$emit('updateBuyerGroupInfo', groupDatas[0]);
                    }
                }
            }
        },
        suppliercustomerNameChange(val) {
            if (!objectIsNull(val)) {
                let obj = {};
                obj = this.supplierCustomerNameData.find((item) => {
                    return item.scmCustomerName === val || item.scmCustomerNameEN === val || item.scmCustomerNameCN === val;
                });
                if (!objectIsNull(obj)) {
                    let {customerAddressZh, customerAddressEn, scmCustomerNameCN, contactsList} = obj;
                    this.$set(this.customerForm.supplierCustomer, 'customerReferenceNo', obj.scmCustomerReferenceCode);
                    this.$set(this.customerForm.supplierCustomer, 'bossNo', obj.scmCustomerNo);
                    this.$set(this.customerForm.supplierCustomer, 'customerNameZh', scmCustomerNameCN);
                    this.$set(this.customerForm.supplierCustomer, 'customerAddressEn', customerAddressEn);
                    this.$set(this.customerForm.supplierCustomer, 'customerAddressZh', customerAddressZh);
                    this.supplierContactList = contactsList;
                    if (contactsList && contactsList.length > 0) {
                        let cont = contactsList[0];
                        let {contact, contactEmail, contactTelephone} = cont;
                        this.$set(this.customerForm.supplierCustomer, 'contactName', contact);
                        this.$set(this.customerForm.supplierCustomer, 'contactTelephone', contactTelephone);
                        if (contactEmail) {
                            this.$set(this.customerForm.supplierCustomer, 'contactEmail', contactEmail);
                            this.supplierEmailValue = [contactEmail];
                            this.supplierEmailValueChange([contactEmail])
                        }
                    } else {
                        this.supplierContactList = []
                        this.$set(this.customerForm.supplierCustomer, 'contactName', '');
                        this.$set(this.customerForm.supplierCustomer, 'contactTelephone', '');
                        this.$set(this.customerForm.supplierCustomer, 'contactEmail', '');
                        this.supplierEmailValue = [];
                        this.supplierEmailValueChange([]);
                    }
                }
            } else {
                this.clearSupplier();
            }
            this.queryManufacture();

        },
        queryManufacture(){
            //触发manufacture的查询 if supplier==null || supplier==currentGroupCode or bossNo
            let val = this.customerForm.supplierCustomer.customerNameEn;
            this.manufactureCustomerNameData = [];
            this.handleDeleteManufacture();
            if (objectIsNull(val) || [this.userInfo.bossNo].includes(this.customerForm.supplierCustomer.bossNo)) {
                this.initSCMManufacturer(this.customerForm.applicationCustomer.customerNameEn);
            }else{
                //使用默认cus
                this.manufactureCustomerNameData = this.defaultApplicantCustomer();
            }
        },
        manufactureCustomerNameChange(val) {
            if (!objectIsNull(val)) {
                let obj = {};
                obj = this.manufactureCustomerNameData.find((item) => {
                    return item.scmCustomerName === val || item.scmCustomerNameEN === val || item.scmCustomerNameCN === val;
                });
                if (!objectIsNull(obj)) {
                    let {customerAddressZh, customerAddressEn, scmCustomerNameCN, contactsList} = obj;
                    this.$set(this.customerForm.manufactureCustomer, 'customerReferenceNo', obj.scmCustomerReferenceCode);
                    this.$set(this.customerForm.manufactureCustomer, 'bossNo', obj.scmCustomerNo);
                    this.$set(this.customerForm.manufactureCustomer, 'customerNameZh', scmCustomerNameCN);
                    this.$set(this.customerForm.manufactureCustomer, 'customerAddressEn', customerAddressEn);
                    this.$set(this.customerForm.manufactureCustomer, 'customerAddressZh', customerAddressZh);
                    this.manufactureContactList = contactsList;
                    if (contactsList && contactsList.length > 0) {
                        let cont = contactsList[0];
                        let {contact, contactEmail, contactTelephone} = cont;
                        this.$set(this.customerForm.manufactureCustomer, 'contactName', contact);
                        this.$set(this.customerForm.manufactureCustomer, 'contactTelephone', contactTelephone);
                        if (contactEmail) {
                            this.$set(this.customerForm.manufactureCustomer, 'contactEmail', contactEmail);
                            this.manufactureEmailValue = [contactEmail];
                            this.manufactureEmailValueChange([contactEmail])
                        }
                    } else {
                        this.manufactureContactList = []
                        this.$set(this.customerForm.manufactureCustomer, 'contactName', '');
                        this.$set(this.customerForm.manufactureCustomer, 'contactTelephone', '');
                        this.$set(this.customerForm.manufactureCustomer, 'contactEmail', '');
                        this.manufactureEmailValue = [];
                        this.manufactureEmailValueChange([]);
                    }

                }
            } else {
                this.clearManufacture();

            }
        },
        clearSupplierManaufacture() {
            this.$set(this.customerForm.supplierCustomer, "customerId", null);
            this.$set(this.customerForm.supplierCustomer, "customerNameEn", null);
            this.$set(this.customerForm.supplierCustomer, "customerNameZh", null);
            this.$set(this.customerForm.supplierCustomer, "bossNo", null);
            this.$set(this.customerForm.supplierCustomer, "customerAddressEn", null);
            this.$set(this.customerForm.supplierCustomer, "customerAddressZh", null);
            this.$set(this.customerForm.supplierCustomer, "customerReferenceNo", null);
            this.$set(this.customerForm.supplierCustomer, "contactName", null);
            this.$set(this.customerForm.supplierCustomer, "contactTelephone", null);
            this.$set(this.customerForm.supplierCustomer, "contactEmail", null);

            this.$set(this.customerForm.manufactureCustomer, "customerId", null);
            this.$set(this.customerForm.manufactureCustomer, "customerNameEn", null);
            this.$set(this.customerForm.manufactureCustomer, "customerNameZh", null);
            this.$set(this.customerForm.manufactureCustomer, "bossNo", null);
            this.$set(this.customerForm.manufactureCustomer, "customerAddressEn", null);
            this.$set(this.customerForm.manufactureCustomer, "customerAddressZh", null);
            this.$set(this.customerForm.manufactureCustomer, "customerReferenceNo", null);
            this.$set(this.customerForm.manufactureCustomer, "contactName", null);
            this.$set(this.customerForm.manufactureCustomer, "contactTelephone", null);
            this.$set(this.customerForm.manufactureCustomer, "contactEmail", null);
        },
        async buyerCustomerNameEnChange(val) {//选择买家名称
            this.$set(this.customerForm.buyerCustomer, 'customerNameZh', null);
            this.$set(this.customerForm.buyerCustomer, 'customerAddressEn', null);
            this.$set(this.customerForm.buyerCustomer, 'customerAddressZh', null);
            this.$set(this.customerForm.buyerCustomer, 'customerId', null);
            this.$set(this.customerForm.buyerCustomer, 'bossNo', null);
            this.$set(this.customerForm.buyerCustomer, 'contactName', null);
            // this.$set(this.customerForm.buyerCustomer, 'customerGroupCode',null);
            // this.$set(this.customerForm.buyerCustomer, 'customerGroupId', null);
            // this.$set(this.customerForm.buyerCustomer, 'customerGroupName', null);
            this.buyerContactNameChange();
            //赋值买家信息
            let obj = null;
            if (!objectIsNull(this.buyerCustomerNameData)) {
                obj = this.buyerCustomerNameData.find((item) => {
                    return item.customerNameEn === val;
                });
            }
            if (!objectIsNull(obj)) {
                // this.buyerNameCnDisable = true
                // this.buyerBossNoDisable = true
                this.$set(this.customerForm.buyerCustomer, 'customerGroupCode', obj.customerGroupCode);
                this.$set(this.customerForm.buyerCustomer, 'customerGroupId', obj.customerGroupId);
                this.$set(this.customerForm.buyerCustomer, 'customerGroupName', obj.customerGroupName);

                this.$set(this.customerForm.buyerCustomer, 'customerNameZh', obj.customerNameCn);
                this.$set(this.customerForm.buyerCustomer, 'customerAddressEn', obj.addressEn);
                this.$set(this.customerForm.buyerCustomer, 'customerAddressZh', obj.addressCn);
                this.$set(this.customerForm.buyerCustomer, 'customerId', obj.customerId);
                this.$set(this.customerForm.buyerCustomer, 'bossNo', obj.number);
            }
            //由于每次切换都要获取supplier和manaufacture是否显示，所以每次切换进行数据清空
            this.clearSupplierManaufacture();

            //查询联系人
            let searchContactParam = {};
            searchContactParam.number = this.customerForm.buyerCustomer.bossNo;
            let contacts = await this.searchContact(searchContactParam);
            this.buyerContactData = contacts;
        },
        async agentCustomerNameEnChange(val) {//选择买家名称
            this.$set(this.customerForm.agentCustomer, 'customerGroupCode', null);
            this.$set(this.customerForm.agentCustomer, 'customerGroupId', null);
            this.$set(this.customerForm.agentCustomer, 'customerGroupName', null);
            //赋值买家信息
            let obj = {};
            obj = this.agentCustomerNameData.find((item) => {
                return item.customerNameEn === val;
            });
            if (!objectIsNull(obj)) {
                this.$set(this.customerForm.agentCustomer, 'customerGroupCode', obj.customerGroupCode);
                this.$set(this.customerForm.agentCustomer, 'customerGroupId', obj.customerGroupId);
                this.$set(this.customerForm.agentCustomer, 'customerGroupName', obj.customerGroupName);

                this.$set(this.customerForm.agentCustomer, 'customerNameZh', obj.customerNameCn);
                this.$set(this.customerForm.agentCustomer, 'customerAddressEn', obj.addressEn);
                this.$set(this.customerForm.agentCustomer, 'customerAddressZh', obj.addressCn);
                this.$set(this.customerForm.agentCustomer, 'customerId', obj.customerId);
                this.$set(this.customerForm.agentCustomer, 'bossNo', obj.number);
            }
            //查询联系人
            let searchContactParam = {};
            searchContactParam.number = this.customerForm.agentCustomer.bossNo;
            let contacts = await this.searchContact(searchContactParam);
            this.agentContactData = contacts;
        },


        async searchContact(searchContactParam) {
            searchContactParam.code = 'SHIP_TO';
            searchContactParam.contactRole = 'SHIP_TO';
            let data = await searchCustomerContact(searchContactParam).then(res => {
                return res.data.data;
            });
            return data;
        },
        async searchTemplate() {
            let data = await getTemplateById(this.trf.trfTemplateId).then(res => {
                return res.data.data;
            });
            return data;
        },
        //查询Agent数据
        async searchAgentCustomerInfo(selAgentCustomerObj, agentCustomerGroupCode) {
            this.$set(this.customerForm.agentCustomer, 'customerGroupCode', agentCustomerGroupCode);
            this.$set(this.customerForm.agentCustomer, 'customerGroupId', this.trfCustomer.agentCustomerGroupId);
            this.$set(this.customerForm.agentCustomer, 'customerGroupName', this.trfCustomer.agentCustomerGroupName);
            this.agentCustomerNameData = [];
            if (!objectIsNull(agentCustomerGroupCode)) {
                let searchBuyerParam = {};
                searchBuyerParam.displayType = 'account';
                searchBuyerParam.customerGroupCode = agentCustomerGroupCode;
                searchBuyerParam.page = 1;
                searchBuyerParam.rows = 2000;
                let data = await searchCustomerAndGroup(searchBuyerParam).then(res => {
                    return res.data.data;
                });
                this.agentCustomerNameData = data;
            }
            //将手动录入的agent赋值给agent Customer
            this.$set(this.customerForm.agentCustomer, 'customerNameEn', this.trfCustomer.agentCustomerNameEn);
            //存在agent BossNo的话 将bossNo赋值给agent
            if (!objectIsNull(selAgentCustomerObj)) {
                this.$set(this.customerForm.agentCustomer, 'bossNo', selAgentCustomerObj.customerNumber);
            }
        },
        //查询买家数据 flag  0-新建   1-回显
        async searchBuyerCustomerInfo(buyerCustomerNo, flag) {
            this.buyerRequried = false;
            this.buyerCustomerDisable = false;
            this.buyerNameCnDisable = false;
            this.buyerBossNoDisable = false;
            //清空原有数据和数据源
            if (flag === 0) {
                this.buyerCustomerNameData = [];
                this.$set(this.customerForm.buyerCustomer, 'customerNameEn', null);
                this.$set(this.customerForm.buyerCustomer, 'customerNameZh', null);
                this.$set(this.customerForm.buyerCustomer, 'bossNo', null);
            }
            let searchBuyerParam = {};
            searchBuyerParam.displayType = 'account';
            if (objectIsNull(buyerCustomerNo) && this.trfCustomer.buyerCustomerGroupCode != 'General') {//不存在buyer Customer No，按照Group加载buyer的客户可选数据
                searchBuyerParam.customerGroupCode = this.trfCustomer.buyerCustomerGroupCode;
                if (this.trfCustomer.buyerCustomerGroupCode === 'general' || this.trfCustomer.buyerCustomerGroupCode === 'General') {
                    this.$set(this.customerForm.buyerCustomer, 'customerGroupCode', this.trfCustomer.buyerCustomerGroupCode);
                    this.$set(this.customerForm.buyerCustomer, 'customerGroupName', this.trfCustomer.buyerCustomerGroupName);
                    this.buyerCreateFlag = true;
                } else {
                    this.buyerCreateFlag = false;
                    //初始话Buyer组数据给到BuyerCustomer
                    this.$set(this.customerForm.buyerCustomer, 'customerGroupCode', this.trfCustomer.buyerCustomerGroupCode);
                    this.$set(this.customerForm.buyerCustomer, 'customerGroupId', this.trfCustomer.buyerCustomerGroupId);
                    //this.$set(this.customerForm.buyerCustomer, 'customerGroupName', this.trfCustomer.buyerCustomerGroupName);
                }
            } else if (this.trfCustomer.buyerCustomerGroupCode == 'General' || this.trfCustomer.buyerCustomerGroupCode == 'general') {
                this.buyerCreateFlag = true;
                this.buyerRequried = false;
            } else {
                this.buyerCreateFlag = false;
                searchBuyerParam.number = buyerCustomerNo;
            }
            if (objectIsNull(searchBuyerParam.customerGroupCode) && objectIsNull(searchBuyerParam.number) && this.trfCustomer.buyerCustomerGroupCode == 'General') {
                this.buyerContactData = [];
                this.buyerCustomerNameData = [];
                this.buyerCustomerDisable = false;
                this.buyerNameCnDisable = false;
                this.buyerBossNoDisable = false;
                if (flag === 0) {
                    this.initCustomerRelationship();
                }
                return false;
            } else {
                this.buyerNameCnDisable = true;
                this.buyerBossNoDisable = true;
            }
            let data = [];
            if (this.trfCustomer.buyerCustomerGroupCode != 'general' && this.trfCustomer.buyerCustomerGroupCode != 'General') {
                //this.buyerRequried=false;
                searchBuyerParam.rows = 1000;
                searchBuyerParam.page = 1;
                data = await searchCustomerAndGroup(searchBuyerParam).then(res => {
                    return res.data.data;
                });
            } else {
                this.buyerCreateFlag = true;
                this.buyerRequried = false;
            }

            if (!objectIsNull(buyerCustomerNo) && !objectIsNull(data)) {//买家选择的客户 设置为不可编辑
                this.buyerCustomerDisable = true;
                let buyerCustomer = data[0];
                if (!objectIsNull(buyerCustomer)) {
                    this.reloadBuyerCustomer(buyerCustomer);//重新加载buyer Customer数据

                }
            } else if (objectIsNull(buyerCustomerNo) && !objectIsNull(data)) {//买家选择客户组
                if (buyerCustomerNo != 'General') {
                    this.buyerRequried = true;
                    this.buyerCreateFlag = false;
                } else {
                    this.buyerCreateFlag = true;
                    this.buyerRequried = false;
                }
                //默认加载主 Customer
                if (flag === 0) {
                    var customerParams = {};
                    customerParams.groupCode = this.trfCustomer.buyerCustomerGroupCode;
                    let mainCustomers = [];
                    mainCustomers = await getCustomerGroupByParms(customerParams).then(res => {
                        return res.data.data;
                    });
                    let mainBuyerCustomer = {};
                    //逻辑变更，如果模板为买家组模板的话 则获取模板配置的customerNo信息，如果为agent的模板的话 则使用买家Group下面的main customer
                    if (this.templateDataObj.customerGroupCode == this.trfCustomer.buyerCustomerGroupCode) {
                        if (!validatenull(this.templateDataObj.customerBossNo)) {
                            let reqBoss = {
                                number: this.templateDataObj.customerBossNo
                            }
                            let bossResData = await getSgsCustomer(reqBoss);
                            if (bossResData.status == 200 && bossResData.data && bossResData.data.rows && bossResData.data.rows.length > 0) {
                                let {number, nameEN, nameCN} = bossResData.data.rows[0];
                                mainBuyerCustomer.customerNameEn = nameEN;
                                mainBuyerCustomer.number = this.templateDataObj.customerBossNo;
                            }
                        }
                    } else {
                        if (!objectIsNull(mainCustomers)) {
                            let mainCustomer = mainCustomers[0];
                            if (!objectIsNull(mainCustomer)) {
                                mainBuyerCustomer = data.find((item) => {
                                    return item.number === mainCustomer.number;
                                });
                            }
                        }
                        if (objectIsNull(mainBuyerCustomer)) {
                            let mainBuyerCustomer = data[0];
                        }
                    }
                    if (!objectIsNull(mainBuyerCustomer)) {
                        this.reloadBuyerCustomer(mainBuyerCustomer);//重新加载buyer Customer数据
                    }
                }
            } else {
                this.buyerRequried = false;
                this.buyerCustomerDisable = false;
            }
            if (!objectIsNull(data)) {
                this.buyerCustomerNameData = data;
            }

            //处理buyer关联supplier,munufacure的显示
            if (flag === 0) {
                this.initCustomerRelationship();
            }

        },
        appendSupplierAndManufactureCustomer(supplierAndManufacture) {
            let {
                supplierName,
                supplierContact,
                supplierAddress,
                supplierTel,
                supplierEmail,
                supplierCode,

                manufacturerName,
                manufacturerContact,
                manufacturerAddress,
                manufacturerTel,
                manufacturerEmail,
                manufacturerCode,

            } = supplierAndManufacture;

            this.$set(this.customerForm.supplierCustomer, "customerNameEn", supplierName);
            this.$set(this.customerForm.supplierCustomer, "customerNameZh", null);
            this.$set(this.customerForm.supplierCustomer, "bossNo", null);
            this.$set(this.customerForm.supplierCustomer, "customerAddressEn", supplierAddress);
            this.$set(this.customerForm.supplierCustomer, "customerAddressZh", null);
            this.$set(this.customerForm.supplierCustomer, "customerReferenceNo", supplierCode);
            this.$set(this.customerForm.supplierCustomer, "contactName", supplierContact);
            this.$set(this.customerForm.supplierCustomer, "contactTelephone", supplierTel);
            let supplierEmailValue = (supplierEmail || '').split(',');
            this.$set(this, "supplierEmailValue", supplierEmailValue);
            this.supplierEmailValueChange(supplierEmailValue)

            this.$set(this.customerForm.manufactureCustomer, "customerNameEn", manufacturerName);
            this.$set(this.customerForm.manufactureCustomer, "customerNameZh", null);
            this.$set(this.customerForm.manufactureCustomer, "bossNo", null);
            this.$set(this.customerForm.manufactureCustomer, "customerAddressEn", manufacturerAddress);
            this.$set(this.customerForm.manufactureCustomer, "customerAddressZh", null);
            this.$set(this.customerForm.manufactureCustomer, "customerReferenceNo", manufacturerCode);
            this.$set(this.customerForm.manufactureCustomer, "contactName", manufacturerContact);
            this.$set(this.customerForm.manufactureCustomer, "contactTelephone", manufacturerTel);
            let manufactureEmailValue = (manufacturerEmail || '').split(",");
            this.$set(this, "manufactureEmailValue", manufactureEmailValue);
            this.manufactureEmailValueChange(manufactureEmailValue);
        },
        async initCustomerRelationship() {
            //初始为null
            if(!this.supplierSameAsFlag){
                this.handleDeleteManufacture();
                this.handleDeleteSupplier();
            }
            await this.initSCMCus();
        },
        async initSCMCus(){
            var applicationCustomerName = this.customerForm.applicationCustomer.customerNameEn;
            await this.isNewScmFlag();
            await this.initSCMSupplier(applicationCustomerName);
            await this.initSCMManufacturer(applicationCustomerName);
        },
        async initSCMSupplier(applicationCustomerName) {
            let applicantBossNo = this.customerForm.applicationCustomer.bossNo;
            let supplierData = await this.searchSupplierCustomerGroup(applicationCustomerName);
            //等待异步方法执行完成获取this.supplierCustomerNameData 的值
            //如果this.supplierCustomerNameData 包含applicationCustomerName 则显示supplierCustomerNameData
            if (!objectIsNull(supplierData)) {
                let supplierCustomer = supplierData.find((item) => {
                    return item.scmCustomerNo == applicantBossNo;
                });
                if (!objectIsNull(supplierCustomer) && !this.supplierSameAsFlag) {
                    let {
                        addressDetail,
                        customerAddressEn,
                        customerAddressZh
                    } = supplierCustomer;
                    let addressData = [{
                        addressDetail,
                        customerAddressEn,
                        customerAddressZh
                    }]
                    this.$set(this, 'supplierCustomerAddressData', addressData);
                    if(!this.reloadScmList){
                        this.$set(this.customerForm.supplierCustomer, 'customerNameEn', supplierCustomer.scmCustomerName);
                        this.$set(this.customerForm.supplierCustomer, 'customerNameZh', supplierCustomer.scmCustomerNameCN);
                        this.$set(this.customerForm.supplierCustomer, 'customerReferenceNo', supplierCustomer.scmCustomerReferenceCode);
                        this.$set(this.customerForm.supplierCustomer, 'bossNo', supplierCustomer.scmCustomerNo);
                        this.$set(this.customerForm.supplierCustomer, 'customerAddressEn', addressDetail);
                        this.$set(this.customerForm.supplierCustomer, 'customerAddressZh', customerAddressZh);
                    }
                }
            }else{
                this.supplierCustomerNameData = this.defaultApplicantCustomer();
            }
        },
        async initSCMManufacturer() {
            let applicantBossNo = this.customerForm.applicationCustomer.bossNo;
            let manufactureData = await this.searchManufactureCustomerGroup();
            if (!objectIsNull(manufactureData)) {
                let manufactureCustomer = manufactureData.find((item) => {
                    return item.scmCustomerNo == applicantBossNo;
                });
                if (!objectIsNull(manufactureCustomer)) {
                    let {
                        addressDetail,
                        customerAddressEn,
                        customerAddressZh
                    } = manufactureCustomer;
                    let addressData = [{
                        addressDetail,
                        customerAddressEn,
                        customerAddressZh
                    }]
                    this.$set(this, 'manufactureCustomerAddressData', addressData);
                    if(!this.reloadScmList){
                        this.$set(this.customerForm.manufactureCustomer, 'customerNameEn', manufactureCustomer.scmCustomerName);
                        this.$set(this.customerForm.manufactureCustomer, 'customerNameZh', manufactureCustomer.scmCustomerNameCN);
                        this.$set(this.customerForm.manufactureCustomer, 'customerReferenceNo', manufactureCustomer.scmCustomerReferenceCode);
                        this.$set(this.customerForm.manufactureCustomer, 'bossNo', manufactureCustomer.scmCustomerNo);
                        this.$set(this.customerForm.manufactureCustomer, 'customerAddressEn', addressDetail);
                        this.$set(this.customerForm.manufactureCustomer, 'customerAddressZh', customerAddressZh);
                    }
                }
            }else{
                this.manufactureCustomerNameData = this.defaultApplicantCustomer();
            }
        },
        async reloadBuyerCustomer(buyerCustomer) {
            if (!objectIsNull(buyerCustomer)) {
                //清空原有组数据
                this.$set(this.customerForm.buyerCustomer, 'customerGroupCode', null);
                this.$set(this.customerForm.buyerCustomer, 'customerGroupId', null);
                this.$set(this.customerForm.buyerCustomer, 'customerGroupName', null);

                this.$set(this.customerForm.buyerCustomer, 'customerNameEn', buyerCustomer.customerNameEn);
                this.$set(this.customerForm.buyerCustomer, 'customerNameZh', buyerCustomer.customerNameCn);
                this.$set(this.customerForm.buyerCustomer, 'bossNo', buyerCustomer.number);
                //查询联系人
                let searchContactParam = {};
                searchContactParam.number = this.customerForm.buyerCustomer.bossNo;
                let contacts = await this.searchContact(searchContactParam);
                this.buyerContactData = contacts;
            }
        },
        validataCustomerForm() {
            return new Promise(resolve => {
                this.$refs['customerForm'].validate((valid, error) => {
                    if (valid) {
                        resolve(true);
                    } else {
                        let keys = Object.keys(error);
                        let dom = this.$refs[keys[0]];
                        if (Object.prototype.toString.call(dom) !== '[object Object]') {
                            dom = dom[0]
                        }
                        dom.$el.scrollIntoView({
                            block: 'center',
                            behavior: 'smooth'
                        });
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.trfValidateError'),
                            type: 'warning'
                        });
                        resolve(false);
                    }
                });
            })
        },


        //组装CustomerList集合数据
        mergeCustomerInfo(source) {
            let customerList = [];
            if (!objectIsNull(this.customerForm.applicationCustomer)) {
                this.$set(this.customerForm.applicationCustomer, 'customerUsage', CustomerUsageEnums.APPLICATION.code);
                this.mergeContactInfo(this.customerForm.applicationCustomer);
                this.mergeLanguageCustomer(this.customerForm.applicationCustomer);
                this.setCustomerMainLanguageData(this.customerForm.applicationCustomer);
                customerList.push(this.customerForm.applicationCustomer);
            }
            if (!objectIsNull(this.customerForm.payerCustomer) && this.trfCustomer.isSame == 0) {
                // if(!objectIsNull(this.customerForm.payerCustomer.bossNo)||!objectIsNull(this.customerForm.payerCustomer.customerNameEn)||!objectIsNull(this.customerForm.payerCustomer.customerNameCn)){
                this.$set(this.customerForm.payerCustomer, 'customerUsage', CustomerUsageEnums.PAYER.code);
                this.mergeContactInfo(this.customerForm.payerCustomer);
                this.mergeLanguageCustomer(this.customerForm.payerCustomer);
                this.setCustomerMainLanguageData(this.customerForm.payerCustomer);
                customerList.push(this.customerForm.payerCustomer);
                // }

            }
            console.log('buyer信息', this.customerForm.buyerCustomer);
            if (!objectIsNull(this.customerForm.buyerCustomer)) {
                this.$set(this.customerForm.buyerCustomer, 'customerUsage', CustomerUsageEnums.BUYER.code);
                this.mergeContactInfo(this.customerForm.buyerCustomer);
                this.mergeLanguageCustomer(this.customerForm.buyerCustomer);
                this.setCustomerMainLanguageData(this.customerForm.buyerCustomer);
                customerList.push(this.customerForm.buyerCustomer);
            }

            if (!objectIsNull(this.customerForm.supplierCustomer) && this.templateSpecificCode.indexOf('Supplier') != -1) {
                // if(!objectIsNull(this.customerForm.supplierCustomer.bossNo)||!objectIsNull(this.customerForm.supplierCustomer.customerNameEn)){
                this.$set(this.customerForm.supplierCustomer, 'customerUsage', CustomerUsageEnums.SUPPLIER.code);
                this.mergeContactInfo(this.customerForm.supplierCustomer);
                this.mergeLanguageCustomer(this.customerForm.supplierCustomer);
                this.setCustomerMainLanguageData(this.customerForm.supplierCustomer);
                customerList.push(this.customerForm.supplierCustomer);
                //  }
            }
            if (!objectIsNull(this.customerForm.agentCustomer)) {
                if (this.templateSpecificCode.indexOf('Agent') == -1) {//不存在Agant配置，但历史数据或copy原数据可能存在Agent数据
                    this.$set(this.customerForm.agentCustomer, 'customerNameZh', null);
                    this.$set(this.customerForm.agentCustomer, 'bossNo', null);
                    this.$set(this.customerForm.agentCustomer, 'customerReferenceNo', null);
                    this.$set(this.customerForm.agentCustomer, 'customerAddressEn', null);
                    this.$set(this.customerForm.agentCustomer, 'customerAddressZh', null);
                    this.$set(this.customerForm.agentCustomer, 'customerContactList', null);
                    this.$set(this.customerForm.agentCustomer, 'contactName', null);
                    this.$set(this.customerForm.agentCustomer, 'customerContactList', null);
                    this.$set(this.customerForm.agentCustomer, 'contactTelephone', null);
                    this.$set(this.customerForm.agentCustomer, 'contactEmail', null);
                }
                let b = this.validateCustomerPropertie(this.customerForm.agentCustomer);
                if (b) {
                    this.$set(this.customerForm.agentCustomer, 'customerUsage', CustomerUsageEnums.AGENT.code);
                    this.mergeContactInfo(this.customerForm.agentCustomer);
                    this.mergeLanguageCustomer(this.customerForm.agentCustomer);
                    this.setCustomerMainLanguageData(this.customerForm.agentCustomer);
                    customerList.push(this.customerForm.agentCustomer);
                }
            }
            if (!objectIsNull(this.customerForm.manufactureCustomer) && this.templateSpecificCode.indexOf('Manufacture') != -1) {
                // if(!objectIsNull(this.customerForm.manufactureCustomer.bossNo)||!objectIsNull(this.customerForm.manufactureCustomer.customerNameEn)||!objectIsNull(this.customerForm.manufactureCustomer.customerNameCn)){
                this.$set(this.customerForm.manufactureCustomer, 'customerUsage', CustomerUsageEnums.MANUFACTURE.code);
                this.mergeContactInfo(this.customerForm.manufactureCustomer);
                this.mergeLanguageCustomer(this.customerForm.manufactureCustomer);
                this.setCustomerMainLanguageData(this.customerForm.manufactureCustomer);
                customerList.push(this.customerForm.manufactureCustomer);
                // }
            }
            //source = Object.assign(source, customerList);
            return customerList;
        },
        validateCustomerPropertie(customerObj) {
            let result = false;
            this.validateCustomerProperties.forEach(propertie => {
                if (!validatenull(customerObj[propertie])) {
                    result = true;
                    return false;
                }

            })
            return result;
        },

        setCustomerMainLanguageData(obj) {
            let customerName = '';
            if (!objectIsNull(obj.customerNameEn)) {
                customerName = obj.customerNameEn;
            }
            if (!objectIsNull(customerName)) {
                customerName = obj.customerNameZh;
            }
            this.$set(obj, 'customerName', customerName);
            let customerAddress = '';
            if (!objectIsNull(obj.customerAddressEn)) {
                customerAddress = obj.customerAddressEn;
            }
            if (!objectIsNull(customerAddress)) {
                customerAddress = obj.customerAddressZh;
            }
            this.$set(obj, 'customerAddress', customerAddress);
        },
        mergeContactInfo(obj) {
            let contacts = [];
            let contactObj = {};
            contactObj.contactEmail = obj.contactEmail;
            contactObj.contactName = obj.contactName;
            contactObj.contactTelephone = obj.contactTelephone;
            if (!objectIsNull(contactObj)) {
                contacts.push(contactObj);
            }
            this.$set(obj, 'customerContactList', contacts);
        },
        mergeLanguageCustomer(obj) {
            let languageCustomers = [];
            if (!objectIsNull(obj.customerNameEn) || !objectIsNull(obj.customerAddressEn)) {
                let languageCustomer = {};
                languageCustomer.customerName = obj.customerNameEn;
                languageCustomer.customerAddress = obj.customerAddressEn;
                languageCustomer.languageId = LanguageEnums.EN.code;
                languageCustomers.push(languageCustomer);
            }else{
                let languageCustomer = {customerName:'',customerAddress:'',languageId:LanguageEnums.EN.code};
                languageCustomers.push(languageCustomer);
            }
            if (!objectIsNull(obj.customerNameZh) || !objectIsNull(obj.customerAddressZh)) {
                let languageCustomer = {};
                languageCustomer.customerName = obj.customerNameZh;
                languageCustomer.customerAddress = obj.customerAddressZh;
                languageCustomer.languageId = LanguageEnums.CN.code;
                languageCustomers.push(languageCustomer);
            }else{
                let languageCustomer = {customerName:'',customerAddress:'',languageId:LanguageEnums.CN.code};
                languageCustomers.push(languageCustomer);
            }
            this.$set(obj, 'languageList', languageCustomers);
        },
        async setBuyerCustomerInfoByTemplateId() {
            //if(validatenull(this.customerForm.buyerCustomer.bossNo) ){
            //查询模板数据
            if (!validatenull(this.trf.trfTemplateId)) {
                let templateObj = await this.searchTemplate();
                if (!validatenull(templateObj) && templateObj.customerGroupCode != 'General') {
                    if (templateObj.customerGroupCode == this.customerForm.buyerCustomer.customerGroupCode) {
                        this.customerForm.buyerCustomer.bossNo = templateObj.customerBossNo;
                        //加载名称
                        await searchCustomer({number: templateObj.customerBossNo}).then(async res => {
                            if (!res.data.data && res.data.data.length <= 0) return;
                            let customer = res.data.data[0];
                            this.customerForm.buyerCustomer.customerNameEn = objectIsNull(customer.nameEN) ? customer.nameCN : customer.nameEN;
                        })
                    } else if (!validatenull(this.customerForm.buyerCustomer.customerGroupCode)) {//获取group 默认的Customer 和新建单逻辑一致
                        var customerParams = {};
                        customerParams.groupCode = this.customerForm.buyerCustomer.customerGroupCode;
                        let mainCustomers = [];
                        mainCustomers = await getCustomerGroupByParms(customerParams).then(res => {
                            return res.data.data;
                        });
                        let data = [];
                        if (this.customerForm.buyerCustomer.customerGroupCode != 'general' && this.customerForm.buyerCustomer.customerGroupCode != 'General') {
                            //this.buyerRequried=false;
                            let searchBuyerParam = {};
                            searchBuyerParam.displayType = 'account';
                            searchBuyerParam.customerGroupCode = this.customerForm.buyerCustomer.customerGroupCode;
                            searchBuyerParam.rows = 1000;
                            searchBuyerParam.page = 1;
                            data = await searchCustomerAndGroup(searchBuyerParam).then(res => {
                                return res.data.data;
                            });
                        }
                        let mainBuyerCustomer = {};
                        if (!objectIsNull(mainCustomers) && !objectIsNull(data)) {
                            let mainCustomer = mainCustomers[0];
                            if (!objectIsNull(mainCustomer)) {
                                mainBuyerCustomer = data.find((item) => {
                                    return item.number === mainCustomer.number;
                                });
                            }
                        }
                        if (!validatenull(mainBuyerCustomer)) {
                            this.customerForm.buyerCustomer.bossNo = mainBuyerCustomer.number;
                            this.customerForm.buyerCustomer.customerNameEn = mainBuyerCustomer.customerNameEn;
                        }
                    }
                }
            }
            // }
        },
        //回显拆分CustomerList集合数据
        async splitCustomerInfo() {
            if (!objectIsNull(this.trfCustomers)) {
                let applicationObj = this.trfCustomers.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.APPLICATION.code) {
                        return item;
                    }
                });
                if (!objectIsNull(applicationObj)) {
                    this.$set(this.customerForm, 'applicationCustomer', applicationObj);

                    let languageList = applicationObj.languageList;
                    if (!objectIsNull(languageList)) {
                        this.setLanguageData(this.customerForm.applicationCustomer, languageList);
                    }
                    if (!objectIsNull(applicationObj.customerContactList)) {
                        let contact = applicationObj.customerContactList[0];
                        this.$set(this.customerForm.applicationCustomer, 'contactName', contact.contactName);
                        this.$set(this.customerForm.applicationCustomer, 'contactEmail', contact.contactEmail);
                        this.applyEmailValue = objectIsNull(this.customerForm.applicationCustomer.contactEmail) ? [] : this.customerForm.applicationCustomer.contactEmail.split(',');
                        this.$set(this.customerForm.applicationCustomer, 'contactTelephone', contact.contactTelephone);
                    }
                }
                let payerCustomerObj = this.trfCustomers.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.PAYER.code) {
                        return item;
                    }
                });
                if (!objectIsNull(payerCustomerObj)) {
                    this.$set(this.customerForm, 'payerCustomer', payerCustomerObj);

                    let languageList = payerCustomerObj.languageList;
                    if (!objectIsNull(languageList)) {
                        this.setLanguageData(this.customerForm.payerCustomer, languageList);
                    }
                    if (!objectIsNull(payerCustomerObj.customerContactList)) {
                        let contact = payerCustomerObj.customerContactList[0];
                        this.$set(this.customerForm.payerCustomer, 'contactName', contact.contactName);
                        this.$set(this.customerForm.payerCustomer, 'contactEmail', contact.contactEmail);
                        this.$set(this.customerForm.payerCustomer, 'contactTelephone', contact.contactTelephone);
                        // this.customerForm.payerCustomer.contactName=contact.contactName;
                        // this.customerForm.payerCustomer.contactEmail=contact.contactEmail;
                        // this.customerForm.payerCustomer.contactTelephone=contact.contactTelephone;
                    }
                }
                let buyerCustomerObj = this.trfCustomers.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.BUYER.code) {
                        return item;
                    }
                });
                if (!objectIsNull(buyerCustomerObj)) {
                    this.$set(this.customerForm, 'buyerCustomer', buyerCustomerObj);

                    let languageList = buyerCustomerObj.languageList;
                    if (!objectIsNull(languageList)) {
                        this.setLanguageData(this.customerForm.buyerCustomer, languageList);
                    }
                    //如果用户模板无BossNo和customer信息 则自动赋值模板默认customer信息
                    if (this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code) {
                        //SMART-4034 模板建单时 如果非General的话 买家必填
                        this.buyerRequried = false;
                        if (this.customerForm.buyerCustomer.customerGroupCode != 'General') {
                            this.buyerRequried = true;
                        }
                        if (validatenull(this.customerForm.buyerCustomer.bossNo)) {
                            this.setBuyerCustomerInfoByTemplateId();
                        }
                    }
                    if (!objectIsNull(buyerCustomerObj.customerContactList)) {
                        let contact = buyerCustomerObj.customerContactList[0];
                        this.$set(this.customerForm.buyerCustomer, 'contactName', contact.contactName);
                        this.$set(this.customerForm.buyerCustomer, 'contactEmail', contact.contactEmail);
                        this.$set(this.customerForm.buyerCustomer, 'contactTelephone', contact.contactTelephone);
                        // this.customerForm.buyerCustomer.contactName=contact.contactName;
                        // this.customerForm.buyerCustomer.contactEmail=contact.contactEmail;
                        this.buyerEmailValue = objectIsNull(this.customerForm.buyerCustomer.contactEmail) ? [] : this.customerForm.buyerCustomer.contactEmail.split(',');
                        // this.customerForm.buyerCustomer.contactTelephone=contact.contactTelephone;
                    }
                    //将Buyer信息回传至TRF主页
                    this.$emit('updateBuyerInfo', buyerCustomerObj);
                } else {
                    if (this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code) {
                        this.$set(this.customerForm, 'buyerCustomer', {});
                        this.setBuyerCustomerInfoByTemplateId();
                    }
                }
                let supplierCustomerObj = this.trfCustomers.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.SUPPLIER.code) {
                        return item;
                    }
                });
                if (!objectIsNull(supplierCustomerObj)) {
                    this.$set(this.customerForm, 'supplierCustomer', supplierCustomerObj);
                    // this.customerForm.supplierCustomer=supplierCustomerObj;
                    let languageList = supplierCustomerObj.languageList;
                    if (!objectIsNull(languageList)) {
                        this.setLanguageData(this.customerForm.supplierCustomer, languageList);
                    }
                    if (!objectIsNull(supplierCustomerObj.customerContactList)) {
                        let contact = supplierCustomerObj.customerContactList[0];
                        this.$set(this.customerForm.supplierCustomer, 'contactName', contact.contactName);
                        this.$set(this.customerForm.supplierCustomer, 'contactEmail', contact.contactEmail);
                        this.$set(this.customerForm.supplierCustomer, 'contactTelephone', contact.contactTelephone);
                        // this.customerForm.supplierCustomer.contactName=contact.contactName;
                        // this.customerForm.supplierCustomer.contactEmail=contact.contactEmail;
                        this.supplierEmailValue = objectIsNull(this.customerForm.supplierCustomer.contactEmail) ? [] : this.customerForm.supplierCustomer.contactEmail.split(',');
                        // this.customerForm.supplierCustomer.contactTelephone=contact.contactTelephone;
                    }
                }
                let agentCustomerObj = this.trfCustomers.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.AGENT.code) {
                        return item;
                    }
                });
                if (!objectIsNull(agentCustomerObj)) {
                    this.$set(this.customerForm, 'agentCustomer', agentCustomerObj);
                    // this.customerForm.agentCustomer=agentCustomerObj;
                    let languageList = agentCustomerObj.languageList;
                    if (!objectIsNull(languageList)) {
                        this.setLanguageData(this.customerForm.agentCustomer, languageList);
                    }
                    if (!objectIsNull(agentCustomerObj.customerContactList)) {
                        let contact = agentCustomerObj.customerContactList[0];
                        this.$set(this.customerForm.agentCustomer, 'contactName', contact.contactName);
                        this.$set(this.customerForm.agentCustomer, 'contactEmail', contact.contactEmail);
                        this.$set(this.customerForm.agentCustomer, 'contactTelephone', contact.contactTelephone);
                        // this.customerForm.agentCustomer.contactName=contact.contactName;
                        // this.customerForm.agentCustomer.contactEmail=contact.contactEmail;
                        this.agentEmailValue = objectIsNull(this.customerForm.agentCustomer.contactEmail) ? [] : this.customerForm.agentCustomer.contactEmail.split(',');
                        // this.customerForm.agentCustomer.contactTelephone=contact.contactTelephone;
                    }
                }
                let manufactureCustomerObj = this.trfCustomers.find((item) => {
                    if (item.customerUsage === CustomerUsageEnums.MANUFACTURE.code) {
                        return item;
                    }
                });
                if (!objectIsNull(manufactureCustomerObj)) {
                    this.$set(this.customerForm, 'manufactureCustomer', manufactureCustomerObj);
                    // this.customerForm.manufactureCustomer=manufactureCustomerObj;
                    let languageList = manufactureCustomerObj.languageList;
                    if (!objectIsNull(languageList)) {
                        this.setLanguageData(this.customerForm.manufactureCustomer, languageList);
                    }
                    if (!objectIsNull(manufactureCustomerObj.customerContactList)) {
                        let contact = manufactureCustomerObj.customerContactList[0];
                        this.$set(this.customerForm.manufactureCustomer, 'contactName', contact.contactName);
                        this.$set(this.customerForm.manufactureCustomer, 'contactEmail', contact.contactEmail);
                        this.$set(this.customerForm.manufactureCustomer, 'contactTelephone', contact.contactTelephone);
                        // this.customerForm.manufactureCustomer.contactName=contact.contactName;
                        // this.customerForm.manufactureCustomer.contactEmail=contact.contactEmail;
                        this.manufactureEmailValue = objectIsNull(this.customerForm.manufactureCustomer.contactEmail) ? [] : this.customerForm.manufactureCustomer.contactEmail.split(',');
                        // this.customerForm.manufactureCustomer.contactTelephone=contact.contactTelephone;
                    }
                }
            }
        },
        setLanguageData(obj, languageList) {
            if (!objectIsNull(languageList)) {
                for (let languageObj of languageList) {
                    if (languageObj.languageId === LanguageEnums.EN.code) {
                        this.$set(obj, 'customerNameEn', languageObj.customerName);
                        this.$set(obj, 'customerAddressEn', languageObj.customerAddress);
                    } else {
                        this.$set(obj, 'customerNameZh', languageObj.customerName);
                        this.$set(obj, 'customerAddressZh', languageObj.customerAddress);
                    }
                }
            } else {
                obj.customerAddressEn = obj.customerAddress;
                obj.customerAddressZh = obj.customerAddress;
                obj.customerNameEn = obj.customerName;
                obj.customerNameEn = obj.customerName;
            }
        },
        payEmailValueChange(emailList) {
            //调用通用方法
            let obj = {};
            let pasteEmailArr = []
            emailList.find((item1) => {
                obj = this.emailGroupData.find((item) => {
                    if (item1 == item.emailGroupName) {//判断当前邮件是否和
                        return item;
                    }
                });
                if (validatenull(obj)) {//手动输入邮箱验证
                    let validateRes = validateEmail(item1);
                    if (validateRes) {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1);
                        }
                    }
                } else {
                    if (obj.type != 2) {//邮件组数据
                        var contactEmail = obj.contacts;
                        contactEmail.find((item2) => {
                            if (pasteEmailArr.indexOf(item2.contactEmail) == -1) {
                                pasteEmailArr.push(item2.contactEmail);
                            }
                        });
                    } else {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1);
                        }
                    }
                }
            });
            this.payEmailValue = pasteEmailArr;
            if (this.payEmailValue.length != 0) {
                this.$set(this.customerForm.payerCustomer, 'contactEmail', this.payEmailValue.join(','));
                // this.customerForm.payerCustomer.contactEmail =
            } else {
                this.$set(this.customerForm.payerCustomer, 'contactEmail', null);
                // this.customerForm.payerCustomer.contactEmail = '';
            }
        },
        //reloadFlg =1 标识为草稿状态回显
        async initTrfCustomer(reloadFlg) {
            this.applicationCustomerNameData = [];
            const loading = this.$loading({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            //this.applyEmailValue = [];
            this.customerAddressData = [];
            this.customerContactData = [];
            if (validatenull(this.trfCustomer.customerId) && this.trfStatus === 1) {
                this.trfCustomer.customerId = this.userInfo.companyId;
            }
            await this.searchBossNoByCustomerId(this.trfCustomer.customerId || this.userInfo.companyId, reloadFlg).then(async res => {
                if (res) await this.queryCustomers(res.bossNo, reloadFlg);
            });
            this.listCustomerAuthList();
            await this.queryCustomerAddress(this.trfCustomer.customerId, reloadFlg);
            await this.queryCustomerContact(this.trfCustomer.customerId, reloadFlg);
            if (!objectIsNull(this.trfCustomerContact.applyContactEmail)) {
                this.applyEmailValue = this.trfCustomerContact.applyContactEmail.split(',');
            }
            //加载scm关系数据
            if((this.trf.id && this.trfStatus==1) || [TrfActionTypeEnums.TEMPLATE.code].includes(this.actionType)){
                this.reloadScmList = true;
                await this.initSCMCus();
            }
            loading.close();
        },

        showModifyBtn(usageType) {
            let statusVlid = [3, 4].includes(this.trf.trfStatus - 0);
            if (!statusVlid) {
                return false;
            }
            let customerField = usageType + 'ModifyBtn';
            return this.permissionList[customerField];

        },
        async updateModifyCustomer(usageType) {
            let needCustomer = this['need' + this.$lodash.upperFirst(usageType)];
            let customerObjField = usageType + 'Customer';
            let {customerNameEn} = this.customerForm[customerObjField];
            if (needCustomer && !customerNameEn) {
                this.$notify({
                    title: this.$t('tip'),
                    message: this.$t('trf.trfValidateError'),
                    type: 'warning'
                });
                this.$refs[usageType + 'CustomerSelect'].$el.scrollIntoView({
                    block: 'center',
                    behavior: 'smooth'
                });
                return;
            }
            this.$set(this.customerForm[customerObjField], 'customerUsage', CustomerUsageEnums[usageType.toUpperCase()].code);
            this.mergeContactInfo(this.customerForm[customerObjField]);
            this.mergeLanguageCustomer(this.customerForm[customerObjField]);
            this.setCustomerMainLanguageData(this.customerForm[customerObjField]);
            let saveObj = {
                ...{trfId: this.trf.id},
                ...this.customerForm[customerObjField],
            }
            //console.log("save obj",saveObj);
            this.$set(this, usageType + 'SaveLoading', true);
            updateTrfCustomer(saveObj).then(res => {
                //console.log("res",res);
                this.$set(this, usageType + 'ModifyDisabled', true);
                this.$set(this, usageType + 'SaveLoading', false);
                if (res.status == 200 && res.data.status == 200) {
                    this.$notify.success(this.$t('api.success'));
                } else {
                    this.$notify.error(res.data.msg || this.$t('api.error'));
                }
            }).catch(err => {
                this.$notify.error(this.$t('api.error'));
                this.$set(this, usageType + 'SaveLoading', false);
            })
        },
        changeSupplierSameFlag(){
            if(!this.supplierSameAsFlag || (!this.needSupplier && this.templateSpecificCode.indexOf('Supplier')==-1)){
                return
            }
            //同步applicant数据
            let oldSupplierName = this.customerForm.supplierCustomer.customerNameEn || '';
            let copyApplicantCustomer = JSON.parse(JSON.stringify(this.customerForm.applicationCustomer));
            let {customerNameEn,customerNameZh,bossNo,
                customerAddressEn,customerAddressZh,customerReferenceNo,
                contactName,contactTelephone,contactEmail,customerId
            } = copyApplicantCustomer;
            let supplierEmailValue = this.applyEmailValue || [];
            this.$set(this.customerForm.supplierCustomer, "customerNameEn", customerNameEn || '');
            this.$set(this.customerForm.supplierCustomer, "customerNameZh", customerNameZh|| '');
            this.$set(this.customerForm.supplierCustomer, "bossNo", bossNo|| '');
            this.$set(this.customerForm.supplierCustomer, "customerAddressEn", customerAddressEn|| '');
            this.$set(this.customerForm.supplierCustomer, "customerAddressZh", customerAddressZh|| '');
            this.$set(this.customerForm.supplierCustomer, "customerReferenceNo", customerReferenceNo|| '');
            this.$set(this.customerForm.supplierCustomer, "contactName", contactName|| '');
            this.$set(this.customerForm.supplierCustomer, "customerId", customerId|| '');
            this.$set(this.customerForm.supplierCustomer, "contactTelephone", contactTelephone|| '');
            this.$set(this, "supplierEmailValue", supplierEmailValue);
            this.$set(this.customerForm.supplierCustomer, 'contactEmail', contactEmail);
            if(oldSupplierName!=customerNameEn){
                this.queryManufacture();
            }

        },
        disabledCustomer(usageType) {
            if (this.permissionList[usageType + 'ModifyBtn']) {
                return this.trfDisabled && this[usageType + 'ModifyDisabled'];
            }
            return this.trfDisabled;
        },
        handleBlur(e) {
            this.$emit('clear', e);
        },

        handleFocus(e) {
            this.$emit('focus', e);
        },

        customerNameChange(val) {
            this.$emit('input', val);
        },

        customerAddressChange(val) {
            this.trfCustomer.customerAddressEn = val;
        },

        applicationCustomerEnNameChange(val) {
            /* if(objectIsNull(val)){
         this.$set(this.applicationCustomer, 'bossNo',null);
         this.$set(this.applicationCustomer, 'customerNameZh',null);
         return false;
       }*/
            let obj = {};
            obj = this.applicationCustomerNameData.find((item) => {
                return item.customerNameEn === val;
            });
            if (!objectIsNull(obj)) {
                this.$set(this.customerForm.applicationCustomer, 'bossNo', obj.customerNo);
                this.$set(this.customerForm.applicationCustomer, 'customerNameZh', objectIsNull(obj.customerNameZh) ? val : obj.customerNameZh);
                //this.$set(this.trfCustomer, 'customerId',obj.customerId);
                this.$set(this.customerForm.applicationCustomer, 'customerId', obj.customerId);
                this.$set(this.trfCustomer, 'sgsAccountId', obj.sgsAccountId);
                this.$set(this.trfCustomer, 'sgsCustomerId', obj.sgsCustomerId);
                if (obj.type === 'AuthCustomer') {//代填
                    obj.authCustomerId = obj.sgsCustomerId;
                    this.choiceApplicant(obj);
                } else {//选择的当前用户
                    this.setCurrentCustomerInfo(obj);
                    // obj.authCustomerId=obj.customerId;
                    // this.getCustomerUserByCustomer(obj);
                }

            }
        },
        //非代填客户赋值
        async setCurrentCustomerInfo(customer) {
            await this.queryCustomerAddress(customer.customerId, 0);
            await this.queryCustomerContact(customer.customerId, 0);
            if (!objectIsNull(this.trfCustomerContact.applyContactEmail)) {
                this.applyEmailValue = this.trfCustomerContact.applyContactEmail.split(',');
            }
        },

        appContactNameChange(val) {
            // console.log("选中的默认联系人", val);
            let contact = this.customerContactData.find(item => item.contactName === val);
            if (!objectIsNull(contact)) {
                this.$set(this.customerForm.applicationCustomer, 'contactName', contact.contactName);
                this.$set(this.customerForm.applicationCustomer, 'contactTelephone', contact.contactTel);

                //原有逻辑
                this.$set(this.trfCustomerContact, 'applyContactName', contact.contactName);
                this.$set(this.trfCustomerContact, 'applyContactTel', contact.contactTel);

                this.applyEmailValue = [];
                this.applyEmailValue.push(contact.contactEmail);
                this.applyEmailValueChange(this.applyEmailValue);
            }
        },
        buyerContactNameChange(val) {
            /*if(objectIsNull(val)){
        this.$set(this.buyerCustomer, 'contactName', null);
        this.$set(this.buyerCustomer, 'contactTelephone', null);
        this.$set(this.buyerCustomer, 'contactEmail', null);
        this.buyerEmailValue =[];
        return false;
      }
      this.$set(this.buyerCustomer, 'contactTelephone', null);
      this.$set(this.buyerCustomer, 'contactEmail', null);
      this.buyerEmailValue =[];

       */
            //赋值买家联系人信息
            let obj = {};
            obj = this.buyerContactData.find((item) => {
                return item.contactName === val;
            });
            if (!objectIsNull(obj)) {
                this.$set(this.customerForm.buyerCustomer, 'contactTelephone', obj.primaryTelephone);
                this.$set(this.customerForm.buyerCustomer, 'contactEmail', obj.email);
                this.buyerEmailValue = objectIsNull(obj.email) ? [] : obj.email.split(',');
            }
        },
        agentContactNameChange(val) {
            //赋值买家联系人信息
            let obj = {};
            obj = this.agentContactData.find((item) => {
                return item.contactName === val;
            });
            if (!objectIsNull(obj)) {
                this.$set(this.customerForm.agentCustomer, 'contactTelephone', obj.primaryTelephone);
                this.$set(this.customerForm.agentCustomer, 'contactEmail', obj.email);
                this.agentEmailValue = objectIsNull(obj.email) ? [] : obj.email.split(',');
            }
        },
        applyEmailValueChange(values) {
            this.applyEmailValue = serviceRequirement.changeContacts(this.emailGroupData, values, this.applyEmailValue);
            if (!objectIsNull(this.applyEmailValue)) {
                this.$set(this.customerForm.applicationCustomer, 'contactEmail', this.applyEmailValue.join(','));
                this.$set(this.trfCustomerContact, 'contactEmail', this.applyEmailValue.join(','));
            } else {
                this.$set(this.customerForm.applicationCustomer, 'contactEmail', null);
                this.$set(this.trfCustomerContact, 'contactEmail', null);
            }

        },
        buyerEmailValueChange(values) {
            this.buyerEmailValue = serviceRequirement.changeContacts(this.emailGroupData, values, this.buyerEmailValue);
            if (!objectIsNull(this.buyerEmailValue)) {
                this.$set(this.customerForm.buyerCustomer, 'contactEmail', this.buyerEmailValue.join(','));
            } else {
                this.$set(this.customerForm.buyerCustomer, 'contactEmail', null);
            }
        },
        supplierEmailValueChange(values) {
            this.supplierEmailValue = serviceRequirement.changeContacts(this.emailGroupData, values, this.supplierEmailValue);
            if (!objectIsNull(this.supplierEmailValue)) {
                this.$set(this.customerForm.supplierCustomer, 'contactEmail', this.supplierEmailValue.join(','));
            } else {
                this.$set(this.customerForm.supplierCustomer, 'contactEmail', null);
            }
        },
        agentEmailValueChange(values) {
            this.agentEmailValue = serviceRequirement.changeContacts(this.emailGroupData, values, this.agentEmailValue);
            if (!objectIsNull(this.agentEmailValue)) {
                this.$set(this.customerForm.agentCustomer, 'contactEmail', this.agentEmailValue.join(','));
            } else {
                this.$set(this.customerForm.agentCustomer, 'contactEmail', null);
            }

        },
        manufactureEmailValueChange(values) {
            this.manufactureEmailValue = serviceRequirement.changeContacts(this.emailGroupData, values, this.manufactureEmailValue);
            if (!objectIsNull(this.manufactureEmailValue)) {
                this.$set(this.customerForm.manufactureCustomer, 'contactEmail', this.manufactureEmailValue.join(','));
            } else {
                this.$set(this.customerForm.manufactureCustomer, 'contactEmail', null);
            }
        },


        //根据customerId查询客户信息
        async searchBossNoByCustomerId(customerId, reloadFlg) {
            if (objectIsNull(customerId)) return;
            let res = await queryCustomerForId(customerId);
            if (!objectIsNull(res)) {
                let customer = res.data.data;
                //先将客户信息放入申请方 不存在BossNo的客户数据
                if (validatenull(customer.bossNo)) {
                    this.trfCustomer.customerNameEn = objectIsNull(customer.customerNameEn) ? customer.customerNameZh : customer.customerNameEn;
                    this.trfCustomer.customerNameZh = objectIsNull(customer.customerNameZh) ? customer.customerNameEn : customer.customerNameZh;
                    this.trfCustomer.sgsCustomerId = customerId;
                    let applicationCustomer = {};
                    applicationCustomer.customerNameEn = objectIsNull(customer.customerNameEn) ? customer.customerNameZh : customer.customerNameEn;
                    applicationCustomer.customerNameZh = objectIsNull(customer.customerNameZh) ? customer.customerNameEn : customer.customerNameZh;
                    applicationCustomer.sgsCustomerId = customerId;
                    this.applicationCustomerNameData.push(applicationCustomer)
                    //默认为当前申请方
                    if (reloadFlg != 1) {
                        this.$set(this.customerForm.applicationCustomer, 'customerNameEn', this.trfCustomer.customerNameEn);
                        this.applicationCustomerEnNameChange(this.trfCustomer.customerNameEn);
                    }
                }
            }
            if (!res.data.data || objectIsNull(res.data.data.bossNo)) return;
            return res.data.data;
        },

        //查询客户信息
        async queryCustomers(customerNo, reloadFlg) {
            if (objectIsNull(customerNo)) return [];
            await searchCustomer({number: customerNo}).then(async res => {
                if (!res.data.data && res.data.data.length <= 0) return;
                let customer = res.data.data[0];
                //组装申请方信息
                //默认加载当前客户信息
                if (this.userInfo.bossNo != customerNo && !objectIsNull(this.userInfo.bossNo)) {//将当前用户存入applicationCustomerNameData
                    await searchCustomer({number: this.userInfo.bossNo}).then(async res1 => {
                        if (!res1.data.data && res1.data.data.length <= 0) return;
                        let customer1 = res1.data.data[0];
                        if (!objectIsNull(customer1)) {
                            let currentCustomer = {};
                            currentCustomer.customerNameEn = objectIsNull(customer1.nameEN) ? customer1.nameCN : customer1.nameEN;
                            currentCustomer.customerNameZh = objectIsNull(customer1.nameCN) ? customer1.nameEN : customer1.nameCN;
                            currentCustomer.sgsCustomerId = customer1.customerId;
                            currentCustomer.sgsAccountId = customer1.accountId;
                            currentCustomer.customerNo = this.userInfo.bossNo;
                            currentCustomer.customerId = this.userInfo.companyId;
                            currentCustomer.type = 'CurrentCustomer';//当前客户
                            this.applicationCustomerNameData.push(currentCustomer)
                        }
                    });
                } else {
                    let applicationCustomer = {};
                    applicationCustomer.customerNameEn = objectIsNull(customer.nameEN) ? customer.nameCN : customer.nameEN;
                    applicationCustomer.customerNameZh = objectIsNull(customer.nameCN) ? customer.nameEN : customer.nameCN;
                    applicationCustomer.sgsCustomerId = customer.customerId;
                    applicationCustomer.sgsAccountId = customer.accountId;
                    applicationCustomer.customerNo = customerNo;
                    applicationCustomer.customerId = this.trfCustomer.customerId;
                    applicationCustomer.type = 'CurrentCustomer';//当前客户
                    this.applicationCustomerNameData.push(applicationCustomer)
                }

                //保留原有逻辑
                this.trfCustomer.customerNameEn = objectIsNull(customer.nameEN) ? customer.nameCN : customer.nameEN;
                this.trfCustomer.customerNameZh = objectIsNull(customer.nameCN) ? customer.nameEN : customer.nameCN;
                this.trfCustomer.sgsCustomerId = customer.customerId;
                this.trfCustomer.sgsAccountId = customer.accountId;

                //默认为当前申请方
                if (reloadFlg != 1) {
                    this.$set(this.customerForm.applicationCustomer, 'customerNameEn', this.trfCustomer.customerNameEn);
                    this.applicationCustomerEnNameChange(this.trfCustomer.customerNameEn);
                }
            });
        },
        //查询客户地址
        async queryCustomerAddress(customerId, reloadFlg) {
            if (objectIsNull(customerId)) return [];
            // if(this.trfCustomer.customerId != this.userInfo.companyId) {
            //   return;
            // }
            getCustomerAddressList({customerId: customerId, status: 1}).then(res => {
                this.customerAddressData = res.data.data ? res.data.data : [];
                console.log('ACTION Type', this.actionType);
                if (reloadFlg == 0 || this.actionType === TrfActionTypeEnums.ADD.code || this.actionType === 'material' ||
                    ((this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code))) {
                    let index = this.customerAddressData.findIndex(item => item.isDefault === 1);
                    //清空原有选择的地址信息
                    if (reloadFlg == 0 && this.actionType != TrfActionTypeEnums.TEMPLATE.code) {
                        this.$set(this.customerForm.applicationCustomer, "customerAddressEn", null);
                    }
                    if (index >= 0 && reloadFlg == 0 && this.actionType != TrfActionTypeEnums.TEMPLATE.code) {
                        //默认选择默认
                        this.defaultAddress = this.customerAddressData[index].addressDetail;
                        this.$set(this.customerForm.applicationCustomer, "customerAddressEn", this.defaultAddress);
                        this.customerAddressChange(this.customerAddressData[index].addressDetail);
                    }
                }
            });
        },

        //申请方查询代填数据
        listCustomerAuthList() {
            if (objectIsNull(this.createUserId)) return;
            proxyCustomersNoPage({
                applyUserId: this.createUserId,
                authStatus: CUSTOMER_AUTH_STATUS,
                authType: CUSTOMER_AUTH_TYPE,
            }).then(res => {
                if (res.data && res.data.data) {
                    let results = res.data.data;
                    //组装申请方代填信息
                    if (!objectIsNull(results)) {
                        for (let proxyCustomer of results) {
                            let applicationCustomer = {};
                            applicationCustomer.customerNameEn = proxyCustomer.customerNameEn;
                            applicationCustomer.customerNameZh = objectIsNull(proxyCustomer.customerNameZh) ? proxyCustomer.customerNameEn : proxyCustomer.customerNameZh;
                            applicationCustomer.sgsCustomerId = proxyCustomer.authCustomerId;
                            applicationCustomer.sgsAccountId = proxyCustomer.authUserId;
                            applicationCustomer.customerNo = proxyCustomer.bossNo;
                            applicationCustomer.authUserId = proxyCustomer.authUserId;
                            applicationCustomer.authUserName = proxyCustomer.authUserName;
                            applicationCustomer.type = 'AuthCustomer';//代填
                            //this.authList = result.records;
                            this.applicationCustomerNameData.push(applicationCustomer);
                            if (this.trf.ownerCustomerId) {
                                if (this.trf.ownerCustomerId == applicationCustomer.sgsCustomerId) {
                                    this.trfCustomer.applyUserId = applicationCustomer.sgsAccountId
                                }
                            }
                        }
                    }

                }
            });
        },
        //查询授权公司相关信息
        async getAuthCustomerUserByCustomer(customer) {
            if (!customer.authCustomerId) return;
            this.trfCustomer.customerAddressEn = '';
            this.$set(this.customerForm.applicationCustomer, 'contactName', customer.authUserName);
            this.trfCustomerContact.applyContactName = customer.authUserName;
            //查询授权公司相关地址信息
            this.$set(this.customerForm.applicationCustomer, "customerAddressEn", null);
            await this.queryCustomerAddress(customer.authCustomerId, 0);
            let res = await this.searchBossNoByCustomerId(customer.authCustomerId);
            if (!res) return;
            getUsers({customerNo: res.bossNo, id: customer.authUserId}).then(user => {

                this.customerForm.applicationCustomer.customerId = customer.authCustomerId;
                this.$set(this.customerForm.applicationCustomer, 'customerId', customer.authCustomerId);
                this.trfCustomer.applyUserId = customer.authUserId;
                this.trfCustomer.customerId = customer.authCustomerId;
                if (!user || !user.data || objectIsNull(user.data.rows)) return;
                let contact = user.data.rows[0];
                if (contact.deleteFlag && contact.enableStatus) {
                    this.$set(this.customerForm.applicationCustomer, 'contactTelephone', contact.telephone);
                    this.$set(this.customerForm.applicationCustomer, 'contactEmail', contact.email);
                    this.trfCustomerContact.applyContactTel = contact.telephone;
                    this.applyEmailValue = objectIsNull(contact.email) ? [] : contact.email.split(',');
                    this.trfCustomerContact.applyContactEmail = contact.email;
                }
                this.$emit('enterApplicant', customer);
            });
        },
        async choiceApplicant(customer) {
            if (this.userInfo.companyId == customer.authCustomerId) return;
            this.getAuthCustomerUserByCustomer(customer);
        },

        //查询客户联系人
        async queryCustomerContact(customerId, reloadFlg) {
            if (objectIsNull(customerId)) return [];
            //if(this.trfCustomer.customerId != this.userInfo.companyId) return;
            getCustomerContactList({customerId: customerId, status: 1}).then(res => {
                this.customerContactData = res.data.data ? res.data.data : [];
                if (reloadFlg == 0 || this.actionType === TrfActionTypeEnums.ADD.code || this.actionType === 'material' ||
                    ((this.actionType === TrfActionTypeEnums.COPY.code || this.actionType === TrfActionTypeEnums.TEMPLATE.code))) {
                    let index = this.customerContactData.findIndex(item => item.isDefault === 1);
                    if (index >= 0 && reloadFlg != 1) this.appContactNameChange(this.customerContactData[index].contactName);
                }
            });
        },
        handleDelKey(e, type) {
            let {keyCode, code} = e;
            if(!this.customerForm[type+'Customer'].bossNo){
                return
            }
            if(e.target.value){
                return;
            }
            type == 'supplier' ? this.handleDeleteSupplier(e) : this.handleDeleteManufacture(e);
        },
        handleDeleteSupplier(e) {
            this.$set(this.customerForm.supplierCustomer, "customerId", null);
            this.$set(this.customerForm.supplierCustomer, "bossNo", null);
            this.$set(this.customerForm.supplierCustomer, "customerNameEn", null);
            this.$set(this.customerForm.supplierCustomer, "customerNameZh", null);
            this.$set(this.customerForm.supplierCustomer, "customerAddressEn", null);
            this.$set(this.customerForm.supplierCustomer, "customerAddressZh", null);
            this.$set(this.customerForm.supplierCustomer, 'customerReferenceNo', null);
            this.supplierContactList = []
            this.$set(this.customerForm.supplierCustomer, 'contactName', '');
            this.$set(this.customerForm.supplierCustomer, 'contactTelephone', '');
            this.$set(this.customerForm.supplierCustomer, 'contactEmail', '');
            this.supplierEmailValue = [];
            this.handleBlur(e);
        },
        clearSupplier(e) {
            this.$set(this.customerForm.supplierCustomer, "customerId", null);
            if(this.customerForm.supplierCustomer.bossNo){
                this.handleDeleteSupplier();
            }
        },
        defaultApplicantCustomer(){
            let applicantBossNo = this.customerForm.applicationCustomer.bossNo;
            if(!applicantBossNo){
                return [];
            }
            let {customerNameEn,customerNameZh} = this.customerForm.applicationCustomer;
            let cus = {
                scmCustomerName: customerNameEn || customerNameZh,
                scmCustomerNameEN: customerNameEn || customerNameZh,
                scmCustomerNameCN: customerNameZh || customerNameEn,
                scmCustomerReferenceCode: '',
                scmCustomerNo: applicantBossNo,
                addressDetail: '',
                customerAddressZh: '',
                customerAddressEn: '',
                contactsList:[],
                tier:'T0'
            }
            return [cus];
        },
        async searchSupplierCustomerGroup() {
            this.supplierCustomerNameData = [];
            let buyerBossNo = this.newScmBossNo;
            let buyerIsCurrentLoginUser = buyerBossNo.includes(this.userInfo.bossNo);
            //if trfBuyer != currentUser 使用applicant的数据
            if(!buyerIsCurrentLoginUser){
                this.supplierCustomerNameData = this.defaultApplicantCustomer();
                return;
            }
            let data = null;
            let supplierBossNo = this.userInfo.bossNo;
            if (!supplierBossNo) {
                data = []
            } else {
                let param = {
                    "buyerBossNo": [],
                    "supplierBossNo": this.userInfo.bossNo,
                    "roleType": "SUPPLIER",
                    "companyName": ''
                }
                data = await this.queryScmListByNew(param);
            }
            data = data.length>0?data: this.defaultApplicantCustomer();
            //buyer不是当前登录用户过滤掉其他supplier数据
            this.supplierCustomerNameData = data;
            return data;
        },
        handleDeleteManufacture(e) {
            this.$set(this.customerForm.manufactureCustomer, "customerId", null);
            this.$set(this.customerForm.manufactureCustomer, "bossNo", null);
            this.$set(this.customerForm.manufactureCustomer, "customerNameEn", null);
            this.$set(this.customerForm.manufactureCustomer, "customerNameZh", null);
            this.$set(this.customerForm.manufactureCustomer, "customerAddressEn", null);
            this.$set(this.customerForm.manufactureCustomer, "customerAddressZh", null);
            this.$set(this.customerForm.manufactureCustomer, 'contactName', '');
            this.$set(this.customerForm.manufactureCustomer, 'contactTelephone', '');
            this.$set(this.customerForm.manufactureCustomer, 'contactEmail', '');
            this.$set(this.customerForm.manufactureCustomer, 'customerReferenceNo', null);
            this.manufactureEmailValue = [];
            this.handleBlur(e);
        },
        clearManufacture(e) {
            this.$set(this.customerForm.manufactureCustomer, "customerId", null);
            if(this.customerForm.manufactureCustomer.bossNo){
                this.handleDeleteManufacture();
            }
        },
        clearApplicant(e){
            if(this.supplierCustomerNameData.length==1){
                let {scmCustomerNameEN,scmCustomerNo} = this.supplierCustomerNameData[0];
                let {bossNo,customerNameEn} = this.customerForm.applicationCustomer;
                if(bossNo==scmCustomerNo || customerNameEn==scmCustomerNameEN){
                    this.supplierCustomerNameData.splice(0,1);
                }
            }

            if(!this.customerForm.applicationCustomer.bossNo){
                this.handleBlur(e);
                return
            }
            this.$set(this.customerForm.applicationCustomer, "customerId", null);
            this.$set(this.customerForm.applicationCustomer, "bossNo", null);
            this.$set(this.customerForm.applicationCustomer, "customerNameEn", null);
            this.$set(this.customerForm.applicationCustomer, "customerNameZh", null);
            this.$set(this.customerForm.applicationCustomer, "customerAddressEn", null);
            this.$set(this.customerForm.applicationCustomer, "customerAddressZh", null);
            this.handleBlur(e);
        },
        clearPayer(e){
            if(!this.customerForm.payerCustomer.bossNo){
                this.handleBlur(e);
                return
            }
            this.$set(this.customerForm.payerCustomer, "customerId", null);
            this.$set(this.customerForm.payerCustomer, "bossNo", null);
            this.$set(this.customerForm.payerCustomer, "customerNameEn", null);
            this.$set(this.customerForm.payerCustomer, "customerNameZh", null);
            this.$set(this.customerForm.payerCustomer, "customerAddressEn", null);
            this.$set(this.customerForm.payerCustomer, "customerAddressZh", null);
            this.handleBlur(e);
        },
        async isNewScmFlag() {
            let buyerBossNo = this.trfCustomer.buyerCustomerNo || this.customerForm.buyerCustomer.bossNo || '';
            let customerGroupCode = this.customerForm.buyerCustomer.customerGroupCode || this.trfCustomer.buyerCustomerGroupCode;
            if (!customerGroupCode && !buyerBossNo) {
                return ;
            }
            if(customerGroupCode){
                //优先以groupCode为主
                let resultCustomerData = await getCustomerNotPageList({
                    customerGroupCode
                });
                if (resultCustomerData.status == 200 && resultCustomerData.data.data) {
                    let cus = resultCustomerData.data.data.map(c=>c.bossNo);
                    this.newScmBossNo = cus;
                }
            }else{
                this.newScmBossNo = [buyerBossNo];
            }


        },
        async queryScmListByNew(param) {
            let datas = await queryScmList(param);
            if (!datas || datas.status != 200) {
                return [];
            }
            let result = (datas.data.data || []).map(da => {
                let {
                    scmCustomerNameCN, scmCustomerNameEN, buyerCustomerCode,
                    contactsList, tier,
                    customerNo, companyAddressCN, companyAddressEN
                } = da;
                return {
                    scmCustomerName: scmCustomerNameEN || scmCustomerNameCN,
                    scmCustomerNameEN: scmCustomerNameEN || scmCustomerNameCN,
                    scmCustomerNameCN: scmCustomerNameCN || scmCustomerNameEN,
                    scmCustomerReferenceCode: buyerCustomerCode,
                    scmCustomerNo: customerNo,
                    addressDetail: companyAddressEN || companyAddressCN,
                    customerAddressZh: companyAddressCN,
                    customerAddressEn: companyAddressEN,
                    contactsList,
                    tier
                }
            });
            return result;
        },
        async searchManufactureCustomerGroup() {
            this.manufactureCustomerNameData = [];
            let data = null;
            let buyerBossNo = this.newScmBossNo;
            let supplierBossNo = this.customerForm.supplierCustomer.bossNo || this.userInfo.bossNo;
            if (!buyerBossNo || !supplierBossNo) {
                data = []
            } else {
                let param = {
                    buyerBossNo,
                    supplierBossNo,
                    companyName: '',
                    roleType: "MANUFACTURER",
                }
                data = await this.queryScmListByNew(param);
            }
            this.manufactureCustomerNameData = data;
            return data;
        },
        async queryScmCustomerList(scmCustomerReqItem) {
            if (objectIsNull(this.userInfo.bossNo)) {//当前用户不存在BossNo
                return [];
            }
            this.scmCustomerReqParam.list = [];
            scmCustomerReqItem.buCode = this.productLineCode
            scmCustomerReqItem.customerNo = this.userInfo.bossNo
            this.scmCustomerReqParam.list.push(scmCustomerReqItem)
            let data = await queryScmCustomerList(this.scmCustomerReqParam).then(res => {
                let customerData = res.data.rows;
                if (objectIsNull(customerData)) {
                    customerData = [];
                }
                return customerData;
            });
            if (scmCustomerReqItem.relationshipType === 'manufacturer' || scmCustomerReqItem.relationshipType === 'supplier') {

                this.scmCustomerReqParam.list = [];
                scmCustomerReqItem.buCode = this.productLineCode;
                scmCustomerReqItem.customerNo = this.customerForm.buyerCustomer.bossNo;
                scmCustomerReqItem.relationshipType = scmCustomerReqItem.relationshipType;
                scmCustomerReqItem.groupCode = this.trfCustomer.buyerCustomerGroupCode;
                this.scmCustomerReqParam.list.push(scmCustomerReqItem)
                let result = await queryScmCustomerList(this.scmCustomerReqParam).then(res => {
                    let customerData = res.data.rows;
                    if (objectIsNull(customerData)) {
                        customerData = [];
                    }
                    return customerData;
                });
                //对result里是数据按scmCustomerId 去重
                result.forEach(item => {
                    if (data.findIndex(item1 => item1.scmCustomerId === item.scmCustomerId) < 0) {
                        data.push(item);
                    }
                });
            }
            return data;
        },

        //校验当前授权是否有效
        async validateCurrentAuth() {
            // console.log("授权校验前");
            if (objectIsNull(this.trfCustomer.applyUserId)) return;
            return await validateAuth({
                authUserId: this.trfCustomer.applyUserId,
                applyUserId: this.userInfo.userMgtId,
                authType: ProxyEnums.CREATE_TRF.code
            }).then(res => {
                // console.log("授权校验中");
                let authUser = res.data && res.data.code === 200 ? res.data.data : null;
                return authUser && authUser.deleteFlag && authUser.enableStatus ? true : false;
            });
        },

        packagingCustomerData(source) {
            if (objectIsNull(this.trfCustomer.applyUserId)) this.trfCustomer.applyUserId = this.createUserId;
            source = Object.assign(source, this.trfCustomer);
            return source;
        },

        packagingContactData(source) {
            source = Object.assign(source, this.trfCustomerContact);
            return source;
        },

        openTips() {
            this.$notify({
                title: this.$t('tip'),
                message: this.$t('authorization.authorValid'),
                type: 'warning'
            });
        },
        haseRole(type, role) {
            if (validatenull(type) || validatenull(role)) {
                return false;
            }
            console.log('当前登录用户', this.userInfo)
            if (validatenull(this.userInfo.dimensions)) {
                return false;
            } else {
                if (this.userInfo.dimensions.hasOwnProperty(type)) {
                    if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }
        },
        changeManufactureSupplierContact(cusType,contactList) {
            let {contactName} = this.customerForm[cusType];
            let contact = contactList.find(c=>c.contact==contactName) || {};
            let {contactEmail,contactTelephone} = contact;
            this.$set(this.customerForm[cusType],'contactEmail',contactEmail);
            this.$set(this.customerForm[cusType],'contactTelephone',contactTelephone);
            if(cusType=='manufactureCustomer'){
                this.manufactureEmailValue = [contactEmail];
                this.manufactureEmailValueChange([contactEmail]);
            }
            if(cusType=='supplierCustomer'){
                this.supplierEmailValue = [contactEmail];
                this.supplierEmailValueChange([contactEmail]);
            }

        },
        customerReferenceNoChange() {
        },
        initCustomerExt(customerId) {
            let params = {
                buCode: this.productLineCode,
                customerId
            }
            this.needSupplier = false;
            this.needManufacture = false;
            queryCustomerByBuCodeAndCustomerId(params).then(res => {
                if (res.data && res.data.customerId) {
                    let {customerExtNewList} = res.data;
                    let cust = customerExtNewList.find(c => c.customerId == customerId && c.locationCode == 'CN');
                    if (!cust) {
                        return
                    }
                    let {needSupplier, needManufacture} = cust;
                    console.log("查询supplier的结果：", needSupplier, needManufacture)
                    this.needSupplier = needSupplier == 1;
                    this.needManufacture = needManufacture == 1;
                }
            }, error => {
            }).catch(err => {
            })
        },
        queryCustomerExtByBossNo(bossNo) {
            let customerId = this.customerForm.buyerCustomer.customerId;
            if (customerId) {
                this.initCustomerExt(customerId);
                return;
            }
            if (!bossNo) {
                this.needSupplier = false;
                this.needManufacture = false;
                return;
            }
            let param = {
                number: bossNo
            }
            getSgsCustomer(param).then(res => {
                //console.log("获取到的customerId:",res);
                if (res.data && res.data.rows) {
                    let {rows} = res.data;
                    if (rows && rows.length > 0) {
                        let resCstomerId = rows[0].customerId;
                        this.initCustomerExt(resCstomerId);
                    }
                }

            })
        },
        fieldRequired(usageType, columnName) {
            let dbConfig = this.customerRequireFieldMap[usageType];
            if (!dbConfig) {
                return {required: false};
            }
            return [{
                required: dbConfig[columnName] - 0 == 1,
                message: this.$t('trf.validate.requiredBlur'),
                trigger: 'change'
            }]
        },
        initTemplateCustomerFieldConfig(templateDataObj) {
            let {customerRequireField} = templateDataObj || this.templateDataObj;
            if (!customerRequireField) {
                return
            }
            try {
                let dbConfig = JSON.parse(customerRequireField) || [];
                let customerRequiredFieldMap = {};
                dbConfig.forEach(dbC => {
                    let {usageType} = dbC;
                    customerRequiredFieldMap[usageType] = dbC;
                })
                this.customerRequireFieldMap = customerRequiredFieldMap;
            } catch (e) {

            }
        },
    }
}
</script>

<style lang="scss">
.trf_customer_class {
  .collapse-wrap {
    padding: 0 10px;
  }

  .edit-applicant-btn {
    margin-left: 8px;
  }

  .el-collapse-item__header {
    color: #1B1B1B !important;
  }

  .contact {
    table {
      th {
        padding: 5px;
        text-align: center;
      }

      td {
        padding: 5px 20px;
      }
    }
  }
}
</style>
