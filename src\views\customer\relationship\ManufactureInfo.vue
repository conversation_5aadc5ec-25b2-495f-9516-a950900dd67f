<template>
    <div class="smart_views_customer_relationship_manufacture" id="smart_views_customer_relationship_manufacture">
        <common-table
                border
                v-loading="tableOption.loading"
                ref="manufactureInfoTable"
                style="width:100%"
                row-key="id"
                :size="tableOption.size"
                :data="tableOption.dataList"
                :page="tableOption.page"
                :menu-show="tableOption.menuShow"
                :option="tableOption.option"
                :filters="tableOption.filters"
                @sortBy="changeSort"
                @selection-change="selectionChange"
                @changePage="changePage"
        >
            <template #menuRight>
                <el-button v-if="showLinkBtn" plain type="primary" icon="Plus" @click="handlerAddManufacturer">{{t('scm.linkName.linkManufacture')}}</el-button>
            </template>
            <template #childAssignCode="{row}">
                <div class="table_column_slot">
                    <div class="table_column_left">
                        <span v-if="!row.edit" :class="row.status==0?'strikethrough':''">
                            {{row.childAssignCode}}
                        </span>
                        <el-input :ref="(el)=>setAssignCodeInputRef(el, row.id)"  show-word-limit maxlength="50" size="small" clearable v-model="row.childAssignCode" @blur="updateAssignCode(row)" v-if="row.edit"></el-input>
                    </div>
                    <div class="table_column_right_icon" v-show="!row.edit">
                        <el-icon @click="editAssignCode(row)" v-if="btnRole(row,'Edit')"><EditPen/></el-icon>
                    </div>
                    <div class="table_column_right_icon" v-show="row.edit">
                        <el-icon @click="updateAssignCode(row)" v-if="btnRole(row,'Edit') && !row.saveLoading"><Finished/></el-icon>
                    </div>
                </div>
            </template>
            <template #actionColumn="{row}">
                <div class="action_icon">
                    <el-row :gutter="5">
                        <el-col :span="8" v-if="showLinkBuyer && btnRole(row,'Query')">
                            <el-tooltip placement="top" :content="t('scm.linkName.linkBuyer')">
                                <el-icon  @click="handlerLinkedBuyer(row)">
                                    <Suitcase/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="showContacts && btnRole(row,'Contacts')" class="contact-div">
                            <el-tooltip placement="top" :content="t('scm.contact.title')">
                                <el-icon  class="contacts-user" @click="handlerOpenContacts(row)">
                                    <Avatar />
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="row.status=='1' && btnRole(row,'Disable')">
                            <el-tooltip placement="top" :content="t('scm.btnName.disable')">
                                <el-icon class="icon-disabled disabled-color"  @click="handlerChangeStatus(row,'0')">
                                    <Remove/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="row.status=='0' && btnRole(row,'Enable')" >
                            <el-tooltip placement="top" :content="t('scm.btnName.enable')">
                                <el-icon @click="handlerChangeStatus(row,'1')">
                                    <Remove/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="btnRole(row,'Approve')">
                            <el-tooltip placement="top" :content="t('scm.btnName.approve')">
                                <el-icon  @click="handlerApproveReject(row,'APPROVED')">
                                    <CircleCheck/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="btnRole(row,'Reject')" >
                            <el-tooltip placement="top" :content="t('scm.btnName.reject')">
                                <el-icon @click="handlerApproveReject(row,'REJECTED')">
                                    <CircleClose/>
                                </el-icon>
                            </el-tooltip>
                        </el-col>
                    </el-row>
                </div>
            </template>
        </common-table>
    </div>
    <SearchCustomer
            v-if="addManufacturerDia"
            :title="t('scm.searchManufacture')"
            showCreatedBtn
            :refParentId="parentId"
            roleType="MANUFACTURER"
            systemId="SMART"
            :createdBtnName="t('scm.createManufacture')"
            :queryRelation="onlyQueryRelationCustomer"
            :childCompanyId="childCompanyId"
            @cancelDia="cancelSeachCustomer"
            @saveSuccess="selectCustomer"
    ></SearchCustomer>

    <EditContactDialog
            v-if="openEditContactFlag"
            :companyId="queryCompanyId"
            @cancelDia="cancelEditContact"
    ></EditContactDialog>


    <el-dialog
            v-model="openLinkedBuyer"
            :title="t('scm.linkName.linkBuyer')"
            width="75%"
            lock-scroll
            show-close
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            draggable>
        <LinkedBuyer
                v-if="openLinkedBuyer"
                :parentId="linkBuyerProps.parentId"
                :tier="linkBuyerProps.tier"
                :currentRole="linkBuyerProps.currentRole"
                :targetRole="linkBuyerProps.targetRole"
                :childCompanyId="linkBuyerProps.childCompanyId"
                roleType="MANUFACTURER"
                @cancelDia="cancelLinkedBuyer"
        ></LinkedBuyer>
    </el-dialog>
</template>

<script setup>
import {mapGetters} from 'vuex';
import {reactive, ref, onBeforeMount, onMounted, watch, nextTick} from 'vue';
import CommonTable from "../../../components/TableList/CommonTable.vue";
import LinkedBuyer from "./innerComponent/LinkedBuyer.vue";
import SearchCustomer from './innerComponent/SearchCustomer.vue'
import EditContactDialog from "./innerComponent/EditContactDialog.vue";
import { ElNotification, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
const {t,locale} = useI18n();
import dayjs from 'dayjs';

watch(locale, (newV)=>{
    initDataList();
})

const manufactureInfoTable = ref();
const openLinkedBuyer = ref(false);
const addManufacturerDia = ref(false);
import customerRelationApi from "@/api/customerRelation";

const props = defineProps({
    showLinkBtn:{
        type:Boolean,
        default:true
    },
    parentId: {
        type: String,
        default: ''
    },
    childCompanyId:{
        //作为子组件时，有效，场景为 buyer link时
        type:String,
        default:''
    },
    tier:{
        type:String,
        default:'T1'
    },
    currentRole:{
        type:String,
        default:'SUPPLIER'
    },
    targetRole:{
        type:String,
        default:'MANUFACTURER'
    },
    showLinkBuyer:{
        type:Boolean,
        default:true
    },
    showContacts:{
        type:Boolean,
        default:true
    },
    onlyQueryRelationCustomer:{
        //从buery link 时，只能查询关联关系，不查询全部
        type:Boolean,
        default:false
    }
})

const tableOption = reactive({
    dataList: [],
    originalDataList: [],
    loading: false,
    menuShow: true,
    size: 'small',
    option: {
        hideRowColor: true,
        selection: false,
        selectionDis: false,
        sortSelectionFiledName: '',
        showSortIcon: true,
        index: true,
        action: true,
        actionWidth: 150,
        disableOption:{
            disableNeedStrikethrough:true,
            disableValue:'0',
            disableProp:'status'
        },
        column: [
            {prop: 'companyName', label: 'scm.manufacturerName', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'companyAddress', label: 'scm.address', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'childAssignCode', label:  'scm.assignedCode', hide: false, filter: true, slot: true, type: 'Input'},
            {prop: 'sgsCode', label: 'scm.sgsCode', hide: false, filter: true, slot: false, type: 'Input'},
            {
                prop: 'approvalStatus',
                label: 'scm.approveStatus',
                multiple:true,
                hide: false,
                filter: true,
                slot: false,
                i18n:true,
                dicData: [
                    {label: 'scm.approveStatusEnumName.approve', value: 'APPROVED',tag:true,type:'success'},
                    {label: 'scm.approveStatusEnumName.inProgress', value: 'PENDING',tag:true,type:'warning'},
                    {label: 'scm.approveStatusEnumName.noRequired', value: 'NOT_REQUIRED',tag:true,type:'info'},
                    {label: 'scm.approveStatusEnumName.reject', value: 'REJECTED',tag:true,type:'primary'},
                ],
                type: 'Select'
            },
            {prop: 'updateUserRole', label: 'scm.updateUser', hide: false, filter: true, slot: false, type: 'Input'},
            {prop: 'updateTime', label: 'scm.updateTime', hide: false, filter: true, slot: false, type: 'DateRange', minWidth: 280},
        ]
    },
    sort:{
        sortBy:"",
        sortOrder:""
    },
    filters: {},
    page: {
        show: true,
        size: 1,
        page: 1,
        rows: 20,
        small: true,
        sizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 100
    }
})

const changePage = (page) => {
    tableOption.page.page = page.page;
    tableOption.page.rows = page.rows;
    initDataList();
}
const selectionChange = (val) => {
}
const changeSort=(sort)=>{
    tableOption.sort.sortOrder = sort.prop;
    tableOption.sort.sortBy = sort.orderBy;
    initDataList();
}
const linkBuyerProps = reactive({
    parentId:'',
    tier:'',
    currentRole:'',
    targetRole:'',
    childCompanyId:''
})
const handlerLinkedBuyer = (row)=>{
    let {id,childCompanyId} = row;
    linkBuyerProps.parentId = id;
    linkBuyerProps.tier = 'T2';
    linkBuyerProps.currentRole = props.currentRole;
    linkBuyerProps.targetRole = 'BUYER';
    linkBuyerProps.childCompanyId = childCompanyId;
    openLinkedBuyer.value = true
}
const handlerAddManufacturer = ()=>{
    addManufacturerDia.value = true
}

const cancelLinkedBuyer = ()=>{
    openLinkedBuyer.value = false
}
const selectCustomer = (customer)=>{
    initDataList();
}
const cancelSeachCustomer = ()=>{
    addManufacturerDia.value = false
}

/* --- */
const openEditContactFlag = ref(false);
const queryCompanyId = ref('')
const handlerOpenContacts = (row)=>{
    let {id} = row;
    queryCompanyId.value = id;
    openEditContactFlag.value = true;
}
const cancelEditContact = ()=>{
    queryCompanyId.value ='';
    openEditContactFlag.value=false;
}
const handlerChangeStatus = (row,status)=>{
    let {id} = row;
    customerRelationApi.changeStatus({id,status}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    }).catch(err=>{

    })
}
const handlerApproveReject = (row,approvalStatus)=>{
    let {id} = row;
    customerRelationApi.approveReject({shipId:id,approvalStatus}).then(res=>{
        if(res.status==200){
            ElNotification.success({
                message: t('success'),
                duration: 2000
            });
            initDataList();
        }
    })
}
const assignCodeInputRefs = ref({});
const setAssignCodeInputRef = (el,id)=>{
    assignCodeInputRefs.value[id] = el;
}
const editAssignCode = (row)=>{
    row.edit=true
    let {id} = row;
    nextTick(()=>{
        assignCodeInputRefs.value[id].focus();
    })
}
const updateAssignCode = (row)=>{
    let {id,childAssignCode,oldChildAssignCode} = row;
    if(oldChildAssignCode == childAssignCode){
        row.saveLoading=false;
        row.edit = false;
        return;
    }
    row.saveLoading = true;
    customerRelationApi.changAssignCode({id,childAssignCode}).then(res=>{
        row.saveLoading=false;
        row.edit = false;
        row.oldChildAssignCode = childAssignCode;
    }).catch(err=>{
        row.saveLoading=false;
    })
}

watch(()=>tableOption.filters,()=>{
    initDataList();
},{deep:true});

const initDataList = ()=>{
    let startUpdateTime = '';
    let endUpdateTime = '';
    if (tableOption.filters.updateTime && tableOption.filters.updateTime.length === 2) {
        startUpdateTime = dayjs(tableOption.filters.updateTime[0]).format('YYYY-MM-DD');
        endUpdateTime = dayjs(tableOption.filters.updateTime[1]).format('YYYY-MM-DD');
    }
    let param = {
        id: props.parentId,
        "currentRole": props.currentRole,
        "targetRole": props.targetRole,
        "tier": props.tier,
        ...tableOption.sort,
        ...tableOption.filters,
        current:tableOption.page.page,
        size:tableOption.page.rows,
        startUpdateTime,
        endUpdateTime
    }
    //tableOption.loading=true;
    customerRelationApi.querySCM(param).then(res=>{
        //tableOption.loading=false;
        if(res.status==200 && res.data){
            let {records,total} = res.data;
            records.forEach(re=>{
                re['oldChildAssignCode'] = re.childAssignCode;
            })
            tableOption.dataList = records;
            tableOption.page.total = total;
        }
    }).catch(err=>{

    })
}
const btnRole = ({permissions},code)=>{
    return (permissions || []).includes(code);
}

onBeforeMount(() => {
    initDataList();
})

</script>

<style lang="scss" scoped>
.smart_views_customer_relationship_manufacture {
}
</style>