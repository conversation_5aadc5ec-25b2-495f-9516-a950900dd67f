<template>
    <basic-container>
        <el-row>
            <br>
            <el-col :span="20">
                <el-form :model="form" size="small" :rules="rules" ref="ruleForm" label-width="150px"
                         v-if="isShowWechatPush">
                    <el-form-item>
                        <el-checkbox :label="1" v-model="isWechatPushFlag">{{$t('notifaction.wechat.isWechatPush')}}
                        </el-checkbox>

                    </el-form-item>
                    <el-form-item style="float:right;margin-right: 20px">
                        <el-button type="primary" @click="updateWechatPush">{{$t('operation.save')}}</el-button>
                    </el-form-item>
                </el-form>
                <h4 style="margin-left: 240px;color: red; font-size: 14px; font-weight: normal;" v-else>{{$t('notifaction.wechat.noBindWechat')}} </h4>
            </el-col>
        </el-row>
    </basic-container>
</template>

<script>

    import {queryWechatBinding, updateWechatPush} from "@/api/system/user";
    import {mapGetters, mapState} from "vuex";
    import {validatenull} from "../../util/validate";

    export default {
        name: "wechatPush",
        data() {
            return {
                //wechatPush
                isWechatPushFlag: false,
                isShowWechatPush: true,
                form: {},
                rules: {}
            };
        },
        created() {
            //查询当前用户是否已开发微推送通知
            this.onLoad();
        },
        computed: {
            ...mapGetters([
                "userInfo"
            ])
        },
        watch: {
            isWechatPushFlag: function (newVal) {
                this.$set(this.form, 'wechatPush', 0);
                if (newVal) {
                    this.$set(this.form, 'wechatPush', 1);
                }
            },
        },
        methods: {
            onLoad() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading…',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)',
                })
                queryWechatBinding().then(res => {
                     loading.close();
                    debugger;
                    const data = res.data.data;
                    if (!validatenull(data)) {
                        this.isShowWechatPush = true;

                        if (data.wechatPush == 1 || data.wechatPush == '1') {
                            this.isWechatPushFlag = true;
                        } else {
                            this.isWechatPushFlag = false;
                        }
                        this.$set(this.form, 'wechatPush', data.wechatPush);
                    } else {
                        this.isShowWechatPush = false;
                        this.$message({
                            type: "error",
                            message: this.$t('notifaction.wechat.noBindWechat')
                        });
                    }
                }).catch(() => {
                    loading.close();
                });
            },
            updateWechatPush() {
                updateWechatPush(this.form).then(res => {
                    if (res.data.success) {
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                    } else {
                        this.$message({
                            type: "error",
                            message: res.data.msg
                        });
                    }
                })

            },
        }
    };
</script>

<style scoped>

</style>
