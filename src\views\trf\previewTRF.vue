<template>
    <div class="previewTRF" id="previewTRF">
        <div>
            <div id="barCodeHtmlView" style="margin-right: 5%;margin-left: 5%;margin-top: 40px">
                <section id="report-logo" class="minimum-unit" style="margin: 1px 1px;">
                    <div style="height: 100px;">
                        <div style="left: 5px">
                            <img v-if="isNotNull('isShowSgsLogo')"
                                 style="padding: 0px 5px;height: 80px" :src="data.logo"/>
                            <img v-if="isNotNull('buyerLogo')" style="float:right;padding: 0px 5px; height: 80px;"
                                 :src="data.buyerLogo"/>
                            <img style="float:right;height: 80px;" :src="data.barCode"/>
                        </div>
                    </div>
                    <div style="left: 5px">
                        <h2 style="font-size: 30px">{{data.templateName}}</h2>
                    </div>
                </section>
                <!-- 汇总统计信息 start -->
                <section id="report-basic" class="count-info minimum-unit">
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.serviceType')}}:</b><u>{{data.serviceTypeName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.trfNo')}}:</b><u>{{data.customerReferenceNo || data.trfNo}}</u></td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.buyerName')}}:</b><u>{{data.buyerCustomerGroupName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.labName')}}:</b><u>{{data.labName}}</u></td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.agent')}}:</b><u>{{data.agentCustomerGroupName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.labAddress')}}:</b><u>{{data.labAddress}}</u></td>
                        </tr>
                        <tr v-if="data.labContact && data.labContact.trim().length>0">
                            <td style="text-align: left"><b>{{$t('trfPrint.general.labContact')}}:</b><u>{{data.labContact}}</u></td>
                            <td style="text-align: left"></td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.general.selfReference')}}:</b><u>{{data.isSelfRefrence}}</u></td>
                            <td style="text-align: left" v-if="data.eFiling"><b>{{$t('trfPrint.general.isEfilling')}}:</b><u>{{data.eFiling}}</u></td>
                        </tr>
                    </table>
                </section>
            </div>
            <!--汇总统计信息 end-->
            <div style="margin-right: 5%;margin-left: 5%;">
                <!-- 明细 start -->
                <section id="report-customer" class="detail minimum-unit">
                    <!--<h4>APPLICANT INFORMATION</h4>-->
                    <span style="font-size: 18px"><b>{{$t('trfPrint.customerInformation.applicant')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td colspan="2" style="text-align: left"><b>
                                {{$t('trfPrint.customerInformation.sgsCustomerNo')}}: </b><u>{{data.applicantCustomerNo}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.applicantCustomerCode')}}: </b><u>{{data.applicantCustomerReferenceNo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyNameEn')}}: </b><u>{{data.applicantCustomerNameEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyNameLocal')}}: </b><u>{{data.applicantCustomerNameCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressEn')}}: </b><u>{{data.applicantCustomerAddressEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressLocal')}}: </b><u>
                                {{data.applicantCustomerAddressCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.contact')}}: </b><u>{{data.applicantContactName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.phone')}}: </b><u>{{data.applicantContactTelephone}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.email')}}: </b><u>{{data.applicantContactEmail}}</u>
                            </td>
                        </tr>
                    </table>
                </section>
                <section id="report-invoice" class="detail minimum-unit">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.customerInformation.payer')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.sgsCustomerNo')}}: </b><u>{{data.payerCustomerNo}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.payerCustomerCode')}}: </b><u>{{data.payerCustomerReferenceNo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameEn')}}: </b><u>{{data.payerCustomerNameEn}}</u></td>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameLocal')}}: </b><u>{{data.payerCustomerNameCn}}</u>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyAddressEn')}}: </b><u>{{data.payerCustomerAddressEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressLocal')}}: </b><u>{{data.payerCustomerAddressCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.contact')}}: </b><u>{{data.payerContactName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.phone')}}: </b><u>{{data.payerContactTelephone}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.email')}}: </b><u>{{data.payerContactEmail}}</u>
                            </td>
                        </tr>
                    </table>
                </section>


                <!--buyer-->
                <section v-if="data.isShowBuyer-0!=0" id="report-buyer" class="detail minimum-unit">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.customerInformation.buyer')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.sgsCustomerNo')}}: </b><u>{{data.buyerCustomerNo}}</u>
                            </td>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.buyerCustomerCode')}}: </b><u>{{data.buyerCustomerReferenceNo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameEn')}}: </b><u>{{data.buyerCustomerNameEn}}</u></td>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameLocal')}}: </b><u>{{data.buyerCustomerNameCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyAddressEn')}}: </b><u>{{data.buyerCustomerAddressEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressLocal')}}: </b><u>{{data.buyerCustomerAddressCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.contact')}}: </b><u>{{data.buyerContactName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.phone')}}: </b><u>{{data.buyerContactTelephone}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.email')}}: </b><u>{{data.buyerContactEmail}}</u>
                            </td>
                        </tr>
                    </table>
                </section>

                <!--agent-->
                <section v-if="data.isShowAgent-0!=0" id="report-agent" class="detail minimum-unit">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.customerInformation.agent')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.sgsCustomerNo')}}: </b><u>{{data.agentCustomerNo}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.agentCustomerCode')}}: </b><u>{{data.agentCustomerReferenceNo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameEn')}}: </b><u>{{data.agentCustomerNameEn}}</u></td>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameLocal')}}: </b><u>{{data.agentCustomerNameCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyAddressEn')}}: </b><u>{{data.agentCustomerAddressEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressLocal')}}: </b><u>{{data.agentCustomerAddressCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.contact')}}: </b><u>{{data.agentContactName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.phone')}}: </b><u>{{data.agentContactTelephone}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.email')}}: </b><u>{{data.agentContactEmail}}</u>
                            </td>
                        </tr>
                    </table>
                </section>
                <!--supplier-->
                <section v-if="data.isShowSupplier-0!=0" id="report-supplier" class="detail minimum-unit">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.customerInformation.supplier')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.sgsCustomerNo')}}: </b>
                                <u>{{data.supplierCustomerNo}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.supplierCustomerCode')}}: </b><u>{{data.supplierCustomerReferenceNo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.companyNameEn')}}: </b><u>{{data.supplierCustomerNameEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyNameLocal')}}: </b><u>{{data.supplierCustomerNameCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressEn')}}: </b><u>{{data.supplierCustomerAddressEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressLocal')}}: </b><u>{{data.supplierCustomerAddressCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.contact')}}: </b><u>{{data.supplierContactName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.phone')}}: </b><u>{{data.supplierContactTelephone}}</u></td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.email')}}: </b><u>{{data.supplierContactEmail}}</u>
                            </td>
                        </tr>
                    </table>
                </section>
                <!--manufacture-->
                <section v-if="data.isShowManufacture-0!=0" id="report-manufacture" class="detail minimum-unit">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.customerInformation.manufacture')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.sgsCustomerNo')}}: </b><u>{{data.manufactureCustomerNo}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.manufactureCustomerCode')}}: </b><u>{{data.manufactureCustomerReferenceNo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyNameEn')}}: </b><u>{{data.manufactureCustomerNameEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyNameLocal')}}: </b><u>{{data.manufactureCustomerNameCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressEn')}}: </b><u>{{data.manufactureCustomerAddressEn}}</u>
                            </td>
                            <td colspan="2" style="text-align: left"><b>{{$t('trfPrint.customerInformation.companyAddressLocal')}}: </b><u>{{data.manufactureCustomerAddressCn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.contact')}}: </b><u>{{data.manufactureContactName}}</u></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.customerInformation.phone')}}: </b><u>{{data.manufactureContactTelephone}}</u></td>
                            <td colspan="2" style="text-align: left">
                                <b>{{$t('trfPrint.customerInformation.email')}}: </b><u>{{data.manufactureContactEmail}}</u></td>
                        </tr>
                    </table>
                </section>

                <!--Service required-->
                <section id="report-other" class="detail minimum-unit">
                    <!--<h4>APPLICANT INFORMATION</h4>-->
                    <span style="font-size: 18px"><b>{{$t('trfPrint.otherRequest.title')}}</b></span>
                    <table style="border:1px solid #050505;">
                        <tr v-if="isNotNull('isShowReportZh')">
                            <td style="text-align: left"><b>{{$t('trfPrint.otherRequest.reportHeaderZh')}}: </b>
                                <u v-if="isNotNull('reportHeader')">{{data.reportHeader}}</u>
                            </td>
                            <td style="text-align: left"><b>{{$t('trfPrint.otherRequest.reportAddressZh')}}: </b>
                                <u v-if="isNotNull('reportAddress')">{{data.reportAddress}}</u>
                            </td>
                        </tr>
                        <tr v-if="isNotNull('isShowReportEn')">
                            <td style="text-align: left"><b>{{$t('trfPrint.otherRequest.reportHeaderEn')}}: </b>
                                <u v-if="isNotNull('reportHeaderEn')">{{data.reportHeaderEn}}</u>
                            </td>
                            <td style="text-align: left"><b>{{$t('trfPrint.otherRequest.reportAddressEn')}}: </b>
                                <u v-if="isNotNull('reportAddressEn')">{{data.reportAddressEn}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td v-if="isNotNull('productLineCode','HL')" style="text-align: left">
                                <b>{{$t('trfPrint.otherRequest.reportDeliveredTo')}}: </b>
                                <u v-if="isNotNull('reportDeliveredTo')">{{data.reportDeliveredTo}}</u>
                            </td>
                            <td v-if="isNotNull('productLineCode','HL')" style="text-align: left">
                                <b>{{$t('trfPrint.otherRequest.failedReportDeliveredTo')}}: </b>
                                <u v-if="isNotNull('failedReportDeliveredTo')">{{data.failedReportDeliveredTo}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.otherRequest.reportLanguage')}}: </b>
                                <u v-if="isNotNull('reportLanguage')">{{data.reportLanguage}}</u>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.otherRequest.additionalRequest')}}:</b>
                                <u v-if="isNotNull('otherRequire')">{{data.otherRequire}}</u>
                            </td>
                        </tr>

                        <template v-if="isNotNull('isGeneralRequest')">
                            <template v-if="isEq('productLineCode','SL')">
                                <tr>                   
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.isCommon')}}: </b><u>{{data.isCommon}}</u></td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.returnSampleRequire')}}: </b>
                                        <u v-if="isNotNull('returnSampleRequire')">{{data.returnSampleRequire}}</u>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.judgmentRule')}}: </b>
                                        <u v-if="isNotNull('judgmentRuleStr')">{{data.judgmentRuleStr}}</u>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.reportSymbols')}}: </b><u>{{data.accreditation}}</u></td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.isCopyName')}}: </b><u>{{data.isCopyName}}</u></td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.photo')}}: </b><u>{{data.isPhoto}}</u></td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.isConfimCover')}}: </b><u>{{data.isConfimCover}}</u></td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.isQuotation')}}: </b><u>{{data.isQuotation}}</u></td>
                                </tr>
                            </template>
                            <template v-if="isEq('productLineCode','HL')">
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.softCopyContactName')}}: </b><u>{{data.softCopyContactName}}</u>
                                    </td>
                                    <td style="text-align: left"></td>

                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.vatType')}}: </b><u>{{data.vatType}}</u></td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.needProformaInvoice')}}: </b><u>{{data.needProformaInvoice}}</u></td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.invoiceDeliverWay')}}: </b><u>{{data.invoiceDeliverWay}}</u></td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.invoiceContactName')}}: </b><u>{{data.invoiceContactName}}</u></td>
                                </tr>

                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.returnSampleRequest')}}: </b>
                                        <u v-if="isNotNull('returnSampleRequire')">{{data.returnSampleRequire}}</u>
                                    </td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.returnSampleContactName')}}: </b>
                                        <u>{{data.returnSampleContactName}}</u>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.returnSampleDeliverWay')}}: </b><u>{{data.returnSampleDeliverWay}}</u>
                                    </td>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.liquid')}}: </b><u>{{data.liquid}}</u></td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.isOutside')}}: </b><u>{{data.isOutside}}</u>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align: left"><b>{{$t('trfPrint.returnSample.isLegalProceeding')}}: </b><u>{{data.isLegalProceeding}}</u>
                                    </td>
                                </tr>
                            </template>
                        </template>
                    </table>
                </section>
                <!-- 动态表单-->
                <section id="form-design" class="detail minimum-unit" v-if="formTemplate.formType==2">
                    <div style="border: 1px solid rgb(5, 5, 5);padding:5px 12px;">
                        <sgs-form-design-render
                                v-if="formTemplate.formCode"
                                id="VFormDesignrender"
                                ref="VFormDesignrender"
                                :form-obj="data.formDesignData"
                                disabled
                                :form-id="formTemplate.formDesignId"
                                :language="language=='zh-CN'?2:1"
                                :only-display-always="!(this.role.isSGS || this.role.isThirdPartyLab)"
                        ></sgs-form-design-render>
                    </div>
                </section>

                <!-- DFF 信息-->
                <div v-if="formTemplate.formType!=2">
                    <section id="report-product" class="detail minimum-unit">
                        <template v-if="isSizeGt0('formListName')">
                            <span style="font-size: 18px"><b>{{$t('trfPrint.dff.formTitle')}}</b></span>
                            <table style="border:1px solid #050505;">
                                <tr v-for="item in data.formListName">
                                    <td style="text-align: left">
                                        <b>{{item.left.displayName}}</b><u v-html="item.left.labelValue"></u></td>
                                    <td style="text-align: left">
                                        <b>{{item.right.displayName}}</b><u v-html="item.right.labelValue"></u>
                                    </td>
                                </tr>
                            </table>
                        </template>
                    </section>
                    <section id="report-sample" class="detail minimum-unit">
                        <template v-if="isSizeGt0('carelabs')">
                            <span style="font-size: 18px"><b>{{$t('trfPrint.dff.gridTitle')}}</b></span>
                            <table border="1" cellspacing="0" cellpadding="0">
                                <thead>
                                <tr>
                                    <th style="text-align: center"><b>{{$t('trfPrint.dff.itemId')}}</b></th>
                                    <th v-for="(item,index) in data.carelabs" :key="index" style="text-align: center">
                                        <b v-html='(item.dispalyName|| "").replace("\"","\\\"")'></b>
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <template v-if="isSizeGt0ByObj('order.productInstance.productInstanceVos')">
                                    <tr v-for="(item,index) in data.order.productInstance.productInstanceVos" :key="index">
                                        <td style="word-wrap:break-word;">{{item.productItemNo}}</td>
                                        <td v-if="isSizeGt0('carelabs')" v-for="(childrenItem,ind) in data.carelabs" style="word-wrap:break-word;">
                                            {{getFirstUncapCodeValue(item,childrenItem.fieldCode)}}
                                        </td>
                                    </tr>
                                </template>
                                <tr v-if="!isSizeGt0ByObj('order.productInstance.productInstanceVos')">
                                    <td v-for="i in 7" :key="'empty_td'+i"></td>
                                </tr>
                                </tbody>
                            </table>
                        </template>
                    </section>
                    <section id="report-carelabel" class="detail minimum-unit" v-if="isSizeGt0('careLabelList')">
                        <span style="font-size: 18px"><b>{{$t('trfPrint.dff.carelabelTitle')}}</b></span>
                        <table border="1" cellspacing="0" cellpadding="0">
                            <tr>
                                <td style="text-align: left"><b>{{$t('trfPrint.dff.carelabelItemId')}}</b></td>
                                <td style="text-align: left"><b>{{$t('trfPrint.dff.carelabelCareWording')}}</b></td>
                                <td style="text-align: left"><b>{{$t('trfPrint.dff.carelabelSymbols')}}</b></td>
                            </tr>
                            <tr v-for="(item,clindex) in data.careLabelList" :key="'carelabel_'+clindex">
                                <td style="text-align: left">{{item.itemIdValue}}</td>
                                <td style="text-align: left">{{item.careLabelInstruction}}</td>
                                <td style="text-align: left"><img v-if="item.imgUrl" :src='item.imgUrl'/></td>
                            </tr>
                        </table>
                    </section>
                </div>

                <section id="report-testpackage" class=" minimum-unit" style="display:block;margin: 1px 1px;">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.testRequest')}}</b></span>
                    <div v-if="isSizeGt0('testpackages')">
                        <table border="1" cellspacing="0" cellpadding="0" style="width: 100%;">
                            <tbody>
                                <template v-for="pItem in chunk(data.testpackages,2)">
                                    <tr style="align:left;vertical-align: top">
                                        <template v-for="cItem in pItem">
                                            <td :colspan="pItem.length==1?2:1" style="border-left: none;border-right: none;word-wrap:break-word;">
                                                {{cItem.testPackageName}}
                                                <preview-t-r-f-test-request-item
                                                        v-if="cItem.children && cItem.children.length>0"
                                                        :data="cItem.children"
                                                ></preview-t-r-f-test-request-item>
                                            </td>
                                        </template>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <table v-if="!isSizeGt0('testpackages')" border="1" cellspacing="0" cellpadding="0">
                        <tr height="70px">
                            <td style="text-align: left" valign="top"><b>{{$t('trfPrint.noData')}} </b></td>
                        </tr>
                    </table>
                </section>
                <!--Trf 附件信息-->
                <section id="trf-attachment" class="detail minimum-unit" v-if="isSizeGt0('trfAttachments')">
                    <span style="font-size: 18px"><b>{{$t('trfPrint.attach.info')}}</b></span>
                    <table border="1" cellspacing="0" cellpadding="0" style="width: 40%">
                        <tr>
                            <td style="text-align: left"><b>{{$t('trfPrint.attach.num')}}</b></td>
                            <td style="text-align: left"><b>{{$t('trfPrint.attach.fileName')}}</b></td>
                        </tr>
                        <tr v-for="(item,indexAtt) in data.trfAttachments">
                            <td style="text-align: left">{{indexAtt+1}}</td>
                            <td style="text-align: left">{{item.fileName}}</td>
                        </tr>
                    </table>
                </section>

                <section id="report-remark" class="detail minimum-unit">
            <span style="font-size: 18px"><b>{{$t('trfPrint.remark')}}:</b>
               </span>
                    <table border="1" cellspacing="0" cellpadding="0">
                        <tr height="70px">
                            <td style="text-align: left" valign="top"><b>{{$t('trfPrint.remark')}}:</b>
                                <span class="inline">{{data.remark}}</span>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left">
                                <b>{{$t('trfPrint.declaration1')}} : </b>
                                {{$t('trfPrint.declaration2')}}
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left">
                                {{$t('trfPrint.availability')}}
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: left">
                                {{$t('trfPrint.website')}}
                                <a target="_blank" style="color: #ff6600;cursor: pointer" href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
                            </td>
                        </tr>
                    </table>
                </section>
                <section v-if="isSizeGt0('specificConfigs')">
                    <br/>
                    <section v-for="(item,indexSpec) in data.specificConfigs" id="trf-specific-extend-cn">
                        <span style="font-size: 18px"><b>{{item.specificConfigName}}</b></span>
                        <div v-html="item.specificContent"></div>
                    </section>
                </section>
                <section id="print-time" class="detail minimum-unit">
                    <span id="printDate_span" style="float: left;margin-top: 20px;"></span>
                    <span style="float: right;margin-top: 20px;">
                        <b>{{$t('trfPrint.sign')}}</b>：________________________________
                    </span>
                </section>
            </div>
        </div>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {validatenull} from "@/util/validate";
    import PreviewTRFTestRequestItem from "./previewTRFTestRequestItem";
    import _ from 'lodash';

    export default {
        name: "previewTRF",
        inject:['loadFinish'],
        data() {
            return {}
        },
        methods: {
            chunk(array,chunkSize){
                let result = [];
                for (let i = 0; i < array.length; i += chunkSize) {
                    result.push(array.slice(i, i + chunkSize));
                }
                return result;
            },
            isSizeGt0ByObj(objs) {
                if(!objs){
                    return false;
                }
                let fields = objs.split(".");
                if(!fields || fields.length==0){
                    return false;
                }
                let data = JSON.parse(JSON.stringify(this.data));
                let isNoneData = false;
                for(let i =0;i<(fields || []).length;i++){
                    data = data[fields[i]];
                    if(data == undefined || data==null){
                        isNoneData = true;
                        return
                    }
                }
                if(isNoneData){
                    return false;
                }
                return data && data.length > 0;
            },
            isSizeGt0(field) {
                return this.data[field] && (this.data[field] || []).length > 0;
            },
            isNotNull(field) {
                return this.data[field] && (this.data[field] + '').trim().length > 0
            },
            notEq(field, matchVal, isNumber) {
                return !this.isEq(field, matchVal, isNumber);
            },
            isEq(field, matchVal, isNumber) {
                if (isNumber) {
                    return this.data[field] - 0 == matchVal;
                }
                return this.data[field] == matchVal;
            },
            getFirstUncapCodeValue(item, fieldCode) {
                let code = _.lowerFirst(fieldCode)
                return item[code];
            },
            /* --------- 转换动态表单 start---------------*/
            removeIcon(formDom){
                let iss = formDom.querySelectorAll("i");
                let numberD = formDom.querySelectorAll("span.el-input-number__decrease");
                let numberI = formDom.querySelectorAll("span.el-input-number__increase");
                let tips = formDom.querySelectorAll("div[id^='sgs_tips']");
                (iss || []).forEach(i=>{i.remove()});
                (numberD || []).forEach(i=>{i.remove()});
                (numberI || []).forEach(i=>{i.remove()});
                (tips || []).forEach(i=>{i.remove()});
            },
            parseNormalInput(formDom){
                let inputs = formDom.querySelectorAll("input:not(.avue-dynamic input)");
                inputs.forEach(input=>{
                    let inputType = input.getAttribute("type");
                    let parentClassList = input.parentNode.classList;
                    let isDate = false,isSelect2 = false;
                    parentClassList.forEach(c=>{
                        if(isDate){
                            return
                        }
                        isDate = 'el-date-editor'==c;
                    })
                    //判断是否是select2
                    isSelect2 =  input.parentNode && input.parentNode.parentNode && !!input.parentNode.parentNode.querySelector("div.el-select__tags");

                    let inputVal = input.value;
                    if("checkbox"==inputType){
                        let checkBoxParent = input.closest('div[role="group"]');
                        if(!checkBoxParent){
                            return;
                        }
                        if(!checkBoxParent.parentNode){
                            return;
                        }
                        let checkBoxs = checkBoxParent.querySelectorAll('label.is-checked');


                       /* let checkBoxSpans = checkBoxParent.querySelectorAll('label.is-checked span.el-checkbox__label');
                        let val = [];
                        (checkBoxSpans || []).forEach(span=>{
                            let v = (span.innerText || '').trim();
                            if(v){
                                val.push(v);
                            }
                        })
                        inputVal = val.join(',');*/
                        let div = document.createElement("div");
                        div.className="trans_input_span";
                        div.style.textAlign='left';
                        div.style.marginTop="2px";
                        (checkBoxs || []).forEach(chbox=>{
                            div.appendChild(chbox);
                        })

                       /* let u = `<u>${inputVal}</u>`
                        div.innerHTML = u;*/
                        //删除当前的div元素
                        checkBoxParent.parentNode.appendChild(div);
                        checkBoxParent.remove();
                        return;
                    }
                    if("radio" ==inputType){
                        let radioParent = input.closest('div[role="radiogroup"]');
                        if(!radioParent){
                            return;
                        }
                        if(!radioParent.parentNode){
                            return;
                        }
                        let checkRadioSpan = radioParent.querySelector('label[aria-checked="true"] span.el-radio__label');
                        inputVal = checkRadioSpan ? (checkRadioSpan.innerText || '').trim() : '';
                        let div = document.createElement("div");
                        div.className="trans_input_span";
                        div.style.textAlign='left';
                        div.style.marginTop="2px";
                        let u = `<u>${inputVal}</u>`
                        div.innerHTML = u;
                        //删除当前的div元素
                        radioParent.parentNode.appendChild(div);
                        radioParent.remove();
                        return;
                    }
                    if(inputType==null && isDate){
                        //这一个判断是因为日期区间是两个input，第一个input已经解析了数据，然后删除了父节点，第二个input就不存在了
                        if(!input.parentNode.parentNode){
                            return;
                        }
                        let hasDateDiv = input.parentNode.parentNode.querySelector("div.trans_input_span");
                        if(hasDateDiv){
                            return;
                        }
                        let dateRangeInputs =  input.parentNode.querySelectorAll('input.el-range-input');
                        let dateVal = [];
                        (dateRangeInputs || []).forEach(da=>{
                            if(da.value){
                                dateVal.push(da.value);
                            }
                        })
                        inputVal = dateVal.join("-");
                    }
                    if(isSelect2){
                        if(!input.parentNode || !input.parentNode.parentNode){
                            return;
                        }
                        let select2Vals = input.parentNode.parentNode.querySelectorAll("span.el-select__tags-text");
                        let val = [];
                        (select2Vals || []).forEach(span=>{
                            let v = (span.innerText || '').trim();
                            if(v){
                                val.push(v);
                            }
                        })
                        inputVal = val.join(",");
                        let div = document.createElement("div");
                        div.className="trans_input_span";
                        div.style.textAlign='left';
                        div.style.marginTop="2px";
                        let u = `<u>${inputVal}</u>`
                        div.innerHTML = u;
                        //删除除了新增的div之外的所有子元素
                        input.parentNode.parentNode.appendChild(div);
                        let childrens = input.parentNode.parentNode.children;
                        for(let i = 0;i<childrens.length;i++){
                            if(div==childrens[i]){
                                continue;
                            }
                            childrens[i].remove();
                        }
                        input.style.display = 'none';
                        return;
                    }
                    if(!input.parentNode || !input.parentNode.parentNode){
                        return;
                    }
                    let div = document.createElement("div");
                    div.className="trans_input_span";
                    div.style.textAlign='left';
                    div.style.marginTop="2px";
                    let u = `<u>${inputVal}</u>`
                    div.innerHTML = u;
                    input.parentNode.parentNode.appendChild(div);
                    input.parentNode.remove();
                    input.style.display = 'none';
                })
                let textareas = formDom.querySelectorAll("textarea:not(.avue-dynamic textarea)");
                textareas.forEach(ta=>{
                    let div = document.createElement("div");
                    div.className="trans_input_span";
                    div.style.textAlign='left';
                    let u = `<u>${ta.value}</u>`
                    div.innerHTML = u;
                    ta.parentNode.appendChild(div);
                    ta.style.display = 'none';
                })
            },
            parseDynamicTable(formDom){
                let avueDynamics = formDom.querySelectorAll(".avue-dynamic");
                (avueDynamics || []).forEach(avueDynamic=>{
                    let table = avueDynamic.querySelector("table.el-table__header");
                    let tr = table.querySelector("thead tr");
                    let ths = tr.querySelectorAll("th.el-table__cell.is-left,th.el-table__cell.is-center,th.el-table__cell.is-right");
                    let thArr = [];
                    (ths || []).forEach((th,index)=>{
                        if(index==0){
                            return;
                        }
                        let text = th.innerText;
                        //获取子表单的头部信息
                        thArr.push(text);
                    })
                    //获取内容信息
                    let tableContent = avueDynamic.querySelector("table.el-table__body");
                    let trContents = tableContent.querySelectorAll("tbody tr");
                    let contentArr = [];
                    (trContents || []).forEach((tr)=>{
                        let tds = tr.querySelectorAll("td.el-table__cell.is-left,td.el-table__cell.is-center,td.el-table__cell.is-right");
                        let trArr = [];
                        (tds || []).forEach((td,index)=>{
                            if(index==0){
                                return;
                            }
                            //解析不同类型的input 获取对应的值
                            let valueText = this.parseTdValue(td);
                            let tdContent = valueText;
                            trArr.push(tdContent);
                        })
                        contentArr.push(trArr);
                    })
                    //在原始位置，绘制一个table
                    let tableHtml = '<table class="trans_dynamic_table">';
                    //tr thead
                    let trHtml = '<tr>';
                    thArr.forEach(th=>{
                        trHtml+= `<th style="border:solid 1px">${th}</th>`
                    })
                    trHtml += '</tr>'

                    //具体内容 二位数组
                    let trTdHtml = '';
                    contentArr.forEach(tr=>{
                        let trContentHtml = '<tr>';
                        tr.forEach(td=>{
                            trContentHtml+=`<td style="border:solid 1px; word-wrap: break-word;white-space: pre-line;word-break: break-word;">${td}</td>`
                        })
                        trContentHtml += '</tr>';
                        trTdHtml+=trContentHtml;
                    })
                    tableHtml+=trHtml;
                    tableHtml+=trTdHtml;
                    tableHtml+='</table>';
                    avueDynamic.parentNode.parentNode.insertAdjacentHTML('beforebegin',tableHtml);
                    avueDynamic.style.display='none';
                })
            },
            transInputToSpan(){
                if(this.formTemplate.formType!=2){
                    this.loadFinish();
                    return
                }
                setTimeout(()=>{
                    let interVal = setInterval(()=>{
                        let formDom = document.querySelector("#VFormDesignrender .avue-form");
                        if(!formDom){
                            return
                        }
                        clearInterval(interVal);
                        this.removeIcon(formDom);
                        this.parseNormalInput(formDom);
                        this.parseDynamicTable(formDom);
                        this.loadFinish();
                    },1000)
                },1000)
            },
            parseTdValue(td){
                let input = td.querySelector('input');
                let textarea = td.querySelector('textarea');
                let ellipsis = td.querySelector('span.avue-text-ellipsis__text');
                if(ellipsis){
                    return (ellipsis.innerText || '').trim();
                }
                if(input){
                    let inputType = input.getAttribute("type");
                    if(inputType=='text'){
                        //继续判断是否是多选下拉框
                        if(!input.parentNode || !input.parentNode.parentNode){
                            return input.value;
                        }
                        let isSelect2 = input.parentNode && input.parentNode.parentNode && !!input.parentNode.parentNode.querySelector("div.el-select__tags");
                        if(isSelect2){
                            let select2Vals = input.parentNode.parentNode.querySelectorAll("span.el-select__tags-text");
                            let val = [];
                            (select2Vals || []).forEach(span=>{
                                let v = (span.innerText || '').trim();
                                if(v){
                                    val.push(v);
                                }
                            })
                            return val.join(",");
                        }
                        return input.value;

                    }
                    if(inputType=='radio'){
                        let checkRadioSpan = td.querySelector('div label[aria-checked="true"] span.el-radio__label');
                        if(!checkRadioSpan){
                            return ''
                        }
                        return (checkRadioSpan.innerText || '').trim();
                    }
                    if(inputType=='checkbox'){
                        let checkBoxSpans = td.querySelectorAll('div label.is-checked span.el-checkbox__label');
                        let val = [];
                        (checkBoxSpans || []).forEach(span=>{
                            let v = (span.innerText || '').trim();
                            if(v){
                                val.push(v);
                            }
                        })
                        return val.join(',');
                    }
                    if(!inputType){
                        //判断是否是日期区间
                        let inputClass = input.getAttribute("class");
                        if(inputClass=='el-range-input'){
                            let dateRangeInputs = td.querySelectorAll('input.el-range-input');
                            let dateVal = [];
                            (dateRangeInputs || []).forEach(da=>{
                                if(da.value){
                                    dateVal.push(da.value);
                                }
                            })
                            return dateVal.join("-");
                        }
                    }
                }
                if(textarea){
                    return textarea.value;
                }
                return '';
            },
            /* --------- 转换动态表单 end---------------*/
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.userInfo.dimensions)) {
                    return false;
                } else {
                    if (this.userInfo.dimensions.hasOwnProperty(type)) {
                        if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
        },
        mounted() {
            this.transInputToSpan();
        },
        created() {
        },
        watch: {
            language: function (newVal) {
                if(newVal=='zh-CN'){
                    this.languageNumber=2;
                }else{
                    this.languageNumber=1;
                }
                if(this.showDynamicForm){
                    this.$refs.VFormDesignrender.changeLanguage(this.languageNumber);
                }
            }
        },
        computed: {
            ...mapGetters(["userInfo", "language"]),
            role() {
                return {
                    isSGS: this.haseRole("SGSUserRole", "SgsAdmin")|| this.haseRole("SGSUserRole", "SgsLabUser"),
                    isThirdPartyLab: this.haseRole('UserRole', 'ThirdPartyLab'),
                    isBuyer: this.haseRole('UserRole', 'Buyer'),
                }
            },
        },
        props: {
            data: {
                type: Object,
                default() {
                    return {}
                }
            },
            formTemplate: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        updated() {
        },
        components: {PreviewTRFTestRequestItem}
    }
</script>

<style lang="scss">
    .previewTRF {
        @media print{
            @page{
                margin-bottom:10mm;
            }
        }
        .avue-form{
            table.trans_dynamic_table th{
                text-align: center !important;
                font-weight: bold;
            }
            label.el-form-item__label{
                font-weight: bold;
            }
            .avue-text-ellipsis__text{
                border-bottom: 1px solid;
            }
        }
        #VFormDesignrender{
            .avue-form__group.avue-form__group--flex{
                display: flex !important;
                flex-wrap: wrap !important;
            }
            .trans_input_span{
                word-wrap: break-word !important;
                white-space: pre-wrap !important;

                label.el-checkbox{
                    display: block;
                    .el-checkbox__inner::after{
                        border-color:black !important;
                    }
                    .el-checkbox__label{
                        color:black !important;
                        text-decoration: underline !important;
                    }
                }
            }
        }
        #trf-specific-extend-cn{
            u{
                white-space: pre;
            }
        }

    }
</style>