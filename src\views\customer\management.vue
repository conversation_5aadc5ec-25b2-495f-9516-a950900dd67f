<template>
    <basic-container>
        <h3 class="top-title">{{$t('navbar.customerManagement')}}</h3>
        <el-row :gutter="24">
            <!-- <el-col :span="5">
                <el-card class="company-info">
                    <customer-detail></customer-detail>
                </el-card>
            </el-col> -->
            <el-col :span="24">
                <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" class="tab-wrap">
                    <!-- <el-tab-pane :label="$t('customer.title.CompanyBase')" v-if="permissionList.infoTab" name="info">
                        <customer-detail v-if="tabRefresh.info"></customer-detail>
                    </el-tab-pane> -->
                    <el-tab-pane :label="$t('account.title.tab')" v-if="permissionList.accountTab" :lazy="true" name="account">
                        <customer-account v-if="tabRefresh.account"></customer-account>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('department.title.tab')" v-if="permissionList.departmentTab" :lazy="true" name="department">
                        <customer-dept v-if="tabRefresh.department"></customer-dept>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('address.title.tab')" v-if="permissionList.addressTab" :lazy="true" name="address">
                        <customer-address  v-if="tabRefresh.address"></customer-address>
                    </el-tab-pane>
                    <!--<el-tab-pane :label="$t('invoice.title')" v-if="permissionList.invoiceTab"  :lazy="true" name="invoice">
                        <customer-invoice  v-if="tabRefresh.invoice"></customer-invoice>
                    </el-tab-pane>-->
                    <el-tab-pane :label="$t('contact.title.tab')" v-if="permissionList.contactTab" :lazy="true" name="contact">
                        <customer-contact v-if="tabRefresh.contact"></customer-contact>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('customer.relationship.title.default')" v-if="permissionList.relationshipTab" :lazy="true" name="relationship">
                        <!-- <customer-relation  v-if="tabRefresh.relationship"></customer-relation> -->
                    </el-tab-pane>
                    <el-tab-pane :label="$t('emailGroup.title.tab')"  v-if="permissionList.emailGroupTab" :lazy="true" name="emailGroup">
                        <customer-email-group v-if="tabRefresh.emailGroup"></customer-email-group>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('notifaction.title.tab')" v-if="permissionList.notifactionTab" :lazy="true" name="notifaction">
                        <customer-notifaction v-if="tabRefresh.notifaction"></customer-notifaction>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('reviewConclusion.title.tab')" v-if="permissionList.reviewTab" :lazy="true" name="review">
                        <customer-reviewConclusion v-if="tabRefresh.review"></customer-reviewConclusion>
                    </el-tab-pane>
                    <!--<el-tab-pane :label="$t('buyerRelationship.title.tab')" :lazy="true">
                        <customer-buyerRelationship></customer-buyerRelationship>
                    </el-tab-pane>-->
                </el-tabs>
            </el-col>
        </el-row>

    </basic-container>
</template>

<script>

    import {mapGetters} from "vuex";

    export default {
        components: {
            customerNotifaction: resolve => require(['./notifaction'], resolve),
            customerDetail: resolve => require(['@/views/customer/detailNew'], resolve),
            // customerDetail: resolve => require(['@/views/customer/detail'], resolve),
            customerDept: resolve => require(['@/views/customer/dept/index'], resolve),
            customerEmailGroup: resolve => require(['@/views/customer/emailGroup'], resolve),
            customerAddress: resolve => require(['@/views/customer/address'], resolve),
            customerAccount: resolve => require(['@/views/customer/account/index'], resolve),
            customerRelation: resolve => require(['@/views/customer/relationship/index'], resolve),
            customerContact: resolve => require(['@/views/customer/contact'], resolve),
            customerReviewConclusion: resolve => require(['@/views/customer/reviewConclusion'], resolve),
            customerBuyerRelationship: resolve => require(['@/views/customer/buyerRelationship'], resolve)
        },
        data() {
            return{
                activeName: 'account',
                tabRefresh: {
                    info: false,
                    account: true,
                    department: false,
                    address: false,
                    invoice:false,
                    contact: false,
                    relationship: false,
                    emailGroup: false,
                    notifaction: false,
                    review: false
                }
            }
        },
        computed: {
            ...mapGetters(["permission","companyId","dimensions"]),
            permissionList() {
                return {
                    infoTab: this.vaildData(this.permission['sgs:customer:tab:info'],false),
                    accountTab: this.vaildData(this.permission['sgs:customer:tab:info'],false),
                    departmentTab: this.vaildData(this.permission['sgs:customer:tab:department'],false),
                    addressTab: this.vaildData(this.permission['sgs:customer:tab:address'],false),
                    invoiceTab: this.vaildData(this.permission['sgs:customer:tab:invoice'],false),
                    contactTab: this.vaildData(this.permission['sgs:customer:tab:contact'],false),
                    relationshipTab: this.vaildData(this.permission['sgs:customer:tab:relationship'],false),
                    emailGroupTab: this.vaildData(this.permission['sgs:customer:tab:emailGroup'],false),
                    notifactionTab: this.vaildData(this.permission['sgs:customer:tab:notification'],false),
                    reviewTab: this.vaildData(this.permission['sgs:customer:tab:review'],false),
                };
            },
            role() {
                return {
                    isSGS: this.haseRole('SGSUserRole', 'SgsAdmin')|| this.haseRole("SGSUserRole", "SgsLabUser"),
                    isBuyer: this.haseRole('UserRole', 'Buyer'),
                };
            },
        },
        mounted () {
            let that = this
            this.$nextTick(function() {
                if(this.$route.query.tab != void 0) {
                    that.activeName = this.$route.query.tab
                    this.tabRefresh[this.$route.query.tab] = true;
                }
            })
        },
        watch: {
            $route(newVal, oldVal) {
                setTimeout(()=>{
                    this.activeName = newVal.query.tab
                    this.tabRefresh[newVal.query.tab] = true;
                }, 300)
            }
        },
        methods: {
            handleClick(tab) {
                if (tab.name === 'relationship') {
                    const url = '/web/customer/relationship'; 
                    window.location.replace(url)
                }
                Object.keys(this.tabRefresh).forEach(item=>{
                    this.tabRefresh[item]=false;
                })
                this.tabRefresh[tab.name]=true;
            }
        }

    }
</script>

<style scoped lang="scss">
.mt0 {
    margin-top: 0;
}
.mb12 {
    margin-bottom: 12px;
}
h1 {
    margin: 34px 0 26px 32px;
}
.company-info {
    padding: 4px 12px;
}
.tab-wrap {
    ::v-deep > .el-tabs__content {
        padding: 10px 32px 24px !important;
    }
}
</style>
