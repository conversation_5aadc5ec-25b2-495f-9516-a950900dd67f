import request from 'axios';
import qs from 'qs'

let api = {
    doRemoteFormData:(remoteUrl,param)=> {
        return request.post(remoteUrl,qs.stringify(param))
    },
    doRemoteGet:(remoteUrl,param)=>{
        return request({
            url:remoteUrl,
            method:'get',
            data:param
        })
    },
    doRemotePost:(remoteUrl,param)=>{
        return request({
            url:remoteUrl,
            method:'post',
            data:param
        })
    }

}

export default api;
