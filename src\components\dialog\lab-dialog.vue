<template>
    <el-main>
        <el-dialog  :visible.sync="visible" @close="close" size="70%">
            <el-card class="box-card">
                <!--实验室联系人-->
                <el-form :model="searchForm" rules="searchFormRules" ref="searchForm" label-position="left" size="medium" label-width="180px">
                    <div class="sgs-group">
                        <h4>{{$t('lab.labInfo')}}</h4>
                    </div>
                    <el-form-item :label="$t('lab.title')" prop="labCode">
                        <el-select v-model="searchForm.labIds" :placeholder="$t('lab.selLabName')" multiple
                                   @change="selectLabChange"  reserve-keyword filterable    style="width: 100%;" clearable>
                            <el-option v-for="(lab,index) in labData" :label="lab.labName" :value="lab.id"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="bottom clearfix " style="text-align: center">
                    <el-button size="small" @click="visible = false" :disabled="isDisabled">{{$t('operation.cancel')}}</el-button>
                    <el-button size="small"  type="primary" @click="submitLabForm" :disabled="isDisabled">
                        {{$t('operation.confirm')}}
                    </el-button>
                </div>
            </el-card>
        </el-dialog>
    </el-main>
</template>


<script>
    import {
        getPage,
        getList,
        getTestPackage
    } from "@/api/lab/test-package";
    import {getLabList, getLabContactListPage} from "@/api/lab/lab";
    import {mapGetters} from "vuex";

    export default {
        name: "lab-contact-dialog",
        props: ['visible','productLineCode','customerGroupCode'],
        data() {
            return {
                selLabs:[],
                labData: [],
                contactloading: false,
                searchForm: {
                    sgsToken:'',
                   // labId: '',
                    labIds:[],
                    contactName: '',
                    labType:'',
                },
                contactForm: {
                    labId: '',
                },
                labForm: {
                    labName: '',
                },
                tableData: [],
                form: {},
                selectionList: [],
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                data: [],
                searchFormRules:{
                    labCode: [
                        { required: true, message: this.$t('lab.selLab'), trigger: 'blur' },
                    ],
                }
            };
        },
        computed: {
            ...mapGetters(["userInfo","permission","token"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['nkop:user:add'], false),
                    viewBtn: this.vaildData(this.permission['nkop:user:view'], false),
                    delBtn: this.vaildData(this.permission['nkop:user:delete'], false),
                    editBtn: this.vaildData(this.permission['nkop:user:edit'], false)
                };
            },
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            },
        },
        created() {
            this.selectLab();
        },
        methods: {
            dataFilter(val) {
                this.labForm.labName = val;
                this.labData = this.labData.filter(item => {
                  return item.labName.toLowerCase()
                    .indexOf(val.toLowerCase()) > -1;
            });

                //放弃远程过滤lab
                //this.selectLab();
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            submitLabForm() {
                this.visible = false;
                //20-07-02逻辑修改
                //将实验室数据放入
                   /* var lab={};
                    lab['labId']=this.labForm.labId;
                    lab['labCode'] = this.labForm.labCode;
                    lab['labName'] = this.labForm.labName;
                    lab['labType'] = this.labForm.labType;
                    lab['labAddress'] = this.labForm.labAddress;*/
                    this.$emit('labData', this.selLabs);
            },
            selectLabChange(val) {
                this.selLabs=[];

                //先循环选中的实验室
                val.find((selLab) => {
                    let newLab={};
                     this.labData.find((lab) => {
                        if (selLab == lab.id) {
                            newLab.labName=lab.labName;
                            newLab.labCode = lab.labCode;
                            newLab.labAddress = lab.labAddress;
                            newLab.labType = lab.labType;
                            newLab.labId = lab.id;
                            newLab.labType=lab.labType;
                            //return newLab;
                        }
                    });
                    this.selLabs.push(newLab);
                });
                console.log( this.selLabs);
              /*  this.searchForm.contactName = '';
                let obj = {};
                obj = this.labData.find((item)=>{
                    return item.labCode === val;
                });
                if(obj!=null && obj!=undefined){
                    this.labForm.labName = obj.labName;
                    this.labForm.labCode = obj.labCode;
                    this.labForm.labAddress = obj.labAddress;
                    this.labForm.labType = obj.labType;
                    this.labForm.labId = obj.id;
                    this.searchForm.labType=obj.labType;
                }*/
            },
            //请求实验室联系人数据
            searchLabContactList() {
                var params = {};
                if(this.searchForm.labCode=='' || this.searchForm.labCode==undefined){
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('lab.selLab'),
                        type: 'warning'
                    });
                    return false;
                }
                //this.searchForm.labId=id;
                this.$refs['searchForm'].validate((valid) => {
                    if(valid){
                        this.contactloading = true;
                        this.searchForm.sgsToken=this.token;
                        getLabContactListPage(this.page.currentPage, this.page.pageSize, Object.assign(params, this.searchForm)).then(res => {
                            const data = res.data.data;
                            this.contactloading = false;
                            this.page.total = data.total;
                            this.tableData = data.records;
                        }, error => {
                            this.contactloading = false;
                            this.$message.error(this.$t('api.error'));
                            console.log(error);
                        });
                    }else{
                        this.contactloading = false;
                        console.log('error submit!!');
                        return false;
                    }
                });

            },
            /*labNamechange(value) {
                //清空已查询数据
                this.labData = [];
                this.searchForm.labId = '';
                //模糊查询实验室
                this.selectLab();
            },*/
            selectLab() {
                var params = {"labName":this.labForm.labName,"productLineCode":this.productLineCode,"customerGroupCode":this.customerGroupCode};
                getLabList(Object.assign(params)).then(res => {
                    const data = res.data.data;
                    this.labData = data;
                });
            },
            selectionChange(list) {
                this.selectionList = list;
            },
            beforeOpen(done, type) {
                if (["edit", "view"].includes(type)) {
                    getTestPackage(this.form.id).then(res => {
                        this.form = res.data.data;
                    });
                }
                done();
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
                this.searchLabContactList();
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            close() {
                this.visible = false;
                this.$emit('update:visible', this.visible);
            }
        }
    };
</script>

<style>
</style>
