<template>
    <basic-container>
        <!-- <el-breadcrumb class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">{{$t('navbar.dashboard')}}</el-breadcrumb-item>
            <el-breadcrumb-item>{{$t('navbar.newTemplate')}}</el-breadcrumb-item>
        </el-breadcrumb> -->
        <h1 class="top-title">{{$t('navbar.newTemplate')}}</h1>
        <el-card shadow="never" class="box-card">
            <!--<avue-form :option="option" v-model="form" @submit="handleSubmit"></avue-form>-->
            <el-row class="sgs-group">
                <h3>{{$t('template.baseInfoTitle')}}</h3>
                <br>
            </el-row>
            <el-form :model="templateform"  @submit.native.prevent :rules="rules" ref="templateform" label-width="200px" size="medium"
                     label-position="top" class="sgs-form">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item :label="$t('template.templateName')" prop="templateName">
                            <el-input clearable maxlength="200" v-model="templateform.templateName"
                                      autocomplete="off"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('template.productLine')" prop="productLineCode">
                            <el-select clearable filterable v-model="templateform.productLineCode" style="width:100%"
                                       @change="selecProductLineChange" :disabled="templateDisabled" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                                <el-option v-for="(productLine,index) in productLineData"
                                           :label="productLine.productLineName"
                                           :value="productLine.productLineCode"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('template.productCategory')" prop="productCategory">
                            <el-select clearable filterable v-model="templateform.productCategory" style="width:100%"
                                       @change="selecProductCategoryChange" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                                <el-option v-for="(dict,index) in productCategoryData" :key="dict.productCategoryId+index"
                                           :label="dict.productCategoryName"
                                           :value="dict.productCategoryId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                  <el-col :span="2">
                    <el-form-item :label="$t('template.customer')">
                      <el-select v-model="templateform.customerModel" style="width:100%"
                                 @change="customerSelectModelChange" :disabled="templateDisabled" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(model) in $t('customer.selectModel')"
                                   :label="model.label"
                                   :value="model.value">

                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                    <el-col :span="5" align="bottom" v-if="customerGroupShow">
                        <el-form-item style="padding-top: 28px" prop="customerGroupCode">
                            <el-select clearable filterable v-model="templateform.customerGroupCode" style="width:100%"
                                       @change="customerGroupCodeChange" :disabled="templateDisabled" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                                <el-option v-for="(customerGroup,index) in customerGroupData"
                                           :key = "index+'_'+customerGroup.customerGroupCode"
                                           :label="customerGroup.customerGroupName"
                                           :value="customerGroup.customerGroupCode"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                  <el-col :span="5" align="bottom" v-if="customerShow">
                    <el-form-item style="padding-top: 28px" prop="customerBossNo">
                      <el-select clearable filterable remote :remote-method="queryCustomerList" v-model="templateform.customerName" style="width:100%"
                                 @change="customerChange" :disabled="templateDisabled" :placeholder="$t('operation.pleaseEnterContent')" :no-data-text="$t('NoData')">
                        <el-option v-for="(customer,index) in customerData"
                                   :key = "customer.customerId"
                                   :label="customer.customerName"
                                   :value="customer.customerId">
                            <span style="float: left">{{ customer.customerNameEn }}</span>
                            <span style="float: right;">{{ customer.customerNameCn }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :label="$t('template.defaultCustomer')" prop="customerBossNo" :required="!customerShow && templateform.customerGroupCode!='General'" >
                        <el-select  filterable  v-model="templateform.customerName" style="width:100%"
                                 @change="defaultCustomerChange" :disabled="templateform.customerGroupCode=='General' || customerShow" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                        <el-option v-for="(customer,index) in defaultCustomerData"
                                   :key = "customer.customerId"
                                   :label=" '('+customer.number+') '+customer.customerNameEn"
                                   :value="customer.customerId">
                            <span style="float: left; font-size: 13px">({{ customer.number}}) &nbsp;</span>
                            <span style="float: left">{{ customer.customerNameEn }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                </el-col>

                    <el-col :span="12">
                        <el-form-item :label="$t('template.dff')" prop="dffFormId">
                            <el-select ref="dffSel"
                                       clearable
                                       filterable
                                       v-model="dffAndDynamicObj"
                                       style="width:100%"
                                       @change="dffChange"
                                       value-key="id"
                                       :placeholder="$t('operation.pleaseSelect')"
                                       :no-data-text="$t('NoData')">
                                <el-option v-for="(dff,index) in dffData"
                                           :key="'dff_'+index"
                                           :label="dff.name"
                                           :value="dff">{{dff.name}}
                                </el-option>
                                <el-option v-for="(dff,index) in dffDynamicData"
                                           :key="'dynamic_'+index"
                                           :label="dff.name"
                                           :value="dff">{{dff.name}}
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('template.testPackage')" prop="testPackageId">
                            <input type="hidden" v-model="templateform.testPackageId"></input>
                            <el-input  v-model="templateform.testPackageName" readonly="readonly"
                                      autocomplete="off" class="input-with-select">
                                <el-button slot="append" :disabled="templateDisabled" icon="el-icon-search" @click="selectParentPackage"></el-button>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="$t('template.specific')">
                            <el-drag-select v-model="selSpecificCodes"
                                            style="width: 100%;" multiple
                                            :placeholder="$t('operation.pleaseSelect')"
                                            :no-data-text="$t('NoData')"
                                            :popper-class="'specific_select'"
                                            classs="specific_drag_select"
                                            @remove-tag="removeSpecific"
                            >
                                <el-option v-for="item in specificData"
                                           :key="item.specificCode"
                                           :class="'specific_'+item.specificCode"
                                           :label="item.productLineComponent"
                                           :value="item.specificCode"/>
                            </el-drag-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('template.printTemplate')" prop="printTemplateId">
                        <el-select ref="printTemplate" clearable filterable v-model="templateform.printTemplateId"
                                   style="width:100%" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                          <el-option v-for="(print,index) in printTemplateData" :label="print.templateName"
                                     :value="print.templateSettingID">{{print.templateName}}
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="$t('template.dffDefaultFlag')" prop="dffDefaultFlag">
                        <el-select ref="dffDefaultFlag" clearable filterable v-model="templateform.dffDefaultFlag"
                                   style="width:100%" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                            <el-option  :label="$t('template.flagTrue')"
                                     :value="1">
                          </el-option>
                          <el-option  :label="$t('template.flagFalse')"
                                     :value="0">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="$t('template.serviceUnitDefaultFlag')" prop="serviceUnitDefaultFlag">
                        <el-select ref="serviceUnitDefaultFlag" clearable filterable v-model="templateform.serviceUnitDefaultFlag"
                                   style="width:100%" :placeholder="$t('operation.pleaseSelect')" :no-data-text="$t('NoData')">
                            <el-option  :label="$t('template.flagTrue')"
                                     :value="1">
                          </el-option>
                          <el-option  :label="$t('template.flagFalse')"
                                     :value="0">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                </el-row>

                <el-row class="sgs-group" style="margin-bottom: 15px;">
                    <el-col>
                        <h3 style="line-height: 1.5;">CUSTOMER INFORMATION REQUIRED FIELDS</h3>
                    </el-col>
                </el-row>
                <el-row>
                    <el-table
                        :data="customerField.dataList.filter(c=>c.show)"
                        fit
                        border
                    >
                        <el-table-column fixed="left" prop="title" :label="$t('customer.title.tab')" min-width="120px">
                            <template slot-scope="{row}">
                                {{$t(row['title'])}}
                            </template>
                        </el-table-column>
                        <el-table-column
                                 v-for="(col,index) in customerField.columns"
                                 :key="'field_'+index"
                                 :prop="col.prop"
                                 :label="$t(col.label)"
                                 min-width="220px">
                            <template slot-scope="{row}">
                                <el-checkbox
                                             v-model="row[col.prop]"
                                             :true-label="1"
                                             :false-label="0">
                                </el-checkbox>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>

                <el-row class="sgs-group" style="margin-bottom: 15px;">
                    <h3 style="line-height: 1.5;">{{$t('template.labContactTitle')}}</h3>
                    <div class="right">
                        <el-button type="primary" icon="el-icon-circle-plus-outline" size="small" @click="addLab()">
                            {{$t('operation.add')}}
                        </el-button>
                    </div>
                </el-row>
                <el-table class="contactLab" ref="labContactTable" :show-header="true" :data="templateform.labs"
                          v-loading="contactloading"
                          :element-loading-text="$t('loading')"
                          style="width: 100%"
                          @expand-change="labContactChange"
                          :row-key="getRowKeys">
                    <el-table-column type="index" label="#" width="50" fixed></el-table-column>
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <el-table :data="props.row.labContacts" v-loading="contactloading"
                                      :element-loading-text="$t('loading')" style="width: 90%">
                                <el-table-column
                                        prop="contactName"
                                        :label="$t('labContact.contactName1')"
                                >
                                </el-table-column>
                                <el-table-column
                                        prop="contactTel"
                                        width="240"
                                        :label="$t('labContact.contactTel')">
                                </el-table-column>
                                <el-table-column
                                        prop="contactEmail"
                                        width="260"
                                        :label="$t('labContact.contactEmail')">
                                </el-table-column>
                                <el-table-column
                                        prop="isDefault"
                                        :label="$t('common.isDefault')"
                                        align="center"
                                        width="100">
                                    <template slot-scope="scope">
                                        <el-tooltip :content="scope.row.isDefault==1?$t('common.yes'):$t('common.no')"
                                                    placement="top">
                                            <el-switch @change="contactIsDefaultChange(props.row,scope.row,value)"
                                                       v-model="scope.row.isDefault"
                                                       active-color="#ff6600"
                                                       inactive-color="#ff4949"
                                                       :active-value="1"
                                                       :inactive-value="0">
                                            </el-switch>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>

                                <el-table-column :label="$t('operation.title')" width="180">
                                    <template slot-scope="scope">
                                        <el-button @click.native.prevent="deleteLabContact(scope.$index,scope.row)"
                                                   type="text" size="mini" icon="el-icon-delete">
                                            {{$t('operation.remove')}}
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>

                    <el-table-column
                            prop="labName"
                            :label="$t('lab.labName')"
                    >
                    </el-table-column>

                    <el-table-column :label="$t('operation.title')"
                                     align="center"
                                     width="180">
                        <template slot-scope="scope">
                            <el-button @click="addLabContact(scope.row)" type="text" size="mini"
                                       icon="el-icon-circle-plus-outline">{{$t('lab.addContact')}}
                            </el-button>
                            <el-button @click.native.prevent="deleteRow(scope.$index)" type="text"
                                       size="mini" icon="el-icon-delete">{{$t('operation.remove')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-row style="margin-top: 10px;text-align: center">
                    <!--<el-button :disabled="isDisabled">{{$t('operation.cancel')}}</el-button>-->
                    <el-button type="primary" @click="goBackTemplateList" v-if="permissionList.goBackTemplateListBtn" >{{$t('operation.goBack')}}</el-button>
                    <el-button type="primary" v-if="permissionList.submitTemplateBtn"  @click="submitTemplateForm('templateform')" :disabled="isDisabled">
                        {{$t('operation.confirm')}}
                    </el-button>
                </el-row>

            </el-form>
        </el-card>

        <lab-dialog v-if="isShowLab" :visible.sync="selectLabDialogVisible" :productLineCode="templateform.productLineCode"
                    :customerGroupCode="templateform.customerGroupCode"
                    @labData="getLabData">
        </lab-dialog>
        <lab-contact-dialog :visible.sync="selectLabContactDialogVisible"
                            :productLineCode="templateform.productLineCode"
                            :customerGroupCode="templateform.customerGroupCode"
                            :labCode="labCode"
                            :labType="labType"
                            @labContact="testReceive">
        </lab-contact-dialog>
        <test-package-dialog :visible.sync="selectParentDialogVisible"
                             :testpackage.sync="form" :productLineCode="templateform.productLineCode"
                             :testPackageCustomerGroupData="testPackageCustomerGroupData"
                             :customerGroupCode="templateform.customerGroupCode"
                             :customerName = "templateform.customerName"
                             @selection="handleSelectParentPackage">
        </test-package-dialog>
        <el-dialog title="" width="40%" height="40%" :visible.sync="fieldSettingsDialog">
            <el-row>
                <el-table class="contactLab" :show-header="true" :data="fieldSettingsTableData"
                          v-loading="contactloading"
                          :element-loading-text="$t('loading')" style="width: 100%">
                    <el-table-column
                            prop="category"
                            :label="$t('template.productCategory')"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="fieldName"
                            :label="$t('customerMaterialConfig.dffFieldName')"
                    >
                    </el-table-column>
                    <el-table-column prop="usable" width="120" align="center" :label="$t('template.usable')">
                        <template slot-scope="scope">
                            <el-switch

                                    v-model="scope.row.usable"
                                    active-color="#ff6600"
                                    inactive-color="#ff4949"
                                    active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column prop="required" width="120" align="center" :label="$t('template.required')">
                        <template slot-scope="scope">
                            <el-switch
                                    v-if="scope.row.usable === 1"
                                    v-model="scope.row.required"
                                    active-color="#ff6600"
                                    inactive-color="#ff4949"
                                    active-text="" :active-value="1" inactive-text="" :inactive-value="0">
                            </el-switch>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row>
                <el-col :span="24" style="text-align: right">
                    <div class="sgs-bottom">
                        <el-button type="primary" @click="fieldSettingsDialog = false">
                            {{$t('operation.submit')}}
                        </el-button>
                    </div>
                </el-col>
            </el-row>
        </el-dialog>
        <el-dialog
            :visible.sync="confirmSaveDisabled"
            title=""
            width="500"
        >
            <span>{{ $t('template.confirmSave') }}</span>
            <template #footer>
            <div class="dialog-footer">
                <el-button @click="confirmSaveDisabled = false">{{$t('operation.cancel')}}</el-button>
                <el-button type="primary" @click="confirmSaveTemplateForm">
                    {{$t('operation.submit')}}
                </el-button>
            </div>
            </template>
        </el-dialog>
    </basic-container>
</template>

<script>
    import {getList} from "@/api/lab/test-package";
    import {
      checkDefaultTemplate,
      submitTemplate,
      detail,
      queryTrimsProductCategory,
      getFieldSettings,
      submitFieldSettings, queryDigitalReportSetting,
        getFormDynamicList
    } from "@/api/template/template";
    import {
        getDictByCode,
        getCustomerGroup,
        getProductLine,
        getDffList,
        getSpecificByProductLineId
    } from "@/api/common/index";
    import {searchCustomerAndGroup,queryCustomerByBuCodeAndCustomerId} from "@/api/customer/customer";
    import {mapGetters} from "vuex";
    import {validatenull} from "../../util/validate";

    export default {
        components: {
            LabContactDialog: resolve => require(['../../components/dialog/lab-contact-dialog'], resolve),
            LabDialog: resolve => require(['../../components/dialog/lab-dialog'], resolve),
            TestPackageDialog: resolve => require(['../../components/dialog/test-package-dialog'], resolve),
            ElDragSelect: resolve => require(['../../components/select/drageSelect'], resolve),
        },
        created() {
            this.queryproductLineData();
            this.queryCustomerGroupData();
            this.queryTestPackageData();
            this.initCustomerFieldData();
            //获取路由参数
            this.getParams();
        },
        data() {
            var validateTemplate = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('template.inputTemplateName')));
              } else {
                callback();
              }
            };
            var validateTestPackage = (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('template.selTestPackage')));
              } else {
                callback();
              }
            };
            return {
                customerField:{
                    dataList:[],
                    columns:[
                        { prop:"customerNameEn", label:'customer.new.companyNameEn'},
                        { prop:"customerNameZh" ,label:'customer.new.companyNameCn'},
                        { prop:"customerAddressEn", label:'customer.new.companyAddressEn'},
                        { prop:"customerAddressZh", label:'customer.new.companyAddressCn'},
                        { prop:"customerReferenceNo", label:'customer.new.customerReferenceNo'},
                        { prop:"contactName", label:'contact.title.default'},
                        { prop:"contactTelephone", label:'contact.phone'},
                        { prop:"contactEmail", label:'contact.email'}
                    ]
                },
                isShowLab:false,
                defaultCustomerData:[],
                templateDisabled:false,
                confirmSaveDisabled:false,
                getRowKeys(row) {
                    return row.labCode;
                },
                specDisabled:[],
                selSpecificCodes: [],
                contactloading: false,
                expands: [],
                labCode: '',
                labType: '',
                testPackageCustomerGroupData: [],
                testPackageFlag: false,
                productCategoryObj: '',
                dffObj: '',
                dffName: '',
                templateId: '',
                isDisabled: false,
                insertTestPackagedialogVisible: false,
                fieldSettingsDialog: false,
                fieldSettingsTableData: [],
                productLineData: [],
                customerGroupData: [],
                customerData:[],
                dffData: [],
                dffDynamicData: [],
                testPackageData: [],
                productCategoryData: [],
                specificData: [],
                printTemplateData:[],
                productLineSpecificFlag: false,
                btnGuestbookSubmit: false,
                customerGroupShow: true,
                customerShow:false,
                dffAndDynamicObj:{},//分组存储的DFFForm或者动态表单
                templateform: {
                    id: '',
                    customerGroupCode: '',
                    customerGroupName: '',
                    customerBossNo:'',
                    customerName:'',
                    productLineSpecificId: '',
                    specificCode: '',
                    productLineId: '',
                    productLineCode: '',
                    productLineName: '',
                    dffFormId: '',//最终入库的formId
                    dffGridId: '',
                    dffName: '',
                    sortIndex: '',
                    productCategory: '',
                    productCategoryName: '',
                    templateName: '',
                    testPackageId: '',
                    testPackageName: '',
                    labId: '',
                    labCode: '',
                    labName: '',
                    labs: [],
                    defaultContactIds: [],
                    printTemplateId:null,
                    customerModel:"1",
                    confirmSave: 1,
                    formType:'',
                    formDesignId:'',//动态表单的主键iD
                    formDesignGroupId:'',//
                    formCode:'',//指动态表单配置的code ,查询是通过code 查询，并非主键id
                    dffDefaultFlag:0,
                    serviceUnitDefaultFlag:0,
                },
                productLineSpecific: {
                    productLineCode: '',
                },
                queryDffDataParam: {
                    selectedFormId: '',
                    customerGroupID: '',
                    buCode: '',
                    systemCode: "PreOrder",
                    //moduleCode: "TRF-Product"
                },
                queryCustomerExtParam:{
                    buCode:'',
                    customerId:''
                },
                queryDigitalReport:{
                  applicationID: null,
                  buCode: '',
                  customerGroupCode: '',
                  templateSettingTypeID: null
                },
                labContacts: [],
                selectParentDialogVisible: false,
                selectLabContactDialogVisible: false,
                selectLabDialogVisible: false,
                form: {},
                selectionList: [],
                query: {},
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                data: [],
                productCategoryParam: {
                    productLineId: '',
                },
                rules: {
                    templateName: [
                        {required: true, validator:validateTemplate, trigger: 'blur'},
                        /*{  max: 20, message: '名称长度不可大于20个字符', trigger: 'blur' }*/
                    ],
                    productLineCode: [
                        {required: true, message: this.$t('template.selProductLine')}
                    ],
                    // customerGroupCode: [
                    //     {required: true, message: this.$t('template.selCustomerGroup')}
                    // ],
                    dffFormId: [
                        {required: true, message: this.$t('template.selDff')}
                    ],
                    // productCategory: [
                    //     {required: true, message: this.$t('template.selProductCategory')}
                    // ],
                    testPackageId: [
                        {required: true, validator:validateTestPackage}
                    ]
                },
            };
        },
        watch: {
            'templateForm.productLineSpecificFlag': function (val) {
                console.log(val);
            },
            'templateForm.productLineCode': function (val) {//产品线重新选择的话则清空下面关联数据
                this.templateform.testPackageId = '';
                this.templateform.printTemplateId = null;
            },
            'templateForm.customerGroupCode': function (val) {//客户组重新选择的话则清空下面关联数据
                this.templateform.testPackageId = '';
                this.templateform.printTemplateId = null;
            },
            'templateForm.defaultFlag':function(val){
                console.log("defaultFlag"+val);
                this.templateForm.defaultFlag=val?1:0;
            },
            selSpecificCodes : function (val) {//客户组重新选择的话则清空下面关联数据
              this.customerField.dataList.forEach(da=>{
                  da.show = (val || []).includes(da.usageType);
              })
            },
            "$i18n.locale": function () {
              //this.$refs['trf'].clearValidate();

              if (this.$refs['templateform'] != undefined) {
                this.$refs['templateform'].fields.forEach(item => {
                  if (item.validateState === 'error') {
                    this.$refs['templateform'].validateField(item.labelFor)
                  }
                })
              }
            },

        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:template:add'], false),
                    addContactBtn: this.vaildData(this.permission['sgs:template:addContact'], false),
                    goBackTemplateListBtn: this.vaildData(this.permission['sgs:template:goBack'], false),
                    submitTemplateBtn: this.vaildData(this.permission['sgs:template:save'], false),
                };
            },
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            },
        },


        methods: {
            openFieldSettings() {
                this.fieldSettingsTableData = [];
                this.fieldSettingsDialog = true;
                getFieldSettings(Object.assign({'templateId': this.templateId})).then(res => {
                    this.fieldSettingsTableData = res.data.data;
                })
            },
            submitFieldSettings() {
                this.btnGuestbookSubmit = true;
                this.fieldSettingsTableData.forEach(item => {
                    item.testPackageId = this.templateform.testPackageId
                })
                submitFieldSettings(this.fieldSettingsTableData).then(res => {
                    this.btnGuestbookSubmit = false;
                    this.$message({
                        type: "success",
                        message: this.$t('api.success')
                    });
                })
            },
            contactIsDefaultChange(selLab, selContacts) {
              debugger;
                this.contactloading = true;
              this.templateform.labs.forEach(lab=>{
                if (lab.labCode == selLab.labCode) {
                  lab.labContacts.forEach(item => {

                    if (item.id == selContacts.id) {
                      item.isDefault = selContacts.isDefault;
                    }else{
                      item.isDefault = 0;
                    }
                  });
                }
              })

                /*this.templateform.labs.find((lab) => {
                    if (lab.labCode == selLab.labCode) {
                        lab.labContacts.find((item) => {
                            item.isDefault = 0;
                            if (item.id == selContacts.id) {
                                item.isDefault = selContacts.isDefault;
                            }
                        });
                    }
                });*/
                this.$refs.labContactTable.toggleRowExpansion(selLab, false);// 点击button展开
                setTimeout(() => {
                    this.$refs.labContactTable.toggleRowExpansion(selLab, true);
                    this.contactloading = false;
                }, 500);
                console.log('contact 默认设置',this.templateform.labs)
                /*this.templateform.labs.find((item)=>{
                  if(item.id!=row.id){
                      item.isDefault=0;
                  }
                });*/
            },
            resetTemplateForm(){
                this.dffAndDynamicObj = {};
                this.templateform.dffFormId = '';
                this.templateform.testPackageId = '';
                this.templateform.testPackageName = '';
                this.templateform.printTemplateId = null;
                this.templateform.customerGroupCode = null;
                this.templateform.customerGroupName = null;
                this.templateform.customerBossNo = null;
                this.templateform.customerName = null;

                this.templateform.dffGridId = '';
                this.templateform.formType = '';
                this.templateform.formCode='';
                this.templateform.formDesignId='';
                this.dffData = [];
                this.dffDynamicData = [];
            },
            customerSelectModelChange(val){
              if(val === "1"){
                this.customerGroupShow = true;
                this.customerShow  = false;
              }
              if(val === "2"){
                this.customerShow = true;
                this.customerGroupShow = false;
              }
              this.resetTemplateForm();
            },
            customerGroupCodeChange(val) {
                this.dffData = [];
                var dffSel = this.$refs.dffSel;
                this.dffAndDynamicObj = {};
                this.templateform.dffFormId = '';
                this.templateform.testPackageId = '';
                this.templateform.testPackageName = '';
                this.templateform.printTemplateId = null;
                this.templateform.dffGridId = '';
                this.templateform.formType = '';
                this.templateform.formCode='';
                this.templateform.formDesignId='';
                this.templateform.customerBossNo='';
                this.templateform.customerName='';
                this.dffData = [];
                this.dffDynamicData = [];

                this.queryDigitalReport.customerGroupCode = val;
                let obj = {};
                obj = this.customerGroupData.find((item) => {
                    return item.customerGroupCode === val;
                });
                //console.log("change group",val,obj)
                let customerId = null;
                if(obj){
                    customerId = obj.customerId;
                }
                this.queryCustomerExtParam.customerId = customerId;
                let newCustomerGroup = [];
                if (obj != undefined && obj != null) {
                    newCustomerGroup.push(obj);
                    this.testPackageCustomerGroupData = newCustomerGroup;
                    if (val != 'General') {
                        var generalObj = {};
                        generalObj.customerGroupName = 'General';
                        generalObj.customerGroupCode = 'General';
                        this.testPackageCustomerGroupData.push(generalObj);
                    }
                    this.templateform.customerGroupName = obj.customerGroupName;
                    this.queryDffDataParam.customerGroupID = obj.customerGroupId;
                    this.searchCustomerByGroupCode(obj.customerGroupCode,true);
                }
                if (val == 'General') {
                    this.templateform.customerGroupName = val;
                }
                //查询DFF表格数据
                if (this.templateform.productLineCode != '' && this.templateform.productLineCode != undefined) {
                    //console.log("customerGroupCoedchange",val)
                    this.queryDffData();
                    this.getDigitalReportSetting();
                } else {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('template.selProductLine'),
                        type: 'warning'
                    });
                }

            },
            //根据Group获取对应客户信息
            searchCustomerByGroupCode(customerGroupCode,loadOneCustomerFlag){
                if(customerGroupCode==='General'){//设置为非必填
                    this.defaultCustomerData = [];
                    return;
                }
                let searchBuyerParam = {};
                searchBuyerParam.displayType='account';
                searchBuyerParam.customerGroupCode = customerGroupCode;
                searchBuyerParam.rows = 1000;
                searchBuyerParam.page = 1;
                searchCustomerAndGroup(searchBuyerParam).then(res => {
                    console.log(res.data.data);
                    const data = res.data.data;
                    this.defaultCustomerData = data;
                    if(data.length==1 && loadOneCustomerFlag){
                         let oneData = data[0];
                         this.templateform.customerName= '('+oneData.number+') '+oneData.customerNameEn;
                         this.templateform.customerBossNo= oneData.number;
                         this.templateform.customerId= oneData.customerId;
                    }else{//反向回显，根据CustomerNo 查找对应客户
                        debugger;
                        let currentBossNo = this.templateform.customerBossNo;
                        if(!validatenull(currentBossNo)){
                            let obj = {};
                            obj = this.defaultCustomerData.find((item) => {
                                return item.number === currentBossNo+'';
                            });
                            if(!validatenull(obj)){
                                this.templateform.customerId= obj.customerId;
                                this.templateform.customerName ='('+obj.number+') '+obj.customerNameEn;
                            }
                        }

                    }
                });
                // data =  await searchCustomerAndGroup(searchBuyerParam).then( res => {
                // return  res.data.data;
                // });
            },
            removeSpecific(val){
            },
            defaultCustomerChange(val){
              console.log(val);
              let obj = {};
              obj = this.defaultCustomerData.find((item) => {
                  return item.customerId === val;
              });
              console.log(obj);
              if(obj != null && obj != ''){
                this.templateform.customerBossNo = obj.number;
                this.templateform.customerName = '('+ obj.number+') '+obj.customerNameEn;
              }
            },
            customerChange(val){
              console.log(val);
              let obj = {};
              obj = this.customerData.find((item) => {
                  return item.customerId === val;
              });
              console.log(obj);
              if(obj != null && obj != ''){
                this.templateform.customerBossNo = obj.number;
                this.templateform.customerName = obj.customerName;
                this.templateform.customerGroupCode = obj.customerGroupCode;
                this.templateform.customerGroupName = obj.customerGroupName;
                this.queryDigitalReport.customerGroupCode = obj.customerGroupCode;
                this.queryDffDataParam.customerGroupID = obj.customerGroupId;
                this.queryCustomerExtParam.customerId = val;
              } else {
                this.templateform.customerBossNo = '';
                this.templateform.customerName = '';
                this.templateform.customerGroupCode = '';
                this.templateform.customerGroupName = '';
                this.queryDigitalReport.customerGroupCode = '';
                this.queryDffDataParam.customerGroupID = '';
                this.queryCustomerExtParam.customerId = '';
              }
              this.dffData = [];
              this.templateform.dffFormId = null;
              this.templateform.testPackageId = '';
              this.templateform.testPackageName = '';
              this.templateform.printTemplateId = null;
              // 如果选择的客户有客户组数据，选择测试包的时候需要默认选中
              this.testPackageCustomerGroupData = []
              if(obj.customerGroupCode){
                let customerGroup = {};
                customerGroup = this.customerGroupData.find((item) => {
                  return item.customerGroupCode === obj.customerGroupCode;
                });
                this.testPackageCustomerGroupData.push(customerGroup);
              }

              //查询DFF表格数据
              if (this.templateform.productLineCode != '' && this.templateform.productLineCode != undefined) {
                console.log("customerChange",val)
                  this.queryDffData();
                this.getDigitalReportSetting();
              } else {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('template.selProductLine'),
                  type: 'warning'
                });
              }
            },
            selecProductCategoryChange(val) {
                let obj = {};
                obj = this.productCategoryData.find((item) => {
                    return item.productCategoryId === val;
                });
                let getName = ''
                getName = obj.productCategoryName;
                this.templateform.productCategoryName = getName;
                this.templateform.productCategory = val;
            },
            dffChange(dffObj) {
                let {name,formId,gridId,formType,id,code,formDesignGroupId} = dffObj;
                this.templateform.dffFormId = formId;
                this.templateform.formType = formType;
                this.templateform.dffName = name;
                this.templateform.dffGridId = gridId;
                if(formType==2){
                    this.templateform.formCode = code;
                    this.templateform.formDesignId = id;
                    this.templateform.formDesignGroupId = formDesignGroupId;
                }
            },
            specifiChandleChange(boolean, val1, val2, specificCode) {
                console.log(boolean); //change事件自带的参数 boolean类型
                console.log(val1); //自定义的参数
                console.log(val2); //自定义的参数
                if (boolean) {
                    this.templateform.productLineSpecificId = val2;
                    this.templateform.specificCode = specificCode;
                } else {
                    this.templateform.productLineSpecificId = 0;
                    this.templateform.specificCode = '';
                }
            },
            selectionContactChange(list) {
                //设为默认的ids
                let ids = [];
                list.forEach(ele => {
                    ids.push(ele.id);
                });
                ids.join(",");
                this.templateform.defaultContactIds = ids;
            },
            handleSelectParentPackage(data) {
                this.selectParentDialogVisible = false;
                this.templateform.testPackageId = data.id;
                this.templateform.testPackageName = data.testPackageName;
            },
            selecProductLineChange(value) {
                //重新初始化选择Lab组件
                this.isShowLab=false;
                this.dffData = [];
                this.selSpecificCodes = [];
                this.dffAndDynamicObj = {};
                this.templateform.dffFormId = null;
                this.templateform.productCategory = '';
                this.templateform.testPackageId = '';
                this.templateform.testPackageName = '';
                this.templateform.printTemplateId = null;
                this.templateform.dffGridId = '';
                this.templateform.formType = '';
                this.templateform.formCode='';
                this.templateform.formDesignId='';
                this.dffData = [];
                this.dffDynamicData = [];
                let obj = {};
                obj = this.productLineData.find((item) => {
                    return item.productLineCode === value;
                });
                if (obj != undefined && obj != null) {
                    this.templateform.productLineId = obj.productLineID;
                    this.templateform.productLineName = obj.productLineName;
                    this.productCategoryParam.productLineId = parseInt(obj.productLineID);
                }

                this.templateform.productLineCode = value;

                this.specificData = [];
                this.templateform.productCategory = '';
                //根据产品线ID查询产品类别
                this.queryProductCategoryData();
                //查询产品线定制化标签
                this.queryProductLineSpecificData(value);
                //查询dff数据
                this.queryDffDataParam.buCode = value;
                this.queryDigitalReport.buCode = value;
                this.queryCustomerExtParam.buCode = value;
                if (this.templateform.customerGroupCode != '' && this.templateform.customerGroupCode != undefined) {
                    //查询DFF表格数据
                    console.log("productLineCode",value)
                    this.queryDffData();
                    this.getDigitalReportSetting();
                }
            },
            queryProductLineSpecificData(value) {
                this.specDisabled = [];
                this.selSpecificCodes = [];
                let params = {};
                this.productLineSpecific.productLineCode = value;
                getSpecificByProductLineId(Object.assign(params, this.productLineSpecific)).then(res => {
                    const data = res.data.data;
                    this.specificData = data;
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                })
            },
            getParams() {
                // 取到路由带过来的参数
                const routerParams = this.$route.query.id
                // 将数据放在当前组件的数据内
                this.templateId = routerParams;
                if (routerParams != '' && routerParams != undefined) {
                    //查询模板详情
                    this.queryTemplate(this.templateId);
                }

            },
            queryTemplate(templateId) {
                detail(templateId).then(res => {
                    const data = res.data.data;
                    let {dffFormId,customerRequireField} = data;
                    this.templateform = data;
                    debugger
                    if(customerRequireField){
                        try{
                            let dbCustomerField = JSON.parse(customerRequireField) || [];
                            this.customerField.dataList.forEach(da=>{
                                let {usageType} = da;
                                let dbC = dbCustomerField.find(dc=>dc.usageType == usageType);
                                if(dbC){
                                    let {show, customerNameEn,customerNameZh,customerAddressEn,customerAddressZh,customerReferenceNo,contactName,contactTelephone,contactEmail} = dbC;
                                    this.$set(da,'show',show);
                                    this.$set(da,'customerNameEn',customerNameEn);
                                    this.$set(da,'customerNameZh',customerNameZh);
                                    this.$set(da,'customerAddressEn',customerAddressEn);
                                    this.$set(da,'customerAddressZh',customerAddressZh);
                                    this.$set(da,'customerReferenceNo',customerReferenceNo);
                                    this.$set(da,'contactName',contactName);
                                    this.$set(da,'contactTelephone',contactTelephone);
                                    this.$set(da,'contactEmail',contactEmail);
                                }
                            })
                        }catch(e){
                            console.log("解析customerRequireField失败",e);
                        }
                    }
                    debugger;
                    //根据返回数据判断显示客户or客户组
                    if(this.templateform.customerModel === "1"){
                      this.customerGroupShow = true;
                      this.customerShow = false;
                      //选择的客户组，重新编辑需根据客户组加载客户数据
                      this.searchCustomerByGroupCode(this.templateform.customerGroupCode,false);

                    } else if(this.templateform.customerModel === "2"){
                      this.customerGroupShow = false;
                      this.customerShow = true;
                    }
                    this.productCategoryParam.productLineId = this.templateform.productLineId;
                    this.queryCustomerExtParam.buCode = data.productLineCode;
                    //设置模板product Line和客户组不可编辑
                    this.templateDisabled=true;
                    //查询产品类别回显
                    queryTrimsProductCategory(this.productCategoryParam.productLineId).then(res => {
                        const data = res.data.result;
                        this.productCategoryData = data;
                        if (this.templateform.productCategory != -1 && this.templateform.productCategory != '') {
                            this.templateform.productCategory = parseInt(this.templateform.productCategory);
                        } else {
                            this.templateform.productCategory = '';
                        }
                    });
                    getCustomerGroup().then(res => {
                        const data = res.data.data;
                        this.customerGroupData = data;
                        let obj = {};
                        obj = this.customerGroupData.find((item) => {
                            return item.customerGroupCode === this.templateform.customerGroupCode;
                        });
                        let newCustomerGroup = [];
                        if (obj != undefined && obj != null) {
                            newCustomerGroup.push(obj);
                            this.testPackageCustomerGroupData = newCustomerGroup;
                            if (this.templateform.customerGroupCode != 'General') {
                                var generalObj = {};
                                generalObj.customerGroupName = 'General';
                                generalObj.customerGroupCode = 'General';
                                this.testPackageCustomerGroupData.push(generalObj);
                            }
                            this.queryDffDataParam.customerGroupID = obj.customerGroupId;
                            this.queryCustomerExtParam.customerId = obj.customerId;
                        }
                        this.queryDffDataParam.buCode = this.templateform.productLineCode;
                        this.queryDffDataParam.selectedFormId = this.templateform.dffFormId;
                        //查询dff数据回显
                        this.queryDffData();
                        if(this.customerGroupShow){
                            //console.log("group 查询ext")
                        }
                    });
                    if(this.customerShow){
                        let {customerBossNo} = this.templateform;
                        this.queryCustomerList(this.templateform.customerName,()=>{
                            let cusObj = this.customerData.find(c=>c.number == customerBossNo);
                            if(cusObj){
                                this.queryCustomerExtParam.customerId = cusObj.customerId;
                            }
                        })
                    }
                    this.queryDigitalReport.customerGroupCode = this.templateform.customerGroupCode;
                    this.queryDigitalReport.buCode = this.templateform.productLineCode
                    this.getDigitalReportSetting();
                    //特定标签回显
                    this.productLineSpecific.productLineCode = this.templateform.productLineCode;
                    var params = {};
                    getSpecificByProductLineId(Object.assign(params, this.productLineSpecific)).then(res => {
                        const data = res.data.data;
                        this.specificData = data;
                        if (data) {
                            if (!validatenull(this.templateform.specificCode)) {
                                this.selSpecificCodes = this.templateform.specificCode.split(',')
                            }
                        }
                    }, error => {
                        this.$message.error(this.$t('api.error'));
                        console.log(error);
                    })
                }, error => {
                    this.$message.error(this.$t('api.error'));
                    console.log(error);
                });
            },
            selectParentPackage(form) {
                //验证是否选择产品线以及客户组
                if (this.templateform.productLineCode == '' || this.templateform.productLineCode == null) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('template.selProductLine'),
                        type: 'warning'
                    });
                    return false;
                }
                if (this.templateform.customerModel == "1" &&(this.templateform.customerGroupCode == '' || this.templateform.customerGroupCode == null )) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('template.selCustomerGroup'),
                        type: 'warning'
                    });
                    return false;
                }
              if (this.templateform.customerModel === "2" &&
                  (this.templateform.customerName == '' || this.templateform.customerName == null)) {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('template.selCustomer'),
                  type: 'warning'
                });
                return false;
              }
              if (this.templateform.customerModel == "2" &&(this.templateform.customerBossNo == '' || this.templateform.customerBossNo == null )) {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('template.selCustomer'),
                  type: 'warning'
                });
                return false;
              }
                this.testPackageFlag = true;
                this.selectParentDialogVisible = true;
            },
            deleteRow(index, rows) {
                this.templateform.labs.splice(index, 1);
            },
            deleteLabContact(index, row) {
                var obj = {};
                obj = this.templateform.labs.find((lab) => {
                    if (row.labCode == lab.labCode) {
                        return lab;
                    }
                });
                if (obj != null && obj != undefined) {
                    obj.labContacts.splice(index, 1);
                }
            },
            queryTestPackageData() {
                getList().then(res => {
                    const data = res.data.data;
                    this.testPackageData = data;
                });
            },
            queryProductCategoryData() {
                var params = {};
                queryTrimsProductCategory(this.productCategoryParam.productLineId).then(res => {
                    const data = res.data.result;
                    this.productCategoryData = data;
                });
                /* getDictByCode(code).then(res => {
                     const data = res.data.data;
                     this.productCategoryData = data;
                 });*/
            },
            queryCustomerGroupData() {
                getCustomerGroup().then(res => {
                    const data = res.data.data;
                    this.customerGroupData = data;
                });
            },
          queryCustomerList(customerName,callback){
            if(customerName&&customerName.length<2){
             return false;
            }
            const param = {
              page:1,
              rows:200,
              displayType:'account',
              customerNameCn:customerName
            };
            //console.log(param)
            searchCustomerAndGroup(param).then(res => {
                console.log(res.data.data);
                const data = res.data.data;
                this.customerData =data;
                if(callback){
                    callback();
                }
            });
          },
            queryproductLineData() {
                getProductLine().then(res => {
                    const data = res.data.data;
                    this.productLineData = data;
                });
            },
            async queryDffData() {
                console.log(this.queryDffDataParam);
                var params = {};
                await getDffList(this.queryDffDataParam).then(res => {
                    const data = res.data.data;
                    data.forEach(da=>{
                        da['formType'] = 1;
                        da['id'] = da.formId+'_';
                    })
                    this.dffData = data;
                    //加载动态表单的list
                })
                let queryParam = {
                    productLineCode:this.templateform.productLineCode,
                    customerGroupCode:this.templateform.customerGroupCode
                }
                //console.log("query form dynamic",queryParam)
                await getFormDynamicList(0,1000,queryParam).then(res=>{
                    //console.log("form list res",res);
                    let {records} =  res.data.data;
                    //按照group分组，取最大versionId
                    let groupIdVersionId = {};
                    records.forEach(c=>{
                        let {groupId} = c;
                        if(groupIdVersionId[groupId]){
                            groupIdVersionId[groupId].push(c);
                        }else{
                            groupIdVersionId[groupId] = [c];
                        }
                    })
                    let keys = Object.keys(groupIdVersionId);
                    let newRecords = [];
                    for(let k of keys){
                        let arr = groupIdVersionId[k];
                        let maxObj = arr.reduce((maxVersion,current)=> (maxVersion.versionId>current.versionId)?maxVersion:current)
                        newRecords.push(maxObj);
                    }
                    let arr = [];
                    newRecords.forEach(c=>{
                        let {name,dffFormId,dffGridId,code,id,groupId,versionId} = c;
                        let formType = 2;
                        arr.push({
                            name,
                            formId:dffFormId,
                            gridId:dffGridId,
                            formType,
                            id,
                            code,
                            versionId,
                            formDesignGroupId:groupId
                        })
                    })
                    this.dffDynamicData = arr;
                },error=>{
                })
                //dff下拉框回显
                setTimeout(()=>{
                    this.initDffDynamicObj();
                },300)
            },
            initDffDynamicObj(){
                let {dffFormId,formType,formDesignGroupId} = this.templateform;
                if(!dffFormId){
                    return;
                }
                let obj = {};
                if(formType==1){
                    obj = this.dffData.find(item=>item.formId==dffFormId);
                }
                if(formType==2){
                    obj = this.dffDynamicData.find(item=>item.formDesignGroupId ==formDesignGroupId && item.formId==dffFormId);
                }
                this.dffAndDynamicObj = obj;
            },
            getLabCode(labCode) {
                this.templateform.labCode = labCode;
            },
            getLabName(labName) {
                this.templateform.labName = labName;
            },
            //获取弹框中选择的lab的数据
            getLabData(contactList) {
                console.log("new:", contactList);
                console.log("old:", this.templateform.labs);
                contactList.forEach(contact => {
                    const exist = this.templateform.labs.filter(item => {
                        return contact.labId == item.labId;
                        if (this.templateform.labs.length > 0) {
                            for (var i = 0; i < this.templateform.labs.length; i++) {
                                if (this.templateform.labs[i].labCode == item.labCode) {
                                    if (item.contactName == undefined || item.contactName == '') {//移除该数据
                                        this.templateform.labs.splice(i, 1);
                                    }
                                }
                            }
                        }
                        /* if(contact.labCode == item.labCode){
                             if(item.contactName==undefined || item.contactName ==''){//移除该数据
                                 this.templateform.labContacts.splice(i,1);
                             }
                         }*/
                        return ((contact.id == item.id)) && contact.labCode == item.labCode;
                    })
                    if (exist.length == 0) {
                        this.templateform.labs.push(contact);
                    }
                });


                //this.templateform.labs=val;

                this.selectLabDialogVisible = false;
            },
            testReceive(data) {
                const index = this.templateform.labs.findIndex(data => data.labCode === this.labCode);
                if (data && data.length) {
                    data.forEach(item => {
                        item.labCode = this.labCode
                    })
                }
                //原有实验室联系人
                var oldLabContact = this.templateform.labs[index].labContacts;
                if (oldLabContact && oldLabContact.length) {
                    var newContacts = [];
                    var contactNames = [];
                    oldLabContact.forEach(oldContact => {
                        contactNames.push(oldContact.contactName)
                    })
                    data.find((newContact) => {
                        if (!contactNames.includes(newContact.contactName)) {
                            newContacts.push(newContact);
                        }
                    });
                    if (newContacts && newContacts.length) {
                        newContacts.forEach(contact => {
                            oldLabContact.push(contact);
                        });
                    }
                    this.$set(this.templateform.labs[index], 'labContacts', oldLabContact);
                } else {
                    this.$set(this.templateform.labs[index], 'labContacts', data);
                    this.labContacts = data;
                }
                this.selectLabContactDialogVisible = false;
            },
            goBackTemplateList(){
                this.$router.push({path: '/template/list', query: {}});
            },
            confirmSaveTemplateForm(){
                this.templateform.confirmSave = 0;
                const loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                let customerRequireField = JSON.stringify(this.customerField.dataList.filter(c=>c.show));
                let saveTemplate = Object.assign({},this.templateform,{customerRequireField})
                submitTemplate(saveTemplate).then((res) => {
                            loading.close();
                            this.confirmSaveDisabled = false;
                            this.isDisabled = false;
                            this.$message({
                                type: "success",
                                message: this.$t('api.success')
                            });
                            this.$router.push({path: '/template/list', query: {}});

                        }, error => {
                            loading.close();
                            this.isDisabled = false;
                            this.$message.error(this.$t('api.error'));
                            console.log(error);
                        });
            },
            submitTemplateForm() {
                this.isDisabled = true;
                this.$refs['templateform'].validate((valid) => {
                    if (valid) {
                        const loading = this.$loading({
                            lock: true,
                            text: 'Loading',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });

                        this.isDisabled = true;
                        this.templateform.confirmSave = 1;
                        this.templateform.cclFieldConfigs = this.fieldSettingsTableData
                        this.templateform.specificCode = this.selSpecificCodes.join(',');
                        console.log(this.selSpecificCodes);
                        console.log(this.templateform);
                        checkDefaultTemplate(this.templateform).then((res)=>{
                            this.isDisabled = false;
                            if(res.data.status==40002){
                                loading.close();
                                this.confirmSaveDisabled = true;
                                return;
                            }
                            let customerRequireField = JSON.stringify(this.customerField.dataList.filter(c=>c.show));
                            let saveTemplate = Object.assign({},this.templateform,{customerRequireField})
                            submitTemplate(saveTemplate)
                            .then((res) => {
                                loading.close();
                                this.isDisabled = false;
                                this.$message({
                                    type: "success",
                                    message: this.$t('api.success')
                                });
                                this.$router.push({path: '/template/list', query: {}});

                            }, error => {
                                loading.close();
                                this.isDisabled = false;
                                this.$message.error(this.$t('api.error'));
                                console.log(error);
                            })
                        },error=>{
                            //loading.close();
                            //this.confirmSaveDisabled = true;
                            loading.close();
                            this.isDisabled = false;
                            this.$message.error(this.$t('api.error'));
                            console.log(error);
                        })
                        // }
                    } else {
                        this.isDisabled = false;
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            //打开选择实验室dialog
            addLab() {
                //查询是否选择产品线
                if (this.templateform.productLineCode == '' || this.templateform.productLineCode == null) {
                    this.$notify({
                        title: this.$t('tip'),
                        message: this.$t('template.selProductLine'),
                        type: 'warning'
                    });
                    return false;
                }
                this.isShowLab=true;
                this.selectLabDialogVisible = true;
            },
            labContactChange(row, expandedRows) {
                this.$refs.labContactTable.setCurrentRow();
                this.currentRow = row
                if (this.expands.join(',').indexOf(row.labCode) === -1) {
                    this.expands = [this.currentRow.labCode]
                    //this.searchLabContactList(row.id);
                } else {
                    this.expands.splice(0, this.expands.length)
                }
                /* if(vlaue2.length!=0){//展开
                     this.searchLabContactList(value1.id);
                 }*/
            },
            //打开选择实验室联系人dialog
            addLabContact(index, rows) {
                var row = index;
                this.$refs.labContactTable.setCurrentRow();
                this.currentRow = row
                if (this.expands.join(',').indexOf(row.labCode) === -1) {
                    this.expands = [this.currentRow.labCode]
                    this.labCode = index.labCode;
                    this.labType = index.labType;
                    this.selectLabContactDialogVisible = true;
                } else {
                    this.expands.splice(0, this.expands.length)
                }

            },
            searchChange(params, done) {
                this.query = params;
                this.onLoad(this.page, params);
                done();
            },
            selectionChange(list) {
                this.selectionList = list;
            },

            currentChange(currentPage) {
                this.page.currentPage = currentPage;
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
            getDigitalReportSetting(){
              let _this = this;
              queryDigitalReportSetting(this.queryDigitalReport).then(res=>{
                if(res && res.data && res.data.data){
                  _this.printTemplateData = res.data.data;
                }
              })
            },
            initCustomerFieldData(){
                this.customerField.dataList = [];
                let usageTypes = [
                    {type: "Supplier", code: 'supplier'},
                    {type: "Manufacture", code: 'manufacturer'},
                    {type: "Agent", code: 'agent'}
                ]
                usageTypes.forEach(usageType=>{
                    this.customerField.dataList.push({title:'customer.new.'+usageType.code,usageType:usageType.type,show:false, customerNameEn:0,customerNameZh:0,customerAddressEn:0,customerAddressZh:0,customerReferenceNo:0,contactName:0,contactTelephone:0,contactEmail:0});
                })
            },
        }
    };
</script>

<style scoped>
    .el-table__header thead .el-table-column--selection label {
        display: none;
    }

    .el-table__header thead .el-table-column--selection div {
        content: "全选";
    }
</style>
