<template>
    <div>
        <!-- <el-form :inline="true" :model="formInline" size="medium">
            <el-form-item>
                <el-input clearable v-model="query.customerGroupName" :placeholder="$t('buyer.placeholder.name')"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">{{$t('operation.search')}}</el-button>
                <el-button v-if="permissionList.addBtn" type="primary" @click="addRow">{{$t('operation.add')}}</el-button>
            </el-form-item>
            <span style="float: right;margin-right:20px;color:red;">
                {{$t('operation.remark')}} {{$t('buyer.customerGroupTip')}}
            </span>
        </el-form> -->

        <el-form :inline="true" :model="formInline" @submit.native.prevent size="medium" class="text-right">
            <el-form-item>
                <el-input v-model="query.customerGroupName" @input="onSearch" @keyup.enter.native="onSearch" @clear="onSearch" :placeholder="$t('buyer.placeholder.name')" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search" @click.stop="onSearch"></i>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-button v-if="permissionList.addBtn" @click="addRow" class="line-btn" id="add-buyer">
                    <i class="el-icon-circle-plus-outline"></i>
                    {{$t('operation.add')}}
                </el-button>
            </el-form-item>
        </el-form>
         <div style="margin: -10px 0 10px;">
            <span style="color:red;padding-top: 6px; display: block;">
                {{$t('operation.remark')}} {{$t('buyer.customerGroupTip')}}
            </span>
        </div>

        <el-table
                ref = "buyerTable"
                :data="tableData"
                style="width: 100%">
            <el-table-column
                    type="index"
                    label="#"
                    width="50">
            </el-table-column>

            <el-table-column  prop="customerGroupCode"
                              :label="$t('customer.relationship.title.buyerCode')"
                              width="200">
                <template slot-scope="scope">
                    <span v-if="scope.row.isSgsUser == 1" style="color: red;">*</span>
                        {{scope.row.customerGroupCode}}
                </template>
            </el-table-column>

            <el-table-column
                    prop="customerGroupName"
                    :label="$t('customer.relationship.title.buyerName')">
            </el-table-column>
            <el-table-column
                    :label="$t('common.status.title')"
                    width="80px">
                <template slot-scope="scope">
                    <el-tooltip :content="scope.row.activeIndicator==='A'?$t('common.status.enable'):$t('common.status.disable')" placement="top">
                        <el-switch
                                v-model="scope.row.activeIndicator"
                                active-color="#ff6600"
                                inactive-color="#D9D9D9FF"
                                active-value = "A"
                                inactive-value = "I"
                                @change="changeStatus(scope.row)">
                        </el-switch>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChange"
                @current-change="currentChange"
                :current-page="page.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total">
        </el-pagination>
        <el-dialog
            width="660px"
            append-to-body="true" 
            @close="handleClose"
            :modal="$store.state.user.addBuyer ? false : true" 
            :title="$t('buyer.title.add')" 
            :visible.sync="dialogFormVisible" 
            :close-on-click-modal="false" 
            id="add-buyer-dialog"
            class="z-index-cover">
            <el-form ref="form" :model="form"  label-width="160px" label-position="left"  size="medium">
                <el-form-item id="input-buyer-name" :label="$t('customer.relationship.title.buyerName')" :rules="{ required: true, message: $t('term.customerGroupBlur'), trigger: 'blur' }" prop="customerGroupName">
                    <el-autocomplete
                                v-model="form.customerGroupName"
                                filterable
                                :trigger-on-focus="false"
                                :fetch-suggestions="searchCustomerGroup"
                                @select="selectCustomerGroupChange"
                                style="width: 100%;">
                    </el-autocomplete>
                </el-form-item>
            </el-form>
            <div id="stepContent" :class="{'submit-tip-step': submitTips}"></div>
            <div class="bottom clearfix " style="text-align: center">
                <el-button size="small" @click="dialogFormVisible = false">{{$t('operation.cancel')}}</el-button>
                <el-button size="small" type="primary" @click="submitForm('form')" :loading="submitLoading" id="add-buyer-confirm">{{$t('operation.submit')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {addRelationship,getPage,remove, addRelationshipNeedApprove,addBuyerRelationship,getCustomerGroupPage} from "@/api/customer/relationship";
    import {validatenull} from "@/util/validate";
    import {getCustomerGroupByParms} from "@//api/common";
    import { mapGetters, mapState } from "vuex";
    import {update} from "@/api/customer/buyerRelationship";
    export default {
        name: "buyers",
        props:{
            customerId: {
                type: Number,
                default: null,
            }
        },
        data(){
            return{
                title:'',
                query: {},
                form: {},
                tableData: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                dialogFormVisible: false,
                customerGroupOptions: [],
                submitLoading: false,
                customerGroupParam:{},
                stepContent: '',
                submitTips: false,
            }
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission['sgs:customer:relationship:buyer:add'],false),
                    deleteBtn: this.vaildData(this.permission['sgs:customer:relationship:buyer:delete'],false),
                };
            }
        },
        watch: {
            '$store.state.user.addBuyer' (newVal, oldVal) {
                console.log('添加买家引导 ', newVal)
                this.dialogFormVisible = newVal
                if(newVal) {
                    setTimeout(() => {
                        document.querySelector("#add-buyer-dialog #stepContent").appendChild(document.querySelector('.step-content'))
                    }, 200)
                }
            },
            '$store.state.user.taskType'(newVal, oldVal) {
                if(newVal == '') {
                    this.dialogFormVisible = false
                }
            },
            'form.customerGroupName'(newVal, oldVal) {
                if(newVal && this.$store.state.user.taskType != '') {
                    this.submitTips = true
                    document.querySelector("#stepContent .tit").innerText = this.$t('guide.clickSubmit')
                    document.querySelector("#stepContent .step-num span").innerText = this.$t('guide.step5') + this.$t('guide.total5')
                }
            }
        },
        methods:{
            handleClose() {
                this.$store.commit('SET_TASK_TYPE', '')
                this.$store.commit('SET_GUIDE', { name: 'addBuyer', val: false }) // 关闭step中的添加弹窗
                document.querySelectorAll("#stepContent").forEach(item => item.innerHTML = '')
                this.form.customerGroupName = ''
                this.submitTips = false
            },
            onSearch() {
                this.page.currentPage = 1;
                this.onLoad(this.page);
            },
            onLoad(page, params = {}) {
                if(!validatenull(this.userInfo.bossNo)){
                    this.query.customerNumber = this.userInfo.bossNo;
                    getCustomerGroupPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                        this.tableData = res.data.data.records;
                        debugger;
                        this.page.total = res.data.data.total;
                    });
                }
            },
            submitForm() {
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        this.submitLoading=true;
                        this.form.supplierCustomerNo = this.userInfo.bossNo;
                        this.form.customerNumber = this.userInfo.bossNo;
                        this.form.createdBy = this.userInfo.account;
                        this.form.createUser = this.userInfo.account;
                        //判断是否需要审核
                        addRelationshipNeedApprove(this.form).then(res =>{
                            console.log(res.data.data);
                            if(this.$store.state.user.taskType != '') { // 完成添加买家引导
                                let task = JSON.parse(localStorage.getItem('guideTask'))
                                let index = task.findIndex(item => item.type == 'addBuyer')
                                if(!task[index].val) {
                                    task[index].val = true  // 设置当前任务为完成状态
                                    this.$notify({
                                        title: this.$t('success'),
                                        message: this.$t('guide.addBuyerSuccess'),
                                        type: 'success',
                                        duration: 1500
                                    });
                                }
                                localStorage.setItem('guideTask', JSON.stringify(task))
                                let complete = task.every(item => item.val == true);
                                if(!complete) {
                                    setTimeout(()=>{
                                        this.$store.commit('SET_TASK_DIALOG', true) // 2s后打开任务列表
                                    }, 2000)
                                }
                            }

                            if (res.data.data){
                                addBuyerRelationship(this.form).then(res =>{
                                    this.$message({
                                        type: "success",
                                        message: this.$t('customer.title.relationshipNeedApprove')
                                    });
                                    this.submitLoading = false;
                                    this.dialogFormVisible=false;
                                    this.onLoad(this.page);
                                }).catch(() => {
                                    this.submitLoading = false;
                                });
                            } else {
                                addRelationship([this.form]).then(res =>{
                                    if("1" == res.data.success){
                                        this.$message({
                                            type: "success",
                                            message: this.$t('api.success')
                                        });
                                        this.submitLoading = false;
                                        this.dialogFormVisible=false;
                                        this.onLoad(this.page);
                                    }else{
                                        this.$message({
                                            type: "error",
                                            message: res.data.errorMessage
                                        });
                                        this.submitLoading = false;
                                    }
                                }).catch(() => {
                                    this.submitLoading = false;
                                });
                            }
                        }).catch(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        this.submitLoading=false;
                        return false;
                    }
                });
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
                this.onLoad(this.page);
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
                this.onLoad(this.page);
            },
            removeRow(row){

                this.form.customerGroupCode = row.customerGroupCode;
                this.form.supplierCustomerNo = row.customerNumber;
                this.form.createdBy = this.userInfo.account;

                remove([this.form]).then(res =>{
                    if("1" == res.data.success){
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.submitLoading = false;
                        this.dialogFormVisible=false;
                        this.onLoad(this.page);
                    }else{
                        this.$message({
                            type: "error",
                            message: res.data.errorMessage
                        });
                    }
                });
            },
            addRow(){
                this.form = {};
                this.title = this.$t('buyer.title.add');
                this.dialogFormVisible =true;
            },
            changeStatus(row) {
                // 如果禁用调用删除
                if ('A' === row.activeIndicator) {
                    this.submitLoading = true;
                    this.form.customerGroupCode = row.customerGroupCode;
                    this.form.supplierCustomerNo = row.customerNumber;
                    this.form.createdBy = this.userInfo.account;
                    this.form.bossNo = this.userInfo.bossNo;
                    addRelationship([this.form]).then(res => {
                      if("1" == res.data.success){
                        this.$message({
                          type: "success",
                          message: this.$t('api.success')
                        });
                        this.submitLoading = false;
                        this.dialogFormVisible = false;
                        this.onLoad(this.page);

                      }else{
                        this.$message({
                          type: "error",
                          message: res.data.errorMessage
                        });
                        this.submitLoading = false;
                        this.dialogFormVisible = false;
                        //this.onLoad(this.page);
                      }
                    });
                }
                // 如果启用调用创建
                else {
                    this.removeRow(row)
                }
            },
            //代理商客户组查询 输入3个字符后再执行查询匹配 最大查询五条数据，防止全部客户组暴露
            searchCustomerGroup(query, callback) {
                if (!validatenull(query) && query.length >= 3) {//输入满足三个字符后查询客户组数据
                    this.customerGroupParam.groupName = query;
                    getCustomerGroupByParms( this.customerGroupParam).then(res => {
                        const data = res.data.data;
                        const results = [];
                        data.forEach(function(currentValue, index){
                            results.push({'value':currentValue.customerGroupName,"customerGroupCode":currentValue.customerGroupCode,"customerGroupId":currentValue.customerGroupId});
                        });
                        callback(results);
                    });
                }
            },
            selectCustomerGroupChange(val) {
                this.form.customerGroupCode = val.customerGroupCode;
            },
        },
        created() {
            this.onLoad(this.page);
        }
    }
</script>

<style lang="scss" scoped>
#add-buyer-dialog {
    .el-dialog {
        .el-form-item__label {
            padding-top: 6px;
        }
        #stepContent {
            position: absolute;
            left: 180px;
            bottom: 80px;
            transition: all .2s;
        }
        .submit-tip-step {
            transform: translate(150px, 50px);
        }
    }
}
</style>
