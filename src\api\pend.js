import request from '@/router/axios';
import {baseUrl} from '@/config/env';

export const queryPendList = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/pend/list',
        method: 'post',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const queryPendCount = (params) => {
    return request({
        url: '/api/sgs-mart/pend/count',
        method: 'post',
        params: {
            ...params
        }
    })
}
export const updatePendReadStatus = (form) => {
    return request({
        url: '/api/sgs-mart/pend/updatePendReadStatus',
        method: 'post',
        data: form
    })
}
