<template>
    <basic-container>
        <el-row>
            <h1 class="top-title">{{ $t("trf.trfTemplateList") }}</h1>
        </el-row>

        <div class="wrap">
            <div style="position: relative">
                <el-table
                    class="scrollbar_table"
                    id="trfTable"
                    fixed
                    :data="trfTemplateList"
                    v-loading="loading"
                    :element-loading-text="$t('loading')"
                    style="width: 100%"
                    height="500"
                >
                    <el-table-column
                        prop="trfTemplateName"
                        :show-overflow-tooltip="true"
                        :label="$t('userTemplate.name')"
                        width="auto"
                        min-width="120"
                    >
                        <template slot="header" slot-scope="scope">
                            <div>{{ $t("userTemplate.name") }}</div>
                            <el-input
                                size="small"
                                :value="trfTemplateQuery.trfTemplateName"
                                :placeholder="$t('userTemplate.name')"
                                @keyup.enter.native="handelSubmit"
                                @input="
                                    (val) =>
                                        (trfTemplateQuery.trfTemplateName = val)
                                "
                                clearable
                                @clear="handelSubmit"
                            ></el-input>
                        </template>
                        <template slot-scope="scope">
                            <a
                                style="
                                    text-decoration-line: underline;
                                    color: #aeacbb;
                                "
                                href="javascript:void(0);"
                                @click="trfTemplateClick(scope.row)"
                            >
                                {{ scope.row.trfTemplateName }}
                            </a>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="trfName"
                        :label="$t('userTemplate.trfName')"
                        min-width="140"
                    >
                        <template slot="header" slot-scope="scope">
                            <div>{{ $t("userTemplate.trfName") }}</div>
                            <el-input
                                size="small"
                                :value="trfTemplateQuery.trfName"
                                :placeholder="$t('userTemplate.trfName')"
                                @keyup.enter.native="handelSubmit"
                                @input="
                                    (val) => (trfTemplateQuery.trfName = val)
                                "
                                clearable
                                @clear="handelSubmit"
                            ></el-input>
                        </template>
                        <template slot-scope="scope">
                            <el-tooltip
                                v-if="
                                    $lodash.get(scope.row, 'trfName', '')
                                        .length > 35
                                "
                                :content="$lodash.get(scope.row, 'trfName', '')"
                                placement="top"
                            >
                                <div>
                                    <span
                                        v-text="
                                            scope.row.status == 0
                                                ? lengthFilter(
                                                      $lodash.get(
                                                          scope.row,
                                                          'trfName',
                                                          '',
                                                      ),
                                                      35,
                                                  )
                                                : $lodash.get(
                                                      scope.row,
                                                      'trfName',
                                                      '',
                                                  )
                                        "
                                    ></span>
                                    <el-tag
                                        v-if="scope.row.status == 0"
                                        size="mini"
                                        type="info"
                                    >
                                        {{ $t("userTemplate.phaseout") }}
                                    </el-tag>
                                </div>
                            </el-tooltip>
                            <div v-else>
                                <span
                                    v-text="
                                        scope.row.status == 0
                                            ? lengthFilter(
                                                  $lodash.get(
                                                      scope.row,
                                                      'trfName',
                                                      '',
                                                  ),
                                                  35,
                                              )
                                            : $lodash.get(
                                                  scope.row,
                                                  'trfName',
                                                  '',
                                              )
                                    "
                                ></span>
                                <el-tag
                                    v-if="scope.row.status == 0"
                                    size="mini"
                                    type="info"
                                >
                                    {{ $t("userTemplate.phaseout") }}
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="trfBuyerCustomerGroupName"
                        :show-overflow-tooltip="true"
                        :label="$t('userTemplate.customerName')"
                        width="auto"
                        min-width="100"
                    >
                        <template slot="header" slot-scope="scope">
                            <div>{{ $t("userTemplate.customerName") }}</div>
                            <el-input
                                size="small"
                                :value="
                                    trfTemplateQuery.trfBuyerCustomerGroupName
                                "
                                :placeholder="$t('userTemplate.customerName')"
                                @keyup.enter.native="handelSubmit"
                                @input="
                                    (val) =>
                                        (trfTemplateQuery.trfBuyerCustomerGroupName =
                                            val)
                                "
                                clearable
                                @clear="handelSubmit"
                            ></el-input>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-text="
                                    $lodash.get(
                                        scope.row,
                                        'trfBuyerCustomerGroupName',
                                        '',
                                    )
                                "
                            ></span>
                        </template>
                    </el-table-column>

                    <!--        <el-table-column prop="status" label="测试"  width="auto"  min-width="100">
            <template slot-scope="scope">
              <span v-text="$lodash.get(scope.row, 'status', '')"></span>
            </template>
          </el-table-column>-->

                    <el-table-column
                        prop="createTime"
                        :label="$t('user.createDate')"
                        min-width="60"
                    >
                        <template slot="header" slot-scope="scope">
                            <div>{{ $t("user.createDate") }}</div>
                            <div style="width: 260px; height: 32px"></div>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-text="
                                    $lodash.get(scope.row, 'createTime', '')
                                "
                            ></span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('operation.title')"
                        width="240"
                        align="center"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <!-- <el-button @click="showLabDetail(scope.row)" type="text" size="mini">查看</el-button>-->
                            <el-button
                                @click="deleteTrfTemplate(scope.row)"
                                type="text"
                            >
                                {{ $t("operation.remove") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    @size-change="sizeChange"
                    @current-change="currentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total"
                ></el-pagination>
            </div>
        </div>
    </basic-container>
</template>

<script>
import { mapGetters } from "vuex"
import moment from "moment"
import i18n from "@/lang"
import { removeTrfTemplate, queryTrfTemplateList } from "@/api/welIndex"

export default {
    data() {
        return {
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0,
            },
            sort: { descs: "update_time" },
            trfTemplateList: [],
            trfTemplateQuery: {
                trfTemplateName: "",
                trfName: "",
                trfBuyerCustomerGroupName: "",
            },
        }
    },
    created() {
        this.initLoadTrfTemplate()
    },
    /*filters: {
    lengthFilter: function (value,len) {
      value = value.toString();
      if (value.length > len) {
        value = value.slice(0, len) + '...'
      }
      return value
    }
  },*/
    methods: {
        lengthFilter: function (value, len) {
            value = value.toString()
            if (value.length > len) {
                value = value.slice(0, len) + "..."
            }
            return value
        },
        async handelSubmit() {
            this.page.currentPage = 1
            await this.initLoadTrfTemplate()
        },
        initLoadTrfTemplate() {
            this.loading = true
            var params = {}
            queryTrfTemplateList(
                this.page.currentPage,
                this.page.pageSize,
                Object.assign(params, this.trfTemplateQuery, this.sort),
            )
                .then((res) => {
                    this.loading = false
                    this.trfTemplateList = res.data.data.records
                    this.page.total = res.data.data.total
                })
                .catch(() => {
                    this.loading = false
                })
        },

        //分页查询
        sizeChange(pageSize) {
            this.page.pageSize = pageSize
            this.initLoadTrfTemplate()
        },
        currentChange(pageCurrent) {
            this.page.currentPage = pageCurrent
            this.initLoadTrfTemplate()
        },

        search() {
            this.page.currentPage = 1
            this.initLoadTrfTemplate()
        },

        deleteTrfTemplate(row) {
            this.$confirm(this.$t("operation.confirmDelete"), this.$t("tip"), {
                confirmButtonText: this.$t("submitText"),
                cancelButtonText: this.$t("cancelText"),
                type: "warning",
            })
                .then(() => {
                    removeTrfTemplate(row.id).then(
                        () => {
                            this.$message({
                                type: "success",
                                message: "操作成功!",
                            })
                            this.initLoadTrfTemplate()
                        },
                        (error) => {
                            console.log(error)
                        },
                    )
                })
                .catch(() => {
                    /* this.$message({
          type: 'info',
          message: '已取消删除'
        });*/
                })
        },

        trfTemplateClick(row) {
            var trfType = row.trfType
            //给出提示
            let tipMsg = this.$t("userTemplate.changeToTrfMsg")
            if (row.status == 0) {
                //失效
                tipMsg = this.$t("userTemplate.outChangeToTrfMsg")
            }
            this.$confirm(tipMsg, this.$t("tip"), {
                confirmButtonText: this.$t("submitText"),
                cancelButtonText: this.$t("cancelText"),
                type: "warning",
            })
                .then(() => {
                    if (
                        trfType == null ||
                        trfType == undefined ||
                        trfType == ""
                    ) {
                        trfType = 1
                    }
                    if (trfType == 30) {
                        const routerUrl = this.$router.resolve({
                            path: "/afl/trf/trfDetail",
                            query: {
                                trfTemplateId: row.id,
                                actionType: "trfTemplate",
                            },
                        })
                        window.open(routerUrl.href, "_blank")
                    } else {
                        const routerUrl = this.$router.resolve({
                            path: "/trf/trfDetail",
                            query: {
                                trfTemplateId: row.id,
                                flag: trfType,
                                actionType: "trfTemplate",
                            },
                        })
                        window.open(routerUrl.href, "_blank")
                    }
                })
                .catch(() => {})
        },
    },
}
</script>
<style lang="scss">
.top-title {
    font-size: 24px;
    font-family: "Regular", Arial, "localArial", "Microsoft Yahei",
        "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
    // font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 32px;
    margin: 0px 0 17px;
}

.el-icon-arrow-right:before {
    font-weight: 700;
}

.trf-o-btn {
    margin-top: -14px;
}

.f-sort a {
    float: left;
    padding: 0 9px;
    height: 23px;
    border: 1px solid #ccc;
    line-height: 23px;
    margin-right: -1px;
    background: #fff;
    color: #333;
}

a.curr {
    border-color: #919191;
    background: #919191;
    color: #fff;
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #ffffff;
    background-color: #919191;
    border-color: #919191;
    -webkit-box-shadow: -1px 0 0 0 #919191;
    box-shadow: -1px 0 0 0 #919191;
}

.otherActiveClass {
    color: #fff;
    background-color: #ebeef5;
}

.row-expand-cover {
    .el-table__expand-icon {
        visibility: hidden;
    }
}

.otherActiveClass:hover {
    background: #ebeef5;
    color: #fff;
}

.newIcon {
    position: absolute;
    right: 0;
    top: 0;
}

.wrap {
    background: #fff;
    padding: 10px 10px;
}

.date-group {
    button {
        border: 0;
        border-bottom: 1px solid transparent;
        border-radius: 0;
        padding: 10px 0;
        margin: 0 10px;

        &:hover,
        &:focus {
            background-color: initial;
            border-color: transparent;
        }

        &.active {
            border-bottom-color: #f60;
            color: #f60;
        }
    }

    .bg_orange {
        color: #ea4336;
        margin-right: 20px;

        img {
            margin-top: -7px;
        }
    }
}

.btn-sort {
    .active {
        color: #ff6600;
        border-color: #ffd1b3;
        background-color: #fff0e6;
    }
}

.scrollbar_table {
    .cell {
        white-space: nowrap !important;
    }

    th {
        padding-top: 16px;

        &.is-left {
            padding-top: 22px;
        }

        .el-input__inner {
            background: transparent;
            border-color: #d8d8d8;
            padding-left: 0;
            /* color: #000; */
            &::-webkit-input-placeholder {
                /* color: transparent; */
            }
        }

        .date-input {
            .el-input__inner {
                padding-left: 24px;
            }
        }

        .cell {
            > div {
                &:last-of-type {
                    margin-top: 8px;
                }
            }

            .el-select .el-input__inner:focus {
                border-color: #d8d8d8;
            }

            .el-select .el-input .el-select__caret {
                color: #d8d8d8;
            }

            .el-input__prefix {
                left: -5px;
            }
        }

        &:last-of-type {
            .cell {
                margin-top: -49px;
            }

            .operatop > span {
                float: right;
            }
        }
    }
}

#trf-list-filter {
    float: left;
    width: fit-content;
    /* margin-bottom: 24px; */
    .el-form-item {
        margin-bottom: 0;
    }
}

.sort-btn {
    float: right;

    button {
        border-color: #ccc;
    }
}

.add-menu {
    li {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        display: inline-block;
        padding-left: 10px;
        transition: all 0.2s;

        a {
            display: block;
        }

        &:hover {
            background: rgba(255, 102, 0, 0.1);
            color: #ff6600;
        }
    }
}

.plain-black {
    height: 40px;
    padding-top: 0;
    padding-bottom: 0;
}

.trf-sort-icon {
    /* transition: .2s all; */
    &.isSort {
        transform: rotate(180deg);
    }
}

.tools {
    /* display: flex;
  justify-content: space-between; */
    margin-bottom: 24px;
}

.filter-date {
    width: 260px !important;
    padding-right: 0 !important;
}

.check-time-out-2 {
    display: none;
}

@media screen and (max-width: 1900px) {
    #trf-list-filter {
        float: left;
    }
    .en-trf-list-filter {
        margin-bottom: 15px;
    }
    .en-sort-btn {
        width: 100%;
    }
}

@media screen and (max-width: 1600px) {
    .cn-trf-list-filter {
        margin-bottom: 15px;
    }
    .cn-sort-btn {
        float: left;
    }
}

@media screen and (max-width: 1366px) {
    #trf-list-filter .el-input__inner,
    .filter-date {
        width: 260px !important;
    }
    .en-trf-list-filter .check-time-out-1 {
        /* display: none; */
    }
    .en-sort-btn {
        /* text-align: right; */
        .check-time-out-2 {
            /* display: inline-block;
      height: 40px;
      line-height: 40px; */
        }

        button {
            /* margin-left: 16px !important; */
        }
    }
    .cn-sort-btn {
        .check-time-out-2 {
            /* display: none; */
        }
    }
}
</style>
