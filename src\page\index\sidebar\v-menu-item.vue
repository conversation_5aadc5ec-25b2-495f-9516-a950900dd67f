<template>

  <div v-if="item.children">
    <!-- 实际可点击 -->
    <el-menu-item v-if="validatenull(item[childrenKey]) && vaildRoles(item)" :index="item.id" @click="open(item)"
      :key="item[labelKey]" :class="{ 'is-active': vaildAvtive(item) }">
        <span slot="title" :alt="item[pathKey]" class="nosub-menu">{{ item.name }}</span>
    </el-menu-item>
    <!-- 嵌套 -->
    <el-submenu :index="item.id" :id="item.name == '设置' || item.name == 'Setting' ? 'guideSetting' : 'noSetting'">
      <template slot="title">
       <!-- <i :class="item[iconKey]"></i>-->
        <span slot="title" >{{ generateTitle(item) }}</span>
      </template>
      <template v-for="(child, cindex) in item[childrenKey]">
        <!--有child-->
        <navigation-item v-if="!validatenull(child[childrenKey])" :key="child[labelKey]" :item="child" :props="props"
          :screen="screen" />
        <!--无child-->
        <el-menu-item v-else :key="cindex" :index="child.id,cindex" :class="{ 'is-active': vaildAvtive(child) }"
          @click="open(child)">
          <!--<i :class="child[iconKey]" style="position: relative;"></i>-->
          <span slot="title">{{ generateTitle(child) }}
          </span>
        </el-menu-item>
      </template>
    </el-submenu>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { validatenull } from "@/util/validate";
import config from "./config.js";
export default {
  name: 'NavigationItem',
  data() {
    return {
      config: config
    };
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    props: {
      type: Object,
      default: () => {
        return {};
      }
    },
    screen: {
      type: Number
    },
  },
  mounted() {

  },
  computed: {
    ...mapGetters(["roles"]),
    labelKey() {
      return this.props.label || this.config.propsDefault.label;
    },
    pathKey() {
      return this.props.path || this.config.propsDefault.path;
    },
    iconKey() {
      return this.props.icon || this.config.propsDefault.icon;
    },
    childrenKey() {
      return this.props.children || this.config.propsDefault.children;
    },
    nowTagValue() {
      return this.$router.$avueRouter.getValue(this.$route);
    }
  },

  methods: {
    chooseRole(role) {
      this.$emit('chooseRole', role)
    },
    generateTitle(item) {
      return this.$router.$avueRouter.generateTitle(
        item[this.labelKey],
        (item.meta || {}).i18n
      );
    },
    vaildAvtive(item) {
      const groupFlag = (item["group"] || []).some(ele =>
        this.$route.path.includes(ele)
      );
      return this.nowTagValue === item[this.pathKey] || groupFlag;
    },
    vaildRoles(item) {
      item.meta = item.meta || {};
      return item.meta.roles ? item.meta.roles.includes(this.roles) : true;
    },
    validatenull(val) {
      return validatenull(val);
    },
    open(item) {
      if (this.screen <= 1) this.$store.commit("SET_COLLAPSE");
      this.$router.$avueRouter.group = item.group;
      this.$router.$avueRouter.meta = item.meta;
      if(item.path.indexOf('/ccl/trf/newTrf') != -1 || item.path.indexOf('/afl/trf/newTrf') != -1) {
        const routeData = this.$router.resolve({
          path: this.$router.$avueRouter.getPath({
            name: item[this.labelKey],
            src: item[this.pathKey],
            i18n: (item.meta || {}).i18n
          }),
          query: item.query
        });
        window.open(routeData.href, '_blank');
      } else if(/\/web\/|\/knowledge\//.test(item.path)){
        window.open(item.path, '_blank');
      } else {
        this.$router.push({
          path: this.$router.$avueRouter.getPath({
            name: item[this.labelKey],
            src: item[this.pathKey],
            i18n: (item.meta || {}).i18n
          }),
          query: item.query
        });
      }
    }
  }
}
</script>
<style scoped>
.nosub-menu{
  font-size: 20px!important;
  position: relative;
  top: 2px;
}
</style>