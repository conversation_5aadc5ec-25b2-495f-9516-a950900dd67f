<template>
  <div class="register-container">
    <el-card style="padding: 48px 98px; position: relative;">
      <top-lang :loadMenu="false" :is-load-lanage-by-ip="true" style="position: absolute; right:30px; top:25px; z-index:9;"></top-lang>
      <h2 class="text-center">{{ $t('register.tip.perfectInfo') }}</h2>
      <el-form :model="form" ref="registerForm" status-icon :rules="rules" label-width="160px" label-position="top" class="demo-ruleForm">
        <div class="sgs-group">
          <h3>{{ $t('customer.title.admin') }}</h3>
        </div>
        <el-row :gutter="64">
          <el-col :span="12">
            <el-form-item :label="$t('register.loginAccount')">
              <el-input maxlength="200" clearable v-model="loginAccount" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('account.userName')" prop="userName">
              <el-input maxlength="100" clearable v-model="form.userName" autoComplete="off"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('register.phoneNumber')" prop="contactMobile">
              <el-input maxlength="30" clearable v-model="form.contactMobile" :disabled="contactMobileDisabled">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('account.email')" prop="email">
              <el-input maxlength="150" clearable v-model="form.email" :placeholder="$t('register.emailBlur')"
                :disabled="emailDisabled"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="64">
          <el-col :span="12">
            <el-form-item :label="$t('account.password')" prop="password">
              <el-input type="password" v-model="form.password" :placeholder="$t('login.password')" maxlength="50" autocomplete="off" show-password></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('account.passwordConfirm')" prop="doublePassword">
              <el-input type="password" v-model="form.doublePassword" :placeholder="$t('login.rePassword')" auto-complete="new-password" maxlength="50" show-password></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="sgs-group">
          <el-row>
            <h3>{{ $t('customer.title.base') }}</h3>
          </el-row>
        </div>
        <el-row :gutter="64">
          <el-col :span="12">
            <el-form-item :label="$t('customer.name')" prop="customerName">
              <el-autocomplete ref="autocomplete" clearable v-model="form.customerName" :fetch-suggestions="querySearch"
                :placeholder="$t('register.companyNameBlur')" :trigger-on-focus="false" @select="handleSelect"
                @clear="clearCustomer" @blur="checkExists" maxlength="200" style="width: 100%"></el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('customer.taxNo')" prop="taxNo" :placeholder="$t('register.taxNoBlur')">
              <el-input clearable ref="taxNo" v-model="form.taxNo" :disabled="taxDisabledFlag" maxlength="50">
              </el-input>
            </el-form-item>
          </el-col>

<!--          <el-col :span="12">
            <el-form-item :label="$t('register.serveType')" prop="productLineCode">
              <el-select v-model="form.productLineCode" clearable :placeholder="$t('register.serveTypeBlur')"
                @change="selectServiceTypeChange" style="width: 100%;">
                <el-option v-for="(serviceType, index) in serviceTypeData" :key="index" :label="serviceType.serviceName"
                  :value="serviceType.productLineCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>-->
          <el-col :span="24">

            <el-form-item
                :label="$t('register.serviceUnit')"
                prop="selServiceUnits">
              <el-select v-model="form.selServiceUnits"
                         multiple
                         collapse-tags
                         :placeholder="$t('register.serviceUnitBlur')"
                         @change="selectServiceTypeChange" style="width: 100%;">
                <el-option v-for="(serviceUnit, index) in serviceUnits" :key="serviceUnit.serviceUnitCode" :label="serviceUnit.serviceUnitName"
                           :value="serviceUnit.serviceUnitCode">
<!--                  <span style="float: left">{{ serviceUnit.serviceUnitName }}</span>-->
<!--                  <span style="float: right; color: #8492a6; font-size: 13px">
                    <el-button type="text" size="small" @click="setDefault(serviceUnit)">set default</el-button>
                  </span>-->
                </el-option>
              </el-select>
            </el-form-item>
<!--            <el-form-item :label="$t('common.isDefault')"  prop="selDefaultServiceUnit">
              <el-select v-model="selDefaultServiceUnit"
                         placeholder=""
                         @change="selectDefaultServiceUnitChange" style="width: 30%;">
                <el-option v-for="(serviceUnit, index) in defaultServiceUnitDatas" :key="serviceUnit.sysKey" :label="serviceUnit.sysValue"
                           :value="serviceUnit.sysKey">
                </el-option>
              </el-select>
            </el-form-item>-->
          </el-col>




          <el-col :span="24">
            <el-form-item :label="$t('customer.address')" prop="customerAddressZh">
              <el-input maxlength="200" clearable v-model="form.customerAddressZh"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="isShow"></div>

        <el-row style="text-align: right; margin: 20px -40px 0 0;">
          <router-link type="text" :to="{ path: '/' }" style="padding-right: 20px;">
            {{ $t('register.returnLogin') }}
          </router-link>
          <el-button 
            type="primary" 
            class="black-btn"
            :disabled="submitDisabled" 
            :loading="registerSubmitLoading" 
            @click="onSubmit">{{ $t('register.submitRegister') }}
          </el-button>
        </el-row>
      </el-form>
    </el-card>

    <!-- 有税号 -->
    <el-dialog :visible="successViable" width="480px" :show-close="false">
      <div class="register-success text-center">
        <img src="/img/success.png" width="87px" height="87px" />
        <h1>{{ $t('register.registSuccess') }}</h1>
        <ul class="list-unstyled">
          <li style="list-style-type:none;">{{ $t('register.tip.login1') }}</li>
        </ul>
        <div>
          <el-button type="primary" @click="$router.push('/')">
            {{ $t('login.submit') }}
          </el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 没税号 -->
    <el-dialog :visible="taxNoSuccessViable" width="480px" :show-close="false">
      <div class="register-success text-center">
        <img src="/img/success.png" width="87px" height="87px" />
        <h1>{{ $t('register.registSuccess') }}</h1>
        <ul style="padding-left: 20px; text-align: left;list-style: decimal;">
            <li>{{$t('register.tip.emailconfim')}}</li>
            <li>{{$t('register.tip.sgsapprove')}}</li>
        </ul>
        <div>
          <el-button type="primary" @click="$router.push('/')">
            {{ $t('login.submit') }}
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog :title="$t('register.message.title')" :visible="emailApplyDialog" width="60%" top="10vh"
      :show-close="false">
      <div class="modal-body">
        <h3>{{ $t('register.message.emailSendTip') }} <span style="color: #ff6600">{{ accountApplyForm.email }}</span></h3>
        <h4>{{ $t('register.message.emailSendMessage') }}</h4>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;top: 5px;">
        <router-link type="text" :to="{ path: '/' }">
          <p style="cursor: pointer;color: #ff6600">
            {{ $t('register.message.returnLogin') }}</p>
        </router-link>
      </div>
    </el-dialog>
    <el-dialog title="General Conditions of Service" :visible="clauseVisibleEn" width="70%" top="10vh"
      :show-close="false">
      <div class="modal-body">
        <img src="/img/logo.png" style="width: 200px" />
        <h3 style="font-weight: 600">TERMS AND CONDITIONS OF USE</h3>
        <br />
        <br />
        <p><a target="_blank" style="color: #ff6600"
            href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
        </p>
        <p style="word-wrap:break-word">
          SGS Supplier Chain Knowledge Sharing services include a compilation(s) of data which are mainly
          based on the sources publicly available at the relevant time for which SGS bears no responsibility.
          Unless specifically advised in advance, the compilation of data is a generic analysis which does not
          cover specific product(s). SGS may have extracted from the compiled data specific criteria which are
          not intended to be substitute for the relevant legislation and/or standards.
          Unless agreed otherwise in writing, SGS's services and liability are governed by the general
          Conditions of Service <a style="color: #ff6600" target="_blank"
            href="http://www.sgs.com/terms_and_conditions.htm">http://www.sgs.com/terms_and_conditions.htm</a>
          . All material developed for the clients including the relevant systematic and methodology have been
          developed by the company and are copyrighted by it. Clients shall not copy reproduce or disclose to
          third parties the data as provided by the company without its prior written consent. All information
          provided by SGS is for strategic and informational purposes only, and should not be construed as
          company-specific legal compliance advice or counsel. SGS makes no representation whatsoever
          about the suitability of the information and services contained herein for resolving any question of
          law. SGS does not provide legal services.
        </p>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
        <el-button type="primary" @click="clauseVisibleEn = false">Agree and continue</el-button>
      </div>
    </el-dialog>
    <!--NEW-->
    <!--<el-dialog :visible="clauseVisibleEn" width="70%" top="10vh"
                   :show-close="false">
            <div class="modal-body second el-dialog-div">
                <img src="/img/logo.png" style="width: 200px"/>
                &lt;!&ndash; <div class="right">
                        <top-lang ref="languageRef" :loadMenu="false" :is-load-lanage-by-ip="false"></top-lang>
                    </div>&ndash;&gt;
                <h3 style="font-weight: 600">{{$t('agreement.useConditions')}}</h3>
                <br/>
                <br/>
                <p style="word-wrap:break-word">
                    <h4>The SGS Group is committed to the responsible handling and protection of your personal data.</h4>
                    <br>
                    <p>
                    We have created this statement to provide you with clear and understandable information regarding
                    our privacy practices when you are accessing or using our website (www.sgs.com or its local
                    equivalents) so that you can make informed choices about the use of your personal data by SGS.
                    </p>
                    <br>
                    <p>When accessing other services or applications through our website, please read the specific privacy
                    conditions which may apply to them.</p>
                    <br>
                    <p>
                    SGS reserves the right to update this statement from time to time by publishing a new version
                    online. This statement was last updated in May 2018.
                    </p>
                    <br>
                    <p>
                    When this statement refers to SGS, it means SGS as the controller of your data, namely the SGS
                    Affiliates with which you had, have or will have a business relationship or that otherwise decides
                    which of your data is collected and how it is used, as well as SGS SA (Registration number
                    CHE-105.923.438 Switzerland). You may obtain a list of SGS Affiliates by visiting the Office
                    Directory.
                    </p>
                </p>
            </div>
            <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
                <el-button type="primary" @click="clauseVisibleEn = false">{{$t('agreement.agreeAndContinue')}}
                </el-button>
            </div>
        </el-dialog>-->
    <el-dialog :visible="clauseVisible" width="70%" top="10vh" :show-close="false">
      <div class="modal-body second el-dialog-div">
        <img src="/img/logo.png" style="width: 200px" />
        <!-- <div class="right">
                        <top-lang ref="languageRef" :loadMenu="false" :is-load-lanage-by-ip="false"></top-lang>
                    </div>-->
        <h3 style="font-weight: 600">{{ $t('agreement.useConditions') }}</h3>
        <br />
        <br />

        <!--<p><a target="_blank" style="color: #ff6600" href="https://www.sgs.com/en/terms-and-conditions">https://www.sgs.com/en/terms-and-conditions</a>
                </p>-->
        {{ $t('agreement.head') }}
        <h3> {{ $t('agreement.definition') }}</h3>
        <p> {{ $t('agreement.one_1') }}</p>
        <p> {{ $t('agreement.one_2') }}</p>
        <h3> {{ $t('agreement.accountRegisterAndUse_2') }}</h3>
        <p> {{ $t('agreement.two_1') }}</p>
        <p> {{ $t('agreement.two_2') }}</p>
        <p> {{ $t('agreement.two_3') }}</p>
        <p> {{ $t('agreement.two_4') }}</p>
        <p> {{ $t('agreement.two_5') }}</p>
        <p> {{ $t('agreement.two_6') }}</p>
        <h3> {{ $t('agreement.userInfoProtection_3') }}</h3>
        <p> {{ $t('agreement.three_1') }}</p>
        <p> {{ $t('agreement.three_2') }}</p>
        <p> {{ $t('agreement.three_3') }}</p>
        <h3> {{ $t('agreement.rightsAndObligations_4') }}</h3>
        <p> {{ $t('agreement.four_1') }}</p>
        <p> {{ $t('agreement.four_2') }}</p>
        <p> {{ $t('agreement.four_3') }}</p>
        <p> {{ $t('agreement.four_4') }}</p>
        <p> {{ $t('agreement.four_5') }}</p>
        <p> {{ $t('agreement.four_6') }}</p>
        <p> {{ $t('agreement.four_6_1') }}</p>
        <p> {{ $t('agreement.four_6_2') }}</p>
        <p> {{ $t('agreement.four_6_3') }}</p>
        <p> {{ $t('agreement.four_6_4') }}</p>
        <p> {{ $t('agreement.four_6_5') }}</p>
        <p> {{ $t('agreement.four_6_6') }}</p>
        <p> {{ $t('agreement.four_6_7') }}</p>
        <p> {{ $t('agreement.four_6_8') }}</p>
        <p> {{ $t('agreement.four_6_9') }}</p>
        <p> {{ $t('agreement.four_7') }}</p>
        <p> {{ $t('agreement.four_7_1') }}</p>
        <p> {{ $t('agreement.four_7_2') }}</p>
        <p> {{ $t('agreement.four_7_3') }}</p>
        <p> {{ $t('agreement.four_7_4') }}</p>
        <p> {{ $t('agreement.four_7_5') }}</p>
        <p> {{ $t('agreement.four_8') }}</p>
        <p> {{ $t('agreement.four_8_1') }}</p>
        <p> {{ $t('agreement.four_8_2') }}</p>
        <p> {{ $t('agreement.four_8_3') }}</p>
        <p> {{ $t('agreement.four_8_4') }}</p>
        <p> {{ $t('agreement.four_8_5') }}</p>
        <p> {{ $t('agreement.four_8_6') }}</p>
        <p> {{ $t('agreement.four_8_7') }}</p>
        <p> {{ $t('agreement.four_8_8') }}</p>
        <p> {{ $t('agreement.four_9') }}</p>
        <p> {{ $t('agreement.four_10') }}</p>
        <p> {{ $t('agreement.sgsRightsAndDuties_5') }}</p>
        <p> {{ $t('agreement.five_1') }}</p>
        <p> {{ $t('agreement.five_2') }}</p>
        <p> {{ $t('agreement.five_3') }}</p>
        <p> {{ $t('agreement.five_4') }}</p>
        <p> {{ $t('agreement.five_5') }}</p>
        <p> {{ $t('agreement.intellectualProperty_6') }}</p>
        <p> {{ $t('agreement.six_1') }}</p>
        <p> {{ $t('agreement.six_2') }}</p>
        <p> {{ $t('agreement.six_3') }}</p>
        <p> {{ $t('agreement.six_4') }}</p>
        <p> {{ $t('agreement.privacy_7') }}</p>
        <p> {{ $t('agreement.seven_1') }}</p>
        <p> {{ $t('agreement.seven_2') }}</p>
        <p> {{ $t('agreement.seven_3') }}</p>
        <p> {{ $t('agreement.seven_3_1') }}</p>
        <p> {{ $t('agreement.seven_3_2') }}</p>
        <p> {{ $t('agreement.seven_3_3') }}</p>
        <p> {{ $t('agreement.seven_3_4') }}</p>
        <p> {{ $t('agreement.seven_4') }}</p>
        <p> {{ $t('agreement.seven_5') }}</p>
        <p> {{ $t('agreement.legalResponsibility_8') }}</p>
        <p> {{ $t('agreement.eight_1') }}</p>
        <p> {{ $t('agreement.eight_2') }}</p>
        <p> {{ $t('agreement.eight_3') }}</p>
        <p> {{ $t('agreement.eight_4') }}</p>
        <p> {{ $t('agreement.eight_5') }}</p>
        <p> {{ $t('agreement.eight_6') }}</p>
        <p> {{ $t('agreement.otherAgreements_9') }}</p>
        <p> {{ $t('agreement.nine_1') }}</p>
        <p> {{ $t('agreement.nine_2') }}</p>
        <p> {{ $t('agreement.nine_3') }}</p>
        <p> {{ $t('agreement.nine_4') }}</p>
        <p> {{ $t('agreement.nine_5') }}</p>
        <p> {{ $t('agreement.nine_6') }}</p>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center;top: 5px;">
        <el-button type="primary" @click="clauseVisible = false">{{ $t('agreement.agreeAndContinue') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import {
  add,
  addRegister,
  addAccount,
  checkCustomer,
  checkAccount,
  sendVerificationCode,
  registerDetail,
  checkSmsCode,
  validateAccount,
  getImageCode,
  checkImageCode
} from "@/api/customer/customerRegister";
import {getDictionarySettings} from "@/api/trf/trf";
import { searchCustomer } from "@/api/customer/customer";
import { getCloudFileURL, getSgsCustomer, getTianyanchaCustomer, serviceTypeList } from "@/api/common/index";
import { validatenull, validateEmail } from "@/util/validate";
import { mapGetters } from "vuex";
import TopLang from "../index/top/top-lang";
import {DictionaryEnums} from "@/commons/enums/DictionaryEnums";
import { LanguageEnums } from "@/commons/enums/LanguageEnums";
import {objectIsNull} from "../../util/validate";
import serviceUnitTool from "@/components/serviceUnit/js/serviceUnitTool";



const TIME_COUNT = 60; //更改倒计时时间
export function isvalidPhone(str) {
  const reg = /^[- \d]+$/;///^1[********]\d{9}$/;
  return reg.test(str)
}

export default {
  components: {
    // PrivacyPolicyDialog: resolve => require(['../../components/dialog/privacy-policy-dialog'], resolve),
    TopLang
  },
  data() {
    var validPhone = (rule, value, callback) => {debugger;
      if (!value) {
        callback();//callback(this.$t('register.phoneBlur'))
      } else if (!isvalidPhone(value)) {
        callback(this.$t('register.contactBlur'))
      } else {
        checkAccount({ telephone: value }).then(res => {
          //判断显示客户需要填写的信息
          if (res.data.success === 1 || res.data.success === '1') {
            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
              callback(new Error(this.$t('register.submitPhoneInfo')));
            } else {
              callback(new Error(this.$t('register.lockError')));
            }
          } else {
            callback();
          }
        });
      }
    }
    var validCheckCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.pleaseInputCode')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.pleaseInputCode')));
      } else {
        callback();
      }
    };
    var validateUserName = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.accountBlur')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.accountBlur')));
      } else {
        callback();
      }
    };
    var validateCustomer = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.companyNameBlur')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.companyNameBlur')));
      } else {
        callback();
      }
    };

    var validateProductLineCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.serveTypeBlur')));
      } else {
        callback();
      }
    };

    var validateServiceUnit= (rule, value, callback) => {
      debugger;
      if (objectIsNull(value)) {
        callback(new Error(this.$t('register.serviceUnitBlur')));
      } else {
        callback();
      }
    };


    var validateAddress = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.addressBlur')));
      } else if (value.trim().length === 0) {
        callback(new Error(this.$t('register.addressBlur')));
      } else {
        callback();
      }
    };

    var validateEmailMethod = (rule, value, callback) => {
      debugger;
      if (!value) {
        callback(new Error(this.$t('register.emailBlur')));
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('register.emailRigthBlur')));
      } else {
        callback();
      }
    };
    var validateEmailCaptcha = (rule, value, callback) => {
      debugger;
      if (!value) {
        callback(new Error(this.$t('register.pictureCaptcha.validate')));
      } else {
        callback();
      }
    };

    var validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.passwordBlur')));
      } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/.test(value))) {
        callback(new Error(this.$t('register.passwordError')));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('register.doublePasswordBlur')));
      } else if (value !== this.form.password) {
        callback(new Error(this.$t('register.checkPassNo')));
      } else {
        callback();
      }
    };
    var validateAccount = (rule, value, callback) => {
      if (!validatenull(value)) {
        checkAccount({ email: this.accountApplyForm.email }).then(res => {
          //判断显示客户需要填写的信息
          if (res.data.success === 1 || res.data.success === '1') {
            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
              callback(new Error(this.$t('register.submitInfo')));
            } else {
              callback(new Error(this.$t('register.lockError')));
            }
          } else {
            callback();
          }
        });
      }
    };
    var validateAccount1 = (rule, value, callback) => {
      debugger;
      if (!value) {
        callback(new Error(this.$t('register.emailBlur')));
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('register.emailRigthBlur')));
      } else {
        if (!validatenull(this.customerId)) {
          callback();
        }
        checkAccount({ email: this.form.email }).then(res => {
          //判断显示客户需要填写的信息
          if (res.data.success === 1 || res.data.success === '1') {
            if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
              callback(new Error(this.$t('register.submitInfo')));
            } else {
              callback(new Error(this.$t('register.lockError')));
            }
          } else {
            callback();
          }
        });
      }
    };
    return {
      selDefaultServiceUnit:null,
      defaultServiceUnitDatas:[],
      serviceUnits:[],
      dictionaryEnums:DictionaryEnums,
      LanguageEnums: LanguageEnums,
      successViable: false,
      taxNoSuccessViable: false,
      customerId: '',
      taxDisabledFlag: false,
      errorMsg: null,
      imageCodeData: '',
      serviceTypeData: [],
      bossNoDisabled: false,
      registerMode: 1,
      emailApplyDialog: false,
      hash: '',
      applySubmitDisabled: false,
      applySubmitLoading: false,
      applyShowSendSmsBtn: false,
      applyShowCounter: true,
      applyCounter: 60,
      applyTimer: null,
      accountApplyForm: {
        emailCaptcha: '',
        applyFlag: 0,
        language: this.language,
      },
      active: 1,
      codeErrorMsg: null,
      name: "customerRegister",
      loginAccount: '',
      emailDisabled: false,
      contactMobileDisabled: false,
      accountId: '',
      form: {
        selServiceUnits:[],
        qualificationUrl: '',
        taxNo: '',
        bossNo: '',
        language: this.language,
        productLineCode: '',
        productLineCodes:'',
        serviceUnits:'',
        serviceDomains:''
      },
      submitDisabled: false,
      registerSubmitLoading: false,
      showSendSmsBtn: false,
      isShow: false,
      showCounter: true,
      clauseVisible: false,
      clauseVisibleEn: false,
      counter: 60,
      timer: null,
      customerData: {},
      rules: {
        userName: [
          { required: true, trigger: 'blur', validator: validateUserName }
        ],
        customerAddressZh: [
          { required: true, trigger: 'blur', validator: validateAddress }
        ],
        contactMobile: [
          { required: false, trigger: ['blur','change'], validator: validPhone }//这里需要用到全局变量
        ],
        email: [
          { required: true, validator: validateAccount1, trigger: 'blur' }
        ],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        doublePassword: [{ required: true, validator: validatePass2, trigger: 'blur' }],
        customerName: [
          { required: true, trigger: 'blur', validator: validateCustomer }
        ],
        productLineCode: [
          { required: true, trigger: 'change', validator: validateProductLineCode }
        ],
        selServiceUnits:[
          { required: true, trigger: 'change', validator: validateServiceUnit }
        ]

      },
      applyRules: {
        applyContactMobile: [
          /* { trigger: 'blur', message: this.$t('register.telePhoneBlur')},*///这里需要用到全局变量
          { required: true, validator: validPhone, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, validator: validCheckCode, trigger: 'blur' },
        ],
        email: [
          { required: true, validator: validateEmailMethod, trigger: 'blur' },
        ],
        emailCaptcha: [
          { required: true, validator: validateEmailCaptcha, trigger: 'blur' },
        ],

      }

    }
  },
  computed: {
    ...mapGetters(["language", 'tag']),
  },
  watch: {
    'hash': function (newVal) {
      //获取路由参数
      this.getParams();
    },
    'language': function () {
      this.queryServiceType();
    },
    "$i18n.locale": function () {
      debugger;
      if (this.$refs['accountApplyForm'] != undefined) {
        this.$refs['accountApplyForm'].fields.forEach(item => {
          if (item.validateState === 'error') {
            this.$refs['accountApplyForm'].validateField(item.labelFor)
          }
        })
      }
      if (this.$refs['registerForm'] != undefined) {
        this.$refs['registerForm'].fields.forEach(item => {
          if (item.validateState === 'error') {
            this.$refs['registerForm'].validateField(item.labelFor)
          }
        })
      }


    },
    "accountApplyForm.applyFlag": function () {
      this.$refs['accountApplyForm'].clearValidate();
    }

  },
  mounted() {
    //获取路由参数
    this.getParams();

    //获取服务类别
    this.queryServiceType();
    if (this.active == 1) {
      this.getImageCode();
    }
  },
  methods: {

    setDefault(serviceUnit){
      debugger;


    },
    registTypeChange(val) {
      if (val == 1) {
        this.accountApplyForm.applyFlag = 1
        // this.getImageCode();
      } else {
        this.accountApplyForm.applyFlag = 0
        this.getImageCode();
      }
    },
    /**
     * 获取图片验证码
     */
    getImageCode() {
      getImageCode().then(resp => {
        this.imageCodeData = 'data:image/png;base64,' + resp.data;
      })
    },
    /**
     * 检查图片验证码
     */
    checkImageCode() {
      return checkImageCode({ code: this.accountApplyForm.emailCaptcha }).then(resp => {
        this.authCodeBtnLock = !resp.data.success
        this.authCodeBtnLock && this.$message.warning(resp.msg || '图片验证码错误')
        return this.authCodeBtnLock
      }).catch(() => {
        this.authCodeBtnLock = true
        return this.authCodeBtnLock
      })
    },
    selectServiceTypeChange(values) {
      //将默认的设置为null
      this.selDefaultServiceUnit=null;
      let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, values,this.form.selServiceUnits);
      debugger;
      this.mergeFormServiceUnitData(serviceUnitObj);

    },
   /* selectDefaultServiceUnitChange(defaultValue){
      let serviceUnitObj = serviceUnitTool.changeServiceUnits(this.serviceUnits, this.selServiceUnits,this.selServiceUnits,defaultValue);
      debugger;
      this.mergeFormServiceUnitData(serviceUnitObj);
    },*/
    mergeFormServiceUnitData(serviceUnitObj){
      if(!objectIsNull(serviceUnitObj)){
        this.form.serviceUnits= JSON.stringify(serviceUnitObj.serviceUnits);
        this.form.productLineCodes=JSON.stringify(serviceUnitObj.productLines);
        this.form.serviceDomains=JSON.stringify(serviceUnitObj.serviceDomains);
        //this.defaultServiceUnitDatas=serviceUnitObj.selServiceUnitDatas;
      }
    },

   async queryServiceType() {
      /*serviceTypeList(this.language).then(res => {
        const data = res.data.data;
        this.serviceTypeData = data;
      });*/
      debugger;
      let  serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(this.language);
      this.serviceUnits=serviceUnitsDatas;
      console.log(serviceUnitsDatas);
    },
    getParams() {
      const active = this.$route.query.active;
      const applyFlag = this.$route.query.applyFlag;
      const phone = this.$route.query.phone;
      const email = this.$route.query.email;
      const customerId = this.$route.query.id;
      this.customerId = customerId;
      var language = this.$route.query.language;
      //获取中英文
      debugger;
      if (!validatenull(language)) {
        this.$i18n.locale = language;
        this.$store.commit("SET_LANGUAGE", language);
      }
      // this.$refs.languageRef.changeLanguage(this.language);
      // if (this.language == 'en-US') {
      //   this.clauseVisibleEn = true;
      // } else {
      //   this.clauseVisible = true;
      // }

      let tag = this.tag;
      let title = this.$router.$avueRouter.generateTitle(
        tag.label,
        (tag.meta || {}).i18n
      );

      if (active == '' || active == undefined || active == null) {
        this.active = 1;
      } else if (active == 2) {
        this.active = active;
        //判断是否有ID
        if (!validatenull(customerId)) {//存在ID  标识为用户重新修改注册信息
          this.loadAccountInfo(customerId, email);
        }
        if (applyFlag == 0) {//邮箱
          this.registerMode = 1;
          this.loginAccount = email;
          this.form.email = email;
          this.emailDisabled = true;
        } else {//手机注册
          this.registerMode = 2;
          this.loginAccount = phone;
          this.form.contactMobile = phone;
          this.contactMobileDisabled = true;
        }

      }
    },
    loadAccountInfo(customerId, email) {
      //查询customer & Account数据
      registerDetail(customerId).then(res => {
        const customer = res.data.data.customer;
        if (customer.approveStatus != 80) {
          this.$router.push({ path: '/login' })
        }
        const account = customer.account;
        /*if(account.email!=email){
            this.$router.push({ path: '/login'})
        }*/
        this.$set(this.form, 'customerName', customer.customerNameEn);
        this.$set(this.form, 'email', account.email);
        this.$set(this.form, 'userName', account.userName);
        this.$set(this.form, 'contactMobile', account.mobile);
        this.$set(this.form, 'taxNo', customer.taxNo);
        this.$set(this.form, 'customerAddressZh', customer.customerAddressZh);
        this.$set(this.form, 'bossNo', customer.bossNo);
        this.$set(this.form, 'reportNo', customer.reportNo);
        this.accountId = account.id;
        this.loginAccount = account.account;
        this.registerMode = account.registerMode;
        this.isShow = true;
        if (!validatenull(customer.qualification)) {
          if (!validatenull(customer.qualification.attachmentId)) {
            getCloudFileURL(customer.qualification.attachmentId).then(res => {
              this.$set(this.form, 'qualificationUrl', res.data);
            });
          }
        }
        this.$set(this.form, 'id', customer.id);
      });
    },
    async onSubmitApply() {
      this.accountApplyForm.language = this.language;
      //如果注册方式为邮箱注册 则需校验图片验证码
      if (this.accountApplyForm.applyFlag == 0) {
        let res = await checkImageCode(this.accountApplyForm.emailCaptcha);
        if (!res.data.data.success) {
          this.errorMsg = null;
          this.$nextTick(() => {
            if (res.data.msg == '4001') {//失效
              this.errorMsg = this.$t('register.pictureCaptcha.captchaInvalid');
            } else if (res.data.msg == '4003') {//超时
              this.errorMsg = this.$t('register.pictureCaptcha.captchaOvertime');
            } else {
              this.errorMsg = this.$t('register.pictureCaptcha.captchaError');
            }
          })
          return false;
        }
      }
      this.$refs['accountApplyForm'].validate((valid) => {
        if (valid) {
          this.applySubmitDisabled = true;
          this.applySubmitLoading = true;
          addAccount(this.accountApplyForm).then(res => {
            if (this.accountApplyForm.applyFlag == 0) {//邮箱
              //弹框提示
              this.emailApplyDialog = true;
            } else {
              //路由重新跳转登录页
              let hashVal = new Date().getTime()
              this.$router.push({
                path: '/simpleRegister',
                query: {
                  active: 2,
                  applyFlag: this.accountApplyForm.applyFlag,
                  phone: this.accountApplyForm.applyContactMobile,
                  email: this.accountApplyForm.email,
                  hash: hashVal
                }
              })
              this.hash = hashVal;
            }

          }, error => {
            this.applySubmitDisabled = false;
            this.applySubmitLoading = false;
            console.log(error);
          });
          /*} else {
              this.submitDisabled = false;
              this.registerSubmitLoading = false;
              this.$nextTick(() => {this.codeErrorMsg=this.$t('sms.smsCodeValidateError');})
              this.$message({
                  type: "error",
                  message: this.$t('sms.smsCodeValidateError')
              });
          }*/
          //});
        } else {
          this.submitDisabled = false;
          this.registerSubmitLoading = false;
          console.log('error submit!!');
          return false;
        }
      });
    },
    async onSubmit() {debugger;
      //验证该账号是否在SGSmart中注册过
      let validateRes = await validateAccount(this.loginAccount);
      console.log(validateRes);
      if (!validateRes.data.data) {//该用户已注册
        this.$message({
          type: "error",
          message: this.$t('register.registerError')
        });
        return false;
      }
      //判断是否录入手机号，手机号存在的话，需要再次对手机号进行校验
      if (this.registerMode == 1) {//邮箱注册
        if (!validatenull(this.form.contactMobile)) {
          let validateMobileRes = await validateAccount(this.form.contactMobile);
          if (!validateRes.data.data) {//该用户已注册
            this.$message({
              type: "error",
              message: this.$t('register.registerError')
            });
            return false;
          }
        }
      }
      this.submitDisabled = true;
      this.registerSubmitLoading = true;
      this.$refs['registerForm'].validate((valid) => {
        if (valid) {
          this.submitDisabled = true;
          this.registerSubmitLoading = true;
          //验证短信验证码是否过期
          //checkSmsCode(this.form.contactMobile, this.form.checkCode).then(res => {
          //  if (res.data.data) {//验证通过
          const register = {};
          register.customer = this.form;
          register.checkCode = this.form.checkCode;
          register.customer.customerNameZh = this.form.customerName;
          register.customer.customerNameEn = this.form.customerName;
          //register.customer.productLineCodes = this.form.productLineCode;
          register.customer.qualification = this.form.qualification;
          register.customer.customerAddressZh = this.form.customerAddressZh;
          register.customer.account = {
            id: this.accountId,
            email: this.form.email,
            userName: this.form.userName,
            password: this.form.password,
            mobile: this.form.contactMobile,
            account: this.loginAccount,
            language: this.language,
            registerMode: this.registerMode,
            productLineCodes: this.form.productLineCodes,
            serviceDomains: this.form.serviceDomains,
            serviceUnits: this.form.serviceUnits
          };

          addRegister(register).then(() => {
            this.submitDisabled = false;
            this.registerSubmitLoading = false;
            this.$message({
              type: "success",
              message: this.$t('api.success')
            });
            //判断是否有税号
            if (validatenull(register.customer.taxNo)) {
              // this.$router.push('/simpleRegister/success');
              this.successViable = true
            } else {
              // this.$router.push('/simpleRegister/successInfo');
              this.taxNoSuccessViable = true
            }
          }, error => {
            this.submitDisabled = false;
            this.registerSubmitLoading = false;
            this.$message({
              type: "error",
              message: error.data().msg || this.$t('api.error')
            });
            console.log(error);
          });
          /*} else {
              this.submitDisabled = false;
              this.registerSubmitLoading = false;
              this.$nextTick(() => {this.codeErrorMsg=this.$t('sms.smsCodeValidateError');})
              this.$message({
                  type: "error",
                  message: this.$t('sms.smsCodeValidateError')
              });
          }*/
          //});
        } else {
          this.submitDisabled = false;
          this.registerSubmitLoading = false;
          console.log('error submit!!');
          return false;
        }
      });
    },
    checkExists() {
      if (this.form.customerName != '' && this.form.customerName != undefined && this.form.customerName.length >= 1) {
        checkCustomer(this.form.customerName).then(res => {
          //判断显示客户需要填写的信息
          if (!validatenull(res.data.data)) {
            this.customerData = res.data.result;
            //this.form.bossNo=this.customerData.number;
            this.isShow = false;
          } else {
            this.isShow = true;
            this.form.id = null;
          }
        });
      }
    },
    querySearch(query, callback) {
      this.loading = false;
      if (query != '' && query != undefined && query.length >= 2) {
        this.loading = true;
        getTianyanchaCustomer(query).then(res => {
          const results = [];

          if (!validatenull(res.data)) {
            if (!validatenull(res.data.result)) {
              res.data.result.items.forEach((currentValue, index) => {
                results.push({
                  'value': currentValue.name,
                  "taxNo": currentValue.creditCode,
                });
              });
            }
          }
          this.form.bossNo = '';
          this.form.taxNo = '';
          callback(results);
        })


        /*getSgsCustomer({customerName: query, rows: 5}).then(res => {
            const results = [];
            res.data.rows.forEach((currentValue, index) => {
                if (this.language == 'en-US') {
                    results.push({
                        'value': currentValue.nameEN,
                        "taxNo": currentValue.taxNo,
                        "bossNo": currentValue.number
                    });
                } else {
                    results.push({
                        'value': currentValue.nameCN,
                        "taxNo": currentValue.taxNo,
                        "bossNo": currentValue.number
                    });
                }
            });
            this.form.bossNo = '';
            this.form.taxNo = '';
            callback(results);
        }, error => {
            console.log(error);
        });*/
      }
    },
    clearCustomer() {
      this.taxDisabledFlag = false;
      this.form.taxNo = '';
    },
    handleSelect(item) {
      debugger;
      this.checkExists();
      // this.form.bossNo = item.bossNo;
      // if (item.bossNo != undefined && item.bossNo != '' && item.bossNo != null) {
      //     this.bossNoDisabled = true;
      // } else {
      //     this.bossNoDisabled = false;
      // }
      this.form.taxNo = item.taxNo;
      if (!validatenull(this.form.taxNo)) {
        this.taxDisabledFlag = true;
      } else {
        this.taxDisabledFlag = false;
      }
      this.$refs['taxNo'].focus();
    },
    uploadSuccess(res, file) {
      this.form.qualification = {};
      this.form.qualification.attachmentId = res.data[0].cloudID;
      getCloudFileURL(this.form.qualification.attachmentId).then(res => {
        this.form.qualificationUrl = res.data;
      });
    },
    sendSMSCode() {
      this.showSendSmsBtn = true;
      //验证手机号
      this.$refs.registerForm.validateField("contactMobile", errMsg => {
        if (!errMsg) {//校验通过
          console.log("手机号校验通过");
          sendVerificationCode(this.form.contactMobile, 1).then(res => {
            this.showCounter = false;//展示时间倒计时
            if (!this.timer) {
              this.counter = TIME_COUNT;
              this.showCounter = false;
              this.timer = setInterval(() => {
                if (this.counter > 0 && this.counter <= TIME_COUNT) {
                  this.counter--;
                } else {
                  this.showSendSmsBtn = false;
                  this.showCounter = true;//时间计数完毕重新展示发送按钮
                  clearInterval(this.timer);  // 清除定时器
                  this.timer = null;
                }
              }, 1000)
            } else {
              this.showCounter = true;
            }
          });
        } else {
          //手机号码验证失败
          this.showSendSmsBtn = false;
        }
      });
    },
    applySendSMSCode() {
      this.applyShowSendSmsBtn = true;
      //验证手机号
      if (!this.accountApplyForm.applyContactMobile) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('register.telePhoneBlur'),
          type: 'warning'
        });
        this.applyShowSendSmsBtn = false;
        return false;
      } else if (!isvalidPhone(this.accountApplyForm.applyContactMobile)) {
        this.$notify({
          title: this.$t('tip'),
          message: this.$t('register.telePhoneBlur'),
          type: 'warning'
        });
        this.applyShowSendSmsBtn = false;
        return false;
      } else {
        this.$refs['accountApplyForm'].validateField("applyContactMobile", errMsg => {
          console.log("手机号校验通过");
          //校验是否已注册
          checkAccount({ telephone: this.accountApplyForm.applyContactMobile }).then(res => {
            //判断显示客户需要填写的信息
            if (res.data.success === 1 || res.data.success === '1') {
              if (res.data.result.enableStatus === '1' || res.data.result.enableStatus === 1) {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('register.submitPhoneInfo'),
                  type: 'warning'
                });
              } else {
                this.$notify({
                  title: this.$t('tip'),
                  message: this.$t('register.lockError'),
                  type: 'warning'
                });
              }
              this.applyShowSendSmsBtn = false;
              //setTimeout(this.$router.push('/'),3000);
            } else {
              this.applyShowSendSmsBtn = false;
              sendVerificationCode(this.accountApplyForm.applyContactMobile, 1).then(res => {
                this.applyShowCounter = false;//展示时间倒计时
                if (!this.applyTimer) {
                  this.applyCounter = TIME_COUNT;
                  this.applyShowCounter = false;
                  this.applyShowSendSmsBtn = true;
                  this.applyTimer = setInterval(() => {
                    if (this.applyCounter > 0 && this.applyCounter <= TIME_COUNT) {
                      this.applyCounter--;
                    } else {
                      this.applyShowSendSmsBtn = false;
                      this.applyShowCounter = true;//时间计数完毕重新展示发送按钮
                      this.applyShowSendSmsBtn = false;
                      clearInterval(this.applyTimer);  // 清除定时器
                      this.applyTimer = null;
                    }
                  }, 1000)
                } else {
                  this.applyShowCounter = true;
                }
              });
            }
          });

        });
      }

    },
  }
}
</script>

<style lang="scss">
.second p {
  text-indent: 2em;
}

.modal-body {
  overflow: auto;
  height: 600px;
}

.register-container {
  background: #f5f5f5;
  width: 100%;
  /* height: 100%; */
  padding: 50px;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-card {
    width: 860px;
  }

  .el-card__body {
    padding: 0;
    h2 {
      font-size: 16px;
      font-weight: 400;
      color: #000000;
      margin: 0 0 32px;
    }
    h3 {
      font-size: 20px;
      // font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      margin-bottom: 24px;
    }
  }

  .top-icon {
    color: #303133 !important;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 178px;
    text-align: center;
  }

  /*.el-form-item .el-icon-circle-check {
    background: url('/img/icon/input_ok.png') no-repeat center;
    &::before {
      content: ' ';
    }
  }
  .el-form-item .el-icon-circle-close {
    background: url('/img/icon/input_err.png') no-repeat center;
    &::before {
      content: ' ';
    }
  }*/

  .el-form-item__label {
    padding: 0;
    margin: 0;
  }
  .el-input__inner {
    height: 40px;
    padding-left: 10px;
  }
}

.register-success {
  padding: 18px 28px;
  h1 {
    margin: 32px 0 24px;
    font-size: 18px;
    //font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 26px;
  }
  ul {
    margin-bottom: 48px;
    li {
      width: 364px;
      height: 48px;
      font-size: 14px;
      font-weight: 400;
      color: #000;
      line-height: 24px;
    }
  }
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  margin-right: 80px;
}
</style>
