<template>
  <div class="video-content-container">
    <div v-if="!contentItems.length" class="no-content">
      {{ $t('work.pageCreate.pleaseSelectFile') }}
    </div>
    <div v-else class="content-items">
      <div v-for="(item, index) in contentItems" :key="index" class="content-item">
        <div v-html="item.content" class="content-preview"></div>
        <div class="content-controls">
          <el-icon class="delete-icon" @click="removeItem(index)"><CircleCloseFilled /></el-icon>
          <el-switch 
            v-model="item.authorization" 
            size="small" 
            :active-text="$t('work.pageCreate.authorization')" 
            :active-value="1" 
            :inactive-value="0"
            @change="toggleAuthorization(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, PropType, watch } from 'vue'
import { CircleCloseFilled } from '@element-plus/icons-vue'




interface ContentItem {
  content: string
  url: string
  authorization: number
  deleted?: boolean
}

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  videoFormLinks: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  items: {
    type: Array as PropType<ContentItem[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'update:videoFormLinks', 'itemsUpdated'])

const contentItems = ref<ContentItem[]>([])
// Store a record of deleted file URLs to prevent them from reappearing
const deletedFileUrls = ref<string[]>([])

// Initialize content items when component is mounted or when items prop changes
watch(() => props.items, (newItems) => {
  debugger
  if (newItems && newItems.length) {
    // Filter out any items that are in the deletedFileUrls list
    const filteredItems = newItems.filter(item => !deletedFileUrls.value.includes(item.url))
    contentItems.value = JSON.parse(JSON.stringify(filteredItems))
  }
}, { immediate: true, deep: true })

// Remove an item
const removeItem = (index: number) => {
  // Get the item before removing it
  const removedItem = contentItems.value[index]
  
  // Add to deleted URLs list to prevent it from showing up again
  deletedFileUrls.value.push(removedItem.url)
  
  // Remove from content items
  contentItems.value.splice(index, 1)
  
  // Find and remove the corresponding entry in videoFormLinks
  if (props.videoFormLinks && props.videoFormLinks.length) {
    const matchingUrl = removedItem.url
    
    // Try to find either the exact URL or an encoded version in videoFormLinks
    const linkIndex = props.videoFormLinks.findIndex(link => 
      link === matchingUrl || 
      matchingUrl.includes(encodeURIComponent(link)) ||
      link.includes(encodeURIComponent(matchingUrl))
    )
    
    if (linkIndex > -1) {
      // Create a new array without the removed URL
      const updatedLinks = [...props.videoFormLinks]
      updatedLinks.splice(linkIndex, 1)
      
      // Emit the updated links
      emit('update:videoFormLinks', updatedLinks)
    }
  }
  
  // Update the HTML content and emit it
  updateContent()
  
  // Mark item as deleted and emit updated items
  removedItem.deleted = true
  emit('itemsUpdated', contentItems.value)
}

// Toggle authorization status
const toggleAuthorization = (index: number) => {
  const item = contentItems.value[index]
  
  // Update the authorization class in the content HTML
  const authClass = item.authorization === 1 ? 'authorization' : 'unauthorization'
  const oldAuthClass = item.authorization === 1 ? 'unauthorization' : 'authorization'
  
  // Replace the class in the content HTML
  item.content = item.content.replace(oldAuthClass, authClass)
  
  // Update the content and emit it
  updateContent()
  
  // Immediately emit updated items to ensure parent components are notified of the authorization change
  emit('itemsUpdated', contentItems.value)
}

// Update the complete HTML content
const updateContent = () => {
  const fullContent = contentItems.value.map(item => item.content).join('')
  emit('update:modelValue', fullContent)
}
</script>

<style lang="scss" scoped>
.video-content-container {
  width: 100%;
  height: 100%;
  
  .no-content {
    display: flex;
    justify-content: center;
    align-items: center;
    //height: 100%;
    color: #909399;
    background-color: rgb(245,245,245);
    height: calc(100vh - 57px);
    font-size: 14px;
  }
  
  .content-items {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 10px 0;
     height: calc(100vh - 57px); 
     overflow-y: auto;
    .content-item {
      position: relative;
      border: 1px solid #eee;
      border-radius: 4px;
      
      .content-preview {
        // Override parent styles to prevent conflicts
        :deep(.preview-item) {
          margin: 0;
          padding: 0;
        }
      }
      
      .content-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 5px;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 4px;
        z-index: 100;
        
        .delete-icon {
          font-size: 20px;
          cursor: pointer;
          color: #b3b3b3;
          
          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

:deep(.authorization) {
  border: 2px solid #67c23a;
}

:deep(.unauthorization) {
  border: 2px solid #f56c6c;
}
</style> 