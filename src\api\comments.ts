/*留言组件使用*/
import request from './request'
type RequestParams = Record<string, any>

const sendPost = (
    url: string,
    param: RequestParams,
    headers?: RequestParams,
): Promise<any> => {
    headers = headers ? headers : {}
    return request({
        url,
        method: 'post',
        headers: {
            ...headers,
        },
        data: param,
    })
}

const api = {
    queryCommentList:(param:RequestParams)=>{
        return sendPost("/api/sgs-pbm/sample/web/v1/comments/list", param);
    },
    queryNotification: (sampleId: string) => {
        return sendPost("/api/sgs-pbm/sample/web/v1/notification", {
            id: sampleId,
        });
    },
    addComment: (param: RequestParams) => {
        return sendPost("/api/sgs-pbm/sample/web/v1/comments/add", param);
    },
}
export default api;