<template>
    <basic-container v-loading="pageLoading" style="width: 100%">
        <div class="smart_views_percentage" id="smart_views_percentage">
            <el-table
                v-if="showTable"
                size="small"
                fit
                :border="false"
                :show-header="false"
                :data="getDataList"
            >
                <el-table-column prop="percentage" width="100">
                    <template slot-scope="{row,$index}">
                        <div>
                            <el-input-number
                                    style="width: 60px"
                                    size="mini"
                                    v-model="row.percentage"
                                    :controls="false"
                                    :step="1"
                                    step-strictly
                                    :min="1"
                                    @blur="()=>changePercentage(row,$index)"
                            ></el-input-number>
<!--                            <div style="float: right;padding-left: 5px;line-height: 28px">%</div>-->
                            <el-button style="cursor: auto;color: black;" type="text">%</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="ingredients">
                    <template slot-scope="{row}">
                        <el-input size="mini" clearable placeholder="Please input composition" type="text" v-model="row.ingredients"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="" width="80">
                    <template slot-scope="{row,$index}">
                        <el-button size="medium" v-if="lt100 && showAdd(row,$index)"  type="text" icon="el-icon-plus" @click="addRow"></el-button>
                        <el-button size="medium" v-if="showDel(row,$index)" type="text" icon="el-icon-delete" @click="delRow($index)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div v-show="showTips" style="position: relative">
                <div class="el-form-item__error">The total composition should be 100%.</div>
            </div>
            <div v-show="showNullTips" style="position: relative">
                <div class="el-form-item__error">The composition can't be empty.</div>
            </div>
        </div>
    </basic-container>

</template>

<script>
import {mapGetters} from 'vuex'
export default {
    name: "percentage",
    data() {
        return {
            pageLoading: false,
            showTips:false,
            showNullTips:false,
            showTable:false,
            lt100:true,
        }
    },
    methods: {
        delRow(index){
            this.fieldObj.materialExtendFieldValue.splice(index,1);
            this.showTips = true;
        },
        showAdd(row,rowIndex){
            return rowIndex==this.fieldObj.materialExtendFieldValue.length-1;
        },
        showDel(row,rowIndex){
            if(this.fieldObj.materialExtendFieldValue.length<=1){
                return false;
            }
            return true ;
        },
        changePercentage(row,rowIndex){
            let currentValue = row.percentage;
            //console.log("changePercentage",currentValue,rowIndex);
            if(currentValue==undefined || currentValue=='' || currentValue==null){
                this.fieldObj.materialExtendFieldValue.forEach((da,index)=>{
                    if(index==rowIndex){
                        this.$set(da,'percentage',1);
                    }
                })
                return;
            }
            //this.fieldObj.materialExtendFieldValue 排除下标rowIndex的数据，计算percentage总和，加上当前currentvalue 如果超过100，则currentValue要设置为100-总和
            let sum = 0;
            this.fieldObj.materialExtendFieldValue.forEach((da,index)=>{
                if(index!=rowIndex){
                    sum+=(da.percentage-0);
                }
            });
            if(currentValue+sum>100){
                this.fieldObj.materialExtendFieldValue.forEach((da,index)=>{
                    if(index==rowIndex){
                        this.$set(da,'percentage',100-sum)
                    }
                })
            }
        },
        addRow(){
            let percentage = 0;
            this.fieldObj.materialExtendFieldValue.forEach(da=>{
                percentage+=(da.percentage-0);
            });
            if(percentage>=100){
                return;
            }
            percentage = percentage==0?99:percentage;
            let obj = {seq:this.fieldObj.materialExtendFieldValue.length+1,ingredients:'',percentage:100-percentage}
            this.fieldObj.materialExtendFieldValue.push(obj);
        },
        parseFieldObj(){
            if((this.fieldObj.materialExtendFieldValue || []).length==0){
                this.$set(this.fieldObj,'materialExtendFieldValue',[]);
                this.addRow();
            }
        },
        validatePercentage() {
            let list = this.fieldObj.materialExtendFieldValue || [];
            let sum = 0;
            let hasNull = false;
            list.forEach(da=>{
                let {percentage,ingredients} = da;
                sum+=(percentage-0);
                if(!ingredients){
                    hasNull = true;
                }
            });
            if(sum!=100){
                this.showTips = true;
                return false;
            }
            this.showTips = false;
            if(hasNull){
                this.showNullTips = true;
                return false;
            }
            return true;
        }
    },
    created() {
        this.showTable = false;
    },
    mounted() {
        this.parseFieldObj();
        this.$nextTick(()=>{
            this.showTable = true;
        })
    },
    watch: {
        'fieldObj.materialExtendFieldValue':{
            immediate: false,
            deep: true,
            handler(val) {
                //console.log("开始计算sum")
                let sum = 0;
                this.showTips = false;
                (val || []).forEach(da=>{
                    sum+=(da.percentage-0)
                });
                //console.log("计算sum",sum)
                this.lt100 = sum<100;
            }
        }
    },
    computed: {
        ...mapGetters(['permission', 'userInfo', 'language']),
        getDataList() {
            return this.fieldObj.materialExtendFieldValue || [];
        }
    },
    props: {
        fieldObj:{}
    },
    updated() {
    },
    beforeDestroy() {
    },
    destroy() {
    },
    components: {}
}
</script>

<style lang="scss">
.smart_views_percentage {
  .el-table__body-wrapper{
    min-height: 53px;
  }

}
</style>