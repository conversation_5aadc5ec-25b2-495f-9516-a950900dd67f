const staticRouter = [
  {
    path: '/',
    name: '/',
    redirect: {
      name: 'Layout',
    },
  },
  {
    path: '/login',
    name: 'Login',
    meta: {
      title: '登录',
      requireAuth: false,
    },
    component: () => import('../views/Login.vue'),
  },
  //新增一个注册页面
  {
    path: '/register',
    name: 'Register',
    meta: {
      title: 'Register',
    },
    component: () => import('../views/Register.vue'),
    children: [
      {
        path: '/register/info',
        name: 'RegisterInfo',
        meta: {
          title: '注册',
          requireAuth: false,
        },
        component: () => import('../views/register/index.vue'),
      },
    ],
  },
  {
    path: '/layout',
    name: 'Layout',
    component: () => import('../views/Layout.vue'),
    children: [
      // 404页面
      {
        path: "/404",
        name: "404",
        component: () => import("../views/Error.vue"),
        meta: {
            title: "404页面",
            requireAuth: false,
        },
      },
      {
        path: "/:pathMatch(.*)*",
        name: "undefined",
        meta: {
            title: "undefined页面",
            requireAuth: false,
        },
        redirect: "/404",
      },
      {
        path: '/protocol/detail',
        name: 'ProtocolDetail',
        meta: {
          title: '详情',
          requireAuth: false,
        },
        component: () => import('@/views/protocol/detail/ProtocolDetail.vue'),
      },
      {
        path: '/customer/newProduct/list',
        name: 'ProductLibrary',
        meta: {
          title: 'Product Library',
          requireAuth: false,
        },
        component: () => import('@/views/product/list/ProductList.vue'),
      },
      {
        path: '/customer/newProduct/detail',
        name: 'Product Detail',
        meta: {
          title: 'Product Detail',
          requireAuth: false,
        },
        component: () => import('@/views/product/detail/index.vue'),
      },
      {
        path:'/company/info',
        name:'Company Info',
        meta:{
          title:'公司信息',
          requireAuth:false
        },
        component:()=>import('../views/company/info/index.vue')
      },
      {
        path:'/customer/relationship',
        name:'Customer Relationship',
        meta:{
          title:'客户关系',
          requireAuth: false
        },
        component:()=>import('../views/customer/relationship/index.vue')
      }
    ],
  },
]

export { staticRouter }
