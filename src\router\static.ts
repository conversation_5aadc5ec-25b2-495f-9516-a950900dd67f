const staticRouter = [
  // {
  //   path: '/',
  //   name: '/',
  //   redirect: {
  //     name: '<PERSON>gin',
  //   },
  // },
  {
    path: '/knowledge/login',
    name: 'Login',
    meta: {
      title: '登录',
      requireAuth: false,
    },
    component: () => import('../views/Login.vue'),
  },
  {
    path: '/knowledge',
    name: 'Layout',
    component: () => import('../views/Layout.vue'),
    children: [
      // {
      //   path: 'ccl/trf/trfForm',
      //   name: 'DocumentList',
      //   meta: {
      //     title: '登录',
      //     requireAuth: false,
      //   },
      //   component: () => import('../views/documentList/DocumenList.vue'),
      // },
      // {
      //   path: 'ccl/trf/newTrf',
      //   name: 'newTrf',
      //   meta: {
      //     title: '新建申请单',
      //     requireAuth: false,
      //   },
      //   component: () => import('../views/ccl/trf/inex.vue'),
      // },
      {
        path: 'tagManagement/tagManagement',
        name: 'tagManagement',
        meta: {
          title: '标签管理',
          requireAuth: false,
        },
        component: () => import('../views/tagManagement/tagManagement.vue'),
      },
      {
        path: 'knowledgeBaseHome/knowledgeBaseHome',
        name: 'knowledgeBaseHome',
        meta: {
          title: '知识库首页',
          requireAuth: false,
        },
        component: () => import('../views/knowledgeBaseHome/knowledgeBaseHome.vue'),
      },
      {
        path: 'navigationBar/navigationBar',
        name: 'navigationBar',
        meta: {
          title: '导航栏',
          requireAuth: false,
        },
        component: () => import('../components/navigationBar/navigationBar.vue'),
      },
      {
        path: 'contentPage/contentPage',
        name: 'contentPage',
        meta: {
          title: '内容页面',
          requireAuth: false,
        },
        component: () => import('../views/contentPage/contentPage.vue'),
      },
      {
        path: 'pageCreate/pageCreate',
        name: 'pageCreate',
        meta: {
          title: '文章新建',
          requireAuth: false,
        },
        component: () => import('../views/pageCreate/pageCreate.vue'),
      },
      {
        path: 'detailedArticle/detailedArticle',
        name: 'detailedArticle',
        meta: {
          title: '详情页面',
          requireAuth: false,
        },
        component: () => import('../views/detailedArticle/detailedArticle.vue'),
      },
      {
        path: 'Carousel/Carousel',
        name: 'Carousel',
        meta: {
          title: '走马灯',
          requireAuth: false,
        },
        component: () => import('../views/Carousel/Carousel.vue'),
      },
      {
        path: 'tabsPage/tabsPage',
        name: 'tabsPage',
        meta: {
          title: '标签切换页面',
          requireAuth: false,
        },
        component: () => import('../views/tabsPage/tabsPage.vue'),
      },
      {
        path: 'favPage/favPage',
        name: 'favPage',
        meta: {
          title: '收藏页面',
          requireAuth: false,
        },
        component: () => import('../views/favPage/favPage.vue'),
      }
      // {
      //   path: 'tooltip/tooltip',
      //   name: 'tooltip',
      //   meta: {
      //     title: 'tooltip',
      //     requireAuth: false,
      //   },
      //   component: () => import('../components/Tooltip/Tooltip.vue'),
      // },
    ],
  },
]

export { staticRouter }
