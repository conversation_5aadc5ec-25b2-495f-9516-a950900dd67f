<template>
    <!--  数据是二位数组 [[],[]],[[],[]],第一层代表第几行，第二层：下标代表第几列，元素个数是渲染为checkbox的个数  -->
    <div class="smart_views_CheckboxTable" id="smart_views_CheckboxTable">
        <table style="width: 100%">
            <tbody v-if="title">
            <td>
                <div class="title-wrapper">
                    <span class="title">{{ title }}</span>
                </div>
            </td>
            </tbody>
        </table>
        <div class="check_box_table">
            <table>
                <tr v-for="(th,tr_i) in tableHeader" :key="'tr_'+tr_i">
                    <td v-for="(td,td_i) in th"
                        :style="{width: (100/th.length)+'%'}"
                        :key="'td_'+td_i">
                        <template
                                v-for="(cb,cb_i) in td"
                                :key="'cb_'+cb_i"
                        >
                            <label class="el-checkbox" style="position: relative;top: -2px" v-if="cb.type=='text'">
                                <span class="el-checkbox__label">
                                    {{ cb.name }}
                                </span>
                            </label>
                            <el-checkbox
                                    v-else
                                    :true-value="1"
                                    :false-value="0"
                                    v-model="cb.value"
                            >{{ cb.name }}
                            </el-checkbox>
                        </template>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</template>


<script setup>
import {
    ref,
    reactive,
    onMounted,
    onUnmounted,
    computed,
    watch,
    provide,
    nextTick,
} from 'vue'
import {useStore} from 'vuex'
import {useRouter} from 'vue-router'
import {ElNotification} from 'element-plus'
import {useI18n} from 'vue-i18n'

defineOptions({
    name: 'CheckboxTable'
})
const props = defineProps({
    // 渲染的表头
    tableHeader: {
        type: Array,
        default: () => [[]]
    },
    title: {
        type: String,
        default: ''
    }
})

const {t} = useI18n()
const router = useRouter()
const store = useStore()
const userInfo = computed(() => store.state.user.userInfo)
const roleInfo = computed(() => store.state.user.roleInfo)
const language = computed(() => store.state.common.language)

watch(language, (newVal) => {

})
onMounted(() => {
})
</script>

<style lang="scss">
.smart_views_CheckboxTable {
  width: 100%;
  overflow: hidden;

  .check_box_table {
    max-height: 500px;
    width: 100%;
    overflow: auto;
    table{
      width: 100%;
    }
  }

  table, th, td {
    border: 1px solid #c8c8c8; /* 定义边框的宽度、样式和颜色 */
    border-collapse: collapse; /* 可选，使边框合并为单一边框 */
  }

  tbody {
    background: rgba(200, 200, 200, 0.56);
  }

  td {
    padding: 10px;
  }


  .title-wrapper {
    display: flex;
    align-items: center;
  }

  .title {
    font-weight: bold;
  }
}
</style>