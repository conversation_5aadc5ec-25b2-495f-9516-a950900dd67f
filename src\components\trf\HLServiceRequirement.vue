<template>
    <div>
        <div class="item-warp clearfix">
            <!-- 电子报告 -->
            <el-col :span="24">
                <el-form-item
                    prop=""
                    :label="$t('service.softCopyDeliverTo')"
                    :required="true"
                    style="display: inline-block"
                >
                    <el-checkbox-group
                        v-model="softCopyDeliverTo"
                        :disabled="trfDisabled"
                        @change="
                            serviceItemChange(
                                $event,
                                dictionaryEnums.ServiceRequirement
                                    .SoftCopyDeliverTo.name,
                            )
                        "
                    >
                        <el-checkbox
                            name="softCopyDeliverToCheckbox"
                            v-for="(role, index) in dictionaryEnums
                                .ServiceRequirement.CustomerRoles.items"
                            :key="role.code"
                            :label="role.code"
                            :value="role.code"
                        >
                            {{
                                language === LanguageEnums.CN.name
                                    ? role.cnKey
                                    : role.enKey
                            }}
                        </el-checkbox>
                        <!-- <el-select class="selects"
                       :placeholder="$t('service.contactEmail')"
                       v-show="softCopyDeliverTo && inputFlagChange(softCopyDeliverTo, dictionaryEnums.ServiceRequirement.CustomerRoles.items)"
                       v-model="softCopyDeliverToOther"
                       popper-append-to-body="false"
                       multiple
                       filterable
                       allow-create
                       collapse-tags
                       default-first-option
                       :disabled="trfDisabled">
              <el-option v-for="(item,index) in softCopyDeliverToOther" class="popper-class" :key="item" :label="item" :value="item"></el-option>
            </el-select> -->
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item
                    :required="true"
                    style="display: inline-block"
                    v-if="
                        softCopyDeliverTo &&
                        inputFlagChange(
                            softCopyDeliverTo,
                            dictionaryEnums.ServiceRequirement.CustomerRoles
                                .items,
                        )
                    "
                >
                    <el-select
                        class="selects"
                        :placeholder="$t('service.contactEmail')"
                        v-model="softCopyDeliverToOther"
                        @change="serviceItemChange()"
                        popper-append-to-body="false"
                        multiple
                        filterable
                        allow-create
                        collapse-tags
                        default-first-option
                        :disabled="trfDisabled"
                    >
                        <el-option
                            v-for="(item, index) in softCopyDeliverToOther"
                            class="popper-class"
                            :key="item"
                            :label="item"
                            :value="item"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </div>
        <div class="item-warp clearfix">
            <!--发票类型-->
            <el-col :span="10">
                <el-form-item :label="$t('service.vatType')">
                    <el-radio-group
                        v-model="trfServiceRequire.vatType"
                        :disabled="trfDisabled"
                    >
                        <el-radio
                            v-for="(vat, index) in dictionaryEnums
                                .ServiceRequirement.InvoiceType.items"
                            :key="vat.code"
                            :label="vat.code"
                            :value="vat.code"
                        >
                            {{
                                language === LanguageEnums.CN.name
                                    ? vat.cnKey
                                    : vat.enKey
                            }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
            <!--形式发票-->
            <el-col :span="14">
                <el-form-item :label="$t('service.invoiceProforma')">
                    <el-radio-group
                        v-model="trfServiceRequire.needProformaInvoice"
                        :disabled="trfDisabled"
                    >
                        <el-radio :label="1">
                            {{ $t("service.required") }}
                        </el-radio>
                        <el-radio :label="0">
                            {{ $t("service.notRequired") }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
        </div>
        <div class="item-warp clearfix">
            <!--invoice deliver way-->
            <el-col :span="10">
                <el-form-item :label="$t('service.invoiceDeliverWay')">
                    <el-select
                        clearable
                        v-model="invoiceDeliverWay"
                        :placeholder="$t('select')"
                        :disabled="trfDisabled"
                    >
                        <el-option
                            v-for="(item, index) in dictionaryEnums
                                .ServiceRequirement.InvoiceDeliverWays.items"
                            :label="
                                language === LanguageEnums.CN.name
                                    ? item.cnKey
                                    : item.enKey
                            "
                            :value="item.code"
                        />
                    </el-select>
                    <el-input
                        class="other-select"
                        maxlength="500"
                        v-model="invoiceDeliverWayOther"
                        :disabled="trfDisabled"
                        v-if="
                            invoiceDeliverWay ===
                            dictionaryEnums.ServiceRequirement
                                .InvoiceDeliverWays.items[5].code
                        "
                    />
                </el-form-item>
            </el-col>
            <!--invoice deliver to-->
            <el-col :span="14">
                <el-form-item
                    prop=""
                    :label="$t('service.invoiceDeliverTo')"
                    :required="trfServiceRequire.needProformaInvoice == 1"
                >
                    <el-checkbox-group
                        v-model="invoiceDeliverTo"
                        :disabled="trfDisabled"
                        @change="
                            serviceItemChange(
                                $event,
                                dictionaryEnums.ServiceRequirement
                                    .InvoiceDeliverTo.name,
                            )
                        "
                    >
                        <el-checkbox
                            name="invoiceDeliverToCheckBox"
                            v-for="(role, index) in dictionaryEnums
                                .ServiceRequirement.CustomerRoles.items"
                            :key="role.code"
                            :label="role.code"
                            :value="role.code"
                        >
                            {{
                                language === LanguageEnums.CN.name
                                    ? role.cnKey
                                    : role.enKey
                            }}
                        </el-checkbox>
                        <el-select
                            class="selects"
                            :placeholder="$t('service.contactEmail')"
                            v-if="
                                invoiceDeliverTo &&
                                inputFlagChange(
                                    invoiceDeliverTo,
                                    dictionaryEnums.ServiceRequirement
                                        .CustomerRoles.items,
                                )
                            "
                            v-model="invoiceDeliverToOther"
                            popper-append-to-body="false"
                            multiple
                            filterable
                            allow-create
                            collapse-tags
                            default-first-option
                            :disabled="trfDisabled"
                        >
                            <el-option
                                v-for="(item, index) in invoiceDeliverToOther"
                                class="popper-class"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-option>
                        </el-select>
                    </el-checkbox-group>
                </el-form-item>
            </el-col>
        </div>
        <div class="item-warp clearfix">
            <el-col :span="10">
                <el-form-item :label="$t('service.returnSample')">
                    <el-checkbox-group
                        v-model="returnSampleData"
                        :disabled="trfDisabled"
                        @change="
                            serviceItemChange(
                                $event,
                                dictionaryEnums.ServiceRequirement.ReturnSample
                                    .name,
                            )
                        "
                    >
                        <el-checkbox
                            name="returnSampleCheckbox"
                            v-for="(returnSample, index) in returnSampleList"
                            :label="returnSample.sysKey"
                            :key="returnSample.sysKey"
                        >
                            {{ returnSample.sysValue }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-col>
            <el-col :span="14">
                <el-form-item :label="$t('service.returnSampleDeliverTo')">
                    <el-checkbox-group
                        v-model="returnSampleDeliverTo"
                        :disabled="trfDisabled"
                        @change="
                            serviceItemChange(
                                $event,
                                dictionaryEnums.ServiceRequirement
                                    .ReturnSampleDeliverTo.name,
                            )
                        "
                    >
                        <el-checkbox
                            name="returnSampleDeliverToCheckboxBox"
                            v-for="(role, index) in dictionaryEnums
                                .ServiceRequirement.CustomerRoles.items"
                            :key="role.code"
                            :label="role.code"
                            :value="role.code"
                        >
                            {{
                                language === LanguageEnums.CN.name
                                    ? role.cnKey
                                    : role.enKey
                            }}
                        </el-checkbox>
                        <el-select
                            class="selects"
                            :placeholder="$t('service.contactEmail')"
                            v-if="
                                returnSampleDeliverTo &&
                                inputFlagChange(
                                    returnSampleDeliverTo,
                                    dictionaryEnums.ServiceRequirement
                                        .CustomerRoles.items,
                                )
                            "
                            v-model="returnSampleDeliverToOther"
                            popper-append-to-body="false"
                            multiple
                            filterable
                            allow-create
                            collapse-tags
                            default-first-option
                            :disabled="trfDisabled"
                        >
                            <el-option
                                v-for="(
                                    item, index
                                ) in returnSampleDeliverToOther"
                                class="popper-class"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-option>
                        </el-select>
                    </el-checkbox-group>
                </el-form-item>
            </el-col>
        </div>
        <div class="item-warp clearfix">
            <!--return sample deliver way-->
            <el-col :span="10">
                <el-form-item :label="$t('service.returnSampleDeliverWay')">
                    <el-select
                        clearable
                        v-model="returnSampleDeliverWay"
                        :placeholder="$t('select')"
                        :disabled="trfDisabled"
                    >
                        <el-option
                            v-for="(item, index) in dictionaryEnums
                                .ServiceRequirement.DeliverWays.items"
                            :label="
                                language === LanguageEnums.CN.name
                                    ? item.cnKey
                                    : item.enKey
                            "
                            :value="item.code"
                        />
                    </el-select>
                    <el-input
                        class="other-select"
                        maxlength="1000"
                        v-model="returnSampleDeliverWayOther"
                        :disabled="trfDisabled"
                        v-if="
                            returnSampleDeliverWay ===
                            dictionaryEnums.ServiceRequirement.DeliverWays
                                .items[4].code
                        "
                    />
                </el-form-item>
            </el-col>
            <el-col :span="14">
                <el-form-item :label="$t('service.liquidInkSample')">
                    <el-radio-group
                        v-model="trfServiceRequire.liquid"
                        :disabled="trfDisabled"
                    >
                        <el-radio
                            name="liquidTypeRadioBox"
                            v-for="(liquid, index) in dictionaryEnums
                                .ServiceRequirement.LiquidInkType.items"
                            :key="liquid.code"
                            :label="liquid.code"
                            :value="liquid.code"
                        >
                            {{
                                language === LanguageEnums.CN.name
                                    ? liquid.cnKey
                                    : liquid.enKey
                            }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
        </div>
        <div class="item-warp clearfix">
            <el-col :span="14">
                <el-form-item :label="$t('service.acceptSelection')">
                    <el-radio-group
                        v-model="acceptOutside"
                        :disabled="trfDisabled"
                    >
                        <el-radio :label="1">{{ $t("service.yes") }}</el-radio>
                        <el-radio :label="0">{{ $t("service.no") }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
        </div>
        <div class="item-warp clearfix">
            <el-col :span="10">
                <el-form-item :label="$t('service.legalProceeding')">
                    <el-radio-group
                        v-model="trfServiceRequire.isLegalProceeding"
                        :disabled="trfDisabled"
                    >
                        <el-radio :label="1">{{ $t("common.yes") }}</el-radio>
                        <el-radio :label="0">{{ $t("common.no") }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
        </div>
    </div>
</template>

<script>
import {objectIsNull} from "@/util/validate"
import serviceRequirement from "@/components/trf/js/serviceRequirement"
import {mapGetters} from "vuex"
import {LanguageEnums} from "@/commons/enums/LanguageEnums"
import {DictionaryEnums} from "@/commons/enums/DictionaryEnums"

const SELECT_INPUT_MAX_LENGTH = 300 //选项输入框最大限制长度 500

export default {
    name: "HLServiceRequirement",
    props: {
        trfServiceRequire: {
            type: Object,
            required: true,
            default: {},
            description: "服务需求对象",
        },
        trfDisabled: {
            type: Boolean,
            required: true,
            default: false,
            description: "是否可操作",
        },
        trfDataLoadStatus: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    watch: {
        language: function (newVal) {
            this.initDictionary()
        },
        "trfServiceRequire.isCopy": function (newVal) {
            if (!newVal) {
                this.hardCopyDeliverTo = null
                this.hardCopyDeliverToOther = []
            }
        },
        trfDataLoadStatus: function (newVal) {
            if (newVal) {
                let softCopyContact = objectIsNull(
                    this.trfServiceRequire.contactList,
                )
                    ? []
                    : this.filterContact(
                          DictionaryEnums.ServiceRequirement.SoftCopyDeliverTo
                              .code,
                      )
                if (!objectIsNull(softCopyContact)) {
                    this.softCopyDeliverTo = softCopyContact.deliverTo
                    this.softCopyDeliverToOther = objectIsNull(
                        softCopyContact.deliverOther,
                    )
                        ? []
                        : softCopyContact.deliverOther.split(",")
                }
            }
        },
        //去除联动效果
        /*'trfServiceRequire.needProformaInvoice': function (newVal) {
      if(!newVal) {
        this.invoiceDeliverTo = [];
        this.invoiceDeliverToOther = [];
      }
    },*/
        acceptOutside: function (newVal) {
            this.trfServiceRequire.isOutside = newVal
        },
    },
    computed: {
        ...mapGetters(["language"]),
    },

    data() {
        return {
            returnSampleList: [], //退样可选项
            returnSampleData: [], //退样已选项
            accreditationList: [], //报告资质可选项
            accreditationData: [], //报告资质已选项
            softCopyDeliverTo: [], //选中的电子报告联系人
            softCopyDeliverToOther: [], //输入的电子报告联系人
            hardCopyDeliverWay: null, //选中的纸质报告邮寄方式
            hardCopyDeliverWayOther: null, //输入的纸质报告邮寄方式
            hardCopyDeliverTo: null, //选中的纸质报告联系人
            hardCopyDeliverToOther: [], //输入的纸质报告联系人
            invoiceDeliverTo: [], //选中的发票邮寄联系人
            invoiceDeliverToOther: [], //输入的发票邮寄联系人
            invoiceDeliverWay: null, //选中的纸质报告邮寄方式
            invoiceDeliverWayOther: null, //输入的纸质报告邮寄方式
            returnSampleDeliverTo: [], //选中的退样邮寄联系人
            returnSampleDeliverToOther: [], //输入的退样邮寄联系人
            returnSampleDeliverWay: null, //选中的退样邮寄方式
            returnSampleDeliverWayOther: null, //输入的退样邮寄方式
            LanguageEnums: LanguageEnums,
            acceptOutside: 1,
            dictionaryEnums: DictionaryEnums,
        }
    },
    mounted() {
        this.initDictionary()
        //设置invoiceDeliverWay默认
        this.invoiceDeliverWay =
            this.dictionaryEnums.ServiceRequirement.InvoiceDeliverWays.items[0].code
        this.returnSampleData = objectIsNull(
            this.trfServiceRequire.returnSampleRequire,
        )
            ? []
            : this.trfServiceRequire.returnSampleRequire.split(",")
        this.accreditationData = objectIsNull(
            this.trfServiceRequire.accreditation,
        )
            ? []
            : this.trfServiceRequire.accreditation.split(",")
        let softCopyContact = objectIsNull(this.trfServiceRequire.contactList)
            ? []
            : this.filterContact(
                  DictionaryEnums.ServiceRequirement.SoftCopyDeliverTo.code,
              )
        if (!objectIsNull(softCopyContact)) {
            this.softCopyDeliverTo = softCopyContact.deliverTo
            this.softCopyDeliverToOther = objectIsNull(
                softCopyContact.deliverOther,
            )
                ? []
                : softCopyContact.deliverOther.split(",")
        }

        let hardCopyContact = objectIsNull(this.trfServiceRequire.contactList)
            ? []
            : this.filterContact(
                  DictionaryEnums.ServiceRequirement.HardCopyDeliverTo.code,
              )
        if (!objectIsNull(hardCopyContact)) {
            this.hardCopyDeliverTo = hardCopyContact.deliverTo[0]
            this.hardCopyDeliverToOther = objectIsNull(
                hardCopyContact.deliverOther,
            )
                ? []
                : hardCopyContact.deliverOther.split(",")
            if (
                DictionaryEnums.ServiceRequirement.DeliverWays.items.findIndex(
                    (item) => item.code === hardCopyContact.deliverWay,
                ) >= 0
            ) {
                this.hardCopyDeliverWay = hardCopyContact.deliverWay
            } else if (hardCopyContact.deliverWay) {
                this.hardCopyDeliverWay =
                    DictionaryEnums.ServiceRequirement.DeliverWays.items[4].code
                this.hardCopyDeliverWayOther = hardCopyContact.deliverWay
            }
        }

        let invoiceContact = objectIsNull(this.trfServiceRequire.contactList)
            ? []
            : this.filterContact(
                  DictionaryEnums.ServiceRequirement.InvoiceDeliverTo.code,
              )
        if (!objectIsNull(invoiceContact)) {
            this.invoiceDeliverTo = invoiceContact.deliverTo
            this.invoiceDeliverToOther = objectIsNull(
                invoiceContact.deliverOther,
            )
                ? []
                : invoiceContact.deliverOther.split(",")
            if (
                DictionaryEnums.ServiceRequirement.InvoiceDeliverWays.items.findIndex(
                    (item) => item.code === invoiceContact.deliverWay,
                ) >= 0
            ) {
                this.invoiceDeliverWay = invoiceContact.deliverWay
            } else if (invoiceContact.deliverWay) {
                this.invoiceDeliverWay =
                    DictionaryEnums.ServiceRequirement.InvoiceDeliverWays.items[5].code
                this.invoiceDeliverWayOther = invoiceContact.deliverWay
            }
        }

        let returnSampleContact = objectIsNull(
            this.trfServiceRequire.contactList,
        )
            ? []
            : this.filterContact(
                  DictionaryEnums.ServiceRequirement.ReturnSampleDeliverTo.code,
              )
        if (!objectIsNull(returnSampleContact)) {
            this.returnSampleDeliverTo = returnSampleContact.deliverTo
            this.returnSampleDeliverToOther = objectIsNull(
                returnSampleContact.deliverOther,
            )
                ? []
                : returnSampleContact.deliverOther.split(",")
            if (
                DictionaryEnums.ServiceRequirement.DeliverWays.items.findIndex(
                    (item) => item.code === returnSampleContact.deliverWay,
                ) >= 0
            ) {
                this.returnSampleDeliverWay = returnSampleContact.deliverWay
            } else if (returnSampleContact.deliverWay) {
                this.returnSampleDeliverWay =
                    DictionaryEnums.ServiceRequirement.DeliverWays.items[4].code
                this.returnSampleDeliverWayOther =
                    returnSampleContact.deliverWay
            }
        }
        this.trfServiceRequire.returnSampleDeliverTo =
            this.returnSampleDeliverTo
        this.trfServiceRequire.returnSampleDeliverToOther =
            this.returnSampleDeliverToOther
        this.acceptOutside = objectIsNull(this.trfServiceRequire.isOutside)
            ? 1
            : this.trfServiceRequire.isOutside
        this.trfServiceRequire.isValid = false //渲染时，组件输入合法性初始化为false
    },
    methods: {
        serviceItemChange(values, tag) {
            if (objectIsNull(values)) {
                switch (tag) {
                    case DictionaryEnums.ServiceRequirement.ReturnSample.name:
                        this.trfServiceRequire.returnSampleName = null
                        this.trfServiceRequire.returnSampleRequire = null
                        break

                    case DictionaryEnums.ServiceRequirement.Accreditation.name:
                        this.trfServiceRequire.accreditation = null
                        break

                    case DictionaryEnums.ServiceRequirement.SoftCopyDeliverTo
                        .name:
                        this.softCopyDeliverTo = []
                        this.softCopyDeliverToOther = []
                        break

                    case DictionaryEnums.ServiceRequirement.HardCopyDeliverTo
                        .name:
                        this.hardCopyDeliverTo = null
                        this.hardCopyDeliverToOther = []
                        break

                    case DictionaryEnums.ServiceRequirement.InvoiceDeliverTo
                        .name:
                        this.invoiceDeliverTo = []
                        this.invoiceDeliverToOther = []
                        break

                    case DictionaryEnums.ServiceRequirement
                        .ReturnSampleDeliverTo.name:
                        this.returnSampleDeliverTo = []
                        this.returnSampleDeliverToOther = []
                        break
                }
            }
            this.updateFilledStatus()
        },
        updateFilledStatus() {
            this.$emit("serviceItemChange", this.getNotFilledCount())
        },
        async initDictionary() {
            this.returnSampleList = await serviceRequirement.queryDictionary(
                DictionaryEnums.ServiceRequirement.ReturnSample.name,
                DictionaryEnums.ServiceRequirement.ReturnSample,
                this.language,
            )
            this.accreditationList = await serviceRequirement.queryDictionary(
                DictionaryEnums.ServiceRequirement.Accreditation.name,
                DictionaryEnums.ServiceRequirement.Accreditation,
                this.language,
            )
            let that = this
            console.log("accreditationList")
        },

        //是否显示other选项的输入框
        inputFlagChange(values, items) {
            let finds = items.findIndex((i) => i.inputFlag == true)
            if (finds < 0 || objectIsNull(values)) return false
            for (let i = 0; i < values.length; i++) {
                if (items[finds].code === values[i]) return true
            }
            return false
        },

        //按业务类型过滤联系人
        filterContact(code) {
            let contacts = this.trfServiceRequire.contactList.filter(
                (item) => item.contactType === code,
            )
            if (objectIsNull(contacts)) return []
            if (objectIsNull(contacts[0].deliverTo)) {
                contacts[0].deliverTo = []
                return contacts[0]
            }
            contacts[0].deliverTo = Array.isArray(contacts[0].deliverTo)
                ? contacts[0].deliverTo
                : contacts[0].deliverTo.split(",").map((item) => parseInt(item))
            contacts[0].deliverTo = contacts[0].deliverTo.filter((element) =>
                DictionaryEnums.ServiceRequirement.CustomerRoles.items
                    .map((item) => item.code)
                    .includes(element),
            )
            return contacts[0]
        },

        //包装返回对象
        packagingComponentValue() {
            this.trfServiceRequire.isValid = true
            //this.checkInputValid(this.softCopyDeliverTo, this.$t('service.softCopyDeliverToHolder'));
            this.checkArrayValid(
                this.softCopyDeliverTo,
                this.$t("service.softCopyDeliverToHolder"),
            )

            if (
                this.softCopyDeliverTo &&
                this.softCopyDeliverTo.indexOf(
                    DictionaryEnums.ServiceRequirement.CustomerRoles.items[3]
                        .code,
                ) >= 0
            ) {
                this.checkInputValid(
                    this.softCopyDeliverToOther,
                    this.$t("service.softCopyDeliverOtherHolder"),
                )
            }

            if (
                this.trfServiceRequire.needProformaInvoice &&
                objectIsNull(this.invoiceDeliverTo)
            ) {
                this.checkArrayValid(
                    this.invoiceDeliverTo,
                    this.$t("service.invoiceDeliverToHolder"),
                )
            }

            if (
                !objectIsNull(this.invoiceDeliverTo) &&
                objectIsNull(this.invoiceDeliverWay)
            ) {
                this.checkInputValid(
                    this.invoiceDeliverWay,
                    this.$t("service.invoiceDeliverWayHolder"),
                )
            }

            if (
                this.invoiceDeliverTo ===
                DictionaryEnums.ServiceRequirement.CustomerRoles.items[3].code
            ) {
                this.checkInputValid(
                    this.invoiceDeliverToOther,
                    this.$t("service.invoiceDeliverToOtherHolder"),
                )
            }

            if (
                this.invoiceDeliverWay ===
                DictionaryEnums.ServiceRequirement.InvoiceDeliverWays.items[5]
                    .code
            ) {
                this.checkInputValid(
                    this.invoiceDeliverWayOther,
                    this.$t("service.invoiceDeliverWayOtherHolder"),
                )
            }

            if (
                this.returnSampleDeliverTo ===
                DictionaryEnums.ServiceRequirement.CustomerRoles.items[3].code
            ) {
                this.checkInputValid(
                    this.returnSampleDeliverToOther,
                    this.$t("service.returnSampleDeliverToOtherHolder"),
                )
            }

            if (
                !objectIsNull(this.returnSampleDeliverTo) &&
                objectIsNull(this.returnSampleDeliverWay)
            ) {
                this.checkInputValid(
                    this.returnSampleDeliverWay,
                    this.$t("service.returnSampleDeliverWayHolder"),
                )
            }

            if (
                this.returnSampleDeliverWay ===
                DictionaryEnums.ServiceRequirement.DeliverWays.items[4].code
            ) {
                this.checkInputValid(
                    this.returnSampleDeliverWayOther,
                    this.$t("service.returnSampleDeliverWayOtherHolder"),
                )
            }

            this.trfServiceRequire.isOutside = this.acceptOutside
            this.trfServiceRequire.accreditation = objectIsNull(
                this.accreditationData,
            )
                ? null
                : this.accreditationData.join(",")
            this.trfServiceRequire.returnSampleRequire = objectIsNull(
                this.returnSampleData,
            )
                ? null
                : this.returnSampleData.sort().join(",")
            this.trfServiceRequire.returnSampleName = null

            if (!objectIsNull(this.returnSampleData)) {
                let choiceReturnSample = []
                this.returnSampleList.forEach((item) => {
                    this.returnSampleData.sort().indexOf(item.sysKey) >= 0
                        ? choiceReturnSample.push(item.sysValue)
                        : null
                })
                this.trfServiceRequire.returnSampleName =
                    choiceReturnSample.join(",")
            }

            let contacts = []
            if (this.trfServiceRequire.isCopy) {
                let deliverOtherValues =
                    !objectIsNull(this.hardCopyDeliverToOther) &&
                    this.hardCopyDeliverTo ===
                        DictionaryEnums.ServiceRequirement.CustomerRoles
                            .items[3].code
                        ? this.hardCopyDeliverToOther.join(",")
                        : null
                objectIsNull(deliverOtherValues)
                    ? true
                    : this.checkInputValid(
                          deliverOtherValues,
                          this.$t("service.hardCopyDeliverToOtherOverLength"),
                      )
                contacts.push({
                    contactType:
                        DictionaryEnums.ServiceRequirement.HardCopyDeliverTo
                            .code,
                    deliverTo: this.hardCopyDeliverTo,
                    deliverOther: deliverOtherValues,
                    deliverWay:
                        this.hardCopyDeliverWay ===
                        DictionaryEnums.ServiceRequirement.DeliverWays.items[4]
                            .code
                            ? this.hardCopyDeliverWayOther
                            : this.hardCopyDeliverWay,
                })
            }

            let softDeliverOtherValues =
                !objectIsNull(this.softCopyDeliverToOther) &&
                this.softCopyDeliverTo.indexOf(
                    DictionaryEnums.ServiceRequirement.CustomerRoles.items[3]
                        .code,
                ) >= 0
                    ? this.softCopyDeliverToOther.join(",")
                    : null
            objectIsNull(softDeliverOtherValues)
                ? true
                : this.checkInputValid(
                      softDeliverOtherValues,
                      this.$t("service.softCopyDeliverOtherOverLength"),
                  )

            contacts.push({
                contactType:
                    DictionaryEnums.ServiceRequirement.SoftCopyDeliverTo.code,
                deliverTo: objectIsNull(this.softCopyDeliverTo)
                    ? null
                    : this.softCopyDeliverTo.sort().join(","),
                deliverOther: softDeliverOtherValues,
            })

            // if(this.trfServiceRequire.needProformaInvoice) {
            console.log(!objectIsNull(this.invoiceDeliverToOther))
            console.log(
                this.invoiceDeliverTo.indexOf(
                    DictionaryEnums.ServiceRequirement.CustomerRoles.items[3]
                        .code,
                ),
            )
            let invoiceDeliverOtherValues =
                !objectIsNull(this.invoiceDeliverToOther) &&
                this.invoiceDeliverTo.indexOf(
                    DictionaryEnums.ServiceRequirement.CustomerRoles.items[3]
                        .code,
                ) >= 0
                    ? this.invoiceDeliverToOther.join(",")
                    : null
            objectIsNull(invoiceDeliverOtherValues)
                ? true
                : this.checkInputValid(
                      invoiceDeliverOtherValues,
                      this.$t("service.invoiceDeliverToOtherOverLength"),
                  )

            contacts.push({
                contactType:
                    DictionaryEnums.ServiceRequirement.InvoiceDeliverTo.code,
                deliverTo: objectIsNull(this.invoiceDeliverTo)
                    ? null
                    : this.invoiceDeliverTo.sort().join(","),
                deliverOther: invoiceDeliverOtherValues,
                deliverWay:
                    this.invoiceDeliverWay ===
                    DictionaryEnums.ServiceRequirement.InvoiceDeliverWays
                        .items[5].code
                        ? this.invoiceDeliverWayOther
                        : this.invoiceDeliverWay,
            })
            // }

            // if(this.returnSampleData && this.returnSampleData.length > 0) {
            let returnSampleDeliverOtherValues
            if (this.returnSampleDeliverTo) {
                returnSampleDeliverOtherValues =
                    !objectIsNull(this.returnSampleDeliverToOther) &&
                    this.returnSampleDeliverTo.indexOf(
                        DictionaryEnums.ServiceRequirement.CustomerRoles
                            .items[3].code,
                    ) >= 0
                        ? this.returnSampleDeliverToOther.join(",")
                        : null
                objectIsNull(returnSampleDeliverOtherValues)
                    ? true
                    : this.checkInputValid(
                          returnSampleDeliverOtherValues,
                          this.$t(
                              "service.returnSampleDeliverToOtherOverLength",
                          ),
                      )
            }
            contacts.push({
                contactType:
                    DictionaryEnums.ServiceRequirement.ReturnSampleDeliverTo
                        .code,
                deliverTo: objectIsNull(this.returnSampleDeliverTo)
                    ? null
                    : this.returnSampleDeliverTo.sort().join(","),
                deliverOther: returnSampleDeliverOtherValues,
                deliverWay:
                    this.returnSampleDeliverWay ===
                    DictionaryEnums.ServiceRequirement.DeliverWays.items[4].code
                        ? this.returnSampleDeliverWayOther
                        : this.returnSampleDeliverWay,
            })
            // }

            this.trfServiceRequire.contactList = contacts
            return this.trfServiceRequire
        },

        //输入项验证
        checkInputValid(value, msg) {
            if (objectIsNull(value) || value.length > SELECT_INPUT_MAX_LENGTH) {
                this.trfServiceRequire.isValid = false
                this.trfServiceRequire.validMsg = msg
            }
        },
        checkArrayValid(value, msg) {
            if (objectIsNull(value) || value.length == 0) {
                this.trfServiceRequire.isValid = false
                this.trfServiceRequire.validMsg = msg
            }
        },
        validateForm() {
            // 这里可以根据实际的必填项逻辑进行校验
            // 示例：检查电子报告必填项
            // 获取 SoftCopy Deliver To 对应的 label 文本
            const softCopyLabelText = this.$t("service.softCopyDeliverTo")
            // 获取 Invoice Deliver To 对应的 label 文本
            const invoiceLabelText = this.$t("service.invoiceDeliverTo")

            // 定义一个辅助函数，通过 label 文本查找对应的 el-form-item 元素
            function findFormItemByLabel(rootEl, labelText) {
                const labels = rootEl.querySelectorAll(".el-form-item__label")
                for (let i = 0; i < labels.length; i++) {
                    if (labels[i].textContent.trim() === labelText) {
                        return labels[i].closest(".el-form-item")
                    }
                }
                return null
            }

            // 检查电子报告必填项
            if (this.softCopyDeliverTo.length === 0) {
                const softCopyFormItem = findFormItemByLabel(
                    this.$el,
                    softCopyLabelText,
                )
                if (softCopyFormItem) {
                    return softCopyFormItem
                }
            }

            // 检查发票邮寄联系人必填项
            if (
                this.invoiceDeliverTo.length === 0 &&
                this.trfServiceRequire.needProformaInvoice === 1
            ) {
                const invoiceFormItem = findFormItemByLabel(
                    this.$el,
                    invoiceLabelText,
                )
                if (invoiceFormItem) {
                    return invoiceFormItem
                }
            }
            return null
        },
        getNotFilledCount() {
            let count = 0
            if (
                this.softCopyDeliverTo.length > 0 &&
                this.softCopyDeliverTo.includes(5) &&
                this.softCopyDeliverToOther.length == 0
            ) {
                count++
            }
            return count
        },
    },
}
</script>

<style lang="scss" scoped>
.other-select {
    width: 180px;
    height: 30px;
    margin-left: 10px;
    font-size: 14px;
    color: #333;
    .el-select__tags {
        // 调整输入框里面的多选不换行
        flex-wrap: nowrap !important;
    }
}

.selects {
    /deep/.el-input.el-input--medium.el-input--suffix {
        // 调整箭头符号块的宽度
        width: 175px !important;
    }
    /deep/.el-input.el-input--medium.el-input--suffix {
        .el-input__inner {
            // 调整输入框的宽高
            height: 36px !important;
            width: 220px !important;
        }
        .el-input__suffix-inner {
            // 调整箭头符号的位置
            position: absolute;
            right: -45px;
        }
    }
    /deep/.el-select__tags {
        // 调整输入框里面的多选不换行
        flex-wrap: nowrap !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.popper-class {
    max-width: 260px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
