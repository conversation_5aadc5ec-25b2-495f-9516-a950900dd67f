import request from './request'

/**
 * 保存搜索过滤器
 * @param data - 保存过滤器所需的数据
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const filterSave = (data: any) => {
  return request({
    url: '/api/sgs-mart/search/filter/save',
    method: 'post',
    data,
  })
}

/**
 * 查询搜索过滤器
 * 参数支持 TRF,MATERIAL,PRODUCT
 * @param sourceType - 数据源类型，默认为 'TRF'
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const filterQuery = (sourceType?: string) => {
  return request({
    url: '/api/sgs-mart/search/filter/query',
    method: 'post',
    data: { sourceType: sourceType ? sourceType : 'TRF' },
  })
}

/**
 * 删除搜索过滤器
 * @param id - 要删除的过滤器的 ID
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const filterDelete = (id: any) => {
  return request({
    url: '/api/sgs-mart/search/filter/delete',
    method: 'post',
    data: { id },
  })
}

/**
 * 设置搜索过滤器为默认
 * @param id - 要设置为默认的过滤器的 ID
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const filterSetDefault = (id: any) => {
  return request({
    url: '/api/sgs-mart/search/filter/default',
    method: 'post',
    data: {
      id,
      defaultFlag: 1,
    },
  })
}

/**
 * 查询搜索维度
 * @param param - 查询维度所需的参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const dimensionQuery = (param: any) => {
  return request({
    url: '/api/sgs-mart/search/dimension/list',
    method: 'post',
    data: param,
  })
}

/**
 * 执行搜索查询
 * @param param - 搜索查询所需的参数
 * @returns 返回一个 Promise，该 Promise 解析为请求的响应结果
 */
export const query = (param: any) => {
  const timezone = (0 - new Date().getTimezoneOffset()) / 60
  return request({
    url: '/api/sgs-mart/search/list',
    method: 'post',
    data: param,
    headers: {
      timezone: timezone.toString(),
    },
  })
}
