<template>
  <div class="sgs-top" v-if="hideInTrfDetail">
    <div class="top-bar__left">
      <logo></logo>
    </div>
    <div class="top-bar__title">
      <div class="top-bar__item top-bar__item--show" id="site-nav" slot="" v-if="showMenu">
        <sidebar></sidebar>
      </div>
    </div>
    <div class="top-bar__right">
      <span style="display: inherit" v-if="permissionList.searchTrf">
        <input v-model="queryVal" type="text" class="form-control" :placeholder="$t('navbar.searchTrf')" />
        <button @click="toList" class="btn sgs_btn_black" type="button">
          <span class="glyphicon glyphicon-search" aria-hidden="true"></span>
        </button>
      </span>
      <!-- 240519不上prod 暂时注释 -->
      <!-- <top-domain v-if="isShowDomain"></top-domain> -->
      <div class="sgs_top_divid_line" style="height: 100%;width: 1px;background: #ffffff;"></div>
      <top-lang></top-lang>
      <!--选择Service Unit-->
      <top-service-unit ></top-service-unit>
      <el-badge :value="pendCount" v-if="permissionList.sgsMessage" class="item badeg-num" :style="{margin: `0 ${(((pendCount+'').length)-0)*10}px 0 0`}">
        <i class="el-icon-chat-dot-square" style="color: white;cursor: pointer" @click="pendClick"></i>
      </el-badge>
      <!-- 判断有申请单菜单,且 非SGS用户才允许提示 -->
      <!-- {{userInfo.guide}}-{{menuFlag}}-{{!sgsRole}} -->
      <!-- <el-popover
        v-if="menuFlag && !sgsRole"
        id="viewAgain"
        class="question"
        placement="bottom"
        width="170"
        trigger="hover">
        <ul class="list-unstyled task">
          <li @click="showGuideDialog" v-if="!role.isBuyer">{{ $t('guide.newTask') }}</li>
          <el-popover
            placement="right"
            width="160"
            trigger="hover">

            <div class="sub-menu">
              <div @click="showFnDialog('/', 'dashboard', {})">{{ $t('navbar.dashboard') }}</div>
              <div @click="showFnDialog('/trf/newList', 'trfList', {})">{{ $t('navbar.trfList') }}</div>
              <div @click="showFnDialog('/trf/trfDetail', 'createTrf', {id:'',title:'New TRF'})">{{ $t('trf.createtrf') }}</div>
            </div>

            <li slot="reference">{{ $t('guide.func') }}</li>
          </el-popover>
        </ul>

        <span slot="reference">
          <i class="icon-all iconwenhao-copy" style="font-size: 44px !important;color: #ffffff;margin-left: -5px;"></i>
        </span>
      </el-popover> -->

          <!--<img class="top-bar__img" src="/img/avatar.svg" style="border-left: 1px solid #D9D9D9; padding-left: 10px; margin-left: 10px;filter: grayscale(100%) invert(100%) brightness(2);"/>-->
      <el-dropdown>
         <span class="el-dropdown-link">
          <i class="el-icon-user sgs-header-user-icon"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item disabled>{{userInfo.userName}}</el-dropdown-item>
          <el-dropdown-item divided class="sgs_top_left_user_menu">
            <router-link :to="{path:'/'}">
              <div>
                {{$t("navbar.dashboard")}}
              </div>
            </router-link>
          </el-dropdown-item>
          <el-dropdown-item class="sgs_top_left_user_menu">
            <router-link to="/info/index">
              <div>
                {{$t("navbar.userinfo")}}
              </div>
            </router-link>
          </el-dropdown-item>
          <el-dropdown-item v-if="!role.isSGS" class="sgs_top_left_user_menu">
              <a @click="openNewPage('/web/company/info')">
                  <div>
                      {{$t('navbar.companyInfo')}}
                  </div>
              </a>
<!--            <router-link to="/company/info">
              <div>
                {{$t('navbar.companyInfo')}}
              </div>
            </router-link>-->
          </el-dropdown-item>

          <el-dropdown-item class="sgs_top_left_user_menu">
            <router-link to="/personality">
              <div>
                  {{$t("navbar.personalitySetting")}}
              </div>
            </router-link>
          </el-dropdown-item>

          <!--公司认证-->
          <!--2021-01-29修改逻辑 无需认证功能，存在税号直接待审核-->
          <!--<el-dropdown-item v-if="!role.isSGS">
                        <router-link to="/company/index">{{$t('navbar.companyInfo')}}</router-link>
                    </el-dropdown-item>-->

          <el-dropdown-item divided @click.native="logout">
            {{ $t("navbar.logOut") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <el-dialog :title="$t('pend.name')" :visible.sync="dialogPendVisible" :modal-append-to-body="false" width="70%">
      <el-table :data="pendList" style="width: 100%">
        <el-table-column prop="type" :label="$t('pend.pendType')" width="160"></el-table-column>
        <el-table-column prop="pendDescribe" :label="$t('pend.pendDescribe')"></el-table-column>
        <el-table-column prop="isRead" :label="$t('pend.isRead')" width="140" :formatter="readFormtter">
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('user.createTime')" width="160">
        </el-table-column>
        <el-table-column :label="$t('operation.title')" width="80" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.isHandle == 0" @click="handel(scope.row)" type="text" size="mini">
              {{ $t("pend.goHandle") }}
            </el-button>
            <el-button v-if="scope.row.isHandle == 1" :disabled="true" type="text" size="mini">
              {{ $t("pend.processed") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="sizeChange" @current-change="currentChange" :current-page="page.currentPage"
        :page-sizes="[5, 10, 20, 50, 100]" :page-size="page.pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="page.total">
      </el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters, mapState } from "vuex";
import { fullscreenToggel, listenfullscreen } from "@/util/util";
import {
  queryPendList,
  updatePendReadStatus,
  queryPendCount,
} from "@/api/pend";
import pendCount from "@/api/common/pendCount";
import { validatenull,objectIsNull } from "@/util/validate";

export default {
  components: {
    Logo: (resolve) => require(["../logo"], resolve),
    Sidebar: (resolve) => require(["../sidebar/index"], resolve),
    topLock: (resolve) => require(["./top-lock"], resolve),
    topMenu: (resolve) => require(["./top-menu"], resolve),
    topSearch: (resolve) => require(["./top-search"], resolve),
    topTheme: (resolve) => require(["./top-theme"], resolve),
    topLogs: (resolve) => require(["./top-logs"], resolve),
    topColor: (resolve) => require(["./top-color"], resolve),
    //topDomain: (resolve) => require(["./top-domain"], resolve),
    topLang: (resolve) => require(["./top-lang"], resolve),
    topServiceUnit: (resolve) => require(["./top-service-unit"], resolve),
  },
  name: "top",
  data() {
    return {
      queryVal:'',
      isShowDomain:false,
      hideInTrfDetail: true,
      pendCount: null,
      dialogPendVisible: false,
      pendQuery: {
        isHandle_equal: 0,
      },
      pendList: [],
      sort: { descs: "update_time" },
      page: {
        pageSize: 5,
        currentPage: 1,
        total: 0,
      },
      menuFlag: null,
      sgsRole: null
    };
  },
  filters: {},
  created() {
    //240519不上prod 去掉
    //this.isShowDomainInit();
    //this.initLoadPend();
    this.initLoadPendCount();
    this.hideNav()
  },
  mounted() {
    listenfullscreen(this.setScreen);
    var that = this;
    pendCount.$on("demo", function (msg) {
      that.initLoadPendCount();
    });
    this.$nextTick(() => {
      //判断是否为SGS，SGS无需加入公司认证功能
      setTimeout(() => {
        console.log('判断是否为SGS')
        this.menuFlag = this.validateTrfMenu();
        this.sgsRole = this.role.isSGS;
      }, 500)
    })
  },
  computed: {
    ...mapState({
      showDebug: (state) => state.common.showDebug,
      showTheme: (state) => state.common.showTheme,
      showLock: (state) => state.common.showLock,
      showFullScren: (state) => state.common.showFullScren,
      showCollapse: (state) => state.common.showCollapse,
      showSearch: (state) => state.common.showSearch,
      showMenu: (state) => state.common.showMenu,
      showColor: (state) => state.common.showColor,
      permissionList() {
        return {
          searchTrf: this.vaildData(
            this.permission["sgs:main:searchTrf"],
            false
          ),
          sgsMessage: this.vaildData(
            this.permission["sgs:main:sgsMessage"],
            false
          ),
        };
      },
      role() {
        return {
          isSGS: this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser") ,
          isBuyer: this.haseRole('UserRole', 'Buyer'),
        }
      },
    }),
    ...mapGetters([
      "userInfo",
      "isFullScren",
      "tagWel",
      "tagList",
      "isCollapse",
      "tag",
      "logsLen",
      "logsFlag",
      "permission",
      "dimensions",
      "posts",
      "menu"
    ]),
  },
  methods: {
      openNewPage(path){
          window.open(path ,'_blank');
      },
    //是否展示domain tab切换
    isShowDomainInit(){
      //判断当前用户是否存在多个domain
      debugger
      if(!validatenull(this.userInfo.serviceDomains)){
        let  domains =  this.userInfo.serviceDomains.split(',');
        if(!objectIsNull(domains) && domains.length>1){
              this.isShowDomain = true;
        }
      }
    },
    //判断是否存在创建TRF菜单
    validateTrfMenu(){
      let result = false;
      if(!validatenull(this.menu)){
        let menuStr = JSON.stringify(this.menu);
        if(!validatenull(menuStr)){
          if(menuStr.indexOf("/ccl/trf/newTrf") != -1){
            result = true;
          }
       }
      }
      return result;
    },
    showGuideDialog() {
      if(this.$store.state.user.taskListDialog) this.$store.commit('SET_TASK_DIALOG', false)
      setTimeout(() => {
          this.$store.commit('SET_TASK_DIALOG', true)
      }, 150)
    },
    showFnDialog(path, type, query) {
      // if(path) {
        this.$router.push({ path, query })
      // }
      this.$store.commit('SET_TASK_TYPE', type)
    },
    haseRole(type, role) {
      if (validatenull(type) || validatenull(role)) {
        return false;
      }
      if (validatenull(this.dimensions)) {
        return false;
      } else {
        if (this.dimensions.hasOwnProperty(type)) {
          if (this.dimensions[type].indexOf(role) >= 0) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      }
    },
    hasePost(post) {
      if (validatenull(post) || validatenull(post)) {
        return false;
      }
      if (validatenull(this.posts)) {
        return false;
      } else {
        if (this.posts.includes(post)) {
          return true;
        } else {
          return false;
        }
      }
    },
    readFormtter(row, column) {
      var type = row[column.property];
      var readTypeStr = this.$t("common.yes");
      if (type == 0) {
        readTypeStr = this.$t("common.no");
      }
      return readTypeStr;
    },
    //查询未处理待办数量
    initLoadPendCount() {
      var params = {};
      queryPendCount(Object.assign(params, this.pendQuery, this.sort)).then(
        (res) => {
          var data = res.data.data;
          if (data == 0) {
            this.pendCount = null;
          } else {
            this.pendCount = data;
          }
        }
      );
    },
    handel(row) {
      this.$router.push({ path: row.url });
      //如果是未读的话 则更新待办状态为已读
      if (row.isRead == 0) {
        const modifiedForm = {
          id: row.id,
          isRead: 1,
        };
        updatePendReadStatus(modifiedForm).then((res) => {
          this.page.currentPage = 1;
          this.initLoadPend();
        });
      }
      this.dialogPendVisible = false;
    },
    //分页查询
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.initLoadPend();
    },
    currentChange(pageCurrent) {
      this.page.currentPage = pageCurrent;
      this.initLoadPend();
    },
    pendClick() {
      this.initLoadPend();
      this.dialogPendVisible = true;
    },
    initLoadPend() {
      var params = {};
      queryPendList(
        this.page.currentPage,
        this.page.pageSize,
        Object.assign(params, this.pendQuery, this.sort)
      ).then((res) => {
        this.pendList = res.data.data.records;
        this.page.total = res.data.data.total;
      });
    },
    toList() {
      this.$router.push({
        path: "/trf/newList?from=2&queryValue=" + this.queryVal,
      });
    },
    handleScreen() {
      fullscreenToggel();
    },
    setCollapse() {
      this.$store.commit("SET_COLLAPSE");
    },
    setScreen() {
      this.$store.commit("SET_FULLSCREN");
    },
    logout() {
      this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
        confirmButtonText: this.$t("submitText"),
        cancelButtonText: this.$t("cancelText"),
        type: "warning",
      }).then(() => {
        localStorage.removeItem('AUTO_OFF')
        this.$store.dispatch("LogOut").then(() => {
          this.$router.push({ path: "/login" });
        });
      });
    },
    hideNav() {
      // if(this.$route.path == '/trf/trfDetail' || this.$route.path == '/ccl/trf/newTrf') {
      //   this.hideInTrfDetail = false
      // } else {
      //   this.hideInTrfDetail = true
      // }
    }
  },
  watch: {
    $route: function(newVal) {
      this.hideNav()
    },
  }
};
</script>

<style lang="scss">
  .sgs-top{
    .sgs-header-user-icon::before{
      color:#ffffff;
      font-size: 15px;
      padding: 2px;
      border:solid 2px #ffffff;
      border-radius: 50%;
    }
  }
.el-badge__content {
  top: 30px !important;
}

.top-icon {
  font-weight: 400;
  color: #ffffff;
}
.question {
  //margin-left: 15px;
  color: #ffffff;
  img {
    display: block;
  }
}
.task {
  margin-bottom: 0;
  li {
    padding: 10px;
    cursor: pointer;
    &:hover {
      background: #f2f2f2;
    }
  }
}
.sub-menu {
  > div {
    cursor: pointer;
    padding: 8px 10px;
    &:hover {
      background: #f2f2f2;
    }
  }
}
  li.sgs_top_left_user_menu{
    padding: 0 !important;
    a{
      div{
        padding: 0 20px !important;
      }
    }
  }
</style>
