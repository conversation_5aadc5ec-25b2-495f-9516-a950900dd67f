const router = require("koa-router")();
const Api = require("./../../request");
const moment = require("moment");

// 获取路由
router.get("/api/sgs-auth/routes", async (ctx, next) => {
    const datas = await Api.get("sgs-auth/routes", ctx);
    ctx.body = datas;
});
// 退出登录
router.get("/api/user/logout", async (ctx, next) => {
    const datas = await Api.get("user/logout", ctx);
    ctx.body = datas;
});

// 获取用户信息
router.post(
    "/api/sgs-mart/userSetting/queryUserLanguage",
    async (ctx, next) => {
        const datas = await Api.get("sgs-mart/userSetting/queryUserLanguage", ctx);
        ctx.body = datas;
    }
);
// 修改用户语言
router.get(
    "/api/sgs-mart/userSetting/updateUserLanguage",
    async (ctx, next) => {
        const datas = await Api.get("sgs-mart/userSetting/updateUserLanguage", ctx);
        ctx.body = datas;
    }
);

// 待办数量
router.post("/api/sgs-mart/pend/count", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/pend/count", ctx);
    ctx.body = datas;
});

// 验证token
router.post("/api/sgs-auth/token/validate", async (ctx, next) => {
    const datas = await Api.post("sgs-auth/token/validate", ctx);
    ctx.body = datas;
});

// 待办列表
router.post("/api/sgs-mart/pend/list", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/pend/list", ctx);
    ctx.body = datas;
});

/*
    上传到CS私有文件
*/
router.post("/api/sgsapi/FrameWorkApi/file/doUpload", async (ctx, next) => {
    ctx.requestType = "formData";
    ctx.uploadFile = "true";
    const datas = await Api.post("sgsapi/FrameWorkApi/file/doUpload", ctx);
    ctx.body = { data: datas, code: 200 };
});

// 上传product文件
router.post("/api/sgs-pbm/sample/web/v1/upload", async (ctx, next) => {
    ctx.requestType = "formData";
    ctx.uploadFile = "true";
    const datas = await Api.post("sgs-pbm/sample/web/v1/upload", ctx);
    ctx.body = datas;
});

// 下载
router.post("/api/sgs-mart/trf/downLoadFileByCloudId", async (ctx, next) => {
    const datas = await Api.post("sgs-mart/trf/downLoadFileByCloudId", ctx);
    ctx.body = datas;
});

router.post(
    "/api/sgs-mart/FrameWorkApi/file/downloadByCloudID",
    async (ctx, next) => {
        ctx.requestType = "formData";
        const datas = await Api.post(
            "sgsapi/FrameWorkApi/file/downloadByCloudID?systemID=1&networkType=2",
            ctx
        );
        ctx.body = { data: datas, status: 200 };
    }
);

router.get("/api/heart/get", async (ctx, next) => {
    const time = moment().unix();
    const datas = { code: 200, msg: "ok", data: { time: time } };
    ctx.body = datas;
});
// 修改默认BU
router.get(
    "/api/sgs-mart/customer/account/updateServiceUnit",
    async (ctx, next) => {
        const datas = await Api.get(
            "sgs-mart/customer/account/updateServiceUnit",
            ctx
        );
        ctx.body = datas;
    }
);

router.post(
    "/api/sgs-mart/sgs-api/queryBuParamSetting",
    async (ctx, next) => {
        const datas = await Api.post("sgs-mart/sgs-api/queryBuParamSetting", ctx);
        ctx.body = datas;
    }
);

module.exports = router;
