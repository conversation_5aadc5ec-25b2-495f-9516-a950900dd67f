import request from '@/router/axios';


export const add = (form) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/submit',
        method: 'post',
        data: form
    })
}
export const update = (form) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/update',
        method: 'post',
        data: form
    })
}
export const getList = ( params) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/list',
        method: 'get',
        params: {
            ...params,
        }
    })
}
export const getPage = (current, size, params) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
export const remove = (ids) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/remove',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const getTestPackage = (id) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/detail',
        method: 'get',
        params: {
            id,
        }
    })
}

export const getTestPackageTree = (parentId) => {
    return request({
        url: '/api/sgs-mart/lab/testpackage/tree',
        method: 'get',
        params: {
            parentId,
        }
    })
}
