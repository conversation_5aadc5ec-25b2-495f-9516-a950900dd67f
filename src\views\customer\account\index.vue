<template>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="wrap-tab">
        <el-tab-pane name="account" v-if="permissionList.managementTab" :label="$t('account.userManage')">
            <account v-if="tabRefresh.account"></account>
        </el-tab-pane>
        <el-tab-pane name="approve" v-if="permissionList.approveTab" :label="$t('account.approveTitle')" lazy="true">
           <account-approve v-if="tabRefresh.approve"></account-approve>
        </el-tab-pane>
    </el-tabs>
</template>

<script>
    import {mapGetters} from "vuex";
    export default {
        components: {
            accountApprove: resolve => require(['@/views/customer/account/approve'], resolve),
            account: resolve => require(['@/views/customer/account/account'], resolve)
        },
        data(){
            return{
                name: "customer-account",
                activeName: "account",
                tabRefresh: {
                    account: true,
                    approve: false,
                }
            }
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            permissionList() {
                return {
                    approveTab: this.vaildData(this.permission['sgs:customer:account:tab:approve'],false),
                    managementTab: this.vaildData(this.permission['sgs:customer:account:tab:management'],false),
                };
            }
        },
        methods: {
            handleClick(tab) {
                Object.keys(this.tabRefresh).forEach(item=>{
                    this.tabRefresh[item]=false;
                })
                this.tabRefresh[tab.name]=true;
            }
        }
    }
</script>

<style lang="scss" scoped>
.wrap-tab {
    ::v-deep .el-tabs__nav-wrap {
        &::after {
            position: inherit;
        }
    }
    ::v-deep .el-tabs__content {
        margin-top: -58px;
    }
}
@import '../tab.scss';
</style>
