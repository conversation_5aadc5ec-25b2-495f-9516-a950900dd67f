<template>
  <div id="1" class="sgs-service-unit">
    <el-dropdown class="question" v-if="showServiceUnitFlag" trigger="click">
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(serviceUnit, index) in serviceUnits"
            :key="'service_unit_dropdown_' + index"
            @click="
              handleCommand(
                serviceUnit.productLineCode,
                serviceUnit.serviceDomain,
                serviceUnit,
              )
            "
            :class="
              selServiceUnit === serviceUnit.serviceUnitCode
                ? 'font-weight-bold'
                : 'normal_class'
            "
            :command="serviceUnit.productLineCode"
          >
            {{ serviceUnit.serviceUnitName }}
            <el-button
              v-if="defaultServiceUnit !== serviceUnit.serviceUnitCode"
              @click.stop="setDefault(serviceUnit)"
              round
              size="small"
              style="margin-left: 12px; z-index: 99999"
            >
              {{ $t('common.default') }}
            </el-button>
          </el-dropdown-item>
          <el-dropdown-item
            command="all"
            @click="handleCommand('all')"
            :class="!selServiceUnit ? 'font-weight-bold' : 'normal_class'"
          >
            {{ $t('common.all') }}
            <el-button
              v-if="defaultServiceUnit !== 'all'"
              @click.stop="setDefault('all')"
              round
              size="small"
              style="margin-left: 12px; z-index: 99999"
            >
              {{ $t('common.default') }}
            </el-button>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
      <span class="el-dropdown-link top-icon">
        <el-icon
          v-if="iconVal === 'el-icon-table-lamp'"
          :size="25"
          color="#fff"
        >
          <ReadingLamp />
        </el-icon>
        <el-icon
          v-else-if="iconVal === 'el-icon-watch'"
          :size="25"
          color="#fff"
        >
          <Watch />
        </el-icon>
        <i v-else :class="iconVal" style="color: #ffffff; font-size: 25px"></i>
      </span>
    </el-dropdown>
  </div>
</template>

<script setup>
import { onMounted, ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n' // 假设使用了 vue-i18n 进行国际化
import { useStore } from 'vuex' // 假设使用了 vuex 进行状态管理
import serviceUnitTool from '@/utils/serviceUnitTool'
import { objectIsNull } from '@/utils/validate'
import { ElNotification } from 'element-plus'
import registerApi from '@/api/register'
import { inspectionUrl } from '@/config/env'
import { getToken } from '@/utils/auth'
import { ReadingLamp, Watch } from '@element-plus/icons-vue'

const store = useStore()

const language = computed(() => store.state.common.language)

const userInfo = computed({
  get: () => store.state.user.userInfo,
  set: (newValue) => {
    store.commit('SET_USERIFNO', newValue)
  },
})
const currentUserInfo = ref({})
// 标记是否已经执行过初始化逻辑
const isInitialized = ref(false)

// 使用 i18n
const { t } = useI18n()

// 控制服务单元下拉框是否显示的标志
const showServiceUnitFlag = ref(true)

// 图标类名
const iconVal = ref('icon-all icongary_ALL')
const buIconList = ref([
  {
    productLineCode: 'SL',
    icon: 'icon-all iconxiebao1',
  },
  {
    productLineCode: 'HL',
    icon: 'icon-all iconshuibei',
  },
  {
    productLineCode: 'EE',
    icon: 'el-icon-watch',
  },
  {
    productLineCode: 'CPCH',
    icon: 'el-icon-table-lamp',
  },
  {
    productLineCode: 'AFL',
    icon: 'icon-all iconpingguo',
  },
  {
    productLineCode: 'all',
    icon: 'icon-all icongary_ALL',
  },
])

// 服务单元列表
const serviceUnits = ref([])

// 当前选中的服务单元代码
const selServiceUnit = ref('')

// 默认服务单元代码
const defaultServiceUnit = ref('')

const getBuImgUrl = (bu) => {
  const buIconObj = buIconList.value.find(
    (buIcon) => buIcon.productLineCode === bu,
  )
  if (objectIsNull(buIconObj)) return ''
  return buIconObj.icon
}

const serviceUnitName = ref('')
const showServiceUnitName = async (value) => {
  iconVal.value = getBuImgUrl(value)
  if (value === 'all') {
    serviceUnitName.value = t('common.all')
  } else {
    const serviceUnitObj = serviceUnits.value.find(
      (serviceUnit) => serviceUnit.productLineCode === value,
    )
    // 获取选择的名称
    if (!objectIsNull(serviceUnitObj)) {
      serviceUnitName.value = serviceUnitObj.serviceUnitName
    }
  }
}

const serviceUnitDataItems = ref([])
const queryUserServiceUnit = async (language) => {
  // 将 user 中的 serviceUnits 转成 SGSMart 格式
  if (!objectIsNull(currentUserInfo.value.serviceUnits)) {
    serviceUnitDataItems.value = currentUserInfo.value.serviceUnits.split(',')
    if (!objectIsNull(serviceUnitDataItems.value)) {
      let newServiceUnits = []
      for (let serviceUnitObj of serviceUnits.value) {
        if (
          serviceUnitDataItems.value.includes(serviceUnitObj.serviceUnitCode)
        ) {
          newServiceUnits.push(serviceUnitObj)
        }
      }
      // 去除 other 的 Serviceunit
      newServiceUnits = newServiceUnits.filter(
        (item) => item.productLineCode !== 'other',
      )
      serviceUnits.value = newServiceUnits
      if (newServiceUnits.length > 0) {
        showServiceUnitFlag.value = true
      }
    }
  }

  let productLineCode = currentUserInfo.value.productLineCode || 'all'
  showServiceUnitName(productLineCode)
}
const queryServiceType = async (language) => {
  let serviceUnitsDatas = await serviceUnitTool.queryServiceUnits(language)
  serviceUnits.value = serviceUnitsDatas
}
const initData = async (language) => {
  await queryServiceType(language)
  await queryUserServiceUnit(language)
}

const onloadServiceUnit = async (language) => {
  await initData(language)
  await showServiceUnitName(currentUserInfo.value.productLineCode || 'all')
}
watch(language, (newVal) => {
  onloadServiceUnit(newVal)
})

const setDefault = async (serviceUnit) => {
  if (serviceUnit === 'all') {
    serviceUnit = {
      serviceUnitCode: '',
      productLineCode: 'all',
      serviceDomain: '',
    }
  }
  try {
    const res = await registerApi.updateDefaultServiceUnit({
      serviceUnitCode: serviceUnit.serviceUnitCode,
    })
    if (res.success) {
      const newUserInfo = {
        ...currentUserInfo.value,
        productLineCode: serviceUnit.productLineCode,
        defaultProductLineCode: serviceUnit.productLineCode,
        defaultServiceDomain: serviceUnit.serviceDomain,
        selServiceDomain: serviceUnit.serviceDomain,
        defaultServiceUnit: serviceUnit.serviceUnitCode,
        selServiceUnitCode: serviceUnit.serviceUnitCode,
      }
      userInfo.value = newUserInfo
      defaultServiceUnit.value = serviceUnit.serviceUnitCode
      selServiceUnit.value = serviceUnit.serviceUnitCode

      if (serviceUnit.serviceDomain === 'Inspection') {
        window.open(
          inspectionUrl + '#/sso?redirect=/booking/Booking&token=' + getToken(),
        )
        return
      }

      location.reload()
    } else {
      ElNotification({
        type: 'error',
        message: res.msg,
      })
    }
  } catch (error) {
    console.warn('设置默认服务单元出错:', error)
  }
}
const handleCommand = (bu, serviceDomain, serviceUnitObj) => {
  const newUserInfo = {
    ...currentUserInfo.value,
    selServiceDomain: serviceDomain,
  }
  if (!objectIsNull(serviceUnitObj)) {
    newUserInfo.selServiceUnitCode = serviceUnitObj.serviceUnitCode
    selServiceUnit.value = serviceUnitObj.serviceUnitCode
  } else {
    selServiceUnit.value = 'all'
  }

  if (serviceDomain === 'Inspection') {
    window.open(
      inspectionUrl + '#/sso?redirect=/booking/Booking&token=' + getToken(),
    )
    return
  }

  showServiceUnitName(bu)
  newUserInfo.productLineCode = bu
  userInfo.value = newUserInfo
  location.reload()
}

const reload = () => {
  location.reload()
}
// 监听 userInfo 的变化
watch(
  userInfo,
  (newValue) => {
    if (!isInitialized.value && newValue.account) {
      currentUserInfo.value = newValue
      ;(async () => {
        if (objectIsNull(currentUserInfo.value.selServiceDomain)) {
          currentUserInfo.value.selServiceDomain =
            currentUserInfo.value.defaultServiceDomain
        }
        defaultServiceUnit.value =
          currentUserInfo.value.defaultServiceUnit || 'all'
        if (objectIsNull(currentUserInfo.value.selServiceUnitCode)) {
          selServiceUnit.value = currentUserInfo.value.defaultServiceUnit
        } else {
          selServiceUnit.value = currentUserInfo.value.selServiceUnitCode
        }
        await initData(language.value)
        // 标记为已初始化
        isInitialized.value = true
      })()
    }
  },
  {
    immediate: true,
  },
)
</script>
<style lang="scss" scoped>
.sgs-service-unit {
  .top-icon {
    font-weight: 400;
    color: #ffffff;
    margin-right: 5px;
    cursor: pointer;
  }
  .task {
    margin-bottom: 0;
    li {
      padding: 10px;
      cursor: pointer;
      &:hover {
        background: #f2f2f2;
      }
    }
  }
  .question {
    margin-top: 6px;
    /*margin-left: 5px;*/
    margin-bottom: 5px;
    color: #f60;
    img {
      display: block;
    }
  }
  .top-bar__img {
    margin: 0 8px 0 0;
  }
}
</style>
