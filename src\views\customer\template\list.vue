<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_customer_template_list" id="sgs_smart_customer_template_list">
            <div class="title_justify">
                <div style="display: inline">
                    <h3>{{formPurpose}} Template Management</h3>
                </div>
            </div>
            <el-card :body-style="{padding:'10px'}">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-select
                            size="medium"
                            placeholder="Product Line"
                            style="width: 100%;"
                            clearable
                            filterable
                            @change="changeProductLineCode"
                            v-model="searchForm.productLineCode"
                        >
                            <el-option
                                    v-for="(pl,index) of filterList.productLineList"
                                    :key="'pl_'+index"
                                    :label="pl.productLineName"
                                    :value="pl.productLineCode"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select size="medium"
                                   clearable
                                   filterable
                                   placeholder="Customer Name"
                                   style="width: 100%"
                                   v-model="searchForm.customerGroupCode">
                            <el-option
                                    v-for="(ms,index) of filterList.customerList"
                                    :key="'ms_'+index"
                                    :label="ms.customerGroupName"
                                    :value="ms.customerGroupCode">
                                {{ms.customerGroupName }} ({{ms.customerGroupCode}})
                            </el-option>
                            <el-option v-if="role.isSGS" label="General" value="general"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select size="medium"
                                   clearable
                                   filterable
                                   :placeholder="formPurpose+' Type'"
                                   style="width: 100%"
                                   v-model="searchForm.customerCategory">
                            <el-option
                                    v-for="(mt,index) of (filterList.materialTypeList[language] || [])"
                                    :key="'mt_'+index"
                                    :label="mt.label"
                                    :value="mt.code"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-input type="text" size="medium" placeholder="Template Name" clearable v-model="searchForm.templateName"></el-input>
                    </el-col>
                    <el-col :span="8" style="text-align: right">
                        <el-button  size="medium" type="primary" @click="initTable">Search</el-button>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="padding: 10px 0;">
                    <el-col>
                        <el-button size="medium" type="primary" @click="createNewTemplate">New Template</el-button>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col>
                        <el-table
                                ref="materialTemplateTable"
                                v-loading="tableLoading"
                                :data="dataList"
                                :max-height="500"
                                size="mini"
                                class="sgs_table_material_template_table"
                                fit
                                border
                                resizable
                        >
                            <el-table-column
                                v-for="(col,index) in columns"
                                :key="'col_'+index"
                                :prop="col.prop"
                                :label="col.label"
                                show-overflow-tooltip
                                :min-width="col.minWidth||220"
                            >
                                <template v-if="col.slot" slot="customerCategory" slot-scope="{row}">
                                    {{((filterList.materialTypeList[language] || []).find(l=>l.code==row.customerCategory) || {label:row.customerCategory}).label}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="id" fixed="right" width="200px">
                                <template slot="header">
                                    Action
                                </template>
                                <template slot-scope="{row}">
                                    <el-tooltip content="Edit"  placement="top">
                                        <i class="el-icon-edit menu-icon" @click="editTemplate(row)"></i>
                                    </el-tooltip>
                                    <el-tooltip content="Delete"  placement="top">
                                        <el-popconfirm
                                                confirm-button-text='Confirm'
                                                cancel-button-text='Cancel'
                                                icon="el-icon-info"
                                                icon-color="red"
                                                title="Delete the data?"
                                                @confirm="cancelTemplate(row)"
                                        >
                                            <i slot="reference" class="el-icon-delete menu-icon"></i>
                                        </el-popconfirm>
                                    </el-tooltip>
                                    <el-tooltip content="Return"  placement="top">
                                        <el-popconfirm
                                                confirm-button-text='Confirm'
                                                cancel-button-text='Cancel'
                                                icon="el-icon-info"
                                                icon-color="red"
                                                title="Return the data?"
                                                @confirm="returnTemplate(row)"
                                        >
                                            <i v-if="row.formStatus==1" slot="reference" class="el-icon-refresh-left menu-icon"></i>
                                        </el-popconfirm>
                                    </el-tooltip>
                                    <el-tooltip content="Active"  placement="top">
                                        <i class="el-icon-finished menu-icon" v-if="row.formStatus==0" @click="handlerFormStatus(row,'Confirm')"></i>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-pagination
                                @size-change="sizeChange"
                                @current-change="currentChange"
                                :current-page.sync="page.page"
                                :page-sizes="page.sizes"
                                :page-size.sync="page.rows"
                                :layout="page.layout"
                                :total="page.total"
                        >
                        </el-pagination>
                    </el-col>
                </el-row>
            </el-card>
        </div>
    </basic-container>
</template>

<script>
    import {getCustomerGroup, getProductLine,queryBuSetting} from "@/api/common/index";
    import {mapGetters} from "vuex";
    import api from "@/api/newSamples";
    import {validatenull,objectIsNull} from "@/util/validate";
    import {LanguageEnums} from "@/commons/enums/LanguageEnums";
    export default {
        name: "list",
        data() {
            return {
                LanguageEnums:LanguageEnums,
                pageLoading: false,
                tableLoading:false,
                dataList:[],
                filterList:{
                    productLineList:[],
                    customerList:[],
                    materialTypeList:{}
                },
                searchForm:{
                    productLineCode:'',
                    customerGroupCode:'',
                    customerCategory: '',
                    templateName:''
                },
                columns:[
                    {label:"Template Name",prop:"formName",minWidth:220},
                    {label:"Customer Name",prop:"customerGroupName",minWidth:220},
                    {label:"Product Line",prop:"buName",minWidth:220},
                    {label:this.formPurpose+" Type",prop:"customerCategoryName",minWidth:220,slot:true},
                    {label:"Update By",prop:"modifiedBy",minWidth:220},
                    {label:"Update Time",prop:"modifiedDate",minWidth:220},
                    {label:"Status",prop:"formStatusName",minWidth:220},
                    {label:"Version",prop:"formVersionNo",minWidth:220},
                ],
                page:{//分页对象
                    show: true,
                    page: 1,
                    rows: 20,
                    small: true,
                    sizes: [10, 20, 50, 100],
                    layout: 'total, sizes, prev, pager, next,jumper',
                    total: 0
                },
            }
        },
        methods: {
            createNewTemplate(){
                this.$router.push({
                    path:'/customer/template/templateDetail',
                    query:{formPurpose:this.formPurpose}
                })
            },
            // 切换当前页
            currentChange(currentPage) {
                this.initTable();
            },
            // 调整每页显示行数
            sizeChange(pageSize) {
                this.initTable();
            },
            editTemplate(row){
                let {id} = row;
                let formPurpose = this.formPurpose;
                this.$router.push({
                    path:'/customer/template/templateDetail',
                    query:{id,formPurpose}
                })
            },
            cancelTemplate(row){
                this.handlerFormStatus(row,'Inactive');
            },
            returnTemplate(row){
                this.handlerFormStatus(row,'Draft');
            },
            handlerFormStatus(row,formStatus){
                let {id} = row;
                let currentUser = this.role.isSGS ? "" : this.userInfo.userName;
                let param = {
                    id,
                    currentUser,
                    formStatus
                }
                api.changeDffFormStatus(param).then(res=>{
                    this.initTable();
                }).catch(err=>{

                })
            },
            initPage() {
                this.initProductLine();
                //this.initTemplateCustomerList();
                this.initCustomer();
                this.initTable();
            },
            changeProductLineCode(){
                let {productLineCode} = this.searchForm;
                this.filterList.materialTypeList = {};
                this.searchForm.customerCategory = '';
                if(!productLineCode){
                    return;
                }
                this.initTemplateType();
            },
            initTemplateType(){
                let paramCode = this.formPurpose=='Material'?'MaterialType':'ProductCategory';
                let params ={
                    systemId:1,
                    groupCode:'Template_Product_Material_Type',
                    productLineCode:this.searchForm.productLineCode,
                    paramCode
                }
                this.filterList.materialTypeList = {};
                queryBuSetting(params).then(res=>{
                    console.log("query type list",res);
                    if(res.status==200 && res.data && res.data.data){
                        let {data} = res.data;
                        if(!data || data.length==0){
                            return;
                        }

                        data = data[0];
                        let {paramValue} = data;
                        try{
                            let values = JSON.parse(paramValue);
                            this.filterList.materialTypeList =values;
                        }catch (e) {}
                    }

                }).catch(err=>{

                })
            },
            initTable(){
                this.dataList = [];
                let param = {
                    buCode: this.searchForm.productLineCode,
                    customerGroupCode:this.searchForm.customerGroupCode,
                    formPurpose:this.formPurpose,
                    formName:this.searchForm.templateName,
                    customerCategory:this.searchForm.customerCategory,
                    formStatusList:[0,1],
                    rows:this.page.rows,
                    page:this.page.page
                }
                this.tableLoading = true;
                api.queryProductViewForm(param).then(res=>{
                    console.log("materialist res",res);
                    if(res.status==200 && res.data &&res.data.data && res.data.data.rows){
                        this.dataList = res.data.data.rows || [];
                        this.page.total = res.data.data.records;
                    }
                    this.tableLoading = false;
                }).catch(err=>{
                    this.tableLoading = false;
                })

            },
            initProductLine(){
                getProductLine().then(res => {
                    if(res.status==200 && res.data){
                        const data = res.data.data || [];
                        let currentUserPL = (this.userInfo.productLineCodes || '').split(",");
                        if(!this.userInfo.productLineCodes && this.userInfo.productLineCode=='all'){
                            this.filterList.productLineList = data;
                            return
                        }
                        this.filterList.productLineList = data.filter(da=>currentUserPL.includes(da.productLineCode));
                    }
                });
            },
            initCustomer(){
                if(this.role.isSGS){
                    getCustomerGroup().then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            this.filterList.customerList = res.data.data || [];
                        }
                    }).catch(err=>{
                        console.log("查询customer err",err);
                    })
                }else{
                    let scmCustomerReq = {
                        page:1,
                        rows:9999,
                        list:[{
                            buCode : this.userInfo.productLineCode,
                            relationshipType : 'buyer',
                            customerNo : this.userInfo.bossNo
                        }]
                    }
                    let customerList = [];
                    api.queryScmCustomerList(scmCustomerReq).then(res=>{
                        if(res.status==200 && res.data && res.data.rows){
                            let customerData = res.data.rows;
                            customerData.forEach(c=>{
                                let {scmCustomerGroupCode,scmCustomerGroupName,scmCustomerNo,scmCustomerNameCN,scmCustomerNameEN} = c;
                                let customerName = '';
                                if(validatenull(scmCustomerGroupCode)){//客户
                                    if(this.language==this.LanguageEnums.CN.name){
                                        customerName = scmCustomerNameCN;
                                    }
                                    if(validatenull(customerName)){
                                        customerName=scmCustomerNameEN;
                                    }
                                }

                                customerList.push({
                                    customerGroupCode:scmCustomerGroupCode,
                                    customerGroupName:scmCustomerGroupName || customerName,
                                    bossNo:scmCustomerNo
                                })
                            })
                        }
                        let currentUserCusIndex = customerList.findIndex(c=>c.customerGroupCode==this.userInfo.customerGroupCode);
                        if(!objectIsNull(this.userInfo.customerGroupCode) && currentUserCusIndex==-1){
                            let groupObj = {
                                customerGroupCode : this.userInfo.customerGroupCode,
                                customerGroupName : this.userInfo.customerGroupName || this.userInfo.customerName,
                                bossNo : this.userInfo.customerGroupCode?"":this.userInfo.bossNo
                            }
                            customerList.push(groupObj);
                        }
                        this.filterList.customerList = customerList.filter(c=>c.customerGroupCode);
                    }).catch(err=>{
                        if(!objectIsNull(this.userInfo.customerGroupCode)){
                            let groupObj = {
                                customerGroupCode : this.userInfo.customerGroupCode,
                                customerGroupName : this.userInfo.customerGroupName || this.userInfo.customerName,
                                bossNo : this.userInfo.customerGroupCode?"":this.userInfo.bossNo
                            }
                            customerList.push(groupObj);
                        }
                        this.filterList.customerList = customerList;
                        console.log("query scm err",err)
                    })
                }
            },
            haseRole(type, role) {
                if (validatenull(type) || validatenull(role)) {
                    return false;
                }
                if (validatenull(this.userInfo.dimensions)) {
                    return false;
                } else {
                    if (this.userInfo.dimensions.hasOwnProperty(type)) {
                        if (this.userInfo.dimensions[type].indexOf(role) >= 0) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            },
        },
        mounted() {
        },
        created() {
            this.initPage();
        },
        watch: {},
        computed: {
            ...mapGetters([
                "permission",
                "userInfo",
                "language"
            ]),
            role() {
                return {
                    isSGS: this.haseRole("SGSUserRole", "SgsAdmin") || this.haseRole("SGSUserRole", "SgsLabUser"),
                    isThirdPartyLab: this.haseRole('UserRole', 'ThirdPartyLab')
                }
            },
        },
        props: {
            formPurpose:{
                type:String,
                default(){
                    return 'Material'
                }
            }
        },
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {}
    }
</script>

<style lang="scss">
    .sgs_smart_customer_template_list {
        font-family: 'Arial' !important;
        background: #fff;
        padding: 24px 32px;

        .menu-icon{
            font-size: 20px;
            cursor: pointer;
            margin: 0 10px;
            color:#ff6600
        }
        .sgs_table_material_template_table{
            .el-table--mini .el-table__cell{
                padding: 0 !important;
            }
        }
    }
</style>