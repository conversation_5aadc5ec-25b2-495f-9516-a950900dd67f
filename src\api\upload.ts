import request from "./request";

export const uploadFile = (data: FormData) => {
    return request({
        url: '/sgsapi/FrameWorkApi/file/uploadPublicFile',  
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data
    })
}

export const uploadFileAttachment = (data: FormData) => {
    return request({
        url: '/sgsapi/FrameWorkApi/file/doUpload',  
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data
    })
}