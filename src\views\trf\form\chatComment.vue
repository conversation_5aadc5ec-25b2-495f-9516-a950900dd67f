<template>
    <div id="chatComment" class="chatComment">
       <el-tabs active-name="comment" v-model="activeName">
           <el-tab-pane name="comment" :label="$t('comment.chatDialog.tabComments')">
           </el-tab-pane>
           <!-- 附件列表-->
           <el-tab-pane name="att" :label="$t('comment.chatDialog.tabAttachment')">
               <comment-attach
                    :table-data="getCommentAttr"
               ></comment-attach>
           </el-tab-pane>
       </el-tabs>

        <div v-show="activeName=='comment'">
            <!-- select copy 留言功能-->
            <el-row class="chat_copy_select">
                <el-col :span="4" :offset="18" style="text-align: right">
                    <el-button type="text" @click="showSelection = !showSelection" v-if="!showSelection">{{$t('comment.chatDialog.selectComment')}}</el-button>
                    <el-button type="text" @click="copySelectComment" v-if="showSelection">{{$t('comment.chatDialog.copy')}}</el-button>
                </el-col>
            </el-row>
            <div class="chat_body">
                <div class="chat_content">
                    <!-- 留言表格-->
                    <el-table
                            ref="commentTable"
                            style="width: 100%"
                            class="comment_chat_table"
                            :data="commentDataList"
                            :border="false"
                            size="mini"
                            :show-header="false"
                            :empty-text="$t('NoData')"
                            fit
                    >
                        <el-table-column
                                type="selection"
                                v-if="showSelection"
                        ></el-table-column>
                        <el-table-column
                                prop="comment"
                                label=""
                        >
                            <template slot-scope="{row}">
                                <el-row>
                                    <el-col :offset="9" :span="1" v-if="row.isSupplierShow==1 && leftOrRight(row)=='right'" :style="{'textAlign':leftOrRight(row)}">
                                        <i class="el-icon-view" v-if="isInternal() || isBuyer()"></i>
                                    </el-col>
                                    <el-col :offset="(leftOrRight(row)=='right' && row.isSupplierShow==0) ? 10:0" :span="14">
                                        <div :class="'comment_table_chat_cell_'+leftOrRight(row)">
                                            <el-row>
                                                <el-col :span="20">
                                                    {{row.createUser}}
                                                    {{transTime(row.newCreateTime)}}
                                                </el-col>
                                                <el-col :span="4" style="text-align: right">
                                                    <el-tooltip class="item" effect="light" :content="row.attachments.map(i=>i.fileName).join('\r\n')" placement="top">
                                                        <i
                                                            v-if="row.attachments && row.attachments.length>0"
                                                           class="el-icon-paperclip"
                                                           @click="downloadAttach(row.attachments)"
                                                           style="cursor: pointer"
                                                        ></i>
                                                    </el-tooltip>
                                                    &nbsp;

                                                    <el-popover
                                                            placement="top-start"
                                                            width="200"
                                                            trigger="hover"
                                                    >
                                                        <div v-if="row.contactEmail" v-html="commentEmail(row)"></div>
                                                        <i class="el-icon-message"
                                                           slot="reference"
                                                           v-if="row.contactEmail &&(isInternal() || (report.customerRoleEnums || []).includes(row.createUserRole-0))"
                                                        ></i>
                                                    </el-popover>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col>
                                                    <div style="white-space: pre-wrap">{{row.comment}}</div>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-col>

                                    <el-col :span="1" v-if="row.isSupplierShow==1 && leftOrRight(row)=='left'">
                                        <i class="el-icon-view" v-if="isInternal() || isBuyer()"></i>
                                    </el-col>
                                </el-row>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <!-- 上传文件、发送comment、发送按钮-->
                <el-row :gutter="5">
                    <el-col :span="1" style="text-align: right">
                        <el-tooltip class="item" effect="light" :content="$t('attachment.upload')" placement="top">
                            <el-button type="text" v-if="isLoadUpload" icon="icon-all iconUploading" @click="openUpload" :disabled="applicantDisabled()"></el-button>
                        </el-tooltip>
                    </el-col>
                    <el-col :span="19">
                        <el-form ref="commentForm" :model="commentForm" :rules="commentRules" @submit.native.prevent>
                            <el-form-item prop="comment" required>
                                <el-input
                                        type="textarea"
                                        :rows="1"
                                        clearable
                                        show-word-limit
                                        maxlength="5000"
                                        autosize
                                        :disabled="applicantDisabled()"
                                        class="comment_textarea"
                                        style="width: 100%;"
                                        size="medium"
                                        v-model="commentForm.comment"
                                        :placeholder="isNotBuyerAndFinalize()?$t('comment.chatDialog.replayNotBuyer') : $t('comment.chatDialog.reply')"
                                ></el-input>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="4" style="text-align: left">
                        <el-button
                                style="border: solid 1px #ff6600;color: #ff6600;"
                                circle
                                size="small"
                                :icon="buttonArrow?'el-icon-arrow-down':'el-icon-arrow-left'"
                                @click="changeArrow">
                        </el-button>
                        <el-button
                                :disabled="applicantDisabled()"
                                type="primary"
                                size="medium"
                                @click="sendComment"
                                :loading="saveBtnLoading">
                            {{$t('comment.chatDialog.send')}}
                        </el-button>
                    </el-col>
                    <!-- 文件信息展示 -->
                    <el-col :offset="1" :span="23"
                            v-show="fileTitle"
                            style="margin-top: -15px;padding-bottom: 0 !important;margin-bottom: 0 !important;">
                        <i class="el-icon-paperclip">{{fileTitle}}</i>
                        <el-button type="text" icon="el-icon-close" @click="fileRemove"></el-button>
                        <!--<div style="padding: 0;width: 100%;border-bottom: solid 1px lightgray;" v-show="fileTitle">
                        </div>-->
                    </el-col>
                </el-row>
                <transition name="fade" duration="100">
                    <div v-show="buttonArrow" style="padding-left: 5px">
                        <!-- 附加report-->
                        <el-row :gutter="5">
                            <el-col  :offset="1" :span="4">
                                {{$t('communicationLog.pdfReport')}}
                            </el-col>
                            <el-col :span="19">
                                <el-checkbox-group v-model="reportPdfSend">
                                    <el-checkbox checked="checked"
                                                 v-for="item in report.reportFiles"
                                                 :key="item.cloudId"
                                                 :title="item.fileName"
                                                 :label="item">{{item.fileName | reportPdfNameFilter}}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-col>
                        </el-row>
                        <!-- 可见范围-->
                        <el-row v-show="isBuyer()">
                            <el-col  :offset="1" :span="4">
                                {{$t('comment.chatDialog.visibleTo')}}
                            </el-col>
                            <el-col :span="19">
                                <el-select
                                        style="width: 30%"
                                        size="medium"
                                        v-model="visibleTo"
                                        @change="changeVisibleTo"
                                >
                                    <el-option :label="$t('comment.chatDialog.internalOnly')" value="1"></el-option>
                                    <el-option :label="$t('comment.chatDialog.internalAndApplicants')" value="2"></el-option>
                                </el-select>
                            </el-col>
                        </el-row>
                        <!--同时通知-->
                        <el-row>
                            <el-col  :offset="1" :span="4">
                                {{$t('comment.chatDialog.notificationTo')}}
                            </el-col>
                            <el-col :span="15">
                                <el-select v-model="emailGroupSelected"
                                           multiple
                                           style="width: 100%;background: white"
                                           @change="handleEmailGroupSelectChange"
                                           @remove-tag="removeTag"
                                           :placeholder="$t('comment.emailGroup')"
                                           :no-data-text="$t('NoData')">
                                    <el-option
                                            v-for="item in emailGroupList"
                                            :key="item.id"
                                            :label="item.emailGroupName"
                                            :value="item.id"
                                    ></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="15" :offset="5">
                                <el-select v-model="emailArray"
                                           @change="contactChange"
                                           multiple
                                           filterable
                                           allow-create
                                           default-first-option
                                           size="small"
                                           style="width: 100%;"
                                           :placeholder="$t('comment.emailAddress')"
                                           :no-data-text="$t('NoData')">
                                    <el-option v-for="(contact,index) in customerContactData"
                                               :label="contact.contactEmail" :value="contact.contactEmail"></el-option>
                                </el-select>
                            </el-col>
                        </el-row>
                    </div>
                </transition>
            </div>
        </div>

        <sgs-batch-upload
                v-if="isLoadUpload"
                append-to-body
                :systemID="1"
                :limit="1"
                  :handle-upload-success="uploadSuccess"
                  ref="batchUpload"
                  :accept="batchUploadObj.accept"
                  :upload-url="batchUploadObj.uploadUrl"
                  :handle-upload-error="()=>{}"
                  :attachment-type-options="batchUploadObj.attachmentTypeOptionsForUpload"
                  :attachment-type-default-value="batchUploadObj.attachmentTypeDefaultValue"
                  :file-max-sizes="20">

        </sgs-batch-upload>
    </div>
</template>

<script>
    import moment from "moment";
    import {DictionaryEnums} from "@/commons/enums/DictionaryEnums";
    import {queryDictionaryByLanguage} from "@/api/common";
    import { LanguageEnums } from "@/commons/enums/LanguageEnums";
    import {addComment,downLoadFile,updateCommentReadStatus} from "@/api/trf/trf";
    import {validateEmail, validatenull} from "../../../util/validate";
    import CommentAttach from "./commentAttach";
    import {mapGetters} from "vuex";
    import {tzFormChina} from '@/util/datetimeUtils'

    export default {
        name: "chatComment",
        data() {
            return {
                loadTable:true,
                isLoadUpload:false,
                attachmentFileCodeMap:{},
                commentRules:{
                  comment:[ {required: true, message: this.$t('communicationLog.validate.comment'), trigger: 'blur'}]
                },
                buttonArrow:true,
                collapseActiveNames:"1",
                report:null,
                trfId:null,
                showUploadDia:false,
                reportOrTestLine:0,//用来区分单当前是report还是testline
                attachReport:true,
                visibleToVal:true,
                activeName:'comment',
                showSelection:false,
                tableCommentVisible:true,
                fileTitle:'',
                saveBtnLoading:false,
                commentDataList:[],
                fileList:[],
                customerContactData:[],
                emailArray:[],
                reportPdfSend:[],
                emailGroupSelected: [],//选择的邮件组数据
                visibleTo: '1',
                commentForm:{
                    commentId:'',
                    commentType:'',
                    trfId:null,
                    contactId: null,
                    comment: '',
                    isSupplierShow: 0,
                    contactName: '',
                    contactEmail: '',
                    attachments: [],
                },
                batchUploadObj:{
                    uploadUrl: '/api/sgsapi/FrameWorkApi/file/doUpload',
                    accept: '.txt,.ppt,.pptx,.xls,.xlsx,.doc,.docx,.png,.jpg,.jpeg,.pdf,.msg,.eml',
                    attachmentTypeOptionsForUpload: [],
                    attachmentTypeDefaultValue: '',
                },
                emailGroupIdConcatMap:{},
            }
        },
        methods: {
            isInternal(){
                return !this.userInfo.companyId;
            },
            transTime(time){
                if(!time){
                    return '';
                }
                return tzFormChina(time, 'YYYY-MM-DD HH:mm:ss');
            },
            //区分留言应该展示左边还是右边
            leftOrRight(row){
                //TODO 从留言对象中获取关键属性，然后返回left 还是 right
                let {createUserRole} = row;
                let {customerRoleEnums} = this.report;
                //自己的语言，放在右边
                if((customerRoleEnums || []).includes(createUserRole-0)){
                    return 'right';
                }
                return  'left';
            },
            //文件上传时钩子函数
            openUpload(){
                if(this.fileList.length>0){
                    this.$notify({title: 'Message', message: 'You can upload 1 files at most', type: 'warning'});
                    return false;
                }
                this.$refs.batchUpload.open();
            },
            uploadSuccess(data){
                if(data){
                    let attachmentType=data.attachmentType;
                    data.data.forEach(item =>{
                        const attachment = {
                            'attachmentId': item.cloudID,
                            'fileUrl': item.path,
                            'fileName': item.attachmentName+"."+item.suffixes,
                            'size': item.size,
                            'fileType': attachmentType,
                            'object': 'comment',
                            'updateUser':this.userInfo.userName,
                        }
                        this.fileTitle = attachment.fileName;
                        this.fileList.push(attachment);
                    });
                }
                this.$refs.batchUpload.close();
            },

            fileRemove() {
                this.saveBtnLoading = false;
                this.fileList = []
                this.fileTitle = '';
            },
            changeVisibleTo(){
                if(this.visibleTo==2){
                    this.commentForm.isSupplierShow = 1;
                    if(this.emailGroupSelected.indexOf("applicant_id")==-1){
                        this.emailGroupSelected.unshift('applicant_id');
                        this.handleEmailGroupSelectChange(this.emailGroupSelected);
                    }
                }
            },
            changeArrow(){
              this.buttonArrow = !this.buttonArrow;
              this.collapseActiveNames = this.buttonArrow?"1":"0";
            },
            commentEmail(row){
              let {contactEmail} = row;
              if(!contactEmail){
                  return '';
              }
              let emails =  contactEmail.split(',').join('<br/>')
              let html = `To:<br/>${emails}`;
              return html;
            },
            sendComment(){
                this.$refs['commentForm'].validate((valid)=>{
                    if(!valid){
                        return;
                    }
                    if(this.emailArray.length > 10){
                        this.$message.error(this.$t('comment.chatDialog.lengthOver10'));
                        return;
                    }

                    let {id,reportNo,trfId} = this.report;
                    this.$set(this.commentForm, 'createTime', moment().format('YYYY-MM-DD'));
                    this.$set(this.commentForm, 'trfId', trfId);
                    this.$set(this.commentForm, 'trfReportId', id);
                    this.$set(this.commentForm, 'reportNo',reportNo);
                    var emailStr = '';
                    this.emailArray.find((item) => {
                        emailStr += item + ',';
                    });
                    if (emailStr.length > 0) {
                        emailStr = emailStr.substr(0, emailStr.length - 1);
                    }
                    let emailGroupStr = '';
                    this.emailGroupSelected.find((item) => {
                        emailGroupStr += item + ',';
                    });
                    if (emailGroupStr.length > 0) {
                        emailGroupStr = emailGroupStr.substr(0, emailGroupStr.length - 1);
                    }
                    var reports = [];
                    if (this.reportPdfSend) {
                        this.reportPdfSend.find((item) => {
                            reports.push(item)
                        });
                    }
                    this.$set(this.commentForm, 'attachments', this.fileList);
                    this.$set(this.commentForm, 'reports', reports);
                    this.$set(this.commentForm, 'contactEmail', emailStr);
                    this.$set(this.commentForm, 'emailGroup', emailGroupStr);
                    this.$set(this.commentForm, 'commentDate', moment().format('YYYY-MM-DD'));
                    this.saveBtnLoading = true;
                    addComment(this.commentForm).then(res => {
                        this.saveBtnLoading = false;
                        this.$message({
                            type: "success",
                            message: this.$t('api.success')
                        });
                        this.$emit('saveSuccess')
                    },error=>{
                        this.saveBtnLoading = false;
                    });
                })
            },
            contactChange(emailList) {
                let pasteEmailArr = this.emailArray;
                emailList.find((item1) => {
                    let validateRes = validateEmail(item1);
                    if (validateRes) {
                        if (pasteEmailArr.indexOf(item1) == -1) {
                            pasteEmailArr.push(item1);
                        }
                    } else {
                        pasteEmailArr.splice(pasteEmailArr.indexOf(item1), 1);
                    }
                });
                if(pasteEmailArr.length>10){
                    this.$message.error(this.$t('comment.chatDialog.lengthOver10'));
                    this.emailArray.splice(-1);
                    return;
                }
                this.emailArray = pasteEmailArr;
            },
            removeTag(tagVal){
                let contacts = this.emailGroupIdConcatMap[tagVal];
                contacts.forEach(c=>{
                    let index = this.emailArray.indexOf(c);
                    if(index!=-1){
                        this.emailArray.splice(index,1);
                    }
                })
            },
            handleEmailGroupSelectChange(emailGroupIds) {
                var vm = this;
                //将选择的客户组中的email地址放入Recipient(s) from Address Book中
                if (!validatenull(emailGroupIds)) {
                    emailGroupIds.find((val) => {
                        let obj = {};
                        obj = vm.emailGroupList.find((item) => {
                            return item.id === val;
                        });
                        if (!validatenull(obj)) {
                            console.log(vm.emailArray);
                            obj.contacts.find((item) => {
                                if (vm.emailArray.indexOf(item.contactEmail) == -1) {
                                    vm.emailArray.push(item.contactEmail);
                                }
                            });
                        }
                    });
                }
                console.log(this.emailGroupSelected);
            },
            copySelectComment(){
                let currentSelection = this.$refs.commentTable.selection;
                console.log("currentSlection",currentSelection);
                if(!currentSelection){
                    return;
                }
                let comments = [];
                currentSelection.forEach(sel=>{
                    let {createUser,newCreateTime,comment,attachments} = sel;
                    let msg = `${createUser} ${newCreateTime}: ${comment}`;
                    if(attachments && attachments.length>0){
                        msg += "\r\n";
                        let att = attachments[0];
                        let {fileName} = att;
                        msg += `${createUser} ${newCreateTime}: Attached "${fileName}"`;
                    }
                    comments.push(msg);
                })
                let copyComment = this.commentForm.comment;
                if(copyComment){
                    copyComment = copyComment+"\r\n";
                }
                copyComment += comments.join("\r\n");
                this.commentForm.comment = copyComment;
                this.showSelection = false;

            },
            downloadAttach(attachs){
                attachs.forEach(att=>{
                    let {fileUrl} = att;
                    if (fileUrl) {
                        downLoadFile(fileUrl).then(res => {
                            let downloadURL = res.data.data;
                            window.open(downloadURL, '_blank')
                        }, error => {
                            this.$message.error(this.$t('api.error'));
                        });
                    }
                })
            },
            updateCommentRead(){
                let param ={
                    trfId:this.trfId,
                    commentId:this.commentForm.commentId
                }
                updateCommentReadStatus(param).then(res=>{

                }).catch(err=>{

                });
            },
            isBuyer(){
                let {customerRoleEnums} = this.report;
                return (customerRoleEnums || []).includes(3);
            },
            isNotBuyerAndFinalize(){
                let {customerRoleEnums,reviewConclusionStatus} = this.report;
                let isBuyer = (customerRoleEnums || []).includes(3);
                let finalize = reviewConclusionStatus-0 == 3;
                return !isBuyer && finalize;
            },
            applicantDisabled(){
                let {customerRoleEnums,reviewConclusionStatus} = this.report;
                let isBuyer = (customerRoleEnums || []).includes(3);
                //不是buyer ，但是reviewConclusion = finalize ，不能编辑
                if(!isBuyer && reviewConclusionStatus-0 == 3){
                    return true;
                }
                //applicant 可以编辑
                if(!(customerRoleEnums || []).includes(1)){
                    return false;
                }
                //是buyer 可以编辑
                if(isBuyer){
                    return false;
                }
                //只有applicant 且 reviewConlusionstatus是finalize时，不能编辑
                return reviewConclusionStatus-0 == 3;

            },
            initUploadDictionary(){
                this.batchUploadObj.attachmentTypeOptionsForUpload = [];
                //根据当前语言查询附件类型字典项数据
                let langaugeCode = 'EN';
                if(this.language=='zh-CN'){
                    langaugeCode='CHI'
                }
                let params = {"sysKeyGroup":"OrderAttachmentType","systemID":1,"languageCode":langaugeCode}
                queryDictionaryByLanguage(params).then(res=>{
                    let  attachmentTypes = res.data;
                    attachmentTypes.forEach(item => {
                        let label = item.sysValue;
                        let value = item.sysKey;
                        const option = {
                            'label': label,
                            'value': value
                        }
                        this.attachmentFileCodeMap[value] = label;  // 建立key-value对，方便查询名称
                        this.batchUploadObj.attachmentTypeOptionsForUpload.push(option);
                    })
                    debugger;
                    let values = DictionaryEnums.Attachment.CommentAttachmentType.items.map(i=>i.code);
                    let smartFileTyleList = this.batchUploadObj.attachmentTypeOptionsForUpload.filter(d=>values.includes(d.value));
                    this.batchUploadObj.attachmentTypeOptionsForUpload=smartFileTyleList;

                    console.log("attachmentTypeOptionsForUpload",this.batchUploadObj.attachmentTypeOptionsForUpload)
                    this.batchUploadObj.attachmentTypeDefaultValue=this.batchUploadObj.attachmentTypeOptionsForUpload[0].value;
                    this.isLoadUpload=true;
                    this.loadTable=false;
                    this.$nextTick(()=>{
                        this.loadTable=true;
                    })
                });

                // DictionaryEnums.Attachment.CommentAttachmentType.items.forEach(item => {
                //     let label = item.enKey;
                //     let value = item.code;
                //     if (this.language === LanguageEnums.CN.name) {
                //         label = item.cnKey;
                //         value = item.code;
                //     }
                //     const option = {
                //         'label': label,
                //         'value': value
                //     }
                //     this.batchUploadObj.attachmentTypeOptions.push(option);
                //     this.batchUploadObj.attachmentTypeOptionsForUpload.push(option);
                // })
            },
            initEmailGroupConcat(){
                let map = {}
                this.emailGroupList.forEach(eg=>{
                    let {id,contacts} = eg;
                    let emails = (contacts || []).map(c=>c.contactEmail);
                    map[id] = emails || [];
                })
                this.emailGroupIdConcatMap = map;
            },
            commentScrollBottom(){
                if(this.activeName == 'comment'){
                    setTimeout(()=>{
                        document.getElementsByClassName("chat_content")[0].scrollTop
                            = document.getElementsByClassName("chat_content")[0].scrollHeight;
                    },500)
                }
            },
        },
        computed:{
            ...mapGetters(["language","userInfo"]),
            getCommentAttr(){
                let comments = this.commentDataList;
                let attrs = [];
                (comments || []).forEach(c=>{
                    let {attachments} = c;
                    if(attachments && attachments.length>0){
                        attrs = [...attrs,...attachments];
                    }
                })
                return attrs;
            }
        },
        filters:{
            reportPdfNameFilter(value) {
                value = value.toString();
                if (value.length > 50) {
                    value = value.slice(0, 50) + '...'
                }
                return value
            }
        },
        created() {
            let {report,data,type} = this.commentData;
            this.report = report;
            this.trfId = report.trfId;
            this.reportOrTestLine = data;
            let {comments,id,rootReportNo,testId} = data;
            let index = 0;
            (comments || []).forEach(c=>{
                c.index = index++;
            })
            let commentId = type==1 ? rootReportNo : testId;
            this.commentDataList = comments;
            this.commentForm.commentId = commentId ;
            this.commentForm.commentType = type;
            this.initEmailGroupConcat();
            this.updateCommentRead();
            this.initUploadDictionary();
            this.commentScrollBottom();
        },
        mounted() {
        },
        components: {CommentAttach},
        props:{
            //commentData中 type=1 report type=2 testline 维度
            commentData:{
                type:Object,
                default:{
                    report:null, //report 对象，如果type=1 则report=data
                    data:null, //具体的report 还是testliune
                    type:null
                }
            },
            emailGroupList:{
                type:Array,
                default:[]
            }
        }
    }
</script>

<style lang="scss">
    .chatComment {
        height: 600px;
        overflow: auto;
        &::-webkit-scrollbar{
          width: 2px !important;
        }
        i.iconUploading{
            font-size: 24px !important;
        }
        .chat_body{
            height: 530px;
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 0 !important;
            }
        }
        .chat_copy_select.el-row {
            position: absolute;
            top: 70px;
            z-index: 9999;
            right: 130px;
        }
        .chat_content{
            width: 100%;
            height: 320px;
            max-height: 360px;
            overflow-y: auto;
            border-bottom: solid 1px #CCCCCC;
            margin-bottom: 10px;
            &::-webkit-scrollbar{
                width: 2px !important;
            }
        }
        .comment_textarea .el-textarea__inner{
            max-height: 150px !important;
            border-top: none;
            border-right: none;
            border-left: none;
        }
        .comment_chat_table td{
            border: none !important;
        }
        .el-table::v-deep tbody tr:hover{
            background-color: transparent !important;
        }
        .el-table::v-deep tbody tr:hover>td{
            background-color: transparent !important;
        }
        .comment_table_chat_cell_left,.comment_table_chat_cell_right{
            position: relative;
            background-color: #f1f1f1;
            border-radius: 10px;
            padding:2px 10px;
            margin: 0 20px;
        }
        .comment_table_chat_cell_left::before{
            content: "";
            position: absolute;
            left: -10px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-right: 10px solid #f1f1f1;
            border-bottom: 10px solid transparent;
        }
        .comment_table_chat_cell_right::before{
            content: "";
            position: absolute;
            right: -20px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 10px solid #f1f1f1;
        }
        .commentTableHeader{

        }
    }
</style>