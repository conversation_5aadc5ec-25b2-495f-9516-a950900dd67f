import Vue from 'vue';
import axios from './router/axios';
import VueAxios from 'vue-axios';
import App from './App';
import router from './router/router';
import './permission'; // 权限
import './error'; // 日志
import store from './store';
import { loadStyle } from './util/util'
import * as urls from '@/config/env';
import Element from 'element-ui';
import sgsCustomerTotoList from '@sgs/sci-todo-list-v2'
//引入icontfontcss
import './assets/third-party/ali-icon/icontfont.css'
import { getStore } from '@/util/store'
import * as Sentry from "@sentry/vue";
import { BrowserTracing } from "@sentry/tracing";
import sentryVersion from '../sentryVersion.json'

import dialogDrag from '@/commons/directive/dialog-drag.js'
Vue.directive(dialogDrag)

import _ from 'lodash'
import {
    iconfontUrl,
    iconfontVersion
} from '@/config/env'; // Internationalization

import basicContainer from './components/basic-container/main'


Vue.use(router)
Vue.use(VueAxios, axios)
Vue.prototype.$axios = axios;
import SgsComponent from '@sgs/component-v2'
Vue.use(SgsComponent)



// import Avue from '@smallwei/avue';
import Avue from '@sgs/sgs-plugin-form-design-render/public/cdn/avue/avue.min.js'
// //import '@smallwei/avue/lib/index.css';
Vue.use(Avue,{ locale:'zh' });

import sgsDff from '@sgs/dff-v2';
import sgsForm from '@sgs/sgs-mart-dataentry-form'
Vue.use(sgsForm);
Vue.config.devtools=true;
const env = process.env
var currentEnv='dev';
if (env.VUE_APP_ENV == 'development') {
    currentEnv='dev'
} else if (env.VUE_APP_ENV == 'production') {
    currentEnv='prod'
    Sentry.init({
        Vue,
        dsn: "https://<EMAIL>/37",
        release: sentryVersion.version,
        integrations: [
            new BrowserTracing({
                routingInstrumentation: Sentry.vueRouterInstrumentation(router),
                tracingOrigins: ["localhost", "my-site-url.com", /^\//],
            }),
            new Sentry.Replay()
        ],
        tracesSampleRate: 1.0,
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
        ignoreErrors: [],
        ignoreUrls:[
            /https:\/\/cdn\.cookielaw\.org/
        ]
    });
} else if (env.VUE_APP_ENV == 'uat') {
    currentEnv='uat'
}else if (env.VUE_APP_ENV == 'test') {
    currentEnv='test'
}
Vue.prototype.$currentEnv = currentEnv;
Vue.setEnv(currentEnv,'sgsmart','https');
// Vue.setEnv('uat')
// Vue.setEnv('prod')
import sgsFramework from "@sgs/framework-v2";
Vue.use(sgsFramework);
Vue.use(sgsDff);
Vue.setEnvFramework(currentEnv,'sgsmart','https')
Vue.use(sgsCustomerTotoList)
Vue.setEnvCustomerToDoList(currentEnv,'sgsmart','https');

//powerbi
//预加载powberbi
console.log('开始预加载pwerbi');
let config = {
    type: 'report',
    embedUrl: 'https://app.powerbi.com/reportEmbed',
};
powerbi.preload(config);

import filters from './util/filters'
import {baseUrl, changePasswordUrl, testResultUrl} from "./config/env";
Object.keys(filters).forEach(k => {
    Vue.filter(k, filters[k])
})
Vue.prototype.$lodash = _;
Vue.prototype.$l = _;
var VueScrollTo = require('vue-scrollto');

Vue.use(VueScrollTo);

//注册全局容器
Vue.component('basicContainer', basicContainer)
// 加载相关url地址
Object.keys(urls).forEach(key => {
    Vue.prototype[key] = urls[key];
})
import SgsFormDesign from '@sgs/sgs-plugin-form-design-render'
Vue.use(SgsFormDesign)
// 动态加载阿里云字体库
iconfontVersion.forEach(ele => {
    loadStyle(iconfontUrl.replace('$key', ele));
})

//Vue.config.productionTip = false;
import './styles/common.scss';
import i18n from './lang';

Vue.use(Element, {
    i18n: (key, value) => i18n.t(key, value)
})

new Vue({
    router,
    store,
    i18n,
    render: h => h(App)
}).$mount('#app')
// import NkopFormDesign from 'nkop-plugin-form-design'
// Vue.use(NkopFormDesign)

