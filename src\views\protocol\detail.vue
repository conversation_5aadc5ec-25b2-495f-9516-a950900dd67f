<template>
    <basic-container v-loading="pageLoading">
        <div class="sgs_smart_protocol_detail" id="sgs_smart_protocol_detail">
            <div>
                <h4>Protocol Detail</h4>
                <div style="display: flex;justify-content: space-between;">
                    <section>
                        <h3>{{protocolForm.protocolName}}</h3>
                    </section>
                    <h5 style="line-height: 27px">
                        Create Time:{{protocolForm.createTime || nowDate}}
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        Update Time:{{protocolForm.updateTime || nowDate}}
                    </h5>
                </div>
                <div style="width: 100%;height: 1px;background-color:#DCDFE6;"></div>
            </div>
            <el-row >
                <el-col :span="4">
                    <ul class="nav-list" ref="nav" id="trf-left-nav">
                        <template v-for="(item, index) in navList.filter(n=>n.show)">
                            <li :class="{'is-sub': item.isSub,'active': item.active}"
                                :key="item.name"
                                class="nav-item"
                                @click="toTarget(index)">
                                <h5>{{ $t(item.name) }}</h5>
                            </li>
                        </template>
                    </ul>
                </el-col>
                <el-col :span="18">
                    <el-form
                            :disabled="protocolForm.id && !userEdit"
                             :model="protocolForm"
                             ref="protocolForm"
                             label-position="left"
                             label-width="150px">
                        <el-collapse v-model="activeNames">
                            <el-card class="sgs-box content-item">
                                <el-collapse-item name="1">
                                    <template slot="title">
                                        <h4 class="sgs-title">Protocol Info</h4>
                                    </template>
                                    <el-divider></el-divider>
                                    <el-row :gutter="20" v-for="(fl,readOnlyIndex) in [(templateObj.readOnlyFieldList || []) ,(templateObj.generalFieldList || []),(templateObj.customerFieldList || [])]" :key="'readonlyField_'+readOnlyIndex">
                                        <el-col :span="12" v-for="(f,index) in fl" :key="'gen_cus_'+index">
                                            <el-form-item
                                                    :rules="{ required: f.isRequired==1 , message: ((!f.fieldType || ['input'].includes(f.fieldType))? 'please input' : 'please select'), trigger: ['blur', 'change']}"
                                                    :label="f.fieldLabel"
                                                    :prop="f.fieldCode">
                                                <el-input-number
                                                        v-if="['number'].includes(f.fieldType)"
                                                        style="width: 100%"
                                                        v-model="protocolForm[f.fieldCode]"
                                                        controls-position="right"
                                                        :min="1"></el-input-number>
                                                <el-date-picker
                                                        v-if="['date'].includes(f.fieldType)"
                                                        :disabled="f.isReadOnly==1"
                                                        size="mini"
                                                        style="width: 100%"
                                                        v-model="protocolForm[f.fieldCode]"
                                                        type="date"
                                                        value-format="yyyy-MM-dd"
                                                        placeholder="Please select">
                                                </el-date-picker>
                                                <el-input v-if="!f.fieldType || ['input','textArea'].includes(f.fieldType)"
                                                          :type="f.fieldType=='textArea'?'textarea':'text'"
                                                          :disabled="f.isReadOnly==1 || (protocolForm.id && f.fieldCode=='protocolName')"
                                                          placeholder="Please input"
                                                          :rows="3"
                                                          clearable
                                                          :show-word-limit="!!f.length"
                                                          :maxlength="f.length"
                                                          v-model="protocolForm[f.fieldCode]"></el-input>
                                                <el-select
                                                        v-if="['select','select2'].includes(f.fieldType)"
                                                        style="width: 100%;"
                                                        :disabled="f.isReadOnly==1"
                                                        clearable
                                                        filterable
                                                        :multiple="['select2'].includes(f.fieldType)"
                                                        placeholder="Please select"
                                                        v-model="protocolForm[f.fieldCode]">
                                                    <el-option
                                                            v-for="(o,ind) in (f.sourceValue || [])"
                                                            :key="'o_'+ind"
                                                            :label="o.name"
                                                            :value="o.code"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col>
                                            <el-divider></el-divider>
                                        </el-col>
                                    </el-row>
                                </el-collapse-item>
                            </el-card>
                            <el-card class="sgs-box content-item">
                                <el-collapse-item name="2">
                                    <template slot="title">
                                        <h4 class="sgs-title">Protocol Scope</h4>
                                    </template>
                                    <el-divider></el-divider>
                                    <el-row :gutter="20">
                                        <el-col>
                                            <div style="padding: 20px" v-show="protocolForm.id && !userEdit"
                                                 v-html="protocolForm.protocolScope"></div>
                                        </el-col>
                                        <el-col :span="24" v-show="!protocolForm.id || userEdit">
                                            <Toolbar
                                                    style="border-bottom: 1px solid #ccc;"
                                                    :editor="editor"
                                                    :defaultConfig="toolbarConfig"
                                            />
                                            <Editor
                                                    style="min-height: 400px; overflow-y: hidden"
                                                    :defaultConfig="editorConfig"
                                                    v-model="protocolForm.protocolScope"
                                                    @onCreated="editorCreated"
                                            />
                                        </el-col>
                                    </el-row>
                                </el-collapse-item>
                            </el-card>
                            <el-card class="sgs-box content-item" v-if="pageId || protocolForm.id">
                                <el-collapse-item name="3">
                                    <template slot="title">
                                        <h4 class="sgs-title">Documents</h4>
                                    </template>
                                    <el-divider></el-divider>
                                    <documents
                                        ref="protocol_documents"
                                        :obj-id="pageId || protocolForm.id"
                                        :edit="pageId || btnRole(protocolForm,'Edit')"
                                        :default-save="false"
                                    ></documents>
                                    </el-collapse-item>
                            </el-card>
                        </el-collapse>
                    </el-form>
                </el-col>
            </el-row>
            <el-row class="sgs-footer page-no-print" style="position: fixed" v-if="!pageLoading">
                <el-col style="text-align: center">
                    <el-button type="primary" @click="backToList">Close</el-button>
                    <el-button type="primary" @click="saveProtocol" v-if="!protocolForm.id || btnRole(protocolForm,'Edit')">Save</el-button>
                </el-col>
            </el-row>
        </div>
    </basic-container>
</template>

<script>
    import protocolApi from '../../api/protocol/protocol.js'
    import protocolConfig from "./protocolConfig";
    import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
    import '@wangeditor/editor/dist/css/style.css';
    import { i18nChangeLanguage } from '@wangeditor/editor'
    import {mapGetters} from "vuex";
    import {objectIsNull} from "@/util/validate";
    import Documents from "./templates/documents";
    import moment from "moment";
    export default {
        name: "detail",
        data() {
            return {
                navList:[
                    {name:'Protocol Info',seq:1,active:true,isSub:false,show:true},
                    {name:'Protocol Scope',seq:2,active:false,isSub:false,show:true},
                    {name:'Documents',seq:3,active:false,isSub:false,show:true},
                ],
                element:{
                    nav:[],
                    content:[]
                },
                pageLoading: false,
                formDisabled: false,
                activeNames:['1','2','3'],
                protocolForm:{
                    protocolScope:''
                },
                editorConfig: {
                    placeholder: 'please input content...',
                    MENU_CONF: {},
                },
                toolbarConfig:{
                    toolbarKeys:[],
                },
                editor:null,
                userEdit:true,
                pageId:'',
                createNew:false,
                templateObj:{
                    id:'',
                    formName:'',
                    readOnlyFieldList:[],
                    generalFieldList:[],
                    customerFieldList:[]
                },
                templateDetailParam:{
                    templateId:'',
                    buCode:'',
                    customerBossNo:'',
                    customerGroupCode:''
                },
                nowDate:''
            }
        },
        methods: {
            saveProtocol(){
                this.$refs.protocolForm.validate(valid=>{
                    if(!valid){
                        let dom = this.$refs['protocolForm']
                        if(!dom){
                            return;
                        }
                        dom.$el.scrollIntoView({
                            block: 'start',
                            behavior: 'smooth'
                        });
                        this.$notify({
                            title: this.$t('tip'),
                            message: this.$t('trf.trfValidateError'),
                            type: 'warning'
                        });
                        return
                    }

                    if(!this.protocolForm.templateId){
                        this.$notify.error("No template data, can't save/update")
                        return;
                    }
                    let param = Object.assign({},this.protocolForm)
                    if(this.createNew){
                        param['id'] = this.pageId;
                    }
                    this.pageLoading = true;
                    protocolApi.saveEditDetail(param).then(res=>{
                        if(res.status==200 && res.data && res.data.data){
                            this.$refs.protocol_documents.saveData();
                            this.$notify({
                                type:'success',
                                message:'Success',
                                duration:1000,
                                onClose:()=>{
                                    if(this.protocolForm.id){
                                        window.location.reload();
                                        return;
                                    }
                                    let {id} = res.data.data;
                                    window.location.href = '/#/protocol/detail?id='+id;
                                    window.location.reload();
                                    this.pageLoading = false;
                                }
                            })
                        }
                    }).catch(err=>{
                        this.pageLoading = false;
                        //this.$notify.error("Error",err)
                    })
                })
            },
            initPage() {
                let {id,templateId,buCode,customerBossNo,customerGroupCode} = this.$route.query;
                if(id){
                    this.protocolForm.id = id;
                    this.initProtocol();
                    return;
                }
                if(!buCode || !templateId || (!customerBossNo && !customerGroupCode)){
                    this.$notify.error("Parameter error, can not load template");
                    return;
                }
                this.createNew = true;
                this.templateDetailParam.buCode = buCode;
                this.templateDetailParam.templateId = templateId;
                this.templateDetailParam.customerBossNo = customerBossNo;
                this.templateDetailParam.customerGroupCode = customerGroupCode;
                this.getNow();
                this.initProtocol(true);
            },
            initProtocol(init=false){
                this.pageLoading = true;
                let param = {
                    id : this.protocolForm.id
                }
                protocolApi.queryProtocolDetail(param).then(res=>{
                    if(res.status==200 && res.data && res.data.data){
                        let formObj = res.data.data;
                        let {id,templateId,productLineCode,buyCustomerBossNo,buyCustomerGroupCode} = formObj;
                        if(init){
                            this.pageId = id;
                            this.queryTemplate();
                            return;
                        }
                        this.templateDetailParam.buCode = productLineCode;
                        this.templateDetailParam.templateId = templateId;
                        this.templateDetailParam.customerBossNo = buyCustomerBossNo;
                        this.templateDetailParam.customerGroupCode = buyCustomerGroupCode;
                        this.userEdit = this.btnRole(formObj,'Edit');
                        this.protocolForm = formObj;
                        this.queryTemplate();
                    }else{
                        this.$notify.error(res.data.message);
                        this.pageLoading = false;
                    }
                }).catch(err=>{
                    this.pageLoading = false;
                })
            },
            queryTemplate(){
                //直接查询模板，渲染页面
                this.pageLoading = true;
                let param = {...this.templateDetailParam};
                protocolApi.queryTemplateDetail(param).then(res=>{
                    this.pageLoading = false;
                    if(res.status==200 && res.data && res.data.data){
                        let {id,formName,generalFieldList,customerFieldList} = res.data.data;
                        this.protocolForm.templateId = id;
                        this.templateObj.id = id;
                        this.templateObj.formName = formName;
                        let readOnlyFields = (generalFieldList || []).filter(c=>c.isReadOnly==1);
                        let generalFList = (generalFieldList || []).filter(c=>c.isReadOnly!=1);
                        (generalFList || []).sort((a,b)=>a.sequence-b.sequence);
                        this.templateObj.readOnlyFieldList = readOnlyFields || [];
                        this.templateObj.readOnlyFieldList.forEach(f=>{
                            let {defaultValue,fieldCode} = f;
                            this.protocolForm[fieldCode] = defaultValue;
                        })

                        this.templateObj.generalFieldList = generalFList || [];
                        this.templateObj.customerFieldList = customerFieldList || [];
                    }else{
                        this.$notify.error("Load template error");
                        this.pageLoading = false;
                    }
                    this.$nextTick(()=>{
                        this.initNavList();
                    })
                }).catch(err=>{
                    console.log("yichang err",err)
                    this.pageLoading = false;
                })
            },
            editorCreated(editor){
                i18nChangeLanguage('en')
                this.editor = Object.seal(editor)
                //var allMenuKeys = editor.getAllMenuKeys();
            },
            initNavList(){
                const nav = document.getElementsByClassName('nav-item')
                const cont = document.getElementsByClassName('content-item')
                this.element.nav = nav
                this.element.content = cont
                //导航初始化样式（默认第一个）
                nav[0].classList.add('active')
                this.navList[0].active = true;
                window.addEventListener("scroll", this.toScroll)
            },
            toTarget(index){
                const { content, nav } = this.element;
                this.navList.forEach((na,ind)=>{
                    na.active = index==ind;
                })
                const scrollTop = document.documentElement.scrollTop|| document.body.scrollTop;
                switch (index) {
                    case 0:
                        document.documentElement.scrollTop = 0;
                        return;
                }
                document.documentElement.scrollTop =  ((document.documentElement.scrollTop===0?-100:document.documentElement.scrollTop)+ (content[index].getBoundingClientRect().top))-100;
            },
            toScroll() {
                let navEle = document.querySelector('.nav-list')
                if(objectIsNull(navEle)){
                    return;
                }
                navEle.style.width = navEle.clientWidth + 'px'
                var w;
                //获取滚动距离
                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                const { content, nav } = this.element
                this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                window.addEventListener("resize", () => {
                    this.minScreen = document.body.offsetWidth <= 1366 ? true : false
                });
                // 侧边栏和评论栏固定
                if(scrollTop != undefined && scrollTop > 170) {
                    navEle.style.position = 'fixed'
                    navEle.style.top = '80px'
                } else {
                    navEle.style.position = 'initial'
                }
                // 侧边栏菜单添加当前高亮
                let len = content.length;
                let scrollIndex = 0;
                for (let i = 0;i < len; i++) {
                    //获取每块内容距离顶部距离
                    const offset = content[i].offsetTop;
                    //当划过第一块内容 改变左侧导航样式
                    if (scrollTop >= offset) {
                        scrollIndex = i;
                    }
                }
                for (let n = 0; n < len; n++) {
                    if(!nav[n]) {
                        return;
                    }
                    n == scrollIndex ? nav[n].classList.add('active') : nav[n].classList.remove('active')
                }

                if(scrollTop == 0) {
                    nav[0].classList.add('active')
                    nav[1].classList.remove('active')
                }
            },
            btnRole({permissions},code){
                return (permissions || []).map(p=>p.action).includes(code);
            },
            getNow(){
                this.nowDate = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
                this.interval = setInterval(()=>{
                    this.nowDate = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
                },1000);
            },
            backToList(){
                if(this.btnRole(this.protocolForm,'Edit')){
                    this.$confirm("Please make sure you have saved the information before you close the window.",'Tips',{
                        confirmButtonText: 'Confirm',
                        cancelButtonText: 'Cancel',
                        type: 'warning'
                    }).then(res=>{
                        this.$router.push({
                            path:'/protocol/list',
                            query:{}
                        })
                    })
                }else{
                    this.$router.push({
                        path:'/protocol/list',
                        query:{}
                    })
                }
            },
        },
        mounted() {
        },
        created() {
            this.toolbarConfig.toolbarKeys = protocolConfig.toolbarKeys;
            this.initPage();
        },
        watch: {
            language:{
                immediate:true,
                handler(newV){
                    console.log("language",newV);
                    //this.editor && i18nChangeLanguage('en')
                }
            }
        },
        computed: {
            ...mapGetters(["permission","language","userInfo"]),
        },
        props: {},
        updated() {
        },
        beforeDestroy() {
        },
        destroyed() {
        },
        components: {Documents, Editor, Toolbar}
    }
</script>

<style lang="scss">
    .sgs_smart_protocol_detail {
        font-family: 'Arial' !important;
        background: #fff;
        padding: 24px 32px;

        .nav-list {
            list-style: none;
            margin-top: 24px;
            padding-left: 0;
            li {
                cursor: pointer;
                border-left: 3px solid #D9D9D9;
                padding-left: 24px;
                font-size: 16px;
                font-weight: 400;
                color: #000000;
                &.is-sub {
                    padding-left: 40px;
                    font-size: 16px;
                    font-weight: 400;
                    color: #656565;
                    h5 {
                        font-weight: normal;
                    }
                }
                &.active {
                    color: #f60;
                    border-color: #f60;
                }
                h5 {
                    padding: 10px 0;
                    margin: 0;
                }
            }
        }
    }
</style>