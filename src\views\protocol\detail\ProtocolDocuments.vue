<template>
  <div>
    <div class="sgs_smart_protocol_documents" id="sgs_smart_protocol_documents">
      <div style="text-align: right" v-if="type">
        <el-icon
          class="icon-primary"
          @click="uploadDoc"
          style="font-size: 20px"
        >
          <Upload />
        </el-icon>
      </div>
      <el-table :data="dataList" border fit>
        <el-table-column
          label="Document Name"
          prop="fileName"
        ></el-table-column>
        <el-table-column
          label="Created Time"
          prop="createTime"
        ></el-table-column>
        <el-table-column show-overflow-tooltip label="Remark" prop="remark">
          <template #default="{ row }">
            <div v-if="!type">{{ row.remark }}</div>
            <el-input
              show-word-limit
              maxlength="1000"
              @blur="updateRemark(row)"
              v-model="row.remark"
              v-if="type"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column width="80px" label="Action" prop="fileName">
          <template #default="{ row, $index }">
            <el-tooltip content="Download" placement="top">
              <el-button
                type="primary"
                link
                :disabled="false"
                :icon="Download"
                @click="downloadFileHandle(row)"
              ></el-button>
            </el-tooltip>
            <el-popconfirm
              confirm-button-text="Confirm"
              cancel-button-text="Cancel"
              icon-color="red"
              :icon="InfoFilled"
              title="Delete the data?"
              width="170"
              @confirm="deleteFile(row, $index)"
            >
              <template #reference>
                <el-button
                  v-if="type"
                  style="margin-left: 10px"
                  type="primary"
                  link
                  :icon="Delete"
                ></el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <batch-upload
      v-if="isLoadUpload"
      title="Upload"
      append-to-body
      :systemID="1"
      :limit="5"
      :handle-upload-success="uploadSuccess"
      :handle-upload-error="uploadError"
      ref="batchUploadRef"
      accept=".pdf,.docx,.doc,.xlsx,.xls,.csv"
      upload-url="/api/sgsapi/FrameWorkApi/file/doUpload"
      :attachment-type-options="[]"
      attachment-type-default-value=""
      :file-max-sizes="20"
    ></batch-upload>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import {
  queryAttr,
  downloadFile,
  deleteAttrById,
  updateAttrRemarkById,
  saveAtt,
} from '@/api/protocol'
import { ElNotification } from 'element-plus'
import BatchUpload from '@/components/BatchUpload/index.vue'
import {
  Delete,
  Edit,
  Download,
  Upload,
  InfoFilled,
} from '@element-plus/icons-vue'
const props = defineProps({
  objId: {
    type: String,
    default: '',
  },
  type: {
    type: Boolean,
    default: true,
  },
  defaultSave: {
    type: Boolean,
    default: true,
  },
})

const pageLoading = ref(false)
const dataList = ref([])
const initDataList = ref([])
const isLoadUpload = ref(false)
const batchUploadRef = ref(null)

const initPage = async () => {
  const param = {
    objectId: props.objId,
    object: 'protocol',
    current: 1,
    size: 1,
  }
  pageLoading.value = true
  try {
    const res = await queryAttr(param)

    const newDataList = res.data.records
    newDataList.forEach((da) => {
      da.edit = false
    })
    dataList.value = newDataList
    initDataList.value = JSON.parse(JSON.stringify(newDataList))
  } catch (err) {
    // 可根据需要处理错误
  } finally {
    pageLoading.value = false
  }
}

const downloadFileHandle = async (row) => {
  const { fileUrl } = row
  const res = await downloadFile(fileUrl)
  const downloadURL = res.data
  if (downloadURL) {
    window.open(downloadURL, '_blank')
  }
}

const deleteFile = async (row, rowIndex) => {
  if (!props.defaultSave) {
    dataList.value.splice(rowIndex, 1)
    return
  }
  const { id } = row
  try {
    await deleteAttrById(id)
    const deleteIndex = dataList.value.findIndex((da) => da.id === id)
      ElNotification.success({
      message: 'Success',
    })
    dataList.value.splice(deleteIndex, 1)
  } catch (err) {
      ElNotification.error({
      message: 'Delete fail',
    })
  }
}

const updateRemark = async (row) => {
  if (!props.defaultSave) {
    row.edit = false
    return
  }
  const { id, remark } = row
  try {
    const res = await updateAttrRemarkById({ id, remark })
    if (res.status === 200 && res.data && res.data.status === 200) {
      row.edit = false
        ElNotification.success({
        message: 'Success',
      })
    } else {
        ElNotification.error({
        message: 'Update fail',
      })
    }
  } catch (err) {
      ElNotification.error({
      message: 'Update fail',
    })
  }
}

const uploadDoc = () => {
  isLoadUpload.value = false
  nextTick(() => {
    isLoadUpload.value = true
    nextTick(() => {
      batchUploadRef.value.open()
    })
  })
}

const uploadError = () => {}

const uploadSuccess = (data) => {
  batchUploadRef.value.close()
  isLoadUpload.value = false

  if (data) {
    const objId = props.objId
    const fileList = (data.data || []).map((item) => {
      const attachment = {
        object: 'protocol',
        objectId: objId,
        attachmentId: item.id,
        fileName: `${item.attachmentName}.${item.suffixes}`,
        fileUrl: item.path,
        fileType: 'File',
        languageId: 1,
        remark: '',
        edit: false,
      }
      return attachment
    })

    if (!props.defaultSave) {
      fileList.forEach((f) => {
        dataList.value.push(f)
      })
      return
    }

    saveData()
  }
}

const saveData = async () => {
  if (!dataList.value || dataList.value.length === 0) {
    return
  }
  const res = await saveAtt(dataList.value)
    ElNotification.success({
    message: 'Save file success',
  })
  initPage()
}

const resetHandle = () => {
  dataList.value = JSON.parse(JSON.stringify(initDataList.value))
}

defineExpose({
  saveData,
  resetHandle,
})
onMounted(() => {
  initPage()
})
</script>

<style lang="scss">
.sgs_smart_protocol_documents {
  font-family: 'Arial' !important;
  background: #fff;
  .menu-icon {
    font-size: 14px;
    cursor: pointer;
    margin: 0 10px;
    color: #ff6600;
  }
}
</style>
