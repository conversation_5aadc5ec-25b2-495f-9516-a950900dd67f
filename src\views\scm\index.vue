<template>
    <div class="scm-sys">
        <scm-component :baseURL="scmUrl"></scm-component>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {scmComponent} from "@sgs/scm-component";
    import {scmBaseUrl} from '@/config/env';


    export default {
        name: "dashboard",
        components: {
            scmComponent
        },
         data() {
            return {
                scmUrl:this.scmBaseUrl,
            }
        },
        computed: {
            ...mapGetters([
                "userInfo", "dimensions", "posts"
            ]),
            role() {
                return {
                    // isBuyer: this.haseRole('UserRole', 'Buyer'),
                    // isSupplier: this.haseRole('UserRole', 'Supplier'),
                    // isTrainingAdmin: this.hasePost('TrainingAdmin'),
                    // isTrainee: this.haseRole('UserRole', 'Trainee'),
                    // isSGS: this.haseRole('SGSUserRole', 'SgsAdmin'),
                    // isThirdPartyLab: this.haseRole('UserRole', 'ThirdPartyLab'),
                    // isCcl: this.haseCclPost('SGSUserRole', 'CCL'),
                };
            }
        },
        mounted() {
            this.scmUrl = scmBaseUrl;
            let timestamp = "timestamp=" + new Date().getTime();
            this.scmUrl = scmBaseUrl.indexOf('?') >= 0 ? scmBaseUrl + "&" + timestamp : "?" + timestamp;
        },
        methods: {}
    }
</script>

<style lang="scss" scoped>
/*.scm-sys {
    /deep/ .el-breadcrumb {
        display: none;
    }
}*/
</style>
