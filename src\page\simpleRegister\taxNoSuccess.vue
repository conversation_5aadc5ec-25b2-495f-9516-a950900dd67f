<template>
  <div class="register-container text-center">
    <el-card>
      <!-- <el-steps :active="1" simple>
        <el-step :title="$t('register.registSuccess')" icon="el-icon-edit"></el-step>
        <el-step :title="$t('register.steps.sgsapprove')" icon="el-icon-upload"></el-step>
      </el-steps> -->
      <h1>{{ $t('register.registSuccess') }}</h1>
      <!-- <div class="sgs-group">
        <el-row>
          <div class="right">
            <top-lang :loadMenu="false"></top-lang>
          </div>
        </el-row>
      </div> -->
      <ul class="list-unstyled">
        <li style="list-style-type:none;">{{ $t('register.tip.login1') }}</li>
        <!--<li style="list-style-type:none;">{{$t('register.tip.success3')}}</li>-->
      </ul>
      <div>
        <el-button type="primary" class="black-btn" @click="$router.push('/')">
            {{ $t('login.submit') }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "success",
  components: {
    TopLang: resolve => require(['../index/top/top-lang'], resolve)
  },
}
</script>

<style scoped lang="scss">
.register-container {
  background: url("/img/loginBackground.png") center no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  padding: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  .el-card {
    width: 700px;
    padding-bottom: 25px;
    ul {
      margin: 50px;
      text-align: left;
      li {
        line-height: 1.5;
      }
    }
  }
}
</style>
